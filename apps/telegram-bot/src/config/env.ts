import { z } from 'zod';

const envSchema = z.object({
  // Telegram Bot Configuration
  TELEGRAM_BOT_TOKEN: z.string().min(1, 'Telegram bot token is required'),
  TELEGRAM_WEBHOOK_URL: z.string().url().optional(),
  TELEGRAM_WEBHOOK_SECRET: z.string().optional(),

  // Convex Configuration
  CONVEX_URL: z.string().url('Convex URL must be a valid URL'),
  CONVEX_DEPLOYMENT: z.string().min(1, 'Convex deployment is required'),

  // Clerk Configuration
  CLERK_SECRET_KEY: z.string().min(1, 'Clerk secret key is required'),
  CLERK_WEBHOOK_SECRET: z.string().optional(),

  // OpenRouter Configuration
  OPENROUTER_API_KEY: z.string().min(1, 'OpenRouter API key is required'),

  // Solana Configuration
  SOLANA_RPC_URL: z.string().url().default('https://api.devnet.solana.com'),
  BONKAI_TOKEN_ADDRESS: z.string().optional(),

  // General Configuration
  NODE_ENV: z
    .enum(['development', 'production', 'test'])
    .default('development'),
  PORT: z.string().transform(Number).default('3000'),

  // Logging
  LOG_LEVEL: z.enum(['debug', 'info', 'warn', 'error']).default('info'),

  // Rate Limiting
  REDIS_URL: z.string().url().optional(),

  // App URLs
  WEB_APP_URL: z.string().url().default('https://bonkai.vercel.app'),
});

export type Env = z.infer<typeof envSchema>;

let env: Env;

try {
  env = envSchema.parse(process.env);
} catch (error) {
  console.error('❌ Invalid environment variables:', error);
  process.exit(1);
}

export { env };

// Helper functions
export const isDevelopment = env.NODE_ENV === 'development';
export const isProduction = env.NODE_ENV === 'production';
export const isTest = env.NODE_ENV === 'test';
