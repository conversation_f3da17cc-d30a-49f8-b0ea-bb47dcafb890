import { create<PERSON>lerkClient } from '@clerk/backend';
import { env } from '../config/env.js';
import { executeConvexMutation } from './convex.js';
import { logger } from './logger.js';

// Initialize Clerk client
const clerk = createClerkClient({ secretKey: env.CLERK_SECRET_KEY });

// Store linking codes temporarily (in production, use Redis)
const linkingCodes = new Map<
  string,
  {
    telegramId: string;
    clerkId: string;
    expiresAt: number;
    used: boolean;
  }
>();

// Generate and store linking code
export function generateLinkingCode(telegramId: string): string {
  const code = Math.random().toString(36).substring(2, 10).toUpperCase();
  const expiresAt = Date.now() + 10 * 60 * 1000; // 10 minutes

  linkingCodes.set(code, {
    telegramId,
    clerkId: '',
    expiresAt,
    used: false,
  });

  // Clean up expired codes periodically
  cleanupExpiredCodes();

  return code;
}

// Verify linking code from web app
export async function verifyLinkingCode(
  code: string,
  clerkId: string,
): Promise<boolean> {
  const linkingData = linkingCodes.get(code);

  if (!linkingData) {
    logger.warn('Invalid linking code attempted', { code, clerkId });
    return false;
  }

  if (linkingData.used) {
    logger.warn('Already used linking code attempted', { code, clerkId });
    return false;
  }

  if (Date.now() > linkingData.expiresAt) {
    logger.warn('Expired linking code attempted', { code, clerkId });
    linkingCodes.delete(code);
    return false;
  }

  try {
    // Get user from Clerk
    const user = await clerk.users.getUser(clerkId);

    if (!user) {
      logger.warn('Invalid Clerk user ID', { clerkId });
      return false;
    }

    // Update linking data
    linkingData.clerkId = clerkId;
    linkingData.used = true;

    // Create/update user in Convex
    const userId = await executeConvexMutation('users:upsertUser', {
      clerkId: user.id,
      email: user.emailAddresses[0]?.emailAddress || '',
      name:
        `${user.firstName || ''} ${user.lastName || ''}`.trim() || undefined,
      imageUrl: user.imageUrl,
    });

    // Link Telegram user
    await executeConvexMutation('telegram:linkTelegramUser', {
      userId,
      telegramId: linkingData.telegramId,
      username: undefined, // Will be updated when user interacts with bot
      firstName: undefined,
      lastName: undefined,
      languageCode: undefined,
      isPremium: false,
    });

    logger.info('Successfully linked Telegram account', {
      clerkId,
      telegramId: linkingData.telegramId,
      userId,
    });

    // Clean up
    linkingCodes.delete(code);

    return true;
  } catch (error) {
    logger.error('Error verifying linking code', error, { code, clerkId });
    return false;
  }
}

// Get linking status
export function getLinkingStatus(code: string): {
  exists: boolean;
  used: boolean;
  expired: boolean;
  telegramId?: string;
} {
  const linkingData = linkingCodes.get(code);

  if (!linkingData) {
    return { exists: false, used: false, expired: false };
  }

  const expired = Date.now() > linkingData.expiresAt;

  if (expired) {
    linkingCodes.delete(code);
  }

  return {
    exists: true,
    used: linkingData.used,
    expired,
    telegramId: linkingData.telegramId,
  };
}

// Clean up expired codes
function cleanupExpiredCodes() {
  const now = Date.now();
  for (const [code, data] of linkingCodes.entries()) {
    if (now > data.expiresAt) {
      linkingCodes.delete(code);
    }
  }
}

// Clean up expired codes every 5 minutes
setInterval(cleanupExpiredCodes, 5 * 60 * 1000);

// Webhook verification for Clerk
export function verifyClerkWebhook(
  payload: string,
  signature: string,
): boolean {
  if (!env.CLERK_WEBHOOK_SECRET) {
    logger.warn('Clerk webhook secret not configured');
    return false;
  }

  // In a real implementation, you'd verify the webhook signature
  // For now, we'll assume it's valid if the secret is configured
  return true;
}
