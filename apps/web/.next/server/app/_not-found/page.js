(()=>{var e={};e.id=9492,e.ids=[9492],e.modules={3171:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,30385,23)),Promise.resolve().then(r.t.bind(r,33737,23)),Promise.resolve().then(r.t.bind(r,86081,23)),Promise.resolve().then(r.t.bind(r,1904,23)),Promise.resolve().then(r.t.bind(r,35856,23)),Promise.resolve().then(r.t.bind(r,55492,23)),Promise.resolve().then(r.t.bind(r,89082,23)),Promise.resolve().then(r.t.bind(r,45812,23))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3851:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,69355,23)),Promise.resolve().then(r.t.bind(r,54439,23)),Promise.resolve().then(r.t.bind(r,67851,23)),Promise.resolve().then(r.t.bind(r,94730,23)),Promise.resolve().then(r.t.bind(r,19774,23)),Promise.resolve().then(r.t.bind(r,53170,23)),Promise.resolve().then(r.t.bind(r,20968,23)),Promise.resolve().then(r.t.bind(r,78298,23))},5631:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNextRouterError",{enumerable:!0,get:function(){return s}});let n=r(22859),o=r(31903);function s(e){return(0,o.isRedirectError)(e)||(0,n.isHTTPAccessFallbackError)(e)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},11997:e=>{"use strict";e.exports=require("punycode")},12412:e=>{"use strict";e.exports=require("assert")},18330:(e,t,r)=>{Promise.resolve().then(r.bind(r,84347)),Promise.resolve().then(r.bind(r,81604))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21336:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>l,pages:()=>d,routeModule:()=>f,tree:()=>c});var n=r(24332),o=r(48819),s=r(67851),i=r.n(s),u=r(97540),a={};for(let e in u)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>u[e]);r.d(t,a);let c={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.t.bind(r,19033,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,34356)),"/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19033,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,39956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,92341,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=[],l={require:r,loadChunk:()=>Promise.resolve()},f=new n.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},21820:e=>{"use strict";e.exports=require("os")},21971:()=>{},22859:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HTTPAccessErrorStatus:function(){return r},HTTP_ERROR_FALLBACK_ERROR_CODE:function(){return o},getAccessFallbackErrorTypeByStatus:function(){return u},getAccessFallbackHTTPStatus:function(){return i},isHTTPAccessFallbackError:function(){return s}});let r={NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401},n=new Set(Object.values(r)),o="NEXT_HTTP_ERROR_FALLBACK";function s(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r]=e.digest.split(";");return t===o&&n.has(Number(r))}function i(e){return Number(e.digest.split(";")[1])}function u(e){switch(e){case 401:return"unauthorized";case 403:return"forbidden";case 404:return"not-found";default:return}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29727:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page-experimental.runtime.prod.js")},31903:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{REDIRECT_ERROR_CODE:function(){return o},RedirectType:function(){return s},isRedirectError:function(){return i}});let n=r(49005),o="NEXT_REDIRECT";var s=function(e){return e.push="push",e.replace="replace",e}({});function i(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let t=e.digest.split(";"),[r,s]=t,i=t.slice(2,-2).join(";"),u=Number(t.at(-2));return r===o&&("replace"===s||"push"===s)&&"string"==typeof i&&!isNaN(u)&&u in n.RedirectStatusCode}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},33873:e=>{"use strict";e.exports=require("path")},34356:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p,metadata:()=>d,viewport:()=>l});var n=r(38828),o=r(52434),s=r(10534),i=r.n(s),u=r(90369),a=r.n(u),c=r(66614);r(21971);let d={metadataBase:new URL("https://bonkai.vercel.app"),title:"BonKai - Web3 AI Ecosystem",description:"AI-powered Web3 platform with token-gated access on Solana."},l={maximumScale:1},f=`\
(function() {
  var html = document.documentElement;
  var meta = document.querySelector('meta[name="theme-color"]');
  if (!meta) {
    meta = document.createElement('meta');
    meta.setAttribute('name', 'theme-color');
    document.head.appendChild(meta);
  }
  function updateThemeColor() {
    var isDark = html.classList.contains('dark');
    meta.setAttribute('content', isDark ? 'hsl(240deg 10% 3.92%)' : 'hsl(0 0% 100%)');
  }
  var observer = new MutationObserver(updateThemeColor);
  observer.observe(html, { attributes: true, attributeFilter: ['class'] });
  updateThemeColor();
})();`;async function p({children:e}){return(0,n.jsxs)("html",{lang:"en",suppressHydrationWarning:!0,className:`${i().variable} ${a().variable}`,children:[(0,n.jsx)("head",{children:(0,n.jsx)("script",{dangerouslySetInnerHTML:{__html:f}})}),(0,n.jsx)("body",{className:"antialiased",children:(0,n.jsxs)(c.Providers,{children:[(0,n.jsx)(o.Toaster,{position:"top-center"}),e]})})]})}},34631:e=>{"use strict";e.exports=require("tls")},44989:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return a}});let n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=o(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var u=s?Object.getOwnPropertyDescriptor(e,i):null;u&&(u.get||u.set)?Object.defineProperty(n,i,u):n[i]=e[i]}return n.default=e,r&&r.set(e,n),n}(r(61365));function o(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(o=function(e){return e?r:t})(e)}let s={current:null},i="function"==typeof n.cache?n.cache:e=>e,u=console.warn;function a(e){return function(...t){u(e(...t))}}i(e=>{try{u(s.current)}finally{s.current=null}})},49005:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}});var r=function(e){return e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect",e}({});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},52073:(e,t,r)=>{"use strict";r.d(t,{y:()=>o});var n=r(70358);let o=(0,n.createServerReference)("7ff75850b73f1dfd41bbc2ede99fb927e18bc81cf0",n.callServer,void 0,n.findSourceMapURL,"invalidateCacheAction")},55122:(e,t,r)=>{Promise.resolve().then(r.bind(r,66614)),Promise.resolve().then(r.bind(r,52434))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58316:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"7f4c08c553bf967cd0b7c8f5250f46c2e73879b9bd":()=>n.ai,"7f8502d0204c4a0fa8f39abb8d058f5a7c86a9cae8":()=>n.at,"7fea155ec4cccf4e8d3a3d15a5aae1d42efed4db52":()=>n.ot,"7ff75850b73f1dfd41bbc2ede99fb927e18bc81cf0":()=>o.y});var n=r(93878),o=r(69335)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66614:(e,t,r)=>{"use strict";r.d(t,{Providers:()=>n});let n=(0,r(33952).registerClientReference)(function(){throw Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/providers.tsx","Providers")},72610:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isPostpone",{enumerable:!0,get:function(){return n}});let r=Symbol.for("react.postpone");function n(e){return"object"==typeof e&&null!==e&&e.$$typeof===r}},73024:e=>{"use strict";e.exports=require("node:fs")},73136:e=>{"use strict";e.exports=require("node:url")},74075:e=>{"use strict";e.exports=require("zlib")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},84347:(e,t,r)=>{"use strict";r.d(t,{Providers:()=>x});var n=r(13486),o=r(30352),s=r(6105),i=r(7608),u=r(48961),a=r(87940),c=r(95680);function d({children:e,...t}){return(0,n.jsx)(u.N,{...t,children:e})}var l=r(60159),f=r(22180),p=r(65133),b=r(74525),m=r(8120),h=r(87695),v=r(7668),_=r(8157);function y({children:e}){let t=process.env.NEXT_PUBLIC_SOLANA_NETWORK||"devnet",r=(0,l.useMemo)(()=>process.env.NEXT_PUBLIC_SOLANA_RPC_URL?process.env.NEXT_PUBLIC_SOLANA_RPC_URL:(0,_.Kw)(t),[t]),o=(0,l.useMemo)(()=>[new m.c,new h.d,new v.BackpackWalletAdapter],[]);return(0,n.jsx)(f.S,{endpoint:r,children:(0,n.jsx)(p.r,{wallets:o,autoConnect:!0,children:(0,n.jsx)(b.I,{children:e})})})}let P=new c.eH(process.env.NEXT_PUBLIC_CONVEX_URL);function x({children:e}){return(0,n.jsx)(d,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:(0,n.jsx)(a.q,{client:P,useAuth:o.d,children:(0,n.jsx)(g,{children:(0,n.jsx)(y,{children:e})})})})}function g({children:e}){let{resolvedTheme:t}=(0,u.D)();return(0,n.jsx)(s.lJ,{appearance:{baseTheme:"dark"===t?i.dark:void 0,variables:{colorPrimary:"oklch(0.6397 0.1720 36.4421)",colorText:"dark"===t?"oklch(0.9219 0 0)":"oklch(0.3211 0 0)",colorBackground:"dark"===t?"oklch(0.2598 0.0306 262.6666)":"oklch(0.9383 0.0042 236.4993)",colorInputBackground:"dark"===t?"oklch(0.3843 0.0301 269.7337)":"oklch(0.9700 0.0029 264.5420)",colorInputText:"dark"===t?"oklch(0.9219 0 0)":"oklch(0.3211 0 0)",borderRadius:"0.75rem",fontFamily:"Inter, sans-serif"},elements:{formButtonPrimary:"bg-primary text-primary-foreground hover:bg-primary/90",card:"bg-card text-card-foreground rounded-lg border border-border shadow-sm",formFieldInput:"bg-input border-input",footerActionLink:"text-primary hover:text-primary/80"}},children:e})}},90507:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{BailoutToCSRError:function(){return n},isBailoutToCSRError:function(){return o}});let r="BAILOUT_TO_CLIENT_SIDE_RENDERING";class n extends Error{constructor(e){super("Bail out to client-side rendering: "+e),this.reason=e,this.digest=r}}function o(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[191,2911,9445],()=>r(21336));module.exports=n})();