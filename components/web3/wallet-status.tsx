'use client';

import { useWallet } from '@solana/wallet-adapter-react';
import { useUser } from '@clerk/nextjs';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Wallet,
  Copy,
  ExternalLink,
  CheckCircle,
  AlertCircle,
} from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

interface WalletStatusProps {
  showBalance?: boolean;
  compact?: boolean;
  className?: string;
}

export function WalletStatus({
  showBalance = true,
  compact = false,
  className,
}: WalletStatusProps) {
  const { publicKey, connected } = useWallet();
  const { user, isLoaded } = useUser();

  const walletLinked = user?.publicMetadata?.walletAddress;
  const walletAddress = publicKey?.toBase58();

  const copyAddress = () => {
    if (walletAddress) {
      navigator.clipboard.writeText(walletAddress);
      toast.success('Wallet address copied to clipboard');
    }
  };

  const openInExplorer = () => {
    if (walletAddress) {
      window.open(
        `https://explorer.solana.com/address/${walletAddress}`,
        '_blank',
      );
    }
  };

  const formatAddress = (address: string) => {
    return `${address.slice(0, 4)}...${address.slice(-4)}`;
  };

  if (!isLoaded) {
    return (
      <Card className={cn('w-full', className)}>
        <CardContent className="p-4">
          <div className="flex items-center gap-3">
            <Skeleton className="size-10 rounded-full" />
            <div className="space-y-2 flex-1">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-3 w-32" />
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!user) {
    return null;
  }

  const getStatusColor = () => {
    if (connected && walletLinked) return 'text-green-600 dark:text-green-400';
    if (connected) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-gray-500 dark:text-gray-400';
  };

  const getStatusIcon = () => {
    if (connected && walletLinked) return <CheckCircle className="size-4" />;
    if (connected) return <AlertCircle className="size-4" />;
    return <Wallet className="size-4" />;
  };

  const getStatusText = () => {
    if (connected && walletLinked) return 'Connected & Linked';
    if (connected) return 'Connected (Not Linked)';
    return 'Not Connected';
  };

  if (compact) {
    return (
      <div className={cn('flex items-center gap-2', className)}>
        <div className={cn('flex items-center gap-1', getStatusColor())}>
          {getStatusIcon()}
          <span className="text-sm font-medium">{getStatusText()}</span>
        </div>
        {connected && walletAddress && (
          <Badge variant="outline" className="font-mono text-xs">
            {formatAddress(walletAddress)}
          </Badge>
        )}
      </div>
    );
  }

  return (
    <Card className={cn('w-full', className)}>
      <CardContent className="p-4">
        <div className="space-y-4">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Wallet className="size-5 text-muted-foreground" />
              <h3 className="font-semibold">Wallet Status</h3>
            </div>
            <div className={cn('flex items-center gap-1', getStatusColor())}>
              {getStatusIcon()}
              <span className="text-sm font-medium">{getStatusText()}</span>
            </div>
          </div>

          {/* Wallet Details */}
          {connected && walletAddress ? (
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">
                    Wallet Address
                  </p>
                  <p className="font-mono text-sm">
                    {formatAddress(walletAddress)}
                  </p>
                </div>
                <div className="flex gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={copyAddress}
                    className="size-8 p-0"
                  >
                    <Copy className="size-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={openInExplorer}
                    className="size-8 p-0"
                  >
                    <ExternalLink className="size-4" />
                  </Button>
                </div>
              </div>

              {/* Linking Status */}
              {!walletLinked && (
                <div className="p-3 bg-yellow-50 dark:bg-yellow-950/20 border border-yellow-200 dark:border-yellow-800/30 rounded-lg">
                  <div className="flex items-start gap-2">
                    <AlertCircle className="size-4 text-yellow-600 dark:text-yellow-400 mt-0.5 shrink-0" />
                    <div className="space-y-1">
                      <p className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                        Wallet Not Linked
                      </p>
                      <p className="text-xs text-yellow-700 dark:text-yellow-300">
                        Link your wallet to your account to unlock tier benefits
                        and staking rewards.
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Balance Section (if enabled) */}
              {showBalance && walletLinked && (
                <div className="p-3 bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800/30 rounded-lg">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="size-4 text-green-600 dark:text-green-400" />
                    <div>
                      <p className="text-sm font-medium text-green-800 dark:text-green-200">
                        Wallet Linked Successfully
                      </p>
                      <p className="text-xs text-green-700 dark:text-green-300">
                        You can now stake BONK tokens and earn rewards.
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-6">
              <Wallet className="size-12 mx-auto text-muted-foreground/50 mb-3" />
              <p className="text-sm text-muted-foreground">
                Connect your Solana wallet to get started
              </p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
