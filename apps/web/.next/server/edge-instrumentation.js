(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[183],{521:t=>{"use strict";t.exports=require("node:async_hooks")},858:(t,e,r)=>{"use strict";r.r(e),r.d(e,{register:()=>iH});var n,o,i,a,s,u,l,c,p,f,h,d={};r.r(d),r.d(d,{DiagConsoleLogger:()=>j,DiagLogLevel:()=>n,INVALID_SPANID:()=>tT,INVALID_SPAN_CONTEXT:()=>tm,INVALID_TRACEID:()=>ty,ProxyTracer:()=>tG,ProxyTracerProvider:()=>tk,ROOT_CONTEXT:()=>V,SamplingDecision:()=>a,SpanKind:()=>s,SpanStatusCode:()=>u,TraceFlags:()=>i,ValueType:()=>o,baggageEntryMetadataFromString:()=>B,context:()=>tz,createContextKey:()=>G,createNoopMeter:()=>ts,createTraceState:()=>tW,default:()=>t9,defaultTextMapGetter:()=>tu,defaultTextMapSetter:()=>tl,diag:()=>tY,isSpanContextValid:()=>tM,isValidSpanId:()=>tw,isValidTraceId:()=>tN,metrics:()=>tQ,propagation:()=>t6,trace:()=>t7});var _={};r.r(_),r.d(_,{NOOP_LOGGER:()=>ei,NOOP_LOGGER_PROVIDER:()=>es,NoopLogger:()=>eo,NoopLoggerProvider:()=>ea,ProxyLogger:()=>eu,ProxyLoggerProvider:()=>el,SeverityNumber:()=>l,logs:()=>eh});var E="object"==typeof globalThis?globalThis:"object"==typeof self?self:"object"==typeof window?window:"object"==typeof r.g?r.g:{},g="1.9.0",T=/^(\d+)\.(\d+)\.(\d+)(-(.+))?$/,y=function(t){var e=new Set([t]),r=new Set,n=t.match(T);if(!n)return function(){return!1};var o={major:+n[1],minor:+n[2],patch:+n[3],prerelease:n[4]};if(null!=o.prerelease)return function(e){return e===t};function i(t){return r.add(t),!1}return function(t){if(e.has(t))return!0;if(r.has(t))return!1;var n=t.match(T);if(!n)return i(t);var a={major:+n[1],minor:+n[2],patch:+n[3],prerelease:n[4]};if(null!=a.prerelease||o.major!==a.major)return i(t);if(0===o.major)return o.minor===a.minor&&o.patch<=a.patch?(e.add(t),!0):i(t);return o.minor<=a.minor?(e.add(t),!0):i(t)}}(g),m=Symbol.for("opentelemetry.js.api."+g.split(".")[0]);function v(t,e,r,n){void 0===n&&(n=!1);var o,i=E[m]=null!=(o=E[m])?o:{version:g};if(!n&&i[t]){var a=Error("@opentelemetry/api: Attempted duplicate registration of API: "+t);return r.error(a.stack||a.message),!1}if(i.version!==g){var a=Error("@opentelemetry/api: Registration of version v"+i.version+" for "+t+" does not match previously registered API v"+g);return r.error(a.stack||a.message),!1}return i[t]=e,r.debug("@opentelemetry/api: Registered a global for "+t+" v"+g+"."),!0}function S(t){var e,r,n=null==(e=E[m])?void 0:e.version;if(n&&y(n))return null==(r=E[m])?void 0:r[t]}function b(t,e){e.debug("@opentelemetry/api: Unregistering a global for "+t+" v"+g+".");var r=E[m];r&&delete r[t]}var A=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},O=function(t,e,r){if(r||2==arguments.length)for(var n,o=0,i=e.length;o<i;o++)!n&&o in e||(n||(n=Array.prototype.slice.call(e,0,o)),n[o]=e[o]);return t.concat(n||Array.prototype.slice.call(e))},R=function(){function t(t){this._namespace=t.namespace||"DiagComponentLogger"}return t.prototype.debug=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return L("debug",this._namespace,t)},t.prototype.error=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return L("error",this._namespace,t)},t.prototype.info=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return L("info",this._namespace,t)},t.prototype.warn=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return L("warn",this._namespace,t)},t.prototype.verbose=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return L("verbose",this._namespace,t)},t}();function L(t,e,r){var n=S("diag");if(n)return r.unshift(e),n[t].apply(n,O([],A(r),!1))}!function(t){t[t.NONE=0]="NONE",t[t.ERROR=30]="ERROR",t[t.WARN=50]="WARN",t[t.INFO=60]="INFO",t[t.DEBUG=70]="DEBUG",t[t.VERBOSE=80]="VERBOSE",t[t.ALL=9999]="ALL"}(n||(n={}));var P=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},C=function(t,e,r){if(r||2==arguments.length)for(var n,o=0,i=e.length;o<i;o++)!n&&o in e||(n||(n=Array.prototype.slice.call(e,0,o)),n[o]=e[o]);return t.concat(n||Array.prototype.slice.call(e))},I=function(){function t(){function t(t){return function(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var n=S("diag");if(n)return n[t].apply(n,C([],P(e),!1))}}var e=this;e.setLogger=function(t,r){if(void 0===r&&(r={logLevel:n.INFO}),t===e){var o,i,a,s=Error("Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation");return e.error(null!=(o=s.stack)?o:s.message),!1}"number"==typeof r&&(r={logLevel:r});var u=S("diag"),l=function(t,e){function r(r,n){var o=e[r];return"function"==typeof o&&t>=n?o.bind(e):function(){}}return t<n.NONE?t=n.NONE:t>n.ALL&&(t=n.ALL),e=e||{},{error:r("error",n.ERROR),warn:r("warn",n.WARN),info:r("info",n.INFO),debug:r("debug",n.DEBUG),verbose:r("verbose",n.VERBOSE)}}(null!=(i=r.logLevel)?i:n.INFO,t);if(u&&!r.suppressOverrideMessage){var c=null!=(a=Error().stack)?a:"<failed to generate stacktrace>";u.warn("Current logger will be overwritten from "+c),l.warn("Current logger will overwrite one already registered from "+c)}return v("diag",l,e,!0)},e.disable=function(){b("diag",e)},e.createComponentLogger=function(t){return new R(t)},e.verbose=t("verbose"),e.debug=t("debug"),e.info=t("info"),e.warn=t("warn"),e.error=t("error")}return t.instance=function(){return this._instance||(this._instance=new t),this._instance},t}(),N=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},w=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},M=function(){function t(t){this._entries=t?new Map(t):new Map}return t.prototype.getEntry=function(t){var e=this._entries.get(t);if(e)return Object.assign({},e)},t.prototype.getAllEntries=function(){return Array.from(this._entries.entries()).map(function(t){var e=N(t,2);return[e[0],e[1]]})},t.prototype.setEntry=function(e,r){var n=new t(this._entries);return n._entries.set(e,r),n},t.prototype.removeEntry=function(e){var r=new t(this._entries);return r._entries.delete(e),r},t.prototype.removeEntries=function(){for(var e,r,n=[],o=0;o<arguments.length;o++)n[o]=arguments[o];var i=new t(this._entries);try{for(var a=w(n),s=a.next();!s.done;s=a.next()){var u=s.value;i._entries.delete(u)}}catch(t){e={error:t}}finally{try{s&&!s.done&&(r=a.return)&&r.call(a)}finally{if(e)throw e.error}}return i},t.prototype.clear=function(){return new t},t}(),D=Symbol("BaggageEntryMetadata"),x=I.instance();function U(t){return void 0===t&&(t={}),new M(new Map(Object.entries(t)))}function B(t){return"string"!=typeof t&&(x.error("Cannot create baggage metadata from unknown type: "+typeof t),t=""),{__TYPE__:D,toString:function(){return t}}}function G(t){return Symbol.for(t)}var V=new function t(e){var r=this;r._currentContext=e?new Map(e):new Map,r.getValue=function(t){return r._currentContext.get(t)},r.setValue=function(e,n){var o=new t(r._currentContext);return o._currentContext.set(e,n),o},r.deleteValue=function(e){var n=new t(r._currentContext);return n._currentContext.delete(e),n}},k=[{n:"error",c:"error"},{n:"warn",c:"warn"},{n:"info",c:"info"},{n:"debug",c:"debug"},{n:"verbose",c:"trace"}],j=function(){for(var t=0;t<k.length;t++)this[k[t].n]=function(t){return function(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];if(console){var n=console[t];if("function"!=typeof n&&(n=console.log),"function"==typeof n)return n.apply(console,e)}}}(k[t].c)},F=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),H=function(){function t(){}return t.prototype.createGauge=function(t,e){return te},t.prototype.createHistogram=function(t,e){return tr},t.prototype.createCounter=function(t,e){return tt},t.prototype.createUpDownCounter=function(t,e){return tn},t.prototype.createObservableGauge=function(t,e){return ti},t.prototype.createObservableCounter=function(t,e){return to},t.prototype.createObservableUpDownCounter=function(t,e){return ta},t.prototype.addBatchObservableCallback=function(t,e){},t.prototype.removeBatchObservableCallback=function(t){},t}(),X=function(){},K=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return F(e,t),e.prototype.add=function(t,e){},e}(X),W=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return F(e,t),e.prototype.add=function(t,e){},e}(X),z=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return F(e,t),e.prototype.record=function(t,e){},e}(X),Y=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return F(e,t),e.prototype.record=function(t,e){},e}(X),$=function(){function t(){}return t.prototype.addCallback=function(t){},t.prototype.removeCallback=function(t){},t}(),q=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return F(e,t),e}($),Q=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return F(e,t),e}($),Z=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return F(e,t),e}($),J=new H,tt=new K,te=new z,tr=new Y,tn=new W,to=new q,ti=new Q,ta=new Z;function ts(){return J}!function(t){t[t.INT=0]="INT",t[t.DOUBLE=1]="DOUBLE"}(o||(o={}));var tu={get:function(t,e){if(null!=t)return t[e]},keys:function(t){return null==t?[]:Object.keys(t)}},tl={set:function(t,e,r){null!=t&&(t[e]=r)}},tc=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},tp=function(t,e,r){if(r||2==arguments.length)for(var n,o=0,i=e.length;o<i;o++)!n&&o in e||(n||(n=Array.prototype.slice.call(e,0,o)),n[o]=e[o]);return t.concat(n||Array.prototype.slice.call(e))},tf=function(){function t(){}return t.prototype.active=function(){return V},t.prototype.with=function(t,e,r){for(var n=[],o=3;o<arguments.length;o++)n[o-3]=arguments[o];return e.call.apply(e,tp([r],tc(n),!1))},t.prototype.bind=function(t,e){return e},t.prototype.enable=function(){return this},t.prototype.disable=function(){return this},t}(),th=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},td=function(t,e,r){if(r||2==arguments.length)for(var n,o=0,i=e.length;o<i;o++)!n&&o in e||(n||(n=Array.prototype.slice.call(e,0,o)),n[o]=e[o]);return t.concat(n||Array.prototype.slice.call(e))},t_="context",tE=new tf,tg=function(){function t(){}return t.getInstance=function(){return this._instance||(this._instance=new t),this._instance},t.prototype.setGlobalContextManager=function(t){return v(t_,t,I.instance())},t.prototype.active=function(){return this._getContextManager().active()},t.prototype.with=function(t,e,r){for(var n,o=[],i=3;i<arguments.length;i++)o[i-3]=arguments[i];return(n=this._getContextManager()).with.apply(n,td([t,e,r],th(o),!1))},t.prototype.bind=function(t,e){return this._getContextManager().bind(t,e)},t.prototype._getContextManager=function(){return S(t_)||tE},t.prototype.disable=function(){this._getContextManager().disable(),b(t_,I.instance())},t}();!function(t){t[t.NONE=0]="NONE",t[t.SAMPLED=1]="SAMPLED"}(i||(i={}));var tT="0000000000000000",ty="00000000000000000000000000000000",tm={traceId:ty,spanId:tT,traceFlags:i.NONE},tv=function(){function t(t){void 0===t&&(t=tm),this._spanContext=t}return t.prototype.spanContext=function(){return this._spanContext},t.prototype.setAttribute=function(t,e){return this},t.prototype.setAttributes=function(t){return this},t.prototype.addEvent=function(t,e){return this},t.prototype.addLink=function(t){return this},t.prototype.addLinks=function(t){return this},t.prototype.setStatus=function(t){return this},t.prototype.updateName=function(t){return this},t.prototype.end=function(t){},t.prototype.isRecording=function(){return!1},t.prototype.recordException=function(t,e){},t}(),tS=G("OpenTelemetry Context Key SPAN");function tb(t){return t.getValue(tS)||void 0}function tA(){return tb(tg.getInstance().active())}function tO(t,e){return t.setValue(tS,e)}function tR(t){return t.deleteValue(tS)}function tL(t,e){return tO(t,new tv(e))}function tP(t){var e;return null==(e=tb(t))?void 0:e.spanContext()}var tC=/^([0-9a-f]{32})$/i,tI=/^[0-9a-f]{16}$/i;function tN(t){return tC.test(t)&&t!==ty}function tw(t){return tI.test(t)&&t!==tT}function tM(t){return tN(t.traceId)&&tw(t.spanId)}function tD(t){return new tv(t)}var tx=tg.getInstance(),tU=function(){function t(){}return t.prototype.startSpan=function(t,e,r){if(void 0===r&&(r=tx.active()),null==e?void 0:e.root)return new tv;var n,o=r&&tP(r);return"object"==typeof(n=o)&&"string"==typeof n.spanId&&"string"==typeof n.traceId&&"number"==typeof n.traceFlags&&tM(o)?new tv(o):new tv},t.prototype.startActiveSpan=function(t,e,r,n){if(!(arguments.length<2)){2==arguments.length?a=e:3==arguments.length?(o=e,a=r):(o=e,i=r,a=n);var o,i,a,s=null!=i?i:tx.active(),u=this.startSpan(t,o,s),l=tO(s,u);return tx.with(l,a,void 0,u)}},t}(),tB=new tU,tG=function(){function t(t,e,r,n){this._provider=t,this.name=e,this.version=r,this.options=n}return t.prototype.startSpan=function(t,e,r){return this._getTracer().startSpan(t,e,r)},t.prototype.startActiveSpan=function(t,e,r,n){var o=this._getTracer();return Reflect.apply(o.startActiveSpan,o,arguments)},t.prototype._getTracer=function(){if(this._delegate)return this._delegate;var t=this._provider.getDelegateTracer(this.name,this.version,this.options);return t?(this._delegate=t,this._delegate):tB},t}(),tV=new(function(){function t(){}return t.prototype.getTracer=function(t,e,r){return new tU},t}()),tk=function(){function t(){}return t.prototype.getTracer=function(t,e,r){var n;return null!=(n=this.getDelegateTracer(t,e,r))?n:new tG(this,t,e,r)},t.prototype.getDelegate=function(){var t;return null!=(t=this._delegate)?t:tV},t.prototype.setDelegate=function(t){this._delegate=t},t.prototype.getDelegateTracer=function(t,e,r){var n;return null==(n=this._delegate)?void 0:n.getTracer(t,e,r)},t}();!function(t){t[t.NOT_RECORD=0]="NOT_RECORD",t[t.RECORD=1]="RECORD",t[t.RECORD_AND_SAMPLED=2]="RECORD_AND_SAMPLED"}(a||(a={})),function(t){t[t.INTERNAL=0]="INTERNAL",t[t.SERVER=1]="SERVER",t[t.CLIENT=2]="CLIENT",t[t.PRODUCER=3]="PRODUCER",t[t.CONSUMER=4]="CONSUMER"}(s||(s={})),function(t){t[t.UNSET=0]="UNSET",t[t.OK=1]="OK",t[t.ERROR=2]="ERROR"}(u||(u={}));var tj="[_0-9a-z-*/]",tF=RegExp("^(?:[a-z]"+tj+"{0,255}|"+("[a-z0-9]"+tj+"{0,240}@[a-z]")+tj+"{0,13})$"),tH=/^[ -~]{0,255}[!-~]$/,tX=/,|=/,tK=function(){function t(t){this._internalState=new Map,t&&this._parse(t)}return t.prototype.set=function(t,e){var r=this._clone();return r._internalState.has(t)&&r._internalState.delete(t),r._internalState.set(t,e),r},t.prototype.unset=function(t){var e=this._clone();return e._internalState.delete(t),e},t.prototype.get=function(t){return this._internalState.get(t)},t.prototype.serialize=function(){var t=this;return this._keys().reduce(function(e,r){return e.push(r+"="+t.get(r)),e},[]).join(",")},t.prototype._parse=function(t){!(t.length>512)&&(this._internalState=t.split(",").reverse().reduce(function(t,e){var r=e.trim(),n=r.indexOf("=");if(-1!==n){var o=r.slice(0,n),i=r.slice(n+1,e.length);tF.test(o)&&tH.test(i)&&!tX.test(i)&&t.set(o,i)}return t},new Map),this._internalState.size>32&&(this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,32))))},t.prototype._keys=function(){return Array.from(this._internalState.keys()).reverse()},t.prototype._clone=function(){var e=new t;return e._internalState=new Map(this._internalState),e},t}();function tW(t){return new tK(t)}var tz=tg.getInstance(),tY=I.instance(),t$=new(function(){function t(){}return t.prototype.getMeter=function(t,e,r){return J},t}()),tq="metrics",tQ=(function(){function t(){}return t.getInstance=function(){return this._instance||(this._instance=new t),this._instance},t.prototype.setGlobalMeterProvider=function(t){return v(tq,t,I.instance())},t.prototype.getMeterProvider=function(){return S(tq)||t$},t.prototype.getMeter=function(t,e,r){return this.getMeterProvider().getMeter(t,e,r)},t.prototype.disable=function(){b(tq,I.instance())},t})().getInstance(),tZ=function(){function t(){}return t.prototype.inject=function(t,e){},t.prototype.extract=function(t,e){return t},t.prototype.fields=function(){return[]},t}(),tJ=G("OpenTelemetry Baggage Key");function t0(t){return t.getValue(tJ)||void 0}function t1(){return t0(tg.getInstance().active())}function t2(t,e){return t.setValue(tJ,e)}function t3(t){return t.deleteValue(tJ)}var t4="propagation",t8=new tZ,t6=(function(){function t(){this.createBaggage=U,this.getBaggage=t0,this.getActiveBaggage=t1,this.setBaggage=t2,this.deleteBaggage=t3}return t.getInstance=function(){return this._instance||(this._instance=new t),this._instance},t.prototype.setGlobalPropagator=function(t){return v(t4,t,I.instance())},t.prototype.inject=function(t,e,r){return void 0===r&&(r=tl),this._getGlobalPropagator().inject(t,e,r)},t.prototype.extract=function(t,e,r){return void 0===r&&(r=tu),this._getGlobalPropagator().extract(t,e,r)},t.prototype.fields=function(){return this._getGlobalPropagator().fields()},t.prototype.disable=function(){b(t4,I.instance())},t.prototype._getGlobalPropagator=function(){return S(t4)||t8},t})().getInstance(),t5="trace",t7=(function(){function t(){this._proxyTracerProvider=new tk,this.wrapSpanContext=tD,this.isSpanContextValid=tM,this.deleteSpan=tR,this.getSpan=tb,this.getActiveSpan=tA,this.getSpanContext=tP,this.setSpan=tO,this.setSpanContext=tL}return t.getInstance=function(){return this._instance||(this._instance=new t),this._instance},t.prototype.setGlobalTracerProvider=function(t){var e=v(t5,this._proxyTracerProvider,I.instance());return e&&this._proxyTracerProvider.setDelegate(t),e},t.prototype.getTracerProvider=function(){return S(t5)||this._proxyTracerProvider},t.prototype.getTracer=function(t,e){return this.getTracerProvider().getTracer(t,e)},t.prototype.disable=function(){b(t5,I.instance()),this._proxyTracerProvider=new tk},t})().getInstance();let t9={context:tz,diag:tY,metrics:tQ,propagation:t6,trace:t7};var et=r(521),ee=r.t(et,2);let er=require("node:events");var en=r.t(er,2);!function(t){t[t.UNSPECIFIED=0]="UNSPECIFIED",t[t.TRACE=1]="TRACE",t[t.TRACE2=2]="TRACE2",t[t.TRACE3=3]="TRACE3",t[t.TRACE4=4]="TRACE4",t[t.DEBUG=5]="DEBUG",t[t.DEBUG2=6]="DEBUG2",t[t.DEBUG3=7]="DEBUG3",t[t.DEBUG4=8]="DEBUG4",t[t.INFO=9]="INFO",t[t.INFO2=10]="INFO2",t[t.INFO3=11]="INFO3",t[t.INFO4=12]="INFO4",t[t.WARN=13]="WARN",t[t.WARN2=14]="WARN2",t[t.WARN3=15]="WARN3",t[t.WARN4=16]="WARN4",t[t.ERROR=17]="ERROR",t[t.ERROR2=18]="ERROR2",t[t.ERROR3=19]="ERROR3",t[t.ERROR4=20]="ERROR4",t[t.FATAL=21]="FATAL",t[t.FATAL2=22]="FATAL2",t[t.FATAL3=23]="FATAL3",t[t.FATAL4=24]="FATAL4"}(l||(l={}));class eo{emit(t){}}let ei=new eo;class ea{getLogger(t,e,r){return new eo}}let es=new ea;class eu{constructor(t,e,r,n){this._provider=t,this.name=e,this.version=r,this.options=n}emit(t){this._getLogger().emit(t)}_getLogger(){if(this._delegate)return this._delegate;let t=this._provider.getDelegateLogger(this.name,this.version,this.options);return t?(this._delegate=t,this._delegate):ei}}class el{getLogger(t,e,r){var n;return null!=(n=this.getDelegateLogger(t,e,r))?n:new eu(this,t,e,r)}getDelegate(){var t;return null!=(t=this._delegate)?t:es}setDelegate(t){this._delegate=t}getDelegateLogger(t,e,r){var n;return null==(n=this._delegate)?void 0:n.getLogger(t,e,r)}}let ec="object"==typeof globalThis?globalThis:"object"==typeof self?self:"object"==typeof window?window:"object"==typeof r.g?r.g:{},ep=Symbol.for("io.opentelemetry.js.api.logs");class ef{constructor(){this._proxyLoggerProvider=new el}static getInstance(){return this._instance||(this._instance=new ef),this._instance}setGlobalLoggerProvider(t){return ec[ep]?this.getLoggerProvider():(ec[ep]=e=>1===e?t:es,this._proxyLoggerProvider.setDelegate(t),t)}getLoggerProvider(){var t,e;return null!=(e=null==(t=ec[ep])?void 0:t.call(ec,1))?e:this._proxyLoggerProvider}getLogger(t,e,r){return this.getLoggerProvider().getLogger(t,e,r)}disable(){delete ec[ep],this._proxyLoggerProvider=new el}}let eh=ef.getInstance();void 0===globalThis.performance&&(globalThis.performance={timeOrigin:0,now:()=>Date.now()});var ed=Object.create,e_=Object.defineProperty,eE=Object.getOwnPropertyDescriptor,eg=Object.getOwnPropertyNames,eT=Object.getPrototypeOf,ey=Object.prototype.hasOwnProperty,em=(t=>"u">typeof require?require:"u">typeof Proxy?new Proxy(t,{get:(t,e)=>("u">typeof require?require:t)[e]}):t)(function(t){if("u">typeof require)return require.apply(this,arguments);throw Error('Dynamic require of "'+t+'" is not supported')}),ev=(t,e)=>()=>(t&&(e=t(t=0)),e),eS=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports),eb=(t,e,r,n)=>{if(e&&"object"==typeof e||"function"==typeof e)for(let o of eg(e))ey.call(t,o)||o===r||e_(t,o,{get:()=>e[o],enumerable:!(n=eE(e,o))||n.enumerable});return t},eA=(t,e,r)=>(eb(t,e,"default"),r&&eb(r,e,"default")),eO=(t,e,r)=>(r=null!=t?ed(eT(t)):{},eb(!e&&t&&t.__esModule?r:e_(r,"default",{value:t,enumerable:!0}),t)),eR=t=>eb(e_({},"__esModule",{value:!0}),t),eL={},eP=ev(()=>{eA(eL,d)}),eC=eS(t=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isTracingSuppressed=t.unsuppressTracing=t.suppressTracing=void 0;var e=(0,(eP(),eR(eL)).createContextKey)("OpenTelemetry SDK Context Key SUPPRESS_TRACING");t.suppressTracing=function(t){return t.setValue(e,!0)},t.unsuppressTracing=function(t){return t.deleteValue(e)},t.isTracingSuppressed=function(t){return!0===t.getValue(e)}}),eI=eS(t=>{Object.defineProperty(t,"__esModule",{value:!0}),t.BAGGAGE_MAX_TOTAL_LENGTH=t.BAGGAGE_MAX_PER_NAME_VALUE_PAIRS=t.BAGGAGE_MAX_NAME_VALUE_PAIRS=t.BAGGAGE_HEADER=t.BAGGAGE_ITEMS_SEPARATOR=t.BAGGAGE_PROPERTIES_SEPARATOR=t.BAGGAGE_KEY_PAIR_SEPARATOR=void 0,t.BAGGAGE_KEY_PAIR_SEPARATOR="=",t.BAGGAGE_PROPERTIES_SEPARATOR=";",t.BAGGAGE_ITEMS_SEPARATOR=",",t.BAGGAGE_HEADER="baggage",t.BAGGAGE_MAX_NAME_VALUE_PAIRS=180,t.BAGGAGE_MAX_PER_NAME_VALUE_PAIRS=4096,t.BAGGAGE_MAX_TOTAL_LENGTH=8192}),eN=eS(t=>{Object.defineProperty(t,"__esModule",{value:!0}),t.parseKeyPairsIntoRecord=t.parsePairKeyValue=t.getKeyPairs=t.serializeKeyPairs=void 0;var e=(eP(),eR(eL)),r=eI();function n(t){let n=t.split(r.BAGGAGE_PROPERTIES_SEPARATOR);if(n.length<=0)return;let o=n.shift();if(!o)return;let i=o.indexOf(r.BAGGAGE_KEY_PAIR_SEPARATOR);if(i<=0)return;let a=decodeURIComponent(o.substring(0,i).trim()),s=decodeURIComponent(o.substring(i+1).trim()),u;return n.length>0&&(u=(0,e.baggageEntryMetadataFromString)(n.join(r.BAGGAGE_PROPERTIES_SEPARATOR))),{key:a,value:s,metadata:u}}t.serializeKeyPairs=function(t){return t.reduce((t,e)=>{let n=`${t}${""!==t?r.BAGGAGE_ITEMS_SEPARATOR:""}${e}`;return n.length>r.BAGGAGE_MAX_TOTAL_LENGTH?t:n},"")},t.getKeyPairs=function(t){return t.getAllEntries().map(([t,e])=>{let n=`${encodeURIComponent(t)}=${encodeURIComponent(e.value)}`;return void 0!==e.metadata&&(n+=r.BAGGAGE_PROPERTIES_SEPARATOR+e.metadata.toString()),n})},t.parsePairKeyValue=n,t.parseKeyPairsIntoRecord=function(t){return"string"!=typeof t||0===t.length?{}:t.split(r.BAGGAGE_ITEMS_SEPARATOR).map(t=>n(t)).filter(t=>void 0!==t&&t.value.length>0).reduce((t,e)=>(t[e.key]=e.value,t),{})}}),ew=eS(t=>{Object.defineProperty(t,"__esModule",{value:!0}),t.W3CBaggagePropagator=void 0;var e=(eP(),eR(eL)),r=eC(),n=eI(),o=eN();t.W3CBaggagePropagator=class{inject(t,i,a){let s=e.propagation.getBaggage(t);if(!s||(0,r.isTracingSuppressed)(t))return;let u=(0,o.getKeyPairs)(s).filter(t=>t.length<=n.BAGGAGE_MAX_PER_NAME_VALUE_PAIRS).slice(0,n.BAGGAGE_MAX_NAME_VALUE_PAIRS),l=(0,o.serializeKeyPairs)(u);l.length>0&&a.set(i,n.BAGGAGE_HEADER,l)}extract(t,r,i){let a=i.get(r,n.BAGGAGE_HEADER),s=Array.isArray(a)?a.join(n.BAGGAGE_ITEMS_SEPARATOR):a;if(!s)return t;let u={};return 0===s.length||(s.split(n.BAGGAGE_ITEMS_SEPARATOR).forEach(t=>{let e=(0,o.parsePairKeyValue)(t);if(e){let t={value:e.value};e.metadata&&(t.metadata=e.metadata),u[e.key]=t}}),0===Object.entries(u).length)?t:e.propagation.setBaggage(t,e.propagation.createBaggage(u))}fields(){return[n.BAGGAGE_HEADER]}}}),eM=eS(t=>{Object.defineProperty(t,"__esModule",{value:!0}),t.AnchoredClock=void 0,t.AnchoredClock=class{constructor(t,e){this._monotonicClock=e,this._epochMillis=t.now(),this._performanceMillis=e.now()}now(){let t=this._monotonicClock.now()-this._performanceMillis;return this._epochMillis+t}}}),eD=eS(t=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isAttributeValue=t.isAttributeKey=t.sanitizeAttributes=void 0;var e=(eP(),eR(eL));function r(t){return"string"==typeof t&&t.length>0}function n(t){return null==t||(Array.isArray(t)?function(t){let e;for(let r of t)if(null!=r){if(!e){if(o(r)){e=typeof r;continue}return!1}if(typeof r!==e)return!1}return!0}(t):o(t))}function o(t){switch(typeof t){case"number":case"boolean":case"string":return!0}return!1}t.sanitizeAttributes=function(t){let o={};if("object"!=typeof t||null==t)return o;for(let[i,a]of Object.entries(t)){if(!r(i)){e.diag.warn(`Invalid attribute key: ${i}`);continue}if(!n(a)){e.diag.warn(`Invalid attribute value set for key: ${i}`);continue}Array.isArray(a)?o[i]=a.slice():o[i]=a}return o},t.isAttributeKey=r,t.isAttributeValue=n}),ex=eS(t=>{Object.defineProperty(t,"__esModule",{value:!0}),t.loggingErrorHandler=void 0;var e=(eP(),eR(eL));t.loggingErrorHandler=function(){return t=>{var r;e.diag.error("string"==typeof(r=t)?r:JSON.stringify(function(t){let e={},r=t;for(;null!==r;)Object.getOwnPropertyNames(r).forEach(t=>{if(e[t])return;let n=r[t];n&&(e[t]=String(n))}),r=Object.getPrototypeOf(r);return e}(r)))}}}),eU=eS(t=>{Object.defineProperty(t,"__esModule",{value:!0}),t.globalErrorHandler=t.setGlobalErrorHandler=void 0;var e=(0,ex().loggingErrorHandler)();t.setGlobalErrorHandler=function(t){e=t},t.globalErrorHandler=function(t){try{e(t)}catch{}}}),eB=eS(t=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TracesSamplerValues=void 0,function(t){t.AlwaysOff="always_off",t.AlwaysOn="always_on",t.ParentBasedAlwaysOff="parentbased_always_off",t.ParentBasedAlwaysOn="parentbased_always_on",t.ParentBasedTraceIdRatio="parentbased_traceidratio",t.TraceIdRatio="traceidratio"}(t.TracesSamplerValues||(t.TracesSamplerValues={}))}),eG=eS(t=>{Object.defineProperty(t,"__esModule",{value:!0}),t._globalThis=void 0,t._globalThis="object"==typeof globalThis?globalThis:"object"==typeof self?self:"object"==typeof window?window:"object"==typeof global?global:{}}),eV=eS(t=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getEnvWithoutDefaults=t.parseEnvironment=t.DEFAULT_ENVIRONMENT=t.DEFAULT_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT=t.DEFAULT_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT=t.DEFAULT_ATTRIBUTE_COUNT_LIMIT=t.DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT=void 0;var e=(eP(),eR(eL)),r=eB(),n=eG(),o=["OTEL_SDK_DISABLED"],i=["OTEL_BSP_EXPORT_TIMEOUT","OTEL_BSP_MAX_EXPORT_BATCH_SIZE","OTEL_BSP_MAX_QUEUE_SIZE","OTEL_BSP_SCHEDULE_DELAY","OTEL_BLRP_EXPORT_TIMEOUT","OTEL_BLRP_MAX_EXPORT_BATCH_SIZE","OTEL_BLRP_MAX_QUEUE_SIZE","OTEL_BLRP_SCHEDULE_DELAY","OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT","OTEL_ATTRIBUTE_COUNT_LIMIT","OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT","OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT","OTEL_LOGRECORD_ATTRIBUTE_VALUE_LENGTH_LIMIT","OTEL_LOGRECORD_ATTRIBUTE_COUNT_LIMIT","OTEL_SPAN_EVENT_COUNT_LIMIT","OTEL_SPAN_LINK_COUNT_LIMIT","OTEL_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT","OTEL_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT","OTEL_EXPORTER_OTLP_TIMEOUT","OTEL_EXPORTER_OTLP_TRACES_TIMEOUT","OTEL_EXPORTER_OTLP_METRICS_TIMEOUT","OTEL_EXPORTER_OTLP_LOGS_TIMEOUT","OTEL_EXPORTER_JAEGER_AGENT_PORT"],a=["OTEL_NO_PATCH_MODULES","OTEL_PROPAGATORS"];t.DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT=1/0,t.DEFAULT_ATTRIBUTE_COUNT_LIMIT=128,t.DEFAULT_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT=128,t.DEFAULT_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT=128,t.DEFAULT_ENVIRONMENT={OTEL_SDK_DISABLED:!1,CONTAINER_NAME:"",ECS_CONTAINER_METADATA_URI_V4:"",ECS_CONTAINER_METADATA_URI:"",HOSTNAME:"",KUBERNETES_SERVICE_HOST:"",NAMESPACE:"",OTEL_BSP_EXPORT_TIMEOUT:3e4,OTEL_BSP_MAX_EXPORT_BATCH_SIZE:512,OTEL_BSP_MAX_QUEUE_SIZE:2048,OTEL_BSP_SCHEDULE_DELAY:5e3,OTEL_BLRP_EXPORT_TIMEOUT:3e4,OTEL_BLRP_MAX_EXPORT_BATCH_SIZE:512,OTEL_BLRP_MAX_QUEUE_SIZE:2048,OTEL_BLRP_SCHEDULE_DELAY:5e3,OTEL_EXPORTER_JAEGER_AGENT_HOST:"",OTEL_EXPORTER_JAEGER_AGENT_PORT:6832,OTEL_EXPORTER_JAEGER_ENDPOINT:"",OTEL_EXPORTER_JAEGER_PASSWORD:"",OTEL_EXPORTER_JAEGER_USER:"",OTEL_EXPORTER_OTLP_ENDPOINT:"",OTEL_EXPORTER_OTLP_TRACES_ENDPOINT:"",OTEL_EXPORTER_OTLP_METRICS_ENDPOINT:"",OTEL_EXPORTER_OTLP_LOGS_ENDPOINT:"",OTEL_EXPORTER_OTLP_HEADERS:"",OTEL_EXPORTER_OTLP_TRACES_HEADERS:"",OTEL_EXPORTER_OTLP_METRICS_HEADERS:"",OTEL_EXPORTER_OTLP_LOGS_HEADERS:"",OTEL_EXPORTER_OTLP_TIMEOUT:1e4,OTEL_EXPORTER_OTLP_TRACES_TIMEOUT:1e4,OTEL_EXPORTER_OTLP_METRICS_TIMEOUT:1e4,OTEL_EXPORTER_OTLP_LOGS_TIMEOUT:1e4,OTEL_EXPORTER_ZIPKIN_ENDPOINT:"http://localhost:9411/api/v2/spans",OTEL_LOG_LEVEL:e.DiagLogLevel.INFO,OTEL_NO_PATCH_MODULES:[],OTEL_PROPAGATORS:["tracecontext","baggage"],OTEL_RESOURCE_ATTRIBUTES:"",OTEL_SERVICE_NAME:"",OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT:t.DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT,OTEL_ATTRIBUTE_COUNT_LIMIT:t.DEFAULT_ATTRIBUTE_COUNT_LIMIT,OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT:t.DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT,OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT:t.DEFAULT_ATTRIBUTE_COUNT_LIMIT,OTEL_LOGRECORD_ATTRIBUTE_VALUE_LENGTH_LIMIT:t.DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT,OTEL_LOGRECORD_ATTRIBUTE_COUNT_LIMIT:t.DEFAULT_ATTRIBUTE_COUNT_LIMIT,OTEL_SPAN_EVENT_COUNT_LIMIT:128,OTEL_SPAN_LINK_COUNT_LIMIT:128,OTEL_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT:t.DEFAULT_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT,OTEL_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT:t.DEFAULT_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT,OTEL_TRACES_EXPORTER:"",OTEL_TRACES_SAMPLER:r.TracesSamplerValues.ParentBasedAlwaysOn,OTEL_TRACES_SAMPLER_ARG:"",OTEL_LOGS_EXPORTER:"",OTEL_EXPORTER_OTLP_INSECURE:"",OTEL_EXPORTER_OTLP_TRACES_INSECURE:"",OTEL_EXPORTER_OTLP_METRICS_INSECURE:"",OTEL_EXPORTER_OTLP_LOGS_INSECURE:"",OTEL_EXPORTER_OTLP_CERTIFICATE:"",OTEL_EXPORTER_OTLP_TRACES_CERTIFICATE:"",OTEL_EXPORTER_OTLP_METRICS_CERTIFICATE:"",OTEL_EXPORTER_OTLP_LOGS_CERTIFICATE:"",OTEL_EXPORTER_OTLP_COMPRESSION:"",OTEL_EXPORTER_OTLP_TRACES_COMPRESSION:"",OTEL_EXPORTER_OTLP_METRICS_COMPRESSION:"",OTEL_EXPORTER_OTLP_LOGS_COMPRESSION:"",OTEL_EXPORTER_OTLP_CLIENT_KEY:"",OTEL_EXPORTER_OTLP_TRACES_CLIENT_KEY:"",OTEL_EXPORTER_OTLP_METRICS_CLIENT_KEY:"",OTEL_EXPORTER_OTLP_LOGS_CLIENT_KEY:"",OTEL_EXPORTER_OTLP_CLIENT_CERTIFICATE:"",OTEL_EXPORTER_OTLP_TRACES_CLIENT_CERTIFICATE:"",OTEL_EXPORTER_OTLP_METRICS_CLIENT_CERTIFICATE:"",OTEL_EXPORTER_OTLP_LOGS_CLIENT_CERTIFICATE:"",OTEL_EXPORTER_OTLP_PROTOCOL:"http/protobuf",OTEL_EXPORTER_OTLP_TRACES_PROTOCOL:"http/protobuf",OTEL_EXPORTER_OTLP_METRICS_PROTOCOL:"http/protobuf",OTEL_EXPORTER_OTLP_LOGS_PROTOCOL:"http/protobuf",OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE:"cumulative"};var s={ALL:e.DiagLogLevel.ALL,VERBOSE:e.DiagLogLevel.VERBOSE,DEBUG:e.DiagLogLevel.DEBUG,INFO:e.DiagLogLevel.INFO,WARN:e.DiagLogLevel.WARN,ERROR:e.DiagLogLevel.ERROR,NONE:e.DiagLogLevel.NONE};function u(e){let r={};for(let n in t.DEFAULT_ENVIRONMENT)if("OTEL_LOG_LEVEL"===n)!function(t,e,r){let n=r[t];if("string"==typeof n){let r=s[n.toUpperCase()];null!=r&&(e[t]=r)}}(n,r,e);else if(o.indexOf(n)>-1)!function(t,e,r){if(typeof r[t]>"u")return;let n=String(r[t]);e[t]="true"===n.toLowerCase()}(n,r,e);else if(i.indexOf(n)>-1)!function(t,e,r,n=-1/0,o=1/0){if("u">typeof r[t]){let i=Number(r[t]);isNaN(i)||(i<n?e[t]=n:i>o?e[t]=o:e[t]=i)}}(n,r,e);else if(a.indexOf(n)>-1)!function(t,e,r,n=","){let o=r[t];"string"==typeof o&&(e[t]=o.split(n).map(t=>t.trim()))}(n,r,e);else{let t=e[n];"u">typeof t&&null!==t&&(r[n]=String(t))}return r}t.parseEnvironment=u,t.getEnvWithoutDefaults=function(){return"u">typeof process&&process&&process.env?u(process.env):u(n._globalThis)}}),ek=eS(t=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getEnv=void 0;var e=eV(),r=eG();t.getEnv=function(){let t=(0,e.parseEnvironment)(r._globalThis);return Object.assign({},e.DEFAULT_ENVIRONMENT,t)}}),ej=eS(t=>{Object.defineProperty(t,"__esModule",{value:!0}),t.hexToBase64=void 0,t.hexToBase64=function(t){let e=t.length,r="";for(let n=0;n<e;n+=2)r+=String.fromCharCode(parseInt(t.substring(n,n+2),16));return btoa(r)}}),eF=eS(t=>{Object.defineProperty(t,"__esModule",{value:!0}),t.RandomIdGenerator=void 0,t.RandomIdGenerator=class{constructor(){this.generateTraceId=r(16),this.generateSpanId=r(8)}};var e=Array(32);function r(t){return function(){for(let r=0;r<2*t;r++)e[r]=Math.floor(16*Math.random())+48,e[r]>=58&&(e[r]+=39);return String.fromCharCode.apply(null,e.slice(0,2*t))}}}),eH=eS(t=>{Object.defineProperty(t,"__esModule",{value:!0}),t.otperformance=void 0,t.otperformance=performance}),eX=eS(t=>{Object.defineProperty(t,"__esModule",{value:!0}),t.VERSION=void 0,t.VERSION="1.19.0"}),eK=eS(t=>{Object.defineProperty(t,"__esModule",{value:!0}),t.MessageTypeValues=t.RpcGrpcStatusCodeValues=t.MessagingOperationValues=t.MessagingDestinationKindValues=t.HttpFlavorValues=t.NetHostConnectionSubtypeValues=t.NetHostConnectionTypeValues=t.NetTransportValues=t.FaasInvokedProviderValues=t.FaasDocumentOperationValues=t.FaasTriggerValues=t.DbCassandraConsistencyLevelValues=t.DbSystemValues=t.SemanticAttributes=void 0,t.SemanticAttributes={AWS_LAMBDA_INVOKED_ARN:"aws.lambda.invoked_arn",DB_SYSTEM:"db.system",DB_CONNECTION_STRING:"db.connection_string",DB_USER:"db.user",DB_JDBC_DRIVER_CLASSNAME:"db.jdbc.driver_classname",DB_NAME:"db.name",DB_STATEMENT:"db.statement",DB_OPERATION:"db.operation",DB_MSSQL_INSTANCE_NAME:"db.mssql.instance_name",DB_CASSANDRA_KEYSPACE:"db.cassandra.keyspace",DB_CASSANDRA_PAGE_SIZE:"db.cassandra.page_size",DB_CASSANDRA_CONSISTENCY_LEVEL:"db.cassandra.consistency_level",DB_CASSANDRA_TABLE:"db.cassandra.table",DB_CASSANDRA_IDEMPOTENCE:"db.cassandra.idempotence",DB_CASSANDRA_SPECULATIVE_EXECUTION_COUNT:"db.cassandra.speculative_execution_count",DB_CASSANDRA_COORDINATOR_ID:"db.cassandra.coordinator.id",DB_CASSANDRA_COORDINATOR_DC:"db.cassandra.coordinator.dc",DB_HBASE_NAMESPACE:"db.hbase.namespace",DB_REDIS_DATABASE_INDEX:"db.redis.database_index",DB_MONGODB_COLLECTION:"db.mongodb.collection",DB_SQL_TABLE:"db.sql.table",EXCEPTION_TYPE:"exception.type",EXCEPTION_MESSAGE:"exception.message",EXCEPTION_STACKTRACE:"exception.stacktrace",EXCEPTION_ESCAPED:"exception.escaped",FAAS_TRIGGER:"faas.trigger",FAAS_EXECUTION:"faas.execution",FAAS_DOCUMENT_COLLECTION:"faas.document.collection",FAAS_DOCUMENT_OPERATION:"faas.document.operation",FAAS_DOCUMENT_TIME:"faas.document.time",FAAS_DOCUMENT_NAME:"faas.document.name",FAAS_TIME:"faas.time",FAAS_CRON:"faas.cron",FAAS_COLDSTART:"faas.coldstart",FAAS_INVOKED_NAME:"faas.invoked_name",FAAS_INVOKED_PROVIDER:"faas.invoked_provider",FAAS_INVOKED_REGION:"faas.invoked_region",NET_TRANSPORT:"net.transport",NET_PEER_IP:"net.peer.ip",NET_PEER_PORT:"net.peer.port",NET_PEER_NAME:"net.peer.name",NET_HOST_IP:"net.host.ip",NET_HOST_PORT:"net.host.port",NET_HOST_NAME:"net.host.name",NET_HOST_CONNECTION_TYPE:"net.host.connection.type",NET_HOST_CONNECTION_SUBTYPE:"net.host.connection.subtype",NET_HOST_CARRIER_NAME:"net.host.carrier.name",NET_HOST_CARRIER_MCC:"net.host.carrier.mcc",NET_HOST_CARRIER_MNC:"net.host.carrier.mnc",NET_HOST_CARRIER_ICC:"net.host.carrier.icc",PEER_SERVICE:"peer.service",ENDUSER_ID:"enduser.id",ENDUSER_ROLE:"enduser.role",ENDUSER_SCOPE:"enduser.scope",THREAD_ID:"thread.id",THREAD_NAME:"thread.name",CODE_FUNCTION:"code.function",CODE_NAMESPACE:"code.namespace",CODE_FILEPATH:"code.filepath",CODE_LINENO:"code.lineno",HTTP_METHOD:"http.method",HTTP_URL:"http.url",HTTP_TARGET:"http.target",HTTP_HOST:"http.host",HTTP_SCHEME:"http.scheme",HTTP_STATUS_CODE:"http.status_code",HTTP_FLAVOR:"http.flavor",HTTP_USER_AGENT:"http.user_agent",HTTP_REQUEST_CONTENT_LENGTH:"http.request_content_length",HTTP_REQUEST_CONTENT_LENGTH_UNCOMPRESSED:"http.request_content_length_uncompressed",HTTP_RESPONSE_CONTENT_LENGTH:"http.response_content_length",HTTP_RESPONSE_CONTENT_LENGTH_UNCOMPRESSED:"http.response_content_length_uncompressed",HTTP_SERVER_NAME:"http.server_name",HTTP_ROUTE:"http.route",HTTP_CLIENT_IP:"http.client_ip",AWS_DYNAMODB_TABLE_NAMES:"aws.dynamodb.table_names",AWS_DYNAMODB_CONSUMED_CAPACITY:"aws.dynamodb.consumed_capacity",AWS_DYNAMODB_ITEM_COLLECTION_METRICS:"aws.dynamodb.item_collection_metrics",AWS_DYNAMODB_PROVISIONED_READ_CAPACITY:"aws.dynamodb.provisioned_read_capacity",AWS_DYNAMODB_PROVISIONED_WRITE_CAPACITY:"aws.dynamodb.provisioned_write_capacity",AWS_DYNAMODB_CONSISTENT_READ:"aws.dynamodb.consistent_read",AWS_DYNAMODB_PROJECTION:"aws.dynamodb.projection",AWS_DYNAMODB_LIMIT:"aws.dynamodb.limit",AWS_DYNAMODB_ATTRIBUTES_TO_GET:"aws.dynamodb.attributes_to_get",AWS_DYNAMODB_INDEX_NAME:"aws.dynamodb.index_name",AWS_DYNAMODB_SELECT:"aws.dynamodb.select",AWS_DYNAMODB_GLOBAL_SECONDARY_INDEXES:"aws.dynamodb.global_secondary_indexes",AWS_DYNAMODB_LOCAL_SECONDARY_INDEXES:"aws.dynamodb.local_secondary_indexes",AWS_DYNAMODB_EXCLUSIVE_START_TABLE:"aws.dynamodb.exclusive_start_table",AWS_DYNAMODB_TABLE_COUNT:"aws.dynamodb.table_count",AWS_DYNAMODB_SCAN_FORWARD:"aws.dynamodb.scan_forward",AWS_DYNAMODB_SEGMENT:"aws.dynamodb.segment",AWS_DYNAMODB_TOTAL_SEGMENTS:"aws.dynamodb.total_segments",AWS_DYNAMODB_COUNT:"aws.dynamodb.count",AWS_DYNAMODB_SCANNED_COUNT:"aws.dynamodb.scanned_count",AWS_DYNAMODB_ATTRIBUTE_DEFINITIONS:"aws.dynamodb.attribute_definitions",AWS_DYNAMODB_GLOBAL_SECONDARY_INDEX_UPDATES:"aws.dynamodb.global_secondary_index_updates",MESSAGING_SYSTEM:"messaging.system",MESSAGING_DESTINATION:"messaging.destination",MESSAGING_DESTINATION_KIND:"messaging.destination_kind",MESSAGING_TEMP_DESTINATION:"messaging.temp_destination",MESSAGING_PROTOCOL:"messaging.protocol",MESSAGING_PROTOCOL_VERSION:"messaging.protocol_version",MESSAGING_URL:"messaging.url",MESSAGING_MESSAGE_ID:"messaging.message_id",MESSAGING_CONVERSATION_ID:"messaging.conversation_id",MESSAGING_MESSAGE_PAYLOAD_SIZE_BYTES:"messaging.message_payload_size_bytes",MESSAGING_MESSAGE_PAYLOAD_COMPRESSED_SIZE_BYTES:"messaging.message_payload_compressed_size_bytes",MESSAGING_OPERATION:"messaging.operation",MESSAGING_CONSUMER_ID:"messaging.consumer_id",MESSAGING_RABBITMQ_ROUTING_KEY:"messaging.rabbitmq.routing_key",MESSAGING_KAFKA_MESSAGE_KEY:"messaging.kafka.message_key",MESSAGING_KAFKA_CONSUMER_GROUP:"messaging.kafka.consumer_group",MESSAGING_KAFKA_CLIENT_ID:"messaging.kafka.client_id",MESSAGING_KAFKA_PARTITION:"messaging.kafka.partition",MESSAGING_KAFKA_TOMBSTONE:"messaging.kafka.tombstone",RPC_SYSTEM:"rpc.system",RPC_SERVICE:"rpc.service",RPC_METHOD:"rpc.method",RPC_GRPC_STATUS_CODE:"rpc.grpc.status_code",RPC_JSONRPC_VERSION:"rpc.jsonrpc.version",RPC_JSONRPC_REQUEST_ID:"rpc.jsonrpc.request_id",RPC_JSONRPC_ERROR_CODE:"rpc.jsonrpc.error_code",RPC_JSONRPC_ERROR_MESSAGE:"rpc.jsonrpc.error_message",MESSAGE_TYPE:"message.type",MESSAGE_ID:"message.id",MESSAGE_COMPRESSED_SIZE:"message.compressed_size",MESSAGE_UNCOMPRESSED_SIZE:"message.uncompressed_size"},t.DbSystemValues={OTHER_SQL:"other_sql",MSSQL:"mssql",MYSQL:"mysql",ORACLE:"oracle",DB2:"db2",POSTGRESQL:"postgresql",REDSHIFT:"redshift",HIVE:"hive",CLOUDSCAPE:"cloudscape",HSQLDB:"hsqldb",PROGRESS:"progress",MAXDB:"maxdb",HANADB:"hanadb",INGRES:"ingres",FIRSTSQL:"firstsql",EDB:"edb",CACHE:"cache",ADABAS:"adabas",FIREBIRD:"firebird",DERBY:"derby",FILEMAKER:"filemaker",INFORMIX:"informix",INSTANTDB:"instantdb",INTERBASE:"interbase",MARIADB:"mariadb",NETEZZA:"netezza",PERVASIVE:"pervasive",POINTBASE:"pointbase",SQLITE:"sqlite",SYBASE:"sybase",TERADATA:"teradata",VERTICA:"vertica",H2:"h2",COLDFUSION:"coldfusion",CASSANDRA:"cassandra",HBASE:"hbase",MONGODB:"mongodb",REDIS:"redis",COUCHBASE:"couchbase",COUCHDB:"couchdb",COSMOSDB:"cosmosdb",DYNAMODB:"dynamodb",NEO4J:"neo4j",GEODE:"geode",ELASTICSEARCH:"elasticsearch",MEMCACHED:"memcached",COCKROACHDB:"cockroachdb"},t.DbCassandraConsistencyLevelValues={ALL:"all",EACH_QUORUM:"each_quorum",QUORUM:"quorum",LOCAL_QUORUM:"local_quorum",ONE:"one",TWO:"two",THREE:"three",LOCAL_ONE:"local_one",ANY:"any",SERIAL:"serial",LOCAL_SERIAL:"local_serial"},t.FaasTriggerValues={DATASOURCE:"datasource",HTTP:"http",PUBSUB:"pubsub",TIMER:"timer",OTHER:"other"},t.FaasDocumentOperationValues={INSERT:"insert",EDIT:"edit",DELETE:"delete"},t.FaasInvokedProviderValues={ALIBABA_CLOUD:"alibaba_cloud",AWS:"aws",AZURE:"azure",GCP:"gcp"},t.NetTransportValues={IP_TCP:"ip_tcp",IP_UDP:"ip_udp",IP:"ip",UNIX:"unix",PIPE:"pipe",INPROC:"inproc",OTHER:"other"},t.NetHostConnectionTypeValues={WIFI:"wifi",WIRED:"wired",CELL:"cell",UNAVAILABLE:"unavailable",UNKNOWN:"unknown"},t.NetHostConnectionSubtypeValues={GPRS:"gprs",EDGE:"edge",UMTS:"umts",CDMA:"cdma",EVDO_0:"evdo_0",EVDO_A:"evdo_a",CDMA2000_1XRTT:"cdma2000_1xrtt",HSDPA:"hsdpa",HSUPA:"hsupa",HSPA:"hspa",IDEN:"iden",EVDO_B:"evdo_b",LTE:"lte",EHRPD:"ehrpd",HSPAP:"hspap",GSM:"gsm",TD_SCDMA:"td_scdma",IWLAN:"iwlan",NR:"nr",NRNSA:"nrnsa",LTE_CA:"lte_ca"},t.HttpFlavorValues={HTTP_1_0:"1.0",HTTP_1_1:"1.1",HTTP_2_0:"2.0",SPDY:"SPDY",QUIC:"QUIC"},t.MessagingDestinationKindValues={QUEUE:"queue",TOPIC:"topic"},t.MessagingOperationValues={RECEIVE:"receive",PROCESS:"process"},t.RpcGrpcStatusCodeValues={OK:0,CANCELLED:1,UNKNOWN:2,INVALID_ARGUMENT:3,DEADLINE_EXCEEDED:4,NOT_FOUND:5,ALREADY_EXISTS:6,PERMISSION_DENIED:7,RESOURCE_EXHAUSTED:8,FAILED_PRECONDITION:9,ABORTED:10,OUT_OF_RANGE:11,UNIMPLEMENTED:12,INTERNAL:13,UNAVAILABLE:14,DATA_LOSS:15,UNAUTHENTICATED:16},t.MessageTypeValues={SENT:"SENT",RECEIVED:"RECEIVED"}}),eW=eS(t=>{var e=t&&t.__createBinding||(Object.create?function(t,e,r,n){void 0===n&&(n=r),Object.defineProperty(t,n,{enumerable:!0,get:function(){return e[r]}})}:function(t,e,r,n){void 0===n&&(n=r),t[n]=e[r]}),r=t&&t.__exportStar||function(t,r){for(var n in t)"default"===n||Object.prototype.hasOwnProperty.call(r,n)||e(r,t,n)};Object.defineProperty(t,"__esModule",{value:!0}),r(eK(),t)}),ez=eS(t=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TelemetrySdkLanguageValues=t.OsTypeValues=t.HostArchValues=t.AwsEcsLaunchtypeValues=t.CloudPlatformValues=t.CloudProviderValues=t.SemanticResourceAttributes=void 0,t.SemanticResourceAttributes={CLOUD_PROVIDER:"cloud.provider",CLOUD_ACCOUNT_ID:"cloud.account.id",CLOUD_REGION:"cloud.region",CLOUD_AVAILABILITY_ZONE:"cloud.availability_zone",CLOUD_PLATFORM:"cloud.platform",AWS_ECS_CONTAINER_ARN:"aws.ecs.container.arn",AWS_ECS_CLUSTER_ARN:"aws.ecs.cluster.arn",AWS_ECS_LAUNCHTYPE:"aws.ecs.launchtype",AWS_ECS_TASK_ARN:"aws.ecs.task.arn",AWS_ECS_TASK_FAMILY:"aws.ecs.task.family",AWS_ECS_TASK_REVISION:"aws.ecs.task.revision",AWS_EKS_CLUSTER_ARN:"aws.eks.cluster.arn",AWS_LOG_GROUP_NAMES:"aws.log.group.names",AWS_LOG_GROUP_ARNS:"aws.log.group.arns",AWS_LOG_STREAM_NAMES:"aws.log.stream.names",AWS_LOG_STREAM_ARNS:"aws.log.stream.arns",CONTAINER_NAME:"container.name",CONTAINER_ID:"container.id",CONTAINER_RUNTIME:"container.runtime",CONTAINER_IMAGE_NAME:"container.image.name",CONTAINER_IMAGE_TAG:"container.image.tag",DEPLOYMENT_ENVIRONMENT:"deployment.environment",DEVICE_ID:"device.id",DEVICE_MODEL_IDENTIFIER:"device.model.identifier",DEVICE_MODEL_NAME:"device.model.name",FAAS_NAME:"faas.name",FAAS_ID:"faas.id",FAAS_VERSION:"faas.version",FAAS_INSTANCE:"faas.instance",FAAS_MAX_MEMORY:"faas.max_memory",HOST_ID:"host.id",HOST_NAME:"host.name",HOST_TYPE:"host.type",HOST_ARCH:"host.arch",HOST_IMAGE_NAME:"host.image.name",HOST_IMAGE_ID:"host.image.id",HOST_IMAGE_VERSION:"host.image.version",K8S_CLUSTER_NAME:"k8s.cluster.name",K8S_NODE_NAME:"k8s.node.name",K8S_NODE_UID:"k8s.node.uid",K8S_NAMESPACE_NAME:"k8s.namespace.name",K8S_POD_UID:"k8s.pod.uid",K8S_POD_NAME:"k8s.pod.name",K8S_CONTAINER_NAME:"k8s.container.name",K8S_REPLICASET_UID:"k8s.replicaset.uid",K8S_REPLICASET_NAME:"k8s.replicaset.name",K8S_DEPLOYMENT_UID:"k8s.deployment.uid",K8S_DEPLOYMENT_NAME:"k8s.deployment.name",K8S_STATEFULSET_UID:"k8s.statefulset.uid",K8S_STATEFULSET_NAME:"k8s.statefulset.name",K8S_DAEMONSET_UID:"k8s.daemonset.uid",K8S_DAEMONSET_NAME:"k8s.daemonset.name",K8S_JOB_UID:"k8s.job.uid",K8S_JOB_NAME:"k8s.job.name",K8S_CRONJOB_UID:"k8s.cronjob.uid",K8S_CRONJOB_NAME:"k8s.cronjob.name",OS_TYPE:"os.type",OS_DESCRIPTION:"os.description",OS_NAME:"os.name",OS_VERSION:"os.version",PROCESS_PID:"process.pid",PROCESS_EXECUTABLE_NAME:"process.executable.name",PROCESS_EXECUTABLE_PATH:"process.executable.path",PROCESS_COMMAND:"process.command",PROCESS_COMMAND_LINE:"process.command_line",PROCESS_COMMAND_ARGS:"process.command_args",PROCESS_OWNER:"process.owner",PROCESS_RUNTIME_NAME:"process.runtime.name",PROCESS_RUNTIME_VERSION:"process.runtime.version",PROCESS_RUNTIME_DESCRIPTION:"process.runtime.description",SERVICE_NAME:"service.name",SERVICE_NAMESPACE:"service.namespace",SERVICE_INSTANCE_ID:"service.instance.id",SERVICE_VERSION:"service.version",TELEMETRY_SDK_NAME:"telemetry.sdk.name",TELEMETRY_SDK_LANGUAGE:"telemetry.sdk.language",TELEMETRY_SDK_VERSION:"telemetry.sdk.version",TELEMETRY_AUTO_VERSION:"telemetry.auto.version",WEBENGINE_NAME:"webengine.name",WEBENGINE_VERSION:"webengine.version",WEBENGINE_DESCRIPTION:"webengine.description"},t.CloudProviderValues={ALIBABA_CLOUD:"alibaba_cloud",AWS:"aws",AZURE:"azure",GCP:"gcp"},t.CloudPlatformValues={ALIBABA_CLOUD_ECS:"alibaba_cloud_ecs",ALIBABA_CLOUD_FC:"alibaba_cloud_fc",AWS_EC2:"aws_ec2",AWS_ECS:"aws_ecs",AWS_EKS:"aws_eks",AWS_LAMBDA:"aws_lambda",AWS_ELASTIC_BEANSTALK:"aws_elastic_beanstalk",AZURE_VM:"azure_vm",AZURE_CONTAINER_INSTANCES:"azure_container_instances",AZURE_AKS:"azure_aks",AZURE_FUNCTIONS:"azure_functions",AZURE_APP_SERVICE:"azure_app_service",GCP_COMPUTE_ENGINE:"gcp_compute_engine",GCP_CLOUD_RUN:"gcp_cloud_run",GCP_KUBERNETES_ENGINE:"gcp_kubernetes_engine",GCP_CLOUD_FUNCTIONS:"gcp_cloud_functions",GCP_APP_ENGINE:"gcp_app_engine"},t.AwsEcsLaunchtypeValues={EC2:"ec2",FARGATE:"fargate"},t.HostArchValues={AMD64:"amd64",ARM32:"arm32",ARM64:"arm64",IA64:"ia64",PPC32:"ppc32",PPC64:"ppc64",X86:"x86"},t.OsTypeValues={WINDOWS:"windows",LINUX:"linux",DARWIN:"darwin",FREEBSD:"freebsd",NETBSD:"netbsd",OPENBSD:"openbsd",DRAGONFLYBSD:"dragonflybsd",HPUX:"hpux",AIX:"aix",SOLARIS:"solaris",Z_OS:"z_os"},t.TelemetrySdkLanguageValues={CPP:"cpp",DOTNET:"dotnet",ERLANG:"erlang",GO:"go",JAVA:"java",NODEJS:"nodejs",PHP:"php",PYTHON:"python",RUBY:"ruby",WEBJS:"webjs"}}),eY=eS(t=>{var e=t&&t.__createBinding||(Object.create?function(t,e,r,n){void 0===n&&(n=r),Object.defineProperty(t,n,{enumerable:!0,get:function(){return e[r]}})}:function(t,e,r,n){void 0===n&&(n=r),t[n]=e[r]}),r=t&&t.__exportStar||function(t,r){for(var n in t)"default"===n||Object.prototype.hasOwnProperty.call(r,n)||e(r,t,n)};Object.defineProperty(t,"__esModule",{value:!0}),r(ez(),t)}),e$=eS(t=>{var e=t&&t.__createBinding||(Object.create?function(t,e,r,n){void 0===n&&(n=r),Object.defineProperty(t,n,{enumerable:!0,get:function(){return e[r]}})}:function(t,e,r,n){void 0===n&&(n=r),t[n]=e[r]}),r=t&&t.__exportStar||function(t,r){for(var n in t)"default"===n||Object.prototype.hasOwnProperty.call(r,n)||e(r,t,n)};Object.defineProperty(t,"__esModule",{value:!0}),r(eW(),t),r(eY(),t)}),eq=eS(t=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SDK_INFO=void 0;var e=eX(),r=e$();t.SDK_INFO={[r.SemanticResourceAttributes.TELEMETRY_SDK_NAME]:"opentelemetry",[r.SemanticResourceAttributes.PROCESS_RUNTIME_NAME]:"browser",[r.SemanticResourceAttributes.TELEMETRY_SDK_LANGUAGE]:r.TelemetrySdkLanguageValues.WEBJS,[r.SemanticResourceAttributes.TELEMETRY_SDK_VERSION]:e.VERSION}}),eQ=eS(t=>{Object.defineProperty(t,"__esModule",{value:!0}),t.unrefTimer=void 0,t.unrefTimer=function(t){}}),eZ=eS(t=>{var e=t&&t.__createBinding||(Object.create?function(t,e,r,n){void 0===n&&(n=r),Object.defineProperty(t,n,{enumerable:!0,get:function(){return e[r]}})}:function(t,e,r,n){void 0===n&&(n=r),t[n]=e[r]}),r=t&&t.__exportStar||function(t,r){for(var n in t)"default"===n||Object.prototype.hasOwnProperty.call(r,n)||e(r,t,n)};Object.defineProperty(t,"__esModule",{value:!0}),r(ek(),t),r(eG(),t),r(ej(),t),r(eF(),t),r(eH(),t),r(eq(),t),r(eQ(),t)}),eJ=eS(t=>{Object.defineProperty(t,"__esModule",{value:!0}),t.addHrTimes=t.isTimeInput=t.isTimeInputHrTime=t.hrTimeToMicroseconds=t.hrTimeToMilliseconds=t.hrTimeToNanoseconds=t.hrTimeToTimeStamp=t.hrTimeDuration=t.timeInputToHrTime=t.hrTime=t.getTimeOrigin=t.millisToHrTime=void 0;var e=eZ();function r(t){return[Math.trunc(t/1e3),Math.round(t%1e3*1e6)]}function n(){let t=e.otperformance.timeOrigin;if("number"!=typeof t){let r=e.otperformance;t=r.timing&&r.timing.fetchStart}return t}function o(t){return a(r(n()),r("number"==typeof t?t:e.otperformance.now()))}function i(t){return Array.isArray(t)&&2===t.length&&"number"==typeof t[0]&&"number"==typeof t[1]}function a(t,e){let r=[t[0]+e[0],t[1]+e[1]];return r[1]>=1e9&&(r[1]-=1e9,r[0]+=1),r}t.millisToHrTime=r,t.getTimeOrigin=n,t.hrTime=o,t.timeInputToHrTime=function(t){if(i(t))return t;if("number"==typeof t)return t<n()?o(t):r(t);if(t instanceof Date)return r(t.getTime());throw TypeError("Invalid input type")},t.hrTimeDuration=function(t,e){let r=e[0]-t[0],n=e[1]-t[1];return n<0&&(r-=1,n+=1e9),[r,n]},t.hrTimeToTimeStamp=function(t){let e=`${"0".repeat(9)}${t[1]}Z`,r=e.substr(e.length-9-1);return new Date(1e3*t[0]).toISOString().replace("000Z",r)},t.hrTimeToNanoseconds=function(t){return 1e9*t[0]+t[1]},t.hrTimeToMilliseconds=function(t){return 1e3*t[0]+t[1]/1e6},t.hrTimeToMicroseconds=function(t){return 1e6*t[0]+t[1]/1e3},t.isTimeInputHrTime=i,t.isTimeInput=function(t){return i(t)||"number"==typeof t||t instanceof Date},t.addHrTimes=a}),e0=eS(t=>{Object.defineProperty(t,"__esModule",{value:!0})}),e1=eS(t=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ExportResultCode=void 0,function(t){t[t.SUCCESS=0]="SUCCESS",t[t.FAILED=1]="FAILED"}(t.ExportResultCode||(t.ExportResultCode={}))}),e2=eS(t=>{Object.defineProperty(t,"__esModule",{value:!0}),t.CompositePropagator=void 0;var e=(eP(),eR(eL));t.CompositePropagator=class{constructor(t={}){var e;this._propagators=null!=(e=t.propagators)?e:[],this._fields=Array.from(new Set(this._propagators.map(t=>"function"==typeof t.fields?t.fields():[]).reduce((t,e)=>t.concat(e),[])))}inject(t,r,n){for(let o of this._propagators)try{o.inject(t,r,n)}catch(t){e.diag.warn(`Failed to inject with ${o.constructor.name}. Err: ${t.message}`)}}extract(t,r,n){return this._propagators.reduce((t,o)=>{try{return o.extract(t,r,n)}catch(t){e.diag.warn(`Failed to inject with ${o.constructor.name}. Err: ${t.message}`)}return t},t)}fields(){return this._fields.slice()}}}),e3=eS(t=>{Object.defineProperty(t,"__esModule",{value:!0}),t.validateValue=t.validateKey=void 0;var e="[_0-9a-z-*/]",r=`[a-z]${e}{0,255}`,n=`[a-z0-9]${e}{0,240}@[a-z]${e}{0,13}`,o=RegExp(`^(?:${r}|${n})$`),i=/^[ -~]{0,255}[!-~]$/,a=/,|=/;t.validateKey=function(t){return o.test(t)},t.validateValue=function(t){return i.test(t)&&!a.test(t)}}),e4=eS(t=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceState=void 0;var e=e3();t.TraceState=class t{constructor(t){this._internalState=new Map,t&&this._parse(t)}set(t,e){let r=this._clone();return r._internalState.has(t)&&r._internalState.delete(t),r._internalState.set(t,e),r}unset(t){let e=this._clone();return e._internalState.delete(t),e}get(t){return this._internalState.get(t)}serialize(){return this._keys().reduce((t,e)=>(t.push(e+"="+this.get(e)),t),[]).join(",")}_parse(t){t.length>512||(this._internalState=t.split(",").reverse().reduce((t,r)=>{let n=r.trim(),o=n.indexOf("=");if(-1!==o){let i=n.slice(0,o),a=n.slice(o+1,r.length);(0,e.validateKey)(i)&&(0,e.validateValue)(a)&&t.set(i,a)}return t},new Map),this._internalState.size>32&&(this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,32))))}_keys(){return Array.from(this._internalState.keys()).reverse()}_clone(){let e=new t;return e._internalState=new Map(this._internalState),e}}}),e8=eS(t=>{Object.defineProperty(t,"__esModule",{value:!0}),t.W3CTraceContextPropagator=t.parseTraceParent=t.TRACE_STATE_HEADER=t.TRACE_PARENT_HEADER=void 0;var e=(eP(),eR(eL)),r=eC(),n=e4();t.TRACE_PARENT_HEADER="traceparent",t.TRACE_STATE_HEADER="tracestate";var o=RegExp("^\\s?((?!ff)[\\da-f]{2})-((?![0]{32})[\\da-f]{32})-((?![0]{16})[\\da-f]{16})-([\\da-f]{2})(-.*)?\\s?$");function i(t){let e=o.exec(t);return!e||"00"===e[1]&&e[5]?null:{traceId:e[2],spanId:e[3],traceFlags:parseInt(e[4],16)}}t.parseTraceParent=i;var a=class{inject(n,o,i){let a=e.trace.getSpanContext(n);if(!a||(0,r.isTracingSuppressed)(n)||!(0,e.isSpanContextValid)(a))return;let s=`00-${a.traceId}-${a.spanId}-0${Number(a.traceFlags||e.TraceFlags.NONE).toString(16)}`;i.set(o,t.TRACE_PARENT_HEADER,s),a.traceState&&i.set(o,t.TRACE_STATE_HEADER,a.traceState.serialize())}extract(r,o,a){let s=a.get(o,t.TRACE_PARENT_HEADER);if(!s)return r;let u=Array.isArray(s)?s[0]:s;if("string"!=typeof u)return r;let l=i(u);if(!l)return r;l.isRemote=!0;let c=a.get(o,t.TRACE_STATE_HEADER);if(c){let t=Array.isArray(c)?c.join(","):c;l.traceState=new n.TraceState("string"==typeof t?t:void 0)}return e.trace.setSpanContext(r,l)}fields(){return[t.TRACE_PARENT_HEADER,t.TRACE_STATE_HEADER]}};t.W3CTraceContextPropagator=a}),e6=eS(t=>{Object.defineProperty(t,"__esModule",{value:!0})}),e5=eS(t=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getRPCMetadata=t.deleteRPCMetadata=t.setRPCMetadata=t.RPCType=void 0;var e=(0,(eP(),eR(eL)).createContextKey)("OpenTelemetry SDK Context Key RPC_METADATA");(t.RPCType||(t.RPCType={})).HTTP="http",t.setRPCMetadata=function(t,r){return t.setValue(e,r)},t.deleteRPCMetadata=function(t){return t.deleteValue(e)},t.getRPCMetadata=function(t){return t.getValue(e)}}),e7=eS(t=>{Object.defineProperty(t,"__esModule",{value:!0}),t.AlwaysOffSampler=void 0;var e=(eP(),eR(eL));t.AlwaysOffSampler=class{shouldSample(){return{decision:e.SamplingDecision.NOT_RECORD}}toString(){return"AlwaysOffSampler"}}}),e9=eS(t=>{Object.defineProperty(t,"__esModule",{value:!0}),t.AlwaysOnSampler=void 0;var e=(eP(),eR(eL));t.AlwaysOnSampler=class{shouldSample(){return{decision:e.SamplingDecision.RECORD_AND_SAMPLED}}toString(){return"AlwaysOnSampler"}}}),rt=eS(t=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ParentBasedSampler=void 0;var e=(eP(),eR(eL)),r=eU(),n=e7(),o=e9();t.ParentBasedSampler=class{constructor(t){var e,i,a,s;this._root=t.root,this._root||((0,r.globalErrorHandler)(Error("ParentBasedSampler must have a root sampler configured")),this._root=new o.AlwaysOnSampler),this._remoteParentSampled=null!=(e=t.remoteParentSampled)?e:new o.AlwaysOnSampler,this._remoteParentNotSampled=null!=(i=t.remoteParentNotSampled)?i:new n.AlwaysOffSampler,this._localParentSampled=null!=(a=t.localParentSampled)?a:new o.AlwaysOnSampler,this._localParentNotSampled=null!=(s=t.localParentNotSampled)?s:new n.AlwaysOffSampler}shouldSample(t,r,n,o,i,a){let s=e.trace.getSpanContext(t);return s&&(0,e.isSpanContextValid)(s)?s.isRemote?s.traceFlags&e.TraceFlags.SAMPLED?this._remoteParentSampled.shouldSample(t,r,n,o,i,a):this._remoteParentNotSampled.shouldSample(t,r,n,o,i,a):s.traceFlags&e.TraceFlags.SAMPLED?this._localParentSampled.shouldSample(t,r,n,o,i,a):this._localParentNotSampled.shouldSample(t,r,n,o,i,a):this._root.shouldSample(t,r,n,o,i,a)}toString(){return`ParentBased{root=${this._root.toString()}, remoteParentSampled=${this._remoteParentSampled.toString()}, remoteParentNotSampled=${this._remoteParentNotSampled.toString()}, localParentSampled=${this._localParentSampled.toString()}, localParentNotSampled=${this._localParentNotSampled.toString()}}`}}}),re=eS(t=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceIdRatioBasedSampler=void 0;var e=(eP(),eR(eL));t.TraceIdRatioBasedSampler=class{constructor(t=0){this._ratio=t,this._ratio=this._normalize(t),this._upperBound=Math.floor(0xffffffff*this._ratio)}shouldSample(t,r){return{decision:(0,e.isValidTraceId)(r)&&this._accumulate(r)<this._upperBound?e.SamplingDecision.RECORD_AND_SAMPLED:e.SamplingDecision.NOT_RECORD}}toString(){return`TraceIdRatioBased{${this._ratio}}`}_normalize(t){return"number"!=typeof t||isNaN(t)?0:t>=1?1:t<=0?0:t}_accumulate(t){let e=0;for(let r=0;r<t.length/8;r++){let n=8*r;e=(e^parseInt(t.slice(n,n+8),16))>>>0}return e}}}),rr=eS(t=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isPlainObject=void 0;var e,r,n=Function.prototype.toString,o=n.call(Object),i=(e=Object.getPrototypeOf,r=Object,function(t){return e(r(t))}),a=Object.prototype,s=a.hasOwnProperty,u=Symbol?Symbol.toStringTag:void 0,l=a.toString;t.isPlainObject=function(t){var e,r,a;if(null==(e=t)||"object"!=typeof e||"[object Object]"!==(null==(r=t)?void 0===r?"[object Undefined]":"[object Null]":u&&u in Object(r)?function(t){let e=s.call(t,u),r=t[u],n=!1;try{t[u]=void 0,n=!0}catch{}let o=l.call(t);return n&&(e?t[u]=r:delete t[u]),o}(r):(a=r,l.call(a))))return!1;let c=i(t);if(null===c)return!0;let p=s.call(c,"constructor")&&c.constructor;return"function"==typeof p&&p instanceof p&&n.call(p)===o}}),rn=eS(t=>{Object.defineProperty(t,"__esModule",{value:!0}),t.merge=void 0;var e=rr();function r(t){return o(t)?t.slice():t}function n(t,e,r){let n=r.get(t[e])||[];for(let r=0,o=n.length;r<o;r++){let o=n[r];if(o.key===e&&o.obj===t)return!0}return!1}function o(t){return Array.isArray(t)}function i(t){return"function"==typeof t}function a(t){return!s(t)&&!o(t)&&!i(t)&&"object"==typeof t}function s(t){return"string"==typeof t||"number"==typeof t||"boolean"==typeof t||typeof t>"u"||t instanceof Date||t instanceof RegExp||null===t}t.merge=function(...t){let u=t.shift(),l=new WeakMap;for(;t.length>0;)u=function t(u,l,c=0,p){let f;if(!(c>20)){if(c++,s(u)||s(l)||i(l))f=r(l);else if(o(u)){if(f=u.slice(),o(l))for(let t=0,e=l.length;t<e;t++)f.push(r(l[t]));else if(a(l)){let t=Object.keys(l);for(let e=0,n=t.length;e<n;e++){let n=t[e];f[n]=r(l[n])}}}else if(a(u))if(a(l)){var h,d;if(h=u,d=l,!(0,e.isPlainObject)(h)||!(0,e.isPlainObject)(d))return l;f=Object.assign({},u);let r=Object.keys(l);for(let e=0,o=r.length;e<o;e++){let o=r[e],i=l[o];if(s(i))typeof i>"u"?delete f[o]:f[o]=i;else{let e=f[o];if(n(u,o,p)||n(l,o,p))delete f[o];else{if(a(e)&&a(i)){let t=p.get(e)||[],r=p.get(i)||[];t.push({obj:u,key:o}),r.push({obj:l,key:o}),p.set(e,t),p.set(i,r)}f[o]=t(f[o],i,c,p)}}}}else f=l;return f}}(u,t.shift(),0,l);return u}}),ro=eS(t=>{Object.defineProperty(t,"__esModule",{value:!0}),t.callWithTimeout=t.TimeoutError=void 0;var e=class t extends Error{constructor(e){super(e),Object.setPrototypeOf(this,t.prototype)}};t.TimeoutError=e,t.callWithTimeout=function(t,r){let n;return Promise.race([t,new Promise(function(t,o){n=setTimeout(function(){o(new e("Operation timed out."))},r)})]).then(t=>(clearTimeout(n),t),t=>{throw clearTimeout(n),t})}}),ri=eS(t=>{function e(t,e){return"string"==typeof e?t===e:!!t.match(e)}Object.defineProperty(t,"__esModule",{value:!0}),t.isUrlIgnored=t.urlMatches=void 0,t.urlMatches=e,t.isUrlIgnored=function(t,r){if(!r)return!1;for(let n of r)if(e(t,n))return!0;return!1}}),ra=eS(t=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isWrapped=void 0,t.isWrapped=function(t){return"function"==typeof t&&"function"==typeof t.__original&&"function"==typeof t.__unwrap&&!0===t.__wrapped}}),rs=eS(t=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Deferred=void 0,t.Deferred=class{constructor(){this._promise=new Promise((t,e)=>{this._resolve=t,this._reject=e})}get promise(){return this._promise}resolve(t){this._resolve(t)}reject(t){this._reject(t)}}}),ru=eS(t=>{Object.defineProperty(t,"__esModule",{value:!0}),t.BindOnceFuture=void 0;var e=rs();t.BindOnceFuture=class{constructor(t,r){this._callback=t,this._that=r,this._isCalled=!1,this._deferred=new e.Deferred}get isCalled(){return this._isCalled}get promise(){return this._deferred.promise}call(...t){if(!this._isCalled){this._isCalled=!0;try{Promise.resolve(this._callback.call(this._that,...t)).then(t=>this._deferred.resolve(t),t=>this._deferred.reject(t))}catch(t){this._deferred.reject(t)}}return this._deferred.promise}}}),rl=eS(t=>{Object.defineProperty(t,"__esModule",{value:!0}),t._export=void 0;var e=(eP(),eR(eL)),r=eC();t._export=function(t,n){return new Promise(o=>{e.context.with((0,r.suppressTracing)(e.context.active()),()=>{t.export(n,t=>{o(t)})})})}}),rc=eS(t=>{var e=t&&t.__createBinding||(Object.create?function(t,e,r,n){void 0===n&&(n=r),Object.defineProperty(t,n,{enumerable:!0,get:function(){return e[r]}})}:function(t,e,r,n){void 0===n&&(n=r),t[n]=e[r]}),r=t&&t.__exportStar||function(t,r){for(var n in t)"default"===n||Object.prototype.hasOwnProperty.call(r,n)||e(r,t,n)};Object.defineProperty(t,"__esModule",{value:!0}),t.internal=t.baggageUtils=void 0,r(ew(),t),r(eM(),t),r(eD(),t),r(eU(),t),r(ex(),t),r(eJ(),t),r(e0(),t),r(e1(),t),t.baggageUtils=eN(),r(eZ(),t),r(e2(),t),r(e8(),t),r(e6(),t),r(e5(),t),r(e7(),t),r(e9(),t),r(rt(),t),r(re(),t),r(eC(),t),r(e4(),t),r(eV(),t),r(rn(),t),r(eB(),t),r(ro(),t),r(ri(),t),r(ra(),t),r(ru(),t),r(eX(),t),t.internal={_export:rl()._export}}),rp=eS(t=>{Object.defineProperty(t,"__esModule",{value:!0}),t.disableInstrumentations=t.enableInstrumentations=t.parseInstrumentationOptions=void 0,t.parseInstrumentationOptions=function t(e=[]){let r=[];for(let n=0,o=e.length;n<o;n++){let o=e[n];if(Array.isArray(o)){let e=t(o);r=r.concat(e.instrumentations)}else"function"==typeof o?r.push(new o):o.instrumentationName&&r.push(o)}return{instrumentations:r}},t.enableInstrumentations=function(t,e,r){for(let n=0,o=t.length;n<o;n++){let o=t[n];e&&o.setTracerProvider(e),r&&o.setMeterProvider(r),o.getConfig().enabled||o.enable()}},t.disableInstrumentations=function(t){t.forEach(t=>t.disable())}}),rf=eS(t=>{Object.defineProperty(t,"__esModule",{value:!0}),t.registerInstrumentations=void 0;var e=(eP(),eR(eL)),r=rp();t.registerInstrumentations=function(t){let{instrumentations:n}=(0,r.parseInstrumentationOptions)(t.instrumentations),o=t.tracerProvider||e.trace.getTracerProvider(),i=t.meterProvider||e.metrics.getMeterProvider();return(0,r.enableInstrumentations)(n,o,i),()=>{(0,r.disableInstrumentations)(n)}}}),rh={},rd=ev(()=>{eA(rh,ee)}),r_={},rE=ev(()=>{eA(r_,en)}),rg=eS(t=>{Object.defineProperty(t,"__esModule",{value:!0}),t.AbstractAsyncHooksContextManager=void 0;var e=(rE(),eR(r_)),r=["addListener","on","once","prependListener","prependOnceListener"];t.AbstractAsyncHooksContextManager=class{constructor(){this._kOtListeners=Symbol("OtListeners"),this._wrapped=!1}bind(t,r){return r instanceof e.EventEmitter?this._bindEventEmitter(t,r):"function"==typeof r?this._bindFunction(t,r):r}_bindFunction(t,e){let r=this,n=function(...n){return r.with(t,()=>e.apply(this,n))};return Object.defineProperty(n,"length",{enumerable:!1,configurable:!0,writable:!1,value:e.length}),n}_bindEventEmitter(t,e){return void 0!==this._getPatchMap(e)||(this._createPatchMap(e),r.forEach(r=>{void 0!==e[r]&&(e[r]=this._patchAddListener(e,e[r],t))}),"function"==typeof e.removeListener&&(e.removeListener=this._patchRemoveListener(e,e.removeListener)),"function"==typeof e.off&&(e.off=this._patchRemoveListener(e,e.off)),"function"==typeof e.removeAllListeners&&(e.removeAllListeners=this._patchRemoveAllListeners(e,e.removeAllListeners))),e}_patchRemoveListener(t,e){let r=this;return function(n,o){var i;let a=null==(i=r._getPatchMap(t))?void 0:i[n];if(void 0===a)return e.call(this,n,o);let s=a.get(o);return e.call(this,n,s||o)}}_patchRemoveAllListeners(t,e){let r=this;return function(n){let o=r._getPatchMap(t);return void 0!==o&&(0==arguments.length?r._createPatchMap(t):void 0!==o[n]&&delete o[n]),e.apply(this,arguments)}}_patchAddListener(t,e,r){let n=this;return function(o,i){if(n._wrapped)return e.call(this,o,i);let a=n._getPatchMap(t);void 0===a&&(a=n._createPatchMap(t));let s=a[o];void 0===s&&(s=new WeakMap,a[o]=s);let u=n.bind(r,i);s.set(i,u),n._wrapped=!0;try{return e.call(this,o,u)}finally{n._wrapped=!1}}}_createPatchMap(t){let e=Object.create(null);return t[this._kOtListeners]=e,e}_getPatchMap(t){return t[this._kOtListeners]}}}),rT=eS(t=>{Object.defineProperty(t,"__esModule",{value:!0}),t.AsyncLocalStorageContextManager=void 0;var e=(eP(),eR(eL)),r=(rd(),eR(rh)),n=rg();t.AsyncLocalStorageContextManager=class extends n.AbstractAsyncHooksContextManager{constructor(){super(),this._asyncLocalStorage=new r.AsyncLocalStorage}active(){var t;return null!=(t=this._asyncLocalStorage.getStore())?t:e.ROOT_CONTEXT}with(t,e,r,...n){let o=null==r?e:e.bind(r);return this._asyncLocalStorage.run(t,o,...n)}enable(){return this}disable(){return this._asyncLocalStorage.disable(),this}}}),ry=eS(t=>{function e(t,e){return{key:t,value:r(e)}}function r(t){let n=typeof t;return"string"===n?{stringValue:t}:"number"===n?Number.isInteger(t)?{intValue:t}:{doubleValue:t}:"boolean"===n?{boolValue:t}:t instanceof Uint8Array?{bytesValue:t}:Array.isArray(t)?{arrayValue:{values:t.map(r)}}:"object"===n&&null!=t?{kvlistValue:{values:Object.entries(t).map(([t,r])=>e(t,r))}}:{}}Object.defineProperty(t,"__esModule",{value:!0}),t.toAnyValue=t.toKeyValue=t.toAttributes=void 0,t.toAttributes=function(t){return Object.keys(t).map(r=>e(r,t[r]))},t.toKeyValue=e,t.toAnyValue=r}),rm=eS(t=>{Object.defineProperty(t,"__esModule",{value:!0}),t.toOtlpSpanEvent=t.toOtlpLink=t.sdkSpanToOtlpSpan=void 0;var e=ry();function r(t,r){var n;return{attributes:t.attributes?(0,e.toAttributes)(t.attributes):[],spanId:r.encodeSpanContext(t.context.spanId),traceId:r.encodeSpanContext(t.context.traceId),traceState:null==(n=t.context.traceState)?void 0:n.serialize(),droppedAttributesCount:t.droppedAttributesCount||0}}function n(t,r){return{attributes:t.attributes?(0,e.toAttributes)(t.attributes):[],name:t.name,timeUnixNano:r.encodeHrTime(t.time),droppedAttributesCount:t.droppedAttributesCount||0}}t.sdkSpanToOtlpSpan=function(t,o){var i;let a=t.spanContext(),s=t.status;return{traceId:o.encodeSpanContext(a.traceId),spanId:o.encodeSpanContext(a.spanId),parentSpanId:o.encodeOptionalSpanContext(t.parentSpanId),traceState:null==(i=a.traceState)?void 0:i.serialize(),name:t.name,kind:null==t.kind?0:t.kind+1,startTimeUnixNano:o.encodeHrTime(t.startTime),endTimeUnixNano:o.encodeHrTime(t.endTime),attributes:(0,e.toAttributes)(t.attributes),droppedAttributesCount:t.droppedAttributesCount,events:t.events.map(t=>n(t,o)),droppedEventsCount:t.droppedEventsCount,status:{code:s.code,message:s.message},links:t.links.map(t=>r(t,o)),droppedLinksCount:t.droppedLinksCount}},t.toOtlpLink=r,t.toOtlpSpanEvent=n}),rv=eS(t=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getOtlpEncoder=t.encodeAsString=t.encodeAsLongBits=t.toLongBits=t.hrTimeToNanos=void 0;var e=rc(),r=BigInt(1e9);function n(t){return BigInt(t[0])*r+BigInt(t[1])}function o(t){return{low:Number(BigInt.asUintN(32,t)),high:Number(BigInt.asUintN(32,t>>BigInt(32)))}}function i(t){return o(n(t))}function a(t){return n(t).toString()}t.hrTimeToNanos=n,t.toLongBits=o,t.encodeAsLongBits=i,t.encodeAsString=a;var s="u">typeof BigInt?a:e.hrTimeToNanoseconds;function u(t){return t}function l(t){if(void 0!==t)return(0,e.hexToBase64)(t)}var c={encodeHrTime:i,encodeSpanContext:e.hexToBase64,encodeOptionalSpanContext:l};t.getOtlpEncoder=function(t){var r,n;if(void 0===t)return c;let o=null==(r=t.useLongBits)||r,a=null!=(n=t.useHex)&&n;return{encodeHrTime:o?i:s,encodeSpanContext:a?u:e.hexToBase64,encodeOptionalSpanContext:a?u:l}}}),rS=eS(t=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createExportTraceServiceRequest=void 0;var e=ry(),r=rm(),n=rv();t.createExportTraceServiceRequest=function(t,o){return{resourceSpans:function(t,n){let o=function(t){let e=new Map;for(let r of t){let t=e.get(r.resource);t||(t=new Map,e.set(r.resource,t));let n=`${r.instrumentationLibrary.name}@${r.instrumentationLibrary.version||""}:${r.instrumentationLibrary.schemaUrl||""}`,o=t.get(n);o||(o=[],t.set(n,o)),o.push(r)}return e}(t),i=[],a=o.entries(),s=a.next();for(;!s.done;){let[t,o]=s.value,u=[],l=o.values(),c=l.next();for(;!c.done;){let t=c.value;if(t.length>0){let{name:e,version:o,schemaUrl:i}=t[0].instrumentationLibrary,a=t.map(t=>(0,r.sdkSpanToOtlpSpan)(t,n));u.push({scope:{name:e,version:o},spans:a,schemaUrl:i})}c=l.next()}let p={resource:{attributes:(0,e.toAttributes)(t.attributes),droppedAttributesCount:0},scopeSpans:u,schemaUrl:void 0};i.push(p),s=a.next()}return i}(t,(0,n.getOtlpEncoder)(o))}}}),rb=eS(t=>{Object.defineProperty(t,"__esModule",{value:!0}),t.parseRetryAfterToMills=t.isExportRetryable=t.invalidTimeout=t.configureExporterTimeout=t.appendRootPathToUrlIfNeeded=t.appendResourcePathToUrl=t.parseHeaders=t.DEFAULT_EXPORT_BACKOFF_MULTIPLIER=t.DEFAULT_EXPORT_MAX_BACKOFF=t.DEFAULT_EXPORT_INITIAL_BACKOFF=t.DEFAULT_EXPORT_MAX_ATTEMPTS=void 0;var e=(eP(),eR(eL)),r=rc();function n(t,r){return e.diag.warn("Timeout must be greater than 0",t),r}t.DEFAULT_EXPORT_MAX_ATTEMPTS=5,t.DEFAULT_EXPORT_INITIAL_BACKOFF=1e3,t.DEFAULT_EXPORT_MAX_BACKOFF=5e3,t.DEFAULT_EXPORT_BACKOFF_MULTIPLIER=1.5,t.parseHeaders=function(t={}){let r={};return Object.entries(t).forEach(([t,n])=>{"u">typeof n?r[t]=String(n):e.diag.warn(`Header "${t}" has wrong value and will be ignored`)}),r},t.appendResourcePathToUrl=function(t,e){return t.endsWith("/")||(t+="/"),t+e},t.appendRootPathToUrlIfNeeded=function(t){try{let e=new URL(t);return""===e.pathname&&(e.pathname=e.pathname+"/"),e.toString()}catch{return e.diag.warn(`Could not parse export URL: '${t}'`),t}},t.configureExporterTimeout=function(t){var e;let o;return"number"==typeof t?t<=0?n(t,1e4):t:(o=Number(null!=(e=(0,r.getEnv)().OTEL_EXPORTER_OTLP_TRACES_TIMEOUT)?e:(0,r.getEnv)().OTEL_EXPORTER_OTLP_TIMEOUT))<=0?n(o,1e4):o},t.invalidTimeout=n,t.isExportRetryable=function(t){return[429,502,503,504].includes(t)},t.parseRetryAfterToMills=function(t){if(null==t)return -1;let e=Number.parseInt(t,10);if(Number.isInteger(e))return e>0?1e3*e:-1;let r=new Date(t).getTime()-Date.now();return r>=0?r:0}}),rA=eS(t=>{Object.defineProperty(t,"__esModule",{value:!0}),t.OTLPExporterBase=void 0;var e=(eP(),eR(eL)),r=rc(),n=rb();t.OTLPExporterBase=class{constructor(t={}){this._sendingPromises=[],this.url=this.getDefaultUrl(t),"string"==typeof t.hostname&&(this.hostname=t.hostname),this.shutdown=this.shutdown.bind(this),this._shutdownOnce=new r.BindOnceFuture(this._shutdown,this),this._concurrencyLimit="number"==typeof t.concurrencyLimit?t.concurrencyLimit:30,this.timeoutMillis=(0,n.configureExporterTimeout)(t.timeoutMillis),this.onInit(t)}export(t,e){return this._shutdownOnce.isCalled?void e({code:r.ExportResultCode.FAILED,error:Error("Exporter has been shutdown")}):this._sendingPromises.length>=this._concurrencyLimit?void e({code:r.ExportResultCode.FAILED,error:Error("Concurrent export limit reached")}):void this._export(t).then(()=>{e({code:r.ExportResultCode.SUCCESS})}).catch(t=>{e({code:r.ExportResultCode.FAILED,error:t})})}_export(t){return new Promise((r,n)=>{try{e.diag.debug("items to be sent",t),this.send(t,r,n)}catch(t){n(t)}})}shutdown(){return this._shutdownOnce.call()}forceFlush(){return Promise.all(this._sendingPromises).then(()=>{})}_shutdown(){return e.diag.debug("shutdown started"),this.onShutdown(),this.forceFlush()}}}),rO=eS((t,e)=>{e.exports=function(t,e){for(var r=Array(arguments.length-1),n=0,o=2,i=!0;o<arguments.length;)r[n++]=arguments[o++];return new Promise(function(o,a){r[n]=function(t){if(i)if(i=!1,t)a(t);else{for(var e=Array(arguments.length-1),r=0;r<e.length;)e[r++]=arguments[r];o.apply(null,e)}};try{t.apply(e||null,r)}catch(t){i&&(i=!1,a(t))}})}}),rR=eS(t=>{t.length=function(t){var e=t.length;if(!e)return 0;for(var r=0;--e%4>1&&"="===t.charAt(e);)++r;return Math.ceil(3*t.length)/4-r};var e,r=Array(64),n=Array(123);for(e=0;e<64;)n[r[e]=e<26?e+65:e<52?e+71:e<62?e-4:e-59|43]=e++;t.encode=function(t,e,n){for(var o,i=null,a=[],s=0,u=0;e<n;){var l=t[e++];switch(u){case 0:a[s++]=r[l>>2],o=(3&l)<<4,u=1;break;case 1:a[s++]=r[o|l>>4],o=(15&l)<<2,u=2;break;case 2:a[s++]=r[o|l>>6],a[s++]=r[63&l],u=0}s>8191&&((i||(i=[])).push(String.fromCharCode.apply(String,a)),s=0)}return u&&(a[s++]=r[o],a[s++]=61,1===u&&(a[s++]=61)),i?(s&&i.push(String.fromCharCode.apply(String,a.slice(0,s))),i.join("")):String.fromCharCode.apply(String,a.slice(0,s))};var o="invalid encoding";t.decode=function(t,e,r){for(var i,a=r,s=0,u=0;u<t.length;){var l=t.charCodeAt(u++);if(61===l&&s>1)break;if(void 0===(l=n[l]))throw Error(o);switch(s){case 0:i=l,s=1;break;case 1:e[r++]=i<<2|(48&l)>>4,i=l,s=2;break;case 2:e[r++]=(15&i)<<4|(60&l)>>2,i=l,s=3;break;case 3:e[r++]=(3&i)<<6|l,s=0}}if(1===s)throw Error(o);return r-a},t.test=function(t){return/^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/.test(t)}}),rL=eS((t,e)=>{function r(){this._listeners={}}e.exports=r,r.prototype.on=function(t,e,r){return(this._listeners[t]||(this._listeners[t]=[])).push({fn:e,ctx:r||this}),this},r.prototype.off=function(t,e){if(void 0===t)this._listeners={};else if(void 0===e)this._listeners[t]=[];else for(var r=this._listeners[t],n=0;n<r.length;)r[n].fn===e?r.splice(n,1):++n;return this},r.prototype.emit=function(t){var e=this._listeners[t];if(e){for(var r=[],n=1;n<arguments.length;)r.push(arguments[n++]);for(n=0;n<e.length;)e[n].fn.apply(e[n++].ctx,r)}return this}}),rP=eS((t,e)=>{function r(t){return"u">typeof Float32Array?function(){var e=new Float32Array([-0]),r=new Uint8Array(e.buffer),n=128===r[3];function o(t,n,o){e[0]=t,n[o]=r[0],n[o+1]=r[1],n[o+2]=r[2],n[o+3]=r[3]}function i(t,n,o){e[0]=t,n[o]=r[3],n[o+1]=r[2],n[o+2]=r[1],n[o+3]=r[0]}function a(t,n){return r[0]=t[n],r[1]=t[n+1],r[2]=t[n+2],r[3]=t[n+3],e[0]}function s(t,n){return r[3]=t[n],r[2]=t[n+1],r[1]=t[n+2],r[0]=t[n+3],e[0]}t.writeFloatLE=n?o:i,t.writeFloatBE=n?i:o,t.readFloatLE=n?a:s,t.readFloatBE=n?s:a}():function(){function e(t,e,r,n){var o=+(e<0);if(o&&(e=-e),0===e)t(1/e>0?0:0x80000000,r,n);else if(isNaN(e))t(0x7fc00000,r,n);else if(e>34028234663852886e22)t((o<<31|0x7f800000)>>>0,r,n);else if(e<11754943508222875e-54)t((o<<31|Math.round(e/1401298464324817e-60))>>>0,r,n);else{var i=Math.floor(Math.log(e)/Math.LN2),a=8388607&Math.round(e*Math.pow(2,-i)*8388608);t((o<<31|i+127<<23|a)>>>0,r,n)}}function r(t,e,r){var n=t(e,r),o=(n>>31)*2+1,i=n>>>23&255,a=8388607&n;return 255===i?a?NaN:1/0*o:0===i?1401298464324817e-60*o*a:o*Math.pow(2,i-150)*(a+8388608)}t.writeFloatLE=e.bind(null,n),t.writeFloatBE=e.bind(null,o),t.readFloatLE=r.bind(null,i),t.readFloatBE=r.bind(null,a)}(),"u">typeof Float64Array?function(){var e=new Float64Array([-0]),r=new Uint8Array(e.buffer),n=128===r[7];function o(t,n,o){e[0]=t,n[o]=r[0],n[o+1]=r[1],n[o+2]=r[2],n[o+3]=r[3],n[o+4]=r[4],n[o+5]=r[5],n[o+6]=r[6],n[o+7]=r[7]}function i(t,n,o){e[0]=t,n[o]=r[7],n[o+1]=r[6],n[o+2]=r[5],n[o+3]=r[4],n[o+4]=r[3],n[o+5]=r[2],n[o+6]=r[1],n[o+7]=r[0]}function a(t,n){return r[0]=t[n],r[1]=t[n+1],r[2]=t[n+2],r[3]=t[n+3],r[4]=t[n+4],r[5]=t[n+5],r[6]=t[n+6],r[7]=t[n+7],e[0]}function s(t,n){return r[7]=t[n],r[6]=t[n+1],r[5]=t[n+2],r[4]=t[n+3],r[3]=t[n+4],r[2]=t[n+5],r[1]=t[n+6],r[0]=t[n+7],e[0]}t.writeDoubleLE=n?o:i,t.writeDoubleBE=n?i:o,t.readDoubleLE=n?a:s,t.readDoubleBE=n?s:a}():function(){function e(t,e,r,n,o,i){var a,s=+(n<0);if(s&&(n=-n),0===n)t(0,o,i+e),t(1/n>0?0:0x80000000,o,i+r);else if(isNaN(n))t(0,o,i+e),t(0x7ff80000,o,i+r);else if(n>17976931348623157e292)t(0,o,i+e),t((s<<31|0x7ff00000)>>>0,o,i+r);else if(n<22250738585072014e-324)t((a=n/5e-324)>>>0,o,i+e),t((s<<31|a/0x100000000)>>>0,o,i+r);else{var u=Math.floor(Math.log(n)/Math.LN2);1024===u&&(u=1023),t(0x10000000000000*(a=n*Math.pow(2,-u))>>>0,o,i+e),t((s<<31|u+1023<<20|1048576*a&1048575)>>>0,o,i+r)}}function r(t,e,r,n,o){var i=t(n,o+e),a=t(n,o+r),s=(a>>31)*2+1,u=a>>>20&2047,l=0x100000000*(1048575&a)+i;return 2047===u?l?NaN:1/0*s:0===u?5e-324*s*l:s*Math.pow(2,u-1075)*(l+0x10000000000000)}t.writeDoubleLE=e.bind(null,n,0,4),t.writeDoubleBE=e.bind(null,o,4,0),t.readDoubleLE=r.bind(null,i,0,4),t.readDoubleBE=r.bind(null,a,4,0)}(),t}function n(t,e,r){e[r]=255&t,e[r+1]=t>>>8&255,e[r+2]=t>>>16&255,e[r+3]=t>>>24}function o(t,e,r){e[r]=t>>>24,e[r+1]=t>>>16&255,e[r+2]=t>>>8&255,e[r+3]=255&t}function i(t,e){return(t[e]|t[e+1]<<8|t[e+2]<<16|t[e+3]<<24)>>>0}function a(t,e){return(t[e]<<24|t[e+1]<<16|t[e+2]<<8|t[e+3])>>>0}e.exports=r(r)}),rC=eS((t,e)=>{e.exports=function(t){return null}}),rI=eS(t=>{t.length=function(t){for(var e=0,r=0,n=0;n<t.length;++n)(r=t.charCodeAt(n))<128?e+=1:r<2048?e+=2:(64512&r)==55296&&(64512&t.charCodeAt(n+1))==56320?(++n,e+=4):e+=3;return e},t.read=function(t,e,r){if(r-e<1)return"";for(var n,o=null,i=[],a=0;e<r;)(n=t[e++])<128?i[a++]=n:n>191&&n<224?i[a++]=(31&n)<<6|63&t[e++]:n>239&&n<365?(n=((7&n)<<18|(63&t[e++])<<12|(63&t[e++])<<6|63&t[e++])-65536,i[a++]=55296+(n>>10),i[a++]=56320+(1023&n)):i[a++]=(15&n)<<12|(63&t[e++])<<6|63&t[e++],a>8191&&((o||(o=[])).push(String.fromCharCode.apply(String,i)),a=0);return o?(a&&o.push(String.fromCharCode.apply(String,i.slice(0,a))),o.join("")):String.fromCharCode.apply(String,i.slice(0,a))},t.write=function(t,e,r){for(var n,o,i=r,a=0;a<t.length;++a)(n=t.charCodeAt(a))<128?e[r++]=n:(n<2048?e[r++]=n>>6|192:((64512&n)==55296&&(64512&(o=t.charCodeAt(a+1)))==56320?(n=65536+((1023&n)<<10)+(1023&o),++a,e[r++]=n>>18|240,e[r++]=n>>12&63|128):e[r++]=n>>12|224,e[r++]=n>>6&63|128),e[r++]=63&n|128);return r-i}}),rN=eS((t,e)=>{e.exports=function(t,e,r){var n=r||8192,o=n>>>1,i=null,a=n;return function(r){if(r<1||r>o)return t(r);a+r>n&&(i=t(n),a=0);var s=e.call(i,a,a+=r);return 7&a&&(a=(7|a)+1),s}}}),rw=eS((t,e)=>{e.exports=n;var r=rM();function n(t,e){this.lo=t>>>0,this.hi=e>>>0}var o=n.zero=new n(0,0);o.toNumber=function(){return 0},o.zzEncode=o.zzDecode=function(){return this},o.length=function(){return 1};var i=n.zeroHash="\0\0\0\0\0\0\0\0";n.fromNumber=function(t){if(0===t)return o;var e=t<0;e&&(t=-t);var r=t>>>0,i=(t-r)/0x100000000>>>0;return e&&(i=~i>>>0,r=~r>>>0,++r>0xffffffff&&(r=0,++i>0xffffffff&&(i=0))),new n(r,i)},n.from=function(t){if("number"==typeof t)return n.fromNumber(t);if(r.isString(t))if(!r.Long)return n.fromNumber(parseInt(t,10));else t=r.Long.fromString(t);return t.low||t.high?new n(t.low>>>0,t.high>>>0):o},n.prototype.toNumber=function(t){if(!t&&this.hi>>>31){var e=~this.lo+1>>>0,r=~this.hi>>>0;return e||(r=r+1>>>0),-(e+0x100000000*r)}return this.lo+0x100000000*this.hi},n.prototype.toLong=function(t){return r.Long?new r.Long(0|this.lo,0|this.hi,!!t):{low:0|this.lo,high:0|this.hi,unsigned:!!t}};var a=String.prototype.charCodeAt;n.fromHash=function(t){return t===i?o:new n((a.call(t,0)|a.call(t,1)<<8|a.call(t,2)<<16|a.call(t,3)<<24)>>>0,(a.call(t,4)|a.call(t,5)<<8|a.call(t,6)<<16|a.call(t,7)<<24)>>>0)},n.prototype.toHash=function(){return String.fromCharCode(255&this.lo,this.lo>>>8&255,this.lo>>>16&255,this.lo>>>24,255&this.hi,this.hi>>>8&255,this.hi>>>16&255,this.hi>>>24)},n.prototype.zzEncode=function(){var t=this.hi>>31;return this.hi=((this.hi<<1|this.lo>>>31)^t)>>>0,this.lo=(this.lo<<1^t)>>>0,this},n.prototype.zzDecode=function(){var t=-(1&this.lo);return this.lo=((this.lo>>>1|this.hi<<31)^t)>>>0,this.hi=(this.hi>>>1^t)>>>0,this},n.prototype.length=function(){var t=this.lo,e=(this.lo>>>28|this.hi<<4)>>>0,r=this.hi>>>24;return 0===r?0===e?t<16384?t<128?1:2:t<2097152?3:4:e<16384?e<128?5:6:e<2097152?7:8:r<128?9:10}}),rM=eS(t=>{function e(t,e,r){for(var n=Object.keys(e),o=0;o<n.length;++o)void 0!==t[n[o]]&&r||(t[n[o]]=e[n[o]]);return t}function r(t){function r(t,n){if(!(this instanceof r))return new r(t,n);Object.defineProperty(this,"message",{get:function(){return t}}),Error.captureStackTrace?Error.captureStackTrace(this,r):Object.defineProperty(this,"stack",{value:Error().stack||""}),n&&e(this,n)}return r.prototype=Object.create(Error.prototype,{constructor:{value:r,writable:!0,enumerable:!1,configurable:!0},name:{get:function(){return t},set:void 0,enumerable:!1,configurable:!0},toString:{value:function(){return this.name+": "+this.message},writable:!0,enumerable:!1,configurable:!0}}),r}t.asPromise=rO(),t.base64=rR(),t.EventEmitter=rL(),t.float=rP(),t.inquire=rC(),t.utf8=rI(),t.pool=rN(),t.LongBits=rw(),t.isNode=!!("u">typeof global&&global&&global.process&&global.process.versions&&global.process.versions.node),t.global=t.isNode&&global||"u">typeof window&&window||"u">typeof self&&self||t,t.emptyArray=Object.freeze?Object.freeze([]):[],t.emptyObject=Object.freeze?Object.freeze({}):{},t.isInteger=Number.isInteger||function(t){return"number"==typeof t&&isFinite(t)&&Math.floor(t)===t},t.isString=function(t){return"string"==typeof t||t instanceof String},t.isObject=function(t){return t&&"object"==typeof t},t.isset=t.isSet=function(t,e){var r=t[e];return!!(null!=r&&t.hasOwnProperty(e))&&("object"!=typeof r||(Array.isArray(r)?r.length:Object.keys(r).length)>0)},t.Buffer=function(){try{var e=t.inquire("buffer").Buffer;return e.prototype.utf8Write?e:null}catch{return null}}(),t._Buffer_from=null,t._Buffer_allocUnsafe=null,t.newBuffer=function(e){return"number"==typeof e?t.Buffer?t._Buffer_allocUnsafe(e):new t.Array(e):t.Buffer?t._Buffer_from(e):typeof Uint8Array>"u"?e:new Uint8Array(e)},t.Array="u">typeof Uint8Array?Uint8Array:Array,t.Long=t.global.dcodeIO&&t.global.dcodeIO.Long||t.global.Long||t.inquire("long"),t.key2Re=/^true|false|0|1$/,t.key32Re=/^-?(?:0|[1-9][0-9]*)$/,t.key64Re=/^(?:[\\x00-\\xff]{8}|-?(?:0|[1-9][0-9]*))$/,t.longToHash=function(e){return e?t.LongBits.from(e).toHash():t.LongBits.zeroHash},t.longFromHash=function(e,r){var n=t.LongBits.fromHash(e);return t.Long?t.Long.fromBits(n.lo,n.hi,r):n.toNumber(!!r)},t.merge=e,t.lcFirst=function(t){return t.charAt(0).toLowerCase()+t.substring(1)},t.newError=r,t.ProtocolError=r("ProtocolError"),t.oneOfGetter=function(t){for(var e={},r=0;r<t.length;++r)e[t[r]]=1;return function(){for(var t=Object.keys(this),r=t.length-1;r>-1;--r)if(1===e[t[r]]&&void 0!==this[t[r]]&&null!==this[t[r]])return t[r]}},t.oneOfSetter=function(t){return function(e){for(var r=0;r<t.length;++r)t[r]!==e&&delete this[t[r]]}},t.toJSONOptions={longs:String,enums:String,bytes:String,json:!0},t._configure=function(){var e=t.Buffer;if(!e){t._Buffer_from=t._Buffer_allocUnsafe=null;return}t._Buffer_from=e.from!==Uint8Array.from&&e.from||function(t,r){return new e(t,r)},t._Buffer_allocUnsafe=e.allocUnsafe||function(t){return new e(t)}}}),rD=eS((t,e)=>{e.exports=c;var r,n=rM(),o=n.LongBits,i=n.base64,a=n.utf8;function s(t,e,r){this.fn=t,this.len=e,this.next=void 0,this.val=r}function u(){}function l(t){this.head=t.head,this.tail=t.tail,this.len=t.len,this.next=t.states}function c(){this.len=0,this.head=new s(u,0,0),this.tail=this.head,this.states=null}var p=function(){return n.Buffer?function(){return(c.create=function(){return new r})()}:function(){return new c}};function f(t,e,r){e[r]=255&t}function h(t,e){this.len=t,this.next=void 0,this.val=e}function d(t,e,r){for(;t.hi;)e[r++]=127&t.lo|128,t.lo=(t.lo>>>7|t.hi<<25)>>>0,t.hi>>>=7;for(;t.lo>127;)e[r++]=127&t.lo|128,t.lo=t.lo>>>7;e[r++]=t.lo}function _(t,e,r){e[r]=255&t,e[r+1]=t>>>8&255,e[r+2]=t>>>16&255,e[r+3]=t>>>24}c.create=p(),c.alloc=function(t){return new n.Array(t)},n.Array!==Array&&(c.alloc=n.pool(c.alloc,n.Array.prototype.subarray)),c.prototype._push=function(t,e,r){return this.tail=this.tail.next=new s(t,e,r),this.len+=e,this},h.prototype=Object.create(s.prototype),h.prototype.fn=function(t,e,r){for(;t>127;)e[r++]=127&t|128,t>>>=7;e[r]=t},c.prototype.uint32=function(t){return this.len+=(this.tail=this.tail.next=new h((t>>>=0)<128?1:t<16384?2:t<2097152?3:t<0x10000000?4:5,t)).len,this},c.prototype.int32=function(t){return t<0?this._push(d,10,o.fromNumber(t)):this.uint32(t)},c.prototype.sint32=function(t){return this.uint32((t<<1^t>>31)>>>0)},c.prototype.uint64=function(t){var e=o.from(t);return this._push(d,e.length(),e)},c.prototype.int64=c.prototype.uint64,c.prototype.sint64=function(t){var e=o.from(t).zzEncode();return this._push(d,e.length(),e)},c.prototype.bool=function(t){return this._push(f,1,+!!t)},c.prototype.fixed32=function(t){return this._push(_,4,t>>>0)},c.prototype.sfixed32=c.prototype.fixed32,c.prototype.fixed64=function(t){var e=o.from(t);return this._push(_,4,e.lo)._push(_,4,e.hi)},c.prototype.sfixed64=c.prototype.fixed64,c.prototype.float=function(t){return this._push(n.float.writeFloatLE,4,t)},c.prototype.double=function(t){return this._push(n.float.writeDoubleLE,8,t)};var E=n.Array.prototype.set?function(t,e,r){e.set(t,r)}:function(t,e,r){for(var n=0;n<t.length;++n)e[r+n]=t[n]};c.prototype.bytes=function(t){var e=t.length>>>0;if(!e)return this._push(f,1,0);if(n.isString(t)){var r=c.alloc(e=i.length(t));i.decode(t,r,0),t=r}return this.uint32(e)._push(E,e,t)},c.prototype.string=function(t){var e=a.length(t);return e?this.uint32(e)._push(a.write,e,t):this._push(f,1,0)},c.prototype.fork=function(){return this.states=new l(this),this.head=this.tail=new s(u,0,0),this.len=0,this},c.prototype.reset=function(){return this.states?(this.head=this.states.head,this.tail=this.states.tail,this.len=this.states.len,this.states=this.states.next):(this.head=this.tail=new s(u,0,0),this.len=0),this},c.prototype.ldelim=function(){var t=this.head,e=this.tail,r=this.len;return this.reset().uint32(r),r&&(this.tail.next=t.next,this.tail=e,this.len+=r),this},c.prototype.finish=function(){for(var t=this.head.next,e=this.constructor.alloc(this.len),r=0;t;)t.fn(t.val,e,r),r+=t.len,t=t.next;return e},c._configure=function(t){r=t,c.create=p(),r._configure()}}),rx=eS((t,e)=>{e.exports=o;var r=rD();(o.prototype=Object.create(r.prototype)).constructor=o;var n=rM();function o(){r.call(this)}function i(t,e,r){t.length<40?n.utf8.write(t,e,r):e.utf8Write?e.utf8Write(t,r):e.write(t,r)}o._configure=function(){o.alloc=n._Buffer_allocUnsafe,o.writeBytesBuffer=n.Buffer&&n.Buffer.prototype instanceof Uint8Array&&"set"===n.Buffer.prototype.set.name?function(t,e,r){e.set(t,r)}:function(t,e,r){if(t.copy)t.copy(e,r,0,t.length);else for(var n=0;n<t.length;)e[r++]=t[n++]}},o.prototype.bytes=function(t){n.isString(t)&&(t=n._Buffer_from(t,"base64"));var e=t.length>>>0;return this.uint32(e),e&&this._push(o.writeBytesBuffer,e,t),this},o.prototype.string=function(t){var e=n.Buffer.byteLength(t);return this.uint32(e),e&&this._push(i,e,t),this},o._configure()}),rU=eS((t,e)=>{e.exports=s;var r,n=rM(),o=n.LongBits,i=n.utf8;function a(t,e){return RangeError("index out of range: "+t.pos+" + "+(e||1)+" > "+t.len)}function s(t){this.buf=t,this.pos=0,this.len=t.length}var u="u">typeof Uint8Array?function(t){if(t instanceof Uint8Array||Array.isArray(t))return new s(t);throw Error("illegal buffer")}:function(t){if(Array.isArray(t))return new s(t);throw Error("illegal buffer")},l=function(){return n.Buffer?function(t){return(s.create=function(t){return n.Buffer.isBuffer(t)?new r(t):u(t)})(t)}:u};function c(){var t=new o(0,0),e=0;if(this.len-this.pos>4){for(;e<4;++e)if(t.lo=(t.lo|(127&this.buf[this.pos])<<7*e)>>>0,this.buf[this.pos++]<128)return t;if(t.lo=(t.lo|(127&this.buf[this.pos])<<28)>>>0,t.hi=(t.hi|(127&this.buf[this.pos])>>4)>>>0,this.buf[this.pos++]<128)return t;e=0}else{for(;e<3;++e){if(this.pos>=this.len)throw a(this);if(t.lo=(t.lo|(127&this.buf[this.pos])<<7*e)>>>0,this.buf[this.pos++]<128)return t}return t.lo=(t.lo|(127&this.buf[this.pos++])<<7*e)>>>0,t}if(this.len-this.pos>4){for(;e<5;++e)if(t.hi=(t.hi|(127&this.buf[this.pos])<<7*e+3)>>>0,this.buf[this.pos++]<128)return t}else for(;e<5;++e){if(this.pos>=this.len)throw a(this);if(t.hi=(t.hi|(127&this.buf[this.pos])<<7*e+3)>>>0,this.buf[this.pos++]<128)return t}throw Error("invalid varint encoding")}function p(t,e){return(t[e-4]|t[e-3]<<8|t[e-2]<<16|t[e-1]<<24)>>>0}function f(){if(this.pos+8>this.len)throw a(this,8);return new o(p(this.buf,this.pos+=4),p(this.buf,this.pos+=4))}s.create=l(),s.prototype._slice=n.Array.prototype.subarray||n.Array.prototype.slice,s.prototype.uint32=function(){var t=0xffffffff;return function(){if(t=(127&this.buf[this.pos])>>>0,this.buf[this.pos++]<128||(t=(t|(127&this.buf[this.pos])<<7)>>>0,this.buf[this.pos++]<128)||(t=(t|(127&this.buf[this.pos])<<14)>>>0,this.buf[this.pos++]<128)||(t=(t|(127&this.buf[this.pos])<<21)>>>0,this.buf[this.pos++]<128)||(t=(t|(15&this.buf[this.pos])<<28)>>>0,this.buf[this.pos++]<128))return t;if((this.pos+=5)>this.len)throw this.pos=this.len,a(this,10);return t}}(),s.prototype.int32=function(){return 0|this.uint32()},s.prototype.sint32=function(){var t=this.uint32();return t>>>1^-(1&t)|0},s.prototype.bool=function(){return 0!==this.uint32()},s.prototype.fixed32=function(){if(this.pos+4>this.len)throw a(this,4);return p(this.buf,this.pos+=4)},s.prototype.sfixed32=function(){if(this.pos+4>this.len)throw a(this,4);return 0|p(this.buf,this.pos+=4)},s.prototype.float=function(){if(this.pos+4>this.len)throw a(this,4);var t=n.float.readFloatLE(this.buf,this.pos);return this.pos+=4,t},s.prototype.double=function(){if(this.pos+8>this.len)throw a(this,4);var t=n.float.readDoubleLE(this.buf,this.pos);return this.pos+=8,t},s.prototype.bytes=function(){var t=this.uint32(),e=this.pos,r=this.pos+t;if(r>this.len)throw a(this,t);if(this.pos+=t,Array.isArray(this.buf))return this.buf.slice(e,r);if(e===r){var o=n.Buffer;return o?o.alloc(0):new this.buf.constructor(0)}return this._slice.call(this.buf,e,r)},s.prototype.string=function(){var t=this.bytes();return i.read(t,0,t.length)},s.prototype.skip=function(t){if("number"==typeof t){if(this.pos+t>this.len)throw a(this,t);this.pos+=t}else do if(this.pos>=this.len)throw a(this);while(128&this.buf[this.pos++]);return this},s.prototype.skipType=function(t){switch(t){case 0:this.skip();break;case 1:this.skip(8);break;case 2:this.skip(this.uint32());break;case 3:for(;4!=(t=7&this.uint32());)this.skipType(t);break;case 5:this.skip(4);break;default:throw Error("invalid wire type "+t+" at offset "+this.pos)}return this},s._configure=function(t){r=t,s.create=l(),r._configure();var e=n.Long?"toLong":"toNumber";n.merge(s.prototype,{int64:function(){return c.call(this)[e](!1)},uint64:function(){return c.call(this)[e](!0)},sint64:function(){return c.call(this).zzDecode()[e](!1)},fixed64:function(){return f.call(this)[e](!0)},sfixed64:function(){return f.call(this)[e](!1)}})}}),rB=eS((t,e)=>{e.exports=o;var r=rU();(o.prototype=Object.create(r.prototype)).constructor=o;var n=rM();function o(t){r.call(this,t)}o._configure=function(){n.Buffer&&(o.prototype._slice=n.Buffer.prototype.slice)},o.prototype.string=function(){var t=this.uint32();return this.buf.utf8Slice?this.buf.utf8Slice(this.pos,this.pos=Math.min(this.pos+t,this.len)):this.buf.toString("utf-8",this.pos,this.pos=Math.min(this.pos+t,this.len))},o._configure()}),rG=eS((t,e)=>{e.exports=n;var r=rM();function n(t,e,n){if("function"!=typeof t)throw TypeError("rpcImpl must be a function");r.EventEmitter.call(this),this.rpcImpl=t,this.requestDelimited=!!e,this.responseDelimited=!!n}(n.prototype=Object.create(r.EventEmitter.prototype)).constructor=n,n.prototype.rpcCall=function t(e,n,o,i,a){if(!i)throw TypeError("request must be specified");var s=this;if(!a)return r.asPromise(t,s,e,n,o,i);if(!s.rpcImpl)return void setTimeout(function(){a(Error("already ended"))},0);try{return s.rpcImpl(e,n[s.requestDelimited?"encodeDelimited":"encode"](i).finish(),function(t,r){if(t)return s.emit("error",t,e),a(t);if(null===r)return void s.end(!0);if(!(r instanceof o))try{r=o[s.responseDelimited?"decodeDelimited":"decode"](r)}catch(t){return s.emit("error",t,e),a(t)}return s.emit("data",r,e),a(null,r)})}catch(t){s.emit("error",t,e),setTimeout(function(){a(t)},0);return}},n.prototype.end=function(t){return this.rpcImpl&&(t||this.rpcImpl(null,null,null),this.rpcImpl=null,this.emit("end").off()),this}}),rV=eS(t=>{t.Service=rG()}),rk=eS((t,e)=>{e.exports={}}),rj=eS(t=>{function e(){t.util._configure(),t.Writer._configure(t.BufferWriter),t.Reader._configure(t.BufferReader)}t.build="minimal",t.Writer=rD(),t.BufferWriter=rx(),t.Reader=rU(),t.BufferReader=rB(),t.util=rM(),t.rpc=rV(),t.roots=rk(),t.configure=e,e()}),rF=eS((t,e)=>{e.exports=rj()});eP();var rH=eO(rc());eP();var rX=eO(rc()),rK=eO(e$()),rW=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},rz=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},rY=function(){function t(t,e,r,n,o,i,a,s,u,l){void 0===a&&(a=[]),this.attributes={},this.links=[],this.events=[],this._droppedAttributesCount=0,this._droppedEventsCount=0,this._droppedLinksCount=0,this.status={code:eL.SpanStatusCode.UNSET},this.endTime=[0,0],this._ended=!1,this._duration=[-1,-1],this.name=r,this._spanContext=n,this.parentSpanId=i,this.kind=o,this.links=a;var c=Date.now();this._performanceStartTime=rX.otperformance.now(),this._performanceOffset=c-(this._performanceStartTime+(0,rX.getTimeOrigin)()),this._startTimeProvided=null!=s,this.startTime=this._getTime(s??c),this.resource=t.resource,this.instrumentationLibrary=t.instrumentationLibrary,this._spanLimits=t.getSpanLimits(),null!=l&&this.setAttributes(l),this._spanProcessor=t.getActiveSpanProcessor(),this._spanProcessor.onStart(this,e),this._attributeValueLengthLimit=this._spanLimits.attributeValueLengthLimit||0}return t.prototype.spanContext=function(){return this._spanContext},t.prototype.setAttribute=function(t,e){return null==e||this._isSpanEnded()||(0===t.length?eL.diag.warn("Invalid attribute key: "+t):(0,rX.isAttributeValue)(e)?Object.keys(this.attributes).length>=this._spanLimits.attributeCountLimit&&!Object.prototype.hasOwnProperty.call(this.attributes,t)?this._droppedAttributesCount++:this.attributes[t]=this._truncateToSize(e):eL.diag.warn("Invalid attribute value set for key: "+t)),this},t.prototype.setAttributes=function(t){var e,r;try{for(var n=rW(Object.entries(t)),o=n.next();!o.done;o=n.next()){var i=rz(o.value,2),a=i[0],s=i[1];this.setAttribute(a,s)}}catch(t){e={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(e)throw e.error}}return this},t.prototype.addEvent=function(t,e,r){if(this._isSpanEnded())return this;if(0===this._spanLimits.eventCountLimit)return eL.diag.warn("No events allowed."),this._droppedEventsCount++,this;this.events.length>=this._spanLimits.eventCountLimit&&(eL.diag.warn("Dropping extra events."),this.events.shift(),this._droppedEventsCount++),(0,rX.isTimeInput)(e)&&((0,rX.isTimeInput)(r)||(r=e),e=void 0);var n=(0,rX.sanitizeAttributes)(e);return this.events.push({name:t,attributes:n,time:this._getTime(r),droppedAttributesCount:0}),this},t.prototype.setStatus=function(t){return this._isSpanEnded()||(this.status=t),this},t.prototype.updateName=function(t){return this._isSpanEnded()||(this.name=t),this},t.prototype.end=function(t){if(this._isSpanEnded())return void eL.diag.error(this.name+" "+this._spanContext.traceId+"-"+this._spanContext.spanId+" - You can only call end() on a span once.");this._ended=!0,this.endTime=this._getTime(t),this._duration=(0,rX.hrTimeDuration)(this.startTime,this.endTime),this._duration[0]<0&&(eL.diag.warn("Inconsistent start and end time, startTime > endTime. Setting span duration to 0ms.",this.startTime,this.endTime),this.endTime=this.startTime.slice(),this._duration=[0,0]),this._spanProcessor.onEnd(this)},t.prototype._getTime=function(t){if("number"==typeof t&&t<rX.otperformance.now())return(0,rX.hrTime)(t+this._performanceOffset);if("number"==typeof t)return(0,rX.millisToHrTime)(t);if(t instanceof Date)return(0,rX.millisToHrTime)(t.getTime());if((0,rX.isTimeInputHrTime)(t))return t;if(this._startTimeProvided)return(0,rX.millisToHrTime)(Date.now());var e=rX.otperformance.now()-this._performanceStartTime;return(0,rX.addHrTimes)(this.startTime,(0,rX.millisToHrTime)(e))},t.prototype.isRecording=function(){return!1===this._ended},t.prototype.recordException=function(t,e){var r={};"string"==typeof t?r[rK.SemanticAttributes.EXCEPTION_MESSAGE]=t:t&&(t.code?r[rK.SemanticAttributes.EXCEPTION_TYPE]=t.code.toString():t.name&&(r[rK.SemanticAttributes.EXCEPTION_TYPE]=t.name),t.message&&(r[rK.SemanticAttributes.EXCEPTION_MESSAGE]=t.message),t.stack&&(r[rK.SemanticAttributes.EXCEPTION_STACKTRACE]=t.stack)),r[rK.SemanticAttributes.EXCEPTION_TYPE]||r[rK.SemanticAttributes.EXCEPTION_MESSAGE]?this.addEvent("exception",r,e):eL.diag.warn("Failed to record an exception "+t)},Object.defineProperty(t.prototype,"duration",{get:function(){return this._duration},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"ended",{get:function(){return this._ended},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"droppedAttributesCount",{get:function(){return this._droppedAttributesCount},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"droppedEventsCount",{get:function(){return this._droppedEventsCount},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"droppedLinksCount",{get:function(){return this._droppedLinksCount},enumerable:!1,configurable:!0}),t.prototype._isSpanEnded=function(){return this._ended&&eL.diag.warn("Can not execute the operation on ended Span {traceId: "+this._spanContext.traceId+", spanId: "+this._spanContext.spanId+"}"),this._ended},t.prototype._truncateToLimitUtil=function(t,e){return t.length<=e?t:t.substr(0,e)},t.prototype._truncateToSize=function(t){var e=this,r=this._attributeValueLengthLimit;return r<=0?(eL.diag.warn("Attribute value limit must be positive, got "+r),t):"string"==typeof t?this._truncateToLimitUtil(t,r):Array.isArray(t)?t.map(function(t){return"string"==typeof t?e._truncateToLimitUtil(t,r):t}):t},t}();eP();var r$=eO(rc());!function(t){t[t.NOT_RECORD=0]="NOT_RECORD",t[t.RECORD=1]="RECORD",t[t.RECORD_AND_SAMPLED=2]="RECORD_AND_SAMPLED"}(c||(c={}));var rq=function(){function t(){}return t.prototype.shouldSample=function(){return{decision:c.NOT_RECORD}},t.prototype.toString=function(){return"AlwaysOffSampler"},t}(),rQ=function(){function t(){}return t.prototype.shouldSample=function(){return{decision:c.RECORD_AND_SAMPLED}},t.prototype.toString=function(){return"AlwaysOnSampler"},t}();eP();var rZ=eO(rc()),rJ=function(){function t(t){var e,r,n,o;this._root=t.root,this._root||((0,rZ.globalErrorHandler)(Error("ParentBasedSampler must have a root sampler configured")),this._root=new rQ),this._remoteParentSampled=null!=(e=t.remoteParentSampled)?e:new rQ,this._remoteParentNotSampled=null!=(r=t.remoteParentNotSampled)?r:new rq,this._localParentSampled=null!=(n=t.localParentSampled)?n:new rQ,this._localParentNotSampled=null!=(o=t.localParentNotSampled)?o:new rq}return t.prototype.shouldSample=function(t,e,r,n,o,i){var a=eL.trace.getSpanContext(t);return a&&(0,eL.isSpanContextValid)(a)?a.isRemote?a.traceFlags&eL.TraceFlags.SAMPLED?this._remoteParentSampled.shouldSample(t,e,r,n,o,i):this._remoteParentNotSampled.shouldSample(t,e,r,n,o,i):a.traceFlags&eL.TraceFlags.SAMPLED?this._localParentSampled.shouldSample(t,e,r,n,o,i):this._localParentNotSampled.shouldSample(t,e,r,n,o,i):this._root.shouldSample(t,e,r,n,o,i)},t.prototype.toString=function(){return"ParentBased{root="+this._root.toString()+", remoteParentSampled="+this._remoteParentSampled.toString()+", remoteParentNotSampled="+this._remoteParentNotSampled.toString()+", localParentSampled="+this._localParentSampled.toString()+", localParentNotSampled="+this._localParentNotSampled.toString()+"}"},t}();eP();var r0=function(){function t(t){void 0===t&&(t=0),this._ratio=t,this._ratio=this._normalize(t),this._upperBound=Math.floor(0xffffffff*this._ratio)}return t.prototype.shouldSample=function(t,e){return{decision:(0,eL.isValidTraceId)(e)&&this._accumulate(e)<this._upperBound?c.RECORD_AND_SAMPLED:c.NOT_RECORD}},t.prototype.toString=function(){return"TraceIdRatioBased{"+this._ratio+"}"},t.prototype._normalize=function(t){return"number"!=typeof t||isNaN(t)?0:t>=1?1:t<=0?0:t},t.prototype._accumulate=function(t){for(var e=0,r=0;r<t.length/8;r++){var n=8*r;e=(e^parseInt(t.slice(n,n+8),16))>>>0}return e},t}(),r1=(0,r$.getEnv)(),r2=r$.TracesSamplerValues.AlwaysOn;function r3(){return{sampler:r4(r1),forceFlushTimeoutMillis:3e4,generalLimits:{attributeValueLengthLimit:(0,r$.getEnv)().OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT,attributeCountLimit:(0,r$.getEnv)().OTEL_ATTRIBUTE_COUNT_LIMIT},spanLimits:{attributeValueLengthLimit:(0,r$.getEnv)().OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT,attributeCountLimit:(0,r$.getEnv)().OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT,linkCountLimit:(0,r$.getEnv)().OTEL_SPAN_LINK_COUNT_LIMIT,eventCountLimit:(0,r$.getEnv)().OTEL_SPAN_EVENT_COUNT_LIMIT,attributePerEventCountLimit:(0,r$.getEnv)().OTEL_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT,attributePerLinkCountLimit:(0,r$.getEnv)().OTEL_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT}}}function r4(t){switch(void 0===t&&(t=(0,r$.getEnv)()),t.OTEL_TRACES_SAMPLER){case r$.TracesSamplerValues.AlwaysOn:return new rQ;case r$.TracesSamplerValues.AlwaysOff:return new rq;case r$.TracesSamplerValues.ParentBasedAlwaysOn:return new rJ({root:new rQ});case r$.TracesSamplerValues.ParentBasedAlwaysOff:return new rJ({root:new rq});case r$.TracesSamplerValues.TraceIdRatio:return new r0(r8(t));case r$.TracesSamplerValues.ParentBasedTraceIdRatio:return new rJ({root:new r0(r8(t))});default:return eL.diag.error('OTEL_TRACES_SAMPLER value "'+t.OTEL_TRACES_SAMPLER+" invalid, defaulting to "+r2+'".'),new rQ}}function r8(t){if(void 0===t.OTEL_TRACES_SAMPLER_ARG||""===t.OTEL_TRACES_SAMPLER_ARG)return eL.diag.error("OTEL_TRACES_SAMPLER_ARG is blank, defaulting to 1."),1;var e=Number(t.OTEL_TRACES_SAMPLER_ARG);return isNaN(e)?(eL.diag.error("OTEL_TRACES_SAMPLER_ARG="+t.OTEL_TRACES_SAMPLER_ARG+" was given, but it is invalid, defaulting to 1."),1):e<0||e>1?(eL.diag.error("OTEL_TRACES_SAMPLER_ARG="+t.OTEL_TRACES_SAMPLER_ARG+" was given, but it is out of range ([0..1]), defaulting to 1."),1):e}var r6=eO(rc());eP();var r5=eO(rc()),r7=function(){function t(t,e){this._exporter=t,this._isExporting=!1,this._finishedSpans=[],this._droppedSpansCount=0;var r=(0,r5.getEnv)();this._maxExportBatchSize="number"==typeof e?.maxExportBatchSize?e.maxExportBatchSize:r.OTEL_BSP_MAX_EXPORT_BATCH_SIZE,this._maxQueueSize="number"==typeof e?.maxQueueSize?e.maxQueueSize:r.OTEL_BSP_MAX_QUEUE_SIZE,this._scheduledDelayMillis="number"==typeof e?.scheduledDelayMillis?e.scheduledDelayMillis:r.OTEL_BSP_SCHEDULE_DELAY,this._exportTimeoutMillis="number"==typeof e?.exportTimeoutMillis?e.exportTimeoutMillis:r.OTEL_BSP_EXPORT_TIMEOUT,this._shutdownOnce=new r5.BindOnceFuture(this._shutdown,this),this._maxExportBatchSize>this._maxQueueSize&&(eL.diag.warn("BatchSpanProcessor: maxExportBatchSize must be smaller or equal to maxQueueSize, setting maxExportBatchSize to match maxQueueSize"),this._maxExportBatchSize=this._maxQueueSize)}return t.prototype.forceFlush=function(){return this._shutdownOnce.isCalled?this._shutdownOnce.promise:this._flushAll()},t.prototype.onStart=function(t,e){},t.prototype.onEnd=function(t){this._shutdownOnce.isCalled||t.spanContext().traceFlags&eL.TraceFlags.SAMPLED&&this._addToBuffer(t)},t.prototype.shutdown=function(){return this._shutdownOnce.call()},t.prototype._shutdown=function(){var t=this;return Promise.resolve().then(function(){return t.onShutdown()}).then(function(){return t._flushAll()}).then(function(){return t._exporter.shutdown()})},t.prototype._addToBuffer=function(t){if(this._finishedSpans.length>=this._maxQueueSize){0===this._droppedSpansCount&&eL.diag.debug("maxQueueSize reached, dropping spans"),this._droppedSpansCount++;return}this._droppedSpansCount>0&&(eL.diag.warn("Dropped "+this._droppedSpansCount+" spans because maxQueueSize reached"),this._droppedSpansCount=0),this._finishedSpans.push(t),this._maybeStartTimer()},t.prototype._flushAll=function(){var t=this;return new Promise(function(e,r){for(var n=[],o=Math.ceil(t._finishedSpans.length/t._maxExportBatchSize),i=0;i<o;i++)n.push(t._flushOneBatch());Promise.all(n).then(function(){e()}).catch(r)})},t.prototype._flushOneBatch=function(){var t=this;return this._clearTimer(),0===this._finishedSpans.length?Promise.resolve():new Promise(function(e,r){var n=setTimeout(function(){r(Error("Timeout"))},t._exportTimeoutMillis);eL.context.with((0,r5.suppressTracing)(eL.context.active()),function(){var o=t._finishedSpans.splice(0,t._maxExportBatchSize),i=function(){return t._exporter.export(o,function(t){var o;clearTimeout(n),t.code===r5.ExportResultCode.SUCCESS?e():r(null!=(o=t.error)?o:Error("BatchSpanProcessor: span export failed"))})},a=o.map(function(t){return t.resource}).filter(function(t){return t.asyncAttributesPending});0===a.length?i():Promise.all(a.map(function(t){var e;return null==(e=t.waitForAsyncAttributes)?void 0:e.call(t)})).then(i,function(t){(0,r5.globalErrorHandler)(t),r(t)})})})},t.prototype._maybeStartTimer=function(){var t=this;if(!this._isExporting){var e=function(){t._isExporting=!0,t._flushOneBatch().then(function(){t._isExporting=!1,t._finishedSpans.length>0&&(t._clearTimer(),t._maybeStartTimer())}).catch(function(e){t._isExporting=!1,(0,r5.globalErrorHandler)(e)})};if(this._finishedSpans.length>=this._maxExportBatchSize)return e();void 0===this._timer&&(this._timer=setTimeout(function(){return e()},this._scheduledDelayMillis),(0,r5.unrefTimer)(this._timer))}},t.prototype._clearTimer=function(){void 0!==this._timer&&(clearTimeout(this._timer),this._timer=void 0)},t}(),r9=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),nt=function(t){function e(e,r){var n=t.call(this,e,r)||this;return n.onInit(r),n}return r9(e,t),e.prototype.onInit=function(t){var e=this;t?.disableAutoFlushOnDocumentHide!==!0&&"u">typeof document&&(this._visibilityChangeListener=function(){"hidden"===document.visibilityState&&e.forceFlush()},this._pageHideListener=function(){e.forceFlush()},document.addEventListener("visibilitychange",this._visibilityChangeListener),document.addEventListener("pagehide",this._pageHideListener))},e.prototype.onShutdown=function(){"u">typeof document&&(this._visibilityChangeListener&&document.removeEventListener("visibilitychange",this._visibilityChangeListener),this._pageHideListener&&document.removeEventListener("pagehide",this._pageHideListener))},e}(r7),ne=function(){this.generateTraceId=nn(16),this.generateSpanId=nn(8)},nr=Array(32);function nn(t){return function(){for(var e=0;e<2*t;e++)nr[e]=Math.floor(16*Math.random())+48,nr[e]>=58&&(nr[e]+=39);return String.fromCharCode.apply(null,nr.slice(0,2*t))}}var no=function(){function t(t,e,r){this._tracerProvider=r;var n,o,i,a=(n={sampler:r4()},(i=Object.assign({},o=r3(),n,e)).generalLimits=Object.assign({},o.generalLimits,e.generalLimits||{}),i.spanLimits=Object.assign({},o.spanLimits,e.spanLimits||{}),i);this._sampler=a.sampler,this._generalLimits=a.generalLimits,this._spanLimits=a.spanLimits,this._idGenerator=e.idGenerator||new ne,this.resource=r.resource,this.instrumentationLibrary=t}return t.prototype.startSpan=function(t,e,r){void 0===e&&(e={}),void 0===r&&(r=eL.context.active()),e.root&&(r=eL.trace.deleteSpan(r));var n=eL.trace.getSpan(r);if((0,rH.isTracingSuppressed)(r)){eL.diag.debug("Instrumentation suppressed, returning Noop Span");var o=eL.trace.wrapSpanContext(eL.INVALID_SPAN_CONTEXT);return o}var i,a,s,u,l,c,p=n?.spanContext(),f=this._idGenerator.generateSpanId();p&&eL.trace.isSpanContextValid(p)?(u=p.traceId,l=p.traceState,c=p.spanId):u=this._idGenerator.generateTraceId();var h=null!=(i=e.kind)?i:eL.SpanKind.INTERNAL,d=(null!=(a=e.links)?a:[]).map(function(t){return{context:t.context,attributes:(0,rH.sanitizeAttributes)(t.attributes)}}),_=(0,rH.sanitizeAttributes)(e.attributes),E=this._sampler.shouldSample(r,u,t,h,_,d);l=null!=(s=E.traceState)?s:l;var g={traceId:u,spanId:f,traceFlags:E.decision===eL.SamplingDecision.RECORD_AND_SAMPLED?eL.TraceFlags.SAMPLED:eL.TraceFlags.NONE,traceState:l};if(E.decision===eL.SamplingDecision.NOT_RECORD){eL.diag.debug("Recording is off, propagating context in a non-recording span");var o=eL.trace.wrapSpanContext(g);return o}var T=(0,rH.sanitizeAttributes)(Object.assign(_,E.attributes));return new rY(this,r,t,g,h,c,d,e.startTime,void 0,T)},t.prototype.startActiveSpan=function(t,e,r,n){var o,i,a;if(!(arguments.length<2)){2==arguments.length?a=e:3==arguments.length?(o=e,a=r):(o=e,i=r,a=n);var s=i??eL.context.active(),u=this.startSpan(t,o,s),l=eL.trace.setSpan(s,u);return eL.context.with(l,a,void 0,u)}},t.prototype.getGeneralLimits=function(){return this._generalLimits},t.prototype.getSpanLimits=function(){return this._spanLimits},t.prototype.getActiveSpanProcessor=function(){return this._tracerProvider.getActiveSpanProcessor()},t}();eP();var ni=eO(rc());eP();var na=eO(e$()),ns=eO(rc()),nu=function(){return(nu=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var o in e=arguments[r])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)},nl=function(t,e){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(i){return function(s){var u=[i,s];if(r)throw TypeError("Generator is already executing.");for(;a;)try{if(r=1,n&&(o=2&u[0]?n.return:u[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,u[1])).done)return o;switch(n=0,o&&(u=[2&u[0],o.value]),u[0]){case 0:case 1:o=u;break;case 4:return a.label++,{value:u[1],done:!1};case 5:a.label++,n=u[1],u=[0];continue;case 7:u=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===u[0]||2===u[0])){a=0;continue}if(3===u[0]&&(!o||u[1]>o[0]&&u[1]<o[3])){a.label=u[1];break}if(6===u[0]&&a.label<o[1]){a.label=o[1],o=u;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(u);break}o[2]&&a.ops.pop(),a.trys.pop();continue}u=e.call(t,a)}catch(t){u=[6,t],n=0}finally{r=o=0}if(5&u[0])throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}}},nc=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},np=function(){function t(t,e){var r,n=this;this._attributes=t,this.asyncAttributesPending=null!=e,this._syncAttributes=null!=(r=this._attributes)?r:{},this._asyncAttributesPromise=e?.then(function(t){return n._attributes=Object.assign({},n._attributes,t),n.asyncAttributesPending=!1,t},function(t){return eL.diag.debug("a resource's async attributes promise rejected: %s",t),n.asyncAttributesPending=!1,{}})}return t.empty=function(){return t.EMPTY},t.default=function(){var e;return new t(((e={})[na.SemanticResourceAttributes.SERVICE_NAME]="unknown_service",e[na.SemanticResourceAttributes.TELEMETRY_SDK_LANGUAGE]=ns.SDK_INFO[na.SemanticResourceAttributes.TELEMETRY_SDK_LANGUAGE],e[na.SemanticResourceAttributes.TELEMETRY_SDK_NAME]=ns.SDK_INFO[na.SemanticResourceAttributes.TELEMETRY_SDK_NAME],e[na.SemanticResourceAttributes.TELEMETRY_SDK_VERSION]=ns.SDK_INFO[na.SemanticResourceAttributes.TELEMETRY_SDK_VERSION],e))},Object.defineProperty(t.prototype,"attributes",{get:function(){var t;return this.asyncAttributesPending&&eL.diag.error("Accessing resource attributes before async attributes settled"),null!=(t=this._attributes)?t:{}},enumerable:!1,configurable:!0}),t.prototype.waitForAsyncAttributes=function(){var t,e,r,n;return t=this,e=void 0,r=void 0,n=function(){return nl(this,function(t){switch(t.label){case 0:return this.asyncAttributesPending?[4,this._asyncAttributesPromise]:[3,2];case 1:t.sent(),t.label=2;case 2:return[2]}})},new(r||(r=Promise))(function(o,i){function a(t){try{u(n.next(t))}catch(t){i(t)}}function s(t){try{u(n.throw(t))}catch(t){i(t)}}function u(t){var e;t.done?o(t.value):((e=t.value)instanceof r?e:new r(function(t){t(e)})).then(a,s)}u((n=n.apply(t,e||[])).next())})},t.prototype.merge=function(e){var r,n=this;if(!e)return this;var o=nu(nu({},this._syncAttributes),null!=(r=e._syncAttributes)?r:e.attributes);return this._asyncAttributesPromise||e._asyncAttributesPromise?new t(o,Promise.all([this._asyncAttributesPromise,e._asyncAttributesPromise]).then(function(t){var r,o=nc(t,2),i=o[0],a=o[1];return nu(nu(nu(nu({},n._syncAttributes),i),null!=(r=e._syncAttributes)?r:e.attributes),a)})):new t(o)},t.EMPTY=new t({}),t}();eP();var nf=eO(rc()),nh=eO(e$()),nd=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},n_=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},nE=new(function(){function t(){this._MAX_LENGTH=255,this._COMMA_SEPARATOR=",",this._LABEL_KEY_VALUE_SPLITTER="=",this._ERROR_MESSAGE_INVALID_CHARS="should be a ASCII string with a length greater than 0 and not exceed "+this._MAX_LENGTH+" characters.",this._ERROR_MESSAGE_INVALID_VALUE="should be a ASCII string with a length not exceed "+this._MAX_LENGTH+" characters."}return t.prototype.detect=function(t){var e={},r=(0,nf.getEnv)(),n=r.OTEL_RESOURCE_ATTRIBUTES,o=r.OTEL_SERVICE_NAME;if(n)try{var i=this._parseResourceAttributes(n);Object.assign(e,i)}catch(t){eL.diag.debug("EnvDetector failed: "+t.message)}return o&&(e[nh.SemanticResourceAttributes.SERVICE_NAME]=o),new np(e)},t.prototype._parseResourceAttributes=function(t){if(!t)return{};var e,r,n={},o=t.split(this._COMMA_SEPARATOR,-1);try{for(var i=nd(o),a=i.next();!a.done;a=i.next()){var s=a.value.split(this._LABEL_KEY_VALUE_SPLITTER,-1);if(2===s.length){var u=n_(s,2),l=u[0],c=u[1];if(l=l.trim(),c=c.trim().split(/^"|"$/).join(""),!this._isValidAndNotEmpty(l))throw Error("Attribute key "+this._ERROR_MESSAGE_INVALID_CHARS);if(!this._isValid(c))throw Error("Attribute value "+this._ERROR_MESSAGE_INVALID_VALUE);n[l]=decodeURIComponent(c)}}}catch(t){e={error:t}}finally{try{a&&!a.done&&(r=i.return)&&r.call(i)}finally{if(e)throw e.error}}return n},t.prototype._isValid=function(t){return t.length<=this._MAX_LENGTH&&this._isBaggageOctetString(t)},t.prototype._isBaggageOctetString=function(t){for(var e=0;e<t.length;e++){var r=t.charCodeAt(e);if(r<33||44===r||59===r||92===r||r>126)return!1}return!0},t.prototype._isValidAndNotEmpty=function(t){return t.length>0&&this._isValid(t)},t}());eP();var ng=function(t,e){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(i){return function(s){var u=[i,s];if(r)throw TypeError("Generator is already executing.");for(;a;)try{if(r=1,n&&(o=2&u[0]?n.return:u[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,u[1])).done)return o;switch(n=0,o&&(u=[2&u[0],o.value]),u[0]){case 0:case 1:o=u;break;case 4:return a.label++,{value:u[1],done:!1};case 5:a.label++,n=u[1],u=[0];continue;case 7:u=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===u[0]||2===u[0])){a=0;continue}if(3===u[0]&&(!o||u[1]>o[0]&&u[1]<o[3])){a.label=u[1];break}if(6===u[0]&&a.label<o[1]){a.label=o[1],o=u;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(u);break}o[2]&&a.ops.pop(),a.trys.pop();continue}u=e.call(t,a)}catch(t){u=[6,t],n=0}finally{r=o=0}if(5&u[0])throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}}},nT=function(t){void 0===t&&(t={});var e,r=(null!=(e=t.detectors)?e:[]).map(function(e){try{var r,n,o,i,a,s=e.detect(t);return(a=null!==s&&"object"==typeof s&&"function"==typeof s.then?new np({},(r=void 0,n=void 0,o=void 0,i=function(){var t;return ng(this,function(e){switch(e.label){case 0:return[4,s];case 1:return t=e.sent(),[2,t.attributes]}})},new(o||(o=Promise))(function(t,e){function a(t){try{u(i.next(t))}catch(t){e(t)}}function s(t){try{u(i.throw(t))}catch(t){e(t)}}function u(e){var r;e.done?t(e.value):((r=e.value)instanceof o?r:new o(function(t){t(r)})).then(a,s)}u((i=i.apply(r,n||[])).next())}))):s).waitForAsyncAttributes?a.waitForAsyncAttributes().then(function(){return eL.diag.debug(e.constructor.name+" found resource.",a)}):eL.diag.debug(e.constructor.name+" found resource.",a),a}catch(t){return eL.diag.error(e.constructor.name+" failed: "+t.message),np.empty()}}),n=r.reduce(function(t,e){return t.merge(e)},np.empty());return n.waitForAsyncAttributes&&n.waitForAsyncAttributes().then(function(){ny(r)}),n},ny=function(t){t.forEach(function(t){if(Object.keys(t.attributes).length>0){var e=JSON.stringify(t.attributes,null,4);eL.diag.verbose(e)}})},nm=eO(rc()),nv=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},nS=function(){function t(t){this._spanProcessors=t}return t.prototype.forceFlush=function(){var t,e,r=[];try{for(var n=nv(this._spanProcessors),o=n.next();!o.done;o=n.next()){var i=o.value;r.push(i.forceFlush())}}catch(e){t={error:e}}finally{try{o&&!o.done&&(e=n.return)&&e.call(n)}finally{if(t)throw t.error}}return new Promise(function(t){Promise.all(r).then(function(){t()}).catch(function(e){(0,nm.globalErrorHandler)(e||Error("MultiSpanProcessor: forceFlush failed")),t()})})},t.prototype.onStart=function(t,e){var r,n;try{for(var o=nv(this._spanProcessors),i=o.next();!i.done;i=o.next())i.value.onStart(t,e)}catch(t){r={error:t}}finally{try{i&&!i.done&&(n=o.return)&&n.call(o)}finally{if(r)throw r.error}}},t.prototype.onEnd=function(t){var e,r;try{for(var n=nv(this._spanProcessors),o=n.next();!o.done;o=n.next())o.value.onEnd(t)}catch(t){e={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(e)throw e.error}}},t.prototype.shutdown=function(){var t,e,r=[];try{for(var n=nv(this._spanProcessors),o=n.next();!o.done;o=n.next()){var i=o.value;r.push(i.shutdown())}}catch(e){t={error:e}}finally{try{o&&!o.done&&(e=n.return)&&e.call(n)}finally{if(t)throw t.error}}return new Promise(function(t,e){Promise.all(r).then(function(){t()},e)})},t}(),nb=function(){function t(){}return t.prototype.onStart=function(t,e){},t.prototype.onEnd=function(t){},t.prototype.shutdown=function(){return Promise.resolve()},t.prototype.forceFlush=function(){return Promise.resolve()},t}();!function(t){t[t.resolved=0]="resolved",t[t.timeout=1]="timeout",t[t.error=2]="error",t[t.unresolved=3]="unresolved"}(p||(p={}));var nA=function(){function t(t){void 0===t&&(t={}),this._registeredSpanProcessors=[],this._tracers=new Map;var e,r,n,o,i,a,s,u,l,c,p,f,h,d,_,E,g=(0,ni.merge)({},r3(),(d=Object.assign({},(e=t).spanLimits),_=(0,r6.getEnvWithoutDefaults)(),d.attributeCountLimit=null!=(s=null!=(a=null!=(i=null!=(n=null==(r=e.spanLimits)?void 0:r.attributeCountLimit)?n:null==(o=e.generalLimits)?void 0:o.attributeCountLimit)?i:_.OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT)?a:_.OTEL_ATTRIBUTE_COUNT_LIMIT)?s:r6.DEFAULT_ATTRIBUTE_COUNT_LIMIT,d.attributeValueLengthLimit=null!=(h=null!=(f=null!=(p=null!=(l=null==(u=e.spanLimits)?void 0:u.attributeValueLengthLimit)?l:null==(c=e.generalLimits)?void 0:c.attributeValueLengthLimit)?p:_.OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT)?f:_.OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT)?h:r6.DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT,Object.assign({},e,{spanLimits:d})));this.resource=null!=(E=g.resource)?E:np.empty(),this.resource=np.default().merge(this.resource),this._config=Object.assign({},g,{resource:this.resource});var T=this._buildExporterFromEnv();if(void 0!==T){var y=new nt(T);this.activeSpanProcessor=y}else this.activeSpanProcessor=new nb}return t.prototype.getTracer=function(t,e,r){var n=t+"@"+(e||"")+":"+(r?.schemaUrl||"");return this._tracers.has(n)||this._tracers.set(n,new no({name:t,version:e,schemaUrl:r?.schemaUrl},this._config,this)),this._tracers.get(n)},t.prototype.addSpanProcessor=function(t){0===this._registeredSpanProcessors.length&&this.activeSpanProcessor.shutdown().catch(function(t){return eL.diag.error("Error while trying to shutdown current span processor",t)}),this._registeredSpanProcessors.push(t),this.activeSpanProcessor=new nS(this._registeredSpanProcessors)},t.prototype.getActiveSpanProcessor=function(){return this.activeSpanProcessor},t.prototype.register=function(t){void 0===t&&(t={}),eL.trace.setGlobalTracerProvider(this),void 0===t.propagator&&(t.propagator=this._buildPropagatorFromEnv()),t.contextManager&&eL.context.setGlobalContextManager(t.contextManager),t.propagator&&eL.propagation.setGlobalPropagator(t.propagator)},t.prototype.forceFlush=function(){var t=this._config.forceFlushTimeoutMillis,e=this._registeredSpanProcessors.map(function(e){return new Promise(function(r){var n,o=setTimeout(function(){r(Error("Span processor did not completed within timeout period of "+t+" ms")),n=p.timeout},t);e.forceFlush().then(function(){clearTimeout(o),n!==p.timeout&&r(n=p.resolved)}).catch(function(t){clearTimeout(o),n=p.error,r(t)})})});return new Promise(function(t,r){Promise.all(e).then(function(e){var n=e.filter(function(t){return t!==p.resolved});n.length>0?r(n):t()}).catch(function(t){return r([t])})})},t.prototype.shutdown=function(){return this.activeSpanProcessor.shutdown()},t.prototype._getPropagator=function(t){var e;return null==(e=this.constructor._registeredPropagators.get(t))?void 0:e()},t.prototype._getSpanExporter=function(t){var e;return null==(e=this.constructor._registeredExporters.get(t))?void 0:e()},t.prototype._buildPropagatorFromEnv=function(){var t=this,e=Array.from(new Set((0,ni.getEnv)().OTEL_PROPAGATORS)),r=e.map(function(e){var r=t._getPropagator(e);return r||eL.diag.warn('Propagator "'+e+'" requested through environment variable is unavailable.'),r}).reduce(function(t,e){return e&&t.push(e),t},[]);if(0!==r.length)return 1===e.length?r[0]:new ni.CompositePropagator({propagators:r})},t.prototype._buildExporterFromEnv=function(){var t=(0,ni.getEnv)().OTEL_TRACES_EXPORTER;if("none"!==t&&""!==t){var e=this._getSpanExporter(t);return e||eL.diag.error('Exporter "'+t+'" requested through environment variable is unavailable.'),e}},t._registeredPropagators=new Map([["tracecontext",function(){return new ni.W3CTraceContextPropagator}],["baggage",function(){return new ni.W3CBaggagePropagator}]]),t._registeredExporters=new Map,t}();eP();var nO={};eA(nO,_);var nR=eO(rf(),1);eP();var nL=eO(rc());eP(),eP(),eP();var nP=eO(rc()),nC=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},nI=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},nN=function(){function t(t,e,r){this.attributes={},this.totalAttributesCount=0,this._isReadonly=!1;var n=r.timestamp,o=r.observedTimestamp,i=r.severityNumber,a=r.severityText,s=r.body,u=r.attributes,l=r.context,c=Date.now();if(this.hrTime=(0,nP.timeInputToHrTime)(n??c),this.hrTimeObserved=(0,nP.timeInputToHrTime)(o??c),l){var p=eL.trace.getSpanContext(l);p&&eL.isSpanContextValid(p)&&(this.spanContext=p)}this.severityNumber=i,this.severityText=a,this.body=s,this.resource=t.resource,this.instrumentationScope=e,this._logRecordLimits=t.logRecordLimits,this.setAttributes(void 0===u?{}:u)}return Object.defineProperty(t.prototype,"severityText",{get:function(){return this._severityText},set:function(t){this._isLogRecordReadonly()||(this._severityText=t)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"severityNumber",{get:function(){return this._severityNumber},set:function(t){this._isLogRecordReadonly()||(this._severityNumber=t)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"body",{get:function(){return this._body},set:function(t){this._isLogRecordReadonly()||(this._body=t)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"droppedAttributesCount",{get:function(){return this.totalAttributesCount-Object.keys(this.attributes).length},enumerable:!1,configurable:!0}),t.prototype.setAttribute=function(t,e){return this._isLogRecordReadonly()||null===e||(0===t.length?eL.diag.warn("Invalid attribute key: "+t):(0,nP.isAttributeValue)(e)||"object"==typeof e&&!Array.isArray(e)&&Object.keys(e).length>0?(this.totalAttributesCount+=1,Object.keys(this.attributes).length>=this._logRecordLimits.attributeCountLimit&&!Object.prototype.hasOwnProperty.call(this.attributes,t)||((0,nP.isAttributeValue)(e)?this.attributes[t]=this._truncateToSize(e):this.attributes[t]=e)):eL.diag.warn("Invalid attribute value set for key: "+t)),this},t.prototype.setAttributes=function(t){var e,r;try{for(var n=nC(Object.entries(t)),o=n.next();!o.done;o=n.next()){var i=nI(o.value,2),a=i[0],s=i[1];this.setAttribute(a,s)}}catch(t){e={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(e)throw e.error}}return this},t.prototype.setBody=function(t){return this.body=t,this},t.prototype.setSeverityNumber=function(t){return this.severityNumber=t,this},t.prototype.setSeverityText=function(t){return this.severityText=t,this},t.prototype._makeReadonly=function(){this._isReadonly=!0},t.prototype._truncateToSize=function(t){var e=this,r=this._logRecordLimits.attributeValueLengthLimit;return r<=0?(eL.diag.warn("Attribute value limit must be positive, got "+r),t):"string"==typeof t?this._truncateToLimitUtil(t,r):Array.isArray(t)?t.map(function(t){return"string"==typeof t?e._truncateToLimitUtil(t,r):t}):t},t.prototype._truncateToLimitUtil=function(t,e){return t.length<=e?t:t.substring(0,e)},t.prototype._isLogRecordReadonly=function(){return this._isReadonly&&eL.diag.warn("Can not execute the operation on emitted log record"),this._isReadonly},t}(),nw=function(){return(nw=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var o in e=arguments[r])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)},nM=function(){function t(t,e){this.instrumentationScope=t,this._sharedState=e}return t.prototype.emit=function(t){var e=t.context||eL.context.active(),r=new nN(this._sharedState,this.instrumentationScope,nw({context:e},t));this._sharedState.activeProcessor.onEmit(r,e),r._makeReadonly()},t}(),nD=eO(rc()),nx=eO(rc()),nU=function(t,e,r,n){return new(r||(r=Promise))(function(o,i){function a(t){try{u(n.next(t))}catch(t){i(t)}}function s(t){try{u(n.throw(t))}catch(t){i(t)}}function u(t){var e;t.done?o(t.value):((e=t.value)instanceof r?e:new r(function(t){t(e)})).then(a,s)}u((n=n.apply(t,e||[])).next())})},nB=function(t,e){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(i){return function(s){var u=[i,s];if(r)throw TypeError("Generator is already executing.");for(;a;)try{if(r=1,n&&(o=2&u[0]?n.return:u[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,u[1])).done)return o;switch(n=0,o&&(u=[2&u[0],o.value]),u[0]){case 0:case 1:o=u;break;case 4:return a.label++,{value:u[1],done:!1};case 5:a.label++,n=u[1],u=[0];continue;case 7:u=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===u[0]||2===u[0])){a=0;continue}if(3===u[0]&&(!o||u[1]>o[0]&&u[1]<o[3])){a.label=u[1];break}if(6===u[0]&&a.label<o[1]){a.label=o[1],o=u;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(u);break}o[2]&&a.ops.pop(),a.trys.pop();continue}u=e.call(t,a)}catch(t){u=[6,t],n=0}finally{r=o=0}if(5&u[0])throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}}},nG=function(){function t(t,e){this.processors=t,this.forceFlushTimeoutMillis=e}return t.prototype.forceFlush=function(){return nU(this,void 0,void 0,function(){var t;return nB(this,function(e){switch(e.label){case 0:return t=this.forceFlushTimeoutMillis,[4,Promise.all(this.processors.map(function(e){return(0,nx.callWithTimeout)(e.forceFlush(),t)}))];case 1:return e.sent(),[2]}})})},t.prototype.onEmit=function(t,e){this.processors.forEach(function(r){return r.onEmit(t,e)})},t.prototype.shutdown=function(){return nU(this,void 0,void 0,function(){return nB(this,function(t){switch(t.label){case 0:return[4,Promise.all(this.processors.map(function(t){return t.shutdown()}))];case 1:return t.sent(),[2]}})})},t}(),nV=function(){function t(){}return t.prototype.forceFlush=function(){return Promise.resolve()},t.prototype.onEmit=function(t,e){},t.prototype.shutdown=function(){return Promise.resolve()},t}(),nk=function(t,e,r){this.resource=t,this.forceFlushTimeoutMillis=e,this.logRecordLimits=r,this.loggers=new Map,this.registeredLogRecordProcessors=[],this.activeProcessor=new nV},nj=function(){function t(t){void 0===t&&(t={});var e,r,n,o,i,a,s,u=(0,nL.merge)({},{forceFlushTimeoutMillis:3e4,logRecordLimits:{attributeValueLengthLimit:(0,nD.getEnv)().OTEL_LOGRECORD_ATTRIBUTE_VALUE_LENGTH_LIMIT,attributeCountLimit:(0,nD.getEnv)().OTEL_LOGRECORD_ATTRIBUTE_COUNT_LIMIT},includeTraceContext:!0},t),l=u.resource,c=void 0===l?np.default():l,p=u.logRecordLimits,f=u.forceFlushTimeoutMillis;this._sharedState=new nk(c,f,(s=(0,nD.getEnvWithoutDefaults)(),{attributeCountLimit:null!=(n=null!=(r=null!=(e=p.attributeCountLimit)?e:s.OTEL_LOGRECORD_ATTRIBUTE_COUNT_LIMIT)?r:s.OTEL_ATTRIBUTE_COUNT_LIMIT)?n:nD.DEFAULT_ATTRIBUTE_COUNT_LIMIT,attributeValueLengthLimit:null!=(a=null!=(i=null!=(o=p.attributeValueLengthLimit)?o:s.OTEL_LOGRECORD_ATTRIBUTE_VALUE_LENGTH_LIMIT)?i:s.OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT)?a:nD.DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT})),this._shutdownOnce=new nL.BindOnceFuture(this._shutdown,this)}return t.prototype.getLogger=function(t,e,r){if(this._shutdownOnce.isCalled)return eL.diag.warn("A shutdown LoggerProvider cannot provide a Logger"),nO.NOOP_LOGGER;t||eL.diag.warn("Logger requested without instrumentation scope name.");var n=t||"unknown",o=n+"@"+(e||"")+":"+(r?.schemaUrl||"");return this._sharedState.loggers.has(o)||this._sharedState.loggers.set(o,new nM({name:n,version:e,schemaUrl:r?.schemaUrl},this._sharedState)),this._sharedState.loggers.get(o)},t.prototype.addLogRecordProcessor=function(t){0===this._sharedState.registeredLogRecordProcessors.length&&this._sharedState.activeProcessor.shutdown().catch(function(t){return eL.diag.error("Error while trying to shutdown current log record processor",t)}),this._sharedState.registeredLogRecordProcessors.push(t),this._sharedState.activeProcessor=new nG(this._sharedState.registeredLogRecordProcessors,this._sharedState.forceFlushTimeoutMillis)},t.prototype.forceFlush=function(){return this._shutdownOnce.isCalled?(eL.diag.warn("invalid attempt to force flush after LoggerProvider shutdown"),this._shutdownOnce.promise):this._sharedState.activeProcessor.forceFlush()},t.prototype.shutdown=function(){return this._shutdownOnce.isCalled?(eL.diag.warn("shutdown may only be called once per LoggerProvider"),this._shutdownOnce.promise):this._shutdownOnce.call()},t.prototype._shutdown=function(){return this._sharedState.activeProcessor.shutdown()},t}();!function(t){t[t.DELTA=0]="DELTA",t[t.CUMULATIVE=1]="CUMULATIVE"}(f||(f={}));var nF=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),nH=function(t,e,r,n){return new(r||(r=Promise))(function(o,i){function a(t){try{u(n.next(t))}catch(t){i(t)}}function s(t){try{u(n.throw(t))}catch(t){i(t)}}function u(t){var e;t.done?o(t.value):((e=t.value)instanceof r?e:new r(function(t){t(e)})).then(a,s)}u((n=n.apply(t,e||[])).next())})},nX=function(t,e){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(i){return function(s){var u=[i,s];if(r)throw TypeError("Generator is already executing.");for(;a;)try{if(r=1,n&&(o=2&u[0]?n.return:u[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,u[1])).done)return o;switch(n=0,o&&(u=[2&u[0],o.value]),u[0]){case 0:case 1:o=u;break;case 4:return a.label++,{value:u[1],done:!1};case 5:a.label++,n=u[1],u=[0];continue;case 7:u=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===u[0]||2===u[0])){a=0;continue}if(3===u[0]&&(!o||u[1]>o[0]&&u[1]<o[3])){a.label=u[1];break}if(6===u[0]&&a.label<o[1]){a.label=o[1],o=u;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(u);break}o[2]&&a.ops.pop(),a.trys.pop();continue}u=e.call(t,a)}catch(t){u=[6,t],n=0}finally{r=o=0}if(5&u[0])throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}}},nK=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};function nW(t){return null!=t}function nz(t){var e=Object.keys(t);return 0===e.length?"":JSON.stringify((e=e.sort()).map(function(e){return[e,t[e]]}))}var nY=function(t){function e(r){var n=t.call(this,r)||this;return Object.setPrototypeOf(n,e.prototype),n}return nF(e,t),e}(Error);function n$(t,e){var r;return Promise.race([t,new Promise(function(t,n){r=setTimeout(function(){n(new nY("Operation timed out."))},e)})]).then(function(t){return clearTimeout(r),t},function(t){throw clearTimeout(r),t})}function nq(t){return"rejected"===t.status}function nQ(t,e,r){var n,o,i,a;return null!=t.match(nZ)||eL.diag.warn('Invalid metric name: "'+t+'". The metric name should be a ASCII string with a length no greater than 255 characters.'),{name:t,type:e,description:null!=(n=r?.description)?n:"",unit:null!=(o=r?.unit)?o:"",valueType:null!=(i=r?.valueType)?i:eL.ValueType.DOUBLE,advice:null!=(a=r?.advice)?a:{}}}eP(),function(t){t.COUNTER="COUNTER",t.HISTOGRAM="HISTOGRAM",t.UP_DOWN_COUNTER="UP_DOWN_COUNTER",t.OBSERVABLE_COUNTER="OBSERVABLE_COUNTER",t.OBSERVABLE_GAUGE="OBSERVABLE_GAUGE",t.OBSERVABLE_UP_DOWN_COUNTER="OBSERVABLE_UP_DOWN_COUNTER"}(h||(h={}));var nZ=/^[a-z][a-z0-9_.\-/]{0,254}$/i;eP();var nJ=function(){function t(){this._registeredViews=[]}return t.prototype.addView=function(t){this._registeredViews.push(t)},t.prototype.findViews=function(t,e){var r=this;return this._registeredViews.filter(function(n){return r._matchInstrument(n.instrumentSelector,t)&&r._matchMeter(n.meterSelector,e)})},t.prototype._matchInstrument=function(t,e){return(void 0===t.getType()||e.type===t.getType())&&t.getNameFilter().match(e.name)&&t.getUnitFilter().match(e.unit)},t.prototype._matchMeter=function(t,e){return t.getNameFilter().match(e.name)&&(void 0===e.version||t.getVersionFilter().match(e.version))&&(void 0===e.schemaUrl||t.getSchemaUrlFilter().match(e.schemaUrl))},t}();eP();var n0=eO(rc()),n1=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),n2=function(){function t(t,e){this._writableMetricStorage=t,this._descriptor=e}return t.prototype._record=function(t,e,r){if(void 0===e&&(e={}),void 0===r&&(r=eL.context.active()),"number"!=typeof t)return void eL.diag.warn("non-number value provided to metric "+this._descriptor.name+": "+t);(this._descriptor.valueType!==eL.ValueType.INT||Number.isInteger(t)||(eL.diag.warn("INT value type cannot accept a floating-point value for "+this._descriptor.name+", ignoring the fractional digits."),Number.isInteger(t=Math.trunc(t))))&&this._writableMetricStorage.record(t,e,r,(0,n0.millisToHrTime)(Date.now()))},t}(),n3=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n1(e,t),e.prototype.add=function(t,e,r){this._record(t,e,r)},e}(n2),n4=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n1(e,t),e.prototype.add=function(t,e,r){if(t<0)return void eL.diag.warn("negative value provided to counter "+this._descriptor.name+": "+t);this._record(t,e,r)},e}(n2),n8=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n1(e,t),e.prototype.record=function(t,e,r){if(t<0)return void eL.diag.warn("negative value provided to histogram "+this._descriptor.name+": "+t);this._record(t,e,r)},e}(n2),n6=function(){function t(t,e,r){this._observableRegistry=r,this._descriptor=t,this._metricStorages=e}return t.prototype.addCallback=function(t){this._observableRegistry.addCallback(t,this)},t.prototype.removeCallback=function(t){this._observableRegistry.removeCallback(t,this)},t}(),n5=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n1(e,t),e}(n6),n7=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n1(e,t),e}(n6),n9=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n1(e,t),e}(n6);function ot(t){return t instanceof n6}var oe=function(){function t(t){this._meterSharedState=t}return t.prototype.createHistogram=function(t,e){var r=nQ(t,h.HISTOGRAM,e);return new n8(this._meterSharedState.registerMetricStorage(r),r)},t.prototype.createCounter=function(t,e){var r=nQ(t,h.COUNTER,e);return new n4(this._meterSharedState.registerMetricStorage(r),r)},t.prototype.createUpDownCounter=function(t,e){var r=nQ(t,h.UP_DOWN_COUNTER,e);return new n3(this._meterSharedState.registerMetricStorage(r),r)},t.prototype.createObservableGauge=function(t,e){var r=nQ(t,h.OBSERVABLE_GAUGE,e),n=this._meterSharedState.registerAsyncMetricStorage(r);return new n7(r,n,this._meterSharedState.observableRegistry)},t.prototype.createObservableCounter=function(t,e){var r=nQ(t,h.OBSERVABLE_COUNTER,e),n=this._meterSharedState.registerAsyncMetricStorage(r);return new n5(r,n,this._meterSharedState.observableRegistry)},t.prototype.createObservableUpDownCounter=function(t,e){var r=nQ(t,h.OBSERVABLE_UP_DOWN_COUNTER,e),n=this._meterSharedState.registerAsyncMetricStorage(r);return new n9(r,n,this._meterSharedState.observableRegistry)},t.prototype.addBatchObservableCallback=function(t,e){this._meterSharedState.observableRegistry.addBatchCallback(t,e)},t.prototype.removeBatchObservableCallback=function(t,e){this._meterSharedState.observableRegistry.removeBatchCallback(t,e)},t}(),or=function(){function t(t){this._instrumentDescriptor=t}return t.prototype.getInstrumentDescriptor=function(){return this._instrumentDescriptor},t.prototype.updateDescription=function(t){this._instrumentDescriptor=nQ(this._instrumentDescriptor.name,this._instrumentDescriptor.type,{description:t,valueType:this._instrumentDescriptor.valueType,unit:this._instrumentDescriptor.unit,advice:this._instrumentDescriptor.advice})},t}(),on=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),oo=function(t,e){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(i){return function(s){var u=[i,s];if(r)throw TypeError("Generator is already executing.");for(;a;)try{if(r=1,n&&(o=2&u[0]?n.return:u[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,u[1])).done)return o;switch(n=0,o&&(u=[2&u[0],o.value]),u[0]){case 0:case 1:o=u;break;case 4:return a.label++,{value:u[1],done:!1};case 5:a.label++,n=u[1],u=[0];continue;case 7:u=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===u[0]||2===u[0])){a=0;continue}if(3===u[0]&&(!o||u[1]>o[0]&&u[1]<o[3])){a.label=u[1];break}if(6===u[0]&&a.label<o[1]){a.label=o[1],o=u;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(u);break}o[2]&&a.ops.pop(),a.trys.pop();continue}u=e.call(t,a)}catch(t){u=[6,t],n=0}finally{r=o=0}if(5&u[0])throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}}},oi=function(t){function e(){return t.call(this,nz)||this}return on(e,t),e}(function(){function t(t){this._hash=t,this._valueMap=new Map,this._keyMap=new Map}return t.prototype.get=function(t,e){return e??(e=this._hash(t)),this._valueMap.get(e)},t.prototype.getOrDefault=function(t,e){var r=this._hash(t);if(this._valueMap.has(r))return this._valueMap.get(r);var n=e();return this._keyMap.has(r)||this._keyMap.set(r,t),this._valueMap.set(r,n),n},t.prototype.set=function(t,e,r){r??(r=this._hash(t)),this._keyMap.has(r)||this._keyMap.set(r,t),this._valueMap.set(r,e)},t.prototype.has=function(t,e){return e??(e=this._hash(t)),this._valueMap.has(e)},t.prototype.keys=function(){var t,e;return oo(this,function(r){switch(r.label){case 0:e=(t=this._keyMap.entries()).next(),r.label=1;case 1:return!0===e.done?[3,3]:[4,[e.value[1],e.value[0]]];case 2:return r.sent(),e=t.next(),[3,1];case 3:return[2]}})},t.prototype.entries=function(){var t,e;return oo(this,function(r){switch(r.label){case 0:e=(t=this._valueMap.entries()).next(),r.label=1;case 1:return!0===e.done?[3,3]:[4,[this._keyMap.get(e.value[0]),e.value[1],e.value[0]]];case 2:return r.sent(),e=t.next(),[3,1];case 3:return[2]}})},Object.defineProperty(t.prototype,"size",{get:function(){return this._valueMap.size},enumerable:!1,configurable:!0}),t}()),oa=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},os=function(){function t(t){this._aggregator=t,this._activeCollectionStorage=new oi,this._cumulativeMemoStorage=new oi}return t.prototype.record=function(t,e,r,n){var o=this,i=this._activeCollectionStorage.getOrDefault(e,function(){return o._aggregator.createAccumulation(n)});i?.record(t)},t.prototype.batchCumulate=function(t,e){var r=this;Array.from(t.entries()).forEach(function(t){var n=oa(t,3),o=n[0],i=n[1],a=n[2],s=r._aggregator.createAccumulation(e);s?.record(i);var u=s;if(r._cumulativeMemoStorage.has(o,a)){var l=r._cumulativeMemoStorage.get(o,a);u=r._aggregator.diff(l,s)}if(r._activeCollectionStorage.has(o,a)){var c=r._activeCollectionStorage.get(o,a);u=r._aggregator.merge(c,u)}r._cumulativeMemoStorage.set(o,s,a),r._activeCollectionStorage.set(o,u,a)})},t.prototype.collect=function(){var t=this._activeCollectionStorage;return this._activeCollectionStorage=new oi,t},t}(),ou=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},ol=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},oc=function(){function t(t,e){var r=this;this._aggregator=t,this._unreportedAccumulations=new Map,this._reportHistory=new Map,e.forEach(function(t){r._unreportedAccumulations.set(t,[])})}return t.prototype.buildMetrics=function(e,r,n,o){this._stashAccumulations(n);var i,a=this._getMergedUnreportedAccumulations(e),s=a;if(this._reportHistory.has(e)){var u=this._reportHistory.get(e),l=u.collectionTime;s=(i=u.aggregationTemporality)===f.CUMULATIVE?t.merge(u.accumulations,a,this._aggregator):t.calibrateStartTime(u.accumulations,a,l)}else i=e.selectAggregationTemporality(r.type);this._reportHistory.set(e,{accumulations:s,collectionTime:o,aggregationTemporality:i});var c=Array.from(s.entries());if(0!==c.length)return this._aggregator.toMetricData(r,i,c,o)},t.prototype._stashAccumulations=function(t){var e,r,n=this._unreportedAccumulations.keys();try{for(var o=ou(n),i=o.next();!i.done;i=o.next()){var a=i.value,s=this._unreportedAccumulations.get(a);void 0===s&&(s=[],this._unreportedAccumulations.set(a,s)),s.push(t)}}catch(t){e={error:t}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(e)throw e.error}}},t.prototype._getMergedUnreportedAccumulations=function(e){var r,n,o=new oi,i=this._unreportedAccumulations.get(e);if(this._unreportedAccumulations.set(e,[]),void 0===i)return o;try{for(var a=ou(i),s=a.next();!s.done;s=a.next()){var u=s.value;o=t.merge(o,u,this._aggregator)}}catch(t){r={error:t}}finally{try{s&&!s.done&&(n=a.return)&&n.call(a)}finally{if(r)throw r.error}}return o},t.merge=function(t,e,r){for(var n=e.entries(),o=n.next();!0!==o.done;){var i=ol(o.value,3),a=i[0],s=i[1],u=i[2];if(t.has(a,u)){var l=t.get(a,u),c=r.merge(l,s);t.set(a,c,u)}else t.set(a,s,u);o=n.next()}return t},t.calibrateStartTime=function(t,e,r){var n,o;try{for(var i=ou(t.keys()),a=i.next();!a.done;a=i.next()){var s=ol(a.value,2),u=s[0],l=s[1],c=e.get(u,l);c?.setStartTime(r)}}catch(t){n={error:t}}finally{try{a&&!a.done&&(o=i.return)&&o.call(i)}finally{if(n)throw n.error}}return e},t}(),op=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),of=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},oh=function(t){function e(e,r,n,o){var i=t.call(this,e)||this;return i._attributesProcessor=n,i._deltaMetricStorage=new os(r),i._temporalMetricStorage=new oc(r,o),i}return op(e,t),e.prototype.record=function(t,e){var r=this,n=new oi;Array.from(t.entries()).forEach(function(t){var e=of(t,2),o=e[0],i=e[1];n.set(r._attributesProcessor.process(o),i)}),this._deltaMetricStorage.batchCumulate(n,e)},e.prototype.collect=function(t,e){var r=this._deltaMetricStorage.collect();return this._temporalMetricStorage.buildMetrics(t,this._instrumentDescriptor,r,e)},e}(or);function od(t,e){var r="";return t.unit!==e.unit&&(r+="	- Unit '"+t.unit+"' does not match '"+e.unit+`'
`),t.type!==e.type&&(r+="	- Type '"+t.type+"' does not match '"+e.type+`'
`),t.valueType!==e.valueType&&(r+="	- Value Type '"+t.valueType+"' does not match '"+e.valueType+`'
`),t.description!==e.description&&(r+="	- Description '"+t.description+"' does not match '"+e.description+`'
`),r}function o_(t,e){var r,n;return t.valueType!==e.valueType?"	- use valueType '"+t.valueType+"' on instrument creation or use an instrument name other than '"+e.name+"'":t.unit!==e.unit?"	- use unit '"+t.unit+"' on instrument creation or use an instrument name other than '"+e.name+"'":t.type!==e.type?(r=JSON.stringify({name:e.name,type:e.type,unit:e.unit}),"	- create a new view with a name other than '"+t.name+"' and InstrumentSelector '"+r+"'"):t.description!==e.description?(n=JSON.stringify({name:e.name,type:e.type,unit:e.unit}),"	- create a new view with a name other than '"+t.name+"' and InstrumentSelector '"+n+`'
    	- OR - create a new view with the name `+t.name+" and description '"+t.description+"' and InstrumentSelector "+n+`
    	- OR - create a new view with the name `+e.name+" and description '"+t.description+"' and InstrumentSelector "+n):""}eP();var oE=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},og=function(){function t(){this._sharedRegistry=new Map,this._perCollectorRegistry=new Map}return t.create=function(){return new t},t.prototype.getStorages=function(t){var e,r,n,o,i=[];try{for(var a=oE(this._sharedRegistry.values()),s=a.next();!s.done;s=a.next()){var u=s.value;i=i.concat(u)}}catch(t){e={error:t}}finally{try{s&&!s.done&&(r=a.return)&&r.call(a)}finally{if(e)throw e.error}}var l=this._perCollectorRegistry.get(t);if(null!=l)try{for(var c=oE(l.values()),p=c.next();!p.done;p=c.next()){var u=p.value;i=i.concat(u)}}catch(t){n={error:t}}finally{try{p&&!p.done&&(o=c.return)&&o.call(c)}finally{if(n)throw n.error}}return i},t.prototype.register=function(t){this._registerStorage(t,this._sharedRegistry)},t.prototype.registerForCollector=function(t,e){var r=this._perCollectorRegistry.get(t);null==r&&(r=new Map,this._perCollectorRegistry.set(t,r)),this._registerStorage(e,r)},t.prototype.findOrUpdateCompatibleStorage=function(t){var e=this._sharedRegistry.get(t.name);return void 0===e?null:this._findOrUpdateCompatibleStorage(t,e)},t.prototype.findOrUpdateCompatibleCollectorStorage=function(t,e){var r=this._perCollectorRegistry.get(t);if(void 0===r)return null;var n=r.get(e.name);return void 0===n?null:this._findOrUpdateCompatibleStorage(e,n)},t.prototype._registerStorage=function(t,e){var r=t.getInstrumentDescriptor(),n=e.get(r.name);if(void 0===n)return void e.set(r.name,[t]);n.push(t)},t.prototype._findOrUpdateCompatibleStorage=function(t,e){var r,n,o=null;try{for(var i=oE(e),a=i.next();!a.done;a=i.next()){var s=a.value,u=s.getInstrumentDescriptor();!function(t,e){var r,n;return r=t.name,n=e.name,r.toLowerCase()===n.toLowerCase()&&t.unit===e.unit&&t.type===e.type&&t.valueType===e.valueType}(u,t)?eL.diag.warn("A view or instrument with the name ",t.name,` has already been registered and is incompatible with another registered view.
`,`Details:
`,od(u,t),`To resolve the conflict:
`,o_(u,t)):(u.description!==t.description&&(t.description.length>u.description.length&&s.updateDescription(t.description),eL.diag.warn("A view or instrument with the name ",t.name,` has already been registered, but has a different description and is incompatible with another registered view.
`,`Details:
`,od(u,t),`The longer description will be used.
To resolve the conflict:`,o_(u,t))),o=s)}}catch(t){r={error:t}}finally{try{a&&!a.done&&(n=i.return)&&n.call(i)}finally{if(r)throw r.error}}return o},t}(),oT=function(){function t(t){this._backingStorages=t}return t.prototype.record=function(t,e,r,n){this._backingStorages.forEach(function(o){o.record(t,e,r,n)})},t}();eP(),eP();var oy=function(){function t(t,e){this._instrumentName=t,this._valueType=e,this._buffer=new oi}return t.prototype.observe=function(t,e){if(void 0===e&&(e={}),"number"!=typeof t)return void eL.diag.warn("non-number value provided to metric "+this._instrumentName+": "+t);(this._valueType!==eL.ValueType.INT||Number.isInteger(t)||(eL.diag.warn("INT value type cannot accept a floating-point value for "+this._instrumentName+", ignoring the fractional digits."),Number.isInteger(t=Math.trunc(t))))&&this._buffer.set(e,t)},t}(),om=function(){function t(){this._buffer=new Map}return t.prototype.observe=function(t,e,r){if(void 0===r&&(r={}),ot(t)){var n=this._buffer.get(t);if(null==n&&(n=new oi,this._buffer.set(t,n)),"number"!=typeof e)return void eL.diag.warn("non-number value provided to metric "+t._descriptor.name+": "+e);(t._descriptor.valueType!==eL.ValueType.INT||Number.isInteger(e)||(eL.diag.warn("INT value type cannot accept a floating-point value for "+t._descriptor.name+", ignoring the fractional digits."),Number.isInteger(e=Math.trunc(e))))&&n.set(r,e)}},t}(),ov=function(t,e,r,n){return new(r||(r=Promise))(function(o,i){function a(t){try{u(n.next(t))}catch(t){i(t)}}function s(t){try{u(n.throw(t))}catch(t){i(t)}}function u(t){var e;t.done?o(t.value):((e=t.value)instanceof r?e:new r(function(t){t(e)})).then(a,s)}u((n=n.apply(t,e||[])).next())})},oS=function(t,e){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(i){return function(s){var u=[i,s];if(r)throw TypeError("Generator is already executing.");for(;a;)try{if(r=1,n&&(o=2&u[0]?n.return:u[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,u[1])).done)return o;switch(n=0,o&&(u=[2&u[0],o.value]),u[0]){case 0:case 1:o=u;break;case 4:return a.label++,{value:u[1],done:!1};case 5:a.label++,n=u[1],u=[0];continue;case 7:u=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===u[0]||2===u[0])){a=0;continue}if(3===u[0]&&(!o||u[1]>o[0]&&u[1]<o[3])){a.label=u[1];break}if(6===u[0]&&a.label<o[1]){a.label=o[1],o=u;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(u);break}o[2]&&a.ops.pop(),a.trys.pop();continue}u=e.call(t,a)}catch(t){u=[6,t],n=0}finally{r=o=0}if(5&u[0])throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}}},ob=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},oA=function(t,e,r){if(r||2==arguments.length)for(var n,o=0,i=e.length;o<i;o++)!n&&o in e||(n||(n=Array.prototype.slice.call(e,0,o)),n[o]=e[o]);return t.concat(n||Array.prototype.slice.call(e))},oO=function(){function t(){this._callbacks=[],this._batchCallbacks=[]}return t.prototype.addCallback=function(t,e){this._findCallback(t,e)>=0||this._callbacks.push({callback:t,instrument:e})},t.prototype.removeCallback=function(t,e){var r=this._findCallback(t,e);r<0||this._callbacks.splice(r,1)},t.prototype.addBatchCallback=function(t,e){var r=new Set(e.filter(ot));if(0===r.size)return void eL.diag.error("BatchObservableCallback is not associated with valid instruments",e);this._findBatchCallback(t,r)>=0||this._batchCallbacks.push({callback:t,instruments:r})},t.prototype.removeBatchCallback=function(t,e){var r=new Set(e.filter(ot)),n=this._findBatchCallback(t,r);n<0||this._batchCallbacks.splice(n,1)},t.prototype.observe=function(t,e){return ov(this,void 0,void 0,function(){var r,n;return oS(this,function(o){switch(o.label){case 0:return r=this._observeCallbacks(t,e),n=this._observeBatchCallbacks(t,e),[4,function(t){return nH(this,void 0,void 0,function(){var e=this;return nX(this,function(r){return[2,Promise.all(t.map(function(t){return nH(e,void 0,void 0,function(){return nX(this,function(e){switch(e.label){case 0:return e.trys.push([0,2,,3]),[4,t];case 1:return[2,{status:"fulfilled",value:e.sent()}];case 2:return[2,{status:"rejected",reason:e.sent()}];case 3:return[2]}})})}))]})})}(oA(oA([],ob(r),!1),ob(n),!1))];case 1:return[2,o.sent().filter(nq).map(function(t){return t.reason})]}})})},t.prototype._observeCallbacks=function(t,e){var r=this;return this._callbacks.map(function(n){var o=n.callback,i=n.instrument;return ov(r,void 0,void 0,function(){var r,n;return oS(this,function(a){switch(a.label){case 0:return n=Promise.resolve(o(r=new oy(i._descriptor.name,i._descriptor.valueType))),null!=e&&(n=n$(n,e)),[4,n];case 1:return a.sent(),i._metricStorages.forEach(function(e){e.record(r._buffer,t)}),[2]}})})})},t.prototype._observeBatchCallbacks=function(t,e){var r=this;return this._batchCallbacks.map(function(n){var o=n.callback,i=n.instruments;return ov(r,void 0,void 0,function(){var r,n;return oS(this,function(a){switch(a.label){case 0:return n=Promise.resolve(o(r=new om)),null!=e&&(n=n$(n,e)),[4,n];case 1:return a.sent(),i.forEach(function(e){var n=r._buffer.get(e);null!=n&&e._metricStorages.forEach(function(e){e.record(n,t)})}),[2]}})})})},t.prototype._findCallback=function(t,e){return this._callbacks.findIndex(function(r){return r.callback===t&&r.instrument===e})},t.prototype._findBatchCallback=function(t,e){return this._batchCallbacks.findIndex(function(r){return r.callback===t&&function(t,e){var r,n;if(t.size!==e.size)return!1;try{for(var o=nK(t),i=o.next();!i.done;i=o.next()){var a=i.value;if(!e.has(a))return!1}}catch(t){r={error:t}}finally{try{i&&!i.done&&(n=o.return)&&n.call(o)}finally{if(r)throw r.error}}return!0}(r.instruments,e)})},t}(),oR=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),oL=function(t){function e(e,r,n,o){var i=t.call(this,e)||this;return i._attributesProcessor=n,i._deltaMetricStorage=new os(r),i._temporalMetricStorage=new oc(r,o),i}return oR(e,t),e.prototype.record=function(t,e,r,n){e=this._attributesProcessor.process(e,r),this._deltaMetricStorage.record(t,e,r,n)},e.prototype.collect=function(t,e){var r=this._deltaMetricStorage.collect();return this._temporalMetricStorage.buildMetrics(t,this._instrumentDescriptor,r,e)},e}(or),oP=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),oC=function(){function t(){}return t.Noop=function(){return oN},t}(),oI=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return oP(e,t),e.prototype.process=function(t,e){return t},e}(oC);!function(t){function e(e){var r=t.call(this)||this;return r._allowedAttributeNames=e,r}oP(e,t),e.prototype.process=function(t,e){var r=this,n={};return Object.keys(t).filter(function(t){return r._allowedAttributeNames.includes(t)}).forEach(function(e){return n[e]=t[e]}),n}}(oC);var oN=new oI,ow=function(t,e){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(i){return function(s){var u=[i,s];if(r)throw TypeError("Generator is already executing.");for(;a;)try{if(r=1,n&&(o=2&u[0]?n.return:u[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,u[1])).done)return o;switch(n=0,o&&(u=[2&u[0],o.value]),u[0]){case 0:case 1:o=u;break;case 4:return a.label++,{value:u[1],done:!1};case 5:a.label++,n=u[1],u=[0];continue;case 7:u=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===u[0]||2===u[0])){a=0;continue}if(3===u[0]&&(!o||u[1]>o[0]&&u[1]<o[3])){a.label=u[1];break}if(6===u[0]&&a.label<o[1]){a.label=o[1],o=u;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(u);break}o[2]&&a.ops.pop(),a.trys.pop();continue}u=e.call(t,a)}catch(t){u=[6,t],n=0}finally{r=o=0}if(5&u[0])throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}}},oM=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},oD=function(){function t(t,e){this._meterProviderSharedState=t,this._instrumentationScope=e,this.metricStorageRegistry=new og,this.observableRegistry=new oO,this.meter=new oe(this)}return t.prototype.registerMetricStorage=function(t){var e=this._registerMetricStorage(t,oL);return 1===e.length?e[0]:new oT(e)},t.prototype.registerAsyncMetricStorage=function(t){return this._registerMetricStorage(t,oh)},t.prototype.collect=function(t,e,r){var n,o,i,a;return n=this,o=void 0,i=void 0,a=function(){var n,o,i;return ow(this,function(a){switch(a.label){case 0:return[4,this.observableRegistry.observe(e,r?.timeoutMillis)];case 1:return n=a.sent(),0===(o=this.metricStorageRegistry.getStorages(t)).length?[2,null]:0===(i=o.map(function(r){return r.collect(t,e)}).filter(nW)).length?[2,{errors:n}]:[2,{scopeMetrics:{scope:this._instrumentationScope,metrics:i},errors:n}]}})},new(i||(i=Promise))(function(t,e){function r(t){try{u(a.next(t))}catch(t){e(t)}}function s(t){try{u(a.throw(t))}catch(t){e(t)}}function u(e){var n;e.done?t(e.value):((n=e.value)instanceof i?n:new i(function(t){t(n)})).then(r,s)}u((a=a.apply(n,o||[])).next())})},t.prototype._registerMetricStorage=function(t,e){var r=this,n=this._meterProviderSharedState.viewRegistry.findViews(t,this._instrumentationScope).map(function(n){var o,i,a={name:null!=(o=n.name)?o:t.name,description:null!=(i=n.description)?i:t.description,type:t.type,unit:t.unit,valueType:t.valueType,advice:t.advice},s=r.metricStorageRegistry.findOrUpdateCompatibleStorage(a);if(null!=s)return s;var u=n.aggregation.createAggregator(a),l=new e(a,u,n.attributesProcessor,r._meterProviderSharedState.metricCollectors);return r.metricStorageRegistry.register(l),l});if(0===n.length){var o=this._meterProviderSharedState.selectAggregations(t.type).map(function(n){var o=oM(n,2),i=o[0],a=o[1],s=r.metricStorageRegistry.findOrUpdateCompatibleCollectorStorage(i,t);if(null!=s)return s;var u=a.createAggregator(t),l=new e(t,u,oC.Noop(),[i]);return r.metricStorageRegistry.registerForCollector(i,l),l});n=n.concat(o)}return n},t}(),ox=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},oU=function(){function t(t){this.resource=t,this.viewRegistry=new nJ,this.metricCollectors=[],this.meterSharedStates=new Map}return t.prototype.getMeterSharedState=function(t){var e,r,n=t.name+":"+(null!=(e=t.version)?e:"")+":"+(null!=(r=t.schemaUrl)?r:""),o=this.meterSharedStates.get(n);return null==o&&(o=new oD(this,t),this.meterSharedStates.set(n,o)),o},t.prototype.selectAggregations=function(t){var e,r,n=[];try{for(var o=ox(this.metricCollectors),i=o.next();!i.done;i=o.next()){var a=i.value;n.push([a,a.selectAggregation(t)])}}catch(t){e={error:t}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(e)throw e.error}}return n},t}(),oB=eO(rc()),oG=function(t,e,r,n){return new(r||(r=Promise))(function(o,i){function a(t){try{u(n.next(t))}catch(t){i(t)}}function s(t){try{u(n.throw(t))}catch(t){i(t)}}function u(t){var e;t.done?o(t.value):((e=t.value)instanceof r?e:new r(function(t){t(e)})).then(a,s)}u((n=n.apply(t,e||[])).next())})},oV=function(t,e){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(i){return function(s){var u=[i,s];if(r)throw TypeError("Generator is already executing.");for(;a;)try{if(r=1,n&&(o=2&u[0]?n.return:u[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,u[1])).done)return o;switch(n=0,o&&(u=[2&u[0],o.value]),u[0]){case 0:case 1:o=u;break;case 4:return a.label++,{value:u[1],done:!1};case 5:a.label++,n=u[1],u=[0];continue;case 7:u=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===u[0]||2===u[0])){a=0;continue}if(3===u[0]&&(!o||u[1]>o[0]&&u[1]<o[3])){a.label=u[1];break}if(6===u[0]&&a.label<o[1]){a.label=o[1],o=u;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(u);break}o[2]&&a.ops.pop(),a.trys.pop();continue}u=e.call(t,a)}catch(t){u=[6,t],n=0}finally{r=o=0}if(5&u[0])throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}}},ok=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},oj=function(t,e,r){if(r||2==arguments.length)for(var n,o=0,i=e.length;o<i;o++)!n&&o in e||(n||(n=Array.prototype.slice.call(e,0,o)),n[o]=e[o]);return t.concat(n||Array.prototype.slice.call(e))},oF=function(){function t(t,e){this._sharedState=t,this._metricReader=e}return t.prototype.collect=function(t){return oG(this,void 0,void 0,function(){var e,r,n,o=this;return oV(this,function(i){switch(i.label){case 0:return e=(0,oB.millisToHrTime)(Date.now()),r=[],n=[],[4,Promise.all(Array.from(this._sharedState.meterSharedStates.values()).map(function(i){return oG(o,void 0,void 0,function(){var o;return oV(this,function(a){switch(a.label){case 0:return[4,i.collect(this,e,t)];case 1:return o=a.sent(),o?.scopeMetrics!=null&&r.push(o.scopeMetrics),o?.errors!=null&&n.push.apply(n,oj([],ok(o.errors),!1)),[2]}})})}))];case 1:return i.sent(),[2,{resourceMetrics:{resource:this._sharedState.resource,scopeMetrics:r},errors:n}]}})})},t.prototype.forceFlush=function(t){return oG(this,void 0,void 0,function(){return oV(this,function(e){switch(e.label){case 0:return[4,this._metricReader.forceFlush(t)];case 1:return e.sent(),[2]}})})},t.prototype.shutdown=function(t){return oG(this,void 0,void 0,function(){return oV(this,function(e){switch(e.label){case 0:return[4,this._metricReader.shutdown(t)];case 1:return e.sent(),[2]}})})},t.prototype.selectAggregationTemporality=function(t){return this._metricReader.selectAggregationTemporality(t)},t.prototype.selectAggregation=function(t){return this._metricReader.selectAggregation(t)},t}(),oH=function(t,e,r,n){return new(r||(r=Promise))(function(o,i){function a(t){try{u(n.next(t))}catch(t){i(t)}}function s(t){try{u(n.throw(t))}catch(t){i(t)}}function u(t){var e;t.done?o(t.value):((e=t.value)instanceof r?e:new r(function(t){t(e)})).then(a,s)}u((n=n.apply(t,e||[])).next())})},oX=function(t,e){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(i){return function(s){var u=[i,s];if(r)throw TypeError("Generator is already executing.");for(;a;)try{if(r=1,n&&(o=2&u[0]?n.return:u[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,u[1])).done)return o;switch(n=0,o&&(u=[2&u[0],o.value]),u[0]){case 0:case 1:o=u;break;case 4:return a.label++,{value:u[1],done:!1};case 5:a.label++,n=u[1],u=[0];continue;case 7:u=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===u[0]||2===u[0])){a=0;continue}if(3===u[0]&&(!o||u[1]>o[0]&&u[1]<o[3])){a.label=u[1];break}if(6===u[0]&&a.label<o[1]){a.label=o[1],o=u;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(u);break}o[2]&&a.ops.pop(),a.trys.pop();continue}u=e.call(t,a)}catch(t){u=[6,t],n=0}finally{r=o=0}if(5&u[0])throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}}},oK=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},oW=function(){function t(t){this._shutdown=!1;var e,r,n,o=np.default().merge(null!=(n=t?.resource)?n:np.empty());if(this._sharedState=new oU(o),t?.views!=null&&t.views.length>0)try{for(var i=oK(t.views),a=i.next();!a.done;a=i.next()){var s=a.value;this._sharedState.viewRegistry.addView(s)}}catch(t){e={error:t}}finally{try{a&&!a.done&&(r=i.return)&&r.call(i)}finally{if(e)throw e.error}}}return t.prototype.getMeter=function(t,e,r){return void 0===e&&(e=""),void 0===r&&(r={}),this._shutdown?(eL.diag.warn("A shutdown MeterProvider cannot provide a Meter"),(0,eL.createNoopMeter)()):this._sharedState.getMeterSharedState({name:t,version:e,schemaUrl:r.schemaUrl}).meter},t.prototype.addMetricReader=function(t){var e=new oF(this._sharedState,t);t.setMetricProducer(e),this._sharedState.metricCollectors.push(e)},t.prototype.shutdown=function(t){return oH(this,void 0,void 0,function(){return oX(this,function(e){switch(e.label){case 0:return this._shutdown?(eL.diag.warn("shutdown may only be called once per MeterProvider"),[2]):(this._shutdown=!0,[4,Promise.all(this._sharedState.metricCollectors.map(function(e){return e.shutdown(t)}))]);case 1:return e.sent(),[2]}})})},t.prototype.forceFlush=function(t){return oH(this,void 0,void 0,function(){return oX(this,function(e){switch(e.label){case 0:return this._shutdown?(eL.diag.warn("invalid attempt to force flush after MeterProvider shutdown"),[2]):[4,Promise.all(this._sharedState.metricCollectors.map(function(e){return e.forceFlush(t)}))];case 1:return e.sent(),[2]}})})},t}(),oz=eO(eV(),1),oY=eO(rT(),1),o$=eO(rc(),1),oq="http.host",oQ="http.status_code",oZ="http.response_content_length_uncompressed";eP();var oJ=Symbol.for("@vercel/request-context");function o0(){return globalThis[oJ]?.get()}function o1(t){return Object.fromEntries(Object.entries(t).filter(([t,e])=>void 0!==e))}var o2={keys:t=>[],get:(t,e)=>t[e.toLocaleLowerCase()]};function o3(t){return(t&eL.TraceFlags.SAMPLED)!=0}eP();var o4=class{constructor(t,e){this.processors=t,this.attributesFromHeaders=e,this.rootSpanIds=new Map,this.waitSpanEnd=new Map}forceFlush(){return Promise.all(this.processors.map(t=>t.forceFlush().catch(t=>{eL.diag.error("@vercel/otel: forceFlush failed:",t)}))).then(()=>{})}shutdown(){return Promise.all(this.processors.map(t=>t.shutdown().catch(()=>{}))).then(()=>{})}onStart(t,e){let{traceId:r,spanId:n,traceFlags:o}=t.spanContext(),i=!t.parentSpanId||!this.rootSpanIds.has(r);if(i?this.rootSpanIds.set(r,{rootSpanId:n,open:[]}):this.rootSpanIds.get(r)?.open.push(t),i&&o3(o)){let e=o0(),n=function(t=o0(),e){var r;if(!t)return;let n=e?function(t,e){if("function"==typeof t)return t(e,o2);let r={};for(let[n,o]of Object.entries(t)){let t=e[o.toLocaleLowerCase()];void 0!==t&&(r[n]=t)}return r}(e,t.headers):void 0;return o1({[oq]:t.headers.host,"http.user_agent":t.headers["user-agent"],"http.referer":t.headers.referer,"vercel.request_id":(r=t.headers["x-vercel-id"])?r.split("::").at(-1):void 0,"vercel.matched_path":t.headers["x-matched-path"],"vercel.edge_region":process.env.VERCEL_REGION,...n})}(e,this.attributesFromHeaders);n&&t.setAttributes(n),e&&e.waitUntil(async()=>{if(this.rootSpanIds.has(r)){let t=new Promise(t=>{this.waitSpanEnd.set(r,t)}),e;await Promise.race([t,new Promise(t=>{e=setTimeout(()=>{this.waitSpanEnd.delete(r),t(void 0)},50)})]),e&&clearTimeout(e)}return this.forceFlush()})}for(let r of this.processors)r.onStart(t,e)}onEnd(t){let{traceId:e,spanId:r,traceFlags:n}=t.spanContext(),o=o3(n),i=this.rootSpanIds.get(e),a=i?.rootSpanId===r;if(o){let e=function(t){let{kind:e,attributes:r}=t,{"operation.name":n,"resource.name":o,"span.type":i,"next.span_type":a,"http.method":s,"http.route":u}=r;if(n)return;let l=o??(s&&"string"==typeof s&&u&&"string"==typeof u?`${s} ${u}`:u);if(t.kind===eL.SpanKind.SERVER&&s&&u&&"string"==typeof s&&"string"==typeof u)return{"operation.name":"web.request","resource.name":l};let c=t.instrumentationLibrary.name,p=a??i;if(p&&"string"==typeof p){let t=o6(c,p);return u?{"operation.name":t,"resource.name":l}:{"operation.name":t}}return{"operation.name":o6(c,e===eL.SpanKind.INTERNAL?"":o8[e])}}(t);e&&Object.assign(t.attributes,e)}if(a){if(this.rootSpanIds.delete(e),i.open.length>0){for(let t of i.open)if(!t.ended&&t.spanContext().spanId!==r)try{t.end()}catch(t){eL.diag.error("@vercel/otel: onEnd failed:",t)}}}else if(i)for(let t=0;t<i.open.length;t++)i.open[t]?.spanContext().spanId===r&&i.open.splice(t,1);for(let e of this.processors)e.onEnd(t);if(a){let t=this.waitSpanEnd.get(e);t&&(this.waitSpanEnd.delete(e),t())}}},o8={[eL.SpanKind.INTERNAL]:"internal",[eL.SpanKind.SERVER]:"server",[eL.SpanKind.CLIENT]:"client",[eL.SpanKind.PRODUCER]:"producer",[eL.SpanKind.CONSUMER]:"consumer"};function o6(t,e){if(!t)return e;let r=t.replace(/[ @./]/g,"_");return r.startsWith("_")&&(r=r.slice(1)),e?`${r}.${e}`:r}var o5=eO(rS(),1),o7=eO(rA(),1);eP();var o9=class extends o7.OTLPExporterBase{constructor(t={}){super(t),t.headers&&(this._headers=t.headers)}onShutdown(){eL.diag.debug("@vercel/otel/otlp: onShutdown")}onInit(){eL.diag.debug("@vercel/otel/otlp: onInit")}send(t,e,r){if(this._shutdownOnce.isCalled)return void eL.diag.debug("@vercel/otel/otlp: Shutdown already started. Cannot send objects");let n=this.convert(t),o,i,a;try{({body:o,contentType:i,headers:a}=this.toMessage(n))}catch(t){eL.diag.warn("@vercel/otel/otlp: no proto",t);return}let s=fetch(this.url,{method:"POST",body:o,headers:{...this._headers,...a,"Content-Type":i,"User-Agent":"OTel-OTLP-Exporter-JavaScript/0.46.0"},next:{internal:!0}}).then(t=>{eL.diag.debug("@vercel/otel/otlp: onSuccess",t.status,t.statusText),e(),t.arrayBuffer().catch(()=>{})}).catch(t=>{eL.diag.error("@vercel/otel/otlp: onError",t),r(t)}).finally(()=>{let t=this._sendingPromises.indexOf(s);this._sendingPromises.splice(t,1)});this._sendingPromises.push(s)}getDefaultUrl(t){throw Error("Method not implemented.")}};function it(t){return"string"==typeof t.url?t.url:"http://localhost:4318/v1/traces"}var ie=class{constructor(t={}){this.impl=new ir(t)}export(t,e){this.impl.export(t,e)}shutdown(){return this.impl.shutdown()}forceFlush(){return this.impl.forceFlush()}},ir=class extends o9{convert(t){return(0,o5.createExportTraceServiceRequest)(t,{useHex:!0,useLongBits:!1})}toMessage(t){return{body:JSON.stringify(t),contentType:"application/json"}}getDefaultUrl(t){return it(t)}},io=eO(rS(),1),ii=eO(rF(),1);function ia(t,e){return null!=t.key&&e.uint32(10).string(t.key),null!=t.value&&(function t(e,r){return null!=e.stringValue&&r.uint32(10).string(e.stringValue),null!=e.boolValue&&r.uint32(16).bool(e.boolValue),null!=e.intValue&&r.uint32(24).int64(e.intValue),null!=e.doubleValue&&r.uint32(33).double(e.doubleValue),null!=e.arrayValue&&(function(e,r){if(null!=e.values&&e.values.length)for(let n=0;n<e.values.length;++n)t(e.values[n],r.uint32(10).fork()).ldelim();return r})(e.arrayValue,r.uint32(42).fork()).ldelim(),null!=e.kvlistValue&&(function(t,e){if(null!=t.values&&t.values.length)for(let r=0;r<t.values.length;++r)ia(t.values[r],e.uint32(10).fork()).ldelim();return e})(e.kvlistValue,r.uint32(50).fork()).ldelim(),null!=e.bytesValue&&r.uint32(58).bytes(e.bytesValue),r})(t.value,e.uint32(18).fork()).ldelim(),e}var is=class{constructor(t={}){this.impl=new iu(t)}export(t,e){this.impl.export(t,e)}shutdown(){return this.impl.shutdown()}forceFlush(){return this.impl.forceFlush()}},iu=class extends o9{convert(t){return(0,io.createExportTraceServiceRequest)(t,void 0)}toMessage(t){let e;return{body:(function(t,e){if(null!=t.resourceSpans&&t.resourceSpans.length)for(let r=0;r<t.resourceSpans.length;++r)(function(t,e){if(null!=t.resource&&(function(t,e){if(null!=t.attributes&&t.attributes.length)for(let r=0;r<t.attributes.length;++r)ia(t.attributes[r],e.uint32(10).fork()).ldelim();return null!=t.droppedAttributesCount&&e.uint32(16).uint32(t.droppedAttributesCount),e})(t.resource,e.uint32(10).fork()).ldelim(),null!=t.scopeSpans&&t.scopeSpans.length)for(let r=0;r<t.scopeSpans.length;++r)(function(t,e){if(null!=t.scope&&(function(t,e){if(null!=t.name&&e.uint32(10).string(t.name),null!=t.version&&e.uint32(18).string(t.version),null!=t.attributes&&t.attributes.length)for(let r=0;r<t.attributes.length;++r)ia(t.attributes[r],e.uint32(26).fork()).ldelim();return null!=t.droppedAttributesCount&&e.uint32(32).uint32(t.droppedAttributesCount),e})(t.scope,e.uint32(10).fork()).ldelim(),null!=t.spans&&t.spans.length)for(let r=0;r<t.spans.length;++r)(function(t,e){var r,n;if(null!=t.traceId&&e.uint32(10).bytes(t.traceId),null!=t.spanId&&e.uint32(18).bytes(t.spanId),null!=t.traceState&&e.uint32(26).string(t.traceState),null!=t.parentSpanId&&e.uint32(34).bytes(t.parentSpanId),null!=t.name&&e.uint32(42).string(t.name),null!=t.kind&&e.uint32(48).int32(t.kind),null!=t.startTimeUnixNano&&e.uint32(57).fixed64(t.startTimeUnixNano),null!=t.endTimeUnixNano&&e.uint32(65).fixed64(t.endTimeUnixNano),null!=t.attributes&&t.attributes.length)for(let r=0;r<t.attributes.length;++r)ia(t.attributes[r],e.uint32(74).fork()).ldelim();if(null!=t.droppedAttributesCount&&e.uint32(80).uint32(t.droppedAttributesCount),null!=t.events&&t.events.length)for(let r=0;r<t.events.length;++r)(function(t,e){if(null!=t.timeUnixNano&&e.uint32(9).fixed64(t.timeUnixNano),null!=t.name&&e.uint32(18).string(t.name),null!=t.attributes&&t.attributes.length)for(let r=0;r<t.attributes.length;++r)ia(t.attributes[r],e.uint32(26).fork()).ldelim();return null!=t.droppedAttributesCount&&e.uint32(32).uint32(t.droppedAttributesCount),e})(t.events[r],e.uint32(90).fork()).ldelim();if(null!=t.droppedEventsCount&&e.uint32(96).uint32(t.droppedEventsCount),null!=t.links&&t.links.length)for(let r=0;r<t.links.length;++r)(function(t,e){if(null!=t.traceId&&e.uint32(10).bytes(t.traceId),null!=t.spanId&&e.uint32(18).bytes(t.spanId),null!=t.traceState&&e.uint32(26).string(t.traceState),null!=t.attributes&&t.attributes.length)for(let r=0;r<t.attributes.length;++r)ia(t.attributes[r],e.uint32(34).fork()).ldelim();return null!=t.droppedAttributesCount&&e.uint32(40).uint32(t.droppedAttributesCount),e})(t.links[r],e.uint32(106).fork()).ldelim();return null!=t.droppedLinksCount&&e.uint32(112).uint32(t.droppedLinksCount),null!=t.status&&(r=t.status,n=e.uint32(122).fork(),null!=r.message&&n.uint32(18).string(r.message),null!=r.code&&n.uint32(24).int32(r.code),n).ldelim(),e})(t.spans[r],e.uint32(18).fork()).ldelim();return null!=t.schemaUrl&&e.uint32(26).string(t.schemaUrl),e})(t.scopeSpans[r],e.uint32(18).fork()).ldelim();return null!=t.schemaUrl&&e.uint32(26).string(t.schemaUrl),e})(t.resourceSpans[r],e.uint32(10).fork()).ldelim()}(t,e=new ii.Writer),e.finish()),contentType:"application/x-protobuf",headers:{accept:"application/x-protobuf"}}}getDefaultUrl(t){return it(t)}};eP();var il=class{constructor(t={}){this.instrumentationName="@vercel/otel/fetch",this.instrumentationVersion="1.0.0",this.config=t}getConfig(){return this.config}setConfig(){}setTracerProvider(t){this.tracerProvider=t}setMeterProvider(){}shouldIgnore(t,e){let r=this.config.ignoreUrls??[];if(e?.opentelemetry?.ignore!==void 0)return e.opentelemetry.ignore;if(0===r.length)return!1;let n=t.toString();return r.some(t=>"string"==typeof t?"*"===t||n.startsWith(t):t.test(n))}shouldPropagate(t,e){let r=process.env.VERCEL_URL||process.env.NEXT_PUBLIC_VERCEL_URL||null,n=process.env.VERCEL_BRANCH_URL||process.env.NEXT_PUBLIC_VERCEL_BRANCH_URL||null,o=this.config.propagateContextUrls??[],i=this.config.dontPropagateContextUrls??[];if(e?.opentelemetry?.propagateContext)return e.opentelemetry.propagateContext;let a=t.toString();return!(i.length>0&&i.some(t=>"string"==typeof t?"*"===t||a.startsWith(t):t.test(a)))&&(!!r&&"https:"===t.protocol&&(t.host===r||t.host===n||t.host===o0()?.headers.host)||!r&&"http:"===t.protocol&&"localhost"===t.hostname||o.some(t=>"string"==typeof t?"*"===t||a.startsWith(t):t.test(a)))}startSpan({tracer:t,url:e,fetchType:r,method:n="GET",name:o,attributes:i={}}){var a;let s,u=this.config.resourceNameTemplate,l={"http.method":n,"http.url":e.toString(),[oq]:e.host,"http.scheme":e.protocol.replace(":",""),"net.peer.name":e.hostname,"net.peer.port":e.port},c=u?u.replace(/\{(?<temp1>[^{}]+)\}/g,(t,e)=>{let r=l[e];return void 0!==r?String(r):t}):-1===(s=(a=e.toString()).indexOf("?"))?a:a.substring(0,s),p=o??`${r} ${n} ${e.toString()}`,f=eL.context.active();return t.startSpan(p,{kind:eL.SpanKind.CLIENT,attributes:{...l,"operation.name":`${r}.${n}`,"http.client.name":r,"resource.name":c,...i}},f)}instrumentHttp(t,e){let{tracerProvider:r}=this;if(!r)return;let n=r.getTracer(this.instrumentationName,this.instrumentationVersion),{attributesFromRequestHeaders:o,attributesFromResponseHeaders:i}=this.config,a=t.request,s=t.get,u=t=>(r,a,s)=>{let u,l={},c;if("string"==typeof r||r instanceof URL?(u=new URL(r.toString()),"function"==typeof a?c=a:a&&"function"==typeof s?(l=a,c=s):a&&(l=a)):(l=r,"function"==typeof a&&(c=a),u=function(t,e){let r;if(t.socketPath)throw Error("Cannot construct a network URL: options.socketPath is specified, indicating a Unix domain socket.");let n=t.protocol??e;n&&!n.endsWith(":")&&(n+=":");let o=t.hostname,i=t.port??t.defaultPort;if(!o&&t.host){let e=t.host.split(":");o=e[0];let r=e[1];if(e.length>1&&r&&void 0===i){let t=parseInt(r,10);isNaN(t)||(i=t)}}if(o||(o="localhost"),void 0!==i&&""!==i){let t=parseInt(String(i),10);r=isNaN(t)?"https:"===n?443:80:t}else r="https:"===n?443:80;let a=new URL(t.path||"/",`${n}//${o}:${r}`);if(t.auth){let e=t.auth.split(":");a.username=decodeURIComponent(e[0]||""),e.length>1&&(a.password=decodeURIComponent(e[1]||""))}return a}(l,e)),this.shouldIgnore(u))return t.apply(this,[u,l,c]);let p=this.startSpan({tracer:n,url:u,fetchType:"http",method:l.method||"GET"});if(!p.isRecording()||!o3(p.spanContext().traceFlags))return p.end(),t.apply(this,[u,l,c]);if(this.shouldPropagate(u)){let t=eL.context.active(),e=eL.trace.setSpan(t,p);eL.propagation.inject(e,l.headers||{},ih)}o&&iE(p,o,l.headers||{},id);try{let e=Date.now(),r=t.apply(this,[u,l,c]);return r.prependListener("response",t=>{let n=Date.now()-e;p.setAttribute("http.response_time",n),void 0!==t.statusCode?(p.setAttribute(oQ,t.statusCode),t.statusCode>=500&&i_(p,`Status: ${t.statusCode}`)):i_(p,"Response status code is undefined"),i&&iE(p,i,t.headers,id),1>=r.listenerCount("response")&&t.resume(),t.on("end",()=>{let e,r=t.statusCode;e=t.aborted&&!t.complete?{code:eL.SpanStatusCode.ERROR}:r&&r>=100&&r<500?{code:eL.SpanStatusCode.UNSET}:{code:eL.SpanStatusCode.ERROR},p.setStatus(e),p.isRecording()&&(t.headers["content-length"]&&p.setAttribute(oZ,t.headers["content-length"]),p.end())})}),r.on("error",t=>{p.isRecording()&&(i_(p,t),p.end())}),r.on("close",()=>{p.isRecording()&&p.end()}),r}catch(t){throw i_(p,t),p.end(),t}};t.request=u(a),t.get=u(s)}instrumentFetch(){let{tracerProvider:t}=this;if(!t)return;let e=t.getTracer(this.instrumentationName,this.instrumentationVersion),{attributesFromRequestHeaders:r,attributesFromResponseHeaders:n}=this.config;process.env.NEXT_OTEL_FETCH_DISABLED="1";let o=globalThis.fetch;this.originalFetch=o;let i=async(t,i)=>{if(i?.next?.internal)return o(t,i);let a=new Request(t instanceof Request?t.clone():t,i),s=new URL(a.url);if(this.shouldIgnore(s,i))return o(t,i);let u=this.startSpan({tracer:e,url:s,fetchType:"fetch",method:a.method,name:i?.opentelemetry?.spanName,attributes:i?.opentelemetry?.attributes});if(!u.isRecording()||!o3(u.spanContext().traceFlags))return u.end(),o(t,i);if(this.shouldPropagate(s,i)){let t=eL.context.active(),e=eL.trace.setSpan(t,u);eL.propagation.inject(e,a.headers,ic)}r&&iE(u,r,a.headers,ip);try{let e=Date.now();i?.body&&i.body instanceof FormData&&a.headers.delete("content-type");let r=await o(t,{...i,headers:a.headers}),s=Date.now()-e;return u.setAttribute(oQ,r.status),u.setAttribute("http.response_time",s),n&&iE(u,n,r.headers,ip),r.status>=500&&i_(u,`Status: ${r.status} (${r.statusText})`),r.body?(function(t){let e=0,r=t.clone().body?.getReader();if(!r)return Promise.resolve(0);let n=()=>r.read().then(({done:t,value:r})=>{if(!t)return e+=r.byteLength,n()});return n().then(()=>e)})(r).then(t=>{u.isRecording()&&(u.setAttribute(oZ,t),u.end())},t=>{u.isRecording()&&(i_(u,t),u.end())}):u.end(),r}catch(t){throw i_(u,t),u.end(),t}};globalThis.fetch=i}enable(){this.disable(),this.instrumentFetch();try{let t=em("node:http"),e=em("node:https");this.instrumentHttp(t,"http:"),this.instrumentHttp(e,"https:")}catch{}}disable(){this.originalFetch&&(globalThis.fetch=this.originalFetch)}},ic={set(t,e,r){t.set(e,r)}},ip={get(t,e){let r=t.get(e);if(null!==r)return r.includes(",")?r.split(",").map(t=>t.trimStart()):r},keys(t){let e=[];return t.forEach((t,r)=>{e.push(r)}),e}},ih={set(t,e,r){t[e.toLowerCase()]=r}},id={get:(t,e)=>t[e.toLowerCase()],keys:t=>Object.keys(t)};function i_(t,e){if(e instanceof Error)t.recordException(e),t.setStatus({code:eL.SpanStatusCode.ERROR,message:e.message});else{let r=String(e);t.setStatus({code:eL.SpanStatusCode.ERROR,message:r})}}function iE(t,e,r,n){for(let[o,i]of Object.entries(e)){let e=n.get(r,i);void 0!==e&&t.setAttribute(o,e)}}eP();var ig=eO(rc(),1),iT="traceparent",iy="tracestate",im=class{fields(){return[iT,iy]}inject(t,e,r){let n=eL.trace.getSpanContext(t);if(!n||(0,ig.isTracingSuppressed)(t)||!(0,eL.isSpanContextValid)(n))return;let o=`00-${n.traceId}-${n.spanId}-0${Number(n.traceFlags||0).toString(16)}`;r.set(e,iT,o),n.traceState&&r.set(e,iy,n.traceState.serialize())}extract(t,e,r){let n=r.get(e,iT);if(!n)return t;let o=Array.isArray(n)?n[0]:n;if("string"!=typeof o)return t;let i=function(t){let[e,r,n,o,i]=t.split("-");return e&&r&&n&&o&&2===e.length&&32===r.length&&16===n.length&&2===o.length&&("00"!==e||!i)?{traceId:r,spanId:n,traceFlags:parseInt(o,16)}:null}(o);if(!i)return t;i.isRemote=!0;let a=r.get(e,iy);if(a){let t=Array.isArray(a)?a.join(","):a;i.traceState=(0,eL.createTraceState)("string"==typeof t?t:void 0)}return eL.trace.setSpanContext(t,i)}};eP();var iv=class{fields(){return[]}inject(){}extract(t){let e=o0();if(!e?.telemetry)return eL.diag.warn("@vercel/otel: Vercel telemetry extension not found."),t;let{rootSpanContext:r}=e.telemetry;return r?(eL.diag.debug("@vercel/otel: Extracted root SpanContext from Vercel request context.",r),eL.trace.setSpanContext(t,{...r,isRemote:!0,traceFlags:r.traceFlags||eL.TraceFlags.SAMPLED})):t}};eP();var iS=eO(rc(),1),ib=eO(rc()),iA=BigInt(1e9);function iO(t){return BigInt(t[0])*iA+BigInt(t[1])}function iR(t){var e;return{low:Number(BigInt.asUintN(32,e=iO(t))),high:Number(BigInt.asUintN(32,e>>BigInt(32)))}}var iL="u">typeof BigInt?function(t){return iO(t).toString()}:ib.hrTimeToNanoseconds;function iP(t){return t}function iC(t){if(void 0!==t)return(0,ib.hexToBase64)(t)}var iI={encodeHrTime:iR,encodeSpanContext:ib.hexToBase64,encodeOptionalSpanContext:iC},iN=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a};function iw(t){return Object.keys(t).map(function(e){return function t(e,r){return{key:e,value:function e(r){var n=typeof r;return"string"===n?{stringValue:r}:"number"===n?Number.isInteger(r)?{intValue:r}:{doubleValue:r}:"boolean"===n?{boolValue:r}:r instanceof Uint8Array?{bytesValue:r}:Array.isArray(r)?{arrayValue:{values:r.map(e)}}:"object"===n&&null!=r?{kvlistValue:{values:Object.entries(r).map(function(e){var r=iN(e,2);return t(r[0],r[1])})}}:{}}(r)}}(e,t[e])})}var iM=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},iD=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},ix=class{export(t,e){let r=o0();if(!r?.telemetry){eL.diag.warn("@vercel/otel: no telemetry context found"),e({code:iS.ExportResultCode.SUCCESS,error:void 0});return}try{var n;let o=(n={useHex:!0,useLongBits:!1},{resourceSpans:function(t,e){for(var r=function(t){var e,r,n=new Map;try{for(var o=iM(t),i=o.next();!i.done;i=o.next()){var a=i.value,s=n.get(a.resource);s||(s=new Map,n.set(a.resource,s));var u=a.instrumentationLibrary.name+"@"+(a.instrumentationLibrary.version||"")+":"+(a.instrumentationLibrary.schemaUrl||""),l=s.get(u);l||(l=[],s.set(u,l)),l.push(a)}}catch(t){e={error:t}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(e)throw e.error}}return n}(t),n=[],o=r.entries(),i=o.next();!i.done;){for(var a=iD(i.value,2),s=a[0],u=a[1],l=[],c=u.values(),p=c.next();!p.done;){var f=p.value;if(f.length>0){var h=f[0].instrumentationLibrary,d=h.name,_=h.version,E=h.schemaUrl,g=f.map(function(t){return function(t,e){var r,n=t.spanContext(),o=t.status;return{traceId:e.encodeSpanContext(n.traceId),spanId:e.encodeSpanContext(n.spanId),parentSpanId:e.encodeOptionalSpanContext(t.parentSpanId),traceState:null==(r=n.traceState)?void 0:r.serialize(),name:t.name,kind:null==t.kind?0:t.kind+1,startTimeUnixNano:e.encodeHrTime(t.startTime),endTimeUnixNano:e.encodeHrTime(t.endTime),attributes:iw(t.attributes),droppedAttributesCount:t.droppedAttributesCount,events:t.events.map(function(t){var r,n;return r=t,n=e,{attributes:r.attributes?iw(r.attributes):[],name:r.name,timeUnixNano:n.encodeHrTime(r.time),droppedAttributesCount:r.droppedAttributesCount||0}}),droppedEventsCount:t.droppedEventsCount,status:{code:o.code,message:o.message},links:t.links.map(function(t){return function(t,e){var r;return{attributes:t.attributes?iw(t.attributes):[],spanId:e.encodeSpanContext(t.context.spanId),traceId:e.encodeSpanContext(t.context.traceId),traceState:null==(r=t.context.traceState)?void 0:r.serialize(),droppedAttributesCount:t.droppedAttributesCount||0}}(t,e)}),droppedLinksCount:t.droppedLinksCount}}(t,e)});l.push({scope:{name:d,version:_},spans:g,schemaUrl:E})}p=c.next()}var T={resource:{attributes:iw(s.attributes),droppedAttributesCount:0},scopeSpans:l,schemaUrl:void 0};n.push(T),i=o.next()}return n}(t,function(t){if(void 0===t)return iI;var e,r,n=null==(e=t.useLongBits)||e,o=null!=(r=t.useHex)&&r;return{encodeHrTime:n?iR:iL,encodeSpanContext:o?iP:ib.hexToBase64,encodeOptionalSpanContext:o?iP:iC}}(n))});r.telemetry.reportSpans(o),e({code:iS.ExportResultCode.SUCCESS,error:void 0})}catch(t){e({code:iS.ExportResultCode.FAILED,error:t instanceof Error?t:Error(String(t))})}}shutdown(){return Promise.resolve()}forceFlush(){return Promise.resolve()}},iU={ALL:eL.DiagLogLevel.ALL,VERBOSE:eL.DiagLogLevel.VERBOSE,DEBUG:eL.DiagLogLevel.DEBUG,INFO:eL.DiagLogLevel.INFO,WARN:eL.DiagLogLevel.WARN,ERROR:eL.DiagLogLevel.ERROR,NONE:eL.DiagLogLevel.NONE},iB=class{constructor(t={}){this.configuration=t}start(){var t,e,r,n,o,i,a,s;let u,l,c=(u=(0,oz.parseEnvironment)(process.env),{...oz.DEFAULT_ENVIRONMENT,...u}),p=this.configuration,f="edge",h=!!c.OTEL_SDK_DISABLED;if(process.env.OTEL_LOG_LEVEL&&eL.diag.setLogger(new eL.DiagConsoleLogger,{logLevel:iU[process.env.OTEL_LOG_LEVEL.toUpperCase()]}),h)return;let d=p.idGenerator??new ne,_=p.contextManager??new oY.AsyncLocalStorageContextManager;_.enable(),this.contextManager=_;let E=c.OTEL_SERVICE_NAME||p.serviceName||"app",g=new np(o1({"service.name":E,"node.ci":!!process.env.CI||void 0,"node.env":"production",env:process.env.VERCEL_ENV||process.env.NEXT_PUBLIC_VERCEL_ENV,"vercel.region":process.env.VERCEL_REGION,"vercel.runtime":f,"vercel.sha":process.env.VERCEL_GIT_COMMIT_SHA||process.env.NEXT_PUBLIC_VERCEL_GIT_COMMIT_SHA,"vercel.host":process.env.VERCEL_URL||process.env.NEXT_PUBLIC_VERCEL_URL||void 0,"vercel.branch_host":process.env.VERCEL_BRANCH_URL||process.env.NEXT_PUBLIC_VERCEL_BRANCH_URL||void 0,"vercel.deployment_id":process.env.VERCEL_DEPLOYMENT_ID||void 0,"service.version":process.env.VERCEL_DEPLOYMENT_ID,...p.attributes})),T=p.resourceDetectors??[nE];(p.autoDetectResources??!0)&&(g=g.merge(nT({detectors:T})));let y=(t=p.propagators,e=0,r=c,l=process.env.OTEL_PROPAGATORS&&r.OTEL_PROPAGATORS&&r.OTEL_PROPAGATORS.length>0?r.OTEL_PROPAGATORS:void 0,(t??l??["auto"]).map(t=>{if("none"===t)return[];if("auto"===t){let t=[];return t.push({name:"tracecontext",propagator:new im}),t.push({name:"baggage",propagator:new o$.W3CBaggagePropagator}),t.push({name:"vercel-runtime",propagator:new iv}),eL.diag.debug(`@vercel/otel: Configure propagators: ${t.map(t=>t.name).join(", ")}`),t.map(t=>t.propagator)}if("tracecontext"===t)return eL.diag.debug("@vercel/otel: Configure propagator: tracecontext"),new im;if("baggage"===t)return eL.diag.debug("@vercel/otel: Configure propagator: baggage"),new o$.W3CBaggagePropagator;if("string"==typeof t)throw Error(`Unknown propagator: "${t}"`);return t}).flat()),m=function(t,e){if(t&&"string"!=typeof t)return t;let r=t&&"auto"!==t?t:e.OTEL_TRACES_SAMPLER||iG;switch(eL.diag.debug("@vercel/otel: Configure sampler: ",r),r){case"always_on":return new rQ;case"always_off":return new rq;case"parentbased_always_on":return new rJ({root:new rQ});case"parentbased_always_off":return new rJ({root:new rq});case"traceidratio":return new r0(iV(e));case"parentbased_traceidratio":return new rJ({root:new r0(iV(e))});default:return eL.diag.error(`@vercel/otel: OTEL_TRACES_SAMPLER value "${String(e.OTEL_TRACES_SAMPLER)} invalid, defaulting to ${iG}".`),new rQ}}(p.traceSampler,c),v=(n=p.spanProcessors,o=p,i=c,[...(n??["auto"]).flatMap(t=>{if("auto"===t){let t=[new nt(new ix)];if(process.env.VERCEL_OTEL_ENDPOINTS){let e=process.env.VERCEL_OTEL_ENDPOINTS_PORT||"4318",r=process.env.VERCEL_OTEL_ENDPOINTS_PROTOCOL||"http/protobuf";eL.diag.debug("@vercel/otel: Configure vercel otel collector on port: ",e,r);let n={url:`http://localhost:${e}/v1/traces`,headers:{}},o="http/protobuf"===r?new is(n):new ie(n);t.push(new nt(o))}else(!o.traceExporter||"auto"===o.traceExporter||i.OTEL_EXPORTER_OTLP_TRACES_ENDPOINT||i.OTEL_EXPORTER_OTLP_ENDPOINT)&&t.push(new nt(function(t){let e=process.env.OTEL_EXPORTER_OTLP_TRACES_PROTOCOL??process.env.OTEL_EXPORTER_OTLP_PROTOCOL??"http/protobuf",r=function(t){let e=t.OTEL_EXPORTER_OTLP_TRACES_ENDPOINT;if(e&&"string"==typeof e)return e;let r=t.OTEL_EXPORTER_OTLP_ENDPOINT;return r&&"string"==typeof r?`${r}/${ik}`:ij}(t),n={...o$.baggageUtils.parseKeyPairsIntoRecord(t.OTEL_EXPORTER_OTLP_HEADERS),...o$.baggageUtils.parseKeyPairsIntoRecord(t.OTEL_EXPORTER_OTLP_TRACES_HEADERS)};switch(eL.diag.debug("@vercel/otel: Configure trace exporter: ",e,r,`headers: ${Object.keys(n).join(",")||"<none>"}`),e){case"http/json":return new ie({url:r,headers:n});case"http/protobuf":return new is({url:r,headers:n});default:return eL.diag.warn(`@vercel/otel: Unsupported OTLP traces protocol: ${e}. Using http/protobuf.`),new is}}(i)));return t}return t}).filter(iF),...o.traceExporter&&"auto"!==o.traceExporter?[new nt(o.traceExporter)]:[]]);0===v.length&&eL.diag.warn("@vercel/otel: No span processors configured. No spans will be exported.");let S=new nA({resource:g,idGenerator:d,sampler:m,spanLimits:p.spanLimits});if(S.addSpanProcessor(new o4(v,p.attributesFromHeaders)),S.register({contextManager:_,propagator:new o$.CompositePropagator({propagators:y})}),this.tracerProvider=S,p.logRecordProcessor){let t=new nj({resource:g});this.loggerProvider=t,t.addLogRecordProcessor(p.logRecordProcessor),nO.logs.setGlobalLoggerProvider(t)}if(p.metricReader||p.views){let t=new oW({resource:g,views:p.views??[]});p.metricReader&&t.addMetricReader(p.metricReader),eL.metrics.setGlobalMeterProvider(t),this.meterProvider=t}let b=(a=p.instrumentations,s=p.instrumentationConfig,(a??["auto"]).map(t=>"auto"===t?(eL.diag.debug("@vercel/otel: Configure instrumentations: fetch",s?.fetch),[new il(s?.fetch)]):"fetch"===t?(eL.diag.debug("@vercel/otel: Configure instrumentations: fetch",s?.fetch),new il(s?.fetch)):t).flat());this.disableInstrumentations=(0,nR.registerInstrumentations)({instrumentations:b}),eL.diag.info("@vercel/otel: started",E,f)}async shutdown(){let t=[];this.tracerProvider&&t.push(this.tracerProvider.shutdown()),this.loggerProvider&&t.push(this.loggerProvider.shutdown()),this.meterProvider&&t.push(this.meterProvider.shutdown()),eL.diag.info("@vercel/otel: shutting down",t.length,"edge"),await Promise.all(t),this.contextManager&&this.contextManager.disable();let{disableInstrumentations:e}=this;e&&e()}},iG="always_on";function iV(t){if(void 0===t.OTEL_TRACES_SAMPLER_ARG||""===t.OTEL_TRACES_SAMPLER_ARG)return eL.diag.error("@vercel/otel: OTEL_TRACES_SAMPLER_ARG is blank, defaulting to 1."),1;eL.diag.debug("@vercel/otel: Configure sampler probability: ",t.OTEL_TRACES_SAMPLER_ARG);let e=Number(t.OTEL_TRACES_SAMPLER_ARG);return isNaN(e)?(eL.diag.error(`@vercel/otel: OTEL_TRACES_SAMPLER_ARG=${t.OTEL_TRACES_SAMPLER_ARG} was given, but it is invalid, defaulting to 1.`),1):e<0||e>1?(eL.diag.error(`@vercel/otel: OTEL_TRACES_SAMPLER_ARG=${t.OTEL_TRACES_SAMPLER_ARG} was given, but it is out of range ([0..1]), defaulting to 1.`),1):e}var ik="v1/traces",ij=`http://localhost:4318/${ik}`;function iF(t){return null!=t}function iH(){var t;let e;new iB((t={serviceName:"ai-chatbot"},e=t)).start()}}},t=>{var e=t(t.s=858);(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES).middleware_instrumentation=e}]);
//# sourceMappingURL=edge-instrumentation.js.map