{"version": 3, "file": "edge-instrumentation.js", "mappings": "oFAAA,sGMoBO,ESJA,EIDA,ESKA,ECLA,ECZA,EiBYA,ESZ059E,EAAuysB,EAAkwY,EAAsjH,4yBvDsBz/pH,kCACP,WACA,sBACA,KACA,wBACA,OACA,iBAAqB,GAAM,CACT,GAAM,CACxB,GCjBO,UCAP,kCAoGO,WAnFA,GACP,mBACA,UACA,aACA,MAEA,kBAA6B,UAE7B,OACA,YACA,YACA,YACA,iBAGA,sBACA,mBACA,YACA,EAEA,cAEA,OADA,SACA,EACA,CAKA,mBACA,YACA,SAEA,YACA,SAEA,iBACA,MAGA,YAEA,OACA,YACA,YACA,YACA,iBAGA,uBAIA,kBAHA,YAMA,sBACA,mBACA,kBAhCA,MAiCA,GAhCA,IAkCA,YAEA,kBArCA,MAsCA,GArCA,IAuCA,IACA,CACA,EAgBkD,GCjGlD,IDiGyD,KCjGzD,4BADY,EAAO,eAGZ,oBAEP,aAAoC,MACpC,IAFA,EAEA,yBACA,QAAiB,CACjB,CAAK,CACL,aAEA,+EAEA,OADA,4BACA,EACA,CACA,eAAwB,EAAO,CAE/B,IAF+B,EAE/B,wHAA6J,GAE7J,IAFoK,GACpK,4BACA,EACA,CAGA,OAFA,OACA,8DAA8E,EAAO,KACrF,EACA,CACO,cAEP,IADA,IACA,kCACA,MAA2B,EAAY,GAGvC,kCAEO,gBACP,iEAAiF,EAAO,SACxF,EAjCc,CAiCd,IACA,GACA,GAnCyB,IAmCzB,KCxCA,MAA0B,QAAZ,CAAY,KAC1B,GADkB,CAClB,GADsB,SAAI,EAC1B,kCACA,eACA,yBACA,IACA,8DACA,CACA,SAAoB,GAAM,gBAC1B,CACA,IACA,mCACA,QACA,CAAkB,mBAClB,CACA,QACA,EACA,EAAiC,eAAZ,CACrB,QADyB,CACzB,GAD6B,OAC7B,EADiC,IACjC,0BAA6E,IAAO,KACpF,YACA,yCACA,WAGA,iDACA,EAWA,aACA,cACA,kDACA,CAoCA,OAnCA,6BAEA,QADA,KACA,IAAyB,mBAAuB,IAChD,kBAEA,mCACA,EACA,6BAEA,QADA,KACA,IAAyB,mBAAuB,IAChD,kBAEA,mCACA,EACA,4BAEA,QADA,KACA,IAAyB,mBAAuB,IAChD,kBAEA,kCACA,EACA,4BAEA,QADA,KACA,IAAyB,mBAAuB,IAChD,kBAEA,kCACA,EACA,+BAEA,QADA,KACA,IAAyB,mBAAuB,IAChD,kBAEA,qCACA,EACA,CACA,CAAC,GAED,kBACA,MAAiB,EAAS,QAE1B,KAIA,OADA,aACA,2BACA,EC/EA,YAEA,mBAEA,sBAEA,oBAEA,oBAEA,sBAKA,0BAEA,mBACA,CAAC,UAAoC,EExBrC,IAAI,EAAsB,OAAhB,EAAgB,EAAZ,CAAY,EAC1B,MADkB,IAAI,QACtB,CAD0B,MAC1B,2BACA,eACA,yBACA,IACA,8DACA,CACA,SAAoB,GAAM,gBAC1B,CACA,IACA,mCACA,QACA,CAAkB,mBAClB,CACA,QACA,EACI,EAA6B,cAAhB,EACjB,EADqB,CACrB,QADyB,IAAI,IAC7B,KADiC,CACjC,0BAA6E,IAAO,KACpF,YACA,yCACA,WAGA,iDACA,EAUA,aAKA,aACA,cACA,kBAEA,QADA,KACA,IAAiC,mBAAuB,IACxD,kBAFA,IAIA,EAA6B,EAAS,QAEtC,KAEA,oBAAsD,EAAa,GAAK,EAAM,QAE9E,CAEA,WA4BA,YA1BA,cAGA,GADA,aAAgD,GAAsB,SAAU,EAAY,OAC5F,GAD4F,CAC5F,GAIA,IANA,MAMA,8IAEA,OADA,uCACA,EACA,CACA,oBACA,IACA,UACA,GAEA,MAA4B,EAAS,QACrC,EDxEO,cASP,UC+DoD,CD/DpD,KACA,iBACA,2BACA,UAEA,YACA,CACA,OAfA,EAAmB,EAAY,KAC/B,EAAmB,EAAY,CADA,GACA,CAE/B,EAAwB,EAAY,CAFL,EAEK,EACpC,GAAmB,EAAY,KAG/B,KAH+B,CAG/B,EAQA,CACA,gBAAoC,EAAY,OAChD,GADgD,EAChD,SAAkC,EAAY,MAC9C,IAD8C,CAC9C,SAAkC,EAAY,MAC9C,IAD8C,EAC9C,UAAoC,EAAY,OAChD,GADgD,KAChD,YAAwC,EAAY,QACpD,CACA,CAFoD,CCmDA,uBAAoE,EAAY,QAEpI,EAFoI,CAEpI,+BACA,kEACA,qDACA,sEACA,CACA,OAAmB,EAAc,cACjC,EAEA,qBACY,EAvDZ,OAuD4B,EAC5B,EACA,GAF4B,oBAE5B,aACA,WAAuB,EAAmB,EAC1C,EACA,aAF0C,UAG1C,mBACA,iBACA,iBACA,kBACA,CAQA,OANA,sBAIA,OAHA,gBACA,uBAEA,gBAEA,CACA,CAAC,GCvGG,EAAsB,cAC1B,CADU,GACV,CADc,CACd,QADkB,IAClB,SAD0B,MAC1B,mBACA,eACA,yBACA,IACA,8DACA,CACA,SAAoB,GAAM,gBAC1B,CACA,IACA,mCACA,QACA,CAAkB,mBAClB,CACA,QACA,EACA,EAA4B,UAAZ,EAChB,OADoB,IAAI,OACxB,EAD4B,KAC5B,sCACA,sBACA,uCACA,gBAEA,OADA,2BACA,CAAqB,wBACrB,CACA,CACA,+EACA,EACA,aACA,cACA,kCACA,CAiDA,OAhDA,iCACA,2BACA,KAGA,uBAA+B,GAC/B,EACA,qCACA,2DACA,MAAqB,EAAM,KAC3B,OAD2B,UAC3B,CACS,CACT,EACA,mCACA,2BAEA,OADA,oBACA,CACA,EACA,oCACA,2BAEA,OADA,qBACA,CACA,EACA,qCAGA,QAFA,IACA,KACA,IAAyB,mBAAuB,IAChD,kBAFA,IAIA,uBACA,IACA,0BAAwE,QAAgB,YACxF,cACA,oBACA,CACA,CACA,SAAwB,GAAQ,gBAChC,CACA,IACA,mCACA,QACA,CAAsB,mBACtB,CACA,QACA,EACA,6BACA,YACA,EACA,CACA,CAAC,GC7EM,iCCAP,EAAW,EAAO,WAMX,cAEP,OADA,aAA8B,MAC9B,IAAe,EAAW,2BAC1B,CAOO,cAKP,MAJA,qBACA,uEACA,MAEA,CACA,SAAkB,EAClB,oBACA,IAF4C,GAE5C,CACA,CACA,CACA,CC7BO,cAOP,oBACA,CA0BO,UAnBP,cAEA,WACA,uCACA,uBAAyC,iCACzC,yBACA,+BAEA,OADA,2BACA,CACA,EACA,0BACA,+BAEA,OADA,4BACA,CACA,CACA,EC/BA,GACA,CAAM,oBAAwB,CAC9B,CAAM,kBAAsB,CAC5B,CAAM,kBAAsB,CAC5B,CAAM,oBAAwB,CAC9B,CAAM,sBAA0B,CAChC,CAMA,EACA,WAuBA,YAAwB,WAAuB,IAC/C,sBAvBA,GACA,kBAEA,QADA,KACA,IAAiC,mBAAuB,IACxD,kBAEA,YAGA,iBAOA,GANA,sBAGA,gBAGA,qBACA,yBAEA,CACA,CACA,EAEA,OAEA,ECvCA,EAA6B,WAC7B,SADqB,IAAI,EACzB,KAIA,EAL6B,IAE7B,0BACA,EAAe,eAAgB,+BAAsC,cAAkB,EACvF,cAA8B,wEAC9B,IACA,EACA,qBACA,kCACA,kFAEA,aAAwB,mBADxB,OAEA,qEACA,CACA,CAAC,GAKD,aACA,aACA,CAmDA,OA/CA,sCACA,SACA,EAIA,0CACA,SACA,EAIA,wCACA,SACA,EAIA,8CACA,SACA,EAIA,gDACA,SACA,EAIA,kDACA,SACA,EAIA,wDACA,SACA,EAIA,uDAIA,wDACA,CACA,CAAC,GAED,EACA,WACA,EAIA,cAEA,aACA,+CAGA,OALA,OAIA,gCACA,CACA,CAAC,IAED,cAEA,aACA,+CAGA,OALA,OAIA,gCACA,CACA,CAAC,IAED,cAEA,aACA,+CAGA,OALA,OAIA,mCACA,CACA,CAAC,IAED,cAEA,aACA,+CAGA,OALA,OAIA,mCACA,CACA,CAAC,IAED,aACA,aACA,CAGA,OAFA,sCACA,yCACA,CACA,CAAC,GAED,cAEA,aACA,8CACA,CACA,OAJA,OAIA,CACA,CAAC,IAED,cAEA,aACA,8CACA,CACA,OAJA,OAIA,CACA,CAAC,IAED,cAEA,aACA,+CAEA,OAJA,OAIA,CACA,CAAC,IAEM,QAEA,SACA,SACA,SACA,SAEA,SACA,SACA,SAIA,cACP,QACA,EClKA,YACA,iBACA,sBACA,CAAC,YCLM,QACP,kBACA,WAGA,YACK,CACL,wBACA,QACA,GAEA,cACA,CAAK,EAEE,IACP,oBACA,SAGA,QACA,CAAK,ECpBD,GAAsB,cAC1B,MADU,IAAI,QACd,CADkB,IAAI,EACtB,OAD0B,CAC1B,mBACA,eACA,yBACA,IACA,8DACA,CACA,SAAoB,GAAM,gBAC1B,CACA,IACA,mCACA,QACA,CAAkB,mBAClB,CACA,QACA,EACI,GAA6B,gBACjC,WADiB,IAAI,IACrB,KADyB,CACzB,GAD6B,EAC7B,OADiC,CACjC,aAA6E,IAAO,KACpF,YACA,yCACA,WAGA,iDACA,EAEA,cACA,aACA,CAoBA,OAnBA,8BACA,OAAe,CACf,EACA,SAF2B,EAE3B,sBAEA,QADA,KACA,IAAyB,mBAAuB,IAChD,oBAEA,sBAAiC,GAAa,IAAY,GAAM,OAChE,EACA,WAFgE,CAEhE,mBACA,QACA,EACA,8BACA,aAEA,+BACA,aAEA,CACA,CAAC,GCjDG,GAAsB,aAAZ,CACd,QADkB,IAAI,MACtB,GAD0B,IAC1B,2BACA,eACA,yBACA,IACA,8DACA,CACA,SAAoB,GAAM,gBAC1B,CACA,IACA,mCACA,QACA,CAAkB,mBAClB,CACA,QACA,EACI,GAA6B,gBAAhB,GACjB,CADqB,EACrB,OADyB,IAAI,EAC7B,OADiC,IACjC,qBAA6E,IAAO,KACpF,YACA,yCACA,WAGA,iDACA,EAII,GAAQ,UACZ,OAA+B,GAI/B,cAEA,CANiD,QAMjD,IACA,CAuDA,OArDA,yBAIA,OAHA,gBACA,uBAEA,gBAOA,gDACA,OAAe,EAAe,GAAQ,EAAkB,EAAO,KAAlC,GAAkC,CAAzB,EACtC,EAIA,8BACA,yCACA,EASA,iCAGA,QAFA,EACA,KACA,IAAyB,mBAAuB,IAChD,oBAEA,iDAA+D,GAAa,QAAyB,GAAM,KAA/B,EAC5E,EAD2G,EAQ3G,6BACA,0CACA,EACA,0CACA,OAAe,EAAU,KAAQ,EAAT,EAGxB,OAHiC,IAGjC,oBACA,oCACQ,EAAiB,GAAU,EAAO,SAAlB,EACxB,EACA,CACA,CAAC,IC3FD,YAEA,mBAEA,wBACA,CAAC,UAAgC,ECL1B,0BACA,sCACA,IACP,WACA,UACA,WAAgB,EAAU,MCA1B,EDA0B,CCA1B,WACA,cACA,aAAuC,EAAe,GAAoB,CAC1E,mBACA,CAuCA,OArCA,mCACA,0BAGA,uCACA,aAGA,sCACA,WACA,EAEA,mCACA,aAEA,gCACA,aAEA,iCACA,aAGA,kCACA,aAGA,mCACA,aAGA,8BAEA,mCACA,QACA,EAEA,4CACA,CACA,CAAC,GC5CD,GAAe,EAAgB,kCAMxB,eACP,6BACA,CAIO,cACP,UAAmB,GAAU,uBAC7B,CAOO,iBACP,uBACA,CAMO,eACP,wBACA,CAQO,iBACP,gBAAgC,GAAgB,GAChD,CAMO,SAPyC,GAOzC,GACP,MACA,6CACA,CCtDA,mBAAsC,GAAG,KACzC,cAAoC,GAAG,IAChC,eACP,uBAA4D,EAC5D,CACO,YAFoE,CAEpE,EACP,uBAAyD,EACzD,CAKO,WANgE,CAMhE,GACP,kCACA,CAOO,eACP,WAAe,GAAgB,EAC/B,CCrBA,OAAiB,GDoBc,OCpBJ,OAI3B,cACA,aACA,CAyCA,OAvCA,sCAGA,GAFA,aAAkC,eAClC,sBAEA,WAAuB,GAEvB,IAoCA,EApCA,KAA2C,EAFJ,CAEkB,SAqCzD,EArCyD,QAqCzD,OADA,EAnCA,IAqCA,2BACA,4BACA,+BAtCY,GAAkB,GAC9B,IAAuB,GAAgB,GAGvC,EAJ8B,EAIP,EAEvB,EACA,EANuC,SAMvC,CAHuC,eAGvC,mBAIA,0BAGA,oBACA,IAEA,qBACA,IACA,MAGA,IACA,IACA,KAEA,IAlBA,EACA,EACA,EAgBA,wBACA,wBACA,EAAiC,GAAO,KACxC,6BACA,EACA,CACA,CAAC,GCnDD,OAAsB,GAItB,OAJgC,OAKhC,oBACA,iBACA,YACA,eACA,cACA,CAuBA,OAtBA,sCACA,yCACA,EACA,8CACA,wBACA,mDACA,EAKA,kCACA,kBACA,sBAEA,mFACA,GAGA,iBACA,gBAHA,EAIA,EACA,CACA,CAAC,GEjCD,MDKA,YACA,OCNiD,EDMjD,IACA,CAIA,OAHA,sCACA,WAAmB,EACnB,EACA,CACA,CAAC,ICHD,cACA,aACA,CAsBA,OAlBA,sCACA,MACA,qDAA2G,GAAW,WACtH,EACA,mCACA,MACA,oCACA,EAIA,oCACA,gBACA,EACA,8CACA,MACA,yDACA,EACA,CACA,CAAC,IC9BD,YAKA,+BAKA,uBAKA,8CACA,CAAC,UAA4C,ECrB7C,YAEA,2BAKA,uBAKA,uBAMA,2BAMA,0BACA,CAAC,UAA4B,ECrC7B,YAIA,qBAKA,eAIA,oBACA,CAAC,UAAwC,ECHzC,sBAGA,sBAFA,GAEA,WADA,WADmD,CACnD,KADyD,MACU,YACnE,YACA,UAFsG,CAEjE,IAFsE,EAEhE,QAC3C,SCSA,cACA,cACA,4BACA,GACA,cACA,CAgEA,OA/DA,8BAGA,oBAKA,OAJA,yBACA,2BAEA,0BACA,CACA,EACA,8BACA,oBAEA,OADA,2BACA,CACA,EACA,4BACA,iCACA,EACA,iCACA,WACA,oBACA,qBAEA,OADA,SAtCA,IAsCA,UACA,CACA,CAAS,KACT,KA1CA,IA2CA,EACA,gCACA,UA9CA,GA8CA,IAEA,sBACA,MAhDA,KAiDA,UACA,qBACA,eACA,CAD0C,CAC1C,UAnDA,KAoDA,WACA,mBACA,uBACoB,CD5CpB,QC4C+B,IDrC/B,ICqCwC,GDrCxC,CCqCqD,IDpCrD,KCoCqD,GDpCrD,CCoCqD,IACrD,UAKA,CACA,QACA,CAAS,UAET,yBApEA,IAqEA,uEACA,UACA,QAvEA,IAuEA,EAEA,EACA,6BACA,uDACA,EACA,8BACA,YAEA,OADA,8CACA,CACA,EACA,CACA,CAAC,GCnFM,eACP,WAAe,GAAc,EAC7B,CCCO,OAAc,CDFQ,ECEE,cCKpB,GAAO,EAAO,QAAV,GCKR,MATP,YACA,aACA,CAIA,OAHA,qCACA,OAAe,CACf,EACA,CACA,CAAC,ICTG,CDMqB,ECNb,UCCL,GAAc,CDGrB,SCH+B,EDK/B,aACA,CA+BA,OA7BA,yBAIA,OAHA,gBACA,uBAEA,gBAMA,+CACA,OAAe,EAAe,GAAQ,EAAY,EAAO,KAA5B,GAA4B,CAAnB,EACtC,EAIA,wCACA,OAAe,EAAU,KAAa,EAAd,EAKxB,OALiC,IAKjC,IALyD,KAKzD,iBACA,8CACA,EAEA,+BACQ,EAAiB,GAAU,EAAO,SAAlB,EACxB,EACA,EACA,CAAC,GCtC8B,cCD/B,cACA,aACA,CAUA,OARA,mCAEA,kCACA,QACA,EACA,8BACA,UAEA,CACA,CAAC,GCXD,GAAkB,EAAgB,6BAO3B,eACP,6BACA,CAMO,cACP,UAAsB,GAAU,uBAChC,CAOO,iBACP,uBACA,CAMO,eACP,wBACA,CCjCA,IAAI,GAAQ,cACZ,GADY,IACuB,GCH5B,GAAkB,CDOzB,WAEA,ECTuC,CDGiB,MAMxD,IACA,mBAA6B,EAC7B,WAD0C,IAC1C,CAA0B,GAC1B,OADoC,cACpC,CAAgC,GAChC,aADgD,EAChD,CAA0B,GAC1B,OADoC,WACpC,CAA6B,EAC7B,CAmDA,OAjDA,GAH0C,UAG1C,YAIA,OAHA,gBACA,uBAEA,gBAOA,4CACA,OAAe,EAAe,GAAQ,EAAc,EAAO,KAA9B,GAA8B,GAC3D,EAQA,mCAEA,OADA,aAAiC,EAAS,GAAoB,CAC9D,yCACA,EAQA,oCAEA,OADA,aAAiC,EAAS,GAAoB,CAC9D,0CACA,EAIA,8BACA,2CACA,EAEA,+BACQ,EAAiB,GAAU,EAAO,SAAlB,EACxB,EACA,4CACA,OAAe,EAAU,KAAQ,EAAT,EAExB,CACA,CAAC,ICnEsC,KDgEN,MChEM,GCCnC,GAAQ,QCDL,GDCK,CAIZ,OCL2B,IDO3B,aACA,8BAAwC,GACxC,gBAD2D,IAC3D,CAA+B,GAC/B,YAD8C,WAC9C,CAAkC,GAClC,eADoD,CAC1B,GAC1B,OADoC,KACpC,CAAuB,GACvB,mBAA6B,GAC7B,UAD0C,SAC1C,CAA8B,GAC9B,WAD4C,CAC5C,CAAuB,GACvB,IAD8B,CAC9B,eAA8B,EAC9B,CAqCA,OAnCA,IAH4C,SAG5C,YAIA,OAHA,gBACA,uBAEA,gBAOA,gDACA,MAAsB,EAAe,GAAQ,SAAT,EAAS,eAA6B,EAAO,YAIjF,OAHA,GACA,yCAEA,CACA,EAIA,yCACA,OAAe,EAAU,KAAQ,EAAT,EAAS,uBAKjC,oCACA,8CACA,EAEA,+BACQ,EAAiB,GAAU,EAAO,SAAlB,GACxB,8BAAwC,EACxC,EACA,EACA,CAAC,GCvD0B,SDoDgC,ECpDhC,GC0B3B,OAAe,CACf,QAAa,GACb,IADoB,CACV,GACV,QAAa,EADC,CAEd,IADoB,QACH,GACjB,MAAW,EACX,CAAC,EAAC,0BCnDF,IAAM,GAA4B,yCCgBlC,YACA,iCACA,qBACA,uBACA,uBACA,uBACA,qBACA,uBACA,uBACA,uBACA,mBACA,sBACA,sBACA,sBACA,oBACA,sBACA,sBACA,sBACA,sBACA,wBACA,wBACA,wBACA,sBACA,wBACA,wBACA,uBACA,CAAC,UAAwC,CC3BlC,UACP,SACA,CACO,aCFA,UACP,iBACA,WAAmB,EACnB,CACA,CACO,MAHsB,CAGtB,MCLA,UACP,qBACA,iBACA,YACA,eACA,cACA,CAMA,QACA,yBACA,CAKA,aACA,kBACA,sBAEA,mFACA,GAGA,iBACA,gBAHmB,EAInB,CACA,CC7BO,ODwBuB,ECvB9B,iBACA,MACA,qDAA2G,GAAW,WACtH,CACA,cACA,MACA,kCAAsE,EACtE,CAIA,eACA,EAN0F,EAM1F,YACA,CACA,yBACA,MACA,yDACA,CACA,CCVO,IAAM,GAAW,4BACxB,WACA,sBACA,KACA,wBACA,OAC2B,UAA3B,OAAqB,GAAM,CACT,GAAM,CACxB,GClBO,6CCEA,UACP,cACA,8BAAwC,EACxC,CACA,gBAF2D,IAE3D,CAIA,OAHA,gBACA,wBAEA,eAEA,kCACA,EAAmB,CAAC,GAAmB,CACvC,eADuC,OACvC,IAEQ,EAAO,CAAC,GAAmB,CDLnC,ECKuC,CDIhC,IATP,ECKqF,EAAY,CAAhD,CAAC,CAClD,CADmC,GACnC,aADqH,QACrH,SADqF,GACrF,IACA,EACA,CAMA,oBACA,QACA,yBAA4B,EAAO,CAAC,GAAmB,gBAAgD,GDPhG,ECO4I,cAArC,EAAE,aAAmC,CAOnJ,iBACA,gDACA,CAEA,UACA,OAAe,EAAO,CAAC,GAAmB,CAC1C,cDvCkC,CCsCQ,UAC1C,KAAwC,EACxC,CACA,CCrCO,OAAa,GAAO,KDmCgC,MCnChC,ECrB3B,kCACA,yBAA+B,kCAE/B,qBAAqB,yBAA6B,mCAAuC,8BAAkC,4DAAgE,gEAAoE,6CAA6C,iBAAiB,0DAA2D,4DAA2D,EAAE,gCAAoC,wBAA2B,YAAW,uBAAwB,eAAmB,+FAAgG,mDAAmD,EAAE,SAAS,yFAAuF,wBAA+D,EAA/D,gBAAyC,sBAAsB,oBAAqB,eAAe,SAAS,KAAK,MAA+C,WAAc,MAAI,EAAE,CAAE,EAAE,UAA2B,CAAb,GAAlB,GAA+B,+BAAuC,SAAS,EAAE,mEAAsE,2FAAuI,kBAAxC,YAAe,yBAAuF,oBAAxC,YAAe,yBAA2F,sBAA1C,YAAe,yBAA2B,CAA0B,EAAE,UAAa,OAAa,+BAAsC,SAAS,EAAE,2MAA2M,iCAAiC,iCAAiC,EAAE,8BAA8B,2BAA2B,mCAAmC,wCAAwC,gCAAgC,EAAE,UAA2B,CAAb,MAAa,+BAAuC,SAAS,EAAE,uFAA2F,2BAAya,cAAe,8CAA+C,sBAAsB,gBAAgB,aAAa,8CAA+C,eAAe,kGAAkG,qGAAuG,0BAAnnB,oBAApJ,YAAe,wBAAwB,SAAS,EAAE,EAAE,oCAAqC,EAAE,EAAE,EAAE,+CAAgD,MAA8O,cAAjN,YAAe,uCAAuC,SAAS,sBAAsB,GAAG,4BAA4B,EAAE,wFAAyF,GAAsa,sBAAmN,0BAA3L,YAAe,yCAA0C,8HAAgI,EAAE,CAA8B,EAAE,UAA2B,CAAb,MAAa,+BAAuC,SAAS,EAAE,8BAA+B,yCAA6wB,uBAA7wB,MAAmD,cAAc,kCAAmC,2CAA4C,wJAA4J,wCAAyC,eAAe,uFAAyF,eAAe,SAAS,qEAAsE,iCAAkC,MAAM,OAAO,eAAe,gDAAgD,6FAA+F,SAAS,0BAA4B,CAA2B,EAAE,UAA2B,CAAb,MAAa,+BAAuC,SAAS,EAAE,uBAAmO,gBAA3M,MAAa,iBAAiB,iFAAiF,MAAM,yDAAyD,4BAA6B,CAAoB,EAAE,UAA2B,CAAb,MAAa,+BAAuC,SAAS,EAAE,gEAAmE,oBAA2U,cAAe,qCAA2D,cAAe,2CAAsE,GAAe,MAAM,2BAA2B,OAAO,SAAU,WAAW,SAAS,SAAS,yBAAyB,UAA7L,SAAsM,cAAe,iBAAiB,iDAAiD,SAAzY,qBAAhS,YAAe,SAAS,wCAAwC,kCAAkC,UAAW,sCAAuC,EAAE,GAAG,SAAS,UAAW,oDAAqD,EAAE,GAAG,SAAS,uCAAuC,UAAuF,mBAAmF,oBAAwO,CAAU,EAAE,UAA2B,CAAb,MAAa,+BAAuC,SAAS,EAAE,6BAA8B,oBAAiE,sBAA/C,GAAyE,QAA3D,eAA2D,EAAhD,aAA+D,iBAAf,EAAhD,CAA+D,4BAAkD,GAAe,QAAQ,KAAK,KAAK,KAAS,EAAT,GAAS,0CAA2C,eAAe,WAAW,oBAAoB,6BAA6B,UAAlN,IAA/D,EAAiR,CAAU,EAAE,UAA2B,CAAb,MAAa,+BAAuC,SAAS,EAAE,oDAAsD,oCAAgE,yBAApB,YAAe,KAAkE,qBAAjC,YAAe,IAAI,KAAM,QAAQ,CAAyB,EAAE,UAA2B,CAAb,MAAa,+BAAuC,SAAS,EAAE,6BAA8B,SAAO,GAAa,iOAAiO,iDAAuD,GAAG,EAAE,UAA2B,CAAb,MAAa,+BAAuC,SAAS,EAAE,qBAAsB,iJAAkJ,EAAE,UAAa,OAAa,+BAAsC,SAAS,EAAE,4OAA4O,0DAAkE,EAAuC,aAAxB,cAAwB,suBAA+yB,eAAxB,YAAwB,oBAAoD,EAAuC,aAAxB,uBAAwB,KAA2C,oCAAoC,mDAAmD,kDAAkD,uBAAuB,y/FAAoyG,OAAQ,wLAAiS,cAAe,SAAS,mCAA4C,CAAR,EAAkB,mBAAlB,GAA9J,gBAAmB,WAAW,uBAAuB,+BAA0B,cAA+E,EAAkB,KAA+B,KAAM,GAA7yJ,UAAsvJ,GAAtvJ,GAAwB,CAAqxJ,SAArsB,OAAmB,0BAA0B,mBAAmB,+BAA8kB,EAAuD,KAA2B,QAAzhI,UAAu8H,GAAv8H,GAAwB,CAAigI,SAAloB,oBAAgC,oBAAoB,mBAAmB,2CAAye,EAAkF,KAAwB,QAAt9H,UAA42H,GAA52H,IAA66G,iBAAx4I,GAAw4I,EAAwB,WAAW,wDAA4Z,EAA0G,KAAwB,KAAK,QAAvI,EAAuI,CAAW,2BAAlJ,EAAkJ,YAA2C,SAAS,qBAAuH,wBAAjG,WAAc,+EAAmF,CAA2B,EAAE,UAA2B,CAAb,MAAa,+BAAuC,SAAS,EAAE,gBAAiB,kBAAkI,SAA9G,WAAc,4CAA8C,uBAAuB,0BAA2B,CAAa,EAAE,UAA2B,CAAb,MAAa,+BAAuC,SAAS,EAAE,qBAAkK,cAA5I,YAAe,oBAAoB,YAAY,IAAI,KAAgD,CAA1C,EAA0C,oBAA1C,iCAAoE,eAAe,CAAkB,EAAE,UAA2B,CAAb,MAAa,+BAAuC,SAAS,EAAE,2BAA4B,EAA+F,kBAA/F,MAAwB,cAAc,uBAAtC,IAAsC,sBAAtC,EAAsC,GAAiF,gBAAiB,cAAe,kBAAkB,YAAY,MAAM,8DAAiE,wDAAyD,EAAE,UAA2B,CAAb,MAAa,+BAAuC,SAAS,EAAE,uBAAwB,4BAA6B,EAAE,UAA2B,CAAb,MAAa,+BAAuC,SAAS,EAAE,iBAAkB,mBAAoB,EAAE,UAAa,OAAa,+BAAsC,SAAS,EAAE,gXAAgX,sBAAsB,6lLAA6lL,kBAAkB,u1BAAu1B,qCAAqC,+LAA+L,qBAAqB,iFAAiF,+BAA+B,6CAA6C,6BAA6B,iEAAiE,sBAAsB,+FAA+F,+BAA+B,mFAAmF,kCAAkC,6SAA6S,oBAAoB,sEAAsE,kCAAkC,6BAA6B,4BAA4B,qCAAqC,2BAA2B,sQAAsQ,qBAAqB,iCAAiC,EAAE,UAA2B,CAAb,GAAa,yDAAgE,6CAA6C,6BAA6B,aAAa,EAAE,mBAAmB,6BAA4B,oCAAwC,mFAAqF,sCAAuC,SAAS,EAAE,UAAY,EAAE,UAAa,OAAa,+BAAsC,SAAS,EAAE,sKAAsK,8BAA8B,i4FAAi4F,uBAAuB,iEAAiE,uBAAuB,6iBAA6iB,0BAA0B,6BAA6B,kBAAkB,6FAA6F,gBAAgB,qLAAqL,8BAA8B,mIAAmI,EAAE,UAA2B,CAAb,GAAa,yDAAgE,6CAA6C,6BAA6B,aAAa,EAAE,mBAAmB,6BAA4B,oCAAwC,mFAAqF,sCAAuC,SAAS,EAAE,UAAY,EAAE,UAA2B,CAAb,GAAa,yDAAgE,6CAA6C,6BAA6B,aAAa,EAAE,mBAAmB,6BAA4B,oCAAwC,mFAAqF,sCAAuC,SAAS,EAAE,UAAY,UAAY,EAAE,UAA2B,CAAb,MAAa,+BAAuC,SAAS,EAAE,kBAAmB,kBAAoB,YAAa,yRAA+R,EAAE,UAA2B,CAAb,MAAa,+BAAuC,SAAS,EAAE,oBAAqC,aAAhB,aAAgB,CAAiB,EAAE,UAAa,IAAa,yDAA8D,6CAA6C,6BAA6B,aAAa,EAAE,mBAAmB,6BAA4B,oCAAsC,mFAAqF,sCAAsC,SAAS,EAAE,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,EAAE,UAAa,OAAa,+BAAsC,SAAS,EAAE,6NAA6N,WAA4D,cAAe,MAAmD,CAAnD,mCAA3E,KAA8H,CAAgC,aAAc,iCAAkC,uBAAuB,sBAAuB,gCAAgC,SAA4B,cAAe,OAAiE,EAAjE,qDAAiE,CAAopB,cAAe,oFAA+L,gBAAiB,4BAA4B,aAAvxC,KAAuxC,OAAvxC,IAAuxC,WAA7oC,mBAA2J,kBAAkH,WAA+K,oBAAnK,YAAe,iBAAkB,6CAAgD,0CAA4C,wCAAyI,iBAA5E,cAAiB,4BAA4B,qBAA1oB,GAA0oB,SAA2L,oBAAxI,YAAe,SAAc,WAA1tB,GAAwuB,EAAE,KAAK,uBAA/uB,EAA+uB,GAA4B,2DAAoH,sBAAnC,YAAe,sBAAqF,uBAAxC,YAAe,0BAA2F,uBAAxC,YAAe,0BAAsJ,sBAA0F,cAAnE,YAAe,oDAAwJ,eAAgB,EAAE,UAA2B,CAAb,MAAa,+BAAuC,SAAS,EAAE,EAAE,UAA2B,CAAb,MAAa,+BAAuC,SAAS,EAAE,0BAA2B,SAAO,GAAa,gDAAgD,4CAAiD,EAAG,EAAE,UAA2B,CAAb,MAAa,+BAAuC,SAAS,EAAE,6BAA8B,oBAA4mB,sBAA5mB,MAA2B,gBAAgB,EAAE,MAAM,mLAAgM,cAAc,mCAAmC,gBAAgB,SAAS,qCAAsC,mBAAmB,SAAS,UAAU,IAAI,eAAe,wCAAwC,IAAI,wBAAwB,SAAS,qCAAsC,mBAAmB,SAAS,UAAU,GAAG,SAAS,IAAI,SAAS,6BAA8B,CAA0B,EAAE,UAA2B,CAAb,MAAa,+BAAuC,SAAS,EAAE,qCAAuC,+BAAiC,GAAI,MAAM,eAAgB,GAAI,MAAM,QAAQ,GAAI,KAAK,kBAAuB,EAAG,GAAG,EAAG,eAAgB,MAAM,gBAAkD,cAAjC,YAAe,kBAAkF,gBAA9C,YAAe,6BAA+B,CAAoB,EAAE,UAA2B,CAAb,MAAa,+BAAuC,SAAS,EAAE,oBAAqB,WAAo6B,aAAp6B,QAAkD,eAAe,8CAA8C,SAAS,oBAAoB,uFAAuF,SAAS,oBAAoB,oCAAoC,OAAO,kCAAkC,YAAY,4CAAzV,IAAyV,yBAAzV,IAAyV,CAA4E,UAAU,SAA/a,KAA+a,6BAA/a,KAA+a,yBAAuE,2BAAtf,KAAqhB,WAAW,2CAA2C,yDAA2D,SAAS,mCAA/oB,IAA+oB,yFAA/oB,IAA+oB,GAAuI,QAAQ,wDAAwD,SAAS,YAAY,wDAAyD,CAAiB,EAAE,UAAa,OAAa,+BAAsC,SAAS,EAAE,iGAAiG,kCAAkC,oCAAoC,kCAAkC,IAA4G,kBAAyB,GAAG,KAAK,CAA9G,EAAiH,KAAK,GAAG,KAAK,GAAG,UAArG,GAAG,aAAa,GAAG,UAAU,GAAG,eAAe,EAAE,UAAmE,cAAe,gBAAiB,kCAAkC,uDAAuD,qBAAsB,YAAa,cAAc,gCAAiC,yEAA2E,SAAS,GAAG,EAAG,QAAU,GAAG,SAAS,IAAI,qDAAsD,EAAE,sGAAsG,eAAe,qCAAqC,eAAe,8BAA8B,+BAA+B,WAAY,eAAe,cAAc,oCAAoC,MAAM,oCAAqC,4DAA4D,mCAAoC,SAAS,oDAAqD,+BAA+B,EAAE,UAA2B,CAAb,MAAa,+BAAuC,SAAS,EAAE,EAAE,UAAa,OAAa,+BAAsC,SAAS,EAAE,uEAAuE,sFAA2G,CAAc,0BAA6B,EAA3C,YAAuF,iBAAzC,cAAiB,wBAAoF,oBAAxC,YAAe,yBAAqF,iBAArC,YAAe,qBAAsB,CAAoB,EAAE,UAA2B,CAAb,MAAa,+BAAuC,SAAS,EAAE,0BAA2B,oBAAgI,mBAAhI,MAA2B,eAAe,OAAO,wCAAyC,WAAW,0BAA2B,CAAuB,EAAE,UAA2B,CAAb,MAAa,+BAAuC,SAAS,EAAE,yBAA0B,mBAAuI,mBAAvI,MAA2B,eAAe,OAAO,gDAAiD,WAAW,yBAA0B,CAAsB,EAAE,UAA2B,CAAb,MAAa,+BAAuC,SAAS,EAAE,4BAA6B,yCAAk2C,qBAAl2C,MAAmD,eAAe,YAAY,6eAA2iB,0BAA0B,gCAAiC,8WAAkX,WAAW,mBAAmB,OAAO,sBAAsB,wBAAwB,qCAAqC,2BAA2B,wCAAwC,uBAAuB,oCAAoC,0BAA0B,wCAAwC,GAAI,CAAyB,EAAE,UAA2B,CAAb,MAAa,+BAAuC,SAAS,EAAE,kCAAmC,oBAAkiB,2BAAliB,MAA2B,iBAAiB,iGAAiG,kBAAkB,OAAO,4IAA+I,WAAW,yBAAyB,EAAE,aAAa,EAAE,cAAc,qDAAsD,eAAe,QAAQ,YAAY,aAAa,KAAK,UAAwC,KAAxC,2BAAwC,MAAY,UAAW,CAA+B,EAAE,UAA2B,CAAb,MAAa,+BAAuC,SAAS,EAAE,uBAAwB,IAA4P,IAA5P,CAA6Q,CAA7Q,gDAA4P,EAA5P,sBAA4P,EAA5P,OAA6Q,YAAmB,iBAAhS,sFAA4P,EAA8O,cAA1L,GAA8M,MAA9M,OAA8M,EAAkD,CAAnC,CAAqP,CAAnM,CAAhQ,UAA+L,EAA/L,IAA8M,SAAmC,CAAnC,UAA7gB,oBAA+jB,QAAf,EAAjP,GAAgQ,WAA/jB,qCAA+jB,2BAAgE,GAAe,8BAAiC,IAAI,iBAAkB,OAAO,gBAAiB,mCAAqC,CAAnM,IAAmM,EAAnM,CAAkN,WAAlN,EAAhQ,SAA+B,WAAY,qBAAqB,6CAA8C,0DAAoW,CAAmB,EAAE,UAA2B,CAAb,MAAa,+BAAuC,SAAS,EAAE,eAAgB,WAA8H,cAAe,wBAAuqB,kBAAmB,sBAAsB,uBAAuB,IAAI,KAAK,WAAW,iCAAiC,SAAS,cAAe,wBAAwB,cAAe,2BAA4B,cAAe,8CAAiD,cAAe,kIAAmI,EAApmC,MAAhG,eAAkB,8BAA8B,KAAK,WAAW,YAAoF,aAAuB,MAAM,OAAnM,EAAmM,GAAY,+BAAmC,cAAe,2CAA4C,IAAI,oBAAqB,cAAe,qBAAqB,uBAAuB,IAAI,KAAK,YAAW,eAAgB,yBAAizB,IAAzxB,GAAyxB,EAAzxB,EAAyxB,EAAzxB,CAA0yB,kDAA1yB,SAAqB,kBAAkB,IAAI,qBAAqB,uBAAuB,IAAI,KAAK,kBAAkB,wCAAyC,KAAK,WAAe,kCAAoC,KAAK,WAAxD,GAAwD,CAAiB,2BAAzE,IAAyE,GAAkC,QAAQ,YAAY,UAAU,YAAY,mBAArJ,EAAqJ,GAAwB,sBAAuB,SAAS,WAAxtB,iBAAuB,SAA8nC,CAA6D,EAAE,UAA2B,CAAb,MAAa,+BAAuC,SAAS,EAAE,wCAA0C,4BAA6B,eAAe,mDAAmD,iBAA2N,kBAAxM,cAAiB,MAAkG,uBAAlG,0BAAkC,wBAAwB,iCAAkC,IAAI,EAAE,kCAA2D,wBAAwB,EAAE,CAAsB,EAAE,UAAkH,CAApG,QAAoG,OAAiB,4CAAxG,sCAAuC,SAAS,EAAE,mCAAmG,eAA6F,eAA5E,cAAiB,eAAe,kCAAmC,SAAS,CAAmB,EAAE,UAA2B,CAAb,MAAa,+BAAuC,SAAS,EAAE,mBAAiJ,YAA7H,YAAe,6GAA8G,CAAgB,EAAE,UAA2B,CAAb,MAAa,+BAAuC,SAAS,EAAE,kBAAmB,EAAyL,SAAzL,MAAa,cAAc,kCAAkC,+BAA+B,EAAE,cAAc,qBAAqB,WAAW,iBAAiB,UAAU,iBAAkB,CAAe,EAAE,UAA2B,CAAb,MAAa,+BAAuC,SAAS,EAAE,wBAAyB,WAAmb,iBAAnb,MAAqB,iBAAiB,8EAA+E,eAAe,sBAAsB,cAAc,8BAA8B,WAAW,oBAAoB,kBAAkB,IAAI,qHAAqH,SAAS,0BAA0B,+BAAgC,CAAqB,EAAE,UAA2B,CAAb,MAAa,+BAAuC,SAAS,EAAE,iBAAkB,0BAA6J,WAAnI,cAAiB,uBAAuB,8DAAiE,eAAe,KAAK,EAAE,EAAE,EAAE,CAAc,EAAE,UAAY,IAAa,yDAA8D,6CAA6C,6BAA6B,aAAa,EAAE,mBAAmB,6BAA4B,oCAAqC,mFAAqF,sCAAsC,SAAS,EAAE,iCAAiC,UAAU,UAAU,UAAU,UAAU,UAAU,UAAU,UAAU,UAAU,oBAAoB,UAAU,UAAU,UAAU,UAAU,UAAU,UAAU,UAAU,UAAU,UAAU,UAAU,UAAU,UAAU,UAAU,UAAU,UAAU,UAAU,UAAU,UAAU,UAAU,EAAY,UAAY,sBAAoB,EAAE,UAA2B,CAAb,MAAa,+BAAuC,SAAS,EAAE,wFAAqU,8BAA1O,iBAAkB,SAAS,uBAAuB,IAAI,KAAK,WAAW,qBAAqB,WAAY,+BAA+B,wEAAyE,OAAO,qBAA0M,yBAApJ,gBAAmB,uBAAuB,IAAI,KAAK,WAAW,uFAA4J,0BAAzC,YAAe,0BAA0B,CAA8B,EAAE,UAA2B,CAAb,MAAa,+BAAuC,SAAS,EAAE,kCAAmC,2BAA6S,2BAAnR,YAAe,IAAI,mBAAmB,uJAA0J,+CAAgD,kCAAmC,CAA+B,EAAE,MAAyC,WAAe,MAAK,GAAE,CAAE,EAAE,MAAoC,WAAe,MAAK,GAAE,CAAE,EAAE,MAAlE,IAA6F,CAAb,MAAa,+BAAuC,SAAS,EAAE,0CAA2C,0FAA2tD,mCAA3tD,MAAoG,cAAc,0DAA0D,UAAU,8GAA+G,mBAAmB,4BAA4B,sCAAsC,yCAAyC,yDAAyD,IAAI,uBAAuB,6EAA8E,uDAAuD,sSAAsS,0BAA0B,WAAW,qBAAqB,MAAM,8CAA2D,sCAAsC,eAAe,4BAA4B,8BAA8B,WAAW,mBAAmB,wBAAwB,kHAAmH,yBAAyB,WAAW,qBAAqB,sCAAsC,uBAAwB,sCAAqC,UAAW,oCAAmC,kBAAkB,yBAAyB,IAAI,wBAAwB,QAAQ,gBAAgB,mBAAmB,0BAA0B,iCAAiC,gBAAgB,8BAA+B,CAAuC,EAAE,UAA2B,CAAb,MAAa,+BAAuC,SAAS,EAAE,yCAA0C,2CAA8a,kCAA9a,iDAA+F,cAAc,wDAAyD,SAAS,MAAM,qEAAkF,iBAAiB,0BAA0B,6CAA6C,SAAS,YAAY,UAAU,+CAAgD,CAAsC,EAAE,UAA2M,CAA7L,QAA6L,OAAiB,OAAO,kBAAoC,cAAe,eAAe,oBAAqB,cAAc,mCAAmC,WAAW,EAAE,cAAc,gBAAgB,YAAY,0BAA0B,aAAa,mBAAmB,YAAY,iBAAkB,wBAAwB,aAAa,+CAAgD,IAApjB,sCAAuC,SAAS,EAAE,gDAA2G,eAAxD,YAAe,yCAAuG,eAA6V,eAAiB,EAAE,UAA2B,CAAb,MAAa,+BAAuC,SAAS,EAAE,0DAA6D,WAA8rB,gBAAiB,MAAM,OAAO,2QAA0S,gBAAiB,OAAO,gKAAxX,oBAA1pB,cAAiB,MAAM,iCAAiC,OAAO,weAAuf,8BAA8B,sEAAqZ,eAA0M,oBAAsB,EAAE,UAA0B,sCAAsC,SAAS,EAAE,yFAAyF,yBAA0B,cAAe,mCAAuD,cAAe,MAA8E,CAAO,IAArF,6BAAqF,KAArF,wCAAqF,EAA8B,cAAe,OAAY,EAAZ,KAAY,CAAmC,cAAe,uBAAlO,kBAAqI,eAAwD,qBAA6D,mBAAoB,gDAAmD,cAAe,SAAS,cAAe,yCAA0C,OAAQ,4EAA0T,iBAA3O,YAAe,QAAQ,uBAAwB,yDAAuF,OAAO,wFAA8F,CAAoB,EAAE,UAA2B,CAAb,MAAa,+BAAuC,SAAS,EAAE,yCAA0C,wBAA0G,mCAA9E,cAAiB,MAA+B,CAAO,uBAAmV,KAAiB,eAAvS,GAAe,cAAc,gBAAgB,uBAAwB,oCAAmC,SAAS,8BAA8B,GAAG,qCAAqC,GAAG,uCAAuC,aAAa,+BAA+B,UAA0B,iCAA0C,KAAK,QAAQ,EAAE,8CAA8C,KAAK,QAAQ,EAAE,cAAc,eAAe,IAAI,6BAA6B,sEAAuE,QAAQ,OAAO,iBAAiB,qBAAqB,EAAE,WAAW,OAAO,UAAU,qEAAsE,gCAAgC,qBAAqB,UAA7yB,EAAtC,wBAAsC,EAA6yB,CAAU,EAAE,UAAa,OAAa,+BAAsC,SAAS,EAAE,uSAAuS,2BAAg5B,gBAAiB,yDAAj4B,gCAAgC,qCAAqC,iCAAiC,wCAAgN,eAAxK,aAAgB,EAAE,SAAS,2CAA2C,mDAAoD,EAAE,wCAAwC,KAA6E,0BAAvD,cAAiB,sCAA2O,8BAAxK,YAAe,IAAI,iBAAiB,gEAAiE,MAAM,mDAAoD,EAAE,QAAsG,2BAA9D,GAA4F,MAA5F,OAA0G,EAAM,MAAjG,kCAAppB,KAAopB,EAAqO,CAApI,iHAAoI,QAAz3B,KAAy3B,CAArO,EAAuU,mBAAuE,oBAAnD,YAAe,qCAA4N,yBAAjK,YAAe,qBAAoB,4BAA4B,2CAA2C,uCAAuC,gBAAgB,CAA4B,EAAE,UAA2B,CAAb,MAAa,+BAAuC,SAAS,EAAE,0BAA2B,kCAAyoC,mBAAzoC,MAA0C,gBAAgB,EAAE,uXAAyX,mBAAY,iCAAgC,GAAG,yEAA8E,EAAS,0DAAyD,GAAG,8EAAmF,EAAE,KAAO,0BAA0B,GAAG,gCAAiC,EAAE,YAAY,GAAG,uCAAwC,EAAE,EAAE,WAAW,2BAA2B,IAAI,oDAAqD,SAAS,MAAM,EAAE,WAAW,iCAAiC,aAAa,qDAAqD,EAAE,YAAY,6EAA+E,CAAuB,EAAE,cAAgC,CAAb,CAAa,QAAc,cAAiB,iDAAqD,mBAAmB,uBAAuB,iCAAiC,iBAAiB,oBAAoB,KAAK,wCAA4C,WAAW,qBAAqB,kBAAkB,IAAI,mBAAmB,SAAS,gBAAgB,GAAG,EAAE,UAAqC,CAAvB,CAAuB,YAAV,IAAU,GAAsB,eAAe,eAAe,YAAY,aAA2B,EAA3B,aAA2B,IAAK,kCAAkC,IAAwG,EAAxG,yBAAuC,QAAQ,KAAK,iDAA0D,CAAN,EAAM,uBAA0B,8BAA8B,IAAI,EAAE,aAAa,UAAU,qCAAsC,KAAM,yCAAyC,KAAM,2CAA6C,GAAM,kEAAoE,8KAA+K,wBAA0B,CAA1uB,EAA0uB,uBAA0B,sBAAsB,WAAW,EAAE,wBAAwB,qBAAqB,oCAAsC,UAAU,eAAe,KAAM,sCAAqC,KAAM,2CAA0C,KAAM,6BAA6B,EAAO,wBAAyB,YAAjjC,EAA6jC,iBAAoB,yBAAyB,EAAE,mBAAmB,EAAE,iBAAiB,EAAE,eAAe,EAAE,cAA8C,CAA3B,QAA2B,IAAc,mBAA5B,YAA+C,+BAAgC,0DAA0D,iBAAiB,QAAQ,8BAA+B,iCAAiC,yCAAyC,sCAAsC,WAAW,+BAA+B,aAAa,6BAA8B,yBAAyB,MAAM,iBAAiB,mBAAmB,wBAAwB,QAAQ,WAAW,6BAA6B,aAAa,EAAE,cAAkD,CAA/B,QAA+B,KAAe,yCAA0C,qEAAqE,kBAAkB,qDAAqD,kBAAkB,qDAA+F,gBAAgB,0DAA0D,gBAAgB,0DAApI,0CAA8L,wCAAwC,cAAc,oBAAoB,aAAc,6CAA6C,mCAAmC,6DAA6D,sFAAsF,KAAK,sFAAsF,gCAA8F,kBAAkB,oDAAoD,0FAApI,4DAAgO,0DAA4D,sCAAsC,qEAAqE,kBAAkB,qGAAqG,kBAAkB,qGAAiJ,gBAAgB,0GAA0G,gBAAgB,0GAAtL,4CAAgS,0CAA0C,cAAc,wBAAwB,IAAwM,EAAxM,SAAc,0DAA0D,gDAAgD,2EAA2E,KAAK,GAAM,sFAAwF,KAAK,sCAAuC,0HAAkM,sBAAsB,kFAAiF,uFAA/K,sEAAwQ,oEAAsE,KAAK,kBAAmB,4DAA4D,kBAAmB,4DAA4D,gBAAiB,iDAAiD,gBAAiB,iDAAv4F,cAAu4F,CAAkD,EAAE,cAAgC,CAAb,CAAa,QAAc,YAAe,aAAa,EAAE,UAA2B,CAAb,CAAuB,YAAV,IAAU,GAAsB,oBAAoB,WAAW,gHAAkH,UAAjL,EAA2L,qBAAwB,OAAU,WAAgB,0BAA0B,IAAI,uSAAuS,gIAAljB,EAAkrB,sBAAyB,oBAAoB,WAAW,sPAA2S,YAAY,EAAE,cAAgC,CAAb,CAAa,QAAc,gBAAmB,iCAAiC,mBAAmB,wBAAwB,oBAAoB,uBAAuB,4BAA4B,EAAE,cAAgC,CAAb,CAAa,UAAa,WAAY,gBAAgB,4BAA4B,uBAAyB,uBAAuB,UAAU,iCAAmC,aAAa,oBAAqB,UAAU,mCAAqC,0BAAyB,kBAAmB,UAAU,UAAU,oCAAmC,sFAAsF,mBAAmB,6CAA6C,4BAAqD,yCAArD,uBAAqD,OAAyC,6CAAqD,iCAAiC,qBAAqB,oCAAoC,yCAAwC,oCAAmC,+BAA+B,mDAAqD,4CAA4C,kCAAmC,uBAAuB,yJAAmK,8BAA8B,iJAAiJ,gCAAgC,kBAAkB,kFAAkF,gCAAgC,mBAAmB,mFAAmF,8BAA8B,6DAA6D,+FAA+F,EAAE,UAA6jD,CAA/iD,QAA+iD,SAAmB,6BAA6B,WAAW,2CAA8C,SAA2F,cAAe,gBAAgB,0CAA0C,sCAAsC,eAAe,UAAU,8FAA8F,wBAA4B,eAAgB,kDAAkD,aAAa,kDAAkD,OAAO,eAAe,SAAS,0CAA0C,WAAW,iBAAiB,mCAAmC,4CAA4C,IAA7xE,EAAS,eAAT,EAA0B,YAA1B,EAAwC,kBAAxC,EAA4D,WAA5D,EAAyE,aAAzE,EAAwF,UAAxF,EAAoG,UAApG,EAAgH,cAAhH,EAAgI,4GAAhI,EAA8O,OAA9O,EAA8O,oEAA9O,EAA8T,8CAAgD,4CAA4C,KAAK,0CAA0C,0DAAzc,EAAogB,qBAAuB,+CAA3hB,EAA2kB,qBAAuB,8BAAlmB,EAAgoB,MAAhoB,EAAgoB,oBAA8B,WAAW,mHAAzqB,EAAyxB,kBAAoB,IAAI,MAAjzB,EAAizB,yBAAiC,oCAAoC,MAAM,aAAa,GAAz4B,EAA44B,kBAA54B,EAAg6B,yBAAh6B,EAA27B,sBAAwB,0EAAn9B,EAAm9B,OAAn9B,EAAm9B,2DAAn9B,EAAomC,6CAA+C,iFAAnpC,EAAouC,0BAApuC,EAAgwC,gCAAkC,6BAA6B,EAAE,wBAAwB,yBAAyB,0DAAl3C,EAA46C,2BAA6B,6BAA6B,cAAt+C,EAAs+C,4CAA8K,UAAppD,EAA+pD,oBAAsB,iDAA4mB,aAAc,mCAAoC,0BAA0B,YAAY,KAAK,WAAW,cAAc,kBAAkB,yCAAyC,KAAK,yEAAyE,0BAA0B,mBAAmB,YAAY,WAAW,kCAAkC,iBAAiB,gDAAgD,wBAAwB,eAAe,OAAO,0CAA0C,OAAO,+DAA+D,kBAAkB,CAAv3F,EAAu3F,+CAAkD,kBAAkB,EAAE,cAAgC,CAAb,CAAa,UAAa,8CAAkD,kBAAmB,iDAAiD,cAAe,cAAe,oEAAoE,aAAa,uEAAyE,iBAAkB,2BAA2B,2BAA2B,cAAc,GAAI,YAAY,eAAgP,kBAAmB,WAAyE,gBAAiB,uCAAyZ,kBAAmB,KAAK,KAAK,4DAA4D,KAAK,SAAS,kCAAmC,aAAgS,kBAAmB,4DAApqC,aAAc,oBAAoB,uBAAuB,sEAAsE,kCAAkC,+DAAoN,uCAAyC,eAA/J,gBAAmB,KAAK,MAAM,yBAAyB,QAA2H,+BAA+B,qHAAsH,8BAA8B,4DAA8D,+BAA+B,sCAA4L,+BAA+B,gBAAiB,mCAAoC,qCAAqC,+BAA+B,2BAA4B,mCAAoC,6BAA6B,6BAA8G,gCAAgC,8BAA+B,yCAAyC,gCAAgC,gBAAiB,6CAA+C,yCAAyC,8BAA8B,6CAA6C,+BAA+B,8CAA8C,4CAA6C,WAAW,iBAAiB,YAAY,WAAW,iBAAiB,8BAA8B,mBAAmB,+BAAgC,kBAAkB,6BAA8B,oBAAqB,oCAAqC,+BAA+B,kBAAmB,8DAAgE,4BAA4B,iFAAoF,6BAA6B,qLAAuL,8BAA8B,uCAAuC,uFAAuF,8BAA8B,gEAAgE,EAAE,mCAAmC,UAAU,yBAAyB,iCAAoC,EAAE,cAAgC,CAAb,CAAa,UAAc,UAAY,wDAA0D,WAAY,aAAc,aAAqd,kBAAmB,0EAA1d,wBAAyB,yJAA+J,WAAW,iBAAiB,iCAAiC,iBAAiB,WAAW,iBAAiB,8BAA+B,8CAAgD,mBAAmB,kEAAiK,+BAAgC,6BAA8B,iDAAkD,eAAgB,EAAE,cAAgC,CAAb,CAAa,UAAa,mCAAsC,gBAAiB,yEAAyE,cAAc,wCAAwC,wCAAyC,4DAA6D,+BAA8B,aAAa,mCAAoC,+BAA8B,cAAe,4BAA4B,4BAA4B,2CAA4C,IAAK,IAAkrB,aAAc,qBAAsB,wBAAwB,KAAK,IAAI,uFAAuF,8HAA8H,IAAI,KAAK,KAAK,IAAI,KAAK,oCAAqC,mFAAmF,yDAAyD,wBAAwB,KAAK,IAAI,0FAAyF,SAAU,IAAI,KAAK,oCAAqC,qFAAqF,uCAA6F,gBAAiB,mDAAsQ,aAAc,uCAAwC,8DAAh0D,aAAc,uEAAuE,8BAA8B,iBAAiB,kBAAkB,8UAA8U,6DAA8D,UAAU,GAAG,6BAA6B,wBAAwB,8BAA8B,oBAAoB,uBAAuyB,4BAA4B,0BAA8F,+BAA+B,uCAAwC,gCAAiC,gCAAgC,uCAAwC,kCAA0J,6BAA6B,uCAAwC,6CAA6C,sBAAsB,8BAA8B,uCAAwC,8CAA8C,sBAAsB,6BAA6B,4CAA4C,8BAA+B,kEAAkE,UAAU,eAAe,gDAAgD,uCAAuC,8BAA8B,mBAAmB,6BAA8B,6BAA6B,uBAAuB,uCAAwC,YAAY,4CAA6C,gCAAgC,aAAa,iCAAiC,UAAU,mBAAmB,KAAM,qBAAoB,KAAM,iCAAgC,KAAM,aAAY,EAAwB,CAAxB,qBAAwB,iBAAkB,KAAM,qBAAoB,KAAM,oEAAmE,aAAa,yBAAyB,gCAAmC,iCAAiC,qBAAqB,iBAAiB,2BAA4B,mBAAmB,2BAA4B,mBAAmB,sCAAuC,oBAAoB,2BAA4B,qBAAqB,4BAA6B,GAAG,EAAE,cAAgC,CAAb,CAAa,UAAc,UAAY,wDAA0D,WAAY,cAAe,eAAgB,wBAAyB,yDAA4D,8BAA+B,oBAAoB,0KAA0K,eAAgB,EAAE,cAAgC,CAAb,CAAa,UAAc,WAAmF,kBAAmB,sEAAsE,8FAAhK,oEAA+P,0CAA2C,mDAAmD,WAAW,sCAAuC,0BAAe,sBAAsB,0BAA0B,IAAI,GAAO,CAAI,8FAA8F,qCAAqC,wBAAa,UAAiB,yBAAyB,uDAAuD,SAAS,gCAAgC,oCAAoC,EAAE,SAAS,0CAA0C,KAAK,IAAI,SAAS,4BAA6B,sGAAsG,EAAE,UAAqC,CAAvB,CAAuB,YAAV,CAA0B,EAAE,cAAgC,CAAb,CAAa,WAAc,EAAE,UAA6K,CAA/J,QAA+J,IAAlJ,EAAgK,0FAAhK,EAAS,gBAAT,EAA2B,YAA3B,EAAyC,kBAAzC,EAA6D,YAA7D,EAA2E,kBAAoB,YAA/F,EAA2G,SAA3G,EAAsH,WAAa,cAAyH,IAAK,EAAE,cAAgC,CAAb,CAAa,aAAgB,EAAE,KAAI,gBAAc,KAAI,4BAAwB,GAAmB,YAAmB,+DAA+D,sBAAsB,uCAAuC,gBAAgB,mCAAmC,0BAA2B,gFAAmF,kBAAkB,oDAAoD,eAAe,yBAAyB,IAAI,KAAK,yCAAyC,iBAAiB,SAAS,GAAG,SAAS,QAAQ,IAAI,oCAAoC,QAAQ,oBAAoB,SAAS,eAAe,gCAAgC,qCAAqC,+HAA+H,6BAA4B,uIAAuI,iBAAiB,ofAAkf,0CAA0C,yBAAyB,wCAAwC,oYAA0Z,uCAAuC,QAAQ,IAAI,2CAA2C,QAAQ,YAAY,kCAAkC,wBAAwB,SAAS,GAAG,SAAS,QAAQ,IAAI,oCAAoC,QAAQ,oBAAoB,YAAY,sCAAsC,mCAAmC,kHAAiH,6MAA0M,mCAAkC,yBAAyB,mEAAmE,OAAO,mCAAmC,iDAAqD,oCAAoC,+CAAmD,6BAA6B,mCAAwB,iIAAgI,IAAO,2VAA6V,kCAAkC,+FAA6F,qDAAoD,8DAA6D,wCAAuC,mEAAkE,wDAAuD,iEAA+D,oCAAoC,uBAAwB,2CAA2C,QAAS,qaAArgI,YAAmB,EAAk/H,oDAA4d,+CAA+C,eAAe,sBAAsB,+BAA+B,6CAA6C,eAAe,mBAAmB,+BAA+B,8DAA8D,eAAe,oCAAoC,+BAA+B,0DAA0D,eAAe,gCAAgC,+BAA+B,yDAAyD,eAAe,+BAA+B,+BAA+B,sCAAsC,+EAA8E,6EAA6E,eAAe,gDAAgD,mCAAmC,yCAAyC,6CAA6C,oKAAmK,uDAAwD,IAAI,GAAG,GAAG,KAAI,eAAa,EAAO,YAAa,qGAAqG,YAAe,kBAAkB,cAAc,2CAA2C,OAAO,uBAAwB,iCAAiC,yBAAyB,GAAG,GAAG,cAAiB,cAAc,2CAA2C,OAAO,+BAAgC,iCAAiC,wBAAwB,GAAG,GAAG,KAAI,eAAc,eAAkB,cAAc,YAAY,iaAAsd,sDAAsD,iCAAgC,iXAA+W,iCAAiC,mBAAmB,+RAA+R,EAAE,GAAG,GAAG,KAAI,kBAAkB,cAAc,mHAAmH,8CAA8C,OAAO,2GAA4G,iCAAiC,yBAAyB,gBAAgB,EAAE,oCAAoC,qDAAsD,qCAAqC,gBAAgB,aAAa,KAAK,UAAwC,KAAxC,2BAAwC,MAAY,SAAS,GAAG,GAAG,sDAA6D,cAAc,OAAO,0DAA0D,2IAAyI,aAAa,ubAAib,eAAe,8DAA6D,kDAAiD,oDAAmD,gEAA8D,YAAW,CAAE,iEAA+D,YAAY,CAAE,8DAA6D,oEAAkE,mBAAmB,CAAE,8HAA2H,eAAe,iJAAzzC,EAAi9C,wCAAwC,uIAAz/C,CAAy/C,gJAAz/C,CAAy/C,IAAuS,eAAc,CAA6kC,KAAI,SAAlkC,CAAkkC,oBAA2B,gBAAgB,uFAAuF,sBAAqB,quBAAmuB,yCAAyC,+EAA+E,oCAAoC,+BAA+B,oGAAmG,iCAAiC,iCAAiC,kCAAkC,WAAW,yCAAyC,sBAAsB,kBAAkB,qBAAqB,kBAAkB,8BAA8B,EAAE,sCAAsC,mDAAmD,6GAA4G,OAAO,kMAAiM,kCAAkC,WAAW,iCAAiC,4EAAgF,EAAhF,EAAoF,+BAA+B,+BAA+B,IAAI,WAAW,EAAE,uCAAuC,WAAW,qGAAqG,4BAA4B,oBAAwB,yBAAyB,uEAAoE,oEAAoE,wCAAwC,KAAM,iIAAgJ,EAAE,qBAAqB,kBAAkB,qBAAqB,gCAAgC,CAAE,gDAA+C,MAAM,2DAAuE,sBAAsB,kCAAiC,EAAE,EAAE,EAAE,yCAAyC,WAAW,uBAAuB,iBAAiB,qDAAqD,oFAAoF,oBAAoB,+CAA8C,GAAG,kEAAmE,0DAAyD,WAAW,8DAA6D,oCAAoC,qEAAqE,GAAG,GAAG,cAAkB,oBAAoB,kCAAiC,cAAa,gCAAgC,cAAc,gBAAgB,uEAAsE,OAAS,qBAAqB,oHAA+H,aAAa,mBAApB,OAAuC,uEAAuE,kBAA0B,gBAAgB,6BAA6B,qBAAqB,UAA1E,KAA0E,+BAAsC,WAAW,wGAAwG,oDAAoD,mCAAmC,eAAe,4IAA4I,mCAAmC,+NAA+N,GAAG,KAAK,GAA6B,WAAa,wBAA1C,IAA0C,uBAA1C,EAA0C,CAAuD,CAAY,QAAH,CAAG,IAAiB,eAAe,kBAAkB,YAAY,MAAM,iEAAiE,wDAAwD,kBAAkB,kBAAkB,uBAAuB,IAAjjM,EAAoB,IAA6hM,GAAjjM,GAAO,aAAa,CAAkC,CAAlC,kBAA0B,CAA1B,OAA0B,EAAmgM,GAA3/L,+BAAuC,iBAAo9L,EAAp9L,iBAAoC,+BAA+B,cAAi5L,EAAj5L,cAA8B,IAAI,CAA23L,yLAAyL,6CAA6C,KAAU,cAAiB,uEAAuE,0BAAyB,kCAAkC,iEAAgE,wDAAsD,SAAS,IAA5R,MAA4R,6DAAkE,iHAAiH,yFAAmH,OAAO,sEAAwE,sFAAsF,6BAA0C,OAAoG,8BAApG,6FAAoG,cAA+C,gDAA+C,+EAA8E,kCAAiC,SAAS,+DAAgH,OAAhH,6CAAgH,CAAU,+CAA+C,UAAU,0BAA0B,oEAAsE,6EAA2E,sCAAqC,yCAAyC,2BAA2B,sCAAsC,wBAAwB,+CAA+C,qDAAqD,GAAG,GAAG,KAAI,gBAAa,KAAI,2BAAyB,CAAsC,aAAxB,CAA0C,qCAAqC,iCAAiC,IAAI,IAAoB,aAAf,eAAe,uDAAsE,UAAS,uBAA0B,CAAiT,iBAAkB,IAAgF,QAAhF,GAAO,wBAAwB,qBAAqB,YAAY,gBAAgB,CAAS,UAAU,iCAAiC,2DAA2D,aAAY,GAAI,cAAc,mBAAmB,IAAiB,EAAjB,MAA+B,wDAA4D,KAAK,EAAE,KAAK,kHAAkH,yCAAyC,kBAAkB,KAAM,0BAAyB,mBAAoB,+BAA8B,QAAS,mCAAkC,QAAS,2EAA0E,IAAI,SAAS,yCAAyC,aAAa,MAAM,2BAA2B,iBAAiB,MAAM,oBAAoB,2BAA2B,MAAM,+BAA+B,SAAS,cAAc,SAAS,YAAY,QAAQ,MAAM,qBAAqB,OAAO,+BAAxwB,EAAwwB,CAAiC,kBAAkB,oDAAoD,eAAe,yBAAyB,IAAI,KAAK,yCAAyC,iBAAiB,SAAS,GAAG,SAAS,QAAQ,IAAI,oCAAoC,QAAQ,oBAAoB,SAAS,eAAc,gBAAgB,aAAa,2GAAwH,kDAAkD,qCAAqC,gDAAgD,aAAa,4GAA2G,EAAE,0BAA0B,eAAe,sBAAsB,MAAM,oBAAkB,6CAAn2E,kBAAwB,CAA20E,uWAA4Z,iDAAiD,eAAe,MAAM,mJAA+J,+BAA+B,oDAAlxF,QAAk0F,OAAl0F,EAAk0F,KAAl0F,EAAk0F,OAAl0F,EAAk0F,OAAl0F,EAAk0F,CAA5yF,UAAo1F,2BAA2B,gBAAgB,sFAAiF,oBAA0B,mBAAkB,EAAE,CAA/7F,kCAAyC,cAAc,IAAI,aAAa,SAAS,MAAM,cAAc,IAAI,cAAc,SAAS,MAAM,kBAArM,EAAmN,kBAArM,EAAd,EAAmN,CAArM,CAAqM,iBAArM,sBAA0C,MAAK,EAAsJ,UAAuC,+BAA+B,CAAquF,CAAE,+BAA+B,aAAa,kBAAkB,cAAc,0EAAgF,wDAAkT,QAArO,uFAA6F,8BAA8B,qBAAqB,sEAAmF,EAAE,CAAlT,QAAkT,CAArO,CAAuP,gBAAiB,IAAI,GAAG,KAAI,4BAAyB,eAAmB,+DAA+D,sBAAsB,uCAAuC,gBAAgB,mCAAmC,0BAA2B,gFAAmF,kBAAkB,oDAAoD,eAAe,yBAAyB,IAAI,KAAK,yCAAyC,iBAAiB,SAAS,GAAG,SAAS,QAAQ,IAAI,oCAAoC,QAAQ,oBAAoB,SAAS,CAAynD,MAAznD,YAAe,aAAa,mVAAmV,sCAAsC,QAAQ,sEAAsE,SAAS,uCAAuC,mBAAmB,SAAS,gDAA+C,sEAAqE,kDAAkD,GAAQ,YAAe,IAAvB,IAAuB,IAAQ,qCAAqC,IAAI,2BAA2B,QAAQ,YAAY,uDAA2D,iBAAiB,4BAA4B,8IAAkJ,uFAA2F,6BAA6B,SAAS,GAAG,SAAS,QAAQ,IAAI,oCAAoC,QAAQ,oBAAoB,SAAS,kCAAkC,iEAAiE,+CAA+C,YAAY,WAAW,KAAK,sBAAsB,gDAAgD,SAAS,6CAA6C,oCAAoC,GAAG,IAAa,IAAI,CAAmF,IAAoT,cAApX,CAAoX,EAAkB,IAAgF,QAAhF,GAAO,wBAAwB,qBAAqB,YAAY,gBAAgB,CAAS,UAAU,iCAAiC,2DAA2D,aAAY,GAAI,cAAc,mBAAmB,IAAiB,EAAjB,MAA+B,wDAA4D,KAAK,EAAE,KAAK,kHAAkH,yCAAyC,kBAAkB,KAAM,0BAAyB,mBAAoB,+BAA8B,QAAS,mCAAkC,QAAS,2EAA0E,IAAI,SAAS,yCAAyC,aAAa,MAAM,2BAA2B,iBAAiB,MAAM,oBAAoB,2BAA2B,MAAM,+BAA+B,SAAS,cAAc,SAAS,YAAY,QAAQ,MAAM,qBAAqB,OAAO,+BAAxwB,EAAwwB,EAAkC,eAAmB,KAAM,aAAiB,CAAE,IAAzB,EAAyB,+CAAgE,IAAI,IAAh9C,QAAg9C,CAAv7C,CAAu7C,cAAsO,OAAzB,EAA7tD,OAAoiD,GAApiD,iBAAoiD,GAApiD,mBAAoiD,EAApiD,EAA8iD,EAA9iD,CAA6tD,SAAU,EAAvqD,EAA+/C,EAA8K,GAA9K,EAA//C,EAA+/C,OAA//C,EAA+/C,OAA//C,EAA+/C,WAA0C,MAAM,2BAA2B,gBAAgB,wBAAmB,sCAA2C,EAAE,CAAlkD,kCAAyC,cAAc,IAAI,aAAa,SAAS,MAAM,cAAc,IAAI,cAAc,SAAS,MAAM,kBAArM,EAAmN,mBAArM,CAAd,EAAmN,CAArM,CAAqM,iBAArM,sBAA0C,MAAK,EAAE,IAAoJ,MAAuC,+BAA+B,EAAE,EAAy3C,GAAS,kEAA2E,8DAA6D,2DAA0D,SAAS,2EAAyE,2BAA2B,kBAAkB,aAAY,4EAA4E,MAAM,IAAI,gBAAgB,sBAAsB,uCAAuC,0CAA0C,oBAAmB,GAAG,2BAA6B,+DAA+D,sBAAsB,uCAAuC,gBAAgB,mCAAmC,0BAA2B,gFAAmF,eAAe,cAAc,uBAAuB,yCAAyC,aAAa,IAAI,8CAA8C,QAAQ,YAAY,cAAc,wBAAwB,SAAS,GAAG,SAAS,QAAQ,IAAI,oCAAoC,QAAQ,oBAAoB,+BAA+B,+BAA+B,IAAI,oBAAoB,iFAAqF,EAAE,EAAE,mCAAmC,QAAQ,IAAI,8CAA8C,QAAQ,WAA0B,CAAd,OAAc,aAAgB,SAAS,GAAG,SAAS,QAAQ,IAAI,oCAAoC,QAAQ,qBAAqB,+BAA+B,QAAQ,IAAI,8CAA8C,QAAQ,WAAY,QAAc,SAAY,SAAS,GAAG,SAAS,QAAQ,IAAI,oCAAoC,QAAQ,qBAAqB,iCAAiC,aAAa,IAAI,8CAA8C,QAAQ,YAAY,cAAc,sBAAsB,SAAS,GAAG,SAAS,QAAQ,IAAI,oCAAoC,QAAQ,oBAAoB,iCAAiC,+BAA+B,IAAI,IAAI,EAAE,GAAG,GAAG,cAAkB,cAAc,0CAA0C,gCAAgC,iCAAiC,yBAAyB,mCAAmC,yBAAyB,GAAG,GAAG,CAAO,YAAa,wGAAwG,UAAY,EAAG,kBAAkB,cAAc,kBAAiB,CAAQ,CAAN,GAAM,oDAAwD,IAAtqkB,EAAe,0BAA8C,EAA2ikB,EAA8D,iBAAoB,OAA3qkB,kBAA8C,EAA7D,EAA0rkB,CAA3qkB,EAA8C,6CAAgD,ylBAA4vB,IAAI,aAAa,IAA60iB,6HAAwI,IAAI,uBAAuB,EAAE,mCAAmC,eAAe,gBAAgB,2BAA2B,qCAAqC,6CAA6C,2CAA2C,yDAAyD,wCAAwC,0CAA0C,0CAA0C,iGAAiG,gFAA+E,yGAAyG,+CAA+C,gCAAgC,kCAAkC,mBAAiB,kPAAiP,mCAAmC,4FAA4F,+BAA+B,8BAA8B,2FAAgG,IAAI,+BAA+B,+CAAqD,oBAAoB,+BAAgC,EAAE,EAAE,EAAE,iCAAiC,gCAAgC,2BAA2B,sBAAuB,EAAE,oBAAoB,oBAAoB,cAAc,EAAE,EAAE,iCAAiC,2CAA2C,wCAAwC,MAAM,2EAAuF,0CAA0C,MAAM,yEAAqF,gDAAgD,mEAAqN,EAArN,kBAAsF,0BAA0B,sGAAqG,uBAA2B,sBAAsB,KAAK,qEAAoE,cAAc,EAAE,8CAA8C,2CAA0C,uBAA0B,+BAA+B,sGAAqG,8DAA8D,wCAAuC,wBAAwB,mCAAkC,qCAAqC,GAAG,KAAI,UAAU,MAAK,GAA+C,kBAAiB,IAA9D,CAAkE,gBAAc,KAAI,KAAI,KAAI,+BAA6B,+DAA+D,sBAAsB,uCAAuC,gBAAgB,mCAAmC,0BAA2B,gFAAmF,kBAAkB,oDAAoD,eAAe,yBAAyB,IAAI,KAAK,yCAAyC,iBAAiB,SAAS,GAAG,SAAS,QAAQ,IAAI,oCAAoC,QAAQ,oBAAoB,SAAS,eAAe,kBAAkB,kBAAkB,iDAAiD,oGAAmH,yBAA4B,oGAAoG,gCAAgC,mDAAiD,sKAApU,aAAmH,GAAiN,CAAyK,yDAAyD,eAAe,0BAA0B,iBAAiB,oDAAoD,+BAA+B,sDAAsD,eAAe,4BAA4B,iBAAiB,sDAAsD,+BAA+B,4CAA4C,eAAe,kBAAkB,iBAAiB,4CAA4C,+BAA+B,8DAA8D,eAAe,qEAAqE,+BAA+B,yCAAyC,qgBAA+hB,uCAAuC,QAAQ,IAAI,2CAA2C,QAAQ,YAAY,kCAAkC,wBAAwB,SAAS,GAAG,SAAS,QAAQ,IAAI,oCAAoC,QAAQ,oBAAoB,YAAY,iCAAiC,wBAAwB,2CAA2C,kCAAkC,yCAAyC,gCAAgC,sCAAsC,oBAAoB,yCAAyC,6DAA6D,oKAAmK,uDAAwD,IAAI,gDAAgD,sCAAsC,6CAA6C,8GAA6G,GAAG,GAAG,cAAkB,qCAAqC,iCAAiC,IAAI,IAAoB,aAAf,eAAe,uDAAsE,UAAS,uBAA0B,eAAe,gBAAgB,gDAAgD,oCAAoC,8FAA6F,UAAU,KAAK,gEAAgE,GAAG,GAAG,WAAc,CAAuxB,aAAzwB,EAAywB,kBAAmC,OAA+D,kCAAyC,cAAc,IAAI,aAAa,SAAS,MAAM,cAAc,IAAI,cAAc,SAAS,MAAM,kBAArM,EAAmN,mBAArM,CAAd,EAAmN,CAArM,CAAqM,iBAArM,sBAA0C,MAAK,EAAsJ,UAAuC,+BAA+B,EAAE,kBAAkB,IAAgF,QAAhF,GAAO,wBAAwB,qBAAqB,YAAY,gBAAgB,CAAS,UAAU,iCAAiC,2DAA2D,aAAY,GAAI,cAAc,mBAAmB,IAAiB,EAAjB,MAA+B,wDAA4D,KAAK,EAAE,KAAK,kHAAkH,yCAAyC,kBAAkB,KAAM,0BAAyB,mBAAoB,+BAA8B,QAAS,mCAAkC,QAAS,2EAA0E,IAAI,SAAS,yCAAyC,aAAa,MAAM,2BAA2B,iBAAiB,MAAM,oBAAoB,2BAA2B,MAAM,+BAA+B,SAAS,cAAc,SAAS,YAAY,QAAQ,MAAM,qBAAqB,OAAO,+BAAxwB,EAAwwB,CAAiC,eAAe,gBAAgB,iDAAiD,yCAAyC,wCAAwC,MAAM,2BAA2B,gBAAgB,4FAA4F,+CAA+C,SAAI,uBAA4B,EAAE,EAAE,kCAAkC,oCAAoC,qBAAqB,EAAE,iCAAiC,wCAAwC,2BAA2B,gBAAgB,4DAA4D,oBAAoB,SAAI,uBAA4B,EAAE,EAAE,GAAG,GAAG,cAAkB,cAAc,yCAAyC,yBAAyB,mCAAmC,iCAAiC,yBAAyB,GAAG,GAAG,GAAkB,gBAAkB,6JAA6J,CAAY,QAAH,GAAG,GAA+B,cAAc,kBAAiB,CAAE,IAArhG,cAAqhG,iBAAqB,CAAryG,CAAO,6CAA6C,+JAA+J,yBAAyB,GAAyjG,YAA1iG,CAA0iG,4EAAiG,8BAA3oG,iCAAiD,CAAO,+CAAmlG,EAAnlG,uVAAsd,EAA6nF,8DAAkG,6CAA6C,uHAAsH,wEAAuE,SAAlgB,UAAkgB,uCAAmD,iFAAiF,wCAAwC,sDAAsD,+CAA+C,2HAA2H,sFAAqF,8LAA8L,mCAAmC,4LAA2L,iCAAiC,8JAA6J,kCAAkC,oDAAoD,GAAG,GAAG,CAAO,YAAa,oDAAoD,UAAY,EAAG,kBAAkB,oBAAoB,kCAAiC,cAAa,gCAAgC,cAAc,gBAAgB,uEAAsE,OAAS,qBAAqB,oHAA+H,aAAa,mBAApB,OAAuC,uEAAuE,wBAAwB,OAA+D,kCAAyC,cAAc,IAAI,aAAa,SAAS,MAAM,cAAc,IAAI,cAAc,SAAS,MAAM,kBAArM,CAAmN,oBAArM,CAAd,EAAmN,CAArM,CAAqM,iBAArM,sBAA0C,MAAK,EAAsJ,UAAuC,+BAA+B,EAAE,kBAAkB,IAAgF,QAAhF,GAAO,wBAAwB,qBAAqB,YAAY,gBAAgB,CAAS,UAAU,iCAAiC,2DAA2D,aAAY,GAAI,cAAc,mBAAmB,IAAiB,EAAjB,MAA+B,wDAA4D,KAAK,EAAE,KAAK,kHAAkH,yCAAyC,kBAAkB,KAAM,0BAAyB,mBAAoB,+BAA8B,QAAS,mCAAkC,QAAS,2EAA0E,IAAI,SAAS,yCAAyC,aAAa,MAAM,2BAA2B,iBAAiB,MAAM,oBAAoB,2BAA2B,MAAM,+BAA+B,SAAS,cAAc,SAAS,YAAY,QAAQ,MAAM,qBAAqB,OAAO,+BAAxwB,EAAwwB,EAAkC,eAAmB,+DAA+D,sBAAsB,uCAAuC,gBAAgB,mCAAmC,0BAA2B,iFAAoF,eAAe,eAAe,eAAe,qBAAqB,mEAAoE,eAAe,GAAI,IAA+H,WAAhH,CAAgH,GAA2B,cAAc,2BAA2B,8CAA8C,OAA/F,QAA+F,EAAS,QAAQ,iBAAiB,MAAkG,uBAAlG,0BAAkC,wBAAwB,kCAAkC,IAAI,EAAE,mBAA4C,yBAAyB,aAAa,wBAAwB,EAA0Z,eAAe,4BAA+kB,mBAAmB,YAAY,eAA0tB,WAA1tB,sIAAmJ,4KAAnb,IAAI,CAAO,YAAa,0NAA0N,UAAY,EAAusB,4BAA4B,MAAM,IAA4C,KAAI,UAA7B,QAA+C,aAAa,yBAAyB,uCAAuC,8BAA8B,qCAAqC,WAA4I,OAA5I,yCAAsD,oFAAoF,CAAE,CAAS,4CAA4C,qHAAqH,uCAAuC,uKAAuK,GAAG,GAAG,KAAI,8BAA4B,oBAAoB,kCAAiC,cAAa,gCAAgC,cAAc,gBAAgB,uEAAsE,OAAS,qBAAqB,oHAA+H,aAAa,mBAApB,OAAuC,uEAAuE,iBAAiB,gBAAgB,iDAAiD,2CAA2C,qBAAoB,oEAAyD,kFAAiF,CAAO,yTAA0T,GAAG,GAAG,eAA2B,aAAa,+CAA+C,UAApE,KAAoE,gCAAuC,oBAAoB,GAAG,KAAK,eAA2B,aAAa,+CAA+C,OAApE,QAAoE,gCAAuC,mBAAQ,iFAAgF,IAAO,gBAAoB,GAAG,KAAK,eAA2B,aAAa,+CAA+C,OAApE,QAAoE,mCAA0C,mBAAQ,mFAAkF,IAAO,gBAAoB,GAAG,KAAK,cAAkB,kBAAkB,qEAAqE,2CAA2C,6CAA6C,wCAAwC,gDAAgD,GAAG,GAAG,eAA2B,aAAa,+CAA+C,OAApE,QAAoE,EAAS,KAAK,eAA2B,aAAa,+CAA+C,OAApE,QAAoE,EAAS,KAAK,eAA2B,aAAa,+CAA+C,OAApE,QAAoE,EAAS,KAAK,eAAe,uBAAuB,kBAAkB,cAAc,yBAAyB,iDAAiD,0BAA6E,cAA7E,gDAA6E,GAAmB,yCAAyC,wBAA2E,cAA3E,gDAA2E,GAAmB,+CAA+C,gCAAmF,cAAnF,gDAAmF,GAAmB,iDAAiD,wFAAyF,6DAA6D,mDAAmD,0FAA2F,6DAA6D,yDAAyD,kGAAmG,6DAA6D,sDAAsD,gEAAgE,yDAAyD,mEAAmE,GAAG,GAAG,cAAkB,cAAc,6BAA6B,sDAAsD,kCAAkC,2CAA2C,+FAA+F,2IAA2I,EAAE,GAAG,GAAG,cAAkB,oBAAoB,kCAAiC,cAAa,gCAAgC,cAAc,gBAAgB,uEAAsE,OAAS,qBAAqB,oHAA+H,aAAa,mBAApB,OAAuC,uEAAuE,oBAAoB,IAAgF,QAAhF,GAAO,wBAAwB,qBAAqB,YAAY,gBAAgB,CAAS,UAAU,iCAAiC,2DAA2D,aAAY,GAAI,cAAc,mBAAmB,IAAiB,EAAjB,MAA+B,wDAA4D,KAAK,EAAE,KAAK,kHAAkH,yCAAyC,kBAAkB,KAAM,0BAAyB,mBAAoB,+BAA8B,QAAS,mCAAkC,QAAS,2EAA0E,IAAI,SAAS,yCAAyC,aAAa,MAAM,2BAA2B,iBAAiB,MAAM,oBAAoB,2BAA2B,MAAM,+BAA+B,SAAS,cAAc,SAAS,YAAY,QAAQ,MAAM,qBAAqB,OAAO,+BAAxwB,EAAwwB,CAAiC,CAAstC,eAA0B,aAAa,6BAA6B,OAAlD,QAAkD,EAAS,CAAnyC,IAAwyC,OAAzxC,cAAc,yDAAyD,qCAAqC,kDAAkD,wCAAwC,oBAAoB,sDAAsD,UAAU,4EAA4E,iCAAiC,wFAAwF,+BAA+B,kDAAkD,6BAA6B,QAAQ,2BAA2B,gBAAgB,oDAAqD,iEAA4D,wCAAwC,aAAkB,EAAE,gCAAgC,QAAQ,2BAA2B,gBAAgB,sDAAuD,wFAAyF,8CAAwC,aAAkB,EAAE,2CAA2C,eAAe,2BAA2B,+BAA+B,IAAI,GAAG,CAAkF,iBAAqB,oDAAoD,eAAe,yBAAyB,IAAI,KAAK,yCAAyC,iBAAiB,SAAS,GAAG,SAAS,QAAQ,IAAI,oCAAoC,QAAQ,oBAAoB,SAAS,eAAe,cAAc,2FAAyF,4CAA4C,qEAAqE,2CAA2C,EAAE,aAAa,yCAAyC,WAAW,4CAA4C,yEAAyE,aAAa,QAAQ,sCAAsC,wCAAwC,0BAA0B,wCAAwC,0CAA0C,2BAA2B,0EAA0E,EAAE,gCAAgC,oCAAoC,8CAA6C,GAAG,GAAG,eAAmB,+DAA+D,sBAAsB,uCAAuC,gBAAgB,mCAAmC,0BAA2B,gFAAmF,kBAAkB,oDAAoD,eAAe,yBAAyB,IAAI,KAAK,yCAAyC,iBAAiB,SAAS,GAAG,SAAS,QAAQ,IAAI,oCAAoC,QAAQ,oBAAoB,SAAS,eAAe,gBAAgB,WAAW,2GAA2G,qCAAqC,EAAE,kDAAkD,4BAA4B,sDAAsD,+BAA+B,oDAAoD,oIAAuI,8CAA8C,2BAA2B,0DAA0D,EAAE,MAAmzC,WAAnzC,EAAmzC,WAAvyC,8DAA8D,6CAA6C,+CAA+C,IAAI,2BAA2B,QAAQ,YAAY,oDAAqD,sEAAqE,SAAS,GAAG,SAAS,QAAQ,IAAI,oCAAoC,QAAQ,qBAAqB,2DAA2D,wDAAuD,+DAA+D,IAAI,2BAA2B,QAAQ,YAAY,cAAc,iCAAiC,SAAS,GAAG,SAAS,QAAQ,IAAI,oCAAoC,QAAQ,oBAAoB,SAAS,yBAAyB,iCAAqC,GAAY,IAAZ,MAAY,CAAE,yCAAyC,eAAe,gCAA3G,EAA2I,WAAa,KAAxJ,EAAwJ,WAAkB,WAAW,OAArL,CAAqL,CAAS,sCAAsC,QAAQ,IAAI,kCAAkC,QAAQ,YAAY,+CAA+C,oBAAoB,SAAS,GAAG,SAAS,QAAQ,IAAI,oCAAoC,QAAQ,oBAAoB,SAAS,GAAG,GAAG,GAA8C,WAAkB,CAAjD,GAAiD,gBAAoB,kCAAiC,cAAa,gCAAgC,cAAc,gBAAgB,uEAAsE,OAAS,qBAAqB,oHAA+H,aAAa,mBAApB,OAAuC,uEAAuE,oBAAoB,oDAAoD,eAAe,yBAAyB,IAAI,KAAK,yCAAyC,iBAAiB,SAAS,GAAG,SAAS,QAAQ,IAAI,oCAAoC,QAAQ,oBAAoB,SAAS,gBAAwB,oBAAoB,2BAA2B,uGAAuG,OAA9J,QAA8J,iCAAwC,mBAAmB,6CAA4C,4BAA4B,2CAA2C,8CAA8C,mCAAmC,yCAAyC,kFAAkF,GAAG,KAAS,iBAAiB,SAAS;CACpllI;CACA;CACA;CACA,IAAK,SAEiH,QAFhG,IAA2S,EAAiL,EAE3W,iCAFjH,8GAAoH,EAEH,cAFoB,gBAEpB,EAFoB,kFAA0G,EAE9H,eAF0L,iBAA3C,CAAO,EAAyD,GAAzD,+BAAoC,EAAqB,0FAAgG,CAE/S,gBAFgU,EAEhU,aAF2W,iBAA3C,CAAO,EAAyD,GAAzD,+BAAoC,EAAqB;AACvgB;AACA,0EAAuI,EAAvI,2CAAuI,GAN+6kI,KAM7ykI,mBAAmB,+DAA+D,sBAAsB,uCAAuC,gBAAgB,mCAAmC,0BAA2B,gFAAmF,eAAe,aAAa,gEAAgE,2BAA2B,aAAa,qCAAqC,iBAAiB,IAAI,uDAAuD,QAAQ,YAAY,cAAc,eAAe,SAAS,GAAG,SAAS,QAAQ,IAAI,oCAAoC,QAAQ,oBAAoB,wCAAwC,eAAe,oCAAoC,QAAQ,YAAY,cAAc,eAAe,SAAS,GAAG,SAAS,QAAQ,IAAI,oCAAoC,QAAQ,oBAAoB,SAAS,kCAAkC,8CAA8C,gDAAgD,uCAAwC,qFAAoF,uDAAuD,uCAAuC,gEAAgE,kEAAkE,wCAAwC,0BAA0B,oBAAoB,gEAAgE,4CAA4C,kDAAkD,0BAAe,kBAAkB,EAAO,QAAU,0DAA0D,eAAe,IAAI,2BAA2B,QAAQ,YAAY,2CAA4C,EAN0vnH,kBAAx5B,IAAy6B,gBAAz6B,EAAy6B,CAAx5B,CAAw5B,KAAx5B,mCAAw5B,6DAM3wnH,KAGhmE;AACA;AACA;AACA,WANgmE;AAChmE;AACA;AACA,wCAGA,EAAY,SAAS,GAAG,SAAS,QAAQ,IAAI,oCAAoC,QAAQ,oBAAoB,SAAS,GAAG,GAAG,cAAkB,cAAc,wBAAwB,4CAA4C,0CAA0C,kBAAkB,EAAE,GAAG,GAAG,KAAI,KAAI,kBAAkB,gBAAgB,6DAA4D,yCAAyC,qBAAoB,gCAAsB,gFAAuF,CAAP,KAAO,mPAAwP,GAAG,GAAG,cAAkB,aAAa,qBAAqB,2CAA2C,qBAAoB,QAAW,0BAA0B,4EAAgE,+EAA8E,GAAO,iPAAoP,GAAG,GAAG,qBAAyB,OAA+D,kCAAyC,cAAc,IAAI,aAAa,SAAS,MAAM,cAAc,IAAI,cAAc,SAAS,MAAM,kBAArM,EAAmN,kBAArM,EAAd,EAAmN,CAArM,CAAqM,iBAArM,sBAA0C,MAAK,EAAsJ,UAAuC,+BAA+B,EAAE,kBAAkB,IAAgF,QAAhF,GAAO,wBAAwB,qBAAqB,YAAY,gBAAgB,CAAS,UAAU,iCAAiC,2DAA2D,aAAY,GAAI,cAAc,mBAAmB,IAAiB,EAAjB,MAA+B,wDAA4D,KAAK,EAAE,KAAK,kHAAkH,yCAAyC,kBAAkB,KAAM,0BAAyB,mBAAoB,+BAA8B,QAAS,mCAAkC,QAAS,2EAA0E,IAAI,SAAS,yCAAyC,aAAa,MAAM,2BAA2B,iBAAiB,MAAM,oBAAoB,2BAA2B,MAAM,+BAA+B,SAAS,cAAc,SAAS,YAAY,QAAQ,MAAM,qBAAqB,OAAO,+BAAxwB,EAAiB,CAAwxB,kBAAkB,oDAAoD,eAAe,yBAAyB,IAAI,KAAK,yCAAyC,iBAAiB,SAAS,GAAG,SAAS,QAAQ,IAAI,oCAAoC,QAAQ,oBAAoB,SAAS,oBAAoB,mDAAoD,IAAI,qEAAyE,kDAAkD,eAAe,aAAa,2CAA2C,6CAA6C,yBAA8B,yBAA4B,wBAAwB,EAAE,0CAA0C,8BAA8B,iCAAiC,4CAA4C,4BAA4B,0BAAe,mFAA6H,CAA1C,IAAO,0BAAmC,8BAAiC,yBAAyB,EAAE,+CAA+C,2DAA2D,sCAAsC,mCAAmC,wCAAwC,QAAY,2BAA2B,gBAAgB,2FAZ0xgH,GAAe,wCAAwC,WAAW,2BAA2B,uCAAuC,qCAAqC,OAAQ,oBAA2B,gBAAgB,+CAA0C,YAA6B,yBAA7B,QAA6B,CAA2B,OAAE,YAA6B,yBAA7B,QAA6B,CAA2B,OAAE,aAAkB,EAAE,EAAE,IAAI,EAAE,GAYhrhH,8BAAoH,eAAwE,EAAxE,oCAAwD,gBAAgB,IAAS,EAAE,EAAE,6CAA6C,WAAW,uCAAuC,gCAAgC,qCAAqC,QAAQ,2BAA2B,gBAAgB,yHAAsH,wDAA6D,sBAAsB,OAAO,EAAE,EAAE,EAAE,kDAAkD,WAAW,4CAA4C,iCAAiC,qCAAqC,QAAQ,2BAA2B,gBAAgB,6EAA0E,wCAA6C,sBAAuB,gDAA+C,cAAc,EAAE,OAAO,EAAE,EAAE,EAAE,yCAAyC,6CAA6C,wCAAwC,EAAE,8CAA8C,kDAAkD,uBAZ48+G,cAAiB,QAAQ,4BAA4B,IAAI,2BAA2B,QAAQ,YAAY,cAAc,uBAAuB,SAAS,GAAG,SAAS,QAAQ,IAAI,oCAAoC,QAAQ,oBAAoB,SAAS,CAYns/G,iBAA2C,EAAE,GAAG,GAAG,cAAkB,oBAAoB,kCAAiC,cAAa,gCAAgC,cAAc,gBAAgB,uEAAsE,OAAS,qBAAqB,oHAA+H,aAAa,mBAApB,OAAuC,uEAAuE,kBAA0B,oBAAoB,2BAA2B,uGAAuG,UAA9J,KAA8J,qCAA4C,kFAAkF,mCAAmC,yCAAyC,kFAAkF,GAAG,KAAK,cAAkB,oBAAoB,kCAAiC,yBAAa,qBAAgC,cAAc,gBAAgB,uEAAsE,OAAS,qBAAqB,oHAA+H,aAAa,mBAApB,OAAuC,uEAAuE,iBAAiB,cAAc,yBAAyB,UAAU,GAAG,GAAG,eAA2B,aAAa,+CAA+C,UAApE,KAAoE,kCAAyC,SAAS,GAAG,KAAK,aAA2B,cAAc,yBAAyB,oCAAoC,GAAnF,KAAmF,kCAAyC,gBAAgB,yCAAyC,4CAA4C,sBAAsB,iBAAiB,IAAI,CAAG,KAAK,cAAc,GAAoT,cAAkB,IAAgF,QAAhF,GAAO,wBAAwB,qBAAqB,YAAY,gBAAgB,CAAS,UAAU,iCAAiC,2DAA2D,aAAY,GAAI,cAAc,mBAAmB,IAAiB,EAAjB,MAA+B,wDAA4D,KAAK,EAAE,KAAK,kHAAkH,yCAAyC,kBAAkB,KAAM,0BAAyB,mBAAoB,+BAA8B,QAAS,mCAAkC,QAAS,2EAA0E,IAAI,SAAS,yCAAyC,aAAa,MAAM,2BAA2B,iBAAiB,MAAM,oBAAoB,2BAA2B,MAAM,+BAA+B,SAAS,cAAc,SAAS,YAAY,QAAQ,MAAM,qBAAqB,OAAO,+BAAxwB,EAAiB,CAAwxB,kBAAkB,oDAAoD,eAAe,yBAAyB,IAAI,KAAK,yCAAyC,iBAAiB,SAAS,GAAG,SAAS,QAAQ,IAAI,oCAAoC,QAAQ,oBAAoB,SAAS,eAAe,gBAAgB,uJAAuJ,qDAAqD,wCAAwC,mCAAmC,oDAAoD,wCAAwC,CAAS,yCAArhE,QAA0jE,OAA1jE,EAA0jE,KAA1jE,EAA0jE,OAA1jE,EAA0jE,OAA1jE,EAA0jE,CAAjiE,UAAykE,UAAU,2BAA2B,gBAAgB,0EAAqE,+GAAgH,sBAAsB,yBAA8B,SAAS,MAAM,cAAc,2CAA2C,UAAU,GAAI,EAAE,CAAh4E,kCAAyC,cAAc,IAAI,aAAa,SAAS,MAAM,cAAc,IAAI,cAAc,SAAS,MAAM,kBAArM,EAAmN,CAArM,CAAqM,gBAArM,EAAd,EAAmN,mBAArM,sBAA0C,MAAK,EAAE,IAAoJ,MAAuC,+BAA+B,CAAsqE,CAApqE,CAAsqE,iDAAkD,iHAAqH,IAZk13G,IAAQ,GAAO,cAYj23G,EAZi23G,QAYj23G,EAZi23G,8DAYj23G,EAZi23G,UAYj23G,EAZi23G,eAYj23G,EAZi23G,iBAYj23G,EAZi23G,QAYj23G,2DAAyE,oBAAoB,wHAAwH,6CAA6C,EAAE,iBAAiB,gFAAoF,kGAAkG,oBAAoB,uDAAuD,2DAA2D,EAAE,cAAc,SAAS,GAAG,GAAG,eAAmB,+DAA+D,sBAAsB,uCAAuC,gBAAgB,mCAAmC,0BAA2B,gFAAmF,eAAe,cAAc,iGAAiG,mDAAmD,IAZu5xG,IAAQ,EAY/5xG,EAZ+5xG,qEAY/5xG,gCAA4C,qEAAqE,4CAA4C,aAAa,IAAI,+CAA+C,QAAQ,YAAY,cAAc,oCAAoC,SAAS,GAAG,SAAS,QAAQ,IAAI,oCAAoC,QAAQ,oBAAoB,SAAS,GAAG,GAAG,iCAAmC,OAA+D,kCAAyC,cAAc,IAAI,aAAa,SAAS,MAAM,cAAc,IAAI,cAAc,SAAS,MAAM,kBAArM,EAAmN,kBAArM,EAAd,EAAmN,CAArM,CAAqM,iBAArM,sBAA0C,MAAK,EAAsJ,UAAuC,+BAA+B,EAAE,kBAAkB,IAAgF,QAAhF,GAAO,wBAAwB,qBAAqB,YAAY,gBAAgB,CAAS,UAAU,iCAAiC,2DAA2D,aAAY,GAAI,cAAc,mBAAmB,IAAiB,EAAjB,MAA+B,wDAA4D,KAAK,EAAE,KAAK,kHAAkH,yCAAyC,kBAAkB,KAAM,0BAAyB,mBAAoB,+BAA8B,QAAS,mCAAkC,QAAS,2EAA0E,IAAI,SAAS,yCAAyC,aAAa,MAAM,2BAA2B,iBAAiB,MAAM,oBAAoB,2BAA2B,MAAM,+BAA+B,SAAS,cAAc,SAAS,YAAY,QAAQ,MAAM,qBAAqB,OAAO,+BAAxwB,EAAiB,CAAwxB,kBAAkB,oDAAoD,eAAe,yBAAyB,IAAI,KAAK,yCAAyC,iBAAiB,SAAS,GAAG,SAAS,QAAQ,IAAI,oCAAoC,QAAQ,oBAAoB,SAAS,oBAAoB,mDAAoD,IAAI,qEAAyE,kDAAkD,eAAe,gBAAgB,yCAAyC,uCAAuC,wCAAwC,iBAAmB,2BAA2B,gBAAgB,4DAA0Y,eAA1Y,yEAAuI,qCAAqC,MAAM,2BAA2B,gBAAgB,0CAAqC,+HAAoI,EAAE,EAAE,GAAqB,4BAA2B,iBAAiB,mDAAmD,UAAU,GAAG,EAAE,EAAE,oCAAoC,wCAAwC,2BAA2B,gBAAgB,iDAAkD,6BAA4B,EAAE,EAAE,kCAAkC,wCAAwC,2BAA2B,gBAAgB,+CAAgD,6BAA4B,EAAE,EAAE,sDAAsD,0DAA0D,2CAA2C,+CAA+C,GAAG,GAAG,qBAAwF,yCAAyC,cAAc,IAAI,aAAa,SAAS,MAAM,cAAc,IAAI,cAAc,SAAS,MAAM,kBAArM,EAAmN,kBAArM,EAAd,EAAmN,CAArM,CAAqM,iBAArM,sBAA0C,MAAK,EAAsJ,UAAuC,+BAA+B,EAAE,kBAAkB,IAAgF,QAAhF,GAAO,wBAAwB,qBAAqB,YAAY,gBAAgB,CAAS,UAAU,iCAAiC,2DAA2D,aAAY,GAAI,cAAc,mBAAmB,IAAiB,EAAjB,MAA+B,wDAA4D,KAAK,EAAE,KAAK,kHAAkH,yCAAyC,kBAAkB,KAAM,0BAAyB,mBAAoB,+BAA8B,QAAS,mCAAkC,QAAS,2EAA0E,IAAI,SAAS,yCAAyC,aAAa,MAAM,2BAA2B,iBAAiB,MAAM,oBAAoB,2BAA2B,MAAM,+BAA+B,SAAS,cAAc,SAAS,YAAY,QAAQ,MAAM,qBAAqB,OAAO,+BAAxwB,EAAwwB,CAAiC,gBAAgB,+DAA+D,sBAAsB,uCAAuC,gBAAgB,mCAAmC,0BAA2B,gFAAmF,eAAe,cAAc,IAAU,cAAkB,IAA5B,MAA4B,yDAAwE,oEAAoE,iCAAiC,QAAQ,YAAY,cAAc,2CAA2C,SAAS,GAAG,SAAS,QAAQ,IAAI,oCAAoC,QAAQ,qBAAqB,4CAA4C,4CAA2C,kJAAiJ,uCAAuC,QAAQ,yCAAyC,kCAAkC,kEAAkE,kCAAkC,wCAAwC,2BAA2B,gBAAgB,2LAA0L,qBAAqB,IAAK,6BAA4B,EAAE,EAAE,oCAAoC,wCAAwC,2BAA2B,gBAAgB,iLAAgL,uBAAuB,SAAI,uBAA4B,EAAE,EAAE,GAAG,GAAG,0CAA0C,oFAA8O,KAAI,6CAA6C,cAAc,6BAA6B,eAAe,yEAAyE,IAA4W,IAAQ,QAAQ,GAAS,UAAU,2BAAsM,eAAe,mCAAnB,KAAsD,aAAa,iBAAiB,kBAAkB,6BAA6B,yBAAyB,yBAAyB,aAAa,mEAAmE,oDAAmD,eAAe,EAAE,WAAW,mEAAmE,eAAe,EAAE,aAAa,IAAI,gCAAgC,6DAA6D,6BAA6B,qBAAqB,kDAAkD,aAAvpC,uBAApD,EAA0E,aAAa,iBAA0V,KAAiB,uCAAuC,SAAS,kCAAkC,8BAA+B,sBAAqB,UAAjf,oBAA+B,WAAW,oBAAniB,kBAAmiB,6EAAlH,CAAf,EAAiI,CAAlH,CAAkH,wBAAlH,4BAAkH,sGAAyO,GAAi2B,8BAA8C,+CAA+C,4BAA4B,sBAAsB,0BAA0B,GAAI,uCAAsC,kBAAkB,qCAAqC,KAAK,uBAAuB,yBAAyB,EAAE,4CAA4C,SAAS,IAAI,gCAAgC,uEAAuE,MAAM,eAA0lB,GAAe,IAAI,oBAAoB,IAAI,qGAAqG,GAAG,YAAY,0DAA0D,GAAG,EAAE,EAAE,KAAK,oFAAmF,kDAAkD,2CAA2C,0BAA0B,cAAc,UAAU,qCAAqC,EAAE,oBAAoB,OAAO,2DAAnmC,GAAY,iCAAiC,MAAM,8CAA+C,iEAAgE,QAAQ,SAAS,kDAAgD,qBAAsB,gBAAgB,4DAA4D,wCAAwC,MAAM,8BAA8B,sCAAsC,KAAK,mKAAguB,iBAAiB,eAAe,+BAA+B,8CAA8C,EAAE,GAAG,EAAE,IAAI,kBAAiB,cAAiB,KAAI,yCAAyC,gBAAgB,EAAE,8CAA8C,aAAa,+CAA8C,SAAS,2CAA0C,YAAY,2CAAgC,kFAAiF,IAAO,wBAA4B,IAAI,EAAE,+BAA+B,oBAAoB,SAAS,8CAA6C,OAAO,sBAAsB,8BAA8B,2FAA2F,OAAO,aAAa,WAAW,oGAAmG,EAAE,YAAY,mDAAkD,eAAe,uCAAuC,kCAAkC,EAAE,8BAA8B,iBAAiB,yCAAiG,eAAe,mCAAnE,yBAA+C,GAAG,KAAiB,CAAuC,aAAa,gBAAgB,EAAE,oBAAoB,YAAY,sBAAsB,WAAW,4BAA4B,aAAa,+BAA+B,qBAAqB,WAAW,gDAAgD,yBAAyB,EAAE,aAAa,OAAO,uDAAuD,iBAAiB,eAAe,cAAiB,cAAiB,SAAi9B,MAAl8B,EAAm9B,+DAAyG,gBAAiB,+OAAuZ,cAAiB,+CAA+C,kBAAkB,gDAAiD,WAA1hB,iEAAmiB,cAAiB,+CAA+C,kBAAkB,iDAAiD,WAAtqB,qGAA1H,wCAA+9F,aAAa,gBAAgB,EAAE,oBAAoB,YAAY,sBAAsB,WAAW,4BAA4B,aAAa,+BAA+B,qBAAqB,WAAW,uDAAuD,iBAAjrI,EAA8rI,OAAO,MAAvpI,cAAiB,6DAA6D,yBAAyB,cAAiE,KAAiB,sBAA6P,cAAiB,uDAAuD,sBAAsB,qDAAqD,yFAAhZ,6FAAqH,sBAAsB,cAA4V,KAAiB,mBAA6hC,cAAiB,iJAAiJ,sBAAsB,qDAAqD,yFAA1wC,gFAAqG,iBAAiB,KAA2uC,kBAAqjC,IAApiC,2dAA2d,sBAAsB,qDAAqD,6HAA6H,kBAAkB,KAAke,cAAiB,2JAA2J,sBAAsB,qDAAqD,yFAAztB,0CAAiD,mHAAmH,iBAAiB,KAA2nB,cAAiB,yMAAyM,sBAAsB,qDAAqD,yFAAh6B,0CAAiD,6FAAyI,EAAzI,SAAyI,EAAzI,CAA0J,CAA1J,mBAA0J,4FAA1J,YAAyI,CAAhyE,yCAAgD,+DAAnhB,8CAAqD,+DAAlR,gDAAwD,EAAw/H,EAArsI,gBAAoB,aAAirI,8CAAyD,kCAAkC,iBAAiB,eAAe,IAAI,CAA6G,aAAa,GAAzG,SAAyG,IAAgB,EAAE,8CAA8C,oCAAoC,cAAc,YAAY,mBAAmB,aAAa,qBAAqB,sBAAsB,oBAAoB,kBAAkB,iCAAiC,mEAAmE,yBAAyB,mBAAmB,wEAA0E,qBAAqB,0OAA0O,8EAA8E,mBAAmB,iSAAmS,WAAW,iEAAiE,EAAE,IAAyvH,MAAe,EAAxwH,sCAA0C,gBAA7hV,WAA6hV,yBAA7hV,cAA6hV,2BAA7hV,gBAA6hV,WAA7hV,gBAA6hV,OAAiG,KAA94C,aAAoB,aAAa,KAAK,WAAW,WAAW,8BAA8B,EAAs8J,MAArB,GAAf,EAA9mH,CAA6nH,CAA7nH,YAA6nH,aAAqB,oBAAlpH,QAAsC,GAAG,EAAE,GAAG,EAAE,aAAa,wBAAuB,sBAAsB,oCAAmC,yBAAyB,EAAE,GAAG,EAAE,+CAA+C,IAAI,oBAAoB,IAAI,iBAAiB,MAAM,aAAa,yEAAyE,+DAA+D,gDAAgD,UAAU,GAAG,kLAA2rH,SAAiY,EAAhX,iIAAqI,oBAAoB,8BAA8B,yCAAyC,eAAe,wBAAwB,OAAO,WAAW,8BAA8B,qBAAqB,iBAA0C,GAAzB,mBAAyB,CAAN,IAAM,eAAuB,6BAA6B,iCAAmC,2BAA2B,IAAoC,UAApC,eAAuB,EAAE,IAAI,EAAE,GAAG,EAAE,GAAiB,WAAW,wBAAwB,8FAA8F,UAA12I,wDAAoO,sBAAsB,uDAAuD,EAAE,0FAA0F,4BAA4B,kDAAkD,qCAAoC,KAAK,uBAAuB,KAAK,IAAI,yCAA0C,wCAAwC,mBAAqB,iIAAkI,aAAa,iIAAiI,qBAAuB,0BAA2B,6BAA4B,mBAAyB,6BAA4B,EAAK,6BAA4B,uHAAwH,EAAE,mBAAmB,mCAAmC,oBAAoB,yBAAyB,IAAI,SAAU,0BAA4B,0BAA0B,kBAAkB,IAAI,iBAAiB,MAAM,aAAa,yEAAyE,+DAA+D,YAAa,0CAAyC,uBAAuB,qBAAqB,mBAAmB,MAAQ,0BAAR,GAA2C,mDAA3C,GAA2C,iBAAuE,uBAAlH,GAAkH,WAAlH,GAA0J,sBAAsB,sDAAhL,GAAgL,mCAAhL,GAAgL,0BAAyH,EAAE,wEAA3S,GAAsX,0BAAtX,GAAsX,CAA8B,kDAAgD,sCAAqC,wBAAwB,IAAI,gBAAiB,CAAthB,GAAshB,mEAAsE,iBAA5lB,IAA6mB,mBAAuB,iBAAiB,iIAAiI,UAAU,GAAG,aAAa,YAA2wB,YAAe,sCAAsC,gCAAgC,0BAA0B,eAAe,IAAI,iCAAiC,EAAE,yBAAh7B,YAA0B,gDAAgD,KAAK,mCAAmC,YAAY,SAAS,0BAA0B,mBAAmB,SAAS,sCAAsC,IAAI,yCAAyC,+DAA+D,QAAQ,UAAU,2DAA2D,KAAK,WAAW,YAAY,KAAK,SAAS,eAAe,wEAAwE,SAAS,SAAS,yBAAyB,UAAU,KAAK,KAAK,WAAW,sBAAsB,SAAK,MAAS,oBAA0B,QAAS,gBAAwB,SAAiQ,QAAiB,wDAAwD,+CAA8C,EAAE,KAAK,gBAAgB,aAAa,uCAAsC,GAAG,qBAAqB,kCAAkC,gBAAiB,kCAAytB,KAAI,4DAAkE,SAAS,cAAc,cAAc,iCAAgC,2EAA0E,SAAS,GAAG,EAAG,QAAU,GAAG,SAAS,IAAI,qCAAqC,EAAE,iEAAiE,eAAe,kBAAkB,eAAe,8BAA8B,+BAA+B,eAA4M,GAAe,4BAA4B,6FAAkG,8CAAlG,IAAkG,EAAzV,GAAY,cAAe,eAAc,kBAAkB,MAAM,oCAAqC,mEAAiE,sCAAgO,KAAI,aAAa,SAAS,SAAS,UAAU,WAAW,WAAW,8FAA6F,IAAI,kBAAkB,aAAa,8HAA4H,iEAA+D,MAAO,KAAI,kBAAgB,2BAA6B,eAAe,oCAAoC,SAAkH,MAAe,IAAjI,EAA6I,CAA9H,KAA8E,CAAO,IAArF,yBAAf,EAAiI,QAA7B,KAArF,wCAAqF,CAAP,CAA4G,yBAA/C,YAAe,OAAY,GAAZ,GAAY,YAAoB,uBAAmD,eAAe,SAAS,eAAe,0CAA0C,QAAQ,+EAA0T,iBAAqB,oDAAoD,eAAe,yBAAyB,IAAI,KAAK,yCAAyC,iBAAiB,SAAS,GAAG,SAAS,QAAQ,IAAI,oCAAoC,QAAQ,oBAAoB,UAAU,SAAS,GAAE,GAAI,CAAJ,MAAI,+BAAsC,gBAAoB,OAAiB,OAAO,qBAAmB,KAAe,eAAe,oBAAqB,cAAc,mCAAmC,WAAW,EAAE,cAAc,gBAAgB,YAAY,0BAA0B,aAAa,mBAAmB,YAAY,iBAAkB,wBAAwB,aAAa,yCAAyC,cAA4B,SAA5B,UAA4B,CAAe,GAAG,KAAlY,KAA5C,QAAkB,EAAqhD,mBAAmB,+DAA+D,sBAAsB,uCAAuC,gBAAgB,mCAAmC,0BAA2B,gFAAmF,kBAAkB,oDAAoD,eAAe,yBAAyB,IAAI,KAAK,yCAAyC,iBAAiB,SAAS,GAAG,SAAS,QAAQ,IAAI,oCAAoC,QAAQ,oBAAoB,UAAU,GAA08B,MAAa,YAAY,WAAW,kBAAkB,4DAA2D,8CAA8C,EAAE,OAAO,QAAlnC,EAAsnC,OAAtnC,EAAsnC,CAArmC,OAAinC,kBAAyB,CAA9nC,CAAO,cAAic,cAAiB,mBAA3b,GAAe,kBAAkB,IAAI,2BAA2B,QAAQ,YAAY,kCAAkC,mCAAmC,uIAAuI,gCAAgC,SAAS,GAAG,SAAS,QAAQ,IAAI,oCAAoC,QAAQ,oBAAoB,UAA0B,iCAA8C,QAAQ,EAAE,mEAAmE,QAAQ,EAAE,cAAc,eAAe,yFAAyF,gBAAl7E,KAAiB,mCAAmC,OAAO,wUAAqV,GAAE,kGAA2jB,IAA5d,OAA4d,EAA5d,EAA4d,EAA5d,CAA6e,EAAO,wBAAwB,GAAE,oHAA9gB,CAAe,kDAAkD,8BAA8B,+BAA+B,gBAAwD,KAAiB,MAAM,OAAO,wBAAwB,GAAE,gOAA8O,CAA9V,KAAe,0CAAozD,KAAe,EAAE,QAAQ,OAAO,iBAAiB,qBAAqB,EAAE,WAAW,OAAQ,UAAU,WAAW,GAAE,uCAAwC,+BAAgC,sBAAsB,SAAS,CAA4K,EAArmC,SAAnsF,GAAe,GAAQ,qBAAwB,IAAhC,IAAgC,qDAAuF,OAAO,8FAA8F,CAAw9E,GAAmB,GAAynC,8BAA8B,8CAA8C,EAAE,SAAS,GAAG,4EAAgF,GAAG,WAAW,yBAAyB,aAAa,2BAA2B,IAAQ,8LAAuL,UAAU,gBAAgB,EAAE,qBAAqB,YAAuuF,MAAk+D,MAAzuE,IAAx9E,IAAs4E,EAA4W,EAAlvF,GAAs4E,uCAA2C,CAAO,gCAAgC,CAAx9E,uBAAkC,MAAwB,CAAU,CAAR,CAAQ,sBAAyB,2EAAyE,sDAAsD,WAAW,uFAAuF,iCAAiC,6DAArprB,eAAitrB,8gBAAjtrB,kBAAitrB,iDAAykB,+BAA+B,6BAA8B,CAAoB,aAApB,CAAO,cAAa,EAAiB,OAAypD,EAAzpD,cAAypD,EAAzpD,EAAypD,EAAzpD,CAA4qD,2GAA8G,yBAA+B,uBAAuB,eAAe,SAAS,eAAe,sCAAsC,UAAU,sDAAsD,UAAU,wCAAwC,wDAAuD,4BAA4B,0BAA0B,sGAAqG,iHAAgH,0DAA8D,EAAE,IAAI,SAAS,SAAS,CAAv6E,EAA+7E,cAAiB,kCAAkC,gDAAgD,+DAA8D,6BAA6B,+BAA+B,4CAA2C,YAAW,CAAE,6CAA4C,YAAY,CAAE,wCAAwC,+CAA8C,mBAAmB,CAAE,0EAAwE,+BAA+B,yBAAyB,GAAG,cAA5/F,qBAA2nH,EAA3nH,iBAA2nH,EAA3nH,EAA2nH,EAA3nH,CAA8oH,+BAAoC,eAAe,uBAAuB,sCAAsC,mHAAmH,6EAA4E,OAAO,wBAAwB,EAAE,uBAAuB,2CAA2C,kBAAkB,qIAAuP,YAAe,0HAAupB,GAAe,2CAA2C,kCAAkC,oCAAoC,gCAAgC,EAAE,GAAG,GAAG,MAA/zB,MAA0H,0JAA0J,gFAA+E,mCAAmC,MAAM,+BAA+B,gBAAgB,CAAE,oCAAmC,gBAAgB,CAAE,gFAA8E,EAAE,kCAAx0B,KAA6I,SAAS,SAAS,wFAAnoI,uGAAqG,cAA6B,8CAA7B,aAA6E,EAAE,qEAAqE,wDAAwD,cAAc,EAAE,8CAA8C,cAAc,WAAW,EAAE,uGAAuG,4BAA4B,cAAc,6BAA6B,EAAE,4GAA2G,OAA+pB,EAA/pB,mBAA+pB,EAA/pB,CAAgrB,CAAhrB,sBAAgrB,kPAAsP,CAAj3B,8DAA8D,mBAAmB,4CAA2C,iBAAiB,aAAS,kOAAqO,MAAwB,0EAA0E,IAAI,0BAA0B,MAAM,SAAS,GAAqnC,WAAvmC,CAAwsD,eAAe,4JAA2J,GAA3wB,EAAqxB,yFAAwF,wCAAwC,wEAAuE,2BAA2B,8CAA8C,CAAG,sEAAwE,2BAA2B,4DAA4D,CAAG,EAA1sC,CAA0sC,IAAU,IAAgyC,2CAA+C,GAAG,EAA+K,eAAe,eAAe,SCbv06CA,SDau06C,MAAe,EAAM,IAA+C,IAApE,ECZx06C,CDYu16C,YCZx06C,YAAa,EDYi06C,EAAsC,CAAd,GAAuB,OCX356C,CDWqi7C,GAAnJ", "sources": ["webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/../../node_modules/@opentelemetry/api/build/esm/platform/browser/globalThis.js", "webpack://_N_E/../../node_modules/@opentelemetry/api/build/esm/version.js", "webpack://_N_E/../../node_modules/@opentelemetry/api/build/esm/internal/semver.js", "webpack://_N_E/../../node_modules/@opentelemetry/api/build/esm/internal/global-utils.js", "webpack://_N_E/../../node_modules/@opentelemetry/api/build/esm/diag/ComponentLogger.js", "webpack://_N_E/../../node_modules/@opentelemetry/api/build/esm/diag/types.js", "webpack://_N_E/../../node_modules/@opentelemetry/api/build/esm/diag/internal/logLevelLogger.js", "webpack://_N_E/../../node_modules/@opentelemetry/api/build/esm/api/diag.js", "webpack://_N_E/../../node_modules/@opentelemetry/api/build/esm/baggage/internal/baggage-impl.js", "webpack://_N_E/../../node_modules/@opentelemetry/api/build/esm/baggage/internal/symbol.js", "webpack://_N_E/../../node_modules/@opentelemetry/api/build/esm/baggage/utils.js", "webpack://_N_E/../../node_modules/@opentelemetry/api/build/esm/context/context.js", "webpack://_N_E/../../node_modules/@opentelemetry/api/build/esm/diag/consoleLogger.js", "webpack://_N_E/../../node_modules/@opentelemetry/api/build/esm/metrics/NoopMeter.js", "webpack://_N_E/../../node_modules/@opentelemetry/api/build/esm/metrics/Metric.js", "webpack://_N_E/../../node_modules/@opentelemetry/api/build/esm/propagation/TextMapPropagator.js", "webpack://_N_E/../../node_modules/@opentelemetry/api/build/esm/context/NoopContextManager.js", "webpack://_N_E/../../node_modules/@opentelemetry/api/build/esm/api/context.js", "webpack://_N_E/../../node_modules/@opentelemetry/api/build/esm/trace/trace_flags.js", "webpack://_N_E/../../node_modules/@opentelemetry/api/build/esm/trace/invalid-span-constants.js", "webpack://_N_E/../../node_modules/@opentelemetry/api/build/esm/trace/NonRecordingSpan.js", "webpack://_N_E/../../node_modules/@opentelemetry/api/build/esm/trace/context-utils.js", "webpack://_N_E/../../node_modules/@opentelemetry/api/build/esm/trace/spancontext-utils.js", "webpack://_N_E/../../node_modules/@opentelemetry/api/build/esm/trace/NoopTracer.js", "webpack://_N_E/../../node_modules/@opentelemetry/api/build/esm/trace/ProxyTracer.js", "webpack://_N_E/../../node_modules/@opentelemetry/api/build/esm/trace/NoopTracerProvider.js", "webpack://_N_E/../../node_modules/@opentelemetry/api/build/esm/trace/ProxyTracerProvider.js", "webpack://_N_E/../../node_modules/@opentelemetry/api/build/esm/trace/SamplingResult.js", "webpack://_N_E/../../node_modules/@opentelemetry/api/build/esm/trace/span_kind.js", "webpack://_N_E/../../node_modules/@opentelemetry/api/build/esm/trace/status.js", "webpack://_N_E/../../node_modules/@opentelemetry/api/build/esm/trace/internal/tracestate-validators.js", "webpack://_N_E/../../node_modules/@opentelemetry/api/build/esm/trace/internal/tracestate-impl.js", "webpack://_N_E/../../node_modules/@opentelemetry/api/build/esm/trace/internal/utils.js", "webpack://_N_E/../../node_modules/@opentelemetry/api/build/esm/context-api.js", "webpack://_N_E/../../node_modules/@opentelemetry/api/build/esm/diag-api.js", "webpack://_N_E/../../node_modules/@opentelemetry/api/build/esm/metrics/NoopMeterProvider.js", "webpack://_N_E/../../node_modules/@opentelemetry/api/build/esm/api/metrics.js", "webpack://_N_E/../../node_modules/@opentelemetry/api/build/esm/metrics-api.js", "webpack://_N_E/../../node_modules/@opentelemetry/api/build/esm/propagation/NoopTextMapPropagator.js", "webpack://_N_E/../../node_modules/@opentelemetry/api/build/esm/baggage/context-helpers.js", "webpack://_N_E/../../node_modules/@opentelemetry/api/build/esm/api/propagation.js", "webpack://_N_E/../../node_modules/@opentelemetry/api/build/esm/propagation-api.js", "webpack://_N_E/../../node_modules/@opentelemetry/api/build/esm/api/trace.js", "webpack://_N_E/../../node_modules/@opentelemetry/api/build/esm/trace-api.js", "webpack://_N_E/../../node_modules/@opentelemetry/api/build/esm/index.js", "webpack://_N_E/external commonjs \"node:events\"", "webpack://_N_E/../../node_modules/@opentelemetry/api-logs/build/esm/types/LogRecord.js", "webpack://_N_E/../../node_modules/@opentelemetry/api-logs/build/esm/NoopLogger.js", "webpack://_N_E/../../node_modules/@opentelemetry/api-logs/build/esm/NoopLoggerProvider.js", "webpack://_N_E/../../node_modules/@opentelemetry/api-logs/build/esm/ProxyLogger.js", "webpack://_N_E/../../node_modules/@opentelemetry/api-logs/build/esm/ProxyLoggerProvider.js", "webpack://_N_E/../../node_modules/@opentelemetry/api-logs/build/esm/platform/browser/globalThis.js", "webpack://_N_E/../../node_modules/@opentelemetry/api-logs/build/esm/internal/global-utils.js", "webpack://_N_E/../../node_modules/@opentelemetry/api-logs/build/esm/api/logs.js", "webpack://_N_E/../../node_modules/@opentelemetry/api-logs/build/esm/index.js", "webpack://_N_E/../../node_modules/@vercel/otel/dist/edge/index.js", "webpack://_N_E/./instrumentation.ts"], "sourcesContent": ["module.exports = require(\"node:async_hooks\");", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// Updates to this file should also be replicated to @opentelemetry/core too.\n/**\n * - globalThis (New standard)\n * - self (Will return the current window instance for supported browsers)\n * - window (fallback for older browser implementations)\n * - global (NodeJS implementation)\n * - <object> (When all else fails)\n */\n/** only globals that common to node and browsers are allowed */\n// eslint-disable-next-line node/no-unsupported-features/es-builtins, no-undef\nexport var _globalThis = typeof globalThis === 'object'\n    ? globalThis\n    : typeof self === 'object'\n        ? self\n        : typeof window === 'object'\n            ? window\n            : typeof global === 'object'\n                ? global\n                : {};\n//# sourceMappingURL=globalThis.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// this is autogenerated file, see scripts/version-update.js\nexport var VERSION = '1.9.0';\n//# sourceMappingURL=version.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { VERSION } from '../version';\nvar re = /^(\\d+)\\.(\\d+)\\.(\\d+)(-(.+))?$/;\n/**\n * Create a function to test an API version to see if it is compatible with the provided ownVersion.\n *\n * The returned function has the following semantics:\n * - Exact match is always compatible\n * - Major versions must match exactly\n *    - 1.x package cannot use global 2.x package\n *    - 2.x package cannot use global 1.x package\n * - The minor version of the API module requesting access to the global API must be less than or equal to the minor version of this API\n *    - 1.3 package may use 1.4 global because the later global contains all functions 1.3 expects\n *    - 1.4 package may NOT use 1.3 global because it may try to call functions which don't exist on 1.3\n * - If the major version is 0, the minor version is treated as the major and the patch is treated as the minor\n * - Patch and build tag differences are not considered at this time\n *\n * @param ownVersion version which should be checked against\n */\nexport function _makeCompatibilityCheck(ownVersion) {\n    var acceptedVersions = new Set([ownVersion]);\n    var rejectedVersions = new Set();\n    var myVersionMatch = ownVersion.match(re);\n    if (!myVersionMatch) {\n        // we cannot guarantee compatibility so we always return noop\n        return function () { return false; };\n    }\n    var ownVersionParsed = {\n        major: +myVersionMatch[1],\n        minor: +myVersionMatch[2],\n        patch: +myVersionMatch[3],\n        prerelease: myVersionMatch[4],\n    };\n    // if ownVersion has a prerelease tag, versions must match exactly\n    if (ownVersionParsed.prerelease != null) {\n        return function isExactmatch(globalVersion) {\n            return globalVersion === ownVersion;\n        };\n    }\n    function _reject(v) {\n        rejectedVersions.add(v);\n        return false;\n    }\n    function _accept(v) {\n        acceptedVersions.add(v);\n        return true;\n    }\n    return function isCompatible(globalVersion) {\n        if (acceptedVersions.has(globalVersion)) {\n            return true;\n        }\n        if (rejectedVersions.has(globalVersion)) {\n            return false;\n        }\n        var globalVersionMatch = globalVersion.match(re);\n        if (!globalVersionMatch) {\n            // cannot parse other version\n            // we cannot guarantee compatibility so we always noop\n            return _reject(globalVersion);\n        }\n        var globalVersionParsed = {\n            major: +globalVersionMatch[1],\n            minor: +globalVersionMatch[2],\n            patch: +globalVersionMatch[3],\n            prerelease: globalVersionMatch[4],\n        };\n        // if globalVersion has a prerelease tag, versions must match exactly\n        if (globalVersionParsed.prerelease != null) {\n            return _reject(globalVersion);\n        }\n        // major versions must match\n        if (ownVersionParsed.major !== globalVersionParsed.major) {\n            return _reject(globalVersion);\n        }\n        if (ownVersionParsed.major === 0) {\n            if (ownVersionParsed.minor === globalVersionParsed.minor &&\n                ownVersionParsed.patch <= globalVersionParsed.patch) {\n                return _accept(globalVersion);\n            }\n            return _reject(globalVersion);\n        }\n        if (ownVersionParsed.minor <= globalVersionParsed.minor) {\n            return _accept(globalVersion);\n        }\n        return _reject(globalVersion);\n    };\n}\n/**\n * Test an API version to see if it is compatible with this API.\n *\n * - Exact match is always compatible\n * - Major versions must match exactly\n *    - 1.x package cannot use global 2.x package\n *    - 2.x package cannot use global 1.x package\n * - The minor version of the API module requesting access to the global API must be less than or equal to the minor version of this API\n *    - 1.3 package may use 1.4 global because the later global contains all functions 1.3 expects\n *    - 1.4 package may NOT use 1.3 global because it may try to call functions which don't exist on 1.3\n * - If the major version is 0, the minor version is treated as the major and the patch is treated as the minor\n * - Patch and build tag differences are not considered at this time\n *\n * @param version version of the API requesting an instance of the global API\n */\nexport var isCompatible = _makeCompatibilityCheck(VERSION);\n//# sourceMappingURL=semver.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { _globalThis } from '../platform';\nimport { VERSION } from '../version';\nimport { isCompatible } from './semver';\nvar major = VERSION.split('.')[0];\nvar GLOBAL_OPENTELEMETRY_API_KEY = Symbol.for(\"opentelemetry.js.api.\" + major);\nvar _global = _globalThis;\nexport function registerGlobal(type, instance, diag, allowOverride) {\n    var _a;\n    if (allowOverride === void 0) { allowOverride = false; }\n    var api = (_global[GLOBAL_OPENTELEMETRY_API_KEY] = (_a = _global[GLOBAL_OPENTELEMETRY_API_KEY]) !== null && _a !== void 0 ? _a : {\n        version: VERSION,\n    });\n    if (!allowOverride && api[type]) {\n        // already registered an API of this type\n        var err = new Error(\"@opentelemetry/api: Attempted duplicate registration of API: \" + type);\n        diag.error(err.stack || err.message);\n        return false;\n    }\n    if (api.version !== VERSION) {\n        // All registered APIs must be of the same version exactly\n        var err = new Error(\"@opentelemetry/api: Registration of version v\" + api.version + \" for \" + type + \" does not match previously registered API v\" + VERSION);\n        diag.error(err.stack || err.message);\n        return false;\n    }\n    api[type] = instance;\n    diag.debug(\"@opentelemetry/api: Registered a global for \" + type + \" v\" + VERSION + \".\");\n    return true;\n}\nexport function getGlobal(type) {\n    var _a, _b;\n    var globalVersion = (_a = _global[GLOBAL_OPENTELEMETRY_API_KEY]) === null || _a === void 0 ? void 0 : _a.version;\n    if (!globalVersion || !isCompatible(globalVersion)) {\n        return;\n    }\n    return (_b = _global[GLOBAL_OPENTELEMETRY_API_KEY]) === null || _b === void 0 ? void 0 : _b[type];\n}\nexport function unregisterGlobal(type, diag) {\n    diag.debug(\"@opentelemetry/api: Unregistering a global for \" + type + \" v\" + VERSION + \".\");\n    var api = _global[GLOBAL_OPENTELEMETRY_API_KEY];\n    if (api) {\n        delete api[type];\n    }\n}\n//# sourceMappingURL=global-utils.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nimport { getGlobal } from '../internal/global-utils';\n/**\n * Component Logger which is meant to be used as part of any component which\n * will add automatically additional namespace in front of the log message.\n * It will then forward all message to global diag logger\n * @example\n * const cLogger = diag.createComponentLogger({ namespace: '@opentelemetry/instrumentation-http' });\n * cLogger.debug('test');\n * // @opentelemetry/instrumentation-http test\n */\nvar DiagComponentLogger = /** @class */ (function () {\n    function DiagComponentLogger(props) {\n        this._namespace = props.namespace || 'DiagComponentLogger';\n    }\n    DiagComponentLogger.prototype.debug = function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        return logProxy('debug', this._namespace, args);\n    };\n    DiagComponentLogger.prototype.error = function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        return logProxy('error', this._namespace, args);\n    };\n    DiagComponentLogger.prototype.info = function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        return logProxy('info', this._namespace, args);\n    };\n    DiagComponentLogger.prototype.warn = function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        return logProxy('warn', this._namespace, args);\n    };\n    DiagComponentLogger.prototype.verbose = function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        return logProxy('verbose', this._namespace, args);\n    };\n    return DiagComponentLogger;\n}());\nexport { DiagComponentLogger };\nfunction logProxy(funcName, namespace, args) {\n    var logger = getGlobal('diag');\n    // shortcut if logger not set\n    if (!logger) {\n        return;\n    }\n    args.unshift(namespace);\n    return logger[funcName].apply(logger, __spreadArray([], __read(args), false));\n}\n//# sourceMappingURL=ComponentLogger.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Defines the available internal logging levels for the diagnostic logger, the numeric values\n * of the levels are defined to match the original values from the initial LogLevel to avoid\n * compatibility/migration issues for any implementation that assume the numeric ordering.\n */\nexport var DiagLogLevel;\n(function (DiagLogLevel) {\n    /** Diagnostic Logging level setting to disable all logging (except and forced logs) */\n    DiagLogLevel[DiagLogLevel[\"NONE\"] = 0] = \"NONE\";\n    /** Identifies an error scenario */\n    DiagLogLevel[DiagLogLevel[\"ERROR\"] = 30] = \"ERROR\";\n    /** Identifies a warning scenario */\n    DiagLogLevel[DiagLogLevel[\"WARN\"] = 50] = \"WARN\";\n    /** General informational log message */\n    DiagLogLevel[DiagLogLevel[\"INFO\"] = 60] = \"INFO\";\n    /** General debug log message */\n    DiagLogLevel[DiagLogLevel[\"DEBUG\"] = 70] = \"DEBUG\";\n    /**\n     * Detailed trace level logging should only be used for development, should only be set\n     * in a development environment.\n     */\n    DiagLogLevel[DiagLogLevel[\"VERBOSE\"] = 80] = \"VERBOSE\";\n    /** Used to set the logging level to include all logging */\n    DiagLogLevel[DiagLogLevel[\"ALL\"] = 9999] = \"ALL\";\n})(DiagLogLevel || (DiagLogLevel = {}));\n//# sourceMappingURL=types.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { DiagLogLevel } from '../types';\nexport function createLogLevelDiagLogger(maxLevel, logger) {\n    if (maxLevel < DiagLogLevel.NONE) {\n        maxLevel = DiagLogLevel.NONE;\n    }\n    else if (maxLevel > DiagLogLevel.ALL) {\n        maxLevel = DiagLogLevel.ALL;\n    }\n    // In case the logger is null or undefined\n    logger = logger || {};\n    function _filterFunc(funcName, theLevel) {\n        var theFunc = logger[funcName];\n        if (typeof theFunc === 'function' && maxLevel >= theLevel) {\n            return theFunc.bind(logger);\n        }\n        return function () { };\n    }\n    return {\n        error: _filterFunc('error', DiagLogLevel.ERROR),\n        warn: _filterFunc('warn', DiagLogLevel.WARN),\n        info: _filterFunc('info', DiagLogLevel.INFO),\n        debug: _filterFunc('debug', DiagLogLevel.DEBUG),\n        verbose: _filterFunc('verbose', DiagLogLevel.VERBOSE),\n    };\n}\n//# sourceMappingURL=logLevelLogger.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nimport { DiagComponentLogger } from '../diag/ComponentLogger';\nimport { createLogLevelDiagLogger } from '../diag/internal/logLevelLogger';\nimport { DiagLogLevel, } from '../diag/types';\nimport { getGlobal, registerGlobal, unregisterGlobal, } from '../internal/global-utils';\nvar API_NAME = 'diag';\n/**\n * Singleton object which represents the entry point to the OpenTelemetry internal\n * diagnostic API\n */\nvar DiagAPI = /** @class */ (function () {\n    /**\n     * Private internal constructor\n     * @private\n     */\n    function DiagAPI() {\n        function _logProxy(funcName) {\n            return function () {\n                var args = [];\n                for (var _i = 0; _i < arguments.length; _i++) {\n                    args[_i] = arguments[_i];\n                }\n                var logger = getGlobal('diag');\n                // shortcut if logger not set\n                if (!logger)\n                    return;\n                return logger[funcName].apply(logger, __spreadArray([], __read(args), false));\n            };\n        }\n        // Using self local variable for minification purposes as 'this' cannot be minified\n        var self = this;\n        // DiagAPI specific functions\n        var setLogger = function (logger, optionsOrLogLevel) {\n            var _a, _b, _c;\n            if (optionsOrLogLevel === void 0) { optionsOrLogLevel = { logLevel: DiagLogLevel.INFO }; }\n            if (logger === self) {\n                // There isn't much we can do here.\n                // Logging to the console might break the user application.\n                // Try to log to self. If a logger was previously registered it will receive the log.\n                var err = new Error('Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation');\n                self.error((_a = err.stack) !== null && _a !== void 0 ? _a : err.message);\n                return false;\n            }\n            if (typeof optionsOrLogLevel === 'number') {\n                optionsOrLogLevel = {\n                    logLevel: optionsOrLogLevel,\n                };\n            }\n            var oldLogger = getGlobal('diag');\n            var newLogger = createLogLevelDiagLogger((_b = optionsOrLogLevel.logLevel) !== null && _b !== void 0 ? _b : DiagLogLevel.INFO, logger);\n            // There already is an logger registered. We'll let it know before overwriting it.\n            if (oldLogger && !optionsOrLogLevel.suppressOverrideMessage) {\n                var stack = (_c = new Error().stack) !== null && _c !== void 0 ? _c : '<failed to generate stacktrace>';\n                oldLogger.warn(\"Current logger will be overwritten from \" + stack);\n                newLogger.warn(\"Current logger will overwrite one already registered from \" + stack);\n            }\n            return registerGlobal('diag', newLogger, self, true);\n        };\n        self.setLogger = setLogger;\n        self.disable = function () {\n            unregisterGlobal(API_NAME, self);\n        };\n        self.createComponentLogger = function (options) {\n            return new DiagComponentLogger(options);\n        };\n        self.verbose = _logProxy('verbose');\n        self.debug = _logProxy('debug');\n        self.info = _logProxy('info');\n        self.warn = _logProxy('warn');\n        self.error = _logProxy('error');\n    }\n    /** Get the singleton instance of the DiagAPI API */\n    DiagAPI.instance = function () {\n        if (!this._instance) {\n            this._instance = new DiagAPI();\n        }\n        return this._instance;\n    };\n    return DiagAPI;\n}());\nexport { DiagAPI };\n//# sourceMappingURL=diag.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar BaggageImpl = /** @class */ (function () {\n    function BaggageImpl(entries) {\n        this._entries = entries ? new Map(entries) : new Map();\n    }\n    BaggageImpl.prototype.getEntry = function (key) {\n        var entry = this._entries.get(key);\n        if (!entry) {\n            return undefined;\n        }\n        return Object.assign({}, entry);\n    };\n    BaggageImpl.prototype.getAllEntries = function () {\n        return Array.from(this._entries.entries()).map(function (_a) {\n            var _b = __read(_a, 2), k = _b[0], v = _b[1];\n            return [k, v];\n        });\n    };\n    BaggageImpl.prototype.setEntry = function (key, entry) {\n        var newBaggage = new BaggageImpl(this._entries);\n        newBaggage._entries.set(key, entry);\n        return newBaggage;\n    };\n    BaggageImpl.prototype.removeEntry = function (key) {\n        var newBaggage = new BaggageImpl(this._entries);\n        newBaggage._entries.delete(key);\n        return newBaggage;\n    };\n    BaggageImpl.prototype.removeEntries = function () {\n        var e_1, _a;\n        var keys = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            keys[_i] = arguments[_i];\n        }\n        var newBaggage = new BaggageImpl(this._entries);\n        try {\n            for (var keys_1 = __values(keys), keys_1_1 = keys_1.next(); !keys_1_1.done; keys_1_1 = keys_1.next()) {\n                var key = keys_1_1.value;\n                newBaggage._entries.delete(key);\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (keys_1_1 && !keys_1_1.done && (_a = keys_1.return)) _a.call(keys_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        return newBaggage;\n    };\n    BaggageImpl.prototype.clear = function () {\n        return new BaggageImpl();\n    };\n    return BaggageImpl;\n}());\nexport { BaggageImpl };\n//# sourceMappingURL=baggage-impl.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Symbol used to make BaggageEntryMetadata an opaque type\n */\nexport var baggageEntryMetadataSymbol = Symbol('BaggageEntryMetadata');\n//# sourceMappingURL=symbol.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { DiagAPI } from '../api/diag';\nimport { BaggageImpl } from './internal/baggage-impl';\nimport { baggageEntryMetadataSymbol } from './internal/symbol';\nvar diag = DiagAPI.instance();\n/**\n * Create a new Baggage with optional entries\n *\n * @param entries An array of baggage entries the new baggage should contain\n */\nexport function createBaggage(entries) {\n    if (entries === void 0) { entries = {}; }\n    return new BaggageImpl(new Map(Object.entries(entries)));\n}\n/**\n * Create a serializable BaggageEntryMetadata object from a string.\n *\n * @param str string metadata. Format is currently not defined by the spec and has no special meaning.\n *\n */\nexport function baggageEntryMetadataFromString(str) {\n    if (typeof str !== 'string') {\n        diag.error(\"Cannot create baggage metadata from unknown type: \" + typeof str);\n        str = '';\n    }\n    return {\n        __TYPE__: baggageEntryMetadataSymbol,\n        toString: function () {\n            return str;\n        },\n    };\n}\n//# sourceMappingURL=utils.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/** Get a key to uniquely identify a context value */\nexport function createContextKey(description) {\n    // The specification states that for the same input, multiple calls should\n    // return different keys. Due to the nature of the JS dependency management\n    // system, this creates problems where multiple versions of some package\n    // could hold different keys for the same property.\n    //\n    // Therefore, we use Symbol.for which returns the same key for the same input.\n    return Symbol.for(description);\n}\nvar BaseContext = /** @class */ (function () {\n    /**\n     * Construct a new context which inherits values from an optional parent context.\n     *\n     * @param parentContext a context from which to inherit values\n     */\n    function BaseContext(parentContext) {\n        // for minification\n        var self = this;\n        self._currentContext = parentContext ? new Map(parentContext) : new Map();\n        self.getValue = function (key) { return self._currentContext.get(key); };\n        self.setValue = function (key, value) {\n            var context = new BaseContext(self._currentContext);\n            context._currentContext.set(key, value);\n            return context;\n        };\n        self.deleteValue = function (key) {\n            var context = new BaseContext(self._currentContext);\n            context._currentContext.delete(key);\n            return context;\n        };\n    }\n    return BaseContext;\n}());\n/** The root context is used as the default parent context when there is no active context */\nexport var ROOT_CONTEXT = new BaseContext();\n//# sourceMappingURL=context.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar consoleMap = [\n    { n: 'error', c: 'error' },\n    { n: 'warn', c: 'warn' },\n    { n: 'info', c: 'info' },\n    { n: 'debug', c: 'debug' },\n    { n: 'verbose', c: 'trace' },\n];\n/**\n * A simple Immutable Console based diagnostic logger which will output any messages to the Console.\n * If you want to limit the amount of logging to a specific level or lower use the\n * {@link createLogLevelDiagLogger}\n */\nvar DiagConsoleLogger = /** @class */ (function () {\n    function DiagConsoleLogger() {\n        function _consoleFunc(funcName) {\n            return function () {\n                var args = [];\n                for (var _i = 0; _i < arguments.length; _i++) {\n                    args[_i] = arguments[_i];\n                }\n                if (console) {\n                    // Some environments only expose the console when the F12 developer console is open\n                    // eslint-disable-next-line no-console\n                    var theFunc = console[funcName];\n                    if (typeof theFunc !== 'function') {\n                        // Not all environments support all functions\n                        // eslint-disable-next-line no-console\n                        theFunc = console.log;\n                    }\n                    // One last final check\n                    if (typeof theFunc === 'function') {\n                        return theFunc.apply(console, args);\n                    }\n                }\n            };\n        }\n        for (var i = 0; i < consoleMap.length; i++) {\n            this[consoleMap[i].n] = _consoleFunc(consoleMap[i].c);\n        }\n    }\n    return DiagConsoleLogger;\n}());\nexport { DiagConsoleLogger };\n//# sourceMappingURL=consoleLogger.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n/**\n * NoopMeter is a noop implementation of the {@link Meter} interface. It reuses\n * constant NoopMetrics for all of its methods.\n */\nvar NoopMeter = /** @class */ (function () {\n    function NoopMeter() {\n    }\n    /**\n     * @see {@link Meter.createGauge}\n     */\n    NoopMeter.prototype.createGauge = function (_name, _options) {\n        return NOOP_GAUGE_METRIC;\n    };\n    /**\n     * @see {@link Meter.createHistogram}\n     */\n    NoopMeter.prototype.createHistogram = function (_name, _options) {\n        return NOOP_HISTOGRAM_METRIC;\n    };\n    /**\n     * @see {@link Meter.createCounter}\n     */\n    NoopMeter.prototype.createCounter = function (_name, _options) {\n        return NOOP_COUNTER_METRIC;\n    };\n    /**\n     * @see {@link Meter.createUpDownCounter}\n     */\n    NoopMeter.prototype.createUpDownCounter = function (_name, _options) {\n        return NOOP_UP_DOWN_COUNTER_METRIC;\n    };\n    /**\n     * @see {@link Meter.createObservableGauge}\n     */\n    NoopMeter.prototype.createObservableGauge = function (_name, _options) {\n        return NOOP_OBSERVABLE_GAUGE_METRIC;\n    };\n    /**\n     * @see {@link Meter.createObservableCounter}\n     */\n    NoopMeter.prototype.createObservableCounter = function (_name, _options) {\n        return NOOP_OBSERVABLE_COUNTER_METRIC;\n    };\n    /**\n     * @see {@link Meter.createObservableUpDownCounter}\n     */\n    NoopMeter.prototype.createObservableUpDownCounter = function (_name, _options) {\n        return NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC;\n    };\n    /**\n     * @see {@link Meter.addBatchObservableCallback}\n     */\n    NoopMeter.prototype.addBatchObservableCallback = function (_callback, _observables) { };\n    /**\n     * @see {@link Meter.removeBatchObservableCallback}\n     */\n    NoopMeter.prototype.removeBatchObservableCallback = function (_callback) { };\n    return NoopMeter;\n}());\nexport { NoopMeter };\nvar NoopMetric = /** @class */ (function () {\n    function NoopMetric() {\n    }\n    return NoopMetric;\n}());\nexport { NoopMetric };\nvar NoopCounterMetric = /** @class */ (function (_super) {\n    __extends(NoopCounterMetric, _super);\n    function NoopCounterMetric() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    NoopCounterMetric.prototype.add = function (_value, _attributes) { };\n    return NoopCounterMetric;\n}(NoopMetric));\nexport { NoopCounterMetric };\nvar NoopUpDownCounterMetric = /** @class */ (function (_super) {\n    __extends(NoopUpDownCounterMetric, _super);\n    function NoopUpDownCounterMetric() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    NoopUpDownCounterMetric.prototype.add = function (_value, _attributes) { };\n    return NoopUpDownCounterMetric;\n}(NoopMetric));\nexport { NoopUpDownCounterMetric };\nvar NoopGaugeMetric = /** @class */ (function (_super) {\n    __extends(NoopGaugeMetric, _super);\n    function NoopGaugeMetric() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    NoopGaugeMetric.prototype.record = function (_value, _attributes) { };\n    return NoopGaugeMetric;\n}(NoopMetric));\nexport { NoopGaugeMetric };\nvar NoopHistogramMetric = /** @class */ (function (_super) {\n    __extends(NoopHistogramMetric, _super);\n    function NoopHistogramMetric() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    NoopHistogramMetric.prototype.record = function (_value, _attributes) { };\n    return NoopHistogramMetric;\n}(NoopMetric));\nexport { NoopHistogramMetric };\nvar NoopObservableMetric = /** @class */ (function () {\n    function NoopObservableMetric() {\n    }\n    NoopObservableMetric.prototype.addCallback = function (_callback) { };\n    NoopObservableMetric.prototype.removeCallback = function (_callback) { };\n    return NoopObservableMetric;\n}());\nexport { NoopObservableMetric };\nvar NoopObservableCounterMetric = /** @class */ (function (_super) {\n    __extends(NoopObservableCounterMetric, _super);\n    function NoopObservableCounterMetric() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    return NoopObservableCounterMetric;\n}(NoopObservableMetric));\nexport { NoopObservableCounterMetric };\nvar NoopObservableGaugeMetric = /** @class */ (function (_super) {\n    __extends(NoopObservableGaugeMetric, _super);\n    function NoopObservableGaugeMetric() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    return NoopObservableGaugeMetric;\n}(NoopObservableMetric));\nexport { NoopObservableGaugeMetric };\nvar NoopObservableUpDownCounterMetric = /** @class */ (function (_super) {\n    __extends(NoopObservableUpDownCounterMetric, _super);\n    function NoopObservableUpDownCounterMetric() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    return NoopObservableUpDownCounterMetric;\n}(NoopObservableMetric));\nexport { NoopObservableUpDownCounterMetric };\nexport var NOOP_METER = new NoopMeter();\n// Synchronous instruments\nexport var NOOP_COUNTER_METRIC = new NoopCounterMetric();\nexport var NOOP_GAUGE_METRIC = new NoopGaugeMetric();\nexport var NOOP_HISTOGRAM_METRIC = new NoopHistogramMetric();\nexport var NOOP_UP_DOWN_COUNTER_METRIC = new NoopUpDownCounterMetric();\n// Asynchronous instruments\nexport var NOOP_OBSERVABLE_COUNTER_METRIC = new NoopObservableCounterMetric();\nexport var NOOP_OBSERVABLE_GAUGE_METRIC = new NoopObservableGaugeMetric();\nexport var NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC = new NoopObservableUpDownCounterMetric();\n/**\n * Create a no-op Meter\n */\nexport function createNoopMeter() {\n    return NOOP_METER;\n}\n//# sourceMappingURL=NoopMeter.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/** The Type of value. It describes how the data is reported. */\nexport var ValueType;\n(function (ValueType) {\n    ValueType[ValueType[\"INT\"] = 0] = \"INT\";\n    ValueType[ValueType[\"DOUBLE\"] = 1] = \"DOUBLE\";\n})(ValueType || (ValueType = {}));\n//# sourceMappingURL=Metric.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nexport var defaultTextMapGetter = {\n    get: function (carrier, key) {\n        if (carrier == null) {\n            return undefined;\n        }\n        return carrier[key];\n    },\n    keys: function (carrier) {\n        if (carrier == null) {\n            return [];\n        }\n        return Object.keys(carrier);\n    },\n};\nexport var defaultTextMapSetter = {\n    set: function (carrier, key, value) {\n        if (carrier == null) {\n            return;\n        }\n        carrier[key] = value;\n    },\n};\n//# sourceMappingURL=TextMapPropagator.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nimport { ROOT_CONTEXT } from './context';\nvar NoopContextManager = /** @class */ (function () {\n    function NoopContextManager() {\n    }\n    NoopContextManager.prototype.active = function () {\n        return ROOT_CONTEXT;\n    };\n    NoopContextManager.prototype.with = function (_context, fn, thisArg) {\n        var args = [];\n        for (var _i = 3; _i < arguments.length; _i++) {\n            args[_i - 3] = arguments[_i];\n        }\n        return fn.call.apply(fn, __spreadArray([thisArg], __read(args), false));\n    };\n    NoopContextManager.prototype.bind = function (_context, target) {\n        return target;\n    };\n    NoopContextManager.prototype.enable = function () {\n        return this;\n    };\n    NoopContextManager.prototype.disable = function () {\n        return this;\n    };\n    return NoopContextManager;\n}());\nexport { NoopContextManager };\n//# sourceMappingURL=NoopContextManager.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nimport { NoopContextManager } from '../context/NoopContextManager';\nimport { getGlobal, registerGlobal, unregisterGlobal, } from '../internal/global-utils';\nimport { DiagAPI } from './diag';\nvar API_NAME = 'context';\nvar NOOP_CONTEXT_MANAGER = new NoopContextManager();\n/**\n * Singleton object which represents the entry point to the OpenTelemetry Context API\n */\nvar ContextAPI = /** @class */ (function () {\n    /** Empty private constructor prevents end users from constructing a new instance of the API */\n    function ContextAPI() {\n    }\n    /** Get the singleton instance of the Context API */\n    ContextAPI.getInstance = function () {\n        if (!this._instance) {\n            this._instance = new ContextAPI();\n        }\n        return this._instance;\n    };\n    /**\n     * Set the current context manager.\n     *\n     * @returns true if the context manager was successfully registered, else false\n     */\n    ContextAPI.prototype.setGlobalContextManager = function (contextManager) {\n        return registerGlobal(API_NAME, contextManager, DiagAPI.instance());\n    };\n    /**\n     * Get the currently active context\n     */\n    ContextAPI.prototype.active = function () {\n        return this._getContextManager().active();\n    };\n    /**\n     * Execute a function with an active context\n     *\n     * @param context context to be active during function execution\n     * @param fn function to execute in a context\n     * @param thisArg optional receiver to be used for calling fn\n     * @param args optional arguments forwarded to fn\n     */\n    ContextAPI.prototype.with = function (context, fn, thisArg) {\n        var _a;\n        var args = [];\n        for (var _i = 3; _i < arguments.length; _i++) {\n            args[_i - 3] = arguments[_i];\n        }\n        return (_a = this._getContextManager()).with.apply(_a, __spreadArray([context, fn, thisArg], __read(args), false));\n    };\n    /**\n     * Bind a context to a target function or event emitter\n     *\n     * @param context context to bind to the event emitter or function. Defaults to the currently active context\n     * @param target function or event emitter to bind\n     */\n    ContextAPI.prototype.bind = function (context, target) {\n        return this._getContextManager().bind(context, target);\n    };\n    ContextAPI.prototype._getContextManager = function () {\n        return getGlobal(API_NAME) || NOOP_CONTEXT_MANAGER;\n    };\n    /** Disable and remove the global context manager */\n    ContextAPI.prototype.disable = function () {\n        this._getContextManager().disable();\n        unregisterGlobal(API_NAME, DiagAPI.instance());\n    };\n    return ContextAPI;\n}());\nexport { ContextAPI };\n//# sourceMappingURL=context.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nexport var TraceFlags;\n(function (TraceFlags) {\n    /** Represents no flag set. */\n    TraceFlags[TraceFlags[\"NONE\"] = 0] = \"NONE\";\n    /** Bit to represent whether trace is sampled in trace flags. */\n    TraceFlags[TraceFlags[\"SAMPLED\"] = 1] = \"SAMPLED\";\n})(TraceFlags || (TraceFlags = {}));\n//# sourceMappingURL=trace_flags.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { TraceFlags } from './trace_flags';\nexport var INVALID_SPANID = '0000000000000000';\nexport var INVALID_TRACEID = '00000000000000000000000000000000';\nexport var INVALID_SPAN_CONTEXT = {\n    traceId: INVALID_TRACEID,\n    spanId: INVALID_SPANID,\n    traceFlags: TraceFlags.NONE,\n};\n//# sourceMappingURL=invalid-span-constants.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { INVALID_SPAN_CONTEXT } from './invalid-span-constants';\n/**\n * The NonRecordingSpan is the default {@link Span} that is used when no Span\n * implementation is available. All operations are no-op including context\n * propagation.\n */\nvar NonRecordingSpan = /** @class */ (function () {\n    function NonRecordingSpan(_spanContext) {\n        if (_spanContext === void 0) { _spanContext = INVALID_SPAN_CONTEXT; }\n        this._spanContext = _spanContext;\n    }\n    // Returns a SpanContext.\n    NonRecordingSpan.prototype.spanContext = function () {\n        return this._spanContext;\n    };\n    // By default does nothing\n    NonRecordingSpan.prototype.setAttribute = function (_key, _value) {\n        return this;\n    };\n    // By default does nothing\n    NonRecordingSpan.prototype.setAttributes = function (_attributes) {\n        return this;\n    };\n    // By default does nothing\n    NonRecordingSpan.prototype.addEvent = function (_name, _attributes) {\n        return this;\n    };\n    NonRecordingSpan.prototype.addLink = function (_link) {\n        return this;\n    };\n    NonRecordingSpan.prototype.addLinks = function (_links) {\n        return this;\n    };\n    // By default does nothing\n    NonRecordingSpan.prototype.setStatus = function (_status) {\n        return this;\n    };\n    // By default does nothing\n    NonRecordingSpan.prototype.updateName = function (_name) {\n        return this;\n    };\n    // By default does nothing\n    NonRecordingSpan.prototype.end = function (_endTime) { };\n    // isRecording always returns false for NonRecordingSpan.\n    NonRecordingSpan.prototype.isRecording = function () {\n        return false;\n    };\n    // By default does nothing\n    NonRecordingSpan.prototype.recordException = function (_exception, _time) { };\n    return NonRecordingSpan;\n}());\nexport { NonRecordingSpan };\n//# sourceMappingURL=NonRecordingSpan.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { createContextKey } from '../context/context';\nimport { NonRecordingSpan } from './NonRecordingSpan';\nimport { ContextAPI } from '../api/context';\n/**\n * span key\n */\nvar SPAN_KEY = createContextKey('OpenTelemetry Context Key SPAN');\n/**\n * Return the span if one exists\n *\n * @param context context to get span from\n */\nexport function getSpan(context) {\n    return context.getValue(SPAN_KEY) || undefined;\n}\n/**\n * Gets the span from the current context, if one exists.\n */\nexport function getActiveSpan() {\n    return getSpan(ContextAPI.getInstance().active());\n}\n/**\n * Set the span on a context\n *\n * @param context context to use as parent\n * @param span span to set active\n */\nexport function setSpan(context, span) {\n    return context.setValue(SPAN_KEY, span);\n}\n/**\n * Remove current span stored in the context\n *\n * @param context context to delete span from\n */\nexport function deleteSpan(context) {\n    return context.deleteValue(SPAN_KEY);\n}\n/**\n * Wrap span context in a NoopSpan and set as span in a new\n * context\n *\n * @param context context to set active span on\n * @param spanContext span context to be wrapped\n */\nexport function setSpanContext(context, spanContext) {\n    return setSpan(context, new NonRecordingSpan(spanContext));\n}\n/**\n * Get the span context of the span if it exists.\n *\n * @param context context to get values from\n */\nexport function getSpanContext(context) {\n    var _a;\n    return (_a = getSpan(context)) === null || _a === void 0 ? void 0 : _a.spanContext();\n}\n//# sourceMappingURL=context-utils.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { INVALID_SPANID, INVALID_TRACEID } from './invalid-span-constants';\nimport { NonRecordingSpan } from './NonRecordingSpan';\nvar VALID_TRACEID_REGEX = /^([0-9a-f]{32})$/i;\nvar VALID_SPANID_REGEX = /^[0-9a-f]{16}$/i;\nexport function isValidTraceId(traceId) {\n    return VALID_TRACEID_REGEX.test(traceId) && traceId !== INVALID_TRACEID;\n}\nexport function isValidSpanId(spanId) {\n    return VALID_SPANID_REGEX.test(spanId) && spanId !== INVALID_SPANID;\n}\n/**\n * Returns true if this {@link SpanContext} is valid.\n * @return true if this {@link SpanContext} is valid.\n */\nexport function isSpanContextValid(spanContext) {\n    return (isValidTraceId(spanContext.traceId) && isValidSpanId(spanContext.spanId));\n}\n/**\n * Wrap the given {@link SpanContext} in a new non-recording {@link Span}\n *\n * @param spanContext span context to be wrapped\n * @returns a new non-recording {@link Span} with the provided context\n */\nexport function wrapSpanContext(spanContext) {\n    return new NonRecordingSpan(spanContext);\n}\n//# sourceMappingURL=spancontext-utils.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { ContextAPI } from '../api/context';\nimport { getSpanContext, setSpan } from '../trace/context-utils';\nimport { NonRecordingSpan } from './NonRecordingSpan';\nimport { isSpanContextValid } from './spancontext-utils';\nvar contextApi = ContextAPI.getInstance();\n/**\n * No-op implementations of {@link Tracer}.\n */\nvar NoopTracer = /** @class */ (function () {\n    function NoopTracer() {\n    }\n    // startSpan starts a noop span.\n    NoopTracer.prototype.startSpan = function (name, options, context) {\n        if (context === void 0) { context = contextApi.active(); }\n        var root = Boolean(options === null || options === void 0 ? void 0 : options.root);\n        if (root) {\n            return new NonRecordingSpan();\n        }\n        var parentFromContext = context && getSpanContext(context);\n        if (isSpanContext(parentFromContext) &&\n            isSpanContextValid(parentFromContext)) {\n            return new NonRecordingSpan(parentFromContext);\n        }\n        else {\n            return new NonRecordingSpan();\n        }\n    };\n    NoopTracer.prototype.startActiveSpan = function (name, arg2, arg3, arg4) {\n        var opts;\n        var ctx;\n        var fn;\n        if (arguments.length < 2) {\n            return;\n        }\n        else if (arguments.length === 2) {\n            fn = arg2;\n        }\n        else if (arguments.length === 3) {\n            opts = arg2;\n            fn = arg3;\n        }\n        else {\n            opts = arg2;\n            ctx = arg3;\n            fn = arg4;\n        }\n        var parentContext = ctx !== null && ctx !== void 0 ? ctx : contextApi.active();\n        var span = this.startSpan(name, opts, parentContext);\n        var contextWithSpanSet = setSpan(parentContext, span);\n        return contextApi.with(contextWithSpanSet, fn, undefined, span);\n    };\n    return NoopTracer;\n}());\nexport { NoopTracer };\nfunction isSpanContext(spanContext) {\n    return (typeof spanContext === 'object' &&\n        typeof spanContext['spanId'] === 'string' &&\n        typeof spanContext['traceId'] === 'string' &&\n        typeof spanContext['traceFlags'] === 'number');\n}\n//# sourceMappingURL=NoopTracer.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { NoopTracer } from './NoopTracer';\nvar NOOP_TRACER = new NoopTracer();\n/**\n * Proxy tracer provided by the proxy tracer provider\n */\nvar ProxyTracer = /** @class */ (function () {\n    function ProxyTracer(_provider, name, version, options) {\n        this._provider = _provider;\n        this.name = name;\n        this.version = version;\n        this.options = options;\n    }\n    ProxyTracer.prototype.startSpan = function (name, options, context) {\n        return this._getTracer().startSpan(name, options, context);\n    };\n    ProxyTracer.prototype.startActiveSpan = function (_name, _options, _context, _fn) {\n        var tracer = this._getTracer();\n        return Reflect.apply(tracer.startActiveSpan, tracer, arguments);\n    };\n    /**\n     * Try to get a tracer from the proxy tracer provider.\n     * If the proxy tracer provider has no delegate, return a noop tracer.\n     */\n    ProxyTracer.prototype._getTracer = function () {\n        if (this._delegate) {\n            return this._delegate;\n        }\n        var tracer = this._provider.getDelegateTracer(this.name, this.version, this.options);\n        if (!tracer) {\n            return NOOP_TRACER;\n        }\n        this._delegate = tracer;\n        return this._delegate;\n    };\n    return ProxyTracer;\n}());\nexport { ProxyTracer };\n//# sourceMappingURL=ProxyTracer.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { NoopTracer } from './NoopTracer';\n/**\n * An implementation of the {@link TracerProvider} which returns an impotent\n * Tracer for all calls to `getTracer`.\n *\n * All operations are no-op.\n */\nvar NoopTracerProvider = /** @class */ (function () {\n    function NoopTracerProvider() {\n    }\n    NoopTracerProvider.prototype.getTracer = function (_name, _version, _options) {\n        return new NoopTracer();\n    };\n    return NoopTracerProvider;\n}());\nexport { NoopTracerProvider };\n//# sourceMappingURL=NoopTracerProvider.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { ProxyTracer } from './ProxyTracer';\nimport { NoopTracerProvider } from './NoopTracerProvider';\nvar NOOP_TRACER_PROVIDER = new NoopTracerProvider();\n/**\n * Tracer provider which provides {@link ProxyTracer}s.\n *\n * Before a delegate is set, tracers provided are NoOp.\n *   When a delegate is set, traces are provided from the delegate.\n *   When a delegate is set after tracers have already been provided,\n *   all tracers already provided will use the provided delegate implementation.\n */\nvar ProxyTracerProvider = /** @class */ (function () {\n    function ProxyTracerProvider() {\n    }\n    /**\n     * Get a {@link ProxyTracer}\n     */\n    ProxyTracerProvider.prototype.getTracer = function (name, version, options) {\n        var _a;\n        return ((_a = this.getDelegateTracer(name, version, options)) !== null && _a !== void 0 ? _a : new ProxyTracer(this, name, version, options));\n    };\n    ProxyTracerProvider.prototype.getDelegate = function () {\n        var _a;\n        return (_a = this._delegate) !== null && _a !== void 0 ? _a : NOOP_TRACER_PROVIDER;\n    };\n    /**\n     * Set the delegate tracer provider\n     */\n    ProxyTracerProvider.prototype.setDelegate = function (delegate) {\n        this._delegate = delegate;\n    };\n    ProxyTracerProvider.prototype.getDelegateTracer = function (name, version, options) {\n        var _a;\n        return (_a = this._delegate) === null || _a === void 0 ? void 0 : _a.getTracer(name, version, options);\n    };\n    return ProxyTracerProvider;\n}());\nexport { ProxyTracerProvider };\n//# sourceMappingURL=ProxyTracerProvider.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @deprecated use the one declared in @opentelemetry/sdk-trace-base instead.\n * A sampling decision that determines how a {@link Span} will be recorded\n * and collected.\n */\nexport var SamplingDecision;\n(function (SamplingDecision) {\n    /**\n     * `Span.isRecording() === false`, span will not be recorded and all events\n     * and attributes will be dropped.\n     */\n    SamplingDecision[SamplingDecision[\"NOT_RECORD\"] = 0] = \"NOT_RECORD\";\n    /**\n     * `Span.isRecording() === true`, but `Sampled` flag in {@link TraceFlags}\n     * MUST NOT be set.\n     */\n    SamplingDecision[SamplingDecision[\"RECORD\"] = 1] = \"RECORD\";\n    /**\n     * `Span.isRecording() === true` AND `Sampled` flag in {@link TraceFlags}\n     * MUST be set.\n     */\n    SamplingDecision[SamplingDecision[\"RECORD_AND_SAMPLED\"] = 2] = \"RECORD_AND_SAMPLED\";\n})(SamplingDecision || (SamplingDecision = {}));\n//# sourceMappingURL=SamplingResult.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nexport var SpanKind;\n(function (SpanKind) {\n    /** Default value. Indicates that the span is used internally. */\n    SpanKind[SpanKind[\"INTERNAL\"] = 0] = \"INTERNAL\";\n    /**\n     * Indicates that the span covers server-side handling of an RPC or other\n     * remote request.\n     */\n    SpanKind[SpanKind[\"SERVER\"] = 1] = \"SERVER\";\n    /**\n     * Indicates that the span covers the client-side wrapper around an RPC or\n     * other remote request.\n     */\n    SpanKind[SpanKind[\"CLIENT\"] = 2] = \"CLIENT\";\n    /**\n     * Indicates that the span describes producer sending a message to a\n     * broker. Unlike client and server, there is no direct critical path latency\n     * relationship between producer and consumer spans.\n     */\n    SpanKind[SpanKind[\"PRODUCER\"] = 3] = \"PRODUCER\";\n    /**\n     * Indicates that the span describes consumer receiving a message from a\n     * broker. Unlike client and server, there is no direct critical path latency\n     * relationship between producer and consumer spans.\n     */\n    SpanKind[SpanKind[\"CONSUMER\"] = 4] = \"CONSUMER\";\n})(SpanKind || (SpanKind = {}));\n//# sourceMappingURL=span_kind.js.map", "/**\n * An enumeration of status codes.\n */\nexport var SpanStatusCode;\n(function (SpanStatusCode) {\n    /**\n     * The default status.\n     */\n    SpanStatusCode[SpanStatusCode[\"UNSET\"] = 0] = \"UNSET\";\n    /**\n     * The operation has been validated by an Application developer or\n     * Operator to have completed successfully.\n     */\n    SpanStatusCode[SpanStatusCode[\"OK\"] = 1] = \"OK\";\n    /**\n     * The operation contains an error.\n     */\n    SpanStatusCode[SpanStatusCode[\"ERROR\"] = 2] = \"ERROR\";\n})(SpanStatusCode || (SpanStatusCode = {}));\n//# sourceMappingURL=status.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar VALID_KEY_CHAR_RANGE = '[_0-9a-z-*/]';\nvar VALID_KEY = \"[a-z]\" + VALID_KEY_CHAR_RANGE + \"{0,255}\";\nvar VALID_VENDOR_KEY = \"[a-z0-9]\" + VALID_KEY_CHAR_RANGE + \"{0,240}@[a-z]\" + VALID_KEY_CHAR_RANGE + \"{0,13}\";\nvar VALID_KEY_REGEX = new RegExp(\"^(?:\" + VALID_KEY + \"|\" + VALID_VENDOR_KEY + \")$\");\nvar VALID_VALUE_BASE_REGEX = /^[ -~]{0,255}[!-~]$/;\nvar INVALID_VALUE_COMMA_EQUAL_REGEX = /,|=/;\n/**\n * Key is opaque string up to 256 characters printable. It MUST begin with a\n * lowercase letter, and can only contain lowercase letters a-z, digits 0-9,\n * underscores _, dashes -, asterisks *, and forward slashes /.\n * For multi-tenant vendor scenarios, an at sign (@) can be used to prefix the\n * vendor name. Vendors SHOULD set the tenant ID at the beginning of the key.\n * see https://www.w3.org/TR/trace-context/#key\n */\nexport function validateKey(key) {\n    return VALID_KEY_REGEX.test(key);\n}\n/**\n * Value is opaque string up to 256 characters printable ASCII RFC0020\n * characters (i.e., the range 0x20 to 0x7E) except comma , and =.\n */\nexport function validateValue(value) {\n    return (VALID_VALUE_BASE_REGEX.test(value) &&\n        !INVALID_VALUE_COMMA_EQUAL_REGEX.test(value));\n}\n//# sourceMappingURL=tracestate-validators.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { validateKey, validateValue } from './tracestate-validators';\nvar MAX_TRACE_STATE_ITEMS = 32;\nvar MAX_TRACE_STATE_LEN = 512;\nvar LIST_MEMBERS_SEPARATOR = ',';\nvar LIST_MEMBER_KEY_VALUE_SPLITTER = '=';\n/**\n * TraceState must be a class and not a simple object type because of the spec\n * requirement (https://www.w3.org/TR/trace-context/#tracestate-field).\n *\n * Here is the list of allowed mutations:\n * - New key-value pair should be added into the beginning of the list\n * - The value of any key can be updated. Modified keys MUST be moved to the\n * beginning of the list.\n */\nvar TraceStateImpl = /** @class */ (function () {\n    function TraceStateImpl(rawTraceState) {\n        this._internalState = new Map();\n        if (rawTraceState)\n            this._parse(rawTraceState);\n    }\n    TraceStateImpl.prototype.set = function (key, value) {\n        // TODO: Benchmark the different approaches(map vs list) and\n        // use the faster one.\n        var traceState = this._clone();\n        if (traceState._internalState.has(key)) {\n            traceState._internalState.delete(key);\n        }\n        traceState._internalState.set(key, value);\n        return traceState;\n    };\n    TraceStateImpl.prototype.unset = function (key) {\n        var traceState = this._clone();\n        traceState._internalState.delete(key);\n        return traceState;\n    };\n    TraceStateImpl.prototype.get = function (key) {\n        return this._internalState.get(key);\n    };\n    TraceStateImpl.prototype.serialize = function () {\n        var _this = this;\n        return this._keys()\n            .reduce(function (agg, key) {\n            agg.push(key + LIST_MEMBER_KEY_VALUE_SPLITTER + _this.get(key));\n            return agg;\n        }, [])\n            .join(LIST_MEMBERS_SEPARATOR);\n    };\n    TraceStateImpl.prototype._parse = function (rawTraceState) {\n        if (rawTraceState.length > MAX_TRACE_STATE_LEN)\n            return;\n        this._internalState = rawTraceState\n            .split(LIST_MEMBERS_SEPARATOR)\n            .reverse() // Store in reverse so new keys (.set(...)) will be placed at the beginning\n            .reduce(function (agg, part) {\n            var listMember = part.trim(); // Optional Whitespace (OWS) handling\n            var i = listMember.indexOf(LIST_MEMBER_KEY_VALUE_SPLITTER);\n            if (i !== -1) {\n                var key = listMember.slice(0, i);\n                var value = listMember.slice(i + 1, part.length);\n                if (validateKey(key) && validateValue(value)) {\n                    agg.set(key, value);\n                }\n                else {\n                    // TODO: Consider to add warning log\n                }\n            }\n            return agg;\n        }, new Map());\n        // Because of the reverse() requirement, trunc must be done after map is created\n        if (this._internalState.size > MAX_TRACE_STATE_ITEMS) {\n            this._internalState = new Map(Array.from(this._internalState.entries())\n                .reverse() // Use reverse same as original tracestate parse chain\n                .slice(0, MAX_TRACE_STATE_ITEMS));\n        }\n    };\n    TraceStateImpl.prototype._keys = function () {\n        return Array.from(this._internalState.keys()).reverse();\n    };\n    TraceStateImpl.prototype._clone = function () {\n        var traceState = new TraceStateImpl();\n        traceState._internalState = new Map(this._internalState);\n        return traceState;\n    };\n    return TraceStateImpl;\n}());\nexport { TraceStateImpl };\n//# sourceMappingURL=tracestate-impl.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { TraceStateImpl } from './tracestate-impl';\nexport function createTraceState(rawTraceState) {\n    return new TraceStateImpl(rawTraceState);\n}\n//# sourceMappingURL=utils.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// Split module-level variable definition into separate files to allow\n// tree-shaking on each api instance.\nimport { ContextAPI } from './api/context';\n/** Entrypoint for context API */\nexport var context = ContextAPI.getInstance();\n//# sourceMappingURL=context-api.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// Split module-level variable definition into separate files to allow\n// tree-shaking on each api instance.\nimport { DiagAPI } from './api/diag';\n/**\n * Entrypoint for Diag API.\n * Defines Diagnostic handler used for internal diagnostic logging operations.\n * The default provides a Noop DiagLogger implementation which may be changed via the\n * diag.setLogger(logger: DiagLogger) function.\n */\nexport var diag = DiagAPI.instance();\n//# sourceMappingURL=diag-api.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { NOOP_METER } from './NoopMeter';\n/**\n * An implementation of the {@link MeterProvider} which returns an impotent Meter\n * for all calls to `getMeter`\n */\nvar NoopMeterProvider = /** @class */ (function () {\n    function NoopMeterProvider() {\n    }\n    NoopMeterProvider.prototype.getMeter = function (_name, _version, _options) {\n        return NOOP_METER;\n    };\n    return NoopMeterProvider;\n}());\nexport { NoopMeterProvider };\nexport var NOOP_METER_PROVIDER = new NoopMeterProvider();\n//# sourceMappingURL=NoopMeterProvider.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { NOOP_METER_PROVIDER } from '../metrics/NoopMeterProvider';\nimport { getGlobal, registerGlobal, unregisterGlobal, } from '../internal/global-utils';\nimport { DiagAPI } from './diag';\nvar API_NAME = 'metrics';\n/**\n * Singleton object which represents the entry point to the OpenTelemetry Metrics API\n */\nvar MetricsAPI = /** @class */ (function () {\n    /** Empty private constructor prevents end users from constructing a new instance of the API */\n    function MetricsAPI() {\n    }\n    /** Get the singleton instance of the Metrics API */\n    MetricsAPI.getInstance = function () {\n        if (!this._instance) {\n            this._instance = new MetricsAPI();\n        }\n        return this._instance;\n    };\n    /**\n     * Set the current global meter provider.\n     * Returns true if the meter provider was successfully registered, else false.\n     */\n    MetricsAPI.prototype.setGlobalMeterProvider = function (provider) {\n        return registerGlobal(API_NAME, provider, DiagAPI.instance());\n    };\n    /**\n     * Returns the global meter provider.\n     */\n    MetricsAPI.prototype.getMeterProvider = function () {\n        return getGlobal(API_NAME) || NOOP_METER_PROVIDER;\n    };\n    /**\n     * Returns a meter from the global meter provider.\n     */\n    MetricsAPI.prototype.getMeter = function (name, version, options) {\n        return this.getMeterProvider().getMeter(name, version, options);\n    };\n    /** Remove the global meter provider */\n    MetricsAPI.prototype.disable = function () {\n        unregisterGlobal(API_NAME, DiagAPI.instance());\n    };\n    return MetricsAPI;\n}());\nexport { MetricsAPI };\n//# sourceMappingURL=metrics.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// Split module-level variable definition into separate files to allow\n// tree-shaking on each api instance.\nimport { MetricsAPI } from './api/metrics';\n/** Entrypoint for metrics API */\nexport var metrics = MetricsAPI.getInstance();\n//# sourceMappingURL=metrics-api.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * No-op implementations of {@link TextMapPropagator}.\n */\nvar NoopTextMapPropagator = /** @class */ (function () {\n    function NoopTextMapPropagator() {\n    }\n    /** Noop inject function does nothing */\n    NoopTextMapPropagator.prototype.inject = function (_context, _carrier) { };\n    /** Noop extract function does nothing and returns the input context */\n    NoopTextMapPropagator.prototype.extract = function (context, _carrier) {\n        return context;\n    };\n    NoopTextMapPropagator.prototype.fields = function () {\n        return [];\n    };\n    return NoopTextMapPropagator;\n}());\nexport { NoopTextMapPropagator };\n//# sourceMappingURL=NoopTextMapPropagator.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { ContextAPI } from '../api/context';\nimport { createContextKey } from '../context/context';\n/**\n * Baggage key\n */\nvar BAGGAGE_KEY = createContextKey('OpenTelemetry Baggage Key');\n/**\n * Retrieve the current baggage from the given context\n *\n * @param {Context} Context that manage all context values\n * @returns {Baggage} Extracted baggage from the context\n */\nexport function getBaggage(context) {\n    return context.getValue(BAGGAGE_KEY) || undefined;\n}\n/**\n * Retrieve the current baggage from the active/current context\n *\n * @returns {Baggage} Extracted baggage from the context\n */\nexport function getActiveBaggage() {\n    return getBaggage(ContextAPI.getInstance().active());\n}\n/**\n * Store a baggage in the given context\n *\n * @param {Context} Context that manage all context values\n * @param {Baggage} baggage that will be set in the actual context\n */\nexport function setBaggage(context, baggage) {\n    return context.setValue(BAGGAGE_KEY, baggage);\n}\n/**\n * Delete the baggage stored in the given context\n *\n * @param {Context} Context that manage all context values\n */\nexport function deleteBaggage(context) {\n    return context.deleteValue(BAGGAGE_KEY);\n}\n//# sourceMappingURL=context-helpers.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { getGlobal, registerGlobal, unregisterGlobal, } from '../internal/global-utils';\nimport { NoopTextMapPropagator } from '../propagation/NoopTextMapPropagator';\nimport { defaultTextMapGetter, defaultTextMapSetter, } from '../propagation/TextMapPropagator';\nimport { getBaggage, getActiveBaggage, setBaggage, deleteBaggage, } from '../baggage/context-helpers';\nimport { createBaggage } from '../baggage/utils';\nimport { DiagAPI } from './diag';\nvar API_NAME = 'propagation';\nvar NOOP_TEXT_MAP_PROPAGATOR = new NoopTextMapPropagator();\n/**\n * Singleton object which represents the entry point to the OpenTelemetry Propagation API\n */\nvar PropagationAPI = /** @class */ (function () {\n    /** Empty private constructor prevents end users from constructing a new instance of the API */\n    function PropagationAPI() {\n        this.createBaggage = createBaggage;\n        this.getBaggage = getBaggage;\n        this.getActiveBaggage = getActiveBaggage;\n        this.setBaggage = setBaggage;\n        this.deleteBaggage = deleteBaggage;\n    }\n    /** Get the singleton instance of the Propagator API */\n    PropagationAPI.getInstance = function () {\n        if (!this._instance) {\n            this._instance = new PropagationAPI();\n        }\n        return this._instance;\n    };\n    /**\n     * Set the current propagator.\n     *\n     * @returns true if the propagator was successfully registered, else false\n     */\n    PropagationAPI.prototype.setGlobalPropagator = function (propagator) {\n        return registerGlobal(API_NAME, propagator, DiagAPI.instance());\n    };\n    /**\n     * Inject context into a carrier to be propagated inter-process\n     *\n     * @param context Context carrying tracing data to inject\n     * @param carrier carrier to inject context into\n     * @param setter Function used to set values on the carrier\n     */\n    PropagationAPI.prototype.inject = function (context, carrier, setter) {\n        if (setter === void 0) { setter = defaultTextMapSetter; }\n        return this._getGlobalPropagator().inject(context, carrier, setter);\n    };\n    /**\n     * Extract context from a carrier\n     *\n     * @param context Context which the newly created context will inherit from\n     * @param carrier Carrier to extract context from\n     * @param getter Function used to extract keys from a carrier\n     */\n    PropagationAPI.prototype.extract = function (context, carrier, getter) {\n        if (getter === void 0) { getter = defaultTextMapGetter; }\n        return this._getGlobalPropagator().extract(context, carrier, getter);\n    };\n    /**\n     * Return a list of all fields which may be used by the propagator.\n     */\n    PropagationAPI.prototype.fields = function () {\n        return this._getGlobalPropagator().fields();\n    };\n    /** Remove the global propagator */\n    PropagationAPI.prototype.disable = function () {\n        unregisterGlobal(API_NAME, DiagAPI.instance());\n    };\n    PropagationAPI.prototype._getGlobalPropagator = function () {\n        return getGlobal(API_NAME) || NOOP_TEXT_MAP_PROPAGATOR;\n    };\n    return PropagationAPI;\n}());\nexport { PropagationAPI };\n//# sourceMappingURL=propagation.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// Split module-level variable definition into separate files to allow\n// tree-shaking on each api instance.\nimport { PropagationAPI } from './api/propagation';\n/** Entrypoint for propagation API */\nexport var propagation = PropagationAPI.getInstance();\n//# sourceMappingURL=propagation-api.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { getGlobal, registerGlobal, unregisterGlobal, } from '../internal/global-utils';\nimport { ProxyTracerProvider } from '../trace/ProxyTracerProvider';\nimport { isSpanContextValid, wrapSpanContext, } from '../trace/spancontext-utils';\nimport { deleteSpan, getActiveSpan, getSpan, getSpanContext, setSpan, setSpanContext, } from '../trace/context-utils';\nimport { DiagAPI } from './diag';\nvar API_NAME = 'trace';\n/**\n * Singleton object which represents the entry point to the OpenTelemetry Tracing API\n */\nvar TraceAPI = /** @class */ (function () {\n    /** Empty private constructor prevents end users from constructing a new instance of the API */\n    function TraceAPI() {\n        this._proxyTracerProvider = new ProxyTracerProvider();\n        this.wrapSpanContext = wrapSpanContext;\n        this.isSpanContextValid = isSpanContextValid;\n        this.deleteSpan = deleteSpan;\n        this.getSpan = getSpan;\n        this.getActiveSpan = getActiveSpan;\n        this.getSpanContext = getSpanContext;\n        this.setSpan = setSpan;\n        this.setSpanContext = setSpanContext;\n    }\n    /** Get the singleton instance of the Trace API */\n    TraceAPI.getInstance = function () {\n        if (!this._instance) {\n            this._instance = new TraceAPI();\n        }\n        return this._instance;\n    };\n    /**\n     * Set the current global tracer.\n     *\n     * @returns true if the tracer provider was successfully registered, else false\n     */\n    TraceAPI.prototype.setGlobalTracerProvider = function (provider) {\n        var success = registerGlobal(API_NAME, this._proxyTracerProvider, DiagAPI.instance());\n        if (success) {\n            this._proxyTracerProvider.setDelegate(provider);\n        }\n        return success;\n    };\n    /**\n     * Returns the global tracer provider.\n     */\n    TraceAPI.prototype.getTracerProvider = function () {\n        return getGlobal(API_NAME) || this._proxyTracerProvider;\n    };\n    /**\n     * Returns a tracer from the global tracer provider.\n     */\n    TraceAPI.prototype.getTracer = function (name, version) {\n        return this.getTracerProvider().getTracer(name, version);\n    };\n    /** Remove the global tracer provider */\n    TraceAPI.prototype.disable = function () {\n        unregisterGlobal(API_NAME, DiagAPI.instance());\n        this._proxyTracerProvider = new ProxyTracerProvider();\n    };\n    return TraceAPI;\n}());\nexport { TraceAPI };\n//# sourceMappingURL=trace.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// Split module-level variable definition into separate files to allow\n// tree-shaking on each api instance.\nimport { TraceAPI } from './api/trace';\n/** Entrypoint for trace API */\nexport var trace = TraceAPI.getInstance();\n//# sourceMappingURL=trace-api.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nexport { baggageEntryMetadataFromString } from './baggage/utils';\n// Context APIs\nexport { createContextKey, ROOT_CONTEXT } from './context/context';\n// Diag APIs\nexport { DiagConsoleLogger } from './diag/consoleLogger';\nexport { DiagLogLevel, } from './diag/types';\n// Metrics APIs\nexport { createNoopMeter } from './metrics/NoopMeter';\nexport { ValueType, } from './metrics/Metric';\n// Propagation APIs\nexport { defaultTextMapGetter, defaultTextMapSetter, } from './propagation/TextMapPropagator';\nexport { ProxyTracer } from './trace/ProxyTracer';\nexport { ProxyTracerProvider } from './trace/ProxyTracerProvider';\nexport { SamplingDecision } from './trace/SamplingResult';\nexport { SpanKind } from './trace/span_kind';\nexport { SpanStatusCode } from './trace/status';\nexport { TraceFlags } from './trace/trace_flags';\nexport { createTraceState } from './trace/internal/utils';\nexport { isSpanContextValid, isValidTraceId, isValidSpanId, } from './trace/spancontext-utils';\nexport { INVALID_SPANID, INVALID_TRACEID, INVALID_SPAN_CONTEXT, } from './trace/invalid-span-constants';\n// Split module-level variable definition into separate files to allow\n// tree-shaking on each api instance.\nimport { context } from './context-api';\nimport { diag } from './diag-api';\nimport { metrics } from './metrics-api';\nimport { propagation } from './propagation-api';\nimport { trace } from './trace-api';\n// Named export.\nexport { context, diag, metrics, propagation, trace };\n// Default export.\nexport default {\n    context: context,\n    diag: diag,\n    metrics: metrics,\n    propagation: propagation,\n    trace: trace,\n};\n//# sourceMappingURL=index.js.map", "const __WEBPACK_NAMESPACE_OBJECT__ = require(\"node:events\");", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nexport var SeverityNumber;\n(function (SeverityNumber) {\n    SeverityNumber[SeverityNumber[\"UNSPECIFIED\"] = 0] = \"UNSPECIFIED\";\n    SeverityNumber[SeverityNumber[\"TRACE\"] = 1] = \"TRACE\";\n    SeverityNumber[SeverityNumber[\"TRACE2\"] = 2] = \"TRACE2\";\n    SeverityNumber[SeverityNumber[\"TRACE3\"] = 3] = \"TRACE3\";\n    SeverityNumber[SeverityNumber[\"TRACE4\"] = 4] = \"TRACE4\";\n    SeverityNumber[SeverityNumber[\"DEBUG\"] = 5] = \"DEBUG\";\n    SeverityNumber[SeverityNumber[\"DEBUG2\"] = 6] = \"DEBUG2\";\n    SeverityNumber[SeverityNumber[\"DEBUG3\"] = 7] = \"DEBUG3\";\n    SeverityNumber[SeverityNumber[\"DEBUG4\"] = 8] = \"DEBUG4\";\n    SeverityNumber[SeverityNumber[\"INFO\"] = 9] = \"INFO\";\n    SeverityNumber[SeverityNumber[\"INFO2\"] = 10] = \"INFO2\";\n    SeverityNumber[SeverityNumber[\"INFO3\"] = 11] = \"INFO3\";\n    SeverityNumber[SeverityNumber[\"INFO4\"] = 12] = \"INFO4\";\n    SeverityNumber[SeverityNumber[\"WARN\"] = 13] = \"WARN\";\n    SeverityNumber[SeverityNumber[\"WARN2\"] = 14] = \"WARN2\";\n    SeverityNumber[SeverityNumber[\"WARN3\"] = 15] = \"WARN3\";\n    SeverityNumber[SeverityNumber[\"WARN4\"] = 16] = \"WARN4\";\n    SeverityNumber[SeverityNumber[\"ERROR\"] = 17] = \"ERROR\";\n    SeverityNumber[SeverityNumber[\"ERROR2\"] = 18] = \"ERROR2\";\n    SeverityNumber[SeverityNumber[\"ERROR3\"] = 19] = \"ERROR3\";\n    SeverityNumber[SeverityNumber[\"ERROR4\"] = 20] = \"ERROR4\";\n    SeverityNumber[SeverityNumber[\"FATAL\"] = 21] = \"FATAL\";\n    SeverityNumber[SeverityNumber[\"FATAL2\"] = 22] = \"FATAL2\";\n    SeverityNumber[SeverityNumber[\"FATAL3\"] = 23] = \"FATAL3\";\n    SeverityNumber[SeverityNumber[\"FATAL4\"] = 24] = \"FATAL4\";\n})(SeverityNumber || (SeverityNumber = {}));\n//# sourceMappingURL=LogRecord.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nexport class NoopLogger {\n    emit(_logRecord) { }\n}\nexport const NOOP_LOGGER = new NoopLogger();\n//# sourceMappingURL=NoopLogger.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { NoopLogger } from './NoopLogger';\nexport class NoopLoggerProvider {\n    getLogger(_name, _version, _options) {\n        return new NoopLogger();\n    }\n}\nexport const NOOP_LOGGER_PROVIDER = new NoopLoggerProvider();\n//# sourceMappingURL=NoopLoggerProvider.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { NOOP_LOGGER } from './NoopLogger';\nexport class ProxyLogger {\n    constructor(_provider, name, version, options) {\n        this._provider = _provider;\n        this.name = name;\n        this.version = version;\n        this.options = options;\n    }\n    /**\n     * Emit a log record. This method should only be used by log appenders.\n     *\n     * @param logRecord\n     */\n    emit(logRecord) {\n        this._getLogger().emit(logRecord);\n    }\n    /**\n     * Try to get a logger from the proxy logger provider.\n     * If the proxy logger provider has no delegate, return a noop logger.\n     */\n    _getLogger() {\n        if (this._delegate) {\n            return this._delegate;\n        }\n        const logger = this._provider.getDelegateLogger(this.name, this.version, this.options);\n        if (!logger) {\n            return NOOP_LOGGER;\n        }\n        this._delegate = logger;\n        return this._delegate;\n    }\n}\n//# sourceMappingURL=ProxyLogger.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { NOOP_LOGGER_PROVIDER } from './NoopLoggerProvider';\nimport { ProxyLogger } from './ProxyLogger';\nexport class ProxyLoggerProvider {\n    getLogger(name, version, options) {\n        var _a;\n        return ((_a = this.getDelegateLogger(name, version, options)) !== null && _a !== void 0 ? _a : new ProxyLogger(this, name, version, options));\n    }\n    getDelegate() {\n        var _a;\n        return (_a = this._delegate) !== null && _a !== void 0 ? _a : NOOP_LOGGER_PROVIDER;\n    }\n    /**\n     * Set the delegate logger provider\n     */\n    setDelegate(delegate) {\n        this._delegate = delegate;\n    }\n    getDelegateLogger(name, version, options) {\n        var _a;\n        return (_a = this._delegate) === null || _a === void 0 ? void 0 : _a.getLogger(name, version, options);\n    }\n}\n//# sourceMappingURL=ProxyLoggerProvider.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// Updates to this file should also be replicated to @opentelemetry/api and\n// @opentelemetry/core too.\n/**\n * - globalThis (New standard)\n * - self (Will return the current window instance for supported browsers)\n * - window (fallback for older browser implementations)\n * - global (NodeJS implementation)\n * - <object> (When all else fails)\n */\n/** only globals that common to node and browsers are allowed */\n// eslint-disable-next-line node/no-unsupported-features/es-builtins, no-undef\nexport const _globalThis = typeof globalThis === 'object'\n    ? globalThis\n    : typeof self === 'object'\n        ? self\n        : typeof window === 'object'\n            ? window\n            : typeof global === 'object'\n                ? global\n                : {};\n//# sourceMappingURL=globalThis.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { _globalThis } from '../platform';\nexport const GLOBAL_LOGS_API_KEY = Symbol.for('io.opentelemetry.js.api.logs');\nexport const _global = _globalThis;\n/**\n * Make a function which accepts a version integer and returns the instance of an API if the version\n * is compatible, or a fallback version (usually NOOP) if it is not.\n *\n * @param requiredVersion Backwards compatibility version which is required to return the instance\n * @param instance Instance which should be returned if the required version is compatible\n * @param fallback Fallback instance, usually NOOP, which will be returned if the required version is not compatible\n */\nexport function makeGetter(requiredVersion, instance, fallback) {\n    return (version) => version === requiredVersion ? instance : fallback;\n}\n/**\n * A number which should be incremented each time a backwards incompatible\n * change is made to the API. This number is used when an API package\n * attempts to access the global API to ensure it is getting a compatible\n * version. If the global API is not compatible with the API package\n * attempting to get it, a NOOP API implementation will be returned.\n */\nexport const API_BACKWARDS_COMPATIBILITY_VERSION = 1;\n//# sourceMappingURL=global-utils.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { API_BACKWARDS_COMPATIBILITY_VERSION, GLOBAL_LOGS_API_KEY, _global, makeGetter, } from '../internal/global-utils';\nimport { NOOP_LOGGER_PROVIDER } from '../NoopLoggerProvider';\nimport { ProxyLoggerProvider } from '../ProxyLoggerProvider';\nexport class LogsAPI {\n    constructor() {\n        this._proxyLoggerProvider = new ProxyLoggerProvider();\n    }\n    static getInstance() {\n        if (!this._instance) {\n            this._instance = new LogsAPI();\n        }\n        return this._instance;\n    }\n    setGlobalLoggerProvider(provider) {\n        if (_global[GLOBAL_LOGS_API_KEY]) {\n            return this.getLoggerProvider();\n        }\n        _global[GLOBAL_LOGS_API_KEY] = makeGetter(API_BACKWARDS_COMPATIBILITY_VERSION, provider, NOOP_LOGGER_PROVIDER);\n        this._proxyLoggerProvider.setDelegate(provider);\n        return provider;\n    }\n    /**\n     * Returns the global logger provider.\n     *\n     * @returns LoggerProvider\n     */\n    getLoggerProvider() {\n        var _a, _b;\n        return ((_b = (_a = _global[GLOBAL_LOGS_API_KEY]) === null || _a === void 0 ? void 0 : _a.call(_global, API_BACKWARDS_COMPATIBILITY_VERSION)) !== null && _b !== void 0 ? _b : this._proxyLoggerProvider);\n    }\n    /**\n     * Returns a logger from the global logger provider.\n     *\n     * @returns Logger\n     */\n    getLogger(name, version, options) {\n        return this.getLoggerProvider().getLogger(name, version, options);\n    }\n    /** Remove the global logger provider */\n    disable() {\n        delete _global[GLOBAL_LOGS_API_KEY];\n        this._proxyLoggerProvider = new ProxyLoggerProvider();\n    }\n}\n//# sourceMappingURL=logs.js.map", "/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nexport { SeverityNumber, } from './types/LogRecord';\nexport { NOOP_LOGGER, NoopLogger } from './NoopLogger';\nexport { NOOP_LOGGER_PROVIDER, NoopLoggerProvider } from './NoopLoggerProvider';\nexport { ProxyLogger } from './ProxyLogger';\nexport { ProxyLoggerProvider } from './ProxyLoggerProvider';\nimport { LogsAPI } from './api/logs';\nexport const logs = LogsAPI.getInstance();\n//# sourceMappingURL=index.js.map", "if (globalThis.performance === undefined) {\n    globalThis.performance = { timeOrigin: 0, now: () => Date.now() };\n  }\nvar Nu=Object.create;var xr=Object.defineProperty;var wu=Object.getOwnPropertyDescriptor;var Mu=Object.getOwnPropertyNames;var xu=Object.getPrototypeOf,Du=Object.prototype.hasOwnProperty;var ci=(e=>typeof require<\"u\"?require:typeof Proxy<\"u\"?new Proxy(e,{get:(t,r)=>(typeof require<\"u\"?require:t)[r]}):e)(function(e){if(typeof require<\"u\")return require.apply(this,arguments);throw Error('Dynamic require of \"'+e+'\" is not supported')});var Dr=(e,t)=>()=>(e&&(t=e(e=0)),t);var _=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports);var Se=(e,t,r,n)=>{if(t&&typeof t==\"object\"||typeof t==\"function\")for(let o of Mu(t))!Du.call(e,o)&&o!==r&&xr(e,o,{get:()=>t[o],enumerable:!(n=wu(t,o))||n.enumerable});return e},Y=(e,t,r)=>(Se(e,t,\"default\"),r&&Se(r,t,\"default\")),v=(e,t,r)=>(r=e!=null?Nu(xu(e)):{},Se(t||!e||!e.__esModule?xr(r,\"default\",{value:e,enumerable:!0}):r,e)),w=e=>Se(xr({},\"__esModule\",{value:!0}),e);var l={};import*as ed from\"@opentelemetry/api\";var E=Dr(()=>{Y(l,ed)});var ie=_(ft=>{\"use strict\";Object.defineProperty(ft,\"__esModule\",{value:!0});ft.isTracingSuppressed=ft.unsuppressTracing=ft.suppressTracing=void 0;var Uu=(E(),w(l)),Ur=(0,Uu.createContextKey)(\"OpenTelemetry SDK Context Key SUPPRESS_TRACING\");function Bu(e){return e.setValue(Ur,!0)}ft.suppressTracing=Bu;function Gu(e){return e.deleteValue(Ur)}ft.unsuppressTracing=Gu;function Vu(e){return e.getValue(Ur)===!0}ft.isTracingSuppressed=Vu});var Br=_(B=>{\"use strict\";Object.defineProperty(B,\"__esModule\",{value:!0});B.BAGGAGE_MAX_TOTAL_LENGTH=B.BAGGAGE_MAX_PER_NAME_VALUE_PAIRS=B.BAGGAGE_MAX_NAME_VALUE_PAIRS=B.BAGGAGE_HEADER=B.BAGGAGE_ITEMS_SEPARATOR=B.BAGGAGE_PROPERTIES_SEPARATOR=B.BAGGAGE_KEY_PAIR_SEPARATOR=void 0;B.BAGGAGE_KEY_PAIR_SEPARATOR=\"=\";B.BAGGAGE_PROPERTIES_SEPARATOR=\";\";B.BAGGAGE_ITEMS_SEPARATOR=\",\";B.BAGGAGE_HEADER=\"baggage\";B.BAGGAGE_MAX_NAME_VALUE_PAIRS=180;B.BAGGAGE_MAX_PER_NAME_VALUE_PAIRS=4096;B.BAGGAGE_MAX_TOTAL_LENGTH=8192});var Gr=_(rt=>{\"use strict\";Object.defineProperty(rt,\"__esModule\",{value:!0});rt.parseKeyPairsIntoRecord=rt.parsePairKeyValue=rt.getKeyPairs=rt.serializeKeyPairs=void 0;var Hu=(E(),w(l)),Lt=Br();function Fu(e){return e.reduce((t,r)=>{let n=`${t}${t!==\"\"?Lt.BAGGAGE_ITEMS_SEPARATOR:\"\"}${r}`;return n.length>Lt.BAGGAGE_MAX_TOTAL_LENGTH?t:n},\"\")}rt.serializeKeyPairs=Fu;function ju(e){return e.getAllEntries().map(([t,r])=>{let n=`${encodeURIComponent(t)}=${encodeURIComponent(r.value)}`;return r.metadata!==void 0&&(n+=Lt.BAGGAGE_PROPERTIES_SEPARATOR+r.metadata.toString()),n})}rt.getKeyPairs=ju;function li(e){let t=e.split(Lt.BAGGAGE_PROPERTIES_SEPARATOR);if(t.length<=0)return;let r=t.shift();if(!r)return;let n=r.indexOf(Lt.BAGGAGE_KEY_PAIR_SEPARATOR);if(n<=0)return;let o=decodeURIComponent(r.substring(0,n).trim()),i=decodeURIComponent(r.substring(n+1).trim()),s;return t.length>0&&(s=(0,Hu.baggageEntryMetadataFromString)(t.join(Lt.BAGGAGE_PROPERTIES_SEPARATOR))),{key:o,value:i,metadata:s}}rt.parsePairKeyValue=li;function ku(e){return typeof e!=\"string\"||e.length===0?{}:e.split(Lt.BAGGAGE_ITEMS_SEPARATOR).map(t=>li(t)).filter(t=>t!==void 0&&t.value.length>0).reduce((t,r)=>(t[r.key]=r.value,t),{})}rt.parseKeyPairsIntoRecord=ku});var fi=_(ge=>{\"use strict\";Object.defineProperty(ge,\"__esModule\",{value:!0});ge.W3CBaggagePropagator=void 0;var Vr=(E(),w(l)),qu=ie(),Pt=Br(),Hr=Gr(),Fr=class{inject(t,r,n){let o=Vr.propagation.getBaggage(t);if(!o||(0,qu.isTracingSuppressed)(t))return;let i=(0,Hr.getKeyPairs)(o).filter(u=>u.length<=Pt.BAGGAGE_MAX_PER_NAME_VALUE_PAIRS).slice(0,Pt.BAGGAGE_MAX_NAME_VALUE_PAIRS),s=(0,Hr.serializeKeyPairs)(i);s.length>0&&n.set(r,Pt.BAGGAGE_HEADER,s)}extract(t,r,n){let o=n.get(r,Pt.BAGGAGE_HEADER),i=Array.isArray(o)?o.join(Pt.BAGGAGE_ITEMS_SEPARATOR):o;if(!i)return t;let s={};return i.length===0||(i.split(Pt.BAGGAGE_ITEMS_SEPARATOR).forEach(c=>{let a=(0,Hr.parsePairKeyValue)(c);if(a){let f={value:a.value};a.metadata&&(f.metadata=a.metadata),s[a.key]=f}}),Object.entries(s).length===0)?t:Vr.propagation.setBaggage(t,Vr.propagation.createBaggage(s))}fields(){return[Pt.BAGGAGE_HEADER]}};ge.W3CBaggagePropagator=Fr});var pi=_(ye=>{\"use strict\";Object.defineProperty(ye,\"__esModule\",{value:!0});ye.AnchoredClock=void 0;var jr=class{constructor(t,r){this._monotonicClock=r,this._epochMillis=t.now(),this._performanceMillis=r.now()}now(){let t=this._monotonicClock.now()-this._performanceMillis;return this._epochMillis+t}};ye.AnchoredClock=jr});var mi=_(pt=>{\"use strict\";Object.defineProperty(pt,\"__esModule\",{value:!0});pt.isAttributeValue=pt.isAttributeKey=pt.sanitizeAttributes=void 0;var di=(E(),w(l));function Xu(e){let t={};if(typeof e!=\"object\"||e==null)return t;for(let[r,n]of Object.entries(e)){if(!_i(r)){di.diag.warn(`Invalid attribute key: ${r}`);continue}if(!hi(n)){di.diag.warn(`Invalid attribute value set for key: ${r}`);continue}Array.isArray(n)?t[r]=n.slice():t[r]=n}return t}pt.sanitizeAttributes=Xu;function _i(e){return typeof e==\"string\"&&e.length>0}pt.isAttributeKey=_i;function hi(e){return e==null?!0:Array.isArray(e)?Ku(e):Ei(e)}pt.isAttributeValue=hi;function Ku(e){let t;for(let r of e)if(r!=null){if(!t){if(Ei(r)){t=typeof r;continue}return!1}if(typeof r!==t)return!1}return!0}function Ei(e){switch(typeof e){case\"number\":case\"boolean\":case\"string\":return!0}return!1}});var kr=_(ve=>{\"use strict\";Object.defineProperty(ve,\"__esModule\",{value:!0});ve.loggingErrorHandler=void 0;var Wu=(E(),w(l));function Yu(){return e=>{Wu.diag.error(zu(e))}}ve.loggingErrorHandler=Yu;function zu(e){return typeof e==\"string\"?e:JSON.stringify($u(e))}function $u(e){let t={},r=e;for(;r!==null;)Object.getOwnPropertyNames(r).forEach(n=>{if(t[n])return;let o=r[n];o&&(t[n]=String(o))}),r=Object.getPrototypeOf(r);return t}});var qr=_(Vt=>{\"use strict\";Object.defineProperty(Vt,\"__esModule\",{value:!0});Vt.globalErrorHandler=Vt.setGlobalErrorHandler=void 0;var Qu=kr(),Ti=(0,Qu.loggingErrorHandler)();function Zu(e){Ti=e}Vt.setGlobalErrorHandler=Zu;function Ju(e){try{Ti(e)}catch{}}Vt.globalErrorHandler=Ju});var Xr=_(oe=>{\"use strict\";Object.defineProperty(oe,\"__esModule\",{value:!0});oe.TracesSamplerValues=void 0;var tc;(function(e){e.AlwaysOff=\"always_off\",e.AlwaysOn=\"always_on\",e.ParentBasedAlwaysOff=\"parentbased_always_off\",e.ParentBasedAlwaysOn=\"parentbased_always_on\",e.ParentBasedTraceIdRatio=\"parentbased_traceidratio\",e.TraceIdRatio=\"traceidratio\"})(tc=oe.TracesSamplerValues||(oe.TracesSamplerValues={}))});var Re=_(Ae=>{\"use strict\";Object.defineProperty(Ae,\"__esModule\",{value:!0});Ae._globalThis=void 0;Ae._globalThis=typeof globalThis==\"object\"?globalThis:typeof self==\"object\"?self:typeof window==\"object\"?window:typeof global==\"object\"?global:{}});var Oe=_(L=>{\"use strict\";Object.defineProperty(L,\"__esModule\",{value:!0});L.getEnvWithoutDefaults=L.parseEnvironment=L.DEFAULT_ENVIRONMENT=L.DEFAULT_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT=L.DEFAULT_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT=L.DEFAULT_ATTRIBUTE_COUNT_LIMIT=L.DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT=void 0;var dt=(E(),w(l)),ec=Xr(),rc=Re(),nc=\",\",ic=[\"OTEL_SDK_DISABLED\"];function oc(e){return ic.indexOf(e)>-1}var ac=[\"OTEL_BSP_EXPORT_TIMEOUT\",\"OTEL_BSP_MAX_EXPORT_BATCH_SIZE\",\"OTEL_BSP_MAX_QUEUE_SIZE\",\"OTEL_BSP_SCHEDULE_DELAY\",\"OTEL_BLRP_EXPORT_TIMEOUT\",\"OTEL_BLRP_MAX_EXPORT_BATCH_SIZE\",\"OTEL_BLRP_MAX_QUEUE_SIZE\",\"OTEL_BLRP_SCHEDULE_DELAY\",\"OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT\",\"OTEL_ATTRIBUTE_COUNT_LIMIT\",\"OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT\",\"OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT\",\"OTEL_LOGRECORD_ATTRIBUTE_VALUE_LENGTH_LIMIT\",\"OTEL_LOGRECORD_ATTRIBUTE_COUNT_LIMIT\",\"OTEL_SPAN_EVENT_COUNT_LIMIT\",\"OTEL_SPAN_LINK_COUNT_LIMIT\",\"OTEL_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT\",\"OTEL_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT\",\"OTEL_EXPORTER_OTLP_TIMEOUT\",\"OTEL_EXPORTER_OTLP_TRACES_TIMEOUT\",\"OTEL_EXPORTER_OTLP_METRICS_TIMEOUT\",\"OTEL_EXPORTER_OTLP_LOGS_TIMEOUT\",\"OTEL_EXPORTER_JAEGER_AGENT_PORT\"];function sc(e){return ac.indexOf(e)>-1}var uc=[\"OTEL_NO_PATCH_MODULES\",\"OTEL_PROPAGATORS\"];function cc(e){return uc.indexOf(e)>-1}L.DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT=1/0;L.DEFAULT_ATTRIBUTE_COUNT_LIMIT=128;L.DEFAULT_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT=128;L.DEFAULT_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT=128;L.DEFAULT_ENVIRONMENT={OTEL_SDK_DISABLED:!1,CONTAINER_NAME:\"\",ECS_CONTAINER_METADATA_URI_V4:\"\",ECS_CONTAINER_METADATA_URI:\"\",HOSTNAME:\"\",KUBERNETES_SERVICE_HOST:\"\",NAMESPACE:\"\",OTEL_BSP_EXPORT_TIMEOUT:3e4,OTEL_BSP_MAX_EXPORT_BATCH_SIZE:512,OTEL_BSP_MAX_QUEUE_SIZE:2048,OTEL_BSP_SCHEDULE_DELAY:5e3,OTEL_BLRP_EXPORT_TIMEOUT:3e4,OTEL_BLRP_MAX_EXPORT_BATCH_SIZE:512,OTEL_BLRP_MAX_QUEUE_SIZE:2048,OTEL_BLRP_SCHEDULE_DELAY:5e3,OTEL_EXPORTER_JAEGER_AGENT_HOST:\"\",OTEL_EXPORTER_JAEGER_AGENT_PORT:6832,OTEL_EXPORTER_JAEGER_ENDPOINT:\"\",OTEL_EXPORTER_JAEGER_PASSWORD:\"\",OTEL_EXPORTER_JAEGER_USER:\"\",OTEL_EXPORTER_OTLP_ENDPOINT:\"\",OTEL_EXPORTER_OTLP_TRACES_ENDPOINT:\"\",OTEL_EXPORTER_OTLP_METRICS_ENDPOINT:\"\",OTEL_EXPORTER_OTLP_LOGS_ENDPOINT:\"\",OTEL_EXPORTER_OTLP_HEADERS:\"\",OTEL_EXPORTER_OTLP_TRACES_HEADERS:\"\",OTEL_EXPORTER_OTLP_METRICS_HEADERS:\"\",OTEL_EXPORTER_OTLP_LOGS_HEADERS:\"\",OTEL_EXPORTER_OTLP_TIMEOUT:1e4,OTEL_EXPORTER_OTLP_TRACES_TIMEOUT:1e4,OTEL_EXPORTER_OTLP_METRICS_TIMEOUT:1e4,OTEL_EXPORTER_OTLP_LOGS_TIMEOUT:1e4,OTEL_EXPORTER_ZIPKIN_ENDPOINT:\"http://localhost:9411/api/v2/spans\",OTEL_LOG_LEVEL:dt.DiagLogLevel.INFO,OTEL_NO_PATCH_MODULES:[],OTEL_PROPAGATORS:[\"tracecontext\",\"baggage\"],OTEL_RESOURCE_ATTRIBUTES:\"\",OTEL_SERVICE_NAME:\"\",OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT:L.DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT,OTEL_ATTRIBUTE_COUNT_LIMIT:L.DEFAULT_ATTRIBUTE_COUNT_LIMIT,OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT:L.DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT,OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT:L.DEFAULT_ATTRIBUTE_COUNT_LIMIT,OTEL_LOGRECORD_ATTRIBUTE_VALUE_LENGTH_LIMIT:L.DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT,OTEL_LOGRECORD_ATTRIBUTE_COUNT_LIMIT:L.DEFAULT_ATTRIBUTE_COUNT_LIMIT,OTEL_SPAN_EVENT_COUNT_LIMIT:128,OTEL_SPAN_LINK_COUNT_LIMIT:128,OTEL_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT:L.DEFAULT_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT,OTEL_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT:L.DEFAULT_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT,OTEL_TRACES_EXPORTER:\"\",OTEL_TRACES_SAMPLER:ec.TracesSamplerValues.ParentBasedAlwaysOn,OTEL_TRACES_SAMPLER_ARG:\"\",OTEL_LOGS_EXPORTER:\"\",OTEL_EXPORTER_OTLP_INSECURE:\"\",OTEL_EXPORTER_OTLP_TRACES_INSECURE:\"\",OTEL_EXPORTER_OTLP_METRICS_INSECURE:\"\",OTEL_EXPORTER_OTLP_LOGS_INSECURE:\"\",OTEL_EXPORTER_OTLP_CERTIFICATE:\"\",OTEL_EXPORTER_OTLP_TRACES_CERTIFICATE:\"\",OTEL_EXPORTER_OTLP_METRICS_CERTIFICATE:\"\",OTEL_EXPORTER_OTLP_LOGS_CERTIFICATE:\"\",OTEL_EXPORTER_OTLP_COMPRESSION:\"\",OTEL_EXPORTER_OTLP_TRACES_COMPRESSION:\"\",OTEL_EXPORTER_OTLP_METRICS_COMPRESSION:\"\",OTEL_EXPORTER_OTLP_LOGS_COMPRESSION:\"\",OTEL_EXPORTER_OTLP_CLIENT_KEY:\"\",OTEL_EXPORTER_OTLP_TRACES_CLIENT_KEY:\"\",OTEL_EXPORTER_OTLP_METRICS_CLIENT_KEY:\"\",OTEL_EXPORTER_OTLP_LOGS_CLIENT_KEY:\"\",OTEL_EXPORTER_OTLP_CLIENT_CERTIFICATE:\"\",OTEL_EXPORTER_OTLP_TRACES_CLIENT_CERTIFICATE:\"\",OTEL_EXPORTER_OTLP_METRICS_CLIENT_CERTIFICATE:\"\",OTEL_EXPORTER_OTLP_LOGS_CLIENT_CERTIFICATE:\"\",OTEL_EXPORTER_OTLP_PROTOCOL:\"http/protobuf\",OTEL_EXPORTER_OTLP_TRACES_PROTOCOL:\"http/protobuf\",OTEL_EXPORTER_OTLP_METRICS_PROTOCOL:\"http/protobuf\",OTEL_EXPORTER_OTLP_LOGS_PROTOCOL:\"http/protobuf\",OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE:\"cumulative\"};function lc(e,t,r){if(typeof r[e]>\"u\")return;let n=String(r[e]);t[e]=n.toLowerCase()===\"true\"}function fc(e,t,r,n=-1/0,o=1/0){if(typeof r[e]<\"u\"){let i=Number(r[e]);isNaN(i)||(i<n?t[e]=n:i>o?t[e]=o:t[e]=i)}}function pc(e,t,r,n=nc){let o=r[e];typeof o==\"string\"&&(t[e]=o.split(n).map(i=>i.trim()))}var dc={ALL:dt.DiagLogLevel.ALL,VERBOSE:dt.DiagLogLevel.VERBOSE,DEBUG:dt.DiagLogLevel.DEBUG,INFO:dt.DiagLogLevel.INFO,WARN:dt.DiagLogLevel.WARN,ERROR:dt.DiagLogLevel.ERROR,NONE:dt.DiagLogLevel.NONE};function _c(e,t,r){let n=r[e];if(typeof n==\"string\"){let o=dc[n.toUpperCase()];o!=null&&(t[e]=o)}}function Kr(e){let t={};for(let r in L.DEFAULT_ENVIRONMENT){let n=r;switch(n){case\"OTEL_LOG_LEVEL\":_c(n,t,e);break;default:if(oc(n))lc(n,t,e);else if(sc(n))fc(n,t,e);else if(cc(n))pc(n,t,e);else{let o=e[n];typeof o<\"u\"&&o!==null&&(t[n]=String(o))}}}return t}L.parseEnvironment=Kr;function hc(){return typeof process<\"u\"&&process&&process.env?Kr(process.env):Kr(rc._globalThis)}L.getEnvWithoutDefaults=hc});var gi=_(be=>{\"use strict\";Object.defineProperty(be,\"__esModule\",{value:!0});be.getEnv=void 0;var Si=Oe(),Ec=Re();function mc(){let e=(0,Si.parseEnvironment)(Ec._globalThis);return Object.assign({},Si.DEFAULT_ENVIRONMENT,e)}be.getEnv=mc});var yi=_(Le=>{\"use strict\";Object.defineProperty(Le,\"__esModule\",{value:!0});Le.hexToBase64=void 0;function Tc(e){let t=e.length,r=\"\";for(let n=0;n<t;n+=2){let o=e.substring(n,n+2),i=parseInt(o,16);r+=String.fromCharCode(i)}return btoa(r)}Le.hexToBase64=Tc});var Ai=_(Ie=>{\"use strict\";Object.defineProperty(Ie,\"__esModule\",{value:!0});Ie.RandomIdGenerator=void 0;var Sc=8,gc=16,Wr=class{constructor(){this.generateTraceId=vi(gc),this.generateSpanId=vi(Sc)}};Ie.RandomIdGenerator=Wr;var Pe=Array(32);function vi(e){return function(){for(let r=0;r<e*2;r++)Pe[r]=Math.floor(Math.random()*16)+48,Pe[r]>=58&&(Pe[r]+=39);return String.fromCharCode.apply(null,Pe.slice(0,e*2))}}});var Ri=_(Ce=>{\"use strict\";Object.defineProperty(Ce,\"__esModule\",{value:!0});Ce.otperformance=void 0;Ce.otperformance=performance});var Yr=_(Ne=>{\"use strict\";Object.defineProperty(Ne,\"__esModule\",{value:!0});Ne.VERSION=void 0;Ne.VERSION=\"1.19.0\"});var Oi=_(A=>{\"use strict\";Object.defineProperty(A,\"__esModule\",{value:!0});A.MessageTypeValues=A.RpcGrpcStatusCodeValues=A.MessagingOperationValues=A.MessagingDestinationKindValues=A.HttpFlavorValues=A.NetHostConnectionSubtypeValues=A.NetHostConnectionTypeValues=A.NetTransportValues=A.FaasInvokedProviderValues=A.FaasDocumentOperationValues=A.FaasTriggerValues=A.DbCassandraConsistencyLevelValues=A.DbSystemValues=A.SemanticAttributes=void 0;A.SemanticAttributes={AWS_LAMBDA_INVOKED_ARN:\"aws.lambda.invoked_arn\",DB_SYSTEM:\"db.system\",DB_CONNECTION_STRING:\"db.connection_string\",DB_USER:\"db.user\",DB_JDBC_DRIVER_CLASSNAME:\"db.jdbc.driver_classname\",DB_NAME:\"db.name\",DB_STATEMENT:\"db.statement\",DB_OPERATION:\"db.operation\",DB_MSSQL_INSTANCE_NAME:\"db.mssql.instance_name\",DB_CASSANDRA_KEYSPACE:\"db.cassandra.keyspace\",DB_CASSANDRA_PAGE_SIZE:\"db.cassandra.page_size\",DB_CASSANDRA_CONSISTENCY_LEVEL:\"db.cassandra.consistency_level\",DB_CASSANDRA_TABLE:\"db.cassandra.table\",DB_CASSANDRA_IDEMPOTENCE:\"db.cassandra.idempotence\",DB_CASSANDRA_SPECULATIVE_EXECUTION_COUNT:\"db.cassandra.speculative_execution_count\",DB_CASSANDRA_COORDINATOR_ID:\"db.cassandra.coordinator.id\",DB_CASSANDRA_COORDINATOR_DC:\"db.cassandra.coordinator.dc\",DB_HBASE_NAMESPACE:\"db.hbase.namespace\",DB_REDIS_DATABASE_INDEX:\"db.redis.database_index\",DB_MONGODB_COLLECTION:\"db.mongodb.collection\",DB_SQL_TABLE:\"db.sql.table\",EXCEPTION_TYPE:\"exception.type\",EXCEPTION_MESSAGE:\"exception.message\",EXCEPTION_STACKTRACE:\"exception.stacktrace\",EXCEPTION_ESCAPED:\"exception.escaped\",FAAS_TRIGGER:\"faas.trigger\",FAAS_EXECUTION:\"faas.execution\",FAAS_DOCUMENT_COLLECTION:\"faas.document.collection\",FAAS_DOCUMENT_OPERATION:\"faas.document.operation\",FAAS_DOCUMENT_TIME:\"faas.document.time\",FAAS_DOCUMENT_NAME:\"faas.document.name\",FAAS_TIME:\"faas.time\",FAAS_CRON:\"faas.cron\",FAAS_COLDSTART:\"faas.coldstart\",FAAS_INVOKED_NAME:\"faas.invoked_name\",FAAS_INVOKED_PROVIDER:\"faas.invoked_provider\",FAAS_INVOKED_REGION:\"faas.invoked_region\",NET_TRANSPORT:\"net.transport\",NET_PEER_IP:\"net.peer.ip\",NET_PEER_PORT:\"net.peer.port\",NET_PEER_NAME:\"net.peer.name\",NET_HOST_IP:\"net.host.ip\",NET_HOST_PORT:\"net.host.port\",NET_HOST_NAME:\"net.host.name\",NET_HOST_CONNECTION_TYPE:\"net.host.connection.type\",NET_HOST_CONNECTION_SUBTYPE:\"net.host.connection.subtype\",NET_HOST_CARRIER_NAME:\"net.host.carrier.name\",NET_HOST_CARRIER_MCC:\"net.host.carrier.mcc\",NET_HOST_CARRIER_MNC:\"net.host.carrier.mnc\",NET_HOST_CARRIER_ICC:\"net.host.carrier.icc\",PEER_SERVICE:\"peer.service\",ENDUSER_ID:\"enduser.id\",ENDUSER_ROLE:\"enduser.role\",ENDUSER_SCOPE:\"enduser.scope\",THREAD_ID:\"thread.id\",THREAD_NAME:\"thread.name\",CODE_FUNCTION:\"code.function\",CODE_NAMESPACE:\"code.namespace\",CODE_FILEPATH:\"code.filepath\",CODE_LINENO:\"code.lineno\",HTTP_METHOD:\"http.method\",HTTP_URL:\"http.url\",HTTP_TARGET:\"http.target\",HTTP_HOST:\"http.host\",HTTP_SCHEME:\"http.scheme\",HTTP_STATUS_CODE:\"http.status_code\",HTTP_FLAVOR:\"http.flavor\",HTTP_USER_AGENT:\"http.user_agent\",HTTP_REQUEST_CONTENT_LENGTH:\"http.request_content_length\",HTTP_REQUEST_CONTENT_LENGTH_UNCOMPRESSED:\"http.request_content_length_uncompressed\",HTTP_RESPONSE_CONTENT_LENGTH:\"http.response_content_length\",HTTP_RESPONSE_CONTENT_LENGTH_UNCOMPRESSED:\"http.response_content_length_uncompressed\",HTTP_SERVER_NAME:\"http.server_name\",HTTP_ROUTE:\"http.route\",HTTP_CLIENT_IP:\"http.client_ip\",AWS_DYNAMODB_TABLE_NAMES:\"aws.dynamodb.table_names\",AWS_DYNAMODB_CONSUMED_CAPACITY:\"aws.dynamodb.consumed_capacity\",AWS_DYNAMODB_ITEM_COLLECTION_METRICS:\"aws.dynamodb.item_collection_metrics\",AWS_DYNAMODB_PROVISIONED_READ_CAPACITY:\"aws.dynamodb.provisioned_read_capacity\",AWS_DYNAMODB_PROVISIONED_WRITE_CAPACITY:\"aws.dynamodb.provisioned_write_capacity\",AWS_DYNAMODB_CONSISTENT_READ:\"aws.dynamodb.consistent_read\",AWS_DYNAMODB_PROJECTION:\"aws.dynamodb.projection\",AWS_DYNAMODB_LIMIT:\"aws.dynamodb.limit\",AWS_DYNAMODB_ATTRIBUTES_TO_GET:\"aws.dynamodb.attributes_to_get\",AWS_DYNAMODB_INDEX_NAME:\"aws.dynamodb.index_name\",AWS_DYNAMODB_SELECT:\"aws.dynamodb.select\",AWS_DYNAMODB_GLOBAL_SECONDARY_INDEXES:\"aws.dynamodb.global_secondary_indexes\",AWS_DYNAMODB_LOCAL_SECONDARY_INDEXES:\"aws.dynamodb.local_secondary_indexes\",AWS_DYNAMODB_EXCLUSIVE_START_TABLE:\"aws.dynamodb.exclusive_start_table\",AWS_DYNAMODB_TABLE_COUNT:\"aws.dynamodb.table_count\",AWS_DYNAMODB_SCAN_FORWARD:\"aws.dynamodb.scan_forward\",AWS_DYNAMODB_SEGMENT:\"aws.dynamodb.segment\",AWS_DYNAMODB_TOTAL_SEGMENTS:\"aws.dynamodb.total_segments\",AWS_DYNAMODB_COUNT:\"aws.dynamodb.count\",AWS_DYNAMODB_SCANNED_COUNT:\"aws.dynamodb.scanned_count\",AWS_DYNAMODB_ATTRIBUTE_DEFINITIONS:\"aws.dynamodb.attribute_definitions\",AWS_DYNAMODB_GLOBAL_SECONDARY_INDEX_UPDATES:\"aws.dynamodb.global_secondary_index_updates\",MESSAGING_SYSTEM:\"messaging.system\",MESSAGING_DESTINATION:\"messaging.destination\",MESSAGING_DESTINATION_KIND:\"messaging.destination_kind\",MESSAGING_TEMP_DESTINATION:\"messaging.temp_destination\",MESSAGING_PROTOCOL:\"messaging.protocol\",MESSAGING_PROTOCOL_VERSION:\"messaging.protocol_version\",MESSAGING_URL:\"messaging.url\",MESSAGING_MESSAGE_ID:\"messaging.message_id\",MESSAGING_CONVERSATION_ID:\"messaging.conversation_id\",MESSAGING_MESSAGE_PAYLOAD_SIZE_BYTES:\"messaging.message_payload_size_bytes\",MESSAGING_MESSAGE_PAYLOAD_COMPRESSED_SIZE_BYTES:\"messaging.message_payload_compressed_size_bytes\",MESSAGING_OPERATION:\"messaging.operation\",MESSAGING_CONSUMER_ID:\"messaging.consumer_id\",MESSAGING_RABBITMQ_ROUTING_KEY:\"messaging.rabbitmq.routing_key\",MESSAGING_KAFKA_MESSAGE_KEY:\"messaging.kafka.message_key\",MESSAGING_KAFKA_CONSUMER_GROUP:\"messaging.kafka.consumer_group\",MESSAGING_KAFKA_CLIENT_ID:\"messaging.kafka.client_id\",MESSAGING_KAFKA_PARTITION:\"messaging.kafka.partition\",MESSAGING_KAFKA_TOMBSTONE:\"messaging.kafka.tombstone\",RPC_SYSTEM:\"rpc.system\",RPC_SERVICE:\"rpc.service\",RPC_METHOD:\"rpc.method\",RPC_GRPC_STATUS_CODE:\"rpc.grpc.status_code\",RPC_JSONRPC_VERSION:\"rpc.jsonrpc.version\",RPC_JSONRPC_REQUEST_ID:\"rpc.jsonrpc.request_id\",RPC_JSONRPC_ERROR_CODE:\"rpc.jsonrpc.error_code\",RPC_JSONRPC_ERROR_MESSAGE:\"rpc.jsonrpc.error_message\",MESSAGE_TYPE:\"message.type\",MESSAGE_ID:\"message.id\",MESSAGE_COMPRESSED_SIZE:\"message.compressed_size\",MESSAGE_UNCOMPRESSED_SIZE:\"message.uncompressed_size\"};A.DbSystemValues={OTHER_SQL:\"other_sql\",MSSQL:\"mssql\",MYSQL:\"mysql\",ORACLE:\"oracle\",DB2:\"db2\",POSTGRESQL:\"postgresql\",REDSHIFT:\"redshift\",HIVE:\"hive\",CLOUDSCAPE:\"cloudscape\",HSQLDB:\"hsqldb\",PROGRESS:\"progress\",MAXDB:\"maxdb\",HANADB:\"hanadb\",INGRES:\"ingres\",FIRSTSQL:\"firstsql\",EDB:\"edb\",CACHE:\"cache\",ADABAS:\"adabas\",FIREBIRD:\"firebird\",DERBY:\"derby\",FILEMAKER:\"filemaker\",INFORMIX:\"informix\",INSTANTDB:\"instantdb\",INTERBASE:\"interbase\",MARIADB:\"mariadb\",NETEZZA:\"netezza\",PERVASIVE:\"pervasive\",POINTBASE:\"pointbase\",SQLITE:\"sqlite\",SYBASE:\"sybase\",TERADATA:\"teradata\",VERTICA:\"vertica\",H2:\"h2\",COLDFUSION:\"coldfusion\",CASSANDRA:\"cassandra\",HBASE:\"hbase\",MONGODB:\"mongodb\",REDIS:\"redis\",COUCHBASE:\"couchbase\",COUCHDB:\"couchdb\",COSMOSDB:\"cosmosdb\",DYNAMODB:\"dynamodb\",NEO4J:\"neo4j\",GEODE:\"geode\",ELASTICSEARCH:\"elasticsearch\",MEMCACHED:\"memcached\",COCKROACHDB:\"cockroachdb\"};A.DbCassandraConsistencyLevelValues={ALL:\"all\",EACH_QUORUM:\"each_quorum\",QUORUM:\"quorum\",LOCAL_QUORUM:\"local_quorum\",ONE:\"one\",TWO:\"two\",THREE:\"three\",LOCAL_ONE:\"local_one\",ANY:\"any\",SERIAL:\"serial\",LOCAL_SERIAL:\"local_serial\"};A.FaasTriggerValues={DATASOURCE:\"datasource\",HTTP:\"http\",PUBSUB:\"pubsub\",TIMER:\"timer\",OTHER:\"other\"};A.FaasDocumentOperationValues={INSERT:\"insert\",EDIT:\"edit\",DELETE:\"delete\"};A.FaasInvokedProviderValues={ALIBABA_CLOUD:\"alibaba_cloud\",AWS:\"aws\",AZURE:\"azure\",GCP:\"gcp\"};A.NetTransportValues={IP_TCP:\"ip_tcp\",IP_UDP:\"ip_udp\",IP:\"ip\",UNIX:\"unix\",PIPE:\"pipe\",INPROC:\"inproc\",OTHER:\"other\"};A.NetHostConnectionTypeValues={WIFI:\"wifi\",WIRED:\"wired\",CELL:\"cell\",UNAVAILABLE:\"unavailable\",UNKNOWN:\"unknown\"};A.NetHostConnectionSubtypeValues={GPRS:\"gprs\",EDGE:\"edge\",UMTS:\"umts\",CDMA:\"cdma\",EVDO_0:\"evdo_0\",EVDO_A:\"evdo_a\",CDMA2000_1XRTT:\"cdma2000_1xrtt\",HSDPA:\"hsdpa\",HSUPA:\"hsupa\",HSPA:\"hspa\",IDEN:\"iden\",EVDO_B:\"evdo_b\",LTE:\"lte\",EHRPD:\"ehrpd\",HSPAP:\"hspap\",GSM:\"gsm\",TD_SCDMA:\"td_scdma\",IWLAN:\"iwlan\",NR:\"nr\",NRNSA:\"nrnsa\",LTE_CA:\"lte_ca\"};A.HttpFlavorValues={HTTP_1_0:\"1.0\",HTTP_1_1:\"1.1\",HTTP_2_0:\"2.0\",SPDY:\"SPDY\",QUIC:\"QUIC\"};A.MessagingDestinationKindValues={QUEUE:\"queue\",TOPIC:\"topic\"};A.MessagingOperationValues={RECEIVE:\"receive\",PROCESS:\"process\"};A.RpcGrpcStatusCodeValues={OK:0,CANCELLED:1,UNKNOWN:2,INVALID_ARGUMENT:3,DEADLINE_EXCEEDED:4,NOT_FOUND:5,ALREADY_EXISTS:6,PERMISSION_DENIED:7,RESOURCE_EXHAUSTED:8,FAILED_PRECONDITION:9,ABORTED:10,OUT_OF_RANGE:11,UNIMPLEMENTED:12,INTERNAL:13,UNAVAILABLE:14,DATA_LOSS:15,UNAUTHENTICATED:16};A.MessageTypeValues={SENT:\"SENT\",RECEIVED:\"RECEIVED\"}});var bi=_(It=>{\"use strict\";var yc=It&&It.__createBinding||(Object.create?function(e,t,r,n){n===void 0&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){n===void 0&&(n=r),e[n]=t[r]}),vc=It&&It.__exportStar||function(e,t){for(var r in e)r!==\"default\"&&!Object.prototype.hasOwnProperty.call(t,r)&&yc(t,e,r)};Object.defineProperty(It,\"__esModule\",{value:!0});vc(Oi(),It)});var Li=_(G=>{\"use strict\";Object.defineProperty(G,\"__esModule\",{value:!0});G.TelemetrySdkLanguageValues=G.OsTypeValues=G.HostArchValues=G.AwsEcsLaunchtypeValues=G.CloudPlatformValues=G.CloudProviderValues=G.SemanticResourceAttributes=void 0;G.SemanticResourceAttributes={CLOUD_PROVIDER:\"cloud.provider\",CLOUD_ACCOUNT_ID:\"cloud.account.id\",CLOUD_REGION:\"cloud.region\",CLOUD_AVAILABILITY_ZONE:\"cloud.availability_zone\",CLOUD_PLATFORM:\"cloud.platform\",AWS_ECS_CONTAINER_ARN:\"aws.ecs.container.arn\",AWS_ECS_CLUSTER_ARN:\"aws.ecs.cluster.arn\",AWS_ECS_LAUNCHTYPE:\"aws.ecs.launchtype\",AWS_ECS_TASK_ARN:\"aws.ecs.task.arn\",AWS_ECS_TASK_FAMILY:\"aws.ecs.task.family\",AWS_ECS_TASK_REVISION:\"aws.ecs.task.revision\",AWS_EKS_CLUSTER_ARN:\"aws.eks.cluster.arn\",AWS_LOG_GROUP_NAMES:\"aws.log.group.names\",AWS_LOG_GROUP_ARNS:\"aws.log.group.arns\",AWS_LOG_STREAM_NAMES:\"aws.log.stream.names\",AWS_LOG_STREAM_ARNS:\"aws.log.stream.arns\",CONTAINER_NAME:\"container.name\",CONTAINER_ID:\"container.id\",CONTAINER_RUNTIME:\"container.runtime\",CONTAINER_IMAGE_NAME:\"container.image.name\",CONTAINER_IMAGE_TAG:\"container.image.tag\",DEPLOYMENT_ENVIRONMENT:\"deployment.environment\",DEVICE_ID:\"device.id\",DEVICE_MODEL_IDENTIFIER:\"device.model.identifier\",DEVICE_MODEL_NAME:\"device.model.name\",FAAS_NAME:\"faas.name\",FAAS_ID:\"faas.id\",FAAS_VERSION:\"faas.version\",FAAS_INSTANCE:\"faas.instance\",FAAS_MAX_MEMORY:\"faas.max_memory\",HOST_ID:\"host.id\",HOST_NAME:\"host.name\",HOST_TYPE:\"host.type\",HOST_ARCH:\"host.arch\",HOST_IMAGE_NAME:\"host.image.name\",HOST_IMAGE_ID:\"host.image.id\",HOST_IMAGE_VERSION:\"host.image.version\",K8S_CLUSTER_NAME:\"k8s.cluster.name\",K8S_NODE_NAME:\"k8s.node.name\",K8S_NODE_UID:\"k8s.node.uid\",K8S_NAMESPACE_NAME:\"k8s.namespace.name\",K8S_POD_UID:\"k8s.pod.uid\",K8S_POD_NAME:\"k8s.pod.name\",K8S_CONTAINER_NAME:\"k8s.container.name\",K8S_REPLICASET_UID:\"k8s.replicaset.uid\",K8S_REPLICASET_NAME:\"k8s.replicaset.name\",K8S_DEPLOYMENT_UID:\"k8s.deployment.uid\",K8S_DEPLOYMENT_NAME:\"k8s.deployment.name\",K8S_STATEFULSET_UID:\"k8s.statefulset.uid\",K8S_STATEFULSET_NAME:\"k8s.statefulset.name\",K8S_DAEMONSET_UID:\"k8s.daemonset.uid\",K8S_DAEMONSET_NAME:\"k8s.daemonset.name\",K8S_JOB_UID:\"k8s.job.uid\",K8S_JOB_NAME:\"k8s.job.name\",K8S_CRONJOB_UID:\"k8s.cronjob.uid\",K8S_CRONJOB_NAME:\"k8s.cronjob.name\",OS_TYPE:\"os.type\",OS_DESCRIPTION:\"os.description\",OS_NAME:\"os.name\",OS_VERSION:\"os.version\",PROCESS_PID:\"process.pid\",PROCESS_EXECUTABLE_NAME:\"process.executable.name\",PROCESS_EXECUTABLE_PATH:\"process.executable.path\",PROCESS_COMMAND:\"process.command\",PROCESS_COMMAND_LINE:\"process.command_line\",PROCESS_COMMAND_ARGS:\"process.command_args\",PROCESS_OWNER:\"process.owner\",PROCESS_RUNTIME_NAME:\"process.runtime.name\",PROCESS_RUNTIME_VERSION:\"process.runtime.version\",PROCESS_RUNTIME_DESCRIPTION:\"process.runtime.description\",SERVICE_NAME:\"service.name\",SERVICE_NAMESPACE:\"service.namespace\",SERVICE_INSTANCE_ID:\"service.instance.id\",SERVICE_VERSION:\"service.version\",TELEMETRY_SDK_NAME:\"telemetry.sdk.name\",TELEMETRY_SDK_LANGUAGE:\"telemetry.sdk.language\",TELEMETRY_SDK_VERSION:\"telemetry.sdk.version\",TELEMETRY_AUTO_VERSION:\"telemetry.auto.version\",WEBENGINE_NAME:\"webengine.name\",WEBENGINE_VERSION:\"webengine.version\",WEBENGINE_DESCRIPTION:\"webengine.description\"};G.CloudProviderValues={ALIBABA_CLOUD:\"alibaba_cloud\",AWS:\"aws\",AZURE:\"azure\",GCP:\"gcp\"};G.CloudPlatformValues={ALIBABA_CLOUD_ECS:\"alibaba_cloud_ecs\",ALIBABA_CLOUD_FC:\"alibaba_cloud_fc\",AWS_EC2:\"aws_ec2\",AWS_ECS:\"aws_ecs\",AWS_EKS:\"aws_eks\",AWS_LAMBDA:\"aws_lambda\",AWS_ELASTIC_BEANSTALK:\"aws_elastic_beanstalk\",AZURE_VM:\"azure_vm\",AZURE_CONTAINER_INSTANCES:\"azure_container_instances\",AZURE_AKS:\"azure_aks\",AZURE_FUNCTIONS:\"azure_functions\",AZURE_APP_SERVICE:\"azure_app_service\",GCP_COMPUTE_ENGINE:\"gcp_compute_engine\",GCP_CLOUD_RUN:\"gcp_cloud_run\",GCP_KUBERNETES_ENGINE:\"gcp_kubernetes_engine\",GCP_CLOUD_FUNCTIONS:\"gcp_cloud_functions\",GCP_APP_ENGINE:\"gcp_app_engine\"};G.AwsEcsLaunchtypeValues={EC2:\"ec2\",FARGATE:\"fargate\"};G.HostArchValues={AMD64:\"amd64\",ARM32:\"arm32\",ARM64:\"arm64\",IA64:\"ia64\",PPC32:\"ppc32\",PPC64:\"ppc64\",X86:\"x86\"};G.OsTypeValues={WINDOWS:\"windows\",LINUX:\"linux\",DARWIN:\"darwin\",FREEBSD:\"freebsd\",NETBSD:\"netbsd\",OPENBSD:\"openbsd\",DRAGONFLYBSD:\"dragonflybsd\",HPUX:\"hpux\",AIX:\"aix\",SOLARIS:\"solaris\",Z_OS:\"z_os\"};G.TelemetrySdkLanguageValues={CPP:\"cpp\",DOTNET:\"dotnet\",ERLANG:\"erlang\",GO:\"go\",JAVA:\"java\",NODEJS:\"nodejs\",PHP:\"php\",PYTHON:\"python\",RUBY:\"ruby\",WEBJS:\"webjs\"}});var Pi=_(Ct=>{\"use strict\";var Ac=Ct&&Ct.__createBinding||(Object.create?function(e,t,r,n){n===void 0&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){n===void 0&&(n=r),e[n]=t[r]}),Rc=Ct&&Ct.__exportStar||function(e,t){for(var r in e)r!==\"default\"&&!Object.prototype.hasOwnProperty.call(t,r)&&Ac(t,e,r)};Object.defineProperty(Ct,\"__esModule\",{value:!0});Rc(Li(),Ct)});var ae=_(_t=>{\"use strict\";var Oc=_t&&_t.__createBinding||(Object.create?function(e,t,r,n){n===void 0&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){n===void 0&&(n=r),e[n]=t[r]}),Ii=_t&&_t.__exportStar||function(e,t){for(var r in e)r!==\"default\"&&!Object.prototype.hasOwnProperty.call(t,r)&&Oc(t,e,r)};Object.defineProperty(_t,\"__esModule\",{value:!0});Ii(bi(),_t);Ii(Pi(),_t)});var Ci=_(we=>{\"use strict\";Object.defineProperty(we,\"__esModule\",{value:!0});we.SDK_INFO=void 0;var bc=Yr(),se=ae();we.SDK_INFO={[se.SemanticResourceAttributes.TELEMETRY_SDK_NAME]:\"opentelemetry\",[se.SemanticResourceAttributes.PROCESS_RUNTIME_NAME]:\"browser\",[se.SemanticResourceAttributes.TELEMETRY_SDK_LANGUAGE]:se.TelemetrySdkLanguageValues.WEBJS,[se.SemanticResourceAttributes.TELEMETRY_SDK_VERSION]:bc.VERSION}});var Ni=_(Me=>{\"use strict\";Object.defineProperty(Me,\"__esModule\",{value:!0});Me.unrefTimer=void 0;function Lc(e){}Me.unrefTimer=Lc});var zr=_(j=>{\"use strict\";var Pc=j&&j.__createBinding||(Object.create?function(e,t,r,n){n===void 0&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){n===void 0&&(n=r),e[n]=t[r]}),Nt=j&&j.__exportStar||function(e,t){for(var r in e)r!==\"default\"&&!Object.prototype.hasOwnProperty.call(t,r)&&Pc(t,e,r)};Object.defineProperty(j,\"__esModule\",{value:!0});Nt(gi(),j);Nt(Re(),j);Nt(yi(),j);Nt(Ai(),j);Nt(Ri(),j);Nt(Ci(),j);Nt(Ni(),j)});var Di=_(O=>{\"use strict\";Object.defineProperty(O,\"__esModule\",{value:!0});O.addHrTimes=O.isTimeInput=O.isTimeInputHrTime=O.hrTimeToMicroseconds=O.hrTimeToMilliseconds=O.hrTimeToNanoseconds=O.hrTimeToTimeStamp=O.hrTimeDuration=O.timeInputToHrTime=O.hrTime=O.getTimeOrigin=O.millisToHrTime=void 0;var $r=zr(),wi=9,Ic=6,Cc=Math.pow(10,Ic),xe=Math.pow(10,wi);function ue(e){let t=e/1e3,r=Math.trunc(t),n=Math.round(e%1e3*Cc);return[r,n]}O.millisToHrTime=ue;function Qr(){let e=$r.otperformance.timeOrigin;if(typeof e!=\"number\"){let t=$r.otperformance;e=t.timing&&t.timing.fetchStart}return e}O.getTimeOrigin=Qr;function Mi(e){let t=ue(Qr()),r=ue(typeof e==\"number\"?e:$r.otperformance.now());return xi(t,r)}O.hrTime=Mi;function Nc(e){if(Zr(e))return e;if(typeof e==\"number\")return e<Qr()?Mi(e):ue(e);if(e instanceof Date)return ue(e.getTime());throw TypeError(\"Invalid input type\")}O.timeInputToHrTime=Nc;function wc(e,t){let r=t[0]-e[0],n=t[1]-e[1];return n<0&&(r-=1,n+=xe),[r,n]}O.hrTimeDuration=wc;function Mc(e){let t=wi,r=`${\"0\".repeat(t)}${e[1]}Z`,n=r.substr(r.length-t-1);return new Date(e[0]*1e3).toISOString().replace(\"000Z\",n)}O.hrTimeToTimeStamp=Mc;function xc(e){return e[0]*xe+e[1]}O.hrTimeToNanoseconds=xc;function Dc(e){return e[0]*1e3+e[1]/1e6}O.hrTimeToMilliseconds=Dc;function Uc(e){return e[0]*1e6+e[1]/1e3}O.hrTimeToMicroseconds=Uc;function Zr(e){return Array.isArray(e)&&e.length===2&&typeof e[0]==\"number\"&&typeof e[1]==\"number\"}O.isTimeInputHrTime=Zr;function Bc(e){return Zr(e)||typeof e==\"number\"||e instanceof Date}O.isTimeInput=Bc;function xi(e,t){let r=[e[0]+t[0],e[1]+t[1]];return r[1]>=xe&&(r[1]-=xe,r[0]+=1),r}O.addHrTimes=xi});var Bi=_(Ui=>{\"use strict\";Object.defineProperty(Ui,\"__esModule\",{value:!0})});var Gi=_(ce=>{\"use strict\";Object.defineProperty(ce,\"__esModule\",{value:!0});ce.ExportResultCode=void 0;var Gc;(function(e){e[e.SUCCESS=0]=\"SUCCESS\",e[e.FAILED=1]=\"FAILED\"})(Gc=ce.ExportResultCode||(ce.ExportResultCode={}))});var Hi=_(De=>{\"use strict\";Object.defineProperty(De,\"__esModule\",{value:!0});De.CompositePropagator=void 0;var Vi=(E(),w(l)),Jr=class{constructor(t={}){var r;this._propagators=(r=t.propagators)!==null&&r!==void 0?r:[],this._fields=Array.from(new Set(this._propagators.map(n=>typeof n.fields==\"function\"?n.fields():[]).reduce((n,o)=>n.concat(o),[])))}inject(t,r,n){for(let o of this._propagators)try{o.inject(t,r,n)}catch(i){Vi.diag.warn(`Failed to inject with ${o.constructor.name}. Err: ${i.message}`)}}extract(t,r,n){return this._propagators.reduce((o,i)=>{try{return i.extract(o,r,n)}catch(s){Vi.diag.warn(`Failed to inject with ${i.constructor.name}. Err: ${s.message}`)}return o},t)}fields(){return this._fields.slice()}};De.CompositePropagator=Jr});var Fi=_(Ht=>{\"use strict\";Object.defineProperty(Ht,\"__esModule\",{value:!0});Ht.validateValue=Ht.validateKey=void 0;var tn=\"[_0-9a-z-*/]\",Vc=`[a-z]${tn}{0,255}`,Hc=`[a-z0-9]${tn}{0,240}@[a-z]${tn}{0,13}`,Fc=new RegExp(`^(?:${Vc}|${Hc})$`),jc=/^[ -~]{0,255}[!-~]$/,kc=/,|=/;function qc(e){return Fc.test(e)}Ht.validateKey=qc;function Xc(e){return jc.test(e)&&!kc.test(e)}Ht.validateValue=Xc});var rn=_(Ue=>{\"use strict\";Object.defineProperty(Ue,\"__esModule\",{value:!0});Ue.TraceState=void 0;var ji=Fi(),ki=32,Kc=512,qi=\",\",Xi=\"=\",en=class e{constructor(t){this._internalState=new Map,t&&this._parse(t)}set(t,r){let n=this._clone();return n._internalState.has(t)&&n._internalState.delete(t),n._internalState.set(t,r),n}unset(t){let r=this._clone();return r._internalState.delete(t),r}get(t){return this._internalState.get(t)}serialize(){return this._keys().reduce((t,r)=>(t.push(r+Xi+this.get(r)),t),[]).join(qi)}_parse(t){t.length>Kc||(this._internalState=t.split(qi).reverse().reduce((r,n)=>{let o=n.trim(),i=o.indexOf(Xi);if(i!==-1){let s=o.slice(0,i),u=o.slice(i+1,n.length);(0,ji.validateKey)(s)&&(0,ji.validateValue)(u)&&r.set(s,u)}return r},new Map),this._internalState.size>ki&&(this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,ki))))}_keys(){return Array.from(this._internalState.keys()).reverse()}_clone(){let t=new e;return t._internalState=new Map(this._internalState),t}};Ue.TraceState=en});var Wi=_(V=>{\"use strict\";Object.defineProperty(V,\"__esModule\",{value:!0});V.W3CTraceContextPropagator=V.parseTraceParent=V.TRACE_STATE_HEADER=V.TRACE_PARENT_HEADER=void 0;var Be=(E(),w(l)),Wc=ie(),Yc=rn();V.TRACE_PARENT_HEADER=\"traceparent\";V.TRACE_STATE_HEADER=\"tracestate\";var zc=\"00\",$c=\"(?!ff)[\\\\da-f]{2}\",Qc=\"(?![0]{32})[\\\\da-f]{32}\",Zc=\"(?![0]{16})[\\\\da-f]{16}\",Jc=\"[\\\\da-f]{2}\",tl=new RegExp(`^\\\\s?(${$c})-(${Qc})-(${Zc})-(${Jc})(-.*)?\\\\s?$`);function Ki(e){let t=tl.exec(e);return!t||t[1]===\"00\"&&t[5]?null:{traceId:t[2],spanId:t[3],traceFlags:parseInt(t[4],16)}}V.parseTraceParent=Ki;var nn=class{inject(t,r,n){let o=Be.trace.getSpanContext(t);if(!o||(0,Wc.isTracingSuppressed)(t)||!(0,Be.isSpanContextValid)(o))return;let i=`${zc}-${o.traceId}-${o.spanId}-0${Number(o.traceFlags||Be.TraceFlags.NONE).toString(16)}`;n.set(r,V.TRACE_PARENT_HEADER,i),o.traceState&&n.set(r,V.TRACE_STATE_HEADER,o.traceState.serialize())}extract(t,r,n){let o=n.get(r,V.TRACE_PARENT_HEADER);if(!o)return t;let i=Array.isArray(o)?o[0]:o;if(typeof i!=\"string\")return t;let s=Ki(i);if(!s)return t;s.isRemote=!0;let u=n.get(r,V.TRACE_STATE_HEADER);if(u){let c=Array.isArray(u)?u.join(\",\"):u;s.traceState=new Yc.TraceState(typeof c==\"string\"?c:void 0)}return Be.trace.setSpanContext(t,s)}fields(){return[V.TRACE_PARENT_HEADER,V.TRACE_STATE_HEADER]}};V.W3CTraceContextPropagator=nn});var zi=_(Yi=>{\"use strict\";Object.defineProperty(Yi,\"__esModule\",{value:!0})});var $i=_(z=>{\"use strict\";Object.defineProperty(z,\"__esModule\",{value:!0});z.getRPCMetadata=z.deleteRPCMetadata=z.setRPCMetadata=z.RPCType=void 0;var el=(E(),w(l)),on=(0,el.createContextKey)(\"OpenTelemetry SDK Context Key RPC_METADATA\"),rl;(function(e){e.HTTP=\"http\"})(rl=z.RPCType||(z.RPCType={}));function nl(e,t){return e.setValue(on,t)}z.setRPCMetadata=nl;function il(e){return e.deleteValue(on)}z.deleteRPCMetadata=il;function ol(e){return e.getValue(on)}z.getRPCMetadata=ol});var sn=_(Ge=>{\"use strict\";Object.defineProperty(Ge,\"__esModule\",{value:!0});Ge.AlwaysOffSampler=void 0;var al=(E(),w(l)),an=class{shouldSample(){return{decision:al.SamplingDecision.NOT_RECORD}}toString(){return\"AlwaysOffSampler\"}};Ge.AlwaysOffSampler=an});var cn=_(Ve=>{\"use strict\";Object.defineProperty(Ve,\"__esModule\",{value:!0});Ve.AlwaysOnSampler=void 0;var sl=(E(),w(l)),un=class{shouldSample(){return{decision:sl.SamplingDecision.RECORD_AND_SAMPLED}}toString(){return\"AlwaysOnSampler\"}};Ve.AlwaysOnSampler=un});var Zi=_(Fe=>{\"use strict\";Object.defineProperty(Fe,\"__esModule\",{value:!0});Fe.ParentBasedSampler=void 0;var He=(E(),w(l)),ul=qr(),Qi=sn(),ln=cn(),fn=class{constructor(t){var r,n,o,i;this._root=t.root,this._root||((0,ul.globalErrorHandler)(new Error(\"ParentBasedSampler must have a root sampler configured\")),this._root=new ln.AlwaysOnSampler),this._remoteParentSampled=(r=t.remoteParentSampled)!==null&&r!==void 0?r:new ln.AlwaysOnSampler,this._remoteParentNotSampled=(n=t.remoteParentNotSampled)!==null&&n!==void 0?n:new Qi.AlwaysOffSampler,this._localParentSampled=(o=t.localParentSampled)!==null&&o!==void 0?o:new ln.AlwaysOnSampler,this._localParentNotSampled=(i=t.localParentNotSampled)!==null&&i!==void 0?i:new Qi.AlwaysOffSampler}shouldSample(t,r,n,o,i,s){let u=He.trace.getSpanContext(t);return!u||!(0,He.isSpanContextValid)(u)?this._root.shouldSample(t,r,n,o,i,s):u.isRemote?u.traceFlags&He.TraceFlags.SAMPLED?this._remoteParentSampled.shouldSample(t,r,n,o,i,s):this._remoteParentNotSampled.shouldSample(t,r,n,o,i,s):u.traceFlags&He.TraceFlags.SAMPLED?this._localParentSampled.shouldSample(t,r,n,o,i,s):this._localParentNotSampled.shouldSample(t,r,n,o,i,s)}toString(){return`ParentBased{root=${this._root.toString()}, remoteParentSampled=${this._remoteParentSampled.toString()}, remoteParentNotSampled=${this._remoteParentNotSampled.toString()}, localParentSampled=${this._localParentSampled.toString()}, localParentNotSampled=${this._localParentNotSampled.toString()}}`}};Fe.ParentBasedSampler=fn});var Ji=_(je=>{\"use strict\";Object.defineProperty(je,\"__esModule\",{value:!0});je.TraceIdRatioBasedSampler=void 0;var pn=(E(),w(l)),dn=class{constructor(t=0){this._ratio=t,this._ratio=this._normalize(t),this._upperBound=Math.floor(this._ratio*4294967295)}shouldSample(t,r){return{decision:(0,pn.isValidTraceId)(r)&&this._accumulate(r)<this._upperBound?pn.SamplingDecision.RECORD_AND_SAMPLED:pn.SamplingDecision.NOT_RECORD}}toString(){return`TraceIdRatioBased{${this._ratio}}`}_normalize(t){return typeof t!=\"number\"||isNaN(t)?0:t>=1?1:t<=0?0:t}_accumulate(t){let r=0;for(let n=0;n<t.length/8;n++){let o=n*8,i=parseInt(t.slice(o,o+8),16);r=(r^i)>>>0}return r}};je.TraceIdRatioBasedSampler=dn});var io=_(ke=>{\"use strict\";Object.defineProperty(ke,\"__esModule\",{value:!0});ke.isPlainObject=void 0;var cl=\"[object Object]\",ll=\"[object Null]\",fl=\"[object Undefined]\",pl=Function.prototype,to=pl.toString,dl=to.call(Object),_l=hl(Object.getPrototypeOf,Object),eo=Object.prototype,ro=eo.hasOwnProperty,wt=Symbol?Symbol.toStringTag:void 0,no=eo.toString;function hl(e,t){return function(r){return e(t(r))}}function El(e){if(!ml(e)||Tl(e)!==cl)return!1;let t=_l(e);if(t===null)return!0;let r=ro.call(t,\"constructor\")&&t.constructor;return typeof r==\"function\"&&r instanceof r&&to.call(r)===dl}ke.isPlainObject=El;function ml(e){return e!=null&&typeof e==\"object\"}function Tl(e){return e==null?e===void 0?fl:ll:wt&&wt in Object(e)?Sl(e):gl(e)}function Sl(e){let t=ro.call(e,wt),r=e[wt],n=!1;try{e[wt]=void 0,n=!0}catch{}let o=no.call(e);return n&&(t?e[wt]=r:delete e[wt]),o}function gl(e){return no.call(e)}});var co=_(Ke=>{\"use strict\";Object.defineProperty(Ke,\"__esModule\",{value:!0});Ke.merge=void 0;var oo=io(),yl=20;function vl(...e){let t=e.shift(),r=new WeakMap;for(;e.length>0;)t=so(t,e.shift(),0,r);return t}Ke.merge=vl;function _n(e){return Xe(e)?e.slice():e}function so(e,t,r=0,n){let o;if(!(r>yl)){if(r++,qe(e)||qe(t)||uo(t))o=_n(t);else if(Xe(e)){if(o=e.slice(),Xe(t))for(let i=0,s=t.length;i<s;i++)o.push(_n(t[i]));else if(le(t)){let i=Object.keys(t);for(let s=0,u=i.length;s<u;s++){let c=i[s];o[c]=_n(t[c])}}}else if(le(e))if(le(t)){if(!Al(e,t))return t;o=Object.assign({},e);let i=Object.keys(t);for(let s=0,u=i.length;s<u;s++){let c=i[s],a=t[c];if(qe(a))typeof a>\"u\"?delete o[c]:o[c]=a;else{let f=o[c],p=a;if(ao(e,c,n)||ao(t,c,n))delete o[c];else{if(le(f)&&le(p)){let d=n.get(f)||[],m=n.get(p)||[];d.push({obj:e,key:c}),m.push({obj:t,key:c}),n.set(f,d),n.set(p,m)}o[c]=so(o[c],a,r,n)}}}}else o=t;return o}}function ao(e,t,r){let n=r.get(e[t])||[];for(let o=0,i=n.length;o<i;o++){let s=n[o];if(s.key===t&&s.obj===e)return!0}return!1}function Xe(e){return Array.isArray(e)}function uo(e){return typeof e==\"function\"}function le(e){return!qe(e)&&!Xe(e)&&!uo(e)&&typeof e==\"object\"}function qe(e){return typeof e==\"string\"||typeof e==\"number\"||typeof e==\"boolean\"||typeof e>\"u\"||e instanceof Date||e instanceof RegExp||e===null}function Al(e,t){return!(!(0,oo.isPlainObject)(e)||!(0,oo.isPlainObject)(t))}});var lo=_(Ft=>{\"use strict\";Object.defineProperty(Ft,\"__esModule\",{value:!0});Ft.callWithTimeout=Ft.TimeoutError=void 0;var We=class e extends Error{constructor(t){super(t),Object.setPrototypeOf(this,e.prototype)}};Ft.TimeoutError=We;function Rl(e,t){let r,n=new Promise(function(i,s){r=setTimeout(function(){s(new We(\"Operation timed out.\"))},t)});return Promise.race([e,n]).then(o=>(clearTimeout(r),o),o=>{throw clearTimeout(r),o})}Ft.callWithTimeout=Rl});var po=_(jt=>{\"use strict\";Object.defineProperty(jt,\"__esModule\",{value:!0});jt.isUrlIgnored=jt.urlMatches=void 0;function fo(e,t){return typeof t==\"string\"?e===t:!!e.match(t)}jt.urlMatches=fo;function Ol(e,t){if(!t)return!1;for(let r of t)if(fo(e,r))return!0;return!1}jt.isUrlIgnored=Ol});var _o=_(Ye=>{\"use strict\";Object.defineProperty(Ye,\"__esModule\",{value:!0});Ye.isWrapped=void 0;function bl(e){return typeof e==\"function\"&&typeof e.__original==\"function\"&&typeof e.__unwrap==\"function\"&&e.__wrapped===!0}Ye.isWrapped=bl});var ho=_(ze=>{\"use strict\";Object.defineProperty(ze,\"__esModule\",{value:!0});ze.Deferred=void 0;var hn=class{constructor(){this._promise=new Promise((t,r)=>{this._resolve=t,this._reject=r})}get promise(){return this._promise}resolve(t){this._resolve(t)}reject(t){this._reject(t)}};ze.Deferred=hn});var Eo=_($e=>{\"use strict\";Object.defineProperty($e,\"__esModule\",{value:!0});$e.BindOnceFuture=void 0;var Ll=ho(),En=class{constructor(t,r){this._callback=t,this._that=r,this._isCalled=!1,this._deferred=new Ll.Deferred}get isCalled(){return this._isCalled}get promise(){return this._deferred.promise}call(...t){if(!this._isCalled){this._isCalled=!0;try{Promise.resolve(this._callback.call(this._that,...t)).then(r=>this._deferred.resolve(r),r=>this._deferred.reject(r))}catch(r){this._deferred.reject(r)}}return this._deferred.promise}};$e.BindOnceFuture=En});var To=_(Qe=>{\"use strict\";Object.defineProperty(Qe,\"__esModule\",{value:!0});Qe._export=void 0;var mo=(E(),w(l)),Pl=ie();function Il(e,t){return new Promise(r=>{mo.context.with((0,Pl.suppressTracing)(mo.context.active()),()=>{e.export(t,n=>{r(n)})})})}Qe._export=Il});var P=_(S=>{\"use strict\";var Cl=S&&S.__createBinding||(Object.create?function(e,t,r,n){n===void 0&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){n===void 0&&(n=r),e[n]=t[r]}),R=S&&S.__exportStar||function(e,t){for(var r in e)r!==\"default\"&&!Object.prototype.hasOwnProperty.call(t,r)&&Cl(t,e,r)};Object.defineProperty(S,\"__esModule\",{value:!0});S.internal=S.baggageUtils=void 0;R(fi(),S);R(pi(),S);R(mi(),S);R(qr(),S);R(kr(),S);R(Di(),S);R(Bi(),S);R(Gi(),S);S.baggageUtils=Gr();R(zr(),S);R(Hi(),S);R(Wi(),S);R(zi(),S);R($i(),S);R(sn(),S);R(cn(),S);R(Zi(),S);R(Ji(),S);R(ie(),S);R(rn(),S);R(Oe(),S);R(co(),S);R(Xr(),S);R(lo(),S);R(po(),S);R(_o(),S);R(Eo(),S);R(Yr(),S);var Nl=To();S.internal={_export:Nl._export}});var Vo=_(St=>{\"use strict\";Object.defineProperty(St,\"__esModule\",{value:!0});St.disableInstrumentations=St.enableInstrumentations=St.parseInstrumentationOptions=void 0;function Go(e=[]){let t=[];for(let r=0,n=e.length;r<n;r++){let o=e[r];if(Array.isArray(o)){let i=Go(o);t=t.concat(i.instrumentations)}else typeof o==\"function\"?t.push(new o):o.instrumentationName&&t.push(o)}return{instrumentations:t}}St.parseInstrumentationOptions=Go;function Yl(e,t,r){for(let n=0,o=e.length;n<o;n++){let i=e[n];t&&i.setTracerProvider(t),r&&i.setMeterProvider(r),i.getConfig().enabled||i.enable()}}St.enableInstrumentations=Yl;function zl(e){e.forEach(t=>t.disable())}St.disableInstrumentations=zl});var Fo=_(nr=>{\"use strict\";Object.defineProperty(nr,\"__esModule\",{value:!0});nr.registerInstrumentations=void 0;var Ho=(E(),w(l)),Tn=Vo();function $l(e){let{instrumentations:t}=(0,Tn.parseInstrumentationOptions)(e.instrumentations),r=e.tracerProvider||Ho.trace.getTracerProvider(),n=e.meterProvider||Ho.metrics.getMeterProvider();return(0,Tn.enableInstrumentations)(t,r,n),()=>{(0,Tn.disableInstrumentations)(t)}}nr.registerInstrumentations=$l});var pe={};import*as wE from\"async_hooks\";var Ua=Dr(()=>{Y(pe,wE)});var de={};import*as ME from\"events\";var Ba=Dr(()=>{Y(de,ME)});var Ga=_(dr=>{\"use strict\";Object.defineProperty(dr,\"__esModule\",{value:!0});dr.AbstractAsyncHooksContextManager=void 0;var bf=(Ba(),w(de)),Lf=[\"addListener\",\"on\",\"once\",\"prependListener\",\"prependOnceListener\"],Cn=class{constructor(){this._kOtListeners=Symbol(\"OtListeners\"),this._wrapped=!1}bind(t,r){return r instanceof bf.EventEmitter?this._bindEventEmitter(t,r):typeof r==\"function\"?this._bindFunction(t,r):r}_bindFunction(t,r){let n=this,o=function(...i){return n.with(t,()=>r.apply(this,i))};return Object.defineProperty(o,\"length\",{enumerable:!1,configurable:!0,writable:!1,value:r.length}),o}_bindEventEmitter(t,r){return this._getPatchMap(r)!==void 0||(this._createPatchMap(r),Lf.forEach(o=>{r[o]!==void 0&&(r[o]=this._patchAddListener(r,r[o],t))}),typeof r.removeListener==\"function\"&&(r.removeListener=this._patchRemoveListener(r,r.removeListener)),typeof r.off==\"function\"&&(r.off=this._patchRemoveListener(r,r.off)),typeof r.removeAllListeners==\"function\"&&(r.removeAllListeners=this._patchRemoveAllListeners(r,r.removeAllListeners))),r}_patchRemoveListener(t,r){let n=this;return function(o,i){var s;let u=(s=n._getPatchMap(t))===null||s===void 0?void 0:s[o];if(u===void 0)return r.call(this,o,i);let c=u.get(i);return r.call(this,o,c||i)}}_patchRemoveAllListeners(t,r){let n=this;return function(o){let i=n._getPatchMap(t);return i!==void 0&&(arguments.length===0?n._createPatchMap(t):i[o]!==void 0&&delete i[o]),r.apply(this,arguments)}}_patchAddListener(t,r,n){let o=this;return function(i,s){if(o._wrapped)return r.call(this,i,s);let u=o._getPatchMap(t);u===void 0&&(u=o._createPatchMap(t));let c=u[i];c===void 0&&(c=new WeakMap,u[i]=c);let a=o.bind(n,s);c.set(s,a),o._wrapped=!0;try{return r.call(this,i,a)}finally{o._wrapped=!1}}}_createPatchMap(t){let r=Object.create(null);return t[this._kOtListeners]=r,r}_getPatchMap(t){return t[this._kOtListeners]}};dr.AbstractAsyncHooksContextManager=Cn});var Va=_(_r=>{\"use strict\";Object.defineProperty(_r,\"__esModule\",{value:!0});_r.AsyncLocalStorageContextManager=void 0;var Pf=(E(),w(l)),If=(Ua(),w(pe)),Cf=Ga(),Nn=class extends Cf.AbstractAsyncHooksContextManager{constructor(){super(),this._asyncLocalStorage=new If.AsyncLocalStorage}active(){var t;return(t=this._asyncLocalStorage.getStore())!==null&&t!==void 0?t:Pf.ROOT_CONTEXT}with(t,r,n,...o){let i=n==null?r:r.bind(n);return this._asyncLocalStorage.run(t,i,...o)}enable(){return this}disable(){return this._asyncLocalStorage.disable(),this}};_r.AsyncLocalStorageContextManager=Nn});var Bn=_(gt=>{\"use strict\";Object.defineProperty(gt,\"__esModule\",{value:!0});gt.toAnyValue=gt.toKeyValue=gt.toAttributes=void 0;function Uf(e){return Object.keys(e).map(t=>Dn(t,e[t]))}gt.toAttributes=Uf;function Dn(e,t){return{key:e,value:Un(t)}}gt.toKeyValue=Dn;function Un(e){let t=typeof e;return t===\"string\"?{stringValue:e}:t===\"number\"?Number.isInteger(e)?{intValue:e}:{doubleValue:e}:t===\"boolean\"?{boolValue:e}:e instanceof Uint8Array?{bytesValue:e}:Array.isArray(e)?{arrayValue:{values:e.map(Un)}}:t===\"object\"&&e!=null?{kvlistValue:{values:Object.entries(e).map(([r,n])=>Dn(r,n))}}:{}}gt.toAnyValue=Un});var Ja=_(yt=>{\"use strict\";Object.defineProperty(yt,\"__esModule\",{value:!0});yt.toOtlpSpanEvent=yt.toOtlpLink=yt.sdkSpanToOtlpSpan=void 0;var Gn=Bn();function Bf(e,t){var r;let n=e.spanContext(),o=e.status;return{traceId:t.encodeSpanContext(n.traceId),spanId:t.encodeSpanContext(n.spanId),parentSpanId:t.encodeOptionalSpanContext(e.parentSpanId),traceState:(r=n.traceState)===null||r===void 0?void 0:r.serialize(),name:e.name,kind:e.kind==null?0:e.kind+1,startTimeUnixNano:t.encodeHrTime(e.startTime),endTimeUnixNano:t.encodeHrTime(e.endTime),attributes:(0,Gn.toAttributes)(e.attributes),droppedAttributesCount:e.droppedAttributesCount,events:e.events.map(i=>Za(i,t)),droppedEventsCount:e.droppedEventsCount,status:{code:o.code,message:o.message},links:e.links.map(i=>Qa(i,t)),droppedLinksCount:e.droppedLinksCount}}yt.sdkSpanToOtlpSpan=Bf;function Qa(e,t){var r;return{attributes:e.attributes?(0,Gn.toAttributes)(e.attributes):[],spanId:t.encodeSpanContext(e.context.spanId),traceId:t.encodeSpanContext(e.context.traceId),traceState:(r=e.context.traceState)===null||r===void 0?void 0:r.serialize(),droppedAttributesCount:e.droppedAttributesCount||0}}yt.toOtlpLink=Qa;function Za(e,t){return{attributes:e.attributes?(0,Gn.toAttributes)(e.attributes):[],name:e.name,timeUnixNano:t.encodeHrTime(e.time),droppedAttributesCount:e.droppedAttributesCount||0}}yt.toOtlpSpanEvent=Za});var is=_(K=>{\"use strict\";Object.defineProperty(K,\"__esModule\",{value:!0});K.getOtlpEncoder=K.encodeAsString=K.encodeAsLongBits=K.toLongBits=K.hrTimeToNanos=void 0;var Tr=P(),Gf=BigInt(1e9);function Vn(e){return BigInt(e[0])*Gf+BigInt(e[1])}K.hrTimeToNanos=Vn;function es(e){let t=Number(BigInt.asUintN(32,e)),r=Number(BigInt.asUintN(32,e>>BigInt(32)));return{low:t,high:r}}K.toLongBits=es;function Hn(e){let t=Vn(e);return es(t)}K.encodeAsLongBits=Hn;function rs(e){return Vn(e).toString()}K.encodeAsString=rs;var Vf=typeof BigInt<\"u\"?rs:Tr.hrTimeToNanoseconds;function ts(e){return e}function ns(e){if(e!==void 0)return(0,Tr.hexToBase64)(e)}var Hf={encodeHrTime:Hn,encodeSpanContext:Tr.hexToBase64,encodeOptionalSpanContext:ns};function Ff(e){var t,r;if(e===void 0)return Hf;let n=(t=e.useLongBits)!==null&&t!==void 0?t:!0,o=(r=e.useHex)!==null&&r!==void 0?r:!1;return{encodeHrTime:n?Hn:Vf,encodeSpanContext:o?ts:Tr.hexToBase64,encodeOptionalSpanContext:o?ts:ns}}K.getOtlpEncoder=Ff});var Fn=_(Sr=>{\"use strict\";Object.defineProperty(Sr,\"__esModule\",{value:!0});Sr.createExportTraceServiceRequest=void 0;var jf=Bn(),kf=Ja(),qf=is();function Xf(e,t){let r=(0,qf.getOtlpEncoder)(t);return{resourceSpans:Wf(e,r)}}Sr.createExportTraceServiceRequest=Xf;function Kf(e){let t=new Map;for(let r of e){let n=t.get(r.resource);n||(n=new Map,t.set(r.resource,n));let o=`${r.instrumentationLibrary.name}@${r.instrumentationLibrary.version||\"\"}:${r.instrumentationLibrary.schemaUrl||\"\"}`,i=n.get(o);i||(i=[],n.set(o,i)),i.push(r)}return t}function Wf(e,t){let r=Kf(e),n=[],o=r.entries(),i=o.next();for(;!i.done;){let[s,u]=i.value,c=[],a=u.values(),f=a.next();for(;!f.done;){let d=f.value;if(d.length>0){let{name:m,version:g,schemaUrl:C}=d[0].instrumentationLibrary,M=d.map(T=>(0,kf.sdkSpanToOtlpSpan)(T,t));c.push({scope:{name:m,version:g},spans:M,schemaUrl:C})}f=a.next()}let p={resource:{attributes:(0,jf.toAttributes)(s.attributes),droppedAttributesCount:0},scopeSpans:c,schemaUrl:void 0};n.push(p),i=o.next()}return n}});var ss=_(I=>{\"use strict\";Object.defineProperty(I,\"__esModule\",{value:!0});I.parseRetryAfterToMills=I.isExportRetryable=I.invalidTimeout=I.configureExporterTimeout=I.appendRootPathToUrlIfNeeded=I.appendResourcePathToUrl=I.parseHeaders=I.DEFAULT_EXPORT_BACKOFF_MULTIPLIER=I.DEFAULT_EXPORT_MAX_BACKOFF=I.DEFAULT_EXPORT_INITIAL_BACKOFF=I.DEFAULT_EXPORT_MAX_ATTEMPTS=void 0;var jn=(E(),w(l)),os=P(),as=1e4;I.DEFAULT_EXPORT_MAX_ATTEMPTS=5;I.DEFAULT_EXPORT_INITIAL_BACKOFF=1e3;I.DEFAULT_EXPORT_MAX_BACKOFF=5e3;I.DEFAULT_EXPORT_BACKOFF_MULTIPLIER=1.5;function Yf(e={}){let t={};return Object.entries(e).forEach(([r,n])=>{typeof n<\"u\"?t[r]=String(n):jn.diag.warn(`Header \"${r}\" has wrong value and will be ignored`)}),t}I.parseHeaders=Yf;function zf(e,t){return e.endsWith(\"/\")||(e=e+\"/\"),e+t}I.appendResourcePathToUrl=zf;function $f(e){try{let t=new URL(e);return t.pathname===\"\"&&(t.pathname=t.pathname+\"/\"),t.toString()}catch{return jn.diag.warn(`Could not parse export URL: '${e}'`),e}}I.appendRootPathToUrlIfNeeded=$f;function Qf(e){return typeof e==\"number\"?e<=0?kn(e,as):e:Zf()}I.configureExporterTimeout=Qf;function Zf(){var e;let t=Number((e=(0,os.getEnv)().OTEL_EXPORTER_OTLP_TRACES_TIMEOUT)!==null&&e!==void 0?e:(0,os.getEnv)().OTEL_EXPORTER_OTLP_TIMEOUT);return t<=0?kn(t,as):t}function kn(e,t){return jn.diag.warn(\"Timeout must be greater than 0\",e),t}I.invalidTimeout=kn;function Jf(e){return[429,502,503,504].includes(e)}I.isExportRetryable=Jf;function tp(e){if(e==null)return-1;let t=Number.parseInt(e,10);if(Number.isInteger(t))return t>0?t*1e3:-1;let r=new Date(e).getTime()-Date.now();return r>=0?r:0}I.parseRetryAfterToMills=tp});var cs=_(gr=>{\"use strict\";Object.defineProperty(gr,\"__esModule\",{value:!0});gr.OTLPExporterBase=void 0;var us=(E(),w(l)),_e=P(),ep=ss(),qn=class{constructor(t={}){this._sendingPromises=[],this.url=this.getDefaultUrl(t),typeof t.hostname==\"string\"&&(this.hostname=t.hostname),this.shutdown=this.shutdown.bind(this),this._shutdownOnce=new _e.BindOnceFuture(this._shutdown,this),this._concurrencyLimit=typeof t.concurrencyLimit==\"number\"?t.concurrencyLimit:30,this.timeoutMillis=(0,ep.configureExporterTimeout)(t.timeoutMillis),this.onInit(t)}export(t,r){if(this._shutdownOnce.isCalled){r({code:_e.ExportResultCode.FAILED,error:new Error(\"Exporter has been shutdown\")});return}if(this._sendingPromises.length>=this._concurrencyLimit){r({code:_e.ExportResultCode.FAILED,error:new Error(\"Concurrent export limit reached\")});return}this._export(t).then(()=>{r({code:_e.ExportResultCode.SUCCESS})}).catch(n=>{r({code:_e.ExportResultCode.FAILED,error:n})})}_export(t){return new Promise((r,n)=>{try{us.diag.debug(\"items to be sent\",t),this.send(t,r,n)}catch(o){n(o)}})}shutdown(){return this._shutdownOnce.call()}forceFlush(){return Promise.all(this._sendingPromises).then(()=>{})}_shutdown(){return us.diag.debug(\"shutdown started\"),this.onShutdown(),this.forceFlush()}};gr.OTLPExporterBase=qn});var ds=_((um,ps)=>{\"use strict\";ps.exports=ip;function ip(e,t){for(var r=new Array(arguments.length-1),n=0,o=2,i=!0;o<arguments.length;)r[n++]=arguments[o++];return new Promise(function(u,c){r[n]=function(f){if(i)if(i=!1,f)c(f);else{for(var p=new Array(arguments.length-1),d=0;d<p.length;)p[d++]=arguments[d];u.apply(null,p)}};try{e.apply(t||null,r)}catch(a){i&&(i=!1,c(a))}})}});var ms=_(Es=>{\"use strict\";var vr=Es;vr.length=function(t){var r=t.length;if(!r)return 0;for(var n=0;--r%4>1&&t.charAt(r)===\"=\";)++n;return Math.ceil(t.length*3)/4-n};var Zt=new Array(64),hs=new Array(123);for(Z=0;Z<64;)hs[Zt[Z]=Z<26?Z+65:Z<52?Z+71:Z<62?Z-4:Z-59|43]=Z++;var Z;vr.encode=function(t,r,n){for(var o=null,i=[],s=0,u=0,c;r<n;){var a=t[r++];switch(u){case 0:i[s++]=Zt[a>>2],c=(a&3)<<4,u=1;break;case 1:i[s++]=Zt[c|a>>4],c=(a&15)<<2,u=2;break;case 2:i[s++]=Zt[c|a>>6],i[s++]=Zt[a&63],u=0;break}s>8191&&((o||(o=[])).push(String.fromCharCode.apply(String,i)),s=0)}return u&&(i[s++]=Zt[c],i[s++]=61,u===1&&(i[s++]=61)),o?(s&&o.push(String.fromCharCode.apply(String,i.slice(0,s))),o.join(\"\")):String.fromCharCode.apply(String,i.slice(0,s))};var _s=\"invalid encoding\";vr.decode=function(t,r,n){for(var o=n,i=0,s,u=0;u<t.length;){var c=t.charCodeAt(u++);if(c===61&&i>1)break;if((c=hs[c])===void 0)throw Error(_s);switch(i){case 0:s=c,i=1;break;case 1:r[n++]=s<<2|(c&48)>>4,s=c,i=2;break;case 2:r[n++]=(s&15)<<4|(c&60)>>2,s=c,i=3;break;case 3:r[n++]=(s&3)<<6|c,i=0;break}}if(i===1)throw Error(_s);return n-o};vr.test=function(t){return/^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/.test(t)}});var Ss=_((lm,Ts)=>{\"use strict\";Ts.exports=Ar;function Ar(){this._listeners={}}Ar.prototype.on=function(t,r,n){return(this._listeners[t]||(this._listeners[t]=[])).push({fn:r,ctx:n||this}),this};Ar.prototype.off=function(t,r){if(t===void 0)this._listeners={};else if(r===void 0)this._listeners[t]=[];else for(var n=this._listeners[t],o=0;o<n.length;)n[o].fn===r?n.splice(o,1):++o;return this};Ar.prototype.emit=function(t){var r=this._listeners[t];if(r){for(var n=[],o=1;o<arguments.length;)n.push(arguments[o++]);for(o=0;o<r.length;)r[o].fn.apply(r[o++].ctx,n)}return this}});var bs=_((fm,Os)=>{\"use strict\";Os.exports=gs(gs);function gs(e){return typeof Float32Array<\"u\"?function(){var t=new Float32Array([-0]),r=new Uint8Array(t.buffer),n=r[3]===128;function o(c,a,f){t[0]=c,a[f]=r[0],a[f+1]=r[1],a[f+2]=r[2],a[f+3]=r[3]}function i(c,a,f){t[0]=c,a[f]=r[3],a[f+1]=r[2],a[f+2]=r[1],a[f+3]=r[0]}e.writeFloatLE=n?o:i,e.writeFloatBE=n?i:o;function s(c,a){return r[0]=c[a],r[1]=c[a+1],r[2]=c[a+2],r[3]=c[a+3],t[0]}function u(c,a){return r[3]=c[a],r[2]=c[a+1],r[1]=c[a+2],r[0]=c[a+3],t[0]}e.readFloatLE=n?s:u,e.readFloatBE=n?u:s}():function(){function t(n,o,i,s){var u=o<0?1:0;if(u&&(o=-o),o===0)n(1/o>0?0:2147483648,i,s);else if(isNaN(o))n(2143289344,i,s);else if(o>34028234663852886e22)n((u<<31|2139095040)>>>0,i,s);else if(o<11754943508222875e-54)n((u<<31|Math.round(o/1401298464324817e-60))>>>0,i,s);else{var c=Math.floor(Math.log(o)/Math.LN2),a=Math.round(o*Math.pow(2,-c)*8388608)&8388607;n((u<<31|c+127<<23|a)>>>0,i,s)}}e.writeFloatLE=t.bind(null,ys),e.writeFloatBE=t.bind(null,vs);function r(n,o,i){var s=n(o,i),u=(s>>31)*2+1,c=s>>>23&255,a=s&8388607;return c===255?a?NaN:u*(1/0):c===0?u*1401298464324817e-60*a:u*Math.pow(2,c-150)*(a+8388608)}e.readFloatLE=r.bind(null,As),e.readFloatBE=r.bind(null,Rs)}(),typeof Float64Array<\"u\"?function(){var t=new Float64Array([-0]),r=new Uint8Array(t.buffer),n=r[7]===128;function o(c,a,f){t[0]=c,a[f]=r[0],a[f+1]=r[1],a[f+2]=r[2],a[f+3]=r[3],a[f+4]=r[4],a[f+5]=r[5],a[f+6]=r[6],a[f+7]=r[7]}function i(c,a,f){t[0]=c,a[f]=r[7],a[f+1]=r[6],a[f+2]=r[5],a[f+3]=r[4],a[f+4]=r[3],a[f+5]=r[2],a[f+6]=r[1],a[f+7]=r[0]}e.writeDoubleLE=n?o:i,e.writeDoubleBE=n?i:o;function s(c,a){return r[0]=c[a],r[1]=c[a+1],r[2]=c[a+2],r[3]=c[a+3],r[4]=c[a+4],r[5]=c[a+5],r[6]=c[a+6],r[7]=c[a+7],t[0]}function u(c,a){return r[7]=c[a],r[6]=c[a+1],r[5]=c[a+2],r[4]=c[a+3],r[3]=c[a+4],r[2]=c[a+5],r[1]=c[a+6],r[0]=c[a+7],t[0]}e.readDoubleLE=n?s:u,e.readDoubleBE=n?u:s}():function(){function t(n,o,i,s,u,c){var a=s<0?1:0;if(a&&(s=-s),s===0)n(0,u,c+o),n(1/s>0?0:2147483648,u,c+i);else if(isNaN(s))n(0,u,c+o),n(2146959360,u,c+i);else if(s>17976931348623157e292)n(0,u,c+o),n((a<<31|2146435072)>>>0,u,c+i);else{var f;if(s<22250738585072014e-324)f=s/5e-324,n(f>>>0,u,c+o),n((a<<31|f/4294967296)>>>0,u,c+i);else{var p=Math.floor(Math.log(s)/Math.LN2);p===1024&&(p=1023),f=s*Math.pow(2,-p),n(f*4503599627370496>>>0,u,c+o),n((a<<31|p+1023<<20|f*1048576&1048575)>>>0,u,c+i)}}}e.writeDoubleLE=t.bind(null,ys,0,4),e.writeDoubleBE=t.bind(null,vs,4,0);function r(n,o,i,s,u){var c=n(s,u+o),a=n(s,u+i),f=(a>>31)*2+1,p=a>>>20&2047,d=4294967296*(a&1048575)+c;return p===2047?d?NaN:f*(1/0):p===0?f*5e-324*d:f*Math.pow(2,p-1075)*(d+4503599627370496)}e.readDoubleLE=r.bind(null,As,0,4),e.readDoubleBE=r.bind(null,Rs,4,0)}(),e}function ys(e,t,r){t[r]=e&255,t[r+1]=e>>>8&255,t[r+2]=e>>>16&255,t[r+3]=e>>>24}function vs(e,t,r){t[r]=e>>>24,t[r+1]=e>>>16&255,t[r+2]=e>>>8&255,t[r+3]=e&255}function As(e,t){return(e[t]|e[t+1]<<8|e[t+2]<<16|e[t+3]<<24)>>>0}function Rs(e,t){return(e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3])>>>0}});var Ps=_((pm,Ls)=>{\"use strict\";Ls.exports=op;function op(e){return null}});var Cs=_(Is=>{\"use strict\";var Kn=Is;Kn.length=function(t){for(var r=0,n=0,o=0;o<t.length;++o)n=t.charCodeAt(o),n<128?r+=1:n<2048?r+=2:(n&64512)===55296&&(t.charCodeAt(o+1)&64512)===56320?(++o,r+=4):r+=3;return r};Kn.read=function(t,r,n){var o=n-r;if(o<1)return\"\";for(var i=null,s=[],u=0,c;r<n;)c=t[r++],c<128?s[u++]=c:c>191&&c<224?s[u++]=(c&31)<<6|t[r++]&63:c>239&&c<365?(c=((c&7)<<18|(t[r++]&63)<<12|(t[r++]&63)<<6|t[r++]&63)-65536,s[u++]=55296+(c>>10),s[u++]=56320+(c&1023)):s[u++]=(c&15)<<12|(t[r++]&63)<<6|t[r++]&63,u>8191&&((i||(i=[])).push(String.fromCharCode.apply(String,s)),u=0);return i?(u&&i.push(String.fromCharCode.apply(String,s.slice(0,u))),i.join(\"\")):String.fromCharCode.apply(String,s.slice(0,u))};Kn.write=function(t,r,n){for(var o=n,i,s,u=0;u<t.length;++u)i=t.charCodeAt(u),i<128?r[n++]=i:i<2048?(r[n++]=i>>6|192,r[n++]=i&63|128):(i&64512)===55296&&((s=t.charCodeAt(u+1))&64512)===56320?(i=65536+((i&1023)<<10)+(s&1023),++u,r[n++]=i>>18|240,r[n++]=i>>12&63|128,r[n++]=i>>6&63|128,r[n++]=i&63|128):(r[n++]=i>>12|224,r[n++]=i>>6&63|128,r[n++]=i&63|128);return n-o}});var ws=_((_m,Ns)=>{\"use strict\";Ns.exports=ap;function ap(e,t,r){var n=r||8192,o=n>>>1,i=null,s=n;return function(c){if(c<1||c>o)return e(c);s+c>n&&(i=e(n),s=0);var a=t.call(i,s,s+=c);return s&7&&(s=(s|7)+1),a}}});var xs=_((hm,Ms)=>{\"use strict\";Ms.exports=D;var he=At();function D(e,t){this.lo=e>>>0,this.hi=t>>>0}var Dt=D.zero=new D(0,0);Dt.toNumber=function(){return 0};Dt.zzEncode=Dt.zzDecode=function(){return this};Dt.length=function(){return 1};var sp=D.zeroHash=\"\\0\\0\\0\\0\\0\\0\\0\\0\";D.fromNumber=function(t){if(t===0)return Dt;var r=t<0;r&&(t=-t);var n=t>>>0,o=(t-n)/4294967296>>>0;return r&&(o=~o>>>0,n=~n>>>0,++n>4294967295&&(n=0,++o>4294967295&&(o=0))),new D(n,o)};D.from=function(t){if(typeof t==\"number\")return D.fromNumber(t);if(he.isString(t))if(he.Long)t=he.Long.fromString(t);else return D.fromNumber(parseInt(t,10));return t.low||t.high?new D(t.low>>>0,t.high>>>0):Dt};D.prototype.toNumber=function(t){if(!t&&this.hi>>>31){var r=~this.lo+1>>>0,n=~this.hi>>>0;return r||(n=n+1>>>0),-(r+n*4294967296)}return this.lo+this.hi*4294967296};D.prototype.toLong=function(t){return he.Long?new he.Long(this.lo|0,this.hi|0,!!t):{low:this.lo|0,high:this.hi|0,unsigned:!!t}};var vt=String.prototype.charCodeAt;D.fromHash=function(t){return t===sp?Dt:new D((vt.call(t,0)|vt.call(t,1)<<8|vt.call(t,2)<<16|vt.call(t,3)<<24)>>>0,(vt.call(t,4)|vt.call(t,5)<<8|vt.call(t,6)<<16|vt.call(t,7)<<24)>>>0)};D.prototype.toHash=function(){return String.fromCharCode(this.lo&255,this.lo>>>8&255,this.lo>>>16&255,this.lo>>>24,this.hi&255,this.hi>>>8&255,this.hi>>>16&255,this.hi>>>24)};D.prototype.zzEncode=function(){var t=this.hi>>31;return this.hi=((this.hi<<1|this.lo>>>31)^t)>>>0,this.lo=(this.lo<<1^t)>>>0,this};D.prototype.zzDecode=function(){var t=-(this.lo&1);return this.lo=((this.lo>>>1|this.hi<<31)^t)>>>0,this.hi=(this.hi>>>1^t)>>>0,this};D.prototype.length=function(){var t=this.lo,r=(this.lo>>>28|this.hi<<4)>>>0,n=this.hi>>>24;return n===0?r===0?t<16384?t<128?1:2:t<2097152?3:4:r<16384?r<128?5:6:r<2097152?7:8:n<128?9:10}});var At=_(Wn=>{\"use strict\";var h=Wn;h.asPromise=ds();h.base64=ms();h.EventEmitter=Ss();h.float=bs();h.inquire=Ps();h.utf8=Cs();h.pool=ws();h.LongBits=xs();h.isNode=!!(typeof global<\"u\"&&global&&global.process&&global.process.versions&&global.process.versions.node);h.global=h.isNode&&global||typeof window<\"u\"&&window||typeof self<\"u\"&&self||Wn;h.emptyArray=Object.freeze?Object.freeze([]):[];h.emptyObject=Object.freeze?Object.freeze({}):{};h.isInteger=Number.isInteger||function(t){return typeof t==\"number\"&&isFinite(t)&&Math.floor(t)===t};h.isString=function(t){return typeof t==\"string\"||t instanceof String};h.isObject=function(t){return t&&typeof t==\"object\"};h.isset=h.isSet=function(t,r){var n=t[r];return n!=null&&t.hasOwnProperty(r)?typeof n!=\"object\"||(Array.isArray(n)?n.length:Object.keys(n).length)>0:!1};h.Buffer=function(){try{var e=h.inquire(\"buffer\").Buffer;return e.prototype.utf8Write?e:null}catch{return null}}();h._Buffer_from=null;h._Buffer_allocUnsafe=null;h.newBuffer=function(t){return typeof t==\"number\"?h.Buffer?h._Buffer_allocUnsafe(t):new h.Array(t):h.Buffer?h._Buffer_from(t):typeof Uint8Array>\"u\"?t:new Uint8Array(t)};h.Array=typeof Uint8Array<\"u\"?Uint8Array:Array;h.Long=h.global.dcodeIO&&h.global.dcodeIO.Long||h.global.Long||h.inquire(\"long\");h.key2Re=/^true|false|0|1$/;h.key32Re=/^-?(?:0|[1-9][0-9]*)$/;h.key64Re=/^(?:[\\\\x00-\\\\xff]{8}|-?(?:0|[1-9][0-9]*))$/;h.longToHash=function(t){return t?h.LongBits.from(t).toHash():h.LongBits.zeroHash};h.longFromHash=function(t,r){var n=h.LongBits.fromHash(t);return h.Long?h.Long.fromBits(n.lo,n.hi,r):n.toNumber(!!r)};function Ds(e,t,r){for(var n=Object.keys(t),o=0;o<n.length;++o)(e[n[o]]===void 0||!r)&&(e[n[o]]=t[n[o]]);return e}h.merge=Ds;h.lcFirst=function(t){return t.charAt(0).toLowerCase()+t.substring(1)};function Us(e){function t(r,n){if(!(this instanceof t))return new t(r,n);Object.defineProperty(this,\"message\",{get:function(){return r}}),Error.captureStackTrace?Error.captureStackTrace(this,t):Object.defineProperty(this,\"stack\",{value:new Error().stack||\"\"}),n&&Ds(this,n)}return t.prototype=Object.create(Error.prototype,{constructor:{value:t,writable:!0,enumerable:!1,configurable:!0},name:{get:function(){return e},set:void 0,enumerable:!1,configurable:!0},toString:{value:function(){return this.name+\": \"+this.message},writable:!0,enumerable:!1,configurable:!0}}),t}h.newError=Us;h.ProtocolError=Us(\"ProtocolError\");h.oneOfGetter=function(t){for(var r={},n=0;n<t.length;++n)r[t[n]]=1;return function(){for(var o=Object.keys(this),i=o.length-1;i>-1;--i)if(r[o[i]]===1&&this[o[i]]!==void 0&&this[o[i]]!==null)return o[i]}};h.oneOfSetter=function(t){return function(r){for(var n=0;n<t.length;++n)t[n]!==r&&delete this[t[n]]}};h.toJSONOptions={longs:String,enums:String,bytes:String,json:!0};h._configure=function(){var e=h.Buffer;if(!e){h._Buffer_from=h._Buffer_allocUnsafe=null;return}h._Buffer_from=e.from!==Uint8Array.from&&e.from||function(r,n){return new e(r,n)},h._Buffer_allocUnsafe=e.allocUnsafe||function(r){return new e(r)}}});var ti=_((mm,Hs)=>{\"use strict\";Hs.exports=y;var W=At(),Yn,Rr=W.LongBits,Bs=W.base64,Gs=W.utf8;function Ee(e,t,r){this.fn=e,this.len=t,this.next=void 0,this.val=r}function $n(){}function up(e){this.head=e.head,this.tail=e.tail,this.len=e.len,this.next=e.states}function y(){this.len=0,this.head=new Ee($n,0,0),this.tail=this.head,this.states=null}var Vs=function(){return W.Buffer?function(){return(y.create=function(){return new Yn})()}:function(){return new y}};y.create=Vs();y.alloc=function(t){return new W.Array(t)};W.Array!==Array&&(y.alloc=W.pool(y.alloc,W.Array.prototype.subarray));y.prototype._push=function(t,r,n){return this.tail=this.tail.next=new Ee(t,r,n),this.len+=r,this};function Qn(e,t,r){t[r]=e&255}function cp(e,t,r){for(;e>127;)t[r++]=e&127|128,e>>>=7;t[r]=e}function Zn(e,t){this.len=e,this.next=void 0,this.val=t}Zn.prototype=Object.create(Ee.prototype);Zn.prototype.fn=cp;y.prototype.uint32=function(t){return this.len+=(this.tail=this.tail.next=new Zn((t=t>>>0)<128?1:t<16384?2:t<2097152?3:t<268435456?4:5,t)).len,this};y.prototype.int32=function(t){return t<0?this._push(Jn,10,Rr.fromNumber(t)):this.uint32(t)};y.prototype.sint32=function(t){return this.uint32((t<<1^t>>31)>>>0)};function Jn(e,t,r){for(;e.hi;)t[r++]=e.lo&127|128,e.lo=(e.lo>>>7|e.hi<<25)>>>0,e.hi>>>=7;for(;e.lo>127;)t[r++]=e.lo&127|128,e.lo=e.lo>>>7;t[r++]=e.lo}y.prototype.uint64=function(t){var r=Rr.from(t);return this._push(Jn,r.length(),r)};y.prototype.int64=y.prototype.uint64;y.prototype.sint64=function(t){var r=Rr.from(t).zzEncode();return this._push(Jn,r.length(),r)};y.prototype.bool=function(t){return this._push(Qn,1,t?1:0)};function zn(e,t,r){t[r]=e&255,t[r+1]=e>>>8&255,t[r+2]=e>>>16&255,t[r+3]=e>>>24}y.prototype.fixed32=function(t){return this._push(zn,4,t>>>0)};y.prototype.sfixed32=y.prototype.fixed32;y.prototype.fixed64=function(t){var r=Rr.from(t);return this._push(zn,4,r.lo)._push(zn,4,r.hi)};y.prototype.sfixed64=y.prototype.fixed64;y.prototype.float=function(t){return this._push(W.float.writeFloatLE,4,t)};y.prototype.double=function(t){return this._push(W.float.writeDoubleLE,8,t)};var lp=W.Array.prototype.set?function(t,r,n){r.set(t,n)}:function(t,r,n){for(var o=0;o<t.length;++o)r[n+o]=t[o]};y.prototype.bytes=function(t){var r=t.length>>>0;if(!r)return this._push(Qn,1,0);if(W.isString(t)){var n=y.alloc(r=Bs.length(t));Bs.decode(t,n,0),t=n}return this.uint32(r)._push(lp,r,t)};y.prototype.string=function(t){var r=Gs.length(t);return r?this.uint32(r)._push(Gs.write,r,t):this._push(Qn,1,0)};y.prototype.fork=function(){return this.states=new up(this),this.head=this.tail=new Ee($n,0,0),this.len=0,this};y.prototype.reset=function(){return this.states?(this.head=this.states.head,this.tail=this.states.tail,this.len=this.states.len,this.states=this.states.next):(this.head=this.tail=new Ee($n,0,0),this.len=0),this};y.prototype.ldelim=function(){var t=this.head,r=this.tail,n=this.len;return this.reset().uint32(n),n&&(this.tail.next=t.next,this.tail=r,this.len+=n),this};y.prototype.finish=function(){for(var t=this.head.next,r=this.constructor.alloc(this.len),n=0;t;)t.fn(t.val,r,n),n+=t.len,t=t.next;return r};y._configure=function(e){Yn=e,y.create=Vs(),Yn._configure()}});var ks=_((Tm,js)=>{\"use strict\";js.exports=it;var Fs=ti();(it.prototype=Object.create(Fs.prototype)).constructor=it;var Rt=At();function it(){Fs.call(this)}it._configure=function(){it.alloc=Rt._Buffer_allocUnsafe,it.writeBytesBuffer=Rt.Buffer&&Rt.Buffer.prototype instanceof Uint8Array&&Rt.Buffer.prototype.set.name===\"set\"?function(t,r,n){r.set(t,n)}:function(t,r,n){if(t.copy)t.copy(r,n,0,t.length);else for(var o=0;o<t.length;)r[n++]=t[o++]}};it.prototype.bytes=function(t){Rt.isString(t)&&(t=Rt._Buffer_from(t,\"base64\"));var r=t.length>>>0;return this.uint32(r),r&&this._push(it.writeBytesBuffer,r,t),this};function fp(e,t,r){e.length<40?Rt.utf8.write(e,t,r):t.utf8Write?t.utf8Write(e,r):t.write(e,r)}it.prototype.string=function(t){var r=Rt.Buffer.byteLength(t);return this.uint32(r),r&&this._push(fp,r,t),this};it._configure()});var ni=_((Sm,Ys)=>{\"use strict\";Ys.exports=N;var J=At(),ri,Ks=J.LongBits,pp=J.utf8;function tt(e,t){return RangeError(\"index out of range: \"+e.pos+\" + \"+(t||1)+\" > \"+e.len)}function N(e){this.buf=e,this.pos=0,this.len=e.length}var qs=typeof Uint8Array<\"u\"?function(t){if(t instanceof Uint8Array||Array.isArray(t))return new N(t);throw Error(\"illegal buffer\")}:function(t){if(Array.isArray(t))return new N(t);throw Error(\"illegal buffer\")},Ws=function(){return J.Buffer?function(r){return(N.create=function(o){return J.Buffer.isBuffer(o)?new ri(o):qs(o)})(r)}:qs};N.create=Ws();N.prototype._slice=J.Array.prototype.subarray||J.Array.prototype.slice;N.prototype.uint32=function(){var t=4294967295;return function(){if(t=(this.buf[this.pos]&127)>>>0,this.buf[this.pos++]<128||(t=(t|(this.buf[this.pos]&127)<<7)>>>0,this.buf[this.pos++]<128)||(t=(t|(this.buf[this.pos]&127)<<14)>>>0,this.buf[this.pos++]<128)||(t=(t|(this.buf[this.pos]&127)<<21)>>>0,this.buf[this.pos++]<128)||(t=(t|(this.buf[this.pos]&15)<<28)>>>0,this.buf[this.pos++]<128))return t;if((this.pos+=5)>this.len)throw this.pos=this.len,tt(this,10);return t}}();N.prototype.int32=function(){return this.uint32()|0};N.prototype.sint32=function(){var t=this.uint32();return t>>>1^-(t&1)|0};function ei(){var e=new Ks(0,0),t=0;if(this.len-this.pos>4){for(;t<4;++t)if(e.lo=(e.lo|(this.buf[this.pos]&127)<<t*7)>>>0,this.buf[this.pos++]<128)return e;if(e.lo=(e.lo|(this.buf[this.pos]&127)<<28)>>>0,e.hi=(e.hi|(this.buf[this.pos]&127)>>4)>>>0,this.buf[this.pos++]<128)return e;t=0}else{for(;t<3;++t){if(this.pos>=this.len)throw tt(this);if(e.lo=(e.lo|(this.buf[this.pos]&127)<<t*7)>>>0,this.buf[this.pos++]<128)return e}return e.lo=(e.lo|(this.buf[this.pos++]&127)<<t*7)>>>0,e}if(this.len-this.pos>4){for(;t<5;++t)if(e.hi=(e.hi|(this.buf[this.pos]&127)<<t*7+3)>>>0,this.buf[this.pos++]<128)return e}else for(;t<5;++t){if(this.pos>=this.len)throw tt(this);if(e.hi=(e.hi|(this.buf[this.pos]&127)<<t*7+3)>>>0,this.buf[this.pos++]<128)return e}throw Error(\"invalid varint encoding\")}N.prototype.bool=function(){return this.uint32()!==0};function Or(e,t){return(e[t-4]|e[t-3]<<8|e[t-2]<<16|e[t-1]<<24)>>>0}N.prototype.fixed32=function(){if(this.pos+4>this.len)throw tt(this,4);return Or(this.buf,this.pos+=4)};N.prototype.sfixed32=function(){if(this.pos+4>this.len)throw tt(this,4);return Or(this.buf,this.pos+=4)|0};function Xs(){if(this.pos+8>this.len)throw tt(this,8);return new Ks(Or(this.buf,this.pos+=4),Or(this.buf,this.pos+=4))}N.prototype.float=function(){if(this.pos+4>this.len)throw tt(this,4);var t=J.float.readFloatLE(this.buf,this.pos);return this.pos+=4,t};N.prototype.double=function(){if(this.pos+8>this.len)throw tt(this,4);var t=J.float.readDoubleLE(this.buf,this.pos);return this.pos+=8,t};N.prototype.bytes=function(){var t=this.uint32(),r=this.pos,n=this.pos+t;if(n>this.len)throw tt(this,t);if(this.pos+=t,Array.isArray(this.buf))return this.buf.slice(r,n);if(r===n){var o=J.Buffer;return o?o.alloc(0):new this.buf.constructor(0)}return this._slice.call(this.buf,r,n)};N.prototype.string=function(){var t=this.bytes();return pp.read(t,0,t.length)};N.prototype.skip=function(t){if(typeof t==\"number\"){if(this.pos+t>this.len)throw tt(this,t);this.pos+=t}else do if(this.pos>=this.len)throw tt(this);while(this.buf[this.pos++]&128);return this};N.prototype.skipType=function(e){switch(e){case 0:this.skip();break;case 1:this.skip(8);break;case 2:this.skip(this.uint32());break;case 3:for(;(e=this.uint32()&7)!==4;)this.skipType(e);break;case 5:this.skip(4);break;default:throw Error(\"invalid wire type \"+e+\" at offset \"+this.pos)}return this};N._configure=function(e){ri=e,N.create=Ws(),ri._configure();var t=J.Long?\"toLong\":\"toNumber\";J.merge(N.prototype,{int64:function(){return ei.call(this)[t](!1)},uint64:function(){return ei.call(this)[t](!0)},sint64:function(){return ei.call(this).zzDecode()[t](!1)},fixed64:function(){return Xs.call(this)[t](!0)},sfixed64:function(){return Xs.call(this)[t](!1)}})}});var Zs=_((gm,Qs)=>{\"use strict\";Qs.exports=Ut;var $s=ni();(Ut.prototype=Object.create($s.prototype)).constructor=Ut;var zs=At();function Ut(e){$s.call(this,e)}Ut._configure=function(){zs.Buffer&&(Ut.prototype._slice=zs.Buffer.prototype.slice)};Ut.prototype.string=function(){var t=this.uint32();return this.buf.utf8Slice?this.buf.utf8Slice(this.pos,this.pos=Math.min(this.pos+t,this.len)):this.buf.toString(\"utf-8\",this.pos,this.pos=Math.min(this.pos+t,this.len))};Ut._configure()});var tu=_((ym,Js)=>{\"use strict\";Js.exports=me;var ii=At();(me.prototype=Object.create(ii.EventEmitter.prototype)).constructor=me;function me(e,t,r){if(typeof e!=\"function\")throw TypeError(\"rpcImpl must be a function\");ii.EventEmitter.call(this),this.rpcImpl=e,this.requestDelimited=!!t,this.responseDelimited=!!r}me.prototype.rpcCall=function e(t,r,n,o,i){if(!o)throw TypeError(\"request must be specified\");var s=this;if(!i)return ii.asPromise(e,s,t,r,n,o);if(!s.rpcImpl){setTimeout(function(){i(Error(\"already ended\"))},0);return}try{return s.rpcImpl(t,r[s.requestDelimited?\"encodeDelimited\":\"encode\"](o).finish(),function(c,a){if(c)return s.emit(\"error\",c,t),i(c);if(a===null){s.end(!0);return}if(!(a instanceof n))try{a=n[s.responseDelimited?\"decodeDelimited\":\"decode\"](a)}catch(f){return s.emit(\"error\",f,t),i(f)}return s.emit(\"data\",a,t),i(null,a)})}catch(u){s.emit(\"error\",u,t),setTimeout(function(){i(u)},0);return}};me.prototype.end=function(t){return this.rpcImpl&&(t||this.rpcImpl(null,null,null),this.rpcImpl=null,this.emit(\"end\").off()),this}});var ru=_(eu=>{\"use strict\";var dp=eu;dp.Service=tu()});var iu=_((Am,nu)=>{\"use strict\";nu.exports={}});var su=_(au=>{\"use strict\";var F=au;F.build=\"minimal\";F.Writer=ti();F.BufferWriter=ks();F.Reader=ni();F.BufferReader=Zs();F.util=At();F.rpc=ru();F.roots=iu();F.configure=ou;function ou(){F.util._configure(),F.Writer._configure(F.BufferWriter),F.Reader._configure(F.BufferReader)}ou()});var cu=_((Om,uu)=>{\"use strict\";uu.exports=su()});E();var Kt=v(P());E();var b=v(P()),ht=v(ae());var So=\"exception\";var wl=function(e){var t=typeof Symbol==\"function\"&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&typeof e.length==\"number\")return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?\"Object is not iterable.\":\"Symbol.iterator is not defined.\")},Ml=function(e,t){var r=typeof Symbol==\"function\"&&e[Symbol.iterator];if(!r)return e;var n=r.call(e),o,i=[],s;try{for(;(t===void 0||t-- >0)&&!(o=n.next()).done;)i.push(o.value)}catch(u){s={error:u}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(s)throw s.error}}return i},go=function(){function e(t,r,n,o,i,s,u,c,a,f){u===void 0&&(u=[]),this.attributes={},this.links=[],this.events=[],this._droppedAttributesCount=0,this._droppedEventsCount=0,this._droppedLinksCount=0,this.status={code:l.SpanStatusCode.UNSET},this.endTime=[0,0],this._ended=!1,this._duration=[-1,-1],this.name=n,this._spanContext=o,this.parentSpanId=s,this.kind=i,this.links=u;var p=Date.now();this._performanceStartTime=b.otperformance.now(),this._performanceOffset=p-(this._performanceStartTime+(0,b.getTimeOrigin)()),this._startTimeProvided=c!=null,this.startTime=this._getTime(c??p),this.resource=t.resource,this.instrumentationLibrary=t.instrumentationLibrary,this._spanLimits=t.getSpanLimits(),f!=null&&this.setAttributes(f),this._spanProcessor=t.getActiveSpanProcessor(),this._spanProcessor.onStart(this,r),this._attributeValueLengthLimit=this._spanLimits.attributeValueLengthLimit||0}return e.prototype.spanContext=function(){return this._spanContext},e.prototype.setAttribute=function(t,r){return r==null||this._isSpanEnded()?this:t.length===0?(l.diag.warn(\"Invalid attribute key: \"+t),this):(0,b.isAttributeValue)(r)?Object.keys(this.attributes).length>=this._spanLimits.attributeCountLimit&&!Object.prototype.hasOwnProperty.call(this.attributes,t)?(this._droppedAttributesCount++,this):(this.attributes[t]=this._truncateToSize(r),this):(l.diag.warn(\"Invalid attribute value set for key: \"+t),this)},e.prototype.setAttributes=function(t){var r,n;try{for(var o=wl(Object.entries(t)),i=o.next();!i.done;i=o.next()){var s=Ml(i.value,2),u=s[0],c=s[1];this.setAttribute(u,c)}}catch(a){r={error:a}}finally{try{i&&!i.done&&(n=o.return)&&n.call(o)}finally{if(r)throw r.error}}return this},e.prototype.addEvent=function(t,r,n){if(this._isSpanEnded())return this;if(this._spanLimits.eventCountLimit===0)return l.diag.warn(\"No events allowed.\"),this._droppedEventsCount++,this;this.events.length>=this._spanLimits.eventCountLimit&&(l.diag.warn(\"Dropping extra events.\"),this.events.shift(),this._droppedEventsCount++),(0,b.isTimeInput)(r)&&((0,b.isTimeInput)(n)||(n=r),r=void 0);var o=(0,b.sanitizeAttributes)(r);return this.events.push({name:t,attributes:o,time:this._getTime(n),droppedAttributesCount:0}),this},e.prototype.setStatus=function(t){return this._isSpanEnded()?this:(this.status=t,this)},e.prototype.updateName=function(t){return this._isSpanEnded()?this:(this.name=t,this)},e.prototype.end=function(t){if(this._isSpanEnded()){l.diag.error(this.name+\" \"+this._spanContext.traceId+\"-\"+this._spanContext.spanId+\" - You can only call end() on a span once.\");return}this._ended=!0,this.endTime=this._getTime(t),this._duration=(0,b.hrTimeDuration)(this.startTime,this.endTime),this._duration[0]<0&&(l.diag.warn(\"Inconsistent start and end time, startTime > endTime. Setting span duration to 0ms.\",this.startTime,this.endTime),this.endTime=this.startTime.slice(),this._duration=[0,0]),this._spanProcessor.onEnd(this)},e.prototype._getTime=function(t){if(typeof t==\"number\"&&t<b.otperformance.now())return(0,b.hrTime)(t+this._performanceOffset);if(typeof t==\"number\")return(0,b.millisToHrTime)(t);if(t instanceof Date)return(0,b.millisToHrTime)(t.getTime());if((0,b.isTimeInputHrTime)(t))return t;if(this._startTimeProvided)return(0,b.millisToHrTime)(Date.now());var r=b.otperformance.now()-this._performanceStartTime;return(0,b.addHrTimes)(this.startTime,(0,b.millisToHrTime)(r))},e.prototype.isRecording=function(){return this._ended===!1},e.prototype.recordException=function(t,r){var n={};typeof t==\"string\"?n[ht.SemanticAttributes.EXCEPTION_MESSAGE]=t:t&&(t.code?n[ht.SemanticAttributes.EXCEPTION_TYPE]=t.code.toString():t.name&&(n[ht.SemanticAttributes.EXCEPTION_TYPE]=t.name),t.message&&(n[ht.SemanticAttributes.EXCEPTION_MESSAGE]=t.message),t.stack&&(n[ht.SemanticAttributes.EXCEPTION_STACKTRACE]=t.stack)),n[ht.SemanticAttributes.EXCEPTION_TYPE]||n[ht.SemanticAttributes.EXCEPTION_MESSAGE]?this.addEvent(So,n,r):l.diag.warn(\"Failed to record an exception \"+t)},Object.defineProperty(e.prototype,\"duration\",{get:function(){return this._duration},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,\"ended\",{get:function(){return this._ended},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,\"droppedAttributesCount\",{get:function(){return this._droppedAttributesCount},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,\"droppedEventsCount\",{get:function(){return this._droppedEventsCount},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,\"droppedLinksCount\",{get:function(){return this._droppedLinksCount},enumerable:!1,configurable:!0}),e.prototype._isSpanEnded=function(){return this._ended&&l.diag.warn(\"Can not execute the operation on ended Span {traceId: \"+this._spanContext.traceId+\", spanId: \"+this._spanContext.spanId+\"}\"),this._ended},e.prototype._truncateToLimitUtil=function(t,r){return t.length<=r?t:t.substr(0,r)},e.prototype._truncateToSize=function(t){var r=this,n=this._attributeValueLengthLimit;return n<=0?(l.diag.warn(\"Attribute value limit must be positive, got \"+n),t):typeof t==\"string\"?this._truncateToLimitUtil(t,n):Array.isArray(t)?t.map(function(o){return typeof o==\"string\"?r._truncateToLimitUtil(o,n):o}):t},e}();E();var x=v(P());var ot;(function(e){e[e.NOT_RECORD=0]=\"NOT_RECORD\",e[e.RECORD=1]=\"RECORD\",e[e.RECORD_AND_SAMPLED=2]=\"RECORD_AND_SAMPLED\"})(ot||(ot={}));var at=function(){function e(){}return e.prototype.shouldSample=function(){return{decision:ot.NOT_RECORD}},e.prototype.toString=function(){return\"AlwaysOffSampler\"},e}();var X=function(){function e(){}return e.prototype.shouldSample=function(){return{decision:ot.RECORD_AND_SAMPLED}},e.prototype.toString=function(){return\"AlwaysOnSampler\"},e}();E();var yo=v(P());var Et=function(){function e(t){var r,n,o,i;this._root=t.root,this._root||((0,yo.globalErrorHandler)(new Error(\"ParentBasedSampler must have a root sampler configured\")),this._root=new X),this._remoteParentSampled=(r=t.remoteParentSampled)!==null&&r!==void 0?r:new X,this._remoteParentNotSampled=(n=t.remoteParentNotSampled)!==null&&n!==void 0?n:new at,this._localParentSampled=(o=t.localParentSampled)!==null&&o!==void 0?o:new X,this._localParentNotSampled=(i=t.localParentNotSampled)!==null&&i!==void 0?i:new at}return e.prototype.shouldSample=function(t,r,n,o,i,s){var u=l.trace.getSpanContext(t);return!u||!(0,l.isSpanContextValid)(u)?this._root.shouldSample(t,r,n,o,i,s):u.isRemote?u.traceFlags&l.TraceFlags.SAMPLED?this._remoteParentSampled.shouldSample(t,r,n,o,i,s):this._remoteParentNotSampled.shouldSample(t,r,n,o,i,s):u.traceFlags&l.TraceFlags.SAMPLED?this._localParentSampled.shouldSample(t,r,n,o,i,s):this._localParentNotSampled.shouldSample(t,r,n,o,i,s)},e.prototype.toString=function(){return\"ParentBased{root=\"+this._root.toString()+\", remoteParentSampled=\"+this._remoteParentSampled.toString()+\", remoteParentNotSampled=\"+this._remoteParentNotSampled.toString()+\", localParentSampled=\"+this._localParentSampled.toString()+\", localParentNotSampled=\"+this._localParentNotSampled.toString()+\"}\"},e}();E();var kt=function(){function e(t){t===void 0&&(t=0),this._ratio=t,this._ratio=this._normalize(t),this._upperBound=Math.floor(this._ratio*4294967295)}return e.prototype.shouldSample=function(t,r){return{decision:(0,l.isValidTraceId)(r)&&this._accumulate(r)<this._upperBound?ot.RECORD_AND_SAMPLED:ot.NOT_RECORD}},e.prototype.toString=function(){return\"TraceIdRatioBased{\"+this._ratio+\"}\"},e.prototype._normalize=function(t){return typeof t!=\"number\"||isNaN(t)?0:t>=1?1:t<=0?0:t},e.prototype._accumulate=function(t){for(var r=0,n=0;n<t.length/8;n++){var o=n*8,i=parseInt(t.slice(o,o+8),16);r=(r^i)>>>0}return r},e}();var xl=(0,x.getEnv)(),Dl=x.TracesSamplerValues.AlwaysOn,qt=1;function Ze(){return{sampler:mn(xl),forceFlushTimeoutMillis:3e4,generalLimits:{attributeValueLengthLimit:(0,x.getEnv)().OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT,attributeCountLimit:(0,x.getEnv)().OTEL_ATTRIBUTE_COUNT_LIMIT},spanLimits:{attributeValueLengthLimit:(0,x.getEnv)().OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT,attributeCountLimit:(0,x.getEnv)().OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT,linkCountLimit:(0,x.getEnv)().OTEL_SPAN_LINK_COUNT_LIMIT,eventCountLimit:(0,x.getEnv)().OTEL_SPAN_EVENT_COUNT_LIMIT,attributePerEventCountLimit:(0,x.getEnv)().OTEL_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT,attributePerLinkCountLimit:(0,x.getEnv)().OTEL_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT}}}function mn(e){switch(e===void 0&&(e=(0,x.getEnv)()),e.OTEL_TRACES_SAMPLER){case x.TracesSamplerValues.AlwaysOn:return new X;case x.TracesSamplerValues.AlwaysOff:return new at;case x.TracesSamplerValues.ParentBasedAlwaysOn:return new Et({root:new X});case x.TracesSamplerValues.ParentBasedAlwaysOff:return new Et({root:new at});case x.TracesSamplerValues.TraceIdRatio:return new kt(vo(e));case x.TracesSamplerValues.ParentBasedTraceIdRatio:return new Et({root:new kt(vo(e))});default:return l.diag.error('OTEL_TRACES_SAMPLER value \"'+e.OTEL_TRACES_SAMPLER+\" invalid, defaulting to \"+Dl+'\".'),new X}}function vo(e){if(e.OTEL_TRACES_SAMPLER_ARG===void 0||e.OTEL_TRACES_SAMPLER_ARG===\"\")return l.diag.error(\"OTEL_TRACES_SAMPLER_ARG is blank, defaulting to \"+qt+\".\"),qt;var t=Number(e.OTEL_TRACES_SAMPLER_ARG);return isNaN(t)?(l.diag.error(\"OTEL_TRACES_SAMPLER_ARG=\"+e.OTEL_TRACES_SAMPLER_ARG+\" was given, but it is invalid, defaulting to \"+qt+\".\"),qt):t<0||t>1?(l.diag.error(\"OTEL_TRACES_SAMPLER_ARG=\"+e.OTEL_TRACES_SAMPLER_ARG+\" was given, but it is out of range ([0..1]), defaulting to \"+qt+\".\"),qt):t}var Xt=v(P());function Ao(e){var t={sampler:mn()},r=Ze(),n=Object.assign({},r,t,e);return n.generalLimits=Object.assign({},r.generalLimits,e.generalLimits||{}),n.spanLimits=Object.assign({},r.spanLimits,e.spanLimits||{}),n}function Ro(e){var t,r,n,o,i,s,u,c,a,f,p,d,m=Object.assign({},e.spanLimits),g=(0,Xt.getEnvWithoutDefaults)();return m.attributeCountLimit=(s=(i=(o=(r=(t=e.spanLimits)===null||t===void 0?void 0:t.attributeCountLimit)!==null&&r!==void 0?r:(n=e.generalLimits)===null||n===void 0?void 0:n.attributeCountLimit)!==null&&o!==void 0?o:g.OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT)!==null&&i!==void 0?i:g.OTEL_ATTRIBUTE_COUNT_LIMIT)!==null&&s!==void 0?s:Xt.DEFAULT_ATTRIBUTE_COUNT_LIMIT,m.attributeValueLengthLimit=(d=(p=(f=(c=(u=e.spanLimits)===null||u===void 0?void 0:u.attributeValueLengthLimit)!==null&&c!==void 0?c:(a=e.generalLimits)===null||a===void 0?void 0:a.attributeValueLengthLimit)!==null&&f!==void 0?f:g.OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT)!==null&&p!==void 0?p:g.OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT)!==null&&d!==void 0?d:Xt.DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT,Object.assign({},e,{spanLimits:m})}E();var k=v(P()),Oo=function(){function e(t,r){this._exporter=t,this._isExporting=!1,this._finishedSpans=[],this._droppedSpansCount=0;var n=(0,k.getEnv)();this._maxExportBatchSize=typeof r?.maxExportBatchSize==\"number\"?r.maxExportBatchSize:n.OTEL_BSP_MAX_EXPORT_BATCH_SIZE,this._maxQueueSize=typeof r?.maxQueueSize==\"number\"?r.maxQueueSize:n.OTEL_BSP_MAX_QUEUE_SIZE,this._scheduledDelayMillis=typeof r?.scheduledDelayMillis==\"number\"?r.scheduledDelayMillis:n.OTEL_BSP_SCHEDULE_DELAY,this._exportTimeoutMillis=typeof r?.exportTimeoutMillis==\"number\"?r.exportTimeoutMillis:n.OTEL_BSP_EXPORT_TIMEOUT,this._shutdownOnce=new k.BindOnceFuture(this._shutdown,this),this._maxExportBatchSize>this._maxQueueSize&&(l.diag.warn(\"BatchSpanProcessor: maxExportBatchSize must be smaller or equal to maxQueueSize, setting maxExportBatchSize to match maxQueueSize\"),this._maxExportBatchSize=this._maxQueueSize)}return e.prototype.forceFlush=function(){return this._shutdownOnce.isCalled?this._shutdownOnce.promise:this._flushAll()},e.prototype.onStart=function(t,r){},e.prototype.onEnd=function(t){this._shutdownOnce.isCalled||t.spanContext().traceFlags&l.TraceFlags.SAMPLED&&this._addToBuffer(t)},e.prototype.shutdown=function(){return this._shutdownOnce.call()},e.prototype._shutdown=function(){var t=this;return Promise.resolve().then(function(){return t.onShutdown()}).then(function(){return t._flushAll()}).then(function(){return t._exporter.shutdown()})},e.prototype._addToBuffer=function(t){if(this._finishedSpans.length>=this._maxQueueSize){this._droppedSpansCount===0&&l.diag.debug(\"maxQueueSize reached, dropping spans\"),this._droppedSpansCount++;return}this._droppedSpansCount>0&&(l.diag.warn(\"Dropped \"+this._droppedSpansCount+\" spans because maxQueueSize reached\"),this._droppedSpansCount=0),this._finishedSpans.push(t),this._maybeStartTimer()},e.prototype._flushAll=function(){var t=this;return new Promise(function(r,n){for(var o=[],i=Math.ceil(t._finishedSpans.length/t._maxExportBatchSize),s=0,u=i;s<u;s++)o.push(t._flushOneBatch());Promise.all(o).then(function(){r()}).catch(n)})},e.prototype._flushOneBatch=function(){var t=this;return this._clearTimer(),this._finishedSpans.length===0?Promise.resolve():new Promise(function(r,n){var o=setTimeout(function(){n(new Error(\"Timeout\"))},t._exportTimeoutMillis);l.context.with((0,k.suppressTracing)(l.context.active()),function(){var i=t._finishedSpans.splice(0,t._maxExportBatchSize),s=function(){return t._exporter.export(i,function(c){var a;clearTimeout(o),c.code===k.ExportResultCode.SUCCESS?r():n((a=c.error)!==null&&a!==void 0?a:new Error(\"BatchSpanProcessor: span export failed\"))})},u=i.map(function(c){return c.resource}).filter(function(c){return c.asyncAttributesPending});u.length===0?s():Promise.all(u.map(function(c){var a;return(a=c.waitForAsyncAttributes)===null||a===void 0?void 0:a.call(c)})).then(s,function(c){(0,k.globalErrorHandler)(c),n(c)})})})},e.prototype._maybeStartTimer=function(){var t=this;if(!this._isExporting){var r=function(){t._isExporting=!0,t._flushOneBatch().then(function(){t._isExporting=!1,t._finishedSpans.length>0&&(t._clearTimer(),t._maybeStartTimer())}).catch(function(n){t._isExporting=!1,(0,k.globalErrorHandler)(n)})};if(this._finishedSpans.length>=this._maxExportBatchSize)return r();this._timer===void 0&&(this._timer=setTimeout(function(){return r()},this._scheduledDelayMillis),(0,k.unrefTimer)(this._timer))}},e.prototype._clearTimer=function(){this._timer!==void 0&&(clearTimeout(this._timer),this._timer=void 0)},e}();var Ul=function(){var e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,o){n.__proto__=o}||function(n,o){for(var i in o)Object.prototype.hasOwnProperty.call(o,i)&&(n[i]=o[i])},e(t,r)};return function(t,r){if(typeof r!=\"function\"&&r!==null)throw new TypeError(\"Class extends value \"+String(r)+\" is not a constructor or null\");e(t,r);function n(){this.constructor=t}t.prototype=r===null?Object.create(r):(n.prototype=r.prototype,new n)}}(),Mt=function(e){Ul(t,e);function t(r,n){var o=e.call(this,r,n)||this;return o.onInit(n),o}return t.prototype.onInit=function(r){var n=this;r?.disableAutoFlushOnDocumentHide!==!0&&typeof document<\"u\"&&(this._visibilityChangeListener=function(){document.visibilityState===\"hidden\"&&n.forceFlush()},this._pageHideListener=function(){n.forceFlush()},document.addEventListener(\"visibilitychange\",this._visibilityChangeListener),document.addEventListener(\"pagehide\",this._pageHideListener))},t.prototype.onShutdown=function(){typeof document<\"u\"&&(this._visibilityChangeListener&&document.removeEventListener(\"visibilitychange\",this._visibilityChangeListener),this._pageHideListener&&document.removeEventListener(\"pagehide\",this._pageHideListener))},t}(Oo);var Bl=8,Gl=16,tr=function(){function e(){this.generateTraceId=bo(Gl),this.generateSpanId=bo(Bl)}return e}();var Je=Array(32);function bo(e){return function(){for(var r=0;r<e*2;r++)Je[r]=Math.floor(Math.random()*16)+48,Je[r]>=58&&(Je[r]+=39);return String.fromCharCode.apply(null,Je.slice(0,e*2))}}var Lo=function(){function e(t,r,n){this._tracerProvider=n;var o=Ao(r);this._sampler=o.sampler,this._generalLimits=o.generalLimits,this._spanLimits=o.spanLimits,this._idGenerator=r.idGenerator||new tr,this.resource=n.resource,this.instrumentationLibrary=t}return e.prototype.startSpan=function(t,r,n){var o,i,s;r===void 0&&(r={}),n===void 0&&(n=l.context.active()),r.root&&(n=l.trace.deleteSpan(n));var u=l.trace.getSpan(n);if((0,Kt.isTracingSuppressed)(n)){l.diag.debug(\"Instrumentation suppressed, returning Noop Span\");var c=l.trace.wrapSpanContext(l.INVALID_SPAN_CONTEXT);return c}var a=u?.spanContext(),f=this._idGenerator.generateSpanId(),p,d,m;!a||!l.trace.isSpanContextValid(a)?p=this._idGenerator.generateTraceId():(p=a.traceId,d=a.traceState,m=a.spanId);var g=(o=r.kind)!==null&&o!==void 0?o:l.SpanKind.INTERNAL,C=((i=r.links)!==null&&i!==void 0?i:[]).map(function(bt){return{context:bt.context,attributes:(0,Kt.sanitizeAttributes)(bt.attributes)}}),M=(0,Kt.sanitizeAttributes)(r.attributes),T=this._sampler.shouldSample(n,p,t,g,M,C);d=(s=T.traceState)!==null&&s!==void 0?s:d;var et=T.decision===l.SamplingDecision.RECORD_AND_SAMPLED?l.TraceFlags.SAMPLED:l.TraceFlags.NONE,q={traceId:p,spanId:f,traceFlags:et,traceState:d};if(T.decision===l.SamplingDecision.NOT_RECORD){l.diag.debug(\"Recording is off, propagating context in a non-recording span\");var c=l.trace.wrapSpanContext(q);return c}var U=(0,Kt.sanitizeAttributes)(Object.assign(M,T.attributes)),wr=new go(this,n,t,q,g,m,C,r.startTime,void 0,U);return wr},e.prototype.startActiveSpan=function(t,r,n,o){var i,s,u;if(!(arguments.length<2)){arguments.length===2?u=r:arguments.length===3?(i=r,u=n):(i=r,s=n,u=o);var c=s??l.context.active(),a=this.startSpan(t,i,c),f=l.trace.setSpan(c,a);return l.context.with(f,u,void 0,a)}},e.prototype.getGeneralLimits=function(){return this._generalLimits},e.prototype.getSpanLimits=function(){return this._spanLimits},e.prototype.getActiveSpanProcessor=function(){return this._tracerProvider.getActiveSpanProcessor()},e}();E();var $=v(P());E();var mt=v(ae()),er=v(P());function Po(){return\"unknown_service\"}var Tt=function(){return Tt=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++){t=arguments[r];for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o])}return e},Tt.apply(this,arguments)},Vl=function(e,t,r,n){function o(i){return i instanceof r?i:new r(function(s){s(i)})}return new(r||(r=Promise))(function(i,s){function u(f){try{a(n.next(f))}catch(p){s(p)}}function c(f){try{a(n.throw(f))}catch(p){s(p)}}function a(f){f.done?i(f.value):o(f.value).then(u,c)}a((n=n.apply(e,t||[])).next())})},Hl=function(e,t){var r={label:0,sent:function(){if(i[0]&1)throw i[1];return i[1]},trys:[],ops:[]},n,o,i,s;return s={next:u(0),throw:u(1),return:u(2)},typeof Symbol==\"function\"&&(s[Symbol.iterator]=function(){return this}),s;function u(a){return function(f){return c([a,f])}}function c(a){if(n)throw new TypeError(\"Generator is already executing.\");for(;r;)try{if(n=1,o&&(i=a[0]&2?o.return:a[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,a[1])).done)return i;switch(o=0,i&&(a=[a[0]&2,i.value]),a[0]){case 0:case 1:i=a;break;case 4:return r.label++,{value:a[1],done:!1};case 5:r.label++,o=a[1],a=[0];continue;case 7:a=r.ops.pop(),r.trys.pop();continue;default:if(i=r.trys,!(i=i.length>0&&i[i.length-1])&&(a[0]===6||a[0]===2)){r=0;continue}if(a[0]===3&&(!i||a[1]>i[0]&&a[1]<i[3])){r.label=a[1];break}if(a[0]===6&&r.label<i[1]){r.label=i[1],i=a;break}if(i&&r.label<i[2]){r.label=i[2],r.ops.push(a);break}i[2]&&r.ops.pop(),r.trys.pop();continue}a=t.call(e,r)}catch(f){a=[6,f],o=0}finally{n=i=0}if(a[0]&5)throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}},Fl=function(e,t){var r=typeof Symbol==\"function\"&&e[Symbol.iterator];if(!r)return e;var n=r.call(e),o,i=[],s;try{for(;(t===void 0||t-- >0)&&!(o=n.next()).done;)i.push(o.value)}catch(u){s={error:u}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(s)throw s.error}}return i},H=function(){function e(t,r){var n=this,o;this._attributes=t,this.asyncAttributesPending=r!=null,this._syncAttributes=(o=this._attributes)!==null&&o!==void 0?o:{},this._asyncAttributesPromise=r?.then(function(i){return n._attributes=Object.assign({},n._attributes,i),n.asyncAttributesPending=!1,i},function(i){return l.diag.debug(\"a resource's async attributes promise rejected: %s\",i),n.asyncAttributesPending=!1,{}})}return e.empty=function(){return e.EMPTY},e.default=function(){var t;return new e((t={},t[mt.SemanticResourceAttributes.SERVICE_NAME]=Po(),t[mt.SemanticResourceAttributes.TELEMETRY_SDK_LANGUAGE]=er.SDK_INFO[mt.SemanticResourceAttributes.TELEMETRY_SDK_LANGUAGE],t[mt.SemanticResourceAttributes.TELEMETRY_SDK_NAME]=er.SDK_INFO[mt.SemanticResourceAttributes.TELEMETRY_SDK_NAME],t[mt.SemanticResourceAttributes.TELEMETRY_SDK_VERSION]=er.SDK_INFO[mt.SemanticResourceAttributes.TELEMETRY_SDK_VERSION],t))},Object.defineProperty(e.prototype,\"attributes\",{get:function(){var t;return this.asyncAttributesPending&&l.diag.error(\"Accessing resource attributes before async attributes settled\"),(t=this._attributes)!==null&&t!==void 0?t:{}},enumerable:!1,configurable:!0}),e.prototype.waitForAsyncAttributes=function(){return Vl(this,void 0,void 0,function(){return Hl(this,function(t){switch(t.label){case 0:return this.asyncAttributesPending?[4,this._asyncAttributesPromise]:[3,2];case 1:t.sent(),t.label=2;case 2:return[2]}})})},e.prototype.merge=function(t){var r=this,n;if(!t)return this;var o=Tt(Tt({},this._syncAttributes),(n=t._syncAttributes)!==null&&n!==void 0?n:t.attributes);if(!this._asyncAttributesPromise&&!t._asyncAttributesPromise)return new e(o);var i=Promise.all([this._asyncAttributesPromise,t._asyncAttributesPromise]).then(function(s){var u,c=Fl(s,2),a=c[0],f=c[1];return Tt(Tt(Tt(Tt({},r._syncAttributes),a),(u=t._syncAttributes)!==null&&u!==void 0?u:t.attributes),f)});return new e(o,i)},e.EMPTY=new e({}),e}();E();var Io=v(P()),Co=v(ae());var jl=function(e){var t=typeof Symbol==\"function\"&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&typeof e.length==\"number\")return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?\"Object is not iterable.\":\"Symbol.iterator is not defined.\")},kl=function(e,t){var r=typeof Symbol==\"function\"&&e[Symbol.iterator];if(!r)return e;var n=r.call(e),o,i=[],s;try{for(;(t===void 0||t-- >0)&&!(o=n.next()).done;)i.push(o.value)}catch(u){s={error:u}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(s)throw s.error}}return i},ql=function(){function e(){this._MAX_LENGTH=255,this._COMMA_SEPARATOR=\",\",this._LABEL_KEY_VALUE_SPLITTER=\"=\",this._ERROR_MESSAGE_INVALID_CHARS=\"should be a ASCII string with a length greater than 0 and not exceed \"+this._MAX_LENGTH+\" characters.\",this._ERROR_MESSAGE_INVALID_VALUE=\"should be a ASCII string with a length not exceed \"+this._MAX_LENGTH+\" characters.\"}return e.prototype.detect=function(t){var r={},n=(0,Io.getEnv)(),o=n.OTEL_RESOURCE_ATTRIBUTES,i=n.OTEL_SERVICE_NAME;if(o)try{var s=this._parseResourceAttributes(o);Object.assign(r,s)}catch(u){l.diag.debug(\"EnvDetector failed: \"+u.message)}return i&&(r[Co.SemanticResourceAttributes.SERVICE_NAME]=i),new H(r)},e.prototype._parseResourceAttributes=function(t){var r,n;if(!t)return{};var o={},i=t.split(this._COMMA_SEPARATOR,-1);try{for(var s=jl(i),u=s.next();!u.done;u=s.next()){var c=u.value,a=c.split(this._LABEL_KEY_VALUE_SPLITTER,-1);if(a.length===2){var f=kl(a,2),p=f[0],d=f[1];if(p=p.trim(),d=d.trim().split(/^\"|\"$/).join(\"\"),!this._isValidAndNotEmpty(p))throw new Error(\"Attribute key \"+this._ERROR_MESSAGE_INVALID_CHARS);if(!this._isValid(d))throw new Error(\"Attribute value \"+this._ERROR_MESSAGE_INVALID_VALUE);o[p]=decodeURIComponent(d)}}}catch(m){r={error:m}}finally{try{u&&!u.done&&(n=s.return)&&n.call(s)}finally{if(r)throw r.error}}return o},e.prototype._isValid=function(t){return t.length<=this._MAX_LENGTH&&this._isBaggageOctetString(t)},e.prototype._isBaggageOctetString=function(t){for(var r=0;r<t.length;r++){var n=t.charCodeAt(r);if(n<33||n===44||n===59||n===92||n>126)return!1}return!0},e.prototype._isValidAndNotEmpty=function(t){return t.length>0&&this._isValid(t)},e}(),No=new ql;E();var wo=function(e){return e!==null&&typeof e==\"object\"&&typeof e.then==\"function\"};var Xl=function(e,t,r,n){function o(i){return i instanceof r?i:new r(function(s){s(i)})}return new(r||(r=Promise))(function(i,s){function u(f){try{a(n.next(f))}catch(p){s(p)}}function c(f){try{a(n.throw(f))}catch(p){s(p)}}function a(f){f.done?i(f.value):o(f.value).then(u,c)}a((n=n.apply(e,t||[])).next())})},Kl=function(e,t){var r={label:0,sent:function(){if(i[0]&1)throw i[1];return i[1]},trys:[],ops:[]},n,o,i,s;return s={next:u(0),throw:u(1),return:u(2)},typeof Symbol==\"function\"&&(s[Symbol.iterator]=function(){return this}),s;function u(a){return function(f){return c([a,f])}}function c(a){if(n)throw new TypeError(\"Generator is already executing.\");for(;r;)try{if(n=1,o&&(i=a[0]&2?o.return:a[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,a[1])).done)return i;switch(o=0,i&&(a=[a[0]&2,i.value]),a[0]){case 0:case 1:i=a;break;case 4:return r.label++,{value:a[1],done:!1};case 5:r.label++,o=a[1],a=[0];continue;case 7:a=r.ops.pop(),r.trys.pop();continue;default:if(i=r.trys,!(i=i.length>0&&i[i.length-1])&&(a[0]===6||a[0]===2)){r=0;continue}if(a[0]===3&&(!i||a[1]>i[0]&&a[1]<i[3])){r.label=a[1];break}if(a[0]===6&&r.label<i[1]){r.label=i[1],i=a;break}if(i&&r.label<i[2]){r.label=i[2],r.ops.push(a);break}i[2]&&r.ops.pop(),r.trys.pop();continue}a=t.call(e,r)}catch(f){a=[6,f],o=0}finally{n=i=0}if(a[0]&5)throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}};var Mo=function(e){var t;e===void 0&&(e={});var r=((t=e.detectors)!==null&&t!==void 0?t:[]).map(function(o){try{var i=o.detect(e),s;if(wo(i)){var u=function(){return Xl(void 0,void 0,void 0,function(){var c;return Kl(this,function(a){switch(a.label){case 0:return[4,i];case 1:return c=a.sent(),[2,c.attributes]}})})};s=new H({},u())}else s=i;return s.waitForAsyncAttributes?s.waitForAsyncAttributes().then(function(){return l.diag.debug(o.constructor.name+\" found resource.\",s)}):l.diag.debug(o.constructor.name+\" found resource.\",s),s}catch(c){return l.diag.error(o.constructor.name+\" failed: \"+c.message),H.empty()}}),n=r.reduce(function(o,i){return o.merge(i)},H.empty());return n.waitForAsyncAttributes&&n.waitForAsyncAttributes().then(function(){Wl(r)}),n},Wl=function(e){e.forEach(function(t){if(Object.keys(t.attributes).length>0){var r=JSON.stringify(t.attributes,null,4);l.diag.verbose(r)}})};var xo=v(P()),rr=function(e){var t=typeof Symbol==\"function\"&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&typeof e.length==\"number\")return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?\"Object is not iterable.\":\"Symbol.iterator is not defined.\")},Do=function(){function e(t){this._spanProcessors=t}return e.prototype.forceFlush=function(){var t,r,n=[];try{for(var o=rr(this._spanProcessors),i=o.next();!i.done;i=o.next()){var s=i.value;n.push(s.forceFlush())}}catch(u){t={error:u}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(t)throw t.error}}return new Promise(function(u){Promise.all(n).then(function(){u()}).catch(function(c){(0,xo.globalErrorHandler)(c||new Error(\"MultiSpanProcessor: forceFlush failed\")),u()})})},e.prototype.onStart=function(t,r){var n,o;try{for(var i=rr(this._spanProcessors),s=i.next();!s.done;s=i.next()){var u=s.value;u.onStart(t,r)}}catch(c){n={error:c}}finally{try{s&&!s.done&&(o=i.return)&&o.call(i)}finally{if(n)throw n.error}}},e.prototype.onEnd=function(t){var r,n;try{for(var o=rr(this._spanProcessors),i=o.next();!i.done;i=o.next()){var s=i.value;s.onEnd(t)}}catch(u){r={error:u}}finally{try{i&&!i.done&&(n=o.return)&&n.call(o)}finally{if(r)throw r.error}}},e.prototype.shutdown=function(){var t,r,n=[];try{for(var o=rr(this._spanProcessors),i=o.next();!i.done;i=o.next()){var s=i.value;n.push(s.shutdown())}}catch(u){t={error:u}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(t)throw t.error}}return new Promise(function(u,c){Promise.all(n).then(function(){u()},c)})},e}();var Uo=function(){function e(){}return e.prototype.onStart=function(t,r){},e.prototype.onEnd=function(t){},e.prototype.shutdown=function(){return Promise.resolve()},e.prototype.forceFlush=function(){return Promise.resolve()},e}();var xt;(function(e){e[e.resolved=0]=\"resolved\",e[e.timeout=1]=\"timeout\",e[e.error=2]=\"error\",e[e.unresolved=3]=\"unresolved\"})(xt||(xt={}));var Bo=function(){function e(t){t===void 0&&(t={});var r;this._registeredSpanProcessors=[],this._tracers=new Map;var n=(0,$.merge)({},Ze(),Ro(t));this.resource=(r=n.resource)!==null&&r!==void 0?r:H.empty(),this.resource=H.default().merge(this.resource),this._config=Object.assign({},n,{resource:this.resource});var o=this._buildExporterFromEnv();if(o!==void 0){var i=new Mt(o);this.activeSpanProcessor=i}else this.activeSpanProcessor=new Uo}return e.prototype.getTracer=function(t,r,n){var o=t+\"@\"+(r||\"\")+\":\"+(n?.schemaUrl||\"\");return this._tracers.has(o)||this._tracers.set(o,new Lo({name:t,version:r,schemaUrl:n?.schemaUrl},this._config,this)),this._tracers.get(o)},e.prototype.addSpanProcessor=function(t){this._registeredSpanProcessors.length===0&&this.activeSpanProcessor.shutdown().catch(function(r){return l.diag.error(\"Error while trying to shutdown current span processor\",r)}),this._registeredSpanProcessors.push(t),this.activeSpanProcessor=new Do(this._registeredSpanProcessors)},e.prototype.getActiveSpanProcessor=function(){return this.activeSpanProcessor},e.prototype.register=function(t){t===void 0&&(t={}),l.trace.setGlobalTracerProvider(this),t.propagator===void 0&&(t.propagator=this._buildPropagatorFromEnv()),t.contextManager&&l.context.setGlobalContextManager(t.contextManager),t.propagator&&l.propagation.setGlobalPropagator(t.propagator)},e.prototype.forceFlush=function(){var t=this._config.forceFlushTimeoutMillis,r=this._registeredSpanProcessors.map(function(n){return new Promise(function(o){var i,s=setTimeout(function(){o(new Error(\"Span processor did not completed within timeout period of \"+t+\" ms\")),i=xt.timeout},t);n.forceFlush().then(function(){clearTimeout(s),i!==xt.timeout&&(i=xt.resolved,o(i))}).catch(function(u){clearTimeout(s),i=xt.error,o(u)})})});return new Promise(function(n,o){Promise.all(r).then(function(i){var s=i.filter(function(u){return u!==xt.resolved});s.length>0?o(s):n()}).catch(function(i){return o([i])})})},e.prototype.shutdown=function(){return this.activeSpanProcessor.shutdown()},e.prototype._getPropagator=function(t){var r;return(r=this.constructor._registeredPropagators.get(t))===null||r===void 0?void 0:r()},e.prototype._getSpanExporter=function(t){var r;return(r=this.constructor._registeredExporters.get(t))===null||r===void 0?void 0:r()},e.prototype._buildPropagatorFromEnv=function(){var t=this,r=Array.from(new Set((0,$.getEnv)().OTEL_PROPAGATORS)),n=r.map(function(i){var s=t._getPropagator(i);return s||l.diag.warn('Propagator \"'+i+'\" requested through environment variable is unavailable.'),s}),o=n.reduce(function(i,s){return s&&i.push(s),i},[]);if(o.length!==0)return r.length===1?o[0]:new $.CompositePropagator({propagators:o})},e.prototype._buildExporterFromEnv=function(){var t=(0,$.getEnv)().OTEL_TRACES_EXPORTER;if(!(t===\"none\"||t===\"\")){var r=this._getSpanExporter(t);return r||l.diag.error('Exporter \"'+t+'\" requested through environment variable is unavailable.'),r}},e._registeredPropagators=new Map([[\"tracecontext\",function(){return new $.W3CTraceContextPropagator}],[\"baggage\",function(){return new $.W3CBaggagePropagator}]]),e._registeredExporters=new Map,e}();E();var st={};Y(st,$_);import*as $_ from\"@opentelemetry/api-logs\";var Pu=v(Fo(),1);E();var ir=v(P());E();E();E();var Wt=v(P()),Ql=function(e){var t=typeof Symbol==\"function\"&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&typeof e.length==\"number\")return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?\"Object is not iterable.\":\"Symbol.iterator is not defined.\")},Zl=function(e,t){var r=typeof Symbol==\"function\"&&e[Symbol.iterator];if(!r)return e;var n=r.call(e),o,i=[],s;try{for(;(t===void 0||t-- >0)&&!(o=n.next()).done;)i.push(o.value)}catch(u){s={error:u}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(s)throw s.error}}return i},jo=function(){function e(t,r,n){this.attributes={},this.totalAttributesCount=0,this._isReadonly=!1;var o=n.timestamp,i=n.observedTimestamp,s=n.severityNumber,u=n.severityText,c=n.body,a=n.attributes,f=a===void 0?{}:a,p=n.context,d=Date.now();if(this.hrTime=(0,Wt.timeInputToHrTime)(o??d),this.hrTimeObserved=(0,Wt.timeInputToHrTime)(i??d),p){var m=l.trace.getSpanContext(p);m&&l.isSpanContextValid(m)&&(this.spanContext=m)}this.severityNumber=s,this.severityText=u,this.body=c,this.resource=t.resource,this.instrumentationScope=r,this._logRecordLimits=t.logRecordLimits,this.setAttributes(f)}return Object.defineProperty(e.prototype,\"severityText\",{get:function(){return this._severityText},set:function(t){this._isLogRecordReadonly()||(this._severityText=t)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,\"severityNumber\",{get:function(){return this._severityNumber},set:function(t){this._isLogRecordReadonly()||(this._severityNumber=t)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,\"body\",{get:function(){return this._body},set:function(t){this._isLogRecordReadonly()||(this._body=t)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,\"droppedAttributesCount\",{get:function(){return this.totalAttributesCount-Object.keys(this.attributes).length},enumerable:!1,configurable:!0}),e.prototype.setAttribute=function(t,r){return this._isLogRecordReadonly()?this:r===null?this:t.length===0?(l.diag.warn(\"Invalid attribute key: \"+t),this):!(0,Wt.isAttributeValue)(r)&&!(typeof r==\"object\"&&!Array.isArray(r)&&Object.keys(r).length>0)?(l.diag.warn(\"Invalid attribute value set for key: \"+t),this):(this.totalAttributesCount+=1,Object.keys(this.attributes).length>=this._logRecordLimits.attributeCountLimit&&!Object.prototype.hasOwnProperty.call(this.attributes,t)?this:((0,Wt.isAttributeValue)(r)?this.attributes[t]=this._truncateToSize(r):this.attributes[t]=r,this))},e.prototype.setAttributes=function(t){var r,n;try{for(var o=Ql(Object.entries(t)),i=o.next();!i.done;i=o.next()){var s=Zl(i.value,2),u=s[0],c=s[1];this.setAttribute(u,c)}}catch(a){r={error:a}}finally{try{i&&!i.done&&(n=o.return)&&n.call(o)}finally{if(r)throw r.error}}return this},e.prototype.setBody=function(t){return this.body=t,this},e.prototype.setSeverityNumber=function(t){return this.severityNumber=t,this},e.prototype.setSeverityText=function(t){return this.severityText=t,this},e.prototype._makeReadonly=function(){this._isReadonly=!0},e.prototype._truncateToSize=function(t){var r=this,n=this._logRecordLimits.attributeValueLengthLimit;return n<=0?(l.diag.warn(\"Attribute value limit must be positive, got \"+n),t):typeof t==\"string\"?this._truncateToLimitUtil(t,n):Array.isArray(t)?t.map(function(o){return typeof o==\"string\"?r._truncateToLimitUtil(o,n):o}):t},e.prototype._truncateToLimitUtil=function(t,r){return t.length<=r?t:t.substring(0,r)},e.prototype._isLogRecordReadonly=function(){return this._isReadonly&&l.diag.warn(\"Can not execute the operation on emitted log record\"),this._isReadonly},e}();var Sn=function(){return Sn=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++){t=arguments[r];for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o])}return e},Sn.apply(this,arguments)},ko=function(){function e(t,r){this.instrumentationScope=t,this._sharedState=r}return e.prototype.emit=function(t){var r=t.context||l.context.active(),n=new jo(this._sharedState,this.instrumentationScope,Sn({context:r},t));this._sharedState.activeProcessor.onEmit(n,r),n._makeReadonly()},e}();var ut=v(P());function qo(){return{forceFlushTimeoutMillis:3e4,logRecordLimits:{attributeValueLengthLimit:(0,ut.getEnv)().OTEL_LOGRECORD_ATTRIBUTE_VALUE_LENGTH_LIMIT,attributeCountLimit:(0,ut.getEnv)().OTEL_LOGRECORD_ATTRIBUTE_COUNT_LIMIT},includeTraceContext:!0}}function Xo(e){var t,r,n,o,i,s,u=(0,ut.getEnvWithoutDefaults)();return{attributeCountLimit:(n=(r=(t=e.attributeCountLimit)!==null&&t!==void 0?t:u.OTEL_LOGRECORD_ATTRIBUTE_COUNT_LIMIT)!==null&&r!==void 0?r:u.OTEL_ATTRIBUTE_COUNT_LIMIT)!==null&&n!==void 0?n:ut.DEFAULT_ATTRIBUTE_COUNT_LIMIT,attributeValueLengthLimit:(s=(i=(o=e.attributeValueLengthLimit)!==null&&o!==void 0?o:u.OTEL_LOGRECORD_ATTRIBUTE_VALUE_LENGTH_LIMIT)!==null&&i!==void 0?i:u.OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT)!==null&&s!==void 0?s:ut.DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT}}var Yo=v(P()),Ko=function(e,t,r,n){function o(i){return i instanceof r?i:new r(function(s){s(i)})}return new(r||(r=Promise))(function(i,s){function u(f){try{a(n.next(f))}catch(p){s(p)}}function c(f){try{a(n.throw(f))}catch(p){s(p)}}function a(f){f.done?i(f.value):o(f.value).then(u,c)}a((n=n.apply(e,t||[])).next())})},Wo=function(e,t){var r={label:0,sent:function(){if(i[0]&1)throw i[1];return i[1]},trys:[],ops:[]},n,o,i,s;return s={next:u(0),throw:u(1),return:u(2)},typeof Symbol==\"function\"&&(s[Symbol.iterator]=function(){return this}),s;function u(a){return function(f){return c([a,f])}}function c(a){if(n)throw new TypeError(\"Generator is already executing.\");for(;r;)try{if(n=1,o&&(i=a[0]&2?o.return:a[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,a[1])).done)return i;switch(o=0,i&&(a=[a[0]&2,i.value]),a[0]){case 0:case 1:i=a;break;case 4:return r.label++,{value:a[1],done:!1};case 5:r.label++,o=a[1],a=[0];continue;case 7:a=r.ops.pop(),r.trys.pop();continue;default:if(i=r.trys,!(i=i.length>0&&i[i.length-1])&&(a[0]===6||a[0]===2)){r=0;continue}if(a[0]===3&&(!i||a[1]>i[0]&&a[1]<i[3])){r.label=a[1];break}if(a[0]===6&&r.label<i[1]){r.label=i[1],i=a;break}if(i&&r.label<i[2]){r.label=i[2],r.ops.push(a);break}i[2]&&r.ops.pop(),r.trys.pop();continue}a=t.call(e,r)}catch(f){a=[6,f],o=0}finally{n=i=0}if(a[0]&5)throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}},zo=function(){function e(t,r){this.processors=t,this.forceFlushTimeoutMillis=r}return e.prototype.forceFlush=function(){return Ko(this,void 0,void 0,function(){var t;return Wo(this,function(r){switch(r.label){case 0:return t=this.forceFlushTimeoutMillis,[4,Promise.all(this.processors.map(function(n){return(0,Yo.callWithTimeout)(n.forceFlush(),t)}))];case 1:return r.sent(),[2]}})})},e.prototype.onEmit=function(t,r){this.processors.forEach(function(n){return n.onEmit(t,r)})},e.prototype.shutdown=function(){return Ko(this,void 0,void 0,function(){return Wo(this,function(t){switch(t.label){case 0:return[4,Promise.all(this.processors.map(function(r){return r.shutdown()}))];case 1:return t.sent(),[2]}})})},e}();var $o=function(){function e(){}return e.prototype.forceFlush=function(){return Promise.resolve()},e.prototype.onEmit=function(t,r){},e.prototype.shutdown=function(){return Promise.resolve()},e}();var Qo=function(){function e(t,r,n){this.resource=t,this.forceFlushTimeoutMillis=r,this.logRecordLimits=n,this.loggers=new Map,this.registeredLogRecordProcessors=[],this.activeProcessor=new $o}return e}();var Jl=\"unknown\",gn=function(){function e(t){t===void 0&&(t={});var r=(0,ir.merge)({},qo(),t),n=r.resource,o=n===void 0?H.default():n,i=r.logRecordLimits,s=r.forceFlushTimeoutMillis;this._sharedState=new Qo(o,s,Xo(i)),this._shutdownOnce=new ir.BindOnceFuture(this._shutdown,this)}return e.prototype.getLogger=function(t,r,n){if(this._shutdownOnce.isCalled)return l.diag.warn(\"A shutdown LoggerProvider cannot provide a Logger\"),st.NOOP_LOGGER;t||l.diag.warn(\"Logger requested without instrumentation scope name.\");var o=t||Jl,i=o+\"@\"+(r||\"\")+\":\"+(n?.schemaUrl||\"\");return this._sharedState.loggers.has(i)||this._sharedState.loggers.set(i,new ko({name:o,version:r,schemaUrl:n?.schemaUrl},this._sharedState)),this._sharedState.loggers.get(i)},e.prototype.addLogRecordProcessor=function(t){this._sharedState.registeredLogRecordProcessors.length===0&&this._sharedState.activeProcessor.shutdown().catch(function(r){return l.diag.error(\"Error while trying to shutdown current log record processor\",r)}),this._sharedState.registeredLogRecordProcessors.push(t),this._sharedState.activeProcessor=new zo(this._sharedState.registeredLogRecordProcessors,this._sharedState.forceFlushTimeoutMillis)},e.prototype.forceFlush=function(){return this._shutdownOnce.isCalled?(l.diag.warn(\"invalid attempt to force flush after LoggerProvider shutdown\"),this._shutdownOnce.promise):this._sharedState.activeProcessor.forceFlush()},e.prototype.shutdown=function(){return this._shutdownOnce.isCalled?(l.diag.warn(\"shutdown may only be called once per LoggerProvider\"),this._shutdownOnce.promise):this._shutdownOnce.call()},e.prototype._shutdown=function(){return this._sharedState.activeProcessor.shutdown()},e}();var or;(function(e){e[e.DELTA=0]=\"DELTA\",e[e.CUMULATIVE=1]=\"CUMULATIVE\"})(or||(or={}));var tf=function(){var e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,o){n.__proto__=o}||function(n,o){for(var i in o)Object.prototype.hasOwnProperty.call(o,i)&&(n[i]=o[i])},e(t,r)};return function(t,r){if(typeof r!=\"function\"&&r!==null)throw new TypeError(\"Class extends value \"+String(r)+\" is not a constructor or null\");e(t,r);function n(){this.constructor=t}t.prototype=r===null?Object.create(r):(n.prototype=r.prototype,new n)}}(),Zo=function(e,t,r,n){function o(i){return i instanceof r?i:new r(function(s){s(i)})}return new(r||(r=Promise))(function(i,s){function u(f){try{a(n.next(f))}catch(p){s(p)}}function c(f){try{a(n.throw(f))}catch(p){s(p)}}function a(f){f.done?i(f.value):o(f.value).then(u,c)}a((n=n.apply(e,t||[])).next())})},Jo=function(e,t){var r={label:0,sent:function(){if(i[0]&1)throw i[1];return i[1]},trys:[],ops:[]},n,o,i,s;return s={next:u(0),throw:u(1),return:u(2)},typeof Symbol==\"function\"&&(s[Symbol.iterator]=function(){return this}),s;function u(a){return function(f){return c([a,f])}}function c(a){if(n)throw new TypeError(\"Generator is already executing.\");for(;r;)try{if(n=1,o&&(i=a[0]&2?o.return:a[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,a[1])).done)return i;switch(o=0,i&&(a=[a[0]&2,i.value]),a[0]){case 0:case 1:i=a;break;case 4:return r.label++,{value:a[1],done:!1};case 5:r.label++,o=a[1],a=[0];continue;case 7:a=r.ops.pop(),r.trys.pop();continue;default:if(i=r.trys,!(i=i.length>0&&i[i.length-1])&&(a[0]===6||a[0]===2)){r=0;continue}if(a[0]===3&&(!i||a[1]>i[0]&&a[1]<i[3])){r.label=a[1];break}if(a[0]===6&&r.label<i[1]){r.label=i[1],i=a;break}if(i&&r.label<i[2]){r.label=i[2],r.ops.push(a);break}i[2]&&r.ops.pop(),r.trys.pop();continue}a=t.call(e,r)}catch(f){a=[6,f],o=0}finally{n=i=0}if(a[0]&5)throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}};var ef=function(e){var t=typeof Symbol==\"function\"&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&typeof e.length==\"number\")return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?\"Object is not iterable.\":\"Symbol.iterator is not defined.\")};function ta(e){return e!=null}function ea(e){var t=Object.keys(e);return t.length===0?\"\":(t=t.sort(),JSON.stringify(t.map(function(r){return[r,e[r]]})))}function ra(e){var t,r;return e.name+\":\"+((t=e.version)!==null&&t!==void 0?t:\"\")+\":\"+((r=e.schemaUrl)!==null&&r!==void 0?r:\"\")}var rf=function(e){tf(t,e);function t(r){var n=e.call(this,r)||this;return Object.setPrototypeOf(n,t.prototype),n}return t}(Error);function yn(e,t){var r,n=new Promise(function(i,s){r=setTimeout(function(){s(new rf(\"Operation timed out.\"))},t)});return Promise.race([e,n]).then(function(o){return clearTimeout(r),o},function(o){throw clearTimeout(r),o})}function na(e){return Zo(this,void 0,void 0,function(){var t=this;return Jo(this,function(r){return[2,Promise.all(e.map(function(n){return Zo(t,void 0,void 0,function(){var o,i;return Jo(this,function(s){switch(s.label){case 0:return s.trys.push([0,2,,3]),[4,n];case 1:return o=s.sent(),[2,{status:\"fulfilled\",value:o}];case 2:return i=s.sent(),[2,{status:\"rejected\",reason:i}];case 3:return[2]}})})}))]})})}function ia(e){return e.status===\"rejected\"}function oa(e,t){var r,n;if(e.size!==t.size)return!1;try{for(var o=ef(e),i=o.next();!i.done;i=o.next()){var s=i.value;if(!t.has(s))return!1}}catch(u){r={error:u}}finally{try{i&&!i.done&&(n=o.return)&&n.call(o)}finally{if(r)throw r.error}}return!0}function aa(e,t){return e.toLowerCase()===t.toLowerCase()}E();var ct;(function(e){e.COUNTER=\"COUNTER\",e.HISTOGRAM=\"HISTOGRAM\",e.UP_DOWN_COUNTER=\"UP_DOWN_COUNTER\",e.OBSERVABLE_COUNTER=\"OBSERVABLE_COUNTER\",e.OBSERVABLE_GAUGE=\"OBSERVABLE_GAUGE\",e.OBSERVABLE_UP_DOWN_COUNTER=\"OBSERVABLE_UP_DOWN_COUNTER\"})(ct||(ct={}));function lt(e,t,r){var n,o,i,s;return of(e)||l.diag.warn('Invalid metric name: \"'+e+'\". The metric name should be a ASCII string with a length no greater than 255 characters.'),{name:e,type:t,description:(n=r?.description)!==null&&n!==void 0?n:\"\",unit:(o=r?.unit)!==null&&o!==void 0?o:\"\",valueType:(i=r?.valueType)!==null&&i!==void 0?i:l.ValueType.DOUBLE,advice:(s=r?.advice)!==null&&s!==void 0?s:{}}}function sa(e,t){var r,n;return{name:(r=e.name)!==null&&r!==void 0?r:t.name,description:(n=e.description)!==null&&n!==void 0?n:t.description,type:t.type,unit:t.unit,valueType:t.valueType,advice:t.advice}}function ua(e,t){return aa(e.name,t.name)&&e.unit===t.unit&&e.type===t.type&&e.valueType===t.valueType}var nf=/^[a-z][a-z0-9_.\\-/]{0,254}$/i;function of(e){return e.match(nf)!=null}E();var ca=function(){function e(){this._registeredViews=[]}return e.prototype.addView=function(t){this._registeredViews.push(t)},e.prototype.findViews=function(t,r){var n=this,o=this._registeredViews.filter(function(i){return n._matchInstrument(i.instrumentSelector,t)&&n._matchMeter(i.meterSelector,r)});return o},e.prototype._matchInstrument=function(t,r){return(t.getType()===void 0||r.type===t.getType())&&t.getNameFilter().match(r.name)&&t.getUnitFilter().match(r.unit)},e.prototype._matchMeter=function(t,r){return t.getNameFilter().match(r.name)&&(r.version===void 0||t.getVersionFilter().match(r.version))&&(r.schemaUrl===void 0||t.getSchemaUrlFilter().match(r.schemaUrl))},e}();E();var la=v(P()),Yt=function(){var e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,o){n.__proto__=o}||function(n,o){for(var i in o)Object.prototype.hasOwnProperty.call(o,i)&&(n[i]=o[i])},e(t,r)};return function(t,r){if(typeof r!=\"function\"&&r!==null)throw new TypeError(\"Class extends value \"+String(r)+\" is not a constructor or null\");e(t,r);function n(){this.constructor=t}t.prototype=r===null?Object.create(r):(n.prototype=r.prototype,new n)}}(),vn=function(){function e(t,r){this._writableMetricStorage=t,this._descriptor=r}return e.prototype._record=function(t,r,n){if(r===void 0&&(r={}),n===void 0&&(n=l.context.active()),typeof t!=\"number\"){l.diag.warn(\"non-number value provided to metric \"+this._descriptor.name+\": \"+t);return}this._descriptor.valueType===l.ValueType.INT&&!Number.isInteger(t)&&(l.diag.warn(\"INT value type cannot accept a floating-point value for \"+this._descriptor.name+\", ignoring the fractional digits.\"),t=Math.trunc(t),!Number.isInteger(t))||this._writableMetricStorage.record(t,r,n,(0,la.millisToHrTime)(Date.now()))},e}();var fa=function(e){Yt(t,e);function t(){return e!==null&&e.apply(this,arguments)||this}return t.prototype.add=function(r,n,o){this._record(r,n,o)},t}(vn);var pa=function(e){Yt(t,e);function t(){return e!==null&&e.apply(this,arguments)||this}return t.prototype.add=function(r,n,o){if(r<0){l.diag.warn(\"negative value provided to counter \"+this._descriptor.name+\": \"+r);return}this._record(r,n,o)},t}(vn);var da=function(e){Yt(t,e);function t(){return e!==null&&e.apply(this,arguments)||this}return t.prototype.record=function(r,n,o){if(r<0){l.diag.warn(\"negative value provided to histogram \"+this._descriptor.name+\": \"+r);return}this._record(r,n,o)},t}(vn);var ar=function(){function e(t,r,n){this._observableRegistry=n,this._descriptor=t,this._metricStorages=r}return e.prototype.addCallback=function(t){this._observableRegistry.addCallback(t,this)},e.prototype.removeCallback=function(t){this._observableRegistry.removeCallback(t,this)},e}();var _a=function(e){Yt(t,e);function t(){return e!==null&&e.apply(this,arguments)||this}return t}(ar);var ha=function(e){Yt(t,e);function t(){return e!==null&&e.apply(this,arguments)||this}return t}(ar);var Ea=function(e){Yt(t,e);function t(){return e!==null&&e.apply(this,arguments)||this}return t}(ar);function fe(e){return e instanceof ar}var ma=function(){function e(t){this._meterSharedState=t}return e.prototype.createHistogram=function(t,r){var n=lt(t,ct.HISTOGRAM,r),o=this._meterSharedState.registerMetricStorage(n);return new da(o,n)},e.prototype.createCounter=function(t,r){var n=lt(t,ct.COUNTER,r),o=this._meterSharedState.registerMetricStorage(n);return new pa(o,n)},e.prototype.createUpDownCounter=function(t,r){var n=lt(t,ct.UP_DOWN_COUNTER,r),o=this._meterSharedState.registerMetricStorage(n);return new fa(o,n)},e.prototype.createObservableGauge=function(t,r){var n=lt(t,ct.OBSERVABLE_GAUGE,r),o=this._meterSharedState.registerAsyncMetricStorage(n);return new ha(n,o,this._meterSharedState.observableRegistry)},e.prototype.createObservableCounter=function(t,r){var n=lt(t,ct.OBSERVABLE_COUNTER,r),o=this._meterSharedState.registerAsyncMetricStorage(n);return new _a(n,o,this._meterSharedState.observableRegistry)},e.prototype.createObservableUpDownCounter=function(t,r){var n=lt(t,ct.OBSERVABLE_UP_DOWN_COUNTER,r),o=this._meterSharedState.registerAsyncMetricStorage(n);return new Ea(n,o,this._meterSharedState.observableRegistry)},e.prototype.addBatchObservableCallback=function(t,r){this._meterSharedState.observableRegistry.addBatchCallback(t,r)},e.prototype.removeBatchObservableCallback=function(t,r){this._meterSharedState.observableRegistry.removeBatchCallback(t,r)},e}();var sr=function(){function e(t){this._instrumentDescriptor=t}return e.prototype.getInstrumentDescriptor=function(){return this._instrumentDescriptor},e.prototype.updateDescription=function(t){this._instrumentDescriptor=lt(this._instrumentDescriptor.name,this._instrumentDescriptor.type,{description:t,valueType:this._instrumentDescriptor.valueType,unit:this._instrumentDescriptor.unit,advice:this._instrumentDescriptor.advice})},e}();var af=function(){var e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,o){n.__proto__=o}||function(n,o){for(var i in o)Object.prototype.hasOwnProperty.call(o,i)&&(n[i]=o[i])},e(t,r)};return function(t,r){if(typeof r!=\"function\"&&r!==null)throw new TypeError(\"Class extends value \"+String(r)+\" is not a constructor or null\");e(t,r);function n(){this.constructor=t}t.prototype=r===null?Object.create(r):(n.prototype=r.prototype,new n)}}(),Ta=function(e,t){var r={label:0,sent:function(){if(i[0]&1)throw i[1];return i[1]},trys:[],ops:[]},n,o,i,s;return s={next:u(0),throw:u(1),return:u(2)},typeof Symbol==\"function\"&&(s[Symbol.iterator]=function(){return this}),s;function u(a){return function(f){return c([a,f])}}function c(a){if(n)throw new TypeError(\"Generator is already executing.\");for(;r;)try{if(n=1,o&&(i=a[0]&2?o.return:a[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,a[1])).done)return i;switch(o=0,i&&(a=[a[0]&2,i.value]),a[0]){case 0:case 1:i=a;break;case 4:return r.label++,{value:a[1],done:!1};case 5:r.label++,o=a[1],a=[0];continue;case 7:a=r.ops.pop(),r.trys.pop();continue;default:if(i=r.trys,!(i=i.length>0&&i[i.length-1])&&(a[0]===6||a[0]===2)){r=0;continue}if(a[0]===3&&(!i||a[1]>i[0]&&a[1]<i[3])){r.label=a[1];break}if(a[0]===6&&r.label<i[1]){r.label=i[1],i=a;break}if(i&&r.label<i[2]){r.label=i[2],r.ops.push(a);break}i[2]&&r.ops.pop(),r.trys.pop();continue}a=t.call(e,r)}catch(f){a=[6,f],o=0}finally{n=i=0}if(a[0]&5)throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}},sf=function(){function e(t){this._hash=t,this._valueMap=new Map,this._keyMap=new Map}return e.prototype.get=function(t,r){return r??(r=this._hash(t)),this._valueMap.get(r)},e.prototype.getOrDefault=function(t,r){var n=this._hash(t);if(this._valueMap.has(n))return this._valueMap.get(n);var o=r();return this._keyMap.has(n)||this._keyMap.set(n,t),this._valueMap.set(n,o),o},e.prototype.set=function(t,r,n){n??(n=this._hash(t)),this._keyMap.has(n)||this._keyMap.set(n,t),this._valueMap.set(n,r)},e.prototype.has=function(t,r){return r??(r=this._hash(t)),this._valueMap.has(r)},e.prototype.keys=function(){var t,r;return Ta(this,function(n){switch(n.label){case 0:t=this._keyMap.entries(),r=t.next(),n.label=1;case 1:return r.done===!0?[3,3]:[4,[r.value[1],r.value[0]]];case 2:return n.sent(),r=t.next(),[3,1];case 3:return[2]}})},e.prototype.entries=function(){var t,r;return Ta(this,function(n){switch(n.label){case 0:t=this._valueMap.entries(),r=t.next(),n.label=1;case 1:return r.done===!0?[3,3]:[4,[this._keyMap.get(r.value[0]),r.value[1],r.value[0]]];case 2:return n.sent(),r=t.next(),[3,1];case 3:return[2]}})},Object.defineProperty(e.prototype,\"size\",{get:function(){return this._valueMap.size},enumerable:!1,configurable:!0}),e}();var Q=function(e){af(t,e);function t(){return e.call(this,ea)||this}return t}(sf);var uf=function(e,t){var r=typeof Symbol==\"function\"&&e[Symbol.iterator];if(!r)return e;var n=r.call(e),o,i=[],s;try{for(;(t===void 0||t-- >0)&&!(o=n.next()).done;)i.push(o.value)}catch(u){s={error:u}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(s)throw s.error}}return i},ur=function(){function e(t){this._aggregator=t,this._activeCollectionStorage=new Q,this._cumulativeMemoStorage=new Q}return e.prototype.record=function(t,r,n,o){var i=this,s=this._activeCollectionStorage.getOrDefault(r,function(){return i._aggregator.createAccumulation(o)});s?.record(t)},e.prototype.batchCumulate=function(t,r){var n=this;Array.from(t.entries()).forEach(function(o){var i=uf(o,3),s=i[0],u=i[1],c=i[2],a=n._aggregator.createAccumulation(r);a?.record(u);var f=a;if(n._cumulativeMemoStorage.has(s,c)){var p=n._cumulativeMemoStorage.get(s,c);f=n._aggregator.diff(p,a)}if(n._activeCollectionStorage.has(s,c)){var d=n._activeCollectionStorage.get(s,c);f=n._aggregator.merge(d,f)}n._cumulativeMemoStorage.set(s,a,c),n._activeCollectionStorage.set(s,f,c)})},e.prototype.collect=function(){var t=this._activeCollectionStorage;return this._activeCollectionStorage=new Q,t},e}();var An=function(e){var t=typeof Symbol==\"function\"&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&typeof e.length==\"number\")return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?\"Object is not iterable.\":\"Symbol.iterator is not defined.\")},Sa=function(e,t){var r=typeof Symbol==\"function\"&&e[Symbol.iterator];if(!r)return e;var n=r.call(e),o,i=[],s;try{for(;(t===void 0||t-- >0)&&!(o=n.next()).done;)i.push(o.value)}catch(u){s={error:u}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(s)throw s.error}}return i},cr=function(){function e(t,r){var n=this;this._aggregator=t,this._unreportedAccumulations=new Map,this._reportHistory=new Map,r.forEach(function(o){n._unreportedAccumulations.set(o,[])})}return e.prototype.buildMetrics=function(t,r,n,o){this._stashAccumulations(n);var i=this._getMergedUnreportedAccumulations(t),s=i,u;if(this._reportHistory.has(t)){var c=this._reportHistory.get(t),a=c.collectionTime;u=c.aggregationTemporality,u===or.CUMULATIVE?s=e.merge(c.accumulations,i,this._aggregator):s=e.calibrateStartTime(c.accumulations,i,a)}else u=t.selectAggregationTemporality(r.type);this._reportHistory.set(t,{accumulations:s,collectionTime:o,aggregationTemporality:u});var f=cf(s);if(f.length!==0)return this._aggregator.toMetricData(r,u,f,o)},e.prototype._stashAccumulations=function(t){var r,n,o=this._unreportedAccumulations.keys();try{for(var i=An(o),s=i.next();!s.done;s=i.next()){var u=s.value,c=this._unreportedAccumulations.get(u);c===void 0&&(c=[],this._unreportedAccumulations.set(u,c)),c.push(t)}}catch(a){r={error:a}}finally{try{s&&!s.done&&(n=i.return)&&n.call(i)}finally{if(r)throw r.error}}},e.prototype._getMergedUnreportedAccumulations=function(t){var r,n,o=new Q,i=this._unreportedAccumulations.get(t);if(this._unreportedAccumulations.set(t,[]),i===void 0)return o;try{for(var s=An(i),u=s.next();!u.done;u=s.next()){var c=u.value;o=e.merge(o,c,this._aggregator)}}catch(a){r={error:a}}finally{try{u&&!u.done&&(n=s.return)&&n.call(s)}finally{if(r)throw r.error}}return o},e.merge=function(t,r,n){for(var o=t,i=r.entries(),s=i.next();s.done!==!0;){var u=Sa(s.value,3),c=u[0],a=u[1],f=u[2];if(t.has(c,f)){var p=t.get(c,f),d=n.merge(p,a);o.set(c,d,f)}else o.set(c,a,f);s=i.next()}return o},e.calibrateStartTime=function(t,r,n){var o,i;try{for(var s=An(t.keys()),u=s.next();!u.done;u=s.next()){var c=Sa(u.value,2),a=c[0],f=c[1],p=r.get(a,f);p?.setStartTime(n)}}catch(d){o={error:d}}finally{try{u&&!u.done&&(i=s.return)&&i.call(s)}finally{if(o)throw o.error}}return r},e}();function cf(e){return Array.from(e.entries())}var lf=function(){var e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,o){n.__proto__=o}||function(n,o){for(var i in o)Object.prototype.hasOwnProperty.call(o,i)&&(n[i]=o[i])},e(t,r)};return function(t,r){if(typeof r!=\"function\"&&r!==null)throw new TypeError(\"Class extends value \"+String(r)+\" is not a constructor or null\");e(t,r);function n(){this.constructor=t}t.prototype=r===null?Object.create(r):(n.prototype=r.prototype,new n)}}(),ff=function(e,t){var r=typeof Symbol==\"function\"&&e[Symbol.iterator];if(!r)return e;var n=r.call(e),o,i=[],s;try{for(;(t===void 0||t-- >0)&&!(o=n.next()).done;)i.push(o.value)}catch(u){s={error:u}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(s)throw s.error}}return i},ga=function(e){lf(t,e);function t(r,n,o,i){var s=e.call(this,r)||this;return s._attributesProcessor=o,s._deltaMetricStorage=new ur(n),s._temporalMetricStorage=new cr(n,i),s}return t.prototype.record=function(r,n){var o=this,i=new Q;Array.from(r.entries()).forEach(function(s){var u=ff(s,2),c=u[0],a=u[1];i.set(o._attributesProcessor.process(c),a)}),this._deltaMetricStorage.batchCumulate(i,n)},t.prototype.collect=function(r,n){var o=this._deltaMetricStorage.collect();return this._temporalMetricStorage.buildMetrics(r,this._instrumentDescriptor,o,n)},t}(sr);E();function Rn(e,t){var r=\"\";return e.unit!==t.unit&&(r+=\"\t- Unit '\"+e.unit+\"' does not match '\"+t.unit+`'\n`),e.type!==t.type&&(r+=\"\t- Type '\"+e.type+\"' does not match '\"+t.type+`'\n`),e.valueType!==t.valueType&&(r+=\"\t- Value Type '\"+e.valueType+\"' does not match '\"+t.valueType+`'\n`),e.description!==t.description&&(r+=\"\t- Description '\"+e.description+\"' does not match '\"+t.description+`'\n`),r}function pf(e,t){return\"\t- use valueType '\"+e.valueType+\"' on instrument creation or use an instrument name other than '\"+t.name+\"'\"}function df(e,t){return\"\t- use unit '\"+e.unit+\"' on instrument creation or use an instrument name other than '\"+t.name+\"'\"}function _f(e,t){var r={name:t.name,type:t.type,unit:t.unit},n=JSON.stringify(r);return\"\t- create a new view with a name other than '\"+e.name+\"' and InstrumentSelector '\"+n+\"'\"}function hf(e,t){var r={name:t.name,type:t.type,unit:t.unit},n=JSON.stringify(r);return\"\t- create a new view with a name other than '\"+e.name+\"' and InstrumentSelector '\"+n+`'\n    \t- OR - create a new view with the name `+e.name+\" and description '\"+e.description+\"' and InstrumentSelector \"+n+`\n    \t- OR - create a new view with the name `+t.name+\" and description '\"+e.description+\"' and InstrumentSelector \"+n}function On(e,t){return e.valueType!==t.valueType?pf(e,t):e.unit!==t.unit?df(e,t):e.type!==t.type?_f(e,t):e.description!==t.description?hf(e,t):\"\"}var bn=function(e){var t=typeof Symbol==\"function\"&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&typeof e.length==\"number\")return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?\"Object is not iterable.\":\"Symbol.iterator is not defined.\")},ya=function(){function e(){this._sharedRegistry=new Map,this._perCollectorRegistry=new Map}return e.create=function(){return new e},e.prototype.getStorages=function(t){var r,n,o,i,s=[];try{for(var u=bn(this._sharedRegistry.values()),c=u.next();!c.done;c=u.next()){var a=c.value;s=s.concat(a)}}catch(m){r={error:m}}finally{try{c&&!c.done&&(n=u.return)&&n.call(u)}finally{if(r)throw r.error}}var f=this._perCollectorRegistry.get(t);if(f!=null)try{for(var p=bn(f.values()),d=p.next();!d.done;d=p.next()){var a=d.value;s=s.concat(a)}}catch(m){o={error:m}}finally{try{d&&!d.done&&(i=p.return)&&i.call(p)}finally{if(o)throw o.error}}return s},e.prototype.register=function(t){this._registerStorage(t,this._sharedRegistry)},e.prototype.registerForCollector=function(t,r){var n=this._perCollectorRegistry.get(t);n==null&&(n=new Map,this._perCollectorRegistry.set(t,n)),this._registerStorage(r,n)},e.prototype.findOrUpdateCompatibleStorage=function(t){var r=this._sharedRegistry.get(t.name);return r===void 0?null:this._findOrUpdateCompatibleStorage(t,r)},e.prototype.findOrUpdateCompatibleCollectorStorage=function(t,r){var n=this._perCollectorRegistry.get(t);if(n===void 0)return null;var o=n.get(r.name);return o===void 0?null:this._findOrUpdateCompatibleStorage(r,o)},e.prototype._registerStorage=function(t,r){var n=t.getInstrumentDescriptor(),o=r.get(n.name);if(o===void 0){r.set(n.name,[t]);return}o.push(t)},e.prototype._findOrUpdateCompatibleStorage=function(t,r){var n,o,i=null;try{for(var s=bn(r),u=s.next();!u.done;u=s.next()){var c=u.value,a=c.getInstrumentDescriptor();ua(a,t)?(a.description!==t.description&&(t.description.length>a.description.length&&c.updateDescription(t.description),l.diag.warn(\"A view or instrument with the name \",t.name,` has already been registered, but has a different description and is incompatible with another registered view.\n`,`Details:\n`,Rn(a,t),`The longer description will be used.\nTo resolve the conflict:`,On(a,t))),i=c):l.diag.warn(\"A view or instrument with the name \",t.name,` has already been registered and is incompatible with another registered view.\n`,`Details:\n`,Rn(a,t),`To resolve the conflict:\n`,On(a,t))}}catch(f){n={error:f}}finally{try{u&&!u.done&&(o=s.return)&&o.call(s)}finally{if(n)throw n.error}}return i},e}();var va=function(){function e(t){this._backingStorages=t}return e.prototype.record=function(t,r,n,o){this._backingStorages.forEach(function(i){i.record(t,r,n,o)})},e}();E();E();var Aa=function(){function e(t,r){this._instrumentName=t,this._valueType=r,this._buffer=new Q}return e.prototype.observe=function(t,r){if(r===void 0&&(r={}),typeof t!=\"number\"){l.diag.warn(\"non-number value provided to metric \"+this._instrumentName+\": \"+t);return}this._valueType===l.ValueType.INT&&!Number.isInteger(t)&&(l.diag.warn(\"INT value type cannot accept a floating-point value for \"+this._instrumentName+\", ignoring the fractional digits.\"),t=Math.trunc(t),!Number.isInteger(t))||this._buffer.set(r,t)},e}();var Ra=function(){function e(){this._buffer=new Map}return e.prototype.observe=function(t,r,n){if(n===void 0&&(n={}),!!fe(t)){var o=this._buffer.get(t);if(o==null&&(o=new Q,this._buffer.set(t,o)),typeof r!=\"number\"){l.diag.warn(\"non-number value provided to metric \"+t._descriptor.name+\": \"+r);return}t._descriptor.valueType===l.ValueType.INT&&!Number.isInteger(r)&&(l.diag.warn(\"INT value type cannot accept a floating-point value for \"+t._descriptor.name+\", ignoring the fractional digits.\"),r=Math.trunc(r),!Number.isInteger(r))||o.set(n,r)}},e}();var Ln=function(e,t,r,n){function o(i){return i instanceof r?i:new r(function(s){s(i)})}return new(r||(r=Promise))(function(i,s){function u(f){try{a(n.next(f))}catch(p){s(p)}}function c(f){try{a(n.throw(f))}catch(p){s(p)}}function a(f){f.done?i(f.value):o(f.value).then(u,c)}a((n=n.apply(e,t||[])).next())})},Pn=function(e,t){var r={label:0,sent:function(){if(i[0]&1)throw i[1];return i[1]},trys:[],ops:[]},n,o,i,s;return s={next:u(0),throw:u(1),return:u(2)},typeof Symbol==\"function\"&&(s[Symbol.iterator]=function(){return this}),s;function u(a){return function(f){return c([a,f])}}function c(a){if(n)throw new TypeError(\"Generator is already executing.\");for(;r;)try{if(n=1,o&&(i=a[0]&2?o.return:a[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,a[1])).done)return i;switch(o=0,i&&(a=[a[0]&2,i.value]),a[0]){case 0:case 1:i=a;break;case 4:return r.label++,{value:a[1],done:!1};case 5:r.label++,o=a[1],a=[0];continue;case 7:a=r.ops.pop(),r.trys.pop();continue;default:if(i=r.trys,!(i=i.length>0&&i[i.length-1])&&(a[0]===6||a[0]===2)){r=0;continue}if(a[0]===3&&(!i||a[1]>i[0]&&a[1]<i[3])){r.label=a[1];break}if(a[0]===6&&r.label<i[1]){r.label=i[1],i=a;break}if(i&&r.label<i[2]){r.label=i[2],r.ops.push(a);break}i[2]&&r.ops.pop(),r.trys.pop();continue}a=t.call(e,r)}catch(f){a=[6,f],o=0}finally{n=i=0}if(a[0]&5)throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}},Oa=function(e,t){var r=typeof Symbol==\"function\"&&e[Symbol.iterator];if(!r)return e;var n=r.call(e),o,i=[],s;try{for(;(t===void 0||t-- >0)&&!(o=n.next()).done;)i.push(o.value)}catch(u){s={error:u}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(s)throw s.error}}return i},ba=function(e,t,r){if(r||arguments.length===2)for(var n=0,o=t.length,i;n<o;n++)(i||!(n in t))&&(i||(i=Array.prototype.slice.call(t,0,n)),i[n]=t[n]);return e.concat(i||Array.prototype.slice.call(t))},La=function(){function e(){this._callbacks=[],this._batchCallbacks=[]}return e.prototype.addCallback=function(t,r){var n=this._findCallback(t,r);n>=0||this._callbacks.push({callback:t,instrument:r})},e.prototype.removeCallback=function(t,r){var n=this._findCallback(t,r);n<0||this._callbacks.splice(n,1)},e.prototype.addBatchCallback=function(t,r){var n=new Set(r.filter(fe));if(n.size===0){l.diag.error(\"BatchObservableCallback is not associated with valid instruments\",r);return}var o=this._findBatchCallback(t,n);o>=0||this._batchCallbacks.push({callback:t,instruments:n})},e.prototype.removeBatchCallback=function(t,r){var n=new Set(r.filter(fe)),o=this._findBatchCallback(t,n);o<0||this._batchCallbacks.splice(o,1)},e.prototype.observe=function(t,r){return Ln(this,void 0,void 0,function(){var n,o,i,s;return Pn(this,function(u){switch(u.label){case 0:return n=this._observeCallbacks(t,r),o=this._observeBatchCallbacks(t,r),[4,na(ba(ba([],Oa(n),!1),Oa(o),!1))];case 1:return i=u.sent(),s=i.filter(ia).map(function(c){return c.reason}),[2,s]}})})},e.prototype._observeCallbacks=function(t,r){var n=this;return this._callbacks.map(function(o){var i=o.callback,s=o.instrument;return Ln(n,void 0,void 0,function(){var u,c;return Pn(this,function(a){switch(a.label){case 0:return u=new Aa(s._descriptor.name,s._descriptor.valueType),c=Promise.resolve(i(u)),r!=null&&(c=yn(c,r)),[4,c];case 1:return a.sent(),s._metricStorages.forEach(function(f){f.record(u._buffer,t)}),[2]}})})})},e.prototype._observeBatchCallbacks=function(t,r){var n=this;return this._batchCallbacks.map(function(o){var i=o.callback,s=o.instruments;return Ln(n,void 0,void 0,function(){var u,c;return Pn(this,function(a){switch(a.label){case 0:return u=new Ra,c=Promise.resolve(i(u)),r!=null&&(c=yn(c,r)),[4,c];case 1:return a.sent(),s.forEach(function(f){var p=u._buffer.get(f);p!=null&&f._metricStorages.forEach(function(d){d.record(p,t)})}),[2]}})})})},e.prototype._findCallback=function(t,r){return this._callbacks.findIndex(function(n){return n.callback===t&&n.instrument===r})},e.prototype._findBatchCallback=function(t,r){return this._batchCallbacks.findIndex(function(n){return n.callback===t&&oa(n.instruments,r)})},e}();var Ef=function(){var e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,o){n.__proto__=o}||function(n,o){for(var i in o)Object.prototype.hasOwnProperty.call(o,i)&&(n[i]=o[i])},e(t,r)};return function(t,r){if(typeof r!=\"function\"&&r!==null)throw new TypeError(\"Class extends value \"+String(r)+\" is not a constructor or null\");e(t,r);function n(){this.constructor=t}t.prototype=r===null?Object.create(r):(n.prototype=r.prototype,new n)}}(),Pa=function(e){Ef(t,e);function t(r,n,o,i){var s=e.call(this,r)||this;return s._attributesProcessor=o,s._deltaMetricStorage=new ur(n),s._temporalMetricStorage=new cr(n,i),s}return t.prototype.record=function(r,n,o,i){n=this._attributesProcessor.process(n,o),this._deltaMetricStorage.record(r,n,o,i)},t.prototype.collect=function(r,n){var o=this._deltaMetricStorage.collect();return this._temporalMetricStorage.buildMetrics(r,this._instrumentDescriptor,o,n)},t}(sr);var Ia=function(){var e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,o){n.__proto__=o}||function(n,o){for(var i in o)Object.prototype.hasOwnProperty.call(o,i)&&(n[i]=o[i])},e(t,r)};return function(t,r){if(typeof r!=\"function\"&&r!==null)throw new TypeError(\"Class extends value \"+String(r)+\" is not a constructor or null\");e(t,r);function n(){this.constructor=t}t.prototype=r===null?Object.create(r):(n.prototype=r.prototype,new n)}}(),lr=function(){function e(){}return e.Noop=function(){return Tf},e}();var mf=function(e){Ia(t,e);function t(){return e!==null&&e.apply(this,arguments)||this}return t.prototype.process=function(r,n){return r},t}(lr);var uE=function(e){Ia(t,e);function t(r){var n=e.call(this)||this;return n._allowedAttributeNames=r,n}return t.prototype.process=function(r,n){var o=this,i={};return Object.keys(r).filter(function(s){return o._allowedAttributeNames.includes(s)}).forEach(function(s){return i[s]=r[s]}),i},t}(lr);var Tf=new mf;var Sf=function(e,t,r,n){function o(i){return i instanceof r?i:new r(function(s){s(i)})}return new(r||(r=Promise))(function(i,s){function u(f){try{a(n.next(f))}catch(p){s(p)}}function c(f){try{a(n.throw(f))}catch(p){s(p)}}function a(f){f.done?i(f.value):o(f.value).then(u,c)}a((n=n.apply(e,t||[])).next())})},gf=function(e,t){var r={label:0,sent:function(){if(i[0]&1)throw i[1];return i[1]},trys:[],ops:[]},n,o,i,s;return s={next:u(0),throw:u(1),return:u(2)},typeof Symbol==\"function\"&&(s[Symbol.iterator]=function(){return this}),s;function u(a){return function(f){return c([a,f])}}function c(a){if(n)throw new TypeError(\"Generator is already executing.\");for(;r;)try{if(n=1,o&&(i=a[0]&2?o.return:a[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,a[1])).done)return i;switch(o=0,i&&(a=[a[0]&2,i.value]),a[0]){case 0:case 1:i=a;break;case 4:return r.label++,{value:a[1],done:!1};case 5:r.label++,o=a[1],a=[0];continue;case 7:a=r.ops.pop(),r.trys.pop();continue;default:if(i=r.trys,!(i=i.length>0&&i[i.length-1])&&(a[0]===6||a[0]===2)){r=0;continue}if(a[0]===3&&(!i||a[1]>i[0]&&a[1]<i[3])){r.label=a[1];break}if(a[0]===6&&r.label<i[1]){r.label=i[1],i=a;break}if(i&&r.label<i[2]){r.label=i[2],r.ops.push(a);break}i[2]&&r.ops.pop(),r.trys.pop();continue}a=t.call(e,r)}catch(f){a=[6,f],o=0}finally{n=i=0}if(a[0]&5)throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}},yf=function(e,t){var r=typeof Symbol==\"function\"&&e[Symbol.iterator];if(!r)return e;var n=r.call(e),o,i=[],s;try{for(;(t===void 0||t-- >0)&&!(o=n.next()).done;)i.push(o.value)}catch(u){s={error:u}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(s)throw s.error}}return i},Ca=function(){function e(t,r){this._meterProviderSharedState=t,this._instrumentationScope=r,this.metricStorageRegistry=new ya,this.observableRegistry=new La,this.meter=new ma(this)}return e.prototype.registerMetricStorage=function(t){var r=this._registerMetricStorage(t,Pa);return r.length===1?r[0]:new va(r)},e.prototype.registerAsyncMetricStorage=function(t){var r=this._registerMetricStorage(t,ga);return r},e.prototype.collect=function(t,r,n){return Sf(this,void 0,void 0,function(){var o,i,s;return gf(this,function(u){switch(u.label){case 0:return[4,this.observableRegistry.observe(r,n?.timeoutMillis)];case 1:return o=u.sent(),i=this.metricStorageRegistry.getStorages(t),i.length===0?[2,null]:(s=i.map(function(c){return c.collect(t,r)}).filter(ta),s.length===0?[2,{errors:o}]:[2,{scopeMetrics:{scope:this._instrumentationScope,metrics:s},errors:o}])}})})},e.prototype._registerMetricStorage=function(t,r){var n=this,o=this._meterProviderSharedState.viewRegistry.findViews(t,this._instrumentationScope),i=o.map(function(c){var a=sa(c,t),f=n.metricStorageRegistry.findOrUpdateCompatibleStorage(a);if(f!=null)return f;var p=c.aggregation.createAggregator(a),d=new r(a,p,c.attributesProcessor,n._meterProviderSharedState.metricCollectors);return n.metricStorageRegistry.register(d),d});if(i.length===0){var s=this._meterProviderSharedState.selectAggregations(t.type),u=s.map(function(c){var a=yf(c,2),f=a[0],p=a[1],d=n.metricStorageRegistry.findOrUpdateCompatibleCollectorStorage(f,t);if(d!=null)return d;var m=p.createAggregator(t),g=new r(t,m,lr.Noop(),[f]);return n.metricStorageRegistry.registerForCollector(f,g),g});i=i.concat(u)}return i},e}();var vf=function(e){var t=typeof Symbol==\"function\"&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&typeof e.length==\"number\")return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?\"Object is not iterable.\":\"Symbol.iterator is not defined.\")},Na=function(){function e(t){this.resource=t,this.viewRegistry=new ca,this.metricCollectors=[],this.meterSharedStates=new Map}return e.prototype.getMeterSharedState=function(t){var r=ra(t),n=this.meterSharedStates.get(r);return n==null&&(n=new Ca(this,t),this.meterSharedStates.set(r,n)),n},e.prototype.selectAggregations=function(t){var r,n,o=[];try{for(var i=vf(this.metricCollectors),s=i.next();!s.done;s=i.next()){var u=s.value;o.push([u,u.selectAggregation(t)])}}catch(c){r={error:c}}finally{try{s&&!s.done&&(n=i.return)&&n.call(i)}finally{if(r)throw r.error}}return o},e}();var wa=v(P()),fr=function(e,t,r,n){function o(i){return i instanceof r?i:new r(function(s){s(i)})}return new(r||(r=Promise))(function(i,s){function u(f){try{a(n.next(f))}catch(p){s(p)}}function c(f){try{a(n.throw(f))}catch(p){s(p)}}function a(f){f.done?i(f.value):o(f.value).then(u,c)}a((n=n.apply(e,t||[])).next())})},pr=function(e,t){var r={label:0,sent:function(){if(i[0]&1)throw i[1];return i[1]},trys:[],ops:[]},n,o,i,s;return s={next:u(0),throw:u(1),return:u(2)},typeof Symbol==\"function\"&&(s[Symbol.iterator]=function(){return this}),s;function u(a){return function(f){return c([a,f])}}function c(a){if(n)throw new TypeError(\"Generator is already executing.\");for(;r;)try{if(n=1,o&&(i=a[0]&2?o.return:a[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,a[1])).done)return i;switch(o=0,i&&(a=[a[0]&2,i.value]),a[0]){case 0:case 1:i=a;break;case 4:return r.label++,{value:a[1],done:!1};case 5:r.label++,o=a[1],a=[0];continue;case 7:a=r.ops.pop(),r.trys.pop();continue;default:if(i=r.trys,!(i=i.length>0&&i[i.length-1])&&(a[0]===6||a[0]===2)){r=0;continue}if(a[0]===3&&(!i||a[1]>i[0]&&a[1]<i[3])){r.label=a[1];break}if(a[0]===6&&r.label<i[1]){r.label=i[1],i=a;break}if(i&&r.label<i[2]){r.label=i[2],r.ops.push(a);break}i[2]&&r.ops.pop(),r.trys.pop();continue}a=t.call(e,r)}catch(f){a=[6,f],o=0}finally{n=i=0}if(a[0]&5)throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}},Af=function(e,t){var r=typeof Symbol==\"function\"&&e[Symbol.iterator];if(!r)return e;var n=r.call(e),o,i=[],s;try{for(;(t===void 0||t-- >0)&&!(o=n.next()).done;)i.push(o.value)}catch(u){s={error:u}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(s)throw s.error}}return i},Rf=function(e,t,r){if(r||arguments.length===2)for(var n=0,o=t.length,i;n<o;n++)(i||!(n in t))&&(i||(i=Array.prototype.slice.call(t,0,n)),i[n]=t[n]);return e.concat(i||Array.prototype.slice.call(t))},Ma=function(){function e(t,r){this._sharedState=t,this._metricReader=r}return e.prototype.collect=function(t){return fr(this,void 0,void 0,function(){var r,n,o,i,s=this;return pr(this,function(u){switch(u.label){case 0:return r=(0,wa.millisToHrTime)(Date.now()),n=[],o=[],i=Array.from(this._sharedState.meterSharedStates.values()).map(function(c){return fr(s,void 0,void 0,function(){var a;return pr(this,function(f){switch(f.label){case 0:return[4,c.collect(this,r,t)];case 1:return a=f.sent(),a?.scopeMetrics!=null&&n.push(a.scopeMetrics),a?.errors!=null&&o.push.apply(o,Rf([],Af(a.errors),!1)),[2]}})})}),[4,Promise.all(i)];case 1:return u.sent(),[2,{resourceMetrics:{resource:this._sharedState.resource,scopeMetrics:n},errors:o}]}})})},e.prototype.forceFlush=function(t){return fr(this,void 0,void 0,function(){return pr(this,function(r){switch(r.label){case 0:return[4,this._metricReader.forceFlush(t)];case 1:return r.sent(),[2]}})})},e.prototype.shutdown=function(t){return fr(this,void 0,void 0,function(){return pr(this,function(r){switch(r.label){case 0:return[4,this._metricReader.shutdown(t)];case 1:return r.sent(),[2]}})})},e.prototype.selectAggregationTemporality=function(t){return this._metricReader.selectAggregationTemporality(t)},e.prototype.selectAggregation=function(t){return this._metricReader.selectAggregation(t)},e}();var xa=function(e,t,r,n){function o(i){return i instanceof r?i:new r(function(s){s(i)})}return new(r||(r=Promise))(function(i,s){function u(f){try{a(n.next(f))}catch(p){s(p)}}function c(f){try{a(n.throw(f))}catch(p){s(p)}}function a(f){f.done?i(f.value):o(f.value).then(u,c)}a((n=n.apply(e,t||[])).next())})},Da=function(e,t){var r={label:0,sent:function(){if(i[0]&1)throw i[1];return i[1]},trys:[],ops:[]},n,o,i,s;return s={next:u(0),throw:u(1),return:u(2)},typeof Symbol==\"function\"&&(s[Symbol.iterator]=function(){return this}),s;function u(a){return function(f){return c([a,f])}}function c(a){if(n)throw new TypeError(\"Generator is already executing.\");for(;r;)try{if(n=1,o&&(i=a[0]&2?o.return:a[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,a[1])).done)return i;switch(o=0,i&&(a=[a[0]&2,i.value]),a[0]){case 0:case 1:i=a;break;case 4:return r.label++,{value:a[1],done:!1};case 5:r.label++,o=a[1],a=[0];continue;case 7:a=r.ops.pop(),r.trys.pop();continue;default:if(i=r.trys,!(i=i.length>0&&i[i.length-1])&&(a[0]===6||a[0]===2)){r=0;continue}if(a[0]===3&&(!i||a[1]>i[0]&&a[1]<i[3])){r.label=a[1];break}if(a[0]===6&&r.label<i[1]){r.label=i[1],i=a;break}if(i&&r.label<i[2]){r.label=i[2],r.ops.push(a);break}i[2]&&r.ops.pop(),r.trys.pop();continue}a=t.call(e,r)}catch(f){a=[6,f],o=0}finally{n=i=0}if(a[0]&5)throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}},Of=function(e){var t=typeof Symbol==\"function\"&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&typeof e.length==\"number\")return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?\"Object is not iterable.\":\"Symbol.iterator is not defined.\")},In=function(){function e(t){var r,n,o;this._shutdown=!1;var i=H.default().merge((o=t?.resource)!==null&&o!==void 0?o:H.empty());if(this._sharedState=new Na(i),t?.views!=null&&t.views.length>0)try{for(var s=Of(t.views),u=s.next();!u.done;u=s.next()){var c=u.value;this._sharedState.viewRegistry.addView(c)}}catch(a){r={error:a}}finally{try{u&&!u.done&&(n=s.return)&&n.call(s)}finally{if(r)throw r.error}}}return e.prototype.getMeter=function(t,r,n){return r===void 0&&(r=\"\"),n===void 0&&(n={}),this._shutdown?(l.diag.warn(\"A shutdown MeterProvider cannot provide a Meter\"),(0,l.createNoopMeter)()):this._sharedState.getMeterSharedState({name:t,version:r,schemaUrl:n.schemaUrl}).meter},e.prototype.addMetricReader=function(t){var r=new Ma(this._sharedState,t);t.setMetricProducer(r),this._sharedState.metricCollectors.push(r)},e.prototype.shutdown=function(t){return xa(this,void 0,void 0,function(){return Da(this,function(r){switch(r.label){case 0:return this._shutdown?(l.diag.warn(\"shutdown may only be called once per MeterProvider\"),[2]):(this._shutdown=!0,[4,Promise.all(this._sharedState.metricCollectors.map(function(n){return n.shutdown(t)}))]);case 1:return r.sent(),[2]}})})},e.prototype.forceFlush=function(t){return xa(this,void 0,void 0,function(){return Da(this,function(r){switch(r.label){case 0:return this._shutdown?(l.diag.warn(\"invalid attempt to force flush after MeterProvider shutdown\"),[2]):[4,Promise.all(this._sharedState.metricCollectors.map(function(n){return n.forceFlush(t)}))];case 1:return r.sent(),[2]}})})},e}();var Nr=v(Oe(),1),Iu=v(Va(),1),Ot=v(P(),1);var Ha=\"http.method\",Fa=\"http.url\",hr=\"http.host\",ja=\"http.scheme\",wn=\"http.status_code\",ka=\"http.user_agent\",Mn=\"http.response_content_length_uncompressed\",qa=\"net.peer.port\",Xa=\"net.peer.name\";var Ka=\"service.name\",Wa=\"service.version\";E();var Nf=Symbol.for(\"@vercel/request-context\");function nt(){return globalThis[Nf]?.get()}function Er(e){return Object.fromEntries(Object.entries(e).filter(([t,r])=>r!==void 0))}function Ya(e){return e?e.split(\"::\").at(-1):void 0}function za(e=nt(),t){if(!e)return;let r=t?Mf(t,e.headers):void 0;return Er({[hr]:e.headers.host,[ka]:e.headers[\"user-agent\"],\"http.referer\":e.headers.referer,\"vercel.request_id\":Ya(e.headers[\"x-vercel-id\"]),\"vercel.matched_path\":e.headers[\"x-matched-path\"],\"vercel.edge_region\":process.env.VERCEL_REGION,...r})}var wf={keys(e){return[]},get(e,t){return e[t.toLocaleLowerCase()]}};function Mf(e,t){if(typeof e==\"function\")return e(t,wf);let r={};for(let[n,o]of Object.entries(e)){let i=t[o.toLocaleLowerCase()];i!==void 0&&(r[n]=i)}return r}E();function zt(e){return(e&l.TraceFlags.SAMPLED)!==0}var mr=class{constructor(t,r){this.processors=t;this.attributesFromHeaders=r;this.rootSpanIds=new Map;this.waitSpanEnd=new Map}forceFlush(){return Promise.all(this.processors.map(t=>t.forceFlush().catch(r=>{l.diag.error(\"@vercel/otel: forceFlush failed:\",r)}))).then(()=>{})}shutdown(){return Promise.all(this.processors.map(t=>t.shutdown().catch(()=>{}))).then(()=>{})}onStart(t,r){let{traceId:n,spanId:o,traceFlags:i}=t.spanContext(),s=!t.parentSpanId||!this.rootSpanIds.has(n);if(s?this.rootSpanIds.set(n,{rootSpanId:o,open:[]}):this.rootSpanIds.get(n)?.open.push(t),s&&zt(i)){let u=nt(),c=za(u,this.attributesFromHeaders);c&&t.setAttributes(c),u&&u.waitUntil(async()=>{if(this.rootSpanIds.has(n)){let a=new Promise(p=>{this.waitSpanEnd.set(n,p)}),f;await Promise.race([a,new Promise(p=>{f=setTimeout(()=>{this.waitSpanEnd.delete(n),p(void 0)},50)})]),f&&clearTimeout(f)}return this.forceFlush()})}for(let u of this.processors)u.onStart(t,r)}onEnd(t){let{traceId:r,spanId:n,traceFlags:o}=t.spanContext(),i=zt(o),s=this.rootSpanIds.get(r),u=s?.rootSpanId===n;if(i){let c=Df(t);c&&Object.assign(t.attributes,c)}if(u){if(this.rootSpanIds.delete(r),s.open.length>0){for(let c of s.open)if(!c.ended&&c.spanContext().spanId!==n)try{c.end()}catch(a){l.diag.error(\"@vercel/otel: onEnd failed:\",a)}}}else if(s)for(let c=0;c<s.open.length;c++)s.open[c]?.spanContext().spanId===n&&s.open.splice(c,1);for(let c of this.processors)c.onEnd(t);if(u){let c=this.waitSpanEnd.get(r);c&&(this.waitSpanEnd.delete(r),c())}}},xf={[l.SpanKind.INTERNAL]:\"internal\",[l.SpanKind.SERVER]:\"server\",[l.SpanKind.CLIENT]:\"client\",[l.SpanKind.PRODUCER]:\"producer\",[l.SpanKind.CONSUMER]:\"consumer\"};function Df(e){let{kind:t,attributes:r}=e,{\"operation.name\":n,\"resource.name\":o,\"span.type\":i,\"next.span_type\":s,\"http.method\":u,\"http.route\":c}=r;if(n)return;let a=o??(u&&typeof u==\"string\"&&c&&typeof c==\"string\"?`${u} ${c}`:c);if(e.kind===l.SpanKind.SERVER&&u&&c&&typeof u==\"string\"&&typeof c==\"string\")return{\"operation.name\":\"web.request\",\"resource.name\":a};let f=e.instrumentationLibrary.name,p=s??i;if(p&&typeof p==\"string\"){let d=$a(f,p);return c?{\"operation.name\":d,\"resource.name\":a}:{\"operation.name\":d}}return{\"operation.name\":$a(f,t===l.SpanKind.INTERNAL?\"\":xf[t])}}function $a(e,t){if(!e)return t;let r=e.replace(/[ @./]/g,\"_\");return r.startsWith(\"_\")&&(r=r.slice(1)),t?`${r}.${t}`:r}var fs=v(Fn(),1);var ls=v(cs(),1);E();var $t=class extends ls.OTLPExporterBase{constructor(t={}){super(t),t.headers&&(this._headers=t.headers)}onShutdown(){l.diag.debug(\"@vercel/otel/otlp: onShutdown\")}onInit(){l.diag.debug(\"@vercel/otel/otlp: onInit\")}send(t,r,n){if(this._shutdownOnce.isCalled){l.diag.debug(\"@vercel/otel/otlp: Shutdown already started. Cannot send objects\");return}let o=this.convert(t),i,s,u;try{({body:i,contentType:s,headers:u}=this.toMessage(o))}catch(a){l.diag.warn(\"@vercel/otel/otlp: no proto\",a);return}let c=fetch(this.url,{method:\"POST\",body:i,headers:{...this._headers,...u,\"Content-Type\":s,\"User-Agent\":\"OTel-OTLP-Exporter-JavaScript/0.46.0\"},next:{internal:!0}}).then(a=>{l.diag.debug(\"@vercel/otel/otlp: onSuccess\",a.status,a.statusText),r(),a.arrayBuffer().catch(()=>{})}).catch(a=>{l.diag.error(\"@vercel/otel/otlp: onError\",a),n(a)}).finally(()=>{let a=this._sendingPromises.indexOf(c);this._sendingPromises.splice(a,1)});this._sendingPromises.push(c)}getDefaultUrl(t){throw new Error(\"Method not implemented.\")}};var rp=\"v1/traces\",np=`http://localhost:4318/${rp}`;function yr(e){return typeof e.url==\"string\"?e.url:np}var Qt=class{constructor(t={}){this.impl=new Xn(t)}export(t,r){this.impl.export(t,r)}shutdown(){return this.impl.shutdown()}forceFlush(){return this.impl.forceFlush()}},Xn=class extends $t{convert(t){return(0,fs.createExportTraceServiceRequest)(t,{useHex:!0,useLongBits:!1})}toMessage(t){return{body:JSON.stringify(t),contentType:\"application/json\"}}getDefaultUrl(t){return yr(t)}};var du=v(Fn(),1);var lu=v(cu(),1);function fu(e){let t=new lu.Writer;return _p(e,t),t.finish()}function _p(e,t){if(e.resourceSpans!=null&&e.resourceSpans.length)for(let r=0;r<e.resourceSpans.length;++r)hp(e.resourceSpans[r],t.uint32(10).fork()).ldelim();return t}function hp(e,t){if(e.resource!=null&&Ep(e.resource,t.uint32(10).fork()).ldelim(),e.scopeSpans!=null&&e.scopeSpans.length)for(let r=0;r<e.scopeSpans.length;++r)mp(e.scopeSpans[r],t.uint32(18).fork()).ldelim();return e.schemaUrl!=null&&t.uint32(26).string(e.schemaUrl),t}function Ep(e,t){if(e.attributes!=null&&e.attributes.length)for(let r=0;r<e.attributes.length;++r)Jt(e.attributes[r],t.uint32(10).fork()).ldelim();return e.droppedAttributesCount!=null&&t.uint32(16).uint32(e.droppedAttributesCount),t}function mp(e,t){if(e.scope!=null&&gp(e.scope,t.uint32(10).fork()).ldelim(),e.spans!=null&&e.spans.length)for(let r=0;r<e.spans.length;++r)yp(e.spans[r],t.uint32(18).fork()).ldelim();return e.schemaUrl!=null&&t.uint32(26).string(e.schemaUrl),t}function Jt(e,t){return e.key!=null&&t.uint32(10).string(e.key),e.value!=null&&pu(e.value,t.uint32(18).fork()).ldelim(),t}function pu(e,t){return e.stringValue!=null&&t.uint32(10).string(e.stringValue),e.boolValue!=null&&t.uint32(16).bool(e.boolValue),e.intValue!=null&&t.uint32(24).int64(e.intValue),e.doubleValue!=null&&t.uint32(33).double(e.doubleValue),e.arrayValue!=null&&Tp(e.arrayValue,t.uint32(42).fork()).ldelim(),e.kvlistValue!=null&&Sp(e.kvlistValue,t.uint32(50).fork()).ldelim(),e.bytesValue!=null&&t.uint32(58).bytes(e.bytesValue),t}function Tp(e,t){if(e.values!=null&&e.values.length)for(let r=0;r<e.values.length;++r)pu(e.values[r],t.uint32(10).fork()).ldelim();return t}function Sp(e,t){if(e.values!=null&&e.values.length)for(let r=0;r<e.values.length;++r)Jt(e.values[r],t.uint32(10).fork()).ldelim();return t}function gp(e,t){if(e.name!=null&&t.uint32(10).string(e.name),e.version!=null&&t.uint32(18).string(e.version),e.attributes!=null&&e.attributes.length)for(let r=0;r<e.attributes.length;++r)Jt(e.attributes[r],t.uint32(26).fork()).ldelim();return e.droppedAttributesCount!=null&&t.uint32(32).uint32(e.droppedAttributesCount),t}function yp(e,t){if(e.traceId!=null&&t.uint32(10).bytes(e.traceId),e.spanId!=null&&t.uint32(18).bytes(e.spanId),e.traceState!=null&&t.uint32(26).string(e.traceState),e.parentSpanId!=null&&t.uint32(34).bytes(e.parentSpanId),e.name!=null&&t.uint32(42).string(e.name),e.kind!=null&&t.uint32(48).int32(e.kind),e.startTimeUnixNano!=null&&t.uint32(57).fixed64(e.startTimeUnixNano),e.endTimeUnixNano!=null&&t.uint32(65).fixed64(e.endTimeUnixNano),e.attributes!=null&&e.attributes.length)for(let r=0;r<e.attributes.length;++r)Jt(e.attributes[r],t.uint32(74).fork()).ldelim();if(e.droppedAttributesCount!=null&&t.uint32(80).uint32(e.droppedAttributesCount),e.events!=null&&e.events.length)for(let r=0;r<e.events.length;++r)Ap(e.events[r],t.uint32(90).fork()).ldelim();if(e.droppedEventsCount!=null&&t.uint32(96).uint32(e.droppedEventsCount),e.links!=null&&e.links.length)for(let r=0;r<e.links.length;++r)Rp(e.links[r],t.uint32(106).fork()).ldelim();return e.droppedLinksCount!=null&&t.uint32(112).uint32(e.droppedLinksCount),e.status!=null&&vp(e.status,t.uint32(122).fork()).ldelim(),t}function vp(e,t){return e.message!=null&&t.uint32(18).string(e.message),e.code!=null&&t.uint32(24).int32(e.code),t}function Ap(e,t){if(e.timeUnixNano!=null&&t.uint32(9).fixed64(e.timeUnixNano),e.name!=null&&t.uint32(18).string(e.name),e.attributes!=null&&e.attributes.length)for(let r=0;r<e.attributes.length;++r)Jt(e.attributes[r],t.uint32(26).fork()).ldelim();return e.droppedAttributesCount!=null&&t.uint32(32).uint32(e.droppedAttributesCount),t}function Rp(e,t){if(e.traceId!=null&&t.uint32(10).bytes(e.traceId),e.spanId!=null&&t.uint32(18).bytes(e.spanId),e.traceState!=null&&t.uint32(26).string(e.traceState),e.attributes!=null&&e.attributes.length)for(let r=0;r<e.attributes.length;++r)Jt(e.attributes[r],t.uint32(34).fork()).ldelim();return e.droppedAttributesCount!=null&&t.uint32(40).uint32(e.droppedAttributesCount),t}var Bt=class{constructor(t={}){this.impl=new oi(t)}export(t,r){this.impl.export(t,r)}shutdown(){return this.impl.shutdown()}forceFlush(){return this.impl.forceFlush()}},oi=class extends $t{convert(t){return(0,du.createExportTraceServiceRequest)(t,void 0)}toMessage(t){return{body:fu(t),contentType:\"application/x-protobuf\",headers:{accept:\"application/x-protobuf\"}}}getDefaultUrl(t){return yr(t)}};E();function _u(e,t){return e.replace(/\\{(?<temp1>[^{}]+)\\}/g,(r,n)=>{let o=t[n];return o!==void 0?String(o):r})}var te=class{constructor(t={}){this.instrumentationName=\"@vercel/otel/fetch\";this.instrumentationVersion=\"1.0.0\";this.config=t}getConfig(){return this.config}setConfig(){}setTracerProvider(t){this.tracerProvider=t}setMeterProvider(){}shouldIgnore(t,r){let n=this.config.ignoreUrls??[];if(r?.opentelemetry?.ignore!==void 0)return r.opentelemetry.ignore;if(n.length===0)return!1;let o=t.toString();return n.some(i=>typeof i==\"string\"?i===\"*\"?!0:o.startsWith(i):i.test(o))}shouldPropagate(t,r){let n=process.env.VERCEL_URL||process.env.NEXT_PUBLIC_VERCEL_URL||null,o=process.env.VERCEL_BRANCH_URL||process.env.NEXT_PUBLIC_VERCEL_BRANCH_URL||null,i=this.config.propagateContextUrls??[],s=this.config.dontPropagateContextUrls??[];if(r?.opentelemetry?.propagateContext)return r.opentelemetry.propagateContext;let u=t.toString();return s.length>0&&s.some(c=>typeof c==\"string\"?c===\"*\"?!0:u.startsWith(c):c.test(u))?!1:n&&t.protocol===\"https:\"&&(t.host===n||t.host===o||t.host===nt()?.headers.host)||!n&&t.protocol===\"http:\"&&t.hostname===\"localhost\"?!0:i.some(c=>typeof c==\"string\"?c===\"*\"?!0:u.startsWith(c):c.test(u))}startSpan({tracer:t,url:r,fetchType:n,method:o=\"GET\",name:i,attributes:s={}}){let u=this.config.resourceNameTemplate,c={[Ha]:o,[Fa]:r.toString(),[hr]:r.host,[ja]:r.protocol.replace(\":\",\"\"),[Xa]:r.hostname,[qa]:r.port},a=u?_u(u,c):Lp(r.toString()),f=i??`${n} ${o} ${r.toString()}`,p=l.context.active();return t.startSpan(f,{kind:l.SpanKind.CLIENT,attributes:{...c,\"operation.name\":`${n}.${o}`,\"http.client.name\":n,\"resource.name\":a,...s}},p)}instrumentHttp(t,r){let{tracerProvider:n}=this;if(!n)return;let o=n.getTracer(this.instrumentationName,this.instrumentationVersion),{attributesFromRequestHeaders:i,attributesFromResponseHeaders:s}=this.config,u=t.request,c=t.get,a=f=>(p,d,m)=>{let g,C={},M;if(typeof p==\"string\"||p instanceof URL?(g=new URL(p.toString()),typeof d==\"function\"?M=d:d&&typeof m==\"function\"?(C=d,M=m):d&&(C=d)):(C=p,typeof d==\"function\"&&(M=d),g=Ip(C,r)),this.shouldIgnore(g))return f.apply(this,[g,C,M]);let T=this.startSpan({tracer:o,url:g,fetchType:\"http\",method:C.method||\"GET\"});if(!T.isRecording()||!zt(T.spanContext().traceFlags))return T.end(),f.apply(this,[g,C,M]);if(this.shouldPropagate(g)){let et=l.context.active(),q=l.trace.setSpan(et,T);l.propagation.inject(q,C.headers||{},bp)}i&&br(T,i,C.headers||{},Eu);try{let et=Date.now(),q=f.apply(this,[g,C,M]);return q.prependListener(\"response\",U=>{let wr=Date.now()-et;T.setAttribute(\"http.response_time\",wr),U.statusCode!==void 0?(T.setAttribute(wn,U.statusCode),U.statusCode>=500&&Gt(T,`Status: ${U.statusCode}`)):Gt(T,\"Response status code is undefined\"),s&&br(T,s,U.headers,Eu),q.listenerCount(\"response\")<=1&&U.resume(),U.on(\"end\",()=>{let bt,Mr=U.statusCode;U.aborted&&!U.complete?bt={code:l.SpanStatusCode.ERROR}:Mr&&Mr>=100&&Mr<500?bt={code:l.SpanStatusCode.UNSET}:bt={code:l.SpanStatusCode.ERROR},T.setStatus(bt),T.isRecording()&&(U.headers[\"content-length\"]&&T.setAttribute(Mn,U.headers[\"content-length\"]),T.end())})}),q.on(\"error\",U=>{T.isRecording()&&(Gt(T,U),T.end())}),q.on(\"close\",()=>{T.isRecording()&&T.end()}),q}catch(et){throw Gt(T,et),T.end(),et}};t.request=a(u),t.get=a(c)}instrumentFetch(){let{tracerProvider:t}=this;if(!t)return;let r=t.getTracer(this.instrumentationName,this.instrumentationVersion),{attributesFromRequestHeaders:n,attributesFromResponseHeaders:o}=this.config;process.env.NEXT_OTEL_FETCH_DISABLED=\"1\";let i=globalThis.fetch;this.originalFetch=i;let s=async(u,c)=>{let a=c;if(a?.next?.internal)return i(u,a);let f=new Request(u instanceof Request?u.clone():u,a),p=new URL(f.url);if(this.shouldIgnore(p,a))return i(u,a);let d=this.startSpan({tracer:r,url:p,fetchType:\"fetch\",method:f.method,name:a?.opentelemetry?.spanName,attributes:a?.opentelemetry?.attributes});if(!d.isRecording()||!zt(d.spanContext().traceFlags))return d.end(),i(u,a);if(this.shouldPropagate(p,a)){let m=l.context.active(),g=l.trace.setSpan(m,d);l.propagation.inject(g,f.headers,Op)}n&&br(d,n,f.headers,hu);try{let m=Date.now();a?.body&&a.body instanceof FormData&&f.headers.delete(\"content-type\");let g=await i(u,{...a,headers:f.headers}),C=Date.now()-m;return d.setAttribute(wn,g.status),d.setAttribute(\"http.response_time\",C),o&&br(d,o,g.headers,hu),g.status>=500&&Gt(d,`Status: ${g.status} (${g.statusText})`),g.body?Pp(g).then(M=>{d.isRecording()&&(d.setAttribute(Mn,M),d.end())},M=>{d.isRecording()&&(Gt(d,M),d.end())}):d.end(),g}catch(m){throw Gt(d,m),d.end(),m}};globalThis.fetch=s}enable(){this.disable(),this.instrumentFetch();try{let t=ci(\"node:http\"),r=ci(\"node:https\");this.instrumentHttp(t,\"http:\"),this.instrumentHttp(r,\"https:\")}catch{}}disable(){this.originalFetch&&(globalThis.fetch=this.originalFetch)}},Op={set(e,t,r){e.set(t,r)}},hu={get(e,t){let r=e.get(t);if(r!==null)return r.includes(\",\")?r.split(\",\").map(n=>n.trimStart()):r},keys(e){let t=[];return e.forEach((r,n)=>{t.push(n)}),t}},bp={set(e,t,r){e[t.toLowerCase()]=r}},Eu={get(e,t){return e[t.toLowerCase()]},keys(e){return Object.keys(e)}};function Lp(e){let t=e.indexOf(\"?\");return t===-1?e:e.substring(0,t)}function Pp(e){let t=0,n=e.clone().body?.getReader();if(!n)return Promise.resolve(0);let o=()=>n.read().then(({done:i,value:s})=>{if(!i)return t+=s.byteLength,o()});return o().then(()=>t)}function Gt(e,t){if(t instanceof Error)e.recordException(t),e.setStatus({code:l.SpanStatusCode.ERROR,message:t.message});else{let r=String(t);e.setStatus({code:l.SpanStatusCode.ERROR,message:r})}}function br(e,t,r,n){for(let[o,i]of Object.entries(t)){let s=n.get(r,i);s!==void 0&&e.setAttribute(o,s)}}function Ip(e,t){if(e.socketPath)throw new Error(\"Cannot construct a network URL: options.socketPath is specified, indicating a Unix domain socket.\");let r=e.protocol??t;r&&!r.endsWith(\":\")&&(r+=\":\");let n=e.hostname,o=e.port??e.defaultPort;if(!n&&e.host){let a=e.host.split(\":\");n=a[0];let f=a[1];if(a.length>1&&f&&o===void 0){let p=parseInt(f,10);isNaN(p)||(o=p)}}n||(n=\"localhost\");let i;if(o!==void 0&&o!==\"\"){let a=parseInt(String(o),10);isNaN(a)?i=r===\"https:\"?443:80:i=a}else i=r===\"https:\"?443:80;let s=e.path||\"/\",u=`${r}//${n}:${i}`,c=new URL(s,u);if(e.auth){let a=e.auth.split(\":\");c.username=decodeURIComponent(a[0]||\"\"),a.length>1&&(c.password=decodeURIComponent(a[1]||\"\"))}return c}E();var mu=v(P(),1),Cp=\"00\",ai=\"traceparent\",si=\"tracestate\",Te=class{fields(){return[ai,si]}inject(t,r,n){let o=l.trace.getSpanContext(t);if(!o||(0,mu.isTracingSuppressed)(t)||!(0,l.isSpanContextValid)(o))return;let i=`${Cp}-${o.traceId}-${o.spanId}-0${Number(o.traceFlags||0).toString(16)}`;n.set(r,ai,i),o.traceState&&n.set(r,si,o.traceState.serialize())}extract(t,r,n){let o=n.get(r,ai);if(!o)return t;let i=Array.isArray(o)?o[0]:o;if(typeof i!=\"string\")return t;let s=Np(i);if(!s)return t;s.isRemote=!0;let u=n.get(r,si);if(u){let c=Array.isArray(u)?u.join(\",\"):u;s.traceState=(0,l.createTraceState)(typeof c==\"string\"?c:void 0)}return l.trace.setSpanContext(t,s)}};function Np(e){let[t,r,n,o,i]=e.split(\"-\");return!t||!r||!n||!o||t.length!==2||r.length!==32||n.length!==16||o.length!==2||t===\"00\"&&i?null:{traceId:r,spanId:n,traceFlags:parseInt(o,16)}}E();var Lr=class{fields(){return[]}inject(){}extract(t){let r=nt();if(!r?.telemetry)return l.diag.warn(\"@vercel/otel: Vercel telemetry extension not found.\"),t;let{rootSpanContext:n}=r.telemetry;return n?(l.diag.debug(\"@vercel/otel: Extracted root SpanContext from Vercel request context.\",n),l.trace.setSpanContext(t,{...n,isRemote:!0,traceFlags:n.traceFlags||l.TraceFlags.SAMPLED})):t}};E();var Pr=v(P(),1);var ee=v(P()),wp=BigInt(1e9);function Su(e){return BigInt(e[0])*wp+BigInt(e[1])}function Mp(e){var t=Number(BigInt.asUintN(32,e)),r=Number(BigInt.asUintN(32,e>>BigInt(32)));return{low:t,high:r}}function gu(e){var t=Su(e);return Mp(t)}function xp(e){var t=Su(e);return t.toString()}var Dp=typeof BigInt<\"u\"?xp:ee.hrTimeToNanoseconds;function Tu(e){return e}function yu(e){if(e!==void 0)return(0,ee.hexToBase64)(e)}var Up={encodeHrTime:gu,encodeSpanContext:ee.hexToBase64,encodeOptionalSpanContext:yu};function vu(e){var t,r;if(e===void 0)return Up;var n=(t=e.useLongBits)!==null&&t!==void 0?t:!0,o=(r=e.useHex)!==null&&r!==void 0?r:!1;return{encodeHrTime:n?gu:Dp,encodeSpanContext:o?Tu:ee.hexToBase64,encodeOptionalSpanContext:o?Tu:yu}}var Bp=function(e,t){var r=typeof Symbol==\"function\"&&e[Symbol.iterator];if(!r)return e;var n=r.call(e),o,i=[],s;try{for(;(t===void 0||t-- >0)&&!(o=n.next()).done;)i.push(o.value)}catch(u){s={error:u}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(s)throw s.error}}return i};function re(e){return Object.keys(e).map(function(t){return Au(t,e[t])})}function Au(e,t){return{key:e,value:Ru(t)}}function Ru(e){var t=typeof e;return t===\"string\"?{stringValue:e}:t===\"number\"?Number.isInteger(e)?{intValue:e}:{doubleValue:e}:t===\"boolean\"?{boolValue:e}:e instanceof Uint8Array?{bytesValue:e}:Array.isArray(e)?{arrayValue:{values:e.map(Ru)}}:t===\"object\"&&e!=null?{kvlistValue:{values:Object.entries(e).map(function(r){var n=Bp(r,2),o=n[0],i=n[1];return Au(o,i)})}}:{}}function Ou(e,t){var r,n=e.spanContext(),o=e.status;return{traceId:t.encodeSpanContext(n.traceId),spanId:t.encodeSpanContext(n.spanId),parentSpanId:t.encodeOptionalSpanContext(e.parentSpanId),traceState:(r=n.traceState)===null||r===void 0?void 0:r.serialize(),name:e.name,kind:e.kind==null?0:e.kind+1,startTimeUnixNano:t.encodeHrTime(e.startTime),endTimeUnixNano:t.encodeHrTime(e.endTime),attributes:re(e.attributes),droppedAttributesCount:e.droppedAttributesCount,events:e.events.map(function(i){return Vp(i,t)}),droppedEventsCount:e.droppedEventsCount,status:{code:o.code,message:o.message},links:e.links.map(function(i){return Gp(i,t)}),droppedLinksCount:e.droppedLinksCount}}function Gp(e,t){var r;return{attributes:e.attributes?re(e.attributes):[],spanId:t.encodeSpanContext(e.context.spanId),traceId:t.encodeSpanContext(e.context.traceId),traceState:(r=e.context.traceState)===null||r===void 0?void 0:r.serialize(),droppedAttributesCount:e.droppedAttributesCount||0}}function Vp(e,t){return{attributes:e.attributes?re(e.attributes):[],name:e.name,timeUnixNano:t.encodeHrTime(e.time),droppedAttributesCount:e.droppedAttributesCount||0}}var Hp=function(e){var t=typeof Symbol==\"function\"&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&typeof e.length==\"number\")return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?\"Object is not iterable.\":\"Symbol.iterator is not defined.\")},Fp=function(e,t){var r=typeof Symbol==\"function\"&&e[Symbol.iterator];if(!r)return e;var n=r.call(e),o,i=[],s;try{for(;(t===void 0||t-- >0)&&!(o=n.next()).done;)i.push(o.value)}catch(u){s={error:u}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(s)throw s.error}}return i};function ui(e,t){var r=vu(t);return{resourceSpans:kp(e,r)}}function jp(e){var t,r,n=new Map;try{for(var o=Hp(e),i=o.next();!i.done;i=o.next()){var s=i.value,u=n.get(s.resource);u||(u=new Map,n.set(s.resource,u));var c=s.instrumentationLibrary.name+\"@\"+(s.instrumentationLibrary.version||\"\")+\":\"+(s.instrumentationLibrary.schemaUrl||\"\"),a=u.get(c);a||(a=[],u.set(c,a)),a.push(s)}}catch(f){t={error:f}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(t)throw t.error}}return n}function kp(e,t){for(var r=jp(e),n=[],o=r.entries(),i=o.next();!i.done;){for(var s=Fp(i.value,2),u=s[0],c=s[1],a=[],f=c.values(),p=f.next();!p.done;){var d=p.value;if(d.length>0){var m=d[0].instrumentationLibrary,g=m.name,C=m.version,M=m.schemaUrl,T=d.map(function(q){return Ou(q,t)});a.push({scope:{name:g,version:C},spans:T,schemaUrl:M})}p=f.next()}var et={resource:{attributes:re(u.attributes),droppedAttributesCount:0},scopeSpans:a,schemaUrl:void 0};n.push(et),i=o.next()}return n}var Ir=class{export(t,r){let n=nt();if(!n?.telemetry){l.diag.warn(\"@vercel/otel: no telemetry context found\"),r({code:Pr.ExportResultCode.SUCCESS,error:void 0});return}try{let o=ui(t,{useHex:!0,useLongBits:!1});n.telemetry.reportSpans(o),r({code:Pr.ExportResultCode.SUCCESS,error:void 0})}catch(o){r({code:Pr.ExportResultCode.FAILED,error:o instanceof Error?o:new Error(String(o))})}}shutdown(){return Promise.resolve()}forceFlush(){return Promise.resolve()}};var qp={ALL:l.DiagLogLevel.ALL,VERBOSE:l.DiagLogLevel.VERBOSE,DEBUG:l.DiagLogLevel.DEBUG,INFO:l.DiagLogLevel.INFO,WARN:l.DiagLogLevel.WARN,ERROR:l.DiagLogLevel.ERROR,NONE:l.DiagLogLevel.NONE},Cr=class{constructor(t={}){this.configuration=t}start(){let t=Xp(),r=this.configuration,n=process.env.NEXT_RUNTIME||\"nodejs\",o=!!t.OTEL_SDK_DISABLED;if(process.env.OTEL_LOG_LEVEL&&l.diag.setLogger(new l.DiagConsoleLogger,{logLevel:qp[process.env.OTEL_LOG_LEVEL.toUpperCase()]}),o)return;let i=r.idGenerator??new tr,s=r.contextManager??new Iu.AsyncLocalStorageContextManager;s.enable(),this.contextManager=s;let u=t.OTEL_SERVICE_NAME||r.serviceName||\"app\",c=new H(Er({[Ka]:u,\"node.ci\":process.env.CI?!0:void 0,\"node.env\":\"production\",env:process.env.VERCEL_ENV||process.env.NEXT_PUBLIC_VERCEL_ENV,\"vercel.region\":process.env.VERCEL_REGION,\"vercel.runtime\":n,\"vercel.sha\":process.env.VERCEL_GIT_COMMIT_SHA||process.env.NEXT_PUBLIC_VERCEL_GIT_COMMIT_SHA,\"vercel.host\":process.env.VERCEL_URL||process.env.NEXT_PUBLIC_VERCEL_URL||void 0,\"vercel.branch_host\":process.env.VERCEL_BRANCH_URL||process.env.NEXT_PUBLIC_VERCEL_BRANCH_URL||void 0,\"vercel.deployment_id\":process.env.VERCEL_DEPLOYMENT_ID||void 0,[Wa]:process.env.VERCEL_DEPLOYMENT_ID,...r.attributes})),a=r.resourceDetectors??[No];if(r.autoDetectResources??!0){let T={detectors:a};c=c.merge(Mo(T))}let p=Wp(r.propagators,r,t),d=Yp(r.traceSampler,t),m=zp(r.spanProcessors,r,t);m.length===0&&l.diag.warn(\"@vercel/otel: No span processors configured. No spans will be exported.\");let g=r.spanLimits,C=new Bo({resource:c,idGenerator:i,sampler:d,spanLimits:g});if(C.addSpanProcessor(new mr(m,r.attributesFromHeaders)),C.register({contextManager:s,propagator:new Ot.CompositePropagator({propagators:p})}),this.tracerProvider=C,r.logRecordProcessor){let T=new gn({resource:c});this.loggerProvider=T,T.addLogRecordProcessor(r.logRecordProcessor),st.logs.setGlobalLoggerProvider(T)}if(r.metricReader||r.views){let T=new In({resource:c,views:r.views??[]});r.metricReader&&T.addMetricReader(r.metricReader),l.metrics.setGlobalMeterProvider(T),this.meterProvider=T}let M=Kp(r.instrumentations,r.instrumentationConfig);this.disableInstrumentations=(0,Pu.registerInstrumentations)({instrumentations:M}),l.diag.info(\"@vercel/otel: started\",u,n)}async shutdown(){let t=[];this.tracerProvider&&t.push(this.tracerProvider.shutdown()),this.loggerProvider&&t.push(this.loggerProvider.shutdown()),this.meterProvider&&t.push(this.meterProvider.shutdown()),l.diag.info(\"@vercel/otel: shutting down\",t.length,process.env.NEXT_RUNTIME),await Promise.all(t),this.contextManager&&this.contextManager.disable();let{disableInstrumentations:r}=this;r&&r()}};function Xp(){let e=(0,Nr.parseEnvironment)(process.env);return{...Nr.DEFAULT_ENVIRONMENT,...e}}function Kp(e,t){return(e??[\"auto\"]).map(r=>r===\"auto\"?(l.diag.debug(\"@vercel/otel: Configure instrumentations: fetch\",t?.fetch),[new te(t?.fetch)]):r===\"fetch\"?(l.diag.debug(\"@vercel/otel: Configure instrumentations: fetch\",t?.fetch),new te(t?.fetch)):r).flat()}function Wp(e,t,r){let n=process.env.OTEL_PROPAGATORS&&r.OTEL_PROPAGATORS&&r.OTEL_PROPAGATORS.length>0?r.OTEL_PROPAGATORS:void 0;return(e??n??[\"auto\"]).map(o=>{if(o===\"none\")return[];if(o===\"auto\"){let i=[];return i.push({name:\"tracecontext\",propagator:new Te}),i.push({name:\"baggage\",propagator:new Ot.W3CBaggagePropagator}),i.push({name:\"vercel-runtime\",propagator:new Lr}),l.diag.debug(`@vercel/otel: Configure propagators: ${i.map(s=>s.name).join(\", \")}`),i.map(s=>s.propagator)}if(o===\"tracecontext\")return l.diag.debug(\"@vercel/otel: Configure propagator: tracecontext\"),new Te;if(o===\"baggage\")return l.diag.debug(\"@vercel/otel: Configure propagator: baggage\"),new Ot.W3CBaggagePropagator;if(typeof o==\"string\")throw new Error(`Unknown propagator: \"${o}\"`);return o}).flat()}var bu=\"always_on\",ne=1;function Yp(e,t){if(e&&typeof e!=\"string\")return e;let r=e&&e!==\"auto\"?e:t.OTEL_TRACES_SAMPLER||bu;switch(l.diag.debug(\"@vercel/otel: Configure sampler: \",r),r){case\"always_on\":return new X;case\"always_off\":return new at;case\"parentbased_always_on\":return new Et({root:new X});case\"parentbased_always_off\":return new Et({root:new at});case\"traceidratio\":return new kt(Lu(t));case\"parentbased_traceidratio\":return new Et({root:new kt(Lu(t))});default:return l.diag.error(`@vercel/otel: OTEL_TRACES_SAMPLER value \"${String(t.OTEL_TRACES_SAMPLER)} invalid, defaulting to ${bu}\".`),new X}}function Lu(e){if(e.OTEL_TRACES_SAMPLER_ARG===void 0||e.OTEL_TRACES_SAMPLER_ARG===\"\")return l.diag.error(`@vercel/otel: OTEL_TRACES_SAMPLER_ARG is blank, defaulting to ${ne}.`),ne;l.diag.debug(\"@vercel/otel: Configure sampler probability: \",e.OTEL_TRACES_SAMPLER_ARG);let t=Number(e.OTEL_TRACES_SAMPLER_ARG);return isNaN(t)?(l.diag.error(`@vercel/otel: OTEL_TRACES_SAMPLER_ARG=${e.OTEL_TRACES_SAMPLER_ARG} was given, but it is invalid, defaulting to ${ne}.`),ne):t<0||t>1?(l.diag.error(`@vercel/otel: OTEL_TRACES_SAMPLER_ARG=${e.OTEL_TRACES_SAMPLER_ARG} was given, but it is out of range ([0..1]), defaulting to ${ne}.`),ne):t}function zp(e,t,r){return[...(e??[\"auto\"]).flatMap(n=>{if(n===\"auto\"){let o=[new Mt(new Ir)];if(process.env.VERCEL_OTEL_ENDPOINTS){let i=process.env.VERCEL_OTEL_ENDPOINTS_PORT||\"4318\",s=process.env.VERCEL_OTEL_ENDPOINTS_PROTOCOL||\"http/protobuf\";l.diag.debug(\"@vercel/otel: Configure vercel otel collector on port: \",i,s);let u={url:`http://localhost:${i}/v1/traces`,headers:{}},c=s===\"http/protobuf\"?new Bt(u):new Qt(u);o.push(new Mt(c))}else(!t.traceExporter||t.traceExporter===\"auto\"||r.OTEL_EXPORTER_OTLP_TRACES_ENDPOINT||r.OTEL_EXPORTER_OTLP_ENDPOINT)&&o.push(new Mt($p(r)));return o}return n}).filter(Jp),...t.traceExporter&&t.traceExporter!==\"auto\"?[new Mt(t.traceExporter)]:[]]}function $p(e){let t=process.env.OTEL_EXPORTER_OTLP_TRACES_PROTOCOL??process.env.OTEL_EXPORTER_OTLP_PROTOCOL??\"http/protobuf\",r=Zp(e),n={...Ot.baggageUtils.parseKeyPairsIntoRecord(e.OTEL_EXPORTER_OTLP_HEADERS),...Ot.baggageUtils.parseKeyPairsIntoRecord(e.OTEL_EXPORTER_OTLP_TRACES_HEADERS)};switch(l.diag.debug(\"@vercel/otel: Configure trace exporter: \",t,r,`headers: ${Object.keys(n).join(\",\")||\"<none>\"}`),t){case\"http/json\":return new Qt({url:r,headers:n});case\"http/protobuf\":return new Bt({url:r,headers:n});default:return l.diag.warn(`@vercel/otel: Unsupported OTLP traces protocol: ${t}. Using http/protobuf.`),new Bt}}var Cu=\"v1/traces\",Qp=`http://localhost:4318/${Cu}`;function Zp(e){let t=e.OTEL_EXPORTER_OTLP_TRACES_ENDPOINT;if(t&&typeof t==\"string\")return t;let r=e.OTEL_EXPORTER_OTLP_ENDPOINT;return r&&typeof r==\"string\"?`${r}/${Cu}`:Qp}function Jp(e){return e!=null}function RT(e){let t;e?typeof e==\"string\"?t={serviceName:e}:t=e:t={},new Cr(t).start()}export{te as FetchInstrumentation,Qt as OTLPHttpJsonTraceExporter,Bt as OTLPHttpProtoTraceExporter,RT as registerOTel};\n//# sourceMappingURL=index.js.map\n", "import { registerOTel } from '@vercel/otel';\n\nexport function register() {\n  registerOTel({ serviceName: 'ai-chatbot' });\n}\n"], "names": ["register"], "sourceRoot": "", "ignoreList": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56]}