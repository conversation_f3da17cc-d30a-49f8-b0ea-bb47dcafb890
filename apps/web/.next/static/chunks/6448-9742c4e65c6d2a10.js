"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6448],{7156:(e,t,n)=>{n.d(t,{hO:()=>i,sG:()=>f});var r=n(41987),u=n(9604),o=n(79649),l=n(44995),f=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,o.TL)(`Primitive.${t}`),u=r.forwardRef((e,r)=>{let{asChild:u,...o}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(u?n:t,{...o,ref:r})});return u.displayName=`Primitive.${t}`,{...e,[t]:u}},{});function i(e,t){e&&u.flushSync(()=>e.dispatchEvent(t))}},27261:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return v},useLinkStatus:function(){return _}});let r=n(15999),u=n(44995),o=r._(n(41987)),l=n(85908),f=n(9330),i=n(87533),a=n(77849),c=n(58490),s=n(57720);n(21611);let p=n(43781),d=n(74637),y=n(50529);function h(e){return"string"==typeof e?e:(0,l.formatUrl)(e)}function v(e){let t,n,r,[l,v]=(0,o.useOptimistic)(p.IDLE_LINK_STATUS),_=(0,o.useRef)(null),{href:g,as:m,children:j,prefetch:P=null,passHref:T,replace:C,shallow:M,scroll:O,onClick:k,onMouseEnter:E,onTouchStart:L,legacyBehavior:x=!1,onNavigate:S,ref:w,...A}=e;t=j,x&&("string"==typeof t||"number"==typeof t)&&(t=(0,u.jsx)("a",{children:t}));let N=o.default.useContext(f.AppRouterContext),I=!1!==P,K=null===P?i.PrefetchKind.AUTO:i.PrefetchKind.FULL,{href:R,as:U}=o.default.useMemo(()=>{let e=h(g);return{href:e,as:m?h(m):e}},[g,m]);x&&(n=o.default.Children.only(t));let D=x?n&&"object"==typeof n&&n.ref:w,F=o.default.useCallback(e=>(null!==N&&(_.current=(0,p.mountLinkInstance)(e,R,N,K,I,v)),()=>{_.current&&((0,p.unmountLinkForCurrentNavigation)(_.current),_.current=null),(0,p.unmountPrefetchableInstance)(e)}),[I,R,N,K,v]),$={ref:(0,a.useMergedRef)(F,D),onClick(e){x||"function"!=typeof k||k(e),x&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),N&&(e.defaultPrevented||function(e,t,n,r,u,l,f){let{nodeName:i}=e.currentTarget;if(!("A"===i.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,d.isLocalURL)(t)){u&&(e.preventDefault(),location.replace(t));return}e.preventDefault(),o.default.startTransition(()=>{if(f){let e=!1;if(f({preventDefault:()=>{e=!0}}),e)return}(0,y.dispatchNavigateAction)(n||t,u?"replace":"push",null==l||l,r.current)})}}(e,R,U,_,C,O,S))},onMouseEnter(e){x||"function"!=typeof E||E(e),x&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e),N&&I&&(0,p.onNavigationIntent)(e.currentTarget)},onTouchStart:function(e){x||"function"!=typeof L||L(e),x&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e),N&&I&&(0,p.onNavigationIntent)(e.currentTarget)}};return(0,c.isAbsoluteUrl)(U)?$.href=U:x&&!T&&("a"!==n.type||"href"in n.props)||($.href=(0,s.addBasePath)(U)),r=x?o.default.cloneElement(n,$):(0,u.jsx)("a",{...A,...$,children:t}),(0,u.jsx)(b.Provider,{value:l,children:r})}n(76355);let b=(0,o.createContext)(p.IDLE_LINK_STATUS),_=()=>(0,o.useContext)(b);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},76355:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return n}});let n=e=>{}},77849:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return u}});let r=n(41987);function u(e,t){let n=(0,r.useRef)(null),u=(0,r.useRef)(null);return(0,r.useCallback)(r=>{if(null===r){let e=n.current;e&&(n.current=null,e());let t=u.current;t&&(u.current=null,t())}else e&&(n.current=o(e,r)),t&&(u.current=o(t,r))},[e,t])}function o(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let n=e(t);return"function"==typeof n?n:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}}]);