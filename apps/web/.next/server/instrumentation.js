const CHUNK_PUBLIC_PATH = "server/instrumentation.js";
const runtime = require("./chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules_cbb2fbc7._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__bc84e941._.js");
runtime.getOrInstantiateRuntimeModule("[project]/apps/web/instrumentation.ts [instrumentation-edge] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/apps/web/instrumentation.ts [instrumentation-edge] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
