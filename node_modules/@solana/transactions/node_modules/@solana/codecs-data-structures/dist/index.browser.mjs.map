{"version": 3, "sources": ["../src/assertions.ts", "../src/utils.ts", "../src/array.ts", "../src/bit-array.ts", "../src/boolean.ts", "../src/bytes.ts", "../../codecs-strings/src/base16.ts", "../src/constant.ts", "../src/tuple.ts", "../src/union.ts", "../src/discriminated-union.ts", "../src/enum-helpers.ts", "../src/enum.ts", "../src/hidden-prefix.ts", "../src/hidden-suffix.ts", "../src/literal-union.ts", "../src/map.ts", "../src/unit.ts", "../src/nullable.ts", "../src/set.ts", "../src/struct.ts"], "names": ["newOffset", "createEncoder", "createDecoder", "combineCodec", "SolanaError", "getEncodedSize", "isFixedSize", "getU8Encoder", "transformEncoder", "getU8Decoder", "transformDecoder", "containsBytes"], "mappings": ";;;;;AAGO,SAAS,gCAAA,CACZ,gBACA,EAAA,QAAA,EACA,MACF,EAAA;AACE,EAAA,IAAI,aAAa,MAAQ,EAAA;AACrB,IAAM,MAAA,IAAI,YAAY,6CAA+C,EAAA;AAAA,MACjE,MAAA;AAAA,MACA,gBAAA;AAAA,MACA;AAAA,KACH,CAAA;AAAA;AAET;ACDO,SAAS,cAAc,KAAyC,EAAA;AACnE,EAAA,OAAO,KAAM,CAAA,MAAA;AAAA,IACT,CAAC,GAAK,EAAA,IAAA,KAAU,GAAQ,KAAA,IAAA,IAAQ,IAAS,KAAA,IAAA,GAAO,IAAO,GAAA,IAAA,CAAK,GAAI,CAAA,GAAA,EAAK,IAAI,CAAA;AAAA,IACzE;AAAA,GACJ;AACJ;AAEO,SAAS,cAAc,KAAyC,EAAA;AACnE,EAAA,OAAO,KAAM,CAAA,MAAA,CAAO,CAAC,GAAA,EAAK,IAAU,KAAA,GAAA,KAAQ,IAAQ,IAAA,IAAA,KAAS,IAAO,GAAA,IAAA,GAAO,GAAM,GAAA,IAAA,EAAO,CAAkB,CAAA;AAC9G;AAEO,SAAS,aAAa,KAAoE,EAAA;AAC7F,EAAA,OAAO,WAAY,CAAA,KAAK,CAAI,GAAA,KAAA,CAAM,SAAY,GAAA,IAAA;AAClD;AAEO,SAAS,WAAW,KAAoE,EAAA;AAC3F,EAAA,OAAO,YAAY,KAAK,CAAA,GAAI,KAAM,CAAA,SAAA,GAAa,MAAM,OAAW,IAAA,IAAA;AACpE;;;AC+DO,SAAS,eACZ,CAAA,IAAA,EACA,MAA0C,GAAA,EAC1B,EAAA;AAChB,EAAM,MAAA,IAAA,GAAO,MAAO,CAAA,IAAA,IAAQ,aAAc,EAAA;AAC1C,EAAA,MAAM,SAAY,GAAA,yBAAA,CAA0B,IAAM,EAAA,YAAA,CAAa,IAAI,CAAC,CAAA;AACpE,EAAA,MAAM,UAAU,yBAA0B,CAAA,IAAA,EAAM,UAAW,CAAA,IAAI,CAAC,CAAK,IAAA,MAAA;AAErE,EAAA,OAAO,aAAc,CAAA;AAAA,IACjB,GAAI,SAAA,KAAc,IACZ,GAAA,EAAE,WACF,GAAA;AAAA,MACI,gBAAA,EAAkB,CAAC,KAAmB,KAAA;AAClC,QAAM,MAAA,UAAA,GAAa,OAAO,IAAS,KAAA,QAAA,GAAW,eAAe,KAAM,CAAA,MAAA,EAAQ,IAAI,CAAI,GAAA,CAAA;AACnF,QAAA,OAAO,UAAa,GAAA,CAAC,GAAG,KAAK,EAAE,MAAO,CAAA,CAAC,GAAK,EAAA,KAAA,KAAU,GAAM,GAAA,cAAA,CAAe,KAAO,EAAA,IAAI,GAAG,CAAC,CAAA;AAAA,OAC9F;AAAA,MACA;AAAA,KACJ;AAAA,IACN,KAAO,EAAA,CAAC,KAAgB,EAAA,KAAA,EAAO,MAAW,KAAA;AACtC,MAAI,IAAA,OAAO,SAAS,QAAU,EAAA;AAC1B,QAAiC,gCAAA,CAAA,OAAA,EAAS,IAAM,EAAA,KAAA,CAAM,MAAM,CAAA;AAAA;AAEhE,MAAI,IAAA,OAAO,SAAS,QAAU,EAAA;AAC1B,QAAA,MAAA,GAAS,IAAK,CAAA,KAAA,CAAM,KAAM,CAAA,MAAA,EAAQ,OAAO,MAAM,CAAA;AAAA;AAEnD,MAAA,KAAA,CAAM,QAAQ,CAAS,KAAA,KAAA;AACnB,QAAA,MAAA,GAAS,IAAK,CAAA,KAAA,CAAM,KAAO,EAAA,KAAA,EAAO,MAAM,CAAA;AAAA,OAC3C,CAAA;AACD,MAAO,OAAA,MAAA;AAAA;AACX,GACH,CAAA;AACL;AA0CO,SAAS,eAAqB,CAAA,IAAA,EAAoB,MAA0C,GAAA,EAAoB,EAAA;AACnH,EAAM,MAAA,IAAA,GAAO,MAAO,CAAA,IAAA,IAAQ,aAAc,EAAA;AAC1C,EAAM,MAAA,QAAA,GAAW,aAAa,IAAI,CAAA;AAClC,EAAM,MAAA,SAAA,GAAY,yBAA0B,CAAA,IAAA,EAAM,QAAQ,CAAA;AAC1D,EAAA,MAAM,UAAU,yBAA0B,CAAA,IAAA,EAAM,UAAW,CAAA,IAAI,CAAC,CAAK,IAAA,MAAA;AAErE,EAAA,OAAO,aAAc,CAAA;AAAA,IACjB,GAAI,SAAc,KAAA,IAAA,GAAO,EAAE,SAAU,EAAA,GAAI,EAAE,OAAQ,EAAA;AAAA,IACnD,IAAA,EAAM,CAAC,KAAA,EAAwC,MAAW,KAAA;AACtD,MAAA,MAAM,QAAe,EAAC;AACtB,MAAI,IAAA,OAAO,SAAS,QAAY,IAAA,KAAA,CAAM,MAAM,MAAM,CAAA,CAAE,WAAW,CAAG,EAAA;AAC9D,QAAO,OAAA,CAAC,OAAO,MAAM,CAAA;AAAA;AAGzB,MAAA,IAAI,SAAS,WAAa,EAAA;AACtB,QAAO,OAAA,MAAA,GAAS,MAAM,MAAQ,EAAA;AAC1B,UAAA,MAAM,CAAC,KAAOA,EAAAA,UAAS,IAAI,IAAK,CAAA,IAAA,CAAK,OAAO,MAAM,CAAA;AAClD,UAASA,MAAAA,GAAAA,UAAAA;AACT,UAAA,KAAA,CAAM,KAAK,KAAK,CAAA;AAAA;AAEpB,QAAO,OAAA,CAAC,OAAO,MAAM,CAAA;AAAA;AAGzB,MAAA,MAAM,CAAC,YAAA,EAAc,SAAS,CAAA,GAAI,OAAO,IAAS,KAAA,QAAA,GAAW,CAAC,IAAA,EAAM,MAAM,CAAA,GAAI,IAAK,CAAA,IAAA,CAAK,OAAO,MAAM,CAAA;AACrG,MAAS,MAAA,GAAA,SAAA;AACT,MAAA,KAAA,IAAS,CAAI,GAAA,CAAA,EAAG,CAAI,GAAA,YAAA,EAAc,KAAK,CAAG,EAAA;AACtC,QAAA,MAAM,CAAC,KAAOA,EAAAA,UAAS,IAAI,IAAK,CAAA,IAAA,CAAK,OAAO,MAAM,CAAA;AAClD,QAASA,MAAAA,GAAAA,UAAAA;AACT,QAAA,KAAA,CAAM,KAAK,KAAK,CAAA;AAAA;AAEpB,MAAO,OAAA,CAAC,OAAO,MAAM,CAAA;AAAA;AACzB,GACH,CAAA;AACL;AAqFO,SAAS,aACZ,CAAA,IAAA,EACA,MAAwC,GAAA,EACnB,EAAA;AACrB,EAAO,OAAA,YAAA,CAAa,gBAAgB,IAAM,EAAA,MAAgB,GAAG,eAAgB,CAAA,IAAA,EAAM,MAAgB,CAAC,CAAA;AACxG;AAEA,SAAS,yBAAA,CAA0B,MAAqC,QAAwC,EAAA;AAC5G,EAAI,IAAA,OAAO,IAAS,KAAA,QAAA,EAAiB,OAAA,IAAA;AACrC,EAAI,IAAA,IAAA,KAAS,GAAU,OAAA,CAAA;AACvB,EAAO,OAAA,QAAA,KAAa,IAAO,GAAA,IAAA,GAAO,QAAW,GAAA,IAAA;AACjD;AC5OO,SAAS,kBACZ,CAAA,IAAA,EACA,MAAwC,GAAA,EACN,EAAA;AAClC,EAAA,MAAM,eAAoC,OAAO,MAAA,KAAW,YAAY,EAAE,QAAA,EAAU,QAAW,GAAA,MAAA;AAC/F,EAAM,MAAA,QAAA,GAAW,aAAa,QAAY,IAAA,KAAA;AAC1C,EAAA,OAAOC,aAAc,CAAA;AAAA,IACjB,SAAW,EAAA,IAAA;AAAA,IACX,KAAA,CAAM,KAAkB,EAAA,KAAA,EAAO,MAAQ,EAAA;AACnC,MAAA,MAAM,aAAuB,EAAC;AAE9B,MAAA,KAAA,IAAS,CAAI,GAAA,CAAA,EAAG,CAAI,GAAA,IAAA,EAAM,KAAK,CAAG,EAAA;AAC9B,QAAA,IAAI,IAAO,GAAA,CAAA;AACX,QAAA,KAAA,IAAS,CAAI,GAAA,CAAA,EAAG,CAAI,GAAA,CAAA,EAAG,KAAK,CAAG,EAAA;AAC3B,UAAA,MAAM,UAAU,MAAO,CAAA,KAAA,CAAM,IAAI,CAAI,GAAA,CAAC,KAAK,CAAC,CAAA;AAC5C,UAAQ,IAAA,IAAA,OAAA,KAAY,QAAW,GAAA,CAAA,GAAI,CAAI,GAAA,CAAA,CAAA;AAAA;AAE3C,QAAA,IAAI,QAAU,EAAA;AACV,UAAA,UAAA,CAAW,QAAQ,IAAI,CAAA;AAAA,SACpB,MAAA;AACH,UAAA,UAAA,CAAW,KAAK,IAAI,CAAA;AAAA;AACxB;AAGJ,MAAM,KAAA,CAAA,GAAA,CAAI,YAAY,MAAM,CAAA;AAC5B,MAAO,OAAA,IAAA;AAAA;AACX,GACH,CAAA;AACL;AA8BO,SAAS,kBACZ,CAAA,IAAA,EACA,MAAwC,GAAA,EACN,EAAA;AAClC,EAAA,MAAM,eAAoC,OAAO,MAAA,KAAW,YAAY,EAAE,QAAA,EAAU,QAAW,GAAA,MAAA;AAC/F,EAAM,MAAA,QAAA,GAAW,aAAa,QAAY,IAAA,KAAA;AAC1C,EAAA,OAAOC,aAAc,CAAA;AAAA,IACjB,SAAW,EAAA,IAAA;AAAA,IACX,IAAA,CAAK,OAAO,MAAQ,EAAA;AAChB,MAAsC,qCAAA,CAAA,UAAA,EAAY,IAAM,EAAA,KAAA,EAAO,MAAM,CAAA;AACrE,MAAA,MAAM,WAAsB,EAAC;AAC7B,MAAA,IAAI,KAAQ,GAAA,KAAA,CAAM,KAAM,CAAA,MAAA,EAAQ,SAAS,IAAI,CAAA;AAC7C,MAAQ,KAAA,GAAA,QAAA,GAAW,KAAM,CAAA,OAAA,EAAY,GAAA,KAAA;AAErC,MAAA,KAAA,CAAM,QAAQ,CAAQ,IAAA,KAAA;AAClB,QAAA,KAAA,IAAS,CAAI,GAAA,CAAA,EAAG,CAAI,GAAA,CAAA,EAAG,KAAK,CAAG,EAAA;AAC3B,UAAA,IAAI,QAAU,EAAA;AACV,YAAA,QAAA,CAAS,IAAK,CAAA,OAAA,CAAQ,IAAO,GAAA,CAAC,CAAC,CAAA;AAC/B,YAAS,IAAA,KAAA,CAAA;AAAA,WACN,MAAA;AACH,YAAA,QAAA,CAAS,IAAK,CAAA,OAAA,CAAQ,IAAO,GAAA,GAAW,CAAC,CAAA;AACzC,YAAS,IAAA,KAAA,CAAA;AAAA;AACb;AACJ,OACH,CAAA;AAED,MAAO,OAAA,CAAC,QAAU,EAAA,MAAA,GAAS,IAAI,CAAA;AAAA;AACnC,GACH,CAAA;AACL;AAkDO,SAAS,gBACZ,CAAA,IAAA,EACA,MAAwC,GAAA,EACG,EAAA;AAC3C,EAAOC,OAAAA,YAAAA,CAAa,mBAAmB,IAAM,EAAA,MAAM,GAAG,kBAAmB,CAAA,IAAA,EAAM,MAAM,CAAC,CAAA;AAC1F;AC9HO,SAAS,iBAAA,CAAkB,MAA4C,GAAA,EAAsB,EAAA;AAChG,EAAO,OAAA,gBAAA,CAAiB,OAAO,IAAQ,IAAA,YAAA,IAAgB,CAAC,KAAA,KAAoB,KAAQ,GAAA,CAAA,GAAI,CAAE,CAAA;AAC9F;AA6BO,SAAS,iBAAA,CAAkB,MAA4C,GAAA,EAAsB,EAAA;AAChG,EAAO,OAAA,gBAAA,CAAiB,MAAO,CAAA,IAAA,IAAQ,YAAa,EAAA,EAAG,CAAC,KAAoC,KAAA,MAAA,CAAO,KAAK,CAAA,KAAM,CAAC,CAAA;AACnH;AAmDO,SAAS,eAAA,CAAgB,MAA0C,GAAA,EAAoB,EAAA;AAC1F,EAAA,OAAOA,aAAa,iBAAkB,CAAA,MAAM,CAAG,EAAA,iBAAA,CAAkB,MAAM,CAAC,CAAA;AAC5E;AC/HO,SAAS,eAAwE,GAAA;AACpF,EAAA,OAAOF,aAAc,CAAA;AAAA,IACjB,gBAAA,EAAkB,WAAS,KAAM,CAAA,MAAA;AAAA,IACjC,KAAO,EAAA,CAAC,KAAO,EAAA,KAAA,EAAO,MAAW,KAAA;AAC7B,MAAM,KAAA,CAAA,GAAA,CAAI,OAAO,MAAM,CAAA;AACvB,MAAA,OAAO,SAAS,KAAM,CAAA,MAAA;AAAA;AAC1B,GACH,CAAA;AACL;AA2BO,SAAS,eAA2D,GAAA;AACvE,EAAA,OAAOC,aAAc,CAAA;AAAA,IACjB,IAAA,EAAM,CAAC,KAAA,EAAO,MAAW,KAAA;AACrB,MAAM,MAAA,KAAA,GAAQ,KAAM,CAAA,KAAA,CAAM,MAAM,CAAA;AAChC,MAAA,OAAO,CAAC,KAAA,EAAO,MAAS,GAAA,KAAA,CAAM,MAAM,CAAA;AAAA;AACxC,GACH,CAAA;AACL;AAmCO,SAAS,aAAwF,GAAA;AACpG,EAAA,OAAOC,YAAa,CAAA,eAAA,EAAmB,EAAA,eAAA,EAAiB,CAAA;AAC5D;ACRa,IAAA,gBAAA,GAAmB,MAC5BD,aAAc,CAAA;AACV,EAAA,IAAA,CAAK,OAAO,MAAQ,EAAA;AAChB,IAAA,MAAM,QAAQ,KAAM,CAAA,KAAA,CAAM,MAAM,CAAE,CAAA,MAAA,CAAO,CAAC,GAAK,EAAA,IAAA,KAAS,GAAM,GAAA,IAAA,CAAK,SAAS,EAAE,CAAA,CAAE,SAAS,CAAG,EAAA,GAAG,GAAG,EAAE,CAAA;AAC7F,IAAA,OAAA,CAAC,KAAO,EAAA,KAAA,CAAM,MAAM,CAAA;AAAA;AAEnC,CAAC,CAAA;AC1EE,SAAS,mBACZ,QAC2C,EAAA;AAC3C,EAAA,OAAOD,aAAc,CAAA;AAAA,IACjB,WAAW,QAAS,CAAA,MAAA;AAAA,IACpB,KAAO,EAAA,CAAC,CAAG,EAAA,KAAA,EAAO,MAAW,KAAA;AACzB,MAAM,KAAA,CAAA,GAAA,CAAI,UAAU,MAAM,CAAA;AAC1B,MAAA,OAAO,SAAS,QAAS,CAAA,MAAA;AAAA;AAC7B,GACH,CAAA;AACL;AA0BO,SAAS,mBACZ,QAC2C,EAAA;AAC3C,EAAA,OAAOC,aAAc,CAAA;AAAA,IACjB,WAAW,QAAS,CAAA,MAAA;AAAA,IACpB,IAAA,EAAM,CAAC,KAAA,EAAO,MAAW,KAAA;AACrB,MAAA,MAAM,SAAS,gBAAiB,EAAA;AAChC,MAAA,IAAI,CAAC,aAAA,CAAc,KAAO,EAAA,QAAA,EAAU,MAAM,CAAG,EAAA;AACzC,QAAM,MAAA,IAAIE,YAAY,sCAAwC,EAAA;AAAA,UAC1D,QAAA;AAAA,UACA,IAAM,EAAA,KAAA;AAAA,UACN,WAAA,EAAa,MAAO,CAAA,MAAA,CAAO,QAAQ,CAAA;AAAA,UACnC,OAAA,EAAS,MAAO,CAAA,MAAA,CAAO,KAAK,CAAA;AAAA,UAC5B;AAAA,SACH,CAAA;AAAA;AAEL,MAAA,OAAO,CAAC,MAAA,EAAW,MAAS,GAAA,QAAA,CAAS,MAAM,CAAA;AAAA;AAC/C,GACH,CAAA;AACL;AAqCO,SAAS,iBACZ,QAC+C,EAAA;AAC/C,EAAA,OAAOD,aAAa,kBAAmB,CAAA,QAAQ,CAAG,EAAA,kBAAA,CAAmB,QAAQ,CAAC,CAAA;AAClF;AC3DO,SAAS,gBACZ,KACwC,EAAA;AAExC,EAAA,MAAM,SAAY,GAAA,aAAA,CAAc,KAAM,CAAA,GAAA,CAAI,YAAY,CAAC,CAAA;AACvD,EAAA,MAAM,UAAU,aAAc,CAAA,KAAA,CAAM,GAAI,CAAA,UAAU,CAAC,CAAK,IAAA,MAAA;AAExD,EAAA,OAAOF,aAAc,CAAA;AAAA,IACjB,GAAI,cAAc,IACZ,GAAA;AAAA,MACI,gBAAA,EAAkB,CAAC,KACf,KAAA,KAAA,CAAM,IAAI,CAAC,IAAA,EAAM,UAAUI,cAAe,CAAA,KAAA,CAAM,KAAK,CAAG,EAAA,IAAI,CAAC,CAAE,CAAA,MAAA,CAAO,CAAC,GAAK,EAAA,GAAA,KAAQ,GAAM,GAAA,GAAA,EAAK,CAAC,CAAA;AAAA,MACpG;AAAA,KACJ,GACA,EAAE,SAAU,EAAA;AAAA,IAClB,KAAO,EAAA,CAAC,KAAc,EAAA,KAAA,EAAO,MAAW,KAAA;AACpC,MAAA,gCAAA,CAAiC,OAAS,EAAA,KAAA,CAAM,MAAQ,EAAA,KAAA,CAAM,MAAM,CAAA;AACpE,MAAM,KAAA,CAAA,OAAA,CAAQ,CAAC,IAAA,EAAM,KAAU,KAAA;AAC3B,QAAA,MAAA,GAAS,KAAK,KAAM,CAAA,KAAA,CAAM,KAAK,CAAA,EAAG,OAAO,MAAM,CAAA;AAAA,OAClD,CAAA;AACD,MAAO,OAAA,MAAA;AAAA;AACX,GACH,CAAA;AACL;AAkCO,SAAS,gBACZ,KACwC,EAAA;AAExC,EAAA,MAAM,SAAY,GAAA,aAAA,CAAc,KAAM,CAAA,GAAA,CAAI,YAAY,CAAC,CAAA;AACvD,EAAA,MAAM,UAAU,aAAc,CAAA,KAAA,CAAM,GAAI,CAAA,UAAU,CAAC,CAAK,IAAA,MAAA;AAExD,EAAA,OAAOH,aAAc,CAAA;AAAA,IACjB,GAAI,SAAc,KAAA,IAAA,GAAO,EAAE,OAAQ,EAAA,GAAI,EAAE,SAAU,EAAA;AAAA,IACnD,IAAA,EAAM,CAAC,KAAA,EAAwC,MAAW,KAAA;AACtD,MAAA,MAAM,SAAS,EAAC;AAChB,MAAA,KAAA,CAAM,QAAQ,CAAQ,IAAA,KAAA;AAClB,QAAA,MAAM,CAAC,QAAU,EAAA,SAAS,IAAI,IAAK,CAAA,IAAA,CAAK,OAAO,MAAM,CAAA;AACrD,QAAA,MAAA,CAAO,KAAK,QAAQ,CAAA;AACpB,QAAS,MAAA,GAAA,SAAA;AAAA,OACZ,CAAA;AACD,MAAO,OAAA,CAAC,QAAQ,MAAM,CAAA;AAAA;AAC1B,GACH,CAAA;AACL;AAoDO,SAAS,cACZ,KACyG,EAAA;AACzG,EAAOC,OAAAA,YAAAA;AAAA,IACH,gBAAgB,KAAK,CAAA;AAAA,IACrB,gBAAgB,KAAK;AAAA,GACzB;AACJ;ACvIO,SAAS,eAAA,CACZ,UACA,iBAC8C,EAAA;AAE9C,EAAM,MAAA,SAAA,GAAY,kBAAkB,QAAQ,CAAA;AAC5C,EAAA,MAAM,KAAiC,GAAA,CAAC,OAAS,EAAA,KAAA,EAAO,MAAW,KAAA;AAC/D,IAAM,MAAA,KAAA,GAAQ,kBAAkB,OAAO,CAAA;AACvC,IAAA,uBAAA,CAAwB,UAAU,KAAK,CAAA;AACvC,IAAA,OAAO,SAAS,KAAK,CAAA,CAAE,KAAM,CAAA,OAAA,EAAS,OAAO,MAAM,CAAA;AAAA,GACvD;AAEA,EAAA,IAAI,cAAc,IAAM,EAAA;AACpB,IAAA,OAAOF,aAAc,CAAA,EAAE,SAAW,EAAA,KAAA,EAAO,CAAA;AAAA;AAG7C,EAAM,MAAA,OAAA,GAAU,gBAAgB,QAAQ,CAAA;AACxC,EAAA,OAAOA,aAAc,CAAA;AAAA,IACjB,GAAI,OAAY,KAAA,IAAA,GAAO,EAAE,OAAA,KAAY,EAAC;AAAA,IACtC,kBAAkB,CAAW,OAAA,KAAA;AACzB,MAAM,MAAA,KAAA,GAAQ,kBAAkB,OAAO,CAAA;AACvC,MAAA,uBAAA,CAAwB,UAAU,KAAK,CAAA;AACvC,MAAA,OAAOI,cAAe,CAAA,OAAA,EAAS,QAAS,CAAA,KAAK,CAAC,CAAA;AAAA,KAClD;AAAA,IACA;AAAA,GACH,CAAA;AACL;AAkCO,SAAS,eAAA,CACZ,UACA,iBAC8C,EAAA;AAE9C,EAAM,MAAA,SAAA,GAAY,kBAAkB,QAAQ,CAAA;AAC5C,EAAM,MAAA,IAAA,GAA6B,CAAC,KAAA,EAAO,MAAW,KAAA;AAClD,IAAM,MAAA,KAAA,GAAQ,iBAAkB,CAAA,KAAA,EAAO,MAAM,CAAA;AAC7C,IAAA,uBAAA,CAAwB,UAAU,KAAK,CAAA;AACvC,IAAA,OAAO,QAAS,CAAA,KAAK,CAAE,CAAA,IAAA,CAAK,OAAO,MAAM,CAAA;AAAA,GAC7C;AAEA,EAAA,IAAI,cAAc,IAAM,EAAA;AACpB,IAAA,OAAOH,aAAc,CAAA,EAAE,SAAW,EAAA,IAAA,EAAM,CAAA;AAAA;AAG5C,EAAM,MAAA,OAAA,GAAU,gBAAgB,QAAQ,CAAA;AACxC,EAAOA,OAAAA,aAAAA,CAAc,EAAE,GAAI,OAAY,KAAA,IAAA,GAAO,EAAE,OAAA,EAAY,GAAA,EAAK,EAAA,IAAA,EAAM,CAAA;AAC3E;AAiDO,SAAS,aAAA,CACZ,QACA,EAAA,iBAAA,EACA,iBAIF,EAAA;AACE,EAAOC,OAAAA,YAAAA;AAAA,IACH,eAAA,CAAgB,UAAU,iBAAiB,CAAA;AAAA,IAC3C,eAAA,CAAgB,UAAU,iBAAiB;AAAA,GAG/C;AACJ;AAEA,SAAS,uBAAA,CAAwB,UAA8B,KAAe,EAAA;AAC1E,EAAA,IAAI,OAAO,QAAA,CAAS,KAAK,CAAA,KAAM,WAAa,EAAA;AACxC,IAAM,MAAA,IAAIC,YAAY,gDAAkD,EAAA;AAAA,MACpE,QAAA,EAAU,SAAS,MAAS,GAAA,CAAA;AAAA,MAC5B,QAAU,EAAA,CAAA;AAAA,MACV,OAAS,EAAA;AAAA,KACZ,CAAA;AAAA;AAET;AAEA,SAAS,kBAAoF,QAAqB,EAAA;AAC9G,EAAI,IAAA,QAAA,CAAS,MAAW,KAAA,CAAA,EAAU,OAAA,CAAA;AAClC,EAAA,IAAI,CAACE,WAAY,CAAA,QAAA,CAAS,CAAC,CAAC,GAAU,OAAA,IAAA;AACtC,EAAM,MAAA,WAAA,GAAc,QAAS,CAAA,CAAC,CAAE,CAAA,SAAA;AAChC,EAAM,MAAA,iBAAA,GAAoB,SAAS,KAAM,CAAA,CAAA,OAAA,KAAWA,YAAY,OAAO,CAAA,IAAK,OAAQ,CAAA,SAAA,KAAc,WAAW,CAAA;AAC7G,EAAA,OAAO,oBAAoB,WAAc,GAAA,IAAA;AAC7C;AAEA,SAAS,gBAAkF,QAAqB,EAAA;AAC5G,EAAA,OAAO,cAAc,QAAS,CAAA,GAAA,CAAI,aAAW,UAAW,CAAA,OAAO,CAAC,CAAC,CAAA;AACrE;;;AClEO,SAAS,4BAIZ,CAAA,QAAA,EACA,MAA+E,GAAA,EACT,EAAA;AAEtE,EAAM,MAAA,qBAAA,GAAyB,OAAO,aAAiB,IAAA,QAAA;AACvD,EAAM,MAAA,MAAA,GAAS,MAAO,CAAA,IAAA,IAAQC,YAAa,EAAA;AAC3C,EAAO,OAAA,eAAA;AAAA,IACH,QAAS,CAAA,GAAA;AAAA,MAAI,CAAC,GAAG,OAAO,CAAG,EAAA,KAAA,KACvBC,iBAAiB,eAAgB,CAAA,CAAC,MAAQ,EAAA,OAAO,CAAC,CAAG,EAAA,CAAC,UAAkC,CAAC,KAAA,EAAO,KAAK,CAAC;AAAA,KAC1G;AAAA,IACA,CAAS,KAAA,KAAA,uBAAA,CAAwB,QAAU,EAAA,KAAA,CAAM,qBAAqB,CAAC;AAAA,GAC3E;AACJ;AAwCO,SAAS,4BAIZ,CAAA,QAAA,EACA,MAA+E,GAAA,EACT,EAAA;AACtE,EAAM,MAAA,qBAAA,GAAwB,OAAO,aAAiB,IAAA,QAAA;AACtD,EAAM,MAAA,MAAA,GAAS,MAAO,CAAA,IAAA,IAAQC,YAAa,EAAA;AAC3C,EAAO,OAAA,eAAA;AAAA,IACH,QAAS,CAAA,GAAA;AAAA,MAAI,CAAC,CAAC,aAAA,EAAe,OAAO,CAAA,KACjCC,iBAAiB,eAAgB,CAAA,CAAC,MAAQ,EAAA,OAAO,CAAC,CAAG,EAAA,CAAC,GAAG,KAAK,CAAO,MAAA;AAAA,QACjE,CAAC,qBAAqB,GAAG,aAAA;AAAA,QACzB,GAAG;AAAA,OACL,CAAA;AAAA,KACN;AAAA,IACA,CAAC,KAAO,EAAA,MAAA,KAAW,MAAO,CAAA,MAAA,CAAO,KAAK,KAAO,EAAA,MAAM,CAAE,CAAA,CAAC,CAAC;AAAA,GAC3D;AACJ;AA0EO,SAAS,0BAIZ,CAAA,QAAA,EACA,MAA6E,GAAA,EAK/E,EAAA;AACE,EAAOP,OAAAA,YAAAA;AAAA,IACH,4BAAA,CAA6B,UAAU,MAAM,CAAA;AAAA,IAC7C,4BAAA,CAA6B,UAAU,MAAM;AAAA,GAIjD;AACJ;AAEA,SAAS,uBAAA,CACL,UACA,kBACF,EAAA;AACE,EAAM,MAAA,aAAA,GAAgB,SAAS,SAAU,CAAA,CAAC,CAAC,GAAG,CAAA,KAAM,uBAAuB,GAAG,CAAA;AAC9E,EAAA,IAAI,gBAAgB,CAAG,EAAA;AACnB,IAAM,MAAA,IAAIC,YAAY,yDAA2D,EAAA;AAAA,MAC7E,KAAO,EAAA,kBAAA;AAAA,MACP,UAAU,QAAS,CAAA,GAAA,CAAI,CAAC,CAAC,GAAG,MAAM,GAAG;AAAA,KACxC,CAAA;AAAA;AAEL,EAAO,OAAA,aAAA;AACX;AAGO,IAAM,kBAAqB,GAAA;AAG3B,IAAM,kBAAqB,GAAA;AAG3B,IAAM,gBAAmB,GAAA;;;ACzUzB,SAAS,aAAa,WAA+B,EAAA;AACxD,EAAA,MAAM,kBAAkB,CAAC,GAAG,IAAI,GAAA,CAAI,OAAO,MAAO,CAAA,WAAW,CAAE,CAAA,MAAA,CAAO,OAAK,OAAO,CAAA,KAAM,QAAQ,CAAC,CAAC,EAAE,IAAK,EAAA;AACzG,EAAM,MAAA,UAAA,GAAa,MAAO,CAAA,WAAA,CAAY,MAAO,CAAA,OAAA,CAAQ,WAAW,CAAE,CAAA,KAAA,CAAM,eAAgB,CAAA,MAAM,CAAC,CAAA;AAI/F,EAAM,MAAA,QAAA,GAAW,MAAO,CAAA,IAAA,CAAK,UAAU,CAAA;AACvC,EAAM,MAAA,UAAA,GAAa,MAAO,CAAA,MAAA,CAAO,UAAU,CAAA;AAC3C,EAAA,MAAM,YAAyB,GAAA;AAAA,IAC3B,mBAAG,IAAI,GAAI,CAAA,CAAC,GAAG,QAAU,EAAA,GAAG,UAAW,CAAA,MAAA,CAAO,CAAC,CAAmB,KAAA,OAAO,CAAM,KAAA,QAAQ,CAAC,CAAC;AAAA,GAC7F;AAEA,EAAA,OAAO,EAAE,QAAA,EAAU,UAAY,EAAA,UAAA,EAAY,iBAAiB,YAAa,EAAA;AAC7E;AAEO,SAAS,uBAAwB,CAAA;AAAA,EACpC,QAAA;AAAA,EACA,UAAA;AAAA,EACA;AACJ,CAIW,EAAA;AACP,EAAA,MAAM,UAAa,GAAA,aAAA,CAAc,UAAY,EAAA,CAAA,KAAA,KAAS,UAAU,OAAO,CAAA;AACvE,EAAI,IAAA,UAAA,IAAc,GAAU,OAAA,UAAA;AAC5B,EAAA,OAAO,QAAS,CAAA,SAAA,CAAU,CAAO,GAAA,KAAA,GAAA,KAAQ,OAAO,CAAA;AACpD;AAEO,SAAS,6BAA8B,CAAA;AAAA,EAC1C,aAAA;AAAA,EACA,QAAA;AAAA,EACA,UAAA;AAAA,EACA;AACJ,CAKW,EAAA;AACP,EAAA,IAAI,CAAC,yBAA2B,EAAA;AAC5B,IAAA,OAAO,aAAiB,IAAA,CAAA,IAAK,aAAgB,GAAA,QAAA,CAAS,SAAS,aAAgB,GAAA,EAAA;AAAA;AAEnF,EAAA,OAAO,aAAc,CAAA,UAAA,EAAY,CAAS,KAAA,KAAA,KAAA,KAAU,aAAa,CAAA;AACrE;AAEA,SAAS,aAAA,CAAiB,OAAiB,SAAmE,EAAA;AAC1G,EAAA,IAAI,IAAI,KAAM,CAAA,MAAA;AACd,EAAA,OAAO,CAAK,EAAA,EAAA;AACR,IAAA,IAAI,UAAU,KAAM,CAAA,CAAC,GAAG,CAAG,EAAA,KAAK,GAAU,OAAA,CAAA;AAAA;AAE9C,EAAO,OAAA,EAAA;AACX;AAEO,SAAS,sBAAsB,MAA0B,EAAA;AAC5D,EAAI,IAAA,MAAA,CAAO,MAAW,KAAA,CAAA,EAAU,OAAA,EAAA;AAChC,EAAA,IAAI,QAA0B,CAAC,MAAA,CAAO,CAAC,CAAG,EAAA,MAAA,CAAO,CAAC,CAAC,CAAA;AACnD,EAAA,MAAM,SAAmB,EAAC;AAC1B,EAAA,KAAA,IAAS,KAAQ,GAAA,CAAA,EAAG,KAAQ,GAAA,MAAA,CAAO,QAAQ,KAAS,EAAA,EAAA;AAChD,IAAM,MAAA,KAAA,GAAQ,OAAO,KAAK,CAAA;AAC1B,IAAA,IAAI,KAAM,CAAA,CAAC,CAAI,GAAA,CAAA,KAAM,KAAO,EAAA;AACxB,MAAA,KAAA,CAAM,CAAC,CAAI,GAAA,KAAA;AAAA,KACR,MAAA;AACH,MAAO,MAAA,CAAA,IAAA,CAAK,MAAM,CAAC,CAAA,KAAM,MAAM,CAAC,CAAA,GAAI,GAAG,KAAM,CAAA,CAAC,CAAC,CAAK,CAAA,GAAA,CAAA,EAAG,MAAM,CAAC,CAAC,IAAI,KAAM,CAAA,CAAC,CAAC,CAAE,CAAA,CAAA;AAC7E,MAAQ,KAAA,GAAA,CAAC,OAAO,KAAK,CAAA;AAAA;AACzB;AAEJ,EAAO,MAAA,CAAA,IAAA,CAAK,MAAM,CAAC,CAAA,KAAM,MAAM,CAAC,CAAA,GAAI,GAAG,KAAM,CAAA,CAAC,CAAC,CAAK,CAAA,GAAA,CAAA,EAAG,MAAM,CAAC,CAAC,IAAI,KAAM,CAAA,CAAC,CAAC,CAAE,CAAA,CAAA;AAC7E,EAAO,OAAA,MAAA,CAAO,KAAK,IAAI,CAAA;AAC3B;;;ACOO,SAAS,cACZ,CAAA,WAAA,EACA,MAAyC,GAAA,EACd,EAAA;AAC3B,EAAM,MAAA,MAAA,GAAS,MAAO,CAAA,IAAA,IAAQG,YAAa,EAAA;AAC3C,EAAM,MAAA,yBAAA,GAA4B,OAAO,yBAA6B,IAAA,KAAA;AACtE,EAAA,MAAM,EAAE,QAAU,EAAA,UAAA,EAAY,iBAAiB,YAAa,EAAA,GAAI,aAAa,WAAW,CAAA;AACxF,EAAA,IAAI,6BAA6B,UAAW,CAAA,IAAA,CAAK,WAAS,OAAO,KAAA,KAAU,QAAQ,CAAG,EAAA;AAClF,IAAM,MAAA,IAAIH,YAAY,sEAAwE,EAAA;AAAA,MAC1F,cAAc,UAAW,CAAA,MAAA,CAAO,CAAC,CAAmB,KAAA,OAAO,MAAM,QAAQ;AAAA,KAC5E,CAAA;AAAA;AAEL,EAAOI,OAAAA,gBAAAA,CAAiB,MAAQ,EAAA,CAAC,OAAwC,KAAA;AACrE,IAAA,MAAM,QAAQ,uBAAwB,CAAA,EAAE,QAAU,EAAA,UAAA,EAAY,SAAS,CAAA;AACvE,IAAA,IAAI,QAAQ,CAAG,EAAA;AACX,MAAM,MAAA,IAAIJ,YAAY,0CAA4C,EAAA;AAAA,QAC9D,wBAAA,EAA0B,sBAAsB,eAAe,CAAA;AAAA,QAC/D,eAAA;AAAA,QACA,YAAA;AAAA,QACA;AAAA,OACH,CAAA;AAAA;AAEL,IAAO,OAAA,yBAAA,GAA6B,UAAW,CAAA,KAAK,CAAe,GAAA,KAAA;AAAA,GACtE,CAAA;AACL;AA0CO,SAAS,cACZ,CAAA,WAAA,EACA,MAAyC,GAAA,EAChB,EAAA;AACzB,EAAM,MAAA,MAAA,GAAS,MAAO,CAAA,IAAA,IAAQK,YAAa,EAAA;AAC3C,EAAM,MAAA,yBAAA,GAA4B,OAAO,yBAA6B,IAAA,KAAA;AACtE,EAAA,MAAM,EAAE,QAAU,EAAA,UAAA,EAAY,eAAgB,EAAA,GAAI,aAAa,WAAW,CAAA;AAC1E,EAAA,IAAI,6BAA6B,UAAW,CAAA,IAAA,CAAK,WAAS,OAAO,KAAA,KAAU,QAAQ,CAAG,EAAA;AAClF,IAAM,MAAA,IAAIL,YAAY,sEAAwE,EAAA;AAAA,MAC1F,cAAc,UAAW,CAAA,MAAA,CAAO,CAAC,CAAmB,KAAA,OAAO,MAAM,QAAQ;AAAA,KAC5E,CAAA;AAAA;AAEL,EAAOM,OAAAA,gBAAAA,CAAiB,MAAQ,EAAA,CAAC,KAA6C,KAAA;AAC1E,IAAM,MAAA,aAAA,GAAgB,OAAO,KAAK,CAAA;AAClC,IAAA,MAAM,QAAQ,6BAA8B,CAAA;AAAA,MACxC,aAAA;AAAA,MACA,QAAA;AAAA,MACA,UAAA;AAAA,MACA;AAAA,KACH,CAAA;AACD,IAAA,IAAI,QAAQ,CAAG,EAAA;AACX,MAAM,MAAA,mBAAA,GAAsB,yBACtB,GAAA,eAAA,GACA,CAAC,GAAG,MAAM,QAAS,CAAA,MAAM,CAAE,CAAA,IAAA,EAAM,CAAA;AACvC,MAAM,MAAA,IAAIN,YAAY,qDAAuD,EAAA;AAAA,QACzE,aAAA;AAAA,QACA,4BAAA,EAA8B,sBAAsB,mBAAmB,CAAA;AAAA,QACvE;AAAA,OACH,CAAA;AAAA;AAEL,IAAA,OAAO,WAAW,KAAK,CAAA;AAAA,GAC1B,CAAA;AACL;AAiGO,SAAS,YACZ,CAAA,WAAA,EACA,MAAuC,GAAA,EACI,EAAA;AAC3C,EAAOD,OAAAA,YAAAA,CAAa,eAAe,WAAa,EAAA,MAAM,GAAG,cAAe,CAAA,WAAA,EAAa,MAAM,CAAC,CAAA;AAChG;AAGO,IAAM,oBAAuB,GAAA;AAG7B,IAAM,oBAAuB,GAAA;AAG7B,IAAM,kBAAqB,GAAA;ACrQ3B,SAAS,sBAAA,CACZ,SACA,gBACc,EAAA;AACd,EAAOK,OAAAA,gBAAAA;AAAA,IACH,eAAgB,CAAA,CAAC,GAAG,gBAAA,EAAkB,OAAO,CAAC,CAAA;AAAA,IAC9C,CAAC,UAAiB,CAAC,GAAG,iBAAiB,GAAI,CAAA,MAAM,MAAS,CAAA,EAAG,KAAK;AAAA,GACtE;AACJ;AAsCO,SAAS,sBAAA,CACZ,SACA,gBACY,EAAA;AACZ,EAAOE,OAAAA,gBAAAA;AAAA,IACH,eAAgB,CAAA,CAAC,GAAG,gBAAA,EAAkB,OAAO,CAAC,CAAA;AAAA,IAC9C,CAAS,KAAA,KAAA,KAAA,CAAM,KAAM,CAAA,MAAA,GAAS,CAAC;AAAA,GACnC;AACJ;AAgEO,SAAS,oBAAA,CACZ,OACA,cACiB,EAAA;AACjB,EAAOP,OAAAA,YAAAA,CAAa,uBAAuB,KAAO,EAAA,cAAc,GAAG,sBAAuB,CAAA,KAAA,EAAO,cAAc,CAAC,CAAA;AACpH;AC3HO,SAAS,sBAAA,CACZ,SACA,gBACc,EAAA;AACd,EAAOK,OAAAA,gBAAAA;AAAA,IACH,eAAgB,CAAA,CAAC,OAAS,EAAA,GAAG,gBAAgB,CAAC,CAAA;AAAA,IAC9C,CAAC,UAAiB,CAAC,KAAA,EAAO,GAAG,gBAAiB,CAAA,GAAA,CAAI,MAAM,MAAS,CAAC;AAAA,GACtE;AACJ;AAsCO,SAAS,sBAAA,CACZ,SACA,gBACY,EAAA;AACZ,EAAOE,OAAAA,gBAAAA;AAAA,IACH,eAAgB,CAAA,CAAC,OAAS,EAAA,GAAG,gBAAgB,CAAC,CAAA;AAAA,IAC9C,CAAA,KAAA,KAAS,MAAM,CAAC;AAAA,GACpB;AACJ;AAgEO,SAAS,oBAAA,CACZ,OACA,cACiB,EAAA;AACjB,EAAOP,OAAAA,YAAAA,CAAa,uBAAuB,KAAO,EAAA,cAAc,GAAG,sBAAuB,CAAA,KAAA,EAAO,cAAc,CAAC,CAAA;AACpH;AC3FO,SAAS,sBACZ,CAAA,QAAA,EACA,MAAiD,GAAA,EACV,EAAA;AACvC,EAAM,MAAA,aAAA,GAAgB,MAAO,CAAA,IAAA,IAAQI,YAAa,EAAA;AAClD,EAAOC,OAAAA,gBAAAA,CAAiB,eAAe,CAAW,OAAA,KAAA;AAC9C,IAAM,MAAA,KAAA,GAAQ,QAAS,CAAA,OAAA,CAAQ,OAAO,CAAA;AACtC,IAAA,IAAI,QAAQ,CAAG,EAAA;AACX,MAAM,MAAA,IAAIJ,YAAY,mDAAqD,EAAA;AAAA,QACvE,KAAO,EAAA,OAAA;AAAA,QACP;AAAA,OACH,CAAA;AAAA;AAEL,IAAO,OAAA,KAAA;AAAA,GACV,CAAA;AACL;AAwCO,SAAS,sBACZ,CAAA,QAAA,EACA,MAAiD,GAAA,EACV,EAAA;AACvC,EAAM,MAAA,aAAA,GAAgB,MAAO,CAAA,IAAA,IAAQK,YAAa,EAAA;AAClD,EAAOC,OAAAA,gBAAAA,CAAiB,aAAe,EAAA,CAAC,KAA2B,KAAA;AAC/D,IAAA,IAAI,KAAQ,GAAA,CAAA,IAAK,KAAS,IAAA,QAAA,CAAS,MAAQ,EAAA;AACvC,MAAM,MAAA,IAAIN,YAAY,8DAAgE,EAAA;AAAA,QAClF,aAAe,EAAA,KAAA;AAAA,QACf,QAAA,EAAU,SAAS,MAAS,GAAA,CAAA;AAAA,QAC5B,QAAU,EAAA;AAAA,OACb,CAAA;AAAA;AAEL,IAAO,OAAA,QAAA,CAAS,MAAO,CAAA,KAAK,CAAC,CAAA;AAAA,GAChC,CAAA;AACL;AAqFO,SAAS,oBACZ,CAAA,QAAA,EACA,MAA+C,GAAA,EACV,EAAA;AACrC,EAAOD,OAAAA,YAAAA,CAAa,uBAAuB,QAAU,EAAA,MAAM,GAAG,sBAAuB,CAAA,QAAA,EAAU,MAAM,CAAC,CAAA;AAC1G;AClKO,SAAS,aACZ,CAAA,GAAA,EACA,KACA,EAAA,MAAA,GAAwC,EACN,EAAA;AAClC,EAAOK,OAAAA,gBAAAA;AAAA,IACH,gBAAgB,eAAgB,CAAA,CAAC,KAAK,KAAK,CAAC,GAAG,MAAgB,CAAA;AAAA,IAC/D,CAAC,GAA6D,KAAA,CAAC,GAAG,GAAA,CAAI,SAAS;AAAA,GACnF;AACJ;AA8CO,SAAS,aACZ,CAAA,GAAA,EACA,KACA,EAAA,MAAA,GAAwC,EACV,EAAA;AAC9B,EAAOE,OAAAA,gBAAAA;AAAA,IACH,gBAAgB,eAAgB,CAAA,CAAC,KAAK,KAAK,CAAC,GAAG,MAAgB,CAAA;AAAA,IAC/D,CAAC,OAAA,KAAyD,IAAI,GAAA,CAAI,OAAO;AAAA,GAC7E;AACJ;AA2HO,SAAS,WAMZ,CAAA,GAAA,EACA,KACA,EAAA,MAAA,GAAsC,EACiB,EAAA;AACvD,EAAOP,OAAAA,YAAAA,CAAa,aAAc,CAAA,GAAA,EAAK,KAAO,EAAA,MAAgB,GAAG,aAAc,CAAA,GAAA,EAAK,KAAO,EAAA,MAAgB,CAAC,CAAA;AAChH;AC/PO,SAAS,cAA4C,GAAA;AACxD,EAAA,OAAOF,aAAc,CAAA;AAAA,IACjB,SAAW,EAAA,CAAA;AAAA,IACX,KAAO,EAAA,CAAC,MAAQ,EAAA,MAAA,EAAQ,MAAW,KAAA;AAAA,GACtC,CAAA;AACL;AAqBO,SAAS,cAA4C,GAAA;AACxD,EAAA,OAAOC,aAAc,CAAA;AAAA,IACjB,SAAW,EAAA,CAAA;AAAA,IACX,MAAM,CAAC,MAAA,EAAyC,MAAW,KAAA,CAAC,QAAW,MAAM;AAAA,GAChF,CAAA;AACL;AAgDO,SAAS,YAA8C,GAAA;AAC1D,EAAA,OAAOC,YAAa,CAAA,cAAA,EAAkB,EAAA,cAAA,EAAgB,CAAA;AAC1D;;;ACQO,SAAS,kBACZ,CAAA,IAAA,EACA,MAA6C,GAAA,EACxB,EAAA;AACrB,EAAA,MAAM,UAAU,MAAM;AAClB,IAAI,IAAA,MAAA,CAAO,WAAW,IAAM,EAAA;AACxB,MAAA,OAAOK,gBAAiB,CAAA,cAAA,EAAkB,EAAA,CAAC,aAAsB,MAAS,CAAA;AAAA;AAE9E,IAAA,OAAO,kBAAkB,EAAE,IAAA,EAAM,OAAO,MAAUD,IAAAA,YAAAA,IAAgB,CAAA;AAAA,GACnE,GAAA;AACH,EAAA,MAAM,aAAa,MAAM;AACrB,IAAI,IAAA,MAAA,CAAO,cAAc,QAAU,EAAA;AAC/B,MAAA,iBAAA,CAAkB,IAAI,CAAA;AACtB,MAAA,OAAO,cAAe,CAAA,cAAA,EAAkB,EAAA,IAAA,CAAK,SAAS,CAAA;AAAA;AAE1D,IAAI,IAAA,CAAC,OAAO,SAAW,EAAA;AACnB,MAAA,OAAO,cAAe,EAAA;AAAA;AAE1B,IAAO,OAAA,kBAAA,CAAmB,OAAO,SAAS,CAAA;AAAA,GAC3C,GAAA;AAEH,EAAO,OAAA,eAAA;AAAA,IACH;AAAA,MACIC,gBAAAA,CAAiB,gBAAgB,CAAC,MAAA,EAAQ,SAAS,CAAC,CAAA,EAAG,CAAC,MAAkC,KAAA;AAAA,QACtF,KAAA;AAAA,QACA;AAAA,OACH,CAAA;AAAA,MACDA,gBAAiB,CAAA,eAAA,CAAgB,CAAC,MAAA,EAAQ,IAAI,CAAC,CAAG,EAAA,CAAC,KAAmC,KAAA,CAAC,IAAM,EAAA,KAAK,CAAC;AAAA,KACvG;AAAA,IACA,CAAA,OAAA,KAAW,MAAO,CAAA,OAAA,KAAY,IAAI;AAAA,GACtC;AACJ;AA6CO,SAAS,kBACZ,CAAA,IAAA,EACA,MAA6C,GAAA,EAC1B,EAAA;AACnB,EAAA,MAAM,UAAU,MAAM;AAClB,IAAI,IAAA,MAAA,CAAO,WAAW,IAAM,EAAA;AACxB,MAAA,OAAOE,gBAAiB,CAAA,cAAA,EAAkB,EAAA,MAAM,KAAK,CAAA;AAAA;AAEzD,IAAA,OAAO,kBAAkB,EAAE,IAAA,EAAM,OAAO,MAAUD,IAAAA,YAAAA,IAAgB,CAAA;AAAA,GACnE,GAAA;AACH,EAAA,MAAM,aAAa,MAAM;AACrB,IAAI,IAAA,MAAA,CAAO,cAAc,QAAU,EAAA;AAC/B,MAAA,iBAAA,CAAkB,IAAI,CAAA;AACtB,MAAA,OAAO,cAAe,CAAA,cAAA,EAAkB,EAAA,IAAA,CAAK,SAAS,CAAA;AAAA;AAE1D,IAAI,IAAA,CAAC,OAAO,SAAW,EAAA;AACnB,MAAA,OAAO,cAAe,EAAA;AAAA;AAE1B,IAAO,OAAA,kBAAA,CAAmB,OAAO,SAAS,CAAA;AAAA,GAC3C,GAAA;AAEH,EAAO,OAAA,eAAA;AAAA,IACH;AAAA,MACIC,gBAAAA,CAAiB,gBAAgB,CAAC,MAAA,EAAQ,SAAS,CAAC,CAAA,EAAG,MAAM,IAAI,CAAA;AAAA,MACjEA,gBAAiB,CAAA,eAAA,CAAgB,CAAC,MAAA,EAAQ,IAAI,CAAC,CAAG,EAAA,CAAC,GAAG,KAAK,CAAA,KAAW,KAAK;AAAA,KAC/E;AAAA,IACA,CAAC,OAAO,MAAW,KAAA;AACf,MAAA,IAAI,MAAO,CAAA,MAAA,KAAW,IAAQ,IAAA,CAAC,OAAO,SAAW,EAAA;AAC7C,QAAO,OAAA,MAAA,CAAO,MAAS,GAAA,KAAA,CAAM,MAAM,CAAA;AAAA;AAEvC,MAAA,IAAI,MAAO,CAAA,MAAA,KAAW,IAAQ,IAAA,MAAA,CAAO,aAAa,IAAM,EAAA;AACpD,QAAA,MAAM,SACF,GAAA,MAAA,CAAO,SAAc,KAAA,QAAA,GAAW,IAAI,UAAA,CAAW,SAAU,CAAA,SAAS,CAAE,CAAA,IAAA,CAAK,CAAC,CAAA,GAAI,MAAO,CAAA,SAAA;AACzF,QAAA,OAAOC,aAAc,CAAA,KAAA,EAAO,SAAW,EAAA,MAAM,IAAI,CAAI,GAAA,CAAA;AAAA;AAEzD,MAAA,OAAO,OAAO,MAAO,CAAA,IAAA,CAAK,OAAO,MAAM,CAAA,CAAE,CAAC,CAAC,CAAA;AAAA;AAC/C,GACJ;AACJ;AAkHO,SAAS,gBACZ,CAAA,IAAA,EACA,MAA2C,GAAA,EACZ,EAAA;AAE/B,EAAOR,OAAAA,YAAAA;AAAA,IACH,kBAAA,CAA0B,MAAM,MAAoB,CAAA;AAAA,IACpD,kBAAA,CAAwB,MAAM,MAAoB;AAAA,GACtD;AACJ;ACvRO,SAAS,aACZ,CAAA,IAAA,EACA,MAAwC,GAAA,EACrB,EAAA;AACnB,EAAOK,OAAAA,gBAAAA,CAAiB,eAAgB,CAAA,IAAA,EAAM,MAAgB,CAAA,EAAG,CAAC,GAA6B,KAAA,CAAC,GAAG,GAAG,CAAC,CAAA;AAC3G;AAsCO,SAAS,aAAmB,CAAA,IAAA,EAAoB,MAAwC,GAAA,EAAuB,EAAA;AAClH,EAAOE,OAAAA,gBAAAA,CAAiB,eAAgB,CAAA,IAAA,EAAM,MAAgB,CAAA,EAAG,CAAC,OAA6B,KAAA,IAAI,GAAI,CAAA,OAAO,CAAC,CAAA;AACnH;AA+EO,SAAS,WACZ,CAAA,IAAA,EACA,MAAsC,GAAA,EACX,EAAA;AAC3B,EAAOP,OAAAA,YAAAA,CAAa,cAAc,IAAM,EAAA,MAAgB,GAAG,aAAc,CAAA,IAAA,EAAM,MAAgB,CAAC,CAAA;AACpG;ACnHO,SAAS,iBACZ,MAC0C,EAAA;AAE1C,EAAM,MAAA,WAAA,GAAc,OAAO,GAAI,CAAA,CAAC,GAAG,KAAK,MAAM,KAAK,CAAA;AACnD,EAAA,MAAM,SAAY,GAAA,aAAA,CAAc,WAAY,CAAA,GAAA,CAAI,YAAY,CAAC,CAAA;AAC7D,EAAA,MAAM,UAAU,aAAc,CAAA,WAAA,CAAY,GAAI,CAAA,UAAU,CAAC,CAAK,IAAA,MAAA;AAE9D,EAAA,OAAOF,aAAc,CAAA;AAAA,IACjB,GAAI,cAAc,IACZ,GAAA;AAAA,MACI,gBAAA,EAAkB,CAAC,KACf,KAAA,MAAA,CACK,IAAI,CAAC,CAAC,GAAK,EAAA,KAAK,CAAMI,KAAAA,cAAAA,CAAe,MAAM,GAAkB,CAAA,EAAG,KAAK,CAAC,CACtE,CAAA,MAAA,CAAO,CAAC,GAAK,EAAA,GAAA,KAAQ,GAAM,GAAA,GAAA,EAAK,CAAC,CAAA;AAAA,MAC1C;AAAA,KACJ,GACA,EAAE,SAAU,EAAA;AAAA,IAClB,KAAO,EAAA,CAAC,MAAe,EAAA,KAAA,EAAO,MAAW,KAAA;AACrC,MAAA,MAAA,CAAO,OAAQ,CAAA,CAAC,CAAC,GAAA,EAAK,KAAK,CAAM,KAAA;AAC7B,QAAA,MAAA,GAAS,MAAM,KAAM,CAAA,MAAA,CAAO,GAAkB,CAAA,EAAG,OAAO,MAAM,CAAA;AAAA,OACjE,CAAA;AACD,MAAO,OAAA,MAAA;AAAA;AACX,GACH,CAAA;AACL;AAqCO,SAAS,iBACZ,MAC0C,EAAA;AAE1C,EAAM,MAAA,WAAA,GAAc,OAAO,GAAI,CAAA,CAAC,GAAG,KAAK,MAAM,KAAK,CAAA;AACnD,EAAA,MAAM,SAAY,GAAA,aAAA,CAAc,WAAY,CAAA,GAAA,CAAI,YAAY,CAAC,CAAA;AAC7D,EAAA,MAAM,UAAU,aAAc,CAAA,WAAA,CAAY,GAAI,CAAA,UAAU,CAAC,CAAK,IAAA,MAAA;AAE9D,EAAA,OAAOH,aAAc,CAAA;AAAA,IACjB,GAAI,SAAc,KAAA,IAAA,GAAO,EAAE,OAAQ,EAAA,GAAI,EAAE,SAAU,EAAA;AAAA,IACnD,IAAA,EAAM,CAAC,KAAA,EAAwC,MAAW,KAAA;AACtD,MAAA,MAAM,SAAS,EAAC;AAChB,MAAA,MAAA,CAAO,OAAQ,CAAA,CAAC,CAAC,GAAA,EAAK,KAAK,CAAM,KAAA;AAC7B,QAAA,MAAM,CAAC,KAAO,EAAA,SAAS,IAAI,KAAM,CAAA,IAAA,CAAK,OAAO,MAAM,CAAA;AACnD,QAAS,MAAA,GAAA,SAAA;AACT,QAAA,MAAA,CAAO,GAAgB,CAAI,GAAA,KAAA;AAAA,OAC9B,CAAA;AACD,MAAO,OAAA,CAAC,QAAQ,MAAM,CAAA;AAAA;AAC1B,GACH,CAAA;AACL;AA2DO,SAAS,eACZ,MAC+G,EAAA;AAC/G,EAAOC,OAAAA,YAAAA;AAAA,IACH,iBAAiB,MAAM,CAAA;AAAA,IACvB,iBAAiB,MAAM;AAAA,GAC3B;AACJ", "file": "index.browser.mjs", "sourcesContent": ["import { SOLANA_ERROR__CODECS__INVALID_NUMBER_OF_ITEMS, SolanaError } from '@solana/errors';\n\n/** Checks the number of items in an array-like structure is expected. */\nexport function assertValidNumberOfItemsForCodec(\n    codecDescription: string,\n    expected: bigint | number,\n    actual: bigint | number,\n) {\n    if (expected !== actual) {\n        throw new SolanaError(SOLANA_ERROR__CODECS__INVALID_NUMBER_OF_ITEMS, {\n            actual,\n            codecDescription,\n            expected,\n        });\n    }\n}\n", "import { isFixedSize } from '@solana/codecs-core';\n\n/**\n * Functionally, this type helper is equivalent to the identity type — i.e. `type Identity<T> = T`.\n * However, wrapping generic object mappings in this type significantly reduces the number\n * of instantiation expressions processed, which increases TypeScript performance and\n * prevents \"Type instantiation is excessively deep and possibly infinite\" errors.\n *\n * This works because TypeScript doesn't create a new level of nesting when encountering conditional generic types.\n * @see https://github.com/microsoft/TypeScript/issues/34933\n * @see https://github.com/kysely-org/kysely/pull/483\n */\nexport type DrainOuterGeneric<T> = [T] extends [unknown] ? T : never;\n\nexport function maxCodecSizes(sizes: (number | null)[]): number | null {\n    return sizes.reduce(\n        (all, size) => (all === null || size === null ? null : Math.max(all, size)),\n        0 as number | null,\n    );\n}\n\nexport function sumCodecSizes(sizes: (number | null)[]): number | null {\n    return sizes.reduce((all, size) => (all === null || size === null ? null : all + size), 0 as number | null);\n}\n\nexport function getFixedSize(codec: { fixedSize: number } | { maxSize?: number }): number | null {\n    return isFixedSize(codec) ? codec.fixedSize : null;\n}\n\nexport function getMaxSize(codec: { fixedSize: number } | { maxSize?: number }): number | null {\n    return isFixedSize(codec) ? codec.fixedSize : (codec.maxSize ?? null);\n}\n", "import {\n    Codec,\n    combineCodec,\n    createDecoder,\n    create<PERSON>nco<PERSON>,\n    <PERSON>oder,\n    Encoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    getEncodedSize,\n    ReadonlyUint8Array,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\nimport { getU32Decoder, getU32Encoder, NumberCodec, NumberDecoder, NumberEncoder } from '@solana/codecs-numbers';\n\nimport { assertValidNumberOfItemsForCodec } from './assertions';\nimport { getFixedSize, getMaxSize } from './utils';\n\n/**\n * Defines the possible size strategies for array-like codecs (`array`, `map`, and `set`).\n *\n * The size of the collection can be determined using one of the following approaches:\n * - A {@link NumberCodec}, {@link NumberDecoder}, or {@link NumberEncoder} to store a size prefix.\n * - A fixed `number` of items, enforcing an exact length.\n * - The string `\"remainder\"`, which infers the number of items by consuming the rest of the available bytes.\n *   This option is only available when encoding fixed-size items.\n *\n * @typeParam TPrefix - A number codec, decoder, or encoder used for size prefixing.\n */\nexport type ArrayLikeCodecSize<TPrefix extends NumberCodec | NumberDecoder | NumberEncoder> =\n    | TPrefix\n    | number\n    | 'remainder';\n\n/**\n * Defines the configuration options for array codecs.\n *\n * @typeParam TPrefix - A number codec, decoder, or encoder used for size prefixing.\n */\nexport type ArrayCodecConfig<TPrefix extends NumberCodec | NumberDecoder | NumberEncoder> = {\n    /**\n     * Specifies how the size of the array is determined.\n     *\n     * - A {@link NumberCodec}, {@link NumberDecoder}, or {@link NumberEncoder} stores a size prefix before encoding the array.\n     * - A `number` enforces a fixed number of elements.\n     * - `\"remainder\"` uses all remaining bytes to infer the array length (only for fixed-size items).\n     *\n     * @defaultValue A `u32` size prefix.\n     */\n    size?: ArrayLikeCodecSize<TPrefix>;\n};\n\n/**\n * Returns an encoder for arrays of values.\n *\n * This encoder serializes arrays by encoding each element using the provided item encoder.\n * By default, a `u32` size prefix is included to indicate the number of items in the array.\n * The `size` option can be used to modify this behaviour.\n *\n * For more details, see {@link getArrayCodec}.\n *\n * @typeParam TFrom - The type of the elements in the array.\n *\n * @param item - The encoder for each item in the array.\n * @param config - Optional configuration for the size encoding strategy.\n * @returns A `VariableSizeEncoder<TFrom[]>` for encoding arrays.\n *\n * @example\n * Encoding an array of `u8` numbers.\n * ```ts\n * const encoder = getArrayEncoder(getU8Encoder());\n * const bytes = encoder.encode([1, 2, 3]);\n * // 0x03000000010203\n * //   |       └-- 3 items of 1 byte each.\n * //   └-- 4-byte prefix telling us to read 3 items.\n * ```\n *\n * @see {@link getArrayCodec}\n */\nexport function getArrayEncoder<TFrom>(\n    item: Encoder<TFrom>,\n    config: ArrayCodecConfig<NumberEncoder> & { size: 0 },\n): FixedSizeEncoder<TFrom[], 0>;\nexport function getArrayEncoder<TFrom>(\n    item: FixedSizeEncoder<TFrom>,\n    config: ArrayCodecConfig<NumberEncoder> & { size: number },\n): FixedSizeEncoder<TFrom[]>;\nexport function getArrayEncoder<TFrom>(\n    item: Encoder<TFrom>,\n    config?: ArrayCodecConfig<NumberEncoder>,\n): VariableSizeEncoder<TFrom[]>;\nexport function getArrayEncoder<TFrom>(\n    item: Encoder<TFrom>,\n    config: ArrayCodecConfig<NumberEncoder> = {},\n): Encoder<TFrom[]> {\n    const size = config.size ?? getU32Encoder();\n    const fixedSize = computeArrayLikeCodecSize(size, getFixedSize(item));\n    const maxSize = computeArrayLikeCodecSize(size, getMaxSize(item)) ?? undefined;\n\n    return createEncoder({\n        ...(fixedSize !== null\n            ? { fixedSize }\n            : {\n                  getSizeFromValue: (array: TFrom[]) => {\n                      const prefixSize = typeof size === 'object' ? getEncodedSize(array.length, size) : 0;\n                      return prefixSize + [...array].reduce((all, value) => all + getEncodedSize(value, item), 0);\n                  },\n                  maxSize,\n              }),\n        write: (array: TFrom[], bytes, offset) => {\n            if (typeof size === 'number') {\n                assertValidNumberOfItemsForCodec('array', size, array.length);\n            }\n            if (typeof size === 'object') {\n                offset = size.write(array.length, bytes, offset);\n            }\n            array.forEach(value => {\n                offset = item.write(value, bytes, offset);\n            });\n            return offset;\n        },\n    });\n}\n\n/**\n * Returns a decoder for arrays of values.\n *\n * This decoder deserializes arrays by decoding each element using the provided item decoder.\n * By default, a `u32` size prefix is expected to indicate the number of items in the array.\n * The `size` option can be used to modify this behaviour.\n *\n * For more details, see {@link getArrayCodec}.\n *\n * @typeParam TTo - The type of the decoded elements in the array.\n *\n * @param item - The decoder for each item in the array.\n * @param config - Optional configuration for the size decoding strategy.\n * @returns A `VariableSizeDecoder<TTo[]>` for decoding arrays.\n *\n * @example\n * Decoding an array of `u8` numbers.\n * ```ts\n * const decoder = getArrayDecoder(getU8Decoder());\n * const array = decoder.decode(new Uint8Array([0x03, 0x00, 0x00, 0x00, 0x01, 0x02, 0x03]));\n * // [1, 2, 3]\n * // 0x03000000010203\n * //   |       └-- 3 items of 1 byte each.\n * //   └-- 4-byte prefix telling us to read 3 items.\n * ```\n *\n * @see {@link getArrayCodec}\n */\nexport function getArrayDecoder<TTo>(\n    item: Decoder<TTo>,\n    config: ArrayCodecConfig<NumberDecoder> & { size: 0 },\n): FixedSizeDecoder<TTo[], 0>;\nexport function getArrayDecoder<TTo>(\n    item: FixedSizeDecoder<TTo>,\n    config: ArrayCodecConfig<NumberDecoder> & { size: number },\n): FixedSizeDecoder<TTo[]>;\nexport function getArrayDecoder<TTo>(\n    item: Decoder<TTo>,\n    config?: ArrayCodecConfig<NumberDecoder>,\n): VariableSizeDecoder<TTo[]>;\nexport function getArrayDecoder<TTo>(item: Decoder<TTo>, config: ArrayCodecConfig<NumberDecoder> = {}): Decoder<TTo[]> {\n    const size = config.size ?? getU32Decoder();\n    const itemSize = getFixedSize(item);\n    const fixedSize = computeArrayLikeCodecSize(size, itemSize);\n    const maxSize = computeArrayLikeCodecSize(size, getMaxSize(item)) ?? undefined;\n\n    return createDecoder({\n        ...(fixedSize !== null ? { fixedSize } : { maxSize }),\n        read: (bytes: ReadonlyUint8Array | Uint8Array, offset) => {\n            const array: TTo[] = [];\n            if (typeof size === 'object' && bytes.slice(offset).length === 0) {\n                return [array, offset];\n            }\n\n            if (size === 'remainder') {\n                while (offset < bytes.length) {\n                    const [value, newOffset] = item.read(bytes, offset);\n                    offset = newOffset;\n                    array.push(value);\n                }\n                return [array, offset];\n            }\n\n            const [resolvedSize, newOffset] = typeof size === 'number' ? [size, offset] : size.read(bytes, offset);\n            offset = newOffset;\n            for (let i = 0; i < resolvedSize; i += 1) {\n                const [value, newOffset] = item.read(bytes, offset);\n                offset = newOffset;\n                array.push(value);\n            }\n            return [array, offset];\n        },\n    });\n}\n\n/**\n * Returns a codec for encoding and decoding arrays of values.\n *\n * This codec serializes arrays by encoding each element using the provided item codec.\n * By default, a `u32` size prefix is included to indicate the number of items in the array.\n * The `size` option can be used to modify this behaviour.\n *\n * @typeParam TFrom - The type of the elements to encode.\n * @typeParam TTo - The type of the decoded elements.\n *\n * @param item - The codec for each item in the array.\n * @param config - Optional configuration for the size encoding/decoding strategy.\n * @returns A `VariableSizeCodec<TFrom[], TTo[]>` for encoding and decoding arrays.\n *\n * @example\n * Encoding and decoding an array of `u8` numbers.\n * ```ts\n * const codec = getArrayCodec(getU8Codec());\n * const bytes = codec.encode([1, 2, 3]);\n * // 0x03000000010203\n * //   |       └-- 3 items of 1 byte each.\n * //   └-- 4-byte prefix telling us to read 3 items.\n *\n * const array = codec.decode(bytes);\n * // [1, 2, 3]\n * ```\n *\n * @example\n * Using a `u16` size prefix instead of `u32`.\n * ```ts\n * const codec = getArrayCodec(getU8Codec(), { size: getU16Codec() });\n * const bytes = codec.encode([1, 2, 3]);\n * // 0x0300010203\n * //   |   └-- 3 items of 1 byte each.\n * //   └-- 2-byte prefix telling us to read 3 items.\n * ```\n *\n * @example\n * Using a fixed-size array of 3 items.\n * ```ts\n * const codec = getArrayCodec(getU8Codec(), { size: 3 });\n * codec.encode([1, 2, 3]);\n * // 0x010203\n * //   └-- 3 items of 1 byte each. There must always be 3 items in the array.\n * ```\n *\n * @example\n * Using the `\"remainder\"` size strategy.\n * ```ts\n * const codec = getArrayCodec(getU8Codec(), { size: 'remainder' });\n * codec.encode([1, 2, 3]);\n * // 0x010203\n * //   └-- 3 items of 1 byte each. The size is inferred from the remainder of the bytes.\n * ```\n *\n * @remarks\n * The size of the array can be controlled using the `size` option:\n * - A `Codec<number>` (e.g. `getU16Codec()`) stores a size prefix before the array.\n * - A `number` enforces a fixed number of elements.\n * - `\"remainder\"` uses all remaining bytes to infer the array length.\n *\n * Separate {@link getArrayEncoder} and {@link getArrayDecoder} functions are available.\n *\n * ```ts\n * const bytes = getArrayEncoder(getU8Encoder()).encode([1, 2, 3]);\n * const array = getArrayDecoder(getU8Decoder()).decode(bytes);\n * ```\n *\n * @see {@link getArrayEncoder}\n * @see {@link getArrayDecoder}\n */\nexport function getArrayCodec<TFrom, TTo extends TFrom = TFrom>(\n    item: Codec<TFrom, TTo>,\n    config: ArrayCodecConfig<NumberCodec> & { size: 0 },\n): FixedSizeCodec<TFrom[], TTo[], 0>;\nexport function getArrayCodec<TFrom, TTo extends TFrom = TFrom>(\n    item: FixedSizeCodec<TFrom, TTo>,\n    config: ArrayCodecConfig<NumberCodec> & { size: number },\n): FixedSizeCodec<TFrom[], TTo[]>;\nexport function getArrayCodec<TFrom, TTo extends TFrom = TFrom>(\n    item: Codec<TFrom, TTo>,\n    config?: ArrayCodecConfig<NumberCodec>,\n): VariableSizeCodec<TFrom[], TTo[]>;\nexport function getArrayCodec<TFrom, TTo extends TFrom = TFrom>(\n    item: Codec<TFrom, TTo>,\n    config: ArrayCodecConfig<NumberCodec> = {},\n): Codec<TFrom[], TTo[]> {\n    return combineCodec(getArrayEncoder(item, config as object), getArrayDecoder(item, config as object));\n}\n\nfunction computeArrayLikeCodecSize(size: number | object | 'remainder', itemSize: number | null): number | null {\n    if (typeof size !== 'number') return null;\n    if (size === 0) return 0;\n    return itemSize === null ? null : itemSize * size;\n}\n", "import {\n    assertByteArrayHasEnoughBytesForCodec,\n    combineCodec,\n    createDecoder,\n    createEncoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n} from '@solana/codecs-core';\n\n/**\n * Defines the configuration options for bit array codecs.\n *\n * A bit array codec encodes an array of booleans into bits, packing them into bytes.\n * This configuration allows adjusting the bit ordering.\n *\n * @see {@link getBitArrayEncoder}\n * @see {@link getBitArrayDecoder}\n * @see {@link getBitArrayCodec}\n */\nexport type BitArrayCodecConfig = {\n    /**\n     * Determines whether the bits should be read in reverse order.\n     *\n     * - `false` (default): The first boolean is stored in the most significant bit (MSB-first).\n     * - `true`: The first boolean is stored in the least significant bit (LSB-first).\n     *\n     * @defaultValue `false`\n     */\n    backward?: boolean;\n};\n\n/**\n * Returns an encoder that packs an array of booleans into bits.\n *\n * This encoder converts a list of `boolean` values into a compact bit representation,\n * storing 8 booleans per byte.\n *\n * The `backward` config option determines whether the bits are stored in MSB-first (`false`)\n * or LSB-first (`true`).\n *\n * For more details, see {@link getBitArrayCodec}.\n *\n * @typeParam TSize - The number of bytes used to store the bit array.\n *\n * @param size - The number of bytes allocated for the bit array (must be sufficient for the expected boolean count).\n * @param config - Configuration options for encoding the bit array.\n * @returns A `FixedSizeEncoder<boolean[], TSize>` for encoding bit arrays.\n *\n * @example\n * Encoding a bit array.\n * ```ts\n * const encoder = getBitArrayEncoder(1);\n *\n * encoder.encode([true, false, true, false, false, false, false, false]);\n * // 0xa0 (0b10100000)\n * ```\n *\n * @see {@link getBitArrayCodec}\n */\nexport function getBitArrayEncoder<TSize extends number>(\n    size: TSize,\n    config: BitArrayCodecConfig | boolean = {},\n): FixedSizeEncoder<boolean[], TSize> {\n    const parsedConfig: BitArrayCodecConfig = typeof config === 'boolean' ? { backward: config } : config;\n    const backward = parsedConfig.backward ?? false;\n    return createEncoder({\n        fixedSize: size,\n        write(value: boolean[], bytes, offset) {\n            const bytesToAdd: number[] = [];\n\n            for (let i = 0; i < size; i += 1) {\n                let byte = 0;\n                for (let j = 0; j < 8; j += 1) {\n                    const feature = Number(value[i * 8 + j] ?? 0);\n                    byte |= feature << (backward ? j : 7 - j);\n                }\n                if (backward) {\n                    bytesToAdd.unshift(byte);\n                } else {\n                    bytesToAdd.push(byte);\n                }\n            }\n\n            bytes.set(bytesToAdd, offset);\n            return size;\n        },\n    });\n}\n\n/**\n * Returns a decoder that unpacks bits into an array of booleans.\n *\n * This decoder converts a compact bit representation back into a list of `boolean` values.\n * Each byte is expanded into 8 booleans.\n *\n * The `backward` config option determines whether the bits are read in MSB-first (`false`)\n * or LSB-first (`true`).\n *\n * For more details, see {@link getBitArrayCodec}.\n *\n * @typeParam TSize - The number of bytes used to store the bit array.\n *\n * @param size - The number of bytes allocated for the bit array (must be sufficient for the expected boolean count).\n * @param config - Configuration options for decoding the bit array.\n * @returns A `FixedSizeDecoder<boolean[], TSize>` for decoding bit arrays.\n *\n * @example\n * Decoding a bit array.\n * ```ts\n * const decoder = getBitArrayDecoder(1);\n *\n * decoder.decode(new Uint8Array([0xa0]));\n * // [true, false, true, false, false, false, false, false]\n * ```\n *\n * @see {@link getBitArrayCodec}\n */\nexport function getBitArrayDecoder<TSize extends number>(\n    size: TSize,\n    config: BitArrayCodecConfig | boolean = {},\n): FixedSizeDecoder<boolean[], TSize> {\n    const parsedConfig: BitArrayCodecConfig = typeof config === 'boolean' ? { backward: config } : config;\n    const backward = parsedConfig.backward ?? false;\n    return createDecoder({\n        fixedSize: size,\n        read(bytes, offset) {\n            assertByteArrayHasEnoughBytesForCodec('bitArray', size, bytes, offset);\n            const booleans: boolean[] = [];\n            let slice = bytes.slice(offset, offset + size);\n            slice = backward ? slice.reverse() : slice;\n\n            slice.forEach(byte => {\n                for (let i = 0; i < 8; i += 1) {\n                    if (backward) {\n                        booleans.push(Boolean(byte & 1));\n                        byte >>= 1;\n                    } else {\n                        booleans.push(Boolean(byte & 0b1000_0000));\n                        byte <<= 1;\n                    }\n                }\n            });\n\n            return [booleans, offset + size];\n        },\n    });\n}\n\n/**\n * Returns a codec that encodes and decodes boolean arrays as compact bit representations.\n *\n * This codec efficiently stores boolean arrays as bits, packing 8 values per byte.\n * The `backward` config option determines whether bits are stored in MSB-first (`false`)\n * or LSB-first (`true`).\n *\n * @typeParam TSize - The number of bytes used to store the bit array.\n *\n * @param size - The number of bytes allocated for the bit array (must be sufficient for the expected boolean count).\n * @param config - Configuration options for encoding and decoding the bit array.\n * @returns A `FixedSizeCodec<boolean[], boolean[], TSize>` for encoding and decoding bit arrays.\n *\n * @example\n * Encoding and decoding a bit array.\n * ```ts\n * const codec = getBitArrayCodec(1);\n *\n * codec.encode([true, false, true, false, false, false, false, false]);\n * // 0xa0 (0b10100000)\n *\n * codec.decode(new Uint8Array([0xa0]));\n * // [true, false, true, false, false, false, false, false]\n * ```\n *\n * @example\n * Encoding and decoding a bit array backwards.\n * ```ts\n * const codec = getBitArrayCodec(1, { backward: true });\n *\n * codec.encode([true, false, true, false, false, false, false, false]);\n * // 0x05 (0b00000101)\n *\n * codec.decode(new Uint8Array([0x05]));\n * // [true, false, true, false, false, false, false, false]\n * ```\n *\n * @remarks\n * Separate {@link getBitArrayEncoder} and {@link getBitArrayDecoder} functions are available.\n *\n * ```ts\n * const bytes = getBitArrayEncoder(1).encode([true, false, true, false]);\n * const value = getBitArrayDecoder(1).decode(bytes);\n * ```\n *\n * @see {@link getBitArrayEncoder}\n * @see {@link getBitArrayDecoder}\n */\nexport function getBitArrayCodec<TSize extends number>(\n    size: TSize,\n    config: BitArrayCodecConfig | boolean = {},\n): FixedSizeCodec<boolean[], boolean[], TSize> {\n    return combineCodec(getBitArrayEncoder(size, config), getBitArrayDecoder(size, config));\n}\n", "import {\n    Codec,\n    combineCodec,\n    Decoder,\n    Encoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    transformDecoder,\n    transformEncoder,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\nimport {\n    FixedSizeNumberCodec,\n    FixedSizeNumberDecoder,\n    FixedSizeNumberEncoder,\n    getU8Decoder,\n    getU8Encoder,\n    NumberCodec,\n    NumberDecoder,\n    NumberEncoder,\n} from '@solana/codecs-numbers';\n\n/**\n * Defines the configuration options for boolean codecs.\n *\n * A boolean codec encodes `true` as `1` and `false` as `0`.\n * The `size` option allows customizing the number codec used for storage.\n *\n * @typeParam TSize - A number codec, encoder, or decoder used for boolean representation.\n *\n * @see {@link getBooleanEncoder}\n * @see {@link getBooleanDecoder}\n * @see {@link getBooleanCodec}\n */\nexport type BooleanCodecConfig<TSize extends NumberCodec | NumberDecoder | NumberEncoder> = {\n    /**\n     * The number codec used to store boolean values.\n     *\n     * - By default, booleans are stored as a `u8` (`1` for `true`, `0` for `false`).\n     * - A custom number codec can be provided to change the storage size.\n     *\n     * @defaultValue `u8`\n     */\n    size?: TSize;\n};\n\n/**\n * Returns an encoder for boolean values.\n *\n * This encoder converts `true` into `1` and `false` into `0`.\n * The `size` option allows customizing the number codec used for storage.\n *\n * For more details, see {@link getBooleanCodec}.\n *\n * @param config - Configuration options for encoding booleans.\n * @returns A `FixedSizeEncoder<boolean, N>` where `N` is the size of the number codec.\n *\n * @example\n * Encoding booleans.\n * ```ts\n * const encoder = getBooleanEncoder();\n *\n * encoder.encode(false); // 0x00\n * encoder.encode(true);  // 0x01\n * ```\n *\n * @see {@link getBooleanCodec}\n */\nexport function getBooleanEncoder(): FixedSizeEncoder<boolean, 1>;\nexport function getBooleanEncoder<TSize extends number>(\n    config: BooleanCodecConfig<NumberEncoder> & { size: FixedSizeNumberEncoder<TSize> },\n): FixedSizeEncoder<boolean, TSize>;\nexport function getBooleanEncoder(config: BooleanCodecConfig<NumberEncoder>): VariableSizeEncoder<boolean>;\nexport function getBooleanEncoder(config: BooleanCodecConfig<NumberEncoder> = {}): Encoder<boolean> {\n    return transformEncoder(config.size ?? getU8Encoder(), (value: boolean) => (value ? 1 : 0));\n}\n\n/**\n * Returns a decoder for boolean values.\n *\n * This decoder reads a number and interprets `1` as `true` and `0` as `false`.\n * The `size` option allows customizing the number codec used for storage.\n *\n * For more details, see {@link getBooleanCodec}.\n *\n * @param config - Configuration options for decoding booleans.\n * @returns A `FixedSizeDecoder<boolean, N>` where `N` is the size of the number codec.\n *\n * @example\n * Decoding booleans.\n * ```ts\n * const decoder = getBooleanDecoder();\n *\n * decoder.decode(new Uint8Array([0x00])); // false\n * decoder.decode(new Uint8Array([0x01])); // true\n * ```\n *\n * @see {@link getBooleanCodec}\n */\nexport function getBooleanDecoder(): FixedSizeDecoder<boolean, 1>;\nexport function getBooleanDecoder<TSize extends number>(\n    config: BooleanCodecConfig<NumberDecoder> & { size: FixedSizeNumberDecoder<TSize> },\n): FixedSizeDecoder<boolean, TSize>;\nexport function getBooleanDecoder(config: BooleanCodecConfig<NumberDecoder>): VariableSizeDecoder<boolean>;\nexport function getBooleanDecoder(config: BooleanCodecConfig<NumberDecoder> = {}): Decoder<boolean> {\n    return transformDecoder(config.size ?? getU8Decoder(), (value: bigint | number): boolean => Number(value) === 1);\n}\n\n/**\n * Returns a codec for encoding and decoding boolean values.\n *\n * By default, booleans are stored as a `u8` (`1` for `true`, `0` for `false`).\n * The `size` option allows customizing the number codec used for storage.\n *\n * @param config - Configuration options for encoding and decoding booleans.\n * @returns A `FixedSizeCodec<boolean, boolean, N>` where `N` is the size of the number codec.\n *\n * @example\n * Encoding and decoding booleans using a `u8` (default).\n * ```ts\n * const codec = getBooleanCodec();\n *\n * codec.encode(false); // 0x00\n * codec.encode(true);  // 0x01\n *\n * codec.decode(new Uint8Array([0x00])); // false\n * codec.decode(new Uint8Array([0x01])); // true\n * ```\n *\n * @example\n * Encoding and decoding booleans using a custom number codec.\n * ```ts\n * const codec = getBooleanCodec({ size: getU16Codec() });\n *\n * codec.encode(false); // 0x0000\n * codec.encode(true);  // 0x0100\n *\n * codec.decode(new Uint8Array([0x00, 0x00])); // false\n * codec.decode(new Uint8Array([0x01, 0x00])); // true\n * ```\n *\n * @remarks\n * Separate {@link getBooleanEncoder} and {@link getBooleanDecoder} functions are available.\n *\n * ```ts\n * const bytes = getBooleanEncoder().encode(true);\n * const value = getBooleanDecoder().decode(bytes);\n * ```\n *\n * @see {@link getBooleanEncoder}\n * @see {@link getBooleanDecoder}\n */\nexport function getBooleanCodec(): FixedSizeCodec<boolean, boolean, 1>;\nexport function getBooleanCodec<TSize extends number>(\n    config: BooleanCodecConfig<NumberCodec> & { size: FixedSizeNumberCodec<TSize> },\n): FixedSizeCodec<boolean, boolean, TSize>;\nexport function getBooleanCodec(config: BooleanCodecConfig<NumberCodec>): VariableSizeCodec<boolean>;\nexport function getBooleanCodec(config: BooleanCodecConfig<NumberCodec> = {}): Codec<boolean> {\n    return combineCodec(getBooleanEncoder(config), getBooleanDecoder(config));\n}\n", "import {\n    combineCodec,\n    createDecoder,\n    create<PERSON>nco<PERSON>,\n    ReadonlyUint8Array,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\n\n/**\n * Returns an encoder for raw byte arrays.\n *\n * This encoder writes byte arrays exactly as provided without modification.\n *\n * The size of the encoded byte array is determined by the length of the input.\n * - To enforce a fixed size, consider using {@link fixEncoderSize}.\n * - To add a size prefix, use {@link addEncoderSizePrefix}.\n * - To add a sentinel value, use {@link addEncoderSentinel}.\n *\n * For more details, see {@link getBytesCodec}.\n *\n * @returns A `VariableSizeEncoder<ReadonlyUint8Array | Uint8Array>`.\n *\n * @example\n * Encoding a byte array as-is.\n * ```ts\n * const encoder = getBytesEncoder();\n *\n * encoder.encode(new Uint8Array([1, 2, 3])); // 0x010203\n * encoder.encode(new Uint8Array([255, 0, 127])); // 0xff007f\n * ```\n *\n * @see {@link getBytesCodec}\n */\nexport function getBytesEncoder(): VariableSizeEncoder<ReadonlyUint8Array | Uint8Array> {\n    return createEncoder({\n        getSizeFromValue: value => value.length,\n        write: (value, bytes, offset) => {\n            bytes.set(value, offset);\n            return offset + value.length;\n        },\n    });\n}\n\n/**\n * Returns a decoder for raw byte arrays.\n *\n * This decoder reads byte arrays exactly as provided without modification.\n *\n * The decoded byte array extends from the provided offset to the end of the input.\n * - To enforce a fixed size, consider using {@link fixDecoderSize}.\n * - To add a size prefix, use {@link addDecoderSizePrefix}.\n * - To add a sentinel value, use {@link addDecoderSentinel}.\n *\n * For more details, see {@link getBytesCodec}.\n *\n * @returns A `VariableSizeDecoder<ReadonlyUint8Array>`.\n *\n * @example\n * Decoding a byte array as-is.\n * ```ts\n * const decoder = getBytesDecoder();\n *\n * decoder.decode(new Uint8Array([1, 2, 3])); // Uint8Array([1, 2, 3])\n * decoder.decode(new Uint8Array([255, 0, 127])); // Uint8Array([255, 0, 127])\n * ```\n *\n * @see {@link getBytesCodec}\n */\nexport function getBytesDecoder(): VariableSizeDecoder<ReadonlyUint8Array> {\n    return createDecoder({\n        read: (bytes, offset) => {\n            const slice = bytes.slice(offset);\n            return [slice, offset + slice.length];\n        },\n    });\n}\n\n/**\n * Returns a codec for encoding and decoding raw byte arrays.\n *\n * This codec serializes and deserializes byte arrays without modification.\n *\n * The size of the encoded and decoded byte array is determined dynamically.\n * This means, when reading, the codec will consume all remaining bytes in the input.\n * - To enforce a fixed size, consider using {@link fixCodecSize}.\n * - To add a size prefix, use {@link addCodecSizePrefix}.\n * - To add a sentinel value, use {@link addCodecSentinel}.\n *\n * @returns A `VariableSizeCodec<ReadonlyUint8Array | Uint8Array, ReadonlyUint8Array>`.\n *\n * @example\n * Encoding and decoding a byte array.\n * ```ts\n * const codec = getBytesCodec();\n *\n * codec.encode(new Uint8Array([1, 2, 3])); // 0x010203\n * codec.decode(new Uint8Array([255, 0, 127])); // Uint8Array([255, 0, 127])\n * ```\n *\n * @remarks\n * Separate {@link getBytesEncoder} and {@link getBytesDecoder} functions are available.\n *\n * ```ts\n * const bytes = getBytesEncoder().encode(new Uint8Array([1, 2, 3]));\n * const value = getBytesDecoder().decode(bytes);\n * ```\n *\n * @see {@link getBytesEncoder}\n * @see {@link getBytesDecoder}\n */\nexport function getBytesCodec(): VariableSizeCodec<ReadonlyUint8Array | Uint8Array, ReadonlyUint8Array> {\n    return combineCodec(getBytesEncoder(), getBytesDecoder());\n}\n", "import {\n    combineCodec,\n    createDecoder,\n    createEncoder,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\nimport { SOLANA_ERROR__CODECS__INVALID_STRING_FOR_BASE, SolanaError } from '@solana/errors';\n\nconst enum HexC {\n    ZERO = 48, // 0\n    NINE = 57, // 9\n    A_UP = 65, // A\n    F_UP = 70, // F\n    A_LO = 97, // a\n    F_LO = 102, // f\n}\n\nconst INVALID_STRING_ERROR_BASE_CONFIG = {\n    alphabet: '0123456789abcdef',\n    base: 16,\n} as const;\n\nfunction charCodeToBase16(char: number) {\n    if (char >= HexC.ZERO && char <= HexC.NINE) return char - HexC.ZERO;\n    if (char >= HexC.A_UP && char <= HexC.F_UP) return char - (HexC.A_UP - 10);\n    if (char >= HexC.A_LO && char <= HexC.F_LO) return char - (HexC.A_LO - 10);\n}\n\n/**\n * Returns an encoder for base-16 (hexadecimal) strings.\n *\n * This encoder serializes strings using a base-16 encoding scheme.\n * The output consists of bytes representing the hexadecimal values of the input string.\n *\n * For more details, see {@link getBase16Codec}.\n *\n * @returns A `VariableSizeEncoder<string>` for encoding base-16 strings.\n *\n * @example\n * Encoding a base-16 string.\n * ```ts\n * const encoder = getBase16Encoder();\n * const bytes = encoder.encode('deadface'); // 0xdeadface\n * ```\n *\n * @see {@link getBase16Codec}\n */\nexport const getBase16Encoder = (): VariableSizeEncoder<string> =>\n    createEncoder({\n        getSizeFromValue: (value: string) => Math.ceil(value.length / 2),\n        write(value: string, bytes, offset) {\n            const len = value.length;\n            const al = len / 2;\n            if (len === 1) {\n                const c = value.charCodeAt(0);\n                const n = charCodeToBase16(c);\n                if (n === undefined) {\n                    throw new SolanaError(SOLANA_ERROR__CODECS__INVALID_STRING_FOR_BASE, {\n                        ...INVALID_STRING_ERROR_BASE_CONFIG,\n                        value,\n                    });\n                }\n                bytes.set([n], offset);\n                return 1 + offset;\n            }\n            const hexBytes = new Uint8Array(al);\n            for (let i = 0, j = 0; i < al; i++) {\n                const c1 = value.charCodeAt(j++);\n                const c2 = value.charCodeAt(j++);\n\n                const n1 = charCodeToBase16(c1);\n                const n2 = charCodeToBase16(c2);\n                if (n1 === undefined || (n2 === undefined && !Number.isNaN(c2))) {\n                    throw new SolanaError(SOLANA_ERROR__CODECS__INVALID_STRING_FOR_BASE, {\n                        ...INVALID_STRING_ERROR_BASE_CONFIG,\n                        value,\n                    });\n                }\n                hexBytes[i] = !Number.isNaN(c2) ? (n1 << 4) | (n2 ?? 0) : n1;\n            }\n\n            bytes.set(hexBytes, offset);\n            return hexBytes.length + offset;\n        },\n    });\n\n/**\n * Returns a decoder for base-16 (hexadecimal) strings.\n *\n * This decoder deserializes base-16 encoded strings from a byte array.\n *\n * For more details, see {@link getBase16Codec}.\n *\n * @returns A `VariableSizeDecoder<string>` for decoding base-16 strings.\n *\n * @example\n * Decoding a base-16 string.\n * ```ts\n * const decoder = getBase16Decoder();\n * const value = decoder.decode(new Uint8Array([0xde, 0xad, 0xfa, 0xce])); // \"deadface\"\n * ```\n *\n * @see {@link getBase16Codec}\n */\nexport const getBase16Decoder = (): VariableSizeDecoder<string> =>\n    createDecoder({\n        read(bytes, offset) {\n            const value = bytes.slice(offset).reduce((str, byte) => str + byte.toString(16).padStart(2, '0'), '');\n            return [value, bytes.length];\n        },\n    });\n\n/**\n * Returns a codec for encoding and decoding base-16 (hexadecimal) strings.\n *\n * This codec serializes strings using a base-16 encoding scheme.\n * The output consists of bytes representing the hexadecimal values of the input string.\n *\n * @returns A `VariableSizeCodec<string>` for encoding and decoding base-16 strings.\n *\n * @example\n * Encoding and decoding a base-16 string.\n * ```ts\n * const codec = getBase16Codec();\n * const bytes = codec.encode('deadface'); // 0xdeadface\n * const value = codec.decode(bytes);      // \"deadface\"\n * ```\n *\n * @remarks\n * This codec does not enforce a size boundary. It will encode and decode all bytes necessary to represent the string.\n *\n * If you need a fixed-size base-16 codec, consider using {@link fixCodecSize}.\n *\n * ```ts\n * const codec = fixCodecSize(getBase16Codec(), 8);\n * ```\n *\n * If you need a size-prefixed base-16 codec, consider using {@link addCodecSizePrefix}.\n *\n * ```ts\n * const codec = addCodecSizePrefix(getBase16Codec(), getU32Codec());\n * ```\n *\n * Separate {@link getBase16Encoder} and {@link getBase16Decoder} functions are available.\n *\n * ```ts\n * const bytes = getBase16Encoder().encode('deadface');\n * const value = getBase16Decoder().decode(bytes);\n * ```\n *\n * @see {@link getBase16Encoder}\n * @see {@link getBase16Decoder}\n */\nexport const getBase16Codec = (): VariableSizeCodec<string> => combineCodec(getBase16Encoder(), getBase16Decoder());\n", "import {\n    combineCodec,\n    containsBytes,\n    createDecoder,\n    createEncoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    ReadonlyUint8Array,\n} from '@solana/codecs-core';\nimport { getBase16Decoder } from '@solana/codecs-strings';\nimport { SOLANA_ERROR__CODECS__INVALID_CONSTANT, SolanaError } from '@solana/errors';\n\n/**\n * Returns an encoder that always writes a predefined constant byte sequence.\n *\n * This encoder ensures that encoding always produces the specified byte array,\n * ignoring any input values.\n *\n * For more details, see {@link getConstantCodec}.\n *\n * @typeParam TConstant - The fixed byte sequence that will be written during encoding.\n *\n * @param constant - The predefined byte array to encode.\n * @returns A `FixedSizeEncoder<void, N>` where `N` is the length of the constant.\n *\n * @example\n * Encoding a constant magic number.\n * ```ts\n * const encoder = getConstantEncoder(new Uint8Array([1, 2, 3, 4]));\n *\n * const bytes = encoder.encode();\n * // 0x01020304\n * //   └──────┘ The predefined 4-byte constant.\n * ```\n *\n * @see {@link getConstantCodec}\n */\nexport function getConstantEncoder<TConstant extends ReadonlyUint8Array>(\n    constant: TConstant,\n): FixedSizeEncoder<void, TConstant['length']> {\n    return createEncoder({\n        fixedSize: constant.length,\n        write: (_, bytes, offset) => {\n            bytes.set(constant, offset);\n            return offset + constant.length;\n        },\n    });\n}\n\n/**\n * Returns a decoder that verifies a predefined constant byte sequence.\n *\n * This decoder reads the next bytes and checks that they match the provided constant.\n * If the bytes differ, it throws an error.\n *\n * For more details, see {@link getConstantCodec}.\n *\n * @typeParam TConstant - The fixed byte sequence expected during decoding.\n *\n * @param constant - The predefined byte array to verify.\n * @returns A `FixedSizeDecoder<void, N>` where `N` is the length of the constant.\n *\n * @example\n * Decoding a constant magic number.\n * ```ts\n * const decoder = getConstantDecoder(new Uint8Array([1, 2, 3]));\n *\n * decoder.decode(new Uint8Array([1, 2, 3])); // Passes\n * decoder.decode(new Uint8Array([1, 2, 4])); // Throws an error\n * ```\n *\n * @see {@link getConstantCodec}\n */\nexport function getConstantDecoder<TConstant extends ReadonlyUint8Array>(\n    constant: TConstant,\n): FixedSizeDecoder<void, TConstant['length']> {\n    return createDecoder({\n        fixedSize: constant.length,\n        read: (bytes, offset) => {\n            const base16 = getBase16Decoder();\n            if (!containsBytes(bytes, constant, offset)) {\n                throw new SolanaError(SOLANA_ERROR__CODECS__INVALID_CONSTANT, {\n                    constant,\n                    data: bytes,\n                    hexConstant: base16.decode(constant),\n                    hexData: base16.decode(bytes),\n                    offset,\n                });\n            }\n            return [undefined, offset + constant.length];\n        },\n    });\n}\n\n/**\n * Returns a codec that encodes and decodes a predefined constant byte sequence.\n *\n * - **Encoding:** Always writes the specified byte array.\n * - **Decoding:** Asserts that the next bytes match the constant, throwing an error if they do not.\n *\n * This is useful for encoding fixed byte patterns required in a binary format or to use in\n * conjunction with other codecs such as {@link getHiddenPrefixCodec} or {@link getHiddenSuffixCodec}.\n *\n * @typeParam TConstant - The fixed byte sequence to encode and verify during decoding.\n *\n * @param constant - The predefined byte array to encode and assert during decoding.\n * @returns A `FixedSizeCodec<void, void, N>` where `N` is the length of the constant.\n *\n * @example\n * Encoding and decoding a constant magic number.\n * ```ts\n * const codec = getConstantCodec(new Uint8Array([1, 2, 3]));\n *\n * codec.encode(); // 0x010203\n * codec.decode(new Uint8Array([1, 2, 3])); // Passes\n * codec.decode(new Uint8Array([1, 2, 4])); // Throws an error\n * ```\n *\n * @remarks\n * Separate {@link getConstantEncoder} and {@link getConstantDecoder} functions are available.\n *\n * ```ts\n * const bytes = getConstantEncoder(new Uint8Array([1, 2, 3])).encode();\n * getConstantDecoder(new Uint8Array([1, 2, 3])).decode(bytes);\n * ```\n *\n * @see {@link getConstantEncoder}\n * @see {@link getConstantDecoder}\n */\nexport function getConstantCodec<TConstant extends ReadonlyUint8Array>(\n    constant: TConstant,\n): FixedSizeCodec<void, void, TConstant['length']> {\n    return combineCodec(getConstantEncoder(constant), getConstantDecoder(constant));\n}\n", "/* eslint-disable @typescript-eslint/no-explicit-any */\nimport {\n    Code<PERSON>,\n    combineCodec,\n    createDecoder,\n    create<PERSON>nco<PERSON>,\n    Decoder,\n    Encoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    getEncodedSize,\n    ReadonlyUint8Array,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\n\nimport { assertValidNumberOfItemsForCodec } from './assertions';\nimport { DrainOuterGeneric, getFixedSize, getMaxSize, sumCodecSizes } from './utils';\n\n/**\n * Infers the TypeScript type for a tuple that can be encoded using a tuple codec.\n *\n * This type maps each provided item encoder to its corresponding value type.\n *\n * @typeParam TItems - An array of encoders, each corresponding to a tuple element.\n */\ntype GetEncoderTypeFromItems<TItems extends readonly Encoder<any>[]> = DrainOuterGeneric<{\n    [I in keyof TItems]: TItems[I] extends Encoder<infer TFrom> ? TFrom : never;\n}>;\n\n/**\n * Infers the TypeScript type for a tuple that can be decoded using a tuple codec.\n *\n * This type maps each provided item decoder to its corresponding value type.\n *\n * @typeParam TItems - An array of decoders, each corresponding to a tuple element.\n */\ntype GetDecoderTypeFromItems<TItems extends readonly Decoder<any>[]> = DrainOuterGeneric<{\n    [I in keyof TItems]: TItems[I] extends Decoder<infer TTo> ? TTo : never;\n}>;\n\n/**\n * Returns an encoder for tuples.\n *\n * This encoder serializes a fixed-size array (tuple) by encoding its items\n * sequentially using the provided item encoders.\n *\n * For more details, see {@link getTupleCodec}.\n *\n * @typeParam TItems - An array of encoders, each corresponding to a tuple element.\n *\n * @param items - The encoders for each item in the tuple.\n * @returns A `FixedSizeEncoder` or `VariableSizeEncoder` for encoding tuples.\n *\n * @example\n * Encoding a tuple with 2 items.\n * ```ts\n * const encoder = getTupleEncoder([fixCodecSize(getUtf8Encoder(), 5), getU8Encoder()]);\n *\n * const bytes = encoder.encode(['Alice', 42]);\n * // 0x416c6963652a\n * //   |         └── Second item (42)\n * //   └── First item (\"Alice\")\n * ```\n *\n * @see {@link getTupleCodec}\n */\nexport function getTupleEncoder<const TItems extends readonly FixedSizeEncoder<any>[]>(\n    items: TItems,\n): FixedSizeEncoder<GetEncoderTypeFromItems<TItems>>;\nexport function getTupleEncoder<const TItems extends readonly Encoder<any>[]>(\n    items: TItems,\n): VariableSizeEncoder<GetEncoderTypeFromItems<TItems>>;\nexport function getTupleEncoder<const TItems extends readonly Encoder<any>[]>(\n    items: TItems,\n): Encoder<GetEncoderTypeFromItems<TItems>> {\n    type TFrom = GetEncoderTypeFromItems<TItems>;\n    const fixedSize = sumCodecSizes(items.map(getFixedSize));\n    const maxSize = sumCodecSizes(items.map(getMaxSize)) ?? undefined;\n\n    return createEncoder({\n        ...(fixedSize === null\n            ? {\n                  getSizeFromValue: (value: TFrom) =>\n                      items.map((item, index) => getEncodedSize(value[index], item)).reduce((all, one) => all + one, 0),\n                  maxSize,\n              }\n            : { fixedSize }),\n        write: (value: TFrom, bytes, offset) => {\n            assertValidNumberOfItemsForCodec('tuple', items.length, value.length);\n            items.forEach((item, index) => {\n                offset = item.write(value[index], bytes, offset);\n            });\n            return offset;\n        },\n    });\n}\n\n/**\n * Returns a decoder for tuples.\n *\n * This decoder deserializes a fixed-size array (tuple) by decoding its items\n * sequentially using the provided item decoders.\n *\n * For more details, see {@link getTupleCodec}.\n *\n * @typeParam TItems - An array of decoders, each corresponding to a tuple element.\n *\n * @param items - The decoders for each item in the tuple.\n * @returns A `FixedSizeDecoder` or `VariableSizeDecoder` for decoding tuples.\n *\n * @example\n * Decoding a tuple with 2 items.\n * ```ts\n * const decoder = getTupleDecoder([fixCodecSize(getUtf8Decoder(), 5), getU8Decoder()]);\n *\n * const tuple = decoder.decode(new Uint8Array([\n *   0x41,0x6c,0x69,0x63,0x65,0x2a\n * ]));\n * // ['Alice', 42]\n * ```\n *\n * @see {@link getTupleCodec}\n */\nexport function getTupleDecoder<const TItems extends readonly FixedSizeDecoder<any>[]>(\n    items: TItems,\n): FixedSizeDecoder<GetDecoderTypeFromItems<TItems>>;\nexport function getTupleDecoder<const TItems extends readonly Decoder<any>[]>(\n    items: TItems,\n): VariableSizeDecoder<GetDecoderTypeFromItems<TItems>>;\nexport function getTupleDecoder<const TItems extends readonly Decoder<any>[]>(\n    items: TItems,\n): Decoder<GetDecoderTypeFromItems<TItems>> {\n    type TTo = GetDecoderTypeFromItems<TItems>;\n    const fixedSize = sumCodecSizes(items.map(getFixedSize));\n    const maxSize = sumCodecSizes(items.map(getMaxSize)) ?? undefined;\n\n    return createDecoder({\n        ...(fixedSize === null ? { maxSize } : { fixedSize }),\n        read: (bytes: ReadonlyUint8Array | Uint8Array, offset) => {\n            const values = [] as Array<any> & TTo;\n            items.forEach(item => {\n                const [newValue, newOffset] = item.read(bytes, offset);\n                values.push(newValue);\n                offset = newOffset;\n            });\n            return [values, offset];\n        },\n    });\n}\n\n/**\n * Returns a codec for encoding and decoding tuples.\n *\n * This codec serializes tuples by encoding and decoding each item sequentially.\n *\n * Unlike the {@link getArrayCodec} codec, each item in the tuple has its own codec\n * and, therefore, can be of a different type.\n *\n * @typeParam TItems - An array of codecs, each corresponding to a tuple element.\n *\n * @param items - The codecs for each item in the tuple.\n * @returns A `FixedSizeCodec` or `VariableSizeCodec` for encoding and decoding tuples.\n *\n * @example\n * Encoding and decoding a tuple with 2 items.\n * ```ts\n * const codec = getTupleCodec([fixCodecSize(getUtf8Codec(), 5), getU8Codec()]);\n *\n * const bytes = codec.encode(['Alice', 42]);\n * // 0x416c6963652a\n * //   |         └── Second item (42)\n * //   └── First item (\"Alice\")\n *\n * const tuple = codec.decode(bytes);\n * // ['Alice', 42]\n * ```\n *\n * @remarks\n * Separate {@link getTupleEncoder} and {@link getTupleDecoder} functions are available.\n *\n * ```ts\n * const bytes = getTupleEncoder([fixCodecSize(getUtf8Encoder(), 5), getU8Encoder()])\n *   .encode(['Alice', 42]);\n *\n * const tuple = getTupleDecoder([fixCodecSize(getUtf8Decoder(), 5), getU8Decoder()])\n *   .decode(bytes);\n * ```\n *\n * @see {@link getTupleEncoder}\n * @see {@link getTupleDecoder}\n */\nexport function getTupleCodec<const TItems extends readonly FixedSizeCodec<any>[]>(\n    items: TItems,\n): FixedSizeCodec<GetEncoderTypeFromItems<TItems>, GetDecoderTypeFromItems<TItems> & GetEncoderTypeFromItems<TItems>>;\nexport function getTupleCodec<const TItems extends readonly Codec<any>[]>(\n    items: TItems,\n): VariableSizeCodec<\n    GetEncoderTypeFromItems<TItems>,\n    GetDecoderTypeFromItems<TItems> & GetEncoderTypeFromItems<TItems>\n>;\nexport function getTupleCodec<const TItems extends readonly Codec<any>[]>(\n    items: TItems,\n): Codec<GetEncoderTypeFromItems<TItems>, GetDecoderTypeFromItems<TItems> & GetEncoderTypeFromItems<TItems>> {\n    return combineCodec(\n        getTupleEncoder(items),\n        getTupleDecoder(items) as Decoder<GetDecoderTypeFromItems<TItems> & GetEncoderTypeFromItems<TItems>>,\n    );\n}\n", "/* eslint-disable @typescript-eslint/no-explicit-any */\nimport {\n    Codec,\n    combineCodec,\n    createDecoder,\n    createEnco<PERSON>,\n    Decoder,\n    Encoder,\n    getEncodedSize,\n    isFixedSize,\n    Offset,\n    ReadonlyUint8Array,\n} from '@solana/codecs-core';\nimport { SOLANA_ERROR__CODECS__UNION_VARIANT_OUT_OF_RANGE, SolanaError } from '@solana/errors';\n\nimport { DrainOuterGeneric, getMaxSize, maxCodecSizes } from './utils';\n\n/**\n * Infers the TypeScript type for values that can be encoded using a union codec.\n *\n * This type maps the provided variant encoders to their corresponding value types.\n *\n * @typeParam TVariants - An array of encoders, each corresponding to a union variant.\n */\ntype GetEncoderTypeFromVariants<TVariants extends readonly Encoder<any>[]> = DrainOuterGeneric<{\n    [I in keyof TVariants]: TVariants[I] extends Encoder<infer TFrom> ? TFrom : never;\n}>[number];\n\n/**\n * Infers the TypeScript type for values that can be decoded using a union codec.\n *\n * This type maps the provided variant decoders to their corresponding value types.\n *\n * @typeParam TVariants - An array of decoders, each corresponding to a union variant.\n */\ntype GetDecoderTypeFromVariants<TVariants extends readonly Decoder<any>[]> = DrainOuterGeneric<{\n    [I in keyof TVariants]: TVariants[I] extends Decoder<infer TFrom> ? TFrom : never;\n}>[number];\n\n/**\n * Returns an encoder for union types.\n *\n * This encoder serializes values by selecting the correct variant encoder\n * based on the `getIndexFromValue` function.\n *\n * Unlike other codecs, this encoder does not store the variant index.\n * It is the user's responsibility to manage discriminators separately.\n *\n * For more details, see {@link getUnionCodec}.\n *\n * @typeParam TVariants - An array of encoders, each corresponding to a union variant.\n *\n * @param variants - The encoders for each variant of the union.\n * @param getIndexFromValue - A function that determines the variant index from the provided value.\n * @returns An `Encoder` for encoding union values.\n *\n * @example\n * Encoding a union of numbers and booleans.\n * ```ts\n * const encoder = getUnionEncoder(\n *   [getU16Encoder(), getBooleanEncoder()],\n *   value => (typeof value === 'number' ? 0 : 1)\n * );\n *\n * encoder.encode(42);\n * // 0x2a00\n * //   └── Encoded number (42) as `u16`\n *\n * encoder.encode(true);\n * // 0x01\n * //   └── Encoded boolean (`true`) as `u8`\n * ```\n *\n * @see {@link getUnionCodec}\n */\nexport function getUnionEncoder<const TVariants extends readonly Encoder<any>[]>(\n    variants: TVariants,\n    getIndexFromValue: (value: GetEncoderTypeFromVariants<TVariants>) => number,\n): Encoder<GetEncoderTypeFromVariants<TVariants>> {\n    type TFrom = GetEncoderTypeFromVariants<TVariants>;\n    const fixedSize = getUnionFixedSize(variants);\n    const write: Encoder<TFrom>['write'] = (variant, bytes, offset) => {\n        const index = getIndexFromValue(variant);\n        assertValidVariantIndex(variants, index);\n        return variants[index].write(variant, bytes, offset);\n    };\n\n    if (fixedSize !== null) {\n        return createEncoder({ fixedSize, write });\n    }\n\n    const maxSize = getUnionMaxSize(variants);\n    return createEncoder({\n        ...(maxSize !== null ? { maxSize } : {}),\n        getSizeFromValue: variant => {\n            const index = getIndexFromValue(variant);\n            assertValidVariantIndex(variants, index);\n            return getEncodedSize(variant, variants[index]);\n        },\n        write,\n    });\n}\n\n/**\n * Returns a decoder for union types.\n *\n * This decoder deserializes values by selecting the correct variant decoder\n * based on the `getIndexFromBytes` function.\n *\n * Unlike other codecs, this decoder does not assume a stored discriminator.\n * It is the user's responsibility to manage discriminators separately.\n *\n * For more details, see {@link getUnionCodec}.\n *\n * @typeParam TVariants - An array of decoders, each corresponding to a union variant.\n *\n * @param variants - The decoders for each variant of the union.\n * @param getIndexFromBytes - A function that determines the variant index from the byte array.\n * @returns A `Decoder` for decoding union values.\n *\n * @example\n * Decoding a union of numbers and booleans.\n * ```ts\n * const decoder = getUnionDecoder(\n *   [getU16Decoder(), getBooleanDecoder()],\n *   (bytes, offset) => (bytes.length - offset > 1 ? 0 : 1)\n * );\n *\n * decoder.decode(new Uint8Array([0x2a, 0x00])); // 42\n * decoder.decode(new Uint8Array([0x01]));       // true\n * // Type is inferred as `number | boolean`\n * ```\n *\n * @see {@link getUnionCodec}\n */\nexport function getUnionDecoder<const TVariants extends readonly Decoder<any>[]>(\n    variants: TVariants,\n    getIndexFromBytes: (bytes: ReadonlyUint8Array, offset: Offset) => number,\n): Decoder<GetDecoderTypeFromVariants<TVariants>> {\n    type TTo = GetDecoderTypeFromVariants<TVariants>;\n    const fixedSize = getUnionFixedSize(variants);\n    const read: Decoder<TTo>['read'] = (bytes, offset) => {\n        const index = getIndexFromBytes(bytes, offset);\n        assertValidVariantIndex(variants, index);\n        return variants[index].read(bytes, offset);\n    };\n\n    if (fixedSize !== null) {\n        return createDecoder({ fixedSize, read });\n    }\n\n    const maxSize = getUnionMaxSize(variants);\n    return createDecoder({ ...(maxSize !== null ? { maxSize } : {}), read });\n}\n\n/**\n * Returns a codec for encoding and decoding union types.\n *\n * This codec serializes and deserializes union values by selecting the correct variant\n * based on the provided index functions.\n *\n * Unlike the {@link getDiscriminatedUnionCodec}, this codec does not assume a stored\n * discriminator and must be used with an explicit mechanism for managing discriminators.\n *\n * @typeParam TVariants - An array of codecs, each corresponding to a union variant.\n *\n * @param variants - The codecs for each variant of the union.\n * @param getIndexFromValue - A function that determines the variant index from the provided value.\n * @param getIndexFromBytes - A function that determines the variant index from the byte array.\n * @returns A `Codec` for encoding and decoding union values.\n *\n * @example\n * Encoding and decoding a union of numbers and booleans.\n * ```ts\n * const codec = getUnionCodec(\n *   [getU16Codec(), getBooleanCodec()],\n *   value => (typeof value === 'number' ? 0 : 1),\n *   (bytes, offset) => (bytes.length - offset > 1 ? 0 : 1)\n * );\n *\n * const bytes1 = codec.encode(42); // 0x2a00\n * const value1: number | boolean = codec.decode(bytes1); // 42\n *\n * const bytes2 = codec.encode(true); // 0x01\n * const value2: number | boolean = codec.decode(bytes2); // true\n * ```\n *\n * @remarks\n * If you need a codec that includes a stored discriminator,\n * consider using {@link getDiscriminatedUnionCodec}.\n *\n * Separate {@link getUnionEncoder} and {@link getUnionDecoder} functions are also available.\n *\n * ```ts\n * const bytes = getUnionEncoder(variantEncoders, getIndexFromValue).encode(42);\n * const value = getUnionDecoder(variantDecoders, getIndexFromBytes).decode(bytes);\n * ```\n *\n * @see {@link getUnionEncoder}\n * @see {@link getUnionDecoder}\n * @see {@link getDiscriminatedUnionCodec}\n */\nexport function getUnionCodec<const TVariants extends readonly Codec<any>[]>(\n    variants: TVariants,\n    getIndexFromValue: (value: GetEncoderTypeFromVariants<TVariants>) => number,\n    getIndexFromBytes: (bytes: ReadonlyUint8Array, offset: Offset) => number,\n): Codec<\n    GetEncoderTypeFromVariants<TVariants>,\n    GetDecoderTypeFromVariants<TVariants> & GetEncoderTypeFromVariants<TVariants>\n> {\n    return combineCodec(\n        getUnionEncoder(variants, getIndexFromValue),\n        getUnionDecoder(variants, getIndexFromBytes) as Decoder<\n            GetDecoderTypeFromVariants<TVariants> & GetEncoderTypeFromVariants<TVariants>\n        >,\n    );\n}\n\nfunction assertValidVariantIndex(variants: readonly unknown[], index: number) {\n    if (typeof variants[index] === 'undefined') {\n        throw new SolanaError(SOLANA_ERROR__CODECS__UNION_VARIANT_OUT_OF_RANGE, {\n            maxRange: variants.length - 1,\n            minRange: 0,\n            variant: index,\n        });\n    }\n}\n\nfunction getUnionFixedSize<const TVariants extends readonly (Decoder<any> | Encoder<any>)[]>(variants: TVariants) {\n    if (variants.length === 0) return 0;\n    if (!isFixedSize(variants[0])) return null;\n    const variantSize = variants[0].fixedSize;\n    const sameSizedVariants = variants.every(variant => isFixedSize(variant) && variant.fixedSize === variantSize);\n    return sameSizedVariants ? variantSize : null;\n}\n\nfunction getUnionMaxSize<const TVariants extends readonly (Decoder<any> | Encoder<any>)[]>(variants: TVariants) {\n    return maxCodecSizes(variants.map(variant => getMaxSize(variant)));\n}\n", "/* eslint-disable @typescript-eslint/no-explicit-any */\nimport { Codec, combineCodec, Decoder, Encoder, transformDecoder, transformEncoder } from '@solana/codecs-core';\nimport { getU8Decoder, getU8Encoder, NumberCodec, NumberDecoder, NumberEncoder } from '@solana/codecs-numbers';\nimport { SOLANA_ERROR__CODECS__INVALID_DISCRIMINATED_UNION_VARIANT, SolanaError } from '@solana/errors';\n\nimport { getTupleDecoder, getTupleEncoder } from './tuple';\nimport { getUnionDecoder, getUnionEncoder } from './union';\nimport { DrainOuterGeneric } from './utils';\n\n/**\n * Represents a discriminated union using a specific discriminator property.\n *\n * A discriminated union is a TypeScript-friendly way to represent Rust-like enums.\n * Each variant in the union is distinguished by a shared discriminator property.\n *\n * @typeParam TDiscriminatorProperty - The name of the discriminator property.\n * @typeParam TDiscriminatorValue - The type of the discriminator value.\n *\n * @example\n * ```ts\n * type Message =\n *   | { __kind: 'Quit' } // Empty variant\n *   | { __kind: 'Write'; fields: [string] } // Tuple variant\n *   | { __kind: 'Move'; x: number; y: number }; // Struct variant\n * ```\n */\nexport type DiscriminatedUnion<\n    TDiscriminatorProperty extends string = '__kind',\n    TDiscriminatorValue extends string = string,\n> = {\n    [P in TDiscriminatorProperty]: TDiscriminatorValue;\n};\n\n/**\n * Extracts a variant from a discriminated union based on its discriminator value.\n *\n * @typeParam TUnion - The discriminated union type.\n * @typeParam TDiscriminatorProperty - The property used as the discriminator.\n * @typeParam TDiscriminatorValue - The specific variant to extract.\n *\n * @example\n * ```ts\n * type Message =\n *   | { __kind: 'Quit' }\n *   | { __kind: 'Write'; fields: [string] }\n *   | { __kind: 'Move'; x: number; y: number };\n *\n * type ClickEvent = GetDiscriminatedUnionVariant<Message, '__kind', 'Move'>;\n * // -> { __kind: 'Move'; x: number; y: number }\n * ```\n */\nexport type GetDiscriminatedUnionVariant<\n    TUnion extends DiscriminatedUnion<TDiscriminatorProperty>,\n    TDiscriminatorProperty extends string,\n    TDiscriminatorValue extends TUnion[TDiscriminatorProperty],\n> = Extract<TUnion, DiscriminatedUnion<TDiscriminatorProperty, TDiscriminatorValue>>;\n\n/**\n * Extracts a variant from a discriminated union without its discriminator property.\n *\n * @typeParam TUnion - The discriminated union type.\n * @typeParam TDiscriminatorProperty - The property used as the discriminator.\n * @typeParam TDiscriminatorValue - The specific variant to extract.\n *\n * @example\n * ```ts\n * type Message =\n *   | { __kind: 'Quit' }\n *   | { __kind: 'Write'; fields: [string] }\n *   | { __kind: 'Move'; x: number; y: number };\n *\n * type MoveContent = GetDiscriminatedUnionVariantContent<Message, '__kind', 'Move'>;\n * // -> { x: number; y: number }\n * ```\n */\nexport type GetDiscriminatedUnionVariantContent<\n    TUnion extends DiscriminatedUnion<TDiscriminatorProperty>,\n    TDiscriminatorProperty extends string,\n    TDiscriminatorValue extends TUnion[TDiscriminatorProperty],\n> = Omit<GetDiscriminatedUnionVariant<TUnion, TDiscriminatorProperty, TDiscriminatorValue>, TDiscriminatorProperty>;\n\n/**\n * Defines the configuration for discriminated union codecs.\n *\n * This configuration controls how the discriminator is stored and named.\n *\n * @typeParam TDiscriminatorProperty - The property name of the discriminator.\n * @typeParam TDiscriminatorSize - The codec used for the discriminator prefix.\n */\nexport type DiscriminatedUnionCodecConfig<\n    TDiscriminatorProperty extends string = '__kind',\n    TDiscriminatorSize = NumberCodec | NumberDecoder | NumberEncoder,\n> = {\n    /**\n     * The property name of the discriminator.\n     * @defaultValue `__kind`\n     */\n    discriminator?: TDiscriminatorProperty;\n    /**\n     * The codec used to encode/decode the discriminator prefix.\n     * @defaultValue `u8` prefix\n     */\n    size?: TDiscriminatorSize;\n};\n\ntype DiscriminatorValue = bigint | boolean | number | string | null | undefined;\ntype Variants<T> = readonly (readonly [DiscriminatorValue, T])[];\ntype ArrayIndices<T extends readonly unknown[]> = Exclude<Partial<T>['length'], T['length']> & number;\n\ntype GetEncoderTypeFromVariants<\n    TVariants extends Variants<Encoder<any>>,\n    TDiscriminatorProperty extends string,\n> = DrainOuterGeneric<{\n    [I in ArrayIndices<TVariants>]: (TVariants[I][1] extends Encoder<infer TFrom>\n        ? TFrom extends object\n            ? TFrom\n            : object\n        : never) & { [P in TDiscriminatorProperty]: TVariants[I][0] };\n}>[ArrayIndices<TVariants>];\n\ntype GetDecoderTypeFromVariants<\n    TVariants extends Variants<Decoder<any>>,\n    TDiscriminatorProperty extends string,\n> = DrainOuterGeneric<{\n    [I in ArrayIndices<TVariants>]: (TVariants[I][1] extends Decoder<infer TTo>\n        ? TTo extends object\n            ? TTo\n            : object\n        : never) & { [P in TDiscriminatorProperty]: TVariants[I][0] };\n}>[ArrayIndices<TVariants>];\n\n/**\n * Returns an encoder for discriminated unions.\n *\n * This encoder serializes objects that follow the discriminated union pattern\n * by prefixing them with a numerical discriminator that represents their variant.\n *\n * Unlike {@link getUnionEncoder}, this encoder automatically extracts and processes\n * the discriminator property (default: `__kind`) from each variant.\n *\n * For more details, see {@link getDiscriminatedUnionCodec}.\n *\n * @typeParam TVariants - The variants of the discriminated union.\n * @typeParam TDiscriminatorProperty - The property used as the discriminator.\n *\n * @param variants - The variant encoders as `[discriminator, encoder]` pairs.\n * @param config - Configuration options for encoding.\n * @returns An `Encoder` for encoding discriminated union objects.\n *\n * @example\n * Encoding a discriminated union.\n * ```ts\n * type Message =\n *   | { __kind: 'Quit' } // Empty variant.\n *   | { __kind: 'Write'; fields: [string] } // Tuple variant.\n *   | { __kind: 'Move'; x: number; y: number }; // Struct variant.\n *\n * const messageEncoder = getDiscriminatedUnionEncoder([\n *   ['Quit', getUnitEncoder()],\n *   ['Write', getStructEncoder([['fields', getTupleEncoder([addCodecSizePrefix(getUtf8Encoder(), getU32Encoder())])]])],\n *   ['Move', getStructEncoder([['x', getI32Encoder()], ['y', getI32Encoder()]])]\n * ]);\n *\n * messageEncoder.encode({ __kind: 'Move', x: 5, y: 6 });\n * // 0x020500000006000000\n * //   | |       └── Field y (6)\n * //   | └── Field x (5)\n * //   └── 1-byte discriminator (Index 2 — the \"Move\" variant)\n * ```\n *\n * @see {@link getDiscriminatedUnionCodec}\n */\nexport function getDiscriminatedUnionEncoder<\n    const TVariants extends Variants<Encoder<any>>,\n    const TDiscriminatorProperty extends string = '__kind',\n>(\n    variants: TVariants,\n    config: DiscriminatedUnionCodecConfig<TDiscriminatorProperty, NumberEncoder> = {},\n): Encoder<GetEncoderTypeFromVariants<TVariants, TDiscriminatorProperty>> {\n    type TFrom = GetEncoderTypeFromVariants<TVariants, TDiscriminatorProperty>;\n    const discriminatorProperty = (config.discriminator ?? '__kind') as TDiscriminatorProperty;\n    const prefix = config.size ?? getU8Encoder();\n    return getUnionEncoder(\n        variants.map(([, variant], index) =>\n            transformEncoder(getTupleEncoder([prefix, variant]), (value: TFrom): [number, TFrom] => [index, value]),\n        ),\n        value => getVariantDiscriminator(variants, value[discriminatorProperty]),\n    );\n}\n\n/**\n * Returns a decoder for discriminated unions.\n *\n * This decoder deserializes objects that follow the discriminated union pattern\n * by **reading a numerical discriminator** and mapping it to the corresponding variant.\n *\n * Unlike {@link getUnionDecoder}, this decoder automatically inserts the discriminator\n * property (default: `__kind`) into the decoded object.\n *\n * For more details, see {@link getDiscriminatedUnionCodec}.\n *\n * @typeParam TVariants - The variants of the discriminated union.\n * @typeParam TDiscriminatorProperty - The property used as the discriminator.\n *\n * @param variants - The variant decoders as `[discriminator, decoder]` pairs.\n * @param config - Configuration options for decoding.\n * @returns A `Decoder` for decoding discriminated union objects.\n *\n * @example\n * Decoding a discriminated union.\n * ```ts\n * type Message =\n *   | { __kind: 'Quit' } // Empty variant.\n *   | { __kind: 'Write'; fields: [string] } // Tuple variant.\n *   | { __kind: 'Move'; x: number; y: number }; // Struct variant.\n *\n * const messageDecoder = getDiscriminatedUnionDecoder([\n *   ['Quit', getUnitDecoder()],\n *   ['Write', getStructDecoder([['fields', getTupleDecoder([addCodecSizePrefix(getUtf8Decoder(), getU32Decoder())])]])],\n *   ['Move', getStructDecoder([['x', getI32Decoder()], ['y', getI32Decoder()]])]\n * ]);\n *\n * messageDecoder.decode(new Uint8Array([0x02,0x05,0x00,0x00,0x00,0x06,0x00,0x00,0x00]));\n * // { __kind: 'Move', x: 5, y: 6 }\n * ```\n *\n * @see {@link getDiscriminatedUnionCodec}\n */\nexport function getDiscriminatedUnionDecoder<\n    const TVariants extends Variants<Decoder<any>>,\n    const TDiscriminatorProperty extends string = '__kind',\n>(\n    variants: TVariants,\n    config: DiscriminatedUnionCodecConfig<TDiscriminatorProperty, NumberDecoder> = {},\n): Decoder<GetDecoderTypeFromVariants<TVariants, TDiscriminatorProperty>> {\n    const discriminatorProperty = config.discriminator ?? '__kind';\n    const prefix = config.size ?? getU8Decoder();\n    return getUnionDecoder(\n        variants.map(([discriminator, variant]) =>\n            transformDecoder(getTupleDecoder([prefix, variant]), ([, value]) => ({\n                [discriminatorProperty]: discriminator,\n                ...value,\n            })),\n        ),\n        (bytes, offset) => Number(prefix.read(bytes, offset)[0]),\n    );\n}\n\n/**\n * Returns a codec for encoding and decoding {@link DiscriminatedUnion}.\n *\n * A {@link DiscriminatedUnion} is a TypeScript representation of Rust-like enums, where\n * each variant is distinguished by a discriminator field (default: `__kind`).\n *\n * This codec inserts a numerical prefix to represent the variant index.\n *\n * @typeParam TVariants - The variants of the discriminated union.\n * @typeParam TDiscriminatorProperty - The property used as the discriminator.\n *\n * @param variants - The variant codecs as `[discriminator, codec]` pairs.\n * @param config - Configuration options for encoding/decoding.\n * @returns A `Codec` for encoding and decoding discriminated union objects.\n *\n * @example\n * Encoding and decoding a discriminated union.\n * ```ts\n * type Message =\n *   | { __kind: 'Quit' } // Empty variant.\n *   | { __kind: 'Write'; fields: [string] } // Tuple variant.\n *   | { __kind: 'Move'; x: number; y: number }; // Struct variant.\n *\n * const messageCodec = getDiscriminatedUnionCodec([\n *   ['Quit', getUnitCodec()],\n *   ['Write', getStructCodec([['fields', getTupleCodec([addCodecSizePrefix(getUtf8Codec(), getU32Codec())])]])],\n *   ['Move', getStructCodec([['x', getI32Codec()], ['y', getI32Codec()]])]\n * ]);\n *\n * messageCodec.encode({ __kind: 'Move', x: 5, y: 6 });\n * // 0x020500000006000000\n * //   | |       └── Field y (6)\n * //   | └── Field x (5)\n * //   └── 1-byte discriminator (Index 2 — the \"Move\" variant)\n *\n * const value = messageCodec.decode(bytes);\n * // { __kind: 'Move', x: 5, y: 6 }\n * ```\n *\n * @example\n * Using a `u32` discriminator instead of `u8`.\n * ```ts\n * const codec = getDiscriminatedUnionCodec([...], { size: getU32Codec() });\n *\n * codec.encode({ __kind: 'Quit' });\n * // 0x00000000\n * //   └------┘ 4-byte discriminator (Index 0)\n *\n * codec.decode(new Uint8Array([0x00, 0x00, 0x00, 0x00]));\n * // { __kind: 'Quit' }\n * ```\n *\n * @example\n * Customizing the discriminator property.\n * ```ts\n * const codec = getDiscriminatedUnionCodec([...], { discriminator: 'message' });\n *\n * codec.encode({ message: 'Quit' }); // 0x00\n * codec.decode(new Uint8Array([0x00])); // { message: 'Quit' }\n * ```\n *\n * @remarks\n * Separate `getDiscriminatedUnionEncoder` and `getDiscriminatedUnionDecoder` functions are available.\n *\n * ```ts\n * const bytes = getDiscriminatedUnionEncoder(variantEncoders).encode({ __kind: 'Quit' });\n * const message = getDiscriminatedUnionDecoder(variantDecoders).decode(bytes);\n * ```\n *\n * @see {@link getDiscriminatedUnionEncoder}\n * @see {@link getDiscriminatedUnionDecoder}\n */\nexport function getDiscriminatedUnionCodec<\n    const TVariants extends Variants<Codec<any, any>>,\n    const TDiscriminatorProperty extends string = '__kind',\n>(\n    variants: TVariants,\n    config: DiscriminatedUnionCodecConfig<TDiscriminatorProperty, NumberCodec> = {},\n): Codec<\n    GetEncoderTypeFromVariants<TVariants, TDiscriminatorProperty>,\n    GetDecoderTypeFromVariants<TVariants, TDiscriminatorProperty> &\n        GetEncoderTypeFromVariants<TVariants, TDiscriminatorProperty>\n> {\n    return combineCodec(\n        getDiscriminatedUnionEncoder(variants, config),\n        getDiscriminatedUnionDecoder(variants, config) as Decoder<\n            GetDecoderTypeFromVariants<TVariants, TDiscriminatorProperty> &\n                GetEncoderTypeFromVariants<TVariants, TDiscriminatorProperty>\n        >,\n    );\n}\n\nfunction getVariantDiscriminator<const TVariants extends Variants<Decoder<any> | Encoder<any>>>(\n    variants: TVariants,\n    discriminatorValue: DiscriminatorValue,\n) {\n    const discriminator = variants.findIndex(([key]) => discriminatorValue === key);\n    if (discriminator < 0) {\n        throw new SolanaError(SOLANA_ERROR__CODECS__INVALID_DISCRIMINATED_UNION_VARIANT, {\n            value: discriminatorValue,\n            variants: variants.map(([key]) => key),\n        });\n    }\n    return discriminator;\n}\n\n/** @deprecated Use `getDiscriminatedUnionEncoder` instead. */\nexport const getDataEnumEncoder = getDiscriminatedUnionEncoder;\n\n/** @deprecated Use `getDiscriminatedUnionDecoder` instead. */\nexport const getDataEnumDecoder = getDiscriminatedUnionDecoder;\n\n/** @deprecated Use `getDiscriminatedUnionCodec` instead. */\nexport const getDataEnumCodec = getDiscriminatedUnionCodec;\n", "/**\n * Defines the \"lookup object\" of an enum.\n *\n * @example\n * ```ts\n * enum Direction { Left, Right };\n * ```\n */\nexport type EnumLookupObject = { [key: string]: number | string };\n\n/**\n * Returns the allowed input for an enum.\n *\n * @example\n * ```ts\n * enum Direction { Left, Right };\n * type DirectionInput = GetEnumFrom<Direction>; // \"Left\" | \"Right\" | 0 | 1\n * ```\n */\nexport type GetEnumFrom<TEnum extends EnumLookupObject> = TEnum[keyof TEnum] | keyof TEnum;\n\n/**\n * Returns all the available variants of an enum.\n *\n * @example\n * ```ts\n * enum Direction { Left, Right };\n * type DirectionOutput = GetEnumTo<Direction>; // 0 | 1\n * ```\n */\nexport type GetEnumTo<TEnum extends EnumLookupObject> = TEnum[keyof TEnum];\n\nexport function getEnumStats(constructor: EnumLookupObject) {\n    const numericalValues = [...new Set(Object.values(constructor).filter(v => typeof v === 'number'))].sort();\n    const enumRecord = Object.fromEntries(Object.entries(constructor).slice(numericalValues.length)) as Record<\n        string,\n        number | string\n    >;\n    const enumKeys = Object.keys(enumRecord);\n    const enumValues = Object.values(enumRecord);\n    const stringValues: string[] = [\n        ...new Set([...enumKeys, ...enumValues.filter((v): v is string => typeof v === 'string')]),\n    ];\n\n    return { enumKeys, enumRecord, enumValues, numericalValues, stringValues };\n}\n\nexport function getEnumIndexFromVariant({\n    enumKeys,\n    enumValues,\n    variant,\n}: {\n    enumKeys: string[];\n    enumValues: (number | string)[];\n    variant: number | string | symbol;\n}): number {\n    const valueIndex = findLastIndex(enumValues, value => value === variant);\n    if (valueIndex >= 0) return valueIndex;\n    return enumKeys.findIndex(key => key === variant);\n}\n\nexport function getEnumIndexFromDiscriminator({\n    discriminator,\n    enumKeys,\n    enumValues,\n    useValuesAsDiscriminators,\n}: {\n    discriminator: number;\n    enumKeys: string[];\n    enumValues: (number | string)[];\n    useValuesAsDiscriminators: boolean;\n}): number {\n    if (!useValuesAsDiscriminators) {\n        return discriminator >= 0 && discriminator < enumKeys.length ? discriminator : -1;\n    }\n    return findLastIndex(enumValues, value => value === discriminator);\n}\n\nfunction findLastIndex<T>(array: Array<T>, predicate: (value: T, index: number, obj: T[]) => boolean): number {\n    let l = array.length;\n    while (l--) {\n        if (predicate(array[l], l, array)) return l;\n    }\n    return -1;\n}\n\nexport function formatNumericalValues(values: number[]): string {\n    if (values.length === 0) return '';\n    let range: [number, number] = [values[0], values[0]];\n    const ranges: string[] = [];\n    for (let index = 1; index < values.length; index++) {\n        const value = values[index];\n        if (range[1] + 1 === value) {\n            range[1] = value;\n        } else {\n            ranges.push(range[0] === range[1] ? `${range[0]}` : `${range[0]}-${range[1]}`);\n            range = [value, value];\n        }\n    }\n    ranges.push(range[0] === range[1] ? `${range[0]}` : `${range[0]}-${range[1]}`);\n    return ranges.join(', ');\n}\n", "import {\n    Codec,\n    combineCodec,\n    Decoder,\n    Encoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    transformDecoder,\n    transformEncoder,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\nimport {\n    FixedSizeNumberCodec,\n    FixedSizeNumberDecoder,\n    FixedSizeNumberEncoder,\n    getU8Decoder,\n    getU8Encoder,\n    NumberCodec,\n    NumberDecoder,\n    NumberEncoder,\n} from '@solana/codecs-numbers';\nimport {\n    SOLANA_ERROR__CODECS__CANNOT_USE_LEXICAL_VALUES_AS_ENUM_DISCRIMINATORS,\n    SOLANA_ERROR__CODECS__ENUM_DISCRIMINATOR_OUT_OF_RANGE,\n    SOLANA_ERROR__CODECS__INVALID_ENUM_VARIANT,\n    SolanaError,\n} from '@solana/errors';\n\nimport {\n    EnumLookupObject,\n    formatNumericalValues,\n    GetEnumFrom,\n    getEnumIndexFromDiscriminator,\n    getEnumIndexFromVariant,\n    getEnumStats,\n    GetEnumTo,\n} from './enum-helpers';\n\n/**\n * Defines the configuration options for enum codecs.\n *\n * The `size` option determines the numerical encoding used for the enum's discriminant.\n * By default, enums are stored as a `u8` (1 byte).\n *\n * The `useValuesAsDiscriminators` option allows mapping the actual enum values\n * as discriminators instead of using their positional index.\n *\n * @typeParam TDiscriminator - A number codec, encoder, or decoder used for the discriminant.\n */\nexport type EnumCodecConfig<TDiscriminator extends NumberCodec | NumberDecoder | NumberEncoder> = {\n    /**\n     * The codec used to encode/decode the enum discriminator.\n     * @defaultValue `u8` discriminator.\n     */\n    size?: TDiscriminator;\n\n    /**\n     * If set to `true`, the enum values themselves will be used as discriminators.\n     * This is only valid for numerical enum values.\n     *\n     * @defaultValue `false`\n     */\n    useValuesAsDiscriminators?: boolean;\n};\n\n/**\n * Returns an encoder for enums.\n *\n * This encoder serializes enums as a numerical discriminator.\n * By default, the discriminator is based on the positional index of the enum variants.\n *\n * For more details, see {@link getEnumCodec}.\n *\n * @typeParam TEnum - The TypeScript enum or object mapping enum keys to values.\n *\n * @param constructor - The constructor of the enum.\n * @param config - Configuration options for encoding the enum.\n * @returns A `FixedSizeEncoder` or `VariableSizeEncoder` for encoding enums.\n *\n * @example\n * Encoding enum values.\n * ```ts\n * enum Direction { Up,  Down, Left, Right }\n * const encoder = getEnumEncoder(Direction);\n *\n * encoder.encode(Direction.Up);    // 0x00\n * encoder.encode(Direction.Down);  // 0x01\n * encoder.encode(Direction.Left);  // 0x02\n * encoder.encode(Direction.Right); // 0x03\n * ```\n *\n * @see {@link getEnumCodec}\n */\nexport function getEnumEncoder<TEnum extends EnumLookupObject>(\n    constructor: TEnum,\n    config?: Omit<EnumCodecConfig<NumberEncoder>, 'size'>,\n): FixedSizeEncoder<GetEnumFrom<TEnum>, 1>;\nexport function getEnumEncoder<TEnum extends EnumLookupObject, TSize extends number>(\n    constructor: TEnum,\n    config: EnumCodecConfig<NumberEncoder> & { size: FixedSizeNumberEncoder<TSize> },\n): FixedSizeEncoder<GetEnumFrom<TEnum>, TSize>;\nexport function getEnumEncoder<TEnum extends EnumLookupObject>(\n    constructor: TEnum,\n    config?: EnumCodecConfig<NumberEncoder>,\n): VariableSizeEncoder<GetEnumFrom<TEnum>>;\nexport function getEnumEncoder<TEnum extends EnumLookupObject>(\n    constructor: TEnum,\n    config: EnumCodecConfig<NumberEncoder> = {},\n): Encoder<GetEnumFrom<TEnum>> {\n    const prefix = config.size ?? getU8Encoder();\n    const useValuesAsDiscriminators = config.useValuesAsDiscriminators ?? false;\n    const { enumKeys, enumValues, numericalValues, stringValues } = getEnumStats(constructor);\n    if (useValuesAsDiscriminators && enumValues.some(value => typeof value === 'string')) {\n        throw new SolanaError(SOLANA_ERROR__CODECS__CANNOT_USE_LEXICAL_VALUES_AS_ENUM_DISCRIMINATORS, {\n            stringValues: enumValues.filter((v): v is string => typeof v === 'string'),\n        });\n    }\n    return transformEncoder(prefix, (variant: GetEnumFrom<TEnum>): number => {\n        const index = getEnumIndexFromVariant({ enumKeys, enumValues, variant });\n        if (index < 0) {\n            throw new SolanaError(SOLANA_ERROR__CODECS__INVALID_ENUM_VARIANT, {\n                formattedNumericalValues: formatNumericalValues(numericalValues),\n                numericalValues,\n                stringValues,\n                variant,\n            });\n        }\n        return useValuesAsDiscriminators ? (enumValues[index] as number) : index;\n    });\n}\n\n/**\n * Returns a decoder for enums.\n *\n * This decoder deserializes enums from a numerical discriminator.\n * By default, the discriminator is based on the positional index of the enum variants.\n *\n * For more details, see {@link getEnumCodec}.\n *\n * @typeParam TEnum - The TypeScript enum or object mapping enum keys to values.\n *\n * @param constructor - The constructor of the enum.\n * @param config - Configuration options for decoding the enum.\n * @returns A `FixedSizeDecoder` or `VariableSizeDecoder` for decoding enums.\n *\n * @example\n * Decoding enum values.\n * ```ts\n * enum Direction { Up,  Down, Left, Right }\n * const decoder = getEnumDecoder(Direction);\n *\n * decoder.decode(new Uint8Array([0x00])); // Direction.Up\n * decoder.decode(new Uint8Array([0x01])); // Direction.Down\n * decoder.decode(new Uint8Array([0x02])); // Direction.Left\n * decoder.decode(new Uint8Array([0x03])); // Direction.Right\n * ```\n *\n * @see {@link getEnumCodec}\n */\nexport function getEnumDecoder<TEnum extends EnumLookupObject>(\n    constructor: TEnum,\n    config?: Omit<EnumCodecConfig<NumberDecoder>, 'size'>,\n): FixedSizeDecoder<GetEnumTo<TEnum>, 1>;\nexport function getEnumDecoder<TEnum extends EnumLookupObject, TSize extends number>(\n    constructor: TEnum,\n    config: EnumCodecConfig<NumberDecoder> & { size: FixedSizeNumberDecoder<TSize> },\n): FixedSizeDecoder<GetEnumTo<TEnum>, TSize>;\nexport function getEnumDecoder<TEnum extends EnumLookupObject>(\n    constructor: TEnum,\n    config?: EnumCodecConfig<NumberDecoder>,\n): VariableSizeDecoder<GetEnumTo<TEnum>>;\nexport function getEnumDecoder<TEnum extends EnumLookupObject>(\n    constructor: TEnum,\n    config: EnumCodecConfig<NumberDecoder> = {},\n): Decoder<GetEnumTo<TEnum>> {\n    const prefix = config.size ?? getU8Decoder();\n    const useValuesAsDiscriminators = config.useValuesAsDiscriminators ?? false;\n    const { enumKeys, enumValues, numericalValues } = getEnumStats(constructor);\n    if (useValuesAsDiscriminators && enumValues.some(value => typeof value === 'string')) {\n        throw new SolanaError(SOLANA_ERROR__CODECS__CANNOT_USE_LEXICAL_VALUES_AS_ENUM_DISCRIMINATORS, {\n            stringValues: enumValues.filter((v): v is string => typeof v === 'string'),\n        });\n    }\n    return transformDecoder(prefix, (value: bigint | number): GetEnumTo<TEnum> => {\n        const discriminator = Number(value);\n        const index = getEnumIndexFromDiscriminator({\n            discriminator,\n            enumKeys,\n            enumValues,\n            useValuesAsDiscriminators,\n        });\n        if (index < 0) {\n            const validDiscriminators = useValuesAsDiscriminators\n                ? numericalValues\n                : [...Array(enumKeys.length).keys()];\n            throw new SolanaError(SOLANA_ERROR__CODECS__ENUM_DISCRIMINATOR_OUT_OF_RANGE, {\n                discriminator,\n                formattedValidDiscriminators: formatNumericalValues(validDiscriminators),\n                validDiscriminators,\n            });\n        }\n        return enumValues[index] as GetEnumTo<TEnum>;\n    });\n}\n\n/**\n * Returns a codec for encoding and decoding enums.\n *\n * This codec serializes enums as a numerical discriminator, allowing them\n * to be efficiently stored and reconstructed from binary data.\n *\n * By default, the discriminator is derived from the positional index\n * of the enum variant, but it can be configured to use the enum's numeric values instead.\n *\n * @typeParam TEnum - The TypeScript enum or object mapping enum keys to values.\n *\n * @param constructor - The constructor of the enum.\n * @param config - Configuration options for encoding and decoding the enum.\n * @returns A `FixedSizeCodec` or `VariableSizeCodec` for encoding and decoding enums.\n *\n * @example\n * Encoding and decoding enums using positional indexes.\n * ```ts\n * enum Direction { Up, Down, Left, Right }\n * const codec = getEnumCodec(Direction);\n *\n * codec.encode(Direction.Up);    // 0x00\n * codec.encode(Direction.Down);  // 0x01\n * codec.encode(Direction.Left);  // 0x02\n * codec.encode(Direction.Right); // 0x03\n *\n * codec.decode(new Uint8Array([0x00])); // Direction.Up\n * codec.decode(new Uint8Array([0x01])); // Direction.Down\n * codec.decode(new Uint8Array([0x02])); // Direction.Left\n * codec.decode(new Uint8Array([0x03])); // Direction.Right\n * ```\n *\n * @example\n * Encoding and decoding enums using their numeric values.\n * ```ts\n * enum GameDifficulty { Easy = 1, Normal = 4, Hard = 7, Expert = 9 }\n * const codec = getEnumCodec(GameDifficulty, { useValuesAsDiscriminators: true });\n *\n * codec.encode(GameDifficulty.Easy);   // 0x01\n * codec.encode(GameDifficulty.Normal); // 0x04\n * codec.encode(GameDifficulty.Hard);   // 0x07\n * codec.encode(GameDifficulty.Expert); // 0x09\n *\n * codec.decode(new Uint8Array([0x01])); // GameDifficulty.Easy\n * codec.decode(new Uint8Array([0x04])); // GameDifficulty.Normal\n * codec.decode(new Uint8Array([0x07])); // GameDifficulty.Hard\n * codec.decode(new Uint8Array([0x09])); // GameDifficulty.Expert\n * ```\n *\n * Note that, when using values as discriminators, the enum values must be numerical.\n * Otherwise, an error will be thrown.\n *\n * ```ts\n * enum GameDifficulty { Easy = 'EASY', Normal = 'NORMAL', Hard = 'HARD' }\n * getEnumCodec(GameDifficulty, { useValuesAsDiscriminators: true }); // Throws an error.\n * ```\n *\n * @example\n * Using a custom discriminator size.\n * ```ts\n * enum Status { Pending, Approved, Rejected }\n * const codec = getEnumCodec(Status, { size: getU16Codec() });\n *\n * codec.encode(Status.Pending);  // 0x0000\n * codec.encode(Status.Approved); // 0x0100\n * codec.encode(Status.Rejected); // 0x0200\n *\n * codec.decode(new Uint8Array([0x00, 0x00])); // Status.Pending\n * codec.decode(new Uint8Array([0x01, 0x00])); // Status.Approved\n * codec.decode(new Uint8Array([0x02, 0x00])); // Status.Rejected\n * ```\n *\n * @remarks\n * Separate {@link getEnumEncoder} and {@link getEnumDecoder} functions are available.\n *\n * ```ts\n * const bytes = getEnumEncoder(Direction).encode(Direction.Up);\n * const value = getEnumDecoder(Direction).decode(bytes);\n * ```\n *\n * @see {@link getEnumEncoder}\n * @see {@link getEnumDecoder}\n */\nexport function getEnumCodec<TEnum extends EnumLookupObject>(\n    constructor: TEnum,\n    config?: Omit<EnumCodecConfig<NumberCodec>, 'size'>,\n): FixedSizeCodec<GetEnumFrom<TEnum>, GetEnumTo<TEnum>, 1>;\nexport function getEnumCodec<TEnum extends EnumLookupObject, TSize extends number>(\n    constructor: TEnum,\n    config: EnumCodecConfig<NumberCodec> & { size: FixedSizeNumberCodec<TSize> },\n): FixedSizeCodec<GetEnumFrom<TEnum>, GetEnumTo<TEnum>, TSize>;\nexport function getEnumCodec<TEnum extends EnumLookupObject>(\n    constructor: TEnum,\n    config?: EnumCodecConfig<NumberCodec>,\n): VariableSizeCodec<GetEnumFrom<TEnum>, GetEnumTo<TEnum>>;\nexport function getEnumCodec<TEnum extends EnumLookupObject>(\n    constructor: TEnum,\n    config: EnumCodecConfig<NumberCodec> = {},\n): Codec<GetEnumFrom<TEnum>, GetEnumTo<TEnum>> {\n    return combineCodec(getEnumEncoder(constructor, config), getEnumDecoder(constructor, config));\n}\n\n/** @deprecated Use `getEnumEncoder` instead. */\nexport const getScalarEnumEncoder = getEnumEncoder;\n\n/** @deprecated Use `getEnumDecoder` instead. */\nexport const getScalarEnumDecoder = getEnumDecoder;\n\n/** @deprecated Use `getEnumCodec` instead. */\nexport const getScalarEnumCodec = getEnumCodec;\n", "import {\n    Codec,\n    combineCodec,\n    Decoder,\n    Encoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    transformDecoder,\n    transformEncoder,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\n\nimport { getTupleDecoder, getTupleEncoder } from './tuple';\n\n/**\n * Returns an encoder that prefixes encoded values with hidden data.\n *\n * This encoder applies a list of void encoders before encoding the main value.\n * The prefixed data is encoded before the main value without being exposed to the user.\n *\n * For more details, see {@link getHiddenPrefixCodec}.\n *\n * @typeParam TFrom - The type of the main value being encoded.\n *\n * @param encoder - The encoder for the main value.\n * @param prefixedEncoders - A list of void encoders that produce the hidden prefix.\n * @returns A `FixedSizeEncoder` or `VariableSizeEncoder` that encodes the value with a hidden prefix.\n *\n * @example\n * Prefixing a value with constants.\n * ```ts\n * const encoder = getHiddenPrefixEncoder(getUtf8Encoder(), [\n *   getConstantCodec(new Uint8Array([1, 2, 3])),\n *   getConstantCodec(new Uint8Array([4, 5, 6])),\n * ]);\n *\n * encoder.encode('Hello');\n * // 0x01020304050648656c6c6f\n * //   |     |     └-- Our encoded value (\"Hello\").\n * //   |     └-- Our second hidden prefix.\n * //   └-- Our first hidden prefix.\n * ```\n *\n * @see {@link getHiddenPrefixCodec}\n */\nexport function getHiddenPrefixEncoder<TFrom>(\n    encoder: FixedSizeEncoder<TFrom>,\n    prefixedEncoders: readonly FixedSizeEncoder<void>[],\n): FixedSizeEncoder<TFrom>;\nexport function getHiddenPrefixEncoder<TFrom>(\n    encoder: Encoder<TFrom>,\n    prefixedEncoders: readonly Encoder<void>[],\n): VariableSizeEncoder<TFrom>;\nexport function getHiddenPrefixEncoder<TFrom>(\n    encoder: Encoder<TFrom>,\n    prefixedEncoders: readonly Encoder<void>[],\n): Encoder<TFrom> {\n    return transformEncoder(\n        getTupleEncoder([...prefixedEncoders, encoder]) as Encoder<readonly [...void[], TFrom]>,\n        (value: TFrom) => [...prefixedEncoders.map(() => undefined), value] as const,\n    );\n}\n\n/**\n * Returns a decoder that skips hidden prefixed data before decoding the main value.\n *\n * This decoder applies a list of void decoders before decoding the main value.\n * The prefixed data is skipped during decoding without being exposed to the user.\n *\n * For more details, see {@link getHiddenPrefixCodec}.\n *\n * @typeParam TTo - The type of the main value being decoded.\n *\n * @param decoder - The decoder for the main value.\n * @param prefixedDecoders - A list of void decoders that produce the hidden prefix.\n * @returns A `FixedSizeDecoder` or `VariableSizeDecoder` that decodes values while ignoring the hidden prefix.\n *\n * @example\n * Decoding a value with prefixed constants.\n * ```ts\n * const decoder = getHiddenPrefixDecoder(getUtf8Decoder(), [\n *   getConstantCodec(new Uint8Array([1, 2, 3])),\n *   getConstantCodec(new Uint8Array([4, 5, 6])),\n * ]);\n *\n * decoder.decode(new Uint8Array([1, 2, 3, 4, 5, 6, 0x48, 0x65, 0x6C, 0x6C, 0x6F]));\n * // 'Hello'\n * ```\n *\n * @see {@link getHiddenPrefixCodec}\n */\nexport function getHiddenPrefixDecoder<TTo>(\n    decoder: FixedSizeDecoder<TTo>,\n    prefixedDecoders: readonly FixedSizeDecoder<void>[],\n): FixedSizeDecoder<TTo>;\nexport function getHiddenPrefixDecoder<TTo>(\n    decoder: Decoder<TTo>,\n    prefixedDecoders: readonly Decoder<void>[],\n): VariableSizeDecoder<TTo>;\nexport function getHiddenPrefixDecoder<TTo>(\n    decoder: Decoder<TTo>,\n    prefixedDecoders: readonly Decoder<void>[],\n): Decoder<TTo> {\n    return transformDecoder(\n        getTupleDecoder([...prefixedDecoders, decoder]) as Decoder<readonly [...void[], TTo]>,\n        tuple => tuple[tuple.length - 1] as TTo,\n    );\n}\n\n/**\n * Returns a codec that encodes and decodes values with a hidden prefix.\n *\n * - **Encoding:** Prefixes the value with hidden data before encoding.\n * - **Decoding:** Skips the hidden prefix before decoding the main value.\n *\n * This is useful for any implicit metadata that should be present in\n * binary formats but omitted from the API.\n *\n * @typeParam TFrom - The type of the main value being encoded.\n * @typeParam TTo - The type of the main value being decoded.\n *\n * @param codec - The codec for the main value.\n * @param prefixedCodecs - A list of void codecs that produce the hidden prefix.\n * @returns A `FixedSizeCodec` or `VariableSizeCodec` for encoding and decoding values with a hidden prefix.\n *\n * @example\n * Encoding and decoding a value with prefixed constants.\n * ```ts\n * const codec = getHiddenPrefixCodec(getUtf8Codec(), [\n *   getConstantCodec(new Uint8Array([1, 2, 3])),\n *   getConstantCodec(new Uint8Array([4, 5, 6])),\n * ]);\n *\n * const bytes = codec.encode('Hello');\n * // 0x01020304050648656c6c6f\n * //   |     |     └-- Our encoded value (\"Hello\").\n * //   |     └-- Our second hidden prefix.\n * //   └-- Our first hidden prefix.\n *\n * codec.decode(bytes);\n * // 'Hello'\n * ```\n *\n * @remarks\n * If all you need is padding zeroes before a value, consider using {@link padLeftCodec} instead.\n *\n * Separate {@link getHiddenPrefixEncoder} and {@link getHiddenPrefixDecoder} functions are available.\n *\n * ```ts\n * const bytes = getHiddenPrefixEncoder(getUtf8Encoder(), [\n *   getConstantEncoder(new Uint8Array([1, 2, 3])),\n *   getConstantEncoder(new Uint8Array([4, 5, 6])),\n * ]).encode('Hello');\n *\n * const value = getHiddenPrefixDecoder(getUtf8Decoder(), [\n *   getConstantDecoder(new Uint8Array([1, 2, 3])),\n *   getConstantDecoder(new Uint8Array([4, 5, 6])),\n * ]).decode(bytes);\n * ```\n *\n * @see {@link getHiddenPrefixEncoder}\n * @see {@link getHiddenPrefixDecoder}\n */\nexport function getHiddenPrefixCodec<TFrom, TTo extends TFrom>(\n    codec: FixedSizeCodec<TFrom, TTo>,\n    prefixedCodecs: readonly FixedSizeCodec<void>[],\n): FixedSizeCodec<TFrom, TTo>;\nexport function getHiddenPrefixCodec<TFrom, TTo extends TFrom>(\n    codec: Codec<TFrom, TTo>,\n    prefixedCodecs: readonly Codec<void>[],\n): VariableSizeCodec<TFrom, TTo>;\nexport function getHiddenPrefixCodec<TFrom, TTo extends TFrom>(\n    codec: Codec<TFrom, TTo>,\n    prefixedCodecs: readonly Codec<void>[],\n): Codec<TFrom, TTo> {\n    return combineCodec(getHiddenPrefixEncoder(codec, prefixedCodecs), getHiddenPrefixDecoder(codec, prefixedCodecs));\n}\n", "import {\n    Codec,\n    combineCodec,\n    Decoder,\n    Encoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    transformDecoder,\n    transformEncoder,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\n\nimport { getTupleDecoder, getTupleEncoder } from './tuple';\n\n/**\n * Returns an encoder that appends hidden data after the encoded value.\n *\n * This encoder applies a list of void encoders after encoding the main value.\n * The suffixed data is encoded after the main value without being exposed to the user.\n *\n * For more details, see {@link getHiddenSuffixCodec}.\n *\n * @typeParam TFrom - The type of the main value being encoded.\n *\n * @param encoder - The encoder for the main value.\n * @param suffixedEncoders - A list of void encoders that produce the hidden suffix.\n * @returns A `FixedSizeEncoder` or `VariableSizeEncoder` that encodes the value with a hidden suffix.\n *\n * @example\n * Suffixing a value with constants.\n * ```ts\n * const encoder = getHiddenSuffixEncoder(getUtf8Encoder(), [\n *   getConstantCodec(new Uint8Array([1, 2, 3])),\n *   getConstantCodec(new Uint8Array([4, 5, 6])),\n * ]);\n *\n * encoder.encode('Hello');\n * // 0x48656c6c6f010203040506\n * //   |         |     └-- Our second hidden suffix.\n * //   |         └-- Our first hidden suffix.\n * //   └-- Our encoded value (\"Hello\").\n * ```\n *\n * @see {@link getHiddenSuffixCodec}\n */\nexport function getHiddenSuffixEncoder<TFrom>(\n    encoder: FixedSizeEncoder<TFrom>,\n    suffixedEncoders: readonly FixedSizeEncoder<void>[],\n): FixedSizeEncoder<TFrom>;\nexport function getHiddenSuffixEncoder<TFrom>(\n    encoder: Encoder<TFrom>,\n    suffixedEncoders: readonly Encoder<void>[],\n): VariableSizeEncoder<TFrom>;\nexport function getHiddenSuffixEncoder<TFrom>(\n    encoder: Encoder<TFrom>,\n    suffixedEncoders: readonly Encoder<void>[],\n): Encoder<TFrom> {\n    return transformEncoder(\n        getTupleEncoder([encoder, ...suffixedEncoders]) as Encoder<readonly [TFrom, ...void[]]>,\n        (value: TFrom) => [value, ...suffixedEncoders.map(() => undefined)] as const,\n    );\n}\n\n/**\n * Returns a decoder that skips hidden suffixed data after decoding the main value.\n *\n * This decoder applies a list of void decoders after decoding the main value.\n * The suffixed data is skipped during decoding without being exposed to the user.\n *\n * For more details, see {@link getHiddenSuffixCodec}.\n *\n * @typeParam TTo - The type of the main value being decoded.\n *\n * @param decoder - The decoder for the main value.\n * @param suffixedDecoders - A list of void decoders that produce the hidden suffix.\n * @returns A `FixedSizeDecoder` or `VariableSizeDecoder` that decodes values while ignoring the hidden suffix.\n *\n * @example\n * Decoding a value with suffixed constants.\n * ```ts\n * const decoder = getHiddenSuffixDecoder(getUtf8Decoder(), [\n *   getConstantCodec(new Uint8Array([1, 2, 3])),\n *   getConstantCodec(new Uint8Array([4, 5, 6])),\n * ]);\n *\n * decoder.decode(new Uint8Array([0x48, 0x65, 0x6C, 0x6C, 0x6F, 1, 2, 3, 4, 5, 6]));\n * // 'Hello'\n * ```\n *\n * @see {@link getHiddenSuffixCodec}\n */\nexport function getHiddenSuffixDecoder<TTo>(\n    decoder: FixedSizeDecoder<TTo>,\n    suffixedDecoders: readonly FixedSizeDecoder<void>[],\n): FixedSizeDecoder<TTo>;\nexport function getHiddenSuffixDecoder<TTo>(\n    decoder: Decoder<TTo>,\n    suffixedDecoders: readonly Decoder<void>[],\n): VariableSizeDecoder<TTo>;\nexport function getHiddenSuffixDecoder<TTo>(\n    decoder: Decoder<TTo>,\n    suffixedDecoders: readonly Decoder<void>[],\n): Decoder<TTo> {\n    return transformDecoder(\n        getTupleDecoder([decoder, ...suffixedDecoders]) as Decoder<readonly [TTo, ...void[]]>,\n        tuple => tuple[0],\n    );\n}\n\n/**\n * Returns a codec that encodes and decodes values with a hidden suffix.\n *\n * - **Encoding:** Appends hidden data after encoding the main value.\n * - **Decoding:** Skips the hidden suffix after decoding the main value.\n *\n * This is useful for any implicit metadata that should be present in\n * binary formats but omitted from the API.\n *\n * @typeParam TFrom - The type of the main value being encoded.\n * @typeParam TTo - The type of the main value being decoded.\n *\n * @param codec - The codec for the main value.\n * @param suffixedCodecs - A list of void codecs that produce the hidden suffix.\n * @returns A `FixedSizeCodec` or `VariableSizeCodec` for encoding and decoding values with a hidden suffix.\n *\n * @example\n * Encoding and decoding a value with suffixed constants.\n * ```ts\n * const codec = getHiddenSuffixCodec(getUtf8Codec(), [\n *   getConstantCodec(new Uint8Array([1, 2, 3])),\n *   getConstantCodec(new Uint8Array([4, 5, 6])),\n * ]);\n *\n * const bytes = codec.encode('Hello');\n * // 0x48656c6c6f010203040506\n * //   |         |     └-- Our second hidden suffix.\n * //   |         └-- Our first hidden suffix.\n * //   └-- Our encoded value (\"Hello\").\n *\n * codec.decode(bytes);\n * // 'Hello'\n * ```\n *\n * @remarks\n * If all you need is padding zeroes after a value, consider using {@link padRightCodec} instead.\n *\n * Separate {@link getHiddenSuffixEncoder} and {@link getHiddenSuffixDecoder} functions are available.\n *\n * ```ts\n * const bytes = getHiddenSuffixEncoder(getUtf8Encoder(), [\n *   getConstantEncoder(new Uint8Array([1, 2, 3])),\n *   getConstantEncoder(new Uint8Array([4, 5, 6])),\n * ]).encode('Hello');\n *\n * const value = getHiddenSuffixDecoder(getUtf8Decoder(), [\n *   getConstantDecoder(new Uint8Array([1, 2, 3])),\n *   getConstantDecoder(new Uint8Array([4, 5, 6])),\n * ]).decode(bytes);\n * ```\n *\n * @see {@link getHiddenSuffixEncoder}\n * @see {@link getHiddenSuffixDecoder}\n */\nexport function getHiddenSuffixCodec<TFrom, TTo extends TFrom>(\n    codec: FixedSizeCodec<TFrom, TTo>,\n    suffixedCodecs: readonly FixedSizeCodec<void>[],\n): FixedSizeCodec<TFrom, TTo>;\nexport function getHiddenSuffixCodec<TFrom, TTo extends TFrom>(\n    codec: Codec<TFrom, TTo>,\n    suffixedCodecs: readonly Codec<void>[],\n): VariableSizeCodec<TFrom, TTo>;\nexport function getHiddenSuffixCodec<TFrom, TTo extends TFrom>(\n    codec: Codec<TFrom, TTo>,\n    suffixedCodecs: readonly Codec<void>[],\n): Codec<TFrom, TTo> {\n    return combineCodec(getHiddenSuffixEncoder(codec, suffixedCodecs), getHiddenSuffixDecoder(codec, suffixedCodecs));\n}\n", "import {\n    Codec,\n    combineCodec,\n    Decoder,\n    Encoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    transformDecoder,\n    transformEncoder,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\nimport {\n    FixedSizeNumberCodec,\n    FixedSizeNumberDecoder,\n    FixedSizeNumberEncoder,\n    getU8Decoder,\n    getU8Encoder,\n    NumberCodec,\n    NumberDecoder,\n    NumberEncoder,\n} from '@solana/codecs-numbers';\nimport {\n    SOLANA_ERROR__CODECS__INVALID_LITERAL_UNION_VARIANT,\n    SOLANA_ERROR__CODECS__LITERAL_UNION_DISCRIMINATOR_OUT_OF_RANGE,\n    SolanaError,\n} from '@solana/errors';\n\n/**\n * Defines the configuration options for literal union codecs.\n *\n * A literal union codec encodes values from a predefined set of literals.\n * The `size` option determines the numerical encoding used for the discriminant.\n * By default, literals are stored as a `u8` (1 byte).\n *\n * @typeParam TDiscriminator - A number codec, encoder, or decoder used for the discriminant.\n */\nexport type LiteralUnionCodecConfig<TDiscriminator = NumberCodec | NumberDecoder | NumberEncoder> = {\n    /**\n     * The codec used to encode/decode the discriminator.\n     * @defaultValue `u8` discriminator.\n     */\n    size?: TDiscriminator;\n};\n\ntype Variant = bigint | boolean | number | string | null | undefined;\ntype GetTypeFromVariants<TVariants extends readonly Variant[]> = TVariants[number];\n\n/**\n * Returns an encoder for literal unions.\n *\n * This encoder serializes a value from a predefined set of literals\n * as a numerical index representing its position in the `variants` array.\n *\n * For more details, see {@link getLiteralUnionCodec}.\n *\n * @typeParam TVariants - A tuple of allowed literal values.\n *\n * @param variants - The possible literal values for the union.\n * @param config - Configuration options for encoding the literal union.\n * @returns A `FixedSizeEncoder` or `VariableSizeEncoder` for encoding literal unions.\n *\n * @example\n * Encoding a union of string literals.\n * ```ts\n * type Size = 'small' | 'medium' | 'large';\n * const sizeEncoder = getLiteralUnionEncoder(['small', 'medium', 'large']);\n *\n * sizeEncoder.encode('small');  // 0x00\n * sizeEncoder.encode('medium'); // 0x01\n * sizeEncoder.encode('large');  // 0x02\n * ```\n *\n * @see {@link getLiteralUnionCodec}\n */\nexport function getLiteralUnionEncoder<const TVariants extends readonly Variant[]>(\n    variants: TVariants,\n): FixedSizeEncoder<GetTypeFromVariants<TVariants>, 1>;\nexport function getLiteralUnionEncoder<const TVariants extends readonly Variant[], TSize extends number>(\n    variants: TVariants,\n    config: LiteralUnionCodecConfig<NumberEncoder> & { size: FixedSizeNumberEncoder<TSize> },\n): FixedSizeEncoder<GetTypeFromVariants<TVariants>, TSize>;\nexport function getLiteralUnionEncoder<const TVariants extends readonly Variant[]>(\n    variants: TVariants,\n    config?: LiteralUnionCodecConfig<NumberEncoder>,\n): VariableSizeEncoder<GetTypeFromVariants<TVariants>>;\nexport function getLiteralUnionEncoder<const TVariants extends readonly Variant[]>(\n    variants: TVariants,\n    config: LiteralUnionCodecConfig<NumberEncoder> = {},\n): Encoder<GetTypeFromVariants<TVariants>> {\n    const discriminator = config.size ?? getU8Encoder();\n    return transformEncoder(discriminator, variant => {\n        const index = variants.indexOf(variant);\n        if (index < 0) {\n            throw new SolanaError(SOLANA_ERROR__CODECS__INVALID_LITERAL_UNION_VARIANT, {\n                value: variant,\n                variants,\n            });\n        }\n        return index;\n    });\n}\n\n/**\n * Returns a decoder for literal unions.\n *\n * This decoder deserializes a numerical index into a corresponding\n * value from a predefined set of literals.\n *\n * For more details, see {@link getLiteralUnionCodec}.\n *\n * @typeParam TVariants - A tuple of allowed literal values.\n *\n * @param variants - The possible literal values for the union.\n * @param config - Configuration options for decoding the literal union.\n * @returns A `FixedSizeDecoder` or `VariableSizeDecoder` for decoding literal unions.\n *\n * @example\n * Decoding a union of string literals.\n * ```ts\n * type Size = 'small' | 'medium' | 'large';\n * const sizeDecoder = getLiteralUnionDecoder(['small', 'medium', 'large']);\n *\n * sizeDecoder.decode(new Uint8Array([0x00])); // 'small'\n * sizeDecoder.decode(new Uint8Array([0x01])); // 'medium'\n * sizeDecoder.decode(new Uint8Array([0x02])); // 'large'\n * ```\n *\n * @see {@link getLiteralUnionCodec}\n */\nexport function getLiteralUnionDecoder<const TVariants extends readonly Variant[]>(\n    variants: TVariants,\n): FixedSizeDecoder<GetTypeFromVariants<TVariants>, 1>;\nexport function getLiteralUnionDecoder<const TVariants extends readonly Variant[], TSize extends number>(\n    variants: TVariants,\n    config: LiteralUnionCodecConfig<NumberDecoder> & { size: FixedSizeNumberDecoder<TSize> },\n): FixedSizeDecoder<GetTypeFromVariants<TVariants>, TSize>;\nexport function getLiteralUnionDecoder<const TVariants extends readonly Variant[]>(\n    variants: TVariants,\n    config?: LiteralUnionCodecConfig<NumberDecoder>,\n): VariableSizeDecoder<GetTypeFromVariants<TVariants>>;\nexport function getLiteralUnionDecoder<const TVariants extends readonly Variant[]>(\n    variants: TVariants,\n    config: LiteralUnionCodecConfig<NumberDecoder> = {},\n): Decoder<GetTypeFromVariants<TVariants>> {\n    const discriminator = config.size ?? getU8Decoder();\n    return transformDecoder(discriminator, (index: bigint | number) => {\n        if (index < 0 || index >= variants.length) {\n            throw new SolanaError(SOLANA_ERROR__CODECS__LITERAL_UNION_DISCRIMINATOR_OUT_OF_RANGE, {\n                discriminator: index,\n                maxRange: variants.length - 1,\n                minRange: 0,\n            });\n        }\n        return variants[Number(index)];\n    });\n}\n\n/**\n * Returns a codec for encoding and decoding literal unions.\n *\n * A literal union codec serializes and deserializes values\n * from a predefined set of literals, using a numerical index\n * to represent each value in the `variants` array.\n *\n * This allows efficient storage and retrieval of common\n * predefined values such as enum-like structures in TypeScript.\n *\n * @typeParam TVariants - A tuple of allowed literal values.\n *\n * @param variants - The possible literal values for the union.\n * @param config - Configuration options for encoding and decoding the literal union.\n * @returns A `FixedSizeCodec` or `VariableSizeCodec` for encoding and decoding literal unions.\n *\n * @example\n * Encoding and decoding a union of string literals.\n * ```ts\n * type Size = 'small' | 'medium' | 'large';\n * const sizeCodec = getLiteralUnionCodec(['small', 'medium', 'large']);\n *\n * sizeCodec.encode('small');  // 0x00\n * sizeCodec.encode('medium'); // 0x01\n * sizeCodec.encode('large');  // 0x02\n *\n * sizeCodec.decode(new Uint8Array([0x00])); // 'small'\n * sizeCodec.decode(new Uint8Array([0x01])); // 'medium'\n * sizeCodec.decode(new Uint8Array([0x02])); // 'large'\n * ```\n *\n * @example\n * Encoding and decoding a union of number literals.\n * ```ts\n * type Level = 10 | 20 | 30;\n * const levelCodec = getLiteralUnionCodec([10, 20, 30]);\n *\n * levelCodec.encode(10);  // 0x00\n * levelCodec.encode(20);  // 0x01\n * levelCodec.encode(30);  // 0x02\n *\n * levelCodec.decode(new Uint8Array([0x00])); // 10\n * levelCodec.decode(new Uint8Array([0x01])); // 20\n * levelCodec.decode(new Uint8Array([0x02])); // 30\n * ```\n *\n * @example\n * Using a custom discriminator size with different variant types.\n * ```ts\n * type MaybeBoolean = false | true | \"either\";\n * const codec = getLiteralUnionCodec([false, true, 'either'], { size: getU16Codec() });\n *\n * codec.encode(false);    // 0x0000\n * codec.encode(true);     // 0x0100\n * codec.encode('either'); // 0x0200\n *\n * codec.decode(new Uint8Array([0x00, 0x00])); // false\n * codec.decode(new Uint8Array([0x01, 0x00])); // true\n * codec.decode(new Uint8Array([0x02, 0x00])); // 'either'\n * ```\n *\n * @remarks\n * Separate {@link getLiteralUnionEncoder} and {@link getLiteralUnionDecoder} functions are available.\n *\n * ```ts\n * const bytes = getLiteralUnionEncoder(['red', 'green', 'blue']).encode('green');\n * const value = getLiteralUnionDecoder(['red', 'green', 'blue']).decode(bytes);\n * ```\n *\n * @see {@link getLiteralUnionEncoder}\n * @see {@link getLiteralUnionDecoder}\n */\nexport function getLiteralUnionCodec<const TVariants extends readonly Variant[]>(\n    variants: TVariants,\n): FixedSizeCodec<GetTypeFromVariants<TVariants>, GetTypeFromVariants<TVariants>, 1>;\nexport function getLiteralUnionCodec<const TVariants extends readonly Variant[], TSize extends number>(\n    variants: TVariants,\n    config: LiteralUnionCodecConfig<NumberCodec> & { size: FixedSizeNumberCodec<TSize> },\n): FixedSizeCodec<GetTypeFromVariants<TVariants>, GetTypeFromVariants<TVariants>, TSize>;\nexport function getLiteralUnionCodec<const TVariants extends readonly Variant[]>(\n    variants: TVariants,\n    config?: LiteralUnionCodecConfig<NumberCodec>,\n): VariableSizeCodec<GetTypeFromVariants<TVariants>>;\nexport function getLiteralUnionCodec<const TVariants extends readonly Variant[]>(\n    variants: TVariants,\n    config: LiteralUnionCodecConfig<NumberCodec> = {},\n): Codec<GetTypeFromVariants<TVariants>> {\n    return combineCodec(getLiteralUnionEncoder(variants, config), getLiteralUnionDecoder(variants, config));\n}\n", "import {\n    Codec,\n    combineCodec,\n    Decoder,\n    Encoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    transformDecoder,\n    transformEncoder,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\nimport { NumberCodec, NumberDecoder, NumberEncoder } from '@solana/codecs-numbers';\n\nimport { ArrayLikeCodecSize, getArrayDecoder, getArrayEncoder } from './array';\nimport { getTupleDecoder, getTupleEncoder } from './tuple';\n\n/**\n * Defines the configuration options for map codecs.\n *\n * The `size` option determines how the number of entries in the map is stored.\n * It can be:\n * - A {@link NumberCodec} to prefix the map with its size.\n * - A fixed number of entries.\n * - `'remainder'`, which infers the number of entries based on the remaining bytes.\n *   This option is only available for fixed-size keys and values.\n *\n * @typeParam TPrefix - A number codec, encoder, or decoder used for the size prefix.\n */\nexport type MapCodecConfig<TPrefix extends NumberCodec | NumberDecoder | NumberEncoder> = {\n    /**\n     * The size of the map.\n     * @defaultValue u32 prefix.\n     */\n    size?: ArrayLikeCodecSize<TPrefix>;\n};\n\n/**\n * Returns an encoder for maps.\n *\n * This encoder serializes maps where the keys and values are encoded\n * using the provided key and value encoders. The number of entries\n * is determined by the `size` configuration.\n *\n * For more details, see {@link getMapCodec}.\n *\n * @typeParam TFromKey - The type of the keys before encoding.\n * @typeParam TFromValue - The type of the values before encoding.\n *\n * @param key - The encoder for the map's keys.\n * @param value - The encoder for the map's values.\n * @param config - Configuration options for encoding the map.\n * @returns A `FixedSizeEncoder` or `VariableSizeEncoder` for encoding maps.\n *\n * @example\n * Encoding a map with a `u32` size prefix.\n * ```ts\n * const encoder = getMapEncoder(fixCodecSize(getUtf8Encoder(), 5), getU8Encoder());\n * const bytes = encoder.encode(new Map([['alice', 42], ['bob', 5]]));\n * // 0x02000000616c6963652a626f62000005\n * //   |       |         | |         └── Value (5)\n * //   |       |         | └── Key (\"bob\", 5 bytes fixed, null-padded)\n * //   |       |         └── Value (42)\n * //   |       └── Key (\"alice\", 5 bytes fixed)\n * //   └── 4-byte prefix (2 entries)\n * ```\n *\n * @see {@link getMapCodec}\n */\nexport function getMapEncoder<TFromKey, TFromValue>(\n    key: Encoder<TFromKey>,\n    value: Encoder<TFromValue>,\n    config: MapCodecConfig<NumberEncoder> & { size: 0 },\n): FixedSizeEncoder<Map<TFromKey, TFromValue>, 0>;\nexport function getMapEncoder<TFromKey, TFromValue>(\n    key: FixedSizeEncoder<TFromKey>,\n    value: FixedSizeEncoder<TFromValue>,\n    config: MapCodecConfig<NumberEncoder> & { size: number },\n): FixedSizeEncoder<Map<TFromKey, TFromValue>>;\nexport function getMapEncoder<TFromKey, TFromValue>(\n    key: Encoder<TFromKey>,\n    value: Encoder<TFromValue>,\n    config?: MapCodecConfig<NumberEncoder>,\n): VariableSizeEncoder<Map<TFromKey, TFromValue>>;\nexport function getMapEncoder<TFromKey, TFromValue>(\n    key: Encoder<TFromKey>,\n    value: Encoder<TFromValue>,\n    config: MapCodecConfig<NumberEncoder> = {},\n): Encoder<Map<TFromKey, TFromValue>> {\n    return transformEncoder(\n        getArrayEncoder(getTupleEncoder([key, value]), config as object),\n        (map: Map<TFromKey, TFromValue>): [TFromKey, TFromValue][] => [...map.entries()],\n    );\n}\n\n/**\n * Returns a decoder for maps.\n *\n * This decoder deserializes maps where the keys and values are decoded\n * using the provided key and value decoders. The number of entries\n * is determined by the `size` configuration.\n *\n * For more details, see {@link getMapCodec}.\n *\n * @typeParam TToKey - The type of the keys after decoding.\n * @typeParam TToValue - The type of the values after decoding.\n *\n * @param key - The decoder for the map's keys.\n * @param value - The decoder for the map's values.\n * @param config - Configuration options for decoding the map.\n * @returns A `FixedSizeDecoder` or `VariableSizeDecoder` for decoding maps.\n *\n * @example\n * Decoding a map with a `u32` size prefix.\n * ```ts\n * const decoder = getMapDecoder(fixCodecSize(getUtf8Decoder(), 5), getU8Decoder());\n * const map = decoder.decode(new Uint8Array([\n *   0x02,0x00,0x00,0x00,0x61,0x6c,0x69,0x63,0x65,0x2a,0x62,0x6f,0x62,0x00,0x00,0x05\n * ]));\n * // new Map([['alice', 42], ['bob', 5]])\n * ```\n *\n * @see {@link getMapCodec}\n */\nexport function getMapDecoder<TToKey, TToValue>(\n    key: Decoder<TToKey>,\n    value: Decoder<TToValue>,\n    config: MapCodecConfig<NumberDecoder> & { size: 0 },\n): FixedSizeDecoder<Map<TToKey, TToValue>, 0>;\nexport function getMapDecoder<TToKey, TToValue>(\n    key: FixedSizeDecoder<TToKey>,\n    value: FixedSizeDecoder<TToValue>,\n    config: MapCodecConfig<NumberDecoder> & { size: number },\n): FixedSizeDecoder<Map<TToKey, TToValue>>;\nexport function getMapDecoder<TToKey, TToValue>(\n    key: Decoder<TToKey>,\n    value: Decoder<TToValue>,\n    config?: MapCodecConfig<NumberDecoder>,\n): VariableSizeDecoder<Map<TToKey, TToValue>>;\nexport function getMapDecoder<TToKey, TToValue>(\n    key: Decoder<TToKey>,\n    value: Decoder<TToValue>,\n    config: MapCodecConfig<NumberDecoder> = {},\n): Decoder<Map<TToKey, TToValue>> {\n    return transformDecoder(\n        getArrayDecoder(getTupleDecoder([key, value]), config as object) as Decoder<[TToKey, TToValue][]>,\n        (entries: [TToKey, TToValue][]): Map<TToKey, TToValue> => new Map(entries),\n    );\n}\n\n/**\n * Returns a codec for encoding and decoding maps.\n *\n * This codec serializes maps where the key/value pairs are encoded\n * and decoded one after another using the provided key and value codecs.\n * The number of entries is determined by the `size` configuration and\n * defaults to a `u32` size prefix.\n *\n * @typeParam TFromKey - The type of the keys before encoding.\n * @typeParam TFromValue - The type of the values before encoding.\n * @typeParam TToKey - The type of the keys after decoding.\n * @typeParam TToValue - The type of the values after decoding.\n *\n * @param key - The codec for the map's keys.\n * @param value - The codec for the map's values.\n * @param config - Configuration options for encoding and decoding the map.\n * @returns A `FixedSizeCodec` or `VariableSizeCodec` for encoding and decoding maps.\n *\n * @example\n * Encoding and decoding a map with a `u32` size prefix (default).\n * ```ts\n * const codec = getMapCodec(fixCodecSize(getUtf8Codec(), 5), getU8Codec());\n * const bytes = codec.encode(new Map([['alice', 42], ['bob', 5]]));\n * // 0x02000000616c6963652a626f62000005\n * //   |       |         | |         └── Value (5)\n * //   |       |         | └── Key (\"bob\", 5 bytes fixed, null-padded)\n * //   |       |         └── Value (42)\n * //   |       └── Key (\"alice\", 5 bytes fixed)\n * //   └── 4-byte prefix (2 entries)\n *\n * const map = codec.decode(bytes);\n * // new Map([['alice', 42], ['bob', 5]])\n * ```\n *\n * @example\n * Encoding and decoding a map with a `u16` size prefix.\n * ```ts\n * const codec = getMapCodec(fixCodecSize(getUtf8Codec(), 5), getU8Codec(), { size: getU16Codec() });\n * const bytes = codec.encode(new Map([['alice', 42], ['bob', 5]]));\n * // 0x0200616c6963652a626f62000005\n * //   |   |         | |         └── Value (5)\n * //   |   |         | └── Key (\"bob\", 5 bytes fixed, null-padded)\n * //   |   |         └── Value (42)\n * //   |   └── Key (\"alice\", 5 bytes fixed)\n * //   └── 2-byte prefix (2 entries)\n *\n * const map = codec.decode(bytes);\n * // new Map([['alice', 42], ['bob', 5]])\n * ```\n *\n * @example\n * Encoding and decoding a fixed-size map.\n * ```ts\n * const codec = getMapCodec(fixCodecSize(getUtf8Codec(), 5), getU8Codec(), { size: 2 });\n * const bytes = codec.encode(new Map([['alice', 42], ['bob', 5]]));\n * // 0x616c6963652a626f62000005\n * //   |         | |         └── Value (5)\n * //   |         | └── Key (\"bob\", 5 bytes fixed, null-padded)\n * //   |         └── Value (42)\n * //   └── Key (\"alice\", 5 bytes fixed)\n *\n * const map = codec.decode(bytes);\n * // new Map([['alice', 42], ['bob', 5]])\n * ```\n *\n * @example\n * Encoding and decoding a map with remainder size.\n * ```ts\n * const codec = getMapCodec(fixCodecSize(getUtf8Codec(), 5), getU8Codec(), { size: 'remainder' });\n * const bytes = codec.encode(new Map([['alice', 42], ['bob', 5]]));\n * // 0x616c6963652a626f62000005\n * //   |         | |         └── Value (5)\n * //   |         | └── Key (\"bob\", 5 bytes fixed, null-padded)\n * //   |         └── Value (42)\n * //   └── Key (\"alice\", 5 bytes fixed)\n * // No size prefix, the size is inferred from the remaining bytes.\n *\n * const map = codec.decode(bytes);\n * // new Map([['alice', 42], ['bob', 5]])\n * ```\n *\n * @remarks\n * Separate {@link getMapEncoder} and {@link getMapDecoder} functions are available.\n * ```ts\n * const bytes = getMapEncoder(fixCodecSize(getUtf8Encoder(), 5), getU8Encoder()).encode(new Map([['alice', 42]]));\n * const map = getMapDecoder(fixCodecSize(getUtf8Decoder(), 5), getU8Decoder()).decode(bytes);\n * ```\n *\n * @see {@link getMapEncoder}\n * @see {@link getMapDecoder}\n */\nexport function getMapCodec<\n    TFromKey,\n    TFromValue,\n    TToKey extends TFromKey = TFromKey,\n    TToValue extends TFromValue = TFromValue,\n>(\n    key: Codec<TFromKey, TToKey>,\n    value: Codec<TFromValue, TToValue>,\n    config: MapCodecConfig<NumberCodec> & { size: 0 },\n): FixedSizeCodec<Map<TFromKey, TFromValue>, Map<TToKey, TToValue>, 0>;\nexport function getMapCodec<\n    TFromKey,\n    TFromValue,\n    TToKey extends TFromKey = TFromKey,\n    TToValue extends TFromValue = TFromValue,\n>(\n    key: FixedSizeCodec<TFromKey, TToKey>,\n    value: FixedSizeCodec<TFromValue, TToValue>,\n    config: MapCodecConfig<NumberCodec> & { size: number },\n): FixedSizeCodec<Map<TFromKey, TFromValue>, Map<TToKey, TToValue>>;\nexport function getMapCodec<\n    TFromKey,\n    TFromValue,\n    TToKey extends TFromKey = TFromKey,\n    TToValue extends TFromValue = TFromValue,\n>(\n    key: Codec<TFromKey, TToKey>,\n    value: Codec<TFromValue, TToValue>,\n    config?: MapCodecConfig<NumberCodec>,\n): VariableSizeCodec<Map<TFromKey, TFromValue>, Map<TToKey, TToValue>>;\nexport function getMapCodec<\n    TFromKey,\n    TFromValue,\n    TToKey extends TFromKey = TFromKey,\n    TToValue extends TFromValue = TFromValue,\n>(\n    key: Codec<TFromKey, TToKey>,\n    value: Codec<TFromValue, TToValue>,\n    config: MapCodecConfig<NumberCodec> = {},\n): Codec<Map<TFromKey, TFromValue>, Map<TToKey, TToValue>> {\n    return combineCodec(getMapEncoder(key, value, config as object), getMapDecoder(key, value, config as object));\n}\n", "import {\n    combineCodec,\n    createDecoder,\n    createEncoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    ReadonlyUint8Array,\n} from '@solana/codecs-core';\n\n/**\n * Returns an encoder for `void` values.\n *\n * This encoder writes nothing to the byte array and has a fixed size of 0 bytes.\n * It is useful when working with structures that require a no-op encoder,\n * such as empty variants in {@link getDiscriminatedUnionEncoder}.\n *\n * For more details, see {@link getUnitCodec}.\n *\n * @returns A `FixedSizeEncoder<void, 0>`, representing an empty encoder.\n *\n * @example\n * Encoding a `void` value.\n * ```ts\n * getUnitEncoder().encode(undefined); // Produces an empty byte array.\n * ```\n *\n * @see {@link getUnitCodec}\n */\nexport function getUnitEncoder(): FixedSizeEncoder<void, 0> {\n    return createEncoder({\n        fixedSize: 0,\n        write: (_value, _bytes, offset) => offset,\n    });\n}\n\n/**\n * Returns a decoder for `void` values.\n *\n * This decoder always returns `undefined` and has a fixed size of 0 bytes.\n * It is useful when working with structures that require a no-op decoder,\n * such as empty variants in {@link getDiscriminatedUnionDecoder}.\n *\n * For more details, see {@link getUnitCodec}.\n *\n * @returns A `FixedSizeDecoder<void, 0>`, representing an empty decoder.\n *\n * @example\n * Decoding a `void` value.\n * ```ts\n * getUnitDecoder().decode(anyBytes); // Returns `undefined`.\n * ```\n *\n * @see {@link getUnitCodec}\n */\nexport function getUnitDecoder(): FixedSizeDecoder<void, 0> {\n    return createDecoder({\n        fixedSize: 0,\n        read: (_bytes: ReadonlyUint8Array | Uint8Array, offset) => [undefined, offset],\n    });\n}\n\n/**\n * Returns a codec for `void` values.\n *\n * This codec does nothing when encoding or decoding and has a fixed size of 0 bytes.\n * Namely, it always returns `undefined` when decoding and produces an empty byte array when encoding.\n *\n * This can be useful when working with structures that require a no-op codec,\n * such as empty variants in {@link getDiscriminatedUnionCodec}.\n *\n * @returns A `FixedSizeCodec<void, void, 0>`, representing an empty codec.\n *\n * @example\n * Encoding and decoding a `void` value.\n * ```ts\n * const codec = getUnitCodec();\n *\n * codec.encode(undefined); // Produces an empty byte array.\n * codec.decode(new Uint8Array([])); // Returns `undefined`.\n * ```\n *\n * @example\n * Using unit codecs as empty variants in a discriminated union.\n * ```ts\n * type Message =\n *   | { __kind: 'Enter' }\n *   | { __kind: 'Leave' }\n *   | { __kind: 'Move'; x: number; y: number };\n *\n * const messageCodec = getDiscriminatedUnionCodec([\n *   ['Enter', getUnitCodec()], // <- No-op codec for empty data\n *   ['Leave', getUnitCodec()], // <- No-op codec for empty data\n *   ['Move', getStructCodec([...])]\n * ]);\n * ```\n *\n * @remarks\n * Separate {@link getUnitEncoder} and {@link getUnitDecoder} functions are available.\n *\n * ```ts\n * const bytes = getUnitEncoder().encode();\n * const value = getUnitDecoder().decode(bytes);\n * ```\n *\n * @see {@link getUnitEncoder}\n * @see {@link getUnitDecoder}\n */\nexport function getUnitCodec(): FixedSizeCodec<void, void, 0> {\n    return combineCodec(getUnitEncoder(), getUnitDecoder());\n}\n", "import {\n    assertIsFixedSize,\n    Codec,\n    combineCodec,\n    contains<PERSON>yte<PERSON>,\n    Decoder,\n    Encoder,\n    fixDecoderSize,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    fixEncoderSize,\n    ReadonlyUint8Array,\n    transformDecoder,\n    transformEncoder,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\nimport {\n    FixedSizeNumberCodec,\n    FixedSizeNumberDecoder,\n    FixedSizeNumberEncoder,\n    getU8Decoder,\n    getU8Encoder,\n    NumberCodec,\n    NumberDecoder,\n    NumberEncoder,\n} from '@solana/codecs-numbers';\n\nimport { getBooleanDecoder, getBooleanEncoder } from './boolean';\nimport { getConstantDecoder, getConstantEncoder } from './constant';\nimport { getTupleDecoder, getTupleEncoder } from './tuple';\nimport { getUnionDecoder, getUnionEncoder } from './union';\nimport { getUnitDecoder, getUnitEncoder } from './unit';\n\n/**\n * Defines the configuration options for nullable codecs.\n *\n * This configuration controls how nullable values are encoded and decoded.\n *\n * By default, nullable values are prefixed with a `u8` (0 = `null`, 1 = present).\n * The `noneValue` and `prefix` options allow customizing this behavior.\n *\n * @typeParam TPrefix - A number codec, encoder, or decoder used as the presence prefix.\n *\n * @see {@link getNullableEncoder}\n * @see {@link getNullableDecoder}\n * @see {@link getNullableCodec}\n */\nexport type NullableCodecConfig<TPrefix extends NumberCodec | NumberDecoder | NumberEncoder> = {\n    /**\n     * Specifies how `null` values are represented in the encoded data.\n     *\n     * - By default, `null` values are omitted from encoding.\n     * - `'zeroes'`: The bytes allocated for the value are filled with zeroes. This requires a fixed-size codec.\n     * - Custom byte array: `null` values are replaced with a predefined byte sequence. This results in a variable-size codec.\n     *\n     * @defaultValue No explicit `noneValue` is used; `null` values are omitted.\n     */\n    noneValue?: ReadonlyUint8Array | 'zeroes';\n\n    /**\n     * The presence prefix used to distinguish between `null` and present values.\n     *\n     * - By default, a `u8` prefix is used (`0 = null`, `1 = present`).\n     * - Custom number codec: Allows defining a different number size for the prefix.\n     * - `null`: No prefix is used; `noneValue` (if provided) determines `null`.\n     *   If no `noneValue` is set, `null` is identified by the absence of bytes.\n     *\n     * @defaultValue `u8` prefix.\n     */\n    prefix?: TPrefix | null;\n};\n\n/**\n * Returns an encoder for optional values, allowing `null` values to be encoded.\n *\n * This encoder serializes an optional value using a configurable approach:\n * - By default, a `u8` prefix is used (0 = `null`, 1 = present). This can be customized or disabled.\n * - If `noneValue: 'zeroes'` is set, `null` values are encoded as zeroes.\n * - If `noneValue` is a byte array, `null` values are replaced with the provided constant.\n *\n * For more details, see {@link getNullableCodec}.\n *\n * @typeParam TFrom - The type of the main value being encoded.\n *\n * @param item - The encoder for the value that may be present.\n * @param config - Configuration options for encoding optional values.\n * @returns A `FixedSizeEncoder` or `VariableSizeEncoder` for encoding nullable values.\n *\n * @example\n * Encoding an optional number.\n * ```ts\n * const encoder = getNullableEncoder(getU32Encoder());\n *\n * encoder.encode(null); // 0x00\n * encoder.encode(42);   // 0x012a000000\n * ```\n *\n * @see {@link getNullableCodec}\n */\nexport function getNullableEncoder<TFrom, TSize extends number>(\n    item: FixedSizeEncoder<TFrom, TSize>,\n    config: NullableCodecConfig<NumberEncoder> & { noneValue: 'zeroes'; prefix: null },\n): FixedSizeEncoder<TFrom | null, TSize>;\nexport function getNullableEncoder<TFrom>(\n    item: FixedSizeEncoder<TFrom>,\n    config: NullableCodecConfig<FixedSizeNumberEncoder> & { noneValue: 'zeroes' },\n): FixedSizeEncoder<TFrom | null>;\nexport function getNullableEncoder<TFrom>(\n    item: FixedSizeEncoder<TFrom>,\n    config: NullableCodecConfig<NumberEncoder> & { noneValue: 'zeroes' },\n): VariableSizeEncoder<TFrom | null>;\nexport function getNullableEncoder<TFrom>(\n    item: Encoder<TFrom>,\n    config?: NullableCodecConfig<NumberEncoder> & { noneValue?: ReadonlyUint8Array },\n): VariableSizeEncoder<TFrom | null>;\nexport function getNullableEncoder<TFrom>(\n    item: Encoder<TFrom>,\n    config: NullableCodecConfig<NumberEncoder> = {},\n): Encoder<TFrom | null> {\n    const prefix = (() => {\n        if (config.prefix === null) {\n            return transformEncoder(getUnitEncoder(), (_boolean: boolean) => undefined);\n        }\n        return getBooleanEncoder({ size: config.prefix ?? getU8Encoder() });\n    })();\n    const noneValue = (() => {\n        if (config.noneValue === 'zeroes') {\n            assertIsFixedSize(item);\n            return fixEncoderSize(getUnitEncoder(), item.fixedSize);\n        }\n        if (!config.noneValue) {\n            return getUnitEncoder();\n        }\n        return getConstantEncoder(config.noneValue);\n    })();\n\n    return getUnionEncoder(\n        [\n            transformEncoder(getTupleEncoder([prefix, noneValue]), (_value: null): [boolean, void] => [\n                false,\n                undefined,\n            ]),\n            transformEncoder(getTupleEncoder([prefix, item]), (value: TFrom): [boolean, TFrom] => [true, value]),\n        ],\n        variant => Number(variant !== null),\n    );\n}\n\n/**\n * Returns a decoder for optional values, allowing `null` values to be recognized.\n *\n * This decoder deserializes an optional value using a configurable approach:\n * - By default, a `u8` prefix is used (0 = `null`, 1 = present). This can be customized or disabled.\n * - If `noneValue: 'zeroes'` is set, `null` values are identified by zeroes.\n * - If `noneValue` is a byte array, `null` values match the provided constant.\n *\n * For more details, see {@link getNullableCodec}.\n *\n * @typeParam TTo - The type of the main value being decoded.\n *\n * @param item - The decoder for the value that may be present.\n * @param config - Configuration options for decoding optional values.\n * @returns A `FixedSizeDecoder` or `VariableSizeDecoder` for decoding nullable values.\n *\n * @example\n * Decoding an optional number.\n * ```ts\n * const decoder = getNullableDecoder(getU32Decoder());\n *\n * decoder.decode(new Uint8Array([0x00])); // null\n * decoder.decode(new Uint8Array([0x01, 0x2a, 0x00, 0x00, 0x00])); // 42\n * ```\n *\n * @see {@link getNullableCodec}\n */\nexport function getNullableDecoder<TTo, TSize extends number>(\n    item: FixedSizeDecoder<TTo, TSize>,\n    config: NullableCodecConfig<NumberDecoder> & { noneValue: 'zeroes'; prefix: null },\n): FixedSizeDecoder<TTo | null, TSize>;\nexport function getNullableDecoder<TTo>(\n    item: FixedSizeDecoder<TTo>,\n    config: NullableCodecConfig<FixedSizeNumberDecoder> & { noneValue: 'zeroes' },\n): FixedSizeDecoder<TTo | null>;\nexport function getNullableDecoder<TTo>(\n    item: FixedSizeDecoder<TTo>,\n    config: NullableCodecConfig<NumberDecoder> & { noneValue: 'zeroes' },\n): VariableSizeDecoder<TTo | null>;\nexport function getNullableDecoder<TTo>(\n    item: Decoder<TTo>,\n    config?: NullableCodecConfig<NumberDecoder> & { noneValue?: ReadonlyUint8Array },\n): VariableSizeDecoder<TTo | null>;\nexport function getNullableDecoder<TTo>(\n    item: Decoder<TTo>,\n    config: NullableCodecConfig<NumberDecoder> = {},\n): Decoder<TTo | null> {\n    const prefix = (() => {\n        if (config.prefix === null) {\n            return transformDecoder(getUnitDecoder(), () => false);\n        }\n        return getBooleanDecoder({ size: config.prefix ?? getU8Decoder() });\n    })();\n    const noneValue = (() => {\n        if (config.noneValue === 'zeroes') {\n            assertIsFixedSize(item);\n            return fixDecoderSize(getUnitDecoder(), item.fixedSize);\n        }\n        if (!config.noneValue) {\n            return getUnitDecoder();\n        }\n        return getConstantDecoder(config.noneValue);\n    })();\n\n    return getUnionDecoder(\n        [\n            transformDecoder(getTupleDecoder([prefix, noneValue]), () => null),\n            transformDecoder(getTupleDecoder([prefix, item]), ([, value]): TTo => value),\n        ],\n        (bytes, offset) => {\n            if (config.prefix === null && !config.noneValue) {\n                return Number(offset < bytes.length);\n            }\n            if (config.prefix === null && config.noneValue != null) {\n                const zeroValue =\n                    config.noneValue === 'zeroes' ? new Uint8Array(noneValue.fixedSize).fill(0) : config.noneValue;\n                return containsBytes(bytes, zeroValue, offset) ? 0 : 1;\n            }\n            return Number(prefix.read(bytes, offset)[0]);\n        },\n    );\n}\n\n/**\n * Returns a codec for encoding and decoding optional values, allowing `null` values to be handled.\n *\n * This codec serializes and deserializes optional values using a configurable approach:\n * - By default, a `u8` prefix is used (0 = `null`, 1 = present).\n *    This can be customized using a custom number codec or even disabled by setting\n *    the `prefix` to `null`.\n * - If `noneValue: 'zeroes'` is set, `null` values are encoded/decoded as zeroes.\n * - If `noneValue` is a byte array, `null` values are represented by the provided constant.\n *\n * For more details on the configuration options, see {@link NullableCodecConfig}.\n *\n * @typeParam TFrom - The type of the main value being encoded.\n * @typeParam TTo - The type of the main value being decoded.\n *\n * @param item - The codec for the value that may be present.\n * @param config - Configuration options for encoding and decoding optional values.\n * @returns A `FixedSizeCodec` or `VariableSizeCodec` for encoding and decoding nullable values.\n *\n * @example\n * Encoding and decoding an optional number using a `u8` prefix (default).\n * ```ts\n * const codec = getNullableCodec(getU32Codec());\n *\n * codec.encode(null); // 0x00\n * codec.encode(42);   // 0x012a000000\n *\n * codec.decode(new Uint8Array([0x00])); // null\n * codec.decode(new Uint8Array([0x01, 0x2a, 0x00, 0x00, 0x00])); // 42\n * ```\n *\n * @example\n * Encoding and decoding an optional number using a fixed-size codec, by filling `null` values with zeroes.\n * ```ts\n * const codec = getNullableCodec(getU32Codec(), { noneValue: 'zeroes' });\n *\n * codec.encode(null); // 0x0000000000\n * codec.encode(42);   // 0x012a000000\n *\n * codec.decode(new Uint8Array([0x00, 0x00, 0x00, 0x00, 0x00])); // null\n * codec.decode(new Uint8Array([0x01, 0x2a, 0x00, 0x00, 0x00])); // 42\n * ```\n *\n * @example\n * Encoding and decoding `null` values with zeroes and no prefix.\n * ```ts\n * const codec = getNullableCodec(getU32Codec(), {\n *   noneValue: 'zeroes',\n *   prefix: null,\n * });\n *\n * codec.encode(null); // 0x00000000\n * codec.encode(42);   // 0x2a000000\n *\n * codec.decode(new Uint8Array([0x00, 0x00, 0x00, 0x00])); // null\n * codec.decode(new Uint8Array([0x2a, 0x00, 0x00, 0x00])); // 42\n * ```\n *\n * @example\n * Encoding and decoding `null` values with a custom byte sequence and no prefix.\n * ```ts\n * const codec = getNullableCodec(getU16Codec(), {\n *   noneValue: new Uint8Array([0xff, 0xff]),\n *   prefix: null,\n * });\n *\n * codec.encode(null); // 0xffff\n * codec.encode(42); // 0x2a00\n *\n * codec.decode(new Uint8Array([0xff, 0xff])); // null\n * codec.decode(new Uint8Array([0x2a, 0x00])); // 42\n * ```\n *\n * @example\n * Identifying `null` values by the absence of bytes.\n * ```ts\n * const codec = getNullableCodec(getU16Codec(), { prefix: null });\n *\n * codec.encode(null); // Empty bytes\n * codec.encode(42); // 0x2a00\n *\n * codec.decode(new Uint8Array([])); // null\n * codec.decode(new Uint8Array([0x2a, 0x00])); // 42\n * ```\n *\n * @remarks\n * Separate {@link getNullableEncoder} and {@link getNullableDecoder} functions are available.\n *\n * ```ts\n * const bytes = getNullableEncoder(getU32Encoder()).encode(42);\n * const value = getNullableDecoder(getU32Decoder()).decode(bytes);\n * ```\n *\n * @see {@link getNullableEncoder}\n * @see {@link getNullableDecoder}\n */\nexport function getNullableCodec<TFrom, TTo extends TFrom, TSize extends number>(\n    item: FixedSizeCodec<TFrom, TTo, TSize>,\n    config: NullableCodecConfig<NumberCodec> & { noneValue: 'zeroes'; prefix: null },\n): FixedSizeCodec<TFrom | null, TTo | null, TSize>;\nexport function getNullableCodec<TFrom, TTo extends TFrom = TFrom>(\n    item: FixedSizeCodec<TFrom, TTo>,\n    config: NullableCodecConfig<FixedSizeNumberCodec> & { noneValue: 'zeroes' },\n): FixedSizeCodec<TFrom | null, TTo | null>;\nexport function getNullableCodec<TFrom, TTo extends TFrom = TFrom>(\n    item: FixedSizeCodec<TFrom, TTo>,\n    config: NullableCodecConfig<NumberCodec> & { noneValue: 'zeroes' },\n): VariableSizeCodec<TFrom | null, TTo | null>;\nexport function getNullableCodec<TFrom, TTo extends TFrom = TFrom>(\n    item: Codec<TFrom, TTo>,\n    config?: NullableCodecConfig<NumberCodec> & { noneValue?: ReadonlyUint8Array },\n): VariableSizeCodec<TFrom | null, TTo | null>;\nexport function getNullableCodec<TFrom, TTo extends TFrom = TFrom>(\n    item: Codec<TFrom, TTo>,\n    config: NullableCodecConfig<NumberCodec> = {},\n): Codec<TFrom | null, TTo | null> {\n    type ConfigCast = NullableCodecConfig<NumberCodec> & { noneValue?: ReadonlyUint8Array };\n    return combineCodec(\n        getNullableEncoder<TFrom>(item, config as ConfigCast),\n        getNullableDecoder<TTo>(item, config as ConfigCast),\n    );\n}\n", "import {\n    Codec,\n    combineCodec,\n    Decoder,\n    Encoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    transformDecoder,\n    transformEncoder,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\nimport { NumberCodec, NumberDecoder, NumberEncoder } from '@solana/codecs-numbers';\n\nimport { ArrayLikeCodecSize, getArrayDecoder, getArrayEncoder } from './array';\n\n/**\n * Defines the configuration options for set codecs.\n *\n * This configuration allows specifying how the size of the set is encoded.\n * The `size` option can be:\n *\n * - A {@link NumberCodec}, {@link NumberEncoder}, or {@link NumberDecoder} to store the size as a prefix.\n * - A fixed number of items, enforcing a strict length.\n * - The string `'remainder'` to infer the set size from the remaining bytes (only for fixed-size items).\n *\n * @typeParam TPrefix - The type used for encoding the size of the set.\n */\nexport type SetCodecConfig<TPrefix extends NumberCodec | NumberDecoder | NumberEncoder> = {\n    /**\n     * The size encoding strategy for the set.\n     * @defaultValue Uses a `u32` prefix.\n     */\n    size?: ArrayLikeCodecSize<TPrefix>;\n};\n\n/**\n * Returns an encoder for sets of items.\n *\n * This encoder serializes `Set<T>` values by encoding each item using the provided item encoder.\n * The number of items is stored as a prefix using a `u32` codec by default.\n *\n * For more details, see {@link getSetCodec}.\n *\n * @typeParam TFrom - The type of the items in the set before encoding.\n *\n * @param item - The encoder to use for each set item.\n * @param config - Optional configuration specifying the size strategy.\n * @returns An `Encoder<Set<TFrom>>` for encoding sets of items.\n *\n * @example\n * Encoding a set of `u8` numbers.\n * ```ts\n * const encoder = getSetEncoder(getU8Encoder());\n * const bytes = encoder.encode(new Set([1, 2, 3]));\n * // 0x03000000010203\n * //   |       └-- 3 items of 1 byte each.\n * //   └-- 4-byte prefix indicating 3 items.\n * ```\n *\n * @see {@link getSetCodec}\n */\nexport function getSetEncoder<TFrom>(\n    item: Encoder<TFrom>,\n    config: SetCodecConfig<NumberEncoder> & { size: 0 },\n): FixedSizeEncoder<Set<TFrom>, 0>;\nexport function getSetEncoder<TFrom>(\n    item: FixedSizeEncoder<TFrom>,\n    config: SetCodecConfig<NumberEncoder> & { size: number },\n): FixedSizeEncoder<Set<TFrom>>;\nexport function getSetEncoder<TFrom>(\n    item: Encoder<TFrom>,\n    config?: SetCodecConfig<NumberEncoder>,\n): VariableSizeEncoder<Set<TFrom>>;\nexport function getSetEncoder<TFrom>(\n    item: Encoder<TFrom>,\n    config: SetCodecConfig<NumberEncoder> = {},\n): Encoder<Set<TFrom>> {\n    return transformEncoder(getArrayEncoder(item, config as object), (set: Set<TFrom>): TFrom[] => [...set]);\n}\n\n/**\n * Returns a decoder for sets of items.\n *\n * This decoder deserializes a `Set<T>` from a byte array by decoding each item using the provided item decoder.\n * The number of items is determined by a `u32` size prefix by default.\n *\n * For more details, see {@link getSetCodec}.\n *\n * @typeParam TTo - The type of the items in the set after decoding.\n *\n * @param item - The decoder to use for each set item.\n * @param config - Optional configuration specifying the size strategy.\n * @returns A `Decoder<Set<TTo>>` for decoding sets of items.\n *\n * @example\n * Decoding a set of `u8` numbers.\n * ```ts\n * const decoder = getSetDecoder(getU8Decoder());\n * const value = decoder.decode(new Uint8Array([0x03, 0x00, 0x00, 0x00, 0x01, 0x02, 0x03]));\n * // new Set([1, 2, 3])\n * ```\n *\n * @see {@link getSetCodec}\n */\nexport function getSetDecoder<TTo>(\n    item: Decoder<TTo>,\n    config: SetCodecConfig<NumberDecoder> & { size: 0 },\n): FixedSizeDecoder<Set<TTo>, 0>;\nexport function getSetDecoder<TTo>(\n    item: FixedSizeDecoder<TTo>,\n    config: SetCodecConfig<NumberDecoder> & { size: number },\n): FixedSizeDecoder<Set<TTo>>;\nexport function getSetDecoder<TTo>(\n    item: Decoder<TTo>,\n    config?: SetCodecConfig<NumberDecoder>,\n): VariableSizeDecoder<Set<TTo>>;\nexport function getSetDecoder<TTo>(item: Decoder<TTo>, config: SetCodecConfig<NumberDecoder> = {}): Decoder<Set<TTo>> {\n    return transformDecoder(getArrayDecoder(item, config as object), (entries: TTo[]): Set<TTo> => new Set(entries));\n}\n\n/**\n * Returns a codec for encoding and decoding sets of items.\n *\n * This codec serializes `Set<T>` values by encoding each item using the provided item codec.\n * The number of items is stored as a prefix using a `u32` codec by default.\n *\n * @typeParam TFrom - The type of the items in the set before encoding.\n * @typeParam TTo - The type of the items in the set after decoding.\n *\n * @param item - The codec to use for each set item.\n * @param config - Optional configuration specifying the size strategy.\n * @returns A `Codec<Set<TFrom>, Set<TTo>>` for encoding and decoding sets.\n *\n * @example\n * Encoding and decoding a set of `u8` numbers.\n * ```ts\n * const codec = getSetCodec(getU8Codec());\n * const bytes = codec.encode(new Set([1, 2, 3]));\n * // 0x03000000010203\n * //   |       └-- 3 items of 1 byte each.\n * //   └-- 4-byte prefix indicating 3 items.\n *\n * const value = codec.decode(bytes);\n * // new Set([1, 2, 3])\n * ```\n *\n * @example\n * Using a `u16` prefix for size.\n * ```ts\n * const codec = getSetCodec(getU8Codec(), { size: getU16Codec() });\n * const bytes = codec.encode(new Set([1, 2, 3]));\n * // 0x0300010203\n * //   |   └-- 3 items of 1 byte each.\n * //   └-- 2-byte prefix indicating 3 items.\n * ```\n *\n * @example\n * Using a fixed-size set.\n * ```ts\n * const codec = getSetCodec(getU8Codec(), { size: 3 });\n * const bytes = codec.encode(new Set([1, 2, 3]));\n * // 0x010203\n * //   └-- Exactly 3 items of 1 byte each.\n * ```\n *\n * @example\n * Using remainder to infer set size.\n * ```ts\n * const codec = getSetCodec(getU8Codec(), { size: 'remainder' });\n * const bytes = codec.encode(new Set([1, 2, 3]));\n * // 0x010203\n * //   └-- 3 items of 1 byte each. The size is inferred from the remaining bytes.\n * ```\n *\n * @remarks\n * Separate {@link getSetEncoder} and {@link getSetDecoder} functions are available.\n *\n * ```ts\n * const bytes = getSetEncoder(getU8Encoder()).encode(new Set([1, 2, 3]));\n * const value = getSetDecoder(getU8Decoder()).decode(bytes);\n * ```\n *\n * @see {@link getSetEncoder}\n * @see {@link getSetDecoder}\n */\nexport function getSetCodec<TFrom, TTo extends TFrom = TFrom>(\n    item: Codec<TFrom, TTo>,\n    config: SetCodecConfig<NumberCodec> & { size: 0 },\n): FixedSizeCodec<Set<TFrom>, Set<TTo>, 0>;\nexport function getSetCodec<TFrom, TTo extends TFrom = TFrom>(\n    item: FixedSizeCodec<TFrom, TTo>,\n    config: SetCodecConfig<NumberCodec> & { size: number },\n): FixedSizeCodec<Set<TFrom>, Set<TTo>>;\nexport function getSetCodec<TFrom, TTo extends TFrom = TFrom>(\n    item: Codec<TFrom, TTo>,\n    config?: SetCodecConfig<NumberCodec>,\n): VariableSizeCodec<Set<TFrom>, Set<TTo>>;\nexport function getSetCodec<TFrom, TTo extends TFrom = TFrom>(\n    item: Codec<TFrom, TTo>,\n    config: SetCodecConfig<NumberCodec> = {},\n): Codec<Set<TFrom>, Set<TTo>> {\n    return combineCodec(getSetEncoder(item, config as object), getSetDecoder(item, config as object));\n}\n", "/* eslint-disable @typescript-eslint/no-explicit-any */\nimport {\n    Code<PERSON>,\n    combineCodec,\n    createDecoder,\n    createEnco<PERSON>,\n    Decoder,\n    Encoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    getEncodedSize,\n    ReadonlyUint8Array,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\n\nimport { DrainOuterGeneric, getFixedSize, getMaxSize, sumCodecSizes } from './utils';\n\n/**\n * Represents a collection of named fields used in struct codecs.\n *\n * Each field is defined as a tuple containing:\n * - A string key representing the field name.\n * - A codec used to encode and decode the field's value.\n *\n * @typeParam T - The codec type used for each field.\n */\ntype Fields<T> = readonly (readonly [string, T])[];\n\ntype ArrayIndices<T extends readonly unknown[]> = Exclude<Partial<T>['length'], T['length']> & number;\n\n/**\n * Infers the TypeScript type for an object that can be encoded using a struct codec.\n *\n * This type maps the provided field encoders to their corresponding values.\n *\n * @typeParam TFields - The fields of the struct, each paired with an encoder.\n */\ntype GetEncoderTypeFromFields<TFields extends Fields<Encoder<any>>> = DrainOuterGeneric<{\n    [I in ArrayIndices<TFields> as TFields[I][0]]: TFields[I][1] extends Encoder<infer TFrom> ? TFrom : never;\n}>;\n\n/**\n * Infers the TypeScript type for an object that can be decoded using a struct codec.\n *\n * This type maps the provided field decoders to their corresponding values.\n *\n * @typeParam TFields - The fields of the struct, each paired with a decoder.\n */\ntype GetDecoderTypeFromFields<TFields extends Fields<Decoder<any>>> = DrainOuterGeneric<{\n    [I in ArrayIndices<TFields> as TFields[I][0]]: TFields[I][1] extends Decoder<infer TTo> ? TTo : never;\n}>;\n\n/**\n * Returns an encoder for custom objects.\n *\n * This encoder serializes an object by encoding its fields sequentially,\n * using the provided field encoders.\n *\n * For more details, see {@link getStructCodec}.\n *\n * @typeParam TFields - The fields of the struct, each paired with an encoder.\n *\n * @param fields - The name and encoder of each field.\n * @returns A `FixedSizeEncoder` or `VariableSizeEncoder` for encoding custom objects.\n *\n * @example\n * Encoding a custom struct.\n * ```ts\n * const encoder = getStructEncoder([\n *   ['name', fixCodecSize(getUtf8Encoder(), 5)],\n *   ['age', getU8Encoder()]\n * ]);\n *\n * const bytes = encoder.encode({ name: 'Alice', age: 42 });\n * // 0x416c6963652a\n * //   |         └── Age (42)\n * //   └── Name (\"Alice\")\n * ```\n *\n * @see {@link getStructCodec}\n */\nexport function getStructEncoder<const TFields extends Fields<FixedSizeEncoder<any>>>(\n    fields: TFields,\n): FixedSizeEncoder<GetEncoderTypeFromFields<TFields>>;\nexport function getStructEncoder<const TFields extends Fields<Encoder<any>>>(\n    fields: TFields,\n): VariableSizeEncoder<GetEncoderTypeFromFields<TFields>>;\nexport function getStructEncoder<const TFields extends Fields<Encoder<any>>>(\n    fields: TFields,\n): Encoder<GetEncoderTypeFromFields<TFields>> {\n    type TFrom = GetEncoderTypeFromFields<TFields>;\n    const fieldCodecs = fields.map(([, codec]) => codec);\n    const fixedSize = sumCodecSizes(fieldCodecs.map(getFixedSize));\n    const maxSize = sumCodecSizes(fieldCodecs.map(getMaxSize)) ?? undefined;\n\n    return createEncoder({\n        ...(fixedSize === null\n            ? {\n                  getSizeFromValue: (value: TFrom) =>\n                      fields\n                          .map(([key, codec]) => getEncodedSize(value[key as keyof TFrom], codec))\n                          .reduce((all, one) => all + one, 0),\n                  maxSize,\n              }\n            : { fixedSize }),\n        write: (struct: TFrom, bytes, offset) => {\n            fields.forEach(([key, codec]) => {\n                offset = codec.write(struct[key as keyof TFrom], bytes, offset);\n            });\n            return offset;\n        },\n    });\n}\n\n/**\n * Returns a decoder for custom objects.\n *\n * This decoder deserializes an object by decoding its fields sequentially,\n * using the provided field decoders.\n *\n * For more details, see {@link getStructCodec}.\n *\n * @typeParam TFields - The fields of the struct, each paired with a decoder.\n *\n * @param fields - The name and decoder of each field.\n * @returns A `FixedSizeDecoder` or `VariableSizeDecoder` for decoding custom objects.\n *\n * @example\n * Decoding a custom struct.\n * ```ts\n * const decoder = getStructDecoder([\n *   ['name', fixCodecSize(getUtf8Decoder(), 5)],\n *   ['age', getU8Decoder()]\n * ]);\n *\n * const struct = decoder.decode(new Uint8Array([\n *   0x41,0x6c,0x69,0x63,0x65,0x2a\n * ]));\n * // { name: 'Alice', age: 42 }\n * ```\n *\n * @see {@link getStructCodec}\n */\nexport function getStructDecoder<const TFields extends Fields<FixedSizeDecoder<any>>>(\n    fields: TFields,\n): FixedSizeDecoder<GetDecoderTypeFromFields<TFields>>;\nexport function getStructDecoder<const TFields extends Fields<Decoder<any>>>(\n    fields: TFields,\n): VariableSizeDecoder<GetDecoderTypeFromFields<TFields>>;\nexport function getStructDecoder<const TFields extends Fields<Decoder<any>>>(\n    fields: TFields,\n): Decoder<GetDecoderTypeFromFields<TFields>> {\n    type TTo = GetDecoderTypeFromFields<TFields>;\n    const fieldCodecs = fields.map(([, codec]) => codec);\n    const fixedSize = sumCodecSizes(fieldCodecs.map(getFixedSize));\n    const maxSize = sumCodecSizes(fieldCodecs.map(getMaxSize)) ?? undefined;\n\n    return createDecoder({\n        ...(fixedSize === null ? { maxSize } : { fixedSize }),\n        read: (bytes: ReadonlyUint8Array | Uint8Array, offset) => {\n            const struct = {} as TTo;\n            fields.forEach(([key, codec]) => {\n                const [value, newOffset] = codec.read(bytes, offset);\n                offset = newOffset;\n                struct[key as keyof TTo] = value;\n            });\n            return [struct, offset];\n        },\n    });\n}\n\n/**\n * Returns a codec for encoding and decoding custom objects.\n *\n * This codec serializes objects by encoding and decoding each field sequentially.\n *\n * @typeParam TFields - The fields of the struct, each paired with a codec.\n *\n * @param fields - The name and codec of each field.\n * @returns A `FixedSizeCodec` or `VariableSizeCodec` for encoding and decoding custom objects.\n *\n * @example\n * Encoding and decoding a custom struct.\n * ```ts\n * const codec = getStructCodec([\n *   ['name', fixCodecSize(getUtf8Codec(), 5)],\n *   ['age', getU8Codec()]\n * ]);\n *\n * const bytes = codec.encode({ name: 'Alice', age: 42 });\n * // 0x416c6963652a\n * //   |         └── Age (42)\n * //   └── Name (\"Alice\")\n *\n * const struct = codec.decode(bytes);\n * // { name: 'Alice', age: 42 }\n * ```\n *\n * @remarks\n * Separate {@link getStructEncoder} and {@link getStructDecoder} functions are available.\n *\n * ```ts\n * const bytes = getStructEncoder([\n *   ['name', fixCodecSize(getUtf8Encoder(), 5)],\n *   ['age', getU8Encoder()]\n * ]).encode({ name: 'Alice', age: 42 });\n *\n * const struct = getStructDecoder([\n *   ['name', fixCodecSize(getUtf8Decoder(), 5)],\n *   ['age', getU8Decoder()]\n * ]).decode(bytes);\n * ```\n *\n * @see {@link getStructEncoder}\n * @see {@link getStructDecoder}\n */\nexport function getStructCodec<const TFields extends Fields<FixedSizeCodec<any>>>(\n    fields: TFields,\n): FixedSizeCodec<\n    GetEncoderTypeFromFields<TFields>,\n    GetDecoderTypeFromFields<TFields> & GetEncoderTypeFromFields<TFields>\n>;\nexport function getStructCodec<const TFields extends Fields<Codec<any>>>(\n    fields: TFields,\n): VariableSizeCodec<\n    GetEncoderTypeFromFields<TFields>,\n    GetDecoderTypeFromFields<TFields> & GetEncoderTypeFromFields<TFields>\n>;\nexport function getStructCodec<const TFields extends Fields<Codec<any>>>(\n    fields: TFields,\n): Codec<GetEncoderTypeFromFields<TFields>, GetDecoderTypeFromFields<TFields> & GetEncoderTypeFromFields<TFields>> {\n    return combineCodec(\n        getStructEncoder(fields),\n        getStructDecoder(fields) as Decoder<GetDecoderTypeFromFields<TFields> & GetEncoderTypeFromFields<TFields>>,\n    );\n}\n"]}