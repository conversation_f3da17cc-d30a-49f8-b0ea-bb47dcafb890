import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server';
import { NextResponse } from 'next/server';

const isPublicRoute = createRouteMatcher([
  '/login(.*)',
  '/register(.*)',
  '/api/auth/guest(.*)',
  '/',
]);

const isApiRoute = createRouteMatcher(['/api(.*)']);
const isAuthRoute = createRouteMatcher(['/login(.*)', '/register(.*)']);

export default clerkMiddleware(async (auth, req) => {
  const { userId, sessionClaims } = await auth();
  const url = req.nextUrl;

  // Allow public routes
  if (isPublicRoute(req)) {
    // Redirect authenticated users away from auth pages
    if (userId && isAuthRoute(req)) {
      return NextResponse.redirect(new URL('/', req.url));
    }
    return NextResponse.next();
  }

  // Require authentication for all other routes
  if (!userId) {
    // For API routes, return 401
    if (isApiRoute(req)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // For other routes, redirect to login
    return NextResponse.redirect(new URL('/login', req.url));
  }

  // Check for Web3 wallet connection if needed
  const walletAddress = sessionClaims?.walletAddress as string | undefined;

  // Add custom headers for downstream use
  const response = NextResponse.next();
  if (userId) {
    response.headers.set('x-user-id', userId);
  }
  if (walletAddress) {
    response.headers.set('x-wallet-address', walletAddress);
  }

  return response;
});

export const config = {
  matcher: [
    // Skip Next.js internals and all static files, unless found in search params
    '/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
    // Always run for API routes
    '/(api|trpc)(.*)',
  ],
};
