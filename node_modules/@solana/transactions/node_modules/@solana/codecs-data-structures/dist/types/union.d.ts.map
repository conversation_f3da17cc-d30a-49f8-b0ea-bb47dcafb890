{"version": 3, "file": "union.d.ts", "sourceRoot": "", "sources": ["../../src/union.ts"], "names": [], "mappings": "AACA,OAAO,EACH,KAAK,EAIL,OAAO,EACP,OAAO,EAGP,MAAM,EACN,kBAAkB,EACrB,MAAM,qBAAqB,CAAC;AAG7B,OAAO,EAAE,iBAAiB,EAA6B,MAAM,SAAS,CAAC;AAEvE;;;;;;GAMG;AACH,KAAK,0BAA0B,CAAC,SAAS,SAAS,SAAS,OAAO,CAAC,GAAG,CAAC,EAAE,IAAI,iBAAiB,CAAC;KAC1F,CAAC,IAAI,MAAM,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,SAAS,OAAO,CAAC,MAAM,KAAK,CAAC,GAAG,KAAK,GAAG,KAAK;CACpF,CAAC,CAAC,MAAM,CAAC,CAAC;AAEX;;;;;;GAMG;AACH,KAAK,0BAA0B,CAAC,SAAS,SAAS,SAAS,OAAO,CAAC,GAAG,CAAC,EAAE,IAAI,iBAAiB,CAAC;KAC1F,CAAC,IAAI,MAAM,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,SAAS,OAAO,CAAC,MAAM,KAAK,CAAC,GAAG,KAAK,GAAG,KAAK;CACpF,CAAC,CAAC,MAAM,CAAC,CAAC;AAEX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAmCG;AACH,wBAAgB,eAAe,CAAC,KAAK,CAAC,SAAS,SAAS,SAAS,OAAO,CAAC,GAAG,CAAC,EAAE,EAC3E,QAAQ,EAAE,SAAS,EACnB,iBAAiB,EAAE,CAAC,KAAK,EAAE,0BAA0B,CAAC,SAAS,CAAC,KAAK,MAAM,GAC5E,OAAO,CAAC,0BAA0B,CAAC,SAAS,CAAC,CAAC,CAuBhD;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA+BG;AACH,wBAAgB,eAAe,CAAC,KAAK,CAAC,SAAS,SAAS,SAAS,OAAO,CAAC,GAAG,CAAC,EAAE,EAC3E,QAAQ,EAAE,SAAS,EACnB,iBAAiB,EAAE,CAAC,KAAK,EAAE,kBAAkB,EAAE,MAAM,EAAE,MAAM,KAAK,MAAM,GACzE,OAAO,CAAC,0BAA0B,CAAC,SAAS,CAAC,CAAC,CAehD;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA8CG;AACH,wBAAgB,aAAa,CAAC,KAAK,CAAC,SAAS,SAAS,SAAS,KAAK,CAAC,GAAG,CAAC,EAAE,EACvE,QAAQ,EAAE,SAAS,EACnB,iBAAiB,EAAE,CAAC,KAAK,EAAE,0BAA0B,CAAC,SAAS,CAAC,KAAK,MAAM,EAC3E,iBAAiB,EAAE,CAAC,KAAK,EAAE,kBAAkB,EAAE,MAAM,EAAE,MAAM,KAAK,MAAM,GACzE,KAAK,CACJ,0BAA0B,CAAC,SAAS,CAAC,EACrC,0BAA0B,CAAC,SAAS,CAAC,GAAG,0BAA0B,CAAC,SAAS,CAAC,CAChF,CAOA"}