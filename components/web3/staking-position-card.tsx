'use client';

import { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Coins,
  TrendingUp,
  Calendar,
  Zap,
  Settings,
  ArrowUpDown,
  Info,
} from 'lucide-react';
import { TierBadge, type UserTier } from './tier-badge';
import { cn } from '@/lib/utils';

interface StakingPositionData {
  amount: string; // BONK amount as string for precision
  tier: UserTier;
  startTime: string;
  lastClaimTime: string;
  auraPoints: number;
  isActive: boolean;
  apy?: number; // Annual Percentage Yield
}

interface StakingPositionCardProps {
  position: StakingPositionData | null;
  onStake?: () => void;
  onUnstake?: () => void;
  onClaim?: () => void;
  onManage?: () => void;
  className?: string;
}

const tierThresholds = {
  FREE: 0,
  BRONZE: 1000,
  SILVER: 5000,
  DIAMOND: 25000,
};

const tierAPY = {
  FREE: 0,
  BRONZE: 5,
  SILVER: 8,
  DIAMOND: 12,
};

const formatNumber = (num: number | string) => {
  const n = typeof num === 'string' ? parseFloat(num) : num;
  if (n >= 1000000) return `${(n / 1000000).toFixed(2)}M`;
  if (n >= 1000) return `${(n / 1000).toFixed(1)}K`;
  return n.toLocaleString();
};

const calculateTimeStaked = (startTime: string) => {
  const start = new Date(startTime);
  const now = new Date();
  const diffMs = now.getTime() - start.getTime();
  const days = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  const hours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));

  if (days > 0) return `${days}d ${hours}h`;
  return `${hours}h`;
};

const getNextTier = (
  currentTier: UserTier,
): { tier: UserTier; threshold: number } | null => {
  const tiers: UserTier[] = ['FREE', 'BRONZE', 'SILVER', 'DIAMOND'];
  const currentIndex = tiers.indexOf(currentTier);

  if (currentIndex === -1 || currentIndex === tiers.length - 1) return null;

  const nextTier = tiers[currentIndex + 1];
  return { tier: nextTier, threshold: tierThresholds[nextTier] };
};

export function StakingPositionCard({
  position,
  onStake,
  onUnstake,
  onClaim,
  onManage,
  className,
}: StakingPositionCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  // No staking position
  if (!position || !position.isActive) {
    return (
      <Card className={cn('w-full', className)}>
        <CardHeader className="text-center">
          <Coins className="size-12 mx-auto text-muted-foreground mb-3" />
          <CardTitle>Start Staking BONK</CardTitle>
          <CardDescription>
            Stake BONK tokens to unlock tier benefits and earn Aura Points
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-3">
            <div className="p-3 bg-muted/50 rounded-lg text-center">
              <p className="text-sm text-muted-foreground mb-1">
                Benefits of Staking
              </p>
              <ul className="text-xs text-muted-foreground space-y-1">
                <li>• Unlock higher tier features</li>
                <li>• Earn Aura Points rewards</li>
                <li>• Get priority support</li>
                <li>• Access premium AI models</li>
              </ul>
            </div>
          </div>
          {onStake && (
            <Button onClick={onStake} className="w-full">
              <Coins className="size-4 mr-2" />
              Start Staking
            </Button>
          )}
        </CardContent>
      </Card>
    );
  }

  const stakedAmount = parseFloat(position.amount);
  const currentTierThreshold = tierThresholds[position.tier];
  const nextTier = getNextTier(position.tier);
  const apy = tierAPY[position.tier];
  const timeStaked = calculateTimeStaked(position.startTime);

  // Calculate progress to next tier
  const progressToNext = nextTier
    ? Math.min((stakedAmount / nextTier.threshold) * 100, 100)
    : 100;

  return (
    <Card className={cn('w-full', className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Coins className="size-5 text-primary" />
              Staking Position
            </CardTitle>
            <CardDescription>
              Your active BONK staking rewards and tier status
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <TierBadge tier={position.tier} />
            {onManage && (
              <Button variant="ghost" size="sm" onClick={onManage}>
                <Settings className="size-4" />
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Main Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center p-4 bg-primary/5 rounded-lg">
            <p className="text-2xl font-bold text-primary">
              {formatNumber(stakedAmount)}
            </p>
            <p className="text-sm text-muted-foreground">BONK Staked</p>
          </div>

          <div className="text-center p-4 bg-green-50 dark:bg-green-950/20 rounded-lg">
            <p className="text-2xl font-bold text-green-600">
              {position.auraPoints.toLocaleString()}
            </p>
            <p className="text-sm text-muted-foreground">Aura Points</p>
          </div>

          <div className="text-center p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
            <p className="text-2xl font-bold text-blue-600">{apy}%</p>
            <p className="text-sm text-muted-foreground">APY</p>
          </div>
        </div>

        {/* Tier Progress */}
        {nextTier && (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">
                Progress to {nextTier.tier} Tier
              </span>
              <Badge variant="outline">
                {formatNumber(nextTier.threshold - stakedAmount)} more needed
              </Badge>
            </div>
            <Progress value={progressToNext} className="h-3" />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>{formatNumber(currentTierThreshold)} BONK</span>
              <span>{progressToNext.toFixed(1)}%</span>
              <span>{formatNumber(nextTier.threshold)} BONK</span>
            </div>
          </div>
        )}

        {/* Quick Actions */}
        <div className="flex gap-2">
          {onClaim && (
            <Button className="flex-1" size="sm">
              <Zap className="size-4 mr-2" />
              Claim Points
            </Button>
          )}
          <Button
            variant="outline"
            onClick={() => setIsExpanded(!isExpanded)}
            className="flex-1"
            size="sm"
          >
            <Info className="size-4 mr-2" />
            {isExpanded ? 'Less Info' : 'More Info'}
          </Button>
        </div>

        {/* Expanded Details */}
        {isExpanded && (
          <div className="space-y-4 pt-4 border-t">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Time Staked:</span>
                  <span className="font-medium">{timeStaked}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Start Date:</span>
                  <span className="font-medium">
                    {new Date(position.startTime).toLocaleDateString()}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Last Claim:</span>
                  <span className="font-medium">
                    {new Date(position.lastClaimTime).toLocaleDateString()}
                  </span>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Current Tier:</span>
                  <TierBadge tier={position.tier} size="sm" />
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Minimum Stake:</span>
                  <span className="font-medium">
                    {formatNumber(currentTierThreshold)} BONK
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Status:</span>
                  <Badge variant={position.isActive ? 'default' : 'secondary'}>
                    {position.isActive ? 'Active' : 'Inactive'}
                  </Badge>
                </div>
              </div>
            </div>

            {/* Advanced Actions */}
            <div className="flex gap-2 pt-2">
              {onStake && (
                <Button variant="outline" size="sm" className="flex-1">
                  <TrendingUp className="size-4 mr-2" />
                  Add More
                </Button>
              )}
              {onUnstake && (
                <Button variant="outline" size="sm" className="flex-1">
                  <ArrowUpDown className="size-4 mr-2" />
                  Unstake
                </Button>
              )}
            </div>

            {/* Earnings Calculator */}
            <div className="p-3 bg-muted/50 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Calendar className="size-4 text-muted-foreground" />
                <span className="text-sm font-medium">Estimated Earnings</span>
              </div>
              <div className="grid grid-cols-3 gap-3 text-xs">
                <div className="text-center">
                  <p className="font-medium">
                    {((stakedAmount * apy) / 100 / 365).toFixed(0)}
                  </p>
                  <p className="text-muted-foreground">BONK/day</p>
                </div>
                <div className="text-center">
                  <p className="font-medium">
                    {((stakedAmount * apy) / 100 / 12).toFixed(0)}
                  </p>
                  <p className="text-muted-foreground">BONK/month</p>
                </div>
                <div className="text-center">
                  <p className="font-medium">
                    {((stakedAmount * apy) / 100).toFixed(0)}
                  </p>
                  <p className="text-muted-foreground">BONK/year</p>
                </div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
