(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[751],{18:(e,t,r)=>{"use strict";let n,i;r.r(t),r.d(t,{default:()=>nk});var o,a,s,l,c,u,d={};async function p(){return"_ENTRIES"in globalThis&&_ENTRIES.middleware_instrumentation&&await _ENTRIES.middleware_instrumentation}r.r(d),r.d(d,{config:()=>nx,middleware:()=>nS});let h=null;async function f(){if("phase-production-build"===process.env.NEXT_PHASE)return;h||(h=p());let e=await h;if(null==e?void 0:e.register)try{await e.register()}catch(e){throw e.message=`An error occurred while loading instrumentation hook: ${e.message}`,e}}async function g(...e){let t=await p();try{var r;await (null==t||null==(r=t.onRequestError)?void 0:r.call(t,...e))}catch(e){console.error("Error in instrumentation.onRequestError:",e)}}let y=null;function m(){return y||(y=f()),y}function w(e){return`The edge runtime does not support Node.js '${e}' module.
Learn More: https://nextjs.org/docs/messages/node-module-in-edge-runtime`}process!==r.g.process&&(process.env=r.g.process.env,r.g.process=process),Object.defineProperty(globalThis,"__import_unsupported",{value:function(e){let t=new Proxy(function(){},{get(t,r){if("then"===r)return{};throw Object.defineProperty(Error(w(e)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})},construct(){throw Object.defineProperty(Error(w(e)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})},apply(r,n,i){if("function"==typeof i[0])return i[0](t);throw Object.defineProperty(Error(w(e)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}});return new Proxy({},{get:()=>t})},enumerable:!1,configurable:!1}),m();class v extends Error{constructor({page:e}){super(`The middleware "${e}" accepts an async API directly with the form:
  
  export function middleware(request, event) {
    return NextResponse.redirect('/new-location')
  }
  
  Read more: https://nextjs.org/docs/messages/middleware-new-signature
  `)}}class b extends Error{constructor(){super(`The request.page has been deprecated in favour of \`URLPattern\`.
  Read more: https://nextjs.org/docs/messages/middleware-request-page
  `)}}class E extends Error{constructor(){super(`The request.ua has been removed in favour of \`userAgent\` function.
  Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent
  `)}}let _={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",apiNode:"api-node",apiEdge:"api-edge",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",pagesDirBrowser:"pages-dir-browser",pagesDirEdge:"pages-dir-edge",pagesDirNode:"pages-dir-node"};function S(e){var t,r,n,i,o,a=[],s=0;function l(){for(;s<e.length&&/\s/.test(e.charAt(s));)s+=1;return s<e.length}for(;s<e.length;){for(t=s,o=!1;l();)if(","===(r=e.charAt(s))){for(n=s,s+=1,l(),i=s;s<e.length&&"="!==(r=e.charAt(s))&&";"!==r&&","!==r;)s+=1;s<e.length&&"="===e.charAt(s)?(o=!0,s=i,a.push(e.substring(t,n)),t=s):s=n+1}else s+=1;(!o||s>=e.length)&&a.push(e.substring(t,e.length))}return a}function x(e){let t={},r=[];if(e)for(let[n,i]of e.entries())"set-cookie"===n.toLowerCase()?(r.push(...S(i)),t[n]=1===r.length?r[0]:r):t[n]=i;return t}function R(e){try{return String(new URL(String(e)))}catch(t){throw Object.defineProperty(Error(`URL is malformed "${String(e)}". Please use only absolute URLs - https://nextjs.org/docs/messages/middleware-relative-urls`,{cause:t}),"__NEXT_ERROR_CODE",{value:"E61",enumerable:!1,configurable:!0})}}({..._,GROUP:{builtinReact:[_.reactServerComponents,_.actionBrowser],serverOnly:[_.reactServerComponents,_.actionBrowser,_.instrument,_.middleware],neutralTarget:[_.apiNode,_.apiEdge],clientOnly:[_.serverSideRendering,_.appPagesBrowser],bundled:[_.reactServerComponents,_.actionBrowser,_.serverSideRendering,_.appPagesBrowser,_.shared,_.instrument,_.middleware],appPages:[_.reactServerComponents,_.serverSideRendering,_.appPagesBrowser,_.actionBrowser]}});let C=Symbol("response"),A=Symbol("passThrough"),k=Symbol("waitUntil");class T{constructor(e,t){this[A]=!1,this[k]=t?{kind:"external",function:t}:{kind:"internal",promises:[]}}respondWith(e){this[C]||(this[C]=Promise.resolve(e))}passThroughOnException(){this[A]=!0}waitUntil(e){if("external"===this[k].kind)return(0,this[k].function)(e);this[k].promises.push(e)}}class O extends T{constructor(e){var t;super(e.request,null==(t=e.context)?void 0:t.waitUntil),this.sourcePage=e.page}get request(){throw Object.defineProperty(new v({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}respondWith(){throw Object.defineProperty(new v({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}}function P(e){return e.replace(/\/$/,"")||"/"}function I(e){let t=e.indexOf("#"),r=e.indexOf("?"),n=r>-1&&(t<0||r<t);return n||t>-1?{pathname:e.substring(0,n?r:t),query:n?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}function N(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:n,hash:i}=I(e);return""+t+r+n+i}function j(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:n,hash:i}=I(e);return""+r+t+n+i}function M(e,t){if("string"!=typeof e)return!1;let{pathname:r}=I(e);return r===t||r.startsWith(t+"/")}let L=new WeakMap;function D(e,t){let r;if(!t)return{pathname:e};let n=L.get(t);n||(n=t.map(e=>e.toLowerCase()),L.set(t,n));let i=e.split("/",2);if(!i[1])return{pathname:e};let o=i[1].toLowerCase(),a=n.indexOf(o);return a<0?{pathname:e}:(r=t[a],{pathname:e=e.slice(r.length+1)||"/",detectedLocale:r})}let W=/(?!^https?:\/\/)(127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\[::1\]|localhost)/;function U(e,t){return new URL(String(e).replace(W,"localhost"),t&&String(t).replace(W,"localhost"))}let H=Symbol("NextURLInternal");class K{constructor(e,t,r){let n,i;"object"==typeof t&&"pathname"in t||"string"==typeof t?(n=t,i=r||{}):i=r||t||{},this[H]={url:U(e,n??i.base),options:i,basePath:""},this.analyze()}analyze(){var e,t,r,n,i;let o=function(e,t){var r,n;let{basePath:i,i18n:o,trailingSlash:a}=null!=(r=t.nextConfig)?r:{},s={pathname:e,trailingSlash:"/"!==e?e.endsWith("/"):a};i&&M(s.pathname,i)&&(s.pathname=function(e,t){if(!M(e,t))return e;let r=e.slice(t.length);return r.startsWith("/")?r:"/"+r}(s.pathname,i),s.basePath=i);let l=s.pathname;if(s.pathname.startsWith("/_next/data/")&&s.pathname.endsWith(".json")){let e=s.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/");s.buildId=e[0],l="index"!==e[1]?"/"+e.slice(1).join("/"):"/",!0===t.parseData&&(s.pathname=l)}if(o){let e=t.i18nProvider?t.i18nProvider.analyze(s.pathname):D(s.pathname,o.locales);s.locale=e.detectedLocale,s.pathname=null!=(n=e.pathname)?n:s.pathname,!e.detectedLocale&&s.buildId&&(e=t.i18nProvider?t.i18nProvider.analyze(l):D(l,o.locales)).detectedLocale&&(s.locale=e.detectedLocale)}return s}(this[H].url.pathname,{nextConfig:this[H].options.nextConfig,parseData:!0,i18nProvider:this[H].options.i18nProvider}),a=function(e,t){let r;if((null==t?void 0:t.host)&&!Array.isArray(t.host))r=t.host.toString().split(":",1)[0];else{if(!e.hostname)return;r=e.hostname}return r.toLowerCase()}(this[H].url,this[H].options.headers);this[H].domainLocale=this[H].options.i18nProvider?this[H].options.i18nProvider.detectDomainLocale(a):function(e,t,r){if(e)for(let o of(r&&(r=r.toLowerCase()),e)){var n,i;if(t===(null==(n=o.domain)?void 0:n.split(":",1)[0].toLowerCase())||r===o.defaultLocale.toLowerCase()||(null==(i=o.locales)?void 0:i.some(e=>e.toLowerCase()===r)))return o}}(null==(t=this[H].options.nextConfig)||null==(e=t.i18n)?void 0:e.domains,a);let s=(null==(r=this[H].domainLocale)?void 0:r.defaultLocale)||(null==(i=this[H].options.nextConfig)||null==(n=i.i18n)?void 0:n.defaultLocale);this[H].url.pathname=o.pathname,this[H].defaultLocale=s,this[H].basePath=o.basePath??"",this[H].buildId=o.buildId,this[H].locale=o.locale??s,this[H].trailingSlash=o.trailingSlash}formatPathname(){var e;let t;return t=function(e,t,r,n){if(!t||t===r)return e;let i=e.toLowerCase();return!n&&(M(i,"/api")||M(i,"/"+t.toLowerCase()))?e:N(e,"/"+t)}((e={basePath:this[H].basePath,buildId:this[H].buildId,defaultLocale:this[H].options.forceLocale?void 0:this[H].defaultLocale,locale:this[H].locale,pathname:this[H].url.pathname,trailingSlash:this[H].trailingSlash}).pathname,e.locale,e.buildId?void 0:e.defaultLocale,e.ignorePrefix),(e.buildId||!e.trailingSlash)&&(t=P(t)),e.buildId&&(t=j(N(t,"/_next/data/"+e.buildId),"/"===e.pathname?"index.json":".json")),t=N(t,e.basePath),!e.buildId&&e.trailingSlash?t.endsWith("/")?t:j(t,"/"):P(t)}formatSearch(){return this[H].url.search}get buildId(){return this[H].buildId}set buildId(e){this[H].buildId=e}get locale(){return this[H].locale??""}set locale(e){var t,r;if(!this[H].locale||!(null==(r=this[H].options.nextConfig)||null==(t=r.i18n)?void 0:t.locales.includes(e)))throw Object.defineProperty(TypeError(`The NextURL configuration includes no locale "${e}"`),"__NEXT_ERROR_CODE",{value:"E597",enumerable:!1,configurable:!0});this[H].locale=e}get defaultLocale(){return this[H].defaultLocale}get domainLocale(){return this[H].domainLocale}get searchParams(){return this[H].url.searchParams}get host(){return this[H].url.host}set host(e){this[H].url.host=e}get hostname(){return this[H].url.hostname}set hostname(e){this[H].url.hostname=e}get port(){return this[H].url.port}set port(e){this[H].url.port=e}get protocol(){return this[H].url.protocol}set protocol(e){this[H].url.protocol=e}get href(){let e=this.formatPathname(),t=this.formatSearch();return`${this.protocol}//${this.host}${e}${t}${this.hash}`}set href(e){this[H].url=U(e),this.analyze()}get origin(){return this[H].url.origin}get pathname(){return this[H].url.pathname}set pathname(e){this[H].url.pathname=e}get hash(){return this[H].url.hash}set hash(e){this[H].url.hash=e}get search(){return this[H].url.search}set search(e){this[H].url.search=e}get password(){return this[H].url.password}set password(e){this[H].url.password=e}get username(){return this[H].url.username}set username(e){this[H].url.username=e}get basePath(){return this[H].basePath}set basePath(e){this[H].basePath=e.startsWith("/")?e:`/${e}`}toString(){return this.href}toJSON(){return this.href}[Symbol.for("edge-runtime.inspect.custom")](){return{href:this.href,origin:this.origin,protocol:this.protocol,username:this.username,password:this.password,host:this.host,hostname:this.hostname,port:this.port,pathname:this.pathname,search:this.search,searchParams:this.searchParams,hash:this.hash}}clone(){return new K(String(this),this[H].options)}}var q=r(962);let $=Symbol("internal request");class B extends Request{constructor(e,t={}){let r="string"!=typeof e&&"url"in e?e.url:String(e);R(r),e instanceof Request?super(e,t):super(r,t);let n=new K(r,{headers:x(this.headers),nextConfig:t.nextConfig});this[$]={cookies:new q.RequestCookies(this.headers),nextUrl:n,url:n.toString()}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,nextUrl:this.nextUrl,url:this.url,bodyUsed:this.bodyUsed,cache:this.cache,credentials:this.credentials,destination:this.destination,headers:Object.fromEntries(this.headers),integrity:this.integrity,keepalive:this.keepalive,method:this.method,mode:this.mode,redirect:this.redirect,referrer:this.referrer,referrerPolicy:this.referrerPolicy,signal:this.signal}}get cookies(){return this[$].cookies}get nextUrl(){return this[$].nextUrl}get page(){throw new b}get ua(){throw new E}get url(){return this[$].url}}class z{static get(e,t,r){let n=Reflect.get(e,t,r);return"function"==typeof n?n.bind(e):n}static set(e,t,r,n){return Reflect.set(e,t,r,n)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}let J=Symbol("internal response"),V=new Set([301,302,303,307,308]);function G(e,t){var r;if(null==e||null==(r=e.request)?void 0:r.headers){if(!(e.request.headers instanceof Headers))throw Object.defineProperty(Error("request.headers must be an instance of Headers"),"__NEXT_ERROR_CODE",{value:"E119",enumerable:!1,configurable:!0});let r=[];for(let[n,i]of e.request.headers)t.set("x-middleware-request-"+n,i),r.push(n);t.set("x-middleware-override-headers",r.join(","))}}class F extends Response{constructor(e,t={}){super(e,t);let r=this.headers,n=new Proxy(new q.ResponseCookies(r),{get(e,n,i){switch(n){case"delete":case"set":return(...i)=>{let o=Reflect.apply(e[n],e,i),a=new Headers(r);return o instanceof q.ResponseCookies&&r.set("x-middleware-set-cookie",o.getAll().map(e=>(0,q.stringifyCookie)(e)).join(",")),G(t,a),o};default:return z.get(e,n,i)}}});this[J]={cookies:n,url:t.url?new K(t.url,{headers:x(r),nextConfig:t.nextConfig}):void 0}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,url:this.url,body:this.body,bodyUsed:this.bodyUsed,headers:Object.fromEntries(this.headers),ok:this.ok,redirected:this.redirected,status:this.status,statusText:this.statusText,type:this.type}}get cookies(){return this[J].cookies}static json(e,t){let r=Response.json(e,t);return new F(r.body,r)}static redirect(e,t){let r="number"==typeof t?t:(null==t?void 0:t.status)??307;if(!V.has(r))throw Object.defineProperty(RangeError('Failed to execute "redirect" on "response": Invalid status code'),"__NEXT_ERROR_CODE",{value:"E529",enumerable:!1,configurable:!0});let n="object"==typeof t?t:{},i=new Headers(null==n?void 0:n.headers);return i.set("Location",R(e)),new F(null,{...n,headers:i,status:r})}static rewrite(e,t){let r=new Headers(null==t?void 0:t.headers);return r.set("x-middleware-rewrite",R(e)),G(t,r),new F(null,{...t,headers:r})}static next(e){let t=new Headers(null==e?void 0:e.headers);return t.set("x-middleware-next","1"),G(e,t),new F(null,{...e,headers:t})}}function X(e,t){let r="string"==typeof t?new URL(t):t,n=new URL(e,t),i=n.origin===r.origin;return{url:i?n.toString().slice(r.origin.length):n.toString(),isRelative:i}}let Y="Next-Router-Prefetch",Q=["RSC","Next-Router-State-Tree",Y,"Next-HMR-Refresh","Next-Router-Segment-Prefetch"],Z="_rsc";class ee extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new ee}}class et extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,n){if("symbol"==typeof r)return z.get(t,r,n);let i=r.toLowerCase(),o=Object.keys(e).find(e=>e.toLowerCase()===i);if(void 0!==o)return z.get(t,o,n)},set(t,r,n,i){if("symbol"==typeof r)return z.set(t,r,n,i);let o=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===o);return z.set(t,a??r,n,i)},has(t,r){if("symbol"==typeof r)return z.has(t,r);let n=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===n);return void 0!==i&&z.has(t,i)},deleteProperty(t,r){if("symbol"==typeof r)return z.deleteProperty(t,r);let n=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===n);return void 0===i||z.deleteProperty(t,i)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return ee.callable;default:return z.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new et(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,n]of this.entries())e.call(t,n,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}let er=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class en{disable(){throw er}getStore(){}run(){throw er}exit(){throw er}enterWith(){throw er}static bind(e){return e}}let ei="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage;function eo(){return ei?new ei:new en}let ea=eo(),es=eo();class el extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options")}static callable(){throw new el}}class ec{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return el.callable;default:return z.get(e,t,r)}}})}}let eu=Symbol.for("next.mutated.cookies");class ed{static wrap(e,t){let r=new q.ResponseCookies(new Headers);for(let t of e.getAll())r.set(t);let n=[],i=new Set,o=()=>{let e=ea.getStore();if(e&&(e.pathWasRevalidated=!0),n=r.getAll().filter(e=>i.has(e.name)),t){let e=[];for(let t of n){let r=new q.ResponseCookies(new Headers);r.set(t),e.push(r.toString())}t(e)}},a=new Proxy(r,{get(e,t,r){switch(t){case eu:return n;case"delete":return function(...t){i.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.delete(...t),a}finally{o()}};case"set":return function(...t){i.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t),a}finally{o()}};default:return z.get(e,t,r)}}});return a}}function ep(e){if("action"!==function(e){let t=es.getStore();switch(!t&&function(e){throw Object.defineProperty(Error(`\`${e}\` was called outside a request scope. Read more: https://nextjs.org/docs/messages/next-dynamic-api-wrong-context`),"__NEXT_ERROR_CODE",{value:"E251",enumerable:!1,configurable:!0})}(e),t.type){case"request":default:return t;case"prerender":case"prerender-ppr":case"prerender-legacy":throw Object.defineProperty(Error(`\`${e}\` cannot be called inside a prerender. This is a bug in Next.js.`),"__NEXT_ERROR_CODE",{value:"E401",enumerable:!1,configurable:!0});case"cache":throw Object.defineProperty(Error(`\`${e}\` cannot be called inside "use cache". Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E37",enumerable:!1,configurable:!0});case"unstable-cache":throw Object.defineProperty(Error(`\`${e}\` cannot be called inside unstable_cache. Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E69",enumerable:!1,configurable:!0})}}(e).phase)throw new el}var eh=function(e){return e.handleRequest="BaseServer.handleRequest",e.run="BaseServer.run",e.pipe="BaseServer.pipe",e.getStaticHTML="BaseServer.getStaticHTML",e.render="BaseServer.render",e.renderToResponseWithComponents="BaseServer.renderToResponseWithComponents",e.renderToResponse="BaseServer.renderToResponse",e.renderToHTML="BaseServer.renderToHTML",e.renderError="BaseServer.renderError",e.renderErrorToResponse="BaseServer.renderErrorToResponse",e.renderErrorToHTML="BaseServer.renderErrorToHTML",e.render404="BaseServer.render404",e}(eh||{}),ef=function(e){return e.loadDefaultErrorComponents="LoadComponents.loadDefaultErrorComponents",e.loadComponents="LoadComponents.loadComponents",e}(ef||{}),eg=function(e){return e.getRequestHandler="NextServer.getRequestHandler",e.getServer="NextServer.getServer",e.getServerRequestHandler="NextServer.getServerRequestHandler",e.createServer="createServer.createServer",e}(eg||{}),ey=function(e){return e.compression="NextNodeServer.compression",e.getBuildId="NextNodeServer.getBuildId",e.createComponentTree="NextNodeServer.createComponentTree",e.clientComponentLoading="NextNodeServer.clientComponentLoading",e.getLayoutOrPageModule="NextNodeServer.getLayoutOrPageModule",e.generateStaticRoutes="NextNodeServer.generateStaticRoutes",e.generateFsStaticRoutes="NextNodeServer.generateFsStaticRoutes",e.generatePublicRoutes="NextNodeServer.generatePublicRoutes",e.generateImageRoutes="NextNodeServer.generateImageRoutes.route",e.sendRenderResult="NextNodeServer.sendRenderResult",e.proxyRequest="NextNodeServer.proxyRequest",e.runApi="NextNodeServer.runApi",e.render="NextNodeServer.render",e.renderHTML="NextNodeServer.renderHTML",e.imageOptimizer="NextNodeServer.imageOptimizer",e.getPagePath="NextNodeServer.getPagePath",e.getRoutesManifest="NextNodeServer.getRoutesManifest",e.findPageComponents="NextNodeServer.findPageComponents",e.getFontManifest="NextNodeServer.getFontManifest",e.getServerComponentManifest="NextNodeServer.getServerComponentManifest",e.getRequestHandler="NextNodeServer.getRequestHandler",e.renderToHTML="NextNodeServer.renderToHTML",e.renderError="NextNodeServer.renderError",e.renderErrorToHTML="NextNodeServer.renderErrorToHTML",e.render404="NextNodeServer.render404",e.startResponse="NextNodeServer.startResponse",e.route="route",e.onProxyReq="onProxyReq",e.apiResolver="apiResolver",e.internalFetch="internalFetch",e}(ey||{}),em=function(e){return e.startServer="startServer.startServer",e}(em||{}),ew=function(e){return e.getServerSideProps="Render.getServerSideProps",e.getStaticProps="Render.getStaticProps",e.renderToString="Render.renderToString",e.renderDocument="Render.renderDocument",e.createBodyResult="Render.createBodyResult",e}(ew||{}),ev=function(e){return e.renderToString="AppRender.renderToString",e.renderToReadableStream="AppRender.renderToReadableStream",e.getBodyResult="AppRender.getBodyResult",e.fetch="AppRender.fetch",e}(ev||{}),eb=function(e){return e.executeRoute="Router.executeRoute",e}(eb||{}),eE=function(e){return e.runHandler="Node.runHandler",e}(eE||{}),e_=function(e){return e.runHandler="AppRouteRouteHandlers.runHandler",e}(e_||{}),eS=function(e){return e.generateMetadata="ResolveMetadata.generateMetadata",e.generateViewport="ResolveMetadata.generateViewport",e}(eS||{}),ex=function(e){return e.execute="Middleware.execute",e}(ex||{});let eR=["Middleware.execute","BaseServer.handleRequest","Render.getServerSideProps","Render.getStaticProps","AppRender.fetch","AppRender.getBodyResult","Render.renderDocument","Node.runHandler","AppRouteRouteHandlers.runHandler","ResolveMetadata.generateMetadata","ResolveMetadata.generateViewport","NextNodeServer.createComponentTree","NextNodeServer.findPageComponents","NextNodeServer.getLayoutOrPageModule","NextNodeServer.startResponse","NextNodeServer.clientComponentLoading"],eC=["NextNodeServer.findPageComponents","NextNodeServer.createComponentTree","NextNodeServer.clientComponentLoading"];function eA(e){return null!==e&&"object"==typeof e&&"then"in e&&"function"==typeof e.then}let{context:ek,propagation:eT,trace:eO,SpanStatusCode:eP,SpanKind:eI,ROOT_CONTEXT:eN}=n=r(674);class ej extends Error{constructor(e,t){super(),this.bubble=e,this.result=t}}let eM=(e,t)=>{(function(e){return"object"==typeof e&&null!==e&&e instanceof ej})(t)&&t.bubble?e.setAttribute("next.bubble",!0):(t&&e.recordException(t),e.setStatus({code:eP.ERROR,message:null==t?void 0:t.message})),e.end()},eL=new Map,eD=n.createContextKey("next.rootSpanId"),eW=0,eU=()=>eW++,eH={set(e,t,r){e.push({key:t,value:r})}};class eK{getTracerInstance(){return eO.getTracer("next.js","0.0.1")}getContext(){return ek}getTracePropagationData(){let e=ek.active(),t=[];return eT.inject(e,t,eH),t}getActiveScopeSpan(){return eO.getSpan(null==ek?void 0:ek.active())}withPropagatedContext(e,t,r){let n=ek.active();if(eO.getSpanContext(n))return t();let i=eT.extract(n,e,r);return ek.with(i,t)}trace(...e){var t;let[r,n,i]=e,{fn:o,options:a}="function"==typeof n?{fn:n,options:{}}:{fn:i,options:{...n}},s=a.spanName??r;if(!eR.includes(r)&&"1"!==process.env.NEXT_OTEL_VERBOSE||a.hideSpan)return o();let l=this.getSpanContext((null==a?void 0:a.parentSpan)??this.getActiveScopeSpan()),c=!1;l?(null==(t=eO.getSpanContext(l))?void 0:t.isRemote)&&(c=!0):(l=(null==ek?void 0:ek.active())??eN,c=!0);let u=eU();return a.attributes={"next.span_name":s,"next.span_type":r,...a.attributes},ek.with(l.setValue(eD,u),()=>this.getTracerInstance().startActiveSpan(s,a,e=>{let t="performance"in globalThis&&"measure"in performance?globalThis.performance.now():void 0,n=()=>{eL.delete(u),t&&process.env.NEXT_OTEL_PERFORMANCE_PREFIX&&eC.includes(r||"")&&performance.measure(`${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-${(r.split(".").pop()||"").replace(/[A-Z]/g,e=>"-"+e.toLowerCase())}`,{start:t,end:performance.now()})};c&&eL.set(u,new Map(Object.entries(a.attributes??{})));try{if(o.length>1)return o(e,t=>eM(e,t));let t=o(e);if(eA(t))return t.then(t=>(e.end(),t)).catch(t=>{throw eM(e,t),t}).finally(n);return e.end(),n(),t}catch(t){throw eM(e,t),n(),t}}))}wrap(...e){let t=this,[r,n,i]=3===e.length?e:[e[0],{},e[1]];return eR.includes(r)||"1"===process.env.NEXT_OTEL_VERBOSE?function(){let e=n;"function"==typeof e&&"function"==typeof i&&(e=e.apply(this,arguments));let o=arguments.length-1,a=arguments[o];if("function"!=typeof a)return t.trace(r,e,()=>i.apply(this,arguments));{let n=t.getContext().bind(ek.active(),a);return t.trace(r,e,(e,t)=>(arguments[o]=function(e){return null==t||t(e),n.apply(this,arguments)},i.apply(this,arguments)))}}:i}startSpan(...e){let[t,r]=e,n=this.getSpanContext((null==r?void 0:r.parentSpan)??this.getActiveScopeSpan());return this.getTracerInstance().startSpan(t,r,n)}getSpanContext(e){return e?eO.setSpan(ek.active(),e):void 0}getRootSpanAttributes(){let e=ek.active().getValue(eD);return eL.get(e)}setRootSpanAttribute(e,t){let r=ek.active().getValue(eD),n=eL.get(r);n&&n.set(e,t)}}let eq=(()=>{let e=new eK;return()=>e})(),e$="__prerender_bypass";Symbol("__next_preview_data"),Symbol(e$);class eB{constructor(e,t,r,n){var i;let o=e&&function(e,t){let r=et.from(e.headers);return{isOnDemandRevalidate:r.get("x-prerender-revalidate")===t.previewModeId,revalidateOnlyGenerated:r.has("x-prerender-revalidate-if-generated")}}(t,e).isOnDemandRevalidate,a=null==(i=r.get(e$))?void 0:i.value;this._isEnabled=!!(!o&&a&&e&&a===e.previewModeId),this._previewModeId=null==e?void 0:e.previewModeId,this._mutableCookies=n}get isEnabled(){return this._isEnabled}enable(){if(!this._previewModeId)throw Object.defineProperty(Error("Invariant: previewProps missing previewModeId this should never happen"),"__NEXT_ERROR_CODE",{value:"E93",enumerable:!1,configurable:!0});this._mutableCookies.set({name:e$,value:this._previewModeId,httpOnly:!0,sameSite:"none",secure:!0,path:"/"}),this._isEnabled=!0}disable(){this._mutableCookies.set({name:e$,value:"",httpOnly:!0,sameSite:"none",secure:!0,path:"/",expires:new Date(0)}),this._isEnabled=!1}}function ez(e,t){if("x-middleware-set-cookie"in e.headers&&"string"==typeof e.headers["x-middleware-set-cookie"]){let r=e.headers["x-middleware-set-cookie"],n=new Headers;for(let e of S(r))n.append("set-cookie",e);for(let e of new q.ResponseCookies(n).getAll())t.set(e)}}var eJ=r(572),eV=r.n(eJ);class eG extends Error{constructor(e,t){super("Invariant: "+(e.endsWith(".")?e:e+".")+" This is a bug in Next.js.",t),this.name="InvariantError"}}class eF{constructor(e,t){this.cache=new Map,this.sizes=new Map,this.totalSize=0,this.maxSize=e,this.calculateSize=t||(()=>1)}set(e,t){if(!e||!t)return;let r=this.calculateSize(t);if(r>this.maxSize)return void console.warn("Single item size exceeds maxSize");this.cache.has(e)&&(this.totalSize-=this.sizes.get(e)||0),this.cache.set(e,t),this.sizes.set(e,r),this.totalSize+=r,this.touch(e)}has(e){return!!e&&(this.touch(e),!!this.cache.get(e))}get(e){if(!e)return;let t=this.cache.get(e);if(void 0!==t)return this.touch(e),t}touch(e){let t=this.cache.get(e);void 0!==t&&(this.cache.delete(e),this.cache.set(e,t),this.evictIfNecessary())}evictIfNecessary(){for(;this.totalSize>this.maxSize&&this.cache.size>0;)this.evictLeastRecentlyUsed()}evictLeastRecentlyUsed(){let e=this.cache.keys().next().value;if(void 0!==e){let t=this.sizes.get(e)||0;this.totalSize-=t,this.cache.delete(e),this.sizes.delete(e)}}reset(){this.cache.clear(),this.sizes.clear(),this.totalSize=0}keys(){return[...this.cache.keys()]}remove(e){this.cache.has(e)&&(this.totalSize-=this.sizes.get(e)||0,this.cache.delete(e),this.sizes.delete(e))}clear(){this.cache.clear(),this.sizes.clear(),this.totalSize=0}get size(){return this.cache.size}get currentSize(){return this.totalSize}}r(356).Buffer,new eF(0x3200000,e=>e.size),process.env.NEXT_PRIVATE_DEBUG_CACHE||(()=>{}),Symbol.for("@next/cache-handlers"),Symbol.for("@next/cache-handlers-map");let eX=Symbol.for("@next/cache-handlers-set"),eY=globalThis;async function eQ(e,t){if(!e)return t();let r=eZ(e);try{return await t()}finally{let t=function(e,t){let r=new Set(e.pendingRevalidatedTags),n=new Set(e.pendingRevalidateWrites);return{pendingRevalidatedTags:t.pendingRevalidatedTags.filter(e=>!r.has(e)),pendingRevalidates:Object.fromEntries(Object.entries(t.pendingRevalidates).filter(([t])=>!(t in e.pendingRevalidates))),pendingRevalidateWrites:t.pendingRevalidateWrites.filter(e=>!n.has(e))}}(r,eZ(e));await e1(e,t)}}function eZ(e){return{pendingRevalidatedTags:e.pendingRevalidatedTags?[...e.pendingRevalidatedTags]:[],pendingRevalidates:{...e.pendingRevalidates},pendingRevalidateWrites:e.pendingRevalidateWrites?[...e.pendingRevalidateWrites]:[]}}async function e0(e,t){if(0===e.length)return;let r=[];t&&r.push(t.revalidateTag(e));let n=function(){if(eY[eX])return eY[eX].values()}();if(n)for(let t of n)r.push(t.expireTags(...e));await Promise.all(r)}async function e1(e,t){let r=(null==t?void 0:t.pendingRevalidatedTags)??e.pendingRevalidatedTags??[],n=(null==t?void 0:t.pendingRevalidates)??e.pendingRevalidates??{},i=(null==t?void 0:t.pendingRevalidateWrites)??e.pendingRevalidateWrites??[];return Promise.all([e0(r,e.incrementalCache),...Object.values(n),...i])}let e2=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class e5{disable(){throw e2}getStore(){}run(){throw e2}exit(){throw e2}enterWith(){throw e2}static bind(e){return e}}let e6="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage,e3=e6?new e6:new e5;class e4{constructor({waitUntil:e,onClose:t,onTaskError:r}){this.workUnitStores=new Set,this.waitUntil=e,this.onClose=t,this.onTaskError=r,this.callbackQueue=new(eV()),this.callbackQueue.pause()}after(e){if(eA(e))this.waitUntil||e8(),this.waitUntil(e.catch(e=>this.reportTaskError("promise",e)));else if("function"==typeof e)this.addCallback(e);else throw Object.defineProperty(Error("`after()`: Argument must be a promise or a function"),"__NEXT_ERROR_CODE",{value:"E50",enumerable:!1,configurable:!0})}addCallback(e){var t;this.waitUntil||e8();let r=es.getStore();r&&this.workUnitStores.add(r);let n=e3.getStore(),i=n?n.rootTaskSpawnPhase:null==r?void 0:r.phase;this.runCallbacksOnClosePromise||(this.runCallbacksOnClosePromise=this.runCallbacksOnClose(),this.waitUntil(this.runCallbacksOnClosePromise));let o=(t=async()=>{try{await e3.run({rootTaskSpawnPhase:i},()=>e())}catch(e){this.reportTaskError("function",e)}},e6?e6.bind(t):e5.bind(t));this.callbackQueue.add(o)}async runCallbacksOnClose(){return await new Promise(e=>this.onClose(e)),this.runCallbacks()}async runCallbacks(){if(0===this.callbackQueue.size)return;for(let e of this.workUnitStores)e.phase="after";let e=ea.getStore();if(!e)throw Object.defineProperty(new eG("Missing workStore in AfterContext.runCallbacks"),"__NEXT_ERROR_CODE",{value:"E547",enumerable:!1,configurable:!0});return eQ(e,()=>(this.callbackQueue.start(),this.callbackQueue.onIdle()))}reportTaskError(e,t){if(console.error("promise"===e?"A promise passed to `after()` rejected:":"An error occurred in a function passed to `after()`:",t),this.onTaskError)try{null==this.onTaskError||this.onTaskError.call(this,t)}catch(e){console.error(Object.defineProperty(new eG("`onTaskError` threw while handling an error thrown from an `after` task",{cause:e}),"__NEXT_ERROR_CODE",{value:"E569",enumerable:!1,configurable:!0}))}}}function e8(){throw Object.defineProperty(Error("`after()` will not work correctly, because `waitUntil` is not available in the current environment."),"__NEXT_ERROR_CODE",{value:"E91",enumerable:!1,configurable:!0})}class e9{onClose(e){if(this.isClosed)throw Object.defineProperty(Error("Cannot subscribe to a closed CloseController"),"__NEXT_ERROR_CODE",{value:"E365",enumerable:!1,configurable:!0});this.target.addEventListener("close",e),this.listeners++}dispatchClose(){if(this.isClosed)throw Object.defineProperty(Error("Cannot close a CloseController multiple times"),"__NEXT_ERROR_CODE",{value:"E229",enumerable:!1,configurable:!0});this.listeners>0&&this.target.dispatchEvent(new Event("close")),this.isClosed=!0}constructor(){this.target=new EventTarget,this.listeners=0,this.isClosed=!1}}function e7(){return{previewModeId:process.env.__NEXT_PREVIEW_MODE_ID,previewModeSigningKey:process.env.__NEXT_PREVIEW_MODE_SIGNING_KEY||"",previewModeEncryptionKey:process.env.__NEXT_PREVIEW_MODE_ENCRYPTION_KEY||""}}let te=Symbol.for("@next/request-context");class tt extends B{constructor(e){super(e.input,e.init),this.sourcePage=e.page}get request(){throw Object.defineProperty(new v({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}respondWith(){throw Object.defineProperty(new v({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}waitUntil(){throw Object.defineProperty(new v({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}}let tr={keys:e=>Array.from(e.keys()),get:(e,t)=>e.get(t)??void 0},tn=(e,t)=>eq().withPropagatedContext(e.headers,t,tr),ti=!1;async function to(e){var t;let n,i;if(!ti&&(ti=!0,"true"===process.env.NEXT_PRIVATE_TEST_PROXY)){let{interceptTestApis:e,wrapRequestHandler:t}=r(239);e(),tn=t(tn)}await m();let o=void 0!==globalThis.__BUILD_MANIFEST;e.request.url=e.request.url.replace(/\.rsc($|\?)/,"$1");let a=new K(e.request.url,{headers:e.request.headers,nextConfig:e.request.nextConfig});for(let e of[...a.searchParams.keys()]){let t=a.searchParams.getAll(e),r=function(e){for(let t of["nxtP","nxtI"])if(e!==t&&e.startsWith(t))return e.substring(t.length);return null}(e);if(r){for(let e of(a.searchParams.delete(r),t))a.searchParams.append(r,e);a.searchParams.delete(e)}}let s=a.buildId;a.buildId="";let l=function(e){let t=new Headers;for(let[r,n]of Object.entries(e))for(let e of Array.isArray(n)?n:[n])void 0!==e&&("number"==typeof e&&(e=e.toString()),t.append(r,e));return t}(e.request.headers),c=l.has("x-nextjs-data"),u="1"===l.get("RSC");c&&"/index"===a.pathname&&(a.pathname="/");let d=new Map;if(!o)for(let e of Q){let t=e.toLowerCase(),r=l.get(t);null!==r&&(d.set(t,r),l.delete(t))}let p=new tt({page:e.page,input:(function(e){let t="string"==typeof e,r=t?new URL(e):e;return r.searchParams.delete(Z),t?r.toString():r})(a).toString(),init:{body:e.request.body,headers:l,method:e.request.method,nextConfig:e.request.nextConfig,signal:e.request.signal}});c&&Object.defineProperty(p,"__isData",{enumerable:!1,value:!0}),!globalThis.__incrementalCache&&e.IncrementalCache&&(globalThis.__incrementalCache=new e.IncrementalCache({appDir:!0,fetchCache:!0,minimalMode:!0,fetchCacheKeyPrefix:"",dev:!1,requestHeaders:e.request.headers,requestProtocol:"https",getPrerenderManifest:()=>({version:-1,routes:{},dynamicRoutes:{},notFoundRoutes:[],preview:e7()})}));let h=e.request.waitUntil??(null==(t=function(){let e=globalThis[te];return null==e?void 0:e.get()}())?void 0:t.waitUntil),f=new O({request:p,page:e.page,context:h?{waitUntil:h}:void 0});if((n=await tn(p,()=>{if("/middleware"===e.page||"/src/middleware"===e.page){let t=f.waitUntil.bind(f),r=new e9;return eq().trace(ex.execute,{spanName:`middleware ${p.method} ${p.nextUrl.pathname}`,attributes:{"http.target":p.nextUrl.pathname,"http.method":p.method}},async()=>{try{var n,o,a,l,c,u,d;let h=e7(),g=(c=p.nextUrl,u=void 0,d=e=>{i=e},function(e,t,r,n,i,o,a,s,l,c,u){function d(e){r&&r.setHeader("Set-Cookie",e)}let p={};return{type:"request",phase:e,implicitTags:o,url:{pathname:n.pathname,search:n.search??""},rootParams:i,get headers(){return p.headers||(p.headers=function(e){let t=et.from(e);for(let e of Q)t.delete(e.toLowerCase());return et.seal(t)}(t.headers)),p.headers},get cookies(){if(!p.cookies){let e=new q.RequestCookies(et.from(t.headers));ez(t,e),p.cookies=ec.seal(e)}return p.cookies},set cookies(value){p.cookies=value},get mutableCookies(){if(!p.mutableCookies){let e=function(e,t){let r=new q.RequestCookies(et.from(e));return ed.wrap(r,t)}(t.headers,a||(r?d:void 0));ez(t,e),p.mutableCookies=e}return p.mutableCookies},get userspaceMutableCookies(){return p.userspaceMutableCookies||(p.userspaceMutableCookies=function(e){let t=new Proxy(e,{get(e,r,n){switch(r){case"delete":return function(...r){return ep("cookies().delete"),e.delete(...r),t};case"set":return function(...r){return ep("cookies().set"),e.set(...r),t};default:return z.get(e,r,n)}}});return t}(this.mutableCookies)),p.userspaceMutableCookies},get draftMode(){return p.draftMode||(p.draftMode=new eB(l,t,this.cookies,this.mutableCookies)),p.draftMode},renderResumeDataCache:s??null,isHmrRefresh:c,serverComponentsHmrCache:u||globalThis.__serverComponentsHmrCache}}("action",p,void 0,c,{},u,d,void 0,h,!1,void 0)),y=function({page:e,fallbackRouteParams:t,renderOpts:r,requestEndedState:n,isPrefetchRequest:i,buildId:o,previouslyRevalidatedTags:a}){var s;let l={isStaticGeneration:!r.shouldWaitOnAllReady&&!r.supportsDynamicResponse&&!r.isDraftMode&&!r.isServerAction,page:e,fallbackRouteParams:t,route:(s=e.split("/").reduce((e,t,r,n)=>t?"("===t[0]&&t.endsWith(")")||"@"===t[0]||("page"===t||"route"===t)&&r===n.length-1?e:e+"/"+t:e,"")).startsWith("/")?s:"/"+s,incrementalCache:r.incrementalCache||globalThis.__incrementalCache,cacheLifeProfiles:r.cacheLifeProfiles,isRevalidate:r.isRevalidate,isPrerendering:r.nextExport,fetchCache:r.fetchCache,isOnDemandRevalidate:r.isOnDemandRevalidate,isDraftMode:r.isDraftMode,requestEndedState:n,isPrefetchRequest:i,buildId:o,reactLoadableManifest:(null==r?void 0:r.reactLoadableManifest)||{},assetPrefix:(null==r?void 0:r.assetPrefix)||"",afterContext:function(e){let{waitUntil:t,onClose:r,onAfterTaskError:n}=e;return new e4({waitUntil:t,onClose:r,onTaskError:n})}(r),dynamicIOEnabled:r.experimental.dynamicIO,dev:r.dev??!1,previouslyRevalidatedTags:a};return r.store=l,l}({page:"/",fallbackRouteParams:null,renderOpts:{cacheLifeProfiles:null==(o=e.request.nextConfig)||null==(n=o.experimental)?void 0:n.cacheLife,experimental:{isRoutePPREnabled:!1,dynamicIO:!1,authInterrupts:!!(null==(l=e.request.nextConfig)||null==(a=l.experimental)?void 0:a.authInterrupts)},supportsDynamicResponse:!0,waitUntil:t,onClose:r.onClose.bind(r),onAfterTaskError:void 0},requestEndedState:{ended:!1},isPrefetchRequest:p.headers.has(Y),buildId:s??"",previouslyRevalidatedTags:[]});return await ea.run(y,()=>es.run(g,e.handler,p,f))}finally{setTimeout(()=>{r.dispatchClose()},0)}})}return e.handler(p,f)}))&&!(n instanceof Response))throw Object.defineProperty(TypeError("Expected an instance of Response to be returned"),"__NEXT_ERROR_CODE",{value:"E567",enumerable:!1,configurable:!0});n&&i&&n.headers.set("set-cookie",i);let g=null==n?void 0:n.headers.get("x-middleware-rewrite");if(n&&g&&(u||!o)){let t=new K(g,{forceLocale:!0,headers:e.request.headers,nextConfig:e.request.nextConfig});o||t.host!==p.nextUrl.host||(t.buildId=s||t.buildId,n.headers.set("x-middleware-rewrite",String(t)));let{url:r,isRelative:i}=X(t.toString(),a.toString());!o&&c&&n.headers.set("x-nextjs-rewrite",r),u&&i&&(a.pathname!==t.pathname&&n.headers.set("x-nextjs-rewritten-path",t.pathname),a.search!==t.search&&n.headers.set("x-nextjs-rewritten-query",t.search.slice(1)))}let y=null==n?void 0:n.headers.get("Location");if(n&&y&&!o){let t=new K(y,{forceLocale:!1,headers:e.request.headers,nextConfig:e.request.nextConfig});n=new Response(n.body,n),t.host===a.host&&(t.buildId=s||t.buildId,n.headers.set("Location",t.toString())),c&&(n.headers.delete("Location"),n.headers.set("x-nextjs-redirect",X(t.toString(),a.toString()).url))}let w=n||F.next(),v=w.headers.get("x-middleware-override-headers"),b=[];if(v){for(let[e,t]of d)w.headers.set(`x-middleware-request-${e}`,t),b.push(e);b.length>0&&w.headers.set("x-middleware-override-headers",v+","+b.join(","))}return{response:w,waitUntil:("internal"===f[k].kind?Promise.all(f[k].promises).then(()=>{}):void 0)??Promise.resolve(),fetchMetrics:p.fetchMetrics}}r(450),"undefined"==typeof URLPattern||URLPattern;var ta=r(958);new WeakMap;let ts="function"==typeof ta.unstable_postpone;function tl(e,t){return`Route ${e} needs to bail out of prerendering at this point because it used ${t}. React throws this special object to indicate where. It should not be caught by your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`}if(!1===function(e){return e.includes("needs to bail out of prerendering at this point because it used")&&e.includes("Learn more: https://nextjs.org/docs/messages/ppr-caught-error")}(tl("%%%","^^^")))throw Object.defineProperty(Error("Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E296",enumerable:!1,configurable:!0});RegExp(`\\n\\s+at __next_metadata_boundary__[\\n\\s]`),RegExp(`\\n\\s+at __next_viewport_boundary__[\\n\\s]`),RegExp(`\\n\\s+at __next_outlet_boundary__[\\n\\s]`),new WeakMap;let tc=()=>{if("undefined"!=typeof globalThis)return globalThis;if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;throw Error("unable to locate global object")},tu=async(e,t,r,n,i)=>{let{crypto:{subtle:o}}=tc();return new Uint8Array(await o.deriveBits({name:"HKDF",hash:`SHA-${e.substr(3)}`,salt:r,info:n},await o.importKey("raw",t,"HKDF",!1,["deriveBits"]),i<<3))};function td(e,t){if("string"==typeof e)return new TextEncoder().encode(e);if(!(e instanceof Uint8Array))throw TypeError(`"${t}"" must be an instance of Uint8Array or a string`);return e}async function tp(e,t,r,n,i){return tu(function(e){switch(e){case"sha256":case"sha384":case"sha512":case"sha1":return e;default:throw TypeError('unsupported "digest" value')}}(e),function(e){let t=td(e,"ikm");if(!t.byteLength)throw TypeError('"ikm" must be at least one byte in length');return t}(t),td(r,"salt"),function(e){let t=td(e,"info");if(t.byteLength>1024)throw TypeError('"info" must not contain more than 1024 bytes');return t}(n),function(e,t){if("number"!=typeof e||!Number.isInteger(e)||e<1)throw TypeError('"keylen" must be a positive integer');if(e>255*(parseInt(t.substr(3),10)>>3||20))throw TypeError('"keylen" too large');return e}(i,e))}let th=crypto,tf=e=>e instanceof CryptoKey,tg=async(e,t)=>{let r=`SHA-${e.slice(-3)}`;return new Uint8Array(await th.subtle.digest(r,t))},ty=new TextEncoder,tm=new TextDecoder;function tw(...e){let t=new Uint8Array(e.reduce((e,{length:t})=>e+t,0)),r=0;for(let n of e)t.set(n,r),r+=n.length;return t}function tv(e,t,r){if(t<0||t>=0x100000000)throw RangeError(`value must be >= 0 and <= ${0x100000000-1}. Received ${t}`);e.set([t>>>24,t>>>16,t>>>8,255&t],r)}function tb(e){let t=new Uint8Array(4);return tv(t,e),t}function tE(e){return tw(tb(e.length),e)}async function t_(e,t,r){let n=Math.ceil((t>>3)/32),i=new Uint8Array(32*n);for(let t=0;t<n;t++){let n=new Uint8Array(4+e.length+r.length);n.set(tb(t+1)),n.set(e,4),n.set(r,4+e.length),i.set(await tg("sha256",n),32*t)}return i.slice(0,t>>3)}let tS=e=>{let t=e;"string"==typeof t&&(t=ty.encode(t));let r=[];for(let e=0;e<t.length;e+=32768)r.push(String.fromCharCode.apply(null,t.subarray(e,e+32768)));return btoa(r.join(""))},tx=e=>tS(e).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_"),tR=e=>{let t=atob(e),r=new Uint8Array(t.length);for(let e=0;e<t.length;e++)r[e]=t.charCodeAt(e);return r},tC=e=>{let t=e;t instanceof Uint8Array&&(t=tm.decode(t)),t=t.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"");try{return tR(t)}catch{throw TypeError("The input to be decoded is not correctly encoded.")}};class tA extends Error{constructor(e,t){super(e,t),this.code="ERR_JOSE_GENERIC",this.name=this.constructor.name,Error.captureStackTrace?.(this,this.constructor)}}tA.code="ERR_JOSE_GENERIC";class tk extends tA{constructor(e,t,r="unspecified",n="unspecified"){super(e,{cause:{claim:r,reason:n,payload:t}}),this.code="ERR_JWT_CLAIM_VALIDATION_FAILED",this.claim=r,this.reason=n,this.payload=t}}tk.code="ERR_JWT_CLAIM_VALIDATION_FAILED";class tT extends tA{constructor(e,t,r="unspecified",n="unspecified"){super(e,{cause:{claim:r,reason:n,payload:t}}),this.code="ERR_JWT_EXPIRED",this.claim=r,this.reason=n,this.payload=t}}tT.code="ERR_JWT_EXPIRED";class tO extends tA{constructor(){super(...arguments),this.code="ERR_JOSE_ALG_NOT_ALLOWED"}}tO.code="ERR_JOSE_ALG_NOT_ALLOWED";class tP extends tA{constructor(){super(...arguments),this.code="ERR_JOSE_NOT_SUPPORTED"}}tP.code="ERR_JOSE_NOT_SUPPORTED";class tI extends tA{constructor(e="decryption operation failed",t){super(e,t),this.code="ERR_JWE_DECRYPTION_FAILED"}}tI.code="ERR_JWE_DECRYPTION_FAILED";class tN extends tA{constructor(){super(...arguments),this.code="ERR_JWE_INVALID"}}tN.code="ERR_JWE_INVALID";class tj extends tA{constructor(){super(...arguments),this.code="ERR_JWS_INVALID"}}tj.code="ERR_JWS_INVALID";class tM extends tA{constructor(){super(...arguments),this.code="ERR_JWT_INVALID"}}tM.code="ERR_JWT_INVALID";class tL extends tA{constructor(){super(...arguments),this.code="ERR_JWK_INVALID"}}tL.code="ERR_JWK_INVALID";class tD extends tA{constructor(){super(...arguments),this.code="ERR_JWKS_INVALID"}}tD.code="ERR_JWKS_INVALID";class tW extends tA{constructor(e="no applicable key found in the JSON Web Key Set",t){super(e,t),this.code="ERR_JWKS_NO_MATCHING_KEY"}}tW.code="ERR_JWKS_NO_MATCHING_KEY";class tU extends tA{constructor(e="multiple matching keys found in the JSON Web Key Set",t){super(e,t),this.code="ERR_JWKS_MULTIPLE_MATCHING_KEYS"}}Symbol.asyncIterator,tU.code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";class tH extends tA{constructor(e="request timed out",t){super(e,t),this.code="ERR_JWKS_TIMEOUT"}}tH.code="ERR_JWKS_TIMEOUT";class tK extends tA{constructor(e="signature verification failed",t){super(e,t),this.code="ERR_JWS_SIGNATURE_VERIFICATION_FAILED"}}tK.code="ERR_JWS_SIGNATURE_VERIFICATION_FAILED";let tq=(e,t)=>{if(t.length<<3!==function(e){switch(e){case"A128GCM":case"A128GCMKW":case"A192GCM":case"A192GCMKW":case"A256GCM":case"A256GCMKW":return 96;case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return 128;default:throw new tP(`Unsupported JWE Algorithm: ${e}`)}}(e))throw new tN("Invalid Initialization Vector length")},t$=(e,t)=>{let r=e.byteLength<<3;if(r!==t)throw new tN(`Invalid Content Encryption Key length. Expected ${t} bits, got ${r} bits`)},tB=(e,t)=>{if(!(e instanceof Uint8Array))throw TypeError("First argument must be a buffer");if(!(t instanceof Uint8Array))throw TypeError("Second argument must be a buffer");if(e.length!==t.length)throw TypeError("Input buffers must have the same length");let r=e.length,n=0,i=-1;for(;++i<r;)n|=e[i]^t[i];return 0===n};function tz(e,t="algorithm.name"){return TypeError(`CryptoKey does not support this operation, its ${t} must be ${e}`)}function tJ(e,t){return e.name===t}function tV(e,t,...r){switch(t){case"A128GCM":case"A192GCM":case"A256GCM":{if(!tJ(e.algorithm,"AES-GCM"))throw tz("AES-GCM");let r=parseInt(t.slice(1,4),10);if(e.algorithm.length!==r)throw tz(r,"algorithm.length");break}case"A128KW":case"A192KW":case"A256KW":{if(!tJ(e.algorithm,"AES-KW"))throw tz("AES-KW");let r=parseInt(t.slice(1,4),10);if(e.algorithm.length!==r)throw tz(r,"algorithm.length");break}case"ECDH":switch(e.algorithm.name){case"ECDH":case"X25519":case"X448":break;default:throw tz("ECDH, X25519, or X448")}break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":if(!tJ(e.algorithm,"PBKDF2"))throw tz("PBKDF2");break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":{if(!tJ(e.algorithm,"RSA-OAEP"))throw tz("RSA-OAEP");let r=parseInt(t.slice(9),10)||1;if(parseInt(e.algorithm.hash.name.slice(4),10)!==r)throw tz(`SHA-${r}`,"algorithm.hash");break}default:throw TypeError("CryptoKey does not support this operation")}var n=e,i=r;if(i.length&&!i.some(e=>n.usages.includes(e))){let e="CryptoKey does not support this operation, its usages must include ";if(i.length>2){let t=i.pop();e+=`one of ${i.join(", ")}, or ${t}.`}else 2===i.length?e+=`one of ${i[0]} or ${i[1]}.`:e+=`${i[0]}.`;throw TypeError(e)}}function tG(e,t,...r){if((r=r.filter(Boolean)).length>2){let t=r.pop();e+=`one of type ${r.join(", ")}, or ${t}.`}else 2===r.length?e+=`one of type ${r[0]} or ${r[1]}.`:e+=`of type ${r[0]}.`;return null==t?e+=` Received ${t}`:"function"==typeof t&&t.name?e+=` Received function ${t.name}`:"object"==typeof t&&null!=t&&t.constructor?.name&&(e+=` Received an instance of ${t.constructor.name}`),e}let tF=(e,...t)=>tG("Key must be ",e,...t);function tX(e,t,...r){return tG(`Key for the ${e} algorithm must be `,t,...r)}let tY=e=>!!tf(e)||e?.[Symbol.toStringTag]==="KeyObject",tQ=["CryptoKey"];async function tZ(e,t,r,n,i,o){let a,s;if(!(t instanceof Uint8Array))throw TypeError(tF(t,"Uint8Array"));let l=parseInt(e.slice(1,4),10),c=await th.subtle.importKey("raw",t.subarray(l>>3),"AES-CBC",!1,["decrypt"]),u=await th.subtle.importKey("raw",t.subarray(0,l>>3),{hash:`SHA-${l<<1}`,name:"HMAC"},!1,["sign"]),d=tw(o,n,r,function(e){let t=Math.floor(e/0x100000000),r=new Uint8Array(8);return tv(r,t,0),tv(r,e%0x100000000,4),r}(o.length<<3)),p=new Uint8Array((await th.subtle.sign("HMAC",u,d)).slice(0,l>>3));try{a=tB(i,p)}catch{}if(!a)throw new tI;try{s=new Uint8Array(await th.subtle.decrypt({iv:n,name:"AES-CBC"},c,r))}catch{}if(!s)throw new tI;return s}async function t0(e,t,r,n,i,o){let a;t instanceof Uint8Array?a=await th.subtle.importKey("raw",t,"AES-GCM",!1,["decrypt"]):(tV(t,e,"decrypt"),a=t);try{return new Uint8Array(await th.subtle.decrypt({additionalData:o,iv:n,name:"AES-GCM",tagLength:128},a,tw(r,i)))}catch{throw new tI}}let t1=async(e,t,r,n,i,o)=>{if(!tf(t)&&!(t instanceof Uint8Array))throw TypeError(tF(t,...tQ,"Uint8Array"));if(!n)throw new tN("JWE Initialization Vector missing");if(!i)throw new tN("JWE Authentication Tag missing");switch(tq(e,n),e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return t instanceof Uint8Array&&t$(t,parseInt(e.slice(-3),10)),tZ(e,t,r,n,i,o);case"A128GCM":case"A192GCM":case"A256GCM":return t instanceof Uint8Array&&t$(t,parseInt(e.slice(1,4),10)),t0(e,t,r,n,i,o);default:throw new tP("Unsupported JWE Content Encryption Algorithm")}},t2=(...e)=>{let t,r=e.filter(Boolean);if(0===r.length||1===r.length)return!0;for(let e of r){let r=Object.keys(e);if(!t||0===t.size){t=new Set(r);continue}for(let e of r){if(t.has(e))return!1;t.add(e)}}return!0};function t5(e){if("object"!=typeof e||null===e||"[object Object]"!==Object.prototype.toString.call(e))return!1;if(null===Object.getPrototypeOf(e))return!0;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}let t6=[{hash:"SHA-256",name:"HMAC"},!0,["sign"]],t3=async(e,t,r)=>{let n=await function(e,t,r){if(tf(e))return tV(e,t,r),e;if(e instanceof Uint8Array)return th.subtle.importKey("raw",e,"AES-KW",!0,[r]);throw TypeError(tF(e,...tQ,"Uint8Array"))}(t,e,"unwrapKey");!function(e,t){if(e.algorithm.length!==parseInt(t.slice(1,4),10))throw TypeError(`Invalid key size for alg: ${t}`)}(n,e);let i=await th.subtle.unwrapKey("raw",r,n,"AES-KW",...t6);return new Uint8Array(await th.subtle.exportKey("raw",i))};async function t4(e,t,r,n,i=new Uint8Array(0),o=new Uint8Array(0)){let a;if(!tf(e))throw TypeError(tF(e,...tQ));if(tV(e,"ECDH"),!tf(t))throw TypeError(tF(t,...tQ));tV(t,"ECDH","deriveBits");let s=tw(tE(ty.encode(r)),tE(i),tE(o),tb(n));return a="X25519"===e.algorithm.name?256:"X448"===e.algorithm.name?448:Math.ceil(parseInt(e.algorithm.namedCurve.substr(-3),10)/8)<<3,t_(new Uint8Array(await th.subtle.deriveBits({name:e.algorithm.name,public:e},t,a)),n,s)}async function t8(e,t,r,n){if(!(e instanceof Uint8Array)||e.length<8)throw new tN("PBES2 Salt Input must be 8 or more octets");let i=tw(ty.encode(t),new Uint8Array([0]),e),o=parseInt(t.slice(13,16),10),a={hash:`SHA-${t.slice(8,11)}`,iterations:r,name:"PBKDF2",salt:i},s=await function(e,t){if(e instanceof Uint8Array)return th.subtle.importKey("raw",e,"PBKDF2",!1,["deriveBits"]);if(tf(e))return tV(e,t,"deriveBits","deriveKey"),e;throw TypeError(tF(e,...tQ,"Uint8Array"))}(n,t);if(s.usages.includes("deriveBits"))return new Uint8Array(await th.subtle.deriveBits(a,s,o));if(s.usages.includes("deriveKey"))return th.subtle.deriveKey(a,s,{length:o,name:"AES-KW"},!1,["wrapKey","unwrapKey"]);throw TypeError('PBKDF2 key "usages" must include "deriveBits" or "deriveKey"')}let t9=async(e,t,r,n,i)=>{let o=await t8(i,e,n,t);return t3(e.slice(-6),o,r)};function t7(e){switch(e){case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":return"RSA-OAEP";default:throw new tP(`alg ${e} is not supported either by JOSE or your javascript runtime`)}}let re=(e,t)=>{if(e.startsWith("RS")||e.startsWith("PS")){let{modulusLength:r}=t.algorithm;if("number"!=typeof r||r<2048)throw TypeError(`${e} requires key modulusLength to be 2048 bits or larger`)}},rt=async(e,t,r)=>{if(!tf(t))throw TypeError(tF(t,...tQ));if(tV(t,e,"decrypt","unwrapKey"),re(e,t),t.usages.includes("decrypt"))return new Uint8Array(await th.subtle.decrypt(t7(e),t,r));if(t.usages.includes("unwrapKey")){let n=await th.subtle.unwrapKey("raw",r,t,t7(e),...t6);return new Uint8Array(await th.subtle.exportKey("raw",n))}throw TypeError('RSA-OAEP key "usages" must include "decrypt" or "unwrapKey" for this operation')};function rr(e){return t5(e)&&"string"==typeof e.kty}let rn=async e=>{if(!e.alg)throw TypeError('"alg" argument is required when "jwk.alg" is not present');let{algorithm:t,keyUsages:r}=function(e){let t,r;switch(e.kty){case"RSA":switch(e.alg){case"PS256":case"PS384":case"PS512":t={name:"RSA-PSS",hash:`SHA-${e.alg.slice(-3)}`},r=e.d?["sign"]:["verify"];break;case"RS256":case"RS384":case"RS512":t={name:"RSASSA-PKCS1-v1_5",hash:`SHA-${e.alg.slice(-3)}`},r=e.d?["sign"]:["verify"];break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":t={name:"RSA-OAEP",hash:`SHA-${parseInt(e.alg.slice(-3),10)||1}`},r=e.d?["decrypt","unwrapKey"]:["encrypt","wrapKey"];break;default:throw new tP('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"EC":switch(e.alg){case"ES256":t={name:"ECDSA",namedCurve:"P-256"},r=e.d?["sign"]:["verify"];break;case"ES384":t={name:"ECDSA",namedCurve:"P-384"},r=e.d?["sign"]:["verify"];break;case"ES512":t={name:"ECDSA",namedCurve:"P-521"},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:"ECDH",namedCurve:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new tP('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"OKP":switch(e.alg){case"Ed25519":t={name:"Ed25519"},r=e.d?["sign"]:["verify"];break;case"EdDSA":t={name:e.crv},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new tP('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;default:throw new tP('Invalid or unsupported JWK "kty" (Key Type) Parameter value')}return{algorithm:t,keyUsages:r}}(e),n=[t,e.ext??!1,e.key_ops??r],i={...e};return delete i.alg,delete i.use,th.subtle.importKey("jwk",i,...n)},ri=e=>tC(e),ro=e=>e?.[Symbol.toStringTag]==="KeyObject",ra=async(e,t,r,n,i=!1)=>{let o=e.get(t);if(o?.[n])return o[n];let a=await rn({...r,alg:n});return i&&Object.freeze(t),o?o[n]=a:e.set(t,{[n]:a}),a},rs={normalizePrivateKey:(e,t)=>{if(ro(e)){let r=e.export({format:"jwk"});return r.k?ri(r.k):(i||(i=new WeakMap),ra(i,e,r,t))}return rr(e)?e.k?tC(e.k):(i||(i=new WeakMap),ra(i,e,e,t,!0)):e}},rl=th.getRandomValues.bind(th);function rc(e){switch(e){case"A128GCM":return 128;case"A192GCM":return 192;case"A256GCM":case"A128CBC-HS256":return 256;case"A192CBC-HS384":return 384;case"A256CBC-HS512":return 512;default:throw new tP(`Unsupported JWE Algorithm: ${e}`)}}let ru=e=>rl(new Uint8Array(rc(e)>>3));async function rd(e,t){if(!t5(e))throw TypeError("JWK must be an object");switch(t||(t=e.alg),e.kty){case"oct":if("string"!=typeof e.k||!e.k)throw TypeError('missing "k" (Key Value) Parameter value');return tC(e.k);case"RSA":if("oth"in e&&void 0!==e.oth)throw new tP('RSA JWK "oth" (Other Primes Info) Parameter value is not supported');case"EC":case"OKP":return rn({...e,alg:t});default:throw new tP('Unsupported "kty" (Key Type) Parameter value')}}let rp=e=>e?.[Symbol.toStringTag],rh=(e,t,r)=>{if(void 0!==t.use&&"sig"!==t.use)throw TypeError("Invalid key for this operation, when present its use must be sig");if(void 0!==t.key_ops&&t.key_ops.includes?.(r)!==!0)throw TypeError(`Invalid key for this operation, when present its key_ops must include ${r}`);if(void 0!==t.alg&&t.alg!==e)throw TypeError(`Invalid key for this operation, when present its alg must be ${e}`);return!0},rf=(e,t,r,n)=>{if(!(t instanceof Uint8Array)){if(n&&rr(t)){if(function(e){return rr(e)&&"oct"===e.kty&&"string"==typeof e.k}(t)&&rh(e,t,r))return;throw TypeError('JSON Web Key for symmetric algorithms must have JWK "kty" (Key Type) equal to "oct" and the JWK "k" (Key Value) present')}if(!tY(t))throw TypeError(tX(e,t,...tQ,"Uint8Array",n?"JSON Web Key":null));if("secret"!==t.type)throw TypeError(`${rp(t)} instances for symmetric algorithms must be of type "secret"`)}},rg=(e,t,r,n)=>{if(n&&rr(t))switch(r){case"sign":if(function(e){return"oct"!==e.kty&&"string"==typeof e.d}(t)&&rh(e,t,r))return;throw TypeError("JSON Web Key for this operation be a private JWK");case"verify":if(function(e){return"oct"!==e.kty&&void 0===e.d}(t)&&rh(e,t,r))return;throw TypeError("JSON Web Key for this operation be a public JWK")}if(!tY(t))throw TypeError(tX(e,t,...tQ,n?"JSON Web Key":null));if("secret"===t.type)throw TypeError(`${rp(t)} instances for asymmetric algorithms must not be of type "secret"`);if("sign"===r&&"public"===t.type)throw TypeError(`${rp(t)} instances for asymmetric algorithm signing must be of type "private"`);if("decrypt"===r&&"public"===t.type)throw TypeError(`${rp(t)} instances for asymmetric algorithm decryption must be of type "private"`);if(t.algorithm&&"verify"===r&&"private"===t.type)throw TypeError(`${rp(t)} instances for asymmetric algorithm verifying must be of type "public"`);if(t.algorithm&&"encrypt"===r&&"private"===t.type)throw TypeError(`${rp(t)} instances for asymmetric algorithm encryption must be of type "public"`)};function ry(e,t,r,n){t.startsWith("HS")||"dir"===t||t.startsWith("PBES2")||/^A\d{3}(?:GCM)?KW$/.test(t)?rf(t,r,n,e):rg(t,r,n,e)}let rm=ry.bind(void 0,!1);async function rw(e,t,r,n,i){return t1(e.slice(0,7),t,r,n,i,new Uint8Array(0))}async function rv(e,t,r,n,i){switch(rm(e,t,"decrypt"),t=await rs.normalizePrivateKey?.(t,e)||t,e){case"dir":if(void 0!==r)throw new tN("Encountered unexpected JWE Encrypted Key");return t;case"ECDH-ES":if(void 0!==r)throw new tN("Encountered unexpected JWE Encrypted Key");case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{let i,o;if(!t5(n.epk))throw new tN('JOSE Header "epk" (Ephemeral Public Key) missing or invalid');if(!function(e){if(!tf(e))throw TypeError(tF(e,...tQ));return["P-256","P-384","P-521"].includes(e.algorithm.namedCurve)||"X25519"===e.algorithm.name||"X448"===e.algorithm.name}(t))throw new tP("ECDH with the provided key is not allowed or not supported by your javascript runtime");let a=await rd(n.epk,e);if(void 0!==n.apu){if("string"!=typeof n.apu)throw new tN('JOSE Header "apu" (Agreement PartyUInfo) invalid');try{i=tC(n.apu)}catch{throw new tN("Failed to base64url decode the apu")}}if(void 0!==n.apv){if("string"!=typeof n.apv)throw new tN('JOSE Header "apv" (Agreement PartyVInfo) invalid');try{o=tC(n.apv)}catch{throw new tN("Failed to base64url decode the apv")}}let s=await t4(a,t,"ECDH-ES"===e?n.enc:e,"ECDH-ES"===e?rc(n.enc):parseInt(e.slice(-5,-2),10),i,o);if("ECDH-ES"===e)return s;if(void 0===r)throw new tN("JWE Encrypted Key missing");return t3(e.slice(-6),s,r)}case"RSA1_5":case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":if(void 0===r)throw new tN("JWE Encrypted Key missing");return rt(e,t,r);case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{let o;if(void 0===r)throw new tN("JWE Encrypted Key missing");if("number"!=typeof n.p2c)throw new tN('JOSE Header "p2c" (PBES2 Count) missing or invalid');let a=i?.maxPBES2Count||1e4;if(n.p2c>a)throw new tN('JOSE Header "p2c" (PBES2 Count) out is of acceptable bounds');if("string"!=typeof n.p2s)throw new tN('JOSE Header "p2s" (PBES2 Salt) missing or invalid');try{o=tC(n.p2s)}catch{throw new tN("Failed to base64url decode the p2s")}return t9(e,t,r,n.p2c,o)}case"A128KW":case"A192KW":case"A256KW":if(void 0===r)throw new tN("JWE Encrypted Key missing");return t3(e,t,r);case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{let i,o;if(void 0===r)throw new tN("JWE Encrypted Key missing");if("string"!=typeof n.iv)throw new tN('JOSE Header "iv" (Initialization Vector) missing or invalid');if("string"!=typeof n.tag)throw new tN('JOSE Header "tag" (Authentication Tag) missing or invalid');try{i=tC(n.iv)}catch{throw new tN("Failed to base64url decode the iv")}try{o=tC(n.tag)}catch{throw new tN("Failed to base64url decode the tag")}return rw(e,t,r,i,o)}default:throw new tP('Invalid or unsupported "alg" (JWE Algorithm) header value')}}ry.bind(void 0,!0);let rb=function(e,t,r,n,i){let o;if(void 0!==i.crit&&n?.crit===void 0)throw new e('"crit" (Critical) Header Parameter MUST be integrity protected');if(!n||void 0===n.crit)return new Set;if(!Array.isArray(n.crit)||0===n.crit.length||n.crit.some(e=>"string"!=typeof e||0===e.length))throw new e('"crit" (Critical) Header Parameter MUST be an array of non-empty strings when present');for(let a of(o=void 0!==r?new Map([...Object.entries(r),...t.entries()]):t,n.crit)){if(!o.has(a))throw new tP(`Extension Header Parameter "${a}" is not recognized`);if(void 0===i[a])throw new e(`Extension Header Parameter "${a}" is missing`);if(o.get(a)&&void 0===n[a])throw new e(`Extension Header Parameter "${a}" MUST be integrity protected`)}return new Set(n.crit)},rE=(e,t)=>{if(void 0!==t&&(!Array.isArray(t)||t.some(e=>"string"!=typeof e)))throw TypeError(`"${e}" option must be an array of strings`);if(t)return new Set(t)};async function r_(e,t,r){let n,i,o,a,s,l,c;if(!t5(e))throw new tN("Flattened JWE must be an object");if(void 0===e.protected&&void 0===e.header&&void 0===e.unprotected)throw new tN("JOSE Header missing");if(void 0!==e.iv&&"string"!=typeof e.iv)throw new tN("JWE Initialization Vector incorrect type");if("string"!=typeof e.ciphertext)throw new tN("JWE Ciphertext missing or incorrect type");if(void 0!==e.tag&&"string"!=typeof e.tag)throw new tN("JWE Authentication Tag incorrect type");if(void 0!==e.protected&&"string"!=typeof e.protected)throw new tN("JWE Protected Header incorrect type");if(void 0!==e.encrypted_key&&"string"!=typeof e.encrypted_key)throw new tN("JWE Encrypted Key incorrect type");if(void 0!==e.aad&&"string"!=typeof e.aad)throw new tN("JWE AAD incorrect type");if(void 0!==e.header&&!t5(e.header))throw new tN("JWE Shared Unprotected Header incorrect type");if(void 0!==e.unprotected&&!t5(e.unprotected))throw new tN("JWE Per-Recipient Unprotected Header incorrect type");if(e.protected)try{let t=tC(e.protected);n=JSON.parse(tm.decode(t))}catch{throw new tN("JWE Protected Header is invalid")}if(!t2(n,e.header,e.unprotected))throw new tN("JWE Protected, JWE Unprotected Header, and JWE Per-Recipient Unprotected Header Parameter names must be disjoint");let u={...n,...e.header,...e.unprotected};if(rb(tN,new Map,r?.crit,n,u),void 0!==u.zip)throw new tP('JWE "zip" (Compression Algorithm) Header Parameter is not supported.');let{alg:d,enc:p}=u;if("string"!=typeof d||!d)throw new tN("missing JWE Algorithm (alg) in JWE Header");if("string"!=typeof p||!p)throw new tN("missing JWE Encryption Algorithm (enc) in JWE Header");let h=r&&rE("keyManagementAlgorithms",r.keyManagementAlgorithms),f=r&&rE("contentEncryptionAlgorithms",r.contentEncryptionAlgorithms);if(h&&!h.has(d)||!h&&d.startsWith("PBES2"))throw new tO('"alg" (Algorithm) Header Parameter value not allowed');if(f&&!f.has(p))throw new tO('"enc" (Encryption Algorithm) Header Parameter value not allowed');if(void 0!==e.encrypted_key)try{i=tC(e.encrypted_key)}catch{throw new tN("Failed to base64url decode the encrypted_key")}let g=!1;"function"==typeof t&&(t=await t(n,e),g=!0);try{o=await rv(d,t,i,u,r)}catch(e){if(e instanceof TypeError||e instanceof tN||e instanceof tP)throw e;o=ru(p)}if(void 0!==e.iv)try{a=tC(e.iv)}catch{throw new tN("Failed to base64url decode the iv")}if(void 0!==e.tag)try{s=tC(e.tag)}catch{throw new tN("Failed to base64url decode the tag")}let y=ty.encode(e.protected??"");l=void 0!==e.aad?tw(y,ty.encode("."),ty.encode(e.aad)):y;try{c=tC(e.ciphertext)}catch{throw new tN("Failed to base64url decode the ciphertext")}let m={plaintext:await t1(p,o,c,a,s,l)};if(void 0!==e.protected&&(m.protectedHeader=n),void 0!==e.aad)try{m.additionalAuthenticatedData=tC(e.aad)}catch{throw new tN("Failed to base64url decode the aad")}return(void 0!==e.unprotected&&(m.sharedUnprotectedHeader=e.unprotected),void 0!==e.header&&(m.unprotectedHeader=e.header),g)?{...m,key:t}:m}async function rS(e,t,r){if(e instanceof Uint8Array&&(e=tm.decode(e)),"string"!=typeof e)throw new tN("Compact JWE must be a string or Uint8Array");let{0:n,1:i,2:o,3:a,4:s,length:l}=e.split(".");if(5!==l)throw new tN("Invalid Compact JWE");let c=await r_({ciphertext:a,iv:o||void 0,protected:n,tag:s||void 0,encrypted_key:i||void 0},t,r),u={plaintext:c.plaintext,protectedHeader:c.protectedHeader};return"function"==typeof t?{...u,key:c.key}:u}let rx=e=>Math.floor(e.getTime()/1e3),rR=/^(\+|\-)? ?(\d+|\d+\.\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i,rC=e=>{let t,r=rR.exec(e);if(!r||r[4]&&r[1])throw TypeError("Invalid time period format");let n=parseFloat(r[2]);switch(r[3].toLowerCase()){case"sec":case"secs":case"second":case"seconds":case"s":t=Math.round(n);break;case"minute":case"minutes":case"min":case"mins":case"m":t=Math.round(60*n);break;case"hour":case"hours":case"hr":case"hrs":case"h":t=Math.round(3600*n);break;case"day":case"days":case"d":t=Math.round(86400*n);break;case"week":case"weeks":case"w":t=Math.round(604800*n);break;default:t=Math.round(0x1e187e0*n)}return"-"===r[1]||"ago"===r[4]?-t:t},rA=e=>e.toLowerCase().replace(/^application\//,""),rk=(e,t)=>"string"==typeof e?t.includes(e):!!Array.isArray(e)&&t.some(Set.prototype.has.bind(new Set(e))),rT=(e,t,r={})=>{let n,i;try{n=JSON.parse(tm.decode(t))}catch{}if(!t5(n))throw new tM("JWT Claims Set must be a top-level JSON object");let{typ:o}=r;if(o&&("string"!=typeof e.typ||rA(e.typ)!==rA(o)))throw new tk('unexpected "typ" JWT header value',n,"typ","check_failed");let{requiredClaims:a=[],issuer:s,subject:l,audience:c,maxTokenAge:u}=r,d=[...a];for(let e of(void 0!==u&&d.push("iat"),void 0!==c&&d.push("aud"),void 0!==l&&d.push("sub"),void 0!==s&&d.push("iss"),new Set(d.reverse())))if(!(e in n))throw new tk(`missing required "${e}" claim`,n,e,"missing");if(s&&!(Array.isArray(s)?s:[s]).includes(n.iss))throw new tk('unexpected "iss" claim value',n,"iss","check_failed");if(l&&n.sub!==l)throw new tk('unexpected "sub" claim value',n,"sub","check_failed");if(c&&!rk(n.aud,"string"==typeof c?[c]:c))throw new tk('unexpected "aud" claim value',n,"aud","check_failed");switch(typeof r.clockTolerance){case"string":i=rC(r.clockTolerance);break;case"number":i=r.clockTolerance;break;case"undefined":i=0;break;default:throw TypeError("Invalid clockTolerance option type")}let{currentDate:p}=r,h=rx(p||new Date);if((void 0!==n.iat||u)&&"number"!=typeof n.iat)throw new tk('"iat" claim must be a number',n,"iat","invalid");if(void 0!==n.nbf){if("number"!=typeof n.nbf)throw new tk('"nbf" claim must be a number',n,"nbf","invalid");if(n.nbf>h+i)throw new tk('"nbf" claim timestamp check failed',n,"nbf","check_failed")}if(void 0!==n.exp){if("number"!=typeof n.exp)throw new tk('"exp" claim must be a number',n,"exp","invalid");if(n.exp<=h-i)throw new tT('"exp" claim timestamp check failed',n,"exp","check_failed")}if(u){let e=h-n.iat;if(e-i>("number"==typeof u?u:rC(u)))throw new tT('"iat" claim timestamp check failed (too far in the past)',n,"iat","check_failed");if(e<0-i)throw new tk('"iat" claim timestamp check failed (it should be in the past)',n,"iat","check_failed")}return n};async function rO(e,t,r){let n=await rS(e,t,r),i=rT(n.protectedHeader,n.plaintext,r),{protectedHeader:o}=n;if(void 0!==o.iss&&o.iss!==i.iss)throw new tk('replicated "iss" claim header parameter mismatch',i,"iss","mismatch");if(void 0!==o.sub&&o.sub!==i.sub)throw new tk('replicated "sub" claim header parameter mismatch',i,"sub","mismatch");if(void 0!==o.aud&&JSON.stringify(o.aud)!==JSON.stringify(i.aud))throw new tk('replicated "aud" claim header parameter mismatch',i,"aud","mismatch");let a={payload:i,protectedHeader:o};return"function"==typeof t?{...a,key:n.key}:a}let rP=(e,t)=>{if("string"!=typeof e||!e)throw new tL(`${t} missing or invalid`)};async function rI(e,t){let r;if(!t5(e))throw TypeError("JWK must be an object");if(t??(t="sha256"),"sha256"!==t&&"sha384"!==t&&"sha512"!==t)throw TypeError('digestAlgorithm must one of "sha256", "sha384", or "sha512"');switch(e.kty){case"EC":rP(e.crv,'"crv" (Curve) Parameter'),rP(e.x,'"x" (X Coordinate) Parameter'),rP(e.y,'"y" (Y Coordinate) Parameter'),r={crv:e.crv,kty:e.kty,x:e.x,y:e.y};break;case"OKP":rP(e.crv,'"crv" (Subtype of Key Pair) Parameter'),rP(e.x,'"x" (Public Key) Parameter'),r={crv:e.crv,kty:e.kty,x:e.x};break;case"RSA":rP(e.e,'"e" (Exponent) Parameter'),rP(e.n,'"n" (Modulus) Parameter'),r={e:e.e,kty:e.kty,n:e.n};break;case"oct":rP(e.k,'"k" (Key Value) Parameter'),r={k:e.k,kty:e.kty};break;default:throw new tP('"kty" (Key Type) Parameter missing or unsupported')}let n=ty.encode(JSON.stringify(r));return tx(await tg(t,n))}var rN=function(e,t,r,n,i){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!i)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!i:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?i.call(e,r):i?i.value=r:t.set(e,r),r},rj=function(e,t,r,n){if("a"===r&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)};class rM{constructor(e,t,r){if(o.add(this),a.set(this,{}),s.set(this,void 0),l.set(this,void 0),rN(this,l,r,"f"),rN(this,s,e,"f"),!t)return;let{name:n}=e;for(let[e,r]of Object.entries(t))e.startsWith(n)&&r&&(rj(this,a,"f")[e]=r)}get value(){return Object.keys(rj(this,a,"f")).sort((e,t)=>parseInt(e.split(".").pop()||"0")-parseInt(t.split(".").pop()||"0")).map(e=>rj(this,a,"f")[e]).join("")}chunk(e,t){let r=rj(this,o,"m",u).call(this);for(let n of rj(this,o,"m",c).call(this,{name:rj(this,s,"f").name,value:e,options:{...rj(this,s,"f").options,...t}}))r[n.name]=n;return Object.values(r)}clean(){return Object.values(rj(this,o,"m",u).call(this))}}a=new WeakMap,s=new WeakMap,l=new WeakMap,o=new WeakSet,c=function(e){let t=Math.ceil(e.value.length/3936);if(1===t)return rj(this,a,"f")[e.name]=e.value,[e];let r=[];for(let n=0;n<t;n++){let t=`${e.name}.${n}`,i=e.value.substr(3936*n,3936);r.push({...e,name:t,value:i}),rj(this,a,"f")[t]=i}return rj(this,l,"f").debug("CHUNKING_SESSION_COOKIE",{message:"Session cookie exceeds allowed 4096 bytes.",emptyCookieSize:160,valueSize:e.value.length,chunks:r.map(e=>e.value.length+160)}),r},u=function(){let e={};for(let t in rj(this,a,"f"))delete rj(this,a,"f")?.[t],e[t]={name:t,value:"",options:{...rj(this,s,"f").options,maxAge:0}};return e};class rL extends Error{constructor(e,t){e instanceof Error?super(void 0,{cause:{err:e,...e.cause,...t}}):"string"==typeof e?(t instanceof Error&&(t={err:t,...t.cause}),super(e,t)):super(void 0,e),this.name=this.constructor.name,this.type=this.constructor.type??"AuthError",this.kind=this.constructor.kind??"error",Error.captureStackTrace?.(this,this.constructor);let r=`https://errors.authjs.dev#${this.type.toLowerCase()}`;this.message+=`${this.message?". ":""}Read more at ${r}`}}class rD extends rL{}rD.kind="signIn";class rW extends rL{}rW.type="AdapterError";class rU extends rL{}rU.type="AccessDenied";class rH extends rL{}rH.type="CallbackRouteError";class rK extends rL{}rK.type="ErrorPageLoop";class rq extends rL{}rq.type="EventError";class r$ extends rL{}r$.type="InvalidCallbackUrl";class rB extends rD{constructor(){super(...arguments),this.code="credentials"}}rB.type="CredentialsSignin";class rz extends rL{}rz.type="InvalidEndpoints";class rJ extends rL{}rJ.type="InvalidCheck";class rV extends rL{}rV.type="JWTSessionError";class rG extends rL{}rG.type="MissingAdapter";class rF extends rL{}rF.type="MissingAdapterMethods";class rX extends rL{}rX.type="MissingAuthorize";class rY extends rL{}rY.type="MissingSecret";class rQ extends rD{}rQ.type="OAuthAccountNotLinked";class rZ extends rD{}rZ.type="OAuthCallbackError";class r0 extends rL{}r0.type="OAuthProfileParseError";class r1 extends rL{}r1.type="SessionTokenError";class r2 extends rD{}r2.type="OAuthSignInError";class r5 extends rD{}r5.type="EmailSignInError";class r6 extends rL{}r6.type="SignOutError";class r3 extends rL{}r3.type="UnknownAction";class r4 extends rL{}r4.type="UnsupportedStrategy";class r8 extends rL{}r8.type="InvalidProvider";class r9 extends rL{}r9.type="UntrustedHost";class r7 extends rL{}r7.type="Verification";class ne extends rD{}ne.type="MissingCSRF";class nt extends rL{}nt.type="DuplicateConditionalUI";class nr extends rL{}nr.type="MissingWebAuthnAutocomplete";class nn extends rL{}nn.type="WebAuthnVerificationError";class ni extends rD{}ni.type="AccountNotLinked";class no extends rL{}no.type="ExperimentalFeatureNotEnabled";var na=r(421);async function ns(e){let{token:t,secret:r,salt:n}=e,i=Array.isArray(r)?r:[r];if(!t)return null;let{payload:o}=await rO(t,async({kid:e,enc:t})=>{for(let r of i){let i=await nc(t,r,n);if(void 0===e||e===await rI({kty:"oct",k:tx(i)},`sha${i.byteLength<<3}`))return i}throw Error("no matching decryption secret")},{clockTolerance:15,keyManagementAlgorithms:["dir"],contentEncryptionAlgorithms:["A256CBC-HS512","A256GCM"]});return o}async function nl(e){let{secureCookie:t,cookieName:r=function(e){let t=e?"__Secure-":"";return{sessionToken:{name:`${t}authjs.session-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},callbackUrl:{name:`${t}authjs.callback-url`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},csrfToken:{name:`${e?"__Host-":""}authjs.csrf-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},pkceCodeVerifier:{name:`${t}authjs.pkce.code_verifier`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}},state:{name:`${t}authjs.state`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}},nonce:{name:`${t}authjs.nonce`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},webauthnChallenge:{name:`${t}authjs.challenge`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}}}}(t??!1).sessionToken.name,decode:n=ns,salt:i=r,secret:o,logger:a=console,raw:s,req:l}=e;if(!l)throw Error("Must pass `req` to JWT getToken()");let c=l.headers instanceof Headers?l.headers:new Headers(l.headers),u=new rM({name:r,options:{secure:t}},(0,na.q)(c.get("cookie")??""),a).value,d=c.get("authorization");if(u||d?.split(" ")[0]!=="Bearer"||(u=decodeURIComponent(d.split(" ")[1])),!u)return null;if(s)return u;if(!o)throw new rY("Must pass `secret` if not set to JWT getToken()");try{return await n({token:u,secret:o,salt:i})}catch{return null}}async function nc(e,t,r){let n;switch(e){case"A256CBC-HS512":n=64;break;case"A256GCM":n=32;break;default:throw Error("Unsupported JWT Content Encryption Algorithm")}return await tp("sha256",t,r,`Auth.js Generated Encryption Key (${r})`,n)}let nu="./ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),nd=Array.from({length:64},(e,t)=>t),np=e=>Array(e).fill(-1),nh=[...np(46),0,1,...nd.slice(54,64),...np(7),...nd.slice(2,28),...np(6),...nd.slice(28,54),...np(5)],nf=(e,t)=>{let r=null;for("number"==typeof e&&(r=e,e=()=>null);null!==r||null!==(r=e());)r<128?t(127&r):(r<2048?t(r>>6&31|192):(r<65536?t(r>>12&15|224):(t(r>>18&7|240),t(r>>12&63|128)),t(r>>6&63|128)),t(63&r|128)),r=null},ng=(e,t)=>{let r,n=null;for(;null!==(r=null!==n?n:e());){if(r>=55296&&r<=57343&&null!==(n=e())&&n>=56320&&n<=57343){t((r-55296)*1024+n-56320+65536),n=null;continue}t(r)}null!==n&&t(n)},ny=(e,t)=>ng(e,e=>{nf(e,t)}),nm="object"==typeof process?setTimeout:"function"==typeof setImmediate?setImmediate:"object"==typeof process&&"function"==typeof process.nextTick?process.nextTick:setTimeout,nw=(e,t,r,n)=>{let i,o=e[t],a=e[t+1];return o^=r[0],a^=(n[o>>>24]+n[256|o>>16&255]^n[512|o>>8&255])+n[768|255&o]^r[1],o^=(n[a>>>24]+n[256|a>>16&255]^n[512|a>>8&255])+n[768|255&a]^r[2],a^=(n[o>>>24]+n[256|o>>16&255]^n[512|o>>8&255])+n[768|255&o]^r[3],o^=(n[a>>>24]+n[256|a>>16&255]^n[512|a>>8&255])+n[768|255&a]^r[4],a^=(n[o>>>24]+n[256|o>>16&255]^n[512|o>>8&255])+n[768|255&o]^r[5],o^=(n[a>>>24]+n[256|a>>16&255]^n[512|a>>8&255])+n[768|255&a]^r[6],a^=(n[o>>>24]+n[256|o>>16&255]^n[512|o>>8&255])+n[768|255&o]^r[7],o^=(n[a>>>24]+n[256|a>>16&255]^n[512|a>>8&255])+n[768|255&a]^r[8],a^=(n[o>>>24]+n[256|o>>16&255]^n[512|o>>8&255])+n[768|255&o]^r[9],o^=(n[a>>>24]+n[256|a>>16&255]^n[512|a>>8&255])+n[768|255&a]^r[10],a^=(n[o>>>24]+n[256|o>>16&255]^n[512|o>>8&255])+n[768|255&o]^r[11],o^=(n[a>>>24]+n[256|a>>16&255]^n[512|a>>8&255])+n[768|255&a]^r[12],a^=(n[o>>>24]+n[256|o>>16&255]^n[512|o>>8&255])+n[768|255&o]^r[13],o^=(n[a>>>24]+n[256|a>>16&255]^n[512|a>>8&255])+n[768|255&a]^r[14],a^=(n[o>>>24]+n[256|o>>16&255]^n[512|o>>8&255])+n[768|255&o]^r[15],o^=(n[a>>>24]+n[256|a>>16&255]^n[512|a>>8&255])+n[768|255&a]^r[16],e[t]=a^r[17],e[t+1]=o,e},nv=(e,t)=>{let r=0;for(let n=0;n<4;++n)r=r<<8|255&e[t],t=(t+1)%e.length;return{key:r,offp:t}},nb=(e,t,r)=>{let n=t.length,i=r.length,o=0,a=[0,0],s;for(let r=0;r<n;r++)o=(s=nv(e,o)).offp,t[r]=t[r]^s.key;for(let e=0;e<n;e+=2)a=nw(a,0,t,r),t[e]=a[0],t[e+1]=a[1];for(let e=0;e<i;e+=2)a=nw(a,0,t,r),r[e]=a[0],r[e+1]=a[1]},nE=(e,t,r,n)=>{let i=r.length,o=n.length,a=0,s=[0,0],l;for(let e=0;e<i;e++)a=(l=nv(t,a)).offp,r[e]=r[e]^l.key;a=0;for(let t=0;t<i;t+=2)a=(l=nv(e,a)).offp,s[0]^=l.key,a=(l=nv(e,a)).offp,s[1]^=l.key,s=nw(s,0,r,n),r[t]=s[0],r[t+1]=s[1];for(let t=0;t<o;t+=2)a=(l=nv(e,a)).offp,s[0]^=l.key,a=(l=nv(e,a)).offp,s[1]^=l.key,s=nw(s,0,r,n),n[t]=s[0],n[t+1]=s[1]};process.env.PLAYWRIGHT_TEST_BASE_URL||process.env.PLAYWRIGHT||process.env.CI_PLAYWRIGHT;let n_=/^guest-\d+$/;async function nS(e){let{pathname:t}=e.nextUrl;if(t.startsWith("/ping"))return new Response("pong",{status:200});if(t.startsWith("/api/auth"))return F.next();let r=await nl({req:e,secret:process.env.AUTH_SECRET,secureCookie:!0});if(!r){let t=encodeURIComponent(e.url);return F.redirect(new URL(`/api/auth/guest?redirectUrl=${t}`,e.url))}let n=n_.test(r?.email??"");return r&&!n&&["/login","/register"].includes(t)?F.redirect(new URL("/",e.url)):F.next()}let nx={matcher:["/","/chat/:id","/api/:path*","/login","/register","/((?!_next/static|_next/image|favicon.ico|sitemap.xml|robots.txt).*)"]},nR=(Object.values({NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401}),{...d}),nC=nR.middleware||nR.default,nA="/middleware";if("function"!=typeof nC)throw Object.defineProperty(Error(`The Middleware "${nA}" must export a \`middleware\` or a \`default\` function`),"__NEXT_ERROR_CODE",{value:"E120",enumerable:!1,configurable:!0});function nk(e){return to({...e,page:nA,handler:async(...e)=>{try{return await nC(...e)}catch(i){let t=e[0],r=new URL(t.url),n=r.pathname+r.search;throw await g(i,{path:n,method:t.method,headers:Object.fromEntries(t.headers.entries())},{routerKind:"Pages Router",routePath:"/middleware",routeType:"middleware",revalidateReason:void 0}),i}}})}},239:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{interceptTestApis:function(){return o},wrapRequestHandler:function(){return a}});let n=r(415),i=r(930);function o(){return(0,i.interceptFetch)(r.g.fetch)}function a(e){return(t,r)=>(0,n.withRequest)(t,i.reader,()=>e(t,r))}},356:e=>{"use strict";e.exports=require("node:buffer")},415:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getTestReqInfo:function(){return a},withRequest:function(){return o}});let n=new(r(521)).AsyncLocalStorage;function i(e,t){let r=t.header(e,"next-test-proxy-port");if(!r)return;let n=t.url(e);return{url:n,proxyPort:Number(r),testData:t.header(e,"next-test-data")||""}}function o(e,t,r){let o=i(e,t);return o?n.run(o,r):r()}function a(e,t){let r=n.getStore();return r||(e&&t?i(e,t):void 0)}},421:(e,t)=>{"use strict";t.q=function(e,t){if("string"!=typeof e)throw TypeError("argument str must be a string");var r={},n=e.length;if(n<2)return r;var i=t&&t.decode||c,o=0,a=0,u=0;do{if(-1===(a=e.indexOf("=",o)))break;if(-1===(u=e.indexOf(";",o)))u=n;else if(a>u){o=e.lastIndexOf(";",a-1)+1;continue}var d=s(e,o,a),p=l(e,a,d),h=e.slice(d,p);if(!r.hasOwnProperty(h)){var f=s(e,a+1,u),g=l(e,u,f);34===e.charCodeAt(f)&&34===e.charCodeAt(g-1)&&(f++,g--);var y=e.slice(f,g);r[h]=function(e,t){try{return t(e)}catch(t){return e}}(y,i)}o=u+1}while(o<n);return r};var r=Object.prototype.toString,n=/^[!#$%&'*+\-.^_`|~0-9A-Za-z]+$/,i=/^("?)[\u0021\u0023-\u002B\u002D-\u003A\u003C-\u005B\u005D-\u007E]*\1$/,o=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,a=/^[\u0020-\u003A\u003D-\u007E]*$/;function s(e,t,r){do{var n=e.charCodeAt(t);if(32!==n&&9!==n)return t}while(++t<r);return r}function l(e,t,r){for(;t>r;){var n=e.charCodeAt(--t);if(32!==n&&9!==n)return t+1}return r}function c(e){return -1!==e.indexOf("%")?decodeURIComponent(e):e}},424:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab="//");var t={};(()=>{t.parse=function(t,r){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var i={},o=t.split(n),a=(r||{}).decode||e,s=0;s<o.length;s++){var l=o[s],c=l.indexOf("=");if(!(c<0)){var u=l.substr(0,c).trim(),d=l.substr(++c,l.length).trim();'"'==d[0]&&(d=d.slice(1,-1)),void 0==i[u]&&(i[u]=function(e,t){try{return t(e)}catch(t){return e}}(d,a))}}return i},t.serialize=function(e,t,n){var o=n||{},a=o.encode||r;if("function"!=typeof a)throw TypeError("option encode is invalid");if(!i.test(e))throw TypeError("argument name is invalid");var s=a(t);if(s&&!i.test(s))throw TypeError("argument val is invalid");var l=e+"="+s;if(null!=o.maxAge){var c=o.maxAge-0;if(isNaN(c)||!isFinite(c))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(c)}if(o.domain){if(!i.test(o.domain))throw TypeError("option domain is invalid");l+="; Domain="+o.domain}if(o.path){if(!i.test(o.path))throw TypeError("option path is invalid");l+="; Path="+o.path}if(o.expires){if("function"!=typeof o.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+o.expires.toUTCString()}if(o.httpOnly&&(l+="; HttpOnly"),o.secure&&(l+="; Secure"),o.sameSite)switch("string"==typeof o.sameSite?o.sameSite.toLowerCase():o.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var e=decodeURIComponent,r=encodeURIComponent,n=/; */,i=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},450:(e,t,r)=>{var n;(()=>{var i={226:function(i,o){!function(a,s){"use strict";var l="function",c="undefined",u="object",d="string",p="major",h="model",f="name",g="type",y="vendor",m="version",w="architecture",v="console",b="mobile",E="tablet",_="smarttv",S="wearable",x="embedded",R="Amazon",C="Apple",A="ASUS",k="BlackBerry",T="Browser",O="Chrome",P="Firefox",I="Google",N="Huawei",j="Microsoft",M="Motorola",L="Opera",D="Samsung",W="Sharp",U="Sony",H="Xiaomi",K="Zebra",q="Facebook",$="Chromium OS",B="Mac OS",z=function(e,t){var r={};for(var n in e)t[n]&&t[n].length%2==0?r[n]=t[n].concat(e[n]):r[n]=e[n];return r},J=function(e){for(var t={},r=0;r<e.length;r++)t[e[r].toUpperCase()]=e[r];return t},V=function(e,t){return typeof e===d&&-1!==G(t).indexOf(G(e))},G=function(e){return e.toLowerCase()},F=function(e,t){if(typeof e===d)return e=e.replace(/^\s\s*/,""),typeof t===c?e:e.substring(0,350)},X=function(e,t){for(var r,n,i,o,a,c,d=0;d<t.length&&!a;){var p=t[d],h=t[d+1];for(r=n=0;r<p.length&&!a&&p[r];)if(a=p[r++].exec(e))for(i=0;i<h.length;i++)c=a[++n],typeof(o=h[i])===u&&o.length>0?2===o.length?typeof o[1]==l?this[o[0]]=o[1].call(this,c):this[o[0]]=o[1]:3===o.length?typeof o[1]!==l||o[1].exec&&o[1].test?this[o[0]]=c?c.replace(o[1],o[2]):void 0:this[o[0]]=c?o[1].call(this,c,o[2]):void 0:4===o.length&&(this[o[0]]=c?o[3].call(this,c.replace(o[1],o[2])):s):this[o]=c||s;d+=2}},Y=function(e,t){for(var r in t)if(typeof t[r]===u&&t[r].length>0){for(var n=0;n<t[r].length;n++)if(V(t[r][n],e))return"?"===r?s:r}else if(V(t[r],e))return"?"===r?s:r;return e},Q={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},Z={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[m,[f,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[m,[f,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[f,m],[/opios[\/ ]+([\w\.]+)/i],[m,[f,L+" Mini"]],[/\bopr\/([\w\.]+)/i],[m,[f,L]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i,/(ba?idubrowser)[\/ ]?([\w\.]+)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[f,m],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[m,[f,"UC"+T]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i],[m,[f,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[m,[f,"WeChat"]],[/konqueror\/([\w\.]+)/i],[m,[f,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[m,[f,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[m,[f,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[f,/(.+)/,"$1 Secure "+T],m],[/\bfocus\/([\w\.]+)/i],[m,[f,P+" Focus"]],[/\bopt\/([\w\.]+)/i],[m,[f,L+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[m,[f,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[m,[f,"Dolphin"]],[/coast\/([\w\.]+)/i],[m,[f,L+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[m,[f,"MIUI "+T]],[/fxios\/([-\w\.]+)/i],[m,[f,P]],[/\bqihu|(qi?ho?o?|360)browser/i],[[f,"360 "+T]],[/(oculus|samsung|sailfish|huawei)browser\/([\w\.]+)/i],[[f,/(.+)/,"$1 "+T],m],[/(comodo_dragon)\/([\w\.]+)/i],[[f,/_/g," "],m],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i],[f,m],[/(metasr)[\/ ]?([\w\.]+)/i,/(lbbrowser)/i,/\[(linkedin)app\]/i],[f],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[f,q],m],[/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/ ]([-\w\.]+)/i],[f,m],[/\bgsa\/([\w\.]+) .*safari\//i],[m,[f,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[m,[f,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[m,[f,O+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[f,O+" WebView"],m],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[m,[f,"Android "+T]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[f,m],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[m,[f,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[m,f],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[f,[m,Y,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[f,m],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[f,"Netscape"],m],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[m,[f,P+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[f,m],[/(cobalt)\/([\w\.]+)/i],[f,[m,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[w,"amd64"]],[/(ia32(?=;))/i],[[w,G]],[/((?:i[346]|x)86)[;\)]/i],[[w,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[w,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[w,"armhf"]],[/windows (ce|mobile); ppc;/i],[[w,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[w,/ower/,"",G]],[/(sun4\w)[;\)]/i],[[w,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[w,G]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[h,[y,D],[g,E]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[h,[y,D],[g,b]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[h,[y,C],[g,b]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[h,[y,C],[g,E]],[/(macintosh);/i],[h,[y,C]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[h,[y,W],[g,b]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[h,[y,N],[g,E]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[h,[y,N],[g,b]],[/\b(poco[\w ]+)(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[h,/_/g," "],[y,H],[g,b]],[/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[h,/_/g," "],[y,H],[g,E]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[h,[y,"OPPO"],[g,b]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[h,[y,"Vivo"],[g,b]],[/\b(rmx[12]\d{3})(?: bui|;|\))/i],[h,[y,"Realme"],[g,b]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[h,[y,M],[g,b]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[h,[y,M],[g,E]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[h,[y,"LG"],[g,E]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[h,[y,"LG"],[g,b]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[h,[y,"Lenovo"],[g,E]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[h,/_/g," "],[y,"Nokia"],[g,b]],[/(pixel c)\b/i],[h,[y,I],[g,E]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[h,[y,I],[g,b]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[h,[y,U],[g,b]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[h,"Xperia Tablet"],[y,U],[g,E]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[h,[y,"OnePlus"],[g,b]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[h,[y,R],[g,E]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[h,/(.+)/g,"Fire Phone $1"],[y,R],[g,b]],[/(playbook);[-\w\),; ]+(rim)/i],[h,y,[g,E]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[h,[y,k],[g,b]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[h,[y,A],[g,E]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[h,[y,A],[g,b]],[/(nexus 9)/i],[h,[y,"HTC"],[g,E]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[y,[h,/_/g," "],[g,b]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[h,[y,"Acer"],[g,E]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[h,[y,"Meizu"],[g,b]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[y,h,[g,b]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[y,h,[g,E]],[/(surface duo)/i],[h,[y,j],[g,E]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[h,[y,"Fairphone"],[g,b]],[/(u304aa)/i],[h,[y,"AT&T"],[g,b]],[/\bsie-(\w*)/i],[h,[y,"Siemens"],[g,b]],[/\b(rct\w+) b/i],[h,[y,"RCA"],[g,E]],[/\b(venue[\d ]{2,7}) b/i],[h,[y,"Dell"],[g,E]],[/\b(q(?:mv|ta)\w+) b/i],[h,[y,"Verizon"],[g,E]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[h,[y,"Barnes & Noble"],[g,E]],[/\b(tm\d{3}\w+) b/i],[h,[y,"NuVision"],[g,E]],[/\b(k88) b/i],[h,[y,"ZTE"],[g,E]],[/\b(nx\d{3}j) b/i],[h,[y,"ZTE"],[g,b]],[/\b(gen\d{3}) b.+49h/i],[h,[y,"Swiss"],[g,b]],[/\b(zur\d{3}) b/i],[h,[y,"Swiss"],[g,E]],[/\b((zeki)?tb.*\b) b/i],[h,[y,"Zeki"],[g,E]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[y,"Dragon Touch"],h,[g,E]],[/\b(ns-?\w{0,9}) b/i],[h,[y,"Insignia"],[g,E]],[/\b((nxa|next)-?\w{0,9}) b/i],[h,[y,"NextBook"],[g,E]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[y,"Voice"],h,[g,b]],[/\b(lvtel\-)?(v1[12]) b/i],[[y,"LvTel"],h,[g,b]],[/\b(ph-1) /i],[h,[y,"Essential"],[g,b]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[h,[y,"Envizen"],[g,E]],[/\b(trio[-\w\. ]+) b/i],[h,[y,"MachSpeed"],[g,E]],[/\btu_(1491) b/i],[h,[y,"Rotor"],[g,E]],[/(shield[\w ]+) b/i],[h,[y,"Nvidia"],[g,E]],[/(sprint) (\w+)/i],[y,h,[g,b]],[/(kin\.[onetw]{3})/i],[[h,/\./g," "],[y,j],[g,b]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[h,[y,K],[g,E]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[h,[y,K],[g,b]],[/smart-tv.+(samsung)/i],[y,[g,_]],[/hbbtv.+maple;(\d+)/i],[[h,/^/,"SmartTV"],[y,D],[g,_]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[y,"LG"],[g,_]],[/(apple) ?tv/i],[y,[h,C+" TV"],[g,_]],[/crkey/i],[[h,O+"cast"],[y,I],[g,_]],[/droid.+aft(\w)( bui|\))/i],[h,[y,R],[g,_]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[h,[y,W],[g,_]],[/(bravia[\w ]+)( bui|\))/i],[h,[y,U],[g,_]],[/(mitv-\w{5}) bui/i],[h,[y,H],[g,_]],[/Hbbtv.*(technisat) (.*);/i],[y,h,[g,_]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[y,F],[h,F],[g,_]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[g,_]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[y,h,[g,v]],[/droid.+; (shield) bui/i],[h,[y,"Nvidia"],[g,v]],[/(playstation [345portablevi]+)/i],[h,[y,U],[g,v]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[h,[y,j],[g,v]],[/((pebble))app/i],[y,h,[g,S]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[h,[y,C],[g,S]],[/droid.+; (glass) \d/i],[h,[y,I],[g,S]],[/droid.+; (wt63?0{2,3})\)/i],[h,[y,K],[g,S]],[/(quest( 2| pro)?)/i],[h,[y,q],[g,S]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[y,[g,x]],[/(aeobc)\b/i],[h,[y,R],[g,x]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i],[h,[g,b]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[h,[g,E]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[g,E]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[g,b]],[/(android[-\w\. ]{0,9});.+buil/i],[h,[y,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[m,[f,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[m,[f,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[f,m],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[m,f]],os:[[/microsoft (windows) (vista|xp)/i],[f,m],[/(windows) nt 6\.2; (arm)/i,/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i,/(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i],[f,[m,Y,Q]],[/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[f,"Windows"],[m,Y,Q]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/ios;fbsv\/([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[m,/_/g,"."],[f,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[f,B],[m,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[m,f],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[f,m],[/\(bb(10);/i],[m,[f,k]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[m,[f,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[m,[f,P+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[m,[f,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[m,[f,"watchOS"]],[/crkey\/([\d\.]+)/i],[m,[f,O+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[f,$],m],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[f,m],[/(sunos) ?([\w\.\d]*)/i],[[f,"Solaris"],m],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[f,m]]},ee=function(e,t){if(typeof e===u&&(t=e,e=s),!(this instanceof ee))return new ee(e,t).getResult();var r=typeof a!==c&&a.navigator?a.navigator:s,n=e||(r&&r.userAgent?r.userAgent:""),i=r&&r.userAgentData?r.userAgentData:s,o=t?z(Z,t):Z,v=r&&r.userAgent==n;return this.getBrowser=function(){var e,t={};return t[f]=s,t[m]=s,X.call(t,n,o.browser),t[p]=typeof(e=t[m])===d?e.replace(/[^\d\.]/g,"").split(".")[0]:s,v&&r&&r.brave&&typeof r.brave.isBrave==l&&(t[f]="Brave"),t},this.getCPU=function(){var e={};return e[w]=s,X.call(e,n,o.cpu),e},this.getDevice=function(){var e={};return e[y]=s,e[h]=s,e[g]=s,X.call(e,n,o.device),v&&!e[g]&&i&&i.mobile&&(e[g]=b),v&&"Macintosh"==e[h]&&r&&typeof r.standalone!==c&&r.maxTouchPoints&&r.maxTouchPoints>2&&(e[h]="iPad",e[g]=E),e},this.getEngine=function(){var e={};return e[f]=s,e[m]=s,X.call(e,n,o.engine),e},this.getOS=function(){var e={};return e[f]=s,e[m]=s,X.call(e,n,o.os),v&&!e[f]&&i&&"Unknown"!=i.platform&&(e[f]=i.platform.replace(/chrome os/i,$).replace(/macos/i,B)),e},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return n},this.setUA=function(e){return n=typeof e===d&&e.length>350?F(e,350):e,this},this.setUA(n),this};ee.VERSION="1.0.35",ee.BROWSER=J([f,m,p]),ee.CPU=J([w]),ee.DEVICE=J([h,y,g,v,b,_,E,S,x]),ee.ENGINE=ee.OS=J([f,m]),typeof o!==c?(i.exports&&(o=i.exports=ee),o.UAParser=ee):r.amdO?void 0===(n=(function(){return ee}).call(t,r,t,e))||(e.exports=n):typeof a!==c&&(a.UAParser=ee);var et=typeof a!==c&&(a.jQuery||a.Zepto);if(et&&!et.ua){var er=new ee;et.ua=er.getResult(),et.ua.get=function(){return er.getUA()},et.ua.set=function(e){er.setUA(e);var t=er.getResult();for(var r in t)et.ua[r]=t[r]}}}("object"==typeof window?window:this)}},o={};function a(e){var t=o[e];if(void 0!==t)return t.exports;var r=o[e]={exports:{}},n=!0;try{i[e].call(r.exports,r,r.exports,a),n=!1}finally{n&&delete o[e]}return r.exports}a.ab="//",e.exports=a(226)})()},521:e=>{"use strict";e.exports=require("node:async_hooks")},572:e=>{(()=>{"use strict";var t={993:e=>{var t=Object.prototype.hasOwnProperty,r="~";function n(){}function i(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function o(e,t,n,o,a){if("function"!=typeof n)throw TypeError("The listener must be a function");var s=new i(n,o||e,a),l=r?r+t:t;return e._events[l]?e._events[l].fn?e._events[l]=[e._events[l],s]:e._events[l].push(s):(e._events[l]=s,e._eventsCount++),e}function a(e,t){0==--e._eventsCount?e._events=new n:delete e._events[t]}function s(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),(new n).__proto__||(r=!1)),s.prototype.eventNames=function(){var e,n,i=[];if(0===this._eventsCount)return i;for(n in e=this._events)t.call(e,n)&&i.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(e)):i},s.prototype.listeners=function(e){var t=r?r+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var i=0,o=n.length,a=Array(o);i<o;i++)a[i]=n[i].fn;return a},s.prototype.listenerCount=function(e){var t=r?r+e:e,n=this._events[t];return n?n.fn?1:n.length:0},s.prototype.emit=function(e,t,n,i,o,a){var s=r?r+e:e;if(!this._events[s])return!1;var l,c,u=this._events[s],d=arguments.length;if(u.fn){switch(u.once&&this.removeListener(e,u.fn,void 0,!0),d){case 1:return u.fn.call(u.context),!0;case 2:return u.fn.call(u.context,t),!0;case 3:return u.fn.call(u.context,t,n),!0;case 4:return u.fn.call(u.context,t,n,i),!0;case 5:return u.fn.call(u.context,t,n,i,o),!0;case 6:return u.fn.call(u.context,t,n,i,o,a),!0}for(c=1,l=Array(d-1);c<d;c++)l[c-1]=arguments[c];u.fn.apply(u.context,l)}else{var p,h=u.length;for(c=0;c<h;c++)switch(u[c].once&&this.removeListener(e,u[c].fn,void 0,!0),d){case 1:u[c].fn.call(u[c].context);break;case 2:u[c].fn.call(u[c].context,t);break;case 3:u[c].fn.call(u[c].context,t,n);break;case 4:u[c].fn.call(u[c].context,t,n,i);break;default:if(!l)for(p=1,l=Array(d-1);p<d;p++)l[p-1]=arguments[p];u[c].fn.apply(u[c].context,l)}}return!0},s.prototype.on=function(e,t,r){return o(this,e,t,r,!1)},s.prototype.once=function(e,t,r){return o(this,e,t,r,!0)},s.prototype.removeListener=function(e,t,n,i){var o=r?r+e:e;if(!this._events[o])return this;if(!t)return a(this,o),this;var s=this._events[o];if(s.fn)s.fn!==t||i&&!s.once||n&&s.context!==n||a(this,o);else{for(var l=0,c=[],u=s.length;l<u;l++)(s[l].fn!==t||i&&!s[l].once||n&&s[l].context!==n)&&c.push(s[l]);c.length?this._events[o]=1===c.length?c[0]:c:a(this,o)}return this},s.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&a(this,t)):(this._events=new n,this._eventsCount=0),this},s.prototype.off=s.prototype.removeListener,s.prototype.addListener=s.prototype.on,s.prefixed=r,s.EventEmitter=s,e.exports=s},213:e=>{e.exports=(e,t)=>(t=t||(()=>{}),e.then(e=>new Promise(e=>{e(t())}).then(()=>e),e=>new Promise(e=>{e(t())}).then(()=>{throw e})))},574:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t,r){let n=0,i=e.length;for(;i>0;){let o=i/2|0,a=n+o;0>=r(e[a],t)?(n=++a,i-=o+1):i=o}return n}},821:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});let n=r(574);class i{constructor(){this._queue=[]}enqueue(e,t){let r={priority:(t=Object.assign({priority:0},t)).priority,run:e};if(this.size&&this._queue[this.size-1].priority>=t.priority)return void this._queue.push(r);let i=n.default(this._queue,r,(e,t)=>t.priority-e.priority);this._queue.splice(i,0,r)}dequeue(){let e=this._queue.shift();return null==e?void 0:e.run}filter(e){return this._queue.filter(t=>t.priority===e.priority).map(e=>e.run)}get size(){return this._queue.length}}t.default=i},816:(e,t,r)=>{let n=r(213);class i extends Error{constructor(e){super(e),this.name="TimeoutError"}}let o=(e,t,r)=>new Promise((o,a)=>{if("number"!=typeof t||t<0)throw TypeError("Expected `milliseconds` to be a positive number");if(t===1/0)return void o(e);let s=setTimeout(()=>{if("function"==typeof r){try{o(r())}catch(e){a(e)}return}let n="string"==typeof r?r:`Promise timed out after ${t} milliseconds`,s=r instanceof Error?r:new i(n);"function"==typeof e.cancel&&e.cancel(),a(s)},t);n(e.then(o,a),()=>{clearTimeout(s)})});e.exports=o,e.exports.default=o,e.exports.TimeoutError=i}},r={};function n(e){var i=r[e];if(void 0!==i)return i.exports;var o=r[e]={exports:{}},a=!0;try{t[e](o,o.exports,n),a=!1}finally{a&&delete r[e]}return o.exports}n.ab="//";var i={};(()=>{Object.defineProperty(i,"__esModule",{value:!0});let e=n(993),t=n(816),r=n(821),o=()=>{},a=new t.TimeoutError;class s extends e{constructor(e){var t,n,i,a;if(super(),this._intervalCount=0,this._intervalEnd=0,this._pendingCount=0,this._resolveEmpty=o,this._resolveIdle=o,!("number"==typeof(e=Object.assign({carryoverConcurrencyCount:!1,intervalCap:1/0,interval:0,concurrency:1/0,autoStart:!0,queueClass:r.default},e)).intervalCap&&e.intervalCap>=1))throw TypeError(`Expected \`intervalCap\` to be a number from 1 and up, got \`${null!=(n=null==(t=e.intervalCap)?void 0:t.toString())?n:""}\` (${typeof e.intervalCap})`);if(void 0===e.interval||!(Number.isFinite(e.interval)&&e.interval>=0))throw TypeError(`Expected \`interval\` to be a finite number >= 0, got \`${null!=(a=null==(i=e.interval)?void 0:i.toString())?a:""}\` (${typeof e.interval})`);this._carryoverConcurrencyCount=e.carryoverConcurrencyCount,this._isIntervalIgnored=e.intervalCap===1/0||0===e.interval,this._intervalCap=e.intervalCap,this._interval=e.interval,this._queue=new e.queueClass,this._queueClass=e.queueClass,this.concurrency=e.concurrency,this._timeout=e.timeout,this._throwOnTimeout=!0===e.throwOnTimeout,this._isPaused=!1===e.autoStart}get _doesIntervalAllowAnother(){return this._isIntervalIgnored||this._intervalCount<this._intervalCap}get _doesConcurrentAllowAnother(){return this._pendingCount<this._concurrency}_next(){this._pendingCount--,this._tryToStartAnother(),this.emit("next")}_resolvePromises(){this._resolveEmpty(),this._resolveEmpty=o,0===this._pendingCount&&(this._resolveIdle(),this._resolveIdle=o,this.emit("idle"))}_onResumeInterval(){this._onInterval(),this._initializeIntervalIfNeeded(),this._timeoutId=void 0}_isIntervalPaused(){let e=Date.now();if(void 0===this._intervalId){let t=this._intervalEnd-e;if(!(t<0))return void 0===this._timeoutId&&(this._timeoutId=setTimeout(()=>{this._onResumeInterval()},t)),!0;this._intervalCount=this._carryoverConcurrencyCount?this._pendingCount:0}return!1}_tryToStartAnother(){if(0===this._queue.size)return this._intervalId&&clearInterval(this._intervalId),this._intervalId=void 0,this._resolvePromises(),!1;if(!this._isPaused){let e=!this._isIntervalPaused();if(this._doesIntervalAllowAnother&&this._doesConcurrentAllowAnother){let t=this._queue.dequeue();return!!t&&(this.emit("active"),t(),e&&this._initializeIntervalIfNeeded(),!0)}}return!1}_initializeIntervalIfNeeded(){this._isIntervalIgnored||void 0!==this._intervalId||(this._intervalId=setInterval(()=>{this._onInterval()},this._interval),this._intervalEnd=Date.now()+this._interval)}_onInterval(){0===this._intervalCount&&0===this._pendingCount&&this._intervalId&&(clearInterval(this._intervalId),this._intervalId=void 0),this._intervalCount=this._carryoverConcurrencyCount?this._pendingCount:0,this._processQueue()}_processQueue(){for(;this._tryToStartAnother(););}get concurrency(){return this._concurrency}set concurrency(e){if(!("number"==typeof e&&e>=1))throw TypeError(`Expected \`concurrency\` to be a number from 1 and up, got \`${e}\` (${typeof e})`);this._concurrency=e,this._processQueue()}async add(e,r={}){return new Promise((n,i)=>{let o=async()=>{this._pendingCount++,this._intervalCount++;try{let o=void 0===this._timeout&&void 0===r.timeout?e():t.default(Promise.resolve(e()),void 0===r.timeout?this._timeout:r.timeout,()=>{(void 0===r.throwOnTimeout?this._throwOnTimeout:r.throwOnTimeout)&&i(a)});n(await o)}catch(e){i(e)}this._next()};this._queue.enqueue(o,r),this._tryToStartAnother(),this.emit("add")})}async addAll(e,t){return Promise.all(e.map(async e=>this.add(e,t)))}start(){return this._isPaused&&(this._isPaused=!1,this._processQueue()),this}pause(){this._isPaused=!0}clear(){this._queue=new this._queueClass}async onEmpty(){if(0!==this._queue.size)return new Promise(e=>{let t=this._resolveEmpty;this._resolveEmpty=()=>{t(),e()}})}async onIdle(){if(0!==this._pendingCount||0!==this._queue.size)return new Promise(e=>{let t=this._resolveIdle;this._resolveIdle=()=>{t(),e()}})}get size(){return this._queue.size}sizeBy(e){return this._queue.filter(e).length}get pending(){return this._pendingCount}get isPaused(){return this._isPaused}get timeout(){return this._timeout}set timeout(e){this._timeout=e}}i.default=s})(),e.exports=i})()},674:(e,t,r)=>{"use strict";r.r(t),r.d(t,{DiagConsoleLogger:()=>j,DiagLogLevel:()=>n,INVALID_SPANID:()=>ed,INVALID_SPAN_CONTEXT:()=>eh,INVALID_TRACEID:()=>ep,ProxyTracer:()=>eP,ProxyTracerProvider:()=>eN,ROOT_CONTEXT:()=>I,SamplingDecision:()=>a,SpanKind:()=>s,SpanStatusCode:()=>l,TraceFlags:()=>o,ValueType:()=>i,baggageEntryMetadataFromString:()=>O,context:()=>eH,createContextKey:()=>P,createNoopMeter:()=>ee,createTraceState:()=>eU,default:()=>e2,defaultTextMapGetter:()=>et,defaultTextMapSetter:()=>er,diag:()=>eK,isSpanContextValid:()=>eC,isValidSpanId:()=>eR,isValidTraceId:()=>ex,metrics:()=>eB,propagation:()=>eZ,trace:()=>e1});var n,i,o,a,s,l,c="object"==typeof globalThis?globalThis:"object"==typeof self?self:"object"==typeof window?window:"object"==typeof r.g?r.g:{},u="1.9.0",d=/^(\d+)\.(\d+)\.(\d+)(-(.+))?$/,p=function(e){var t=new Set([e]),r=new Set,n=e.match(d);if(!n)return function(){return!1};var i={major:+n[1],minor:+n[2],patch:+n[3],prerelease:n[4]};if(null!=i.prerelease)return function(t){return t===e};function o(e){return r.add(e),!1}return function(e){if(t.has(e))return!0;if(r.has(e))return!1;var n=e.match(d);if(!n)return o(e);var a={major:+n[1],minor:+n[2],patch:+n[3],prerelease:n[4]};if(null!=a.prerelease||i.major!==a.major)return o(e);if(0===i.major)return i.minor===a.minor&&i.patch<=a.patch?(t.add(e),!0):o(e);return i.minor<=a.minor?(t.add(e),!0):o(e)}}(u),h=Symbol.for("opentelemetry.js.api."+u.split(".")[0]);function f(e,t,r,n){void 0===n&&(n=!1);var i,o=c[h]=null!=(i=c[h])?i:{version:u};if(!n&&o[e]){var a=Error("@opentelemetry/api: Attempted duplicate registration of API: "+e);return r.error(a.stack||a.message),!1}if(o.version!==u){var a=Error("@opentelemetry/api: Registration of version v"+o.version+" for "+e+" does not match previously registered API v"+u);return r.error(a.stack||a.message),!1}return o[e]=t,r.debug("@opentelemetry/api: Registered a global for "+e+" v"+u+"."),!0}function g(e){var t,r,n=null==(t=c[h])?void 0:t.version;if(n&&p(n))return null==(r=c[h])?void 0:r[e]}function y(e,t){t.debug("@opentelemetry/api: Unregistering a global for "+e+" v"+u+".");var r=c[h];r&&delete r[e]}var m=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(n=o.next()).done;)a.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return a},w=function(e,t,r){if(r||2==arguments.length)for(var n,i=0,o=t.length;i<o;i++)!n&&i in t||(n||(n=Array.prototype.slice.call(t,0,i)),n[i]=t[i]);return e.concat(n||Array.prototype.slice.call(t))},v=function(){function e(e){this._namespace=e.namespace||"DiagComponentLogger"}return e.prototype.debug=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return b("debug",this._namespace,e)},e.prototype.error=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return b("error",this._namespace,e)},e.prototype.info=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return b("info",this._namespace,e)},e.prototype.warn=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return b("warn",this._namespace,e)},e.prototype.verbose=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return b("verbose",this._namespace,e)},e}();function b(e,t,r){var n=g("diag");if(n)return r.unshift(t),n[e].apply(n,w([],m(r),!1))}!function(e){e[e.NONE=0]="NONE",e[e.ERROR=30]="ERROR",e[e.WARN=50]="WARN",e[e.INFO=60]="INFO",e[e.DEBUG=70]="DEBUG",e[e.VERBOSE=80]="VERBOSE",e[e.ALL=9999]="ALL"}(n||(n={}));var E=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(n=o.next()).done;)a.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return a},_=function(e,t,r){if(r||2==arguments.length)for(var n,i=0,o=t.length;i<o;i++)!n&&i in t||(n||(n=Array.prototype.slice.call(t,0,i)),n[i]=t[i]);return e.concat(n||Array.prototype.slice.call(t))},S=function(){function e(){function e(e){return function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var n=g("diag");if(n)return n[e].apply(n,_([],E(t),!1))}}var t=this;t.setLogger=function(e,r){if(void 0===r&&(r={logLevel:n.INFO}),e===t){var i,o,a,s=Error("Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation");return t.error(null!=(i=s.stack)?i:s.message),!1}"number"==typeof r&&(r={logLevel:r});var l=g("diag"),c=function(e,t){function r(r,n){var i=t[r];return"function"==typeof i&&e>=n?i.bind(t):function(){}}return e<n.NONE?e=n.NONE:e>n.ALL&&(e=n.ALL),t=t||{},{error:r("error",n.ERROR),warn:r("warn",n.WARN),info:r("info",n.INFO),debug:r("debug",n.DEBUG),verbose:r("verbose",n.VERBOSE)}}(null!=(o=r.logLevel)?o:n.INFO,e);if(l&&!r.suppressOverrideMessage){var u=null!=(a=Error().stack)?a:"<failed to generate stacktrace>";l.warn("Current logger will be overwritten from "+u),c.warn("Current logger will overwrite one already registered from "+u)}return f("diag",c,t,!0)},t.disable=function(){y("diag",t)},t.createComponentLogger=function(e){return new v(e)},t.verbose=e("verbose"),t.debug=e("debug"),t.info=e("info"),t.warn=e("warn"),t.error=e("error")}return e.instance=function(){return this._instance||(this._instance=new e),this._instance},e}(),x=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(n=o.next()).done;)a.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return a},R=function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},C=function(){function e(e){this._entries=e?new Map(e):new Map}return e.prototype.getEntry=function(e){var t=this._entries.get(e);if(t)return Object.assign({},t)},e.prototype.getAllEntries=function(){return Array.from(this._entries.entries()).map(function(e){var t=x(e,2);return[t[0],t[1]]})},e.prototype.setEntry=function(t,r){var n=new e(this._entries);return n._entries.set(t,r),n},e.prototype.removeEntry=function(t){var r=new e(this._entries);return r._entries.delete(t),r},e.prototype.removeEntries=function(){for(var t,r,n=[],i=0;i<arguments.length;i++)n[i]=arguments[i];var o=new e(this._entries);try{for(var a=R(n),s=a.next();!s.done;s=a.next()){var l=s.value;o._entries.delete(l)}}catch(e){t={error:e}}finally{try{s&&!s.done&&(r=a.return)&&r.call(a)}finally{if(t)throw t.error}}return o},e.prototype.clear=function(){return new e},e}(),A=Symbol("BaggageEntryMetadata"),k=S.instance();function T(e){return void 0===e&&(e={}),new C(new Map(Object.entries(e)))}function O(e){return"string"!=typeof e&&(k.error("Cannot create baggage metadata from unknown type: "+typeof e),e=""),{__TYPE__:A,toString:function(){return e}}}function P(e){return Symbol.for(e)}var I=new function e(t){var r=this;r._currentContext=t?new Map(t):new Map,r.getValue=function(e){return r._currentContext.get(e)},r.setValue=function(t,n){var i=new e(r._currentContext);return i._currentContext.set(t,n),i},r.deleteValue=function(t){var n=new e(r._currentContext);return n._currentContext.delete(t),n}},N=[{n:"error",c:"error"},{n:"warn",c:"warn"},{n:"info",c:"info"},{n:"debug",c:"debug"},{n:"verbose",c:"trace"}],j=function(){for(var e=0;e<N.length;e++)this[N[e].n]=function(e){return function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];if(console){var n=console[e];if("function"!=typeof n&&(n=console.log),"function"==typeof n)return n.apply(console,t)}}}(N[e].c)},M=function(){var e=function(t,r){return(e=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(t,r)};return function(t,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),L=function(){function e(){}return e.prototype.createGauge=function(e,t){return G},e.prototype.createHistogram=function(e,t){return F},e.prototype.createCounter=function(e,t){return V},e.prototype.createUpDownCounter=function(e,t){return X},e.prototype.createObservableGauge=function(e,t){return Q},e.prototype.createObservableCounter=function(e,t){return Y},e.prototype.createObservableUpDownCounter=function(e,t){return Z},e.prototype.addBatchObservableCallback=function(e,t){},e.prototype.removeBatchObservableCallback=function(e){},e}(),D=function(){},W=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return M(t,e),t.prototype.add=function(e,t){},t}(D),U=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return M(t,e),t.prototype.add=function(e,t){},t}(D),H=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return M(t,e),t.prototype.record=function(e,t){},t}(D),K=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return M(t,e),t.prototype.record=function(e,t){},t}(D),q=function(){function e(){}return e.prototype.addCallback=function(e){},e.prototype.removeCallback=function(e){},e}(),$=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return M(t,e),t}(q),B=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return M(t,e),t}(q),z=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return M(t,e),t}(q),J=new L,V=new W,G=new H,F=new K,X=new U,Y=new $,Q=new B,Z=new z;function ee(){return J}!function(e){e[e.INT=0]="INT",e[e.DOUBLE=1]="DOUBLE"}(i||(i={}));var et={get:function(e,t){if(null!=e)return e[t]},keys:function(e){return null==e?[]:Object.keys(e)}},er={set:function(e,t,r){null!=e&&(e[t]=r)}},en=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(n=o.next()).done;)a.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return a},ei=function(e,t,r){if(r||2==arguments.length)for(var n,i=0,o=t.length;i<o;i++)!n&&i in t||(n||(n=Array.prototype.slice.call(t,0,i)),n[i]=t[i]);return e.concat(n||Array.prototype.slice.call(t))},eo=function(){function e(){}return e.prototype.active=function(){return I},e.prototype.with=function(e,t,r){for(var n=[],i=3;i<arguments.length;i++)n[i-3]=arguments[i];return t.call.apply(t,ei([r],en(n),!1))},e.prototype.bind=function(e,t){return t},e.prototype.enable=function(){return this},e.prototype.disable=function(){return this},e}(),ea=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(n=o.next()).done;)a.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return a},es=function(e,t,r){if(r||2==arguments.length)for(var n,i=0,o=t.length;i<o;i++)!n&&i in t||(n||(n=Array.prototype.slice.call(t,0,i)),n[i]=t[i]);return e.concat(n||Array.prototype.slice.call(t))},el="context",ec=new eo,eu=function(){function e(){}return e.getInstance=function(){return this._instance||(this._instance=new e),this._instance},e.prototype.setGlobalContextManager=function(e){return f(el,e,S.instance())},e.prototype.active=function(){return this._getContextManager().active()},e.prototype.with=function(e,t,r){for(var n,i=[],o=3;o<arguments.length;o++)i[o-3]=arguments[o];return(n=this._getContextManager()).with.apply(n,es([e,t,r],ea(i),!1))},e.prototype.bind=function(e,t){return this._getContextManager().bind(e,t)},e.prototype._getContextManager=function(){return g(el)||ec},e.prototype.disable=function(){this._getContextManager().disable(),y(el,S.instance())},e}();!function(e){e[e.NONE=0]="NONE",e[e.SAMPLED=1]="SAMPLED"}(o||(o={}));var ed="0000000000000000",ep="00000000000000000000000000000000",eh={traceId:ep,spanId:ed,traceFlags:o.NONE},ef=function(){function e(e){void 0===e&&(e=eh),this._spanContext=e}return e.prototype.spanContext=function(){return this._spanContext},e.prototype.setAttribute=function(e,t){return this},e.prototype.setAttributes=function(e){return this},e.prototype.addEvent=function(e,t){return this},e.prototype.addLink=function(e){return this},e.prototype.addLinks=function(e){return this},e.prototype.setStatus=function(e){return this},e.prototype.updateName=function(e){return this},e.prototype.end=function(e){},e.prototype.isRecording=function(){return!1},e.prototype.recordException=function(e,t){},e}(),eg=P("OpenTelemetry Context Key SPAN");function ey(e){return e.getValue(eg)||void 0}function em(){return ey(eu.getInstance().active())}function ew(e,t){return e.setValue(eg,t)}function ev(e){return e.deleteValue(eg)}function eb(e,t){return ew(e,new ef(t))}function eE(e){var t;return null==(t=ey(e))?void 0:t.spanContext()}var e_=/^([0-9a-f]{32})$/i,eS=/^[0-9a-f]{16}$/i;function ex(e){return e_.test(e)&&e!==ep}function eR(e){return eS.test(e)&&e!==ed}function eC(e){return ex(e.traceId)&&eR(e.spanId)}function eA(e){return new ef(e)}var ek=eu.getInstance(),eT=function(){function e(){}return e.prototype.startSpan=function(e,t,r){if(void 0===r&&(r=ek.active()),null==t?void 0:t.root)return new ef;var n,i=r&&eE(r);return"object"==typeof(n=i)&&"string"==typeof n.spanId&&"string"==typeof n.traceId&&"number"==typeof n.traceFlags&&eC(i)?new ef(i):new ef},e.prototype.startActiveSpan=function(e,t,r,n){if(!(arguments.length<2)){2==arguments.length?a=t:3==arguments.length?(i=t,a=r):(i=t,o=r,a=n);var i,o,a,s=null!=o?o:ek.active(),l=this.startSpan(e,i,s),c=ew(s,l);return ek.with(c,a,void 0,l)}},e}(),eO=new eT,eP=function(){function e(e,t,r,n){this._provider=e,this.name=t,this.version=r,this.options=n}return e.prototype.startSpan=function(e,t,r){return this._getTracer().startSpan(e,t,r)},e.prototype.startActiveSpan=function(e,t,r,n){var i=this._getTracer();return Reflect.apply(i.startActiveSpan,i,arguments)},e.prototype._getTracer=function(){if(this._delegate)return this._delegate;var e=this._provider.getDelegateTracer(this.name,this.version,this.options);return e?(this._delegate=e,this._delegate):eO},e}(),eI=new(function(){function e(){}return e.prototype.getTracer=function(e,t,r){return new eT},e}()),eN=function(){function e(){}return e.prototype.getTracer=function(e,t,r){var n;return null!=(n=this.getDelegateTracer(e,t,r))?n:new eP(this,e,t,r)},e.prototype.getDelegate=function(){var e;return null!=(e=this._delegate)?e:eI},e.prototype.setDelegate=function(e){this._delegate=e},e.prototype.getDelegateTracer=function(e,t,r){var n;return null==(n=this._delegate)?void 0:n.getTracer(e,t,r)},e}();!function(e){e[e.NOT_RECORD=0]="NOT_RECORD",e[e.RECORD=1]="RECORD",e[e.RECORD_AND_SAMPLED=2]="RECORD_AND_SAMPLED"}(a||(a={})),function(e){e[e.INTERNAL=0]="INTERNAL",e[e.SERVER=1]="SERVER",e[e.CLIENT=2]="CLIENT",e[e.PRODUCER=3]="PRODUCER",e[e.CONSUMER=4]="CONSUMER"}(s||(s={})),function(e){e[e.UNSET=0]="UNSET",e[e.OK=1]="OK",e[e.ERROR=2]="ERROR"}(l||(l={}));var ej="[_0-9a-z-*/]",eM=RegExp("^(?:[a-z]"+ej+"{0,255}|"+("[a-z0-9]"+ej+"{0,240}@[a-z]")+ej+"{0,13})$"),eL=/^[ -~]{0,255}[!-~]$/,eD=/,|=/,eW=function(){function e(e){this._internalState=new Map,e&&this._parse(e)}return e.prototype.set=function(e,t){var r=this._clone();return r._internalState.has(e)&&r._internalState.delete(e),r._internalState.set(e,t),r},e.prototype.unset=function(e){var t=this._clone();return t._internalState.delete(e),t},e.prototype.get=function(e){return this._internalState.get(e)},e.prototype.serialize=function(){var e=this;return this._keys().reduce(function(t,r){return t.push(r+"="+e.get(r)),t},[]).join(",")},e.prototype._parse=function(e){!(e.length>512)&&(this._internalState=e.split(",").reverse().reduce(function(e,t){var r=t.trim(),n=r.indexOf("=");if(-1!==n){var i=r.slice(0,n),o=r.slice(n+1,t.length);eM.test(i)&&eL.test(o)&&!eD.test(o)&&e.set(i,o)}return e},new Map),this._internalState.size>32&&(this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,32))))},e.prototype._keys=function(){return Array.from(this._internalState.keys()).reverse()},e.prototype._clone=function(){var t=new e;return t._internalState=new Map(this._internalState),t},e}();function eU(e){return new eW(e)}var eH=eu.getInstance(),eK=S.instance(),eq=new(function(){function e(){}return e.prototype.getMeter=function(e,t,r){return J},e}()),e$="metrics",eB=(function(){function e(){}return e.getInstance=function(){return this._instance||(this._instance=new e),this._instance},e.prototype.setGlobalMeterProvider=function(e){return f(e$,e,S.instance())},e.prototype.getMeterProvider=function(){return g(e$)||eq},e.prototype.getMeter=function(e,t,r){return this.getMeterProvider().getMeter(e,t,r)},e.prototype.disable=function(){y(e$,S.instance())},e})().getInstance(),ez=function(){function e(){}return e.prototype.inject=function(e,t){},e.prototype.extract=function(e,t){return e},e.prototype.fields=function(){return[]},e}(),eJ=P("OpenTelemetry Baggage Key");function eV(e){return e.getValue(eJ)||void 0}function eG(){return eV(eu.getInstance().active())}function eF(e,t){return e.setValue(eJ,t)}function eX(e){return e.deleteValue(eJ)}var eY="propagation",eQ=new ez,eZ=(function(){function e(){this.createBaggage=T,this.getBaggage=eV,this.getActiveBaggage=eG,this.setBaggage=eF,this.deleteBaggage=eX}return e.getInstance=function(){return this._instance||(this._instance=new e),this._instance},e.prototype.setGlobalPropagator=function(e){return f(eY,e,S.instance())},e.prototype.inject=function(e,t,r){return void 0===r&&(r=er),this._getGlobalPropagator().inject(e,t,r)},e.prototype.extract=function(e,t,r){return void 0===r&&(r=et),this._getGlobalPropagator().extract(e,t,r)},e.prototype.fields=function(){return this._getGlobalPropagator().fields()},e.prototype.disable=function(){y(eY,S.instance())},e.prototype._getGlobalPropagator=function(){return g(eY)||eQ},e})().getInstance(),e0="trace",e1=(function(){function e(){this._proxyTracerProvider=new eN,this.wrapSpanContext=eA,this.isSpanContextValid=eC,this.deleteSpan=ev,this.getSpan=ey,this.getActiveSpan=em,this.getSpanContext=eE,this.setSpan=ew,this.setSpanContext=eb}return e.getInstance=function(){return this._instance||(this._instance=new e),this._instance},e.prototype.setGlobalTracerProvider=function(e){var t=f(e0,this._proxyTracerProvider,S.instance());return t&&this._proxyTracerProvider.setDelegate(e),t},e.prototype.getTracerProvider=function(){return g(e0)||this._proxyTracerProvider},e.prototype.getTracer=function(e,t){return this.getTracerProvider().getTracer(e,t)},e.prototype.disable=function(){y(e0,S.instance()),this._proxyTracerProvider=new eN},e})().getInstance();let e2={context:eH,diag:eK,metrics:eB,propagation:eZ,trace:e1}},920:(e,t)=>{"use strict";var r={H:null,A:null,TaintRegistryObjects:new WeakMap,TaintRegistryValues:new Map,TaintRegistryByteLengths:new Set,TaintRegistryPendingRequests:new Set},n=Array.isArray,i=Symbol.for("react.transitional.element"),o=Symbol.for("react.portal"),a=(Symbol.for("react.fragment"),Symbol.for("react.strict_mode"),Symbol.for("react.profiler"),Symbol.for("react.forward_ref"),Symbol.for("react.suspense"),Symbol.for("react.suspense_list"),Symbol.for("react.memo"),Symbol.for("react.lazy")),s=Symbol.for("react.postpone"),l=(Symbol.for("react.view_transition"),Symbol.iterator);Object.prototype.hasOwnProperty,Object.assign;var c=/\/+/g;function u(e,t){var r,n;return"object"==typeof e&&null!==e&&null!=e.key?(r=""+e.key,n={"=":"=0",":":"=2"},"$"+r.replace(/[=:]/g,function(e){return n[e]})):t.toString(36)}function d(){}"function"==typeof reportError&&reportError;var p=Object.getPrototypeOf,h=(r.TaintRegistryObjects,r.TaintRegistryValues),f=(r.TaintRegistryByteLengths,r.TaintRegistryPendingRequests);p(Uint32Array.prototype).constructor,"function"==typeof FinalizationRegistry&&new FinalizationRegistry(function(e){var t=h.get(e);void 0!==t&&(f.forEach(function(r){r.push(e),t.count++}),1===t.count?h.delete(e):t.count--)}),t.unstable_postpone=function(e){throw(e=Error(e)).$$typeof=s,e}},930:(e,t,r)=>{"use strict";var n=r(356).Buffer;Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{handleFetch:function(){return s},interceptFetch:function(){return l},reader:function(){return o}});let i=r(415),o={url:e=>e.url,header:(e,t)=>e.headers.get(t)};async function a(e,t){let{url:r,method:i,headers:o,body:a,cache:s,credentials:l,integrity:c,mode:u,redirect:d,referrer:p,referrerPolicy:h}=t;return{testData:e,api:"fetch",request:{url:r,method:i,headers:[...Array.from(o),["next-test-stack",function(){let e=(Error().stack??"").split("\n");for(let t=1;t<e.length;t++)if(e[t].length>0){e=e.slice(t);break}return(e=(e=(e=e.filter(e=>!e.includes("/next/dist/"))).slice(0,5)).map(e=>e.replace("webpack-internal:///(rsc)/","").trim())).join("    ")}()]],body:a?n.from(await t.arrayBuffer()).toString("base64"):null,cache:s,credentials:l,integrity:c,mode:u,redirect:d,referrer:p,referrerPolicy:h}}}async function s(e,t){let r=(0,i.getTestReqInfo)(t,o);if(!r)return e(t);let{testData:s,proxyPort:l}=r,c=await a(s,t),u=await e(`http://localhost:${l}`,{method:"POST",body:JSON.stringify(c),next:{internal:!0}});if(!u.ok)throw Object.defineProperty(Error(`Proxy request failed: ${u.status}`),"__NEXT_ERROR_CODE",{value:"E146",enumerable:!1,configurable:!0});let d=await u.json(),{api:p}=d;switch(p){case"continue":return e(t);case"abort":case"unhandled":throw Object.defineProperty(Error(`Proxy request aborted [${t.method} ${t.url}]`),"__NEXT_ERROR_CODE",{value:"E145",enumerable:!1,configurable:!0})}let{status:h,headers:f,body:g}=d.response;return new Response(g?n.from(g,"base64"):null,{status:h,headers:new Headers(f)})}function l(e){return r.g.fetch=function(t,r){var n;return(null==r||null==(n=r.next)?void 0:n.internal)?e(t,r):s(e,new Request(t,r))},()=>{r.g.fetch=e}}},958:(e,t,r)=>{"use strict";e.exports=r(920)},962:e=>{"use strict";var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,o={};function a(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"partitioned"in e&&e.partitioned&&"Partitioned","priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean),n=`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}`;return 0===r.length?n:`${n}; ${r.join("; ")}`}function s(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[n,i]=[r.slice(0,e),r.slice(e+1)];try{t.set(n,decodeURIComponent(null!=i?i:"true"))}catch{}}return t}function l(e){if(!e)return;let[[t,r],...n]=s(e),{domain:i,expires:o,httponly:a,maxage:l,path:d,samesite:p,secure:h,partitioned:f,priority:g}=Object.fromEntries(n.map(([e,t])=>[e.toLowerCase().replace(/-/g,""),t]));{var y,m,w={name:t,value:decodeURIComponent(r),domain:i,...o&&{expires:new Date(o)},...a&&{httpOnly:!0},..."string"==typeof l&&{maxAge:Number(l)},path:d,...p&&{sameSite:c.includes(y=(y=p).toLowerCase())?y:void 0},...h&&{secure:!0},...g&&{priority:u.includes(m=(m=g).toLowerCase())?m:void 0},...f&&{partitioned:!0}};let e={};for(let t in w)w[t]&&(e[t]=w[t]);return e}}((e,r)=>{for(var n in r)t(e,n,{get:r[n],enumerable:!0})})(o,{RequestCookies:()=>d,ResponseCookies:()=>p,parseCookie:()=>s,parseSetCookie:()=>l,stringifyCookie:()=>a}),e.exports=((e,o,a,s)=>{if(o&&"object"==typeof o||"function"==typeof o)for(let l of n(o))i.call(e,l)||l===a||t(e,l,{get:()=>o[l],enumerable:!(s=r(o,l))||s.enumerable});return e})(t({},"__esModule",{value:!0}),o);var c=["strict","lax","none"],u=["low","medium","high"],d=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,r]of s(t))this._parsed.set(e,{name:e,value:r})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===n).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,n=this._parsed;return n.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(n).map(([e,t])=>a(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>a(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},p=class{constructor(e){var t,r,n;this._parsed=new Map,this._headers=e;let i=null!=(n=null!=(r=null==(t=e.getSetCookie)?void 0:t.call(e))?r:e.get("set-cookie"))?n:[];for(let e of Array.isArray(i)?i:function(e){if(!e)return[];var t,r,n,i,o,a=[],s=0;function l(){for(;s<e.length&&/\s/.test(e.charAt(s));)s+=1;return s<e.length}for(;s<e.length;){for(t=s,o=!1;l();)if(","===(r=e.charAt(s))){for(n=s,s+=1,l(),i=s;s<e.length&&"="!==(r=e.charAt(s))&&";"!==r&&","!==r;)s+=1;s<e.length&&"="===e.charAt(s)?(o=!0,s=i,a.push(e.substring(t,n)),t=s):s=n+1}else s+=1;(!o||s>=e.length)&&a.push(e.substring(t,e.length))}return a}(i)){let t=l(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed.values());if(!e.length)return r;let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(e=>e.name===n)}has(e){return this._parsed.has(e)}set(...e){let[t,r,n]=1===e.length?[e[0].name,e[0].value,e[0]]:e,i=this._parsed;return i.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:t,value:r,...n})),function(e,t){for(let[,r]of(t.delete("set-cookie"),e)){let e=a(r);t.append("set-cookie",e)}}(i,this._headers),this}delete(...e){let[t,r]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0]];return this.set({...r,name:t,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(a).join("; ")}}}},e=>{var t=e(e.s=18);(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES).middleware_middleware=t}]);
//# sourceMappingURL=middleware.js.map