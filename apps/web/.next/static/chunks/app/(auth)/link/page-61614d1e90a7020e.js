(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6615],{22710:(e,t,r)=>{"use strict";r.d(t,{$:()=>l,r:()=>c});var s=r(44995),n=r(41987),a=r(79649),i=r(615),o=r(99749);let c=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),l=n.forwardRef((e,t)=>{let{className:r,variant:n,size:i,asChild:l=!1,...d}=e,u=l?a.DX:"button";return(0,s.jsx)(u,{className:(0,o.cn)(c({variant:n,size:i,className:r})),ref:t,...d})});l.displayName="Button"},25118:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});var s=r(44995),n=r(41987),a=r(99749);let i=n.forwardRef((e,t)=>{let{className:r,type:n,...i}=e;return(0,s.jsx)("input",{type:n,className:(0,a.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",r),ref:t,...i})});i.displayName="Input"},25591:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>k});var s=r(44995),n=r(41987),a=r(62954),i=r(62942),o=r(22710),c=r(25118),l=r(99749);let d=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("div",{ref:t,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...n})});d.displayName="Card";let u=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",r),...n})});u.displayName="CardHeader";let h=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("div",{ref:t,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",r),...n})});h.displayName="CardTitle";let m=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("div",{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",r),...n})});m.displayName="CardDescription";let f=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("div",{ref:t,className:(0,l.cn)("p-6 pt-0",r),...n})});f.displayName="CardContent",n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("div",{ref:t,className:(0,l.cn)("flex items-center p-6 pt-0",r),...n})}).displayName="CardFooter";let g=(0,r(615).F)("relative w-full rounded-lg border px-4 py-3 text-sm [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground [&>svg~*]:pl-7",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),x=n.forwardRef((e,t)=>{let{className:r,variant:n,...a}=e;return(0,s.jsx)("div",{ref:t,role:"alert",className:(0,l.cn)(g({variant:n}),r),...a})});x.displayName="Alert",n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("h5",{ref:t,className:(0,l.cn)("mb-1 font-medium leading-none tracking-tight",r),...n})}).displayName="AlertTitle";let p=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("div",{ref:t,className:(0,l.cn)("text-sm [&_p]:leading-relaxed",r),...n})});p.displayName="AlertDescription";var y=r(98889);let v=(0,y.A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]),b=(0,y.A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),j=(0,y.A)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]),w=(0,y.A)("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]);function k(){let{isSignedIn:e,isLoaded:t}=(0,a.d)(),r=(0,i.useSearchParams)().get("code"),[l,g]=(0,n.useState)(r||""),[y,k]=(0,n.useState)(!1),[N,_]=(0,n.useState)(null),C=async()=>{if(!l.trim())return void _({success:!1,message:"Please enter a valid linking code"});k(!0),_(null);try{let e=await fetch("/api/telegram/link",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({code:l.trim()})}),t=await e.json();e.ok&&t.success?_({success:!0,message:t.message}):_({success:!1,message:t.error||"Failed to link account"})}catch(e){_({success:!1,message:"Network error. Please try again."})}finally{k(!1)}};return t?e?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center p-4",children:(0,s.jsxs)(d,{className:"w-full max-w-md",children:[(0,s.jsxs)(u,{className:"text-center",children:[(0,s.jsxs)(h,{className:"flex items-center justify-center gap-2",children:[(0,s.jsx)("span",{children:"\uD83D\uDD17"}),"Link Telegram Account"]}),(0,s.jsx)(m,{children:"Connect your Telegram account to BonKai AI"})]}),(0,s.jsxs)(f,{className:"space-y-4",children:[!(null==N?void 0:N.success)&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{htmlFor:"code",className:"text-sm font-medium",children:"Linking Code"}),(0,s.jsx)(c.p,{id:"code",type:"text",placeholder:"Enter 8-character code from bot",value:l,onChange:e=>g(e.target.value.toUpperCase()),className:"text-center font-mono",maxLength:10})]}),(0,s.jsx)(o.$,{onClick:C,disabled:y||!l.trim(),className:"w-full",children:y?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(v,{className:"size-4 animate-spin mr-2"}),"Linking Account..."]}):"Link Account"})]}),N&&(0,s.jsx)(x,{className:N.success?"border-green-200 bg-green-50":"border-red-200 bg-red-50",children:(0,s.jsxs)("div",{className:"flex items-start gap-2",children:[N.success?(0,s.jsx)(b,{className:"size-4 text-green-600 mt-0.5"}):(0,s.jsx)(j,{className:"size-4 text-red-600 mt-0.5"}),(0,s.jsx)(p,{className:N.success?"text-green-800":"text-red-800",children:N.message})]})}),(null==N?void 0:N.success)&&(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)(o.$,{onClick:()=>window.open("https://t.me/bonkai_ai_bot","_blank"),className:"w-full",variant:"outline",children:[(0,s.jsx)(w,{className:"size-4 mr-2"}),"Open Telegram Bot"]}),(0,s.jsx)(o.$,{onClick:()=>{_(null),g("")},className:"w-full",variant:"ghost",children:"Link Another Account"})]}),(0,s.jsxs)("div",{className:"text-center text-sm text-gray-600",children:[(0,s.jsx)("p",{children:"Don't have a linking code?"}),(0,s.jsxs)("p",{children:["Start a chat with"," ",(0,s.jsx)("a",{href:"https://t.me/bonkai_ai_bot",target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:underline",children:"@bonkai_ai_bot"})," ","and use /link"]})]}),(0,s.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg text-xs text-gray-600 space-y-1",children:[(0,s.jsx)("p",{className:"font-semibold",children:"How to link:"}),(0,s.jsxs)("ol",{className:"list-decimal list-inside space-y-1",children:[(0,s.jsx)("li",{children:"Open Telegram and find @bonkai_ai_bot"}),(0,s.jsx)("li",{children:"Send /link command to get your code"}),(0,s.jsx)("li",{children:"Enter the code above to complete linking"}),(0,s.jsx)("li",{children:"Start chatting with BonKai AI!"})]})]})]})]})}):(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center p-4",children:(0,s.jsxs)(d,{className:"w-full max-w-md",children:[(0,s.jsxs)(u,{className:"text-center",children:[(0,s.jsx)(h,{children:"Link Telegram Account"}),(0,s.jsx)(m,{children:"Please sign in to link your Telegram account to BonKai"})]}),(0,s.jsx)(f,{children:(0,s.jsx)(x,{children:(0,s.jsx)(p,{children:"You need to be signed in to link your Telegram account. Please sign in first, then return to complete the linking process."})})})]})}):(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsx)(v,{className:"size-8 animate-spin"})})}},29322:(e,t,r)=>{Promise.resolve().then(r.bind(r,25591))},90551:(e,t,r)=>{"use strict";r.d(t,{P7:()=>n});let s={database:"log",chat:"response",auth:"response",stream:"response",api:"response",history:"response",vote:"response",document:"response",suggestions:"response"};class n extends Error{toResponse(){let e="".concat(this.type,":").concat(this.surface),t=s[this.surface],{message:r,cause:n,statusCode:a}=this;return"log"===t?(console.error({code:e,message:r,cause:n}),Response.json({code:"",message:"Something went wrong. Please try again later."},{status:a})):Response.json({code:e,message:r,cause:n},{status:a})}constructor(e,t){super();let[r,s]=e.split(":");this.type=r,this.cause=t,this.surface=s,this.message=function(e){if(e.includes("database"))return"An error occurred while executing a database query.";switch(e){case"bad_request:api":return"The request couldn't be processed. Please check your input and try again.";case"unauthorized:auth":return"You need to sign in before continuing.";case"forbidden:auth":return"Your account does not have access to this feature.";case"rate_limit:chat":return"You have exceeded your maximum number of messages for the day. Please try again later.";case"not_found:chat":return"The requested chat was not found. Please check the chat ID and try again.";case"forbidden:chat":return"This chat belongs to another user. Please check the chat ID and try again.";case"unauthorized:chat":return"You need to sign in to view this chat. Please sign in and try again.";case"offline:chat":return"We're having trouble sending your message. Please check your internet connection and try again.";case"not_found:document":return"The requested document was not found. Please check the document ID and try again.";case"forbidden:document":return"This document belongs to another user. Please check the document ID and try again.";case"unauthorized:document":return"You need to sign in to view this document. Please sign in and try again.";case"bad_request:document":return"The request to create or update the document was invalid. Please check your input and try again.";default:return"Something went wrong. Please try again later."}}(e),this.statusCode=function(e){switch(e){case"bad_request":return 400;case"unauthorized":return 401;case"forbidden":return 403;case"not_found":return 404;case"rate_limit":return 429;case"offline":return 503;default:return 500}}(this.type)}}},98889:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var s=r(41987);let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&r.indexOf(e)===t).join(" ")};var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,s.forwardRef)((e,t)=>{let{color:r="currentColor",size:n=24,strokeWidth:o=2,absoluteStrokeWidth:c,className:l="",children:d,iconNode:u,...h}=e;return(0,s.createElement)("svg",{ref:t,...i,width:n,height:n,stroke:r,strokeWidth:c?24*Number(o)/Number(n):o,className:a("lucide",l),...h},[...u.map(e=>{let[t,r]=e;return(0,s.createElement)(t,r)}),...Array.isArray(d)?d:[d]])}),c=(e,t)=>{let r=(0,s.forwardRef)((r,i)=>{let{className:c,...l}=r;return(0,s.createElement)(o,{ref:i,iconNode:t,className:a("lucide-".concat(n(e)),c),...l})});return r.displayName="".concat(e),r}},99749:(e,t,r)=>{"use strict";r.d(t,{Dn:()=>d,GO:()=>o,JZ:()=>h,cn:()=>i,jZ:()=>u,lk:()=>l,qz:()=>c});var s=r(32987),n=r(60607),a=r(90551);function i(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,n.QP)((0,s.$)(t))}let o=async e=>{let t=await fetch(e);if(!t.ok){let{code:e,cause:r}=await t.json();throw new a.P7(e,r)}return t.json()};async function c(e,t){try{let r=await fetch(e,t);if(!r.ok){let{code:e,cause:t}=await r.json();throw new a.P7(e,t)}return r}catch(e){if("undefined"!=typeof navigator&&!navigator.onLine)throw new a.P7("offline:chat");throw e}}function l(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,e=>{let t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)})}function d(e,t){return!e||t>e.length?new Date:e[t].createdAt}function u(e){return e.replace("<has_function_call>","")}function h(e){return e.parts.filter(e=>"text"===e.type).map(e=>e.text).join("")}}},e=>{var t=t=>e(e.s=t);e.O(0,[9718,6698,2954,1149,1166,7358],()=>t(29322)),_N_E=e.O()}]);