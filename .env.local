# Clerk Authentication
# Get your keys from https://dashboard.clerk.com
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_Z2VudGxlLXBvcnBvaXNlLTg4LmNsZXJrLmFjY291bnRzLmRldiQ
CLERK_SECRET_KEY=sk_test_414cisee718hiQcIgUGEA6TO5sgb24BYMiiQZpm63S
NEXT_PUBLIC_CLERK_SIGN_IN_URL=/login
NEXT_PUBLIC_CLERK_SIGN_UP_URL=/register
NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL=/
NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL=/

# OpenRouter AI
# Get your API key from https://openrouter.ai/keys
OPENROUTER_API_KEY=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

# Convex Database
# Get started at https://www.convex.dev/
CONVEX_DEPLOYMENT=...
NEXT_PUBLIC_CONVEX_URL=https://...convex.cloud

# Uploadthing File Storage
# Get your keys from https://uploadthing.com/dashboard
UPLOADTHING_SECRET=sk_live_...
UPLOADTHING_APP_ID=...

# Solana Configuration
# Use 'devnet' for development, 'mainnet-beta' for production
NEXT_PUBLIC_SOLANA_NETWORK=devnet
# Optional: Use a custom RPC endpoint (e.g., Helius, QuickNode)
NEXT_PUBLIC_SOLANA_RPC_URL=https://api.devnet.solana.com

# BonKai Token Configuration
# The SPL token address for BonKai token
NEXT_PUBLIC_BONKAI_TOKEN_ADDRESS=...

# Telegram Bot
TELEGRAM_BOT_TOKEN=...
TELEGRAM_WEBHOOK_URL=...

# WebSocket Server
WS_PORT=3001

# Legacy (to be removed during migration)
XAI_API_KEY=...
POSTGRES_URL=...
AUTH_SECRET=...
