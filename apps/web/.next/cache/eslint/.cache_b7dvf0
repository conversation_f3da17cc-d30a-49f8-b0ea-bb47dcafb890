[{"/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(auth)/actions.ts": "1", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(auth)/api/auth/[...nextauth]/route.ts": "2", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(auth)/api/auth/guest/route.ts": "3", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(auth)/auth.config.ts": "4", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(auth)/auth.ts": "5", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(auth)/link/page.tsx": "6", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(auth)/login/page-clerk.tsx": "7", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(auth)/login/page.tsx": "8", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(auth)/register/page-clerk.tsx": "9", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(auth)/register/page.tsx": "10", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(chat)/actions.ts": "11", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(chat)/api/chat/[id]/stream/route.ts": "12", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(chat)/api/chat/route-clerk.ts": "13", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(chat)/api/chat/route.ts": "14", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(chat)/api/chat/schema.ts": "15", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(chat)/api/document/route.ts": "16", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(chat)/api/history/route.ts": "17", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(chat)/api/suggestions/route.ts": "18", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(chat)/api/vote/route.ts": "19", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(chat)/chat/[id]/page.tsx": "20", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(chat)/layout.tsx": "21", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(chat)/page.tsx": "22", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/api/telegram/link/route.ts": "23", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/api/uploadthing/core.ts": "24", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/api/uploadthing/route.ts": "25", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/api/wallet/link/route.ts": "26", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/api/wallet/unlink/route.ts": "27", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/layout.tsx": "28", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/providers.tsx": "29", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/app-sidebar.tsx": "30", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/artifact-actions.tsx": "31", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/artifact-close-button.tsx": "32", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/artifact-messages.tsx": "33", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/artifact.tsx": "34", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/auth-form.tsx": "35", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/chat-header.tsx": "36", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/chat.tsx": "37", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/code-block.tsx": "38", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/code-editor.tsx": "39", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/console.tsx": "40", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/create-artifact.tsx": "41", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/data-stream-handler.tsx": "42", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/data-stream-provider.tsx": "43", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/diffview.tsx": "44", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/document-preview.tsx": "45", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/document-skeleton.tsx": "46", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/document.tsx": "47", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/greeting.tsx": "48", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/icons.tsx": "49", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/image-editor.tsx": "50", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/markdown.tsx": "51", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/message-actions.tsx": "52", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/message-editor.tsx": "53", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/message-reasoning.tsx": "54", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/message.tsx": "55", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/messages.tsx": "56", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/model-selector.tsx": "57", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/multimodal-input.tsx": "58", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/preview-attachment.tsx": "59", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/sheet-editor.tsx": "60", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/sidebar-history-item.tsx": "61", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/sidebar-history.tsx": "62", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/sidebar-toggle.tsx": "63", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/sidebar-user-nav.tsx": "64", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/sign-out-form.tsx": "65", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/submit-button.tsx": "66", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/suggested-actions.tsx": "67", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/suggestion.tsx": "68", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/text-editor.tsx": "69", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/theme-provider.tsx": "70", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/toast.tsx": "71", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/toolbar.tsx": "72", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/version-footer.tsx": "73", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/visibility-selector.tsx": "74", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/wallet-connect-button.tsx": "75", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/wallet-provider.tsx": "76", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/weather.tsx": "77", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/web3/aura-points-display.tsx": "78", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/web3/index.ts": "79", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/web3/rate-limit-indicator.tsx": "80", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/web3/staking-position-card.tsx": "81", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/web3/staking-rewards-calculator.tsx": "82", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/web3/tier-badge.tsx": "83", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/web3/tier-benefits.tsx": "84", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/web3/tier-upgrade-prompt.tsx": "85", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/web3/token-balance-display.tsx": "86", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/web3/token-usage-chart.tsx": "87", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/web3/transaction-history.tsx": "88", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/web3/transaction-status.tsx": "89", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/web3/usage-stats.tsx": "90", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/web3/wallet-linking-flow.tsx": "91", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/web3/wallet-status.tsx": "92", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/web3/web3-dashboard.tsx": "93", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/ai/entitlements.ts": "94", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/ai/models.test.ts": "95", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/ai/models.ts": "96", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/ai/prompts.ts": "97", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/ai/providers-openrouter.ts": "98", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/ai/providers.ts": "99", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/ai/tools/create-document.ts": "100", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/ai/tools/get-weather.ts": "101", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/ai/tools/request-suggestions.ts": "102", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/ai/tools/update-document.ts": "103", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/artifacts/server.ts": "104", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/auth/clerk.ts": "105", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/constants.ts": "106", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/convex/client.ts": "107", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/convex/hooks.ts": "108", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/convex/queries.ts": "109", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/db/queries.ts": "110", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/db/utils.ts": "111", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/editor/config.ts": "112", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/editor/diff.js": "113", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/editor/functions.tsx": "114", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/editor/react-renderer.tsx": "115", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/editor/suggestions.tsx": "116", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/errors.ts": "117", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/middleware/token-gate.ts": "118", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/types.ts": "119", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/uploadthing-utils.ts": "120", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/uploadthing.ts": "121", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/utils.ts": "122"}, {"size": 1866, "mtime": 1751967031838, "results": "123", "hashOfConfig": "124"}, {"size": 47, "mtime": 1751967031839, "results": "125", "hashOfConfig": "124"}, {"size": 643, "mtime": 1751967031839, "results": "126", "hashOfConfig": "124"}, {"size": 356, "mtime": 1751967031839, "results": "127", "hashOfConfig": "124"}, {"size": 2037, "mtime": 1751967031839, "results": "128", "hashOfConfig": "124"}, {"size": 6885, "mtime": 1751982764701, "results": "129", "hashOfConfig": "124"}, {"size": 2398, "mtime": 1751982764701, "results": "130", "hashOfConfig": "124"}, {"size": 2427, "mtime": 1751967031839, "results": "131", "hashOfConfig": "124"}, {"size": 2155, "mtime": 1751982764702, "results": "132", "hashOfConfig": "124"}, {"size": 2622, "mtime": 1751967031839, "results": "133", "hashOfConfig": "124"}, {"size": 1437, "mtime": 1751967031839, "results": "134", "hashOfConfig": "124"}, {"size": 2970, "mtime": 1751967031840, "results": "135", "hashOfConfig": "124"}, {"size": 9092, "mtime": 1751982764706, "results": "136", "hashOfConfig": "124"}, {"size": 6862, "mtime": 1751967031840, "results": "137", "hashOfConfig": "124"}, {"size": 755, "mtime": 1751967031840, "results": "138", "hashOfConfig": "124"}, {"size": 2885, "mtime": 1751967031840, "results": "139", "hashOfConfig": "124"}, {"size": 938, "mtime": 1751967031840, "results": "140", "hashOfConfig": "124"}, {"size": 932, "mtime": 1751967031840, "results": "141", "hashOfConfig": "124"}, {"size": 1775, "mtime": 1751967031841, "results": "142", "hashOfConfig": "124"}, {"size": 1888, "mtime": 1751967031841, "results": "143", "hashOfConfig": "124"}, {"size": 987, "mtime": 1751967031841, "results": "144", "hashOfConfig": "124"}, {"size": 1278, "mtime": 1751967031842, "results": "145", "hashOfConfig": "124"}, {"size": 2541, "mtime": 1751982764706, "results": "146", "hashOfConfig": "124"}, {"size": 78, "mtime": 1751982764707, "results": "147", "hashOfConfig": "124"}, {"size": 276, "mtime": 1751982764707, "results": "148", "hashOfConfig": "124"}, {"size": 963, "mtime": 1751982764708, "results": "149", "hashOfConfig": "124"}, {"size": 721, "mtime": 1751982764708, "results": "150", "hashOfConfig": "124"}, {"size": 2295, "mtime": 1751969192563, "results": "151", "hashOfConfig": "124"}, {"size": 2332, "mtime": 1751982764716, "results": "152", "hashOfConfig": "124"}, {"size": 2066, "mtime": 1751967031844, "results": "153", "hashOfConfig": "124"}, {"size": 2971, "mtime": 1751967031844, "results": "154", "hashOfConfig": "124"}, {"size": 827, "mtime": 1751967031844, "results": "155", "hashOfConfig": "124"}, {"size": 2755, "mtime": 1751967031844, "results": "156", "hashOfConfig": "124"}, {"size": 16283, "mtime": 1751967031844, "results": "157", "hashOfConfig": "124"}, {"size": 1352, "mtime": 1751967031845, "results": "158", "hashOfConfig": "124"}, {"size": 3322, "mtime": 1751967031845, "results": "159", "hashOfConfig": "124"}, {"size": 5228, "mtime": 1751967031845, "results": "160", "hashOfConfig": "124"}, {"size": 820, "mtime": 1751967031845, "results": "161", "hashOfConfig": "124"}, {"size": 3347, "mtime": 1751967031845, "results": "162", "hashOfConfig": "124"}, {"size": 5926, "mtime": 1751967031845, "results": "163", "hashOfConfig": "124"}, {"size": 2978, "mtime": 1751967031845, "results": "164", "hashOfConfig": "124"}, {"size": 2093, "mtime": 1751967031845, "results": "165", "hashOfConfig": "124"}, {"size": 1028, "mtime": 1751967031845, "results": "166", "hashOfConfig": "124"}, {"size": 2879, "mtime": 1751967031845, "results": "167", "hashOfConfig": "124"}, {"size": 8151, "mtime": 1751967031845, "results": "168", "hashOfConfig": "124"}, {"size": 1749, "mtime": 1751967031846, "results": "169", "hashOfConfig": "124"}, {"size": 4378, "mtime": 1751967031846, "results": "170", "hashOfConfig": "124"}, {"size": 765, "mtime": 1751967031846, "results": "171", "hashOfConfig": "124"}, {"size": 54605, "mtime": 1751967031846, "results": "172", "hashOfConfig": "124"}, {"size": 1089, "mtime": 1751967031846, "results": "173", "hashOfConfig": "124"}, {"size": 2573, "mtime": 1751967031846, "results": "174", "hashOfConfig": "124"}, {"size": 5672, "mtime": 1751967031846, "results": "175", "hashOfConfig": "124"}, {"size": 2940, "mtime": 1751967031846, "results": "176", "hashOfConfig": "124"}, {"size": 2018, "mtime": 1751967031847, "results": "177", "hashOfConfig": "124"}, {"size": 12471, "mtime": 1751967031847, "results": "178", "hashOfConfig": "124"}, {"size": 2711, "mtime": 1751967031847, "results": "179", "hashOfConfig": "124"}, {"size": 3183, "mtime": 1751967031847, "results": "180", "hashOfConfig": "124"}, {"size": 11588, "mtime": 1751982764748, "results": "181", "hashOfConfig": "124"}, {"size": 1322, "mtime": 1751967031847, "results": "182", "hashOfConfig": "124"}, {"size": 3862, "mtime": 1751967031848, "results": "183", "hashOfConfig": "124"}, {"size": 3634, "mtime": 1751967031848, "results": "184", "hashOfConfig": "124"}, {"size": 11538, "mtime": 1751967031848, "results": "185", "hashOfConfig": "124"}, {"size": 827, "mtime": 1751967031848, "results": "186", "hashOfConfig": "124"}, {"size": 4006, "mtime": 1751982764757, "results": "187", "hashOfConfig": "124"}, {"size": 446, "mtime": 1751967031849, "results": "188", "hashOfConfig": "124"}, {"size": 814, "mtime": 1751967031849, "results": "189", "hashOfConfig": "124"}, {"size": 2679, "mtime": 1751967031849, "results": "190", "hashOfConfig": "124"}, {"size": 2474, "mtime": 1751967031849, "results": "191", "hashOfConfig": "124"}, {"size": 4465, "mtime": 1751967031850, "results": "192", "hashOfConfig": "124"}, {"size": 300, "mtime": 1751967031850, "results": "193", "hashOfConfig": "124"}, {"size": 2000, "mtime": 1751967031850, "results": "194", "hashOfConfig": "124"}, {"size": 12979, "mtime": 1751967031850, "results": "195", "hashOfConfig": "124"}, {"size": 3086, "mtime": 1751967031852, "results": "196", "hashOfConfig": "124"}, {"size": 3065, "mtime": 1751967031852, "results": "197", "hashOfConfig": "124"}, {"size": 3257, "mtime": 1751982764764, "results": "198", "hashOfConfig": "124"}, {"size": 1190, "mtime": 1751982764765, "results": "199", "hashOfConfig": "124"}, {"size": 8271, "mtime": 1751967031852, "results": "200", "hashOfConfig": "124"}, {"size": 10251, "mtime": 1751982764765, "results": "201", "hashOfConfig": "124"}, {"size": 979, "mtime": 1751982764765, "results": "202", "hashOfConfig": "124"}, {"size": 7729, "mtime": 1751982764765, "results": "203", "hashOfConfig": "124"}, {"size": 11108, "mtime": 1751982764770, "results": "204", "hashOfConfig": "124"}, {"size": 12252, "mtime": 1751982764770, "results": "205", "hashOfConfig": "124"}, {"size": 2254, "mtime": 1751982764767, "results": "206", "hashOfConfig": "124"}, {"size": 4734, "mtime": 1751982764768, "results": "207", "hashOfConfig": "124"}, {"size": 5133, "mtime": 1751982764771, "results": "208", "hashOfConfig": "124"}, {"size": 9363, "mtime": 1751982764773, "results": "209", "hashOfConfig": "124"}, {"size": 11548, "mtime": 1751982764773, "results": "210", "hashOfConfig": "124"}, {"size": 11692, "mtime": 1751982764772, "results": "211", "hashOfConfig": "124"}, {"size": 10273, "mtime": 1751982764772, "results": "212", "hashOfConfig": "124"}, {"size": 18112, "mtime": 1751982764777, "results": "213", "hashOfConfig": "124"}, {"size": 10331, "mtime": 1751982764774, "results": "214", "hashOfConfig": "124"}, {"size": 6945, "mtime": 1751982764774, "results": "215", "hashOfConfig": "124"}, {"size": 8663, "mtime": 1751982764774, "results": "216", "hashOfConfig": "124"}, {"size": 658, "mtime": 1751967031853, "results": "217", "hashOfConfig": "124"}, {"size": 2665, "mtime": 1751967031853, "results": "218", "hashOfConfig": "124"}, {"size": 415, "mtime": 1751967031853, "results": "219", "hashOfConfig": "124"}, {"size": 4206, "mtime": 1751967031853, "results": "220", "hashOfConfig": "124"}, {"size": 3013, "mtime": 1751982764785, "results": "221", "hashOfConfig": "124"}, {"size": 997, "mtime": 1751967031854, "results": "222", "hashOfConfig": "124"}, {"size": 1948, "mtime": 1751967031854, "results": "223", "hashOfConfig": "124"}, {"size": 569, "mtime": 1751967031854, "results": "224", "hashOfConfig": "124"}, {"size": 2932, "mtime": 1751967031854, "results": "225", "hashOfConfig": "124"}, {"size": 1767, "mtime": 1751967031854, "results": "226", "hashOfConfig": "124"}, {"size": 2910, "mtime": 1751967031854, "results": "227", "hashOfConfig": "124"}, {"size": 1269, "mtime": 1751982764787, "results": "228", "hashOfConfig": "124"}, {"size": 457, "mtime": 1751967031854, "results": "229", "hashOfConfig": "124"}, {"size": 343, "mtime": 1751982764787, "results": "230", "hashOfConfig": "124"}, {"size": 3567, "mtime": 1751982764787, "results": "231", "hashOfConfig": "124"}, {"size": 7366, "mtime": 1751982764788, "results": "232", "hashOfConfig": "124"}, {"size": 5511, "mtime": 1751982764787, "results": "233", "hashOfConfig": "124"}, {"size": 380, "mtime": 1751982764787, "results": "234", "hashOfConfig": "124"}, {"size": 1449, "mtime": 1751967031861, "results": "235", "hashOfConfig": "124"}, {"size": 13136, "mtime": 1751967031862, "results": "236", "hashOfConfig": "124"}, {"size": 1744, "mtime": 1751967031863, "results": "237", "hashOfConfig": "124"}, {"size": 270, "mtime": 1751967031864, "results": "238", "hashOfConfig": "124"}, {"size": 3835, "mtime": 1751967031864, "results": "239", "hashOfConfig": "124"}, {"size": 3726, "mtime": 1751967031865, "results": "240", "hashOfConfig": "124"}, {"size": 3444, "mtime": 1751982764779, "results": "241", "hashOfConfig": "124"}, {"size": 1567, "mtime": 1751967031865, "results": "242", "hashOfConfig": "124"}, {"size": 205, "mtime": 1751982764770, "results": "243", "hashOfConfig": "124"}, {"size": 1750, "mtime": 1751982764768, "results": "244", "hashOfConfig": "124"}, {"size": 3036, "mtime": 1751967031865, "results": "245", "hashOfConfig": "124"}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1mdr1q5", {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "492", "messages": "493", "suppressedMessages": "494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "495", "messages": "496", "suppressedMessages": "497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "507", "messages": "508", "suppressedMessages": "509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "510", "messages": "511", "suppressedMessages": "512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "513", "messages": "514", "suppressedMessages": "515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "516", "messages": "517", "suppressedMessages": "518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "519", "messages": "520", "suppressedMessages": "521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "522", "messages": "523", "suppressedMessages": "524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "525", "messages": "526", "suppressedMessages": "527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "528", "messages": "529", "suppressedMessages": "530", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "531", "messages": "532", "suppressedMessages": "533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "534", "messages": "535", "suppressedMessages": "536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "537", "messages": "538", "suppressedMessages": "539", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "540", "messages": "541", "suppressedMessages": "542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "543", "messages": "544", "suppressedMessages": "545", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "546", "messages": "547", "suppressedMessages": "548", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "549", "messages": "550", "suppressedMessages": "551", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "552", "messages": "553", "suppressedMessages": "554", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "555", "messages": "556", "suppressedMessages": "557", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "558", "messages": "559", "suppressedMessages": "560", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "561", "messages": "562", "suppressedMessages": "563", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "564", "messages": "565", "suppressedMessages": "566", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "567", "messages": "568", "suppressedMessages": "569", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "570", "messages": "571", "suppressedMessages": "572", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "573", "messages": "574", "suppressedMessages": "575", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "576", "messages": "577", "suppressedMessages": "578", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "579", "messages": "580", "suppressedMessages": "581", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "582", "messages": "583", "suppressedMessages": "584", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "585", "messages": "586", "suppressedMessages": "587", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "588", "messages": "589", "suppressedMessages": "590", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "591", "messages": "592", "suppressedMessages": "593", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "594", "messages": "595", "suppressedMessages": "596", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "597", "messages": "598", "suppressedMessages": "599", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "600", "messages": "601", "suppressedMessages": "602", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "603", "messages": "604", "suppressedMessages": "605", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "606", "messages": "607", "suppressedMessages": "608", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "609", "messages": "610", "suppressedMessages": "611", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(auth)/actions.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(auth)/api/auth/[...nextauth]/route.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(auth)/api/auth/guest/route.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(auth)/auth.config.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(auth)/auth.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(auth)/link/page.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(auth)/login/page-clerk.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(auth)/login/page.tsx", ["612"], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(auth)/register/page-clerk.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(auth)/register/page.tsx", ["613"], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(chat)/actions.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(chat)/api/chat/[id]/stream/route.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(chat)/api/chat/route-clerk.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(chat)/api/chat/route.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(chat)/api/chat/schema.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(chat)/api/document/route.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(chat)/api/history/route.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(chat)/api/suggestions/route.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(chat)/api/vote/route.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(chat)/chat/[id]/page.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(chat)/layout.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(chat)/page.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/api/telegram/link/route.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/api/uploadthing/core.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/api/uploadthing/route.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/api/wallet/link/route.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/api/wallet/unlink/route.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/layout.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/providers.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/app-sidebar.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/artifact-actions.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/artifact-close-button.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/artifact-messages.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/artifact.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/auth-form.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/chat-header.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/chat.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/code-block.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/code-editor.tsx", ["614"], ["615"], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/console.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/create-artifact.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/data-stream-handler.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/data-stream-provider.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/diffview.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/document-preview.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/document-skeleton.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/document.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/greeting.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/icons.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/image-editor.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/markdown.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/message-actions.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/message-editor.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/message-reasoning.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/message.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/messages.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/model-selector.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/multimodal-input.tsx", [], ["616"], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/preview-attachment.tsx", [], ["617"], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/sheet-editor.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/sidebar-history-item.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/sidebar-history.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/sidebar-toggle.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/sidebar-user-nav.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/sign-out-form.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/submit-button.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/suggested-actions.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/suggestion.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/text-editor.tsx", [], ["618"], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/theme-provider.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/toast.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/toolbar.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/version-footer.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/visibility-selector.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/wallet-connect-button.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/wallet-provider.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/weather.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/web3/aura-points-display.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/web3/index.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/web3/rate-limit-indicator.tsx", ["619", "620"], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/web3/staking-position-card.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/web3/staking-rewards-calculator.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/web3/tier-badge.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/web3/tier-benefits.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/web3/tier-upgrade-prompt.tsx", ["621"], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/web3/token-balance-display.tsx", ["622"], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/web3/token-usage-chart.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/web3/transaction-history.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/web3/transaction-status.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/web3/usage-stats.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/web3/wallet-linking-flow.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/web3/wallet-status.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/web3/web3-dashboard.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/ai/entitlements.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/ai/models.test.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/ai/models.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/ai/prompts.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/ai/providers-openrouter.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/ai/providers.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/ai/tools/create-document.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/ai/tools/get-weather.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/ai/tools/request-suggestions.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/ai/tools/update-document.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/artifacts/server.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/auth/clerk.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/constants.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/convex/client.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/convex/hooks.ts", ["623", "624"], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/convex/queries.ts", ["625", "626"], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/db/queries.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/db/utils.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/editor/config.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/editor/diff.js", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/editor/functions.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/editor/react-renderer.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/editor/suggestions.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/errors.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/middleware/token-gate.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/types.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/uploadthing-utils.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/uploadthing.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/utils.ts", [], [], {"ruleId": "627", "severity": 1, "message": "628", "line": 45, "column": 6, "nodeType": "629", "endLine": 45, "endColumn": 20, "suggestions": "630"}, {"ruleId": "627", "severity": 1, "message": "628", "line": 46, "column": 6, "nodeType": "629", "endLine": 46, "endColumn": 13, "suggestions": "631"}, {"ruleId": "632", "severity": 2, "message": "633", "line": 9, "column": 28, "nodeType": "634", "endLine": 9, "endColumn": 45}, {"ruleId": "627", "severity": 1, "message": "635", "line": 45, "column": 6, "nodeType": "629", "endLine": 45, "endColumn": 8, "suggestions": "636", "suppressions": "637"}, {"ruleId": "627", "severity": 1, "message": "638", "line": 99, "column": 6, "nodeType": "629", "endLine": 99, "endColumn": 8, "suggestions": "639", "suppressions": "640"}, {"ruleId": "641", "severity": 1, "message": "642", "line": 20, "column": 13, "nodeType": "643", "endLine": 25, "endColumn": 15, "suppressions": "644"}, {"ruleId": "627", "severity": 1, "message": "635", "line": 77, "column": 6, "nodeType": "629", "endLine": 77, "endColumn": 8, "suggestions": "645", "suppressions": "646"}, {"ruleId": "647", "severity": 2, "message": "648", "line": 170, "column": 24, "nodeType": "649", "messageId": "650", "suggestions": "651"}, {"ruleId": "647", "severity": 2, "message": "648", "line": 180, "column": 24, "nodeType": "649", "messageId": "650", "suggestions": "652"}, {"ruleId": "647", "severity": 2, "message": "648", "line": 78, "column": 16, "nodeType": "649", "messageId": "650", "suggestions": "653"}, {"ruleId": "641", "severity": 1, "message": "642", "line": 208, "column": 25, "nodeType": "643", "endLine": 212, "endColumn": 27}, {"ruleId": "632", "severity": 2, "message": "654", "line": 2, "column": 21, "nodeType": "634", "endLine": 2, "endColumn": 46}, {"ruleId": "632", "severity": 2, "message": "655", "line": 3, "column": 20, "nodeType": "634", "endLine": 3, "endColumn": 51}, {"ruleId": "632", "severity": 2, "message": "654", "line": 3, "column": 21, "nodeType": "634", "endLine": 3, "endColumn": 46}, {"ruleId": "632", "severity": 2, "message": "655", "line": 4, "column": 20, "nodeType": "634", "endLine": 4, "endColumn": 51}, "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'router' and 'updateSession'. Either include them or remove the dependency array.", "ArrayExpression", ["656"], ["657"], "import/no-unresolved", "Unable to resolve path to module '@/lib/db/schema'.", "Literal", "React Hook useEffect has a missing dependency: 'content'. Either include it or remove the dependency array.", ["658"], ["659"], "React Hook useEffect has missing dependencies: 'localStorageInput' and 'setInput'. Either include them or remove the dependency array. If 'setInput' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["660"], ["661"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", ["662"], ["663"], ["664"], "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["665", "666", "667", "668"], ["669", "670", "671", "672"], ["673", "674", "675", "676"], "Unable to resolve path to module '@/convex/_generated/api'.", "Unable to resolve path to module '@/convex/_generated/dataModel'.", {"desc": "677", "fix": "678"}, {"desc": "679", "fix": "680"}, {"desc": "681", "fix": "682"}, {"kind": "683", "justification": "684"}, {"desc": "685", "fix": "686"}, {"kind": "683", "justification": "684"}, {"kind": "683", "justification": "684"}, {"desc": "681", "fix": "687"}, {"kind": "683", "justification": "684"}, {"messageId": "688", "data": "689", "fix": "690", "desc": "691"}, {"messageId": "688", "data": "692", "fix": "693", "desc": "694"}, {"messageId": "688", "data": "695", "fix": "696", "desc": "697"}, {"messageId": "688", "data": "698", "fix": "699", "desc": "700"}, {"messageId": "688", "data": "701", "fix": "702", "desc": "691"}, {"messageId": "688", "data": "703", "fix": "704", "desc": "694"}, {"messageId": "688", "data": "705", "fix": "706", "desc": "697"}, {"messageId": "688", "data": "707", "fix": "708", "desc": "700"}, {"messageId": "688", "data": "709", "fix": "710", "desc": "691"}, {"messageId": "688", "data": "711", "fix": "712", "desc": "694"}, {"messageId": "688", "data": "713", "fix": "714", "desc": "697"}, {"messageId": "688", "data": "715", "fix": "716", "desc": "700"}, "Update the dependencies array to be: [router, state.status, updateSession]", {"range": "717", "text": "718"}, "Update the dependencies array to be: [router, state, updateSession]", {"range": "719", "text": "720"}, "Update the dependencies array to be: [content]", {"range": "721", "text": "722"}, "directive", "", "Update the dependencies array to be: [localStorageInput, setInput]", {"range": "723", "text": "724"}, {"range": "725", "text": "722"}, "replaceWithAlt", {"alt": "726"}, {"range": "727", "text": "728"}, "Replace with `&apos;`.", {"alt": "729"}, {"range": "730", "text": "731"}, "Replace with `&lsquo;`.", {"alt": "732"}, {"range": "733", "text": "734"}, "Replace with `&#39;`.", {"alt": "735"}, {"range": "736", "text": "737"}, "Replace with `&rsquo;`.", {"alt": "726"}, {"range": "738", "text": "739"}, {"alt": "729"}, {"range": "740", "text": "741"}, {"alt": "732"}, {"range": "742", "text": "743"}, {"alt": "735"}, {"range": "744", "text": "745"}, {"alt": "726"}, {"range": "746", "text": "747"}, {"alt": "729"}, {"range": "748", "text": "749"}, {"alt": "732"}, {"range": "750", "text": "751"}, {"alt": "735"}, {"range": "752", "text": "753"}, [1193, 1207], "[router, state.status, updateSession]", [1389, 1396], "[router, state, updateSession]", [1348, 1350], "[content]", [2754, 2756], "[localStorageInput, setInput]", [1973, 1975], "&apos;", [5490, 5661], "\n                    You&apos;ve reached your hourly request limit. Please wait for\n                    the next hour or upgrade your tier for higher limits.\n                  ", "&lsquo;", [5490, 5661], "\n                    You&lsquo;ve reached your hourly request limit. Please wait for\n                    the next hour or upgrade your tier for higher limits.\n                  ", "&#39;", [5490, 5661], "\n                    You&#39;ve reached your hourly request limit. Please wait for\n                    the next hour or upgrade your tier for higher limits.\n                  ", "&rsquo;", [5490, 5661], "\n                    You&rsquo;ve reached your hourly request limit. Please wait for\n                    the next hour or upgrade your tier for higher limits.\n                  ", [5979, 6012], "\n                    You&apos;ve used ", [5979, 6012], "\n                    You&lsquo;ve used ", [5979, 6012], "\n                    You&#39;ve used ", [5979, 6012], "\n                    You&rsquo;ve used ", [2160, 2245], "\n            You&apos;ve unlocked all BonKai features. Welcome to Diamond tier!\n          ", [2160, 2245], "\n            You&lsquo;ve unlocked all BonKai features. Welcome to Diamond tier!\n          ", [2160, 2245], "\n            You&#39;ve unlocked all BonKai features. Welcome to Diamond tier!\n          ", [2160, 2245], "\n            You&rsquo;ve unlocked all BonKai features. Welcome to Diamond tier!\n          "]