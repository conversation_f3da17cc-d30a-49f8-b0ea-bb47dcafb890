"use strict";(()=>{var e={};e.id=5709,e.ids=[5709],e.modules={1708:e=>{e.exports=require("node:process")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{e.exports=require("node:buffer")},11997:e=>{e.exports=require("punycode")},12412:e=>{e.exports=require("assert")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{e.exports=require("os")},22542:(e,r,t)=>{t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>c,tree:()=>p});var i=t(24332),o=t(48819),s=t(67851),n=t.n(s),a=t(97540),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);t.d(r,d);let p={children:["",{children:["(chat)",{children:["chat",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,59180)),"/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(chat)/chat/[id]/page.tsx"]}]},{}]},{metadata:{icon:[],apple:[],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,34040))).default(e)],twitter:[async e=>(await Promise.resolve().then(t.bind(t,96478))).default(e)],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,36784)),"/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(chat)/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19033,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,39956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,92341,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70196))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,34356)),"/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19033,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,39956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,92341,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70196))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(chat)/chat/[id]/page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},c=new i.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/(chat)/chat/[id]/page",pathname:"/chat/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29727:e=>{e.exports=require("next/dist/compiled/next-server/app-page-experimental.runtime.prod.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},57975:e=>{e.exports=require("node:util")},59180:(e,r,t)=>{t.r(r),t.d(r,{default:()=>c});var i=t(38828),o=t(65208),s=t(42543),n=t(52335),a=t(30921),d=t(11455),p=t(96945),l=t(35361),u=t(77877);async function c(e){let{id:r}=await e.params,t=await (0,d.TJ)({id:r});t||(0,s.notFound)();let c=await (0,n.j2)();if(c||(0,s.redirect)("/api/auth/guest"),"private"===t.visibility&&(!c.user||c.user.id!==t.userId))return(0,s.notFound)();let x=await (0,d.de)({id:r}),h=(0,u.b2)(x),m=(await (0,o.UL)()).get("chat-model");return m?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(a.Chat,{id:t.id,initialMessages:h,initialChatModel:m.value,initialVisibilityType:t.visibility,isReadonly:c?.user?.id!==t.userId,session:c,autoResume:!0}),(0,i.jsx)(p.DataStreamHandler,{})]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(a.Chat,{id:t.id,initialMessages:h,initialChatModel:l.j,initialVisibilityType:t.visibility,isReadonly:c?.user?.id!==t.userId,session:c,autoResume:!0}),(0,i.jsx)(p.DataStreamHandler,{})]})}},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{e.exports=require("node:fs")},73136:e=>{e.exports=require("node:url")},74075:e=>{e.exports=require("zlib")},76760:e=>{e.exports=require("node:path")},77598:e=>{e.exports=require("node:crypto")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},84297:e=>{e.exports=require("async_hooks")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),i=r.X(0,[191,4235,2543,5208,8814,2911,9445,8897,3637,7195,6581,6824,331],()=>t(22542));module.exports=i})();