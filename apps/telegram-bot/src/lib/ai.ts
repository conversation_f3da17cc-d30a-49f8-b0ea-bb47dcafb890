import { openai } from 'ai';
import { generateText, streamText } from 'ai';
import { env } from '../config/env.js';
import { UserTier, TaskComplexity } from '../types/index.js';

// OpenRouter configuration
const openrouter = openai({
  baseURL: 'https://openrouter.ai/api/v1',
  apiKey: env.OPENROUTER_API_KEY,
  headers: {
    'HTTP-Referer': env.WEB_APP_URL,
    'X-Title': 'BonKai Telegram Bot',
  },
});

// Model mapping based on tier and complexity
export function getModelForTask(
  complexity: TaskComplexity,
  userTier: UserTier,
  taskType: 'text' | 'image' | 'video' = 'text',
): string {
  // Handle special task types
  if (taskType === 'image') {
    return userTier === UserTier.FREE
      ? 'google/gemini-2.0-flash-exp:free'
      : 'openai/dall-e-3';
  }

  if (taskType === 'video') {
    return userTier === UserTier.FREE
      ? 'google/gemini-2.0-flash-exp:free'
      : 'google/gemini-pro-vision';
  }

  // Text generation based on complexity and tier
  if (userTier === UserTier.FREE) {
    return 'google/gemini-2.0-flash-exp:free';
  }

  switch (complexity) {
    case TaskComplexity.SIMPLE:
      return 'google/gemini-2.0-flash-exp:free';
    case TaskComplexity.MODERATE:
      return userTier === UserTier.BRONZE
        ? 'google/gemini-2.0-flash-exp:free'
        : 'openai/gpt-4o';
    case TaskComplexity.COMPLEX:
      return userTier === UserTier.DIAMOND ? 'openai/o3-mini' : 'openai/gpt-4o';
    default:
      return 'google/gemini-2.0-flash-exp:free';
  }
}

// Determine task complexity from user message
export function determineTaskComplexity(message: string): TaskComplexity {
  const lowerMessage = message.toLowerCase();

  // Complex indicators
  const complexIndicators = [
    'analyze',
    'research',
    'compare',
    'explain in detail',
    'comprehensive',
    'step by step',
    'algorithm',
    'code review',
    'architecture',
    'design pattern',
    'mathematical proof',
    'detailed analysis',
    'complex problem',
  ];

  // Moderate indicators
  const moderateIndicators = [
    'how to',
    'tutorial',
    'guide',
    'example',
    'implement',
    'create',
    'build',
    'develop',
    'solution',
    'fix',
    'debug',
    'optimize',
  ];

  if (complexIndicators.some((indicator) => lowerMessage.includes(indicator))) {
    return TaskComplexity.COMPLEX;
  }

  if (
    moderateIndicators.some((indicator) => lowerMessage.includes(indicator))
  ) {
    return TaskComplexity.MODERATE;
  }

  return TaskComplexity.SIMPLE;
}

// Generate AI response
export async function generateAIResponse(
  message: string,
  userTier: UserTier,
  conversationHistory: Array<{
    role: 'user' | 'assistant';
    content: string;
  }> = [],
): Promise<{ content: string; tokenCount: number; model: string }> {
  const complexity = determineTaskComplexity(message);
  const model = getModelForTask(complexity, userTier);

  const messages = [
    {
      role: 'system' as const,
      content: `You are BonKai AI, a helpful Web3 and blockchain assistant. You specialize in:
- Blockchain technology and cryptocurrencies
- DeFi protocols and smart contracts
- NFTs and digital assets
- Web3 development and dApps
- Solana ecosystem and SPL tokens
- Trading strategies and market analysis

Current user tier: ${userTier}

Be concise but informative. If the user has a FREE tier, keep responses shorter. For higher tiers, you can provide more detailed explanations.

Always be helpful, accurate, and encourage users to upgrade their tier for more advanced features.`,
    },
    ...conversationHistory.slice(-10), // Keep last 10 messages for context
    {
      role: 'user' as const,
      content: message,
    },
  ];

  try {
    const result = await generateText({
      model: openrouter(model),
      messages,
      maxTokens: userTier === UserTier.FREE ? 500 : 2000,
      temperature: 0.7,
    });

    return {
      content: result.text,
      tokenCount: result.usage?.totalTokens || 0,
      model,
    };
  } catch (error) {
    console.error('Error generating AI response:', error);
    throw new Error('Failed to generate AI response');
  }
}

// Stream AI response (for longer conversations)
export async function streamAIResponse(
  message: string,
  userTier: UserTier,
  conversationHistory: Array<{
    role: 'user' | 'assistant';
    content: string;
  }> = [],
) {
  const complexity = determineTaskComplexity(message);
  const model = getModelForTask(complexity, userTier);

  const messages = [
    {
      role: 'system' as const,
      content: `You are BonKai AI, a helpful Web3 and blockchain assistant. Current user tier: ${userTier}`,
    },
    ...conversationHistory.slice(-10),
    {
      role: 'user' as const,
      content: message,
    },
  ];

  return streamText({
    model: openrouter(model),
    messages,
    maxTokens: userTier === UserTier.FREE ? 500 : 2000,
    temperature: 0.7,
  });
}

// Token limits per tier
export const tokenLimits = {
  [UserTier.FREE]: 10_000, // 10K tokens/month
  [UserTier.BRONZE]: 100_000, // 100K tokens/month
  [UserTier.SILVER]: 500_000, // 500K tokens/month
  [UserTier.DIAMOND]: 2_000_000, // 2M tokens/month
};

// Rate limits per tier (requests per hour)
export const rateLimits = {
  [UserTier.FREE]: 10,
  [UserTier.BRONZE]: 50,
  [UserTier.SILVER]: 200,
  [UserTier.DIAMOND]: 1000,
};
