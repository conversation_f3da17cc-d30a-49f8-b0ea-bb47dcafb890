(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[250],{2656:(e,t,s)=>{"use strict";s.d(t,{B:()=>c});var i=s(44995),n=s(71824),a=s(14622),r=s(41249),l=s(94682),o=s(18399),d=s(41987);let c=(0,d.memo)(function(e){let{content:t,onSaveContent:s,status:c}=e,u=(0,d.useRef)(null),m=(0,d.useRef)(null);return(0,d.useEffect)(()=>{if(u.current&&!m.current){let e=a.$t.create({doc:t,extensions:[o.oQ,(0,r.Hg)(),l.bM]});m.current=new n.Lz({state:e,parent:u.current})}return()=>{m.current&&(m.current.destroy(),m.current=null)}},[]),(0,d.useEffect)(()=>{if(m.current){let e=n.Lz.updateListener.of(e=>{e.docChanged&&e.transactions.find(e=>!e.annotation(a.ZX.remote))&&s(e.state.doc.toString(),!0)}),t=m.current.state.selection,i=a.$t.create({doc:m.current.state.doc,extensions:[o.oQ,(0,r.Hg)(),l.bM,e],selection:t});m.current.setState(i)}},[s]),(0,d.useEffect)(()=>{if(m.current&&t){let e=m.current.state.doc.toString();if("streaming"===c||e!==t){let s=m.current.state.update({changes:{from:0,to:e.length,insert:t},annotations:[a.ZX.remote.of(!0)]});m.current.dispatch(s)}}},[t,c]),(0,i.jsx)("div",{className:"relative not-prose w-full pb-[calc(80dvh)] text-sm",ref:u})},function(e,t){return e.suggestions===t.suggestions&&e.currentVersionIndex===t.currentVersionIndex&&e.isCurrentVersion===t.isCurrentVersion&&("streaming"!==e.status||"streaming"!==t.status)&&e.content===t.content})},5318:(e,t,s)=>{"use strict";s.d(t,{n:()=>l});var i=s(44995),n=s(34109),a=s(77785),r=s.n(a);function l(e){let{title:t,content:s,status:a,isInline:l}=e;return(0,i.jsx)("div",{className:r()("flex flex-row items-center justify-center w-full",{"h-[calc(100dvh-60px)]":!l,"h-[200px]":l}),children:"streaming"===a?(0,i.jsxs)("div",{className:"flex flex-row gap-4 items-center",children:[!l&&(0,i.jsx)("div",{className:"animate-spin",children:(0,i.jsx)(n.hz,{})}),(0,i.jsx)("div",{children:"Generating Image..."})]}):(0,i.jsx)("picture",{children:(0,i.jsx)("img",{className:r()("w-full h-fit max-w-[800px]",{"p-0 md:p-20":!l}),src:"data:image/png;base64,".concat(s),alt:t})})})}},10040:(e,t,s)=>{"use strict";s.d(t,{Chat:()=>H});var i=s(44995),n=s(81801),a=s(63672),r=s(41987),l=s(71266),o=s(98686),d=s(27261),c=s.n(d),u=s(62942),m=s(60357),p=s(25316);let x=(0,p.createServerReference)("408f044626fa09ba67df1f7145568ecbd73e3f6915",p.callServer,void 0,p.findSourceMapURL,"saveChatModelAsCookie");var f=s(22710),g=s(72123);let h=[{id:"chat-model",name:"Chat model",description:"Primary model for all-purpose chat"},{id:"chat-model-reasoning",name:"Reasoning model",description:"Uses advanced reasoning"}];var v=s(99749),b=s(34109);let j={guest:{maxMessagesPerDay:20,availableChatModelIds:["chat-model","chat-model-reasoning"]},regular:{maxMessagesPerDay:100,availableChatModelIds:["chat-model","chat-model-reasoning"]}};function y(e){let{session:t,selectedModelId:s,className:n}=e,[a,l]=(0,r.useState)(!1),[o,d]=(0,r.useOptimistic)(s),{availableChatModelIds:c}=j[t.user.type],u=h.filter(e=>c.includes(e.id)),m=(0,r.useMemo)(()=>u.find(e=>e.id===o),[o,u]);return(0,i.jsxs)(g.rI,{open:a,onOpenChange:l,children:[(0,i.jsx)(g.ty,{asChild:!0,className:(0,v.cn)("w-fit data-[state=open]:bg-accent data-[state=open]:text-accent-foreground",n),children:(0,i.jsxs)(f.$,{"data-testid":"model-selector",variant:"outline",className:"md:px-2 md:h-[34px]",children:[null==m?void 0:m.name,(0,i.jsx)(b.D3,{})]})}),(0,i.jsx)(g.SQ,{align:"start",className:"min-w-[300px]",children:u.map(e=>{let{id:t}=e;return(0,i.jsx)(g._2,{"data-testid":"model-selector-item-".concat(t),onSelect:()=>{l(!1),(0,r.startTransition)(()=>{d(t),x(t)})},"data-active":t===o,asChild:!0,children:(0,i.jsxs)("button",{type:"button",className:"gap-4 group/item flex flex-row justify-between items-center w-full",children:[(0,i.jsxs)("div",{className:"flex flex-col gap-1 items-start",children:[(0,i.jsx)("div",{children:e.name}),(0,i.jsx)("div",{className:"text-xs text-muted-foreground",children:e.description})]}),(0,i.jsx)("div",{className:"text-foreground dark:text-foreground opacity-0 group-data-[active=true]/item:opacity-100",children:(0,i.jsx)(b.PW,{})})]})},t)})})]})}var w=s(92630),k=s(66999);function N(e){let{className:t}=e,{toggleSidebar:s}=(0,w.cL)();return(0,i.jsxs)(k.m_,{children:[(0,i.jsx)(k.k$,{asChild:!0,children:(0,i.jsx)(f.$,{"data-testid":"sidebar-toggle-button",onClick:s,variant:"outline",className:"md:px-2 md:h-fit",children:(0,i.jsx)(b.j0,{size:16})})}),(0,i.jsx)(k.ZI,{align:"start",children:"Toggle Sidebar"})]})}var T=s(8653);let C=[{id:"private",label:"Private",description:"Only you can access this chat",icon:(0,i.jsx)(b.XA,{})},{id:"public",label:"Public",description:"Anyone with the link can access this chat",icon:(0,i.jsx)(b.fC,{})}];function z(e){let{chatId:t,className:s,selectedVisibilityType:n}=e,[a,l]=(0,r.useState)(!1),{visibilityType:o,setVisibilityType:d}=(0,T.I)({chatId:t,initialVisibilityType:n}),c=(0,r.useMemo)(()=>C.find(e=>e.id===o),[o]);return(0,i.jsxs)(g.rI,{open:a,onOpenChange:l,children:[(0,i.jsx)(g.ty,{asChild:!0,className:(0,v.cn)("w-fit data-[state=open]:bg-accent data-[state=open]:text-accent-foreground",s),children:(0,i.jsxs)(f.$,{"data-testid":"visibility-selector",variant:"outline",className:"hidden md:flex md:px-2 md:h-[34px]",children:[null==c?void 0:c.icon,null==c?void 0:c.label,(0,i.jsx)(b.D3,{})]})}),(0,i.jsx)(g.SQ,{align:"start",className:"min-w-[300px]",children:C.map(e=>(0,i.jsxs)(g._2,{"data-testid":"visibility-selector-item-".concat(e.id),onSelect:()=>{d(e.id),l(!1)},className:"gap-4 group/item flex flex-row justify-between items-center","data-active":e.id===o,children:[(0,i.jsxs)("div",{className:"flex flex-col gap-1 items-start",children:[e.label,e.description&&(0,i.jsx)("div",{className:"text-xs text-muted-foreground",children:e.description})]}),(0,i.jsx)("div",{className:"text-foreground dark:text-foreground opacity-0 group-data-[active=true]/item:opacity-100",children:(0,i.jsx)(b.PW,{})})]},e.id))})]})}let S=(0,r.memo)(function(e){let{chatId:t,selectedModelId:s,selectedVisibilityType:n,isReadonly:a,session:r}=e,l=(0,u.useRouter)(),{open:o}=(0,w.cL)(),{width:d}=(0,m.lW)();return(0,i.jsxs)("header",{className:"flex sticky top-0 bg-background py-1.5 items-center px-2 md:px-2 gap-2",children:[(0,i.jsx)(N,{}),(!o||d<768)&&(0,i.jsxs)(k.m_,{children:[(0,i.jsx)(k.k$,{asChild:!0,children:(0,i.jsxs)(f.$,{variant:"outline",className:"order-2 md:order-1 md:px-2 px-2 md:h-fit ml-auto md:ml-0",onClick:()=>{l.push("/"),l.refresh()},children:[(0,i.jsx)(b.c1,{}),(0,i.jsx)("span",{className:"md:sr-only",children:"New Chat"})]})}),(0,i.jsx)(k.ZI,{children:"New Chat"})]}),!a&&(0,i.jsx)(y,{session:r,selectedModelId:s,className:"order-1 md:order-2"}),!a&&(0,i.jsx)(z,{chatId:t,selectedVisibilityType:n,className:"order-1 md:order-3"}),(0,i.jsx)(f.$,{className:"bg-zinc-900 dark:bg-zinc-100 hover:bg-zinc-800 dark:hover:bg-zinc-200 text-zinc-50 dark:text-zinc-900 hidden md:flex py-1.5 px-2 h-fit md:h-[34px] order-4 md:ml-auto",asChild:!0,children:(0,i.jsxs)(c(),{href:'https://vercel.com/new/clone?repository-url=https://github.com/vercel/ai-chatbot&env=AUTH_SECRET&envDescription=Learn more about how to get the API Keys for the application&envLink=https://github.com/vercel/ai-chatbot/blob/main/.env.example&demo-title=AI Chatbot&demo-description=An Open-Source AI Chatbot Template Built With Next.js and the AI SDK by Vercel.&demo-url=https://chat.vercel.ai&products=[{"type":"integration","protocol":"ai","productSlug":"grok","integrationSlug":"xai"},{"type":"integration","protocol":"storage","productSlug":"neon","integrationSlug":"neon"},{"type":"integration","protocol":"storage","productSlug":"upstash-kv","integrationSlug":"upstash"},{"type":"blob"}]',target:"_noblank",children:[(0,i.jsx)(b.PY,{size:16}),"Deploy with Vercel"]})})]})},(e,t)=>e.selectedModelId===t.selectedModelId);var I=s(96714),V=s(98870),E=s(61209),M=s(43992);let P=()=>(0,i.jsxs)("div",{className:"max-w-3xl mx-auto md:mt-20 px-8 size-full flex flex-col justify-center",children:[(0,i.jsx)(M.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},exit:{opacity:0,y:10},transition:{delay:.5},className:"text-2xl font-semibold",children:"Hello there!"}),(0,i.jsx)(M.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},exit:{opacity:0,y:10},transition:{delay:.6},className:"text-2xl text-zinc-500",children:"How can I help you today?"})]},"overview");var A=s(5579),R=s.n(A),D=s(92443),_=s(91984);let L=(0,r.memo)(function(e){let{chatId:t,status:s,votes:n,messages:a,setMessages:r,regenerate:l,isReadonly:o}=e,{containerRef:d,endRef:c,onViewportEnter:u,onViewportLeave:m,hasSentMessage:p}=(0,D.o)({chatId:t,status:s});return(0,_.u)(),(0,i.jsxs)("div",{ref:d,className:"flex flex-col min-w-0 gap-6 flex-1 overflow-y-scroll pt-4 relative",children:[0===a.length&&(0,i.jsx)(P,{}),a.map((e,d)=>(0,i.jsx)(E.e,{chatId:t,message:e,isLoading:"streaming"===s&&a.length-1===d,vote:n?n.find(t=>t.messageId===e.id):void 0,setMessages:r,regenerate:l,isReadonly:o,requiresScrollPadding:p&&d===a.length-1},e.id)),"submitted"===s&&a.length>0&&"user"===a[a.length-1].role&&(0,i.jsx)(E.q,{}),(0,i.jsx)(M.P.div,{ref:c,className:"shrink-0 min-w-[24px] min-h-[24px]",onViewportLeave:m,onViewportEnter:u})]})},(e,t)=>!!e.isArtifactVisible&&!!t.isArtifactVisible||e.status===t.status&&e.messages.length===t.messages.length&&!!R()(e.messages,t.messages)&&(!R()(e.votes,t.votes),!1));var U=s(78193),$=s(36698),B=s(87747),O=s(18654),F=s(90551);function H(e){let{id:t,initialMessages:s,initialChatModel:d,initialVisibilityType:c,isReadonly:m,session:p,autoResume:x}=e,{visibilityType:f}=(0,T.I)({chatId:t,initialVisibilityType:c}),{mutate:g}=(0,l.iX)(),{setDataStream:h}=(0,_.u)(),[b,j]=(0,r.useState)(""),{messages:y,setMessages:w,sendMessage:k,status:N,stop:C,regenerate:z,resumeStream:E}=(0,a.Y_)({id:t,messages:s,experimental_throttle:100,generateId:v.lk,transport:new n.rL({api:"/api/chat",fetch:v.qz,prepareSendMessagesRequest(e){let{messages:t,id:s,body:i}=e;return{body:{id:s,message:t.at(-1),selectedChatModel:d,selectedVisibilityType:f,...i}}}}),onData:e=>{h(t=>t?[...t,e]:[])},onFinish:()=>{g((0,$.WI)(B.i))},onError:e=>{e instanceof F.P7&&(0,O.o)({type:"error",description:e.message})}}),M=(0,u.useSearchParams)().get("query"),[P,A]=(0,r.useState)(!1);(0,r.useEffect)(()=>{M&&!P&&(k({role:"user",parts:[{type:"text",text:M}]}),A(!0),window.history.replaceState({},"","/chat/".concat(t)))},[M,k,P,t]);let{data:R}=(0,o.default)(y.length>=2?"/api/vote?chatId=".concat(t):null,v.GO),[D,H]=(0,r.useState)([]),W=(0,U.HO)(e=>e.isVisible);return!function(e){let{autoResume:t,initialMessages:s,resumeStream:i,setMessages:n}=e,{dataStream:a}=(0,_.u)();(0,r.useEffect)(()=>{if(!t)return;let e=s.at(-1);(null==e?void 0:e.role)==="user"&&i()},[]),(0,r.useEffect)(()=>{if(!a||0===a.length)return;let e=a[0];"data-appendMessage"===e.type&&n([...s,JSON.parse(e.data)])},[a,s,n])}({autoResume:x,initialMessages:s,resumeStream:E,setMessages:w}),(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:"flex flex-col min-w-0 h-dvh bg-background",children:[(0,i.jsx)(S,{chatId:t,selectedModelId:d,selectedVisibilityType:c,isReadonly:m,session:p}),(0,i.jsx)(L,{chatId:t,status:N,votes:R,messages:y,setMessages:w,regenerate:z,isReadonly:m,isArtifactVisible:W}),(0,i.jsx)("form",{className:"flex mx-auto px-4 bg-background pb-4 md:pb-6 gap-2 w-full md:max-w-3xl",children:!m&&(0,i.jsx)(V.Z,{chatId:t,input:b,setInput:j,status:N,stop:C,attachments:D,setAttachments:H,messages:y,setMessages:w,sendMessage:k,selectedVisibilityType:f})})]}),(0,i.jsx)(I.F,{chatId:t,input:b,setInput:j,status:N,stop:C,attachments:D,setAttachments:H,sendMessage:k,messages:y,setMessages:w,regenerate:z,votes:R,isReadonly:m,selectedVisibilityType:f})]})}},15032:(e,t,s)=>{"use strict";s.d(t,{T:()=>r});var i=s(44995),n=s(41987),a=s(99749);let r=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,i.jsx)("textarea",{className:(0,a.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",s),ref:t,...n})});r.displayName="Textarea"},43562:(e,t,s)=>{"use strict";s.d(t,{P:()=>n,_:()=>a});var i=s(44995);let n=e=>{let{artifactKind:t}=e;return"image"===t?(0,i.jsx)("div",{className:"flex flex-col gap-4 w-full justify-center items-center h-[calc(100dvh-60px)]",children:(0,i.jsx)("div",{className:"animate-pulse rounded-lg bg-muted-foreground/20 size-96"})}):(0,i.jsxs)("div",{className:"flex flex-col gap-4 w-full",children:[(0,i.jsx)("div",{className:"animate-pulse rounded-lg h-12 bg-muted-foreground/20 w-1/2"}),(0,i.jsx)("div",{className:"animate-pulse rounded-lg h-5 bg-muted-foreground/20 w-full"}),(0,i.jsx)("div",{className:"animate-pulse rounded-lg h-5 bg-muted-foreground/20 w-full"}),(0,i.jsx)("div",{className:"animate-pulse rounded-lg h-5 bg-muted-foreground/20 w-1/3"}),(0,i.jsx)("div",{className:"animate-pulse rounded-lg h-5 bg-transparent w-52"}),(0,i.jsx)("div",{className:"animate-pulse rounded-lg h-8 bg-muted-foreground/20 w-52"}),(0,i.jsx)("div",{className:"animate-pulse rounded-lg h-5 bg-muted-foreground/20 w-2/3"})]})},a=()=>(0,i.jsxs)("div",{className:"flex flex-col gap-4 w-full",children:[(0,i.jsx)("div",{className:"animate-pulse rounded-lg h-4 bg-muted-foreground/20 w-48"}),(0,i.jsx)("div",{className:"animate-pulse rounded-lg h-4 bg-muted-foreground/20 w-3/4"}),(0,i.jsx)("div",{className:"animate-pulse rounded-lg h-4 bg-muted-foreground/20 w-1/2"}),(0,i.jsx)("div",{className:"animate-pulse rounded-lg h-4 bg-muted-foreground/20 w-64"}),(0,i.jsx)("div",{className:"animate-pulse rounded-lg h-4 bg-muted-foreground/20 w-40"}),(0,i.jsx)("div",{className:"animate-pulse rounded-lg h-4 bg-muted-foreground/20 w-36"}),(0,i.jsx)("div",{className:"animate-pulse rounded-lg h-4 bg-muted-foreground/20 w-64"})]})},56285:(e,t,s)=>{"use strict";s.d(t,{DataStreamHandler:()=>l});var i=s(41987),n=s(96714),a=s(78193),r=s(91984);function l(){let{dataStream:e}=(0,r.u)(),{artifact:t,setArtifact:s,setMetadata:l}=(0,a.ST)(),o=(0,i.useRef)(-1);return(0,i.useEffect)(()=>{if(!(null==e?void 0:e.length))return;let i=e.slice(o.current+1);o.current=e.length-1,i.forEach(e=>{let i=n.R.find(e=>e.kind===t.kind);(null==i?void 0:i.onStreamPart)&&i.onStreamPart({streamPart:e,setArtifact:s,setMetadata:l}),s(t=>{if(!t)return{...a.ls,status:"streaming"};switch(e.type){case"data-id":return{...t,documentId:e.data,status:"streaming"};case"data-title":return{...t,title:e.data,status:"streaming"};case"data-kind":return{...t,kind:e.data,status:"streaming"};case"data-clear":return{...t,content:"",status:"streaming"};case"data-finish":return{...t,status:"idle"};default:return t}})})},[e,s,l,t]),null}},57052:(e,t,s)=>{"use strict";s.d(t,{y:()=>d});var i=s(44995),n=s(41987),a=s(42922),r=s(18903),l=s(68309),o=s(99749);s(76856);let d=(0,n.memo)(e=>{let{content:t,saveContent:s,status:d,isCurrentVersion:c}=e,{resolvedTheme:u}=(0,l.D)(),m=(0,n.useMemo)(()=>{if(!t)return Array(50).fill(Array(26).fill(""));let e=(0,r.parse)(t,{skipEmptyLines:!0}).data.map(e=>{let t=[...e];for(;t.length<26;)t.push("");return t});for(;e.length<50;)e.push(Array(26).fill(""));return e},[t]),p=(0,n.useMemo)(()=>[{key:"rowNumber",name:"",frozen:!0,width:50,renderCell:e=>{let{rowIdx:t}=e;return t+1},cellClass:"border-t border-r dark:bg-zinc-950 dark:text-zinc-50",headerCellClass:"border-t border-r dark:bg-zinc-900 dark:text-zinc-50"},...Array.from({length:26},(e,t)=>({key:t.toString(),name:String.fromCharCode(65+t),renderEditCell:a.jE,width:120,cellClass:(0,o.cn)("border-t dark:bg-zinc-950 dark:text-zinc-50",{"border-l":0!==t}),headerCellClass:(0,o.cn)("border-t dark:bg-zinc-900 dark:text-zinc-50",{"border-l":0!==t})}))],[]),x=(0,n.useMemo)(()=>m.map((e,t)=>{let s={id:t,rowNumber:t+1};return p.slice(1).forEach((t,i)=>{s[t.key]=e[i]||""}),s}),[m,p]),[f,g]=(0,n.useState)(x);(0,n.useEffect)(()=>{g(x)},[x]);let h=e=>(0,r.unparse)(e);return(0,i.jsx)(a.Ay,{className:"dark"===u?"rdg-dark":"rdg-light",columns:p,rows:f,enableVirtualization:!0,onRowsChange:e=>{g(e),s(h(e.map(e=>p.slice(1).map(t=>e[t.key]||""))),!0)},onCellClick:e=>{"rowNumber"!==e.column.key&&e.selectCell(!0)},style:{height:"100%"},defaultColumnOptions:{resizable:!0,sortable:!0}})},function(e,t){return e.currentVersionIndex===t.currentVersionIndex&&e.isCurrentVersion===t.isCurrentVersion&&("streaming"!==e.status||"streaming"!==t.status)&&e.content===t.content&&e.saveContent===t.saveContent})},61209:(e,t,s)=>{"use strict";s.d(t,{e:()=>Z,q:()=>K});var i=s(44995),n=s(77785),a=s.n(n),r=s(49645),l=s(43992),o=s(41987),d=s(34109),c=s(51874),u=s(78193);let m=(e,t)=>{switch(e){case"create":return"present"===t?"Creating":"Created";case"update":return"present"===t?"Updating":"Updated";case"request-suggestions":return"present"===t?"Adding suggestions":"Added suggestions to";default:return null}},p=(0,o.memo)(function(e){let{type:t,result:s,isReadonly:n}=e,{setArtifact:a}=(0,u.ST)();return(0,i.jsxs)("button",{type:"button",className:"bg-background cursor-pointer border py-2 px-3 rounded-xl w-fit flex flex-row gap-3 items-start",onClick:e=>{if(n)return void c.o.error("Viewing files in shared chats is currently not supported.");let t=e.currentTarget.getBoundingClientRect(),i={top:t.top,left:t.left,width:t.width,height:t.height};a({documentId:s.id,kind:s.kind,content:"",title:s.title,isVisible:!0,status:"idle",boundingBox:i})},children:[(0,i.jsx)("div",{className:"text-muted-foreground mt-1",children:"create"===t?(0,i.jsx)(d.oS,{}):"update"===t?(0,i.jsx)(d.vL,{}):"request-suggestions"===t?(0,i.jsx)(d.b1,{}):null}),(0,i.jsx)("div",{className:"text-left",children:"".concat(m(t,"past"),' "').concat(s.title,'"')})]})},()=>!0),x=(0,o.memo)(function(e){let{type:t,args:s,isReadonly:n}=e,{setArtifact:a}=(0,u.ST)();return(0,i.jsxs)("button",{type:"button",className:"cursor pointer w-fit border py-2 px-3 rounded-xl flex flex-row items-start justify-between gap-3",onClick:e=>{if(n)return void c.o.error("Viewing files in shared chats is currently not supported.");let t=e.currentTarget.getBoundingClientRect(),s={top:t.top,left:t.left,width:t.width,height:t.height};a(e=>({...e,isVisible:!0,boundingBox:s}))},children:[(0,i.jsxs)("div",{className:"flex flex-row gap-3 items-start",children:[(0,i.jsx)("div",{className:"text-zinc-500 mt-1",children:"create"===t?(0,i.jsx)(d.oS,{}):"update"===t?(0,i.jsx)(d.vL,{}):"request-suggestions"===t?(0,i.jsx)(d.b1,{}):null}),(0,i.jsx)("div",{className:"text-left",children:"".concat(m(t,"present")," ").concat("create"===t&&"title"in s&&s.title?'"'.concat(s.title,'"'):"update"===t&&"description"in s?'"'.concat(s.description,'"'):"request-suggestions"===t?"for document":"")})]}),(0,i.jsx)("div",{className:"animate-spin mt-1",children:(0,i.jsx)(d.hz,{})})]})},()=>!0);var f=s(79031),g=s(71266),h=s(60357),v=s(22710),b=s(66999),j=s(5579),y=s.n(j);let w=(0,o.memo)(function(e){let{chatId:t,message:s,vote:n,isLoading:a}=e,{mutate:r}=(0,g.iX)(),[l,o]=(0,h.Cj)();return a||"user"===s.role?null:(0,i.jsx)(b.Bc,{delayDuration:0,children:(0,i.jsxs)("div",{className:"flex flex-row gap-2",children:[(0,i.jsxs)(b.m_,{children:[(0,i.jsx)(b.k$,{asChild:!0,children:(0,i.jsx)(v.$,{className:"py-1 px-2 h-fit text-muted-foreground",variant:"outline",onClick:async()=>{var e;let t=null==(e=s.parts)?void 0:e.filter(e=>"text"===e.type).map(e=>e.text).join("\n").trim();if(!t)return void c.o.error("There's no text to copy!");await o(t),c.o.success("Copied to clipboard!")},children:(0,i.jsx)(d.Td,{})})}),(0,i.jsx)(b.ZI,{children:"Copy"})]}),(0,i.jsxs)(b.m_,{children:[(0,i.jsx)(b.k$,{asChild:!0,children:(0,i.jsx)(v.$,{"data-testid":"message-upvote",className:"py-1 px-2 h-fit text-muted-foreground !pointer-events-auto",disabled:null==n?void 0:n.isUpvoted,variant:"outline",onClick:async()=>{let e=fetch("/api/vote",{method:"PATCH",body:JSON.stringify({chatId:t,messageId:s.id,type:"up"})});c.o.promise(e,{loading:"Upvoting Response...",success:()=>(r("/api/vote?chatId=".concat(t),e=>e?[...e.filter(e=>e.messageId!==s.id),{chatId:t,messageId:s.id,isUpvoted:!0}]:[],{revalidate:!1}),"Upvoted Response!"),error:"Failed to upvote response."})},children:(0,i.jsx)(d.$U,{})})}),(0,i.jsx)(b.ZI,{children:"Upvote Response"})]}),(0,i.jsxs)(b.m_,{children:[(0,i.jsx)(b.k$,{asChild:!0,children:(0,i.jsx)(v.$,{"data-testid":"message-downvote",className:"py-1 px-2 h-fit text-muted-foreground !pointer-events-auto",variant:"outline",disabled:n&&!n.isUpvoted,onClick:async()=>{let e=fetch("/api/vote",{method:"PATCH",body:JSON.stringify({chatId:t,messageId:s.id,type:"down"})});c.o.promise(e,{loading:"Downvoting Response...",success:()=>(r("/api/vote?chatId=".concat(t),e=>e?[...e.filter(e=>e.messageId!==s.id),{chatId:t,messageId:s.id,isUpvoted:!1}]:[],{revalidate:!1}),"Downvoted Response!"),error:"Failed to downvote response."})},children:(0,i.jsx)(d.EA,{})})}),(0,i.jsx)(b.ZI,{children:"Downvote Response"})]})]})})},(e,t)=>!!y()(e.vote,t.vote)&&e.isLoading===t.isLoading);var k=s(99587),N=s(22559),T=s(73940);let C={latitude:37.763283,longitude:-122.41286,generationtime_ms:.027894973754882813,utc_offset_seconds:0,timezone:"GMT",timezone_abbreviation:"GMT",elevation:18,current_units:{time:"iso8601",interval:"seconds",temperature_2m:"\xb0C"},current:{time:"2024-10-07T19:30",interval:900,temperature_2m:29.3},hourly_units:{time:"iso8601",temperature_2m:"\xb0C"},hourly:{time:["2024-10-07T00:00","2024-10-07T01:00","2024-10-07T02:00","2024-10-07T03:00","2024-10-07T04:00","2024-10-07T05:00","2024-10-07T06:00","2024-10-07T07:00","2024-10-07T08:00","2024-10-07T09:00","2024-10-07T10:00","2024-10-07T11:00","2024-10-07T12:00","2024-10-07T13:00","2024-10-07T14:00","2024-10-07T15:00","2024-10-07T16:00","2024-10-07T17:00","2024-10-07T18:00","2024-10-07T19:00","2024-10-07T20:00","2024-10-07T21:00","2024-10-07T22:00","2024-10-07T23:00","2024-10-08T00:00","2024-10-08T01:00","2024-10-08T02:00","2024-10-08T03:00","2024-10-08T04:00","2024-10-08T05:00","2024-10-08T06:00","2024-10-08T07:00","2024-10-08T08:00","2024-10-08T09:00","2024-10-08T10:00","2024-10-08T11:00","2024-10-08T12:00","2024-10-08T13:00","2024-10-08T14:00","2024-10-08T15:00","2024-10-08T16:00","2024-10-08T17:00","2024-10-08T18:00","2024-10-08T19:00","2024-10-08T20:00","2024-10-08T21:00","2024-10-08T22:00","2024-10-08T23:00","2024-10-09T00:00","2024-10-09T01:00","2024-10-09T02:00","2024-10-09T03:00","2024-10-09T04:00","2024-10-09T05:00","2024-10-09T06:00","2024-10-09T07:00","2024-10-09T08:00","2024-10-09T09:00","2024-10-09T10:00","2024-10-09T11:00","2024-10-09T12:00","2024-10-09T13:00","2024-10-09T14:00","2024-10-09T15:00","2024-10-09T16:00","2024-10-09T17:00","2024-10-09T18:00","2024-10-09T19:00","2024-10-09T20:00","2024-10-09T21:00","2024-10-09T22:00","2024-10-09T23:00","2024-10-10T00:00","2024-10-10T01:00","2024-10-10T02:00","2024-10-10T03:00","2024-10-10T04:00","2024-10-10T05:00","2024-10-10T06:00","2024-10-10T07:00","2024-10-10T08:00","2024-10-10T09:00","2024-10-10T10:00","2024-10-10T11:00","2024-10-10T12:00","2024-10-10T13:00","2024-10-10T14:00","2024-10-10T15:00","2024-10-10T16:00","2024-10-10T17:00","2024-10-10T18:00","2024-10-10T19:00","2024-10-10T20:00","2024-10-10T21:00","2024-10-10T22:00","2024-10-10T23:00","2024-10-11T00:00","2024-10-11T01:00","2024-10-11T02:00","2024-10-11T03:00"],temperature_2m:[36.6,32.8,29.5,28.6,29.2,28.2,27.5,26.6,26.5,26,25,23.5,23.9,24.2,22.9,21,24,28.1,31.4,33.9,32.1,28.9,26.9,25.2,23,21.1,19.6,18.6,17.7,16.8,16.2,15.5,14.9,14.4,14.2,13.7,13.3,12.9,12.5,13.5,15.8,17.7,19.6,21,21.9,22.3,22,20.7,18.9,17.9,17.3,17,16.7,16.2,15.6,15.2,15,15,15.1,14.8,14.8,14.9,14.7,14.8,15.3,16.2,17.9,19.6,20.5,21.6,21,20.7,19.3,18.7,18.4,17.9,17.3,17,17,16.8,16.4,16.2,16,15.8,15.7,15.4,15.4,16.1,16.7,17,18.6,19,19.5,19.4,18.5,17.9,17.5,16.7,16.3,16.1]},daily_units:{time:"iso8601",sunrise:"iso8601",sunset:"iso8601"},daily:{time:["2024-10-07","2024-10-08","2024-10-09","2024-10-10","2024-10-11"],sunrise:["2024-10-07T07:15","2024-10-08T07:16","2024-10-09T07:17","2024-10-10T07:18","2024-10-11T07:19"],sunset:["2024-10-07T19:00","2024-10-08T18:58","2024-10-09T18:57","2024-10-10T18:55","2024-10-11T18:54"]}};function z(e){return Math.ceil(e)}function S(e){let{weatherAtLocation:t=C}=e,s=Math.max(...t.hourly.temperature_2m.slice(0,24)),n=Math.min(...t.hourly.temperature_2m.slice(0,24)),r=(0,N.v)(new Date(t.current.time),{start:new Date(t.daily.sunrise[0]),end:new Date(t.daily.sunset[0])}),[l,d]=(0,o.useState)(!1);(0,o.useEffect)(()=>{let e=()=>{d(window.innerWidth<768)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]);let c=l?5:6,u=t.hourly.time.findIndex(e=>new Date(e)>=new Date(t.current.time)),m=t.hourly.time.slice(u,u+c),p=t.hourly.temperature_2m.slice(u,u+c);return(0,i.jsxs)("div",{className:a()("flex flex-col gap-4 rounded-2xl p-4 skeleton-bg max-w-[500px]",{"bg-blue-400":r},{"bg-indigo-900":!r}),children:[(0,i.jsxs)("div",{className:"flex flex-row justify-between items-center",children:[(0,i.jsxs)("div",{className:"flex flex-row gap-2 items-center",children:[(0,i.jsx)("div",{className:a()("size-10 rounded-full skeleton-div",{"bg-yellow-300":r},{"bg-indigo-100":!r})}),(0,i.jsxs)("div",{className:"text-4xl font-medium text-blue-50",children:[z(t.current.temperature_2m),t.current_units.temperature_2m]})]}),(0,i.jsx)("div",{className:"text-blue-50",children:"H:".concat(z(s),"\xb0 L:").concat(z(n),"\xb0")})]}),(0,i.jsx)("div",{className:"flex flex-row justify-between",children:m.map((e,s)=>(0,i.jsxs)("div",{className:"flex flex-col items-center gap-1",children:[(0,i.jsx)("div",{className:"text-blue-100 text-xs",children:(0,T.GP)(new Date(e),"ha")}),(0,i.jsx)("div",{className:a()("size-6 rounded-full skeleton-div",{"bg-yellow-300":r},{"bg-indigo-200":!r})}),(0,i.jsxs)("div",{className:"text-blue-50 text-sm",children:[z(p[s]),t.hourly_units.temperature_2m]})]},e))})]})}var I=s(99749),V=s(15032),E=s(25316);let M=(0,E.createServerReference)("4069c99398198eb4ac29211355bb7eee8e5d5fb76c",E.callServer,void 0,E.findSourceMapURL,"deleteTrailingMessages");function P(e){let{message:t,setMode:s,setMessages:n,regenerate:a}=e,[r,l]=(0,o.useState)(!1),[d,c]=(0,o.useState)((0,I.JZ)(t)),u=(0,o.useRef)(null);(0,o.useEffect)(()=>{u.current&&m()},[]);let m=()=>{u.current&&(u.current.style.height="auto",u.current.style.height="".concat(u.current.scrollHeight+2,"px"))};return(0,i.jsxs)("div",{className:"flex flex-col gap-2 w-full",children:[(0,i.jsx)(V.T,{"data-testid":"message-editor",ref:u,className:"bg-transparent outline-none overflow-hidden resize-none !text-base rounded-xl w-full",value:d,onChange:e=>{c(e.target.value),m()}}),(0,i.jsxs)("div",{className:"flex flex-row gap-2 justify-end",children:[(0,i.jsx)(v.$,{variant:"outline",className:"h-fit py-2 px-3",onClick:()=>{s("view")},children:"Cancel"}),(0,i.jsx)(v.$,{"data-testid":"message-editor-send-button",variant:"default",className:"h-fit py-2 px-3",disabled:r,onClick:async()=>{l(!0),await M({id:t.id}),n(e=>{let s=e.findIndex(e=>e.id===t.id);if(-1!==s){let i={...t,parts:[{type:"text",text:d}]};return[...e.slice(0,s),i]}return e}),s("view"),a()},children:r?"Sending...":"Send"})]})]})}var A=s(43562),R=s(98686),D=s(80349),_=s(2656),L=s(57052),U=s(5318);function $(e){let{isReadonly:t,result:s,args:n}=e,{artifact:a,setArtifact:r}=(0,u.ST)(),{data:l,isLoading:d}=(0,R.default)(s?"/api/document?id=".concat(s.id):null,I.GO),c=(0,o.useMemo)(()=>null==l?void 0:l[0],[l]),m=(0,o.useRef)(null);if((0,o.useEffect)(()=>{var e;let t=null==(e=m.current)?void 0:e.getBoundingClientRect();a.documentId&&t&&r(e=>({...e,boundingBox:{left:t.x,top:t.y,width:t.width,height:t.height}}))},[a.documentId,r]),a.isVisible){if(s)return(0,i.jsx)(p,{type:"create",result:{id:s.id,title:s.title,kind:s.kind},isReadonly:t});if(n)return(0,i.jsx)(x,{type:"create",args:{title:n.title,kind:n.kind},isReadonly:t})}if(d){var f;return(0,i.jsx)(B,{artifactKind:null!=(f=s.kind)?f:n.kind})}let g=c||("streaming"===a.status?{title:a.title,kind:a.kind,content:a.content,id:a.documentId,createdAt:new Date,userId:"noop"}:null);return g?(0,i.jsxs)("div",{className:"relative w-full cursor-pointer",children:[(0,i.jsx)(O,{hitboxRef:m,result:s,setArtifact:r}),(0,i.jsx)(F,{title:g.title,kind:g.kind,isStreaming:"streaming"===a.status}),(0,i.jsx)(H,{document:g})]}):(0,i.jsx)(B,{artifactKind:a.kind})}let B=e=>{let{artifactKind:t}=e;return(0,i.jsxs)("div",{className:"w-full",children:[(0,i.jsxs)("div",{className:"p-4 border rounded-t-2xl flex flex-row gap-2 items-center justify-between dark:bg-muted h-[57px] dark:border-zinc-700 border-b-0",children:[(0,i.jsxs)("div",{className:"flex flex-row items-center gap-3",children:[(0,i.jsx)("div",{className:"text-muted-foreground",children:(0,i.jsx)("div",{className:"animate-pulse rounded-md size-4 bg-muted-foreground/20"})}),(0,i.jsx)("div",{className:"animate-pulse rounded-lg h-4 bg-muted-foreground/20 w-24"})]}),(0,i.jsx)("div",{children:(0,i.jsx)(d.P7,{})})]}),"image"===t?(0,i.jsx)("div",{className:"overflow-y-scroll border rounded-b-2xl bg-muted border-t-0 dark:border-zinc-700",children:(0,i.jsx)("div",{className:"animate-pulse h-[257px] bg-muted-foreground/20 w-full"})}):(0,i.jsx)("div",{className:"overflow-y-scroll border rounded-b-2xl p-8 pt-4 bg-muted border-t-0 dark:border-zinc-700",children:(0,i.jsx)(A._,{})})]})},O=(0,o.memo)(e=>{let{hitboxRef:t,result:s,setArtifact:n}=e,a=(0,o.useCallback)(e=>{let t=e.currentTarget.getBoundingClientRect();n(e=>"streaming"===e.status?{...e,isVisible:!0}:{...e,title:s.title,documentId:s.id,kind:s.kind,isVisible:!0,boundingBox:{left:t.x,top:t.y,width:t.width,height:t.height}})},[n,s]);return(0,i.jsx)("div",{className:"size-full absolute top-0 left-0 rounded-xl z-10",ref:t,onClick:a,role:"presentation","aria-hidden":"true",children:(0,i.jsx)("div",{className:"w-full p-4 flex justify-end items-center",children:(0,i.jsx)("div",{className:"absolute right-[9px] top-[13px] p-2 hover:dark:bg-zinc-700 rounded-md hover:bg-zinc-100",children:(0,i.jsx)(d.P7,{})})})})},(e,t)=>!!y()(e.result,t.result)),F=(0,o.memo)(e=>{let{title:t,kind:s,isStreaming:n}=e;return(0,i.jsxs)("div",{className:"p-4 border rounded-t-2xl flex flex-row gap-2 items-start sm:items-center justify-between dark:bg-muted border-b-0 dark:border-zinc-700",children:[(0,i.jsxs)("div",{className:"flex flex-row items-start sm:items-center gap-3",children:[(0,i.jsx)("div",{className:"text-muted-foreground",children:n?(0,i.jsx)("div",{className:"animate-spin",children:(0,i.jsx)(d.hz,{})}):"image"===s?(0,i.jsx)(d.xf,{}):(0,i.jsx)(d.oS,{})}),(0,i.jsx)("div",{className:"-translate-y-1 sm:translate-y-0 font-medium",children:t})]}),(0,i.jsx)("div",{className:"w-8"})]})},(e,t)=>e.title===t.title&&e.isStreaming===t.isStreaming),H=e=>{var t,s;let{document:n}=e,{artifact:a}=(0,u.ST)(),r=(0,I.cn)("h-[257px] overflow-y-scroll border rounded-b-2xl dark:bg-muted border-t-0 dark:border-zinc-700",{"p-4 sm:px-14 sm:py-16":"text"===n.kind,"p-0":"code"===n.kind}),l={content:null!=(t=n.content)?t:"",isCurrentVersion:!0,currentVersionIndex:0,status:a.status,saveContent:()=>{},suggestions:[]};return(0,i.jsx)("div",{className:r,children:"text"===n.kind?(0,i.jsx)(D.K,{...l,onSaveContent:()=>{}}):"code"===n.kind?(0,i.jsx)("div",{className:"flex flex-1 relative w-full",children:(0,i.jsx)("div",{className:"absolute inset-0",children:(0,i.jsx)(_.B,{...l,onSaveContent:()=>{}})})}):"sheet"===n.kind?(0,i.jsx)("div",{className:"flex flex-1 relative size-full p-4",children:(0,i.jsx)("div",{className:"absolute inset-0",children:(0,i.jsx)(L.y,{...l})})}):"image"===n.kind?(0,i.jsx)(U.n,{title:n.title,content:null!=(s=n.content)?s:"",isCurrentVersion:!0,currentVersionIndex:0,status:a.status,isInline:!0}):null})};function W(e){let{isLoading:t,reasoning:s}=e,[n,a]=(0,o.useState)(!0);return(0,i.jsxs)("div",{className:"flex flex-col",children:[t?(0,i.jsxs)("div",{className:"flex flex-row gap-2 items-center",children:[(0,i.jsx)("div",{className:"font-medium",children:"Reasoning"}),(0,i.jsx)("div",{className:"animate-spin",children:(0,i.jsx)(d.hz,{})})]}):(0,i.jsxs)("div",{className:"flex flex-row gap-2 items-center",children:[(0,i.jsx)("div",{className:"font-medium",children:"Reasoned for a few seconds"}),(0,i.jsx)("button",{"data-testid":"message-reasoning-toggle",type:"button",className:"cursor-pointer",onClick:()=>{a(!n)},children:(0,i.jsx)(d.D3,{})})]}),(0,i.jsx)(r.N,{initial:!1,children:n&&(0,i.jsx)(l.P.div,{"data-testid":"message-reasoning",initial:"collapsed",animate:"expanded",exit:"collapsed",variants:{collapsed:{height:0,opacity:0,marginTop:0,marginBottom:0},expanded:{height:"auto",opacity:1,marginTop:"1rem",marginBottom:"0.5rem"}},transition:{duration:.2,ease:"easeInOut"},style:{overflow:"hidden"},className:"pl-4 text-zinc-600 dark:text-zinc-400 border-l flex flex-col gap-4",children:(0,i.jsx)(f.o,{children:s})},"content")})]})}var q=s(91984);let Z=(0,o.memo)(e=>{var t;let{chatId:s,message:n,vote:a,isLoading:c,setMessages:u,regenerate:m,isReadonly:g,requiresScrollPadding:h}=e,[j,y]=(0,o.useState)("view"),N=n.parts.filter(e=>"file"===e.type);return(0,q.u)(),(0,i.jsx)(r.N,{children:(0,i.jsx)(l.P.div,{"data-testid":"message-".concat(n.role),className:"w-full mx-auto max-w-3xl px-4 group/message",initial:{y:5,opacity:0},animate:{y:0,opacity:1},"data-role":n.role,children:(0,i.jsxs)("div",{className:(0,I.cn)("flex gap-4 w-full group-data-[role=user]/message:ml-auto group-data-[role=user]/message:max-w-2xl",{"w-full":"edit"===j,"group-data-[role=user]/message:w-fit":"edit"!==j}),children:["assistant"===n.role&&(0,i.jsx)("div",{className:"size-8 flex items-center rounded-full justify-center ring-1 shrink-0 ring-border bg-background",children:(0,i.jsx)("div",{className:"translate-y-px",children:(0,i.jsx)(d.BZ,{size:14})})}),(0,i.jsxs)("div",{className:(0,I.cn)("flex flex-col gap-4 w-full",{"min-h-96":"assistant"===n.role&&h}),children:[N.length>0&&(0,i.jsx)("div",{"data-testid":"message-attachments",className:"flex flex-row justify-end gap-2",children:N.map(e=>{var t;return(0,i.jsx)(k.q,{attachment:{name:null!=(t=e.filename)?t:"file",contentType:e.mediaType,url:e.url}},e.url)})}),null==(t=n.parts)?void 0:t.map((e,t)=>{var s;let{type:a}=e,r="message-".concat(n.id,"-part-").concat(t);if("reasoning"===a&&(null==(s=e.text)?void 0:s.trim().length)>0)return(0,i.jsx)(W,{isLoading:c,reasoning:e.text},r);if("text"===a){if("view"===j)return(0,i.jsxs)("div",{className:"flex flex-row gap-2 items-start",children:["user"===n.role&&!g&&(0,i.jsxs)(b.m_,{children:[(0,i.jsx)(b.k$,{asChild:!0,children:(0,i.jsx)(v.$,{"data-testid":"message-edit-button",variant:"ghost",className:"px-2 h-fit rounded-full text-muted-foreground opacity-0 group-hover/message:opacity-100",onClick:()=>{y("edit")},children:(0,i.jsx)(d.vL,{})})}),(0,i.jsx)(b.ZI,{children:"Edit message"})]}),(0,i.jsx)("div",{"data-testid":"message-content",className:(0,I.cn)("flex flex-col gap-4",{"bg-primary text-primary-foreground px-3 py-2 rounded-xl":"user"===n.role}),children:(0,i.jsx)(f.o,{children:(0,I.jZ)(e.text)})})]},r);if("edit"===j)return(0,i.jsxs)("div",{className:"flex flex-row gap-2 items-start",children:[(0,i.jsx)("div",{className:"size-8"}),(0,i.jsx)(P,{message:n,setMode:y,setMessages:u,regenerate:m},n.id)]},r)}if("tool-getWeather"===a){let{toolCallId:t,state:s}=e;if("input-available"===s)return(0,i.jsx)("div",{className:"skeleton",children:(0,i.jsx)(S,{})},t);if("output-available"===s){let{output:s}=e;return(0,i.jsx)("div",{children:(0,i.jsx)(S,{weatherAtLocation:s})},t)}}if("tool-createDocument"===a){let{toolCallId:t,state:s}=e;if("input-available"===s){let{input:s}=e;return(0,i.jsx)("div",{children:(0,i.jsx)($,{isReadonly:g,args:s})},t)}if("output-available"===s){let{output:s}=e;return"error"in s?(0,i.jsxs)("div",{className:"text-red-500 p-2 border rounded",children:["Error: ",String(s.error)]},t):(0,i.jsx)("div",{children:(0,i.jsx)($,{isReadonly:g,result:s})},t)}}if("tool-updateDocument"===a){let{toolCallId:t,state:s}=e;if("input-available"===s){let{input:s}=e;return(0,i.jsx)("div",{children:(0,i.jsx)(x,{type:"update",args:s,isReadonly:g})},t)}if("output-available"===s){let{output:s}=e;return"error"in s?(0,i.jsxs)("div",{className:"text-red-500 p-2 border rounded",children:["Error: ",String(s.error)]},t):(0,i.jsx)("div",{children:(0,i.jsx)(p,{type:"update",result:s,isReadonly:g})},t)}}if("tool-requestSuggestions"===a){let{toolCallId:t,state:s}=e;if("input-available"===s){let{input:s}=e;return(0,i.jsx)("div",{children:(0,i.jsx)(x,{type:"request-suggestions",args:s,isReadonly:g})},t)}if("output-available"===s){let{output:s}=e;return"error"in s?(0,i.jsxs)("div",{className:"text-red-500 p-2 border rounded",children:["Error: ",String(s.error)]},t):(0,i.jsx)("div",{children:(0,i.jsx)(p,{type:"request-suggestions",result:s,isReadonly:g})},t)}}}),!g&&(0,i.jsx)(w,{chatId:s,message:n,vote:a,isLoading:c},"action-".concat(n.id))]})]})})})},(e,t)=>e.isLoading===t.isLoading&&e.message.id===t.message.id&&e.requiresScrollPadding===t.requiresScrollPadding&&!!y()(e.message.parts,t.message.parts)&&(!y()(e.vote,t.vote),!1)),K=()=>(0,i.jsx)(l.P.div,{"data-testid":"message-assistant-loading",className:"w-full mx-auto max-w-3xl px-4 group/message min-h-96",initial:{y:5,opacity:0},animate:{y:0,opacity:1,transition:{delay:1}},"data-role":"assistant",children:(0,i.jsxs)("div",{className:a()("flex gap-4 group-data-[role=user]/message:px-3 w-full group-data-[role=user]/message:w-fit group-data-[role=user]/message:ml-auto group-data-[role=user]/message:max-w-2xl group-data-[role=user]/message:py-2 rounded-xl",{"group-data-[role=user]/message:bg-muted":!0}),children:[(0,i.jsx)("div",{className:"size-8 flex items-center rounded-full justify-center ring-1 shrink-0 ring-border",children:(0,i.jsx)(d.BZ,{size:14})}),(0,i.jsx)("div",{className:"flex flex-col gap-2 w-full",children:(0,i.jsx)("div",{className:"flex flex-col gap-4 text-muted-foreground",children:"Hmm..."})})]})})},70250:(e,t,s)=>{Promise.resolve().then(s.bind(s,10040)),Promise.resolve().then(s.bind(s,56285))},76930:(e,t,s)=>{"use strict";s.d(t,{R:()=>a});var i=s(98686),n=s(41987);function a(){let e=(0,n.useRef)(null),t=(0,n.useRef)(null),{data:s=!1,mutate:a}=(0,i.default)("messages:is-at-bottom",null,{fallbackData:!1}),{data:r=!1,mutate:l}=(0,i.default)("messages:should-scroll",null,{fallbackData:!1});return(0,n.useEffect)(()=>{if(r){var e;null==(e=t.current)||e.scrollIntoView({behavior:r}),l(!1)}},[l,r]),{containerRef:e,endRef:t,isAtBottom:s,scrollToBottom:(0,n.useCallback)(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"smooth";l(e)},[l]),onViewportEnter:function(){a(!0)},onViewportLeave:function(){a(!1)}}}},78193:(e,t,s)=>{"use strict";s.d(t,{HO:()=>r,ST:()=>l,ls:()=>a});var i=s(98686),n=s(41987);let a={documentId:"init",content:"",kind:"text",title:"",status:"idle",isVisible:!1,boundingBox:{top:0,left:0,width:0,height:0}};function r(e){let{data:t}=(0,i.default)("artifact",null,{fallbackData:a});return(0,n.useMemo)(()=>t?e(t):e(a),[t,e])}function l(){let{data:e,mutate:t}=(0,i.default)("artifact",null,{fallbackData:a}),s=(0,n.useMemo)(()=>e||a,[e]),r=(0,n.useCallback)(e=>{t(t=>{let s=t||a;return"function"==typeof e?e(s):e})},[t]),{data:l,mutate:o}=(0,i.default)(()=>s.documentId?"artifact-metadata-".concat(s.documentId):null,null,{fallbackData:null});return(0,n.useMemo)(()=>({artifact:s,setArtifact:r,metadata:l,setMetadata:o}),[s,r,l,o])}},79031:(e,t,s)=>{"use strict";s.d(t,{o:()=>u});var i=s(44995),n=s(27261),a=s.n(n),r=s(41987),l=s(41627),o=s(56376);let d={code:function(e){let{node:t,inline:s,className:n,children:a,...r}=e;return s?(0,i.jsx)("code",{className:"".concat(n," text-sm bg-zinc-100 dark:bg-zinc-800 py-0.5 px-1 rounded-md"),...r,children:a}):(0,i.jsx)("div",{className:"not-prose flex flex-col",children:(0,i.jsx)("pre",{...r,className:"text-sm w-full overflow-x-auto dark:bg-zinc-900 p-4 border border-zinc-200 dark:border-zinc-700 rounded-xl dark:text-zinc-50 text-zinc-900",children:(0,i.jsx)("code",{className:"whitespace-pre-wrap break-words",children:a})})})},pre:e=>{let{children:t}=e;return(0,i.jsx)(i.Fragment,{children:t})},ol:e=>{let{node:t,children:s,...n}=e;return(0,i.jsx)("ol",{className:"list-decimal list-outside ml-4",...n,children:s})},li:e=>{let{node:t,children:s,...n}=e;return(0,i.jsx)("li",{className:"py-1",...n,children:s})},ul:e=>{let{node:t,children:s,...n}=e;return(0,i.jsx)("ul",{className:"list-decimal list-outside ml-4",...n,children:s})},strong:e=>{let{node:t,children:s,...n}=e;return(0,i.jsx)("span",{className:"font-semibold",...n,children:s})},a:e=>{let{node:t,children:s,...n}=e;return(0,i.jsx)(a(),{className:"text-blue-500 hover:underline",target:"_blank",rel:"noreferrer",...n,children:s})},h1:e=>{let{node:t,children:s,...n}=e;return(0,i.jsx)("h1",{className:"text-3xl font-semibold mt-6 mb-2",...n,children:s})},h2:e=>{let{node:t,children:s,...n}=e;return(0,i.jsx)("h2",{className:"text-2xl font-semibold mt-6 mb-2",...n,children:s})},h3:e=>{let{node:t,children:s,...n}=e;return(0,i.jsx)("h3",{className:"text-xl font-semibold mt-6 mb-2",...n,children:s})},h4:e=>{let{node:t,children:s,...n}=e;return(0,i.jsx)("h4",{className:"text-lg font-semibold mt-6 mb-2",...n,children:s})},h5:e=>{let{node:t,children:s,...n}=e;return(0,i.jsx)("h5",{className:"text-base font-semibold mt-6 mb-2",...n,children:s})},h6:e=>{let{node:t,children:s,...n}=e;return(0,i.jsx)("h6",{className:"text-sm font-semibold mt-6 mb-2",...n,children:s})}},c=[o.A],u=(0,r.memo)(e=>{let{children:t}=e;return(0,i.jsx)(l.oz,{remarkPlugins:c,components:d,children:t})},(e,t)=>e.children===t.children)},80349:(e,t,s)=>{"use strict";s.d(t,{K:()=>E});var i=s(44995),n=s(49153),a=s(86566),r=s(40082),l=s(78660),o=s(41987),d=s(8622),c=s(68023),u=s(73763),m=s(7357),p=s(73117),x=s(79031),f=s(2879),g=s(49645),h=s(43992),v=s(60357),b=s(34109),j=s(22710),y=s(99749);let w=e=>{let{suggestion:t,onApply:s,artifactKind:n}=e,[a,r]=(0,o.useState)(!1),{width:l}=(0,v.lW)();return(0,i.jsx)(g.N,{children:a?(0,i.jsxs)(h.P.div,{className:"absolute bg-background p-3 flex flex-col gap-3 rounded-2xl border text-sm w-56 shadow-xl z-50 -right-12 md:-right-16 font-sans",transition:{type:"spring",stiffness:500,damping:30},initial:{opacity:0,y:-10},animate:{opacity:1,y:-20},exit:{opacity:0,y:-10},whileHover:{scale:1.05},children:[(0,i.jsxs)("div",{className:"flex flex-row items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex flex-row items-center gap-2",children:[(0,i.jsx)("div",{className:"size-4 bg-muted-foreground/25 rounded-full"}),(0,i.jsx)("div",{className:"font-medium",children:"Assistant"})]}),(0,i.jsx)("button",{type:"button",className:"text-xs text-gray-500 cursor-pointer",onClick:()=>{r(!1)},children:(0,i.jsx)(b.w0,{size:12})})]}),(0,i.jsx)("div",{children:t.description}),(0,i.jsx)(j.$,{variant:"outline",className:"w-fit py-1.5 px-3 rounded-full",onClick:s,children:"Apply"})]},t.id):(0,i.jsx)(h.P.div,{className:(0,y.cn)("cursor-pointer text-muted-foreground p-1",{"absolute -right-8":"text"===n,"sticky top-0 right-4":"code"===n}),onClick:()=>{r(!0)},whileHover:{scale:1.1},children:(0,i.jsx)(b.b1,{size:l&&l<768?16:14})})})},k=new r.hs("suggestions"),N=new r.k_({key:k,state:{init:()=>({decorations:l.zF.empty,selected:null}),apply(e,t){let s=e.getMeta(k);return s||{decorations:t.decorations.map(e.mapping,e.doc),selected:t.selected}}},props:{decorations(e){var t,s;return null!=(s=null==(t=this.getState(e))?void 0:t.decorations)?s:l.zF.empty}}}),T=e=>{let t=d.S4.fromSchema(S),s=(0,p.F0)((0,i.jsx)(x.o,{children:e})),n=document.createElement("div");return n.innerHTML=s,t.parse(n)},C=e=>m.lR.serialize(e),z=(e,t)=>{let s=[];for(let t of e)s.push(l.NZ.inline(t.selectionStart,t.selectionEnd,{class:"suggestion-highlight"},{suggestionId:t.id,type:"highlight"})),s.push(l.NZ.widget(t.selectionStart,e=>{let{dom:s}=function(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"text",n=document.createElement("span"),a=(0,f.createRoot)(n);return n.addEventListener("mousedown",e=>{e.preventDefault(),t.dom.blur()}),a.render((0,i.jsx)(w,{suggestion:e,onApply:()=>{let{state:s,dispatch:i}=t,n=s.tr,a=k.getState(s),r=null==a?void 0:a.decorations;if(r){let t=l.zF.create(s.doc,r.find().filter(t=>t.spec.suggestionId!==e.id));n.setMeta(k,{decorations:t,selected:null}),i(n)}let o=t.state.tr.replaceWith(e.selectionStart,e.selectionEnd,s.schema.text(e.suggestedText));o.setMeta("no-debounce",!0),i(o)},artifactKind:s})),{dom:n,destroy:()=>{setTimeout(()=>{a.unmount()},0)}}}(t,e);return s},{suggestionId:t.id,type:"widget"}));return l.zF.create(t.state.doc,s)},S=new d.Sj({nodes:(0,u.ZW)(c.wQ.spec.nodes,"paragraph block*","block"),marks:c.wQ.spec.marks});function I(e){return(0,a.JJ)(new RegExp("^(#{1,".concat(e,"})\\s$")),S.nodes.heading,()=>({level:e}))}let V=e=>{let{transaction:t,editorRef:s,onSaveContent:i}=e;if(!s||!s.current)return;let n=s.current.state.apply(t);s.current.updateState(n),t.docChanged&&!t.getMeta("no-save")&&i(C(n.doc),!t.getMeta("no-debounce"))},E=(0,o.memo)(function(e){let{content:t,onSaveContent:s,suggestions:d,status:c}=e,u=(0,o.useRef)(null),m=(0,o.useRef)(null);return(0,o.useEffect)(()=>{if(u.current&&!m.current){let e=r.$t.create({doc:T(t),plugins:[...(0,n.WX)({schema:S,menuBar:!1}),(0,a.sM)({rules:[I(1),I(2),I(3),I(4),I(5),I(6)]}),N]});m.current=new l.Lz(u.current,{state:e})}return()=>{m.current&&(m.current.destroy(),m.current=null)}},[]),(0,o.useEffect)(()=>{m.current&&m.current.setProps({dispatchTransaction:e=>{V({transaction:e,editorRef:m,onSaveContent:s})}})},[s]),(0,o.useEffect)(()=>{if(m.current&&t){let e=C(m.current.state.doc);if("streaming"===c){let e=T(t),s=m.current.state.tr.replaceWith(0,m.current.state.doc.content.size,e.content);s.setMeta("no-save",!0),m.current.dispatch(s);return}if(e!==t){let e=T(t),s=m.current.state.tr.replaceWith(0,m.current.state.doc.content.size,e.content);s.setMeta("no-save",!0),m.current.dispatch(s)}}},[t,c]),(0,o.useEffect)(()=>{var e,s;if((null==(e=m.current)?void 0:e.state.doc)&&t){let e=z((s=m.current.state.doc,d.map(e=>{var t;let i,n=(t=e.originalText,i=null,s.nodesBetween(0,s.content.size,(e,s)=>{if(e.isText&&e.text){let n=e.text.indexOf(t);if(-1!==n)return i={start:s+n,end:s+n+t.length},!1}return!0}),i);return n?{...e,selectionStart:n.start,selectionEnd:n.end}:{...e,selectionStart:0,selectionEnd:0}})).filter(e=>e.selectionStart&&e.selectionEnd),m.current),t=m.current.state.tr;t.setMeta(k,{decorations:e}),m.current.dispatch(t)}},[d,t]),(0,i.jsx)("div",{className:"relative prose dark:prose-invert",ref:u})},function(e,t){return e.suggestions===t.suggestions&&e.currentVersionIndex===t.currentVersionIndex&&e.isCurrentVersion===t.isCurrentVersion&&("streaming"!==e.status||"streaming"!==t.status)&&e.content===t.content&&e.onSaveContent===t.onSaveContent})},92443:(e,t,s)=>{"use strict";s.d(t,{o:()=>a});var i=s(41987),n=s(76930);function a(e){let{chatId:t,status:s}=e,{containerRef:a,endRef:r,isAtBottom:l,scrollToBottom:o,onViewportEnter:d,onViewportLeave:c}=(0,n.R)(),[u,m]=(0,i.useState)(!1);return(0,i.useEffect)(()=>{t&&(o("instant"),m(!1))},[t,o]),(0,i.useEffect)(()=>{"submitted"===s&&m(!0)},[s]),{containerRef:a,endRef:r,isAtBottom:l,scrollToBottom:o,onViewportEnter:d,onViewportLeave:c,hasSentMessage:u}}},96714:(e,t,s)=>{"use strict";s.d(t,{F:()=>eA,R:()=>eP});var i=s(44995),n=s(21494),a=s(49645),r=s(43992),l=s(41987),o=s(98686),d=s(71266),c=s(60357),u=s(99749),m=s(98870),p=s(77785),x=s.n(p),f=s(22896),g=s(64194),h=s(67301),v=s(66999),b=s(34109);let j=e=>{let{description:t,icon:s,selectedTool:n,setSelectedTool:a,isToolbarVisible:o,setIsToolbarVisible:d,isAnimating:c,sendMessage:u,onClick:m}=e,[p,f]=(0,l.useState)(!1);(0,l.useEffect)(()=>{n!==t&&f(!1)},[n,t]);let g=()=>{if(!o&&d)return void d(!0);if(!n){f(!0),a(t);return}n!==t?a(t):(a(null),m({sendMessage:u}))};return(0,i.jsxs)(v.m_,{open:p&&!c,children:[(0,i.jsx)(v.k$,{asChild:!0,children:(0,i.jsx)(r.P.div,{className:x()("p-3 rounded-full",{"bg-primary !text-primary-foreground":n===t}),onHoverStart:()=>{f(!0)},onHoverEnd:()=>{n!==t&&f(!1)},onKeyDown:e=>{"Enter"===e.key&&g()},initial:{scale:1,opacity:0},animate:{opacity:1,transition:{delay:.1}},whileHover:{scale:1.1},whileTap:{scale:.95},exit:{scale:.9,opacity:0,transition:{duration:.1}},onClick:()=>{g()},children:n===t?(0,i.jsx)(b.Kp,{}):s})}),(0,i.jsx)(v.ZI,{side:"left",sideOffset:16,className:"bg-foreground text-background rounded-2xl p-3 px-4",children:t})]})},y=[...Array(6)].map(e=>(0,h.Ak)(5)),w=e=>{let{setSelectedTool:t,sendMessage:s,isAnimating:n}=e,a=["Elementary","Middle School","Keep current level","High School","College","Graduate"],o=(0,f.d)(-80),d=(0,g.G)(o,[0,-202],[0,5]),[c,u]=(0,l.useState)(2),[m,p]=(0,l.useState)(!1);return(0,l.useEffect)(()=>{let e=d.on("change",e=>{u(Math.min(5,Math.max(0,Math.round(Math.abs(e)))))});return()=>e()},[d]),(0,i.jsxs)("div",{className:"relative flex flex-col justify-end items-center",children:[y.map(e=>(0,i.jsx)(r.P.div,{className:"size-[40px] flex flex-row items-center justify-center",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{delay:.1},children:(0,i.jsx)("div",{className:"size-2 rounded-full bg-muted-foreground/40"})},e)),(0,i.jsx)(v.Bc,{children:(0,i.jsxs)(v.m_,{open:!n,children:[(0,i.jsx)(v.k$,{asChild:!0,children:(0,i.jsx)(r.P.div,{className:x()("absolute bg-background p-3 border rounded-full flex flex-row items-center",{"bg-primary text-primary-foreground":2!==c,"bg-background text-foreground":2===c}),style:{y:o},drag:"y",dragElastic:0,dragMomentum:!1,whileHover:{scale:1.05},whileTap:{scale:.95},transition:{duration:.1},dragConstraints:{top:-202,bottom:0},onDragStart:()=>{p(!1)},onDragEnd:()=>{2===c?t(null):p(!0)},onClick:()=>{2!==c&&m&&(s({role:"user",parts:[{type:"text",text:"Please adjust the reading level to ".concat(a[c]," level.")}]}),t(null))},children:2===c?(0,i.jsx)(b.hD,{}):(0,i.jsx)(b.Kp,{})})}),(0,i.jsx)(v.ZI,{side:"left",sideOffset:16,className:"bg-foreground text-background text-sm rounded-2xl p-3 px-4",children:a[c]})]})})]})},k=e=>{let{isToolbarVisible:t,selectedTool:s,setSelectedTool:n,sendMessage:l,isAnimating:o,setIsToolbarVisible:d,tools:c}=e,[u,...m]=c;return(0,i.jsxs)(r.P.div,{className:"flex flex-col gap-1.5",initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.95},children:[(0,i.jsx)(a.N,{children:t&&m.map(e=>(0,i.jsx)(j,{description:e.description,icon:e.icon,selectedTool:s,setSelectedTool:n,sendMessage:l,isAnimating:o,onClick:e.onClick},e.description))}),(0,i.jsx)(j,{description:u.description,icon:u.icon,selectedTool:s,setSelectedTool:n,isToolbarVisible:t,setIsToolbarVisible:d,sendMessage:l,isAnimating:o,onClick:u.onClick})]})},N=(0,l.memo)(e=>{let{isToolbarVisible:t,setIsToolbarVisible:s,sendMessage:n,status:a,stop:o,setMessages:d,artifactKind:u}=e,m=(0,l.useRef)(null),p=(0,l.useRef)(),[x,f]=(0,l.useState)(null),[g,h]=(0,l.useState)(!1);(0,c.Wr)(m,()=>{s(!1),f(null)});let j=()=>{p.current&&clearTimeout(p.current),p.current=setTimeout(()=>{f(null),s(!1)},2e3)},y=()=>{p.current&&clearTimeout(p.current)};(0,l.useEffect)(()=>()=>{p.current&&clearTimeout(p.current)},[]),(0,l.useEffect)(()=>{"streaming"===a&&s(!1)},[a,s]);let N=eP.find(e=>e.kind===u);if(!N)throw Error("Artifact definition not found!");let T=N.toolbar;return 0===T.length?null:(0,i.jsx)(v.Bc,{delayDuration:0,children:(0,i.jsx)(r.P.div,{className:"cursor-pointer absolute right-6 bottom-6 p-1.5 border rounded-full shadow-lg bg-background flex flex-col justify-end",initial:{opacity:0,y:-20,scale:1},animate:t?"adjust-reading-level"===x?{opacity:1,y:0,height:258,transition:{delay:0},scale:.95}:{opacity:1,y:0,height:50*T.length,transition:{delay:0},scale:1}:{opacity:1,y:0,height:54,transition:{delay:0}},exit:{opacity:0,y:-20,transition:{duration:.1}},transition:{type:"spring",stiffness:300,damping:25},onHoverStart:()=>{"streaming"!==a&&(y(),s(!0))},onHoverEnd:()=>{"streaming"!==a&&j()},onAnimationStart:()=>{h(!0)},onAnimationComplete:()=>{h(!1)},ref:m,children:"streaming"===a?(0,i.jsx)(r.P.div,{initial:{scale:1},animate:{scale:1.4},exit:{scale:1},className:"p-3",onClick:()=>{o(),d(e=>e)},children:(0,i.jsx)(b.wF,{})},"stop-icon"):"adjust-reading-level"===x?(0,i.jsx)(w,{sendMessage:n,setSelectedTool:f,isAnimating:g},"reading-level-selector"):(0,i.jsx)(k,{sendMessage:n,isAnimating:g,isToolbarVisible:t,selectedTool:x,setIsToolbarVisible:s,setSelectedTool:f,tools:T},"tools")})})},(e,t)=>e.status===t.status&&e.isToolbarVisible===t.isToolbarVisible&&e.artifactKind===t.artifactKind);var T=s(8725),C=s(22710),z=s(78193);let S=e=>{let{handleVersionChange:t,documents:s,currentVersionIndex:n}=e,{artifact:a}=(0,z.ST)(),{width:o}=(0,c.lW)(),m=o<768,{mutate:p}=(0,d.iX)(),[x,f]=(0,l.useState)(!1);if(s)return(0,i.jsxs)(r.P.div,{className:"absolute flex flex-col gap-4 lg:flex-row bottom-0 bg-background p-4 w-full border-t z-50 justify-between",initial:{y:m?200:77},animate:{y:0},exit:{y:m?200:77},transition:{type:"spring",stiffness:140,damping:20},children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{children:"You are viewing a previous version"}),(0,i.jsx)("div",{className:"text-muted-foreground text-sm",children:"Restore this version to make edits"})]}),(0,i.jsxs)("div",{className:"flex flex-row gap-4",children:[(0,i.jsxs)(C.$,{disabled:x,onClick:async()=>{f(!0),p("/api/document?id=".concat(a.documentId),await fetch("/api/document?id=".concat(a.documentId,"&timestamp=").concat((0,u.Dn)(s,n)),{method:"DELETE"}),{optimisticData:s?[...s.filter(e=>(0,T.d)(new Date(e.createdAt),new Date((0,u.Dn)(s,n))))]:[]})},children:[(0,i.jsx)("div",{children:"Restore this version"}),x&&(0,i.jsx)("div",{className:"animate-spin",children:(0,i.jsx)(b.hz,{})})]}),(0,i.jsx)(C.$,{variant:"outline",onClick:()=>{t("latest")},children:"Back to latest version"})]})]})};var I=s(51874);let V=(0,l.memo)(function(e){let{artifact:t,handleVersionChange:s,currentVersionIndex:n,isCurrentVersion:a,mode:r,metadata:o,setMetadata:d}=e,[c,m]=(0,l.useState)(!1),p=eP.find(e=>e.kind===t.kind);if(!p)throw Error("Artifact definition not found!");let x={content:t.content,handleVersionChange:s,currentVersionIndex:n,isCurrentVersion:a,mode:r,metadata:o,setMetadata:d};return(0,i.jsx)("div",{className:"flex flex-row gap-1",children:p.actions.map(e=>(0,i.jsxs)(v.m_,{children:[(0,i.jsx)(v.k$,{asChild:!0,children:(0,i.jsxs)(C.$,{variant:"outline",className:(0,u.cn)("h-fit dark:hover:bg-zinc-700",{"p-2":!e.label,"py-1.5 px-2":e.label}),onClick:async()=>{m(!0);try{await Promise.resolve(e.onClick(x))}catch(e){I.o.error("Failed to execute action")}finally{m(!1)}},disabled:!!c||"streaming"===t.status||!!e.isDisabled&&e.isDisabled(x),children:[e.icon,e.label]})}),(0,i.jsx)(v.ZI,{children:e.description})]},e.description))})},(e,t)=>e.artifact.status===t.artifact.status&&e.currentVersionIndex===t.currentVersionIndex&&e.isCurrentVersion===t.isCurrentVersion&&e.artifact.content===t.artifact.content),E=(0,l.memo)(function(){let{setArtifact:e}=(0,z.ST)();return(0,i.jsx)(C.$,{"data-testid":"artifact-close-button",variant:"outline",className:"h-fit p-2 dark:hover:bg-zinc-700",onClick:()=>{e(e=>"streaming"===e.status?{...e,isVisible:!1}:{...z.ls,status:"idle"})},children:(0,i.jsx)(b.w0,{size:18})})},()=>!0);var M=s(61209),P=s(5579),A=s.n(P),R=s(92443);let D=(0,l.memo)(function(e){let{chatId:t,status:s,votes:n,messages:a,setMessages:l,regenerate:o,isReadonly:d}=e,{containerRef:c,endRef:u,onViewportEnter:m,onViewportLeave:p,hasSentMessage:x}=(0,R.o)({chatId:t,status:s});return(0,i.jsxs)("div",{ref:c,className:"flex flex-col gap-4 h-full items-center overflow-y-scroll px-4 pt-20",children:[a.map((e,r)=>(0,i.jsx)(M.e,{chatId:t,message:e,isLoading:"streaming"===s&&r===a.length-1,vote:n?n.find(t=>t.messageId===e.id):void 0,setMessages:l,regenerate:o,isReadonly:d,requiresScrollPadding:x&&r===a.length-1},e.id)),"submitted"===s&&a.length>0&&"user"===a[a.length-1].role&&(0,i.jsx)(M.q,{}),(0,i.jsx)(r.P.div,{ref:u,className:"shrink-0 min-w-[24px] min-h-[24px]",onViewportLeave:p,onViewportEnter:m})]})},function(e,t){return"streaming"===e.artifactStatus&&"streaming"===t.artifactStatus||e.status===t.status&&(!e.status||!t.status)&&e.messages.length===t.messages.length&&!!A()(e.votes,t.votes)});var _=s(92630);class L{constructor(e){this.kind=e.kind,this.description=e.description,this.content=e.content,this.actions=e.actions||[],this.toolbar=e.toolbar||[],this.initialize=e.initialize||(async()=>({})),this.onStreamPart=e.onStreamPart}}let U=new L({kind:"image",description:"Useful for image generation",onStreamPart:e=>{let{streamPart:t,setArtifact:s}=e;"data-imageDelta"===t.type&&s(e=>({...e,content:t.data,isVisible:!0,status:"streaming"}))},content:s(5318).n,actions:[{icon:(0,i.jsx)(b.ej,{size:18}),description:"View Previous version",onClick:e=>{let{handleVersionChange:t}=e;t("prev")},isDisabled:e=>{let{currentVersionIndex:t}=e;return 0===t}},{icon:(0,i.jsx)(b.Uf,{size:18}),description:"View Next version",onClick:e=>{let{handleVersionChange:t}=e;t("next")},isDisabled:e=>{let{isCurrentVersion:t}=e;return!!t}},{icon:(0,i.jsx)(b.Td,{size:18}),description:"Copy image to clipboard",onClick:e=>{let{content:t}=e,s=new Image;s.src="data:image/png;base64,".concat(t),s.onload=()=>{let e=document.createElement("canvas");e.width=s.width,e.height=s.height;let t=e.getContext("2d");null==t||t.drawImage(s,0,0),e.toBlob(e=>{e&&navigator.clipboard.write([new ClipboardItem({"image/png":e})])},"image/png")},I.o.success("Copied image to clipboard!")}}],toolbar:[]});var $=s(2656);function B(e){let{consoleOutputs:t,setConsoleOutputs:s}=e,[n,a]=(0,l.useState)(300),[r,o]=(0,l.useState)(!1),d=(0,l.useRef)(null),c=(0,z.HO)(e=>e.isVisible),m=(0,l.useCallback)(()=>{o(!0)},[]),p=(0,l.useCallback)(()=>{o(!1)},[]),x=(0,l.useCallback)(e=>{if(r){let t=window.innerHeight-e.clientY;t>=100&&t<=800&&a(t)}},[r]);return(0,l.useEffect)(()=>(window.addEventListener("mousemove",x),window.addEventListener("mouseup",p),()=>{window.removeEventListener("mousemove",x),window.removeEventListener("mouseup",p)}),[x,p]),(0,l.useEffect)(()=>{var e;null==(e=d.current)||e.scrollIntoView({behavior:"smooth"})},[t]),(0,l.useEffect)(()=>{c||s([])},[c,s]),t.length>0?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{className:"h-2 w-full fixed cursor-ns-resize z-50",onMouseDown:m,style:{bottom:n-4},role:"slider","aria-valuenow":100}),(0,i.jsxs)("div",{className:(0,u.cn)("fixed flex flex-col bottom-0 dark:bg-zinc-900 bg-zinc-50 w-full border-t z-40 overflow-y-scroll overflow-x-hidden dark:border-zinc-700 border-zinc-200",{"select-none":r}),style:{height:n},children:[(0,i.jsxs)("div",{className:"flex flex-row justify-between items-center w-full h-fit border-b dark:border-zinc-700 border-zinc-200 px-2 py-1 sticky top-0 z-50 bg-muted",children:[(0,i.jsxs)("div",{className:"text-sm pl-2 dark:text-zinc-50 text-zinc-800 flex flex-row gap-3 items-center",children:[(0,i.jsx)("div",{className:"text-muted-foreground",children:(0,i.jsx)(b.uO,{})}),(0,i.jsx)("div",{children:"Console"})]}),(0,i.jsx)(C.$,{variant:"ghost",className:"size-fit p-1 hover:dark:bg-zinc-700 hover:bg-zinc-200",size:"icon",onClick:()=>s([]),children:(0,i.jsx)(b.Fj,{})})]}),(0,i.jsxs)("div",{children:[t.map((e,t)=>(0,i.jsxs)("div",{className:"px-4 py-2 flex flex-row text-sm border-b dark:border-zinc-700 border-zinc-200 dark:bg-zinc-900 bg-zinc-50 font-mono",children:[(0,i.jsxs)("div",{className:(0,u.cn)("w-12 shrink-0",{"text-muted-foreground":["in_progress","loading_packages"].includes(e.status),"text-emerald-500":"completed"===e.status,"text-red-400":"failed"===e.status}),children:["[",t+1,"]"]}),["in_progress","loading_packages"].includes(e.status)?(0,i.jsxs)("div",{className:"flex flex-row gap-2",children:[(0,i.jsx)("div",{className:"animate-spin size-fit self-center mb-auto mt-0.5",children:(0,i.jsx)(b.hz,{})}),(0,i.jsx)("div",{className:"text-muted-foreground",children:"in_progress"===e.status?"Initializing...":"loading_packages"===e.status?e.contents.map(e=>"text"===e.type?e.value:null):null})]}):(0,i.jsx)("div",{className:"dark:text-zinc-50 text-zinc-900 w-full flex flex-col gap-2 overflow-x-scroll",children:e.contents.map((t,s)=>"image"===t.type?(0,i.jsx)("picture",{children:(0,i.jsx)("img",{src:t.value,alt:"output",className:"rounded-md max-w-screen-toast-mobile w-full"})},"".concat(e.id,"-").concat(s)):(0,i.jsx)("div",{className:"whitespace-pre-line break-words w-full",children:t.value},"".concat(e.id,"-").concat(s)))})]},e.id)),(0,i.jsx)("div",{ref:d})]})]})]}):null}let O={matplotlib:"\n    import io\n    import base64\n    from matplotlib import pyplot as plt\n\n    # Clear any existing plots\n    plt.clf()\n    plt.close('all')\n\n    # Switch to agg backend\n    plt.switch_backend('agg')\n\n    def setup_matplotlib_output():\n        def custom_show():\n            if plt.gcf().get_size_inches().prod() * plt.gcf().dpi ** 2 > 25_000_000:\n                print(\"Warning: Plot size too large, reducing quality\")\n                plt.gcf().set_dpi(100)\n\n            png_buf = io.BytesIO()\n            plt.savefig(png_buf, format='png')\n            png_buf.seek(0)\n            png_base64 = base64.b64encode(png_buf.read()).decode('utf-8')\n            print(f'data:image/png;base64,{png_base64}')\n            png_buf.close()\n\n            plt.clf()\n            plt.close('all')\n\n        plt.show = custom_show\n  ",basic:"\n    # Basic output capture setup\n  "},F=new L({kind:"code",description:"Useful for code generation; Code execution is only available for python code.",initialize:async e=>{let{setMetadata:t}=e;t({outputs:[]})},onStreamPart:e=>{let{streamPart:t,setArtifact:s}=e;"data-codeDelta"===t.type&&s(e=>({...e,content:t.data,isVisible:"streaming"===e.status&&e.content.length>300&&e.content.length<310||e.isVisible,status:"streaming"}))},content:e=>{let{metadata:t,setMetadata:s,...n}=e;return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{className:"px-1",children:(0,i.jsx)($.B,{...n})}),(null==t?void 0:t.outputs)&&(0,i.jsx)(B,{consoleOutputs:t.outputs,setConsoleOutputs:()=>{s({...t,outputs:[]})}})]})},actions:[{icon:(0,i.jsx)(b.ud,{size:18}),label:"Run",description:"Execute code",onClick:async e=>{let{content:t,setMetadata:s}=e,i=(0,u.lk)(),n=[];s(e=>({...e,outputs:[...e.outputs,{id:i,contents:[],status:"in_progress"}]}));try{let e=await globalThis.loadPyodide({indexURL:"https://cdn.jsdelivr.net/pyodide/v0.23.4/full/"});for(let a of(e.setStdout({batched:e=>{n.push({type:e.startsWith("data:image/png;base64")?"image":"text",value:e})}}),await e.loadPackagesFromImports(t,{messageCallback:e=>{s(t=>({...t,outputs:[...t.outputs.filter(e=>e.id!==i),{id:i,contents:[{type:"text",value:e}],status:"loading_packages"}]}))}}),function(e){let t=["basic"];return(e.includes("matplotlib")||e.includes("plt."))&&t.push("matplotlib"),t}(t)))O[a]&&(await e.runPythonAsync(O[a]),"matplotlib"===a&&await e.runPythonAsync("setup_matplotlib_output()"));await e.runPythonAsync(t),s(e=>({...e,outputs:[...e.outputs.filter(e=>e.id!==i),{id:i,contents:n,status:"completed"}]}))}catch(e){s(t=>({...t,outputs:[...t.outputs.filter(e=>e.id!==i),{id:i,contents:[{type:"text",value:e.message}],status:"failed"}]}))}}},{icon:(0,i.jsx)(b.ej,{size:18}),description:"View Previous version",onClick:e=>{let{handleVersionChange:t}=e;t("prev")},isDisabled:e=>{let{currentVersionIndex:t}=e;return 0===t}},{icon:(0,i.jsx)(b.Uf,{size:18}),description:"View Next version",onClick:e=>{let{handleVersionChange:t}=e;t("next")},isDisabled:e=>{let{isCurrentVersion:t}=e;return!!t}},{icon:(0,i.jsx)(b.Td,{size:18}),description:"Copy code to clipboard",onClick:e=>{let{content:t}=e;navigator.clipboard.writeText(t),I.o.success("Copied to clipboard!")}}],toolbar:[{icon:(0,i.jsx)(b.b1,{}),description:"Add comments",onClick:e=>{let{sendMessage:t}=e;t({role:"user",parts:[{type:"text",text:"Add comments to the code snippet for understanding"}]})}},{icon:(0,i.jsx)(b.Dk,{}),description:"Add logs",onClick:e=>{let{sendMessage:t}=e;t({role:"user",parts:[{type:"text",text:"Add logs to the code snippet for debugging"}]})}}]});var H=s(57052),W=s(18903);let q=new L({kind:"sheet",description:"Useful for working with spreadsheets",initialize:async()=>{},onStreamPart:e=>{let{setArtifact:t,streamPart:s}=e;"data-sheetDelta"===s.type&&t(e=>({...e,content:s.data,isVisible:!0,status:"streaming"}))},content:e=>{let{content:t,currentVersionIndex:s,isCurrentVersion:n,onSaveContent:a,status:r}=e;return(0,i.jsx)(H.y,{content:t,currentVersionIndex:s,isCurrentVersion:n,saveContent:a,status:r})},actions:[{icon:(0,i.jsx)(b.ej,{size:18}),description:"View Previous version",onClick:e=>{let{handleVersionChange:t}=e;t("prev")},isDisabled:e=>{let{currentVersionIndex:t}=e;return 0===t}},{icon:(0,i.jsx)(b.Uf,{size:18}),description:"View Next version",onClick:e=>{let{handleVersionChange:t}=e;t("next")},isDisabled:e=>{let{isCurrentVersion:t}=e;return!!t}},{icon:(0,i.jsx)(b.Td,{}),description:"Copy as .csv",onClick:e=>{let{content:t}=e,s=(0,W.parse)(t,{skipEmptyLines:!0}).data.filter(e=>e.some(e=>""!==e.trim())),i=(0,W.unparse)(s);navigator.clipboard.writeText(i),I.o.success("Copied csv to clipboard!")}}],toolbar:[{description:"Format and clean data",icon:(0,i.jsx)(b.BZ,{}),onClick:e=>{let{sendMessage:t}=e;t({role:"user",parts:[{type:"text",text:"Can you please format and clean the data?"}]})}},{description:"Analyze and visualize data",icon:(0,i.jsx)(b.gT,{}),onClick:e=>{let{sendMessage:t}=e;t({role:"user",parts:[{type:"text",text:"Can you please analyze and visualize the data by creating a new code artifact in python?"}]})}}]});var Z=s(94301),K=s(8622),Q=s(68023),J=s(73763),G=s(40082),X=s(78660),Y=s(73117),ee=s(41627),et=s(41973);let es={Unchanged:0,Deleted:-1,Inserted:1},ei=(e,t,s)=>{eu(t,s);let i=[],n=[],a=ex(t),r=ex(s),l=a.length,o=r.length,d=Math.min(l,o),c=0,u=0;for(;c<d;c++){let e=a[c];if(!ep(e,r[c]))break;i.push(...em(e))}for(;u+c+1<d;u++){let e=a[l-u-1];if(!ep(e,r[o-u-1]))break;n.unshift(...em(e))}let m=a.slice(c,l-u),p=r.slice(c,o-u);if(m.length&&p.length){let t=en(e,m,p).sort((e,t)=>t.count-e.count)[0];if(t){let{oldStartIndex:s,newStartIndex:a,oldEndIndex:r,newEndIndex:l}=t,o=m.slice(0,s),d=p.slice(0,a);i.push(...er(e,o,d)),i.push(...m.slice(s,r));let c=m.slice(r),u=p.slice(l);n.unshift(...er(e,c,u))}else i.push(...er(e,m,p))}else i.push(...er(e,m,p));return ew(t,[...i,...n])},en=(e,t,s)=>{let i=[];for(let e=0;e<t.length;e++){let n=ea(s,t[e]);if(-1!==n){let a=e+1,r=n+1;for(;a<t.length&&r<s.length;a++,r++){let e=t[a];if(!ep(s[r],e))break}i.push({oldStartIndex:e,newStartIndex:n,oldEndIndex:a,newEndIndex:r,count:r-n})}}return i},ea=function(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;for(let i=s;i<e.length;i++)if(ep(e[i],t))return i;return -1},er=(e,t,s)=>{let i=[],n=[],a=t.length,r=s.length,l=0,o=0;for(;a-l-o>0&&r-l-o>0;){let d=t[l],c=s[l],u=t[a-o-1],m=s[r-o-1],p=!ej(d)&&ey(d,c),x=!ej(u)&&ey(u,m);if(Array.isArray(d)&&Array.isArray(c)){i.push(...el(e,d,c)),l+=1;continue}p&&x&&(ec(d,c)<ec(u,m)?p=!1:x=!1),p?(i.push(ei(e,d,c)),l+=1):x?(n.unshift(ei(e,u,m)),o+=1):(i.push(ek(e,d,es.Deleted)),i.push(ek(e,c,es.Inserted)),l+=1)}let d=a-l-o,c=r-l-o;return d&&i.push(...t.slice(l,l+d).flat().map(t=>ek(e,t,es.Deleted))),c&&n.unshift(...s.slice(l,l+c).flat().map(t=>ek(e,t,es.Inserted))),[...i,...n]},el=(e,t,s)=>{let i=new et.diff_match_patch,n=t.map(e=>eb(e)).join(""),a=s.map(e=>eb(e)).join(""),{chars1:r,chars2:l,lineArray:o}=ed(eo(n),eo(a)),d=i.diff_main(r,l,!1);return(d=d.map(e=>{let[t,s]=e;return[t,s.split("").map(e=>o[e.charCodeAt(0)])]})).flatMap(t=>{let[s,i]=t;return i.map(t=>eT(e,t,s!==es.Unchanged?[eN(e,s)]:[]))})},eo=e=>e.match(/[^.!?]+[.!?]*\s*/g)||[],ed=(e,t)=>{let s=[],i={},n=0;return{chars1:e.map(e=>(e in i||(i[e]=n,s[n]=e,n++),String.fromCharCode(i[e]))).join(""),chars2:t.map(e=>(e in i||(i[e]=n,s[n]=e,n++),String.fromCharCode(i[e]))).join(""),lineArray:s}},ec=(e,t)=>0,eu=(e,t)=>{if(ef(e,"type")!==ef(t,"type"))throw Error("node type not equal: ".concat(e.type," !== ").concat(t.type))},em=e=>Array.isArray(e)?e:[e],ep=(e,t)=>{let s=Array.isArray(e);if(s!==Array.isArray(t))return!1;if(s)return e.length===t.length&&e.every((e,s)=>ep(e,t[s]));if(ef(e,"type")!==ef(t,"type"))return!1;if(ej(e)&&ef(e,"text")!==ef(t,"text"))return!1;let i=eg(e),n=eg(t);for(let e of[...new Set([...Object.keys(i),...Object.keys(n)])])if(i[e]!==n[e])return!1;let a=eh(e),r=eh(t);if(a.length!==r.length)return!1;for(let e=0;e<a.length;e++)if(!ep(a[e],r[e]))return!1;let l=ev(e),o=ev(t);if(l.length!==o.length)return!1;for(let e=0;e<l.length;e++)if(!ep(l[e],o[e]))return!1;return!0},ex=e=>{var t;let s=null!=(t=ev(e))?t:[],i=[];for(let e=0;e<s.length;e++){let t=s[e];if(ej(t)){let t=[];for(let i=s[e];e<s.length&&ej(i);i=s[++e])t.push(i);e--,i.push(t)}else i.push(t)}return i},ef=(e,t)=>{if("type"===t){var s;return null==(s=e.type)?void 0:s.name}return e[t]},eg=e=>e.attrs?e.attrs:{},eh=e=>{var t;return null!=(t=e.marks)?t:[]},ev=e=>{var t,s;return null!=(s=null==(t=e.content)?void 0:t.content)?s:[]},eb=e=>e.text,ej=e=>{var t;return(null==(t=e.type)?void 0:t.name)==="text"},ey=(e,t)=>{var s,i;return(null==(s=e.type)?void 0:s.name)===(null==(i=t.type)?void 0:i.name)||Array.isArray(e)&&Array.isArray(t)},ew=(e,t)=>{if(!e.type)throw Error("oldNode.type is undefined");return new K.bP(e.type,e.attrs,K.FK.fromArray(t),e.marks)},ek=(e,t,s)=>(function e(t,s){let i=t.copy(K.FK.from(t.content.content.map(t=>e(t,s)).filter(e=>e)));return s(i)||i})(t,t=>ej(t)?eT(e,eb(t),[...t.marks||[],eN(e,s)]):t),eN=(e,t)=>{if(t===es.Inserted||t===es.Deleted)return e.mark("diffMark",{type:t});throw Error("type is not valid")},eT=function(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return e.text(t,s)},eC=(e,t,s)=>{let i=K.bP.fromJSON(e,t),n=K.bP.fromJSON(e,s);return ei(e,i,n)},ez=new K.Sj({nodes:(0,J.ZW)(Q.wQ.spec.nodes,"paragraph block*","block"),marks:Z.A.from({...Q.wQ.spec.marks.toObject(),diffMark:{attrs:{type:{default:""}},toDOM(e){let t="";switch(e.attrs.type){case es.Inserted:t="bg-green-100 text-green-700 dark:bg-green-500/70 dark:text-green-300";break;case es.Deleted:t="bg-red-100 line-through text-red-600 dark:bg-red-500/70 dark:text-red-300";break;default:t=""}return["span",{class:t},0]}}})}),eS=e=>{let{oldContent:t,newContent:s}=e,n=(0,l.useRef)(null),a=(0,l.useRef)(null);return(0,l.useEffect)(()=>{if(n.current&&!a.current){let e=K.S4.fromSchema(ez),r=(0,Y.F0)((0,i.jsx)(ee.oz,{children:t})),l=(0,Y.F0)((0,i.jsx)(ee.oz,{children:s})),o=document.createElement("div");o.innerHTML=r;let d=document.createElement("div");d.innerHTML=l;let c=e.parse(o),u=e.parse(d),m=eC(ez,c.toJSON(),u.toJSON()),p=G.$t.create({doc:m,plugins:[]});a.current=new X.Lz(n.current,{state:p,editable:()=>!1})}return()=>{a.current&&(a.current.destroy(),a.current=null)}},[t,s]),(0,i.jsx)("div",{className:"diff-editor",ref:n})};var eI=s(43562),eV=s(80349),eE=s(25316);let eM=(0,eE.createServerReference)("40721b01082ba2a7f2716b984edc803a3f7f203933",eE.callServer,void 0,eE.findSourceMapURL,"getSuggestions"),eP=[new L({kind:"text",description:"Useful for text content, like drafting essays and emails.",initialize:async e=>{let{documentId:t,setMetadata:s}=e;s({suggestions:await eM({documentId:t})})},onStreamPart:e=>{let{streamPart:t,setMetadata:s,setArtifact:i}=e;"data-suggestion"===t.type&&s(e=>({suggestions:[...e.suggestions,t.data]})),"data-textDelta"===t.type&&i(e=>({...e,content:e.content+t.data,isVisible:"streaming"===e.status&&e.content.length>400&&e.content.length<450||e.isVisible,status:"streaming"}))},content:e=>{let{mode:t,status:s,content:n,isCurrentVersion:a,currentVersionIndex:r,onSaveContent:l,getDocumentContentById:o,isLoading:d,metadata:c}=e;if(d)return(0,i.jsx)(eI.P,{artifactKind:"text"});if("diff"===t){let e=o(r-1),t=o(r);return(0,i.jsx)(eS,{oldContent:e,newContent:t})}return(0,i.jsx)(i.Fragment,{children:(0,i.jsxs)("div",{className:"flex flex-row py-8 md:p-20 px-4",children:[(0,i.jsx)(eV.K,{content:n,suggestions:c?c.suggestions:[],isCurrentVersion:a,currentVersionIndex:r,status:s,onSaveContent:l}),(null==c?void 0:c.suggestions)&&c.suggestions.length>0?(0,i.jsx)("div",{className:"md:hidden h-dvh w-12 shrink-0"}):null]})})},actions:[{icon:(0,i.jsx)(b.Qe,{size:18}),description:"View changes",onClick:e=>{let{handleVersionChange:t}=e;t("toggle")},isDisabled:e=>{let{currentVersionIndex:t,setMetadata:s}=e;return 0===t}},{icon:(0,i.jsx)(b.ej,{size:18}),description:"View Previous version",onClick:e=>{let{handleVersionChange:t}=e;t("prev")},isDisabled:e=>{let{currentVersionIndex:t}=e;return 0===t}},{icon:(0,i.jsx)(b.Uf,{size:18}),description:"View Next version",onClick:e=>{let{handleVersionChange:t}=e;t("next")},isDisabled:e=>{let{isCurrentVersion:t}=e;return!!t}},{icon:(0,i.jsx)(b.Td,{size:18}),description:"Copy to clipboard",onClick:e=>{let{content:t}=e;navigator.clipboard.writeText(t),I.o.success("Copied to clipboard!")}}],toolbar:[{icon:(0,i.jsx)(b.rZ,{}),description:"Add final polish",onClick:e=>{let{sendMessage:t}=e;t({role:"user",parts:[{type:"text",text:"Please add final polish and check for grammar, add section titles for better structure, and ensure everything reads smoothly."}]})}},{icon:(0,i.jsx)(b.b1,{}),description:"Request suggestions",onClick:e=>{let{sendMessage:t}=e;t({role:"user",parts:[{type:"text",text:"Please add suggestions you have that could improve the writing."}]})}}]}),F,U,q],eA=(0,l.memo)(function(e){let{chatId:t,input:s,setInput:p,status:x,stop:f,attachments:g,setAttachments:h,sendMessage:v,messages:b,setMessages:j,regenerate:y,votes:w,isReadonly:k,selectedVisibilityType:T}=e,{artifact:C,setArtifact:I,metadata:M,setMetadata:P}=(0,z.ST)(),{data:A,isLoading:R,mutate:L}=(0,o.default)("init"!==C.documentId&&"streaming"!==C.status?"/api/document?id=".concat(C.documentId):null,u.GO),[U,$]=(0,l.useState)("edit"),[B,O]=(0,l.useState)(null),[F,H]=(0,l.useState)(-1),{open:W}=(0,_.cL)();(0,l.useEffect)(()=>{if(A&&A.length>0){let e=A.at(-1);e&&(O(e),H(A.length-1),I(t=>{var s;return{...t,content:null!=(s=e.content)?s:""}}))}},[A,I]),(0,l.useEffect)(()=>{L()},[C.status,L]);let{mutate:q}=(0,d.iX)(),[Z,K]=(0,l.useState)(!1),Q=(0,l.useCallback)(e=>{C&&q("/api/document?id=".concat(C.documentId),async t=>{if(!t)return;let s=t.at(-1);return s&&s.content?s.content!==e?(await fetch("/api/document?id=".concat(C.documentId),{method:"POST",body:JSON.stringify({title:C.title,content:e,kind:C.kind})}),K(!1),[...t,{...s,content:e,createdAt:new Date}]):t:(K(!1),t)},{revalidate:!1})},[C,q]),J=(0,c._6)(Q,2e3),G=(0,l.useCallback)((e,t)=>{B&&e!==B.content&&(K(!0),t?J(e):Q(e))},[B,J,Q]);function X(e){var t;return A&&A[e]&&null!=(t=A[e].content)?t:""}let Y=e=>{A&&("latest"===e&&(H(A.length-1),$("edit")),"toggle"===e&&$(e=>"edit"===e?"diff":"edit"),"prev"===e?F>0&&H(e=>e-1):"next"===e&&F<A.length-1&&H(e=>e+1))},[ee,et]=(0,l.useState)(!1),es=!A||!(A.length>0)||F===A.length-1,{width:ei,height:en}=(0,c.lW)(),ea=!!ei&&ei<768,er=eP.find(e=>e.kind===C.kind);if(!er)throw Error("Artifact definition not found!");return(0,l.useEffect)(()=>{"init"!==C.documentId&&er.initialize&&er.initialize({documentId:C.documentId,setMetadata:P})},[C.documentId,er,P]),(0,i.jsx)(a.N,{children:C.isVisible&&(0,i.jsxs)(r.P.div,{"data-testid":"artifact",className:"flex flex-row h-dvh w-dvw fixed top-0 left-0 z-50 bg-transparent",initial:{opacity:1},animate:{opacity:1},exit:{opacity:0,transition:{delay:.4}},children:[!ea&&(0,i.jsx)(r.P.div,{className:"fixed bg-background h-dvh",initial:{width:W?ei-256:ei,right:0},animate:{width:ei,right:0},exit:{width:W?ei-256:ei,right:0}}),!ea&&(0,i.jsxs)(r.P.div,{className:"relative w-[400px] bg-muted dark:bg-background h-dvh shrink-0",initial:{opacity:0,x:10,scale:1},animate:{opacity:1,x:0,scale:1,transition:{delay:.2,type:"spring",stiffness:200,damping:30}},exit:{opacity:0,x:0,scale:1,transition:{duration:0}},children:[(0,i.jsx)(a.N,{children:!es&&(0,i.jsx)(r.P.div,{className:"left-0 absolute h-dvh w-[400px] top-0 bg-zinc-900/50 z-50",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0}})}),(0,i.jsxs)("div",{className:"flex flex-col h-full justify-between items-center",children:[(0,i.jsx)(D,{chatId:t,status:x,votes:w,messages:b,setMessages:j,regenerate:y,isReadonly:k,artifactStatus:C.status}),(0,i.jsx)("form",{className:"flex flex-row gap-2 relative items-end w-full px-4 pb-4",children:(0,i.jsx)(m.Z,{chatId:t,input:s,setInput:p,status:x,stop:f,attachments:g,setAttachments:h,messages:b,sendMessage:v,className:"bg-background dark:bg-muted",setMessages:j,selectedVisibilityType:T})})]})]}),(0,i.jsxs)(r.P.div,{className:"fixed dark:bg-muted bg-background h-dvh flex flex-col overflow-y-scroll md:border-l dark:border-zinc-700 border-zinc-200",initial:{opacity:1,x:C.boundingBox.left,y:C.boundingBox.top,height:C.boundingBox.height,width:C.boundingBox.width,borderRadius:50},animate:ea?{opacity:1,x:0,y:0,height:en,width:ei||"calc(100dvw)",borderRadius:0,transition:{delay:0,type:"spring",stiffness:200,damping:30,duration:5e3}}:{opacity:1,x:400,y:0,height:en,width:ei?ei-400:"calc(100dvw-400px)",borderRadius:0,transition:{delay:0,type:"spring",stiffness:200,damping:30,duration:5e3}},exit:{opacity:0,scale:.5,transition:{delay:.1,type:"spring",stiffness:600,damping:30}},children:[(0,i.jsxs)("div",{className:"p-2 flex flex-row justify-between items-start",children:[(0,i.jsxs)("div",{className:"flex flex-row gap-4 items-start",children:[(0,i.jsx)(E,{}),(0,i.jsxs)("div",{className:"flex flex-col",children:[(0,i.jsx)("div",{className:"font-medium",children:C.title}),Z?(0,i.jsx)("div",{className:"text-sm text-muted-foreground",children:"Saving changes..."}):B?(0,i.jsx)("div",{className:"text-sm text-muted-foreground",children:"Updated ".concat((0,n.B)(new Date(B.createdAt),new Date,{addSuffix:!0}))}):(0,i.jsx)("div",{className:"w-32 h-3 mt-2 bg-muted-foreground/20 rounded-md animate-pulse"})]})]}),(0,i.jsx)(V,{artifact:C,currentVersionIndex:F,handleVersionChange:Y,isCurrentVersion:es,mode:U,metadata:M,setMetadata:P})]}),(0,i.jsxs)("div",{className:"dark:bg-muted bg-background h-full overflow-y-scroll !max-w-full items-center",children:[(0,i.jsx)(er.content,{title:C.title,content:es?C.content:X(F),mode:U,status:C.status,currentVersionIndex:F,suggestions:[],onSaveContent:G,isInline:!1,isCurrentVersion:es,getDocumentContentById:X,isLoading:R&&!C.content,metadata:M,setMetadata:P}),(0,i.jsx)(a.N,{children:es&&(0,i.jsx)(N,{isToolbarVisible:ee,setIsToolbarVisible:et,sendMessage:v,status:x,stop:f,setMessages:j,artifactKind:C.kind})})]}),(0,i.jsx)(a.N,{children:!es&&(0,i.jsx)(S,{currentVersionIndex:F,documents:A,handleVersionChange:Y})})]})]})})},(e,t)=>e.status===t.status&&!!A()(e.votes,t.votes)&&e.input===t.input&&!!A()(e.messages,t.messages.length)&&e.selectedVisibilityType===t.selectedVisibilityType)},98870:(e,t,s)=>{"use strict";s.d(t,{Z:()=>w});var i=s(44995),n=s(77785),a=s.n(n),r=s(41987),l=s(51874),o=s(60357);let{useUploadThing:d,uploadFiles:c}=(0,s(35950).KB)();var u=s(34109),m=s(99587),p=s(22710),x=s(15032),f=s(43992);let g=(0,r.memo)(function(e){let{chatId:t,sendMessage:s,selectedVisibilityType:n}=e;return(0,i.jsx)("div",{"data-testid":"suggested-actions",className:"grid sm:grid-cols-2 gap-2 w-full",children:[{title:"What are the advantages",label:"of using Next.js?",action:"What are the advantages of using Next.js?"},{title:"Write code to",label:"demonstrate djikstra's algorithm",action:"Write code to demonstrate djikstra's algorithm"},{title:"Help me write an essay",label:"about silicon valley",action:"Help me write an essay about silicon valley"},{title:"What is the weather",label:"in San Francisco?",action:"What is the weather in San Francisco?"}].map((e,n)=>(0,i.jsx)(f.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:20},transition:{delay:.05*n},className:n>1?"hidden sm:block":"block",children:(0,i.jsxs)(p.$,{variant:"ghost",onClick:async()=>{window.history.replaceState({},"","/chat/".concat(t)),s({role:"user",parts:[{type:"text",text:e.action}]})},className:"text-left border rounded-xl px-4 py-3.5 text-sm flex-1 gap-1 sm:flex-col w-full h-auto justify-start items-start",children:[(0,i.jsx)("span",{className:"font-medium",children:e.title}),(0,i.jsx)("span",{className:"text-muted-foreground",children:e.label})]})},"suggested-action-".concat(e.title,"-").concat(n)))})},(e,t)=>e.chatId===t.chatId&&e.selectedVisibilityType===t.selectedVisibilityType);var h=s(5579),v=s.n(h),b=s(49645),j=s(62501),y=s(76930);let w=(0,r.memo)(function(e){let{chatId:t,input:s,setInput:n,status:c,stop:u,attachments:h,setAttachments:v,messages:w,setMessages:C,sendMessage:z,className:S,selectedVisibilityType:I}=e,V=(0,r.useRef)(null),{width:E}=(0,o.lW)();(0,r.useEffect)(()=>{V.current&&M()},[]);let M=()=>{V.current&&(V.current.style.height="auto",V.current.style.height="".concat(V.current.scrollHeight+2,"px"))},P=()=>{V.current&&(V.current.style.height="auto",V.current.style.height="98px")},[A,R]=(0,o.Mj)("input","");(0,r.useEffect)(()=>{V.current&&(n(V.current.value||A||""),M())},[]),(0,r.useEffect)(()=>{R(s)},[s,R]);let D=(0,r.useRef)(null),[_,L]=(0,r.useState)([]),{startUpload:U,isUploading:$}=d("mediaUploader",{onClientUploadComplete:e=>{console.log("Upload completed:",e)},onUploadError:e=>{console.error("Upload error:",e),l.o.error("Upload failed: ".concat(e.message))},onUploadBegin:()=>{console.log("Upload begun")}}),B=(0,r.useCallback)(()=>{if(window.history.replaceState({},"","/chat/".concat(t)),z({role:"user",parts:[...h.map(e=>({type:"file",url:e.url,name:e.name,mediaType:e.contentType})),{type:"text",text:s}]}),v([]),R(""),P(),n(""),E&&E>768){var e;null==(e=V.current)||e.focus()}},[s,n,h,z,v,R,E,t]),O=(0,r.useCallback)(async e=>{try{let t=await U([e]);if(t&&t.length>0){let s=t[0];return{url:s.url,name:s.name||e.name,contentType:e.type}}throw Error("Upload failed")}catch(e){throw console.error("Upload error:",e),l.o.error("Failed to upload file, please try again!"),e}},[U]),F=(0,r.useCallback)(async e=>{let t=Array.from(e.target.files||[]);L(t.map(e=>e.name));try{let e=t.map(e=>O(e)),s=(await Promise.all(e)).filter(e=>void 0!==e);v(e=>[...e,...s])}catch(e){console.error("Error uploading files!",e)}finally{L([])}},[v,O]),{isAtBottom:H,scrollToBottom:W}=(0,y.R)();return(0,r.useEffect)(()=>{"submitted"===c&&W()},[c,W]),(0,i.jsxs)("div",{className:"relative w-full flex flex-col gap-4",children:[(0,i.jsx)(b.N,{children:!H&&(0,i.jsx)(f.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},exit:{opacity:0,y:10},transition:{type:"spring",stiffness:300,damping:20},className:"absolute left-1/2 bottom-28 -translate-x-1/2 z-50",children:(0,i.jsx)(p.$,{"data-testid":"scroll-to-bottom-button",className:"rounded-full",size:"icon",variant:"outline",onClick:e=>{e.preventDefault(),W()},children:(0,i.jsx)(j.A,{})})})}),0===w.length&&0===h.length&&0===_.length&&(0,i.jsx)(g,{sendMessage:z,chatId:t,selectedVisibilityType:I}),(0,i.jsx)("input",{type:"file",className:"fixed -top-4 -left-4 size-0.5 opacity-0 pointer-events-none",ref:D,multiple:!0,accept:"image/*,application/pdf",onChange:F,tabIndex:-1}),(h.length>0||_.length>0)&&(0,i.jsxs)("div",{"data-testid":"attachments-preview",className:"flex flex-row gap-2 overflow-x-scroll items-end",children:[h.map(e=>(0,i.jsx)(m.q,{attachment:e},e.url)),_.map(e=>(0,i.jsx)(m.q,{attachment:{url:"",name:e,contentType:""},isUploading:!0},e))]}),(0,i.jsx)(x.T,{"data-testid":"multimodal-input",ref:V,placeholder:"Send a message...",value:s,onChange:e=>{n(e.target.value),M()},className:a()("min-h-[24px] max-h-[calc(75dvh)] overflow-hidden resize-none rounded-2xl !text-base bg-muted pb-10 dark:border-zinc-700",S),rows:2,autoFocus:!0,onKeyDown:e=>{"Enter"!==e.key||e.shiftKey||e.nativeEvent.isComposing||(e.preventDefault(),"ready"!==c?l.o.error("Please wait for the model to finish its response!"):B())}}),(0,i.jsx)("div",{className:"absolute bottom-0 p-2 w-fit flex flex-row justify-start",children:(0,i.jsx)(k,{fileInputRef:D,status:c})}),(0,i.jsx)("div",{className:"absolute bottom-0 right-0 p-2 w-fit flex flex-row justify-end",children:"submitted"===c?(0,i.jsx)(N,{stop:u,setMessages:C}):(0,i.jsx)(T,{input:s,submitForm:B,uploadQueue:_})})]})},(e,t)=>e.input===t.input&&e.status===t.status&&!!v()(e.attachments,t.attachments)&&e.selectedVisibilityType===t.selectedVisibilityType),k=(0,r.memo)(function(e){let{fileInputRef:t,status:s}=e;return(0,i.jsx)(p.$,{"data-testid":"attachments-button",className:"rounded-md rounded-bl-lg p-[7px] h-fit dark:border-zinc-700 hover:dark:bg-zinc-900 hover:bg-zinc-200",onClick:e=>{var s;e.preventDefault(),null==(s=t.current)||s.click()},disabled:"ready"!==s,variant:"ghost",children:(0,i.jsx)(u.Au,{size:14})})}),N=(0,r.memo)(function(e){let{stop:t,setMessages:s}=e;return(0,i.jsx)(p.$,{"data-testid":"stop-button",className:"rounded-full p-1.5 h-fit border dark:border-zinc-600",onClick:e=>{e.preventDefault(),t(),s(e=>e)},children:(0,i.jsx)(u.wF,{size:14})})}),T=(0,r.memo)(function(e){let{submitForm:t,input:s,uploadQueue:n}=e;return(0,i.jsx)(p.$,{"data-testid":"send-button",className:"rounded-full p-1.5 h-fit border dark:border-zinc-600",onClick:e=>{e.preventDefault(),t()},disabled:0===s.length||n.length>0,children:(0,i.jsx)(u.Kp,{size:14})})},(e,t)=>e.uploadQueue.length===t.uploadQueue.length&&e.input===t.input)},99587:(e,t,s)=>{"use strict";s.d(t,{q:()=>a});var i=s(44995),n=s(34109);let a=e=>{let{attachment:t,isUploading:s=!1}=e,{name:a,url:r,contentType:l}=t;return(0,i.jsxs)("div",{"data-testid":"input-attachment-preview",className:"flex flex-col gap-2",children:[(0,i.jsxs)("div",{className:"w-20 h-16 aspect-video bg-muted rounded-md relative flex flex-col items-center justify-center",children:[l&&l.startsWith("image")?(0,i.jsx)("img",{src:r,alt:null!=a?a:"An image attachment",className:"rounded-md size-full object-cover"},r):(0,i.jsx)("div",{className:""}),s&&(0,i.jsx)("div",{"data-testid":"input-attachment-loader",className:"animate-spin absolute text-zinc-500",children:(0,i.jsx)(n.hz,{})})]}),(0,i.jsx)("div",{className:"text-xs text-zinc-500 max-w-16 truncate",children:a})]})}}}]);