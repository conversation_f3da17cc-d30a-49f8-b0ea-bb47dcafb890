exports.id=7928,exports.ids=[7928],exports.modules={995:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DISALLOWED_FORM_PROPS:function(){return r},checkFormActionUrl:function(){return i},createFormSubmitDestinationUrl:function(){return n},hasReactClientActionAttributes:function(){return l},hasUnsupportedSubmitterAttributes:function(){return c},isSupportedFormEncType:function(){return o},isSupportedFormMethod:function(){return a},isSupportedFormTarget:function(){return s}});let r=["method","encType","target"];function n(e,t){let r;try{let t=window.location.href;r=new URL(e,t)}catch(t){throw Object.defineProperty(Error('Cannot parse form action "'+e+'" as a URL',{cause:t}),"__NEXT_ERROR_CODE",{value:"E152",enumerable:!1,configurable:!0})}for(let[e,n]of(r.searchParams.size&&(r.search=""),new FormData(t)))"string"!=typeof n&&(n=n.name),r.searchParams.append(e,n);return r}function i(e,t){let r,n="action"===t?"an `action`":"a `formAction`";try{r=new URL(e,"http://n")}catch(t){console.error("<Form> received "+n+' that cannot be parsed as a URL: "'+e+'".');return}r.searchParams.size&&console.warn("<Form> received "+n+' that contains search params: "'+e+'". This is not supported, and they will be ignored. If you need to pass in additional search params, use an `<input type="hidden" />` instead.')}let o=e=>"application/x-www-form-urlencoded"===e,a=e=>"get"===e,s=e=>"_self"===e;function c(e){let t=e.getAttribute("formEncType");if(null!==t&&!o(t))return!0;let r=e.getAttribute("formMethod");if(null!==r&&!a(r))return!0;let n=e.getAttribute("formTarget");return!(null===n||s(n))}function l(e){let t=e.getAttribute("formAction");return t&&/\s*javascript:/i.test(t)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4047:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return d}});let n=r(13486),i=r(60159),o=r(38674),a=r(76181),s=r(55551),c=r(14985),l=r(995),u=r(97317);function d(e){let{replace:t,scroll:r,prefetch:d,ref:p,...f}=e,h=(0,i.useContext)(s.AppRouterContext),m=f.action,y="string"==typeof m;for(let e of l.DISALLOWED_FORM_PROPS)e in f&&delete f[e];let w=!!h&&y&&null===(!1===d||null===d?d:null),b=(0,i.useCallback)(e=>(w&&null!==h&&(0,u.mountFormInstance)(e,m,h,c.PrefetchKind.AUTO),()=>{(0,u.unmountPrefetchableInstance)(e)}),[w,m,h]),g=(0,a.useMergedRef)(b,null!=p?p:null);if(!y)return(0,n.jsx)("form",{...f,ref:g});let _=(0,o.addBasePath)(m);return(0,n.jsx)("form",{...f,ref:g,action:_,onSubmit:e=>(function(e,t){let{actionHref:r,onSubmit:n,replace:i,scroll:o,router:a}=t;if("function"==typeof n&&(n(e),e.defaultPrevented)||!a)return;let s=e.currentTarget,c=e.nativeEvent.submitter,u=r;if(c){if((0,l.hasUnsupportedSubmitterAttributes)(c)||(0,l.hasReactClientActionAttributes)(c))return;let e=c.getAttribute("formAction");null!==e&&(u=e)}let d=(0,l.createFormSubmitDestinationUrl)(u,s);e.preventDefault();let p=d.href;a[i?"replace":"push"](p,{scroll:o})})(e,{router:h,actionHref:_,replace:t,scroll:r,onSubmit:f.onSubmit})})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5587:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{PageSignatureError:function(){return r},RemovedPageError:function(){return n},RemovedUAError:function(){return i}});class r extends Error{constructor({page:e}){super(`The middleware "${e}" accepts an async API directly with the form:
  
  export function middleware(request, event) {
    return NextResponse.redirect('/new-location')
  }
  
  Read more: https://nextjs.org/docs/messages/middleware-new-signature
  `)}}class n extends Error{constructor(){super(`The request.page has been deprecated in favour of \`URLPattern\`.
  Read more: https://nextjs.org/docs/messages/middleware-request-page
  `)}}class i extends Error{constructor(){super(`The request.ua has been removed in favour of \`userAgent\` function.
  Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent
  `)}}},5631:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNextRouterError",{enumerable:!0,get:function(){return o}});let n=r(22859),i=r(31903);function o(e){return(0,i.isRedirectError)(e)||(0,n.isHTTPAccessFallbackError)(e)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9793:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizeLocalePath",{enumerable:!0,get:function(){return n}});let r=new WeakMap;function n(e,t){let n;if(!t)return{pathname:e};let i=r.get(t);i||(i=t.map(e=>e.toLowerCase()),r.set(t,i));let o=e.split("/",2);if(!o[1])return{pathname:e};let a=o[1].toLowerCase(),s=i.indexOf(a);return s<0?{pathname:e}:(n=t[s],{pathname:e=e.slice(n.length+1)||"/",detectedLocale:n})}},10136:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"connection",{enumerable:!0,get:function(){return l}});let n=r(29294),i=r(63033),o=r(14365),a=r(60577),s=r(53190),c=r(15963);function l(){let e=n.workAsyncStorage.getStore(),t=i.workUnitAsyncStorage.getStore();if(e){if(t&&"after"===t.phase&&!(0,c.isRequestAPICallableInsideAfter)())throw Object.defineProperty(Error(`Route ${e.route} used "connection" inside "after(...)". The \`connection()\` function is used to indicate the subsequent code must only run when there is an actual Request, but "after(...)" executes after the request, so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E186",enumerable:!1,configurable:!0});if(e.forceStatic)return Promise.resolve(void 0);if(t){if("cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "connection" inside "use cache". The \`connection()\` function is used to indicate the subsequent code must only run when there is an actual Request, but caches must be able to be produced before a Request so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E111",enumerable:!1,configurable:!0});else if("unstable-cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "connection" inside a function cached with "unstable_cache(...)". The \`connection()\` function is used to indicate the subsequent code must only run when there is an actual Request, but caches must be able to be produced before a Request so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E1",enumerable:!1,configurable:!0})}if(e.dynamicShouldError)throw Object.defineProperty(new a.StaticGenBailoutError(`Route ${e.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`connection\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E562",enumerable:!1,configurable:!0});if(t)if("prerender"===t.type)return(0,s.makeHangingPromise)(t.renderSignal,"`connection()`");else"prerender-ppr"===t.type?(0,o.postponeWithTracking)(e.route,"connection",t.dynamicTracking):"prerender-legacy"===t.type&&(0,o.throwToInterruptStaticGeneration)("connection",e,t);(0,o.trackDynamicDataInDynamicRender)(e,t)}return Promise.resolve(void 0)}},15992:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{INTERNALS:function(){return s},NextRequest:function(){return c}});let n=r(42660),i=r(85718),o=r(5587),a=r(4442),s=Symbol("internal request");class c extends Request{constructor(e,t={}){let r="string"!=typeof e&&"url"in e?e.url:String(e);(0,i.validateURL)(r),t.body&&"half"!==t.duplex&&(t.duplex="half"),e instanceof Request?super(e,t):super(r,t);let o=new n.NextURL(r,{headers:(0,i.toNodeOutgoingHttpHeaders)(this.headers),nextConfig:t.nextConfig});this[s]={cookies:new a.RequestCookies(this.headers),nextUrl:o,url:o.toString()}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,nextUrl:this.nextUrl,url:this.url,bodyUsed:this.bodyUsed,cache:this.cache,credentials:this.credentials,destination:this.destination,headers:Object.fromEntries(this.headers),integrity:this.integrity,keepalive:this.keepalive,method:this.method,mode:this.mode,redirect:this.redirect,referrer:this.referrer,referrerPolicy:this.referrerPolicy,signal:this.signal}}get cookies(){return this[s].cookies}get nextUrl(){return this[s].nextUrl}get page(){throw new o.RemovedPageError}get ua(){throw new o.RemovedUAError}get url(){return this[s].url}}},20616:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isBot:function(){return i},userAgent:function(){return a},userAgentFromString:function(){return o}});let n=function(e){return e&&e.__esModule?e:{default:e}}(r(94515));function i(e){return/Googlebot|Mediapartners-Google|AdsBot-Google|googleweblight|Storebot-Google|Google-PageRenderer|Google-InspectionTool|Bingbot|BingPreview|Slurp|DuckDuckBot|baiduspider|yandex|sogou|LinkedInBot|bitlybot|tumblr|vkShare|quora link preview|facebookexternalhit|facebookcatalog|Twitterbot|applebot|redditbot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|ia_archiver/i.test(e)}function o(e){return{...(0,n.default)(e),isBot:void 0!==e&&i(e)}}function a({headers:e}){return o(e.get("user-agent")||void 0)}},21606:(e,t)=>{"use strict";function r(e,t){let r;if((null==t?void 0:t.host)&&!Array.isArray(t.host))r=t.host.toString().split(":",1)[0];else{if(!e.hostname)return;r=e.hostname}return r.toLowerCase()}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getHostname",{enumerable:!0,get:function(){return r}})},22298:(e,t)=>{"use strict";function r(e,t,r){if(e)for(let o of(r&&(r=r.toLowerCase()),e)){var n,i;if(t===(null==(n=o.domain)?void 0:n.split(":",1)[0].toLowerCase())||r===o.defaultLocale.toLowerCase()||(null==(i=o.locales)?void 0:i.some(e=>e.toLowerCase()===r)))return o}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"detectDomainLocale",{enumerable:!0,get:function(){return r}})},22859:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HTTPAccessErrorStatus:function(){return r},HTTP_ERROR_FALLBACK_ERROR_CODE:function(){return i},getAccessFallbackErrorTypeByStatus:function(){return s},getAccessFallbackHTTPStatus:function(){return a},isHTTPAccessFallbackError:function(){return o}});let r={NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401},n=new Set(Object.values(r)),i="NEXT_HTTP_ERROR_FALLBACK";function o(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r]=e.digest.split(";");return t===i&&n.has(Number(r))}function a(e){return Number(e.digest.split(";")[1])}function s(e){switch(e){case 401:return"unauthorized";case 403:return"forbidden";case 404:return"not-found";default:return}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},29568:(e,t)=>{"use strict";function r(){throw Object.defineProperty(Error('ImageResponse moved from "next/server" to "next/og" since Next.js 14, please import from "next/og" instead'),"__NEXT_ERROR_CODE",{value:"E183",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ImageResponse",{enumerable:!0,get:function(){return r}})},31903:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{REDIRECT_ERROR_CODE:function(){return i},RedirectType:function(){return o},isRedirectError:function(){return a}});let n=r(49005),i="NEXT_REDIRECT";var o=function(e){return e.push="push",e.replace="replace",e}({});function a(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let t=e.digest.split(";"),[r,o]=t,a=t.slice(2,-2).join(";"),s=Number(t.at(-2));return r===i&&("replace"===o||"push"===o)&&"string"==typeof a&&!isNaN(s)&&s in n.RedirectStatusCode}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},37503:(e,t,r)=>{"use strict";let n,i,o,a,s,c,l;r.d(t,{Ay:()=>ar});var u=function(e,t,r,n,i){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!i)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!i:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?i.call(e,r):i?i.value=r:t.set(e,r),r},d=function(e,t,r,n){if("a"===r&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)};function p(e){let t=e?"__Secure-":"";return{sessionToken:{name:`${t}authjs.session-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},callbackUrl:{name:`${t}authjs.callback-url`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},csrfToken:{name:`${e?"__Host-":""}authjs.csrf-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},pkceCodeVerifier:{name:`${t}authjs.pkce.code_verifier`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}},state:{name:`${t}authjs.state`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}},nonce:{name:`${t}authjs.nonce`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},webauthnChallenge:{name:`${t}authjs.challenge`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}}}}class f{constructor(e,t,r){if(rw.add(this),rb.set(this,{}),rg.set(this,void 0),r_.set(this,void 0),u(this,r_,r,"f"),u(this,rg,e,"f"),!t)return;let{name:n}=e;for(let[e,r]of Object.entries(t))e.startsWith(n)&&r&&(d(this,rb,"f")[e]=r)}get value(){return Object.keys(d(this,rb,"f")).sort((e,t)=>parseInt(e.split(".").pop()||"0")-parseInt(t.split(".").pop()||"0")).map(e=>d(this,rb,"f")[e]).join("")}chunk(e,t){let r=d(this,rw,"m",rk).call(this);for(let n of d(this,rw,"m",rv).call(this,{name:d(this,rg,"f").name,value:e,options:{...d(this,rg,"f").options,...t}}))r[n.name]=n;return Object.values(r)}clean(){return Object.values(d(this,rw,"m",rk).call(this))}}rb=new WeakMap,rg=new WeakMap,r_=new WeakMap,rw=new WeakSet,rv=function(e){let t=Math.ceil(e.value.length/3936);if(1===t)return d(this,rb,"f")[e.name]=e.value,[e];let r=[];for(let n=0;n<t;n++){let t=`${e.name}.${n}`,i=e.value.substr(3936*n,3936);r.push({...e,name:t,value:i}),d(this,rb,"f")[t]=i}return d(this,r_,"f").debug("CHUNKING_SESSION_COOKIE",{message:"Session cookie exceeds allowed 4096 bytes.",emptyCookieSize:160,valueSize:e.value.length,chunks:r.map(e=>e.value.length+160)}),r},rk=function(){let e={};for(let t in d(this,rb,"f"))delete d(this,rb,"f")?.[t],e[t]={name:t,value:"",options:{...d(this,rg,"f").options,maxAge:0}};return e};class h extends Error{constructor(e,t){e instanceof Error?super(void 0,{cause:{err:e,...e.cause,...t}}):"string"==typeof e?(t instanceof Error&&(t={err:t,...t.cause}),super(e,t)):super(void 0,e),this.name=this.constructor.name,this.type=this.constructor.type??"AuthError",this.kind=this.constructor.kind??"error",Error.captureStackTrace?.(this,this.constructor);let r=`https://errors.authjs.dev#${this.type.toLowerCase()}`;this.message+=`${this.message?". ":""}Read more at ${r}`}}class m extends h{}m.kind="signIn";class y extends h{}y.type="AdapterError";class w extends h{}w.type="AccessDenied";class b extends h{}b.type="CallbackRouteError";class g extends h{}g.type="ErrorPageLoop";class _ extends h{}_.type="EventError";class v extends h{}v.type="InvalidCallbackUrl";class k extends m{constructor(){super(...arguments),this.code="credentials"}}k.type="CredentialsSignin";class E extends h{}E.type="InvalidEndpoints";class S extends h{}S.type="InvalidCheck";class A extends h{}A.type="JWTSessionError";class x extends h{}x.type="MissingAdapter";class P extends h{}P.type="MissingAdapterMethods";class T extends h{}T.type="MissingAuthorize";class R extends h{}R.type="MissingSecret";class O extends m{}O.type="OAuthAccountNotLinked";class C extends m{}C.type="OAuthCallbackError";class U extends h{}U.type="OAuthProfileParseError";class I extends h{}I.type="SessionTokenError";class j extends m{}j.type="OAuthSignInError";class H extends m{}H.type="EmailSignInError";class N extends h{}N.type="SignOutError";class L extends h{}L.type="UnknownAction";class $ extends h{}$.type="UnsupportedStrategy";class D extends h{}D.type="InvalidProvider";class W extends h{}W.type="UntrustedHost";class M extends h{}M.type="Verification";class K extends m{}K.type="MissingCSRF";let J=new Set(["CredentialsSignin","OAuthAccountNotLinked","OAuthCallbackError","AccessDenied","Verification","MissingCSRF","AccountNotLinked","WebAuthnVerificationError"]);class F extends h{}F.type="DuplicateConditionalUI";class B extends h{}B.type="MissingWebAuthnAutocomplete";class z extends h{}z.type="WebAuthnVerificationError";class q extends m{}q.type="AccountNotLinked";class G extends h{}G.type="ExperimentalFeatureNotEnabled";let V=!1;function X(e,t){try{return/^https?:/.test(new URL(e,e.startsWith("/")?t:void 0).protocol)}catch{return!1}}let Y=!1,Z=!1,Q=!1,ee=["createVerificationToken","useVerificationToken","getUserByEmail"],et=["createUser","getUser","getUserByEmail","getUserByAccount","updateUser","linkAccount","createSession","getSessionAndUser","updateSession","deleteSession"],er=["createUser","getUser","linkAccount","getAccount","getAuthenticator","createAuthenticator","listAuthenticatorsByUserId","updateAuthenticatorCounter"];var en=r(55511);let ei=(e,t,r,n,i)=>{let o=parseInt(e.substr(3),10)>>3||20,a=(0,en.createHmac)(e,r.byteLength?r:new Uint8Array(o)).update(t).digest(),s=Math.ceil(i/o),c=new Uint8Array(o*s+n.byteLength+1),l=0,u=0;for(let t=1;t<=s;t++)c.set(n,u),c[u+n.byteLength]=t,c.set((0,en.createHmac)(e,a).update(c.subarray(l,u+n.byteLength+1)).digest(),u),l=u,u+=o;return c.slice(0,i)};"function"!=typeof en.hkdf||process.versions.electron||(n=async(...e)=>new Promise((t,r)=>{en.hkdf(...e,(e,n)=>{e?r(e):t(new Uint8Array(n))})}));let eo=async(e,t,r,i,o)=>(n||ei)(e,t,r,i,o);function ea(e,t){if("string"==typeof e)return new TextEncoder().encode(e);if(!(e instanceof Uint8Array))throw TypeError(`"${t}"" must be an instance of Uint8Array or a string`);return e}async function es(e,t,r,n,i){return eo(function(e){switch(e){case"sha256":case"sha384":case"sha512":case"sha1":return e;default:throw TypeError('unsupported "digest" value')}}(e),function(e){let t=ea(e,"ikm");if(!t.byteLength)throw TypeError('"ikm" must be at least one byte in length');return t}(t),ea(r,"salt"),function(e){let t=ea(e,"info");if(t.byteLength>1024)throw TypeError('"info" must not contain more than 1024 bytes');return t}(n),function(e,t){if("number"!=typeof e||!Number.isInteger(e)||e<1)throw TypeError('"keylen" must be a positive integer');if(e>255*(parseInt(t.substr(3),10)>>3||20))throw TypeError('"keylen" too large');return e}(i,e))}var ec=r(77598);let el=(e,t)=>(0,ec.createHash)(e).update(t).digest();var eu=r(4573);let ed=new TextEncoder,ep=new TextDecoder;function ef(...e){let t=new Uint8Array(e.reduce((e,{length:t})=>e+t,0)),r=0;for(let n of e)t.set(n,r),r+=n.length;return t}function eh(e,t){return ef(ed.encode(e),new Uint8Array([0]),t)}function em(e,t,r){if(t<0||t>=0x100000000)throw RangeError(`value must be >= 0 and <= ${0x100000000-1}. Received ${t}`);e.set([t>>>24,t>>>16,t>>>8,255&t],r)}function ey(e){let t=new Uint8Array(4);return em(t,e),t}function ew(e){return ef(ey(e.length),e)}async function eb(e,t,r){let n=Math.ceil((t>>3)/32),i=new Uint8Array(32*n);for(let t=0;t<n;t++){let n=new Uint8Array(4+e.length+r.length);n.set(ey(t+1)),n.set(e,4),n.set(r,4+e.length),i.set(await el("sha256",n),32*t)}return i.slice(0,t>>3)}let eg=e=>eu.Buffer.from(e).toString("base64url"),e_=e=>new Uint8Array(eu.Buffer.from(function(e){let t=e;return t instanceof Uint8Array&&(t=ep.decode(t)),t}(e),"base64url"));class ev extends Error{static code="ERR_JOSE_GENERIC";code="ERR_JOSE_GENERIC";constructor(e,t){super(e,t),this.name=this.constructor.name,Error.captureStackTrace?.(this,this.constructor)}}class ek extends ev{static code="ERR_JWT_CLAIM_VALIDATION_FAILED";code="ERR_JWT_CLAIM_VALIDATION_FAILED";claim;reason;payload;constructor(e,t,r="unspecified",n="unspecified"){super(e,{cause:{claim:r,reason:n,payload:t}}),this.claim=r,this.reason=n,this.payload=t}}class eE extends ev{static code="ERR_JWT_EXPIRED";code="ERR_JWT_EXPIRED";claim;reason;payload;constructor(e,t,r="unspecified",n="unspecified"){super(e,{cause:{claim:r,reason:n,payload:t}}),this.claim=r,this.reason=n,this.payload=t}}class eS extends ev{static code="ERR_JOSE_ALG_NOT_ALLOWED";code="ERR_JOSE_ALG_NOT_ALLOWED"}class eA extends ev{static code="ERR_JOSE_NOT_SUPPORTED";code="ERR_JOSE_NOT_SUPPORTED"}class ex extends ev{static code="ERR_JWE_DECRYPTION_FAILED";code="ERR_JWE_DECRYPTION_FAILED";constructor(e="decryption operation failed",t){super(e,t)}}class eP extends ev{static code="ERR_JWE_INVALID";code="ERR_JWE_INVALID"}class eT extends ev{static code="ERR_JWT_INVALID";code="ERR_JWT_INVALID"}class eR extends ev{static code="ERR_JWK_INVALID";code="ERR_JWK_INVALID"}class eO extends ev{[Symbol.asyncIterator];static code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";constructor(e="multiple matching keys found in the JSON Web Key Set",t){super(e,t)}}function eC(e){if("object"!=typeof e||null===e||"[object Object]"!==Object.prototype.toString.call(e))return!1;if(null===Object.getPrototypeOf(e))return!0;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}let eU=(e,t)=>{if("string"!=typeof e||!e)throw new eR(`${t} missing or invalid`)};async function eI(e,t){let r;if(!eC(e))throw TypeError("JWK must be an object");if("sha256"!==(t??="sha256")&&"sha384"!==t&&"sha512"!==t)throw TypeError('digestAlgorithm must one of "sha256", "sha384", or "sha512"');switch(e.kty){case"EC":eU(e.crv,'"crv" (Curve) Parameter'),eU(e.x,'"x" (X Coordinate) Parameter'),eU(e.y,'"y" (Y Coordinate) Parameter'),r={crv:e.crv,kty:e.kty,x:e.x,y:e.y};break;case"OKP":eU(e.crv,'"crv" (Subtype of Key Pair) Parameter'),eU(e.x,'"x" (Public Key) Parameter'),r={crv:e.crv,kty:e.kty,x:e.x};break;case"RSA":eU(e.e,'"e" (Exponent) Parameter'),eU(e.n,'"n" (Modulus) Parameter'),r={e:e.e,kty:e.kty,n:e.n};break;case"oct":eU(e.k,'"k" (Key Value) Parameter'),r={k:e.k,kty:e.kty};break;default:throw new eA('"kty" (Key Type) Parameter missing or unsupported')}let n=ed.encode(JSON.stringify(r));return eg(await el(t,n))}let ej=Symbol();function eH(e){switch(e){case"A128GCM":case"A128GCMKW":case"A192GCM":case"A192GCMKW":case"A256GCM":case"A256GCMKW":return 96;case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return 128;default:throw new eA(`Unsupported JWE Algorithm: ${e}`)}}let eN=e=>(0,ec.randomFillSync)(new Uint8Array(eH(e)>>3)),eL=(e,t)=>{if(t.length<<3!==eH(e))throw new eP("Invalid Initialization Vector length")};var e$=r(57975);let eD=e=>e$.types.isKeyObject(e),eW=(e,t)=>{let r;switch(e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":r=parseInt(e.slice(-3),10);break;case"A128GCM":case"A192GCM":case"A256GCM":r=parseInt(e.slice(1,4),10);break;default:throw new eA(`Content Encryption Algorithm ${e} is not supported either by JOSE or your javascript runtime`)}if(t instanceof Uint8Array){let e=t.byteLength<<3;if(e!==r)throw new eP(`Invalid Content Encryption Key length. Expected ${r} bits, got ${e} bits`);return}if(eD(t)&&"secret"===t.type){let e=t.symmetricKeySize<<3;if(e!==r)throw new eP(`Invalid Content Encryption Key length. Expected ${r} bits, got ${e} bits`);return}throw TypeError("Invalid Content Encryption Key type")};function eM(e,t,r,n,i,o){let a=ef(e,t,r,function(e){let t=Math.floor(e/0x100000000),r=new Uint8Array(8);return em(r,t,0),em(r,e%0x100000000,4),r}(e.length<<3)),s=(0,ec.createHmac)(`sha${n}`,i);return s.update(a),s.digest().slice(0,o>>3)}let eK=ec.webcrypto,eJ=e=>e$.types.isCryptoKey(e);function eF(e,t="algorithm.name"){return TypeError(`CryptoKey does not support this operation, its ${t} must be ${e}`)}function eB(e,t){return e.name===t}function ez(e,t,...r){switch(t){case"A128GCM":case"A192GCM":case"A256GCM":{if(!eB(e.algorithm,"AES-GCM"))throw eF("AES-GCM");let r=parseInt(t.slice(1,4),10);if(e.algorithm.length!==r)throw eF(r,"algorithm.length");break}case"A128KW":case"A192KW":case"A256KW":{if(!eB(e.algorithm,"AES-KW"))throw eF("AES-KW");let r=parseInt(t.slice(1,4),10);if(e.algorithm.length!==r)throw eF(r,"algorithm.length");break}case"ECDH":switch(e.algorithm.name){case"ECDH":case"X25519":case"X448":break;default:throw eF("ECDH, X25519, or X448")}break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":if(!eB(e.algorithm,"PBKDF2"))throw eF("PBKDF2");break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":{if(!eB(e.algorithm,"RSA-OAEP"))throw eF("RSA-OAEP");let r=parseInt(t.slice(9),10)||1;if(parseInt(e.algorithm.hash.name.slice(4),10)!==r)throw eF(`SHA-${r}`,"algorithm.hash");break}default:throw TypeError("CryptoKey does not support this operation")}var n=e,i=r;if(i.length&&!i.some(e=>n.usages.includes(e))){let e="CryptoKey does not support this operation, its usages must include ";if(i.length>2){let t=i.pop();e+=`one of ${i.join(", ")}, or ${t}.`}else 2===i.length?e+=`one of ${i[0]} or ${i[1]}.`:e+=`${i[0]}.`;throw TypeError(e)}}function eq(e,t,...r){if((r=r.filter(Boolean)).length>2){let t=r.pop();e+=`one of type ${r.join(", ")}, or ${t}.`}else 2===r.length?e+=`one of type ${r[0]} or ${r[1]}.`:e+=`of type ${r[0]}.`;return null==t?e+=` Received ${t}`:"function"==typeof t&&t.name?e+=` Received function ${t.name}`:"object"==typeof t&&null!=t&&t.constructor?.name&&(e+=` Received an instance of ${t.constructor.name}`),e}let eG=(e,...t)=>eq("Key must be ",e,...t);function eV(e,t,...r){return eq(`Key for the ${e} algorithm must be `,t,...r)}let eX=e=>(i||=new Set((0,ec.getCiphers)())).has(e),eY=e=>eD(e)||eJ(e),eZ=["KeyObject"];(globalThis.CryptoKey||eK?.CryptoKey)&&eZ.push("CryptoKey");let eQ=(e,t,r,n,i)=>{let o;if(eJ(r))ez(r,e,"encrypt"),o=ec.KeyObject.from(r);else if(r instanceof Uint8Array||eD(r))o=r;else throw TypeError(eG(r,...eZ,"Uint8Array"));switch(eW(e,o),n?eL(e,n):n=eN(e),e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return function(e,t,r,n,i){let o=parseInt(e.slice(1,4),10);eD(r)&&(r=r.export());let a=r.subarray(o>>3),s=r.subarray(0,o>>3),c=`aes-${o}-cbc`;if(!eX(c))throw new eA(`alg ${e} is not supported by your javascript runtime`);let l=(0,ec.createCipheriv)(c,a,n),u=ef(l.update(t),l.final()),d=eM(i,n,u,parseInt(e.slice(-3),10),s,o);return{ciphertext:u,tag:d,iv:n}}(e,t,o,n,i);case"A128GCM":case"A192GCM":case"A256GCM":return function(e,t,r,n,i){let o=parseInt(e.slice(1,4),10),a=`aes-${o}-gcm`;if(!eX(a))throw new eA(`alg ${e} is not supported by your javascript runtime`);let s=(0,ec.createCipheriv)(a,r,n,{authTagLength:16});i.byteLength&&s.setAAD(i,{plaintextLength:t.length});let c=s.update(t);return s.final(),{ciphertext:c,tag:s.getAuthTag(),iv:n}}(e,t,o,n,i);default:throw new eA("Unsupported JWE Content Encryption Algorithm")}};function e0(e,t){if(e.symmetricKeySize<<3!==parseInt(t.slice(1,4),10))throw TypeError(`Invalid key size for alg: ${t}`)}function e1(e,t,r){if(eD(e))return e;if(e instanceof Uint8Array)return(0,ec.createSecretKey)(e);if(eJ(e))return ez(e,t,r),ec.KeyObject.from(e);throw TypeError(eG(e,...eZ,"Uint8Array"))}let e2=(e,t,r)=>{let n=parseInt(e.slice(1,4),10),i=`aes${n}-wrap`;if(!eX(i))throw new eA(`alg ${e} is not supported either by JOSE or your javascript runtime`);let o=e1(t,e,"wrapKey");e0(o,e);let a=(0,ec.createCipheriv)(i,o,eu.Buffer.alloc(8,166));return ef(a.update(r),a.final())},e5=(e,t,r)=>{let n=parseInt(e.slice(1,4),10),i=`aes${n}-wrap`;if(!eX(i))throw new eA(`alg ${e} is not supported either by JOSE or your javascript runtime`);let o=e1(t,e,"unwrapKey");e0(o,e);let a=(0,ec.createDecipheriv)(i,o,eu.Buffer.alloc(8,166));return ef(a.update(r),a.final())};function e3(e){return eC(e)&&"string"==typeof e.kty}new WeakMap;let e6=e=>{switch(e){case"prime256v1":return"P-256";case"secp384r1":return"P-384";case"secp521r1":return"P-521";case"secp256k1":return"secp256k1";default:throw new eA("Unsupported key curve for this operation")}},e4=(e,t)=>{let r;if(eJ(e))r=ec.KeyObject.from(e);else if(eD(e))r=e;else if(e3(e))return e.crv;else throw TypeError(eG(e,...eZ));if("secret"===r.type)throw TypeError('only "private" or "public" type keys can be used for this operation');switch(r.asymmetricKeyType){case"ed25519":case"ed448":return`Ed${r.asymmetricKeyType.slice(2)}`;case"x25519":case"x448":return`X${r.asymmetricKeyType.slice(1)}`;case"ec":{let e=r.asymmetricKeyDetails.namedCurve;if(t)return e;return e6(e)}default:throw TypeError("Invalid asymmetric key type for this operation")}},e8=(0,e$.promisify)(ec.generateKeyPair);async function e9(e,t,r,n,i=new Uint8Array(0),o=new Uint8Array(0)){let a,s;if(eJ(e))ez(e,"ECDH"),a=ec.KeyObject.from(e);else if(eD(e))a=e;else throw TypeError(eG(e,...eZ));if(eJ(t))ez(t,"ECDH","deriveBits"),s=ec.KeyObject.from(t);else if(eD(t))s=t;else throw TypeError(eG(t,...eZ));let c=ef(ew(ed.encode(r)),ew(i),ew(o),ey(n));return eb((0,ec.diffieHellman)({privateKey:s,publicKey:a}),n,c)}async function e7(e){let t;if(eJ(e))t=ec.KeyObject.from(e);else if(eD(e))t=e;else throw TypeError(eG(e,...eZ));switch(t.asymmetricKeyType){case"x25519":return e8("x25519");case"x448":return e8("x448");case"ec":return e8("ec",{namedCurve:e4(t)});default:throw new eA("Invalid or unsupported EPK")}}let te=e=>["P-256","P-384","P-521","X25519","X448"].includes(e4(e));function tt(e){if(!(e instanceof Uint8Array)||e.length<8)throw new eP("PBES2 Salt Input must be 8 or more octets")}let tr=(0,e$.promisify)(ec.pbkdf2);function tn(e,t){if(eD(e))return e.export();if(e instanceof Uint8Array)return e;if(eJ(e))return ez(e,t,"deriveBits","deriveKey"),ec.KeyObject.from(e).export();throw TypeError(eG(e,...eZ,"Uint8Array"))}let ti=async(e,t,r,n=2048,i=(0,ec.randomFillSync)(new Uint8Array(16)))=>{tt(i);let o=eh(e,i),a=parseInt(e.slice(13,16),10)>>3,s=tn(t,e),c=await tr(s,o,n,a,`sha${e.slice(8,11)}`);return{encryptedKey:await e2(e.slice(-6),c,r),p2c:n,p2s:eg(i)}},to=async(e,t,r,n,i)=>{tt(i);let o=eh(e,i),a=parseInt(e.slice(13,16),10)>>3,s=tn(t,e),c=await tr(s,o,n,a,`sha${e.slice(8,11)}`);return e5(e.slice(-6),c,r)},ta=(e,t)=>{let r;try{r=e instanceof ec.KeyObject?e.asymmetricKeyDetails?.modulusLength:Buffer.from(e.n,"base64url").byteLength<<3}catch{}if("number"!=typeof r||r<2048)throw TypeError(`${t} requires key modulusLength to be 2048 bits or larger`)},ts=(e,t)=>{if("rsa"!==e.asymmetricKeyType)throw TypeError("Invalid key for this operation, its asymmetricKeyType must be rsa");ta(e,t)},tc=(0,e$.deprecate)(()=>ec.constants.RSA_PKCS1_PADDING,'The RSA1_5 "alg" (JWE Algorithm) is deprecated and will be removed in the next major revision.'),tl=e=>{switch(e){case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":return ec.constants.RSA_PKCS1_OAEP_PADDING;case"RSA1_5":return tc();default:return}},tu=e=>{switch(e){case"RSA-OAEP":return"sha1";case"RSA-OAEP-256":return"sha256";case"RSA-OAEP-384":return"sha384";case"RSA-OAEP-512":return"sha512";default:return}};function td(e,t,...r){if(eD(e))return e;if(eJ(e))return ez(e,t,...r),ec.KeyObject.from(e);throw TypeError(eG(e,...eZ))}let tp=(e,t,r)=>{let n=tl(e),i=tu(e),o=td(t,e,"wrapKey","encrypt");return ts(o,e),(0,ec.publicEncrypt)({key:o,oaepHash:i,padding:n},r)},tf=(e,t,r)=>{let n=tl(e),i=tu(e),o=td(t,e,"unwrapKey","decrypt");return ts(o,e),(0,ec.privateDecrypt)({key:o,oaepHash:i,padding:n},r)},th={};function tm(e){switch(e){case"A128GCM":return 128;case"A192GCM":return 192;case"A256GCM":case"A128CBC-HS256":return 256;case"A192CBC-HS384":return 384;case"A256CBC-HS512":return 512;default:throw new eA(`Unsupported JWE Algorithm: ${e}`)}}let ty=e=>(0,ec.randomFillSync)(new Uint8Array(tm(e)>>3)),tw=e=>{let t;if(eJ(e)){if(!e.extractable)throw TypeError("CryptoKey is not extractable");t=ec.KeyObject.from(e)}else if(eD(e))t=e;else if(e instanceof Uint8Array)return{kty:"oct",k:eg(e)};else throw TypeError(eG(e,...eZ,"Uint8Array"));if("secret"!==t.type&&!["rsa","ec","ed25519","x25519","ed448","x448"].includes(t.asymmetricKeyType))throw new eA("Unsupported key asymmetricKeyType");return t.export({format:"jwk"})};async function tb(e){return tw(e)}let tg=e=>e?.[Symbol.toStringTag],t_=(e,t,r)=>{if(void 0!==t.use&&"sig"!==t.use)throw TypeError("Invalid key for this operation, when present its use must be sig");if(void 0!==t.key_ops&&t.key_ops.includes?.(r)!==!0)throw TypeError(`Invalid key for this operation, when present its key_ops must include ${r}`);if(void 0!==t.alg&&t.alg!==e)throw TypeError(`Invalid key for this operation, when present its alg must be ${e}`);return!0},tv=(e,t,r,n)=>{if(!(t instanceof Uint8Array)){if(n&&e3(t)){if(function(e){return e3(e)&&"oct"===e.kty&&"string"==typeof e.k}(t)&&t_(e,t,r))return;throw TypeError('JSON Web Key for symmetric algorithms must have JWK "kty" (Key Type) equal to "oct" and the JWK "k" (Key Value) present')}if(!eY(t))throw TypeError(eV(e,t,...eZ,"Uint8Array",n?"JSON Web Key":null));if("secret"!==t.type)throw TypeError(`${tg(t)} instances for symmetric algorithms must be of type "secret"`)}},tk=(e,t,r,n)=>{if(n&&e3(t))switch(r){case"sign":if(function(e){return"oct"!==e.kty&&"string"==typeof e.d}(t)&&t_(e,t,r))return;throw TypeError("JSON Web Key for this operation be a private JWK");case"verify":if(function(e){return"oct"!==e.kty&&void 0===e.d}(t)&&t_(e,t,r))return;throw TypeError("JSON Web Key for this operation be a public JWK")}if(!eY(t))throw TypeError(eV(e,t,...eZ,n?"JSON Web Key":null));if("secret"===t.type)throw TypeError(`${tg(t)} instances for asymmetric algorithms must not be of type "secret"`);if("sign"===r&&"public"===t.type)throw TypeError(`${tg(t)} instances for asymmetric algorithm signing must be of type "private"`);if("decrypt"===r&&"public"===t.type)throw TypeError(`${tg(t)} instances for asymmetric algorithm decryption must be of type "private"`);if(t.algorithm&&"verify"===r&&"private"===t.type)throw TypeError(`${tg(t)} instances for asymmetric algorithm verifying must be of type "public"`);if(t.algorithm&&"encrypt"===r&&"private"===t.type)throw TypeError(`${tg(t)} instances for asymmetric algorithm encryption must be of type "public"`)};function tE(e,t,r,n){t.startsWith("HS")||"dir"===t||t.startsWith("PBES2")||/^A\d{3}(?:GCM)?KW$/.test(t)?tv(t,r,n,e):tk(t,r,n,e)}let tS=tE.bind(void 0,!1);tE.bind(void 0,!0);let tA=ec.timingSafeEqual,tx=(e,t,r,n,i,o)=>{let a;if(eJ(t))ez(t,e,"decrypt"),a=ec.KeyObject.from(t);else if(t instanceof Uint8Array||eD(t))a=t;else throw TypeError(eG(t,...eZ,"Uint8Array"));if(!n)throw new eP("JWE Initialization Vector missing");if(!i)throw new eP("JWE Authentication Tag missing");switch(eW(e,a),eL(e,n),e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return function(e,t,r,n,i,o){let a,s,c=parseInt(e.slice(1,4),10);eD(t)&&(t=t.export());let l=t.subarray(c>>3),u=t.subarray(0,c>>3),d=parseInt(e.slice(-3),10),p=`aes-${c}-cbc`;if(!eX(p))throw new eA(`alg ${e} is not supported by your javascript runtime`);let f=eM(o,n,r,d,u,c);try{a=tA(i,f)}catch{}if(!a)throw new ex;try{let e=(0,ec.createDecipheriv)(p,l,n);s=ef(e.update(r),e.final())}catch{}if(!s)throw new ex;return s}(e,a,r,n,i,o);case"A128GCM":case"A192GCM":case"A256GCM":return function(e,t,r,n,i,o){let a=parseInt(e.slice(1,4),10),s=`aes-${a}-gcm`;if(!eX(s))throw new eA(`alg ${e} is not supported by your javascript runtime`);try{let e=(0,ec.createDecipheriv)(s,t,n,{authTagLength:16});e.setAuthTag(i),o.byteLength&&e.setAAD(o,{plaintextLength:r.length});let a=e.update(r);return e.final(),a}catch{throw new ex}}(e,a,r,n,i,o);default:throw new eA("Unsupported JWE Content Encryption Algorithm")}};async function tP(e,t,r,n){let i=e.slice(0,7),o=await eQ(i,r,t,n,new Uint8Array(0));return{encryptedKey:o.ciphertext,iv:eg(o.iv),tag:eg(o.tag)}}async function tT(e,t,r,n,i){return tx(e.slice(0,7),t,r,n,i,new Uint8Array(0))}async function tR(e,t,r,n,i={}){let o,a,s;switch(tS(e,r,"encrypt"),r=await th.normalizePublicKey?.(r,e)||r,e){case"dir":s=r;break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{if(!te(r))throw new eA("ECDH with the provided key is not allowed or not supported by your javascript runtime");let{apu:c,apv:l}=i,{epk:u}=i;u||=(await e7(r)).privateKey;let{x:d,y:p,crv:f,kty:h}=await tb(u),m=await e9(r,u,"ECDH-ES"===e?t:e,"ECDH-ES"===e?tm(t):parseInt(e.slice(-5,-2),10),c,l);if(a={epk:{x:d,crv:f,kty:h}},"EC"===h&&(a.epk.y=p),c&&(a.apu=eg(c)),l&&(a.apv=eg(l)),"ECDH-ES"===e){s=m;break}s=n||ty(t);let y=e.slice(-6);o=await e2(y,m,s);break}case"RSA1_5":case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":s=n||ty(t),o=await tp(e,r,s);break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{s=n||ty(t);let{p2c:c,p2s:l}=i;({encryptedKey:o,...a}=await ti(e,r,s,c,l));break}case"A128KW":case"A192KW":case"A256KW":s=n||ty(t),o=await e2(e,r,s);break;case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{s=n||ty(t);let{iv:c}=i;({encryptedKey:o,...a}=await tP(e,r,s,c));break}default:throw new eA('Invalid or unsupported "alg" (JWE Algorithm) header value')}return{cek:s,encryptedKey:o,parameters:a}}let tO=(...e)=>{let t,r=e.filter(Boolean);if(0===r.length||1===r.length)return!0;for(let e of r){let r=Object.keys(e);if(!t||0===t.size){t=new Set(r);continue}for(let e of r){if(t.has(e))return!1;t.add(e)}}return!0},tC=function(e,t,r,n,i){let o;if(void 0!==i.crit&&n?.crit===void 0)throw new e('"crit" (Critical) Header Parameter MUST be integrity protected');if(!n||void 0===n.crit)return new Set;if(!Array.isArray(n.crit)||0===n.crit.length||n.crit.some(e=>"string"!=typeof e||0===e.length))throw new e('"crit" (Critical) Header Parameter MUST be an array of non-empty strings when present');for(let a of(o=void 0!==r?new Map([...Object.entries(r),...t.entries()]):t,n.crit)){if(!o.has(a))throw new eA(`Extension Header Parameter "${a}" is not recognized`);if(void 0===i[a])throw new e(`Extension Header Parameter "${a}" is missing`);if(o.get(a)&&void 0===n[a])throw new e(`Extension Header Parameter "${a}" MUST be integrity protected`)}return new Set(n.crit)};class tU{_plaintext;_protectedHeader;_sharedUnprotectedHeader;_unprotectedHeader;_aad;_cek;_iv;_keyManagementParameters;constructor(e){if(!(e instanceof Uint8Array))throw TypeError("plaintext must be an instance of Uint8Array");this._plaintext=e}setKeyManagementParameters(e){if(this._keyManagementParameters)throw TypeError("setKeyManagementParameters can only be called once");return this._keyManagementParameters=e,this}setProtectedHeader(e){if(this._protectedHeader)throw TypeError("setProtectedHeader can only be called once");return this._protectedHeader=e,this}setSharedUnprotectedHeader(e){if(this._sharedUnprotectedHeader)throw TypeError("setSharedUnprotectedHeader can only be called once");return this._sharedUnprotectedHeader=e,this}setUnprotectedHeader(e){if(this._unprotectedHeader)throw TypeError("setUnprotectedHeader can only be called once");return this._unprotectedHeader=e,this}setAdditionalAuthenticatedData(e){return this._aad=e,this}setContentEncryptionKey(e){if(this._cek)throw TypeError("setContentEncryptionKey can only be called once");return this._cek=e,this}setInitializationVector(e){if(this._iv)throw TypeError("setInitializationVector can only be called once");return this._iv=e,this}async encrypt(e,t){let r,n,i,o,a;if(!this._protectedHeader&&!this._unprotectedHeader&&!this._sharedUnprotectedHeader)throw new eP("either setProtectedHeader, setUnprotectedHeader, or sharedUnprotectedHeader must be called before #encrypt()");if(!tO(this._protectedHeader,this._unprotectedHeader,this._sharedUnprotectedHeader))throw new eP("JWE Protected, JWE Shared Unprotected and JWE Per-Recipient Header Parameter names must be disjoint");let s={...this._protectedHeader,...this._unprotectedHeader,...this._sharedUnprotectedHeader};if(tC(eP,new Map,t?.crit,this._protectedHeader,s),void 0!==s.zip)throw new eA('JWE "zip" (Compression Algorithm) Header Parameter is not supported.');let{alg:c,enc:l}=s;if("string"!=typeof c||!c)throw new eP('JWE "alg" (Algorithm) Header Parameter missing or invalid');if("string"!=typeof l||!l)throw new eP('JWE "enc" (Encryption Algorithm) Header Parameter missing or invalid');if(this._cek&&("dir"===c||"ECDH-ES"===c))throw TypeError(`setContentEncryptionKey cannot be called with JWE "alg" (Algorithm) Header ${c}`);{let i;({cek:n,encryptedKey:r,parameters:i}=await tR(c,l,e,this._cek,this._keyManagementParameters)),i&&(t&&ej in t?this._unprotectedHeader?this._unprotectedHeader={...this._unprotectedHeader,...i}:this.setUnprotectedHeader(i):this._protectedHeader?this._protectedHeader={...this._protectedHeader,...i}:this.setProtectedHeader(i))}o=this._protectedHeader?ed.encode(eg(JSON.stringify(this._protectedHeader))):ed.encode(""),this._aad?(a=eg(this._aad),i=ef(o,ed.encode("."),ed.encode(a))):i=o;let{ciphertext:u,tag:d,iv:p}=await eQ(l,this._plaintext,n,this._iv,i),f={ciphertext:eg(u)};return p&&(f.iv=eg(p)),d&&(f.tag=eg(d)),r&&(f.encrypted_key=eg(r)),a&&(f.aad=a),this._protectedHeader&&(f.protected=ep.decode(o)),this._sharedUnprotectedHeader&&(f.unprotected=this._sharedUnprotectedHeader),this._unprotectedHeader&&(f.header=this._unprotectedHeader),f}}class tI{_flattened;constructor(e){this._flattened=new tU(e)}setContentEncryptionKey(e){return this._flattened.setContentEncryptionKey(e),this}setInitializationVector(e){return this._flattened.setInitializationVector(e),this}setProtectedHeader(e){return this._flattened.setProtectedHeader(e),this}setKeyManagementParameters(e){return this._flattened.setKeyManagementParameters(e),this}async encrypt(e,t){let r=await this._flattened.encrypt(e,t);return[r.protected,r.encrypted_key,r.iv,r.ciphertext,r.tag].join(".")}}let tj=e=>Math.floor(e.getTime()/1e3),tH=/^(\+|\-)? ?(\d+|\d+\.\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i,tN=e=>{let t,r=tH.exec(e);if(!r||r[4]&&r[1])throw TypeError("Invalid time period format");let n=parseFloat(r[2]);switch(r[3].toLowerCase()){case"sec":case"secs":case"second":case"seconds":case"s":t=Math.round(n);break;case"minute":case"minutes":case"min":case"mins":case"m":t=Math.round(60*n);break;case"hour":case"hours":case"hr":case"hrs":case"h":t=Math.round(3600*n);break;case"day":case"days":case"d":t=Math.round(86400*n);break;case"week":case"weeks":case"w":t=Math.round(604800*n);break;default:t=Math.round(0x1e187e0*n)}return"-"===r[1]||"ago"===r[4]?-t:t};function tL(e,t){if(!Number.isFinite(t))throw TypeError(`Invalid ${e} input`);return t}class t${_payload;constructor(e={}){if(!eC(e))throw TypeError("JWT Claims Set MUST be an object");this._payload=e}setIssuer(e){return this._payload={...this._payload,iss:e},this}setSubject(e){return this._payload={...this._payload,sub:e},this}setAudience(e){return this._payload={...this._payload,aud:e},this}setJti(e){return this._payload={...this._payload,jti:e},this}setNotBefore(e){return"number"==typeof e?this._payload={...this._payload,nbf:tL("setNotBefore",e)}:e instanceof Date?this._payload={...this._payload,nbf:tL("setNotBefore",tj(e))}:this._payload={...this._payload,nbf:tj(new Date)+tN(e)},this}setExpirationTime(e){return"number"==typeof e?this._payload={...this._payload,exp:tL("setExpirationTime",e)}:e instanceof Date?this._payload={...this._payload,exp:tL("setExpirationTime",tj(e))}:this._payload={...this._payload,exp:tj(new Date)+tN(e)},this}setIssuedAt(e){return void 0===e?this._payload={...this._payload,iat:tj(new Date)}:e instanceof Date?this._payload={...this._payload,iat:tL("setIssuedAt",tj(e))}:"string"==typeof e?this._payload={...this._payload,iat:tL("setIssuedAt",tj(new Date)+tN(e))}:this._payload={...this._payload,iat:tL("setIssuedAt",e)},this}}class tD extends t${_cek;_iv;_keyManagementParameters;_protectedHeader;_replicateIssuerAsHeader;_replicateSubjectAsHeader;_replicateAudienceAsHeader;setProtectedHeader(e){if(this._protectedHeader)throw TypeError("setProtectedHeader can only be called once");return this._protectedHeader=e,this}setKeyManagementParameters(e){if(this._keyManagementParameters)throw TypeError("setKeyManagementParameters can only be called once");return this._keyManagementParameters=e,this}setContentEncryptionKey(e){if(this._cek)throw TypeError("setContentEncryptionKey can only be called once");return this._cek=e,this}setInitializationVector(e){if(this._iv)throw TypeError("setInitializationVector can only be called once");return this._iv=e,this}replicateIssuerAsHeader(){return this._replicateIssuerAsHeader=!0,this}replicateSubjectAsHeader(){return this._replicateSubjectAsHeader=!0,this}replicateAudienceAsHeader(){return this._replicateAudienceAsHeader=!0,this}async encrypt(e,t){let r=new tI(ed.encode(JSON.stringify(this._payload)));return this._replicateIssuerAsHeader&&(this._protectedHeader={...this._protectedHeader,iss:this._payload.iss}),this._replicateSubjectAsHeader&&(this._protectedHeader={...this._protectedHeader,sub:this._payload.sub}),this._replicateAudienceAsHeader&&(this._protectedHeader={...this._protectedHeader,aud:this._payload.aud}),r.setProtectedHeader(this._protectedHeader),this._iv&&r.setInitializationVector(this._iv),this._cek&&r.setContentEncryptionKey(this._cek),this._keyManagementParameters&&r.setKeyManagementParameters(this._keyManagementParameters),r.encrypt(e,t)}}let tW=e=>e.d?(0,ec.createPrivateKey)({format:"jwk",key:e}):(0,ec.createPublicKey)({format:"jwk",key:e});async function tM(e,t){if(!eC(e))throw TypeError("JWK must be an object");switch(t||=e.alg,e.kty){case"oct":if("string"!=typeof e.k||!e.k)throw TypeError('missing "k" (Key Value) Parameter value');return e_(e.k);case"RSA":if("oth"in e&&void 0!==e.oth)throw new eA('RSA JWK "oth" (Other Primes Info) Parameter value is not supported');case"EC":case"OKP":return tW({...e,alg:t});default:throw new eA('Unsupported "kty" (Key Type) Parameter value')}}async function tK(e,t,r,n,i){switch(tS(e,t,"decrypt"),t=await th.normalizePrivateKey?.(t,e)||t,e){case"dir":if(void 0!==r)throw new eP("Encountered unexpected JWE Encrypted Key");return t;case"ECDH-ES":if(void 0!==r)throw new eP("Encountered unexpected JWE Encrypted Key");case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{let i,o;if(!eC(n.epk))throw new eP('JOSE Header "epk" (Ephemeral Public Key) missing or invalid');if(!te(t))throw new eA("ECDH with the provided key is not allowed or not supported by your javascript runtime");let a=await tM(n.epk,e);if(void 0!==n.apu){if("string"!=typeof n.apu)throw new eP('JOSE Header "apu" (Agreement PartyUInfo) invalid');try{i=e_(n.apu)}catch{throw new eP("Failed to base64url decode the apu")}}if(void 0!==n.apv){if("string"!=typeof n.apv)throw new eP('JOSE Header "apv" (Agreement PartyVInfo) invalid');try{o=e_(n.apv)}catch{throw new eP("Failed to base64url decode the apv")}}let s=await e9(a,t,"ECDH-ES"===e?n.enc:e,"ECDH-ES"===e?tm(n.enc):parseInt(e.slice(-5,-2),10),i,o);if("ECDH-ES"===e)return s;if(void 0===r)throw new eP("JWE Encrypted Key missing");return e5(e.slice(-6),s,r)}case"RSA1_5":case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":if(void 0===r)throw new eP("JWE Encrypted Key missing");return tf(e,t,r);case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{let o;if(void 0===r)throw new eP("JWE Encrypted Key missing");if("number"!=typeof n.p2c)throw new eP('JOSE Header "p2c" (PBES2 Count) missing or invalid');let a=i?.maxPBES2Count||1e4;if(n.p2c>a)throw new eP('JOSE Header "p2c" (PBES2 Count) out is of acceptable bounds');if("string"!=typeof n.p2s)throw new eP('JOSE Header "p2s" (PBES2 Salt) missing or invalid');try{o=e_(n.p2s)}catch{throw new eP("Failed to base64url decode the p2s")}return to(e,t,r,n.p2c,o)}case"A128KW":case"A192KW":case"A256KW":if(void 0===r)throw new eP("JWE Encrypted Key missing");return e5(e,t,r);case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{let i,o;if(void 0===r)throw new eP("JWE Encrypted Key missing");if("string"!=typeof n.iv)throw new eP('JOSE Header "iv" (Initialization Vector) missing or invalid');if("string"!=typeof n.tag)throw new eP('JOSE Header "tag" (Authentication Tag) missing or invalid');try{i=e_(n.iv)}catch{throw new eP("Failed to base64url decode the iv")}try{o=e_(n.tag)}catch{throw new eP("Failed to base64url decode the tag")}return tT(e,t,r,i,o)}default:throw new eA('Invalid or unsupported "alg" (JWE Algorithm) header value')}}let tJ=(e,t)=>{if(void 0!==t&&(!Array.isArray(t)||t.some(e=>"string"!=typeof e)))throw TypeError(`"${e}" option must be an array of strings`);if(t)return new Set(t)};async function tF(e,t,r){let n,i,o,a,s,c,l;if(!eC(e))throw new eP("Flattened JWE must be an object");if(void 0===e.protected&&void 0===e.header&&void 0===e.unprotected)throw new eP("JOSE Header missing");if(void 0!==e.iv&&"string"!=typeof e.iv)throw new eP("JWE Initialization Vector incorrect type");if("string"!=typeof e.ciphertext)throw new eP("JWE Ciphertext missing or incorrect type");if(void 0!==e.tag&&"string"!=typeof e.tag)throw new eP("JWE Authentication Tag incorrect type");if(void 0!==e.protected&&"string"!=typeof e.protected)throw new eP("JWE Protected Header incorrect type");if(void 0!==e.encrypted_key&&"string"!=typeof e.encrypted_key)throw new eP("JWE Encrypted Key incorrect type");if(void 0!==e.aad&&"string"!=typeof e.aad)throw new eP("JWE AAD incorrect type");if(void 0!==e.header&&!eC(e.header))throw new eP("JWE Shared Unprotected Header incorrect type");if(void 0!==e.unprotected&&!eC(e.unprotected))throw new eP("JWE Per-Recipient Unprotected Header incorrect type");if(e.protected)try{let t=e_(e.protected);n=JSON.parse(ep.decode(t))}catch{throw new eP("JWE Protected Header is invalid")}if(!tO(n,e.header,e.unprotected))throw new eP("JWE Protected, JWE Unprotected Header, and JWE Per-Recipient Unprotected Header Parameter names must be disjoint");let u={...n,...e.header,...e.unprotected};if(tC(eP,new Map,r?.crit,n,u),void 0!==u.zip)throw new eA('JWE "zip" (Compression Algorithm) Header Parameter is not supported.');let{alg:d,enc:p}=u;if("string"!=typeof d||!d)throw new eP("missing JWE Algorithm (alg) in JWE Header");if("string"!=typeof p||!p)throw new eP("missing JWE Encryption Algorithm (enc) in JWE Header");let f=r&&tJ("keyManagementAlgorithms",r.keyManagementAlgorithms),h=r&&tJ("contentEncryptionAlgorithms",r.contentEncryptionAlgorithms);if(f&&!f.has(d)||!f&&d.startsWith("PBES2"))throw new eS('"alg" (Algorithm) Header Parameter value not allowed');if(h&&!h.has(p))throw new eS('"enc" (Encryption Algorithm) Header Parameter value not allowed');if(void 0!==e.encrypted_key)try{i=e_(e.encrypted_key)}catch{throw new eP("Failed to base64url decode the encrypted_key")}let m=!1;"function"==typeof t&&(t=await t(n,e),m=!0);try{o=await tK(d,t,i,u,r)}catch(e){if(e instanceof TypeError||e instanceof eP||e instanceof eA)throw e;o=ty(p)}if(void 0!==e.iv)try{a=e_(e.iv)}catch{throw new eP("Failed to base64url decode the iv")}if(void 0!==e.tag)try{s=e_(e.tag)}catch{throw new eP("Failed to base64url decode the tag")}let y=ed.encode(e.protected??"");c=void 0!==e.aad?ef(y,ed.encode("."),ed.encode(e.aad)):y;try{l=e_(e.ciphertext)}catch{throw new eP("Failed to base64url decode the ciphertext")}let w={plaintext:await tx(p,o,l,a,s,c)};if(void 0!==e.protected&&(w.protectedHeader=n),void 0!==e.aad)try{w.additionalAuthenticatedData=e_(e.aad)}catch{throw new eP("Failed to base64url decode the aad")}return(void 0!==e.unprotected&&(w.sharedUnprotectedHeader=e.unprotected),void 0!==e.header&&(w.unprotectedHeader=e.header),m)?{...w,key:t}:w}async function tB(e,t,r){if(e instanceof Uint8Array&&(e=ep.decode(e)),"string"!=typeof e)throw new eP("Compact JWE must be a string or Uint8Array");let{0:n,1:i,2:o,3:a,4:s,length:c}=e.split(".");if(5!==c)throw new eP("Invalid Compact JWE");let l=await tF({ciphertext:a,iv:o||void 0,protected:n,tag:s||void 0,encrypted_key:i||void 0},t,r),u={plaintext:l.plaintext,protectedHeader:l.protectedHeader};return"function"==typeof t?{...u,key:l.key}:u}let tz=e=>e.toLowerCase().replace(/^application\//,""),tq=(e,t)=>"string"==typeof e?t.includes(e):!!Array.isArray(e)&&t.some(Set.prototype.has.bind(new Set(e))),tG=(e,t,r={})=>{let n,i;try{n=JSON.parse(ep.decode(t))}catch{}if(!eC(n))throw new eT("JWT Claims Set must be a top-level JSON object");let{typ:o}=r;if(o&&("string"!=typeof e.typ||tz(e.typ)!==tz(o)))throw new ek('unexpected "typ" JWT header value',n,"typ","check_failed");let{requiredClaims:a=[],issuer:s,subject:c,audience:l,maxTokenAge:u}=r,d=[...a];for(let e of(void 0!==u&&d.push("iat"),void 0!==l&&d.push("aud"),void 0!==c&&d.push("sub"),void 0!==s&&d.push("iss"),new Set(d.reverse())))if(!(e in n))throw new ek(`missing required "${e}" claim`,n,e,"missing");if(s&&!(Array.isArray(s)?s:[s]).includes(n.iss))throw new ek('unexpected "iss" claim value',n,"iss","check_failed");if(c&&n.sub!==c)throw new ek('unexpected "sub" claim value',n,"sub","check_failed");if(l&&!tq(n.aud,"string"==typeof l?[l]:l))throw new ek('unexpected "aud" claim value',n,"aud","check_failed");switch(typeof r.clockTolerance){case"string":i=tN(r.clockTolerance);break;case"number":i=r.clockTolerance;break;case"undefined":i=0;break;default:throw TypeError("Invalid clockTolerance option type")}let{currentDate:p}=r,f=tj(p||new Date);if((void 0!==n.iat||u)&&"number"!=typeof n.iat)throw new ek('"iat" claim must be a number',n,"iat","invalid");if(void 0!==n.nbf){if("number"!=typeof n.nbf)throw new ek('"nbf" claim must be a number',n,"nbf","invalid");if(n.nbf>f+i)throw new ek('"nbf" claim timestamp check failed',n,"nbf","check_failed")}if(void 0!==n.exp){if("number"!=typeof n.exp)throw new ek('"exp" claim must be a number',n,"exp","invalid");if(n.exp<=f-i)throw new eE('"exp" claim timestamp check failed',n,"exp","check_failed")}if(u){let e=f-n.iat;if(e-i>("number"==typeof u?u:tN(u)))throw new eE('"iat" claim timestamp check failed (too far in the past)',n,"iat","check_failed");if(e<0-i)throw new ek('"iat" claim timestamp check failed (it should be in the past)',n,"iat","check_failed")}return n};async function tV(e,t,r){let n=await tB(e,t,r),i=tG(n.protectedHeader,n.plaintext,r),{protectedHeader:o}=n;if(void 0!==o.iss&&o.iss!==i.iss)throw new ek('replicated "iss" claim header parameter mismatch',i,"iss","mismatch");if(void 0!==o.sub&&o.sub!==i.sub)throw new ek('replicated "sub" claim header parameter mismatch',i,"sub","mismatch");if(void 0!==o.aud&&JSON.stringify(o.aud)!==JSON.stringify(i.aud))throw new ek('replicated "aud" claim header parameter mismatch',i,"aud","mismatch");let a={payload:i,protectedHeader:o};return"function"==typeof t?{...a,key:n.key}:a}var tX=r(44168);let tY=()=>Date.now()/1e3|0,tZ="A256CBC-HS512";async function tQ(e){let{token:t={},secret:r,maxAge:n=2592e3,salt:i}=e,o=Array.isArray(r)?r:[r],a=await t1(tZ,o[0],i),s=await eI({kty:"oct",k:eg(a)},`sha${a.byteLength<<3}`);return await new tD(t).setProtectedHeader({alg:"dir",enc:tZ,kid:s}).setIssuedAt().setExpirationTime(tY()+n).setJti(crypto.randomUUID()).encrypt(a)}async function t0(e){let{token:t,secret:r,salt:n}=e,i=Array.isArray(r)?r:[r];if(!t)return null;let{payload:o}=await tV(t,async({kid:e,enc:t})=>{for(let r of i){let i=await t1(t,r,n);if(void 0===e||e===await eI({kty:"oct",k:eg(i)},`sha${i.byteLength<<3}`))return i}throw Error("no matching decryption secret")},{clockTolerance:15,keyManagementAlgorithms:["dir"],contentEncryptionAlgorithms:[tZ,"A256GCM"]});return o}async function t1(e,t,r){let n;switch(e){case"A256CBC-HS512":n=64;break;case"A256GCM":n=32;break;default:throw Error("Unsupported JWT Content Encryption Algorithm")}return await es("sha256",t,r,`Auth.js Generated Encryption Key (${r})`,n)}async function t2({options:e,paramValue:t,cookieValue:r}){let{url:n,callbacks:i}=e,o=n.origin;return t?o=await i.redirect({url:t,baseUrl:n.origin}):r&&(o=await i.redirect({url:r,baseUrl:n.origin})),{callbackUrl:o,callbackUrlCookie:o!==r?o:void 0}}let t5="\x1b[31m",t3="\x1b[0m",t6={error(e){let t=e instanceof h?e.type:e.name;if(console.error(`${t5}[auth][error]${t3} ${t}: ${e.message}`),e.cause&&"object"==typeof e.cause&&"err"in e.cause&&e.cause.err instanceof Error){let{err:t,...r}=e.cause;console.error(`${t5}[auth][cause]${t3}:`,t.stack),r&&console.error(`${t5}[auth][details]${t3}:`,JSON.stringify(r,null,2))}else e.stack&&console.error(e.stack.replace(/.*/,"").substring(1))},warn(e){let t=`https://warnings.authjs.dev#${e}`;console.warn(`\x1b[33m[auth][warn][${e}]${t3}`,`Read more: ${t}`)},debug(e,t){console.log(`\x1b[90m[auth][debug]:${t3} ${e}`,JSON.stringify(t,null,2))}};function t4(e){let t={...t6};return e.debug||(t.debug=()=>{}),e.logger?.error&&(t.error=e.logger.error),e.logger?.warn&&(t.warn=e.logger.warn),e.logger?.debug&&(t.debug=e.logger.debug),e.logger??(e.logger=t),t}let t8=["providers","session","csrf","signin","signout","callback","verify-request","error","webauthn-options"];async function t9(e){if(!("body"in e)||!e.body||"POST"!==e.method)return;let t=e.headers.get("content-type");return t?.includes("application/json")?await e.json():t?.includes("application/x-www-form-urlencoded")?Object.fromEntries(new URLSearchParams(await e.text())):void 0}async function t7(e,t){try{if("GET"!==e.method&&"POST"!==e.method)throw new L("Only GET and POST requests are supported");t.basePath??(t.basePath="/auth");let r=new URL(e.url),{action:n,providerId:i}=function(e,t){let r=e.match(RegExp(`^${t}(.+)`));if(null===r)throw new L(`Cannot parse action at ${e}`);let n=r.at(-1).replace(/^\//,"").split("/").filter(Boolean);if(1!==n.length&&2!==n.length)throw new L(`Cannot parse action at ${e}`);let[i,o]=n;if(!t8.includes(i)||o&&!["signin","callback","webauthn-options"].includes(i))throw new L(`Cannot parse action at ${e}`);return{action:i,providerId:o}}(r.pathname,t.basePath);return{url:r,action:n,providerId:i,method:e.method,headers:Object.fromEntries(e.headers),body:e.body?await t9(e):void 0,cookies:(0,tX.q)(e.headers.get("cookie")??"")??{},error:r.searchParams.get("error")??void 0,query:Object.fromEntries(r.searchParams)}}catch(n){let r=t4(t);r.error(n),r.debug("request",e)}}function re(e){let t=new Headers(e.headers);e.cookies?.forEach(e=>{let{name:r,value:n,options:i}=e,o=(0,tX.l)(r,n,i);t.has("Set-Cookie")?t.append("Set-Cookie",o):t.set("Set-Cookie",o)});let r=e.body;"application/json"===t.get("content-type")?r=JSON.stringify(e.body):"application/x-www-form-urlencoded"===t.get("content-type")&&(r=new URLSearchParams(e.body).toString());let n=new Response(r,{headers:t,status:e.redirect?302:e.status??200});return e.redirect&&n.headers.set("Location",e.redirect),n}async function rt(e){let t=new TextEncoder().encode(e);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",t))).map(e=>e.toString(16).padStart(2,"0")).join("").toString()}function rr(e){let t=e=>("0"+e.toString(16)).slice(-2);return Array.from(crypto.getRandomValues(new Uint8Array(e))).reduce((e,r)=>e+t(r),"")}async function rn({options:e,cookieValue:t,isPost:r,bodyValue:n}){if(t){let[i,o]=t.split("|");if(o===await rt(`${i}${e.secret}`))return{csrfTokenVerified:r&&i===n,csrfToken:i}}let i=rr(32),o=await rt(`${i}${e.secret}`);return{cookie:`${i}|${o}`,csrfToken:i}}function ri(e,t){if(!t)throw new K(`CSRF token was missing during an action ${e}`)}function ro(e){return null!==e&&"object"==typeof e}function ra(e,...t){if(!t.length)return e;let r=t.shift();if(ro(e)&&ro(r))for(let t in r)ro(r[t])?(ro(e[t])||(e[t]=Array.isArray(r[t])?[]:{}),ra(e[t],r[t])):void 0!==r[t]&&(e[t]=r[t]);return ra(e,...t)}let rs=Symbol("skip-csrf-check"),rc=Symbol("return-type-raw"),rl=Symbol("custom-fetch"),ru=Symbol("conform-internal"),rd=e=>rf({id:e.sub??e.id??crypto.randomUUID(),name:e.name??e.nickname??e.preferred_username,email:e.email,image:e.picture}),rp=e=>rf({access_token:e.access_token,id_token:e.id_token,refresh_token:e.refresh_token,expires_at:e.expires_at,scope:e.scope,token_type:e.token_type,session_state:e.session_state});function rf(e){let t={};for(let[r,n]of Object.entries(e))void 0!==n&&(t[r]=n);return t}function rh(e,t){if(!e&&t)return;if("string"==typeof e)return{url:new URL(e)};let r=new URL(e?.url??"https://authjs.dev");if(e?.params!=null)for(let[t,n]of Object.entries(e.params))"claims"===t&&(n=JSON.stringify(n)),r.searchParams.set(t,String(n));return{url:r,request:e?.request,conform:e?.conform,...e?.clientPrivateKey?{clientPrivateKey:e?.clientPrivateKey}:null}}let rm={signIn:()=>!0,redirect:({url:e,baseUrl:t})=>e.startsWith("/")?`${t}${e}`:new URL(e).origin===t?e:t,session:({session:e})=>({user:{name:e.user?.name,email:e.user?.email,image:e.user?.image},expires:e.expires?.toISOString?.()??e.expires}),jwt:({token:e})=>e};async function ry({authOptions:e,providerId:t,action:r,url:n,cookies:i,callbackUrl:o,csrfToken:a,csrfDisabled:s,isPost:c}){var l,u;let d=t4(e),{providers:f,provider:h}=function(e){let{providerId:t,config:r}=e,n=new URL(r.basePath??"/auth",e.url.origin),i=r.providers.map(e=>{let t="function"==typeof e?e():e,{options:i,...o}=t,a=i?.id??o.id,s=ra(o,i,{signinUrl:`${n}/signin/${a}`,callbackUrl:`${n}/callback/${a}`});if("oauth"===t.type||"oidc"===t.type){s.redirectProxyUrl??(s.redirectProxyUrl=i?.redirectProxyUrl??r.redirectProxyUrl);let e=function(e){e.issuer&&(e.wellKnown??(e.wellKnown=`${e.issuer}/.well-known/openid-configuration`));let t=rh(e.authorization,e.issuer);t&&!t.url?.searchParams.has("scope")&&t.url.searchParams.set("scope","openid profile email");let r=rh(e.token,e.issuer),n=rh(e.userinfo,e.issuer),i=e.checks??["pkce"];return e.redirectProxyUrl&&(i.includes("state")||i.push("state"),e.redirectProxyUrl=`${e.redirectProxyUrl}/callback/${e.id}`),{...e,authorization:t,token:r,checks:i,userinfo:n,profile:e.profile??rd,account:e.account??rp}}(s);return e.authorization?.url.searchParams.get("response_mode")==="form_post"&&delete e.redirectProxyUrl,e[rl]??(e[rl]=i?.[rl]),e}return s});return{providers:i,provider:i.find(({id:e})=>e===t)}}({url:n,providerId:t,config:e}),m=!1;if((h?.type==="oauth"||h?.type==="oidc")&&h.redirectProxyUrl)try{m=new URL(h.redirectProxyUrl).origin===n.origin}catch{throw TypeError(`redirectProxyUrl must be a valid URL. Received: ${h.redirectProxyUrl}`)}let w={debug:!1,pages:{},theme:{colorScheme:"auto",logo:"",brandColor:"",buttonText:""},...e,url:n,action:r,provider:h,cookies:ra(p(e.useSecureCookies??"https:"===n.protocol),e.cookies),providers:f,session:{strategy:e.adapter?"database":"jwt",maxAge:2592e3,updateAge:86400,generateSessionToken:()=>crypto.randomUUID(),...e.session},jwt:{secret:e.secret,maxAge:e.session?.maxAge??2592e3,encode:tQ,decode:t0,...e.jwt},events:(l=e.events??{},u=d,Object.keys(l).reduce((e,t)=>(e[t]=async(...e)=>{try{let r=l[t];return await r(...e)}catch(e){u.error(new _(e))}},e),{})),adapter:function(e,t){if(e)return Object.keys(e).reduce((r,n)=>(r[n]=async(...r)=>{try{t.debug(`adapter_${n}`,{args:r});let i=e[n];return await i(...r)}catch(r){let e=new y(r);throw t.error(e),e}},r),{})}(e.adapter,d),callbacks:{...rm,...e.callbacks},logger:d,callbackUrl:n.origin,isOnRedirectProxy:m,experimental:{...e.experimental}},b=[];if(s)w.csrfTokenVerified=!0;else{let{csrfToken:e,cookie:t,csrfTokenVerified:r}=await rn({options:w,cookieValue:i?.[w.cookies.csrfToken.name],isPost:c,bodyValue:a});w.csrfToken=e,w.csrfTokenVerified=r,t&&b.push({name:w.cookies.csrfToken.name,value:t,options:w.cookies.csrfToken.options})}let{callbackUrl:g,callbackUrlCookie:v}=await t2({options:w,cookieValue:i?.[w.cookies.callbackUrl.name],paramValue:o});return w.callbackUrl=g,v&&b.push({name:w.cookies.callbackUrl.name,value:v,options:w.cookies.callbackUrl.options}),{options:w,cookies:b}}var rw,rb,rg,r_,rv,rk,rE,rS,rA,rx,rP,rT={},rR=[],rO=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;function rC(e,t){for(var r in t)e[r]=t[r];return e}function rU(e){var t=e.parentNode;t&&t.removeChild(e)}function rI(e,t,r,n,i){var o={type:e,props:t,key:r,ref:n,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:null==i?++rA:i};return null==i&&null!=rS.vnode&&rS.vnode(o),o}function rj(e){return e.children}function rH(e,t){this.props=e,this.context=t}function rN(e,t){if(null==t)return e.__?rN(e.__,e.__.__k.indexOf(e)+1):null;for(var r;t<e.__k.length;t++)if(null!=(r=e.__k[t])&&null!=r.__e)return r.__e;return"function"==typeof e.type?rN(e):null}function rL(e){(!e.__d&&(e.__d=!0)&&rx.push(e)&&!r$.__r++||rP!==rS.debounceRendering)&&((rP=rS.debounceRendering)||setTimeout)(r$)}function r$(){for(var e;r$.__r=rx.length;)e=rx.sort(function(e,t){return e.__v.__b-t.__v.__b}),rx=[],e.some(function(e){var t,r,n,i,o;e.__d&&(i=(n=e.__v).__e,(o=e.__P)&&(t=[],(r=rC({},n)).__v=n.__v+1,rB(o,n,r,e.__n,void 0!==o.ownerSVGElement,null!=n.__h?[i]:null,t,null==i?rN(n):i,n.__h),rz(t,n),n.__e!=i&&function e(t){var r,n;if(null!=(t=t.__)&&null!=t.__c){for(t.__e=t.__c.base=null,r=0;r<t.__k.length;r++)if(null!=(n=t.__k[r])&&null!=n.__e){t.__e=t.__c.base=n.__e;break}return e(t)}}(n)))})}function rD(e,t,r,n,i,o,a,s,c,l){var u,d,p,f,h,m,y,w=n&&n.__k||rR,b=w.length;for(r.__k=[],u=0;u<t.length;u++)if(null!=(f=r.__k[u]=null==(f=t[u])||"boolean"==typeof f?null:"string"==typeof f||"number"==typeof f||"bigint"==typeof f?rI(null,f,null,null,f):Array.isArray(f)?rI(rj,{children:f},null,null,null):f.__b>0?rI(f.type,f.props,f.key,f.ref?f.ref:null,f.__v):f)){if(f.__=r,f.__b=r.__b+1,null===(p=w[u])||p&&f.key==p.key&&f.type===p.type)w[u]=void 0;else for(d=0;d<b;d++){if((p=w[d])&&f.key==p.key&&f.type===p.type){w[d]=void 0;break}p=null}rB(e,f,p=p||rT,i,o,a,s,c,l),h=f.__e,(d=f.ref)&&p.ref!=d&&(y||(y=[]),p.ref&&y.push(p.ref,null,f),y.push(d,f.__c||h,f)),null!=h?(null==m&&(m=h),"function"==typeof f.type&&f.__k===p.__k?f.__d=c=function e(t,r,n){for(var i,o=t.__k,a=0;o&&a<o.length;a++)(i=o[a])&&(i.__=t,r="function"==typeof i.type?e(i,r,n):rW(n,i,i,o,i.__e,r));return r}(f,c,e):c=rW(e,f,p,w,h,c),"function"==typeof r.type&&(r.__d=c)):c&&p.__e==c&&c.parentNode!=e&&(c=rN(p))}for(r.__e=m,u=b;u--;)null!=w[u]&&function e(t,r,n){var i,o;if(rS.unmount&&rS.unmount(t),(i=t.ref)&&(i.current&&i.current!==t.__e||rq(i,null,r)),null!=(i=t.__c)){if(i.componentWillUnmount)try{i.componentWillUnmount()}catch(e){rS.__e(e,r)}i.base=i.__P=null,t.__c=void 0}if(i=t.__k)for(o=0;o<i.length;o++)i[o]&&e(i[o],r,n||"function"!=typeof t.type);n||null==t.__e||rU(t.__e),t.__=t.__e=t.__d=void 0}(w[u],w[u]);if(y)for(u=0;u<y.length;u++)rq(y[u],y[++u],y[++u])}function rW(e,t,r,n,i,o){var a,s,c;if(void 0!==t.__d)a=t.__d,t.__d=void 0;else if(null==r||i!=o||null==i.parentNode)e:if(null==o||o.parentNode!==e)e.appendChild(i),a=null;else{for(s=o,c=0;(s=s.nextSibling)&&c<n.length;c+=1)if(s==i)break e;e.insertBefore(i,o),a=o}return void 0!==a?a:i.nextSibling}function rM(e,t,r){"-"===t[0]?e.setProperty(t,r):e[t]=null==r?"":"number"!=typeof r||rO.test(t)?r:r+"px"}function rK(e,t,r,n,i){var o;e:if("style"===t)if("string"==typeof r)e.style.cssText=r;else{if("string"==typeof n&&(e.style.cssText=n=""),n)for(t in n)r&&t in r||rM(e.style,t,"");if(r)for(t in r)n&&r[t]===n[t]||rM(e.style,t,r[t])}else if("o"===t[0]&&"n"===t[1])o=t!==(t=t.replace(/Capture$/,"")),t=t.toLowerCase()in e?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+o]=r,r?n||e.addEventListener(t,o?rF:rJ,o):e.removeEventListener(t,o?rF:rJ,o);else if("dangerouslySetInnerHTML"!==t){if(i)t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("href"!==t&&"list"!==t&&"form"!==t&&"tabIndex"!==t&&"download"!==t&&t in e)try{e[t]=null==r?"":r;break e}catch(e){}"function"==typeof r||(null==r||!1===r&&-1==t.indexOf("-")?e.removeAttribute(t):e.setAttribute(t,r))}}function rJ(e){this.l[e.type+!1](rS.event?rS.event(e):e)}function rF(e){this.l[e.type+!0](rS.event?rS.event(e):e)}function rB(e,t,r,n,i,o,a,s,c){var l,u,d,p,f,h,m,y,w,b,g,_,v,k,E,S=t.type;if(void 0!==t.constructor)return null;null!=r.__h&&(c=r.__h,s=t.__e=r.__e,t.__h=null,o=[s]),(l=rS.__b)&&l(t);try{e:if("function"==typeof S){if(y=t.props,w=(l=S.contextType)&&n[l.__c],b=l?w?w.props.value:l.__:n,r.__c?m=(u=t.__c=r.__c).__=u.__E:("prototype"in S&&S.prototype.render?t.__c=u=new S(y,b):(t.__c=u=new rH(y,b),u.constructor=S,u.render=rG),w&&w.sub(u),u.props=y,u.state||(u.state={}),u.context=b,u.__n=n,d=u.__d=!0,u.__h=[],u._sb=[]),null==u.__s&&(u.__s=u.state),null!=S.getDerivedStateFromProps&&(u.__s==u.state&&(u.__s=rC({},u.__s)),rC(u.__s,S.getDerivedStateFromProps(y,u.__s))),p=u.props,f=u.state,d)null==S.getDerivedStateFromProps&&null!=u.componentWillMount&&u.componentWillMount(),null!=u.componentDidMount&&u.__h.push(u.componentDidMount);else{if(null==S.getDerivedStateFromProps&&y!==p&&null!=u.componentWillReceiveProps&&u.componentWillReceiveProps(y,b),!u.__e&&null!=u.shouldComponentUpdate&&!1===u.shouldComponentUpdate(y,u.__s,b)||t.__v===r.__v){for(u.props=y,u.state=u.__s,t.__v!==r.__v&&(u.__d=!1),u.__v=t,t.__e=r.__e,t.__k=r.__k,t.__k.forEach(function(e){e&&(e.__=t)}),g=0;g<u._sb.length;g++)u.__h.push(u._sb[g]);u._sb=[],u.__h.length&&a.push(u);break e}null!=u.componentWillUpdate&&u.componentWillUpdate(y,u.__s,b),null!=u.componentDidUpdate&&u.__h.push(function(){u.componentDidUpdate(p,f,h)})}if(u.context=b,u.props=y,u.__v=t,u.__P=e,_=rS.__r,v=0,"prototype"in S&&S.prototype.render){for(u.state=u.__s,u.__d=!1,_&&_(t),l=u.render(u.props,u.state,u.context),k=0;k<u._sb.length;k++)u.__h.push(u._sb[k]);u._sb=[]}else do u.__d=!1,_&&_(t),l=u.render(u.props,u.state,u.context),u.state=u.__s;while(u.__d&&++v<25);u.state=u.__s,null!=u.getChildContext&&(n=rC(rC({},n),u.getChildContext())),d||null==u.getSnapshotBeforeUpdate||(h=u.getSnapshotBeforeUpdate(p,f)),E=null!=l&&l.type===rj&&null==l.key?l.props.children:l,rD(e,Array.isArray(E)?E:[E],t,r,n,i,o,a,s,c),u.base=t.__e,t.__h=null,u.__h.length&&a.push(u),m&&(u.__E=u.__=null),u.__e=!1}else null==o&&t.__v===r.__v?(t.__k=r.__k,t.__e=r.__e):t.__e=function(e,t,r,n,i,o,a,s){var c,l,u,d=r.props,p=t.props,f=t.type,h=0;if("svg"===f&&(i=!0),null!=o){for(;h<o.length;h++)if((c=o[h])&&"setAttribute"in c==!!f&&(f?c.localName===f:3===c.nodeType)){e=c,o[h]=null;break}}if(null==e){if(null===f)return document.createTextNode(p);e=i?document.createElementNS("http://www.w3.org/2000/svg",f):document.createElement(f,p.is&&p),o=null,s=!1}if(null===f)d===p||s&&e.data===p||(e.data=p);else{if(o=o&&rE.call(e.childNodes),l=(d=r.props||rT).dangerouslySetInnerHTML,u=p.dangerouslySetInnerHTML,!s){if(null!=o)for(d={},h=0;h<e.attributes.length;h++)d[e.attributes[h].name]=e.attributes[h].value;(u||l)&&(u&&(l&&u.__html==l.__html||u.__html===e.innerHTML)||(e.innerHTML=u&&u.__html||""))}if(function(e,t,r,n,i){var o;for(o in r)"children"===o||"key"===o||o in t||rK(e,o,null,r[o],n);for(o in t)i&&"function"!=typeof t[o]||"children"===o||"key"===o||"value"===o||"checked"===o||r[o]===t[o]||rK(e,o,t[o],r[o],n)}(e,p,d,i,s),u)t.__k=[];else if(rD(e,Array.isArray(h=t.props.children)?h:[h],t,r,n,i&&"foreignObject"!==f,o,a,o?o[0]:r.__k&&rN(r,0),s),null!=o)for(h=o.length;h--;)null!=o[h]&&rU(o[h]);s||("value"in p&&void 0!==(h=p.value)&&(h!==e.value||"progress"===f&&!h||"option"===f&&h!==d.value)&&rK(e,"value",h,d.value,!1),"checked"in p&&void 0!==(h=p.checked)&&h!==e.checked&&rK(e,"checked",h,d.checked,!1))}return e}(r.__e,t,r,n,i,o,a,c);(l=rS.diffed)&&l(t)}catch(e){t.__v=null,(c||null!=o)&&(t.__e=s,t.__h=!!c,o[o.indexOf(s)]=null),rS.__e(e,t,r)}}function rz(e,t){rS.__c&&rS.__c(t,e),e.some(function(t){try{e=t.__h,t.__h=[],e.some(function(e){e.call(t)})}catch(e){rS.__e(e,t.__v)}})}function rq(e,t,r){try{"function"==typeof e?e(t):e.current=t}catch(e){rS.__e(e,r)}}function rG(e,t,r){return this.constructor(e,r)}function rV(e,t){var r,n,i,o;r=e,rS.__&&rS.__(r,t),i=(n="function"==typeof rV)?null:rV&&rV.__k||t.__k,o=[],rB(t,r=(!n&&rV||t).__k=function(e,t,r){var n,i,o,a={};for(o in t)"key"==o?n=t[o]:"ref"==o?i=t[o]:a[o]=t[o];if(arguments.length>2&&(a.children=arguments.length>3?rE.call(arguments,2):r),"function"==typeof e&&null!=e.defaultProps)for(o in e.defaultProps)void 0===a[o]&&(a[o]=e.defaultProps[o]);return rI(e,a,n,i,null)}(rj,null,[r]),i||rT,rT,void 0!==t.ownerSVGElement,!n&&rV?[rV]:i?null:t.firstChild?rE.call(t.childNodes):null,o,!n&&rV?rV:i?i.__e:t.firstChild,n),rz(o,r)}rE=rR.slice,rS={__e:function(e,t,r,n){for(var i,o,a;t=t.__;)if((i=t.__c)&&!i.__)try{if((o=i.constructor)&&null!=o.getDerivedStateFromError&&(i.setState(o.getDerivedStateFromError(e)),a=i.__d),null!=i.componentDidCatch&&(i.componentDidCatch(e,n||{}),a=i.__d),a)return i.__E=i}catch(t){e=t}throw e}},rA=0,rH.prototype.setState=function(e,t){var r;r=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=rC({},this.state),"function"==typeof e&&(e=e(rC({},r),this.props)),e&&rC(r,e),null!=e&&this.__v&&(t&&this._sb.push(t),rL(this))},rH.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),rL(this))},rH.prototype.render=rj,rx=[],r$.__r=0;var rX=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|^--/i,rY=/^(area|base|br|col|embed|hr|img|input|link|meta|param|source|track|wbr)$/,rZ=/[\s\n\\/='"\0<>]/,rQ=/^xlink:?./,r0=/["&<]/;function r1(e){if(!1===r0.test(e+=""))return e;for(var t=0,r=0,n="",i="";r<e.length;r++){switch(e.charCodeAt(r)){case 34:i="&quot;";break;case 38:i="&amp;";break;case 60:i="&lt;";break;default:continue}r!==t&&(n+=e.slice(t,r)),n+=i,t=r+1}return r!==t&&(n+=e.slice(t,r)),n}var r2=function(e,t){return String(e).replace(/(\n+)/g,"$1"+(t||"	"))},r5=function(e,t,r){return String(e).length>(t||40)||!r&&-1!==String(e).indexOf("\n")||-1!==String(e).indexOf("<")},r3={},r6=/([A-Z])/g;function r4(e){var t="";for(var r in e){var n=e[r];null!=n&&""!==n&&(t&&(t+=" "),t+="-"==r[0]?r:r3[r]||(r3[r]=r.replace(r6,"-$1").toLowerCase()),t="number"==typeof n&&!1===rX.test(r)?t+": "+n+"px;":t+": "+n+";")}return t||void 0}function r8(e,t){return Array.isArray(t)?t.reduce(r8,e):null!=t&&!1!==t&&e.push(t),e}function r9(){this.__d=!0}function r7(e,t){return{__v:e,context:t,props:e.props,setState:r9,forceUpdate:r9,__d:!0,__h:[]}}function ne(e,t){var r=e.contextType,n=r&&t[r.__c];return null!=r?n?n.props.value:r.__:t}var nt=[],nr={shallow:!0};ni.render=ni;var nn=[];function ni(e,t,r){t=t||{};var n,i=rS.__s;return rS.__s=!0,n=r&&(r.pretty||r.voidElements||r.sortAttributes||r.shallow||r.allAttributes||r.xml||r.attributeHook)?function e(t,r,n,i,o,a){if(null==t||"boolean"==typeof t)return"";if("object"!=typeof t)return r1(t);var s=n.pretty,c=s&&"string"==typeof s?s:"	";if(Array.isArray(t)){for(var l="",u=0;u<t.length;u++)s&&u>0&&(l+="\n"),l+=e(t[u],r,n,i,o,a);return l}var d,p=t.type,f=t.props,h=!1;if("function"==typeof p){if(h=!0,!n.shallow||!i&&!1!==n.renderRootComponent){if(p===rj){var m=[];return r8(m,t.props.children),e(m,r,n,!1!==n.shallowHighOrder,o,a)}var y,w=t.__c=r7(t,r);rS.__b&&rS.__b(t);var b=rS.__r;if(p.prototype&&"function"==typeof p.prototype.render){var g=ne(p,r);(w=t.__c=new p(f,g)).__v=t,w._dirty=w.__d=!0,w.props=f,null==w.state&&(w.state={}),null==w._nextState&&null==w.__s&&(w._nextState=w.__s=w.state),w.context=g,p.getDerivedStateFromProps?w.state=Object.assign({},w.state,p.getDerivedStateFromProps(w.props,w.state)):w.componentWillMount&&(w.componentWillMount(),w.state=w._nextState!==w.state?w._nextState:w.__s!==w.state?w.__s:w.state),b&&b(t),y=w.render(w.props,w.state,w.context)}else for(var _=ne(p,r),v=0;w.__d&&v++<25;)w.__d=!1,b&&b(t),y=p.call(t.__c,f,_);return w.getChildContext&&(r=Object.assign({},r,w.getChildContext())),rS.diffed&&rS.diffed(t),e(y,r,n,!1!==n.shallowHighOrder,o,a)}p=(d=p).displayName||d!==Function&&d.name||function(e){var t=(Function.prototype.toString.call(e).match(/^\s*function\s+([^( ]+)/)||"")[1];if(!t){for(var r=-1,n=nt.length;n--;)if(nt[n]===e){r=n;break}r<0&&(r=nt.push(e)-1),t="UnnamedComponent"+r}return t}(d)}var k,E,S="<"+p;if(f){var A=Object.keys(f);n&&!0===n.sortAttributes&&A.sort();for(var x=0;x<A.length;x++){var P=A[x],T=f[P];if("children"!==P){if(!rZ.test(P)&&(n&&n.allAttributes||"key"!==P&&"ref"!==P&&"__self"!==P&&"__source"!==P)){if("defaultValue"===P)P="value";else if("defaultChecked"===P)P="checked";else if("defaultSelected"===P)P="selected";else if("className"===P){if(void 0!==f.class)continue;P="class"}else o&&rQ.test(P)&&(P=P.toLowerCase().replace(/^xlink:?/,"xlink:"));if("htmlFor"===P){if(f.for)continue;P="for"}"style"===P&&T&&"object"==typeof T&&(T=r4(T)),"a"===P[0]&&"r"===P[1]&&"boolean"==typeof T&&(T=String(T));var R=n.attributeHook&&n.attributeHook(P,T,r,n,h);if(R||""===R)S+=R;else if("dangerouslySetInnerHTML"===P)E=T&&T.__html;else if("textarea"===p&&"value"===P)k=T;else if((T||0===T||""===T)&&"function"!=typeof T){if(!(!0!==T&&""!==T||(T=P,n&&n.xml))){S=S+" "+P;continue}if("value"===P){if("select"===p){a=T;continue}"option"===p&&a==T&&void 0===f.selected&&(S+=" selected")}S=S+" "+P+'="'+r1(T)+'"'}}}else k=T}}if(s){var O=S.replace(/\n\s*/," ");O===S||~O.indexOf("\n")?s&&~S.indexOf("\n")&&(S+="\n"):S=O}if(S+=">",rZ.test(p))throw Error(p+" is not a valid HTML tag name in "+S);var C,U=rY.test(p)||n.voidElements&&n.voidElements.test(p),I=[];if(E)s&&r5(E)&&(E="\n"+c+r2(E,c)),S+=E;else if(null!=k&&r8(C=[],k).length){for(var j=s&&~S.indexOf("\n"),H=!1,N=0;N<C.length;N++){var L=C[N];if(null!=L&&!1!==L){var $=e(L,r,n,!0,"svg"===p||"foreignObject"!==p&&o,a);if(s&&!j&&r5($)&&(j=!0),$)if(s){var D=$.length>0&&"<"!=$[0];H&&D?I[I.length-1]+=$:I.push($),H=D}else I.push($)}}if(s&&j)for(var W=I.length;W--;)I[W]="\n"+c+r2(I[W],c)}if(I.length||E)S+=I.join("");else if(n&&n.xml)return S.substring(0,S.length-1)+" />";return!U||C||E?(s&&~S.indexOf("\n")&&(S+="\n"),S=S+"</"+p+">"):S=S.replace(/>$/," />"),S}(e,t,r):function e(t,r,n,i){if(null==t||!0===t||!1===t||""===t)return"";if("object"!=typeof t)return r1(t);if(no(t)){for(var o="",a=0;a<t.length;a++)o+=e(t[a],r,n,i);return o}rS.__b&&rS.__b(t);var s=t.type,c=t.props;if("function"==typeof s){if(s===rj)return e(t.props.children,r,n,i);var l,u,d,p,f,h=s.prototype&&"function"==typeof s.prototype.render?(l=r,d=ne(u=t.type,l),p=new u(t.props,d),t.__c=p,p.__v=t,p.__d=!0,p.props=t.props,null==p.state&&(p.state={}),null==p.__s&&(p.__s=p.state),p.context=d,u.getDerivedStateFromProps?p.state=na({},p.state,u.getDerivedStateFromProps(p.props,p.state)):p.componentWillMount&&(p.componentWillMount(),p.state=p.__s!==p.state?p.__s:p.state),(f=rS.__r)&&f(t),p.render(p.props,p.state,p.context)):function(e,t){var r,n=r7(e,t),i=ne(e.type,t);e.__c=n;for(var o=rS.__r,a=0;n.__d&&a++<25;)n.__d=!1,o&&o(e),r=e.type.call(n,e.props,i);return r}(t,r),m=t.__c;m.getChildContext&&(r=na({},r,m.getChildContext()));var y=e(h,r,n,i);return rS.diffed&&rS.diffed(t),y}var w,b,g="<";if(g+=s,c)for(var _ in w=c.children,c){var v,k,E,S=c[_];if(!("key"===_||"ref"===_||"__self"===_||"__source"===_||"children"===_||"className"===_&&"class"in c||"htmlFor"===_&&"for"in c||rZ.test(_))){if(k=_="className"===(v=_)?"class":"htmlFor"===v?"for":"defaultValue"===v?"value":"defaultChecked"===v?"checked":"defaultSelected"===v?"selected":n&&rQ.test(v)?v.toLowerCase().replace(/^xlink:?/,"xlink:"):v,E=S,S="style"===k&&null!=E&&"object"==typeof E?r4(E):"a"===k[0]&&"r"===k[1]&&"boolean"==typeof E?String(E):E,"dangerouslySetInnerHTML"===_)b=S&&S.__html;else if("textarea"===s&&"value"===_)w=S;else if((S||0===S||""===S)&&"function"!=typeof S){if(!0===S||""===S){S=_,g=g+" "+_;continue}if("value"===_){if("select"===s){i=S;continue}"option"!==s||i!=S||"selected"in c||(g+=" selected")}g=g+" "+_+'="'+r1(S)+'"'}}}var A=g;if(g+=">",rZ.test(s))throw Error(s+" is not a valid HTML tag name in "+g);var x="",P=!1;if(b)x+=b,P=!0;else if("string"==typeof w)x+=r1(w),P=!0;else if(no(w))for(var T=0;T<w.length;T++){var R=w[T];if(null!=R&&!1!==R){var O=e(R,r,"svg"===s||"foreignObject"!==s&&n,i);O&&(x+=O,P=!0)}}else if(null!=w&&!1!==w&&!0!==w){var C=e(w,r,"svg"===s||"foreignObject"!==s&&n,i);C&&(x+=C,P=!0)}if(rS.diffed&&rS.diffed(t),P)g+=x;else if(rY.test(s))return A+" />";return g+"</"+s+">"}(e,t,!1,void 0),rS.__c&&rS.__c(e,nn),rS.__s=i,nn.length=0,n}var no=Array.isArray,na=Object.assign;ni.shallowRender=function(e,t){return ni(e,t,nr)};var ns=0;function nc(e,t,r,n,i){var o,a,s={};for(a in t)"ref"==a?o=t[a]:s[a]=t[a];var c={type:e,props:s,key:r,ref:o,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:--ns,__source:i,__self:n};if("function"==typeof e&&(o=e.defaultProps))for(a in o)void 0===s[a]&&(s[a]=o[a]);return rS.vnode&&rS.vnode(c),c}async function nl(e,t){let r=window.SimpleWebAuthnBrowser;async function n(r){let n=new URL(`${e}/webauthn-options/${t}`);r&&n.searchParams.append("action",r),o().forEach(e=>{n.searchParams.append(e.name,e.value)});let i=await fetch(n);return i.ok?i.json():void console.error("Failed to fetch options",i)}function i(){let e=`#${t}-form`,r=document.querySelector(e);if(!r)throw Error(`Form '${e}' not found`);return r}function o(){return Array.from(i().querySelectorAll("input[data-form-field]"))}async function a(e,t){let r=i();if(e){let t=document.createElement("input");t.type="hidden",t.name="action",t.value=e,r.appendChild(t)}if(t){let e=document.createElement("input");e.type="hidden",e.name="data",e.value=JSON.stringify(t),r.appendChild(e)}return r.submit()}async function s(e,t){let n=await r.startAuthentication(e,t);return await a("authenticate",n)}async function c(e){o().forEach(e=>{if(e.required&&!e.value)throw Error(`Missing required field: ${e.name}`)});let t=await r.startRegistration(e);return await a("register",t)}async function l(){if(!r.browserSupportsWebAuthnAutofill())return;let e=await n("authenticate");if(!e)return void console.error("Failed to fetch option for autofill authentication");try{await s(e.options,!0)}catch(e){console.error(e)}}(async function(){let e=i();if(!r.browserSupportsWebAuthn()){e.style.display="none";return}e&&e.addEventListener("submit",async e=>{e.preventDefault();let t=await n(void 0);if(!t)return void console.error("Failed to fetch options for form submission");if("authenticate"===t.action)try{await s(t.options,!1)}catch(e){console.error(e)}else if("register"===t.action)try{await c(t.options)}catch(e){console.error(e)}})})(),l()}let nu={default:"Unable to sign in.",Signin:"Try signing in with a different account.",OAuthSignin:"Try signing in with a different account.",OAuthCallbackError:"Try signing in with a different account.",OAuthCreateAccount:"Try signing in with a different account.",EmailCreateAccount:"Try signing in with a different account.",Callback:"Try signing in with a different account.",OAuthAccountNotLinked:"To confirm your identity, sign in with the same account you used originally.",EmailSignin:"The e-mail could not be sent.",CredentialsSignin:"Sign in failed. Check the details you provided are correct.",SessionRequired:"Please sign in to access this page."},nd=`:root {
  --border-width: 1px;
  --border-radius: 0.5rem;
  --color-error: #c94b4b;
  --color-info: #157efb;
  --color-info-hover: #0f6ddb;
  --color-info-text: #fff;
}

.__next-auth-theme-auto,
.__next-auth-theme-light {
  --color-background: #ececec;
  --color-background-hover: rgba(236, 236, 236, 0.8);
  --color-background-card: #fff;
  --color-text: #000;
  --color-primary: #444;
  --color-control-border: #bbb;
  --color-button-active-background: #f9f9f9;
  --color-button-active-border: #aaa;
  --color-separator: #ccc;
}

.__next-auth-theme-dark {
  --color-background: #161b22;
  --color-background-hover: rgba(22, 27, 34, 0.8);
  --color-background-card: #0d1117;
  --color-text: #fff;
  --color-primary: #ccc;
  --color-control-border: #555;
  --color-button-active-background: #060606;
  --color-button-active-border: #666;
  --color-separator: #444;
}

@media (prefers-color-scheme: dark) {
  .__next-auth-theme-auto {
    --color-background: #161b22;
    --color-background-hover: rgba(22, 27, 34, 0.8);
    --color-background-card: #0d1117;
    --color-text: #fff;
    --color-primary: #ccc;
    --color-control-border: #555;
    --color-button-active-background: #060606;
    --color-button-active-border: #666;
    --color-separator: #444;
  }

  button,
  a.button {
    color: var(--provider-dark-color, var(--color-primary)) !important;
    background-color: var(
      --provider-dark-bg,
      var(--color-background)
    ) !important;
  }

    :is(button,a.button):hover {
      background-color: var(
        --provider-dark-bg-hover,
        var(--color-background-hover)
      ) !important;
    }

    :is(button,a.button) span {
      color: var(--provider-dark-bg) !important;
    }
}

html {
  box-sizing: border-box;
}

*,
*:before,
*:after {
  box-sizing: inherit;
  margin: 0;
  padding: 0;
}

body {
  background-color: var(--color-background);
  margin: 0;
  padding: 0;
  font-family:
    ui-sans-serif,
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Roboto,
    "Helvetica Neue",
    Arial,
    "Noto Sans",
    sans-serif,
    "Apple Color Emoji",
    "Segoe UI Emoji",
    "Segoe UI Symbol",
    "Noto Color Emoji";
}

h1 {
  margin-bottom: 1.5rem;
  padding: 0 1rem;
  font-weight: 400;
  color: var(--color-text);
}

p {
  margin-bottom: 1.5rem;
  padding: 0 1rem;
  color: var(--color-text);
}

form {
  margin: 0;
  padding: 0;
}

label {
  font-weight: 500;
  text-align: left;
  margin-bottom: 0.25rem;
  display: block;
  color: var(--color-text);
}

input[type] {
  box-sizing: border-box;
  display: block;
  width: 100%;
  padding: 0.5rem 1rem;
  border: var(--border-width) solid var(--color-control-border);
  background: var(--color-background-card);
  font-size: 1rem;
  border-radius: var(--border-radius);
  color: var(--color-text);
}

p {
  font-size: 1.1rem;
  line-height: 2rem;
}

a.button {
  text-decoration: none;
  line-height: 1rem;
}

a.button:link,
  a.button:visited {
    background-color: var(--color-background);
    color: var(--color-primary);
  }

button,
a.button {
  padding: 0.75rem 1rem;
  color: var(--provider-color, var(--color-primary));
  background-color: var(--provider-bg, var(--color-background));
  border: 1px solid #00000031;
  font-size: 0.9rem;
  height: 50px;
  border-radius: var(--border-radius);
  transition: background-color 250ms ease-in-out;
  font-weight: 300;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

:is(button,a.button):hover {
    background-color: var(--provider-bg-hover, var(--color-background-hover));
    cursor: pointer;
  }

:is(button,a.button):active {
    cursor: pointer;
  }

:is(button,a.button) span {
    color: #fff;
  }

#submitButton {
  color: var(--button-text-color, var(--color-info-text));
  background-color: var(--brand-color, var(--color-info));
  width: 100%;
}

#submitButton:hover {
    background-color: var(
      --button-hover-bg,
      var(--color-info-hover)
    ) !important;
  }

a.site {
  color: var(--color-primary);
  text-decoration: none;
  font-size: 1rem;
  line-height: 2rem;
}

a.site:hover {
    text-decoration: underline;
  }

.page {
  position: absolute;
  width: 100%;
  height: 100%;
  display: grid;
  place-items: center;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.page > div {
    text-align: center;
  }

.error a.button {
    padding-left: 2rem;
    padding-right: 2rem;
    margin-top: 0.5rem;
  }

.error .message {
    margin-bottom: 1.5rem;
  }

.signin input[type="text"] {
    margin-left: auto;
    margin-right: auto;
    display: block;
  }

.signin hr {
    display: block;
    border: 0;
    border-top: 1px solid var(--color-separator);
    margin: 2rem auto 1rem auto;
    overflow: visible;
  }

.signin hr::before {
      content: "or";
      background: var(--color-background-card);
      color: #888;
      padding: 0 0.4rem;
      position: relative;
      top: -0.7rem;
    }

.signin .error {
    background: #f5f5f5;
    font-weight: 500;
    border-radius: 0.3rem;
    background: var(--color-error);
  }

.signin .error p {
      text-align: left;
      padding: 0.5rem 1rem;
      font-size: 0.9rem;
      line-height: 1.2rem;
      color: var(--color-info-text);
    }

.signin > div,
  .signin form {
    display: block;
  }

.signin > div input[type], .signin form input[type] {
      margin-bottom: 0.5rem;
    }

.signin > div button, .signin form button {
      width: 100%;
    }

.signin .provider + .provider {
    margin-top: 1rem;
  }

.logo {
  display: inline-block;
  max-width: 150px;
  margin: 1.25rem 0;
  max-height: 70px;
}

.card {
  background-color: var(--color-background-card);
  border-radius: 1rem;
  padding: 1.25rem 2rem;
}

.card .header {
    color: var(--color-primary);
  }

.card input[type]::-moz-placeholder {
    color: color-mix(
      in srgb,
      var(--color-text) 20%,
      var(--color-button-active-background)
    );
  }

.card input[type]::placeholder {
    color: color-mix(
      in srgb,
      var(--color-text) 20%,
      var(--color-button-active-background)
    );
  }

.card input[type] {
    background: color-mix(in srgb, var(--color-background-card) 95%, black);
  }

.section-header {
  color: var(--color-text);
}

@media screen and (min-width: 450px) {
  .card {
    margin: 2rem 0;
    width: 368px;
  }
}

@media screen and (max-width: 450px) {
  .card {
    margin: 1rem 0;
    width: 343px;
  }
}
`;function np({html:e,title:t,status:r,cookies:n,theme:i,headTags:o}){return{cookies:n,status:r,headers:{"Content-Type":"text/html"},body:`<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width, initial-scale=1.0"><style>${nd}</style><title>${t}</title>${o??""}</head><body class="__next-auth-theme-${i?.colorScheme??"auto"}"><div class="page">${ni(e)}</div></body></html>`}}function nf(e){let{url:t,theme:r,query:n,cookies:i,pages:o,providers:a}=e;return{csrf:(e,t,r)=>e?(t.logger.warn("csrf-disabled"),r.push({name:t.cookies.csrfToken.name,value:"",options:{...t.cookies.csrfToken.options,maxAge:0}}),{status:404,cookies:r}):{headers:{"Content-Type":"application/json"},body:{csrfToken:t.csrfToken},cookies:r},providers:e=>({headers:{"Content-Type":"application/json"},body:e.reduce((e,{id:t,name:r,type:n,signinUrl:i,callbackUrl:o})=>(e[t]={id:t,name:r,type:n,signinUrl:i,callbackUrl:o},e),{})}),signin(t,s){if(t)throw new L("Unsupported action");if(o?.signIn){let t=`${o.signIn}${o.signIn.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:e.callbackUrl??"/"})}`;return s&&(t=`${t}&${new URLSearchParams({error:s})}`),{redirect:t,cookies:i}}let c=a?.find(e=>"webauthn"===e.type&&e.enableConditionalUI&&!!e.simpleWebAuthnBrowserVersion),l="";if(c){let{simpleWebAuthnBrowserVersion:e}=c;l=`<script src="https://unpkg.com/@simplewebauthn/browser@${e}/dist/bundle/index.umd.min.js" crossorigin="anonymous"></script>`}return np({cookies:i,theme:r,html:function(e){let{csrfToken:t,providers:r=[],callbackUrl:n,theme:i,email:o,error:a}=e;"undefined"!=typeof document&&i?.brandColor&&document.documentElement.style.setProperty("--brand-color",i.brandColor),"undefined"!=typeof document&&i?.buttonText&&document.documentElement.style.setProperty("--button-text-color",i.buttonText);let s=a&&(nu[a]??nu.default),c=r.find(e=>"webauthn"===e.type&&e.enableConditionalUI)?.id;return nc("div",{className:"signin",children:[i?.brandColor&&nc("style",{dangerouslySetInnerHTML:{__html:`:root {--brand-color: ${i.brandColor}}`}}),i?.buttonText&&nc("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --button-text-color: ${i.buttonText}
        }
      `}}),nc("div",{className:"card",children:[s&&nc("div",{className:"error",children:nc("p",{children:s})}),i?.logo&&nc("img",{src:i.logo,alt:"Logo",className:"logo"}),r.map((e,i)=>{let a,s,c;("oauth"===e.type||"oidc"===e.type)&&({bg:a="#fff",brandColor:s,logo:c=`https://authjs.dev/img/providers/${e.id}.svg`}=e.style??{});let l=s??a??"#fff";return nc("div",{className:"provider",children:["oauth"===e.type||"oidc"===e.type?nc("form",{action:e.signinUrl,method:"POST",children:[nc("input",{type:"hidden",name:"csrfToken",value:t}),n&&nc("input",{type:"hidden",name:"callbackUrl",value:n}),nc("button",{type:"submit",className:"button",style:{"--provider-bg":"#fff","--provider-bg-hover":`color-mix(in srgb, ${l} 30%, #fff)`,"--provider-dark-bg":"#161b22","--provider-dark-bg-hover":`color-mix(in srgb, ${l} 30%, #000)`},tabIndex:0,children:[nc("span",{style:{filter:"invert(1) grayscale(1) brightness(1.3) contrast(9000)","mix-blend-mode":"luminosity",opacity:.95},children:["Sign in with ",e.name]}),c&&nc("img",{loading:"lazy",height:24,src:c})]})]}):null,("email"===e.type||"credentials"===e.type||"webauthn"===e.type)&&i>0&&"email"!==r[i-1].type&&"credentials"!==r[i-1].type&&"webauthn"!==r[i-1].type&&nc("hr",{}),"email"===e.type&&nc("form",{action:e.signinUrl,method:"POST",children:[nc("input",{type:"hidden",name:"csrfToken",value:t}),nc("label",{className:"section-header",htmlFor:`input-email-for-${e.id}-provider`,children:"Email"}),nc("input",{id:`input-email-for-${e.id}-provider`,autoFocus:!0,type:"email",name:"email",value:o,placeholder:"<EMAIL>",required:!0}),nc("button",{id:"submitButton",type:"submit",tabIndex:0,children:["Sign in with ",e.name]})]}),"credentials"===e.type&&nc("form",{action:e.callbackUrl,method:"POST",children:[nc("input",{type:"hidden",name:"csrfToken",value:t}),Object.keys(e.credentials).map(t=>nc("div",{children:[nc("label",{className:"section-header",htmlFor:`input-${t}-for-${e.id}-provider`,children:e.credentials[t].label??t}),nc("input",{name:t,id:`input-${t}-for-${e.id}-provider`,type:e.credentials[t].type??"text",placeholder:e.credentials[t].placeholder??"",...e.credentials[t]})]},`input-group-${e.id}`)),nc("button",{id:"submitButton",type:"submit",tabIndex:0,children:["Sign in with ",e.name]})]}),"webauthn"===e.type&&nc("form",{action:e.callbackUrl,method:"POST",id:`${e.id}-form`,children:[nc("input",{type:"hidden",name:"csrfToken",value:t}),Object.keys(e.formFields).map(t=>nc("div",{children:[nc("label",{className:"section-header",htmlFor:`input-${t}-for-${e.id}-provider`,children:e.formFields[t].label??t}),nc("input",{name:t,"data-form-field":!0,id:`input-${t}-for-${e.id}-provider`,type:e.formFields[t].type??"text",placeholder:e.formFields[t].placeholder??"",...e.formFields[t]})]},`input-group-${e.id}`)),nc("button",{id:`submitButton-${e.id}`,type:"submit",tabIndex:0,children:["Sign in with ",e.name]})]}),("email"===e.type||"credentials"===e.type||"webauthn"===e.type)&&i+1<r.length&&nc("hr",{})]},e.id)})]}),c&&nc(rj,{children:nc("script",{dangerouslySetInnerHTML:{__html:`
const currentURL = window.location.href;
const authURL = currentURL.substring(0, currentURL.lastIndexOf('/'));
(${nl})(authURL, "${c}");
`}})})]})}({csrfToken:e.csrfToken,providers:e.providers?.filter(e=>["email","oauth","oidc"].includes(e.type)||"credentials"===e.type&&e.credentials||"webauthn"===e.type&&e.formFields||!1),callbackUrl:e.callbackUrl,theme:e.theme,error:s,...n}),title:"Sign In",headTags:l})},signout:()=>o?.signOut?{redirect:o.signOut,cookies:i}:np({cookies:i,theme:r,html:function(e){let{url:t,csrfToken:r,theme:n}=e;return nc("div",{className:"signout",children:[n?.brandColor&&nc("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${n.brandColor}
        }
      `}}),n?.buttonText&&nc("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --button-text-color: ${n.buttonText}
        }
      `}}),nc("div",{className:"card",children:[n?.logo&&nc("img",{src:n.logo,alt:"Logo",className:"logo"}),nc("h1",{children:"Signout"}),nc("p",{children:"Are you sure you want to sign out?"}),nc("form",{action:t?.toString(),method:"POST",children:[nc("input",{type:"hidden",name:"csrfToken",value:r}),nc("button",{id:"submitButton",type:"submit",children:"Sign out"})]})]})]})}({csrfToken:e.csrfToken,url:t,theme:r}),title:"Sign Out"}),verifyRequest:e=>o?.verifyRequest?{redirect:o.verifyRequest,cookies:i}:np({cookies:i,theme:r,html:function(e){let{url:t,theme:r}=e;return nc("div",{className:"verify-request",children:[r.brandColor&&nc("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${r.brandColor}
        }
      `}}),nc("div",{className:"card",children:[r.logo&&nc("img",{src:r.logo,alt:"Logo",className:"logo"}),nc("h1",{children:"Check your email"}),nc("p",{children:"A sign in link has been sent to your email address."}),nc("p",{children:nc("a",{className:"site",href:t.origin,children:t.host})})]})]})}({url:t,theme:r,...e}),title:"Verify Request"}),error:e=>o?.error?{redirect:`${o.error}${o.error.includes("?")?"&":"?"}error=${e}`,cookies:i}:np({cookies:i,theme:r,...function(e){let{url:t,error:r="default",theme:n}=e,i=`${t}/signin`,o={default:{status:200,heading:"Error",message:nc("p",{children:nc("a",{className:"site",href:t?.origin,children:t?.host})})},Configuration:{status:500,heading:"Server error",message:nc("div",{children:[nc("p",{children:"There is a problem with the server configuration."}),nc("p",{children:"Check the server logs for more information."})]})},AccessDenied:{status:403,heading:"Access Denied",message:nc("div",{children:[nc("p",{children:"You do not have permission to sign in."}),nc("p",{children:nc("a",{className:"button",href:i,children:"Sign in"})})]})},Verification:{status:403,heading:"Unable to sign in",message:nc("div",{children:[nc("p",{children:"The sign in link is no longer valid."}),nc("p",{children:"It may have been used already or it may have expired."})]}),signin:nc("a",{className:"button",href:i,children:"Sign in"})}},{status:a,heading:s,message:c,signin:l}=o[r]??o.default;return{status:a,html:nc("div",{className:"error",children:[n?.brandColor&&nc("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${n?.brandColor}
        }
      `}}),nc("div",{className:"card",children:[n?.logo&&nc("img",{src:n?.logo,alt:"Logo",className:"logo"}),nc("h1",{children:s}),nc("div",{className:"message",children:c}),l]})]})}}({url:t,theme:r,error:e}),title:"Error"})}}function nh(e,t=Date.now()){return new Date(t+1e3*e)}async function nm(e,t,r,n){if(!r?.providerAccountId||!r.type)throw Error("Missing or invalid provider account");if(!["email","oauth","oidc","webauthn"].includes(r.type))throw Error("Provider not supported");let{adapter:i,jwt:o,events:a,session:{strategy:s,generateSessionToken:c}}=n;if(!i)return{user:t,account:r};let l=r,{createUser:u,updateUser:d,getUser:p,getUserByAccount:f,getUserByEmail:h,linkAccount:m,createSession:y,getSessionAndUser:w,deleteSession:b}=i,g=null,_=null,v=!1,k="jwt"===s;if(e)if(k)try{let t=n.cookies.sessionToken.name;(g=await o.decode({...o,token:e,salt:t}))&&"sub"in g&&g.sub&&(_=await p(g.sub))}catch{}else{let t=await w(e);t&&(g=t.session,_=t.user)}if("email"===l.type){let r=await h(t.email);return r?(_?.id!==r.id&&!k&&e&&await b(e),_=await d({id:r.id,emailVerified:new Date}),await a.updateUser?.({user:_})):(_=await u({...t,emailVerified:new Date}),await a.createUser?.({user:_}),v=!0),{session:g=k?{}:await y({sessionToken:c(),userId:_.id,expires:nh(n.session.maxAge)}),user:_,isNewUser:v}}if("webauthn"===l.type){let e=await f({providerAccountId:l.providerAccountId,provider:l.provider});if(e){if(_){if(e.id===_.id){let e={...l,userId:_.id};return{session:g,user:_,isNewUser:v,account:e}}throw new q("The account is already associated with another user",{provider:l.provider})}g=k?{}:await y({sessionToken:c(),userId:e.id,expires:nh(n.session.maxAge)});let t={...l,userId:e.id};return{session:g,user:e,isNewUser:v,account:t}}{if(_){await m({...l,userId:_.id}),await a.linkAccount?.({user:_,account:l,profile:t});let e={...l,userId:_.id};return{session:g,user:_,isNewUser:v,account:e}}if(t.email?await h(t.email):null)throw new q("Another account already exists with the same e-mail address",{provider:l.provider});_=await u({...t}),await a.createUser?.({user:_}),await m({...l,userId:_.id}),await a.linkAccount?.({user:_,account:l,profile:t}),g=k?{}:await y({sessionToken:c(),userId:_.id,expires:nh(n.session.maxAge)});let e={...l,userId:_.id};return{session:g,user:_,isNewUser:!0,account:e}}}let E=await f({providerAccountId:l.providerAccountId,provider:l.provider});if(E){if(_){if(E.id===_.id)return{session:g,user:_,isNewUser:v};throw new O("The account is already associated with another user",{provider:l.provider})}return{session:g=k?{}:await y({sessionToken:c(),userId:E.id,expires:nh(n.session.maxAge)}),user:E,isNewUser:v}}{let{provider:e}=n,{type:r,provider:i,providerAccountId:o,userId:s,...d}=l;if(l=Object.assign(e.account(d)??{},{providerAccountId:o,provider:i,type:r,userId:s}),_)return await m({...l,userId:_.id}),await a.linkAccount?.({user:_,account:l,profile:t}),{session:g,user:_,isNewUser:v};let p=t.email?await h(t.email):null;if(p){let e=n.provider;if(e?.allowDangerousEmailAccountLinking)_=p,v=!1;else throw new O("Another account already exists with the same e-mail address",{provider:l.provider})}else _=await u({...t,emailVerified:null}),v=!0;return await a.createUser?.({user:_}),await m({...l,userId:_.id}),await a.linkAccount?.({user:_,account:l,profile:t}),{session:g=k?{}:await y({sessionToken:c(),userId:_.id,expires:nh(n.session.maxAge)}),user:_,isNewUser:v}}}function ny(e,t){if(null==e)return!1;try{return e instanceof t||Object.getPrototypeOf(e)[Symbol.toStringTag]===t.prototype[Symbol.toStringTag]}catch{return!1}}"undefined"!=typeof navigator&&navigator.userAgent?.startsWith?.("Mozilla/5.0 ")||(o="oauth4webapi/v3.5.5");let nw="ERR_INVALID_ARG_VALUE",nb="ERR_INVALID_ARG_TYPE";function ng(e,t,r){let n=TypeError(e,{cause:r});return Object.assign(n,{code:t}),n}let n_=Symbol(),nv=Symbol(),nk=Symbol(),nE=Symbol(),nS=Symbol(),nA=Symbol(),nx=Symbol(),nP=new TextEncoder,nT=new TextDecoder;function nR(e){return"string"==typeof e?nP.encode(e):nT.decode(e)}function nO(e){return"string"==typeof e?s(e):a(e)}a=Uint8Array.prototype.toBase64?e=>(e instanceof ArrayBuffer&&(e=new Uint8Array(e)),e.toBase64({alphabet:"base64url",omitPadding:!0})):e=>{e instanceof ArrayBuffer&&(e=new Uint8Array(e));let t=[];for(let r=0;r<e.byteLength;r+=32768)t.push(String.fromCharCode.apply(null,e.subarray(r,r+32768)));return btoa(t.join("")).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")},s=Uint8Array.fromBase64?e=>{try{return Uint8Array.fromBase64(e,{alphabet:"base64url"})}catch(e){throw ng("The input to be decoded is not correctly encoded.",nw,e)}}:e=>{try{let t=atob(e.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"")),r=new Uint8Array(t.length);for(let e=0;e<t.length;e++)r[e]=t.charCodeAt(e);return r}catch(e){throw ng("The input to be decoded is not correctly encoded.",nw,e)}};class nC extends Error{code;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=iJ,Error.captureStackTrace?.(this,this.constructor)}}class nU extends Error{code;constructor(e,t){super(e,t),this.name=this.constructor.name,t?.code&&(this.code=t?.code),Error.captureStackTrace?.(this,this.constructor)}}function nI(e,t,r){return new nU(e,{code:t,cause:r})}function nj(e,t){if(!(e instanceof CryptoKey))throw ng(`${t} must be a CryptoKey`,nb)}function nH(e,t){if(nj(e,t),"private"!==e.type)throw ng(`${t} must be a private CryptoKey`,nw)}function nN(e){return!(null===e||"object"!=typeof e||Array.isArray(e))}function nL(e){ny(e,Headers)&&(e=Object.fromEntries(e.entries()));let t=new Headers(e??{});if(o&&!t.has("user-agent")&&t.set("user-agent",o),t.has("authorization"))throw ng('"options.headers" must not include the "authorization" header name',nw);return t}function n$(e){if("function"==typeof e&&(e=e()),!(e instanceof AbortSignal))throw ng('"options.signal" must return or be an instance of AbortSignal',nb);return e}function nD(e){return e.includes("//")?e.replace("//","/"):e}async function nW(e,t,r,n){if(!(e instanceof URL))throw ng(`"${t}" must be an instance of URL`,nb);n4(e,n?.[n_]!==!0);let i=r(new URL(e.href)),o=nL(n?.headers);return o.set("accept","application/json"),(n?.[nE]||fetch)(i.href,{body:void 0,headers:Object.fromEntries(o.entries()),method:"GET",redirect:"manual",signal:n?.signal?n$(n.signal):void 0})}async function nM(e,t){return nW(e,"issuerIdentifier",e=>{switch(t?.algorithm){case void 0:case"oidc":e.pathname=nD(`${e.pathname}/.well-known/openid-configuration`);break;case"oauth2":!function(e,t,r=!1){"/"===e.pathname?e.pathname=t:e.pathname=nD(`${t}/${r?e.pathname:e.pathname.replace(/(\/)$/,"")}`)}(e,".well-known/oauth-authorization-server");break;default:throw ng('"options.algorithm" must be "oidc" (default), or "oauth2"',nw)}return e},t)}function nK(e,t,r,n,i){try{if("number"!=typeof e||!Number.isFinite(e))throw ng(`${r} must be a number`,nb,i);if(e>0)return;if(t){if(0!==e)throw ng(`${r} must be a non-negative number`,nw,i);return}throw ng(`${r} must be a positive number`,nw,i)}catch(e){if(n)throw nI(e.message,n,i);throw e}}function nJ(e,t,r,n){try{if("string"!=typeof e)throw ng(`${t} must be a string`,nb,n);if(0===e.length)throw ng(`${t} must not be empty`,nw,n)}catch(e){if(r)throw nI(e.message,r,n);throw e}}async function nF(e,t){if(!(e instanceof URL)&&e!==oy)throw ng('"expectedIssuerIdentifier" must be an instance of URL',nb);if(!ny(t,Response))throw ng('"response" must be an instance of Response',nb);if(200!==t.status)throw nI('"response" is not a conform Authorization Server Metadata response (unexpected HTTP status code)',iX,t);i4(t);let r=await om(t);if(nJ(r.issuer,'"response" body "issuer" property',iq,{body:r}),e!==oy&&new URL(r.issuer).href!==e.href)throw nI('"response" body "issuer" property does not match the expected value',i1,{expected:e.href,body:r,attribute:"issuer"});return r}function nB(e){var t=e,r="application/json";if(ib(t)!==r)throw nz(t,r)}function nz(e,...t){let r='"response" content-type must be ';if(t.length>2){let e=t.pop();r+=`${t.join(", ")}, or ${e}`}else 2===t.length?r+=`${t[0]} or ${t[1]}`:r+=t[0];return nI(r,iV,e)}function nq(){return nO(crypto.getRandomValues(new Uint8Array(32)))}async function nG(e){return nJ(e,"codeVerifier"),nO(await crypto.subtle.digest("SHA-256",nR(e)))}function nV(e){switch(e.algorithm.name){case"RSA-PSS":switch(e.algorithm.hash.name){case"SHA-256":return"PS256";case"SHA-384":return"PS384";case"SHA-512":return"PS512";default:throw new nC("unsupported RsaHashedKeyAlgorithm hash name",{cause:e})}case"RSASSA-PKCS1-v1_5":switch(e.algorithm.hash.name){case"SHA-256":return"RS256";case"SHA-384":return"RS384";case"SHA-512":return"RS512";default:throw new nC("unsupported RsaHashedKeyAlgorithm hash name",{cause:e})}case"ECDSA":switch(e.algorithm.namedCurve){case"P-256":return"ES256";case"P-384":return"ES384";case"P-521":return"ES512";default:throw new nC("unsupported EcKeyAlgorithm namedCurve",{cause:e})}case"Ed25519":case"EdDSA":return"Ed25519";default:throw new nC("unsupported CryptoKey algorithm name",{cause:e})}}function nX(e){let t=e?.[nv];return"number"==typeof t&&Number.isFinite(t)?t:0}function nY(e){let t=e?.[nk];return"number"==typeof t&&Number.isFinite(t)&&-1!==Math.sign(t)?t:30}function nZ(){return Math.floor(Date.now()/1e3)}function nQ(e){if("object"!=typeof e||null===e)throw ng('"as" must be an object',nb);nJ(e.issuer,'"as.issuer"')}function n0(e){if("object"!=typeof e||null===e)throw ng('"client" must be an object',nb);nJ(e.client_id,'"client.client_id"')}function n1(e,t){let r=nZ()+nX(t);return{jti:nq(),aud:e.issuer,exp:r+60,iat:r,nbf:r,iss:t.client_id,sub:t.client_id}}async function n2(e,t,r){if(!r.usages.includes("sign"))throw ng('CryptoKey instances used for signing assertions must include "sign" in their "usages"',nw);let n=`${nO(nR(JSON.stringify(e)))}.${nO(nR(JSON.stringify(t)))}`,i=nO(await crypto.subtle.sign(ot(r),r,nR(n)));return`${n}.${i}`}async function n5(e){let{kty:t,e:r,n,x:i,y:o,crv:a}=await crypto.subtle.exportKey("jwk",e),s={kty:t,e:r,n,x:i,y:o,crv:a};return c.set(e,s),s}async function n3(e){return(c||=new WeakMap).get(e)||n5(e)}let n6=URL.parse?(e,t)=>URL.parse(e,t):(e,t)=>{try{return new URL(e,t)}catch{return null}};function n4(e,t){if(t&&"https:"!==e.protocol)throw nI("only requests to HTTPS are allowed",iY,e);if("https:"!==e.protocol&&"http:"!==e.protocol)throw nI("only HTTP and HTTPS requests are allowed",iZ,e)}function n8(e,t,r,n){let i;if("string"!=typeof e||!(i=n6(e)))throw nI(`authorization server metadata does not contain a valid ${r?`"as.mtls_endpoint_aliases.${t}"`:`"as.${t}"`}`,void 0===e?i5:i3,{attribute:r?`mtls_endpoint_aliases.${t}`:t});return n4(i,n),i}function n9(e,t,r,n){return r&&e.mtls_endpoint_aliases&&t in e.mtls_endpoint_aliases?n8(e.mtls_endpoint_aliases[t],t,r,n):n8(e[t],t,r,n)}class n7 extends Error{cause;code;error;status;error_description;response;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=iK,this.cause=t.cause,this.error=t.cause.error,this.status=t.response.status,this.error_description=t.cause.error_description,Object.defineProperty(this,"response",{enumerable:!1,value:t.response}),Error.captureStackTrace?.(this,this.constructor)}}class ie extends Error{cause;code;error;error_description;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=iF,this.cause=t.cause,this.error=t.cause.get("error"),this.error_description=t.cause.get("error_description")??void 0,Error.captureStackTrace?.(this,this.constructor)}}class it extends Error{cause;code;response;status;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=iM,this.cause=t.cause,this.status=t.response.status,this.response=t.response,Object.defineProperty(this,"response",{enumerable:!1}),Error.captureStackTrace?.(this,this.constructor)}}let ir="[a-zA-Z0-9!#$%&\\'\\*\\+\\-\\.\\^_`\\|~]+",ii=RegExp("^[,\\s]*("+ir+")\\s(.*)"),io=RegExp("^[,\\s]*("+ir+')\\s*=\\s*"((?:[^"\\\\]|\\\\.)*)"[,\\s]*(.*)'),ia=RegExp("^[,\\s]*"+("("+ir+")\\s*=\\s*(")+ir+")[,\\s]*(.*)"),is=RegExp("^([a-zA-Z0-9\\-\\._\\~\\+\\/]+[=]{0,2})(?:$|[,\\s])(.*)");async function ic(e){if(e.status>399&&e.status<500){i4(e),nB(e);try{let t=await e.clone().json();if(nN(t)&&"string"==typeof t.error&&t.error.length)return t}catch{}}}async function il(e,t,r){if(e.status!==t){let t;if(t=await ic(e))throw await e.body?.cancel(),new n7("server responded with an error in the response body",{cause:t,response:e});throw nI(`"response" is not a conform ${r} response (unexpected HTTP status code)`,iX,e)}}function iu(e){if(!iC.has(e))throw ng('"options.DPoP" is not a valid DPoPHandle',nw)}async function id(e,t,r,n,i,o){if(nJ(e,'"accessToken"'),!(r instanceof URL))throw ng('"url" must be an instance of URL',nb);n4(r,o?.[n_]!==!0),n=nL(n),o?.DPoP&&(iu(o.DPoP),await o.DPoP.addProof(r,n,t.toUpperCase(),e)),n.set("authorization",`${n.has("dpop")?"DPoP":"Bearer"} ${e}`);let a=await (o?.[nE]||fetch)(r.href,{body:i,headers:Object.fromEntries(n.entries()),method:t,redirect:"manual",signal:o?.signal?n$(o.signal):void 0});return o?.DPoP?.cacheNonce(a),a}async function ip(e,t,r,n){nQ(e),n0(t);let i=n9(e,"userinfo_endpoint",t.use_mtls_endpoint_aliases,n?.[n_]!==!0),o=nL(n?.headers);return t.userinfo_signed_response_alg?o.set("accept","application/jwt"):(o.set("accept","application/json"),o.append("accept","application/jwt")),id(r,"GET",i,o,null,{...n,[nv]:nX(t)})}function ih(e,t,r,n){(l||=new WeakMap).set(e,{jwks:t,uat:r,get age(){return nZ()-this.uat}}),n&&Object.assign(n,{jwks:structuredClone(t),uat:r})}function im(e,t){l?.delete(e),delete t?.jwks,delete t?.uat}async function iy(e,t,r){var n;let i,o,a,{alg:s,kid:c}=r;if(function(e){if(!i7(e.alg))throw new nC('unsupported JWS "alg" identifier',{cause:{alg:e.alg}})}(r),!l?.has(e)&&!("object"!=typeof(n=t?.[nx])||null===n||!("uat"in n)||"number"!=typeof n.uat||nZ()-n.uat>=300)&&"jwks"in n&&nN(n.jwks)&&Array.isArray(n.jwks.keys)&&Array.prototype.every.call(n.jwks.keys,nN)&&ih(e,t?.[nx].jwks,t?.[nx].uat),l?.has(e)){if({jwks:i,age:o}=l.get(e),o>=300)return im(e,t?.[nx]),iy(e,t,r)}else i=await i8(e,t).then(i9),o=0,ih(e,i,nZ(),t?.[nx]);switch(s.slice(0,2)){case"RS":case"PS":a="RSA";break;case"ES":a="EC";break;case"Ed":a="OKP";break;default:throw new nC("unsupported JWS algorithm",{cause:{alg:s}})}let u=i.keys.filter(e=>{if(e.kty!==a||void 0!==c&&c!==e.kid||void 0!==e.alg&&s!==e.alg||void 0!==e.use&&"sig"!==e.use||e.key_ops?.includes("verify")===!1)return!1;switch(!0){case"ES256"===s&&"P-256"!==e.crv:case"ES384"===s&&"P-384"!==e.crv:case"ES512"===s&&"P-521"!==e.crv:case"Ed25519"===s&&"Ed25519"!==e.crv:case"EdDSA"===s&&"Ed25519"!==e.crv:return!1}return!0}),{0:d,length:p}=u;if(!p){if(o>=60)return im(e,t?.[nx]),iy(e,t,r);throw nI("error when selecting a JWT verification key, no applicable keys found",i2,{header:r,candidates:u,jwks_uri:new URL(e.jwks_uri)})}if(1!==p)throw nI('error when selecting a JWT verification key, multiple applicable keys found, a "kid" JWT Header Parameter is required',i2,{header:r,candidates:u,jwks_uri:new URL(e.jwks_uri)});return of(s,d)}let iw=Symbol();function ib(e){return e.headers.get("content-type")?.split(";")[0]}async function ig(e,t,r,n,i){let o;if(nQ(e),n0(t),!ny(n,Response))throw ng('"response" must be an instance of Response',nb);if(ix(n),200!==n.status)throw nI('"response" is not a conform UserInfo Endpoint response (unexpected HTTP status code)',iX,n);if(i4(n),"application/jwt"===ib(n)){let{claims:r,jwt:a}=await on(await n.text(),oc.bind(void 0,t.userinfo_signed_response_alg,e.userinfo_signing_alg_values_supported,void 0),nX(t),nY(t),i?.[nA]).then(iP.bind(void 0,t.client_id)).then(iR.bind(void 0,e));iE.set(n,a),o=r}else{if(t.userinfo_signed_response_alg)throw nI("JWT UserInfo Response expected",iB,n);o=await om(n)}if(nJ(o.sub,'"response" body "sub" property',iq,{body:o}),r===iw);else if(nJ(r,'"expectedSubject"'),o.sub!==r)throw nI('unexpected "response" body "sub" property value',i1,{expected:r,body:o,attribute:"sub"});return o}async function i_(e,t,r,n,i,o,a){return await r(e,t,i,o),o.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"),(a?.[nE]||fetch)(n.href,{body:i,headers:Object.fromEntries(o.entries()),method:"POST",redirect:"manual",signal:a?.signal?n$(a.signal):void 0})}async function iv(e,t,r,n,i,o){let a=n9(e,"token_endpoint",t.use_mtls_endpoint_aliases,o?.[n_]!==!0);i.set("grant_type",n);let s=nL(o?.headers);s.set("accept","application/json"),o?.DPoP!==void 0&&(iu(o.DPoP),await o.DPoP.addProof(a,s,"POST"));let c=await i_(e,t,r,a,i,s,o);return o?.DPoP?.cacheNonce(c),c}let ik=new WeakMap,iE=new WeakMap;function iS(e){if(!e.id_token)return;let t=ik.get(e);if(!t)throw ng('"ref" was already garbage collected or did not resolve from the proper sources',nw);return t}async function iA(e,t,r,n,i){if(nQ(e),n0(t),!ny(r,Response))throw ng('"response" must be an instance of Response',nb);ix(r),await il(r,200,"Token Endpoint"),i4(r);let o=await om(r);if(nJ(o.access_token,'"response" body "access_token" property',iq,{body:o}),nJ(o.token_type,'"response" body "token_type" property',iq,{body:o}),o.token_type=o.token_type.toLowerCase(),"dpop"!==o.token_type&&"bearer"!==o.token_type)throw new nC("unsupported `token_type` value",{cause:{body:o}});if(void 0!==o.expires_in){let e="number"!=typeof o.expires_in?parseFloat(o.expires_in):o.expires_in;nK(e,!1,'"response" body "expires_in" property',iq,{body:o}),o.expires_in=e}if(void 0!==o.refresh_token&&nJ(o.refresh_token,'"response" body "refresh_token" property',iq,{body:o}),void 0!==o.scope&&"string"!=typeof o.scope)throw nI('"response" body "scope" property must be a string',iq,{body:o});if(void 0!==o.id_token){nJ(o.id_token,'"response" body "id_token" property',iq,{body:o});let a=["aud","exp","iat","iss","sub"];!0===t.require_auth_time&&a.push("auth_time"),void 0!==t.default_max_age&&(nK(t.default_max_age,!1,'"client.default_max_age"'),a.push("auth_time")),n?.length&&a.push(...n);let{claims:s,jwt:c}=await on(o.id_token,oc.bind(void 0,t.id_token_signed_response_alg,e.id_token_signing_alg_values_supported,"RS256"),nX(t),nY(t),i?.[nA]).then(iH.bind(void 0,a)).then(iO.bind(void 0,e)).then(iT.bind(void 0,t.client_id));if(Array.isArray(s.aud)&&1!==s.aud.length){if(void 0===s.azp)throw nI('ID Token "aud" (audience) claim includes additional untrusted audiences',i0,{claims:s,claim:"aud"});if(s.azp!==t.client_id)throw nI('unexpected ID Token "azp" (authorized party) claim value',i0,{expected:t.client_id,claims:s,claim:"azp"})}void 0!==s.auth_time&&nK(s.auth_time,!1,'ID Token "auth_time" (authentication time)',iq,{claims:s}),iE.set(r,c),ik.set(o,s)}return o}function ix(e){let t;if(t=function(e){if(!ny(e,Response))throw ng('"response" must be an instance of Response',nb);let t=e.headers.get("www-authenticate");if(null===t)return;let r=[],n=t;for(;n;){let e,t=n.match(ii),i=t?.["1"].toLowerCase();if(n=t?.["2"],!i)return;let o={};for(;n;){let r,i;if(t=n.match(io)){if([,r,i,n]=t,i.includes("\\"))try{i=JSON.parse(`"${i}"`)}catch{}o[r.toLowerCase()]=i;continue}if(t=n.match(ia)){[,r,i,n]=t,o[r.toLowerCase()]=i;continue}if(t=n.match(is)){if(Object.keys(o).length)break;[,e,n]=t;break}return}let a={scheme:i,parameters:o};e&&(a.token68=e),r.push(a)}if(r.length)return r}(e))throw new it("server responded with a challenge in the WWW-Authenticate HTTP Header",{cause:t,response:e})}function iP(e,t){return void 0!==t.claims.aud?iT(e,t):t}function iT(e,t){if(Array.isArray(t.claims.aud)){if(!t.claims.aud.includes(e))throw nI('unexpected JWT "aud" (audience) claim value',i0,{expected:e,claims:t.claims,claim:"aud"})}else if(t.claims.aud!==e)throw nI('unexpected JWT "aud" (audience) claim value',i0,{expected:e,claims:t.claims,claim:"aud"});return t}function iR(e,t){return void 0!==t.claims.iss?iO(e,t):t}function iO(e,t){let r=e[ow]?.(t)??e.issuer;if(t.claims.iss!==r)throw nI('unexpected JWT "iss" (issuer) claim value',i0,{expected:r,claims:t.claims,claim:"iss"});return t}let iC=new WeakSet,iU=Symbol();async function iI(e,t,r,n,i,o,a){if(nQ(e),n0(t),!iC.has(n))throw ng('"callbackParameters" must be an instance of URLSearchParams obtained from "validateAuthResponse()", or "validateJwtAuthResponse()',nw);nJ(i,'"redirectUri"');let s=ol(n,"code");if(!s)throw nI('no authorization code in "callbackParameters"',iq);let c=new URLSearchParams(a?.additionalParameters);return c.set("redirect_uri",i),c.set("code",s),o!==iU&&(nJ(o,'"codeVerifier"'),c.set("code_verifier",o)),iv(e,t,r,"authorization_code",c,a)}let ij={aud:"audience",c_hash:"code hash",client_id:"client id",exp:"expiration time",iat:"issued at",iss:"issuer",jti:"jwt id",nonce:"nonce",s_hash:"state hash",sub:"subject",ath:"access token hash",htm:"http method",htu:"http uri",cnf:"confirmation",auth_time:"authentication time"};function iH(e,t){for(let r of e)if(void 0===t.claims[r])throw nI(`JWT "${r}" (${ij[r]}) claim missing`,iq,{claims:t.claims});return t}let iN=Symbol(),iL=Symbol();async function i$(e,t,r,n){return"string"==typeof n?.expectedNonce||"number"==typeof n?.maxAge||n?.requireIdToken?iD(e,t,r,n.expectedNonce,n.maxAge,{[nA]:n[nA]}):iW(e,t,r,n)}async function iD(e,t,r,n,i,o){let a=[];switch(n){case void 0:n=iN;break;case iN:break;default:nJ(n,'"expectedNonce" argument'),a.push("nonce")}switch(i??=t.default_max_age){case void 0:i=iL;break;case iL:break;default:nK(i,!1,'"maxAge" argument'),a.push("auth_time")}let s=await iA(e,t,r,a,o);nJ(s.id_token,'"response" body "id_token" property',iq,{body:s});let c=iS(s);if(i!==iL){let e=nZ()+nX(t),r=nY(t);if(c.auth_time+i<e-r)throw nI("too much time has elapsed since the last End-User authentication",iQ,{claims:c,now:e,tolerance:r,claim:"auth_time"})}if(n===iN){if(void 0!==c.nonce)throw nI('unexpected ID Token "nonce" claim value',i0,{expected:void 0,claims:c,claim:"nonce"})}else if(c.nonce!==n)throw nI('unexpected ID Token "nonce" claim value',i0,{expected:n,claims:c,claim:"nonce"});return s}async function iW(e,t,r,n){let i=await iA(e,t,r,void 0,n),o=iS(i);if(o){if(void 0!==t.default_max_age){nK(t.default_max_age,!1,'"client.default_max_age"');let e=nZ()+nX(t),r=nY(t);if(o.auth_time+t.default_max_age<e-r)throw nI("too much time has elapsed since the last End-User authentication",iQ,{claims:o,now:e,tolerance:r,claim:"auth_time"})}if(void 0!==o.nonce)throw nI('unexpected ID Token "nonce" claim value',i0,{expected:void 0,claims:o,claim:"nonce"})}return i}let iM="OAUTH_WWW_AUTHENTICATE_CHALLENGE",iK="OAUTH_RESPONSE_BODY_ERROR",iJ="OAUTH_UNSUPPORTED_OPERATION",iF="OAUTH_AUTHORIZATION_RESPONSE_ERROR",iB="OAUTH_JWT_USERINFO_EXPECTED",iz="OAUTH_PARSE_ERROR",iq="OAUTH_INVALID_RESPONSE",iG="OAUTH_INVALID_REQUEST",iV="OAUTH_RESPONSE_IS_NOT_JSON",iX="OAUTH_RESPONSE_IS_NOT_CONFORM",iY="OAUTH_HTTP_REQUEST_FORBIDDEN",iZ="OAUTH_REQUEST_PROTOCOL_FORBIDDEN",iQ="OAUTH_JWT_TIMESTAMP_CHECK_FAILED",i0="OAUTH_JWT_CLAIM_COMPARISON_FAILED",i1="OAUTH_JSON_ATTRIBUTE_COMPARISON_FAILED",i2="OAUTH_KEY_SELECTION_FAILED",i5="OAUTH_MISSING_SERVER_METADATA",i3="OAUTH_INVALID_SERVER_METADATA";function i6(e,t){if("string"!=typeof t.header.typ||t.header.typ.toLowerCase().replace(/^application\//,"")!==e)throw nI('unexpected JWT "typ" header parameter value',iq,{header:t.header});return t}function i4(e){if(e.bodyUsed)throw ng('"response" body has been used already',nw)}async function i8(e,t){nQ(e);let r=n9(e,"jwks_uri",!1,t?.[n_]!==!0),n=nL(t?.headers);return n.set("accept","application/json"),n.append("accept","application/jwk-set+json"),(t?.[nE]||fetch)(r.href,{body:void 0,headers:Object.fromEntries(n.entries()),method:"GET",redirect:"manual",signal:t?.signal?n$(t.signal):void 0})}async function i9(e){if(!ny(e,Response))throw ng('"response" must be an instance of Response',nb);if(200!==e.status)throw nI('"response" is not a conform JSON Web Key Set response (unexpected HTTP status code)',iX,e);i4(e);let t=await om(e,e=>(function(e,...t){if(!t.includes(ib(e)))throw nz(e,...t)})(e,"application/json","application/jwk-set+json"));if(!Array.isArray(t.keys))throw nI('"response" body "keys" property must be an array',iq,{body:t});if(!Array.prototype.every.call(t.keys,nN))throw nI('"response" body "keys" property members must be JWK formatted objects',iq,{body:t});return t}function i7(e){switch(e){case"PS256":case"ES256":case"RS256":case"PS384":case"ES384":case"RS384":case"PS512":case"ES512":case"RS512":case"Ed25519":case"EdDSA":return!0;default:return!1}}function oe(e){let{algorithm:t}=e;if("number"!=typeof t.modulusLength||t.modulusLength<2048)throw new nC(`unsupported ${t.name} modulusLength`,{cause:e})}function ot(e){switch(e.algorithm.name){case"ECDSA":return{name:e.algorithm.name,hash:function(e){let{algorithm:t}=e;switch(t.namedCurve){case"P-256":return"SHA-256";case"P-384":return"SHA-384";case"P-521":return"SHA-512";default:throw new nC("unsupported ECDSA namedCurve",{cause:e})}}(e)};case"RSA-PSS":switch(oe(e),e.algorithm.hash.name){case"SHA-256":case"SHA-384":case"SHA-512":return{name:e.algorithm.name,saltLength:parseInt(e.algorithm.hash.name.slice(-3),10)>>3};default:throw new nC("unsupported RSA-PSS hash name",{cause:e})}case"RSASSA-PKCS1-v1_5":return oe(e),e.algorithm.name;case"Ed25519":return e.algorithm.name}throw new nC("unsupported CryptoKey algorithm name",{cause:e})}async function or(e,t,r,n){let i=nR(`${e}.${t}`),o=ot(r);if(!await crypto.subtle.verify(o,r,n,i))throw nI("JWT signature verification failed",iq,{key:r,data:i,signature:n,algorithm:o})}async function on(e,t,r,n,i){let o,a,{0:s,1:c,length:l}=e.split(".");if(5===l)if(void 0!==i)e=await i(e),{0:s,1:c,length:l}=e.split(".");else throw new nC("JWE decryption is not configured",{cause:e});if(3!==l)throw nI("Invalid JWT",iq,e);try{o=JSON.parse(nR(nO(s)))}catch(e){throw nI("failed to parse JWT Header body as base64url encoded JSON",iz,e)}if(!nN(o))throw nI("JWT Header must be a top level object",iq,e);if(t(o),void 0!==o.crit)throw new nC('no JWT "crit" header parameter extensions are supported',{cause:{header:o}});try{a=JSON.parse(nR(nO(c)))}catch(e){throw nI("failed to parse JWT Payload body as base64url encoded JSON",iz,e)}if(!nN(a))throw nI("JWT Payload must be a top level object",iq,e);let u=nZ()+r;if(void 0!==a.exp){if("number"!=typeof a.exp)throw nI('unexpected JWT "exp" (expiration time) claim type',iq,{claims:a});if(a.exp<=u-n)throw nI('unexpected JWT "exp" (expiration time) claim value, expiration is past current timestamp',iQ,{claims:a,now:u,tolerance:n,claim:"exp"})}if(void 0!==a.iat&&"number"!=typeof a.iat)throw nI('unexpected JWT "iat" (issued at) claim type',iq,{claims:a});if(void 0!==a.iss&&"string"!=typeof a.iss)throw nI('unexpected JWT "iss" (issuer) claim type',iq,{claims:a});if(void 0!==a.nbf){if("number"!=typeof a.nbf)throw nI('unexpected JWT "nbf" (not before) claim type',iq,{claims:a});if(a.nbf>u+n)throw nI('unexpected JWT "nbf" (not before) claim value',iQ,{claims:a,now:u,tolerance:n,claim:"nbf"})}if(void 0!==a.aud&&"string"!=typeof a.aud&&!Array.isArray(a.aud))throw nI('unexpected JWT "aud" (audience) claim type',iq,{claims:a});return{header:o,claims:a,jwt:e}}async function oi(e,t,r){let n;switch(t.alg){case"RS256":case"PS256":case"ES256":n="SHA-256";break;case"RS384":case"PS384":case"ES384":n="SHA-384";break;case"RS512":case"PS512":case"ES512":case"Ed25519":case"EdDSA":n="SHA-512";break;default:throw new nC(`unsupported JWS algorithm for ${r} calculation`,{cause:{alg:t.alg}})}let i=await crypto.subtle.digest(n,nR(e));return nO(i.slice(0,i.byteLength/2))}async function oo(e,t,r,n){return t===await oi(e,r,n)}async function oa(e){if(e.bodyUsed)throw ng("form_post Request instances must contain a readable body",nw,{cause:e});return e.text()}async function os(e){if("POST"!==e.method)throw ng("form_post responses are expected to use the POST method",nw,{cause:e});if("application/x-www-form-urlencoded"!==ib(e))throw ng("form_post responses are expected to use the application/x-www-form-urlencoded content-type",nw,{cause:e});return oa(e)}function oc(e,t,r,n){if(void 0!==e){if("string"==typeof e?n.alg!==e:!e.includes(n.alg))throw nI('unexpected JWT "alg" header parameter',iq,{header:n,expected:e,reason:"client configuration"});return}if(Array.isArray(t)){if(!t.includes(n.alg))throw nI('unexpected JWT "alg" header parameter',iq,{header:n,expected:t,reason:"authorization server metadata"});return}if(void 0!==r){if("string"==typeof r?n.alg!==r:"function"==typeof r?!r(n.alg):!r.includes(n.alg))throw nI('unexpected JWT "alg" header parameter',iq,{header:n,expected:r,reason:"default value"});return}throw nI('missing client or server configuration to verify used JWT "alg" header parameter',void 0,{client:e,issuer:t,fallback:r})}function ol(e,t){let{0:r,length:n}=e.getAll(t);if(n>1)throw nI(`"${t}" parameter must be provided only once`,iq);return r}let ou=Symbol(),od=Symbol();function op(e,t,r,n){var i;if(nQ(e),n0(t),r instanceof URL&&(r=r.searchParams),!(r instanceof URLSearchParams))throw ng('"parameters" must be an instance of URLSearchParams, or URL',nb);if(ol(r,"response"))throw nI('"parameters" contains a JARM response, use validateJwtAuthResponse() instead of validateAuthResponse()',iq,{parameters:r});let o=ol(r,"iss"),a=ol(r,"state");if(!o&&e.authorization_response_iss_parameter_supported)throw nI('response parameter "iss" (issuer) missing',iq,{parameters:r});if(o&&o!==e.issuer)throw nI('unexpected "iss" (issuer) response parameter value',iq,{expected:e.issuer,parameters:r});switch(n){case void 0:case od:if(void 0!==a)throw nI('unexpected "state" response parameter encountered',iq,{expected:void 0,parameters:r});break;case ou:break;default:if(nJ(n,'"expectedState" argument'),a!==n)throw nI(void 0===a?'response parameter "state" missing':'unexpected "state" response parameter value',iq,{expected:n,parameters:r})}if(ol(r,"error"))throw new ie("authorization response from the server is an error",{cause:r});let s=ol(r,"id_token"),c=ol(r,"token");if(void 0!==s||void 0!==c)throw new nC("implicit and hybrid flows are not supported");return i=new URLSearchParams(r),iC.add(i),i}async function of(e,t){let{ext:r,key_ops:n,use:i,...o}=t;return crypto.subtle.importKey("jwk",o,function(e){switch(e){case"PS256":case"PS384":case"PS512":return{name:"RSA-PSS",hash:`SHA-${e.slice(-3)}`};case"RS256":case"RS384":case"RS512":return{name:"RSASSA-PKCS1-v1_5",hash:`SHA-${e.slice(-3)}`};case"ES256":case"ES384":return{name:"ECDSA",namedCurve:`P-${e.slice(-3)}`};case"ES512":return{name:"ECDSA",namedCurve:"P-521"};case"Ed25519":case"EdDSA":return"Ed25519";default:throw new nC("unsupported JWS algorithm",{cause:{alg:e}})}}(e),!0,["verify"])}function oh(e){let t=new URL(e);return t.search="",t.hash="",t.href}async function om(e,t=nB){let r;try{r=await e.json()}catch(r){throw t(e),nI('failed to parse "response" body as JSON',iz,r)}if(!nN(r))throw nI('"response" body must be a top level object',iq,{body:r});return r}let oy=Symbol(),ow=Symbol();async function ob(e,t,r){let{cookies:n,logger:i}=r,o=n[e],a=new Date;a.setTime(a.getTime()+9e5),i.debug(`CREATE_${e.toUpperCase()}`,{name:o.name,payload:t,COOKIE_TTL:900,expires:a});let s=await tQ({...r.jwt,maxAge:900,token:{value:t},salt:o.name}),c={...o.options,expires:a};return{name:o.name,value:s,options:c}}async function og(e,t,r){try{let{logger:n,cookies:i,jwt:o}=r;if(n.debug(`PARSE_${e.toUpperCase()}`,{cookie:t}),!t)throw new S(`${e} cookie was missing`);let a=await t0({...o,token:t,salt:i[e].name});if(a?.value)return a.value;throw Error("Invalid cookie")}catch(t){throw new S(`${e} value could not be parsed`,{cause:t})}}function o_(e,t,r){let{logger:n,cookies:i}=t,o=i[e];n.debug(`CLEAR_${e.toUpperCase()}`,{cookie:o}),r.push({name:o.name,value:"",options:{...i[e].options,maxAge:0}})}function ov(e,t){return async function(r,n,i){let{provider:o,logger:a}=i;if(!o?.checks?.includes(e))return;let s=r?.[i.cookies[t].name];a.debug(`USE_${t.toUpperCase()}`,{value:s});let c=await og(t,s,i);return o_(t,i,n),c}}let ok={async create(e){let t=nq(),r=await nG(t);return{cookie:await ob("pkceCodeVerifier",t,e),value:r}},use:ov("pkce","pkceCodeVerifier")},oE="encodedState",oS={async create(e,t){let{provider:r}=e;if(!r.checks.includes("state")){if(t)throw new S("State data was provided but the provider is not configured to use state");return}let n={origin:t,random:nq()},i=await tQ({secret:e.jwt.secret,token:n,salt:oE,maxAge:900});return{cookie:await ob("state",i,e),value:i}},use:ov("state","state"),async decode(e,t){try{t.logger.debug("DECODE_STATE",{state:e});let r=await t0({secret:t.jwt.secret,token:e,salt:oE});if(r)return r;throw Error("Invalid state")}catch(e){throw new S("State could not be decoded",{cause:e})}}},oA={async create(e){if(!e.provider.checks.includes("nonce"))return;let t=nq();return{cookie:await ob("nonce",t,e),value:t}},use:ov("nonce","nonce")},ox="encodedWebauthnChallenge",oP={create:async(e,t,r)=>({cookie:await ob("webauthnChallenge",await tQ({secret:e.jwt.secret,token:{challenge:t,registerData:r},salt:ox,maxAge:900}),e)}),async use(e,t,r){let n=t?.[e.cookies.webauthnChallenge.name],i=await og("webauthnChallenge",n,e),o=await t0({secret:e.jwt.secret,token:i,salt:ox});if(o_("webauthnChallenge",e,r),!o)throw new S("WebAuthn challenge was missing");return o}};function oT(e){return encodeURIComponent(e).replace(/%20/g,"+")}async function oR(e,t,r){let n,i,o,{logger:a,provider:s}=r,{token:c,userinfo:l}=s;if(c?.url&&"authjs.dev"!==c.url.host||l?.url&&"authjs.dev"!==l.url.host)n={issuer:s.issuer??"https://authjs.dev",token_endpoint:c?.url.toString(),userinfo_endpoint:l?.url.toString()};else{let e=new URL(s.issuer),t=await nM(e,{[n_]:!0,[nE]:s[rl]});if(!(n=await nF(e,t)).token_endpoint)throw TypeError("TODO: Authorization server did not provide a token endpoint.");if(!n.userinfo_endpoint)throw TypeError("TODO: Authorization server did not provide a userinfo endpoint.")}let u={client_id:s.clientId,...s.client};switch(u.token_endpoint_auth_method){case void 0:case"client_secret_basic":i=(e,t,r,n)=>{n.set("authorization",function(e,t){let r=oT(e),n=oT(t),i=btoa(`${r}:${n}`);return`Basic ${i}`}(s.clientId,s.clientSecret))};break;case"client_secret_post":var d;nJ(d=s.clientSecret,'"clientSecret"'),i=(e,t,r,n)=>{r.set("client_id",t.client_id),r.set("client_secret",d)};break;case"client_secret_jwt":i=function(e,t){let r;nJ(e,'"clientSecret"');let n=void 0;return async(t,i,o,a)=>{r||=await crypto.subtle.importKey("raw",nR(e),{hash:"SHA-256",name:"HMAC"},!1,["sign"]);let s={alg:"HS256"},c=n1(t,i);n?.(s,c);let l=`${nO(nR(JSON.stringify(s)))}.${nO(nR(JSON.stringify(c)))}`,u=await crypto.subtle.sign(r.algorithm,r,nR(l));o.set("client_id",i.client_id),o.set("client_assertion_type","urn:ietf:params:oauth:client-assertion-type:jwt-bearer"),o.set("client_assertion",`${l}.${nO(new Uint8Array(u))}`)}}(s.clientSecret);break;case"private_key_jwt":i=function(e,t){var r;let{key:n,kid:i}=(r=e)instanceof CryptoKey?{key:r}:r?.key instanceof CryptoKey?(void 0!==r.kid&&nJ(r.kid,'"kid"'),{key:r.key,kid:r.kid}):{};return nH(n,'"clientPrivateKey.key"'),async(e,r,o,a)=>{let s={alg:nV(n),kid:i},c=n1(e,r);t?.[nS]?.(s,c),o.set("client_id",r.client_id),o.set("client_assertion_type","urn:ietf:params:oauth:client-assertion-type:jwt-bearer"),o.set("client_assertion",await n2(s,c,n))}}(s.token.clientPrivateKey,{[nS](e,t){t.aud=[n.issuer,n.token_endpoint]}});break;case"none":i=(e,t,r,n)=>{r.set("client_id",t.client_id)};break;default:throw Error("unsupported client authentication method")}let p=[],f=await oS.use(t,p,r);try{o=op(n,u,new URLSearchParams(e),s.checks.includes("state")?f:ou)}catch(e){if(e instanceof ie){let t={providerId:s.id,...Object.fromEntries(e.cause.entries())};throw a.debug("OAuthCallbackError",t),new C("OAuth Provider returned an error",t)}throw e}let h=await ok.use(t,p,r),m=s.callbackUrl;!r.isOnRedirectProxy&&s.redirectProxyUrl&&(m=s.redirectProxyUrl);let y=await iI(n,u,i,o,m,h??"decoy",{[n_]:!0,[nE]:(...e)=>(s.checks.includes("pkce")||e[1].body.delete("code_verifier"),(s[rl]??fetch)(...e))});s.token?.conform&&(y=await s.token.conform(y.clone())??y);let w={},b="oidc"===s.type;if(s[ru])switch(s.id){case"microsoft-entra-id":case"azure-ad":{let{tid:e}=function(e){let t,r;if("string"!=typeof e)throw new eT("JWTs must use Compact JWS serialization, JWT must be a string");let{1:n,length:i}=e.split(".");if(5===i)throw new eT("Only JWTs using Compact JWS serialization can be decoded");if(3!==i)throw new eT("Invalid JWT");if(!n)throw new eT("JWTs must contain a payload");try{t=e_(n)}catch{throw new eT("Failed to base64url decode the payload")}try{r=JSON.parse(ep.decode(t))}catch{throw new eT("Failed to parse the decoded payload as JSON")}if(!eC(r))throw new eT("Invalid JWT Claims Set");return r}((await y.clone().json()).id_token);if("string"==typeof e){let t=n.issuer?.match(/microsoftonline\.com\/(\w+)\/v2\.0/)?.[1]??"common",r=new URL(n.issuer.replace(t,e)),i=await nM(r,{[nE]:s[rl]});n=await nF(r,i)}}}let g=await i$(n,u,y,{expectedNonce:await oA.use(t,p,r),requireIdToken:b});if(b){let t=iS(g);if(w=t,s[ru]&&"apple"===s.id)try{w.user=JSON.parse(e?.user)}catch{}if(!1===s.idToken){let e=await ip(n,u,g.access_token,{[nE]:s[rl],[n_]:!0});w=await ig(n,u,t.sub,e)}}else if(l?.request){let e=await l.request({tokens:g,provider:s});e instanceof Object&&(w=e)}else if(l?.url){let e=await ip(n,u,g.access_token,{[nE]:s[rl]});w=await e.json()}else throw TypeError("No userinfo endpoint configured");return g.expires_in&&(g.expires_at=Math.floor(Date.now()/1e3)+Number(g.expires_in)),{...await oO(w,s,g,a),profile:w,cookies:p}}async function oO(e,t,r,n){try{let n=await t.profile(e,r);return{user:{...n,id:crypto.randomUUID(),email:n.email?.toLowerCase()},account:{...r,provider:t.id,type:t.type,providerAccountId:n.id??crypto.randomUUID()}}}catch(r){n.debug("getProfile error details",e),n.error(new U(r,{provider:t.id}))}}async function oC(e,t,r,n){let i=await oN(e,t,r),{cookie:o}=await oP.create(e,i.challenge,r);return{status:200,cookies:[...n??[],o],body:{action:"register",options:i},headers:{"Content-Type":"application/json"}}}async function oU(e,t,r,n){let i=await oH(e,t,r),{cookie:o}=await oP.create(e,i.challenge);return{status:200,cookies:[...n??[],o],body:{action:"authenticate",options:i},headers:{"Content-Type":"application/json"}}}async function oI(e,t,r){let n,{adapter:i,provider:o}=e,a=t.body&&"string"==typeof t.body.data?JSON.parse(t.body.data):void 0;if(!a||"object"!=typeof a||!("id"in a)||"string"!=typeof a.id)throw new h("Invalid WebAuthn Authentication response");let s=oD(o$(a.id)),c=await i.getAuthenticator(s);if(!c)throw new h(`WebAuthn authenticator not found in database: ${JSON.stringify({credentialID:s})}`);let{challenge:l}=await oP.use(e,t.cookies,r);try{var u;let r=o.getRelayingParty(e,t);n=await o.simpleWebAuthn.verifyAuthenticationResponse({...o.verifyAuthenticationOptions,expectedChallenge:l,response:a,authenticator:{...u=c,credentialDeviceType:u.credentialDeviceType,transports:oW(u.transports),credentialID:o$(u.credentialID),credentialPublicKey:o$(u.credentialPublicKey)},expectedOrigin:r.origin,expectedRPID:r.id})}catch(e){throw new z(e)}let{verified:d,authenticationInfo:p}=n;if(!d)throw new z("WebAuthn authentication response could not be verified");try{let{newCounter:e}=p;await i.updateAuthenticatorCounter(c.credentialID,e)}catch(e){throw new y(`Failed to update authenticator counter. This may cause future authentication attempts to fail. ${JSON.stringify({credentialID:s,oldCounter:c.counter,newCounter:p.newCounter})}`,e)}let f=await i.getAccount(c.providerAccountId,o.id);if(!f)throw new h(`WebAuthn account not found in database: ${JSON.stringify({credentialID:s,providerAccountId:c.providerAccountId})}`);let m=await i.getUser(f.userId);if(!m)throw new h(`WebAuthn user not found in database: ${JSON.stringify({credentialID:s,providerAccountId:c.providerAccountId,userID:f.userId})}`);return{account:f,user:m}}async function oj(e,t,r){var n;let i,{provider:o}=e,a=t.body&&"string"==typeof t.body.data?JSON.parse(t.body.data):void 0;if(!a||"object"!=typeof a||!("id"in a)||"string"!=typeof a.id)throw new h("Invalid WebAuthn Registration response");let{challenge:s,registerData:c}=await oP.use(e,t.cookies,r);if(!c)throw new h("Missing user registration data in WebAuthn challenge cookie");try{let r=o.getRelayingParty(e,t);i=await o.simpleWebAuthn.verifyRegistrationResponse({...o.verifyRegistrationOptions,expectedChallenge:s,response:a,expectedOrigin:r.origin,expectedRPID:r.id})}catch(e){throw new z(e)}if(!i.verified||!i.registrationInfo)throw new z("WebAuthn registration response could not be verified");let l={providerAccountId:oD(i.registrationInfo.credentialID),provider:e.provider.id,type:o.type},u={providerAccountId:l.providerAccountId,counter:i.registrationInfo.counter,credentialID:oD(i.registrationInfo.credentialID),credentialPublicKey:oD(i.registrationInfo.credentialPublicKey),credentialBackedUp:i.registrationInfo.credentialBackedUp,credentialDeviceType:i.registrationInfo.credentialDeviceType,transports:(n=a.response.transports,n?.join(","))};return{user:c,account:l,authenticator:u}}async function oH(e,t,r){let{provider:n,adapter:i}=e,o=r&&r.id?await i.listAuthenticatorsByUserId(r.id):null,a=n.getRelayingParty(e,t);return await n.simpleWebAuthn.generateAuthenticationOptions({...n.authenticationOptions,rpID:a.id,allowCredentials:o?.map(e=>({id:o$(e.credentialID),type:"public-key",transports:oW(e.transports)}))})}async function oN(e,t,r){let{provider:n,adapter:i}=e,o=r.id?await i.listAuthenticatorsByUserId(r.id):null,a=rr(32),s=n.getRelayingParty(e,t);return await n.simpleWebAuthn.generateRegistrationOptions({...n.registrationOptions,userID:a,userName:r.email,userDisplayName:r.name??void 0,rpID:s.id,rpName:s.name,excludeCredentials:o?.map(e=>({id:o$(e.credentialID),type:"public-key",transports:oW(e.transports)}))})}function oL(e){let{provider:t,adapter:r}=e;if(!r)throw new x("An adapter is required for the WebAuthn provider");if(!t||"webauthn"!==t.type)throw new D("Provider must be WebAuthn");return{...e,provider:t,adapter:r}}function o$(e){return new Uint8Array(Buffer.from(e,"base64"))}function oD(e){return Buffer.from(e).toString("base64")}function oW(e){return e?e.split(","):void 0}async function oM(e,t,r,n){if(!t.provider)throw new D("Callback route called without provider");let{query:i,body:o,method:a,headers:s}=e,{provider:c,adapter:l,url:u,callbackUrl:d,pages:p,jwt:f,events:m,callbacks:y,session:{strategy:w,maxAge:g},logger:_}=t,v="jwt"===w;try{if("oauth"===c.type||"oidc"===c.type){let a,s=c.authorization?.url.searchParams.get("response_mode")==="form_post"?o:i;if(t.isOnRedirectProxy&&s?.state){let e=await oS.decode(s.state,t);if(e?.origin&&new URL(e.origin).origin!==t.url.origin){let t=`${e.origin}?${new URLSearchParams(s)}`;return _.debug("Proxy redirecting to",t),{redirect:t,cookies:n}}}let h=await oR(s,e.cookies,t);h.cookies.length&&n.push(...h.cookies),_.debug("authorization result",h);let{user:w,account:b,profile:k}=h;if(!w||!b||!k)return{redirect:`${u}/signin`,cookies:n};if(l){let{getUserByAccount:e}=l;a=await e({providerAccountId:b.providerAccountId,provider:c.id})}let E=await oK({user:a??w,account:b,profile:k},t);if(E)return{redirect:E,cookies:n};let{user:S,session:A,isNewUser:x}=await nm(r.value,w,b,t);if(v){let e={name:S.name,email:S.email,picture:S.image,sub:S.id?.toString()},i=await y.jwt({token:e,user:S,account:b,profile:k,isNewUser:x,trigger:x?"signUp":"signIn"});if(null===i)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,o=await f.encode({...f,token:i,salt:e}),a=new Date;a.setTime(a.getTime()+1e3*g);let s=r.chunk(o,{expires:a});n.push(...s)}}else n.push({name:t.cookies.sessionToken.name,value:A.sessionToken,options:{...t.cookies.sessionToken.options,expires:A.expires}});if(await m.signIn?.({user:S,account:b,profile:k,isNewUser:x}),x&&p.newUser)return{redirect:`${p.newUser}${p.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:d})}`,cookies:n};return{redirect:d,cookies:n}}if("email"===c.type){let e=i?.token,o=i?.email;if(!e){let t=TypeError("Missing token. The sign-in URL was manually opened without token or the link was not sent correctly in the email.",{cause:{hasToken:!!e}});throw t.name="Configuration",t}let a=c.secret??t.secret,s=await l.useVerificationToken({identifier:o,token:await rt(`${e}${a}`)}),u=!!s,h=u&&s.expires.valueOf()<Date.now();if(!u||h||o&&s.identifier!==o)throw new M({hasInvite:u,expired:h});let{identifier:w}=s,b=await l.getUserByEmail(w)??{id:crypto.randomUUID(),email:w,emailVerified:null},_={providerAccountId:b.email,userId:b.id,type:"email",provider:c.id},k=await oK({user:b,account:_},t);if(k)return{redirect:k,cookies:n};let{user:E,session:S,isNewUser:A}=await nm(r.value,b,_,t);if(v){let e={name:E.name,email:E.email,picture:E.image,sub:E.id?.toString()},i=await y.jwt({token:e,user:E,account:_,isNewUser:A,trigger:A?"signUp":"signIn"});if(null===i)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,o=await f.encode({...f,token:i,salt:e}),a=new Date;a.setTime(a.getTime()+1e3*g);let s=r.chunk(o,{expires:a});n.push(...s)}}else n.push({name:t.cookies.sessionToken.name,value:S.sessionToken,options:{...t.cookies.sessionToken.options,expires:S.expires}});if(await m.signIn?.({user:E,account:_,isNewUser:A}),A&&p.newUser)return{redirect:`${p.newUser}${p.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:d})}`,cookies:n};return{redirect:d,cookies:n}}if("credentials"===c.type&&"POST"===a){let e=o??{};Object.entries(i??{}).forEach(([e,t])=>u.searchParams.set(e,t));let l=await c.authorize(e,new Request(u,{headers:s,method:a,body:JSON.stringify(o)}));if(l)l.id=l.id?.toString()??crypto.randomUUID();else throw new k;let p={providerAccountId:l.id,type:"credentials",provider:c.id},h=await oK({user:l,account:p,credentials:e},t);if(h)return{redirect:h,cookies:n};let w={name:l.name,email:l.email,picture:l.image,sub:l.id},b=await y.jwt({token:w,user:l,account:p,isNewUser:!1,trigger:"signIn"});if(null===b)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,i=await f.encode({...f,token:b,salt:e}),o=new Date;o.setTime(o.getTime()+1e3*g);let a=r.chunk(i,{expires:o});n.push(...a)}return await m.signIn?.({user:l,account:p}),{redirect:d,cookies:n}}else if("webauthn"===c.type&&"POST"===a){let i,o,a,s=e.body?.action;if("string"!=typeof s||"authenticate"!==s&&"register"!==s)throw new h("Invalid action parameter");let c=oL(t);switch(s){case"authenticate":{let t=await oI(c,e,n);i=t.user,o=t.account;break}case"register":{let r=await oj(t,e,n);i=r.user,o=r.account,a=r.authenticator}}await oK({user:i,account:o},t);let{user:l,isNewUser:u,session:w,account:b}=await nm(r.value,i,o,t);if(!b)throw new h("Error creating or finding account");if(a&&l.id&&await c.adapter.createAuthenticator({...a,userId:l.id}),v){let e={name:l.name,email:l.email,picture:l.image,sub:l.id?.toString()},i=await y.jwt({token:e,user:l,account:b,isNewUser:u,trigger:u?"signUp":"signIn"});if(null===i)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,o=await f.encode({...f,token:i,salt:e}),a=new Date;a.setTime(a.getTime()+1e3*g);let s=r.chunk(o,{expires:a});n.push(...s)}}else n.push({name:t.cookies.sessionToken.name,value:w.sessionToken,options:{...t.cookies.sessionToken.options,expires:w.expires}});if(await m.signIn?.({user:l,account:b,isNewUser:u}),u&&p.newUser)return{redirect:`${p.newUser}${p.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:d})}`,cookies:n};return{redirect:d,cookies:n}}throw new D(`Callback for provider type (${c.type}) is not supported`)}catch(t){if(t instanceof h)throw t;let e=new b(t,{provider:c.id});throw _.debug("callback route error details",{method:a,query:i,body:o}),e}}async function oK(e,t){let r,{signIn:n,redirect:i}=t.callbacks;try{r=await n(e)}catch(e){if(e instanceof h)throw e;throw new w(e)}if(!r)throw new w("AccessDenied");if("string"==typeof r)return await i({url:r,baseUrl:t.url.origin})}async function oJ(e,t,r,n,i){let{adapter:o,jwt:a,events:s,callbacks:c,logger:l,session:{strategy:u,maxAge:d}}=e,p={body:null,headers:{"Content-Type":"application/json"},cookies:r},f=t.value;if(!f)return p;if("jwt"===u){try{let r=e.cookies.sessionToken.name,o=await a.decode({...a,token:f,salt:r});if(!o)throw Error("Invalid JWT");let l=await c.jwt({token:o,...n&&{trigger:"update"},session:i}),u=nh(d);if(null!==l){let e={user:{name:l.name,email:l.email,image:l.picture},expires:u.toISOString()},n=await c.session({session:e,token:l});p.body=n;let i=await a.encode({...a,token:l,salt:r}),o=t.chunk(i,{expires:u});p.cookies?.push(...o),await s.session?.({session:n,token:l})}else p.cookies?.push(...t.clean())}catch(e){l.error(new A(e)),p.cookies?.push(...t.clean())}return p}try{let{getSessionAndUser:r,deleteSession:a,updateSession:l}=o,u=await r(f);if(u&&u.session.expires.valueOf()<Date.now()&&(await a(f),u=null),u){let{user:t,session:r}=u,o=e.session.updateAge,a=r.expires.valueOf()-1e3*d+1e3*o,h=nh(d);a<=Date.now()&&await l({sessionToken:f,expires:h});let m=await c.session({session:{...r,user:t},user:t,newSession:i,...n?{trigger:"update"}:{}});p.body=m,p.cookies?.push({name:e.cookies.sessionToken.name,value:f,options:{...e.cookies.sessionToken.options,expires:h}}),await s.session?.({session:m})}else f&&p.cookies?.push(...t.clean())}catch(e){l.error(new I(e))}return p}async function oF(e,t){let r,n,{logger:i,provider:o}=t,a=o.authorization?.url;if(!a||"authjs.dev"===a.host){let e=new URL(o.issuer),t=await nM(e,{[nE]:o[rl],[n_]:!0}),r=await nF(e,t);if(!r.authorization_endpoint)throw TypeError("Authorization server did not provide an authorization endpoint.");a=new URL(r.authorization_endpoint)}let s=a.searchParams,c=o.callbackUrl;!t.isOnRedirectProxy&&o.redirectProxyUrl&&(c=o.redirectProxyUrl,n=o.callbackUrl,i.debug("using redirect proxy",{redirect_uri:c,data:n}));let l=Object.assign({response_type:"code",client_id:o.clientId,redirect_uri:c,...o.authorization?.params},Object.fromEntries(o.authorization?.url.searchParams??[]),e);for(let e in l)s.set(e,l[e]);let u=[];o.authorization?.url.searchParams.get("response_mode")==="form_post"&&(t.cookies.state.options.sameSite="none",t.cookies.state.options.secure=!0,t.cookies.nonce.options.sameSite="none",t.cookies.nonce.options.secure=!0);let d=await oS.create(t,n);if(d&&(s.set("state",d.value),u.push(d.cookie)),o.checks?.includes("pkce"))if(r&&!r.code_challenge_methods_supported?.includes("S256"))"oidc"===o.type&&(o.checks=["nonce"]);else{let{value:e,cookie:r}=await ok.create(t);s.set("code_challenge",e),s.set("code_challenge_method","S256"),u.push(r)}let p=await oA.create(t);return p&&(s.set("nonce",p.value),u.push(p.cookie)),"oidc"!==o.type||a.searchParams.has("scope")||a.searchParams.set("scope","openid profile email"),i.debug("authorization url is ready",{url:a,cookies:u,provider:o}),{redirect:a.toString(),cookies:u}}async function oB(e,t){let r,{body:n}=e,{provider:i,callbacks:o,adapter:a}=t,s=(i.normalizeIdentifier??function(e){if(!e)throw Error("Missing email from request body.");let[t,r]=e.toLowerCase().trim().split("@");return r=r.split(",")[0],`${t}@${r}`})(n?.email),c={id:crypto.randomUUID(),email:s,emailVerified:null},l=await a.getUserByEmail(s)??c,u={providerAccountId:s,userId:l.id,type:"email",provider:i.id};try{r=await o.signIn({user:l,account:u,email:{verificationRequest:!0}})}catch(e){throw new w(e)}if(!r)throw new w("AccessDenied");if("string"==typeof r)return{redirect:await o.redirect({url:r,baseUrl:t.url.origin})};let{callbackUrl:d,theme:p}=t,f=await i.generateVerificationToken?.()??rr(32),h=new Date(Date.now()+(i.maxAge??86400)*1e3),m=i.secret??t.secret,y=new URL(t.basePath,t.url.origin),b=i.sendVerificationRequest({identifier:s,token:f,expires:h,url:`${y}/callback/${i.id}?${new URLSearchParams({callbackUrl:d,token:f,email:s})}`,provider:i,theme:p,request:new Request(e.url,{headers:e.headers,method:e.method,body:"POST"===e.method?JSON.stringify(e.body??{}):void 0})}),g=a.createVerificationToken?.({identifier:s,token:await rt(`${f}${m}`),expires:h});return await Promise.all([b,g]),{redirect:`${y}/verify-request?${new URLSearchParams({provider:i.id,type:i.type})}`}}async function oz(e,t,r){let n=`${r.url.origin}${r.basePath}/signin`;if(!r.provider)return{redirect:n,cookies:t};switch(r.provider.type){case"oauth":case"oidc":{let{redirect:n,cookies:i}=await oF(e.query,r);return i&&t.push(...i),{redirect:n,cookies:t}}case"email":return{...await oB(e,r),cookies:t};default:return{redirect:n,cookies:t}}}async function oq(e,t,r){let{jwt:n,events:i,callbackUrl:o,logger:a,session:s}=r,c=t.value;if(!c)return{redirect:o,cookies:e};try{if("jwt"===s.strategy){let e=r.cookies.sessionToken.name,t=await n.decode({...n,token:c,salt:e});await i.signOut?.({token:t})}else{let e=await r.adapter?.deleteSession(c);await i.signOut?.({session:e})}}catch(e){a.error(new N(e))}return e.push(...t.clean()),{redirect:o,cookies:e}}async function oG(e,t){let{adapter:r,jwt:n,session:{strategy:i}}=e,o=t.value;if(!o)return null;if("jwt"===i){let t=e.cookies.sessionToken.name,r=await n.decode({...n,token:o,salt:t});if(r&&r.sub)return{id:r.sub,name:r.name,email:r.email,image:r.picture}}else{let e=await r?.getSessionAndUser(o);if(e)return e.user}return null}async function oV(e,t,r,n){let i=oL(t),{provider:o}=i,{action:a}=e.query??{};if("register"!==a&&"authenticate"!==a&&void 0!==a)return{status:400,body:{error:"Invalid action"},cookies:n,headers:{"Content-Type":"application/json"}};let s=await oG(t,r),c=s?{user:s,exists:!0}:await o.getUserInfo(t,e),l=c?.user;switch(function(e,t,r){let{user:n,exists:i=!1}=r??{};switch(e){case"authenticate":return"authenticate";case"register":if(n&&t===i)return"register";break;case void 0:if(!t)if(!n)return"authenticate";else if(i)return"authenticate";else return"register"}return null}(a,!!s,c)){case"authenticate":return oU(i,e,l,n);case"register":if("string"==typeof l?.email)return oC(i,e,l,n);break;default:return{status:400,body:{error:"Invalid request"},cookies:n,headers:{"Content-Type":"application/json"}}}}async function oX(e,t){let{action:r,providerId:n,error:i,method:o}=e,a=t.skipCSRFCheck===rs,{options:s,cookies:c}=await ry({authOptions:t,action:r,providerId:n,url:e.url,callbackUrl:e.body?.callbackUrl??e.query?.callbackUrl,csrfToken:e.body?.csrfToken,cookies:e.cookies,isPost:"POST"===o,csrfDisabled:a}),l=new f(s.cookies.sessionToken,e.cookies,s.logger);if("GET"===o){let t=nf({...s,query:e.query,cookies:c});switch(r){case"callback":return await oM(e,s,l,c);case"csrf":return t.csrf(a,s,c);case"error":return t.error(i);case"providers":return t.providers(s.providers);case"session":return await oJ(s,l,c);case"signin":return t.signin(n,i);case"signout":return t.signout();case"verify-request":return t.verifyRequest();case"webauthn-options":return await oV(e,s,l,c)}}else{let{csrfTokenVerified:t}=s;switch(r){case"callback":return"credentials"===s.provider.type&&ri(r,t),await oM(e,s,l,c);case"session":return ri(r,t),await oJ(s,l,c,!0,e.body?.data);case"signin":return ri(r,t),await oz(e,c,s);case"signout":return ri(r,t),await oq(c,l,s)}}throw new L(`Cannot handle action: ${r}`)}function oY(e,t,r,n,i){let o,a=i?.basePath,s=n.AUTH_URL??n.NEXTAUTH_URL;if(s)o=new URL(s),a&&"/"!==a&&"/"!==o.pathname&&(o.pathname!==a&&t4(i).warn("env-url-basepath-mismatch"),o.pathname="/");else{let e=r.get("x-forwarded-host")??r.get("host"),n=r.get("x-forwarded-proto")??t??"https",i=n.endsWith(":")?n:n+":";o=new URL(`${i}//${e}`)}let c=o.toString().replace(/\/$/,"");if(a){let t=a?.replace(/(^\/|\/$)/g,"")??"";return new URL(`${c}/${t}/${e}`)}return new URL(`${c}/${e}`)}async function oZ(e,t){let r=t4(t),n=await t7(e,t);if(!n)return Response.json("Bad request.",{status:400});let i=function(e,t){let{url:r}=e,n=[];if(!V&&t.debug&&n.push("debug-enabled"),!t.trustHost)return new W(`Host must be trusted. URL was: ${e.url}`);if(!t.secret?.length)return new R("Please define a `secret`");let i=e.query?.callbackUrl;if(i&&!X(i,r.origin))return new v(`Invalid callback URL. Received: ${i}`);let{callbackUrl:o}=p(t.useSecureCookies??"https:"===r.protocol),a=e.cookies?.[t.cookies?.callbackUrl?.name??o.name];if(a&&!X(a,r.origin))return new v(`Invalid callback URL. Received: ${a}`);let s=!1;for(let e of t.providers){let t="function"==typeof e?e():e;if(("oauth"===t.type||"oidc"===t.type)&&!(t.issuer??t.options?.issuer)){let e,{authorization:r,token:n,userinfo:i}=t;if("string"==typeof r||r?.url?"string"==typeof n||n?.url?"string"==typeof i||i?.url||(e="userinfo"):e="token":e="authorization",e)return new E(`Provider "${t.id}" is missing both \`issuer\` and \`${e}\` endpoint config. At least one of them is required`)}if("credentials"===t.type)Y=!0;else if("email"===t.type)Z=!0;else if("webauthn"===t.type){var c;if(Q=!0,t.simpleWebAuthnBrowserVersion&&(c=t.simpleWebAuthnBrowserVersion,!/^v\d+(?:\.\d+){0,2}$/.test(c)))return new h(`Invalid provider config for "${t.id}": simpleWebAuthnBrowserVersion "${t.simpleWebAuthnBrowserVersion}" must be a valid semver string.`);if(t.enableConditionalUI){if(s)return new F("Multiple webauthn providers have 'enableConditionalUI' set to True. Only one provider can have this option enabled at a time");if(s=!0,!Object.values(t.formFields).some(e=>e.autocomplete&&e.autocomplete.toString().indexOf("webauthn")>-1))return new B(`Provider "${t.id}" has 'enableConditionalUI' set to True, but none of its formFields have 'webauthn' in their autocomplete param`)}}}if(Y){let e=t.session?.strategy==="database",r=!t.providers.some(e=>"credentials"!==("function"==typeof e?e():e).type);if(e&&r)return new $("Signing in with credentials only supported if JWT strategy is enabled");if(t.providers.some(e=>{let t="function"==typeof e?e():e;return"credentials"===t.type&&!t.authorize}))return new T("Must define an authorize() handler to use credentials authentication provider")}let{adapter:l,session:u}=t,d=[];if(Z||u?.strategy==="database"||!u?.strategy&&l)if(Z){if(!l)return new x("Email login requires an adapter");d.push(...ee)}else{if(!l)return new x("Database session requires an adapter");d.push(...et)}if(Q){if(!t.experimental?.enableWebAuthn)return new G("WebAuthn is an experimental feature. To enable it, set `experimental.enableWebAuthn` to `true` in your config");if(n.push("experimental-webauthn"),!l)return new x("WebAuthn requires an adapter");d.push(...er)}if(l){let e=d.filter(e=>!(e in l));if(e.length)return new P(`Required adapter methods were missing: ${e.join(", ")}`)}return V||(V=!0),n}(n,t);if(Array.isArray(i))i.forEach(r.warn);else if(i){if(r.error(i),!new Set(["signin","signout","error","verify-request"]).has(n.action)||"GET"!==n.method)return Response.json({message:"There was a problem with the server configuration. Check the server logs for more information."},{status:500});let{pages:e,theme:o}=t,a=e?.error&&n.url.searchParams.get("callbackUrl")?.startsWith(e.error);if(!e?.error||a)return a&&r.error(new g(`The error page ${e?.error} should not require authentication`)),re(nf({theme:o}).error("Configuration"));let s=`${n.url.origin}${e.error}?error=Configuration`;return Response.redirect(s)}let o=e.headers?.has("X-Auth-Return-Redirect"),a=t.raw===rc;try{let e=await oX(n,t);if(a)return e;let r=re(e),i=r.headers.get("Location");if(!o||!i)return r;return Response.json({url:i},{headers:r.headers})}catch(d){r.error(d);let i=d instanceof h;if(i&&a&&!o)throw d;if("POST"===e.method&&"session"===n.action)return Response.json(null,{status:400});let s=new URLSearchParams({error:d instanceof h&&J.has(d.type)?d.type:"Configuration"});d instanceof k&&s.set("code",d.code);let c=i&&d.kind||"error",l=t.pages?.[c]??`${t.basePath}/${c.toLowerCase()}`,u=`${n.url.origin}${l}?${s}`;if(o)return Response.json({url:u});return Response.redirect(u)}}var oQ=r(51353);function o0(e){let t=process.env.AUTH_URL??process.env.NEXTAUTH_URL;if(!t)return e;let{origin:r}=new URL(t),{href:n,origin:i}=e.nextUrl;return new oQ.NextRequest(n.replace(i,r),e)}function o1(e){try{e.secret??(e.secret=process.env.AUTH_SECRET??process.env.NEXTAUTH_SECRET);let t=process.env.AUTH_URL??process.env.NEXTAUTH_URL;if(!t)return;let{pathname:r}=new URL(t);if("/"===r)return;e.basePath||(e.basePath=r)}catch{}finally{e.basePath||(e.basePath="/api/auth"),function(e,t,r=!1){try{let n=e.AUTH_URL;n&&(t.basePath?r||t4(t).warn("env-url-basepath-redundant"):t.basePath=new URL(n).pathname)}catch{}finally{t.basePath??(t.basePath="/auth")}if(!t.secret?.length){t.secret=[];let r=e.AUTH_SECRET;for(let n of(r&&t.secret.push(r),[1,2,3])){let r=e[`AUTH_SECRET_${n}`];r&&t.secret.unshift(r)}}t.redirectProxyUrl??(t.redirectProxyUrl=e.AUTH_REDIRECT_PROXY_URL),t.trustHost??(t.trustHost=!!(e.AUTH_URL??e.AUTH_TRUST_HOST??e.VERCEL??e.CF_PAGES??"production"!==e.NODE_ENV)),t.providers=t.providers.map(t=>{let{id:r}="function"==typeof t?t({}):t,n=r.toUpperCase().replace(/-/g,"_"),i=e[`AUTH_${n}_ID`],o=e[`AUTH_${n}_SECRET`],a=e[`AUTH_${n}_ISSUER`],s=e[`AUTH_${n}_KEY`],c="function"==typeof t?t({clientId:i,clientSecret:o,issuer:a,apiKey:s}):t;return"oauth"===c.type||"oidc"===c.type?(c.clientId??(c.clientId=i),c.clientSecret??(c.clientSecret=o),c.issuer??(c.issuer=a)):"email"===c.type&&(c.apiKey??(c.apiKey=s)),c})}(process.env,e,!0)}}var o2=r(27785);async function o5(e,t){return oZ(new Request(oY("session",e.get("x-forwarded-proto"),e,process.env,t),{headers:{cookie:e.get("cookie")??""}}),{...t,callbacks:{...t.callbacks,async session(...e){let r=await t.callbacks?.session?.(...e)??{...e[0].session,expires:e[0].session.expires?.toISOString?.()??e[0].session.expires};return{user:e[0].user??e[0].token,...r}}}})}function o3(e){return"function"==typeof e}function o6(e,t){return"function"==typeof e?async(...r)=>{if(!r.length){let r=await (0,o2.b3)(),n=await e(void 0);return t?.(n),o5(r,n).then(e=>e.json())}if(r[0]instanceof Request){let n=r[0],i=r[1],o=await e(n);return t?.(o),o4([n,i],o)}if(o3(r[0])){let n=r[0];return async(...r)=>{let i=await e(r[0]);return t?.(i),o4(r,i,n)}}let n="req"in r[0]?r[0].req:r[0],i="res"in r[0]?r[0].res:r[1],o=await e(n);return t?.(o),o5(new Headers(n.headers),o).then(async e=>{let t=await e.json();for(let t of e.headers.getSetCookie())"headers"in i?i.headers.append("set-cookie",t):i.appendHeader("set-cookie",t);return t})}:(...t)=>{if(!t.length)return Promise.resolve((0,o2.b3)()).then(t=>o5(t,e).then(e=>e.json()));if(t[0]instanceof Request)return o4([t[0],t[1]],e);if(o3(t[0])){let r=t[0];return async(...t)=>o4(t,e,r).then(e=>e)}let r="req"in t[0]?t[0].req:t[0],n="res"in t[0]?t[0].res:t[1];return o5(new Headers(r.headers),e).then(async e=>{let t=await e.json();for(let t of e.headers.getSetCookie())"headers"in n?n.headers.append("set-cookie",t):n.appendHeader("set-cookie",t);return t})}}async function o4(e,t,r){let n=o0(e[0]),i=await o5(n.headers,t),o=await i.json(),a=!0;t.callbacks?.authorized&&(a=await t.callbacks.authorized({request:n,auth:o}));let s=oQ.NextResponse.next?.();if(a instanceof Response){s=a;let e=a.headers.get("Location"),{pathname:r}=n.nextUrl;e&&function(e,t,r){let n=t.replace(`${e}/`,""),i=Object.values(r.pages??{});return(o8.has(n)||i.includes(t))&&t===e}(r,new URL(e).pathname,t)&&(a=!0)}else if(r)n.auth=o,s=await r(n,e[1])??oQ.NextResponse.next();else if(!a){let e=t.pages?.signIn??`${t.basePath}/signin`;if(n.nextUrl.pathname!==e){let t=n.nextUrl.clone();t.pathname=e,t.searchParams.set("callbackUrl",n.nextUrl.href),s=oQ.NextResponse.redirect(t)}}let c=new Response(s?.body,s);for(let e of i.headers.getSetCookie())c.headers.append("set-cookie",e);return c}let o8=new Set(["providers","session","csrf","signin","signout","callback","verify-request","error"]);var o9=r(67016);async function o7(e,t={},r,n){let i=new Headers(await (0,o2.b3)()),{redirect:o=!0,redirectTo:a,...s}=t instanceof FormData?Object.fromEntries(t):t,c=a?.toString()??i.get("Referer")??"/",l=oY("signin",i.get("x-forwarded-proto"),i,process.env,n);if(!e)return l.searchParams.append("callbackUrl",c),o&&(0,o9.redirect)(l.toString()),l.toString();let u=`${l}/${e}?${new URLSearchParams(r)}`,d={};for(let t of n.providers){let{options:r,...n}="function"==typeof t?t():t,i=r?.id??n.id;if(i===e){d={id:i,type:r?.type??n.type};break}}if(!d.id){let e=`${l}?${new URLSearchParams({callbackUrl:c})}`;return o&&(0,o9.redirect)(e),e}"credentials"===d.type&&(u=u.replace("signin","callback")),i.set("Content-Type","application/x-www-form-urlencoded");let p=new Request(u,{method:"POST",headers:i,body:new URLSearchParams({...s,callbackUrl:c})}),f=await oZ(p,{...n,raw:rc,skipCSRFCheck:rs}),h=await (0,o2.UL)();for(let e of f?.cookies??[])h.set(e.name,e.value,e.options);let m=(f instanceof Response?f.headers.get("Location"):f.redirect)??u;return o?(0,o9.redirect)(m):m}async function ae(e,t){let r=new Headers(await (0,o2.b3)());r.set("Content-Type","application/x-www-form-urlencoded");let n=oY("signout",r.get("x-forwarded-proto"),r,process.env,t),i=new URLSearchParams({callbackUrl:e?.redirectTo??r.get("Referer")??"/"}),o=new Request(n,{method:"POST",headers:r,body:i}),a=await oZ(o,{...t,raw:rc,skipCSRFCheck:rs}),s=await (0,o2.UL)();for(let e of a?.cookies??[])s.set(e.name,e.value,e.options);return e?.redirect??!0?(0,o9.redirect)(a.redirect):a}async function at(e,t){let r=new Headers(await (0,o2.b3)());r.set("Content-Type","application/json");let n=new Request(oY("session",r.get("x-forwarded-proto"),r,process.env,t),{method:"POST",headers:r,body:JSON.stringify({data:e})}),i=await oZ(n,{...t,raw:rc,skipCSRFCheck:rs}),o=await (0,o2.UL)();for(let e of i?.cookies??[])o.set(e.name,e.value,e.options);return i.body}function ar(e){if("function"==typeof e){let t=async t=>{let r=await e(t);return o1(r),oZ(o0(t),r)};return{handlers:{GET:t,POST:t},auth:o6(e,e=>o1(e)),signIn:async(t,r,n)=>{let i=await e(void 0);return o1(i),o7(t,r,n,i)},signOut:async t=>{let r=await e(void 0);return o1(r),ae(t,r)},unstable_update:async t=>{let r=await e(void 0);return o1(r),at(t,r)}}}o1(e);let t=t=>oZ(o0(t),e);return{handlers:{GET:t,POST:t},auth:o6(e),signIn:(t,r,n)=>o7(t,r,n,e),signOut:t=>ae(t,e),unstable_update:t=>at(t,e)}}},39387:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rootParams",{enumerable:!0,get:function(){return u}});let n=r(30181),i=r(14365),o=r(29294),a=r(63033),s=r(53190),c=r(88781),l=new WeakMap;async function u(){let e=o.workAsyncStorage.getStore();if(!e)throw Object.defineProperty(new n.InvariantError("Missing workStore in unstable_rootParams"),"__NEXT_ERROR_CODE",{value:"E615",enumerable:!1,configurable:!0});let t=a.workUnitAsyncStorage.getStore();if(!t)throw Object.defineProperty(Error(`Route ${e.route} used \`unstable_rootParams()\` in Pages Router. This API is only available within App Router.`),"__NEXT_ERROR_CODE",{value:"E641",enumerable:!1,configurable:!0});switch(t.type){case"unstable-cache":case"cache":throw Object.defineProperty(Error(`Route ${e.route} used \`unstable_rootParams()\` inside \`"use cache"\` or \`unstable_cache\`. Support for this API inside cache scopes is planned for a future version of Next.js.`),"__NEXT_ERROR_CODE",{value:"E642",enumerable:!1,configurable:!0});case"prerender":case"prerender-ppr":case"prerender-legacy":return function(e,t,r){let n=t.fallbackRouteParams;if(n){let p=!1;for(let t in e)if(n.has(t)){p=!0;break}if(p){if("prerender"===r.type){let t=l.get(e);if(t)return t;let n=(0,s.makeHangingPromise)(r.renderSignal,"`unstable_rootParams`");return l.set(e,n),n}var o=e,a=n,u=t,d=r;let p=l.get(o);if(p)return p;let f={...o},h=Promise.resolve(f);return l.set(o,h),Object.keys(o).forEach(e=>{c.wellKnownProperties.has(e)||(a.has(e)?Object.defineProperty(f,e,{get(){let t=(0,c.describeStringPropertyAccess)("unstable_rootParams",e);"prerender-ppr"===d.type?(0,i.postponeWithTracking)(u.route,t,d.dynamicTracking):(0,i.throwToInterruptStaticGeneration)(t,u,d)},enumerable:!0}):h[e]=o[e])}),h}}return Promise.resolve(e)}(t.rootParams,e,t);default:return Promise.resolve(t.rootParams)}}},42660:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"NextURL",{enumerable:!0,get:function(){return u}});let n=r(22298),i=r(61967),o=r(21606),a=r(77500),s=/(?!^https?:\/\/)(127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\[::1\]|localhost)/;function c(e,t){return new URL(String(e).replace(s,"localhost"),t&&String(t).replace(s,"localhost"))}let l=Symbol("NextURLInternal");class u{constructor(e,t,r){let n,i;"object"==typeof t&&"pathname"in t||"string"==typeof t?(n=t,i=r||{}):i=r||t||{},this[l]={url:c(e,n??i.base),options:i,basePath:""},this.analyze()}analyze(){var e,t,r,i,s;let c=(0,a.getNextPathnameInfo)(this[l].url.pathname,{nextConfig:this[l].options.nextConfig,parseData:!0,i18nProvider:this[l].options.i18nProvider}),u=(0,o.getHostname)(this[l].url,this[l].options.headers);this[l].domainLocale=this[l].options.i18nProvider?this[l].options.i18nProvider.detectDomainLocale(u):(0,n.detectDomainLocale)(null==(t=this[l].options.nextConfig)||null==(e=t.i18n)?void 0:e.domains,u);let d=(null==(r=this[l].domainLocale)?void 0:r.defaultLocale)||(null==(s=this[l].options.nextConfig)||null==(i=s.i18n)?void 0:i.defaultLocale);this[l].url.pathname=c.pathname,this[l].defaultLocale=d,this[l].basePath=c.basePath??"",this[l].buildId=c.buildId,this[l].locale=c.locale??d,this[l].trailingSlash=c.trailingSlash}formatPathname(){return(0,i.formatNextPathnameInfo)({basePath:this[l].basePath,buildId:this[l].buildId,defaultLocale:this[l].options.forceLocale?void 0:this[l].defaultLocale,locale:this[l].locale,pathname:this[l].url.pathname,trailingSlash:this[l].trailingSlash})}formatSearch(){return this[l].url.search}get buildId(){return this[l].buildId}set buildId(e){this[l].buildId=e}get locale(){return this[l].locale??""}set locale(e){var t,r;if(!this[l].locale||!(null==(r=this[l].options.nextConfig)||null==(t=r.i18n)?void 0:t.locales.includes(e)))throw Object.defineProperty(TypeError(`The NextURL configuration includes no locale "${e}"`),"__NEXT_ERROR_CODE",{value:"E597",enumerable:!1,configurable:!0});this[l].locale=e}get defaultLocale(){return this[l].defaultLocale}get domainLocale(){return this[l].domainLocale}get searchParams(){return this[l].url.searchParams}get host(){return this[l].url.host}set host(e){this[l].url.host=e}get hostname(){return this[l].url.hostname}set hostname(e){this[l].url.hostname=e}get port(){return this[l].url.port}set port(e){this[l].url.port=e}get protocol(){return this[l].url.protocol}set protocol(e){this[l].url.protocol=e}get href(){let e=this.formatPathname(),t=this.formatSearch();return`${this.protocol}//${this.host}${e}${t}${this.hash}`}set href(e){this[l].url=c(e),this.analyze()}get origin(){return this[l].url.origin}get pathname(){return this[l].url.pathname}set pathname(e){this[l].url.pathname=e}get hash(){return this[l].url.hash}set hash(e){this[l].url.hash=e}get search(){return this[l].url.search}set search(e){this[l].url.search=e}get password(){return this[l].url.password}set password(e){this[l].url.password=e}get username(){return this[l].url.username}set username(e){this[l].url.username=e}get basePath(){return this[l].basePath}set basePath(e){this[l].basePath=e.startsWith("/")?e:`/${e}`}toString(){return this.href}toJSON(){return this.href}[Symbol.for("edge-runtime.inspect.custom")](){return{href:this.href,origin:this.origin,protocol:this.protocol,username:this.username,password:this.password,host:this.host,hostname:this.hostname,port:this.port,pathname:this.pathname,search:this.search,searchParams:this.searchParams,hash:this.hash}}clone(){return new u(String(this),this[l].options)}}},44168:(e,t)=>{"use strict";t.q=function(e,t){if("string"!=typeof e)throw TypeError("argument str must be a string");var r={},n=e.length;if(n<2)return r;var i=t&&t.decode||l,o=0,a=0,u=0;do{if(-1===(a=e.indexOf("=",o)))break;if(-1===(u=e.indexOf(";",o)))u=n;else if(a>u){o=e.lastIndexOf(";",a-1)+1;continue}var d=s(e,o,a),p=c(e,a,d),f=e.slice(d,p);if(!r.hasOwnProperty(f)){var h=s(e,a+1,u),m=c(e,u,h);34===e.charCodeAt(h)&&34===e.charCodeAt(m-1)&&(h++,m--);var y=e.slice(h,m);r[f]=function(e,t){try{return t(e)}catch(t){return e}}(y,i)}o=u+1}while(o<n);return r},t.l=function(e,t,s){var c=s&&s.encode||encodeURIComponent;if("function"!=typeof c)throw TypeError("option encode is invalid");if(!n.test(e))throw TypeError("argument name is invalid");var l=c(t);if(!i.test(l))throw TypeError("argument val is invalid");var u=e+"="+l;if(!s)return u;if(null!=s.maxAge){var d=Math.floor(s.maxAge);if(!isFinite(d))throw TypeError("option maxAge is invalid");u+="; Max-Age="+d}if(s.domain){if(!o.test(s.domain))throw TypeError("option domain is invalid");u+="; Domain="+s.domain}if(s.path){if(!a.test(s.path))throw TypeError("option path is invalid");u+="; Path="+s.path}if(s.expires){var p,f=s.expires;if(p=f,"[object Date]"!==r.call(p)||isNaN(f.valueOf()))throw TypeError("option expires is invalid");u+="; Expires="+f.toUTCString()}if(s.httpOnly&&(u+="; HttpOnly"),s.secure&&(u+="; Secure"),s.partitioned&&(u+="; Partitioned"),s.priority)switch("string"==typeof s.priority?s.priority.toLowerCase():s.priority){case"low":u+="; Priority=Low";break;case"medium":u+="; Priority=Medium";break;case"high":u+="; Priority=High";break;default:throw TypeError("option priority is invalid")}if(s.sameSite)switch("string"==typeof s.sameSite?s.sameSite.toLowerCase():s.sameSite){case!0:case"strict":u+="; SameSite=Strict";break;case"lax":u+="; SameSite=Lax";break;case"none":u+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return u};var r=Object.prototype.toString,n=/^[!#$%&'*+\-.^_`|~0-9A-Za-z]+$/,i=/^("?)[\u0021\u0023-\u002B\u002D-\u003A\u003C-\u005B\u005D-\u007E]*\1$/,o=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,a=/^[\u0020-\u003A\u003D-\u007E]*$/;function s(e,t,r){do{var n=e.charCodeAt(t);if(32!==n&&9!==n)return t}while(++t<r);return r}function c(e,t,r){for(;t>r;){var n=e.charCodeAt(--t);if(32!==n&&9!==n)return t+1}return r}function l(e){return -1!==e.indexOf("%")?decodeURIComponent(e):e}},44207:(e,t,r)=>{"use strict";r.d(t,{b:()=>s});var n=r(60159),i=r(71727),o=r(13486),a=n.forwardRef((e,t)=>(0,o.jsx)(i.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));a.displayName="Label";var s=a},44989:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return c}});let n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=i(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var s=o?Object.getOwnPropertyDescriptor(e,a):null;s&&(s.get||s.set)?Object.defineProperty(n,a,s):n[a]=e[a]}return n.default=e,r&&r.set(e,n),n}(r(61365));function i(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(i=function(e){return e?r:t})(e)}let o={current:null},a="function"==typeof n.cache?n.cache:e=>e,s=console.warn;function c(e){return function(...t){s(e(...t))}}a(e=>{try{s(o.current)}finally{o.current=null}})},45893:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removePathPrefix",{enumerable:!0,get:function(){return i}});let n=r(54195);function i(e,t){if(!(0,n.pathHasPrefix)(e,t))return e;let r=e.slice(t.length);return r.startsWith("/")?r:"/"+r}},47841:(e,t)=>{"use strict";function r(e){return e.replace(/\/$/,"")||"/"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeTrailingSlash",{enumerable:!0,get:function(){return r}})},47940:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addLocale",{enumerable:!0,get:function(){return o}});let n=r(59616),i=r(54195);function o(e,t,r,o){if(!t||t===r)return e;let a=e.toLowerCase();return!o&&((0,i.pathHasPrefix)(a,"/api")||(0,i.pathHasPrefix)(a,"/"+t.toLowerCase()))?e:(0,n.addPathPrefix)(e,"/"+t)}},49005:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}});var r=function(e){return e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect",e}({});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},51353:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ImageResponse:function(){return n.ImageResponse},NextRequest:function(){return i.NextRequest},NextResponse:function(){return o.NextResponse},URLPattern:function(){return s.URLPattern},after:function(){return c.after},connection:function(){return l.connection},unstable_rootParams:function(){return u.unstable_rootParams},userAgent:function(){return a.userAgent},userAgentFromString:function(){return a.userAgentFromString}});let n=r(29568),i=r(15992),o=r(88231),a=r(20616),s=r(75089),c=r(84405),l=r(10136),u=r(39387)},54195:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"pathHasPrefix",{enumerable:!0,get:function(){return i}});let n=r(71027);function i(e,t){if("string"!=typeof e)return!1;let{pathname:r}=(0,n.parsePath)(e);return r===t||r.startsWith(t+"/")}},59616:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathPrefix",{enumerable:!0,get:function(){return i}});let n=r(71027);function i(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:i,hash:o}=(0,n.parsePath)(e);return""+t+r+i+o}},61967:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"formatNextPathnameInfo",{enumerable:!0,get:function(){return s}});let n=r(47841),i=r(59616),o=r(77491),a=r(47940);function s(e){let t=(0,a.addLocale)(e.pathname,e.locale,e.buildId?void 0:e.defaultLocale,e.ignorePrefix);return(e.buildId||!e.trailingSlash)&&(t=(0,n.removeTrailingSlash)(t)),e.buildId&&(t=(0,o.addPathSuffix)((0,i.addPathPrefix)(t,"/_next/data/"+e.buildId),"/"===e.pathname?"index.json":".json")),t=(0,i.addPathPrefix)(t,e.basePath),!e.buildId&&e.trailingSlash?t.endsWith("/")?t:(0,o.addPathSuffix)(t,"/"):(0,n.removeTrailingSlash)(t)}},71027:(e,t)=>{"use strict";function r(e){let t=e.indexOf("#"),r=e.indexOf("?"),n=r>-1&&(t<0||r<t);return n||t>-1?{pathname:e.substring(0,n?r:t),query:n?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parsePath",{enumerable:!0,get:function(){return r}})},72610:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isPostpone",{enumerable:!0,get:function(){return n}});let r=Symbol.for("react.postpone");function n(e){return"object"==typeof e&&null!==e&&e.$$typeof===r}},75089:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"URLPattern",{enumerable:!0,get:function(){return r}});let r="undefined"==typeof URLPattern?void 0:URLPattern},77491:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathSuffix",{enumerable:!0,get:function(){return i}});let n=r(71027);function i(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:i,hash:o}=(0,n.parsePath)(e);return""+r+t+i+o}},77500:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getNextPathnameInfo",{enumerable:!0,get:function(){return a}});let n=r(9793),i=r(45893),o=r(54195);function a(e,t){var r,a;let{basePath:s,i18n:c,trailingSlash:l}=null!=(r=t.nextConfig)?r:{},u={pathname:e,trailingSlash:"/"!==e?e.endsWith("/"):l};s&&(0,o.pathHasPrefix)(u.pathname,s)&&(u.pathname=(0,i.removePathPrefix)(u.pathname,s),u.basePath=s);let d=u.pathname;if(u.pathname.startsWith("/_next/data/")&&u.pathname.endsWith(".json")){let e=u.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/");u.buildId=e[0],d="index"!==e[1]?"/"+e.slice(1).join("/"):"/",!0===t.parseData&&(u.pathname=d)}if(c){let e=t.i18nProvider?t.i18nProvider.analyze(u.pathname):(0,n.normalizeLocalePath)(u.pathname,c.locales);u.locale=e.detectedLocale,u.pathname=null!=(a=e.pathname)?a:u.pathname,!e.detectedLocale&&u.buildId&&(e=t.i18nProvider?t.i18nProvider.analyze(d):(0,n.normalizeLocalePath)(d,c.locales)).detectedLocale&&(u.locale=e.detectedLocale)}return u}},84405:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){Object.keys(e).forEach(function(r){"default"===r||Object.prototype.hasOwnProperty.call(t,r)||Object.defineProperty(t,r,{enumerable:!0,get:function(){return e[r]}})})}(r(99367),t)},85718:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{fromNodeOutgoingHttpHeaders:function(){return i},normalizeNextQueryParam:function(){return c},splitCookiesString:function(){return o},toNodeOutgoingHttpHeaders:function(){return a},validateURL:function(){return s}});let n=r(94689);function i(e){let t=new Headers;for(let[r,n]of Object.entries(e))for(let e of Array.isArray(n)?n:[n])void 0!==e&&("number"==typeof e&&(e=e.toString()),t.append(r,e));return t}function o(e){var t,r,n,i,o,a=[],s=0;function c(){for(;s<e.length&&/\s/.test(e.charAt(s));)s+=1;return s<e.length}for(;s<e.length;){for(t=s,o=!1;c();)if(","===(r=e.charAt(s))){for(n=s,s+=1,c(),i=s;s<e.length&&"="!==(r=e.charAt(s))&&";"!==r&&","!==r;)s+=1;s<e.length&&"="===e.charAt(s)?(o=!0,s=i,a.push(e.substring(t,n)),t=s):s=n+1}else s+=1;(!o||s>=e.length)&&a.push(e.substring(t,e.length))}return a}function a(e){let t={},r=[];if(e)for(let[n,i]of e.entries())"set-cookie"===n.toLowerCase()?(r.push(...o(i)),t[n]=1===r.length?r[0]:r):t[n]=i;return t}function s(e){try{return String(new URL(String(e)))}catch(t){throw Object.defineProperty(Error(`URL is malformed "${String(e)}". Please use only absolute URLs - https://nextjs.org/docs/messages/middleware-relative-urls`,{cause:t}),"__NEXT_ERROR_CODE",{value:"E61",enumerable:!1,configurable:!0})}}function c(e){for(let t of[n.NEXT_QUERY_PARAM_PREFIX,n.NEXT_INTERCEPTION_MARKER_PREFIX])if(e!==t&&e.startsWith(t))return e.substring(t.length);return null}},88231:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"NextResponse",{enumerable:!0,get:function(){return d}});let n=r(4442),i=r(42660),o=r(85718),a=r(70587),s=r(4442),c=Symbol("internal response"),l=new Set([301,302,303,307,308]);function u(e,t){var r;if(null==e||null==(r=e.request)?void 0:r.headers){if(!(e.request.headers instanceof Headers))throw Object.defineProperty(Error("request.headers must be an instance of Headers"),"__NEXT_ERROR_CODE",{value:"E119",enumerable:!1,configurable:!0});let r=[];for(let[n,i]of e.request.headers)t.set("x-middleware-request-"+n,i),r.push(n);t.set("x-middleware-override-headers",r.join(","))}}class d extends Response{constructor(e,t={}){super(e,t);let r=this.headers,l=new Proxy(new s.ResponseCookies(r),{get(e,i,o){switch(i){case"delete":case"set":return(...o)=>{let a=Reflect.apply(e[i],e,o),c=new Headers(r);return a instanceof s.ResponseCookies&&r.set("x-middleware-set-cookie",a.getAll().map(e=>(0,n.stringifyCookie)(e)).join(",")),u(t,c),a};default:return a.ReflectAdapter.get(e,i,o)}}});this[c]={cookies:l,url:t.url?new i.NextURL(t.url,{headers:(0,o.toNodeOutgoingHttpHeaders)(r),nextConfig:t.nextConfig}):void 0}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,url:this.url,body:this.body,bodyUsed:this.bodyUsed,headers:Object.fromEntries(this.headers),ok:this.ok,redirected:this.redirected,status:this.status,statusText:this.statusText,type:this.type}}get cookies(){return this[c].cookies}static json(e,t){let r=Response.json(e,t);return new d(r.body,r)}static redirect(e,t){let r="number"==typeof t?t:(null==t?void 0:t.status)??307;if(!l.has(r))throw Object.defineProperty(RangeError('Failed to execute "redirect" on "response": Invalid status code'),"__NEXT_ERROR_CODE",{value:"E529",enumerable:!1,configurable:!0});let n="object"==typeof t?t:{},i=new Headers(null==n?void 0:n.headers);return i.set("Location",(0,o.validateURL)(e)),new d(null,{...n,headers:i,status:r})}static rewrite(e,t){let r=new Headers(null==t?void 0:t.headers);return r.set("x-middleware-rewrite",(0,o.validateURL)(e)),u(t,r),new d(null,{...t,headers:r})}static next(e){let t=new Headers(null==e?void 0:e.headers);return t.set("x-middleware-next","1"),u(e,t),new d(null,{...e,headers:t})}}},88781:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{describeHasCheckingStringProperty:function(){return i},describeStringPropertyAccess:function(){return n},wellKnownProperties:function(){return o}});let r=/^[A-Za-z_$][A-Za-z0-9_$]*$/;function n(e,t){return r.test(t)?"`"+e+"."+t+"`":"`"+e+"["+JSON.stringify(t)+"]`"}function i(e,t){let r=JSON.stringify(t);return"`Reflect.has("+e+", "+r+")`, `"+r+" in "+e+"`, or similar"}let o=new Set(["hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toString","valueOf","toLocaleString","then","catch","finally","status","displayName","toJSON","$$typeof","__esModule"])},90507:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{BailoutToCSRError:function(){return n},isBailoutToCSRError:function(){return i}});let r="BAILOUT_TO_CLIENT_SIDE_RENDERING";class n extends Error{constructor(e){super("Bail out to client-side rendering: "+e),this.reason=e,this.digest=r}}function i(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}},92493:(e,t,r)=>{"use strict";function n(e){return{id:"credentials",name:"Credentials",type:"credentials",credentials:{},authorize:()=>null,options:e}}r.d(t,{A:()=>n})},94515:(e,t,r)=>{var n;(()=>{var i={226:function(i,o){!function(a,s){"use strict";var c="function",l="undefined",u="object",d="string",p="major",f="model",h="name",m="type",y="vendor",w="version",b="architecture",g="console",_="mobile",v="tablet",k="smarttv",E="wearable",S="embedded",A="Amazon",x="Apple",P="ASUS",T="BlackBerry",R="Browser",O="Chrome",C="Firefox",U="Google",I="Huawei",j="Microsoft",H="Motorola",N="Opera",L="Samsung",$="Sharp",D="Sony",W="Xiaomi",M="Zebra",K="Facebook",J="Chromium OS",F="Mac OS",B=function(e,t){var r={};for(var n in e)t[n]&&t[n].length%2==0?r[n]=t[n].concat(e[n]):r[n]=e[n];return r},z=function(e){for(var t={},r=0;r<e.length;r++)t[e[r].toUpperCase()]=e[r];return t},q=function(e,t){return typeof e===d&&-1!==G(t).indexOf(G(e))},G=function(e){return e.toLowerCase()},V=function(e,t){if(typeof e===d)return e=e.replace(/^\s\s*/,""),typeof t===l?e:e.substring(0,350)},X=function(e,t){for(var r,n,i,o,a,l,d=0;d<t.length&&!a;){var p=t[d],f=t[d+1];for(r=n=0;r<p.length&&!a&&p[r];)if(a=p[r++].exec(e))for(i=0;i<f.length;i++)l=a[++n],typeof(o=f[i])===u&&o.length>0?2===o.length?typeof o[1]==c?this[o[0]]=o[1].call(this,l):this[o[0]]=o[1]:3===o.length?typeof o[1]!==c||o[1].exec&&o[1].test?this[o[0]]=l?l.replace(o[1],o[2]):void 0:this[o[0]]=l?o[1].call(this,l,o[2]):void 0:4===o.length&&(this[o[0]]=l?o[3].call(this,l.replace(o[1],o[2])):s):this[o]=l||s;d+=2}},Y=function(e,t){for(var r in t)if(typeof t[r]===u&&t[r].length>0){for(var n=0;n<t[r].length;n++)if(q(t[r][n],e))return"?"===r?s:r}else if(q(t[r],e))return"?"===r?s:r;return e},Z={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},Q={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[w,[h,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[w,[h,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[h,w],[/opios[\/ ]+([\w\.]+)/i],[w,[h,N+" Mini"]],[/\bopr\/([\w\.]+)/i],[w,[h,N]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i,/(ba?idubrowser)[\/ ]?([\w\.]+)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[h,w],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[w,[h,"UC"+R]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i],[w,[h,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[w,[h,"WeChat"]],[/konqueror\/([\w\.]+)/i],[w,[h,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[w,[h,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[w,[h,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[h,/(.+)/,"$1 Secure "+R],w],[/\bfocus\/([\w\.]+)/i],[w,[h,C+" Focus"]],[/\bopt\/([\w\.]+)/i],[w,[h,N+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[w,[h,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[w,[h,"Dolphin"]],[/coast\/([\w\.]+)/i],[w,[h,N+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[w,[h,"MIUI "+R]],[/fxios\/([-\w\.]+)/i],[w,[h,C]],[/\bqihu|(qi?ho?o?|360)browser/i],[[h,"360 "+R]],[/(oculus|samsung|sailfish|huawei)browser\/([\w\.]+)/i],[[h,/(.+)/,"$1 "+R],w],[/(comodo_dragon)\/([\w\.]+)/i],[[h,/_/g," "],w],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i],[h,w],[/(metasr)[\/ ]?([\w\.]+)/i,/(lbbrowser)/i,/\[(linkedin)app\]/i],[h],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[h,K],w],[/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/ ]([-\w\.]+)/i],[h,w],[/\bgsa\/([\w\.]+) .*safari\//i],[w,[h,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[w,[h,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[w,[h,O+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[h,O+" WebView"],w],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[w,[h,"Android "+R]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[h,w],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[w,[h,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[w,h],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[h,[w,Y,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[h,w],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[h,"Netscape"],w],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[w,[h,C+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[h,w],[/(cobalt)\/([\w\.]+)/i],[h,[w,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[b,"amd64"]],[/(ia32(?=;))/i],[[b,G]],[/((?:i[346]|x)86)[;\)]/i],[[b,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[b,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[b,"armhf"]],[/windows (ce|mobile); ppc;/i],[[b,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[b,/ower/,"",G]],[/(sun4\w)[;\)]/i],[[b,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[b,G]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[f,[y,L],[m,v]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[f,[y,L],[m,_]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[f,[y,x],[m,_]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[f,[y,x],[m,v]],[/(macintosh);/i],[f,[y,x]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[f,[y,$],[m,_]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[f,[y,I],[m,v]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[f,[y,I],[m,_]],[/\b(poco[\w ]+)(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[f,/_/g," "],[y,W],[m,_]],[/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[f,/_/g," "],[y,W],[m,v]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[f,[y,"OPPO"],[m,_]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[f,[y,"Vivo"],[m,_]],[/\b(rmx[12]\d{3})(?: bui|;|\))/i],[f,[y,"Realme"],[m,_]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[f,[y,H],[m,_]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[f,[y,H],[m,v]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[f,[y,"LG"],[m,v]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[f,[y,"LG"],[m,_]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[f,[y,"Lenovo"],[m,v]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[f,/_/g," "],[y,"Nokia"],[m,_]],[/(pixel c)\b/i],[f,[y,U],[m,v]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[f,[y,U],[m,_]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[f,[y,D],[m,_]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[f,"Xperia Tablet"],[y,D],[m,v]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[f,[y,"OnePlus"],[m,_]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[f,[y,A],[m,v]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[f,/(.+)/g,"Fire Phone $1"],[y,A],[m,_]],[/(playbook);[-\w\),; ]+(rim)/i],[f,y,[m,v]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[f,[y,T],[m,_]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[f,[y,P],[m,v]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[f,[y,P],[m,_]],[/(nexus 9)/i],[f,[y,"HTC"],[m,v]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[y,[f,/_/g," "],[m,_]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[f,[y,"Acer"],[m,v]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[f,[y,"Meizu"],[m,_]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[y,f,[m,_]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[y,f,[m,v]],[/(surface duo)/i],[f,[y,j],[m,v]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[f,[y,"Fairphone"],[m,_]],[/(u304aa)/i],[f,[y,"AT&T"],[m,_]],[/\bsie-(\w*)/i],[f,[y,"Siemens"],[m,_]],[/\b(rct\w+) b/i],[f,[y,"RCA"],[m,v]],[/\b(venue[\d ]{2,7}) b/i],[f,[y,"Dell"],[m,v]],[/\b(q(?:mv|ta)\w+) b/i],[f,[y,"Verizon"],[m,v]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[f,[y,"Barnes & Noble"],[m,v]],[/\b(tm\d{3}\w+) b/i],[f,[y,"NuVision"],[m,v]],[/\b(k88) b/i],[f,[y,"ZTE"],[m,v]],[/\b(nx\d{3}j) b/i],[f,[y,"ZTE"],[m,_]],[/\b(gen\d{3}) b.+49h/i],[f,[y,"Swiss"],[m,_]],[/\b(zur\d{3}) b/i],[f,[y,"Swiss"],[m,v]],[/\b((zeki)?tb.*\b) b/i],[f,[y,"Zeki"],[m,v]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[y,"Dragon Touch"],f,[m,v]],[/\b(ns-?\w{0,9}) b/i],[f,[y,"Insignia"],[m,v]],[/\b((nxa|next)-?\w{0,9}) b/i],[f,[y,"NextBook"],[m,v]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[y,"Voice"],f,[m,_]],[/\b(lvtel\-)?(v1[12]) b/i],[[y,"LvTel"],f,[m,_]],[/\b(ph-1) /i],[f,[y,"Essential"],[m,_]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[f,[y,"Envizen"],[m,v]],[/\b(trio[-\w\. ]+) b/i],[f,[y,"MachSpeed"],[m,v]],[/\btu_(1491) b/i],[f,[y,"Rotor"],[m,v]],[/(shield[\w ]+) b/i],[f,[y,"Nvidia"],[m,v]],[/(sprint) (\w+)/i],[y,f,[m,_]],[/(kin\.[onetw]{3})/i],[[f,/\./g," "],[y,j],[m,_]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[f,[y,M],[m,v]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[f,[y,M],[m,_]],[/smart-tv.+(samsung)/i],[y,[m,k]],[/hbbtv.+maple;(\d+)/i],[[f,/^/,"SmartTV"],[y,L],[m,k]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[y,"LG"],[m,k]],[/(apple) ?tv/i],[y,[f,x+" TV"],[m,k]],[/crkey/i],[[f,O+"cast"],[y,U],[m,k]],[/droid.+aft(\w)( bui|\))/i],[f,[y,A],[m,k]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[f,[y,$],[m,k]],[/(bravia[\w ]+)( bui|\))/i],[f,[y,D],[m,k]],[/(mitv-\w{5}) bui/i],[f,[y,W],[m,k]],[/Hbbtv.*(technisat) (.*);/i],[y,f,[m,k]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[y,V],[f,V],[m,k]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[m,k]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[y,f,[m,g]],[/droid.+; (shield) bui/i],[f,[y,"Nvidia"],[m,g]],[/(playstation [345portablevi]+)/i],[f,[y,D],[m,g]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[f,[y,j],[m,g]],[/((pebble))app/i],[y,f,[m,E]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[f,[y,x],[m,E]],[/droid.+; (glass) \d/i],[f,[y,U],[m,E]],[/droid.+; (wt63?0{2,3})\)/i],[f,[y,M],[m,E]],[/(quest( 2| pro)?)/i],[f,[y,K],[m,E]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[y,[m,S]],[/(aeobc)\b/i],[f,[y,A],[m,S]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i],[f,[m,_]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[f,[m,v]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[m,v]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[m,_]],[/(android[-\w\. ]{0,9});.+buil/i],[f,[y,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[w,[h,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[w,[h,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[h,w],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[w,h]],os:[[/microsoft (windows) (vista|xp)/i],[h,w],[/(windows) nt 6\.2; (arm)/i,/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i,/(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i],[h,[w,Y,Z]],[/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[h,"Windows"],[w,Y,Z]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/ios;fbsv\/([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[w,/_/g,"."],[h,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[h,F],[w,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[w,h],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[h,w],[/\(bb(10);/i],[w,[h,T]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[w,[h,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[w,[h,C+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[w,[h,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[w,[h,"watchOS"]],[/crkey\/([\d\.]+)/i],[w,[h,O+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[h,J],w],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[h,w],[/(sunos) ?([\w\.\d]*)/i],[[h,"Solaris"],w],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[h,w]]},ee=function(e,t){if(typeof e===u&&(t=e,e=s),!(this instanceof ee))return new ee(e,t).getResult();var r=typeof a!==l&&a.navigator?a.navigator:s,n=e||(r&&r.userAgent?r.userAgent:""),i=r&&r.userAgentData?r.userAgentData:s,o=t?B(Q,t):Q,g=r&&r.userAgent==n;return this.getBrowser=function(){var e,t={};return t[h]=s,t[w]=s,X.call(t,n,o.browser),t[p]=typeof(e=t[w])===d?e.replace(/[^\d\.]/g,"").split(".")[0]:s,g&&r&&r.brave&&typeof r.brave.isBrave==c&&(t[h]="Brave"),t},this.getCPU=function(){var e={};return e[b]=s,X.call(e,n,o.cpu),e},this.getDevice=function(){var e={};return e[y]=s,e[f]=s,e[m]=s,X.call(e,n,o.device),g&&!e[m]&&i&&i.mobile&&(e[m]=_),g&&"Macintosh"==e[f]&&r&&typeof r.standalone!==l&&r.maxTouchPoints&&r.maxTouchPoints>2&&(e[f]="iPad",e[m]=v),e},this.getEngine=function(){var e={};return e[h]=s,e[w]=s,X.call(e,n,o.engine),e},this.getOS=function(){var e={};return e[h]=s,e[w]=s,X.call(e,n,o.os),g&&!e[h]&&i&&"Unknown"!=i.platform&&(e[h]=i.platform.replace(/chrome os/i,J).replace(/macos/i,F)),e},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return n},this.setUA=function(e){return n=typeof e===d&&e.length>350?V(e,350):e,this},this.setUA(n),this};ee.VERSION="1.0.35",ee.BROWSER=z([h,w,p]),ee.CPU=z([b]),ee.DEVICE=z([f,y,m,g,_,k,v,E,S]),ee.ENGINE=ee.OS=z([h,w]),typeof o!==l?(i.exports&&(o=i.exports=ee),o.UAParser=ee):r.amdO?void 0===(n=(function(){return ee}).call(t,r,t,e))||(e.exports=n):typeof a!==l&&(a.UAParser=ee);var et=typeof a!==l&&(a.jQuery||a.Zepto);if(et&&!et.ua){var er=new ee;et.ua=er.getResult(),et.ua.get=function(){return er.getUA()},et.ua.set=function(e){er.setUA(e);var t=er.getResult();for(var r in t)et.ua[r]=t[r]}}}("object"==typeof window?window:this)}},o={};function a(e){var t=o[e];if(void 0!==t)return t.exports;var r=o[e]={exports:{}},n=!0;try{i[e].call(r.exports,r,r.exports,a),n=!1}finally{n&&delete o[e]}return r.exports}a.ab=__dirname+"/",e.exports=a(226)})()},94689:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ACTION_SUFFIX:function(){return d},APP_DIR_ALIAS:function(){return C},CACHE_ONE_YEAR:function(){return E},DOT_NEXT_ALIAS:function(){return R},ESLINT_DEFAULT_DIRS:function(){return X},GSP_NO_RETURNED_VALUE:function(){return F},GSSP_COMPONENT_MEMBER_ERROR:function(){return q},GSSP_NO_RETURNED_VALUE:function(){return B},INFINITE_CACHE:function(){return S},INSTRUMENTATION_HOOK_FILENAME:function(){return P},MATCHED_PATH_HEADER:function(){return i},MIDDLEWARE_FILENAME:function(){return A},MIDDLEWARE_LOCATION_REGEXP:function(){return x},NEXT_BODY_SUFFIX:function(){return h},NEXT_CACHE_IMPLICIT_TAG_ID:function(){return k},NEXT_CACHE_REVALIDATED_TAGS_HEADER:function(){return y},NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER:function(){return w},NEXT_CACHE_SOFT_TAG_MAX_LENGTH:function(){return v},NEXT_CACHE_TAGS_HEADER:function(){return m},NEXT_CACHE_TAG_MAX_ITEMS:function(){return g},NEXT_CACHE_TAG_MAX_LENGTH:function(){return _},NEXT_DATA_SUFFIX:function(){return p},NEXT_INTERCEPTION_MARKER_PREFIX:function(){return n},NEXT_META_SUFFIX:function(){return f},NEXT_QUERY_PARAM_PREFIX:function(){return r},NEXT_RESUME_HEADER:function(){return b},NON_STANDARD_NODE_ENV:function(){return G},PAGES_DIR_ALIAS:function(){return T},PRERENDER_REVALIDATE_HEADER:function(){return o},PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER:function(){return a},PUBLIC_DIR_MIDDLEWARE_CONFLICT:function(){return $},ROOT_DIR_ALIAS:function(){return O},RSC_ACTION_CLIENT_WRAPPER_ALIAS:function(){return L},RSC_ACTION_ENCRYPTION_ALIAS:function(){return N},RSC_ACTION_PROXY_ALIAS:function(){return j},RSC_ACTION_VALIDATE_ALIAS:function(){return I},RSC_CACHE_WRAPPER_ALIAS:function(){return H},RSC_MOD_REF_PROXY_ALIAS:function(){return U},RSC_PREFETCH_SUFFIX:function(){return s},RSC_SEGMENTS_DIR_SUFFIX:function(){return c},RSC_SEGMENT_SUFFIX:function(){return l},RSC_SUFFIX:function(){return u},SERVER_PROPS_EXPORT_ERROR:function(){return J},SERVER_PROPS_GET_INIT_PROPS_CONFLICT:function(){return W},SERVER_PROPS_SSG_CONFLICT:function(){return M},SERVER_RUNTIME:function(){return Y},SSG_FALLBACK_EXPORT_ERROR:function(){return V},SSG_GET_INITIAL_PROPS_CONFLICT:function(){return D},STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR:function(){return K},UNSTABLE_REVALIDATE_RENAME_ERROR:function(){return z},WEBPACK_LAYERS:function(){return Q},WEBPACK_RESOURCE_QUERIES:function(){return ee}});let r="nxtP",n="nxtI",i="x-matched-path",o="x-prerender-revalidate",a="x-prerender-revalidate-if-generated",s=".prefetch.rsc",c=".segments",l=".segment.rsc",u=".rsc",d=".action",p=".json",f=".meta",h=".body",m="x-next-cache-tags",y="x-next-revalidated-tags",w="x-next-revalidate-tag-token",b="next-resume",g=128,_=256,v=1024,k="_N_T_",E=31536e3,S=0xfffffffe,A="middleware",x=`(?:src/)?${A}`,P="instrumentation",T="private-next-pages",R="private-dot-next",O="private-next-root-dir",C="private-next-app-dir",U="next/dist/build/webpack/loaders/next-flight-loader/module-proxy",I="private-next-rsc-action-validate",j="private-next-rsc-server-reference",H="private-next-rsc-cache-wrapper",N="private-next-rsc-action-encryption",L="private-next-rsc-action-client-wrapper",$="You can not have a '_next' folder inside of your public folder. This conflicts with the internal '/_next' route. https://nextjs.org/docs/messages/public-next-folder-conflict",D="You can not use getInitialProps with getStaticProps. To use SSG, please remove your getInitialProps",W="You can not use getInitialProps with getServerSideProps. Please remove getInitialProps.",M="You can not use getStaticProps or getStaticPaths with getServerSideProps. To use SSG, please remove getServerSideProps",K="can not have getInitialProps/getServerSideProps, https://nextjs.org/docs/messages/404-get-initial-props",J="pages with `getServerSideProps` can not be exported. See more info here: https://nextjs.org/docs/messages/gssp-export",F="Your `getStaticProps` function did not return an object. Did you forget to add a `return`?",B="Your `getServerSideProps` function did not return an object. Did you forget to add a `return`?",z="The `unstable_revalidate` property is available for general use.\nPlease use `revalidate` instead.",q="can not be attached to a page's component and must be exported from the page. See more info here: https://nextjs.org/docs/messages/gssp-component-member",G='You are using a non-standard "NODE_ENV" value in your environment. This creates inconsistencies in the project and is strongly advised against. Read more: https://nextjs.org/docs/messages/non-standard-node-env',V="Pages with `fallback` enabled in `getStaticPaths` can not be exported. See more info here: https://nextjs.org/docs/messages/ssg-fallback-true-export",X=["app","pages","components","lib","src"],Y={edge:"edge",experimentalEdge:"experimental-edge",nodejs:"nodejs"},Z={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",apiNode:"api-node",apiEdge:"api-edge",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",pagesDirBrowser:"pages-dir-browser",pagesDirEdge:"pages-dir-edge",pagesDirNode:"pages-dir-node"},Q={...Z,GROUP:{builtinReact:[Z.reactServerComponents,Z.actionBrowser],serverOnly:[Z.reactServerComponents,Z.actionBrowser,Z.instrument,Z.middleware],neutralTarget:[Z.apiNode,Z.apiEdge],clientOnly:[Z.serverSideRendering,Z.appPagesBrowser],bundled:[Z.reactServerComponents,Z.actionBrowser,Z.serverSideRendering,Z.appPagesBrowser,Z.shared,Z.instrument,Z.middleware],appPages:[Z.reactServerComponents,Z.serverSideRendering,Z.appPagesBrowser,Z.actionBrowser]}},ee={edgeSSREntry:"__next_edge_ssr_entry__",metadata:"__next_metadata__",metadataRoute:"__next_metadata_route__",metadataImageMeta:"__next_metadata_image_meta__"}},99367:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"after",{enumerable:!0,get:function(){return i}});let n=r(29294);function i(e){let t=n.workAsyncStorage.getStore();if(!t)throw Object.defineProperty(Error("`after` was called outside a request scope. Read more: https://nextjs.org/docs/messages/next-dynamic-api-wrong-context"),"__NEXT_ERROR_CODE",{value:"E468",enumerable:!1,configurable:!0});let{afterContext:r}=t;return r.after(e)}}};