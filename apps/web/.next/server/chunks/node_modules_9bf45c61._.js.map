{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "file": "node.mjs", "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/Bonkai/node_modules/bcrypt-ts/src/constant.ts", "file:///Users/<USER>/Downloads/Coding/Personal/Bonkai/node_modules/bcrypt-ts/src/base64.ts", "file:///Users/<USER>/Downloads/Coding/Personal/Bonkai/node_modules/bcrypt-ts/src/utfx.ts", "file:///Users/<USER>/Downloads/Coding/Personal/Bonkai/node_modules/bcrypt-ts/src/utils.ts", "file:///Users/<USER>/Downloads/Coding/Personal/Bonkai/node_modules/bcrypt-ts/src/crypt.ts", "file:///Users/<USER>/Downloads/Coding/Personal/Bonkai/node_modules/bcrypt-ts/src/random/node.ts", "file:///Users/<USER>/Downloads/Coding/Personal/Bonkai/node_modules/bcrypt-ts/src/salt.ts", "file:///Users/<USER>/Downloads/Coding/Personal/Bonkai/node_modules/bcrypt-ts/src/hash.ts", "file:///Users/<USER>/Downloads/Coding/Personal/Bonkai/node_modules/bcrypt-ts/src/compare.ts", "file:///Users/<USER>/Downloads/Coding/Personal/Bonkai/node_modules/bcrypt-ts/src/helpers.ts"], "sourcesContent": ["export const BCRYPT_SALT_LEN = 16;\n\nexport const GENERATE_SALT_DEFAULT_LOG2_ROUNDS = 10;\n\nexport const BLOWFISH_NUM_ROUNDS = 16;\n\nexport const MAX_EXECUTION_TIME = 100;\n\nexport const BASE64_CODE =\n  \"./ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\".split(\"\");\n\nconst naturalNumber = Array.from({ length: 64 }, (_, i) => i);\nconst fillNegative1 = (length: number): number[] =>\n  Array<number>(length).fill(-1);\n\nexport const BASE64_INDEX = [\n  ...fillNegative1(46),\n  0,\n  1,\n  ...naturalNumber.slice(54, 64),\n  ...fillNegative1(7),\n  ...naturalNumber.slice(2, 28),\n  ...fillNegative1(6),\n  ...naturalNumber.slice(28, 54),\n  ...fillNegative1(5),\n];\n\nexport const P_ORIG = [\n  0x243f6a88, 0x85a308d3, 0x13198a2e, 0x03707344, 0xa4093822, 0x299f31d0,\n  0x082efa98, 0xec4e6c89, 0x452821e6, 0x38d01377, 0xbe5466cf, 0x34e90c6c,\n  0xc0ac29b7, 0xc97c50dd, 0x3f84d5b5, 0xb5470917, 0x9216d5d9, 0x8979fb1b,\n];\n\nexport const S_ORIG = [\n  0xd1310ba6, 0x98dfb5ac, 0x2ffd72db, 0xd01adfb7, 0xb8e1afed, 0x6a267e96,\n  0xba7c9045, 0xf12c7f99, 0x24a19947, 0xb3916cf7, 0x0801f2e2, 0x858efc16,\n  0x636920d8, 0x71574e69, 0xa458fea3, 0xf4933d7e, 0x0d95748f, 0x728eb658,\n  0x718bcd58, 0x82154aee, 0x7b54a41d, 0xc25a59b5, 0x9c30d539, 0x2af26013,\n  0xc5d1b023, 0x286085f0, 0xca417918, 0xb8db38ef, 0x8e79dcb0, 0x603a180e,\n  0x6c9e0e8b, 0xb01e8a3e, 0xd71577c1, 0xbd314b27, 0x78af2fda, 0x55605c60,\n  0xe65525f3, 0xaa55ab94, 0x57489862, 0x63e81440, 0x55ca396a, 0x2aab10b6,\n  0xb4cc5c34, 0x1141e8ce, 0xa15486af, 0x7c72e993, 0xb3ee1411, 0x636fbc2a,\n  0x2ba9c55d, 0x741831f6, 0xce5c3e16, 0x9b87931e, 0xafd6ba33, 0x6c24cf5c,\n  0x7a325381, 0x28958677, 0x3b8f4898, 0x6b4bb9af, 0xc4bfe81b, 0x66282193,\n  0x61d809cc, 0xfb21a991, 0x487cac60, 0x5dec8032, 0xef845d5d, 0xe98575b1,\n  0xdc262302, 0xeb651b88, 0x23893e81, 0xd396acc5, 0x0f6d6ff3, 0x83f44239,\n  0x2e0b4482, 0xa4842004, 0x69c8f04a, 0x9e1f9b5e, 0x21c66842, 0xf6e96c9a,\n  0x670c9c61, 0xabd388f0, 0x6a51a0d2, 0xd8542f68, 0x960fa728, 0xab5133a3,\n  0x6eef0b6c, 0x137a3be4, 0xba3bf050, 0x7efb2a98, 0xa1f1651d, 0x39af0176,\n  0x66ca593e, 0x82430e88, 0x8cee8619, 0x456f9fb4, 0x7d84a5c3, 0x3b8b5ebe,\n  0xe06f75d8, 0x85c12073, 0x401a449f, 0x56c16aa6, 0x4ed3aa62, 0x363f7706,\n  0x1bfedf72, 0x429b023d, 0x37d0d724, 0xd00a1248, 0xdb0fead3, 0x49f1c09b,\n  0x075372c9, 0x80991b7b, 0x25d479d8, 0xf6e8def7, 0xe3fe501a, 0xb6794c3b,\n  0x976ce0bd, 0x04c006ba, 0xc1a94fb6, 0x409f60c4, 0x5e5c9ec2, 0x196a2463,\n  0x68fb6faf, 0x3e6c53b5, 0x1339b2eb, 0x3b52ec6f, 0x6dfc511f, 0x9b30952c,\n  0xcc814544, 0xaf5ebd09, 0xbee3d004, 0xde334afd, 0x660f2807, 0x192e4bb3,\n  0xc0cba857, 0x45c8740f, 0xd20b5f39, 0xb9d3fbdb, 0x5579c0bd, 0x1a60320a,\n  0xd6a100c6, 0x402c7279, 0x679f25fe, 0xfb1fa3cc, 0x8ea5e9f8, 0xdb3222f8,\n  0x3c7516df, 0xfd616b15, 0x2f501ec8, 0xad0552ab, 0x323db5fa, 0xfd238760,\n  0x53317b48, 0x3e00df82, 0x9e5c57bb, 0xca6f8ca0, 0x1a87562e, 0xdf1769db,\n  0xd542a8f6, 0x287effc3, 0xac6732c6, 0x8c4f5573, 0x695b27b0, 0xbbca58c8,\n  0xe1ffa35d, 0xb8f011a0, 0x10fa3d98, 0xfd2183b8, 0x4afcb56c, 0x2dd1d35b,\n  0x9a53e479, 0xb6f84565, 0xd28e49bc, 0x4bfb9790, 0xe1ddf2da, 0xa4cb7e33,\n  0x62fb1341, 0xcee4c6e8, 0xef20cada, 0x36774c01, 0xd07e9efe, 0x2bf11fb4,\n  0x95dbda4d, 0xae909198, 0xeaad8e71, 0x6b93d5a0, 0xd08ed1d0, 0xafc725e0,\n  0x8e3c5b2f, 0x8e7594b7, 0x8ff6e2fb, 0xf2122b64, 0x8888b812, 0x900df01c,\n  0x4fad5ea0, 0x688fc31c, 0xd1cff191, 0xb3a8c1ad, 0x2f2f2218, 0xbe0e1777,\n  0xea752dfe, 0x8b021fa1, 0xe5a0cc0f, 0xb56f74e8, 0x18acf3d6, 0xce89e299,\n  0xb4a84fe0, 0xfd13e0b7, 0x7cc43b81, 0xd2ada8d9, 0x165fa266, 0x80957705,\n  0x93cc7314, 0x211a1477, 0xe6ad2065, 0x77b5fa86, 0xc75442f5, 0xfb9d35cf,\n  0xebcdaf0c, 0x7b3e89a0, 0xd6411bd3, 0xae1e7e49, 0x00250e2d, 0x2071b35e,\n  0x226800bb, 0x57b8e0af, 0x2464369b, 0xf009b91e, 0x5563911d, 0x59dfa6aa,\n  0x78c14389, 0xd95a537f, 0x207d5ba2, 0x02e5b9c5, 0x83260376, 0x6295cfa9,\n  0x11c81968, 0x4e734a41, 0xb3472dca, 0x7b14a94a, 0x1b510052, 0x9a532915,\n  0xd60f573f, 0xbc9bc6e4, 0x2b60a476, 0x81e67400, 0x08ba6fb5, 0x571be91f,\n  0xf296ec6b, 0x2a0dd915, 0xb6636521, 0xe7b9f9b6, 0xff34052e, 0xc5855664,\n  0x53b02d5d, 0xa99f8fa1, 0x08ba4799, 0x6e85076a, 0x4b7a70e9, 0xb5b32944,\n  0xdb75092e, 0xc4192623, 0xad6ea6b0, 0x49a7df7d, 0x9cee60b8, 0x8fedb266,\n  0xecaa8c71, 0x699a17ff, 0x5664526c, 0xc2b19ee1, 0x193602a5, 0x75094c29,\n  0xa0591340, 0xe4183a3e, 0x3f54989a, 0x5b429d65, 0x6b8fe4d6, 0x99f73fd6,\n  0xa1d29c07, 0xefe830f5, 0x4d2d38e6, 0xf0255dc1, 0x4cdd2086, 0x8470eb26,\n  0x6382e9c6, 0x021ecc5e, 0x09686b3f, 0x3ebaefc9, 0x3c971814, 0x6b6a70a1,\n  0x687f3584, 0x52a0e286, 0xb79c5305, 0xaa500737, 0x3e07841c, 0x7fdeae5c,\n  0x8e7d44ec, 0x5716f2b8, 0xb03ada37, 0xf0500c0d, 0xf01c1f04, 0x0200b3ff,\n  0xae0cf51a, 0x3cb574b2, 0x25837a58, 0xdc0921bd, 0xd19113f9, 0x7ca92ff6,\n  0x94324773, 0x22f54701, 0x3ae5e581, 0x37c2dadc, 0xc8b57634, 0x9af3dda7,\n  0xa9446146, 0x0fd0030e, 0xecc8c73e, 0xa4751e41, 0xe238cd99, 0x3bea0e2f,\n  0x3280bba1, 0x183eb331, 0x4e548b38, 0x4f6db908, 0x6f420d03, 0xf60a04bf,\n  0x2cb81290, 0x24977c79, 0x5679b072, 0xbcaf89af, 0xde9a771f, 0xd9930810,\n  0xb38bae12, 0xdccf3f2e, 0x5512721f, 0x2e6b7124, 0x501adde6, 0x9f84cd87,\n  0x7a584718, 0x7408da17, 0xbc9f9abc, 0xe94b7d8c, 0xec7aec3a, 0xdb851dfa,\n  0x63094366, 0xc464c3d2, 0xef1c1847, 0x3215d908, 0xdd433b37, 0x24c2ba16,\n  0x12a14d43, 0x2a65c451, 0x50940002, 0x133ae4dd, 0x71dff89e, 0x10314e55,\n  0x81ac77d6, 0x5f11199b, 0x043556f1, 0xd7a3c76b, 0x3c11183b, 0x5924a509,\n  0xf28fe6ed, 0x97f1fbfa, 0x9ebabf2c, 0x1e153c6e, 0x86e34570, 0xeae96fb1,\n  0x860e5e0a, 0x5a3e2ab3, 0x771fe71c, 0x4e3d06fa, 0x2965dcb9, 0x99e71d0f,\n  0x803e89d6, 0x5266c825, 0x2e4cc978, 0x9c10b36a, 0xc6150eba, 0x94e2ea78,\n  0xa5fc3c53, 0x1e0a2df4, 0xf2f74ea7, 0x361d2b3d, 0x1939260f, 0x19c27960,\n  0x5223a708, 0xf71312b6, 0xebadfe6e, 0xeac31f66, 0xe3bc4595, 0xa67bc883,\n  0xb17f37d1, 0x018cff28, 0xc332ddef, 0xbe6c5aa5, 0x65582185, 0x68ab9802,\n  0xeecea50f, 0xdb2f953b, 0x2aef7dad, 0x5b6e2f84, 0x1521b628, 0x29076170,\n  0xecdd4775, 0x619f1510, 0x13cca830, 0xeb61bd96, 0x0334fe1e, 0xaa0363cf,\n  0xb5735c90, 0x4c70a239, 0xd59e9e0b, 0xcbaade14, 0xeecc86bc, 0x60622ca7,\n  0x9cab5cab, 0xb2f3846e, 0x648b1eaf, 0x19bdf0ca, 0xa02369b9, 0x655abb50,\n  0x40685a32, 0x3c2ab4b3, 0x319ee9d5, 0xc021b8f7, 0x9b540b19, 0x875fa099,\n  0x95f7997e, 0x623d7da8, 0xf837889a, 0x97e32d77, 0x11ed935f, 0x16681281,\n  0x0e358829, 0xc7e61fd6, 0x96dedfa1, 0x7858ba99, 0x57f584a5, 0x1b227263,\n  0x9b83c3ff, 0x1ac24696, 0xcdb30aeb, 0x532e3054, 0x8fd948e4, 0x6dbc3128,\n  0x58ebf2ef, 0x34c6ffea, 0xfe28ed61, 0xee7c3c73, 0x5d4a14d9, 0xe864b7e3,\n  0x42105d14, 0x203e13e0, 0x45eee2b6, 0xa3aaabea, 0xdb6c4f15, 0xfacb4fd0,\n  0xc742f442, 0xef6abbb5, 0x654f3b1d, 0x41cd2105, 0xd81e799e, 0x86854dc7,\n  0xe44b476a, 0x3d816250, 0xcf62a1f2, 0x5b8d2646, 0xfc8883a0, 0xc1c7b6a3,\n  0x7f1524c3, 0x69cb7492, 0x47848a0b, 0x5692b285, 0x095bbf00, 0xad19489d,\n  0x1462b174, 0x23820e00, 0x58428d2a, 0x0c55f5ea, 0x1dadf43e, 0x233f7061,\n  0x3372f092, 0x8d937e41, 0xd65fecf1, 0x6c223bdb, 0x7cde3759, 0xcbee7460,\n  0x4085f2a7, 0xce77326e, 0xa6078084, 0x19f8509e, 0xe8efd855, 0x61d99735,\n  0xa969a7aa, 0xc50c06c2, 0x5a04abfc, 0x800bcadc, 0x9e447a2e, 0xc3453484,\n  0xfdd56705, 0x0e1e9ec9, 0xdb73dbd3, 0x105588cd, 0x675fda79, 0xe3674340,\n  0xc5c43465, 0x713e38d8, 0x3d28f89e, 0xf16dff20, 0x153e21e7, 0x8fb03d4a,\n  0xe6e39f2b, 0xdb83adf7, 0xe93d5a68, 0x948140f7, 0xf64c261c, 0x94692934,\n  0x411520f7, 0x7602d4f7, 0xbcf46b2e, 0xd4a20068, 0xd4082471, 0x3320f46a,\n  0x43b7d4b7, 0x500061af, 0x1e39f62e, 0x97244546, 0x14214f74, 0xbf8b8840,\n  0x4d95fc1d, 0x96b591af, 0x70f4ddd3, 0x66a02f45, 0xbfbc09ec, 0x03bd9785,\n  0x7fac6dd0, 0x31cb8504, 0x96eb27b3, 0x55fd3941, 0xda2547e6, 0xabca0a9a,\n  0x28507825, 0x530429f4, 0x0a2c86da, 0xe9b66dfb, 0x68dc1462, 0xd7486900,\n  0x680ec0a4, 0x27a18dee, 0x4f3ffea2, 0xe887ad8c, 0xb58ce006, 0x7af4d6b6,\n  0xaace1e7c, 0xd3375fec, 0xce78a399, 0x406b2a42, 0x20fe9e35, 0xd9f385b9,\n  0xee39d7ab, 0x3b124e8b, 0x1dc9faf7, 0x4b6d1856, 0x26a36631, 0xeae397b2,\n  0x3a6efa74, 0xdd5b4332, 0x6841e7f7, 0xca7820fb, 0xfb0af54e, 0xd8feb397,\n  0x454056ac, 0xba489527, 0x55533a3a, 0x20838d87, 0xfe6ba9b7, 0xd096954b,\n  0x55a867bc, 0xa1159a58, 0xcca92963, 0x99e1db33, 0xa62a4a56, 0x3f3125f9,\n  0x5ef47e1c, 0x9029317c, 0xfdf8e802, 0x04272f70, 0x80bb155c, 0x05282ce3,\n  0x95c11548, 0xe4c66d22, 0x48c1133f, 0xc70f86dc, 0x07f9c9ee, 0x41041f0f,\n  0x404779a4, 0x5d886e17, 0x325f51eb, 0xd59bc0d1, 0xf2bcc18f, 0x41113564,\n  0x257b7834, 0x602a9c60, 0xdff8e8a3, 0x1f636c1b, 0x0e12b4c2, 0x02e1329e,\n  0xaf664fd1, 0xcad18115, 0x6b2395e0, 0x333e92e1, 0x3b240b62, 0xeebeb922,\n  0x85b2a20e, 0xe6ba0d99, 0xde720c8c, 0x2da2f728, 0xd0127845, 0x95b794fd,\n  0x647d0862, 0xe7ccf5f0, 0x5449a36f, 0x877d48fa, 0xc39dfd27, 0xf33e8d1e,\n  0x0a476341, 0x992eff74, 0x3a6f6eab, 0xf4f8fd37, 0xa812dc60, 0xa1ebddf8,\n  0x991be14c, 0xdb6e6b0d, 0xc67b5510, 0x6d672c37, 0x2765d43b, 0xdcd0e804,\n  0xf1290dc7, 0xcc00ffa3, 0xb5390f92, 0x690fed0b, 0x667b9ffb, 0xcedb7d9c,\n  0xa091cf0b, 0xd9155ea3, 0xbb132f88, 0x515bad24, 0x7b9479bf, 0x763bd6eb,\n  0x37392eb3, 0xcc115979, 0x8026e297, 0xf42e312d, 0x6842ada7, 0xc66a2b3b,\n  0x12754ccc, 0x782ef11c, 0x6a124237, 0xb79251e7, 0x06a1bbe6, 0x4bfb6350,\n  0x1a6b1018, 0x11caedfa, 0x3d25bdd8, 0xe2e1c3c9, 0x44421659, 0x0a121386,\n  0xd90cec6e, 0xd5abea2a, 0x64af674e, 0xda86a85f, 0xbebfe988, 0x64e4c3fe,\n  0x9dbc8057, 0xf0f7c086, 0x60787bf8, 0x6003604d, 0xd1fd8346, 0xf6381fb0,\n  0x7745ae04, 0xd736fccc, 0x83426b33, 0xf01eab71, 0xb0804187, 0x3c005e5f,\n  0x77a057be, 0xbde8ae24, 0x55464299, 0xbf582e61, 0x4e58f48f, 0xf2ddfda2,\n  0xf474ef38, 0x8789bdc2, 0x5366f9c3, 0xc8b38e74, 0xb475f255, 0x46fcd9b9,\n  0x7aeb2661, 0x8b1ddf84, 0x846a0e79, 0x915f95e2, 0x466e598e, 0x20b45770,\n  0x8cd55591, 0xc902de4c, 0xb90bace1, 0xbb8205d0, 0x11a86248, 0x7574a99e,\n  0xb77f19b6, 0xe0a9dc09, 0x662d09a1, 0xc4324633, 0xe85a1f02, 0x09f0be8c,\n  0x4a99a025, 0x1d6efe10, 0x1ab93d1d, 0x0ba5a4df, 0xa186f20f, 0x2868f169,\n  0xdcb7da83, 0x573906fe, 0xa1e2ce9b, 0x4fcd7f52, 0x50115e01, 0xa70683fa,\n  0xa002b5c4, 0x0de6d027, 0x9af88c27, 0x773f8641, 0xc3604c06, 0x61a806b5,\n  0xf0177a28, 0xc0f586e0, 0x006058aa, 0x30dc7d62, 0x11e69ed7, 0x2338ea63,\n  0x53c2dd94, 0xc2c21634, 0xbbcbee56, 0x90bcb6de, 0xebfc7da1, 0xce591d76,\n  0x6f05e409, 0x4b7c0188, 0x39720a3d, 0x7c927c24, 0x86e3725f, 0x724d9db9,\n  0x1ac15bb4, 0xd39eb8fc, 0xed545578, 0x08fca5b5, 0xd83d7cd3, 0x4dad0fc4,\n  0x1e50ef5e, 0xb161e6f8, 0xa28514d9, 0x6c51133c, 0x6fd5c7e7, 0x56e14ec4,\n  0x362abfce, 0xddc6c837, 0xd79a3234, 0x92638212, 0x670efa8e, 0x406000e0,\n  0x3a39ce37, 0xd3faf5cf, 0xabc27737, 0x5ac52d1b, 0x5cb0679e, 0x4fa33742,\n  0xd3822740, 0x99bc9bbe, 0xd5118e9d, 0xbf0f7315, 0xd62d1c7e, 0xc700c47b,\n  0xb78c1b6b, 0x21a19045, 0xb26eb1be, 0x6a366eb4, 0x5748ab2f, 0xbc946e79,\n  0xc6a376d2, 0x6549c2c8, 0x530ff8ee, 0x468dde7d, 0xd5730a1d, 0x4cd04dc6,\n  0x2939bbdb, 0xa9ba4650, 0xac9526e8, 0xbe5ee304, 0xa1fad5f0, 0x6a2d519a,\n  0x63ef8ce2, 0x9a86ee22, 0xc089c2b8, 0x43242ef6, 0xa51e03aa, 0x9cf2d0a4,\n  0x83c061ba, 0x9be96a4d, 0x8fe51550, 0xba645bd6, 0x2826a2f9, 0xa73a3ae1,\n  0x4ba99586, 0xef5562e9, 0xc72fefd3, 0xf752f7da, 0x3f046f69, 0x77fa0a59,\n  0x80e4a915, 0x87b08601, 0x9b09e6ad, 0x3b3ee593, 0xe990fd5a, 0x9e34d797,\n  0x2cf0b7d9, 0x022b8b51, 0x96d5ac3a, 0x017da67d, 0xd1cf3ed6, 0x7c7d2d28,\n  0x1f9f25cf, 0xadf2b89b, 0x5ad6b472, 0x5a88f54c, 0xe029ac71, 0xe019a5e6,\n  0x47b0acfd, 0xed93fa9b, 0xe8d3c48d, 0x283b57cc, 0xf8d56629, 0x79132e28,\n  0x785f0191, 0xed756055, 0xf7960e44, 0xe3d35e8c, 0x15056dd4, 0x88f46dba,\n  0x03a16125, 0x0564f0bd, 0xc3eb9e15, 0x3c9057a2, 0x97271aec, 0xa93a072a,\n  0x1b3f6d9b, 0x1e6321f5, 0xf59c66fb, 0x26dcf319, 0x7533d928, 0xb155fdf5,\n  0x03563482, 0x8aba3cbb, 0x28517711, 0xc20ad9f8, 0xabcc5167, 0xccad925f,\n  0x4de81751, 0x3830dc8e, 0x379d5862, 0x9320f991, 0xea7a90c2, 0xfb3e7bce,\n  0x5121ce64, 0x774fbe32, 0xa8b6e37e, 0xc3293d46, 0x48de5369, 0x6413e680,\n  0xa2ae0810, 0xdd6db224, 0x69852dfd, 0x09072166, 0xb39a460a, 0x6445c0dd,\n  0x586cdecf, 0x1c20c8ae, 0x5bbef7dd, 0x1b588d40, 0xccd2017f, 0x6bb4e3bb,\n  0xdda26a7e, 0x3a59ff45, 0x3e350a44, 0xbcb4cdd5, 0x72eacea8, 0xfa6484bb,\n  0x8d6612ae, 0xbf3c6f47, 0xd29be463, 0x542f5d9e, 0xaec2771b, 0xf64e6370,\n  0x740e0d8d, 0xe75b1357, 0xf8721671, 0xaf537d5d, 0x4040cb08, 0x4eb4e2cc,\n  0x34d2466a, 0x0115af84, 0xe1b00428, 0x95983a1d, 0x06b89fb4, 0xce6ea048,\n  0x6f3f3b82, 0x3520ab82, 0x011a1d4b, 0x277227f8, 0x611560b1, 0xe7933fdc,\n  0xbb3a792b, 0x344525bd, 0xa08839e1, 0x51ce794b, 0x2f32c9b7, 0xa01fbac9,\n  0xe01cc87e, 0xbcc7d1f6, 0xcf0111c3, 0xa1e8aac7, 0x1a908749, 0xd44fbd9a,\n  0xd0dadecb, 0xd50ada38, 0x0339c32a, 0xc6913667, 0x8df9317c, 0xe0b12b4f,\n  0xf79e59b7, 0x43f5bb3a, 0xf2d519ff, 0x27d9459c, 0xbf97222c, 0x15e6fc2a,\n  0x0f91fc71, 0x9b941525, 0xfae59361, 0xceb69ceb, 0xc2a86459, 0x12baa8d1,\n  0xb6c1075e, 0xe3056a0c, 0x10d25065, 0xcb03a442, 0xe0ec6e0e, 0x1698db3b,\n  0x4c98a0be, 0x3278e964, 0x9f1f9532, 0xe0d392df, 0xd3a0342b, 0x8971f21e,\n  0x1b0a7441, 0x4ba3348c, 0xc5be7120, 0xc37632d8, 0xdf359f8d, 0x9b992f2e,\n  0xe60b6f47, 0x0fe3f11d, 0xe54cda54, 0x1edad891, 0xce6279cf, 0xcd3e7e6f,\n  0x1618b166, 0xfd2c1d05, 0x848fd2c5, 0xf6fb2299, 0xf523f357, 0xa6327623,\n  0x93a83531, 0x56cccd02, 0xacf08162, 0x5a75ebb5, 0x6e163697, 0x88d273cc,\n  0xde966292, 0x81b949d0, 0x4c50901b, 0x71c65614, 0xe6c6c7bd, 0x327a140a,\n  0x45e1d006, 0xc3f27b9a, 0xc9aa53fd, 0x62a80f00, 0xbb25bfe2, 0x35bdd2f6,\n  0x71126905, 0xb2040222, 0xb6cbcf7c, 0xcd769c2b, 0x53113ec0, 0x1640e3d3,\n  0x38abbd60, 0x2547adf0, 0xba38209c, 0xf746ce76, 0x77afa1c5, 0x20756060,\n  0x85cbfe4e, 0x8ae88dd8, 0x7aaaf9b0, 0x4cf9aa7e, 0x1948c25c, 0x02fb8a8c,\n  0x01c36ae4, 0xd6ebe1f9, 0x90d4f869, 0xa65cdea0, 0x3f09252d, 0xc208e69f,\n  0xb74e6132, 0xce77e25b, 0x578fdfe3, 0x3ac372e6,\n];\n\nexport const C_ORIG = [\n  0x4f727068, 0x65616e42, 0x65686f6c, 0x64657253, 0x63727944, 0x6f756274,\n];\n", "import { BASE64_CODE, BASE64_INDEX } from \"./constant.js\";\n\n/**\n * Encodes a byte array to base64 with up to length bytes of input, using the custom bcrypt alphabet.\n *\n * @param byteArray Byte array\n * @param length Maximum input length\n */\nexport const encodeBase64 = (\n  byteArray: number[] | Buffer,\n  length: number,\n): string => {\n  if (length <= 0 || length > byteArray.length)\n    throw Error(`Illegal len: ${length}`);\n\n  let off = 0;\n  let c1: number;\n  let c2: number;\n  const result: string[] = [];\n\n  while (off < length) {\n    c1 = byteArray[off++] & 0xff;\n    result.push(BASE64_CODE[(c1 >> 2) & 0x3f]);\n    c1 = (c1 & 0x03) << 4;\n    if (off >= length) {\n      result.push(BASE64_CODE[c1 & 0x3f]);\n      break;\n    }\n    c2 = byteArray[off++] & 0xff;\n    c1 |= (c2 >> 4) & 0x0f;\n    result.push(BASE64_CODE[c1 & 0x3f]);\n    c1 = (c2 & 0x0f) << 2;\n    if (off >= length) {\n      result.push(BASE64_CODE[c1 & 0x3f]);\n      break;\n    }\n    c2 = byteArray[off++] & 0xff;\n    c1 |= (c2 >> 6) & 0x03;\n    result.push(BASE64_CODE[c1 & 0x3f]);\n    result.push(BASE64_CODE[c2 & 0x3f]);\n  }\n\n  return result.join(\"\");\n};\n\n/**\n * Decodes a base64 encoded string to up to len bytes of output, using the custom bcrypt alphabet.\n *\n * @param contentString String to decode\n * @param length Maximum output length\n */\nexport const decodeBase64 = (\n  contentString: string,\n  length: number,\n): number[] => {\n  if (length <= 0) throw Error(`Illegal len: ${length}`);\n\n  const stringLength = contentString.length;\n  let off = 0;\n  let olen = 0;\n  let c1: number;\n  let c2: number;\n  let c3: number;\n  let c4: number;\n  let o: number;\n  let code: number;\n  const result: string[] = [];\n\n  while (off < stringLength - 1 && olen < length) {\n    code = contentString.charCodeAt(off++);\n    c1 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\n    code = contentString.charCodeAt(off++);\n    c2 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\n\n    if (c1 == -1 || c2 == -1) break;\n\n    o = (c1 << 2) >>> 0;\n    o |= (c2 & 0x30) >> 4;\n    result.push(String.fromCharCode(o));\n\n    if (++olen >= length || off >= stringLength) break;\n\n    code = contentString.charCodeAt(off++);\n    c3 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\n    if (c3 == -1) break;\n    o = ((c2 & 0x0f) << 4) >>> 0;\n    o |= (c3 & 0x3c) >> 2;\n    result.push(String.fromCharCode(o));\n\n    if (++olen >= length || off >= stringLength) break;\n\n    code = contentString.charCodeAt(off++);\n    c4 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\n    o = ((c3 & 0x03) << 6) >>> 0;\n    o |= c4;\n    result.push(String.fromCharCode(o));\n\n    ++olen;\n  }\n\n  return result.map((item) => item.charCodeAt(0));\n};\n", "/**\n * utfx-embeddable (c) 2014 <PERSON> <<EMAIL>>\n * Released under the Apache License, Version 2.0\n * see: https://github.com/dcodeIO/utfx for details\n */\n\nexport const MAX_CODEPOINT = 0x10ffff;\n\n/**\n * Encodes UTF8 code points to UTF8 bytes.\n * @param nextByte Code points source, either as a function returning the next code point\n *  respectively `null` if there are no more code points left or a single numeric code point.\n * @param destination Bytes destination as a function successively called with the next byte\n */\nexport const encodeUTF8 = (\n  nextByte: number | (() => number | null),\n  destination: (byte: number) => void,\n): void => {\n  let cp = null;\n\n  if (typeof nextByte === \"number\") {\n    cp = nextByte;\n    nextByte = (): null => null;\n  }\n\n  while (cp !== null || (cp = nextByte()) !== null) {\n    if (cp < 0x80) destination(cp & 0x7f);\n    else if (cp < 0x800) {\n      destination(((cp >> 6) & 0x1f) | 0xc0);\n      destination((cp & 0x3f) | 0x80);\n    } else if (cp < 0x10000) {\n      destination(((cp >> 12) & 0x0f) | 0xe0);\n      destination(((cp >> 6) & 0x3f) | 0x80);\n      destination((cp & 0x3f) | 0x80);\n    } else {\n      destination(((cp >> 18) & 0x07) | 0xf0);\n      destination(((cp >> 12) & 0x3f) | 0x80);\n      destination(((cp >> 6) & 0x3f) | 0x80);\n      destination((cp & 0x3f) | 0x80);\n    }\n    cp = null;\n  }\n};\n\nexport class TruncatedError extends Error {\n  bytes: number[];\n  constructor(...args: number[]) {\n    super(args.toString());\n    this.bytes = args;\n    this.name = \"TruncatedError\";\n  }\n}\n\n/**\n * Decodes UTF8 bytes to UTF8 code points.\n *\n * @param nextByte Bytes source as a function returning the next byte respectively `null` if there\n *  are no more bytes left.\n * @param destination Code points destination as a function successively called with each decoded code point.\n * @throws {RangeError} If a starting byte is invalid in UTF8\n * @throws {Error} If the last sequence is truncated. Has an array property `bytes` holding the\n *  remaining bytes.\n */\nexport const decodeUTF8 = (\n  nextByte: () => number | null,\n  destination: (byte: number) => void,\n): void => {\n  let firstByte = nextByte();\n  let secondByte: number | null;\n  let thirdByte: number | null;\n  let fourthByte: number | null;\n\n  while (firstByte !== null) {\n    if ((firstByte & 0x80) === 0) {\n      destination(firstByte);\n    } else if ((firstByte & 0xe0) === 0xc0) {\n      secondByte = nextByte();\n      if (secondByte === null) throw new TruncatedError(firstByte);\n      destination(((firstByte & 0x1f) << 6) | (secondByte & 0x3f));\n    } else if ((firstByte & 0xf0) === 0xe0) {\n      secondByte = nextByte();\n      if (secondByte === null) throw new TruncatedError(firstByte);\n      thirdByte = nextByte();\n      if (thirdByte === null) throw new TruncatedError(firstByte, secondByte);\n      destination(\n        ((firstByte & 0x0f) << 12) |\n          ((secondByte & 0x3f) << 6) |\n          (thirdByte & 0x3f),\n      );\n    } else if ((firstByte & 0xf8) === 0xf0) {\n      secondByte = nextByte();\n      if (secondByte === null) throw new TruncatedError(firstByte);\n      thirdByte = nextByte();\n      if (thirdByte === null) throw new TruncatedError(firstByte, secondByte);\n      fourthByte = nextByte();\n      if (fourthByte === null)\n        throw new TruncatedError(firstByte, secondByte, thirdByte);\n      destination(\n        ((firstByte & 0x07) << 18) |\n          ((secondByte & 0x3f) << 12) |\n          ((thirdByte & 0x3f) << 6) |\n          (fourthByte & 0x3f),\n      );\n    } else throw RangeError(`Illegal starting byte: ${firstByte}`);\n\n    firstByte = nextByte();\n  }\n};\n\n/**\n * Converts UTF16 characters to UTF8 code points.\n * @param nextByte Characters source as a function returning the next char code respectively\n *  `null` if there are no more characters left.\n * @param destination Code points destination as a function successively called with each converted code\n *  point.\n */\nexport const UTF16toUTF8 = (\n  nextByte: () => number | null,\n  destination: (byte: number) => void,\n): void => {\n  let c1: number | null;\n  let c2 = null;\n\n  while (true) {\n    // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing\n    if ((c1 = c2 !== null ? c2 : nextByte()) === null) break;\n    if (c1 >= 0xd800 && c1 <= 0xdfff) {\n      if ((c2 = nextByte()) !== null) {\n        if (c2 >= 0xdc00 && c2 <= 0xdfff) {\n          destination((c1 - 0xd800) * 0x400 + c2 - 0xdc00 + 0x10000);\n          c2 = null;\n          continue;\n        }\n      }\n    }\n    destination(c1);\n  }\n  if (c2 !== null) destination(c2);\n};\n\n/**\n * Converts UTF8 code points to UTF16 characters.\n *\n * @param nextByte Code points source, either as a function returning the next code point\n *  respectively `null` if there are no more code points left or a single numeric code point.\n * @param destination Characters destination as a function successively called with each converted char code.\n * @throws {RangeError} If a code point is out of range\n */\nexport const UTF8toUTF16 = (\n  nextByte: (() => number | null) | number,\n  destination: (byte: number) => void,\n): void => {\n  let codePoint = null;\n\n  if (typeof nextByte === \"number\") {\n    codePoint = nextByte;\n    nextByte = (): null => null;\n  }\n\n  while (codePoint !== null || (codePoint = nextByte()) !== null) {\n    if (codePoint <= 0xffff) destination(codePoint);\n    else {\n      codePoint -= 0x10000;\n      destination((codePoint >> 10) + 0xd800);\n      destination((codePoint % 0x400) + 0xdc00);\n    }\n    codePoint = null;\n  }\n};\n\n/**\n * Converts and encodes UTF16 characters to UTF8 bytes.\n * @param nextByte Characters source as a function returning the next char code respectively `null`\n *  if there are no more characters left.\n * @param destination Bytes destination as a function successively called with the next byte.\n */\nexport const encodeUTF16toUTF8 = (\n  nextByte: () => number | null,\n  destination: (byte: number) => void,\n): void =>\n  UTF16toUTF8(nextByte, (codePoint) => {\n    encodeUTF8(codePoint, destination);\n  });\n\n/**\n * Decodes and converts UTF8 bytes to UTF16 characters.\n * @param nextByte Bytes source as a function returning the next byte respectively `null` if there\n *  are no more bytes left.\n * @param destination Characters destination as a function successively called with each converted char code.\n * @throws {RangeError} If a starting byte is invalid in UTF8\n * @throws {Error} If the last sequence is truncated. Has an array property `bytes` holding the remaining bytes.\n */\nexport const decodeUTF8toUTF16 = (\n  nextByte: () => number | null,\n  destination: (byte: number) => void,\n): void =>\n  decodeUTF8(nextByte, (codePoint) => {\n    UTF8toUTF16(codePoint, destination);\n  });\n\n/**\n * Calculates the byte length of an UTF8 code point.\n *\n * @param codePoint UTF8 code point\n * @returns Byte length\n */\nexport const calculateCodePoint = (codePoint: number): number =>\n  codePoint < 0x80 ? 1 : codePoint < 0x800 ? 2 : codePoint < 0x10000 ? 3 : 4;\n\n/**\n * Calculates the number of UTF8 bytes required to store UTF8 code points.\n * @param src Code points source as a function returning the next code point respectively\n *  `null` if there are no more code points left.\n * @returns The number of UTF8 bytes required\n */\nexport const calculateUTF8 = (src: () => number | null): number => {\n  let codePoint: number | null;\n  let length = 0;\n\n  while ((codePoint = src()) !== null) length += calculateCodePoint(codePoint);\n\n  return length;\n};\n\n/**\n * Calculates the number of UTF8 code points respectively UTF8 bytes required to store UTF16 char codes.\n * @param nextCharCode Characters source as a function returning the next char code respectively\n *  `null` if there are no more characters left.\n * @returns The number of UTF8 code points at index 0 and the number of UTF8 bytes required at index 1.\n */\nexport const calculateUTF16asUTF8 = (\n  nextCharCode: () => number | null,\n): number[] => {\n  let charIndexStart = 0,\n    charIndexEnd = 0;\n\n  UTF16toUTF8(nextCharCode, (codePoint) => {\n    ++charIndexStart;\n    charIndexEnd += calculateCodePoint(codePoint);\n  });\n\n  return [charIndexStart, charIndexEnd];\n};\n", "import { encodeUTF16toUTF8 } from \"./utfx.js\";\n\n/**\n * @private\n *\n * Continues with the callback on the next tick.\n */\nexport const nextTick =\n  typeof process === \"object\" && process.env.NEXT_RUNTIME === \"edge\"\n    ? setTimeout\n    : typeof setImmediate === \"function\"\n      ? setImmediate\n      : typeof process === \"object\" && typeof process.nextTick === \"function\"\n        ? // eslint-disable-next-line @typescript-eslint/unbound-method\n          process.nextTick\n        : setTimeout;\n\n/**\n * @private\n *\n * Converts a JavaScript string to UTF8 bytes.\n *\n * @param str String\n * @returns UTF8 bytes\n */\nexport const stringToBytes = (str: string): number[] => {\n  let index = 0;\n  const bytes: number[] = [];\n\n  encodeUTF16toUTF8(\n    () => (index < str.length ? str.charCodeAt(index++) : null),\n    (byte) => {\n      bytes.push(byte);\n    },\n  );\n\n  return bytes;\n};\n", "import {\n  BCRYPT_SALT_LEN,\n  BLOWFISH_NUM_ROUNDS,\n  C_ORIG,\n  MAX_EXECUTION_TIME,\n  P_ORIG,\n  S_ORIG,\n} from \"./constant.js\";\nimport { nextTick } from \"./utils.js\";\n\n// A base64 implementation for the bcrypt algorithm. This is partly non-standard.\n\nconst encipher = (\n  lr: number[],\n  off: number,\n  P: Int32Array | number[],\n  S: Int32Array | number[],\n): number[] => {\n  // This is our bottleneck: 1714/1905 ticks / 90% - see profile.txt\n  let n: number;\n  let l = lr[off];\n  let r = lr[off + 1];\n\n  l ^= P[0];\n\n  //The following is an unrolled version of the above loop.\n  //Iteration 0\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[1];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[2];\n  //Iteration 1\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[3];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[4];\n  //Iteration 2\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[5];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[6];\n  //Iteration 3\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[7];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[8];\n  //Iteration 4\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[9];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[10];\n  //Iteration 5\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[11];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[12];\n  //Iteration 6\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[13];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[14];\n  //Iteration 7\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[15];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[16];\n\n  lr[off] = r ^ P[BLOWFISH_NUM_ROUNDS + 1];\n  lr[off + 1] = l;\n\n  return lr;\n};\n\nconst streamToWord = (\n  data: number[],\n  offp: number,\n): { key: number; offp: number } => {\n  let word = 0;\n\n  for (let i = 0; i < 4; ++i) {\n    word = (word << 8) | (data[offp] & 0xff);\n    offp = (offp + 1) % data.length;\n  }\n\n  return { key: word, offp };\n};\n\nconst key = (\n  key: number[],\n  P: Int32Array | number[],\n  S: Int32Array | number[],\n): void => {\n  const pLength = P.length;\n  const sLength = S.length;\n  let offp = 0;\n  let lr = [0, 0];\n  let sw: {\n    key: number;\n    offp: number;\n  };\n\n  for (let i = 0; i < pLength; i++) {\n    sw = streamToWord(key, offp);\n    offp = sw.offp;\n    P[i] = P[i] ^ sw.key;\n  }\n\n  for (let i = 0; i < pLength; i += 2) {\n    lr = encipher(lr, 0, P, S);\n    P[i] = lr[0];\n    P[i + 1] = lr[1];\n  }\n\n  for (let i = 0; i < sLength; i += 2) {\n    lr = encipher(lr, 0, P, S);\n    S[i] = lr[0];\n    S[i + 1] = lr[1];\n  }\n};\n\n/**\n * Expensive key schedule Blowfish.\n */\nconst expensiveKeyScheduleBlowFish = (\n  data: number[],\n  key: number[],\n  P: Int32Array | number[],\n  S: Int32Array | number[],\n): void => {\n  const pLength = P.length;\n  const sLength = S.length;\n  let offp = 0;\n  let lr = [0, 0];\n  let sw: {\n    key: number;\n    offp: number;\n  };\n\n  for (let i = 0; i < pLength; i++) {\n    sw = streamToWord(key, offp);\n    offp = sw.offp;\n    P[i] = P[i] ^ sw.key;\n  }\n\n  offp = 0;\n\n  for (let i = 0; i < pLength; i += 2) {\n    sw = streamToWord(data, offp);\n    offp = sw.offp;\n    lr[0] ^= sw.key;\n    sw = streamToWord(data, offp);\n    offp = sw.offp;\n    lr[1] ^= sw.key;\n    lr = encipher(lr, 0, P, S);\n    P[i] = lr[0];\n    P[i + 1] = lr[1];\n  }\n\n  for (let i = 0; i < sLength; i += 2) {\n    sw = streamToWord(data, offp);\n    offp = sw.offp;\n    lr[0] ^= sw.key;\n    sw = streamToWord(data, offp);\n    offp = sw.offp;\n    lr[1] ^= sw.key;\n    lr = encipher(lr, 0, P, S);\n    S[i] = lr[0];\n    S[i + 1] = lr[1];\n  }\n};\n\n/**\n * Internally crypts a string.\n *\n * @param bytes Bytes to crypt\n * @param salt Salt bytes to use\n * @param rounds Number of rounds\n * @param progressCallback Callback called with the current progress\n */\nexport const crypt = (\n  bytes: number[],\n  salt: number[],\n  rounds: number,\n  sync: boolean,\n  progressCallback?: (progress: number) => void,\n): Promise<number[]> | number[] => {\n  const cdata = C_ORIG.slice();\n  const cLength = cdata.length;\n\n  // Validate\n  if (rounds < 4 || rounds > 31) {\n    const err = new Error(`Illegal number of rounds (4-31): ${rounds}`);\n\n    if (sync === false) return Promise.reject(err);\n\n    throw err;\n  }\n\n  if (salt.length !== BCRYPT_SALT_LEN) {\n    const err = new Error(\n      `Illegal salt length: ${salt.length} != ${BCRYPT_SALT_LEN}`,\n    );\n\n    if (sync === false) return Promise.reject(err);\n\n    throw err;\n  }\n\n  rounds = (1 << rounds) >>> 0;\n\n  let P: Int32Array | number[];\n  let S: Int32Array | number[];\n  let i = 0;\n  let j: number;\n\n  //Use typed arrays when available - huge speedup!\n  if (Int32Array) {\n    P = new Int32Array(P_ORIG);\n    S = new Int32Array(S_ORIG);\n  } else {\n    P = P_ORIG.slice();\n    S = S_ORIG.slice();\n  }\n\n  expensiveKeyScheduleBlowFish(salt, bytes, P, S);\n\n  /**\n   * Calculates the next round.\n   */\n  const next = (): Promise<number[] | undefined> | number[] | void => {\n    if (progressCallback) progressCallback(i / rounds);\n\n    if (i < rounds) {\n      const start = Date.now();\n\n      for (; i < rounds; ) {\n        i = i + 1;\n        key(bytes, P, S);\n        key(salt, P, S);\n        if (Date.now() - start > MAX_EXECUTION_TIME) break;\n      }\n    } else {\n      for (i = 0; i < 64; i++)\n        for (j = 0; j < cLength >> 1; j++) encipher(cdata, j << 1, P, S);\n      const result: number[] = [];\n\n      for (i = 0; i < cLength; i++) {\n        result.push(((cdata[i] >> 24) & 0xff) >>> 0);\n        result.push(((cdata[i] >> 16) & 0xff) >>> 0);\n        result.push(((cdata[i] >> 8) & 0xff) >>> 0);\n        result.push((cdata[i] & 0xff) >>> 0);\n      }\n\n      if (sync === false) return Promise.resolve(result);\n\n      return result;\n    }\n\n    if (sync === false)\n      return new Promise((resolve) =>\n        nextTick(() => {\n          void (next() as Promise<number[] | undefined>).then(resolve);\n        }),\n      );\n  };\n\n  if (sync === false) return next() as Promise<number[]>;\n  else {\n    let res;\n\n    while (true)\n      if (typeof (res = next()) !== \"undefined\") return (res as number[]) || [];\n  }\n};\n", "import { randomBytes } from \"node:crypto\";\n\n/**\n * @private\n *\n * Generates cryptographically secure random bytes.\n *\n * @param length Bytes length\n * @returns Random bytes\n * @throws {Error} If no random implementation is available\n */\nexport const random = (length: number): Buffer => randomBytes(length);\n", "// eslint-disable-next-line import-x/no-unresolved\nimport { random } from \"random\";\n\nimport { encodeBase64 } from \"./base64.js\";\nimport {\n  BCRYPT_SALT_LEN,\n  GENERATE_SALT_DEFAULT_LOG2_ROUNDS,\n} from \"./constant.js\";\nimport { nextTick } from \"./utils.js\";\n\n/**\n * Synchronously generates a salt.\n *\n * @param rounds Number of rounds to use, defaults to 10 if omitted\n * @returns Resulting salt\n * @throws {Error} If a random fallback is required but not set\n */\nexport const genSaltSync = (\n  rounds = GENERATE_SALT_DEFAULT_LOG2_ROUNDS,\n): string => {\n  if (typeof rounds !== \"number\")\n    throw Error(\"Illegal arguments: \" + typeof rounds);\n  if (rounds < 4) rounds = 4;\n  else if (rounds > 31) rounds = 31;\n\n  const salt = [];\n\n  salt.push(\"$2a$\");\n  if (rounds < 10) salt.push(\"0\");\n  salt.push(rounds.toString());\n  salt.push(\"$\");\n  salt.push(encodeBase64(random(BCRYPT_SALT_LEN), BCRYPT_SALT_LEN)); // May throw\n\n  return salt.join(\"\");\n};\n\n/**\n * Asynchronously generates a salt.\n *\n * @param rounds Number of rounds to use, defaults to 10 if omitted\n */\nexport const genSalt = (\n  rounds = GENERATE_SALT_DEFAULT_LOG2_ROUNDS,\n): Promise<string> => {\n  if (typeof rounds !== \"number\")\n    throw Error(\"illegal arguments: \" + typeof rounds);\n\n  return new Promise((resolve, reject) =>\n    nextTick(() => {\n      // Pretty thin, but salting is fast enough\n      try {\n        resolve(genSaltSync(rounds));\n      } catch (err) {\n        reject(err as Error);\n      }\n    }),\n  );\n};\n", "import { decodeBase64, encodeBase64 } from \"./base64.js\";\nimport {\n  BCRYPT_SALT_LEN,\n  C_ORIG,\n  GENERATE_SALT_DEFAULT_LOG2_ROUNDS,\n} from \"./constant.js\";\nimport { crypt } from \"./crypt.js\";\nimport { genSalt, genSaltSync } from \"./salt.js\";\nimport { stringToBytes } from \"./utils.js\";\n\n/**\n * Internally hashes a string.\n *\n * @private\n * @param contentString String to hash\n * @param salt Salt to use, actually never null\n * @param progressCallback Callback called with the current progress\n */\nfunction _hash(\n  contentString: string,\n  salt: string,\n  sync: boolean,\n  progressCallback?: (progress: number) => void,\n): Promise<string> | string {\n  if (typeof contentString !== \"string\" || typeof salt !== \"string\") {\n    const err = new Error(\"Invalid string / salt: Not a string\");\n\n    if (sync === false) return Promise.reject(err);\n\n    throw err;\n  }\n\n  // Validate the salt\n  let minor: string;\n  let offset: number;\n\n  if (salt.charAt(0) !== \"$\" || salt.charAt(1) !== \"2\") {\n    const err = new Error(\"Invalid salt version: \" + salt.substring(0, 2));\n\n    if (sync === false) return Promise.reject(err);\n\n    throw err;\n  }\n\n  if (salt.charAt(2) === \"$\") {\n    minor = String.fromCharCode(0);\n    offset = 3;\n  } else {\n    minor = salt.charAt(2);\n    if (\n      (minor !== \"a\" && minor !== \"b\" && minor !== \"y\") ||\n      salt.charAt(3) !== \"$\"\n    ) {\n      const err = Error(\"Invalid salt revision: \" + salt.substring(2, 4));\n\n      if (sync === false) return Promise.reject(err);\n\n      throw err;\n    }\n    offset = 4;\n  }\n\n  // Extract number of rounds\n  if (salt.charAt(offset + 2) > \"$\") {\n    const err = new Error(\"Missing salt rounds\");\n\n    if (sync === false) return Promise.reject(err);\n\n    throw err;\n  }\n\n  const r1 = parseInt(salt.substring(offset, offset + 1), 10) * 10,\n    r2 = parseInt(salt.substring(offset + 1, offset + 2), 10),\n    rounds = r1 + r2,\n    realSalt = salt.substring(offset + 3, offset + 25);\n\n  contentString += minor >= \"a\" ? \"\\x00\" : \"\";\n\n  const passwordBytes = stringToBytes(contentString),\n    saltBytes = decodeBase64(realSalt, BCRYPT_SALT_LEN);\n\n  /**\n   * Finishes hashing.\n   * @param bytes Byte array\n   */\n  const finish = (bytes: number[]): string => {\n    const res = [];\n\n    res.push(\"$2\");\n    if (minor >= \"a\") res.push(minor);\n    res.push(\"$\");\n    if (rounds < 10) res.push(\"0\");\n    res.push(rounds.toString());\n    res.push(\"$\");\n    res.push(encodeBase64(saltBytes, saltBytes.length));\n    res.push(encodeBase64(bytes, C_ORIG.length * 4 - 1));\n\n    return res.join(\"\");\n  };\n\n  // Sync\n  if (sync === false)\n    return (\n      crypt(\n        passwordBytes,\n        saltBytes,\n        rounds,\n        false,\n        progressCallback,\n      ) as Promise<number[]>\n    ).then((bytes) => finish(bytes));\n\n  return finish(\n    crypt(passwordBytes, saltBytes, rounds, true, progressCallback) as number[],\n  );\n}\n\n/**\n * Synchronously generates a hash for the given string.\n *\n * @param contentString String to hash\n * @param salt Salt length to generate or salt to use, default to 10\n * @returns Resulting hash\n */\nexport const hashSync = (\n  contentString: string,\n  salt: string | number = GENERATE_SALT_DEFAULT_LOG2_ROUNDS,\n): string => {\n  if (typeof salt === \"number\") salt = genSaltSync(salt);\n  if (typeof contentString !== \"string\" || typeof salt !== \"string\")\n    throw Error(\n      \"Illegal arguments: \" + typeof contentString + \", \" + typeof salt,\n    );\n\n  return _hash(contentString, salt, true) as string;\n};\n\n/**\n * Asynchronously generates a hash for the given string.\n *\n * @param contentString String to hash\n * @param salt Salt length to generate or salt to use\n * @param progressCallback Callback successively called with the percentage of rounds completed\n *  (0.0 - 1.0), maximally once per `MAX_EXECUTION_TIME = 100` ms.\n */\nexport const hash = function (\n  contentString: string,\n  salt: number | string,\n  progressCallback?: (progress: number) => void,\n): Promise<string> {\n  if (typeof contentString === \"string\" && typeof salt === \"number\")\n    return genSalt(salt).then(\n      (salt) =>\n        _hash(contentString, salt, false, progressCallback) as Promise<string>,\n    );\n\n  if (typeof contentString === \"string\" && typeof salt === \"string\")\n    return _hash(\n      contentString,\n      salt,\n      false,\n      progressCallback,\n    ) as Promise<string>;\n\n  return Promise.reject(\n    new Error(`Illegal arguments: ${typeof contentString}, ${typeof salt}`),\n  );\n};\n", "import { hash as hashAsync, hashSync } from \"./hash.js\";\nimport { nextTick } from \"./utils.js\";\n\n/**\n * Synchronously tests a string against a hash.\n *\n * @param content String to compare\n * @param hash Hash to test against\n */\nexport const compareSync = (content: string, hash: string): boolean => {\n  if (typeof content !== \"string\" || typeof hash !== \"string\")\n    throw Error(\"Illegal arguments: \" + typeof content + \", \" + typeof hash);\n  if (hash.length !== 60) return false;\n\n  return hashSync(content, hash.substring(0, hash.length - 31)) === hash;\n};\n\n/**\n * Asynchronously compares the given data against the given hash.\n *\n * @param content Data to compare\n * @param hash Data to be compared to\n * @param progressCallback Callback successively called with the percentage of rounds completed\n *  (0.0 - 1.0), maximally once per `MAX_EXECUTION_TIME = 100` ms.\n */\nexport const compare = (\n  content: string,\n  hash: string,\n  progressCallback?: (percent: number) => void,\n): Promise<boolean> =>\n  new Promise((resolve, reject) => {\n    if (typeof content !== \"string\" || typeof hash !== \"string\") {\n      nextTick(() =>\n        reject(\n          new Error(`Illegal arguments: ${typeof content}, ${typeof hash}`),\n        ),\n      );\n\n      return;\n    }\n\n    if (hash.length !== 60) {\n      nextTick(() =>\n        reject(new Error(\"Illegal hash: hash length should be 60\")),\n      );\n\n      return;\n    }\n\n    hashAsync(content, hash.substring(0, 29), progressCallback)\n      .then((comp) => resolve(comp === hash))\n      .catch((err: Error) => reject(err));\n  });\n", "/**\n * Gets the number of rounds used to encrypt the specified hash.\n *\n * @param hash Hash to extract the used number of rounds from\n * @returns Number of rounds used\n * @throws {Error} If `hash` is not a string\n */\nexport const getRounds = (hash: string): number => {\n  if (typeof hash !== \"string\")\n    throw new Error(`Illegal arguments: ${typeof hash}`);\n\n  return parseInt(hash.split(\"$\")[2], 10);\n};\n\n/**\n * Gets the salt portion from a hash. Does not validate the hash.\n *\n * @param hash Hash to extract the salt from\n * @returns Extracted salt part\n * @throws {Error} If `hash` is not a string or otherwise invalid\n */\nexport const getSalt = (hash: string): string => {\n  if (typeof hash !== \"string\")\n    throw new Error(`Illegal arguments: ${typeof hash}`);\n\n  if (hash.length !== 60)\n    throw new Error(`Illegal hash length: ${hash.length} != 60`);\n\n  return hash.substring(0, 29);\n};\n"], "names": ["BCRYPT_SALT_LEN", "GENERATE_SALT_DEFAULT_LOG2_ROUNDS", "BLOWFISH_NUM_ROUNDS", "MAX_EXECUTION_TIME", "BASE64_CODE", "naturalNumber", "_", "i", "fillNegative1", "length", "BASE64_INDEX", "P_ORIG", "S_ORIG", "C_ORIG", "encodeBase64", "byteArray", "off", "c1", "c2", "result", "decodeBase64", "contentString", "stringLength", "olen", "c3", "c4", "o", "code", "item", "encodeUTF8", "nextByte", "destination", "cp", "UTF16toUTF8", "encodeUTF16toUTF8", "codePoint", "nextTick", "stringToBytes", "str", "index", "bytes", "byte", "encipher", "lr", "P", "S", "n", "l", "r", "streamToWord", "data", "offp", "word", "key", "p<PERSON><PERSON>th", "s<PERSON><PERSON>th", "sw", "expensiveKeyScheduleBlowFish", "crypt", "salt", "rounds", "sync", "progressCallback", "cdata", "c<PERSON><PERSON>th", "err", "j", "next", "start", "resolve", "res", "random", "randomBytes", "genSaltSync", "genSalt", "reject", "_hash", "minor", "offset", "r1", "r2", "realSalt", "passwordBytes", "saltBytes", "finish", "hashSync", "hash", "compareSync", "content", "compare", "hashAsync", "comp", "getRounds", "getSalt"], "mappings": ";;;;;;;;;;;;AAAO,MAAMA,IAAkB,IAElBC,IAAoC,IAEpCC,IAAsB,IAEtBC,IAAqB,KAErBC,IACX,mEAAmE,KAAA,CAAM,EAAE,GAEvEC,IAAgB,MAAM,IAAA,CAAK;IAAE,QAAQ;AAAG,GAAG,CAACC,GAAGC,IAAMA,CAAC,GACtDC,KAAiBC,IACrB,MAAcA,CAAM,EAAE,IAAA,CAAK,CAAA,CAAE,GAElBC,IAAe,CAC1B;OAAGF,EAAc,EAAE;IACnB;IACA,EACA;OAAGH,EAAc,KAAA,CAAM,IAAI,EAAE,EAC7B;OAAGG,EAAc,CAAC,EAClB;OAAGH,EAAc,KAAA,CAAM,GAAG,EAAE,EAC5B;OAAGG,EAAc,CAAC,EAClB;OAAGH,EAAc,KAAA,CAAM,IAAI,EAAE,EAC7B;OAAGG,EAAc,CAAC,CACpB;CAAA,EAEaG,IAAS;IACpB;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY,UAC9D;CAAA,EAEaC,IAAS;IACpB;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY;IAAY;IAAY;IAC5D;IAAY;IAAY;IAAY,SACtC;CAAA,EAEaC,IAAS;IACpB;IAAY;IAAY;IAAY;IAAY;IAAY,UAC9D;CAAA,ECzMaC,IAAe,CAC1BC,GACAN,IACW;IACX,IAAIA,KAAU,KAAKA,IAASM,EAAU,MAAA,EACpC,MAAM,MAAM,CAAA,aAAA,EAAgBN,CAAM,EAAE;IAEtC,IAAIO,IAAM,GACNC,GACAC;IACJ,MAAMC,IAAmB,CAAC,CAAA;IAE1B,MAAOH,IAAMP,GAAQ;QAInB,IAHAQ,IAAKF,CAAAA,CAAUC,GAAK,CAAA,GAAI,KACxBG,EAAO,IAAA,CAAKf,CAAAA,CAAaa,KAAM,IAAK,EAAI,CAAC,GACzCA,IAAAA,CAAMA,IAAK,CAAA,KAAS,GAChBD,KAAOP,GAAQ;YACjBU,EAAO,IAAA,CAAKf,CAAAA,CAAYa,IAAK,EAAI,CAAC;YAClC;QACF;QAKA,IAJAC,IAAKH,CAAAA,CAAUC,GAAK,CAAA,GAAI,KACxBC,KAAOC,KAAM,IAAK,IAClBC,EAAO,IAAA,CAAKf,CAAAA,CAAYa,IAAK,EAAI,CAAC,GAClCA,IAAAA,CAAMC,IAAK,EAAA,KAAS,GAChBF,KAAOP,GAAQ;YACjBU,EAAO,IAAA,CAAKf,CAAAA,CAAYa,IAAK,EAAI,CAAC;YAClC;QACF;QACAC,IAAKH,CAAAA,CAAUC,GAAK,CAAA,GAAI,KACxBC,KAAOC,KAAM,IAAK,GAClBC,EAAO,IAAA,CAAKf,CAAAA,CAAYa,IAAK,EAAI,CAAC,GAClCE,EAAO,IAAA,CAAKf,CAAAA,CAAYc,IAAK,EAAI,CAAC;IACpC;IAEA,OAAOC,EAAO,IAAA,CAAK,EAAE;AACvB,GAQaC,IAAe,CAC1BC,GACAZ,IACa;IAGb,MAAMa,IAAeD,EAAc,MAAA;IACnC,IAAIL,IAAM,GACNO,IAAO,GACPN,GACAC,GACAM,GACAC,GACAC,GACAC;IACJ,MAAMR,IAAmB,CAAA,CAAA;IAEzB,MAAOH,IAAMM,IAAe,KAAKC,IAAOd,KAAAA,CACtCkB,IAAON,EAAc,UAAA,CAAWL,GAAK,GACrCC,IAAKU,IAAOjB,EAAa,MAAA,GAASA,CAAAA,CAAaiB,CAAI,CAAA,GAAI,CAAA,GACvDA,IAAON,EAAc,UAAA,CAAWL,GAAK,GACrCE,IAAKS,IAAOjB,EAAa,MAAA,GAASA,CAAAA,CAAaiB,CAAI,CAAA,GAAI,CAAA,GAEnD,CAAA,CAAAV,KAAM,CAAA,KAAMC,KAAM,CAAA,KAAA,CAEtBQ,IAAKT,KAAM,MAAO,GAClBS,KAAAA,CAAMR,IAAK,EAAA,KAAS,GACpBC,EAAO,IAAA,CAAK,OAAO,YAAA,CAAaO,CAAC,CAAC,GAE9B,EAAEH,KAAQd,KAAUO,KAAOM,CAAAA,KAAAA,CAE/BK,IAAON,EAAc,UAAA,CAAWL,GAAK,GACrCQ,IAAKG,IAAOjB,EAAa,MAAA,GAASA,CAAAA,CAAaiB,CAAI,CAAA,GAAI,CAAA,GACnDH,KAAM,CAAA,CAAA,KAAA,CACVE,IAAAA,CAAMR,IAAK,EAAA,KAAS,MAAO,GAC3BQ,KAAAA,CAAMF,IAAK,EAAA,KAAS,GACpBL,EAAO,IAAA,CAAK,OAAO,YAAA,CAAaO,CAAC,CAAC,GAE9B,EAAEH,KAAQd,KAAUO,KAAOM,CAAAA,CAAAA,CAAAA,GAE/BK,IAAON,EAAc,UAAA,CAAWL,GAAK,GACrCS,IAAKE,IAAOjB,EAAa,MAAA,GAASA,CAAAA,CAAaiB,CAAI,CAAA,GAAI,CAAA,GACvDD,IAAAA,CAAMF,IAAK,CAAA,KAAS,MAAO,GAC3BE,KAAKD,GACLN,EAAO,IAAA,CAAK,OAAO,YAAA,CAAaO,CAAC,CAAC,GAElC,EAAEH;IAGJ,OAAOJ,EAAO,GAAA,CAAKS,KAASA,EAAK,UAAA,CAAW,CAAC,CAAC;AAChD,GCvFaC,IAAa,CACxBC,GACAC,IACS;IACT,IAAIC,IAAK;IAOT,IALI,OAAOF,KAAa,YAAA,CACtBE,IAAKF,GACLA,IAAW,IAAY,IAAA,GAGlBE,MAAO,QAAA,CAASA,IAAKF,EAAAA,CAAAA,MAAgB,MACtCE,IAAK,MAAMD,EAAYC,IAAK,GAAI,IAC3BA,IAAK,OAAA,CACZD,EAAcC,KAAM,IAAK,KAAQ,GAAI,GACrCD,EAAaC,IAAK,KAAQ,GAAI,CAAA,IACrBA,IAAK,QAAA,CACdD,EAAcC,KAAM,KAAM,KAAQ,GAAI,GACtCD,EAAcC,KAAM,IAAK,KAAQ,GAAI,GACrCD,EAAaC,IAAK,KAAQ,GAAI,CAAA,IAAA,CAE9BD,EAAcC,KAAM,KAAM,IAAQ,GAAI,GACtCD,EAAcC,KAAM,KAAM,KAAQ,GAAI,GACtCD,EAAcC,KAAM,IAAK,KAAQ,GAAI,GACrCD,EAAaC,IAAK,KAAQ,GAAI,CAAA,GAEhCA,IAAK;AAET,GA0EaC,IAAc,CACzBH,GACAC,IACS;IACT,IAAId,GACAC,IAAK;IAET,MAAA,CAEOD,IAAKC,MAAO,OAAOA,IAAKY,GAAAA,MAAgB,MAFlC;QAGX,IAAIb,KAAM,SAAUA,KAAM,SAAA,CACnBC,IAAKY,GAAAA,MAAgB,QACpBZ,KAAM,SAAUA,KAAM,OAAQ;YAChCa,EAAAA,CAAad,IAAK,KAAA,IAAU,OAAQC,IAAK,QAAS,KAAO,GACzDA,IAAK;YACL;QACF;QAGJa,EAAYd,CAAE;IAChB;IACIC,MAAO,QAAMa,EAAYb,CAAE;AACjC,GAsCagB,IAAoB,CAC/BJ,GACAC,IAEAE,EAAYH,IAAWK,GAAc;QACnCN,EAAWM,GAAWJ,CAAW;IACnC,CAAC,GC/KUK,IACX,OAAO,SAAY,UAAY,QAAQ,IAAI,eAAiB,OACxD,iBACA,OAAO,gBAAiB,aACtB,eACA,OAAO,WAAY,YAAY,OAAO,QAAQ,QAAA,IAAa,aAEzD,QAAQ,QAAA,GACR,YAUGC,IAAiBC,GAA0B;IACtD,IAAIC,IAAQ;IACZ,MAAMC,IAAkB,CAAA,CAAA;IAExB,OAAAN,EACE,IAAOK,IAAQD,EAAI,MAAA,GAASA,EAAI,UAAA,CAAWC,GAAO,IAAI,OACrDE,GAAS;QACRD,EAAM,IAAA,CAAKC,CAAI;IACjB,CACF,GAEOD;AACT,GCzBME,IAAW,CACfC,GACA3B,GACA4B,GACAC,IACa;IAEb,IAAIC,GACAC,IAAIJ,CAAAA,CAAG3B,CAAG,CAAA,EACVgC,IAAIL,CAAAA,CAAG3B,IAAM,CAAC,CAAA;IAElB,OAAA+B,KAAKH,CAAAA,CAAE,CAAC,CAAA,EAIRE,IAAID,CAAAA,CAAEE,MAAM,EAAE,CAAA,EACdD,KAAKD,CAAAA,CAAE,MAAUE,KAAK,KAAM,GAAK,CAAA,EACjCD,KAAKD,CAAAA,CAAE,MAAUE,KAAK,IAAK,GAAK,CAAA,EAChCD,KAAKD,CAAAA,CAAE,MAASE,IAAI,GAAK,CAAA,EACzBC,KAAKF,IAAIF,CAAAA,CAAE,CAAC,CAAA,EACZE,IAAID,CAAAA,CAAEG,MAAM,EAAE,CAAA,EACdF,KAAKD,CAAAA,CAAE,MAAUG,KAAK,KAAM,GAAK,CAAA,EACjCF,KAAKD,CAAAA,CAAE,MAAUG,KAAK,IAAK,GAAK,CAAA,EAChCF,KAAKD,CAAAA,CAAE,MAASG,IAAI,GAAK,CAAA,EACzBD,KAAKD,IAAIF,CAAAA,CAAE,CAAC,CAAA,EAEZE,IAAID,CAAAA,CAAEE,MAAM,EAAE,CAAA,EACdD,KAAKD,CAAAA,CAAE,MAAUE,KAAK,KAAM,GAAK,CAAA,EACjCD,KAAKD,CAAAA,CAAE,MAAUE,KAAK,IAAK,GAAK,CAAA,EAChCD,KAAKD,CAAAA,CAAE,MAASE,IAAI,GAAK,CAAA,EACzBC,KAAKF,IAAIF,CAAAA,CAAE,CAAC,CAAA,EACZE,IAAID,CAAAA,CAAEG,MAAM,EAAE,CAAA,EACdF,KAAKD,CAAAA,CAAE,MAAUG,KAAK,KAAM,GAAK,CAAA,EACjCF,KAAKD,CAAAA,CAAE,MAAUG,KAAK,IAAK,GAAK,CAAA,EAChCF,KAAKD,CAAAA,CAAE,MAASG,IAAI,GAAK,CAAA,EACzBD,KAAKD,IAAIF,CAAAA,CAAE,CAAC,CAAA,EAEZE,IAAID,CAAAA,CAAEE,MAAM,EAAE,CAAA,EACdD,KAAKD,CAAAA,CAAE,MAAUE,KAAK,KAAM,GAAK,CAAA,EACjCD,KAAKD,CAAAA,CAAE,MAAUE,KAAK,IAAK,GAAK,CAAA,EAChCD,KAAKD,CAAAA,CAAE,MAASE,IAAI,GAAK,CAAA,EACzBC,KAAKF,IAAIF,CAAAA,CAAE,CAAC,CAAA,EACZE,IAAID,CAAAA,CAAEG,MAAM,EAAE,CAAA,EACdF,KAAKD,CAAAA,CAAE,MAAUG,KAAK,KAAM,GAAK,CAAA,EACjCF,KAAKD,CAAAA,CAAE,MAAUG,KAAK,IAAK,GAAK,CAAA,EAChCF,KAAKD,CAAAA,CAAE,MAASG,IAAI,GAAK,CAAA,EACzBD,KAAKD,IAAIF,CAAAA,CAAE,CAAC,CAAA,EAEZE,IAAID,CAAAA,CAAEE,MAAM,EAAE,CAAA,EACdD,KAAKD,CAAAA,CAAE,MAAUE,KAAK,KAAM,GAAK,CAAA,EACjCD,KAAKD,CAAAA,CAAE,MAAUE,KAAK,IAAK,GAAK,CAAA,EAChCD,KAAKD,CAAAA,CAAE,MAASE,IAAI,GAAK,CAAA,EACzBC,KAAKF,IAAIF,CAAAA,CAAE,CAAC,CAAA,EACZE,IAAID,CAAAA,CAAEG,MAAM,EAAE,CAAA,EACdF,KAAKD,CAAAA,CAAE,MAAUG,KAAK,KAAM,GAAK,CAAA,EACjCF,KAAKD,CAAAA,CAAE,MAAUG,KAAK,IAAK,GAAK,CAAA,EAChCF,KAAKD,CAAAA,CAAE,MAASG,IAAI,GAAK,CAAA,EACzBD,KAAKD,IAAIF,CAAAA,CAAE,CAAC,CAAA,EAEZE,IAAID,CAAAA,CAAEE,MAAM,EAAE,CAAA,EACdD,KAAKD,CAAAA,CAAE,MAAUE,KAAK,KAAM,GAAK,CAAA,EACjCD,KAAKD,CAAAA,CAAE,MAAUE,KAAK,IAAK,GAAK,CAAA,EAChCD,KAAKD,CAAAA,CAAE,MAASE,IAAI,GAAK,CAAA,EACzBC,KAAKF,IAAIF,CAAAA,CAAE,CAAC,CAAA,EACZE,IAAID,CAAAA,CAAEG,MAAM,EAAE,CAAA,EACdF,KAAKD,CAAAA,CAAE,MAAUG,KAAK,KAAM,GAAK,CAAA,EACjCF,KAAKD,CAAAA,CAAE,MAAUG,KAAK,IAAK,GAAK,CAAA,EAChCF,KAAKD,CAAAA,CAAE,MAASG,IAAI,GAAK,CAAA,EACzBD,KAAKD,IAAIF,CAAAA,CAAE,EAAE,CAAA,EAEbE,IAAID,CAAAA,CAAEE,MAAM,EAAE,CAAA,EACdD,KAAKD,CAAAA,CAAE,MAAUE,KAAK,KAAM,GAAK,CAAA,EACjCD,KAAKD,CAAAA,CAAE,MAAUE,KAAK,IAAK,GAAK,CAAA,EAChCD,KAAKD,CAAAA,CAAE,MAASE,IAAI,GAAK,CAAA,EACzBC,KAAKF,IAAIF,CAAAA,CAAE,EAAE,CAAA,EACbE,IAAID,CAAAA,CAAEG,MAAM,EAAE,CAAA,EACdF,KAAKD,CAAAA,CAAE,MAAUG,KAAK,KAAM,GAAK,CAAA,EACjCF,KAAKD,CAAAA,CAAE,MAAUG,KAAK,IAAK,GAAK,CAAA,EAChCF,KAAKD,CAAAA,CAAE,MAASG,IAAI,GAAK,CAAA,EACzBD,KAAKD,IAAIF,CAAAA,CAAE,EAAE,CAAA,EAEbE,IAAID,CAAAA,CAAEE,MAAM,EAAE,CAAA,EACdD,KAAKD,CAAAA,CAAE,MAAUE,KAAK,KAAM,GAAK,CAAA,EACjCD,KAAKD,CAAAA,CAAE,MAAUE,KAAK,IAAK,GAAK,CAAA,EAChCD,KAAKD,CAAAA,CAAE,MAASE,IAAI,GAAK,CAAA,EACzBC,KAAKF,IAAIF,CAAAA,CAAE,EAAE,CAAA,EACbE,IAAID,CAAAA,CAAEG,MAAM,EAAE,CAAA,EACdF,KAAKD,CAAAA,CAAE,MAAUG,KAAK,KAAM,GAAK,CAAA,EACjCF,KAAKD,CAAAA,CAAE,MAAUG,KAAK,IAAK,GAAK,CAAA,EAChCF,KAAKD,CAAAA,CAAE,MAASG,IAAI,GAAK,CAAA,EACzBD,KAAKD,IAAIF,CAAAA,CAAE,EAAE,CAAA,EAEbE,IAAID,CAAAA,CAAEE,MAAM,EAAE,CAAA,EACdD,KAAKD,CAAAA,CAAE,MAAUE,KAAK,KAAM,GAAK,CAAA,EACjCD,KAAKD,CAAAA,CAAE,MAAUE,KAAK,IAAK,GAAK,CAAA,EAChCD,KAAKD,CAAAA,CAAE,MAASE,IAAI,GAAK,CAAA,EACzBC,KAAKF,IAAIF,CAAAA,CAAE,EAAE,CAAA,EACbE,IAAID,CAAAA,CAAEG,MAAM,EAAE,CAAA,EACdF,KAAKD,CAAAA,CAAE,MAAUG,KAAK,KAAM,GAAK,CAAA,EACjCF,KAAKD,CAAAA,CAAE,MAAUG,KAAK,IAAK,GAAK,CAAA,EAChCF,KAAKD,CAAAA,CAAE,MAASG,IAAI,GAAK,CAAA,EACzBD,KAAKD,IAAIF,CAAAA,CAAE,EAAE,CAAA,EAEbD,CAAAA,CAAG3B,CAAG,CAAA,GAAIgC,IAAIJ,CAAAA,CAAE1C,IAAsB,CAAC,CAAA,EACvCyC,CAAAA,CAAG3B,IAAM,CAAC,CAAA,GAAI+B,GAEPJ;AACT,GAEMM,IAAe,CACnBC,GACAC,IACkC;IAClC,IAAIC,IAAO;IAEX,IAAA,IAAS7C,IAAI,GAAGA,IAAI,GAAG,EAAEA,EACvB6C,IAAQA,KAAQ,IAAMF,CAAAA,CAAKC,CAAI,CAAA,GAAI,KACnCA,IAAAA,CAAQA,IAAO,CAAA,IAAKD,EAAK,MAAA;IAG3B,OAAO;QAAE,KAAKE;QAAM,MAAAD;IAAK;AAC3B,GAEME,IAAM,CACVA,GACAT,GACAC,IACS;IACT,MAAMS,IAAUV,EAAE,MAAA,EACZW,IAAUV,EAAE,MAAA;IAClB,IAAIM,IAAO,GACPR,IAAK;QAAC;QAAG,CAAC;KAAA,EACVa;IAKJ,IAAA,IAASjD,IAAI,GAAGA,IAAI+C,GAAS/C,IAC3BiD,IAAKP,EAAaI,GAAKF,CAAI,GAC3BA,IAAOK,EAAG,IAAA,EACVZ,CAAAA,CAAErC,CAAC,CAAA,GAAIqC,CAAAA,CAAErC,CAAC,CAAA,GAAIiD,EAAG,GAAA;IAGnB,IAAA,IAASjD,IAAI,GAAGA,IAAI+C,GAAS/C,KAAK,EAChCoC,IAAKD,EAASC,GAAI,GAAGC,GAAGC,CAAC,GACzBD,CAAAA,CAAErC,CAAC,CAAA,GAAIoC,CAAAA,CAAG,CAAC,CAAA,EACXC,CAAAA,CAAErC,IAAI,CAAC,CAAA,GAAIoC,CAAAA,CAAG,CAAC,CAAA;IAGjB,IAAA,IAASpC,IAAI,GAAGA,IAAIgD,GAAShD,KAAK,EAChCoC,IAAKD,EAASC,GAAI,GAAGC,GAAGC,CAAC,GACzBA,CAAAA,CAAEtC,CAAC,CAAA,GAAIoC,CAAAA,CAAG,CAAC,CAAA,EACXE,CAAAA,CAAEtC,IAAI,CAAC,CAAA,GAAIoC,CAAAA,CAAG,CAAC;AAEnB,GAKMc,IAA+B,CACnCP,GACAG,GACAT,GACAC,IACS;IACT,MAAMS,IAAUV,EAAE,MAAA,EACZW,IAAUV,EAAE,MAAA;IAClB,IAAIM,IAAO,GACPR,IAAK;QAAC;QAAG,CAAC;KAAA,EACVa;IAKJ,IAAA,IAASjD,IAAI,GAAGA,IAAI+C,GAAS/C,IAC3BiD,IAAKP,EAAaI,GAAKF,CAAI,GAC3BA,IAAOK,EAAG,IAAA,EACVZ,CAAAA,CAAErC,CAAC,CAAA,GAAIqC,CAAAA,CAAErC,CAAC,CAAA,GAAIiD,EAAG,GAAA;IAGnBL,IAAO;IAEP,IAAA,IAAS5C,IAAI,GAAGA,IAAI+C,GAAS/C,KAAK,EAChCiD,IAAKP,EAAaC,GAAMC,CAAI,GAC5BA,IAAOK,EAAG,IAAA,EACVb,CAAAA,CAAG,CAAC,CAAA,IAAKa,EAAG,GAAA,EACZA,IAAKP,EAAaC,GAAMC,CAAI,GAC5BA,IAAOK,EAAG,IAAA,EACVb,CAAAA,CAAG,CAAC,CAAA,IAAKa,EAAG,GAAA,EACZb,IAAKD,EAASC,GAAI,GAAGC,GAAGC,CAAC,GACzBD,CAAAA,CAAErC,CAAC,CAAA,GAAIoC,CAAAA,CAAG,CAAC,CAAA,EACXC,CAAAA,CAAErC,IAAI,CAAC,CAAA,GAAIoC,CAAAA,CAAG,CAAC,CAAA;IAGjB,IAAA,IAASpC,IAAI,GAAGA,IAAIgD,GAAShD,KAAK,EAChCiD,IAAKP,EAAaC,GAAMC,CAAI,GAC5BA,IAAOK,EAAG,IAAA,EACVb,CAAAA,CAAG,CAAC,CAAA,IAAKa,EAAG,GAAA,EACZA,IAAKP,EAAaC,GAAMC,CAAI,GAC5BA,IAAOK,EAAG,IAAA,EACVb,CAAAA,CAAG,CAAC,CAAA,IAAKa,EAAG,GAAA,EACZb,IAAKD,EAASC,GAAI,GAAGC,GAAGC,CAAC,GACzBA,CAAAA,CAAEtC,CAAC,CAAA,GAAIoC,CAAAA,CAAG,CAAC,CAAA,EACXE,CAAAA,CAAEtC,IAAI,CAAC,CAAA,GAAIoC,CAAAA,CAAG,CAAC;AAEnB,GAUae,IAAQ,CACnBlB,GACAmB,GACAC,GACAC,GACAC,IACiC;IACjC,MAAMC,IAAQlD,EAAO,KAAA,CAAM,GACrBmD,IAAUD,EAAM,MAAA;IAGtB,IAAIH,IAAS,KAAKA,IAAS,IAAI;QAC7B,MAAMK,IAAM,IAAI,MAAM,CAAA,iCAAA,EAAoCL,CAAM,EAAE;QAElE,IAAIC,MAAS,CAAA,GAAO,OAAO,QAAQ,MAAA,CAAOI,CAAG;QAE7C,MAAMA;IACR;IAEA,IAAIN,EAAK,MAAA,KAAW3D,GAAiB;QACnC,MAAMiE,IAAM,IAAI,MACd,CAAA,qBAAA,EAAwBN,EAAK,MAAM,CAAA,IAAA,EAAO3D,CAAe,EAC3D;QAEA,IAAI6D,MAAS,CAAA,GAAO,OAAO,QAAQ,MAAA,CAAOI,CAAG;QAE7C,MAAMA;IACR;IAEAL,IAAU,KAAKA,MAAY;IAE3B,IAAIhB,GACAC,GACAtC,IAAI,GACJ2D;IAGA,aAAA,CACFtB,IAAI,IAAI,WAAWjC,CAAM,GACzBkC,IAAI,IAAI,WAAWjC,CAAM,CAAA,IAAA,CAEzBgC,IAAIjC,EAAO,KAAA,CACXkC,GAAAA,IAAIjC,EAAO,KAAA,CAGb6C,CAAAA,GAAAA,EAA6BE,GAAMnB,GAAOI,GAAGC,CAAC;IAK9C,MAAMsB,IAAO,IAAuD;QAGlE,IAFIL,KAAkBA,EAAiBvD,IAAIqD,CAAM,GAE7CrD,IAAIqD,GAAQ;YACd,MAAMQ,IAAQ,KAAK,GAAA,CAAI;YAEvB,MAAO7D,IAAIqD,KAAAA,CACTrD,IAAIA,IAAI,GACR8C,EAAIb,GAAOI,GAAGC,CAAC,GACfQ,EAAIM,GAAMf,GAAGC,CAAC,GACV,CAAA,CAAA,KAAK,GAAA,CAAQuB,IAAAA,IAAQjE,CAAAA,CAAAA,GAAzB;QAEJ,OAAO;YACL,IAAKI,IAAI,GAAGA,IAAI,IAAIA,IAClB,IAAK2D,IAAI,GAAGA,IAAIF,KAAW,GAAGE,IAAKxB,EAASqB,GAAOG,KAAK,GAAGtB,GAAGC,CAAC;YACjE,MAAM1B,IAAmB,CAAA,CAAA;YAEzB,IAAKZ,IAAI,GAAGA,IAAIyD,GAASzD,IACvBY,EAAO,IAAA,CAAA,CAAO4C,CAAAA,CAAMxD,CAAC,CAAA,IAAK,KAAM,GAAA,MAAU,CAAC,GAC3CY,EAAO,IAAA,CAAA,CAAO4C,CAAAA,CAAMxD,CAAC,CAAA,IAAK,KAAM,GAAA,MAAU,CAAC,GAC3CY,EAAO,IAAA,CAAA,CAAO4C,CAAAA,CAAMxD,CAAC,CAAA,IAAK,IAAK,GAAA,MAAU,CAAC,GAC1CY,EAAO,IAAA,CAAA,CAAM4C,CAAAA,CAAMxD,CAAC,CAAA,GAAI,GAAA,MAAU,CAAC;YAGrC,OAAIsD,MAAS,CAAA,IAAc,QAAQ,OAAA,CAAQ1C,CAAM,IAE1CA;QACT;QAEA,IAAI0C,MAAS,CAAA,GACX,OAAO,IAAI,SAASQ,IAClBjC,EAAS,IAAM;gBACP+B,EAAAA,EAAyC,IAAA,CAAKE,CAAO;YAC7D,CAAC,CACH;IACJ;IAEA,IAAIR,MAAS,CAAA,GAAO,OAAOM,EAAAA;IACtB;QACH,IAAIG;QAEJ,OACE,IAAI,OAAA,CAAQA,IAAMH,EAAK,CAAA,IAAO,KAAa,OAAQG,KAAoB,CAC3E;IAAA;AACF,GCtTaC,IAAU9D,6IAA2B+D,EAAY/D,CAAM,GCMvDgE,IAAc,CACzBb,IAAS3D,CAAAA,GACE;IACX,IAAI,OAAO2D,KAAW,UACpB,MAAM,MAAM,wBAAwB,OAAOA,CAAM;IAC/CA,IAAS,IAAGA,IAAS,IAChBA,IAAS,MAAA,CAAIA,IAAS,EAAA;IAE/B,MAAMD,IAAO,CAAA,CAAA;IAEb,OAAAA,EAAK,IAAA,CAAK,MAAM,GACZC,IAAS,MAAID,EAAK,IAAA,CAAK,GAAG,GAC9BA,EAAK,IAAA,CAAKC,EAAO,QAAA,EAAU,GAC3BD,EAAK,IAAA,CAAK,GAAG,GACbA,EAAK,IAAA,CAAK7C,EAAayD,EAAOvE,CAAe,GAAGA,CAAe,CAAC,GAEzD2D,EAAK,IAAA,CAAK,EAAE;AACrB,GAOae,IAAU,CACrBd,IAAS3D,CAAAA,GACW;IACpB,IAAI,OAAO2D,KAAW,UACpB,MAAM,MAAM,wBAAwB,OAAOA,CAAM;IAEnD,OAAO,IAAI,QAAQ,CAACS,GAASM,IAC3BvC,EAAS,IAAM;YAEb,IAAI;gBACFiC,EAAQI,EAAYb,CAAM,CAAC;YAC7B,EAAA,OAASK,GAAK;gBACZU,EAAOV,CAAY;YACrB;QACF,CAAC,CACH;AACF;ACvCA,SAASW,EACPvD,CAAAA,EACAsC,CAAAA,EACAE,CAAAA,EACAC,CAAAA,CAC0B;IAC1B,IAAI,OAAOzC,KAAkB,YAAY,OAAOsC,KAAS,UAAU;QACjE,MAAMM,IAAM,IAAI,MAAM,qCAAqC;QAE3D,IAAIJ,MAAS,CAAA,GAAO,OAAO,QAAQ,MAAA,CAAOI,CAAG;QAE7C,MAAMA;IACR;IAGA,IAAIY,GACAC;IAEJ,IAAInB,EAAK,MAAA,CAAO,CAAC,MAAM,OAAOA,EAAK,MAAA,CAAO,CAAC,MAAM,KAAK;QACpD,MAAMM,IAAM,IAAI,MAAM,2BAA2BN,EAAK,SAAA,CAAU,GAAG,CAAC,CAAC;QAErE,IAAIE,MAAS,CAAA,GAAO,OAAO,QAAQ,MAAA,CAAOI,CAAG;QAE7C,MAAMA;IACR;IAEA,IAAIN,EAAK,MAAA,CAAO,CAAC,MAAM,KACrBkB,IAAQ,MACRC,IAAS;SACJ;QAEL,IADAD,IAAQlB,EAAK,MAAA,CAAO,CAAC,GAElBkB,MAAU,OAAOA,MAAU,OAAOA,MAAU,OAC7ClB,EAAK,MAAA,CAAO,CAAC,MAAM,KACnB;YACA,MAAMM,IAAM,MAAM,4BAA4BN,EAAK,SAAA,CAAU,GAAG,CAAC,CAAC;YAElE,IAAIE,MAAS,CAAA,GAAO,OAAO,QAAQ,MAAA,CAAOI,CAAG;YAE7C,MAAMA;QACR;QACAa,IAAS;IACX;IAGA,IAAInB,EAAK,MAAA,CAAOmB,IAAS,CAAC,IAAI,KAAK;QACjC,MAAMb,IAAM,IAAI,MAAM,qBAAqB;QAE3C,IAAIJ,MAAS,CAAA,GAAO,OAAO,QAAQ,MAAA,CAAOI,CAAG;QAE7C,MAAMA;IACR;IAEA,MAAMc,IAAK,SAASpB,EAAK,SAAA,CAAUmB,GAAQA,IAAS,CAAC,GAAG,EAAE,IAAI,IAC5DE,IAAK,SAASrB,EAAK,SAAA,CAAUmB,IAAS,GAAGA,IAAS,CAAC,GAAG,EAAE,GACxDlB,IAASmB,IAAKC,GACdC,IAAWtB,EAAK,SAAA,CAAUmB,IAAS,GAAGA,IAAS,EAAE;IAEnDzD,KAAiBwD,KAAS,MAAM,OAAS;IAEzC,MAAMK,IAAgB7C,EAAchB,CAAa,GAC/C8D,IAAY/D,EAAa6D,GAAUjF,CAAe,GAM9CoF,KAAU5C,GAA4B;QAC1C,MAAM8B,IAAM,CAAA,CAAA;QAEZ,OAAAA,EAAI,IAAA,CAAK,IAAI,GACTO,KAAS,OAAKP,EAAI,IAAA,CAAKO,CAAK,GAChCP,EAAI,IAAA,CAAK,GAAG,GACRV,IAAS,MAAIU,EAAI,IAAA,CAAK,GAAG,GAC7BA,EAAI,IAAA,CAAKV,EAAO,QAAA,EAAU,GAC1BU,EAAI,IAAA,CAAK,GAAG,GACZA,EAAI,IAAA,CAAKxD,EAAaqE,GAAWA,EAAU,MAAM,CAAC,GAClDb,EAAI,IAAA,CAAKxD,EAAa0B,GAAO3B,EAAO,MAAA,GAAS,IAAI,CAAC,CAAC,GAE5CyD,EAAI,IAAA,CAAK,EAAE;IACpB;IAGA,OAAIT,MAAS,CAAA,IAETH,EACEwB,GACAC,GACAvB,GACA,CAAA,GACAE,CACF,EACA,IAAA,EAAMtB,IAAU4C,EAAO5C,CAAK,CAAC,IAE1B4C,EACL1B,EAAMwB,GAAeC,GAAWvB,GAAQ,CAAA,GAAME,CAAgB,CAChE;AACF;AAAA,MASauB,IAAW,CACtBhE,GACAsC,IAAwB1D,CAAAA,GACb;IAEX,IADI,OAAO0D,KAAS,YAAA,CAAUA,IAAOc,EAAYd,CAAI,CAAA,GACjD,OAAOtC,KAAkB,YAAY,OAAOsC,KAAS,UACvD,MAAM,MACJ,wBAAwB,OAAOtC,IAAgB,OAAO,OAAOsC,CAC/D;IAEF,OAAOiB,EAAMvD,GAAesC,GAAM,CAAA,CAAI;AACxC,GAUa2B,IAAO,SAClBjE,CAAAA,EACAsC,CAAAA,EACAG,CAAAA,CACiB;IACjB,OAAI,OAAOzC,KAAkB,YAAY,OAAOsC,KAAS,WAChDe,EAAQf,CAAI,EAAE,IAAA,EAClBA,IACCiB,EAAMvD,GAAesC,GAAM,CAAA,GAAOG,CAAgB,CACtD,IAEE,OAAOzC,KAAkB,YAAY,OAAOsC,KAAS,WAChDiB,EACLvD,GACAsC,GACA,CAAA,GACAG,CACF,IAEK,QAAQ,MAAA,CACb,IAAI,MAAM,CAAA,mBAAA,EAAsB,OAAOzC,CAAa,CAAA,EAAA,EAAK,OAAOsC,CAAI,EAAE,CACxE;AACF,GC9Ja4B,IAAc,CAACC,GAAiBF,IAA0B;IACrE,IAAI,OAAOE,KAAY,YAAY,OAAOF,KAAS,UACjD,MAAM,MAAM,wBAAwB,OAAOE,IAAU,OAAO,OAAOF,CAAI;IACzE,OAAIA,EAAK,MAAA,KAAW,KAAW,CAAA,IAExBD,EAASG,GAASF,EAAK,SAAA,CAAU,GAAGA,EAAK,MAAA,GAAS,EAAE,CAAC,MAAMA;AACpE,GAUaG,IAAU,CACrBD,GACAF,GACAxB,IAEA,IAAI,QAAQ,CAACO,GAASM,IAAW;QAC/B,IAAI,OAAOa,KAAY,YAAY,OAAOF,KAAS,UAAU;YAC3DlD,EAAS,IACPuC,EACE,IAAI,MAAM,CAAA,mBAAA,EAAsB,OAAOa,CAAO,CAAA,EAAA,EAAK,OAAOF,CAAI,EAAE,CAClE,CACF;YAEA;QACF;QAEA,IAAIA,EAAK,MAAA,KAAW,IAAI;YACtBlD,EAAS,IACPuC,EAAO,IAAI,MAAM,wCAAwC,CAAC,CAC5D;YAEA;QACF;QAEAe,EAAUF,GAASF,EAAK,SAAA,CAAU,GAAG,EAAE,GAAGxB,CAAgB,EACvD,IAAA,EAAM6B,IAAStB,EAAQsB,MAASL,CAAI,CAAC,EACrC,KAAA,EAAOrB,IAAeU,EAAOV,CAAG,CAAC;IACtC,CAAC,GC7CU2B,KAAaN,GAAyB;IACjD,IAAI,OAAOA,KAAS,UAClB,MAAM,IAAI,MAAM,CAAA,mBAAA,EAAsB,OAAOA,CAAI,EAAE;IAErD,OAAO,SAASA,EAAK,KAAA,CAAM,GAAG,CAAA,CAAE,CAAC,CAAA,EAAG,EAAE;AACxC,GASaO,KAAWP,GAAyB;IAC/C,IAAI,OAAOA,KAAS,UAClB,MAAM,IAAI,MAAM,CAAA,mBAAA,EAAsB,OAAOA,CAAI,EAAE;IAErD,IAAIA,EAAK,MAAA,KAAW,IAClB,MAAM,IAAI,MAAM,CAAA,qBAAA,EAAwBA,EAAK,MAAM,CAAA,MAAA,CAAQ;IAE7D,OAAOA,EAAK,SAAA,CAAU,GAAG,EAAE;AAC7B", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9], "debugId": null}}, {"offset": {"line": 1271, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/Bonkai/node_modules/%40panva/hkdf/dist/node/esm/runtime/fallback.js"], "sourcesContent": ["import { createHmac } from 'crypto';\nexport default (digest, ikm, salt, info, keylen) => {\n    const hashlen = parseInt(digest.substr(3), 10) >> 3 || 20;\n    const prk = createHmac(digest, salt.byteLength ? salt : new Uint8Array(hashlen))\n        .update(ikm)\n        .digest();\n    const N = Math.ceil(keylen / hashlen);\n    const T = new Uint8Array(hashlen * N + info.byteLength + 1);\n    let prev = 0;\n    let start = 0;\n    for (let c = 1; c <= N; c++) {\n        T.set(info, start);\n        T[start + info.byteLength] = c;\n        T.set(createHmac(digest, prk)\n            .update(T.subarray(prev, start + info.byteLength + 1))\n            .digest(), start);\n        prev = start;\n        start += hashlen;\n    }\n    return T.slice(0, keylen);\n};\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAC,QAAQ,KAAK,MAAM,MAAM;IACrC,MAAM,UAAU,SAAS,OAAO,MAAM,CAAC,IAAI,OAAO,KAAK;IACvD,MAAM,MAAM,CAAA,GAAA,qGAAA,CAAA,aAAU,AAAD,EAAE,QAAQ,KAAK,UAAU,GAAG,OAAO,IAAI,WAAW,UAClE,MAAM,CAAC,KACP,MAAM;IACX,MAAM,IAAI,KAAK,IAAI,CAAC,SAAS;IAC7B,MAAM,IAAI,IAAI,WAAW,UAAU,IAAI,KAAK,UAAU,GAAG;IACzD,IAAI,OAAO;IACX,IAAI,QAAQ;IACZ,IAAK,IAAI,IAAI,GAAG,KAAK,GAAG,IAAK;QACzB,EAAE,GAAG,CAAC,MAAM;QACZ,CAAC,CAAC,QAAQ,KAAK,UAAU,CAAC,GAAG;QAC7B,EAAE,GAAG,CAAC,CAAA,GAAA,qGAAA,CAAA,aAAU,AAAD,EAAE,QAAQ,KACpB,MAAM,CAAC,EAAE,QAAQ,CAAC,MAAM,QAAQ,KAAK,UAAU,GAAG,IAClD,MAAM,IAAI;QACf,OAAO;QACP,SAAS;IACb;IACA,OAAO,EAAE,KAAK,CAAC,GAAG;AACtB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1298, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/Bonkai/node_modules/%40panva/hkdf/dist/node/esm/runtime/hkdf.js"], "sourcesContent": ["import * as crypto from 'crypto';\nimport fallback from './fallback.js';\nlet hkdf;\nif (typeof crypto.hkdf === 'function' && !process.versions.electron) {\n    hkdf = async (...args) => new Promise((resolve, reject) => {\n        crypto.hkdf(...args, (err, arrayBuffer) => {\n            if (err)\n                reject(err);\n            else\n                resolve(new Uint8Array(arrayBuffer));\n        });\n    });\n}\nexport default async (digest, ikm, salt, info, keylen) => (hkdf || fallback)(digest, ikm, salt, info, keylen);\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AACA,IAAI;AACJ,IAAI,OAAO,qGAAA,CAAA,OAAW,KAAK,cAAc,CAAC,QAAQ,QAAQ,CAAC,QAAQ,EAAE;IACjE,OAAO,OAAO,GAAG,OAAS,IAAI,QAAQ,CAAC,SAAS;YAC5C,CAAA,GAAA,qGAAA,CAAA,OAAW,AAAD,KAAK,MAAM,CAAC,KAAK;gBACvB,IAAI,KACA,OAAO;qBAEP,QAAQ,IAAI,WAAW;YAC/B;QACJ;AACJ;uCACe,OAAO,QAAQ,KAAK,MAAM,MAAM,SAAW,CAAC,QAAQ,+KAAA,CAAA,UAAQ,EAAE,QAAQ,KAAK,MAAM,MAAM", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1321, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/Bonkai/node_modules/%40panva/hkdf/dist/node/esm/index.js"], "sourcesContent": ["import derive from './runtime/hkdf.js';\nfunction normalizeDigest(digest) {\n    switch (digest) {\n        case 'sha256':\n        case 'sha384':\n        case 'sha512':\n        case 'sha1':\n            return digest;\n        default:\n            throw new TypeError('unsupported \"digest\" value');\n    }\n}\nfunction normalizeUint8Array(input, label) {\n    if (typeof input === 'string')\n        return new TextEncoder().encode(input);\n    if (!(input instanceof Uint8Array))\n        throw new TypeError(`\"${label}\"\" must be an instance of Uint8Array or a string`);\n    return input;\n}\nfunction normalizeIkm(input) {\n    const ikm = normalizeUint8Array(input, 'ikm');\n    if (!ikm.byteLength)\n        throw new TypeError(`\"ikm\" must be at least one byte in length`);\n    return ikm;\n}\nfunction normalizeInfo(input) {\n    const info = normalizeUint8Array(input, 'info');\n    if (info.byteLength > 1024) {\n        throw TypeError('\"info\" must not contain more than 1024 bytes');\n    }\n    return info;\n}\nfunction normalizeKeylen(input, digest) {\n    if (typeof input !== 'number' || !Number.isInteger(input) || input < 1) {\n        throw new TypeError('\"keylen\" must be a positive integer');\n    }\n    const hashlen = parseInt(digest.substr(3), 10) >> 3 || 20;\n    if (input > 255 * hashlen) {\n        throw new TypeError('\"keylen\" too large');\n    }\n    return input;\n}\nasync function hkdf(digest, ikm, salt, info, keylen) {\n    return derive(normalizeDigest(digest), normalizeIkm(ikm), normalizeUint8Array(salt, 'salt'), normalizeInfo(info), normalizeKeylen(keylen, digest));\n}\nexport { hkdf, hkdf as default };\n"], "names": [], "mappings": ";;;;AAAA;;AACA,SAAS,gBAAgB,MAAM;IAC3B,OAAQ;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACD,OAAO;QACX;YACI,MAAM,IAAI,UAAU;IAC5B;AACJ;AACA,SAAS,oBAAoB,KAAK,EAAE,KAAK;IACrC,IAAI,OAAO,UAAU,UACjB,OAAO,IAAI,cAAc,MAAM,CAAC;IACpC,IAAI,CAAC,CAAC,iBAAiB,UAAU,GAC7B,MAAM,IAAI,UAAU,CAAC,CAAC,EAAE,MAAM,gDAAgD,CAAC;IACnF,OAAO;AACX;AACA,SAAS,aAAa,KAAK;IACvB,MAAM,MAAM,oBAAoB,OAAO;IACvC,IAAI,CAAC,IAAI,UAAU,EACf,MAAM,IAAI,UAAU,CAAC,yCAAyC,CAAC;IACnE,OAAO;AACX;AACA,SAAS,cAAc,KAAK;IACxB,MAAM,OAAO,oBAAoB,OAAO;IACxC,IAAI,KAAK,UAAU,GAAG,MAAM;QACxB,MAAM,UAAU;IACpB;IACA,OAAO;AACX;AACA,SAAS,gBAAgB,KAAK,EAAE,MAAM;IAClC,IAAI,OAAO,UAAU,YAAY,CAAC,OAAO,SAAS,CAAC,UAAU,QAAQ,GAAG;QACpE,MAAM,IAAI,UAAU;IACxB;IACA,MAAM,UAAU,SAAS,OAAO,MAAM,CAAC,IAAI,OAAO,KAAK;IACvD,IAAI,QAAQ,MAAM,SAAS;QACvB,MAAM,IAAI,UAAU;IACxB;IACA,OAAO;AACX;AACA,eAAe,KAAK,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM;IAC/C,OAAO,CAAA,GAAA,2KAAA,CAAA,UAAM,AAAD,EAAE,gBAAgB,SAAS,aAAa,MAAM,oBAAoB,MAAM,SAAS,cAAc,OAAO,gBAAgB,QAAQ;AAC9I", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1374, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/Bonkai/node_modules/%40auth/core/node_modules/cookie/index.js"], "sourcesContent": ["/*!\n * cookie\n * Copyright(c) 2012-2014 <PERSON>\n * Copyright(c) 2015 <PERSON>\n * MIT Licensed\n */\n\n'use strict';\n\n/**\n * Module exports.\n * @public\n */\n\nexports.parse = parse;\nexports.serialize = serialize;\n\n/**\n * Module variables.\n * @private\n */\n\nvar __toString = Object.prototype.toString\n\n/**\n * RegExp to match cookie-name in RFC 6265 sec 4.1.1\n * This refers out to the obsoleted definition of token in RFC 2616 sec 2.2\n * which has been replaced by the token definition in RFC 7230 appendix B.\n *\n * cookie-name       = token\n * token             = 1*tchar\n * tchar             = \"!\" / \"#\" / \"$\" / \"%\" / \"&\" / \"'\" /\n *                     \"*\" / \"+\" / \"-\" / \".\" / \"^\" / \"_\" /\n *                     \"`\" / \"|\" / \"~\" / DIGIT / ALPHA\n */\n\nvar cookieNameRegExp = /^[!#$%&'*+\\-.^_`|~0-9A-Za-z]+$/;\n\n/**\n * RegExp to match cookie-value in RFC 6265 sec 4.1.1\n *\n * cookie-value      = *cookie-octet / ( DQUOTE *cookie-octet DQUOTE )\n * cookie-octet      = %x21 / %x23-2B / %x2D-3A / %x3C-5B / %x5D-7E\n *                     ; US-ASCII characters excluding CTLs,\n *                     ; whitespace DQUOTE, comma, semicolon,\n *                     ; and backslash\n */\n\nvar cookieValueRegExp = /^(\"?)[\\u0021\\u0023-\\u002B\\u002D-\\u003A\\u003C-\\u005B\\u005D-\\u007E]*\\1$/;\n\n/**\n * RegExp to match domain-value in RFC 6265 sec 4.1.1\n *\n * domain-value      = <subdomain>\n *                     ; defined in [RFC1034], Section 3.5, as\n *                     ; enhanced by [RFC1123], Section 2.1\n * <subdomain>       = <label> | <subdomain> \".\" <label>\n * <label>           = <let-dig> [ [ <ldh-str> ] <let-dig> ]\n *                     Labels must be 63 characters or less.\n *                     'let-dig' not 'letter' in the first char, per RFC1123\n * <ldh-str>         = <let-dig-hyp> | <let-dig-hyp> <ldh-str>\n * <let-dig-hyp>     = <let-dig> | \"-\"\n * <let-dig>         = <letter> | <digit>\n * <letter>          = any one of the 52 alphabetic characters A through Z in\n *                     upper case and a through z in lower case\n * <digit>           = any one of the ten digits 0 through 9\n *\n * Keep support for leading dot: https://github.com/jshttp/cookie/issues/173\n *\n * > (Note that a leading %x2E (\".\"), if present, is ignored even though that\n * character is not permitted, but a trailing %x2E (\".\"), if present, will\n * cause the user agent to ignore the attribute.)\n */\n\nvar domainValueRegExp = /^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i;\n\n/**\n * RegExp to match path-value in RFC 6265 sec 4.1.1\n *\n * path-value        = <any CHAR except CTLs or \";\">\n * CHAR              = %x01-7F\n *                     ; defined in RFC 5234 appendix B.1\n */\n\nvar pathValueRegExp = /^[\\u0020-\\u003A\\u003D-\\u007E]*$/;\n\n/**\n * Parse a cookie header.\n *\n * Parse the given cookie header string into an object\n * The object has the various cookies as keys(names) => values\n *\n * @param {string} str\n * @param {object} [opt]\n * @return {object}\n * @public\n */\n\nfunction parse(str, opt) {\n  if (typeof str !== 'string') {\n    throw new TypeError('argument str must be a string');\n  }\n\n  var obj = {};\n  var len = str.length;\n  // RFC 6265 sec 4.1.1, RFC 2616 2.2 defines a cookie name consists of one char minimum, plus '='.\n  if (len < 2) return obj;\n\n  var dec = (opt && opt.decode) || decode;\n  var index = 0;\n  var eqIdx = 0;\n  var endIdx = 0;\n\n  do {\n    eqIdx = str.indexOf('=', index);\n    if (eqIdx === -1) break; // No more cookie pairs.\n\n    endIdx = str.indexOf(';', index);\n\n    if (endIdx === -1) {\n      endIdx = len;\n    } else if (eqIdx > endIdx) {\n      // backtrack on prior semicolon\n      index = str.lastIndexOf(';', eqIdx - 1) + 1;\n      continue;\n    }\n\n    var keyStartIdx = startIndex(str, index, eqIdx);\n    var keyEndIdx = endIndex(str, eqIdx, keyStartIdx);\n    var key = str.slice(keyStartIdx, keyEndIdx);\n\n    // only assign once\n    if (!obj.hasOwnProperty(key)) {\n      var valStartIdx = startIndex(str, eqIdx + 1, endIdx);\n      var valEndIdx = endIndex(str, endIdx, valStartIdx);\n\n      if (str.charCodeAt(valStartIdx) === 0x22 /* \" */ && str.charCodeAt(valEndIdx - 1) === 0x22 /* \" */) {\n        valStartIdx++;\n        valEndIdx--;\n      }\n\n      var val = str.slice(valStartIdx, valEndIdx);\n      obj[key] = tryDecode(val, dec);\n    }\n\n    index = endIdx + 1\n  } while (index < len);\n\n  return obj;\n}\n\nfunction startIndex(str, index, max) {\n  do {\n    var code = str.charCodeAt(index);\n    if (code !== 0x20 /*   */ && code !== 0x09 /* \\t */) return index;\n  } while (++index < max);\n  return max;\n}\n\nfunction endIndex(str, index, min) {\n  while (index > min) {\n    var code = str.charCodeAt(--index);\n    if (code !== 0x20 /*   */ && code !== 0x09 /* \\t */) return index + 1;\n  }\n  return min;\n}\n\n/**\n * Serialize data into a cookie header.\n *\n * Serialize a name value pair into a cookie string suitable for\n * http headers. An optional options object specifies cookie parameters.\n *\n * serialize('foo', 'bar', { httpOnly: true })\n *   => \"foo=bar; httpOnly\"\n *\n * @param {string} name\n * @param {string} val\n * @param {object} [opt]\n * @return {string}\n * @public\n */\n\nfunction serialize(name, val, opt) {\n  var enc = (opt && opt.encode) || encodeURIComponent;\n\n  if (typeof enc !== 'function') {\n    throw new TypeError('option encode is invalid');\n  }\n\n  if (!cookieNameRegExp.test(name)) {\n    throw new TypeError('argument name is invalid');\n  }\n\n  var value = enc(val);\n\n  if (!cookieValueRegExp.test(value)) {\n    throw new TypeError('argument val is invalid');\n  }\n\n  var str = name + '=' + value;\n  if (!opt) return str;\n\n  if (null != opt.maxAge) {\n    var maxAge = Math.floor(opt.maxAge);\n\n    if (!isFinite(maxAge)) {\n      throw new TypeError('option maxAge is invalid')\n    }\n\n    str += '; Max-Age=' + maxAge;\n  }\n\n  if (opt.domain) {\n    if (!domainValueRegExp.test(opt.domain)) {\n      throw new TypeError('option domain is invalid');\n    }\n\n    str += '; Domain=' + opt.domain;\n  }\n\n  if (opt.path) {\n    if (!pathValueRegExp.test(opt.path)) {\n      throw new TypeError('option path is invalid');\n    }\n\n    str += '; Path=' + opt.path;\n  }\n\n  if (opt.expires) {\n    var expires = opt.expires\n\n    if (!isDate(expires) || isNaN(expires.valueOf())) {\n      throw new TypeError('option expires is invalid');\n    }\n\n    str += '; Expires=' + expires.toUTCString()\n  }\n\n  if (opt.httpOnly) {\n    str += '; HttpOnly';\n  }\n\n  if (opt.secure) {\n    str += '; Secure';\n  }\n\n  if (opt.partitioned) {\n    str += '; Partitioned'\n  }\n\n  if (opt.priority) {\n    var priority = typeof opt.priority === 'string'\n      ? opt.priority.toLowerCase() : opt.priority;\n\n    switch (priority) {\n      case 'low':\n        str += '; Priority=Low'\n        break\n      case 'medium':\n        str += '; Priority=Medium'\n        break\n      case 'high':\n        str += '; Priority=High'\n        break\n      default:\n        throw new TypeError('option priority is invalid')\n    }\n  }\n\n  if (opt.sameSite) {\n    var sameSite = typeof opt.sameSite === 'string'\n      ? opt.sameSite.toLowerCase() : opt.sameSite;\n\n    switch (sameSite) {\n      case true:\n        str += '; SameSite=Strict';\n        break;\n      case 'lax':\n        str += '; SameSite=Lax';\n        break;\n      case 'strict':\n        str += '; SameSite=Strict';\n        break;\n      case 'none':\n        str += '; SameSite=None';\n        break;\n      default:\n        throw new TypeError('option sameSite is invalid');\n    }\n  }\n\n  return str;\n}\n\n/**\n * URL-decode string value. Optimized to skip native call when no %.\n *\n * @param {string} str\n * @returns {string}\n */\n\nfunction decode (str) {\n  return str.indexOf('%') !== -1\n    ? decodeURIComponent(str)\n    : str\n}\n\n/**\n * Determine if value is a Date.\n *\n * @param {*} val\n * @private\n */\n\nfunction isDate (val) {\n  return __toString.call(val) === '[object Date]';\n}\n\n/**\n * Try decoding a string using a decoding function.\n *\n * @param {string} str\n * @param {function} decode\n * @private\n */\n\nfunction tryDecode(str, decode) {\n  try {\n    return decode(str);\n  } catch (e) {\n    return str;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GAED;AAEA;;;CAGC,GAED,QAAQ,KAAK,GAAG;AAChB,QAAQ,SAAS,GAAG;AAEpB;;;CAGC,GAED,IAAI,aAAa,OAAO,SAAS,CAAC,QAAQ;AAE1C;;;;;;;;;;CAUC,GAED,IAAI,mBAAmB;AAEvB;;;;;;;;CAQC,GAED,IAAI,oBAAoB;AAExB;;;;;;;;;;;;;;;;;;;;;;CAsBC,GAED,IAAI,oBAAoB;AAExB;;;;;;CAMC,GAED,IAAI,kBAAkB;AAEtB;;;;;;;;;;CAUC,GAED,SAAS,MAAM,GAAG,EAAE,GAAG;IACrB,IAAI,OAAO,QAAQ,UAAU;QAC3B,MAAM,IAAI,UAAU;IACtB;IAEA,IAAI,MAAM,CAAC;IACX,IAAI,MAAM,IAAI,MAAM;IACpB,iGAAiG;IACjG,IAAI,MAAM,GAAG,OAAO;IAEpB,IAAI,MAAM,AAAC,OAAO,IAAI,MAAM,IAAK;IACjC,IAAI,QAAQ;IACZ,IAAI,QAAQ;IACZ,IAAI,SAAS;IAEb,GAAG;QACD,QAAQ,IAAI,OAAO,CAAC,KAAK;QACzB,IAAI,UAAU,CAAC,GAAG,OAAO,wBAAwB;QAEjD,SAAS,IAAI,OAAO,CAAC,KAAK;QAE1B,IAAI,WAAW,CAAC,GAAG;YACjB,SAAS;QACX,OAAO,IAAI,QAAQ,QAAQ;YACzB,+BAA+B;YAC/B,QAAQ,IAAI,WAAW,CAAC,KAAK,QAAQ,KAAK;YAC1C;QACF;QAEA,IAAI,cAAc,WAAW,KAAK,OAAO;QACzC,IAAI,YAAY,SAAS,KAAK,OAAO;QACrC,IAAI,MAAM,IAAI,KAAK,CAAC,aAAa;QAEjC,mBAAmB;QACnB,IAAI,CAAC,IAAI,cAAc,CAAC,MAAM;YAC5B,IAAI,cAAc,WAAW,KAAK,QAAQ,GAAG;YAC7C,IAAI,YAAY,SAAS,KAAK,QAAQ;YAEtC,IAAI,IAAI,UAAU,CAAC,iBAAiB,KAAK,KAAK,OAAM,IAAI,UAAU,CAAC,YAAY,OAAO,KAAK,KAAK,KAAI;gBAClG;gBACA;YACF;YAEA,IAAI,MAAM,IAAI,KAAK,CAAC,aAAa;YACjC,GAAG,CAAC,IAAI,GAAG,UAAU,KAAK;QAC5B;QAEA,QAAQ,SAAS;IACnB,QAAS,QAAQ,IAAK;IAEtB,OAAO;AACT;AAEA,SAAS,WAAW,GAAG,EAAE,KAAK,EAAE,GAAG;IACjC,GAAG;QACD,IAAI,OAAO,IAAI,UAAU,CAAC;QAC1B,IAAI,SAAS,KAAK,KAAK,OAAM,SAAS,KAAK,MAAM,KAAI,OAAO;IAC9D,QAAS,EAAE,QAAQ,IAAK;IACxB,OAAO;AACT;AAEA,SAAS,SAAS,GAAG,EAAE,KAAK,EAAE,GAAG;IAC/B,MAAO,QAAQ,IAAK;QAClB,IAAI,OAAO,IAAI,UAAU,CAAC,EAAE;QAC5B,IAAI,SAAS,KAAK,KAAK,OAAM,SAAS,KAAK,MAAM,KAAI,OAAO,QAAQ;IACtE;IACA,OAAO;AACT;AAEA;;;;;;;;;;;;;;CAcC,GAED,SAAS,UAAU,IAAI,EAAE,GAAG,EAAE,GAAG;IAC/B,IAAI,MAAM,AAAC,OAAO,IAAI,MAAM,IAAK;IAEjC,IAAI,OAAO,QAAQ,YAAY;QAC7B,MAAM,IAAI,UAAU;IACtB;IAEA,IAAI,CAAC,iBAAiB,IAAI,CAAC,OAAO;QAChC,MAAM,IAAI,UAAU;IACtB;IAEA,IAAI,QAAQ,IAAI;IAEhB,IAAI,CAAC,kBAAkB,IAAI,CAAC,QAAQ;QAClC,MAAM,IAAI,UAAU;IACtB;IAEA,IAAI,MAAM,OAAO,MAAM;IACvB,IAAI,CAAC,KAAK,OAAO;IAEjB,IAAI,QAAQ,IAAI,MAAM,EAAE;QACtB,IAAI,SAAS,KAAK,KAAK,CAAC,IAAI,MAAM;QAElC,IAAI,CAAC,SAAS,SAAS;YACrB,MAAM,IAAI,UAAU;QACtB;QAEA,OAAO,eAAe;IACxB;IAEA,IAAI,IAAI,MAAM,EAAE;QACd,IAAI,CAAC,kBAAkB,IAAI,CAAC,IAAI,MAAM,GAAG;YACvC,MAAM,IAAI,UAAU;QACtB;QAEA,OAAO,cAAc,IAAI,MAAM;IACjC;IAEA,IAAI,IAAI,IAAI,EAAE;QACZ,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,IAAI,GAAG;YACnC,MAAM,IAAI,UAAU;QACtB;QAEA,OAAO,YAAY,IAAI,IAAI;IAC7B;IAEA,IAAI,IAAI,OAAO,EAAE;QACf,IAAI,UAAU,IAAI,OAAO;QAEzB,IAAI,CAAC,OAAO,YAAY,MAAM,QAAQ,OAAO,KAAK;YAChD,MAAM,IAAI,UAAU;QACtB;QAEA,OAAO,eAAe,QAAQ,WAAW;IAC3C;IAEA,IAAI,IAAI,QAAQ,EAAE;QAChB,OAAO;IACT;IAEA,IAAI,IAAI,MAAM,EAAE;QACd,OAAO;IACT;IAEA,IAAI,IAAI,WAAW,EAAE;QACnB,OAAO;IACT;IAEA,IAAI,IAAI,QAAQ,EAAE;QAChB,IAAI,WAAW,OAAO,IAAI,QAAQ,KAAK,WACnC,IAAI,QAAQ,CAAC,WAAW,KAAK,IAAI,QAAQ;QAE7C,OAAQ;YACN,KAAK;gBACH,OAAO;gBACP;YACF,KAAK;gBACH,OAAO;gBACP;YACF,KAAK;gBACH,OAAO;gBACP;YACF;gBACE,MAAM,IAAI,UAAU;QACxB;IACF;IAEA,IAAI,IAAI,QAAQ,EAAE;QAChB,IAAI,WAAW,OAAO,IAAI,QAAQ,KAAK,WACnC,IAAI,QAAQ,CAAC,WAAW,KAAK,IAAI,QAAQ;QAE7C,OAAQ;YACN,KAAK;gBACH,OAAO;gBACP;YACF,KAAK;gBACH,OAAO;gBACP;YACF,KAAK;gBACH,OAAO;gBACP;YACF,KAAK;gBACH,OAAO;gBACP;YACF;gBACE,MAAM,IAAI,UAAU;QACxB;IACF;IAEA,OAAO;AACT;AAEA;;;;;CAKC,GAED,SAAS,OAAQ,GAAG;IAClB,OAAO,IAAI,OAAO,CAAC,SAAS,CAAC,IACzB,mBAAmB,OACnB;AACN;AAEA;;;;;CAKC,GAED,SAAS,OAAQ,GAAG;IAClB,OAAO,WAAW,IAAI,CAAC,SAAS;AAClC;AAEA;;;;;;CAMC,GAED,SAAS,UAAU,GAAG,EAAE,MAAM;IAC5B,IAAI;QACF,OAAO,OAAO;IAChB,EAAE,OAAO,GAAG;QACV,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1639, "column": 0}, "map": {"version": 3, "file": "preact.module.js", "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/Bonkai/node_modules/preact/src/util.js", "file:///Users/<USER>/Downloads/Coding/Personal/Bonkai/node_modules/preact/src/options.js", "file:///Users/<USER>/Downloads/Coding/Personal/Bonkai/node_modules/preact/src/create-element.js", "file:///Users/<USER>/Downloads/Coding/Personal/Bonkai/node_modules/preact/src/component.js", "file:///Users/<USER>/Downloads/Coding/Personal/Bonkai/node_modules/preact/src/create-context.js", "file:///Users/<USER>/Downloads/Coding/Personal/Bonkai/node_modules/preact/src/constants.js", "file:///Users/<USER>/Downloads/Coding/Personal/Bonkai/node_modules/preact/src/diff/children.js", "file:///Users/<USER>/Downloads/Coding/Personal/Bonkai/node_modules/preact/src/diff/props.js", "file:///Users/<USER>/Downloads/Coding/Personal/Bonkai/node_modules/preact/src/diff/index.js", "file:///Users/<USER>/Downloads/Coding/Personal/Bonkai/node_modules/preact/src/render.js", "file:///Users/<USER>/Downloads/Coding/Personal/Bonkai/node_modules/preact/src/clone-element.js", "file:///Users/<USER>/Downloads/Coding/Personal/Bonkai/node_modules/preact/src/diff/catch-error.js"], "sourcesContent": ["import { EMPTY_ARR } from \"./constants\";\n\n/**\n * Assign properties from `props` to `obj`\n * @template O, P The obj and props types\n * @param {O} obj The object to copy properties to\n * @param {P} props The object to copy properties from\n * @returns {O & P}\n */\nexport function assign(obj, props) {\n\t// @ts-ignore We change the type of `obj` to be `O & P`\n\tfor (let i in props) obj[i] = props[i];\n\treturn /** @type {O & P} */ (obj);\n}\n\n/**\n * Remove a child node from its parent if attached. This is a workaround for\n * IE11 which doesn't support `Element.prototype.remove()`. Using this function\n * is smaller than including a dedicated polyfill.\n * @param {Node} node The node to remove\n */\nexport function removeNode(node) {\n\tlet parentNode = node.parentNode;\n\tif (parentNode) parentNode.removeChild(node);\n}\n\nexport const slice = EMPTY_ARR.slice;\n", "import { _catchError } from './diff/catch-error';\n\n/**\n * The `option` object can potentially contain callback functions\n * that are called during various stages of our renderer. This is the\n * foundation on which all our addons like `preact/debug`, `preact/compat`,\n * and `preact/hooks` are based on. See the `Options` type in `internal.d.ts`\n * for a full list of available option hooks (most editors/IDEs allow you to\n * ctrl+click or cmd+click on mac the type definition below).\n * @type {import('./internal').Options}\n */\nconst options = {\n\t_catchError\n};\n\nexport default options;\n", "import { slice } from './util';\nimport options from './options';\n\nlet vnodeId = 0;\n\n/**\n * Create an virtual node (used for JSX)\n * @param {import('./internal').VNode[\"type\"]} type The node name or Component\n * constructor for this virtual node\n * @param {object | null | undefined} [props] The properties of the virtual node\n * @param {Array<import('.').ComponentChildren>} [children] The children of the virtual node\n * @returns {import('./internal').VNode}\n */\nexport function createElement(type, props, children) {\n\tlet normalizedProps = {},\n\t\tkey,\n\t\tref,\n\t\ti;\n\tfor (i in props) {\n\t\tif (i == 'key') key = props[i];\n\t\telse if (i == 'ref') ref = props[i];\n\t\telse normalizedProps[i] = props[i];\n\t}\n\n\tif (arguments.length > 2) {\n\t\tnormalizedProps.children =\n\t\t\targuments.length > 3 ? slice.call(arguments, 2) : children;\n\t}\n\n\t// If a Component VNode, check for and apply defaultProps\n\t// Note: type may be undefined in development, must never error here.\n\tif (typeof type == 'function' && type.defaultProps != null) {\n\t\tfor (i in type.defaultProps) {\n\t\t\tif (normalizedProps[i] === undefined) {\n\t\t\t\tnormalizedProps[i] = type.defaultProps[i];\n\t\t\t}\n\t\t}\n\t}\n\n\treturn createVNode(type, normalizedProps, key, ref, null);\n}\n\n/**\n * Create a VNode (used internally by Preact)\n * @param {import('./internal').VNode[\"type\"]} type The node name or Component\n * Constructor for this virtual node\n * @param {object | string | number | null} props The properties of this virtual node.\n * If this virtual node represents a text node, this is the text of the node (string or number).\n * @param {string | number | null} key The key for this virtual node, used when\n * diffing it against its children\n * @param {import('./internal').VNode[\"ref\"]} ref The ref property that will\n * receive a reference to its created child\n * @returns {import('./internal').VNode}\n */\nexport function createVNode(type, props, key, ref, original) {\n\t// V8 seems to be better at detecting type shapes if the object is allocated from the same call site\n\t// Do not inline into createElement and coerceToVNode!\n\tconst vnode = {\n\t\ttype,\n\t\tprops,\n\t\tkey,\n\t\tref,\n\t\t_children: null,\n\t\t_parent: null,\n\t\t_depth: 0,\n\t\t_dom: null,\n\t\t// _nextDom must be initialized to undefined b/c it will eventually\n\t\t// be set to dom.nextSibling which can return `null` and it is important\n\t\t// to be able to distinguish between an uninitialized _nextDom and\n\t\t// a _nextDom that has been set to `null`\n\t\t_nextDom: undefined,\n\t\t_component: null,\n\t\t_hydrating: null,\n\t\tconstructor: undefined,\n\t\t_original: original == null ? ++vnodeId : original\n\t};\n\n\t// Only invoke the vnode hook if this was *not* a direct copy:\n\tif (original == null && options.vnode != null) options.vnode(vnode);\n\n\treturn vnode;\n}\n\nexport function createRef() {\n\treturn { current: null };\n}\n\nexport function Fragment(props) {\n\treturn props.children;\n}\n\n/**\n * Check if a the argument is a valid Preact VNode.\n * @param {*} vnode\n * @returns {vnode is import('./internal').VNode}\n */\nexport const isValidElement = vnode =>\n\tvnode != null && vnode.constructor === undefined;\n", "import { assign } from './util';\nimport { diff, commitRoot } from './diff/index';\nimport options from './options';\nimport { Fragment } from './create-element';\n\n/**\n * Base Component class. Provides `setState()` and `forceUpdate()`, which\n * trigger rendering\n * @param {object} props The initial component props\n * @param {object} context The initial context from parent components'\n * getChildContext\n */\nexport function Component(props, context) {\n\tthis.props = props;\n\tthis.context = context;\n}\n\n/**\n * Update component state and schedule a re-render.\n * @this {import('./internal').Component}\n * @param {object | ((s: object, p: object) => object)} update A hash of state\n * properties to update with new values or a function that given the current\n * state and props returns a new partial state\n * @param {() => void} [callback] A function to be called once component state is\n * updated\n */\nComponent.prototype.setState = function(update, callback) {\n\t// only clone state when copying to nextState the first time.\n\tlet s;\n\tif (this._nextState != null && this._nextState !== this.state) {\n\t\ts = this._nextState;\n\t} else {\n\t\ts = this._nextState = assign({}, this.state);\n\t}\n\n\tif (typeof update == 'function') {\n\t\t// Some libraries like `immer` mark the current state as readonly,\n\t\t// preventing us from mutating it, so we need to clone it. See #2716\n\t\tupdate = update(assign({}, s), this.props);\n\t}\n\n\tif (update) {\n\t\tassign(s, update);\n\t}\n\n\t// Skip update if updater function returned null\n\tif (update == null) return;\n\n\tif (this._vnode) {\n\t\tif (callback) {\n\t\t\tthis._stateCallbacks.push(callback);\n\t\t}\n\t\tenqueueRender(this);\n\t}\n};\n\n/**\n * Immediately perform a synchronous re-render of the component\n * @this {import('./internal').Component}\n * @param {() => void} [callback] A function to be called after component is\n * re-rendered\n */\nComponent.prototype.forceUpdate = function(callback) {\n\tif (this._vnode) {\n\t\t// Set render mode so that we can differentiate where the render request\n\t\t// is coming from. We need this because forceUpdate should never call\n\t\t// shouldComponentUpdate\n\t\tthis._force = true;\n\t\tif (callback) this._renderCallbacks.push(callback);\n\t\tenqueueRender(this);\n\t}\n};\n\n/**\n * Accepts `props` and `state`, and returns a new Virtual DOM tree to build.\n * Virtual DOM is generally constructed via [JSX](http://jasonformat.com/wtf-is-jsx).\n * @param {object} props Props (eg: JSX attributes) received from parent\n * element/component\n * @param {object} state The component's current state\n * @param {object} context Context object, as returned by the nearest\n * ancestor's `getChildContext()`\n * @returns {import('./index').ComponentChildren | void}\n */\nComponent.prototype.render = Fragment;\n\n/**\n * @param {import('./internal').VNode} vnode\n * @param {number | null} [childIndex]\n */\nexport function getDomSibling(vnode, childIndex) {\n\tif (childIndex == null) {\n\t\t// Use childIndex==null as a signal to resume the search from the vnode's sibling\n\t\treturn vnode._parent\n\t\t\t? getDomSibling(vnode._parent, vnode._parent._children.indexOf(vnode) + 1)\n\t\t\t: null;\n\t}\n\n\tlet sibling;\n\tfor (; childIndex < vnode._children.length; childIndex++) {\n\t\tsibling = vnode._children[childIndex];\n\n\t\tif (sibling != null && sibling._dom != null) {\n\t\t\t// Since updateParentDomPointers keeps _dom pointer correct,\n\t\t\t// we can rely on _dom to tell us if this subtree contains a\n\t\t\t// rendered DOM node, and what the first rendered DOM node is\n\t\t\treturn sibling._dom;\n\t\t}\n\t}\n\n\t// If we get here, we have not found a DOM node in this vnode's children.\n\t// We must resume from this vnode's sibling (in it's parent _children array)\n\t// Only climb up and search the parent if we aren't searching through a DOM\n\t// VNode (meaning we reached the DOM parent of the original vnode that began\n\t// the search)\n\treturn typeof vnode.type == 'function' ? getDomSibling(vnode) : null;\n}\n\n/**\n * Trigger in-place re-rendering of a component.\n * @param {import('./internal').Component} component The component to rerender\n */\nfunction renderComponent(component) {\n\tlet vnode = component._vnode,\n\t\toldDom = vnode._dom,\n\t\tparentDom = component._parentDom;\n\n\tif (parentDom) {\n\t\tlet commitQueue = [];\n\t\tconst oldVNode = assign({}, vnode);\n\t\toldVNode._original = vnode._original + 1;\n\n\t\tdiff(\n\t\t\tparentDom,\n\t\t\tvnode,\n\t\t\toldVNode,\n\t\t\tcomponent._globalContext,\n\t\t\tparentDom.ownerSVGElement !== undefined,\n\t\t\tvnode._hydrating != null ? [oldDom] : null,\n\t\t\tcommitQueue,\n\t\t\toldDom == null ? getDomSibling(vnode) : oldDom,\n\t\t\tvnode._hydrating\n\t\t);\n\t\tcommitRoot(commitQueue, vnode);\n\n\t\tif (vnode._dom != oldDom) {\n\t\t\tupdateParentDomPointers(vnode);\n\t\t}\n\t}\n}\n\n/**\n * @param {import('./internal').VNode} vnode\n */\nfunction updateParentDomPointers(vnode) {\n\tif ((vnode = vnode._parent) != null && vnode._component != null) {\n\t\tvnode._dom = vnode._component.base = null;\n\t\tfor (let i = 0; i < vnode._children.length; i++) {\n\t\t\tlet child = vnode._children[i];\n\t\t\tif (child != null && child._dom != null) {\n\t\t\t\tvnode._dom = vnode._component.base = child._dom;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\n\t\treturn updateParentDomPointers(vnode);\n\t}\n}\n\n/**\n * The render queue\n * @type {Array<import('./internal').Component>}\n */\nlet rerenderQueue = [];\n\n/*\n * The value of `Component.debounce` must asynchronously invoke the passed in callback. It is\n * important that contributors to Preact can consistently reason about what calls to `setState`, etc.\n * do, and when their effects will be applied. See the links below for some further reading on designing\n * asynchronous APIs.\n * * [Designing APIs for Asynchrony](https://blog.izs.me/2013/08/designing-apis-for-asynchrony)\n * * [Callbacks synchronous and asynchronous](https://blog.ometer.com/2011/07/24/callbacks-synchronous-and-asynchronous/)\n */\n\nlet prevDebounce;\n\n/**\n * Enqueue a rerender of a component\n * @param {import('./internal').Component} c The component to rerender\n */\nexport function enqueueRender(c) {\n\tif (\n\t\t(!c._dirty &&\n\t\t\t(c._dirty = true) &&\n\t\t\trerenderQueue.push(c) &&\n\t\t\t!process._rerenderCount++) ||\n\t\tprevDebounce !== options.debounceRendering\n\t) {\n\t\tprevDebounce = options.debounceRendering;\n\t\t(prevDebounce || setTimeout)(process);\n\t}\n}\n\n/** Flush the render queue by rerendering all queued components */\nfunction process() {\n\tlet queue;\n\twhile ((process._rerenderCount = rerenderQueue.length)) {\n\t\tqueue = rerenderQueue.sort((a, b) => a._vnode._depth - b._vnode._depth);\n\t\trerenderQueue = [];\n\t\t// Don't update `renderCount` yet. Keep its value non-zero to prevent unnecessary\n\t\t// process() calls from getting scheduled while `queue` is still being consumed.\n\t\tqueue.some(c => {\n\t\t\tif (c._dirty) renderComponent(c);\n\t\t});\n\t}\n}\n\nprocess._rerenderCount = 0;\n", "import { enqueueRender } from './component';\n\nexport let i = 0;\n\nexport function createContext(defaultValue, contextId) {\n\tcontextId = '__cC' + i++;\n\n\tconst context = {\n\t\t_id: contextId,\n\t\t_defaultValue: defaultValue,\n\t\t/** @type {import('./internal').FunctionComponent} */\n\t\tConsumer(props, contextValue) {\n\t\t\t// return props.children(\n\t\t\t// \tcontext[contextId] ? context[contextId].props.value : defaultValue\n\t\t\t// );\n\t\t\treturn props.children(contextValue);\n\t\t},\n\t\t/** @type {import('./internal').FunctionComponent} */\n\t\tProvider(props) {\n\t\t\tif (!this.getChildContext) {\n\t\t\t\tlet subs = [];\n\t\t\t\tlet ctx = {};\n\t\t\t\tctx[contextId] = this;\n\n\t\t\t\tthis.getChildContext = () => ctx;\n\n\t\t\t\tthis.shouldComponentUpdate = function(_props) {\n\t\t\t\t\tif (this.props.value !== _props.value) {\n\t\t\t\t\t\t// I think the forced value propagation here was only needed when `options.debounceRendering` was being bypassed:\n\t\t\t\t\t\t// https://github.com/preactjs/preact/commit/4d339fb803bea09e9f198abf38ca1bf8ea4b7771#diff-54682ce380935a717e41b8bfc54737f6R358\n\t\t\t\t\t\t// In those cases though, even with the value corrected, we're double-rendering all nodes.\n\t\t\t\t\t\t// It might be better to just tell folks not to use force-sync mode.\n\t\t\t\t\t\t// Currently, using `useContext()` in a class component will overwrite its `this.context` value.\n\t\t\t\t\t\t// subs.some(c => {\n\t\t\t\t\t\t// \tc.context = _props.value;\n\t\t\t\t\t\t// \tenqueueRender(c);\n\t\t\t\t\t\t// });\n\n\t\t\t\t\t\t// subs.some(c => {\n\t\t\t\t\t\t// \tc.context[contextId] = _props.value;\n\t\t\t\t\t\t// \tenqueueRender(c);\n\t\t\t\t\t\t// });\n\t\t\t\t\t\tsubs.some(enqueueRender);\n\t\t\t\t\t}\n\t\t\t\t};\n\n\t\t\t\tthis.sub = c => {\n\t\t\t\t\tsubs.push(c);\n\t\t\t\t\tlet old = c.componentWillUnmount;\n\t\t\t\t\tc.componentWillUnmount = () => {\n\t\t\t\t\t\tsubs.splice(subs.indexOf(c), 1);\n\t\t\t\t\t\tif (old) old.call(c);\n\t\t\t\t\t};\n\t\t\t\t};\n\t\t\t}\n\n\t\t\treturn props.children;\n\t\t}\n\t};\n\n\t// Devtools needs access to the context object when it\n\t// encounters a Provider. This is necessary to support\n\t// setting `displayName` on the context object instead\n\t// of on the component itself. See:\n\t// https://reactjs.org/docs/context.html#contextdisplayname\n\n\treturn (context.Provider._contextRef = context.Consumer.contextType = context);\n}\n", "export const EMPTY_OBJ = {};\nexport const EMPTY_ARR = [];\nexport const IS_NON_DIMENSIONAL = /acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;\n", "import { diff, unmount, applyRef } from './index';\nimport { createVNode, Fragment } from '../create-element';\nimport { EMPTY_OBJ, EMPTY_ARR } from '../constants';\nimport { getDomSibling } from '../component';\n\n/**\n * Diff the children of a virtual node\n * @param {import('../internal').PreactElement} parentDom The DOM element whose\n * children are being diffed\n * @param {import('../internal').ComponentChildren[]} renderResult\n * @param {import('../internal').VNode} newParentVNode The new virtual\n * node whose children should be diff'ed against oldParentVNode\n * @param {import('../internal').VNode} oldParentVNode The old virtual\n * node whose children should be diff'ed against newParentVNode\n * @param {object} globalContext The current context object - modified by getChildContext\n * @param {boolean} isSvg Whether or not this DOM node is an SVG node\n * @param {Array<import('../internal').PreactElement>} excessDomChildren\n * @param {Array<import('../internal').Component>} commitQueue List of components\n * which have callbacks to invoke in commitRoot\n * @param {import('../internal').PreactElement} oldDom The current attached DOM\n * element any new dom elements should be placed around. Likely `null` on first\n * render (except when hydrating). Can be a sibling DOM element when diffing\n * Fragments that have siblings. In most cases, it starts out as `oldChildren[0]._dom`.\n * @param {boolean} isHydrating Whether or not we are in hydration\n */\nexport function diffChildren(\n\tparentDom,\n\trenderResult,\n\tnewParentVNode,\n\toldParentVNode,\n\tglobalContext,\n\tisSvg,\n\texcessDomChildren,\n\tcommitQueue,\n\toldDom,\n\tisHydrating\n) {\n\tlet i, j, oldVNode, childVNode, newDom, firstChildDom, refs;\n\n\t// This is a compression of oldParentVNode!=null && oldParentVNode != EMPTY_OBJ && oldParentVNode._children || EMPTY_ARR\n\t// as EMPTY_OBJ._children should be `undefined`.\n\tlet oldChildren = (oldParentVNode && oldParentVNode._children) || EMPTY_ARR;\n\n\tlet oldChildrenLength = oldChildren.length;\n\n\tnewParentVNode._children = [];\n\tfor (i = 0; i < renderResult.length; i++) {\n\t\tchildVNode = renderResult[i];\n\n\t\tif (childVNode == null || typeof childVNode == 'boolean') {\n\t\t\tchildVNode = newParentVNode._children[i] = null;\n\t\t}\n\t\t// If this newVNode is being reused (e.g. <div>{reuse}{reuse}</div>) in the same diff,\n\t\t// or we are rendering a component (e.g. setState) copy the oldVNodes so it can have\n\t\t// it's own DOM & etc. pointers\n\t\telse if (\n\t\t\ttypeof childVNode == 'string' ||\n\t\t\ttypeof childVNode == 'number' ||\n\t\t\t// eslint-disable-next-line valid-typeof\n\t\t\ttypeof childVNode == 'bigint'\n\t\t) {\n\t\t\tchildVNode = newParentVNode._children[i] = createVNode(\n\t\t\t\tnull,\n\t\t\t\tchildVNode,\n\t\t\t\tnull,\n\t\t\t\tnull,\n\t\t\t\tchildVNode\n\t\t\t);\n\t\t} else if (Array.isArray(childVNode)) {\n\t\t\tchildVNode = newParentVNode._children[i] = createVNode(\n\t\t\t\tFragment,\n\t\t\t\t{ children: childVNode },\n\t\t\t\tnull,\n\t\t\t\tnull,\n\t\t\t\tnull\n\t\t\t);\n\t\t} else if (childVNode._depth > 0) {\n\t\t\t// VNode is already in use, clone it. This can happen in the following\n\t\t\t// scenario:\n\t\t\t//   const reuse = <div />\n\t\t\t//   <div>{reuse}<span />{reuse}</div>\n\t\t\tchildVNode = newParentVNode._children[i] = createVNode(\n\t\t\t\tchildVNode.type,\n\t\t\t\tchildVNode.props,\n\t\t\t\tchildVNode.key,\n\t\t\t\tchildVNode.ref ? childVNode.ref : null,\n\t\t\t\tchildVNode._original\n\t\t\t);\n\t\t} else {\n\t\t\tchildVNode = newParentVNode._children[i] = childVNode;\n\t\t}\n\n\t\t// Terser removes the `continue` here and wraps the loop body\n\t\t// in a `if (childVNode) { ... } condition\n\t\tif (childVNode == null) {\n\t\t\tcontinue;\n\t\t}\n\n\t\tchildVNode._parent = newParentVNode;\n\t\tchildVNode._depth = newParentVNode._depth + 1;\n\n\t\t// Check if we find a corresponding element in oldChildren.\n\t\t// If found, delete the array item by setting to `undefined`.\n\t\t// We use `undefined`, as `null` is reserved for empty placeholders\n\t\t// (holes).\n\t\toldVNode = oldChildren[i];\n\n\t\tif (\n\t\t\toldVNode === null ||\n\t\t\t(oldVNode &&\n\t\t\t\tchildVNode.key == oldVNode.key &&\n\t\t\t\tchildVNode.type === oldVNode.type)\n\t\t) {\n\t\t\toldChildren[i] = undefined;\n\t\t} else {\n\t\t\t// Either oldVNode === undefined or oldChildrenLength > 0,\n\t\t\t// so after this loop oldVNode == null or oldVNode is a valid value.\n\t\t\tfor (j = 0; j < oldChildrenLength; j++) {\n\t\t\t\toldVNode = oldChildren[j];\n\t\t\t\t// If childVNode is unkeyed, we only match similarly unkeyed nodes, otherwise we match by key.\n\t\t\t\t// We always match by type (in either case).\n\t\t\t\tif (\n\t\t\t\t\toldVNode &&\n\t\t\t\t\tchildVNode.key == oldVNode.key &&\n\t\t\t\t\tchildVNode.type === oldVNode.type\n\t\t\t\t) {\n\t\t\t\t\toldChildren[j] = undefined;\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t\toldVNode = null;\n\t\t\t}\n\t\t}\n\n\t\toldVNode = oldVNode || EMPTY_OBJ;\n\n\t\t// Morph the old element into the new one, but don't append it to the dom yet\n\t\tdiff(\n\t\t\tparentDom,\n\t\t\tchildVNode,\n\t\t\toldVNode,\n\t\t\tglobalContext,\n\t\t\tisSvg,\n\t\t\texcessDomChildren,\n\t\t\tcommitQueue,\n\t\t\toldDom,\n\t\t\tisHydrating\n\t\t);\n\n\t\tnewDom = childVNode._dom;\n\n\t\tif ((j = childVNode.ref) && oldVNode.ref != j) {\n\t\t\tif (!refs) refs = [];\n\t\t\tif (oldVNode.ref) refs.push(oldVNode.ref, null, childVNode);\n\t\t\trefs.push(j, childVNode._component || newDom, childVNode);\n\t\t}\n\n\t\tif (newDom != null) {\n\t\t\tif (firstChildDom == null) {\n\t\t\t\tfirstChildDom = newDom;\n\t\t\t}\n\n\t\t\tif (\n\t\t\t\ttypeof childVNode.type == 'function' &&\n\t\t\t\tchildVNode._children === oldVNode._children\n\t\t\t) {\n\t\t\t\tchildVNode._nextDom = oldDom = reorderChildren(\n\t\t\t\t\tchildVNode,\n\t\t\t\t\toldDom,\n\t\t\t\t\tparentDom\n\t\t\t\t);\n\t\t\t} else {\n\t\t\t\toldDom = placeChild(\n\t\t\t\t\tparentDom,\n\t\t\t\t\tchildVNode,\n\t\t\t\t\toldVNode,\n\t\t\t\t\toldChildren,\n\t\t\t\t\tnewDom,\n\t\t\t\t\toldDom\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tif (typeof newParentVNode.type == 'function') {\n\t\t\t\t// Because the newParentVNode is Fragment-like, we need to set it's\n\t\t\t\t// _nextDom property to the nextSibling of its last child DOM node.\n\t\t\t\t//\n\t\t\t\t// `oldDom` contains the correct value here because if the last child\n\t\t\t\t// is a Fragment-like, then oldDom has already been set to that child's _nextDom.\n\t\t\t\t// If the last child is a DOM VNode, then oldDom will be set to that DOM\n\t\t\t\t// node's nextSibling.\n\t\t\t\tnewParentVNode._nextDom = oldDom;\n\t\t\t}\n\t\t} else if (\n\t\t\toldDom &&\n\t\t\toldVNode._dom == oldDom &&\n\t\t\toldDom.parentNode != parentDom\n\t\t) {\n\t\t\t// The above condition is to handle null placeholders. See test in placeholder.test.js:\n\t\t\t// `efficiently replace null placeholders in parent rerenders`\n\t\t\toldDom = getDomSibling(oldVNode);\n\t\t}\n\t}\n\n\tnewParentVNode._dom = firstChildDom;\n\n\t// Remove remaining oldChildren if there are any.\n\tfor (i = oldChildrenLength; i--; ) {\n\t\tif (oldChildren[i] != null) {\n\t\t\tunmount(oldChildren[i], oldChildren[i]);\n\t\t}\n\t}\n\n\t// Set refs only after unmount\n\tif (refs) {\n\t\tfor (i = 0; i < refs.length; i++) {\n\t\t\tapplyRef(refs[i], refs[++i], refs[++i]);\n\t\t}\n\t}\n}\n\nfunction reorderChildren(childVNode, oldDom, parentDom) {\n\t// Note: VNodes in nested suspended trees may be missing _children.\n\tlet c = childVNode._children;\n\tlet tmp = 0;\n\tfor (; c && tmp < c.length; tmp++) {\n\t\tlet vnode = c[tmp];\n\t\tif (vnode) {\n\t\t\t// We typically enter this code path on sCU bailout, where we copy\n\t\t\t// oldVNode._children to newVNode._children. If that is the case, we need\n\t\t\t// to update the old children's _parent pointer to point to the newVNode\n\t\t\t// (childVNode here).\n\t\t\tvnode._parent = childVNode;\n\n\t\t\tif (typeof vnode.type == 'function') {\n\t\t\t\toldDom = reorderChildren(vnode, oldDom, parentDom);\n\t\t\t} else {\n\t\t\t\toldDom = placeChild(parentDom, vnode, vnode, c, vnode._dom, oldDom);\n\t\t\t}\n\t\t}\n\t}\n\n\treturn oldDom;\n}\n\n/**\n * Flatten and loop through the children of a virtual node\n * @param {import('../index').ComponentChildren} children The unflattened\n * children of a virtual node\n * @returns {import('../internal').VNode[]}\n */\nexport function toChildArray(children, out) {\n\tout = out || [];\n\tif (children == null || typeof children == 'boolean') {\n\t} else if (Array.isArray(children)) {\n\t\tchildren.some(child => {\n\t\t\ttoChildArray(child, out);\n\t\t});\n\t} else {\n\t\tout.push(children);\n\t}\n\treturn out;\n}\n\nfunction placeChild(\n\tparentDom,\n\tchildVNode,\n\toldVNode,\n\toldChildren,\n\tnewDom,\n\toldDom\n) {\n\tlet nextDom;\n\tif (childVNode._nextDom !== undefined) {\n\t\t// Only Fragments or components that return Fragment like VNodes will\n\t\t// have a non-undefined _nextDom. Continue the diff from the sibling\n\t\t// of last DOM child of this child VNode\n\t\tnextDom = childVNode._nextDom;\n\n\t\t// Eagerly cleanup _nextDom. We don't need to persist the value because\n\t\t// it is only used by `diffChildren` to determine where to resume the diff after\n\t\t// diffing Components and Fragments. Once we store it the nextDOM local var, we\n\t\t// can clean up the property\n\t\tchildVNode._nextDom = undefined;\n\t} else if (\n\t\toldVNode == null ||\n\t\tnewDom != oldDom ||\n\t\tnewDom.parentNode == null\n\t) {\n\t\touter: if (oldDom == null || oldDom.parentNode !== parentDom) {\n\t\t\tparentDom.appendChild(newDom);\n\t\t\tnextDom = null;\n\t\t} else {\n\t\t\t// `j<oldChildrenLength; j+=2` is an alternative to `j++<oldChildrenLength/2`\n\t\t\tfor (\n\t\t\t\tlet sibDom = oldDom, j = 0;\n\t\t\t\t(sibDom = sibDom.nextSibling) && j < oldChildren.length;\n\t\t\t\tj += 1\n\t\t\t) {\n\t\t\t\tif (sibDom == newDom) {\n\t\t\t\t\tbreak outer;\n\t\t\t\t}\n\t\t\t}\n\t\t\tparentDom.insertBefore(newDom, oldDom);\n\t\t\tnextDom = oldDom;\n\t\t}\n\t}\n\n\t// If we have pre-calculated the nextDOM node, use it. Else calculate it now\n\t// Strictly check for `undefined` here cuz `null` is a valid value of `nextDom`.\n\t// See more detail in create-element.js:createVNode\n\tif (nextDom !== undefined) {\n\t\toldDom = nextDom;\n\t} else {\n\t\toldDom = newDom.nextSibling;\n\t}\n\n\treturn oldDom;\n}\n", "import { IS_NON_DIMENSIONAL } from '../constants';\nimport options from '../options';\n\n/**\n * Diff the old and new properties of a VNode and apply changes to the DOM node\n * @param {import('../internal').PreactElement} dom The DOM node to apply\n * changes to\n * @param {object} newProps The new props\n * @param {object} oldProps The old props\n * @param {boolean} isSvg Whether or not this node is an SVG node\n * @param {boolean} hydrate Whether or not we are in hydration mode\n */\nexport function diffProps(dom, newProps, oldProps, isSvg, hydrate) {\n\tlet i;\n\n\tfor (i in oldProps) {\n\t\tif (i !== 'children' && i !== 'key' && !(i in newProps)) {\n\t\t\tsetProperty(dom, i, null, oldProps[i], isSvg);\n\t\t}\n\t}\n\n\tfor (i in newProps) {\n\t\tif (\n\t\t\t(!hydrate || typeof newProps[i] == 'function') &&\n\t\t\ti !== 'children' &&\n\t\t\ti !== 'key' &&\n\t\t\ti !== 'value' &&\n\t\t\ti !== 'checked' &&\n\t\t\toldProps[i] !== newProps[i]\n\t\t) {\n\t\t\tsetProperty(dom, i, newProps[i], oldProps[i], isSvg);\n\t\t}\n\t}\n}\n\nfunction setStyle(style, key, value) {\n\tif (key[0] === '-') {\n\t\tstyle.setProperty(key, value);\n\t} else if (value == null) {\n\t\tstyle[key] = '';\n\t} else if (typeof value != 'number' || IS_NON_DIMENSIONAL.test(key)) {\n\t\tstyle[key] = value;\n\t} else {\n\t\tstyle[key] = value + 'px';\n\t}\n}\n\n/**\n * Set a property value on a DOM node\n * @param {import('../internal').PreactElement} dom The DOM node to modify\n * @param {string} name The name of the property to set\n * @param {*} value The value to set the property to\n * @param {*} oldValue The old value the property had\n * @param {boolean} isSvg Whether or not this DOM node is an SVG node or not\n */\nexport function setProperty(dom, name, value, oldValue, isSvg) {\n\tlet useCapture;\n\n\to: if (name === 'style') {\n\t\tif (typeof value == 'string') {\n\t\t\tdom.style.cssText = value;\n\t\t} else {\n\t\t\tif (typeof oldValue == 'string') {\n\t\t\t\tdom.style.cssText = oldValue = '';\n\t\t\t}\n\n\t\t\tif (oldValue) {\n\t\t\t\tfor (name in oldValue) {\n\t\t\t\t\tif (!(value && name in value)) {\n\t\t\t\t\t\tsetStyle(dom.style, name, '');\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (value) {\n\t\t\t\tfor (name in value) {\n\t\t\t\t\tif (!oldValue || value[name] !== oldValue[name]) {\n\t\t\t\t\t\tsetStyle(dom.style, name, value[name]);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\t// Benchmark for comparison: https://esbench.com/bench/574c954bdb965b9a00965ac6\n\telse if (name[0] === 'o' && name[1] === 'n') {\n\t\tuseCapture = name !== (name = name.replace(/Capture$/, ''));\n\n\t\t// Infer correct casing for DOM built-in events:\n\t\tif (name.toLowerCase() in dom) name = name.toLowerCase().slice(2);\n\t\telse name = name.slice(2);\n\n\t\tif (!dom._listeners) dom._listeners = {};\n\t\tdom._listeners[name + useCapture] = value;\n\n\t\tif (value) {\n\t\t\tif (!oldValue) {\n\t\t\t\tconst handler = useCapture ? eventProxyCapture : eventProxy;\n\t\t\t\tdom.addEventListener(name, handler, useCapture);\n\t\t\t}\n\t\t} else {\n\t\t\tconst handler = useCapture ? eventProxyCapture : eventProxy;\n\t\t\tdom.removeEventListener(name, handler, useCapture);\n\t\t}\n\t} else if (name !== 'dangerouslySetInnerHTML') {\n\t\tif (isSvg) {\n\t\t\t// Normalize incorrect prop usage for SVG:\n\t\t\t// - xlink:href / xlinkHref --> href (xlink:href was removed from SVG and isn't needed)\n\t\t\t// - className --> class\n\t\t\tname = name.replace(/xlink(H|:h)/, 'h').replace(/sName$/, 's');\n\t\t} else if (\n\t\t\tname !== 'href' &&\n\t\t\tname !== 'list' &&\n\t\t\tname !== 'form' &&\n\t\t\t// Default value in browsers is `-1` and an empty string is\n\t\t\t// cast to `0` instead\n\t\t\tname !== 'tabIndex' &&\n\t\t\tname !== 'download' &&\n\t\t\tname in dom\n\t\t) {\n\t\t\ttry {\n\t\t\t\tdom[name] = value == null ? '' : value;\n\t\t\t\t// labelled break is 1b smaller here than a return statement (sorry)\n\t\t\t\tbreak o;\n\t\t\t} catch (e) {}\n\t\t}\n\n\t\t// ARIA-attributes have a different notion of boolean values.\n\t\t// The value `false` is different from the attribute not\n\t\t// existing on the DOM, so we can't remove it. For non-boolean\n\t\t// ARIA-attributes we could treat false as a removal, but the\n\t\t// amount of exceptions would cost us too many bytes. On top of\n\t\t// that other VDOM frameworks also always stringify `false`.\n\n\t\tif (typeof value === 'function') {\n\t\t\t// never serialize functions as attribute values\n\t\t} else if (value != null && (value !== false || name.indexOf('-') != -1)) {\n\t\t\tdom.setAttribute(name, value);\n\t\t} else {\n\t\t\tdom.removeAttribute(name);\n\t\t}\n\t}\n}\n\n/**\n * Proxy an event to hooked event handlers\n * @param {Event} e The event object from the browser\n * @private\n */\nfunction eventProxy(e) {\n\tthis._listeners[e.type + false](options.event ? options.event(e) : e);\n}\n\nfunction eventProxyCapture(e) {\n\tthis._listeners[e.type + true](options.event ? options.event(e) : e);\n}\n", "import { EMPTY_OBJ } from '../constants';\nimport { Component, getDomSibling } from '../component';\nimport { Fragment } from '../create-element';\nimport { diffChildren } from './children';\nimport { diffProps, setProperty } from './props';\nimport { assign, removeNode, slice } from '../util';\nimport options from '../options';\n\n/**\n * Diff two virtual nodes and apply proper changes to the DOM\n * @param {import('../internal').PreactElement} parentDom The parent of the DOM element\n * @param {import('../internal').VNode} newVNode The new virtual node\n * @param {import('../internal').VNode} oldVNode The old virtual node\n * @param {object} globalContext The current context object. Modified by getChildContext\n * @param {boolean} isSvg Whether or not this element is an SVG node\n * @param {Array<import('../internal').PreactElement>} excessDomChildren\n * @param {Array<import('../internal').Component>} commitQueue List of components\n * which have callbacks to invoke in commitRoot\n * @param {import('../internal').PreactElement} oldDom The current attached DOM\n * element any new dom elements should be placed around. Likely `null` on first\n * render (except when hydrating). Can be a sibling DOM element when diffing\n * Fragments that have siblings. In most cases, it starts out as `oldChildren[0]._dom`.\n * @param {boolean} [isHydrating] Whether or not we are in hydration\n */\nexport function diff(\n\tparentDom,\n\tnewVNode,\n\toldVNode,\n\tglobalContext,\n\tisSvg,\n\texcessDomChildren,\n\tcommitQueue,\n\toldDom,\n\tisHydrating\n) {\n\tlet tmp,\n\t\tnewType = newVNode.type;\n\n\t// When passing through createElement it assigns the object\n\t// constructor as undefined. This to prevent JSON-injection.\n\tif (newVNode.constructor !== undefined) return null;\n\n\t// If the previous diff bailed out, resume creating/hydrating.\n\tif (oldVNode._hydrating != null) {\n\t\tisHydrating = oldVNode._hydrating;\n\t\toldDom = newVNode._dom = oldVNode._dom;\n\t\t// if we resume, we want the tree to be \"unlocked\"\n\t\tnewVNode._hydrating = null;\n\t\texcessDomChildren = [oldDom];\n\t}\n\n\tif ((tmp = options._diff)) tmp(newVNode);\n\n\ttry {\n\t\touter: if (typeof newType == 'function') {\n\t\t\tlet c, isNew, oldProps, oldState, snapshot, clearProcessingException;\n\t\t\tlet newProps = newVNode.props;\n\n\t\t\t// Necessary for createContext api. Setting this property will pass\n\t\t\t// the context value as `this.context` just for this component.\n\t\t\ttmp = newType.contextType;\n\t\t\tlet provider = tmp && globalContext[tmp._id];\n\t\t\tlet componentContext = tmp\n\t\t\t\t? provider\n\t\t\t\t\t? provider.props.value\n\t\t\t\t\t: tmp._defaultValue\n\t\t\t\t: globalContext;\n\n\t\t\t// Get component and set it to `c`\n\t\t\tif (oldVNode._component) {\n\t\t\t\tc = newVNode._component = oldVNode._component;\n\t\t\t\tclearProcessingException = c._processingException = c._pendingError;\n\t\t\t} else {\n\t\t\t\t// Instantiate the new component\n\t\t\t\tif ('prototype' in newType && newType.prototype.render) {\n\t\t\t\t\t// @ts-ignore The check above verifies that newType is suppose to be constructed\n\t\t\t\t\tnewVNode._component = c = new newType(newProps, componentContext); // eslint-disable-line new-cap\n\t\t\t\t} else {\n\t\t\t\t\t// @ts-ignore Trust me, Component implements the interface we want\n\t\t\t\t\tnewVNode._component = c = new Component(newProps, componentContext);\n\t\t\t\t\tc.constructor = newType;\n\t\t\t\t\tc.render = doRender;\n\t\t\t\t}\n\t\t\t\tif (provider) provider.sub(c);\n\n\t\t\t\tc.props = newProps;\n\t\t\t\tif (!c.state) c.state = {};\n\t\t\t\tc.context = componentContext;\n\t\t\t\tc._globalContext = globalContext;\n\t\t\t\tisNew = c._dirty = true;\n\t\t\t\tc._renderCallbacks = [];\n\t\t\t\tc._stateCallbacks = [];\n\t\t\t}\n\n\t\t\t// Invoke getDerivedStateFromProps\n\t\t\tif (c._nextState == null) {\n\t\t\t\tc._nextState = c.state;\n\t\t\t}\n\n\t\t\tif (newType.getDerivedStateFromProps != null) {\n\t\t\t\tif (c._nextState == c.state) {\n\t\t\t\t\tc._nextState = assign({}, c._nextState);\n\t\t\t\t}\n\n\t\t\t\tassign(\n\t\t\t\t\tc._nextState,\n\t\t\t\t\tnewType.getDerivedStateFromProps(newProps, c._nextState)\n\t\t\t\t);\n\t\t\t}\n\n\t\t\toldProps = c.props;\n\t\t\toldState = c.state;\n\n\t\t\t// Invoke pre-render lifecycle methods\n\t\t\tif (isNew) {\n\t\t\t\tif (\n\t\t\t\t\tnewType.getDerivedStateFromProps == null &&\n\t\t\t\t\tc.componentWillMount != null\n\t\t\t\t) {\n\t\t\t\t\tc.componentWillMount();\n\t\t\t\t}\n\n\t\t\t\tif (c.componentDidMount != null) {\n\t\t\t\t\tc._renderCallbacks.push(c.componentDidMount);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tif (\n\t\t\t\t\tnewType.getDerivedStateFromProps == null &&\n\t\t\t\t\tnewProps !== oldProps &&\n\t\t\t\t\tc.componentWillReceiveProps != null\n\t\t\t\t) {\n\t\t\t\t\tc.componentWillReceiveProps(newProps, componentContext);\n\t\t\t\t}\n\n\t\t\t\tif (\n\t\t\t\t\t(!c._force &&\n\t\t\t\t\t\tc.shouldComponentUpdate != null &&\n\t\t\t\t\t\tc.shouldComponentUpdate(\n\t\t\t\t\t\t\tnewProps,\n\t\t\t\t\t\t\tc._nextState,\n\t\t\t\t\t\t\tcomponentContext\n\t\t\t\t\t\t) === false) ||\n\t\t\t\t\tnewVNode._original === oldVNode._original\n\t\t\t\t) {\n\t\t\t\t\tc.props = newProps;\n\t\t\t\t\tc.state = c._nextState;\n\t\t\t\t\t// More info about this here: https://gist.github.com/JoviDeCroock/bec5f2ce93544d2e6070ef8e0036e4e8\n\t\t\t\t\tif (newVNode._original !== oldVNode._original) c._dirty = false;\n\t\t\t\t\tc._vnode = newVNode;\n\t\t\t\t\tnewVNode._dom = oldVNode._dom;\n\t\t\t\t\tnewVNode._children = oldVNode._children;\n\t\t\t\t\tnewVNode._children.forEach(vnode => {\n\t\t\t\t\t\tif (vnode) vnode._parent = newVNode;\n\t\t\t\t\t});\n\n\t\t\t\t\tfor (let i = 0; i < c._stateCallbacks.length; i++) {\n\t\t\t\t\t\tc._renderCallbacks.push(c._stateCallbacks[i]);\n\t\t\t\t\t}\n\t\t\t\t\tc._stateCallbacks = [];\n\n\t\t\t\t\tif (c._renderCallbacks.length) {\n\t\t\t\t\t\tcommitQueue.push(c);\n\t\t\t\t\t}\n\n\t\t\t\t\tbreak outer;\n\t\t\t\t}\n\n\t\t\t\tif (c.componentWillUpdate != null) {\n\t\t\t\t\tc.componentWillUpdate(newProps, c._nextState, componentContext);\n\t\t\t\t}\n\n\t\t\t\tif (c.componentDidUpdate != null) {\n\t\t\t\t\tc._renderCallbacks.push(() => {\n\t\t\t\t\t\tc.componentDidUpdate(oldProps, oldState, snapshot);\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tc.context = componentContext;\n\t\t\tc.props = newProps;\n\t\t\tc._vnode = newVNode;\n\t\t\tc._parentDom = parentDom;\n\n\t\t\tlet renderHook = options._render,\n\t\t\t\tcount = 0;\n\t\t\tif ('prototype' in newType && newType.prototype.render) {\n\t\t\t\tc.state = c._nextState;\n\t\t\t\tc._dirty = false;\n\n\t\t\t\tif (renderHook) renderHook(newVNode);\n\n\t\t\t\ttmp = c.render(c.props, c.state, c.context);\n\n\t\t\t\tfor (let i = 0; i < c._stateCallbacks.length; i++) {\n\t\t\t\t\tc._renderCallbacks.push(c._stateCallbacks[i]);\n\t\t\t\t}\n\t\t\t\tc._stateCallbacks = [];\n\t\t\t} else {\n\t\t\t\tdo {\n\t\t\t\t\tc._dirty = false;\n\t\t\t\t\tif (renderHook) renderHook(newVNode);\n\n\t\t\t\t\ttmp = c.render(c.props, c.state, c.context);\n\n\t\t\t\t\t// Handle setState called in render, see #2553\n\t\t\t\t\tc.state = c._nextState;\n\t\t\t\t} while (c._dirty && ++count < 25);\n\t\t\t}\n\n\t\t\t// Handle setState called in render, see #2553\n\t\t\tc.state = c._nextState;\n\n\t\t\tif (c.getChildContext != null) {\n\t\t\t\tglobalContext = assign(assign({}, globalContext), c.getChildContext());\n\t\t\t}\n\n\t\t\tif (!isNew && c.getSnapshotBeforeUpdate != null) {\n\t\t\t\tsnapshot = c.getSnapshotBeforeUpdate(oldProps, oldState);\n\t\t\t}\n\n\t\t\tlet isTopLevelFragment =\n\t\t\t\ttmp != null && tmp.type === Fragment && tmp.key == null;\n\t\t\tlet renderResult = isTopLevelFragment ? tmp.props.children : tmp;\n\n\t\t\tdiffChildren(\n\t\t\t\tparentDom,\n\t\t\t\tArray.isArray(renderResult) ? renderResult : [renderResult],\n\t\t\t\tnewVNode,\n\t\t\t\toldVNode,\n\t\t\t\tglobalContext,\n\t\t\t\tisSvg,\n\t\t\t\texcessDomChildren,\n\t\t\t\tcommitQueue,\n\t\t\t\toldDom,\n\t\t\t\tisHydrating\n\t\t\t);\n\n\t\t\tc.base = newVNode._dom;\n\n\t\t\t// We successfully rendered this VNode, unset any stored hydration/bailout state:\n\t\t\tnewVNode._hydrating = null;\n\n\t\t\tif (c._renderCallbacks.length) {\n\t\t\t\tcommitQueue.push(c);\n\t\t\t}\n\n\t\t\tif (clearProcessingException) {\n\t\t\t\tc._pendingError = c._processingException = null;\n\t\t\t}\n\n\t\t\tc._force = false;\n\t\t} else if (\n\t\t\texcessDomChildren == null &&\n\t\t\tnewVNode._original === oldVNode._original\n\t\t) {\n\t\t\tnewVNode._children = oldVNode._children;\n\t\t\tnewVNode._dom = oldVNode._dom;\n\t\t} else {\n\t\t\tnewVNode._dom = diffElementNodes(\n\t\t\t\toldVNode._dom,\n\t\t\t\tnewVNode,\n\t\t\t\toldVNode,\n\t\t\t\tglobalContext,\n\t\t\t\tisSvg,\n\t\t\t\texcessDomChildren,\n\t\t\t\tcommitQueue,\n\t\t\t\tisHydrating\n\t\t\t);\n\t\t}\n\n\t\tif ((tmp = options.diffed)) tmp(newVNode);\n\t} catch (e) {\n\t\tnewVNode._original = null;\n\t\t// if hydrating or creating initial tree, bailout preserves DOM:\n\t\tif (isHydrating || excessDomChildren != null) {\n\t\t\tnewVNode._dom = oldDom;\n\t\t\tnewVNode._hydrating = !!isHydrating;\n\t\t\texcessDomChildren[excessDomChildren.indexOf(oldDom)] = null;\n\t\t\t// ^ could possibly be simplified to:\n\t\t\t// excessDomChildren.length = 0;\n\t\t}\n\t\toptions._catchError(e, newVNode, oldVNode);\n\t}\n}\n\n/**\n * @param {Array<import('../internal').Component>} commitQueue List of components\n * which have callbacks to invoke in commitRoot\n * @param {import('../internal').VNode} root\n */\nexport function commitRoot(commitQueue, root) {\n\tif (options._commit) options._commit(root, commitQueue);\n\n\tcommitQueue.some(c => {\n\t\ttry {\n\t\t\t// @ts-ignore Reuse the commitQueue variable here so the type changes\n\t\t\tcommitQueue = c._renderCallbacks;\n\t\t\tc._renderCallbacks = [];\n\t\t\tcommitQueue.some(cb => {\n\t\t\t\t// @ts-ignore See above ts-ignore on commitQueue\n\t\t\t\tcb.call(c);\n\t\t\t});\n\t\t} catch (e) {\n\t\t\toptions._catchError(e, c._vnode);\n\t\t}\n\t});\n}\n\n/**\n * Diff two virtual nodes representing DOM element\n * @param {import('../internal').PreactElement} dom The DOM element representing\n * the virtual nodes being diffed\n * @param {import('../internal').VNode} newVNode The new virtual node\n * @param {import('../internal').VNode} oldVNode The old virtual node\n * @param {object} globalContext The current context object\n * @param {boolean} isSvg Whether or not this DOM node is an SVG node\n * @param {*} excessDomChildren\n * @param {Array<import('../internal').Component>} commitQueue List of components\n * which have callbacks to invoke in commitRoot\n * @param {boolean} isHydrating Whether or not we are in hydration\n * @returns {import('../internal').PreactElement}\n */\nfunction diffElementNodes(\n\tdom,\n\tnewVNode,\n\toldVNode,\n\tglobalContext,\n\tisSvg,\n\texcessDomChildren,\n\tcommitQueue,\n\tisHydrating\n) {\n\tlet oldProps = oldVNode.props;\n\tlet newProps = newVNode.props;\n\tlet nodeType = newVNode.type;\n\tlet i = 0;\n\n\t// Tracks entering and exiting SVG namespace when descending through the tree.\n\tif (nodeType === 'svg') isSvg = true;\n\n\tif (excessDomChildren != null) {\n\t\tfor (; i < excessDomChildren.length; i++) {\n\t\t\tconst child = excessDomChildren[i];\n\n\t\t\t// if newVNode matches an element in excessDomChildren or the `dom`\n\t\t\t// argument matches an element in excessDomChildren, remove it from\n\t\t\t// excessDomChildren so it isn't later removed in diffChildren\n\t\t\tif (\n\t\t\t\tchild &&\n\t\t\t\t'setAttribute' in child === !!nodeType &&\n\t\t\t\t(nodeType ? child.localName === nodeType : child.nodeType === 3)\n\t\t\t) {\n\t\t\t\tdom = child;\n\t\t\t\texcessDomChildren[i] = null;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t}\n\n\tif (dom == null) {\n\t\tif (nodeType === null) {\n\t\t\t// @ts-ignore createTextNode returns Text, we expect PreactElement\n\t\t\treturn document.createTextNode(newProps);\n\t\t}\n\n\t\tif (isSvg) {\n\t\t\tdom = document.createElementNS(\n\t\t\t\t'http://www.w3.org/2000/svg',\n\t\t\t\t// @ts-ignore We know `newVNode.type` is a string\n\t\t\t\tnodeType\n\t\t\t);\n\t\t} else {\n\t\t\tdom = document.createElement(\n\t\t\t\t// @ts-ignore We know `newVNode.type` is a string\n\t\t\t\tnodeType,\n\t\t\t\tnewProps.is && newProps\n\t\t\t);\n\t\t}\n\n\t\t// we created a new parent, so none of the previously attached children can be reused:\n\t\texcessDomChildren = null;\n\t\t// we are creating a new node, so we can assume this is a new subtree (in case we are hydrating), this deopts the hydrate\n\t\tisHydrating = false;\n\t}\n\n\tif (nodeType === null) {\n\t\t// During hydration, we still have to split merged text from SSR'd HTML.\n\t\tif (oldProps !== newProps && (!isHydrating || dom.data !== newProps)) {\n\t\t\tdom.data = newProps;\n\t\t}\n\t} else {\n\t\t// If excessDomChildren was not null, repopulate it with the current element's children:\n\t\texcessDomChildren = excessDomChildren && slice.call(dom.childNodes);\n\n\t\toldProps = oldVNode.props || EMPTY_OBJ;\n\n\t\tlet oldHtml = oldProps.dangerouslySetInnerHTML;\n\t\tlet newHtml = newProps.dangerouslySetInnerHTML;\n\n\t\t// During hydration, props are not diffed at all (including dangerouslySetInnerHTML)\n\t\t// @TODO we should warn in debug mode when props don't match here.\n\t\tif (!isHydrating) {\n\t\t\t// But, if we are in a situation where we are using existing DOM (e.g. replaceNode)\n\t\t\t// we should read the existing DOM attributes to diff them\n\t\t\tif (excessDomChildren != null) {\n\t\t\t\toldProps = {};\n\t\t\t\tfor (i = 0; i < dom.attributes.length; i++) {\n\t\t\t\t\toldProps[dom.attributes[i].name] = dom.attributes[i].value;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (newHtml || oldHtml) {\n\t\t\t\t// Avoid re-applying the same '__html' if it did not changed between re-render\n\t\t\t\tif (\n\t\t\t\t\t!newHtml ||\n\t\t\t\t\t((!oldHtml || newHtml.__html != oldHtml.__html) &&\n\t\t\t\t\t\tnewHtml.__html !== dom.innerHTML)\n\t\t\t\t) {\n\t\t\t\t\tdom.innerHTML = (newHtml && newHtml.__html) || '';\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tdiffProps(dom, newProps, oldProps, isSvg, isHydrating);\n\n\t\t// If the new vnode didn't have dangerouslySetInnerHTML, diff its children\n\t\tif (newHtml) {\n\t\t\tnewVNode._children = [];\n\t\t} else {\n\t\t\ti = newVNode.props.children;\n\t\t\tdiffChildren(\n\t\t\t\tdom,\n\t\t\t\tArray.isArray(i) ? i : [i],\n\t\t\t\tnewVNode,\n\t\t\t\toldVNode,\n\t\t\t\tglobalContext,\n\t\t\t\tisSvg && nodeType !== 'foreignObject',\n\t\t\t\texcessDomChildren,\n\t\t\t\tcommitQueue,\n\t\t\t\texcessDomChildren\n\t\t\t\t\t? excessDomChildren[0]\n\t\t\t\t\t: oldVNode._children && getDomSibling(oldVNode, 0),\n\t\t\t\tisHydrating\n\t\t\t);\n\n\t\t\t// Remove children that are not part of any vnode.\n\t\t\tif (excessDomChildren != null) {\n\t\t\t\tfor (i = excessDomChildren.length; i--; ) {\n\t\t\t\t\tif (excessDomChildren[i] != null) removeNode(excessDomChildren[i]);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// (as above, don't diff props during hydration)\n\t\tif (!isHydrating) {\n\t\t\tif (\n\t\t\t\t'value' in newProps &&\n\t\t\t\t(i = newProps.value) !== undefined &&\n\t\t\t\t// #2756 For the <progress>-element the initial value is 0,\n\t\t\t\t// despite the attribute not being present. When the attribute\n\t\t\t\t// is missing the progress bar is treated as indeterminate.\n\t\t\t\t// To fix that we'll always update it when it is 0 for progress elements\n\t\t\t\t(i !== dom.value ||\n\t\t\t\t\t(nodeType === 'progress' && !i) ||\n\t\t\t\t\t// This is only for IE 11 to fix <select> value not being updated.\n\t\t\t\t\t// To avoid a stale select value we need to set the option.value\n\t\t\t\t\t// again, which triggers IE11 to re-evaluate the select value\n\t\t\t\t\t(nodeType === 'option' && i !== oldProps.value))\n\t\t\t) {\n\t\t\t\tsetProperty(dom, 'value', i, oldProps.value, false);\n\t\t\t}\n\t\t\tif (\n\t\t\t\t'checked' in newProps &&\n\t\t\t\t(i = newProps.checked) !== undefined &&\n\t\t\t\ti !== dom.checked\n\t\t\t) {\n\t\t\t\tsetProperty(dom, 'checked', i, oldProps.checked, false);\n\t\t\t}\n\t\t}\n\t}\n\n\treturn dom;\n}\n\n/**\n * Invoke or update a ref, depending on whether it is a function or object ref.\n * @param {object|function} ref\n * @param {any} value\n * @param {import('../internal').VNode} vnode\n */\nexport function applyRef(ref, value, vnode) {\n\ttry {\n\t\tif (typeof ref == 'function') ref(value);\n\t\telse ref.current = value;\n\t} catch (e) {\n\t\toptions._catchError(e, vnode);\n\t}\n}\n\n/**\n * Unmount a virtual node from the tree and apply DOM changes\n * @param {import('../internal').VNode} vnode The virtual node to unmount\n * @param {import('../internal').VNode} parentVNode The parent of the VNode that\n * initiated the unmount\n * @param {boolean} [skipRemove] Flag that indicates that a parent node of the\n * current element is already detached from the DOM.\n */\nexport function unmount(vnode, parentVNode, skipRemove) {\n\tlet r;\n\tif (options.unmount) options.unmount(vnode);\n\n\tif ((r = vnode.ref)) {\n\t\tif (!r.current || r.current === vnode._dom) {\n\t\t\tapplyRef(r, null, parentVNode);\n\t\t}\n\t}\n\n\tif ((r = vnode._component) != null) {\n\t\tif (r.componentWillUnmount) {\n\t\t\ttry {\n\t\t\t\tr.componentWillUnmount();\n\t\t\t} catch (e) {\n\t\t\t\toptions._catchError(e, parentVNode);\n\t\t\t}\n\t\t}\n\n\t\tr.base = r._parentDom = null;\n\t\tvnode._component = undefined;\n\t}\n\n\tif ((r = vnode._children)) {\n\t\tfor (let i = 0; i < r.length; i++) {\n\t\t\tif (r[i]) {\n\t\t\t\tunmount(\n\t\t\t\t\tr[i],\n\t\t\t\t\tparentVNode,\n\t\t\t\t\tskipRemove || typeof vnode.type !== 'function'\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\t}\n\n\tif (!skipRemove && vnode._dom != null) {\n\t\tremoveNode(vnode._dom);\n\t}\n\n\t// Must be set to `undefined` to properly clean up `_nextDom`\n\t// for which `null` is a valid value. See comment in `create-element.js`\n\tvnode._parent = vnode._dom = vnode._nextDom = undefined;\n}\n\n/** The `.render()` method for a PFC backing instance. */\nfunction doRender(props, state, context) {\n\treturn this.constructor(props, context);\n}\n", "import { EMPTY_OBJ } from './constants';\nimport { commitRoot, diff } from './diff/index';\nimport { createElement, Fragment } from './create-element';\nimport options from './options';\nimport { slice } from './util';\n\n/**\n * Render a Preact virtual node into a DOM element\n * @param {import('./internal').ComponentChild} vnode The virtual node to render\n * @param {import('./internal').PreactElement} parentDom The DOM element to\n * render into\n * @param {import('./internal').PreactElement | object} [replaceNode] Optional: Attempt to re-use an\n * existing DOM tree rooted at `replaceNode`\n */\nexport function render(vnode, parentDom, replaceNode) {\n\tif (options._root) options._root(vnode, parentDom);\n\n\t// We abuse the `replaceNode` parameter in `hydrate()` to signal if we are in\n\t// hydration mode or not by passing the `hydrate` function instead of a DOM\n\t// element..\n\tlet isHydrating = typeof replaceNode === 'function';\n\n\t// To be able to support calling `render()` multiple times on the same\n\t// DOM node, we need to obtain a reference to the previous tree. We do\n\t// this by assigning a new `_children` property to DOM nodes which points\n\t// to the last rendered tree. By default this property is not present, which\n\t// means that we are mounting a new tree for the first time.\n\tlet oldVNode = isHydrating\n\t\t? null\n\t\t: (replaceNode && replaceNode._children) || parentDom._children;\n\n\tvnode = (\n\t\t(!isHydrating && replaceNode) ||\n\t\tparentDom\n\t)._children = createElement(Fragment, null, [vnode]);\n\n\t// List of effects that need to be called after diffing.\n\tlet commitQueue = [];\n\tdiff(\n\t\tparentDom,\n\t\t// Determine the new vnode tree and store it on the DOM element on\n\t\t// our custom `_children` property.\n\t\tvnode,\n\t\toldVNode || EMPTY_OBJ,\n\t\tEMPTY_OBJ,\n\t\tparentDom.ownerSVGElement !== undefined,\n\t\t!isHydrating && replaceNode\n\t\t\t? [replaceNode]\n\t\t\t: oldVNode\n\t\t\t? null\n\t\t\t: parentDom.firstChild\n\t\t\t? slice.call(parentDom.childNodes)\n\t\t\t: null,\n\t\tcommitQueue,\n\t\t!isHydrating && replaceNode\n\t\t\t? replaceNode\n\t\t\t: oldVNode\n\t\t\t? oldVNode._dom\n\t\t\t: parentDom.firstChild,\n\t\tisHydrating\n\t);\n\n\t// Flush all queued effects\n\tcommitRoot(commitQueue, vnode);\n}\n\n/**\n * Update an existing DOM element with data from a Preact virtual node\n * @param {import('./internal').ComponentChild} vnode The virtual node to render\n * @param {import('./internal').PreactElement} parentDom The DOM element to\n * update\n */\nexport function hydrate(vnode, parentDom) {\n\trender(vnode, parentDom, hydrate);\n}\n", "import { assign, slice } from './util';\nimport { createVNode } from './create-element';\n\n/**\n * Clones the given VNode, optionally adding attributes/props and replacing its children.\n * @param {import('./internal').VNode} vnode The virtual DOM element to clone\n * @param {object} props Attributes/props to add when cloning\n * @param {Array<import('./internal').ComponentChildren>} rest Any additional arguments will be used as replacement children.\n * @returns {import('./internal').VNode}\n */\nexport function cloneElement(vnode, props, children) {\n\tlet normalizedProps = assign({}, vnode.props),\n\t\tkey,\n\t\tref,\n\t\ti;\n\tfor (i in props) {\n\t\tif (i == 'key') key = props[i];\n\t\telse if (i == 'ref') ref = props[i];\n\t\telse normalizedProps[i] = props[i];\n\t}\n\n\tif (arguments.length > 2) {\n\t\tnormalizedProps.children =\n\t\t\targuments.length > 3 ? slice.call(arguments, 2) : children;\n\t}\n\n\treturn createVNode(\n\t\tvnode.type,\n\t\tnormalizedProps,\n\t\tkey || vnode.key,\n\t\tref || vnode.ref,\n\t\tnull\n\t);\n}\n", "/**\n * Find the closest error boundary to a thrown error and call it\n * @param {object} error The thrown value\n * @param {import('../internal').VNode} vnode The vnode that threw\n * the error that was caught (except for unmounting when this parameter\n * is the highest parent that was being unmounted)\n * @param {import('../internal').VNode} [oldVNode]\n * @param {import('../internal').ErrorInfo} [errorInfo]\n */\nexport function _catchError(error, vnode, oldVNode, errorInfo) {\n\t/** @type {import('../internal').Component} */\n\tlet component, ctor, handled;\n\n\tfor (; (vnode = vnode._parent); ) {\n\t\tif ((component = vnode._component) && !component._processingException) {\n\t\t\ttry {\n\t\t\t\tctor = component.constructor;\n\n\t\t\t\tif (ctor && ctor.getDerivedStateFromError != null) {\n\t\t\t\t\tcomponent.setState(ctor.getDerivedStateFromError(error));\n\t\t\t\t\thandled = component._dirty;\n\t\t\t\t}\n\n\t\t\t\tif (component.componentDidCatch != null) {\n\t\t\t\t\tcomponent.componentDidCatch(error, errorInfo || {});\n\t\t\t\t\thandled = component._dirty;\n\t\t\t\t}\n\n\t\t\t\t// This is an error boundary. Mark it as having bailed out, and whether it was mid-hydration.\n\t\t\t\tif (handled) {\n\t\t\t\t\treturn (component._pendingError = component);\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\terror = e;\n\t\t\t}\n\t\t}\n\t}\n\n\tthrow error;\n}\n"], "names": ["slice", "options", "vnodeId", "isValidElement", "rerenderQueue", "prevDebounce", "i", "EMPTY_OBJ", "EMPTY_ARR", "IS_NON_DIMENSIONAL", "assign", "obj", "props", "removeNode", "node", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "createElement", "type", "children", "key", "ref", "normalizedProps", "arguments", "length", "call", "defaultProps", "undefined", "createVNode", "original", "vnode", "__k", "__", "__b", "__e", "__d", "__c", "__h", "constructor", "__v", "createRef", "current", "Fragment", "Component", "context", "this", "getDomSibling", "childIndex", "indexOf", "sibling", "updateParentDomPointers", "child", "base", "enqueueRender", "c", "push", "process", "debounceRendering", "setTimeout", "queue", "__r", "sort", "a", "b", "some", "component", "commitQueue", "oldVNode", "oldDom", "parentDom", "__P", "diff", "ownerSVGElement", "commitRoot", "diff<PERSON><PERSON><PERSON><PERSON>", "renderResult", "newParentVNode", "oldParentVNode", "globalContext", "isSvg", "excessDomChildren", "isHydrating", "j", "childVNode", "newDom", "firstChildDom", "refs", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Array", "isArray", "reorderC<PERSON>dren", "<PERSON><PERSON><PERSON><PERSON>", "unmount", "applyRef", "tmp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "out", "nextDom", "sibDom", "outer", "append<PERSON><PERSON><PERSON>", "nextS<PERSON>ling", "insertBefore", "diffProps", "dom", "newProps", "oldProps", "hydrate", "setProperty", "setStyle", "style", "value", "test", "name", "oldValue", "useCapture", "o", "cssText", "replace", "toLowerCase", "l", "addEventListener", "eventProxyCapture", "eventProxy", "removeEventListener", "e", "removeAttribute", "setAttribute", "event", "newVNode", "isNew", "oldState", "snapshot", "clearProcessingException", "provider", "componentContext", "renderHook", "count", "newType", "contextType", "__E", "prototype", "render", "doR<PERSON>", "sub", "state", "_sb", "__s", "getDerivedStateFromProps", "componentWillMount", "componentDidMount", "componentWillReceiveProps", "shouldComponentUpdate", "for<PERSON>ach", "componentWillUpdate", "componentDidUpdate", "getChildContext", "getSnapshotBeforeUpdate", "diffElementNodes", "diffed", "root", "cb", "oldHtml", "newHtml", "nodeType", "localName", "document", "createTextNode", "createElementNS", "is", "data", "childNodes", "dangerouslySetInnerHTML", "attributes", "__html", "innerHTML", "checked", "parentVNode", "<PERSON><PERSON><PERSON><PERSON>", "r", "componentWillUnmount", "replaceNode", "<PERSON><PERSON><PERSON><PERSON>", "cloneElement", "createContext", "defaultValue", "contextId", "Consumer", "contextValue", "Provider", "subs", "ctx", "_props", "old", "splice", "error", "errorInfo", "ctor", "handled", "getDerivedStateFromError", "setState", "componentDidCatch", "update", "callback", "s", "forceUpdate"], "mappings": ";;;;;;;;;;;;;;IA0BaA,GCfPC,GCRFC,GA6FSC,GC4ETC,GAWAC,GCrLOC,GCFEC,IAAY,CAAlB,GACMC,IAAY,EAAA,EACZC,IAAqB;ALOlBC,SAAAA,EAAOC,CAAAA,EAAKC,CAAAA;IAE3B,IAAK,IAAIN,KAAKM,EAAOD,CAAAA,CAAIL,EAAAA,GAAKM,CAAAA,CAAMN,EAAAA;IACpC,OAA6BK;AAC7B;AAQM,SAASE,EAAWC,CAAAA;IAC1B,IAAIC,IAAaD,EAAKC,UAAAA;IAClBA,KAAYA,EAAWC,WAAAA,CAAYF;AACvC;AEXM,SAASG,EAAcC,CAAAA,EAAMN,CAAAA,EAAOO,CAAAA;IAC1C,IACCC,GACAC,GACAf,GAHGgB,IAAkB,CAAA;IAItB,IAAKhB,KAAKM,EACA,SAALN,IAAYc,IAAMR,CAAAA,CAAMN,EAAAA,GACd,SAALA,IAAYe,IAAMT,CAAAA,CAAMN,EAAAA,GAC5BgB,CAAAA,CAAgBhB,EAAAA,GAAKM,CAAAA,CAAMN,EAAAA;IAUjC,IAPIiB,UAAUC,MAAAA,GAAS,KAAA,CACtBF,EAAgBH,QAAAA,GACfI,UAAUC,MAAAA,GAAS,IAAIxB,EAAMyB,IAAAA,CAAKF,WAAW,KAAKJ,CAAAA,GAKjC,cAAA,OAARD,KAA2C,QAArBA,EAAKQ,YAAAA,EACrC,IAAKpB,KAAKY,EAAKQ,YAAAA,CAAAA,KACaC,MAAvBL,CAAAA,CAAgBhB,EAAAA,IAAAA,CACnBgB,CAAAA,CAAgBhB,EAAAA,GAAKY,EAAKQ,YAAAA,CAAapB,EAAAA;IAK1C,OAAOsB,EAAYV,GAAMI,GAAiBF,GAAKC,GAAK;AACpD;AAAA,SAceO,EAAYV,CAAAA,EAAMN,CAAAA,EAAOQ,CAAAA,EAAKC,CAAAA,EAAKQ,CAAAA;IAGlD,IAAMC,IAAQ;QACbZ,MAAAA;QACAN,OAAAA;QACAQ,KAAAA;QACAC,KAAAA;QACAU,KAAW;QACXC,IAAS;QACTC,KAAQ;QACRC,KAAM;QAKNC,KAAAA,KAAUR;QACVS,KAAY;QACZC,KAAY;QACZC,aAAAA,KAAaX;QACbY,KAAuB,QAAZV,IAAAA,EAAqB3B,IAAU2B;IAAAA;IAM3C,OAFgB,QAAZA,KAAqC,QAAjB5B,EAAQ6B,KAAAA,IAAe7B,EAAQ6B,KAAAA,CAAMA,IAEtDA;AACP;AAEM,SAASU;IACf,OAAO;QAAEC,SAAS;IAAA;AAClB;AAEM,SAASC,EAAS9B,CAAAA;IACxB,OAAOA,EAAMO;AACb;AAAA,SC7EewB,EAAU/B,CAAAA,EAAOgC,CAAAA;IAChCC,IAAAA,CAAKjC,KAAAA,GAAQA,GACbiC,IAAAA,CAAKD,OAAAA,GAAUA;AACf;AAAA,SA0EeE,EAAchB,CAAAA,EAAOiB,CAAAA;IACpC,IAAkB,QAAdA,GAEH,OAAOjB,EAAKE,EAAAA,GACTc,EAAchB,EAADE,EAAAA,EAAgBF,EAAAE,EAAAA,CAAAD,GAAAA,CAAwBiB,OAAAA,CAAQlB,KAAS,KACtE;IAIJ,IADA,IAAImB,GACGF,IAAajB,EAAAC,GAAAA,CAAgBP,MAAAA,EAAQuB,IAG3C,IAAe,QAAA,CAFfE,IAAUnB,EAAKC,GAAAA,CAAWgB,EAAAA,KAEa,QAAhBE,EAAAf,GAAAA,EAItB,OAAOe,EACPf,GAAAA;IAQF,OAA4B,cAAA,OAAdJ,EAAMZ,IAAAA,GAAqB4B,EAAchB,KAAS;AAChE;AAsCD,SAASoB,EAAwBpB,CAAAA;IAAjC,IAGWxB,GACJ6C;IAHN,IAA+B,QAAA,CAA1BrB,IAAQA,EAAHE,EAAAA,KAAiD,QAApBF,EAAKM,GAAAA,EAAqB;QAEhE,IADAN,EAAAA,GAAAA,GAAaA,EAAAM,GAAAA,CAAiBgB,IAAAA,GAAO,MAC5B9C,IAAI,GAAGA,IAAIwB,EAAAA,GAAAA,CAAgBN,MAAAA,EAAQlB,IAE3C,IAAa,QAAA,CADT6C,IAAQrB,EAAAC,GAAAA,CAAgBzB,EAAAA,KACO,QAAd6C,EAAKjB,GAAAA,EAAe;YACxCJ,EAAAI,GAAAA,GAAaJ,EAAKM,GAAAA,CAAYgB,IAAAA,GAAOD,EAAxBjB,GAAAA;YACb;QACA;QAGF,OAAOgB,EAAwBpB;IAC/B;AACD;AAuBM,SAASuB,EAAcC,CAAAA;IAAAA,CAAAA,CAE1BA,EAAAA,GAAAA,IAAAA,CACAA,EAACnB,GAAAA,GAAAA,CAAU,CAAA,KACZ/B,EAAcmD,IAAAA,CAAKD,MAAAA,CAClBE,EAAAA,GAAAA,MACFnD,MAAiBJ,EAAQwD,iBAAAA,KAAAA,CAAAA,CAEzBpD,IAAeJ,EAAQwD,iBAAAA,KACNC,UAAAA,EAAYF;AAE9B;AAGD,SAASA;IAER,IADA,IAAIG,GACIH,EAAOI,GAAAA,GAAkBxD,EAAcoB,MAAAA,EAC9CmC,IAAQvD,EAAcyD,IAAAA,CAAK,SAACC,CAAAA,EAAGC,CAAAA;QAAJ,OAAUD,EAAAvB,GAAAA,CAAAN,GAAAA,GAAkB8B,EAA5BxB,GAAAA,CAAAN;IAAA,IAC3B7B,IAAgB,EAAA,EAGhBuD,EAAMK,IAAAA,CAAK,SAAAV,CAAAA;QAzFb,IAAyBW,GAMnBC,GACEC,GANHrC,GACHsC,GACAC;QAuFKf,EAAJnB,GAAAA,IAAAA,CAxFDiC,IAAAA,CADGtC,IAAAA,CADoBmC,IA0FQX,CAAAA,EAzFhCf,GAAAA,EAAAL,GAAAA,EAAAA,CAECmC,IAAYJ,EAFbK,GAAAA,KAAAA,CAKKJ,IAAc,EAAA,EAAA,CACZC,IAAWzD,EAAO,CAAA,GAAIoB,EAAAA,EAC5BS,GAAAA,GAAqBT,EAAKS,GAAAA,GAAa,GAEvCgC,EACCF,GACAvC,GACAqC,GACAF,EAAAA,GAAAA,EAAAA,KAC8BtC,MAA9B0C,EAAUG,eAAAA,EACU,QAApB1C,EAAKO,GAAAA,GAAsB;YAAC+B;SAAAA,GAAU,MACtCF,GACU,QAAVE,IAAiBtB,EAAchB,KAASsC,GACxCtC,EATDO,GAAAA,GAWAoC,EAAWP,GAAapC,IAEpBA,EAAKI,GAAAA,IAASkC,KACjBlB,EAAwBpB,EAAAA,CAAAA;IAmExB;AAEF;AAAA,SG7Le4C,EACfL,CAAAA,EACAM,CAAAA,EACAC,CAAAA,EACAC,CAAAA,EACAC,CAAAA,EACAC,CAAAA,EACAC,CAAAA,EACAd,CAAAA,EACAE,CAAAA,EACAa,CAAAA;IAAAA,IAEI3E,GAAG4E,GAAGf,GAAUgB,GAAYC,GAAQC,GAAeC,GAInDC,IAAeV,KAAkBA,EAAJ9C,GAAAA,IAAiCvB,GAE9DgF,IAAoBD,EAAY/D,MAAAA;IAGpC,IADAoD,EAAAA,GAAAA,GAA2B,EAAA,EACtBtE,IAAI,GAAGA,IAAIqE,EAAanD,MAAAA,EAAQlB,IAgDpC,IAAkB,QAAA,CA5CjB6E,IAAaP,EAAc7C,GAAAA,CAAWzB,EAAAA,GADrB,QAAA,CAFlB6E,IAAaR,CAAAA,CAAarE,EAAAA,KAEqB,aAAA,OAAd6E,IACW,OAMtB,YAAA,OAAdA,KACc,YAAA,OAAdA,KAEc,YAAA,OAAdA,IAEoCvD,EAC1C,MACAuD,GACA,MACA,MACAA,KAESM,MAAMC,OAAAA,CAAQP,KACmBvD,EAC1Cc,GACA;QAAEvB,UAAUgE;IAAAA,GACZ,MACA,MACA,QAESA,EAAAlD,GAAAA,GAAoB,IAKaL,EAC1CuD,EAAWjE,IAAAA,EACXiE,EAAWvE,KAAAA,EACXuE,EAAW/D,GAAAA,EACX+D,EAAW9D,GAAAA,GAAM8D,EAAW9D,GAAAA,GAAM,MAClC8D,EAED5C,GAAAA,IAC2C4C,CAAAA,GAK5C;QAaA,IATAA,EAAAnD,EAAAA,GAAqB4C,GACrBO,EAAUlD,GAAAA,GAAU2C,EAAA3C,GAAAA,GAAwB,GAS9B,SAAA,CAHdkC,IAAWoB,CAAAA,CAAYjF,EAAAA,KAIrB6D,KACAgB,EAAW/D,GAAAA,IAAO+C,EAAS/C,GAAAA,IAC3B+D,EAAWjE,IAAAA,KAASiD,EAASjD,IAAAA,EAE9BqE,CAAAA,CAAYjF,EAAAA,GAAAA,KAAKqB;aAIjB,IAAKuD,IAAI,GAAGA,IAAIM,GAAmBN,IAAK;YAIvC,IAAA,CAHAf,IAAWoB,CAAAA,CAAYL,EAAAA,KAKtBC,EAAW/D,GAAAA,IAAO+C,EAAS/C,GAAAA,IAC3B+D,EAAWjE,IAAAA,KAASiD,EAASjD,IAAAA,EAC5B;gBACDqE,CAAAA,CAAYL,EAAAA,GAAAA,KAAKvD;gBACjB;YACA;YACDwC,IAAW;QACX;QAMFI,EACCF,GACAc,GALDhB,IAAWA,KAAY5D,GAOtBuE,GACAC,GACAC,GACAd,GACAE,GACAa,IAGDG,IAASD,EAATjD,GAAAA,EAAAA,CAEKgD,IAAIC,EAAW9D,GAAAA,KAAQ8C,EAAS9C,GAAAA,IAAO6D,KAAAA,CACtCI,KAAAA,CAAMA,IAAO,EAAA,GACdnB,EAAS9C,GAAAA,IAAKiE,EAAK/B,IAAAA,CAAKY,EAAS9C,GAAAA,EAAK,MAAM8D,IAChDG,EAAK/B,IAAAA,CAAK2B,GAAGC,EAAA/C,GAAAA,IAAyBgD,GAAQD,EAAAA,GAGjC,QAAVC,IAAAA,CACkB,QAAjBC,KAAAA,CACHA,IAAgBD,CAAAA,GAIU,cAAA,OAAnBD,EAAWjE,IAAAA,IAClBiE,EAAApD,GAAAA,KAAyBoC,EAAzBpC,GAAAA,GAEAoD,EAAUhD,GAAAA,GAAYiC,IAASuB,EAC9BR,GACAf,GACAC,KAGDD,IAASwB,EACRvB,GACAc,GACAhB,GACAoB,GACAH,GACAhB,IAIgC,cAAA,OAAvBQ,EAAe1D,IAAAA,IAAAA,CAQzB0D,EAAAzC,GAAAA,GAA0BiC,CAAAA,CAAAA,IAG3BA,KACAD,EAAQjC,GAAAA,IAASkC,KACjBA,EAAOrD,UAAAA,IAAcsD,KAAAA,CAIrBD,IAAStB,EAAcqB,EAAAA;IAtGvB;IA6GF,IAHAS,EAAA1C,GAAAA,GAAsBmD,GAGjB/E,IAAIkF,GAAmBlF,KACL,QAAlBiF,CAAAA,CAAYjF,EAAAA,IACfuF,EAAQN,CAAAA,CAAYjF,EAAAA,EAAIiF,CAAAA,CAAYjF,EAAAA;IAKtC,IAAIgF,GACH,IAAKhF,IAAI,GAAGA,IAAIgF,EAAK9D,MAAAA,EAAQlB,IAC5BwF,EAASR,CAAAA,CAAKhF,EAAAA,EAAIgF,CAAAA,CAAAA,EAAOhF,EAAAA,EAAIgF,CAAAA,CAAAA,EAAOhF,EAAAA;AAGtC;AAED,SAASqF,EAAgBR,CAAAA,EAAYf,CAAAA,EAAQC,CAAAA;IAI5C,IAJD,IAKMvC,GAHDwB,IAAI6B,EAAHpD,GAAAA,EACDgE,IAAM,GACHzC,KAAKyC,IAAMzC,EAAE9B,MAAAA,EAAQuE,IAAAA,CACvBjE,IAAQwB,CAAAA,CAAEyC,EAAAA,KAAAA,CAMbjE,EAAAA,EAAAA,GAAgBqD,GAGff,IADwB,cAAA,OAAdtC,EAAMZ,IAAAA,GACPyE,EAAgB7D,GAAOsC,GAAQC,KAE/BuB,EAAWvB,GAAWvC,GAAOA,GAAOwB,GAAGxB,EAAYsC,GAAAA,EAAAA,EAAAA;IAK/D,OAAOA;AACP;AAQe4B,SAAAA,EAAa7E,CAAAA,EAAU8E,CAAAA;IAUtC,OATAA,IAAMA,KAAO,EAAA,EACG,QAAZ9E,KAAuC,aAAA,OAAZA,KAAAA,CACpBsE,MAAMC,OAAAA,CAAQvE,KACxBA,EAAS6C,IAAAA,CAAK,SAAAb,CAAAA;QACb6C,EAAa7C,GAAO8C;IACpB,KAEDA,EAAI1C,IAAAA,CAAKpC,EAAAA,GAEH8E;AACP;AAED,SAASL,EACRvB,CAAAA,EACAc,CAAAA,EACAhB,CAAAA,EACAoB,CAAAA,EACAH,CAAAA,EACAhB,CAAAA;IAND,IAQK8B,GAuBGC,GAAiBjB;IAtBxB,IAAA,KAA4BvD,MAAxBwD,EAAUhD,GAAAA,EAIb+D,IAAUf,EAAHhD,GAAAA,EAMPgD,EAAAA,GAAAA,GAAAA,KAAsBxD;SAEtBwC,IAAY,QAAZA,KACAiB,KAAUhB,KACW,QAArBgB,EAAOrE,UAAAA,EAEPqF,GAAO,IAAc,QAAVhC,KAAkBA,EAAOrD,UAAAA,KAAesD,GAClDA,EAAUgC,WAAAA,CAAYjB,IACtBc,IAAU;SACJ;QAEN,IACKC,IAAS/B,GAAQc,IAAI,GAAA,CACxBiB,IAASA,EAAOG,WAAAA,KAAgBpB,IAAIK,EAAY/D,MAAAA,EACjD0D,KAAK,EAEL,IAAIiB,KAAUf,GACb,MAAMgB;QAGR/B,EAAUkC,YAAAA,CAAanB,GAAQhB,IAC/B8B,IAAU9B;IACV;IAYF,OAAA,KANgBzC,MAAZuE,IACMA,IAEAd,EAAOkB;AAIjB;AChTeE,SAAAA,EAAUC,CAAAA,EAAKC,CAAAA,EAAUC,CAAAA,EAAU5B,CAAAA,EAAO6B,CAAAA;IACzD,IAAItG;IAEJ,IAAKA,KAAKqG,EACC,eAANrG,KAA0B,UAANA,KAAiBA,KAAKoG,KAC7CG,EAAYJ,GAAKnG,GAAG,MAAMqG,CAAAA,CAASrG,EAAAA,EAAIyE;IAIzC,IAAKzE,KAAKoG,EAENE,KAAiC,cAAA,OAAfF,CAAAA,CAASpG,EAAAA,IACvB,eAANA,KACM,UAANA,KACM,YAANA,KACM,cAANA,KACAqG,CAAAA,CAASrG,EAAAA,KAAOoG,CAAAA,CAASpG,EAAAA,IAEzBuG,EAAYJ,GAAKnG,GAAGoG,CAAAA,CAASpG,EAAAA,EAAIqG,CAAAA,CAASrG,EAAAA,EAAIyE;AAGhD;AAED,SAAS+B,EAASC,CAAAA,EAAO3F,CAAAA,EAAK4F,CAAAA;IACd,QAAX5F,CAAAA,CAAI,EAAA,GACP2F,EAAMF,WAAAA,CAAYzF,GAAK4F,KAEvBD,CAAAA,CAAM3F,EAAAA,GADa,QAAT4F,IACG,KACa,YAAA,OAATA,KAAqBvG,EAAmBwG,IAAAA,CAAK7F,KACjD4F,IAEAA,IAAQ;AAEtB;AAAA,SAUeH,EAAYJ,CAAAA,EAAKS,CAAAA,EAAMF,CAAAA,EAAOG,CAAAA,EAAUpC,CAAAA;IAAAA,IACnDqC;IAEJC,GAAG,IAAa,YAATH,GACN,IAAoB,YAAA,OAATF,GACVP,EAAIM,KAAAA,CAAMO,OAAAA,GAAUN;SACd;QAKN,IAJuB,YAAA,OAAZG,KAAAA,CACVV,EAAIM,KAAAA,CAAMO,OAAAA,GAAUH,IAAW,EAAA,GAG5BA,GACH,IAAKD,KAAQC,EACNH,KAASE,KAAQF,KACtBF,EAASL,EAAIM,KAAAA,EAAOG,GAAM;QAK7B,IAAIF,GACH,IAAKE,KAAQF,EACPG,KAAYH,CAAAA,CAAME,EAAAA,KAAUC,CAAAA,CAASD,EAAAA,IACzCJ,EAASL,EAAIM,KAAAA,EAAOG,GAAMF,CAAAA,CAAME,EAAAA;IAInC;SAGOA,IAAY,QAAZA,CAAAA,CAAK,EAAA,IAA0B,QAAZA,CAAAA,CAAK,EAAA,EAChCE,IAAaF,MAAAA,CAAUA,IAAOA,EAAKK,OAAAA,CAAQ,YAAY,GAAA,GAGxBL,IAA3BA,EAAKM,WAAAA,MAAiBf,IAAYS,EAAKM,WAAAA,GAAcxH,KAAAA,CAAM,KACnDkH,EAAKlH,KAAAA,CAAM,IAElByG,EAALgB,CAAAA,IAAAA,CAAqBhB,EAAAgB,CAAAA,GAAiB,CAAA,CAAA,GACtChB,EAAAgB,CAAAA,CAAeP,IAAOE,EAAAA,GAAcJ,GAEhCA,IACEG,KAEJV,EAAIiB,gBAAAA,CAAiBR,GADLE,IAAaO,IAAoBC,GACbR,KAIrCX,EAAIoB,mBAAAA,CAAoBX,GADRE,IAAaO,IAAoBC,GACVR;SAAAA,IAErB,8BAATF,GAAoC;QAC9C,IAAInC,GAIHmC,IAAOA,EAAKK,OAAAA,CAAQ,eAAe,KAAKA,OAAAA,CAAQ,UAAU;aACpD,IACG,WAATL,KACS,WAATA,KACS,WAATA,KAGS,eAATA,KACS,eAATA,KACAA,KAAQT,GAER,IAAA;YACCA,CAAAA,CAAIS,EAAAA,GAAiB,QAATF,IAAgB,KAAKA;YAEjC,MAAMK;QAAAA,EACL,OAAOS,GAAAA,CAAAA;QAUW,cAAA,OAAVd,KAAAA,CAES,QAATA,KAAAA,CAA4B,MAAVA,KAAAA,CAAyC,KAAtBE,EAAKlE,OAAAA,CAAQ,OAG5DyD,EAAIsB,eAAAA,CAAgBb,KAFpBT,EAAIuB,YAAAA,CAAad,GAAMF,EAAAA;IAIxB;AACD;AAOD,SAASY,EAAWE,CAAAA;IACnBjF,IAAAA,CAAAA,CAAAA,CAAgBiF,EAAE5G,IAAAA,GAAAA,CAAO,EAAA,CAAOjB,EAAQgI,KAAAA,GAAQhI,EAAQgI,KAAAA,CAAMH,KAAKA;AACnE;AAED,SAASH,EAAkBG,CAAAA;IAC1BjF,IAAAA,CAAA4E,CAAAA,CAAgBK,EAAE5G,IAAAA,GAAAA,CAAO,EAAA,CAAMjB,EAAQgI,KAAAA,GAAQhI,EAAQgI,KAAAA,CAAMH,KAAKA;AAClE;AClIevD,SAAAA,EACfF,CAAAA,EACA6D,CAAAA,EACA/D,CAAAA,EACAW,CAAAA,EACAC,CAAAA,EACAC,CAAAA,EACAd,CAAAA,EACAE,CAAAA,EACAa,CAAAA;IATeV,IAWXwB,GAoBEzC,GAAG6E,GAAOxB,GAAUyB,GAAUC,GAAUC,GACxC5B,GAKA6B,GACAC,GA6FOlI,GA4BPmI,GACHC,GASSpI,GA6BNqE,GA1LLgE,IAAUT,EAAShH,IAAAA;IAIpB,IAAA,KAA6BS,MAAzBuG,EAAS5F,WAAAA,EAA2B,OAAA;IAGb,QAAvB6B,EAAA9B,GAAAA,IAAAA,CACH4C,IAAcd,EAAH9B,GAAAA,EACX+B,IAAS8D,EAAAhG,GAAAA,GAAgBiC,EAAhBjC,GAAAA,EAETgG,EAAA7F,GAAAA,GAAsB,MACtB2C,IAAoB;QAACZ;KAAAA,GAAAA,CAGjB2B,IAAM9F,EAAAA,GAAAA,KAAgB8F,EAAImC;IAE/B,IAAA;QACC9B,GAAO,IAAsB,cAAA,OAAXuC,GAAuB;YA4DxC,IA1DIjC,IAAWwB,EAAStH,KAAAA,EAKpB2H,IAAAA,CADJxC,IAAM4C,EAAQC,WAAAA,KACQ9D,CAAAA,CAAciB,EAApC3D,GAAAA,CAAAA,EACIoG,IAAmBzC,IACpBwC,IACCA,EAAS3H,KAAAA,CAAMoG,KAAAA,GACfjB,EAHsB/D,EAAAA,GAIvB8C,GAGCX,EAAqB/B,GAAAA,GAExBkG,IAAAA,CADAhF,IAAI4E,EAAQ9F,GAAAA,GAAc+B,EAA1B/B,GAAAA,EAC4BJ,EAAAA,GAAwBsB,EACpDuF,GAAAA,GAAAA,CAEI,eAAeF,KAAWA,EAAQG,SAAAA,CAAUC,MAAAA,GAE/Cb,EAAQ9F,GAAAA,GAAckB,IAAI,IAAIqF,EAAQjC,GAAU8B,KAAAA,CAGhDN,EAAA9F,GAAAA,GAAsBkB,IAAI,IAAIX,EAAU+D,GAAU8B,IAClDlF,EAAEhB,WAAAA,GAAcqG,GAChBrF,EAAEyF,MAAAA,GAASC,CAAAA,GAERT,KAAUA,EAASU,GAAAA,CAAI3F,IAE3BA,EAAE1C,KAAAA,GAAQ8F,GACLpD,EAAE4F,KAAAA,IAAAA,CAAO5F,EAAE4F,KAAAA,GAAQ,CAAV,CAAA,GACd5F,EAAEV,OAAAA,GAAU4F,GACZlF,EAAAA,GAAAA,GAAmBwB,GACnBqD,IAAQ7E,EAAAnB,GAAAA,GAAAA,CAAW,GACnBmB,EAACjB,GAAAA,GAAoB,EAAA,EACrBiB,EAAA6F,GAAAA,GAAoB,EAAA,GAID,QAAhB7F,EAAA8F,GAAAA,IAAAA,CACH9F,EAAA8F,GAAAA,GAAe9F,EAAE4F,KAAAA,GAGsB,QAApCP,EAAQU,wBAAAA,IAAAA,CACP/F,EAAA8F,GAAAA,IAAgB9F,EAAE4F,KAAAA,IAAAA,CACrB5F,EAAA8F,GAAAA,GAAe1I,EAAO,CAAD,GAAK4C,EAAL8F,GAAAA,CAAAA,GAGtB1I,EACC4C,EADK8F,GAAAA,EAELT,EAAQU,wBAAAA,CAAyB3C,GAAUpD,EAA3C8F,GAAAA,EAAAA,GAIFzC,IAAWrD,EAAE1C,KAAAA,EACbwH,IAAW9E,EAAE4F,KAAAA,EAGTf,GAEkC,QAApCQ,EAAQU,wBAAAA,IACgB,QAAxB/F,EAAEgG,kBAAAA,IAEFhG,EAAEgG,kBAAAA,IAGwB,QAAvBhG,EAAEiG,iBAAAA,IACLjG,EAACjB,GAAAA,CAAkBkB,IAAAA,CAAKD,EAAEiG,iBAAAA;iBAErB;gBASN,IAPqC,QAApCZ,EAAQU,wBAAAA,IACR3C,MAAaC,KACkB,QAA/BrD,EAAEkG,yBAAAA,IAEFlG,EAAEkG,yBAAAA,CAA0B9C,GAAU8B,IAAAA,CAIpClF,EACDA,GAAAA,IAA2B,QAA3BA,EAAEmG,qBAAAA,IAAAA,CAKI,MAJNnG,EAAEmG,qBAAAA,CACD/C,GACApD,EACAkF,GAAAA,EAAAA,MAEFN,EAAA3F,GAAAA,KAAuB4B,EAAvB5B,GAAAA,EACC;oBAYD,IAXAe,EAAE1C,KAAAA,GAAQ8F,GACVpD,EAAE4F,KAAAA,GAAQ5F,EAEV8F,GAAAA,EAAIlB,EAAQ3F,GAAAA,KAAe4B,EAA3B5B,GAAAA,IAAAA,CAA+Ce,EAACnB,GAAAA,GAAAA,CAAU,CAAA,GAC1DmB,EAAAf,GAAAA,GAAW2F,GACXA,EAAQhG,GAAAA,GAAQiC,EAAhBjC,GAAAA,EACAgG,EAAQnG,GAAAA,GAAaoC,EACrB+D,GAAAA,EAAAA,EAAAnG,GAAAA,CAAmB2H,OAAAA,CAAQ,SAAA5H,CAAAA;wBACtBA,KAAAA,CAAOA,EAAAE,EAAAA,GAAgBkG,CAAAA;oBAC3B,IAEQ5H,IAAI,GAAGA,IAAIgD,EAAA6F,GAAAA,CAAkB3H,MAAAA,EAAQlB,IAC7CgD,EAACjB,GAAAA,CAAkBkB,IAAAA,CAAKD,EAAA6F,GAAAA,CAAkB7I,EAAAA;oBAE3CgD,EAAC6F,GAAAA,GAAmB,EAAA,EAEhB7F,EAACjB,GAAAA,CAAkBb,MAAAA,IACtB0C,EAAYX,IAAAA,CAAKD;oBAGlB,MAAM8C;gBACN;gBAE4B,QAAzB9C,EAAEqG,mBAAAA,IACLrG,EAAEqG,mBAAAA,CAAoBjD,GAAUpD,EAAAA,GAAAA,EAAckF,IAGnB,QAAxBlF,EAAEsG,kBAAAA,IACLtG,EAAAjB,GAAAA,CAAmBkB,IAAAA,CAAK;oBACvBD,EAAEsG,kBAAAA,CAAmBjD,GAAUyB,GAAUC;gBACzC;YAEF;YASD,IAPA/E,EAAEV,OAAAA,GAAU4F,GACZlF,EAAE1C,KAAAA,GAAQ8F,GACVpD,EAAAf,GAAAA,GAAW2F,GACX5E,EAACgB,GAAAA,GAAcD,GAEXoE,IAAaxI,EAAjB2D,GAAAA,EACC8E,IAAQ,GACL,eAAeC,KAAWA,EAAQG,SAAAA,CAAUC,MAAAA,EAAQ;gBAQvD,IAPAzF,EAAE4F,KAAAA,GAAQ5F,EACVA,GAAAA,EAAAA,EAAAnB,GAAAA,GAAAA,CAAW,GAEPsG,KAAYA,EAAWP,IAE3BnC,IAAMzC,EAAEyF,MAAAA,CAAOzF,EAAE1C,KAAAA,EAAO0C,EAAE4F,KAAAA,EAAO5F,EAAEV,OAAAA,GAE1BtC,IAAI,GAAGA,IAAIgD,EAAA6F,GAAAA,CAAkB3H,MAAAA,EAAQlB,IAC7CgD,EAACjB,GAAAA,CAAkBkB,IAAAA,CAAKD,EAAA6F,GAAAA,CAAkB7I,EAAAA;gBAE3CgD,EAAC6F,GAAAA,GAAmB;YACpB,OACA,GAAA;gBACC7F,EAAAA,GAAAA,GAAAA,CAAW,GACPmF,KAAYA,EAAWP,IAE3BnC,IAAMzC,EAAEyF,MAAAA,CAAOzF,EAAE1C,KAAAA,EAAO0C,EAAE4F,KAAAA,EAAO5F,EAAEV,OAAAA,GAGnCU,EAAE4F,KAAAA,GAAQ5F,EACV8F,GAAAA;YAAAA,QAAQ9F,EAAAnB,GAAAA,IAAAA,EAAcuG,IAAQ;YAIhCpF,EAAE4F,KAAAA,GAAQ5F,EAAV8F,GAAAA,EAEyB,QAArB9F,EAAEuG,eAAAA,IAAAA,CACL/E,IAAgBpE,EAAOA,EAAO,CAAD,GAAKoE,IAAgBxB,EAAEuG,eAAAA,GAAAA,GAGhD1B,KAAsC,QAA7B7E,EAAEwG,uBAAAA,IAAAA,CACfzB,IAAW/E,EAAEwG,uBAAAA,CAAwBnD,GAAUyB,EAAAA,GAK5CzD,IADI,QAAPoB,KAAeA,EAAI7E,IAAAA,KAASwB,KAAuB,QAAXqD,EAAI3E,GAAAA,GACL2E,EAAInF,KAAAA,CAAMO,QAAAA,GAAW4E,GAE7DrB,EACCL,GACAoB,MAAMC,OAAAA,CAAQf,KAAgBA,IAAe;gBAACA;aAAAA,EAC9CuD,GACA/D,GACAW,GACAC,GACAC,GACAd,GACAE,GACAa,IAGD3B,EAAEF,IAAAA,GAAO8E,EAGTA,GAAAA,EAAAA,EAAA7F,GAAAA,GAAsB,MAElBiB,EAAAjB,GAAAA,CAAmBb,MAAAA,IACtB0C,EAAYX,IAAAA,CAAKD,IAGdgF,KAAAA,CACHhF,EAACuF,GAAAA,GAAiBvF,EAAAtB,EAAAA,GAAyB,IAAA,GAG5CsB,EAACpB,GAAAA,GAAAA,CAAU;QACX,OACqB,QAArB8C,KACAkD,EAAA3F,GAAAA,KAAuB4B,EAFjB5B,GAAAA,GAAAA,CAIN2F,EAAAnG,GAAAA,GAAqBoC,EAArBpC,GAAAA,EACAmG,EAAQhG,GAAAA,GAAQiC,EAChBjC,GAAAA,IACAgG,EAAQhG,GAAAA,GAAQ6H,EACf5F,EACA+D,GAAAA,EAAAA,GACA/D,GACAW,GACAC,GACAC,GACAd,GACAe;QAAAA,CAIGc,IAAM9F,EAAQ+J,MAAAA,KAASjE,EAAImC;IAYhC,EAXC,OAAOJ,GAAAA;QACRI,EAAA3F,GAAAA,GAAqB,MAAA,CAEjB0C,KAAoC,QAArBD,CAAAA,KAAAA,CAClBkD,EAAAhG,GAAAA,GAAgBkC,GAChB8D,EAAQ7F,GAAAA,GAAAA,CAAAA,CAAgB4C,GACxBD,CAAAA,CAAkBA,EAAkBhC,OAAAA,CAAQoB,GAAAA,GAAW,IAAA,GAIxDnE,EAAAiC,GAAAA,CAAoB4F,GAAGI,GAAU/D;IACjC;AACD;AAOM,SAASM,EAAWP,CAAAA,EAAa+F,CAAAA;IACnChK,EAAiBA,GAAAA,IAAAA,EAAAmC,GAAAA,CAAgB6H,GAAM/F,IAE3CA,EAAYF,IAAAA,CAAK,SAAAV,CAAAA;QAChB,IAAA;YAECY,IAAcZ,EAAdjB,GAAAA,EACAiB,EAACjB,GAAAA,GAAoB,EAAA,EACrB6B,EAAYF,IAAAA,CAAK,SAAAkG,CAAAA;gBAEhBA,EAAGzI,IAAAA,CAAK6B;YACR;QAGD,EAFC,OAAOwE,GAAAA;YACR7H,EAAAiC,GAAAA,CAAoB4F,GAAGxE,EAAvBf,GAAAA;QACA;IACD;AACD;AAgBD,SAASwH,EACRtD,CAAAA,EACAyB,CAAAA,EACA/D,CAAAA,EACAW,CAAAA,EACAC,CAAAA,EACAC,CAAAA,EACAd,CAAAA,EACAe,CAAAA;IARD,IAoBS9B,GAsDHgH,GACAC,GAjEDzD,IAAWxC,EAASvD,KAAAA,EACpB8F,IAAWwB,EAAStH,KAAAA,EACpByJ,IAAWnC,EAAShH,IAAAA,EACpBZ,IAAI;IAKR,IAFiB,UAAb+J,KAAAA,CAAoBtF,IAAAA,CAAQ,CAAA,GAEP,QAArBC;QACH,MAAO1E,IAAI0E,EAAkBxD,MAAAA,EAAQlB,IAMpC,IAAA,CALM6C,IAAQ6B,CAAAA,CAAkB1E,EAAAA,KAO/B,kBAAkB6C,KAAAA,CAAAA,CAAYkH,KAAAA,CAC7BA,IAAWlH,EAAMmH,SAAAA,KAAcD,IAA8B,MAAnBlH,EAAMkH,QAAAA,GAChD;YACD5D,IAAMtD,GACN6B,CAAAA,CAAkB1E,EAAAA,GAAK;YACvB;;IACA;IAIH,IAAW,QAAPmG,GAAa;QAChB,IAAiB,SAAb4D,GAEH,OAAOE,SAASC,cAAAA,CAAe9D;QAI/BD,IADG1B,IACGwF,SAASE,eAAAA,CACd,8BAEAJ,KAGKE,SAAStJ,aAAAA,CAEdoJ,GACA3D,EAASgE,EAAAA,IAAMhE,IAKjB1B,IAAoB,MAEpBC,IAAAA,CAAc;IACd;IAED,IAAiB,SAAboF,GAEC1D,MAAaD,KAAczB,KAAewB,EAAIkE,IAAAA,KAASjE,KAAAA,CAC1DD,EAAIkE,IAAAA,GAAOjE,CAAAA;SAEN;QAWN,IATA1B,IAAoBA,KAAqBhF,EAAMyB,IAAAA,CAAKgF,EAAImE,UAAAA,GAIpDT,IAAAA,CAFJxD,IAAWxC,EAASvD,KAAAA,IAASL,CAAAA,EAENsK,uBAAAA,EACnBT,IAAU1D,EAASmE,uBAAAA,EAAAA,CAIlB5F,GAAa;YAGjB,IAAyB,QAArBD,GAEH,IADA2B,IAAW,CAAA,GACNrG,IAAI,GAAGA,IAAImG,EAAIqE,UAAAA,CAAWtJ,MAAAA,EAAQlB,IACtCqG,CAAAA,CAASF,EAAIqE,UAAAA,CAAWxK,EAAAA,CAAG4G,IAAAA,CAAAA,GAAQT,EAAIqE,UAAAA,CAAWxK,EAAAA,CAAG0G,KAAAA;YAAAA,CAInDoD,KAAWD,CAAAA,KAAAA,CAGZC,KAAAA,CACED,KAAWC,EAAOW,MAAAA,IAAWZ,EAAlBY,MAAAA,IACbX,EAAAW,MAAAA,KAAmBtE,EAAIuE,SAAAA,KAAAA,CAExBvE,EAAIuE,SAAAA,GAAaZ,KAAWA,EAAZW,MAAAA,IAA+B,EAAA,CAAA;QAGjD;QAKD,IAHAvE,EAAUC,GAAKC,GAAUC,GAAU5B,GAAOE,IAGtCmF,GACHlC,EAAQnG,GAAAA,GAAa,EAAA;aAmBrB,IAjBAzB,IAAI4H,EAAStH,KAAAA,CAAMO,QAAAA,EACnBuD,EACC+B,GACAhB,MAAMC,OAAAA,CAAQpF,KAAKA,IAAI;YAACA;SAAAA,EACxB4H,GACA/D,GACAW,GACAC,KAAsB,oBAAbsF,GACTrF,GACAd,GACAc,IACGA,CAAAA,CAAkB,EAAA,GAClBb,EAAApC,GAAAA,IAAsBe,EAAcqB,GAAU,IACjDc,IAIwB,QAArBD,GACH,IAAK1E,IAAI0E,EAAkBxD,MAAAA,EAAQlB,KACN,QAAxB0E,CAAAA,CAAkB1E,EAAAA,IAAYO,EAAWmE,CAAAA,CAAkB1E,EAAAA;QAM7D2E,KAAAA,CAEH,WAAWyB,KAAAA,KACc/E,MAAAA,CAAxBrB,IAAIoG,EAASM,KAAAA,KAAAA,CAKb1G,MAAMmG,EAAIO,KAAAA,IACI,eAAbqD,KAAAA,CAA4B/J,KAIf,aAAb+J,KAAyB/J,MAAMqG,EAASK,KAAAA,KAE1CH,EAAYJ,GAAK,SAASnG,GAAGqG,EAASK,KAAAA,EAAAA,CAAO,IAG7C,aAAaN,KAAAA,KACc/E,MAAAA,CAA1BrB,IAAIoG,EAASuE,OAAAA,KACd3K,MAAMmG,EAAIwE,OAAAA,IAEVpE,EAAYJ,GAAK,WAAWnG,GAAGqG,EAASsE,OAAAA,EAAAA,CAAS,EAAA;IAGnD;IAED,OAAOxE;AACP;AAQeX,SAAAA,EAASzE,CAAAA,EAAK2F,CAAAA,EAAOlF,CAAAA;IACpC,IAAA;QACmB,cAAA,OAAPT,IAAmBA,EAAI2F,KAC7B3F,EAAIoB,OAAAA,GAAUuE;IAGnB,EAFC,OAAOc,GAAAA;QACR7H,EAAAiC,GAAAA,CAAoB4F,GAAGhG;IACvB;AACD;AAUM,SAAS+D,EAAQ/D,CAAAA,EAAOoJ,CAAAA,EAAaC,CAAAA;IAArC,IACFC,GAuBM9K;IAdV,IARIL,EAAQ4F,OAAAA,IAAS5F,EAAQ4F,OAAAA,CAAQ/D,IAAAA,CAEhCsJ,IAAItJ,EAAMT,GAAAA,KAAAA,CACT+J,EAAE3I,OAAAA,IAAW2I,EAAE3I,OAAAA,KAAYX,EAAdI,GAAAA,IACjB4D,EAASsF,GAAG,MAAMF,EAAAA,GAIU,QAAA,CAAzBE,IAAItJ,EAAHM,GAAAA,GAA8B;QACnC,IAAIgJ,EAAEC,oBAAAA,EACL,IAAA;YACCD,EAAEC,oBAAAA;QAGF,EAFC,OAAOvD,GAAAA;YACR7H,EAAOiC,GAAAA,CAAa4F,GAAGoD;QACvB;QAGFE,EAAEhI,IAAAA,GAAOgI,EAAA9G,GAAAA,GAAe,MACxBxC,EAAKM,GAAAA,GAAAA,KAAcT;IACnB;IAED,IAAKyJ,IAAItJ,EAAHC,GAAAA,EACL,IAASzB,IAAI,GAAGA,IAAI8K,EAAE5J,MAAAA,EAAQlB,IACzB8K,CAAAA,CAAE9K,EAAAA,IACLuF,EACCuF,CAAAA,CAAE9K,EAAAA,EACF4K,GACAC,KAAoC,cAAA,OAAfrJ,EAAMZ,IAAAA;IAM1BiK,KAA4B,QAAdrJ,EAAKI,GAAAA,IACvBrB,EAAWiB,EAADI,GAAAA,GAKXJ,EAAAE,EAAAA,GAAgBF,EAAKI,GAAAA,GAAQJ,EAAAK,GAAAA,GAAAA,KAAiBR;AAC9C;AAGD,SAASqH,EAASpI,CAAAA,EAAOsI,CAAAA,EAAOtG,CAAAA;IAC/B,OAAYN,IAAAA,CAAAA,WAAAA,CAAY1B,GAAOgC;AAC/B;AC5hBM,SAASmG,EAAOjH,CAAAA,EAAOuC,CAAAA,EAAWiH,CAAAA;IAAlC,IAMFrG,GAOAd,GAUAD;IAtBAjE,EAAeA,EAAAA,IAAAA,EAAA+B,EAAAA,CAAcF,GAAOuC,IAYpCF,IAAAA,CAPAc,IAAqC,cAAA,OAAhBqG,CAAAA,IAQtB,OACCA,KAAeA,EAAAA,GAAAA,IAA0BjH,EAAAA,GAAAA,EAQzCH,IAAc,EAAA,EAClBK,EACCF,GARDvC,IAAAA,CAAAA,CACGmD,KAAeqG,KACjBjH,CAAAA,EAFOtC,GAAAA,GAGMd,EAAcyB,GAAU,MAAM;QAACZ;KAAAA,GAS5CqC,KAAY5D,GACZA,GAAAA,KAC8BoB,MAA9B0C,EAAUG,eAAAA,EAAAA,CACTS,KAAeqG,IACb;QAACA;KAAAA,GACDnH,IACA,OACAE,EAAUkH,UAAAA,GACVvL,EAAMyB,IAAAA,CAAK4C,EAAUuG,UAAAA,IACrB,MACH1G,GAAAA,CACCe,KAAeqG,IACbA,IACAnH,IACAA,EACAE,GAAAA,GAAAA,EAAUkH,UAAAA,EACbtG,IAIDR,EAAWP,GAAapC;AACxB;AAQe8E,SAAAA,EAAQ9E,CAAAA,EAAOuC,CAAAA;IAC9B0E,EAAOjH,GAAOuC,GAAWuC;AACzB;AAAA,SChEe4E,EAAa1J,CAAAA,EAAOlB,CAAAA,EAAOO,CAAAA;IAC1C,IACCC,GACAC,GACAf,GAHGgB,IAAkBZ,EAAO,CAAA,GAAIoB,EAAMlB,KAAAA;IAIvC,IAAKN,KAAKM,EACA,SAALN,IAAYc,IAAMR,CAAAA,CAAMN,EAAAA,GACd,SAALA,IAAYe,IAAMT,CAAAA,CAAMN,EAAAA,GAC5BgB,CAAAA,CAAgBhB,EAAAA,GAAKM,CAAAA,CAAMN,EAAAA;IAQjC,OALIiB,UAAUC,MAAAA,GAAS,KAAA,CACtBF,EAAgBH,QAAAA,GACfI,UAAUC,MAAAA,GAAS,IAAIxB,EAAMyB,IAAAA,CAAKF,WAAW,KAAKJ,CAAAA,GAG7CS,EACNE,EAAMZ,IAAAA,EACNI,GACAF,KAAOU,EAAMV,GAAAA,EACbC,KAAOS,EAAMT,GAAAA,EACb;AAED;AN7BM,SAASoK,EAAcC,CAAAA,EAAcC,CAAAA;IAG3C,IAAM/I,IAAU;QACfR,KAHDuJ,IAAY,SAASrL;QAIpB0B,IAAe0J;QAEfE,UAJe,SAINhL,CAAAA,EAAOiL,CAAAA;YAIf,OAAOjL,EAAMO,QAAAA,CAAS0K;QACtB;QAEDC,UAAAA,SAASlL,CAAAA;YAAAA,IAEHmL,GACAC;YAmCL,OArCKnJ,IAAAA,CAAKgH,eAAAA,IAAAA,CACLkC,IAAO,EAAA,EAAA,CACPC,IAAM,CAAV,CAAA,CAAA,CACIL,EAAAA,GAAa9I,IAAAA,EAEjBA,IAAAA,CAAKgH,eAAAA,GAAkB;gBAAA,OAAMmC;YAAN,GAEvBnJ,IAAAA,CAAK4G,qBAAAA,GAAwB,SAASwC,CAAAA;gBACjCpJ,IAAAA,CAAKjC,KAAAA,CAAMoG,KAAAA,KAAUiF,EAAOjF,KAAAA,IAe/B+E,EAAK/H,IAAAA,CAAKX;YAEX,GAEDR,IAAAA,CAAKoG,GAAAA,GAAM,SAAA3F,CAAAA;gBACVyI,EAAKxI,IAAAA,CAAKD;gBACV,IAAI4I,IAAM5I,EAAE+H,oBAAAA;gBACZ/H,EAAE+H,oBAAAA,GAAuB;oBACxBU,EAAKI,MAAAA,CAAOJ,EAAK/I,OAAAA,CAAQM,IAAI,IACzB4I,KAAKA,EAAIzK,IAAAA,CAAK6B;gBAClB;YACD,CAAA,GAGK1C,EAAMO;QACb;IAAA;IASF,OAAQyB,EAAQkJ,QAAAA,CAAuBlJ,EAAAA,GAAAA,EAAQgJ,QAAAA,CAAShD,WAAAA,GAAchG;AACtE;AJzCY5C,IAAQQ,EAAUR,KAAAA,ECfzBC,IAAU;IACfiC,KUHM,SAAqBkK,CAAAA,EAAOtK,CAAAA,EAAOqC,CAAAA,EAAUkI,CAAAA;QAInD,IAFA,IAAIpI,GAAWqI,GAAMC,GAEbzK,IAAQA,EAAhBE,EAAAA,EACC,IAAA,CAAKiC,IAAYnC,EAAHM,GAAAA,KAAAA,CAAyB6B,EAADjC,EAAAA,EACrC,IAAA;YAcC,IAAA,CAbAsK,IAAOrI,EAAU3B,WAAAA,KAE4B,QAAjCgK,EAAKE,wBAAAA,IAAAA,CAChBvI,EAAUwI,QAAAA,CAASH,EAAKE,wBAAAA,CAAyBJ,KACjDG,IAAUtI,EAAH9B,GAAAA,GAG2B,QAA/B8B,EAAUyI,iBAAAA,IAAAA,CACbzI,EAAUyI,iBAAAA,CAAkBN,GAAOC,KAAa,CAAhD,IACAE,IAAUtI,EACV9B,GAAAA,GAGGoK,GACH,OAAQtI,EAAS4E,GAAAA,GAAiB5E;QAInC,EAFC,OAAO6D,GAAAA;YACRsE,IAAQtE;QACR;QAIH,MAAMsE;IACN;AAAA,GTpCGlM,IAAU,GA6FDC,IAAiB,SAAA2B,CAAAA;IAAAA,OACpB,QAATA,KAAAA,KAAuCH,MAAtBG,EAAMQ;AADW,GCtEnCK,EAAUmG,SAAAA,CAAU2D,QAAAA,GAAW,SAASE,CAAAA,EAAQC,CAAAA;IAE/C,IAAIC;IAEHA,IADsB,QAAnBhK,IAAAA,CAAAuG,GAAAA,IAA2BvG,IAAAA,CAAAA,GAAAA,KAAoBA,IAAAA,CAAKqG,KAAAA,GACnDrG,IAAAA,CACJuG,GAAAA,GACIvG,IAAAA,CAAAuG,GAAAA,GAAkB1I,EAAO,CAAD,GAAKmC,IAAAA,CAAKqG,KAAAA,GAGlB,cAAA,OAAVyD,KAAAA,CAGVA,IAASA,EAAOjM,EAAO,CAAA,GAAImM,IAAIhK,IAAAA,CAAKjC,KAAAA,CAAAA,GAGjC+L,KACHjM,EAAOmM,GAAGF,IAIG,QAAVA,KAEA9J,IAAAA,CAAaN,GAAAA,IAAAA,CACZqK,KACH/J,IAAAA,CAAAsG,GAAAA,CAAqB5F,IAAAA,CAAKqJ,IAE3BvJ,EAAcR,IAAAA,CAAAA;AAEf,GAQDF,EAAUmG,SAAAA,CAAUgE,WAAAA,GAAc,SAASF,CAAAA;IACtC/J,IAAAA,CAAAA,GAAAA,IAAAA,CAIHA,IAAAA,CAAAX,GAAAA,GAAAA,CAAc,GACV0K,KAAU/J,IAAAA,CAAsBU,GAAAA,CAAAA,IAAAA,CAAKqJ,IACzCvJ,EAAcR,IAAAA,CAAAA;AAEf,GAYDF,EAAUmG,SAAAA,CAAUC,MAAAA,GAASrG,GAyFzBtC,IAAgB,EAAA,EA4CpBoD,EAAOI,GAAAA,GAAkB,GCtNdtD,IAAI", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], "debugId": null}}, {"offset": {"line": 1954, "column": 0}, "map": {"version": 3, "file": "jsxRuntime.module.js", "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/Bonkai/node_modules/preact/jsx-runtime/src/index.js"], "sourcesContent": ["import { options, Fragment } from 'preact';\n\n/** @typedef {import('preact').VNode} VNode */\n\nlet vnodeId = 0;\n\n/**\n * @fileoverview\n * This file exports various methods that implement Babel's \"automatic\" JSX runtime API:\n * - jsx(type, props, key)\n * - jsxs(type, props, key)\n * - jsxDEV(type, props, key, __source, __self)\n *\n * The implementation of createVNode here is optimized for performance.\n * Benchmarks: https://esbench.com/bench/5f6b54a0b4632100a7dcd2b3\n */\n\n/**\n * JSX.Element factory used by Babel's {runtime:\"automatic\"} JSX transform\n * @param {VNode['type']} type\n * @param {VNode['props']} props\n * @param {VNode['key']} [key]\n * @param {string} [__self]\n * @param {string} [__source]\n */\nfunction createVNode(type, props, key, __self, __source) {\n\t// We'll want to preserve `ref` in props to get rid of the need for\n\t// forwardRef components in the future, but that should happen via\n\t// a separate PR.\n\tlet normalizedProps = {},\n\t\tref,\n\t\ti;\n\tfor (i in props) {\n\t\tif (i == 'ref') {\n\t\t\tref = props[i];\n\t\t} else {\n\t\t\tnormalizedProps[i] = props[i];\n\t\t}\n\t}\n\n\tconst vnode = {\n\t\ttype,\n\t\tprops: normalizedProps,\n\t\tkey,\n\t\tref,\n\t\t_children: null,\n\t\t_parent: null,\n\t\t_depth: 0,\n\t\t_dom: null,\n\t\t_nextDom: undefined,\n\t\t_component: null,\n\t\t_hydrating: null,\n\t\tconstructor: undefined,\n\t\t_original: --vnodeId,\n\t\t__source,\n\t\t__self\n\t};\n\n\t// If a Component VNode, check for and apply defaultProps.\n\t// Note: `type` is often a String, and can be `undefined` in development.\n\tif (typeof type === 'function' && (ref = type.defaultProps)) {\n\t\tfor (i in ref)\n\t\t\tif (typeof normalizedProps[i] === 'undefined') {\n\t\t\t\tnormalizedProps[i] = ref[i];\n\t\t\t}\n\t}\n\n\tif (options.vnode) options.vnode(vnode);\n\treturn vnode;\n}\n\nexport {\n\tcreateVNode as jsx,\n\tcreateVNode as jsxs,\n\tcreateVNode as jsxDEV,\n\tFragment\n};\n"], "names": ["vnodeId", "createVNode", "type", "props", "key", "__self", "__source", "ref", "i", "normalizedProps", "vnode", "__k", "__", "__b", "__e", "__d", "undefined", "__c", "__h", "constructor", "__v", "defaultProps", "options"], "mappings": ";;;;;;;;AAIA,IAAIA,IAAU;AAqBd,SAASC,EAAYC,CAAAA,EAAMC,CAAAA,EAAOC,CAAAA,EAAKC,CAAAA,EAAQC,CAAAA;IAI9C,IACCC,GACAC,GAFGC,IAAkB,CAAtB;IAGA,IAAKD,KAAKL,EACA,SAALK,IACHD,IAAMJ,CAAAA,CAAMK,EAAAA,GAEZC,CAAAA,CAAgBD,EAAAA,GAAKL,CAAAA,CAAMK,EAAAA;IAI7B,IAAME,IAAQ;QACbR,MAAAA;QACAC,OAAOM;QACPL,KAAAA;QACAG,KAAAA;QACAI,KAAW;QACXC,IAAS;QACTC,KAAQ;QACRC,KAAM;QACNC,KAAAA,KAAUC;QACVC,KAAY;QACZC,KAAY;QACZC,aAAAA,KAAaH;QACbI,KAAAA,EAAapB;QACbM,UAAAA;QACAD,QAAAA;IAAAA;IAKD,IAAoB,cAAA,OAATH,KAAAA,CAAwBK,IAAML,EAAKmB,YAAAA,GAC7C,IAAKb,KAAKD,EAAAA,KACyB,MAAvBE,CAAAA,CAAgBD,EAAAA,IAAAA,CAC1BC,CAAAA,CAAgBD,EAAAA,GAAKD,CAAAA,CAAIC,EAAAA;IAK5B,mJADIc,UAAAA,CAAQZ,KAAAA,gJAAOY,UAAAA,CAAQZ,KAAAA,CAAMA,IAC1BA;AACP", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2003, "column": 0}, "map": {"version": 3, "file": "index.module.js", "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/Bonkai/node_modules/preact-render-to-string/src/util.js", "file:///Users/<USER>/Downloads/Coding/Personal/Bonkai/node_modules/preact-render-to-string/src/pretty.js", "file:///Users/<USER>/Downloads/Coding/Personal/Bonkai/node_modules/preact-render-to-string/src/index.js"], "sourcesContent": ["// DOM properties that should NOT have \"px\" added when numeric\nexport const IS_NON_DIMENSIONAL = /acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|^--/i;\nexport const VOID_ELEMENTS = /^(area|base|br|col|embed|hr|img|input|link|meta|param|source|track|wbr)$/;\nexport const UNSAFE_NAME = /[\\s\\n\\\\/='\"\\0<>]/;\nexport const XLINK = /^xlink:?./;\n\nconst ENCODED_ENTITIES = /[\"&<]/;\n\nexport function encodeEntities(str) {\n\t// Ensure we're always parsing and returning a string:\n\tstr += '';\n\n\t// Skip all work for strings with no entities needing encoding:\n\tif (ENCODED_ENTITIES.test(str) === false) return str;\n\n\tlet last = 0,\n\t\ti = 0,\n\t\tout = '',\n\t\tch = '';\n\n\t// Seek forward in str until the next entity char:\n\tfor (; i < str.length; i++) {\n\t\tswitch (str.charCodeAt(i)) {\n\t\t\tcase 34:\n\t\t\t\tch = '&quot;';\n\t\t\t\tbreak;\n\t\t\tcase 38:\n\t\t\t\tch = '&amp;';\n\t\t\t\tbreak;\n\t\t\tcase 60:\n\t\t\t\tch = '&lt;';\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tcontinue;\n\t\t}\n\t\t// Append skipped/buffered characters and the encoded entity:\n\t\tif (i !== last) out += str.slice(last, i);\n\t\tout += ch;\n\t\t// Start the next seek/buffer after the entity's offset:\n\t\tlast = i + 1;\n\t}\n\tif (i !== last) out += str.slice(last, i);\n\treturn out;\n}\n\nexport let indent = (s, char) =>\n\tString(s).replace(/(\\n+)/g, '$1' + (char || '\\t'));\n\nexport let isLargeString = (s, length, ignoreLines) =>\n\tString(s).length > (length || 40) ||\n\t(!ignoreLines && String(s).indexOf('\\n') !== -1) ||\n\tString(s).indexOf('<') !== -1;\n\nconst JS_TO_CSS = {};\n\nconst CSS_REGEX = /([A-Z])/g;\n// Convert an Object style to a CSSText string\nexport function styleObjToCss(s) {\n\tlet str = '';\n\tfor (let prop in s) {\n\t\tlet val = s[prop];\n\t\tif (val != null && val !== '') {\n\t\t\tif (str) str += ' ';\n\t\t\t// str += jsToCss(prop);\n\t\t\tstr +=\n\t\t\t\tprop[0] == '-'\n\t\t\t\t\t? prop\n\t\t\t\t\t: JS_TO_CSS[prop] ||\n\t\t\t\t\t  (JS_TO_CSS[prop] = prop.replace(CSS_REGEX, '-$1').toLowerCase());\n\n\t\t\tif (typeof val === 'number' && IS_NON_DIMENSIONAL.test(prop) === false) {\n\t\t\t\tstr = str + ': ' + val + 'px;';\n\t\t\t} else {\n\t\t\t\tstr = str + ': ' + val + ';';\n\t\t\t}\n\t\t}\n\t}\n\treturn str || undefined;\n}\n\n/**\n * Get flattened children from the children prop\n * @param {Array} accumulator\n * @param {any} children A `props.children` opaque object.\n * @returns {Array} accumulator\n * @private\n */\nexport function getChildren(accumulator, children) {\n\tif (Array.isArray(children)) {\n\t\tchildren.reduce(getChildren, accumulator);\n\t} else if (children != null && children !== false) {\n\t\taccumulator.push(children);\n\t}\n\treturn accumulator;\n}\n\nfunction markAsDirty() {\n\tthis.__d = true;\n}\n\nexport function createComponent(vnode, context) {\n\treturn {\n\t\t__v: vnode,\n\t\tcontext,\n\t\tprops: vnode.props,\n\t\t// silently drop state updates\n\t\tsetState: markAsDirty,\n\t\tforceUpdate: markAsDirty,\n\t\t__d: true,\n\t\t// hooks\n\t\t__h: []\n\t};\n}\n\n// Necessary for createContext api. Setting this property will pass\n// the context value as `this.context` just for this component.\nexport function getContext(nodeName, context) {\n\tlet cxType = nodeName.contextType;\n\tlet provider = cxType && context[cxType.__c];\n\treturn cxType != null\n\t\t? provider\n\t\t\t? provider.props.value\n\t\t\t: cxType.__\n\t\t: context;\n}\n", "import {\n\tencodeEntities,\n\tindent,\n\tisLargeString,\n\tstyleObjToCss,\n\tgetChildren,\n\tcreateComponent,\n\tgetContext,\n\tUNSAFE_NAME,\n\tXLINK,\n\tVOID_ELEMENTS\n} from './util';\nimport { options, Fragment } from 'preact';\n\n// components without names, kept as a hash for later comparison to return consistent UnnamedComponentXX names.\nconst UNNAMED = [];\n\nexport function _renderToStringPretty(\n\tvnode,\n\tcontext,\n\topts,\n\tinner,\n\tisSvgMode,\n\tselectValue\n) {\n\tif (vnode == null || typeof vnode === 'boolean') {\n\t\treturn '';\n\t}\n\n\t// #text nodes\n\tif (typeof vnode !== 'object') {\n\t\treturn encodeEntities(vnode);\n\t}\n\n\tlet pretty = opts.pretty,\n\t\tindentChar = pretty && typeof pretty === 'string' ? pretty : '\\t';\n\n\tif (Array.isArray(vnode)) {\n\t\tlet rendered = '';\n\t\tfor (let i = 0; i < vnode.length; i++) {\n\t\t\tif (pretty && i > 0) rendered = rendered + '\\n';\n\t\t\trendered =\n\t\t\t\trendered +\n\t\t\t\t_renderToStringPretty(\n\t\t\t\t\tvnode[i],\n\t\t\t\t\tcontext,\n\t\t\t\t\topts,\n\t\t\t\t\tinner,\n\t\t\t\t\tisSvgMode,\n\t\t\t\t\tselectValue\n\t\t\t\t);\n\t\t}\n\t\treturn rendered;\n\t}\n\n\tlet nodeName = vnode.type,\n\t\tprops = vnode.props,\n\t\tisComponent = false;\n\n\t// components\n\tif (typeof nodeName === 'function') {\n\t\tisComponent = true;\n\t\tif (opts.shallow && (inner || opts.renderRootComponent === false)) {\n\t\t\tnodeName = getComponentName(nodeName);\n\t\t} else if (nodeName === Fragment) {\n\t\t\tconst children = [];\n\t\t\tgetChildren(children, vnode.props.children);\n\t\t\treturn _renderToStringPretty(\n\t\t\t\tchildren,\n\t\t\t\tcontext,\n\t\t\t\topts,\n\t\t\t\topts.shallowHighOrder !== false,\n\t\t\t\tisSvgMode,\n\t\t\t\tselectValue\n\t\t\t);\n\t\t} else {\n\t\t\tlet rendered;\n\n\t\t\tlet c = (vnode.__c = createComponent(vnode, context));\n\n\t\t\t// options._diff\n\t\t\tif (options.__b) options.__b(vnode);\n\n\t\t\t// options._render\n\t\t\tlet renderHook = options.__r;\n\n\t\t\tif (\n\t\t\t\t!nodeName.prototype ||\n\t\t\t\ttypeof nodeName.prototype.render !== 'function'\n\t\t\t) {\n\t\t\t\tlet cctx = getContext(nodeName, context);\n\n\t\t\t\t// If a hook invokes setState() to invalidate the component during rendering,\n\t\t\t\t// re-render it up to 25 times to allow \"settling\" of memoized states.\n\t\t\t\t// Note:\n\t\t\t\t//   This will need to be updated for Preact 11 to use internal.flags rather than component._dirty:\n\t\t\t\t//   https://github.com/preactjs/preact/blob/d4ca6fdb19bc715e49fd144e69f7296b2f4daa40/src/diff/component.js#L35-L44\n\t\t\t\tlet count = 0;\n\t\t\t\twhile (c.__d && count++ < 25) {\n\t\t\t\t\tc.__d = false;\n\n\t\t\t\t\tif (renderHook) renderHook(vnode);\n\n\t\t\t\t\t// stateless functional components\n\t\t\t\t\trendered = nodeName.call(vnode.__c, props, cctx);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tlet cctx = getContext(nodeName, context);\n\n\t\t\t\t// c = new nodeName(props, context);\n\t\t\t\tc = vnode.__c = new nodeName(props, cctx);\n\t\t\t\tc.__v = vnode;\n\t\t\t\t// turn off stateful re-rendering:\n\t\t\t\tc._dirty = c.__d = true;\n\t\t\t\tc.props = props;\n\t\t\t\tif (c.state == null) c.state = {};\n\n\t\t\t\tif (c._nextState == null && c.__s == null) {\n\t\t\t\t\tc._nextState = c.__s = c.state;\n\t\t\t\t}\n\n\t\t\t\tc.context = cctx;\n\t\t\t\tif (nodeName.getDerivedStateFromProps)\n\t\t\t\t\tc.state = Object.assign(\n\t\t\t\t\t\t{},\n\t\t\t\t\t\tc.state,\n\t\t\t\t\t\tnodeName.getDerivedStateFromProps(c.props, c.state)\n\t\t\t\t\t);\n\t\t\t\telse if (c.componentWillMount) {\n\t\t\t\t\tc.componentWillMount();\n\n\t\t\t\t\t// If the user called setState in cWM we need to flush pending,\n\t\t\t\t\t// state updates. This is the same behaviour in React.\n\t\t\t\t\tc.state =\n\t\t\t\t\t\tc._nextState !== c.state\n\t\t\t\t\t\t\t? c._nextState\n\t\t\t\t\t\t\t: c.__s !== c.state\n\t\t\t\t\t\t\t? c.__s\n\t\t\t\t\t\t\t: c.state;\n\t\t\t\t}\n\n\t\t\t\tif (renderHook) renderHook(vnode);\n\n\t\t\t\trendered = c.render(c.props, c.state, c.context);\n\t\t\t}\n\n\t\t\tif (c.getChildContext) {\n\t\t\t\tcontext = Object.assign({}, context, c.getChildContext());\n\t\t\t}\n\n\t\t\tif (options.diffed) options.diffed(vnode);\n\t\t\treturn _renderToStringPretty(\n\t\t\t\trendered,\n\t\t\t\tcontext,\n\t\t\t\topts,\n\t\t\t\topts.shallowHighOrder !== false,\n\t\t\t\tisSvgMode,\n\t\t\t\tselectValue\n\t\t\t);\n\t\t}\n\t}\n\n\t// render JSX to HTML\n\tlet s = '<' + nodeName,\n\t\tpropChildren,\n\t\thtml;\n\n\tif (props) {\n\t\tlet attrs = Object.keys(props);\n\n\t\t// allow sorting lexicographically for more determinism (useful for tests, such as via preact-jsx-chai)\n\t\tif (opts && opts.sortAttributes === true) attrs.sort();\n\n\t\tfor (let i = 0; i < attrs.length; i++) {\n\t\t\tlet name = attrs[i],\n\t\t\t\tv = props[name];\n\t\t\tif (name === 'children') {\n\t\t\t\tpropChildren = v;\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tif (UNSAFE_NAME.test(name)) continue;\n\n\t\t\tif (\n\t\t\t\t!(opts && opts.allAttributes) &&\n\t\t\t\t(name === 'key' ||\n\t\t\t\t\tname === 'ref' ||\n\t\t\t\t\tname === '__self' ||\n\t\t\t\t\tname === '__source')\n\t\t\t)\n\t\t\t\tcontinue;\n\n\t\t\tif (name === 'defaultValue') {\n\t\t\t\tname = 'value';\n\t\t\t} else if (name === 'defaultChecked') {\n\t\t\t\tname = 'checked';\n\t\t\t} else if (name === 'defaultSelected') {\n\t\t\t\tname = 'selected';\n\t\t\t} else if (name === 'className') {\n\t\t\t\tif (typeof props.class !== 'undefined') continue;\n\t\t\t\tname = 'class';\n\t\t\t} else if (isSvgMode && XLINK.test(name)) {\n\t\t\t\tname = name.toLowerCase().replace(/^xlink:?/, 'xlink:');\n\t\t\t}\n\n\t\t\tif (name === 'htmlFor') {\n\t\t\t\tif (props.for) continue;\n\t\t\t\tname = 'for';\n\t\t\t}\n\n\t\t\tif (name === 'style' && v && typeof v === 'object') {\n\t\t\t\tv = styleObjToCss(v);\n\t\t\t}\n\n\t\t\t// always use string values instead of booleans for aria attributes\n\t\t\t// also see https://github.com/preactjs/preact/pull/2347/files\n\t\t\tif (name[0] === 'a' && name['1'] === 'r' && typeof v === 'boolean') {\n\t\t\t\tv = String(v);\n\t\t\t}\n\n\t\t\tlet hooked =\n\t\t\t\topts.attributeHook &&\n\t\t\t\topts.attributeHook(name, v, context, opts, isComponent);\n\t\t\tif (hooked || hooked === '') {\n\t\t\t\ts = s + hooked;\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tif (name === 'dangerouslySetInnerHTML') {\n\t\t\t\thtml = v && v.__html;\n\t\t\t} else if (nodeName === 'textarea' && name === 'value') {\n\t\t\t\t// <textarea value=\"a&b\"> --> <textarea>a&amp;b</textarea>\n\t\t\t\tpropChildren = v;\n\t\t\t} else if ((v || v === 0 || v === '') && typeof v !== 'function') {\n\t\t\t\tif (v === true || v === '') {\n\t\t\t\t\tv = name;\n\t\t\t\t\t// in non-xml mode, allow boolean attributes\n\t\t\t\t\tif (!opts || !opts.xml) {\n\t\t\t\t\t\ts = s + ' ' + name;\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (name === 'value') {\n\t\t\t\t\tif (nodeName === 'select') {\n\t\t\t\t\t\tselectValue = v;\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t} else if (\n\t\t\t\t\t\t// If we're looking at an <option> and it's the currently selected one\n\t\t\t\t\t\tnodeName === 'option' &&\n\t\t\t\t\t\tselectValue == v &&\n\t\t\t\t\t\t// and the <option> doesn't already have a selected attribute on it\n\t\t\t\t\t\ttypeof props.selected === 'undefined'\n\t\t\t\t\t) {\n\t\t\t\t\t\ts = s + ` selected`;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\ts = s + ` ${name}=\"${encodeEntities(v)}\"`;\n\t\t\t}\n\t\t}\n\t}\n\n\t// account for >1 multiline attribute\n\tif (pretty) {\n\t\tlet sub = s.replace(/\\n\\s*/, ' ');\n\t\tif (sub !== s && !~sub.indexOf('\\n')) s = sub;\n\t\telse if (pretty && ~s.indexOf('\\n')) s = s + '\\n';\n\t}\n\n\ts = s + '>';\n\n\tif (UNSAFE_NAME.test(nodeName))\n\t\tthrow new Error(`${nodeName} is not a valid HTML tag name in ${s}`);\n\n\tlet isVoid =\n\t\tVOID_ELEMENTS.test(nodeName) ||\n\t\t(opts.voidElements && opts.voidElements.test(nodeName));\n\tlet pieces = [];\n\n\tlet children;\n\tif (html) {\n\t\t// if multiline, indent.\n\t\tif (pretty && isLargeString(html)) {\n\t\t\thtml = '\\n' + indentChar + indent(html, indentChar);\n\t\t}\n\t\ts = s + html;\n\t} else if (\n\t\tpropChildren != null &&\n\t\tgetChildren((children = []), propChildren).length\n\t) {\n\t\tlet hasLarge = pretty && ~s.indexOf('\\n');\n\t\tlet lastWasText = false;\n\n\t\tfor (let i = 0; i < children.length; i++) {\n\t\t\tlet child = children[i];\n\n\t\t\tif (child != null && child !== false) {\n\t\t\t\tlet childSvgMode =\n\t\t\t\t\t\tnodeName === 'svg'\n\t\t\t\t\t\t\t? true\n\t\t\t\t\t\t\t: nodeName === 'foreignObject'\n\t\t\t\t\t\t\t? false\n\t\t\t\t\t\t\t: isSvgMode,\n\t\t\t\t\tret = _renderToStringPretty(\n\t\t\t\t\t\tchild,\n\t\t\t\t\t\tcontext,\n\t\t\t\t\t\topts,\n\t\t\t\t\t\ttrue,\n\t\t\t\t\t\tchildSvgMode,\n\t\t\t\t\t\tselectValue\n\t\t\t\t\t);\n\n\t\t\t\tif (pretty && !hasLarge && isLargeString(ret)) hasLarge = true;\n\n\t\t\t\t// Skip if we received an empty string\n\t\t\t\tif (ret) {\n\t\t\t\t\tif (pretty) {\n\t\t\t\t\t\tlet isText = ret.length > 0 && ret[0] != '<';\n\n\t\t\t\t\t\t// We merge adjacent text nodes, otherwise each piece would be printed\n\t\t\t\t\t\t// on a new line.\n\t\t\t\t\t\tif (lastWasText && isText) {\n\t\t\t\t\t\t\tpieces[pieces.length - 1] += ret;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tpieces.push(ret);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tlastWasText = isText;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tpieces.push(ret);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tif (pretty && hasLarge) {\n\t\t\tfor (let i = pieces.length; i--; ) {\n\t\t\t\tpieces[i] = '\\n' + indentChar + indent(pieces[i], indentChar);\n\t\t\t}\n\t\t}\n\t}\n\n\tif (pieces.length || html) {\n\t\ts = s + pieces.join('');\n\t} else if (opts && opts.xml) {\n\t\treturn s.substring(0, s.length - 1) + ' />';\n\t}\n\n\tif (isVoid && !children && !html) {\n\t\ts = s.replace(/>$/, ' />');\n\t} else {\n\t\tif (pretty && ~s.indexOf('\\n')) s = s + '\\n';\n\t\ts = s + `</${nodeName}>`;\n\t}\n\n\treturn s;\n}\n\nfunction getComponentName(component) {\n\treturn (\n\t\tcomponent.displayName ||\n\t\t(component !== Function && component.name) ||\n\t\tgetFallbackComponentName(component)\n\t);\n}\n\nfunction getFallbackComponentName(component) {\n\tlet str = Function.prototype.toString.call(component),\n\t\tname = (str.match(/^\\s*function\\s+([^( ]+)/) || '')[1];\n\tif (!name) {\n\t\t// search for an existing indexed name for the given component:\n\t\tlet index = -1;\n\t\tfor (let i = UNNAMED.length; i--; ) {\n\t\t\tif (UNNAMED[i] === component) {\n\t\t\t\tindex = i;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t\t// not found, create a new indexed name:\n\t\tif (index < 0) {\n\t\t\tindex = UNNAMED.push(component) - 1;\n\t\t}\n\t\tname = `UnnamedComponent${index}`;\n\t}\n\treturn name;\n}\n", "import {\n\tencodeEntities,\n\tstyleObjTo<PERSON><PERSON>,\n\tget<PERSON>ontext,\n\tcreate<PERSON>omponent,\n\tUNSAFE_NAME,\n\tXLINK,\n\tVOID_ELEMENTS\n} from './util';\nimport { options, Fragment } from 'preact';\nimport { _renderToStringPretty } from './pretty';\nimport {\n\tCOMMIT,\n\tCOMPONENT,\n\tDIFF,\n\tDIFFED,\n\tDIRTY,\n\tNEXT_STATE,\n\tRENDER,\n\tSKIP_EFFECTS,\n\tVNODE\n} from './constants';\n\n/** @typedef {import('preact').VNode} VNode */\n\nconst SHALLOW = { shallow: true };\n\n/** Render Preact JSX + Components to an HTML string.\n *\t@name render\n *\t@function\n *\t@param {VNode} vnode\tJSX VNode to render.\n *\t@param {Object} [context={}]\tOptionally pass an initial context object through the render path.\n *\t@param {Object} [options={}]\tRendering options\n *\t@param {Boolean} [options.shallow=false]\tIf `true`, renders nested Components as HTML elements (`<Foo a=\"b\" />`).\n *\t@param {Boolean} [options.xml=false]\t\tIf `true`, uses self-closing tags for elements without children.\n *\t@param {Boolean} [options.pretty=false]\t\tIf `true`, adds whitespace for readability\n *\t@param {RegExp|undefined} [options.voidElements]       RegeEx that matches elements that are considered void (self-closing)\n */\nrenderToString.render = renderToString;\n\n/** Only render elements, leaving Components inline as `<ComponentName ... />`.\n *\tThis method is just a convenience alias for `render(vnode, context, { shallow:true })`\n *\t@name shallow\n *\t@function\n *\t@param {VNode} vnode\tJSX VNode to render.\n *\t@param {Object} [context={}]\tOptionally pass an initial context object through the render path.\n */\nlet shallowRender = (vnode, context) => renderToString(vnode, context, SHALLOW);\n\nconst EMPTY_ARR = [];\nfunction renderToString(vnode, context, opts) {\n\tcontext = context || {};\n\n\t// Performance optimization: `renderToString` is synchronous and we\n\t// therefore don't execute any effects. To do that we pass an empty\n\t// array to `options._commit` (`__c`). But we can go one step further\n\t// and avoid a lot of dirty checks and allocations by setting\n\t// `options._skipEffects` (`__s`) too.\n\tconst previousSkipEffects = options[SKIP_EFFECTS];\n\toptions[SKIP_EFFECTS] = true;\n\n\tlet res;\n\tif (\n\t\topts &&\n\t\t(opts.pretty ||\n\t\t\topts.voidElements ||\n\t\t\topts.sortAttributes ||\n\t\t\topts.shallow ||\n\t\t\topts.allAttributes ||\n\t\t\topts.xml ||\n\t\t\topts.attributeHook)\n\t) {\n\t\tres = _renderToStringPretty(vnode, context, opts);\n\t} else {\n\t\tres = _renderToString(vnode, context, false, undefined);\n\t}\n\n\t// options._commit, we don't schedule any effects in this library right now,\n\t// so we can pass an empty queue to this hook.\n\tif (options[COMMIT]) options[COMMIT](vnode, EMPTY_ARR);\n\toptions[SKIP_EFFECTS] = previousSkipEffects;\n\tEMPTY_ARR.length = 0;\n\treturn res;\n}\n\nfunction renderFunctionComponent(vnode, context) {\n\tlet rendered,\n\t\tc = createComponent(vnode, context),\n\t\tcctx = getContext(vnode.type, context);\n\n\tvnode[COMPONENT] = c;\n\n\t// If a hook invokes setState() to invalidate the component during rendering,\n\t// re-render it up to 25 times to allow \"settling\" of memoized states.\n\t// Note:\n\t//   This will need to be updated for Preact 11 to use internal.flags rather than component._dirty:\n\t//   https://github.com/preactjs/preact/blob/d4ca6fdb19bc715e49fd144e69f7296b2f4daa40/src/diff/component.js#L35-L44\n\tlet renderHook = options[RENDER];\n\tlet count = 0;\n\twhile (c[DIRTY] && count++ < 25) {\n\t\tc[DIRTY] = false;\n\n\t\tif (renderHook) renderHook(vnode);\n\n\t\t// stateless functional components\n\t\trendered = vnode.type.call(c, vnode.props, cctx);\n\t}\n\n\treturn rendered;\n}\n\nfunction renderClassComponent(vnode, context) {\n\tlet nodeName = vnode.type,\n\t\tcctx = getContext(nodeName, context);\n\n\t// c = new nodeName(props, context);\n\tlet c = new nodeName(vnode.props, cctx);\n\tvnode[COMPONENT] = c;\n\tc[VNODE] = vnode;\n\t// turn off stateful re-rendering:\n\tc[DIRTY] = true;\n\tc.props = vnode.props;\n\tif (c.state == null) c.state = {};\n\n\tif (c[NEXT_STATE] == null) {\n\t\tc[NEXT_STATE] = c.state;\n\t}\n\n\tc.context = cctx;\n\tif (nodeName.getDerivedStateFromProps) {\n\t\tc.state = assign(\n\t\t\t{},\n\t\t\tc.state,\n\t\t\tnodeName.getDerivedStateFromProps(c.props, c.state)\n\t\t);\n\t} else if (c.componentWillMount) {\n\t\tc.componentWillMount();\n\n\t\t// If the user called setState in cWM we need to flush pending,\n\t\t// state updates. This is the same behaviour in React.\n\t\tc.state = c[NEXT_STATE] !== c.state ? c[NEXT_STATE] : c.state;\n\t}\n\n\tlet renderHook = options[RENDER];\n\tif (renderHook) renderHook(vnode);\n\n\treturn c.render(c.props, c.state, c.context);\n}\n\nfunction normalizePropName(name, isSvgMode) {\n\tif (name === 'className') {\n\t\treturn 'class';\n\t} else if (name === 'htmlFor') {\n\t\treturn 'for';\n\t} else if (name === 'defaultValue') {\n\t\treturn 'value';\n\t} else if (name === 'defaultChecked') {\n\t\treturn 'checked';\n\t} else if (name === 'defaultSelected') {\n\t\treturn 'selected';\n\t} else if (isSvgMode && XLINK.test(name)) {\n\t\treturn name.toLowerCase().replace(/^xlink:?/, 'xlink:');\n\t}\n\n\treturn name;\n}\n\nfunction normalizePropValue(name, v) {\n\tif (name === 'style' && v != null && typeof v === 'object') {\n\t\treturn styleObjToCss(v);\n\t} else if (name[0] === 'a' && name[1] === 'r' && typeof v === 'boolean') {\n\t\t// always use string values instead of booleans for aria attributes\n\t\t// also see https://github.com/preactjs/preact/pull/2347/files\n\t\treturn String(v);\n\t}\n\n\treturn v;\n}\n\nconst isArray = Array.isArray;\nconst assign = Object.assign;\n\n/** The default export is an alias of `render()`. */\nfunction _renderToString(vnode, context, isSvgMode, selectValue) {\n\t// Ignore non-rendered VNodes/values\n\tif (vnode == null || vnode === true || vnode === false || vnode === '') {\n\t\treturn '';\n\t}\n\n\t// Text VNodes: escape as HTML\n\tif (typeof vnode !== 'object') {\n\t\treturn encodeEntities(vnode);\n\t}\n\n\t// Recurse into children / Arrays\n\tif (isArray(vnode)) {\n\t\tlet rendered = '';\n\t\tfor (let i = 0; i < vnode.length; i++) {\n\t\t\trendered =\n\t\t\t\trendered + _renderToString(vnode[i], context, isSvgMode, selectValue);\n\t\t}\n\t\treturn rendered;\n\t}\n\n\tif (options[DIFF]) options[DIFF](vnode);\n\n\tlet type = vnode.type,\n\t\tprops = vnode.props;\n\n\t// Invoke rendering on Components\n\tconst isComponent = typeof type === 'function';\n\tif (isComponent) {\n\t\tif (type === Fragment) {\n\t\t\treturn _renderToString(\n\t\t\t\tvnode.props.children,\n\t\t\t\tcontext,\n\t\t\t\tisSvgMode,\n\t\t\t\tselectValue\n\t\t\t);\n\t\t}\n\n\t\tlet rendered;\n\t\tif (type.prototype && typeof type.prototype.render === 'function') {\n\t\t\trendered = renderClassComponent(vnode, context);\n\t\t} else {\n\t\t\trendered = renderFunctionComponent(vnode, context);\n\t\t}\n\n\t\tlet component = vnode[COMPONENT];\n\t\tif (component.getChildContext) {\n\t\t\tcontext = assign({}, context, component.getChildContext());\n\t\t}\n\n\t\t// Recurse into children before invoking the after-diff hook\n\t\tconst str = _renderToString(rendered, context, isSvgMode, selectValue);\n\t\tif (options[DIFFED]) options[DIFFED](vnode);\n\t\treturn str;\n\t}\n\n\t// Serialize Element VNodes to HTML\n\tlet s = '<',\n\t\tchildren,\n\t\thtml;\n\n\ts = s + type;\n\n\tif (props) {\n\t\tchildren = props.children;\n\t\tfor (let name in props) {\n\t\t\tlet v = props[name];\n\n\t\t\tif (\n\t\t\t\tname === 'key' ||\n\t\t\t\tname === 'ref' ||\n\t\t\t\tname === '__self' ||\n\t\t\t\tname === '__source' ||\n\t\t\t\tname === 'children' ||\n\t\t\t\t(name === 'className' && 'class' in props) ||\n\t\t\t\t(name === 'htmlFor' && 'for' in props)\n\t\t\t) {\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tif (UNSAFE_NAME.test(name)) continue;\n\n\t\t\tname = normalizePropName(name, isSvgMode);\n\t\t\tv = normalizePropValue(name, v);\n\n\t\t\tif (name === 'dangerouslySetInnerHTML') {\n\t\t\t\thtml = v && v.__html;\n\t\t\t} else if (type === 'textarea' && name === 'value') {\n\t\t\t\t// <textarea value=\"a&b\"> --> <textarea>a&amp;b</textarea>\n\t\t\t\tchildren = v;\n\t\t\t} else if ((v || v === 0 || v === '') && typeof v !== 'function') {\n\t\t\t\tif (v === true || v === '') {\n\t\t\t\t\tv = name;\n\t\t\t\t\ts = s + ' ' + name;\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tif (name === 'value') {\n\t\t\t\t\tif (type === 'select') {\n\t\t\t\t\t\tselectValue = v;\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t} else if (\n\t\t\t\t\t\t// If we're looking at an <option> and it's the currently selected one\n\t\t\t\t\t\ttype === 'option' &&\n\t\t\t\t\t\tselectValue == v &&\n\t\t\t\t\t\t// and the <option> doesn't already have a selected attribute on it\n\t\t\t\t\t\t!('selected' in props)\n\t\t\t\t\t) {\n\t\t\t\t\t\ts = s + ' selected';\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\ts = s + ' ' + name + '=\"' + encodeEntities(v) + '\"';\n\t\t\t}\n\t\t}\n\t}\n\n\tlet startElement = s;\n\ts = s + '>';\n\n\tif (UNSAFE_NAME.test(type)) {\n\t\tthrow new Error(`${type} is not a valid HTML tag name in ${s}`);\n\t}\n\n\tlet pieces = '';\n\tlet hasChildren = false;\n\n\tif (html) {\n\t\tpieces = pieces + html;\n\t\thasChildren = true;\n\t} else if (typeof children === 'string') {\n\t\tpieces = pieces + encodeEntities(children);\n\t\thasChildren = true;\n\t} else if (isArray(children)) {\n\t\tfor (let i = 0; i < children.length; i++) {\n\t\t\tlet child = children[i];\n\n\t\t\tif (child != null && child !== false) {\n\t\t\t\tlet childSvgMode =\n\t\t\t\t\ttype === 'svg' || (type !== 'foreignObject' && isSvgMode);\n\t\t\t\tlet ret = _renderToString(child, context, childSvgMode, selectValue);\n\n\t\t\t\t// Skip if we received an empty string\n\t\t\t\tif (ret) {\n\t\t\t\t\tpieces = pieces + ret;\n\t\t\t\t\thasChildren = true;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t} else if (children != null && children !== false && children !== true) {\n\t\tlet childSvgMode =\n\t\t\ttype === 'svg' || (type !== 'foreignObject' && isSvgMode);\n\t\tlet ret = _renderToString(children, context, childSvgMode, selectValue);\n\n\t\t// Skip if we received an empty string\n\t\tif (ret) {\n\t\t\tpieces = pieces + ret;\n\t\t\thasChildren = true;\n\t\t}\n\t}\n\n\tif (options[DIFFED]) options[DIFFED](vnode);\n\n\tif (hasChildren) {\n\t\ts = s + pieces;\n\t} else if (VOID_ELEMENTS.test(type)) {\n\t\treturn startElement + ' />';\n\t}\n\n\treturn s + '</' + type + '>';\n}\n\n/** The default export is an alias of `render()`. */\n\nrenderToString.shallowRender = shallowRender;\n\nexport default renderToString;\n\nexport {\n\trenderToString as render,\n\trenderToString as renderToStaticMarkup,\n\trenderToString,\n\tshallowRender\n};\n"], "names": ["IS_NON_DIMENSIONAL", "VOID_ELEMENTS", "UNSAFE_NAME", "XLINK", "ENCODED_ENTITIES", "encodeEntities", "str", "test", "last", "i", "out", "ch", "length", "charCodeAt", "slice", "indent", "s", "char", "String", "replace", "isLargeString", "ignoreLines", "indexOf", "JS_TO_CSS", "CSS_REGEX", "styleObjToCss", "prop", "val", "toLowerCase", "undefined", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "accumulator", "children", "Array", "isArray", "reduce", "push", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "this", "__d", "createComponent", "vnode", "context", "__v", "props", "setState", "forceUpdate", "__h", "getContext", "nodeName", "cxType", "contextType", "provider", "__c", "value", "__", "UNNAMED", "_renderToStringPretty", "opts", "inner", "isSvgMode", "selectValue", "pretty", "indentChar", "rendered", "component", "type", "isComponent", "shallow", "renderRootComponent", "Fragment", "shallowHighOrder", "c", "options", "__b", "renderHook", "__r", "prototype", "render", "cctx", "_dirty", "state", "_nextState", "__s", "getDerivedStateFromProps", "Object", "assign", "componentWillMount", "count", "call", "getChildContext", "diffed", "displayName", "Function", "name", "toString", "match", "index", "getFallbackComponentName", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "html", "attrs", "keys", "sortAttributes", "sort", "v", "allAttributes", "hooked", "attributeHook", "__html", "xml", "selected", "sub", "Error", "isVoid", "voidElements", "pieces", "<PERSON><PERSON><PERSON><PERSON>", "lastWasText", "child", "ret", "isText", "join", "substring", "SHALLOW", "renderToString", "shallowRender", "EMPTY_ARR", "res", "previousSkipEffects", "_renderToString", "normalizePropName", "normalizePropValue", "renderClassComponent", "renderFunctionComponent", "startElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;IACaA,IAAqB,mEACrBC,IAAgB,4EAChBC,IAAc,oBACdC,IAAQ,aAEfC,IAAmB;AAAA,SAETC,EAAeC,CAAAA;IAK9B,IAAA,CAAmC,MAA/BF,EAAiBG,IAAAA,CAHrBD,KAAO,KAGmC,OAAOA;IAQjD,IANA,IAAIE,IAAO,GACVC,IAAI,GACJC,IAAM,IACNC,IAAK,IAGCF,IAAIH,EAAIM,MAAAA,EAAQH,IAAK;QAC3B,OAAQH,EAAIO,UAAAA,CAAWJ;YACtB,KAAA;gBACCE,IAAK;gBACL;YACD,KAAA;gBACCA,IAAK;gBACL;YACD,KAAA;gBACCA,IAAK;gBACL;YACD;gBACC;QAAA;QAGEF,MAAMD,KAAAA,CAAME,KAAOJ,EAAIQ,KAAAA,CAAMN,GAAMC,EAAAA,GACvCC,KAAOC,GAEPH,IAAOC,IAAI;IAAA;IAGZ,OADIA,MAAMD,KAAAA,CAAME,KAAOJ,EAAIQ,KAAAA,CAAMN,GAAMC,EAAAA,GAChCC;AAAAA;AAAAA,IAGGK,IAAS,SAACC,CAAAA,EAAGC,CAAAA;IAAAA,OACvBC,OAAOF,GAAGG,OAAAA,CAAQ,UAAU,OAAA,CAAQF,KAAQ,IAAA;AAAA,GAElCG,IAAgB,SAACJ,CAAAA,EAAGJ,CAAAA,EAAQS,CAAAA;IAAAA,OACtCH,OAAOF,GAAGJ,MAAAA,GAAAA,CAAUA,KAAU,EAAA,KAAA,CAC5BS,KAAAA,CAA4C,MAA7BH,OAAOF,GAAGM,OAAAA,CAAQ,SAAA,CACP,MAA5BJ,OAAOF,GAAGM,OAAAA,CAAQ;AAAA,GAEbC,IAAY,CAAA,GAEZC,IAAY;AAAA,SAEFC,EAAcT,CAAAA;IAC7B,IAAIV,IAAM;IACV,IAAK,IAAIoB,KAAQV,EAAG;QACnB,IAAIW,IAAMX,CAAAA,CAAEU,EAAAA;QACD,QAAPC,KAAuB,OAARA,KAAAA,CACdrB,KAAAA,CAAKA,KAAO,GAAA,GAEhBA,KACY,OAAXoB,CAAAA,CAAK,EAAA,GACFA,IACAH,CAAAA,CAAUG,EAAAA,IAAAA,CACTH,CAAAA,CAAUG,EAAAA,GAAQA,EAAKP,OAAAA,CAAQK,GAAW,OAAOI,WAAAA,EAAAA,GAGrDtB,IADkB,YAAA,OAARqB,KAAAA,CAAsD,MAAlC3B,EAAmBO,IAAAA,CAAKmB,KAChDpB,IAAM,OAAOqB,IAAM,QAEnBrB,IAAM,OAAOqB,IAAM,GAAA;IAAA;IAI5B,OAAOrB,KAAAA,KAAOuB;AAAAA;AAAAA,SAUCC,EAAYC,CAAAA,EAAaC,CAAAA;IAMxC,OALIC,MAAMC,OAAAA,CAAQF,KACjBA,EAASG,MAAAA,CAAOL,GAAaC,KACP,QAAZC,KAAAA,CAAiC,MAAbA,KAC9BD,EAAYK,IAAAA,CAAKJ,IAEXD;AAAAA;AAGR,SAASM;IACRC,IAAAA,CAAKC,GAAAA,GAAAA,CAAM;AAAA;AAAA,SAGIC,EAAgBC,CAAAA,EAAOC,CAAAA;IACtC,OAAO;QACNC,KAAKF;QACLC,SAAAA;QACAE,OAAOH,EAAMG,KAAAA;QAEbC,UAAUR;QACVS,aAAaT;QACbE,KAAAA,CAAK;QAELQ,KAAK,EAAA;IAAA;AAAA;AAAA,SAMSC,EAAWC,CAAAA,EAAUP,CAAAA;IACpC,IAAIQ,IAASD,EAASE,WAAAA,EAClBC,IAAWF,KAAUR,CAAAA,CAAQQ,EAAOG,GAAAA,CAAAA;IACxC,OAAiB,QAAVH,IACJE,IACCA,EAASR,KAAAA,CAAMU,KAAAA,GACfJ,EAAOK,EAAAA,GACRb;AAAAA;AC5GJ,IAAMc,IAAU,EAAA;AAAA,SAEAC,EACfhB,CAAAA,EACAC,CAAAA,EACAgB,CAAAA,EACAC,CAAAA,EACAC,CAAAA,EACAC,CAAAA;IAEA,IAAa,QAATpB,KAAkC,aAAA,OAAVA,GAC3B,OAAO;IAIR,IAAqB,YAAA,OAAVA,GACV,OAAOpC,EAAeoC;IAGvB,IAAIqB,IAASJ,EAAKI,MAAAA,EACjBC,IAAaD,KAA4B,YAAA,OAAXA,IAAsBA,IAAS;IAE9D,IAAI7B,MAAMC,OAAAA,CAAQO,IAAQ;QAEzB,IADA,IAAIuB,IAAW,IACNvD,IAAI,GAAGA,IAAIgC,EAAM7B,MAAAA,EAAQH,IAC7BqD,KAAUrD,IAAI,KAAA,CAAGuD,KAAsB,IAAA,GAC3CA,KAECP,EACChB,CAAAA,CAAMhC,EAAAA,EACNiC,GACAgB,GACAC,GACAC,GACAC;QAGH,OAAOG;IAAAA;IAGR,IA8SyBC,GA9SrBhB,IAAWR,EAAMyB,IAAAA,EACpBtB,IAAQH,EAAMG,KAAAA,EACduB,IAAAA,CAAc;IAGf,IAAwB,cAAA,OAAblB,GAAyB;QAEnC,IADAkB,IAAAA,CAAc,GAAA,CACVT,EAAKU,OAAAA,IAAAA,CAAYT,KAAAA,CAAsC,MAA7BD,EAAKW,mBAAAA,EAAAA;YAAAA,IAExBpB,kJAAaqB,WAAAA,EAAU;gBACjC,IAAMtC,IAAW,EAAA;gBAEjB,OADAF,EAAYE,GAAUS,EAAMG,KAAAA,CAAMZ,QAAAA,GAC3ByB,EACNzB,GACAU,GACAgB,GAAAA,CAC0B,MAA1BA,EAAKa,gBAAAA,EACLX,GACAC;YAAAA;YAGD,IAAIG,GAEAQ,IAAK/B,EAAMY,GAAAA,GAAMb,EAAgBC,GAAOC;wJAGxC+B,UAAAA,CAAQC,GAAAA,gJAAKD,UAAAA,CAAQC,GAAAA,CAAIjC;YAG7B,IAAIkC,gJAAaF,UAAAA,CAAQG,GAAAA;YAEzB,IACE3B,EAAS4B,SAAAA,IAC2B,cAAA,OAA9B5B,EAAS4B,SAAAA,CAAUC,MAAAA,EAkBpB;gBACN,IAAIC,IAAO/B,EAAWC,GAAUP;gBAAAA,CAGhC8B,IAAI/B,EAAMY,GAAAA,GAAM,IAAIJ,EAASL,GAAOmC,EAAAA,EAClCpC,GAAAA,GAAMF,GAER+B,EAAEQ,MAAAA,GAASR,EAAEjC,GAAAA,GAAAA,CAAM,GACnBiC,EAAE5B,KAAAA,GAAQA,GACK,QAAX4B,EAAES,KAAAA,IAAAA,CAAeT,EAAES,KAAAA,GAAQ,CAAA,CAAA,GAEX,QAAhBT,EAAEU,UAAAA,IAA+B,QAATV,EAAEW,GAAAA,IAAAA,CAC7BX,EAAEU,UAAAA,GAAaV,EAAEW,GAAAA,GAAMX,EAAES,KAAAA,GAG1BT,EAAE9B,OAAAA,GAAUqC,GACR9B,EAASmC,wBAAAA,GACZZ,EAAES,KAAAA,GAAQI,OAAOC,MAAAA,CAChB,CAAA,GACAd,EAAES,KAAAA,EACFhC,EAASmC,wBAAAA,CAAyBZ,EAAE5B,KAAAA,EAAO4B,EAAES,KAAAA,KAEtCT,EAAEe,kBAAAA,IAAAA,CACVf,EAAEe,kBAAAA,IAIFf,EAAES,KAAAA,GACDT,EAAEU,UAAAA,KAAeV,EAAES,KAAAA,GAChBT,EAAEU,UAAAA,GACFV,EAAEW,GAAAA,KAAQX,EAAES,KAAAA,GACZT,EAAEW,GAAAA,GACFX,EAAES,KAAAA,GAGHN,KAAYA,EAAWlC,IAE3BuB,IAAWQ,EAAEM,MAAAA,CAAON,EAAE5B,KAAAA,EAAO4B,EAAES,KAAAA,EAAOT,EAAE9B,OAAAA;YAAAA,OA7CxC,IARA,IAAIqC,IAAO/B,EAAWC,GAAUP,IAO5B8C,IAAQ,GACLhB,EAAEjC,GAAAA,IAAOiD,MAAU,IACzBhB,EAAEjC,GAAAA,GAAAA,CAAM,GAEJoC,KAAYA,EAAWlC,IAG3BuB,IAAWf,EAASwC,IAAAA,CAAKhD,EAAMY,GAAAA,EAAKT,GAAOmC;YA+C7C,OALIP,EAAEkB,eAAAA,IAAAA,CACLhD,IAAU2C,OAAOC,MAAAA,CAAO,CAAA,GAAI5C,GAAS8B,EAAEkB,eAAAA,GAAAA,+IAGpCjB,UAAAA,CAAQkB,MAAAA,gJAAQlB,UAAAA,CAAQkB,MAAAA,CAAOlD,IAC5BgB,EACNO,GACAtB,GACAgB,GAAAA,CAC0B,MAA1BA,EAAKa,gBAAAA,EACLX,GACAC;QAAAA;QA9FDZ,IAAAA,CAsSuBgB,IAtSKhB,CAAAA,EAwSnB2C,WAAAA,IACT3B,MAAc4B,YAAY5B,EAAU6B,IAAAA,IAKvC,SAAkC7B,CAAAA;YACjC,IACC6B,IAAAA,CADSD,SAAShB,SAAAA,CAAUkB,QAAAA,CAASN,IAAAA,CAAKxB,GAC9B+B,KAAAA,CAAM,8BAA8B,EAAA,CAAA,CAAI,EAAA;YACrD,IAAA,CAAKF,GAAM;gBAGV,IADA,IAAIG,IAAAA,CAAS,GACJxF,IAAI+C,EAAQ5C,MAAAA,EAAQH,KAC5B,IAAI+C,CAAAA,CAAQ/C,EAAAA,KAAOwD,GAAW;oBAC7BgC,IAAQxF;oBACR;gBAAA;gBAIEwF,IAAQ,KAAA,CACXA,IAAQzC,EAAQpB,IAAAA,CAAK6B,KAAa,CAAA,GAEnC6B,IAAAA,qBAA0BG;YAAAA;YAE3B,OAAOH;QAAAA,CAtBNI,CAAyBjC;IAAAA;IAtM1B,IACCkC,GACAC,GAFGpF,IAAI,MAAMiC;IAId,IAAIL,GAAO;QACV,IAAIyD,IAAQhB,OAAOiB,IAAAA,CAAK1D;QAGpBc,KAAAA,CAAgC,MAAxBA,EAAK6C,cAAAA,IAAyBF,EAAMG,IAAAA;QAEhD,IAAK,IAAI/F,IAAI,GAAGA,IAAI4F,EAAMzF,MAAAA,EAAQH,IAAK;YACtC,IAAIqF,IAAOO,CAAAA,CAAM5F,EAAAA,EAChBgG,IAAI7D,CAAAA,CAAMkD,EAAAA;YACX,IAAa,eAATA,GAAAA;gBAKJ,IAAA,CAAI5F,EAAYK,IAAAA,CAAKuF,MAAAA,CAGlBpC,KAAQA,EAAKgD,aAAAA,IACL,UAATZ,KACS,UAATA,KACS,aAATA,KACS,eAATA,CAAAA,GALF;oBASA,IAAa,mBAATA,GACHA,IAAO;yBAAA,IACY,qBAATA,GACVA,IAAO;yBAAA,IACY,sBAATA,GACVA,IAAO;yBAAA,IACY,gBAATA,GAAsB;wBAChC,IAAA,KAA2B,MAAhBlD,EAAAA,KAAAA,EAA6B;wBACxCkD,IAAO;oBAAA,OACGlC,KAAazD,EAAMI,IAAAA,CAAKuF,MAAAA,CAClCA,IAAOA,EAAKlE,WAAAA,GAAcT,OAAAA,CAAQ,YAAY,SAAA;oBAG/C,IAAa,cAAT2E,GAAoB;wBACvB,IAAIlD,EAAAA,GAAAA,EAAW;wBACfkD,IAAO;oBAAA;oBAGK,YAATA,KAAoBW,KAAkB,YAAA,OAANA,KAAAA,CACnCA,IAAIhF,EAAcgF,EAAAA,GAKH,QAAZX,CAAAA,CAAK,EAAA,IAA4B,QAAdA,CAAAA,CAAK,EAAA,IAA6B,aAAA,OAANW,KAAAA,CAClDA,IAAIvF,OAAOuF,EAAAA;oBAGZ,IAAIE,IACHjD,EAAKkD,aAAAA,IACLlD,EAAKkD,aAAAA,CAAcd,GAAMW,GAAG/D,GAASgB,GAAMS;oBAC5C,IAAIwC,KAAqB,OAAXA,GACb3F,KAAQ2F;yBAIT,IAAa,8BAATb,GACHM,IAAOK,KAAKA,EAAEI,MAAAA;yBAAAA,IACS,eAAb5D,KAAoC,YAAT6C,GAErCK,IAAeM;yBAAAA,IAAAA,CACJA,KAAW,MAANA,KAAiB,OAANA,CAAAA,KAA0B,cAAA,OAANA,GAAkB;wBACjE,IAAA,CAAA,CAAA,CAAU,MAANA,KAAoB,OAANA,KAAAA,CACjBA,IAAIX,GAECpC,KAASA,EAAKoD,GAAAA,CAAAA,GAAK;4BACvB9F,IAAIA,IAAI,MAAM8E;4BACd;wBAAA;wBAIF,IAAa,YAATA,GAAkB;4BACrB,IAAiB,aAAb7C,GAAuB;gCAC1BY,IAAc4C;gCACd;4BAAA;4BAGa,aAAbxD,KACAY,KAAe4C,KAAAA,KAEW,MAAnB7D,EAAMmE,QAAAA,IAAAA,CAEb/F,KAAAA,WAAAA;wBAAAA;wBAGFA,IAAIA,IAAAA,MAAQ8E,IAAAA,OAASzF,EAAeoG,KAAAA;oBAAAA;gBAAAA;YAAAA,OAhFpCN,IAAeM;QAAAA;IAAAA;IAsFlB,IAAI3C,GAAQ;QACX,IAAIkD,IAAMhG,EAAEG,OAAAA,CAAQ,SAAS;QACzB6F,MAAQhG,KAAAA,CAAOgG,EAAI1F,OAAAA,CAAQ,QACtBwC,KAAAA,CAAW9C,EAAEM,OAAAA,CAAQ,SAAA,CAAON,KAAQ,IAAA,IADPA,IAAIgG;IAAAA;IAM3C,IAFAhG,KAAQ,KAEJd,EAAYK,IAAAA,CAAK0C,IACpB,MAAA,IAAUgE,MAAShE,IAAAA,sCAA4CjC;IAEhE,IAKIgB,GALAkF,IACHjH,EAAcM,IAAAA,CAAK0C,MAClBS,EAAKyD,YAAAA,IAAgBzD,EAAKyD,YAAAA,CAAa5G,IAAAA,CAAK0C,IAC1CmE,IAAS,EAAA;IAGb,IAAIhB,GAECtC,KAAU1C,EAAcgF,MAAAA,CAC3BA,IAAO,OAAOrC,IAAahD,EAAOqF,GAAMrC,EAAAA,GAEzC/C,KAAQoF;SAAAA,IAEQ,QAAhBD,KACArE,EAAaE,IAAW,EAAA,EAAKmE,GAAcvF,MAAAA,EAC1C;QAID,IAHA,IAAIyG,IAAWvD,KAAAA,CAAW9C,EAAEM,OAAAA,CAAQ,OAChCgG,IAAAA,CAAc,GAET7G,IAAI,GAAGA,IAAIuB,EAASpB,MAAAA,EAAQH,IAAK;YACzC,IAAI8G,IAAQvF,CAAAA,CAASvB,EAAAA;YAErB,IAAa,QAAT8G,KAAAA,CAA2B,MAAVA,GAAiB;gBACrC,IAMCC,IAAM/D,EACL8D,GACA7E,GACAgB,GAAAA,CACA,GATa,UAAbT,KAEgB,oBAAbA,KAEAW,GAOHC;gBAMF,IAHIC,KAAAA,CAAWuD,KAAYjG,EAAcoG,MAAAA,CAAMH,IAAAA,CAAW,CAAA,GAGtDG,GACH,IAAI1D,GAAQ;oBACX,IAAI2D,IAASD,EAAI5G,MAAAA,GAAS,KAAe,OAAV4G,CAAAA,CAAI,EAAA;oBAI/BF,KAAeG,IAClBL,CAAAA,CAAOA,EAAOxG,MAAAA,GAAS,EAAA,IAAM4G,IAE7BJ,EAAOhF,IAAAA,CAAKoF,IAGbF,IAAcG;gBAAAA,OAEdL,EAAOhF,IAAAA,CAAKoF;YAAAA;QAAAA;QAKhB,IAAI1D,KAAUuD,GACb,IAAK,IAAI5G,IAAI2G,EAAOxG,MAAAA,EAAQH,KAC3B2G,CAAAA,CAAO3G,EAAAA,GAAK,OAAOsD,IAAahD,EAAOqG,CAAAA,CAAO3G,EAAAA,EAAIsD;IAAAA;IAKrD,IAAIqD,EAAOxG,MAAAA,IAAUwF,GACpBpF,KAAQoG,EAAOM,IAAAA,CAAK;SAAA,IACVhE,KAAQA,EAAKoD,GAAAA,EACvB,OAAO9F,EAAE2G,SAAAA,CAAU,GAAG3G,EAAEJ,MAAAA,GAAS,KAAK;IAUvC,OAAA,CAPIsG,KAAWlF,KAAaoE,IAAAA,CAGvBtC,KAAAA,CAAW9C,EAAEM,OAAAA,CAAQ,SAAA,CAAON,KAAQ,IAAA,GACxCA,IAAIA,IAAAA,OAASiC,IAAAA,GAAAA,IAHbjC,IAAIA,EAAEG,OAAAA,CAAQ,MAAM,QAMdH;AAAAA;AAAAA,ICzUF4G,IAAU;IAAExD,SAAAA,CAAS;AAAA;AAa3ByD,EAAe/C,MAAAA,GAAS+C;AASpBC,IAAAA,IAAgB,SAACrF,CAAAA,EAAOC,CAAAA;IAAAA,OAAYmF,EAAepF,GAAOC,GAASkF;AAAAA,GAEjEG,IAAY,EAAA;AAClB,SAASF,EAAepF,CAAAA,EAAOC,CAAAA,EAASgB,CAAAA;IACvChB,IAAUA,KAAW,CAAA;IAOrB,IAGIsF,GAHEC,gJAAsBxD,UAAAA,CAAO,GAAA;IAwBnC,mJAvBAA,UAAAA,CAAO,GAAA,GAAA,CAAiB,GAavBuD,IATAtE,KAAAA,CACCA,EAAKI,MAAAA,IACLJ,EAAKyD,YAAAA,IACLzD,EAAK6C,cAAAA,IACL7C,EAAKU,OAAAA,IACLV,EAAKgD,aAAAA,IACLhD,EAAKoD,GAAAA,IACLpD,EAAKkD,aAAAA,IAEAnD,EAAsBhB,GAAOC,GAASgB,KAEtCwE,EAAgBzF,GAAOC,GAAAA,CAAS,GAAA,KAAOb,gJAK1C4C,UAAAA,CAAO,GAAA,gJAAUA,UAAAA,CAAO,GAAA,CAAShC,GAAOsF,gJAC5CtD,UAAAA,CAAO,GAAA,GAAiBwD,GACxBF,EAAUnH,MAAAA,GAAS,GACZoH;AAAAA;AAmER,SAASG,EAAkBrC,CAAAA,EAAMlC,CAAAA;IAChC,OAAa,gBAATkC,IACI,UACY,cAATA,IACH,QACY,mBAATA,IACH,UACY,qBAATA,IACH,YACY,sBAATA,IACH,aACGlC,KAAazD,EAAMI,IAAAA,CAAKuF,KAC3BA,EAAKlE,WAAAA,GAAcT,OAAAA,CAAQ,YAAY,YAGxC2E;AAAAA;AAGR,SAASsC,EAAmBtC,CAAAA,EAAMW,CAAAA;IACjC,OAAa,YAATX,KAAyB,QAALW,KAA0B,YAAA,OAANA,IACpChF,EAAcgF,KACC,QAAZX,CAAAA,CAAK,EAAA,IAA0B,QAAZA,CAAAA,CAAK,EAAA,IAA2B,aAAA,OAANW,IAGhDvF,OAAOuF,KAGRA;AAAAA;AAGR,IAAMvE,IAAUD,MAAMC,OAAAA,EAChBoD,IAASD,OAAOC,MAAAA;AAGtB,SAAS4C,EAAgBzF,CAAAA,EAAOC,CAAAA,EAASkB,CAAAA,EAAWC,CAAAA;IAEnD,IAAa,QAATpB,KAAAA,CAA2B,MAAVA,KAAAA,CAA4B,MAAVA,KAA6B,OAAVA,GACzD,OAAO;IAIR,IAAqB,YAAA,OAAVA,GACV,OAAOpC,EAAeoC;IAIvB,IAAIP,EAAQO,IAAQ;QAEnB,IADA,IAAIuB,IAAW,IACNvD,IAAI,GAAGA,IAAIgC,EAAM7B,MAAAA,EAAQH,IACjCuD,KACYkE,EAAgBzF,CAAAA,CAAMhC,EAAAA,EAAIiC,GAASkB,GAAWC;QAE3D,OAAOG;IAAAA;gJAGJS,UAAAA,CAAO,GAAA,gJAAQA,UAAAA,CAAO,GAAA,CAAOhC;IAEjC,IAAIyB,IAAOzB,EAAMyB,IAAAA,EAChBtB,IAAQH,EAAMG,KAAAA;IAIf,IADoC,cAAA,OAATsB,GACV;QAChB,IAAIA,kJAASI,WAAAA,EACZ,OAAO4D,EACNzF,EAAMG,KAAAA,CAAMZ,QAAAA,EACZU,GACAkB,GACAC;QAIF,IAAIG;QAEHA,IADGE,EAAKW,SAAAA,IAA8C,cAAA,OAA1BX,EAAKW,SAAAA,CAAUC,MAAAA,GA/G9C,SAA8BrC,CAAAA,EAAOC,CAAAA;YACpC,IAAIO,IAAWR,EAAMyB,IAAAA,EACpBa,IAAO/B,EAAWC,GAAUP,IAGzB8B,IAAI,IAAIvB,EAASR,EAAMG,KAAAA,EAAOmC;YAClCtC,EAAK,GAAA,GAAc+B,GACnBA,EAAC,GAAA,GAAU/B,GAEX+B,EAAC,GAAA,GAAA,CAAU,GACXA,EAAE5B,KAAAA,GAAQH,EAAMG,KAAAA,EACD,QAAX4B,EAAES,KAAAA,IAAAA,CAAeT,EAAES,KAAAA,GAAQ,CAAA,CAAA,GAEV,QAAjBT,EAAC,GAAA,IAAA,CACJA,EAAC,GAAA,GAAeA,EAAES,KAAAA,GAGnBT,EAAE9B,OAAAA,GAAUqC,GACR9B,EAASmC,wBAAAA,GACZZ,EAAES,KAAAA,GAAQK,EACT,CAAA,GACAd,EAAES,KAAAA,EACFhC,EAASmC,wBAAAA,CAAyBZ,EAAE5B,KAAAA,EAAO4B,EAAES,KAAAA,KAEpCT,EAAEe,kBAAAA,IAAAA,CACZf,EAAEe,kBAAAA,IAIFf,EAAES,KAAAA,GAAQT,EAAC,GAAA,KAAiBA,EAAES,KAAAA,GAAQT,EAAC,GAAA,GAAeA,EAAES,KAAAA;YAGzD,IAAIN,gJAAaF,UAAAA,CAAO,GAAA;YAGxB,OAFIE,KAAYA,EAAWlC,IAEpB+B,EAAEM,MAAAA,CAAON,EAAE5B,KAAAA,EAAO4B,EAAES,KAAAA,EAAOT,EAAE9B,OAAAA;QAAAA,CA6EvB2F,CAAqB5F,GAAOC,KA1I1C,SAAiCD,CAAAA,EAAOC,CAAAA;YACvC,IAAIsB,GACHQ,IAAIhC,EAAgBC,GAAOC,IAC3BqC,IAAO/B,EAAWP,EAAMyB,IAAAA,EAAMxB;YAE/BD,EAAK,GAAA,GAAc+B;YASnB,IAFA,IAAIG,gJAAaF,UAAAA,CAAO,GAAA,EACpBe,IAAQ,GACLhB,EAAC,GAAA,IAAWgB,MAAU,IAC5BhB,EAAC,GAAA,GAAA,CAAU,GAEPG,KAAYA,EAAWlC,IAG3BuB,IAAWvB,EAAMyB,IAAAA,CAAKuB,IAAAA,CAAKjB,GAAG/B,EAAMG,KAAAA,EAAOmC;YAG5C,OAAOf;QAAAA,CAqHMsE,CAAwB7F,GAAOC;QAG3C,IAAIuB,IAAYxB,EAAK,GAAA;QACjBwB,EAAUyB,eAAAA,IAAAA,CACbhD,IAAU4C,EAAO,CAAA,GAAI5C,GAASuB,EAAUyB,eAAAA,GAAAA;QAIzC,IAAMpF,IAAM4H,EAAgBlE,GAAUtB,GAASkB,GAAWC;QAE1D,mJADIY,UAAAA,CAAO,MAAA,gJAAUA,UAAAA,CAAO,MAAA,CAAShC,IAC9BnC;IAAAA;IAIR,IACC0B,GACAoE,GAFGpF,IAAI;IAMR,IAFAA,KAAQkD,GAEJtB,GAEH,IAAK,IAAIkD,KADT9D,IAAWY,EAAMZ,QAAAA,EACAY,EAAO;QACvB,IAAI6D,IAAI7D,CAAAA,CAAMkD,EAAAA;QAEd,IAAA,CAAA,CACU,UAATA,KACS,UAATA,KACS,aAATA,KACS,eAATA,KACS,eAATA,KACU,gBAATA,KAAwB,WAAWlD,KAC1B,cAATkD,KAAsB,SAASlD,KAK7B1C,EAAYK,IAAAA,CAAKuF,EAAAA;YAKrB,IAFAW,IAAI2B,EADJtC,IAAOqC,EAAkBrC,GAAMlC,IACF6C,IAEhB,8BAATX,GACHM,IAAOK,KAAKA,EAAEI,MAAAA;iBAAAA,IACK,eAAT3C,KAAgC,YAAT4B,GAEjC9D,IAAWyE;iBAAAA,IAAAA,CACAA,KAAW,MAANA,KAAiB,OAANA,CAAAA,KAA0B,cAAA,OAANA,GAAkB;gBACjE,IAAA,CAAU,MAANA,KAAoB,OAANA,GAAU;oBAC3BA,IAAIX,GACJ9E,IAAIA,IAAI,MAAM8E;oBACd;gBAAA;gBAGD,IAAa,YAATA,GAAkB;oBACrB,IAAa,aAAT5B,GAAmB;wBACtBL,IAAc4C;wBACd;oBAAA;oBAGS,aAATvC,KACAL,KAAe4C,KAEb,cAAc7D,KAAAA,CAEhB5B,KAAQ,WAAA;gBAAA;gBAGVA,IAAIA,IAAI,MAAM8E,IAAO,OAAOzF,EAAeoG,KAAK;YAAA;QAAA;IAAA;IAKnD,IAAI8B,IAAevH;IAGnB,IAFAA,KAAQ,KAEJd,EAAYK,IAAAA,CAAK2D,IACpB,MAAA,IAAU+C,MAAS/C,IAAAA,sCAAwClD;IAG5D,IAAIoG,IAAS,IACToB,IAAAA,CAAc;IAElB,IAAIpC,GACHgB,KAAkBhB,GAClBoC,IAAAA,CAAc;SAAA,IACgB,YAAA,OAAbxG,GACjBoF,KAAkB/G,EAAe2B,IACjCwG,IAAAA,CAAc;SAAA,IACJtG,EAAQF,IAClB,IAAK,IAAIvB,IAAI,GAAGA,IAAIuB,EAASpB,MAAAA,EAAQH,IAAK;QACzC,IAAI8G,IAAQvF,CAAAA,CAASvB,EAAAA;QAErB,IAAa,QAAT8G,KAAAA,CAA2B,MAAVA,GAAiB;YACrC,IAEIC,IAAMU,EAAgBX,GAAO7E,GADvB,UAATwB,KAA4B,oBAATA,KAA4BN,GACQC;YAGpD2D,KAAAA,CACHJ,KAAkBI,GAClBgB,IAAAA,CAAc,CAAA;QAAA;IAAA;SAAA,IAIK,QAAZxG,KAAAA,CAAiC,MAAbA,KAAAA,CAAmC,MAAbA,GAAmB;QACvE,IAEIwF,IAAMU,EAAgBlG,GAAUU,GAD1B,UAATwB,KAA4B,oBAATA,KAA4BN,GACWC;QAGvD2D,KAAAA,CACHJ,KAAkBI,GAClBgB,IAAAA,CAAc,CAAA;IAAA;IAMhB,gJAFI/D,UAAAA,CAAO,MAAA,gJAAUA,UAAAA,CAAO,MAAA,CAAShC,IAEjC+F,GACHxH,KAAQoG;SAAAA,IACEnH,EAAcM,IAAAA,CAAK2D,IAC7B,OAAOqE,IAAe;IAGvB,OAAOvH,IAAI,OAAOkD,IAAO;AAAA;AAK1B2D,EAAeC,aAAAA,GAAgBA;uCAAAA", "ignoreList": [0, 1, 2], "debugId": null}}, {"offset": {"line": 2269, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAI,UAAkB,CAAA;AAEtB,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,UAAU,EAAE,CAAC,cAAc,CAAC,EAAE,CAAC;IAC3F,MAAM,IAAI,GAAG,cAAc,CAAA;IAC3B,MAAM,OAAO,GAAG,QAAQ,CAAA;IACxB,UAAU,GAAG,GAAG,IAAI,CAAA,CAAA,EAAI,OAAO,EAAE,CAAA;AACnC,CAAC;AAkCD,SAAS,eAAe,CAAe,KAAc,EAAE,QAAwB;IAC7E,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;QAClB,OAAO,KAAK,CAAA;IACd,CAAC;IAED,IAAI,CAAC;QACH,OACE,AADK,KACA,YAAY,QAAQ,IACzB,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,KAAK,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,WAAW,CAAC,CAC5F,CAAA;IACH,CAAC,CAAC,OAAM,CAAC;QACP,OAAO,KAAK,CAAA;IACd,CAAC;AACH,CAAC;AAkCD,MAAM,qBAAqB,GAAG,uBAAuB,CAAA;AACrD,MAAM,oBAAoB,GAAG,sBAAsB,CAAA;AAInD,SAAS,cAAc,CAAC,OAAe,EAAE,IAAW,EAAE,KAAe;IACnE,MAAM,GAAG,GAAG,IAAI,SAAS,CAAC,OAAO,EAAE;QAAE,KAAK;IAAA,CAAE,CAAC,CAAA;IAC7C,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE;QAAE,IAAI;IAAA,CAAE,CAAC,CAAA;IAC5B,OAAO,GAAG,CAAA;AACZ,CAAC;AA2CM,MAAM,qBAAqB,GAAkB,MAAM,EAAE,CAAA;AA8BrD,MAAM,SAAS,GAAkB,MAAM,EAAE,CAAA;AAkBzC,MAAM,cAAc,GAAkB,MAAM,EAAE,CAAA;AAoI9C,MAAM,WAAW,GAAkB,MAAM,EAAE,CAAA;AAoC3C,MAAM,eAAe,GAAkB,MAAM,EAAE,CAAA;AA8C/C,MAAM,UAAU,GAAkB,MAAM,EAAE,CAAA;AAuD1C,MAAM,SAAS,GAAkB,MAAM,EAAE,CAAA;AA+chD,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAA;AACjC,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAA;AAIjC,SAAS,GAAG,CAAC,KAA0B;IACrC,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QAC9B,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;IAC9B,CAAC;IAED,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;AAC9B,CAAC;AAED,IAAI,eAA4D,CAAA;AAEhE,IAAI,UAAU,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;IAClC,eAAe,GAAG,CAAC,KAAK,EAAE,EAAE;QAC1B,IAAI,KAAK,YAAY,WAAW,EAAE,CAAC;YACjC,KAAK,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,CAAA;QAC/B,CAAC;QAGD,OAAO,KAAK,CAAC,QAAQ,CAAC;YAAE,QAAQ,EAAE,WAAW;YAAE,WAAW,EAAE,IAAI;QAAA,CAAE,CAAC,CAAA;IACrE,CAAC,CAAA;AACH,CAAC,MAAM,CAAC;IACN,MAAM,UAAU,GAAG,MAAM,CAAA;IACzB,eAAe,GAAG,CAAC,KAAK,EAAE,EAAE;QAC1B,IAAI,KAAK,YAAY,WAAW,EAAE,CAAC;YACjC,KAAK,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,CAAA;QAC/B,CAAC;QAED,MAAM,GAAG,GAAG,EAAE,CAAA;QACd,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,UAAU,EAAE,CAAC,IAAI,UAAU,CAAE,CAAC;YAEtD,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAA;QAC9E,CAAC;QACD,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAA;IACrF,CAAC,CAAA;AACH,CAAC;AAED,IAAI,eAA8C,CAAA;AAGlD,IAAI,UAAU,CAAC,UAAU,EAAE,CAAC;IAC1B,eAAe,GAAG,CAAC,KAAK,EAAE,EAAE;QAC1B,IAAI,CAAC;YAEH,OAAO,UAAU,CAAC,UAAU,CAAC,KAAK,EAAE;gBAAE,QAAQ,EAAE,WAAW;YAAA,CAAE,CAAC,CAAA;QAChE,CAAC,CAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,cAAc,CAClB,mDAAmD,EACnD,qBAAqB,EACrB,KAAK,CACN,CAAA;QACH,CAAC;IACH,CAAC,CAAA;AACH,CAAC,MAAM,CAAC;IACN,eAAe,GAAG,CAAC,KAAK,EAAE,EAAE;QAC1B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,CAAA;YACnF,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;YAC3C,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;gBACvC,KAAK,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAA;YACjC,CAAC;YACD,OAAO,KAAK,CAAA;QACd,CAAC,CAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,cAAc,CAClB,mDAAmD,EACnD,qBAAqB,EACrB,KAAK,CACN,CAAA;QACH,CAAC;IACH,CAAC,CAAA;AACH,CAAC;AAID,SAAS,IAAI,CAAC,KAAwC;IACpD,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QAC9B,OAAO,eAAe,CAAC,KAAK,CAAC,CAAA;IAC/B,CAAC;IAED,OAAO,eAAe,CAAC,KAAK,CAAC,CAAA;AAC/B,CAAC;AAKK,MAAO,yBAA0B,SAAQ,KAAK;IAClD,IAAI,CAAQ;IAIZ,YAAY,OAAe,EAAE,OAA6B,CAAA;QACxD,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;QACvB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAA;QACjC,IAAI,CAAC,IAAI,GAAG,qBAAqB,CAAA;QAEjC,KAAK,CAAC,iBAAiB,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAA;IACnD,CAAC;CACF;AAKK,MAAO,wBAAyB,SAAQ,KAAK;IACjD,IAAI,CAAS;IAKb,YAAY,OAAe,EAAE,OAA4C,CAAA;QACvE,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;QACvB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAA;QACjC,IAAI,OAAO,EAAE,IAAI,EAAE,CAAC;YAClB,IAAI,CAAC,IAAI,GAAG,OAAO,EAAE,IAAI,CAAA;QAC3B,CAAC;QAED,KAAK,CAAC,iBAAiB,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAA;IACnD,CAAC;CACF;AAED,SAAS,GAAG,CAAC,OAAe,EAAE,IAAa,EAAE,KAAe;IAC1D,OAAO,IAAI,wBAAwB,CAAC,OAAO,EAAE;QAAE,IAAI;QAAE,KAAK;IAAA,CAAE,CAAC,CAAA;AAC/D,CAAC;AAED,SAAS,eAAe,CAAC,GAAY,EAAE,EAAU;IAC/C,IAAI,CAAC,CAAC,GAAG,YAAY,SAAS,CAAC,EAAE,CAAC;QAChC,MAAM,cAAc,CAAC,GAAG,EAAE,CAAA,oBAAA,CAAsB,EAAE,oBAAoB,CAAC,CAAA;IACzE,CAAC;AACH,CAAC;AAED,SAAS,gBAAgB,CACvB,GAAY,EACZ,EAAU;IAEV,eAAe,CAAC,GAAG,EAAE,EAAE,CAAC,CAAA;IAExB,IAAI,GAAG,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;QAC3B,MAAM,cAAc,CAAC,GAAG,EAAE,CAAA,4BAAA,CAA8B,EAAE,qBAAqB,CAAC,CAAA;IAClF,CAAC;AACH,CAAC;AAED,SAAS,eAAe,CAAC,GAAY,EAAE,EAAU;IAC/C,eAAe,CAAC,GAAG,EAAE,EAAE,CAAC,CAAA;IAExB,IAAI,GAAG,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;QAC1B,MAAM,cAAc,CAAC,GAAG,EAAE,CAAA,2BAAA,CAA6B,EAAE,qBAAqB,CAAC,CAAA;IACjF,CAAC;AACH,CAAC;AAkFD,SAAS,YAAY,CAAC,KAAa;IACjC,OAAO,KAAK,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAA;AAC1D,CAAC;AAED,SAAS,YAAY,CAAiB,KAAc;IAClD,IAAI,KAAK,KAAK,IAAI,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;QACxE,OAAO,KAAK,CAAA;IACd,CAAC;IAED,OAAO,IAAI,CAAA;AACb,CAAC;AAED,SAAS,cAAc,CAAC,KAA6D;IACnF,IAAI,eAAe,CAAC,KAAK,EAAE,OAAO,CAAC,EAAE,CAAC;QACpC,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAA;IAC7C,CAAC;IACD,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,KAAK,IAAI,CAAA,CAAE,CAAC,CAAA;IAExC,IAAI,UAAU,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,CAAC;QAC7C,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE,UAAU,CAAC,CAAA;IACvC,CAAC;IAED,IAAI,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,EAAE,CAAC;QACjC,MAAM,cAAc,CAClB,oEAAoE,EACpE,qBAAqB,CACtB,CAAA;IACH,CAAC;IAED,OAAO,OAAO,CAAA;AAChB,CAAC;AAED,SAAS,MAAM,CAAC,KAA4D;IAC1E,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE,CAAC;QAChC,KAAK,GAAG,KAAK,EAAE,CAAA;IACjB,CAAC;IAED,IAAI,CAAC,CAAC,KAAK,YAAY,WAAW,CAAC,EAAE,CAAC;QACpC,MAAM,cAAc,CAClB,+DAA+D,EAC/D,oBAAoB,CACrB,CAAA;IACH,CAAC;IAED,OAAO,KAAK,CAAA;AACd,CAAC;AAED,SAAS,kBAAkB,CAAC,QAAgB;IAC1C,IAAI,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;QAC5B,OAAO,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;IACpC,CAAC;IACD,OAAO,QAAQ,CAAA;AACjB,CAAC;AAED,SAAS,gBAAgB,CAAC,GAAQ,EAAE,SAAiB,EAAE,qBAAqB,GAAG,KAAK;IAClF,IAAI,GAAG,CAAC,QAAQ,KAAK,GAAG,EAAE,CAAC;QACzB,GAAG,CAAC,QAAQ,GAAG,SAAS,CAAA;IAC1B,CAAC,MAAM,CAAC;QACN,GAAG,CAAC,QAAQ,GAAG,kBAAkB,CAC/B,GAAG,SAAS,CAAA,CAAA,EAAI,qBAAqB,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,EAAE,CAC3F,CAAA;IACH,CAAC;IACD,OAAO,GAAG,CAAA;AACZ,CAAC;AAED,SAAS,eAAe,CAAC,GAAQ,EAAE,SAAiB;IAClD,GAAG,CAAC,QAAQ,GAAG,kBAAkB,CAAC,GAAG,GAAG,CAAC,QAAQ,CAAA,CAAA,EAAI,SAAS,EAAE,CAAC,CAAA;IACjE,OAAO,GAAG,CAAA;AACZ,CAAC;AAED,KAAK,UAAU,gBAAgB,CAC7B,KAAU,EACV,OAAe,EACf,SAA4B,EAC5B,OAAmC;IAEnC,IAAI,CAAC,CAAC,KAAK,YAAY,GAAG,CAAC,EAAE,CAAC;QAC5B,MAAM,cAAc,CAAC,CAAA,CAAA,EAAI,OAAO,CAAA,4BAAA,CAA8B,EAAE,oBAAoB,CAAC,CAAA;IACvF,CAAC;IAED,aAAa,CAAC,KAAK,EAAE,OAAO,EAAE,CAAC,qBAAqB,CAAC,KAAK,IAAI,CAAC,CAAA;IAE/D,MAAM,GAAG,GAAG,SAAS,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAA;IAE1C,MAAM,OAAO,GAAG,cAAc,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;IAChD,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,kBAAkB,CAAC,CAAA;IAEzC,OAAO,CAAC,OAAO,EAAE,CAAC,WAAW,CAAC,IAAI,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE;QACjD,IAAI,EAAE,SAAS;QACf,OAAO,EAAE,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;QAC9C,MAAM,EAAE,KAAK;QACb,QAAQ,EAAE,QAAQ;QAClB,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS;KAC7D,CAAC,CAAA;AACJ,CAAC;AAoBM,KAAK,UAAU,gBAAgB,CACpC,gBAAqB,EACrB,OAAiC;IAEjC,OAAO,gBAAgB,CACrB,gBAAgB,EAChB,kBAAkB,EAClB,CAAC,GAAG,EAAE,EAAE;QACN,OAAQ,OAAO,EAAE,SAAS,EAAE,CAAC;YAC3B,KAAK,SAAS,CAAC;YACf,KAAK,MAAM;gBACT,eAAe,CAAC,GAAG,EAAE,kCAAkC,CAAC,CAAA;gBACxD,MAAK;YACP,KAAK,QAAQ;gBACX,gBAAgB,CAAC,GAAG,EAAE,wCAAwC,CAAC,CAAA;gBAC/D,MAAK;YACP;gBACE,MAAM,cAAc,CAClB,2DAA2D,EAC3D,qBAAqB,CACtB,CAAA;QACL,CAAC;QACD,OAAO,GAAG,CAAA;IACZ,CAAC,EACD,OAAO,CACR,CAAA;AACH,CAAC;AAED,SAAS,YAAY,CACnB,KAAc,EACd,MAAe,EACf,EAAU,EACV,IAAa,EACb,KAAe;IAEf,IAAI,CAAC;QACH,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YACzD,MAAM,cAAc,CAAC,GAAG,EAAE,CAAA,iBAAA,CAAmB,EAAE,oBAAoB,EAAE,KAAK,CAAC,CAAA;QAC7E,CAAC;QAED,IAAI,KAAK,GAAG,CAAC,EAAE,OAAM;QAErB,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC;gBAChB,MAAM,cAAc,CAAC,GAAG,EAAE,CAAA,8BAAA,CAAgC,EAAE,qBAAqB,EAAE,KAAK,CAAC,CAAA;YAC3F,CAAC;YACD,OAAM;QACR,CAAC;QAED,MAAM,cAAc,CAAC,GAAG,EAAE,CAAA,0BAAA,CAA4B,EAAE,qBAAqB,EAAE,KAAK,CAAC,CAAA;IACvF,CAAC,CAAC,OAAO,GAAG,EAAE,CAAC;QACb,IAAI,IAAI,EAAE,CAAC;YACT,MAAM,GAAG,CAAE,GAAa,CAAC,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC,CAAA;QAChD,CAAC;QAED,MAAM,GAAG,CAAA;IACX,CAAC;AACH,CAAC;AAED,SAAS,YAAY,CACnB,KAAc,EACd,EAAU,EACV,IAAa,EACb,KAAe;IAEf,IAAI,CAAC;QACH,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC9B,MAAM,cAAc,CAAC,GAAG,EAAE,CAAA,iBAAA,CAAmB,EAAE,oBAAoB,EAAE,KAAK,CAAC,CAAA;QAC7E,CAAC;QAED,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvB,MAAM,cAAc,CAAC,GAAG,EAAE,CAAA,kBAAA,CAAoB,EAAE,qBAAqB,EAAE,KAAK,CAAC,CAAA;QAC/E,CAAC;IACH,CAAC,CAAC,OAAO,GAAG,EAAE,CAAC;QACb,IAAI,IAAI,EAAE,CAAC;YACT,MAAM,GAAG,CAAE,GAAa,CAAC,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC,CAAA;QAChD,CAAC;QAED,MAAM,GAAG,CAAA;IACX,CAAC;AACH,CAAC;AAiBM,KAAK,UAAU,wBAAwB,CAC5C,wBAA6B,EAC7B,QAAkB;IAElB,MAAM,QAAQ,GAAG,wBAA0D,CAAA;IAC3E,IAAI,CAAC,CAAC,QAAQ,YAAY,GAAG,CAAC,IAAI,QAAQ,KAAK,iBAAiB,EAAE,CAAC;QACjE,MAAM,cAAc,CAClB,uDAAuD,EACvD,oBAAoB,CACrB,CAAA;IACH,CAAC;IAED,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE,CAAC;QACzC,MAAM,cAAc,CAAC,4CAA4C,EAAE,oBAAoB,CAAC,CAAA;IAC1F,CAAC;IAED,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;QAC5B,MAAM,GAAG,CACP,kGAAkG,EAClG,uBAAuB,EACvB,QAAQ,CACT,CAAA;IACH,CAAC;IAED,sBAAsB,CAAC,QAAQ,CAAC,CAAA;IAChC,MAAM,IAAI,GAAG,MAAM,mBAAmB,CAAsB,QAAQ,CAAC,CAAA;IAErE,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,mCAAmC,EAAE,gBAAgB,EAAE;QAAE,IAAI,EAAE,IAAI;IAAA,CAAE,CAAC,CAAA;IAEhG,IAAI,QAAQ,KAAK,iBAAiB,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,IAAI,EAAE,CAAC;QAClF,MAAM,GAAG,CACP,qEAAqE,EACrE,yBAAyB,EACzB;YAAE,QAAQ,EAAE,QAAQ,CAAC,IAAI;YAAE,IAAI,EAAE,IAAI;YAAE,SAAS,EAAE,QAAQ;QAAA,CAAE,CAC7D,CAAA;IACH,CAAC;IAED,OAAO,IAAI,CAAA;AACb,CAAC;AAED,SAAS,qBAAqB,CAAC,QAAkB;IAC/C,iBAAiB,CAAC,QAAQ,EAAE,kBAAkB,CAAC,CAAA;AACjD,CAAC;AAED,SAAS,OAAO,CAAC,QAAkB,EAAE,GAAG,KAAe;IACrD,IAAI,GAAG,GAAG,kCAAkC,CAAA;IAC5C,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACrB,MAAM,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAA;QACxB,GAAG,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA,KAAA,EAAQ,IAAI,EAAE,CAAA;IAC1C,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC9B,GAAG,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAA,IAAA,EAAO,KAAK,CAAC,CAAC,CAAC,EAAE,CAAA;IACrC,CAAC,MAAM,CAAC;QACN,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,CAAA;IACjB,CAAC;IACD,OAAO,GAAG,CAAC,GAAG,EAAE,oBAAoB,EAAE,QAAQ,CAAC,CAAA;AACjD,CAAC;AAED,SAAS,kBAAkB,CAAC,QAAkB,EAAE,GAAG,KAAe;IAChE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,cAAc,CAAC,QAAQ,CAAE,CAAC,EAAE,CAAC;QAC/C,MAAM,OAAO,CAAC,QAAQ,EAAE,GAAG,KAAK,CAAC,CAAA;IACnC,CAAC;AACH,CAAC;AAED,SAAS,iBAAiB,CAAC,QAAkB,EAAE,WAAmB;IAChE,IAAI,cAAc,CAAC,QAAQ,CAAC,KAAK,WAAW,EAAE,CAAC;QAC7C,MAAM,OAAO,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAA;IACtC,CAAC;AACH,CAAC;AAKD,SAAS,WAAW;IAClB,OAAO,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,IAAI,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;AACzD,CAAC;AAYK,SAAU,0BAA0B;IACxC,OAAO,WAAW,EAAE,CAAA;AACtB,CAAC;AASK,SAAU,mBAAmB;IACjC,OAAO,WAAW,EAAE,CAAA;AACtB,CAAC;AASK,SAAU,mBAAmB;IACjC,OAAO,WAAW,EAAE,CAAA;AACtB,CAAC;AAcM,KAAK,UAAU,0BAA0B,CAAC,YAAoB;IACnE,YAAY,CAAC,YAAY,EAAE,cAAc,CAAC,CAAA;IAE1C,OAAO,IAAI,CAAC,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,CAAA;AACvE,CAAC;AAOD,SAAS,YAAY,CAAC,KAAyC;IAC7D,IAAI,KAAK,YAAY,SAAS,EAAE,CAAC;QAC/B,OAAO;YAAE,GAAG,EAAE,KAAK;QAAA,CAAE,CAAA;IACvB,CAAC;IAED,IAAI,CAAC,CAAC,KAAK,EAAE,GAAG,YAAY,SAAS,CAAC,EAAE,CAAC;QACvC,OAAO,CAAA,CAAE,CAAA;IACX,CAAC;IAED,IAAI,KAAK,CAAC,GAAG,KAAK,SAAS,EAAE,CAAC;QAC5B,YAAY,CAAC,KAAK,CAAC,GAAG,EAAE,OAAO,CAAC,CAAA;IAClC,CAAC;IAED,OAAO;QACL,GAAG,EAAE,KAAK,CAAC,GAAG;QACd,GAAG,EAAE,KAAK,CAAC,GAAG;KACf,CAAA;AACH,CAAC;AAgBD,SAAS,KAAK,CAAC,GAAc;IAC3B,OAAS,GAAG,CAAC,SAAmC,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;QAC3D,KAAK,SAAS;YACZ,OAAO,OAAO,CAAA;QAChB,KAAK,SAAS;YACZ,OAAO,OAAO,CAAA;QAChB,KAAK,SAAS;YACZ,OAAO,OAAO,CAAA;QAChB;YACE,MAAM,IAAI,yBAAyB,CAAC,6CAA6C,EAAE;gBACjF,KAAK,EAAE,GAAG;aACX,CAAC,CAAA;IACN,CAAC;AACH,CAAC;AAKD,SAAS,KAAK,CAAC,GAAc;IAC3B,OAAS,GAAG,CAAC,SAAmC,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;QAC3D,KAAK,SAAS;YACZ,OAAO,OAAO,CAAA;QAChB,KAAK,SAAS;YACZ,OAAO,OAAO,CAAA;QAChB,KAAK,SAAS;YACZ,OAAO,OAAO,CAAA;QAChB;YACE,MAAM,IAAI,yBAAyB,CAAC,6CAA6C,EAAE;gBACjF,KAAK,EAAE,GAAG;aACX,CAAC,CAAA;IACN,CAAC;AACH,CAAC;AAKD,SAAS,KAAK,CAAC,GAAc;IAC3B,OAAS,GAAG,CAAC,SAA4B,CAAC,UAAU,EAAE,CAAC;QACrD,KAAK,OAAO;YACV,OAAO,OAAO,CAAA;QAChB,KAAK,OAAO;YACV,OAAO,OAAO,CAAA;QAChB,KAAK,OAAO;YACV,OAAO,OAAO,CAAA;QAChB;YACE,MAAM,IAAI,yBAAyB,CAAC,uCAAuC,EAAE;gBAAE,KAAK,EAAE,GAAG;YAAA,CAAE,CAAC,CAAA;IAChG,CAAC;AACH,CAAC;AAKD,SAAS,QAAQ,CAAC,GAAc;IAC9B,OAAQ,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QAC3B,KAAK,SAAS;YACZ,OAAO,KAAK,CAAC,GAAG,CAAC,CAAA;QACnB,KAAK,mBAAmB;YACtB,OAAO,KAAK,CAAC,GAAG,CAAC,CAAA;QACnB,KAAK,OAAO;YACV,OAAO,KAAK,CAAC,GAAG,CAAC,CAAA;QACnB,KAAK,SAAS,CAAC;QACf,KAAK,OAAO;YACV,OAAO,SAAS,CAAA;QAClB;YACE,MAAM,IAAI,yBAAyB,CAAC,sCAAsC,EAAE;gBAAE,KAAK,EAAE,GAAG;YAAA,CAAE,CAAC,CAAA;IAC/F,CAAC;AACH,CAAC;AAED,SAAS,YAAY,CAAC,MAAuC;IAC3D,MAAM,IAAI,GAAG,MAAM,EAAE,CAAC,SAAS,CAAC,CAAA;IAEhC,OAAO,OAAO,IAAI,KAAK,QAAQ,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;AACrE,CAAC;AAED,SAAS,iBAAiB,CAAC,MAA4C;IACrE,MAAM,SAAS,GAAG,MAAM,EAAE,CAAC,cAAc,CAAC,CAAA;IAE1C,OAAO,OAAO,SAAS,KAAK,QAAQ,IAAI,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,GAC7F,SAAS,GACT,EAAE,CAAA;AACR,CAAC;AAKD,SAAS,SAAS;IAChB,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAA;AACtC,CAAC;AAED,SAAS,QAAQ,CAAC,EAAuB;IACvC,IAAI,OAAO,EAAE,KAAK,QAAQ,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC;QAC1C,MAAM,cAAc,CAAC,wBAAwB,EAAE,oBAAoB,CAAC,CAAA;IACtE,CAAC;IAED,YAAY,CAAC,EAAE,CAAC,MAAM,EAAE,aAAa,CAAC,CAAA;AACxC,CAAC;AAED,SAAS,YAAY,CAAC,MAAc;IAClC,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;QAClD,MAAM,cAAc,CAAC,4BAA4B,EAAE,oBAAoB,CAAC,CAAA;IAC1E,CAAC;IAED,YAAY,CAAC,MAAM,CAAC,SAAS,EAAE,oBAAoB,CAAC,CAAA;AACtD,CAAC;AAOD,SAAS,aAAa,CAAC,KAAa;IAClC,OAAO,kBAAkB,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,sBAAsB,EAAE,CAAC,SAAS,EAAE,EAAE;QAC7E,OAAQ,SAAS,EAAE,CAAC;YAClB,KAAK,GAAG,CAAC;YACT,KAAK,GAAG,CAAC;YACT,KAAK,GAAG,CAAC;YACT,KAAK,GAAG,CAAC;YACT,KAAK,GAAG,CAAC;YACT,KAAK,GAAG,CAAC;YACT,KAAK,GAAG,CAAC;YACT,KAAK,GAAG,CAAC;YACT,KAAK,GAAG;gBACN,OAAO,CAAA,CAAA,EAAI,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,EAAE,CAAA;YACjE,KAAK,KAAK;gBACR,OAAO,GAAG,CAAA;YACZ;gBACE,MAAM,IAAI,KAAK,EAAE,CAAA;QACrB,CAAC;IACH,CAAC,CAAC,CAAA;AACJ,CAAC;AAuCK,SAAU,gBAAgB,CAAC,YAAoB;IACnD,YAAY,CAAC,YAAY,EAAE,gBAAgB,CAAC,CAAA;IAC5C,OAAO,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;QACrC,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,MAAM,CAAC,SAAS,CAAC,CAAA;QACvC,IAAI,CAAC,GAAG,CAAC,eAAe,EAAE,YAAY,CAAC,CAAA;IACzC,CAAC,CAAA;AACH,CAAC;AAsBK,SAAU,iBAAiB,CAAC,YAAoB;IACpD,YAAY,CAAC,YAAY,EAAE,gBAAgB,CAAC,CAAA;IAC5C,OAAO,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE;QACrC,MAAM,QAAQ,GAAG,aAAa,CAAC,MAAM,CAAC,SAAS,CAAC,CAAA;QAChD,MAAM,QAAQ,GAAG,aAAa,CAAC,YAAY,CAAC,CAAA;QAC5C,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,QAAQ,CAAA,CAAA,EAAI,QAAQ,EAAE,CAAC,CAAA;QACnD,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,CAAA,MAAA,EAAS,WAAW,EAAE,CAAC,CAAA;IACtD,CAAC,CAAA;AACH,CAAC;AAWD,SAAS,sBAAsB,CAAC,EAAuB,EAAE,MAAc;IACrE,MAAM,GAAG,GAAG,SAAS,EAAE,GAAG,YAAY,CAAC,MAAM,CAAC,CAAA;IAC9C,OAAO;QACL,GAAG,EAAE,WAAW,EAAE;QAClB,GAAG,EAAE,EAAE,CAAC,MAAM;QACd,GAAG,EAAE,GAAG,GAAG,EAAE;QACb,GAAG,EAAE,GAAG;QACR,GAAG,EAAE,GAAG;QACR,GAAG,EAAE,MAAM,CAAC,SAAS;QACrB,GAAG,EAAE,MAAM,CAAC,SAAS;KACtB,CAAA;AACH,CAAC;AAsBK,SAAU,aAAa,CAC3B,gBAAwC,EACxC,OAAgC;IAEhC,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,YAAY,CAAC,gBAAgB,CAAC,CAAA;IACnD,gBAAgB,CAAC,GAAG,EAAE,wBAAwB,CAAC,CAAA;IAC/C,OAAO,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;QAC1C,MAAM,MAAM,GAAG;YAAE,GAAG,EAAE,QAAQ,CAAC,GAAG,CAAC;YAAE,GAAG;QAAA,CAAE,CAAA;QAC1C,MAAM,OAAO,GAAG,sBAAsB,CAAC,EAAE,EAAE,MAAM,CAAC,CAAA;QAElD,OAAO,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;QAE7C,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,MAAM,CAAC,SAAS,CAAC,CAAA;QACvC,IAAI,CAAC,GAAG,CAAC,uBAAuB,EAAE,wDAAwD,CAAC,CAAA;QAC3F,IAAI,CAAC,GAAG,CAAC,kBAAkB,EAAE,MAAM,OAAO,CAAC,MAAM,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC,CAAA;IACnE,CAAC,CAAA;AACH,CAAC;AAuBK,SAAU,eAAe,CAC7B,YAAoB,EACpB,OAAgC;IAEhC,YAAY,CAAC,YAAY,EAAE,gBAAgB,CAAC,CAAA;IAC5C,MAAM,MAAM,GAAG,OAAO,EAAE,CAAC,eAAe,CAAC,CAAA;IACzC,IAAI,GAAc,CAAA;IAClB,OAAO,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;QAC1C,GAAG,KAAK,MAAM,MAAM,CAAC,MAAM,CAAC,SAAS,CACnC,KAAK,EACL,GAAG,CAAC,YAAY,CAAC,EACjB;YAAE,IAAI,EAAE,SAAS;YAAE,IAAI,EAAE,MAAM;QAAA,CAAE,EACjC,KAAK,EACL;YAAC,MAAM;SAAC,CACT,CAAA;QAED,MAAM,MAAM,GAAG;YAAE,GAAG,EAAE,OAAO;QAAA,CAAE,CAAA;QAC/B,MAAM,OAAO,GAAG,sBAAsB,CAAC,EAAE,EAAE,MAAM,CAAC,CAAA;QAElD,MAAM,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;QAEzB,MAAM,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA,CAAA,EAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAA;QACzF,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,CAAA;QAEpE,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,MAAM,CAAC,SAAS,CAAC,CAAA;QACvC,IAAI,CAAC,GAAG,CAAC,uBAAuB,EAAE,wDAAwD,CAAC,CAAA;QAC3F,IAAI,CAAC,GAAG,CAAC,kBAAkB,EAAE,GAAG,IAAI,CAAA,CAAA,EAAI,IAAI,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAA;IACvE,CAAC,CAAA;AACH,CAAC;AAeK,SAAU,IAAI;IAClB,OAAO,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;QACrC,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,MAAM,CAAC,SAAS,CAAC,CAAA;IACzC,CAAC,CAAA;AACH,CAAC;AAgBK,SAAU,aAAa;IAC3B,OAAO,IAAI,EAAE,CAAA;AACf,CAAC;AAKD,KAAK,UAAU,OAAO,CACpB,MAAkC,EAClC,OAAgC,EAChC,GAAc;IAEd,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;QACjC,MAAM,cAAc,CAClB,uFAAuF,EACvF,qBAAqB,CACtB,CAAA;IACH,CAAC;IACD,MAAM,KAAK,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA,CAAA,EAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAA;IAC1F,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;IACnF,OAAO,GAAG,KAAK,CAAA,CAAA,EAAI,SAAS,EAAE,CAAA;AAChC,CAAC;AAeM,KAAK,UAAU,kBAAkB,CACtC,EAAuB,EACvB,MAAc,EACd,UAAiE,EACjE,UAAkC,EAClC,OAAgC;IAEhC,QAAQ,CAAC,EAAE,CAAC,CAAA;IACZ,YAAY,CAAC,MAAM,CAAC,CAAA;IAEpB,UAAU,GAAG,IAAI,eAAe,CAAC,UAAU,CAAC,CAAA;IAE5C,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,YAAY,CAAC,UAAU,CAAC,CAAA;IAC7C,gBAAgB,CAAC,GAAG,EAAE,kBAAkB,CAAC,CAAA;IAEzC,UAAU,CAAC,GAAG,CAAC,WAAW,EAAE,MAAM,CAAC,SAAS,CAAC,CAAA;IAE7C,MAAM,GAAG,GAAG,SAAS,EAAE,GAAG,YAAY,CAAC,MAAM,CAAC,CAAA;IAC9C,MAAM,MAAM,GAA8B;QACxC,GAAG,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;QAC3C,GAAG,EAAE,WAAW,EAAE;QAClB,GAAG,EAAE,EAAE,CAAC,MAAM;QACd,GAAG,EAAE,GAAG,GAAG,EAAE;QACb,GAAG,EAAE,GAAG;QACR,GAAG,EAAE,GAAG;QACR,GAAG,EAAE,MAAM,CAAC,SAAS;KACtB,CAAA;IAED,IAAI,QAAkB,CAAA;IACtB,IACE,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,IAC1B,CAAC,QAAQ,GAAG,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAC1C,QAAQ,CAAC,MAAM,GAAG,CAAC,EACnB,CAAC;QACD,MAAM,CAAC,QAAQ,GAAG,QAAQ,CAAA;IAC5B,CAAC;IAED,CAAC;QACC,IAAI,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;QACrC,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;YACnB,MAAM,CAAC,OAAO,GAAG,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,CAAA;YAEpC,YAAY,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,EAAE,qBAAqB,CAAC,CAAA;QAC3D,CAAC;IACH,CAAC;IAED,CAAC;QACC,IAAI,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;QACpC,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;YACnB,IAAI,CAAC;gBACH,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;YACnC,CAAC,CAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,GAAG,CAAC,gDAAgD,EAAE,WAAW,EAAE,KAAK,CAAC,CAAA;YACjF,CAAC;YAED,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;gBACjC,MAAM,cAAc,CAClB,2DAA2D,EAC3D,qBAAqB,CACtB,CAAA;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAED,CAAC;QACC,IAAI,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAA;QACnD,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;YACnB,IAAI,CAAC;gBACH,MAAM,CAAC,qBAAqB,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;YAClD,CAAC,CAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,GAAG,CACP,+DAA+D,EAC/D,WAAW,EACX,KAAK,CACN,CAAA;YACH,CAAC;YAED,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,qBAAqB,CAAC,EAAE,CAAC;gBACjD,MAAM,cAAc,CAClB,yEAAyE,EACzE,qBAAqB,CACtB,CAAA;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAED,MAAM,MAAM,GAAG;QACb,GAAG,EAAE,QAAQ,CAAC,GAAG,CAAC;QAClB,GAAG,EAAE,qBAAqB;QAC1B,GAAG;KACJ,CAAA;IAED,OAAO,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;IAE5C,OAAO,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAC,CAAA;AACrC,CAAC;AAED,IAAI,QAAiC,CAAA;AAErC,KAAK,UAAU,oBAAoB,CAAC,GAAc;IAChD,MAAM,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,GAAG,CAAC,CAAA;IAC1E,MAAM,GAAG,GAAG;QAAE,GAAG;QAAE,CAAC;QAAE,CAAC;QAAE,CAAC;QAAE,CAAC;QAAE,GAAG;IAAA,CAAE,CAAA;IACpC,QAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAA;IACtB,OAAO,GAAG,CAAA;AACZ,CAAC;AAKD,KAAK,UAAU,SAAS,CAAC,GAAc;IACrC,QAAQ,KAAK,IAAI,OAAO,EAAE,CAAA;IAC1B,OAAO,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,oBAAoB,CAAC,GAAG,CAAC,CAAA;AACvD,CAAC;AAGD,MAAM,QAAQ,GAA2D,GAAG,CAAC,KAAK,GAE9E,CAAC,GAAG,EAAE,IAAI,EAAE,CAAG,CAAD,EAAI,CAAC,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,GACnC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;IACZ,IAAI,CAAC;QACH,OAAO,IAAI,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,CAAA;IAC3B,CAAC,CAAC,OAAM,CAAC;QACP,OAAO,IAAI,CAAA;IACb,CAAC;AACH,CAAC,CAAA;AAKC,SAAU,aAAa,CAAC,GAAQ,EAAE,YAAiC;IACvE,IAAI,YAAY,IAAI,GAAG,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;QAC9C,MAAM,GAAG,CAAC,oCAAoC,EAAE,sBAAsB,EAAE,GAAG,CAAC,CAAA;IAC9E,CAAC;IAED,IAAI,GAAG,CAAC,QAAQ,KAAK,QAAQ,IAAI,GAAG,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;QAC1D,MAAM,GAAG,CAAC,0CAA0C,EAAE,0BAA0B,EAAE,GAAG,CAAC,CAAA;IACxF,CAAC;AACH,CAAC;AAED,SAAS,gBAAgB,CACvB,KAAc,EACd,QAAmC,EACnC,YAAiC,EACjC,YAAiC;IAEjC,IAAI,GAAe,CAAA;IACnB,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,CAAC,CAAC,GAAG,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;QAC1D,MAAM,GAAG,CACP,CAAA,uDAAA,EAA0D,YAAY,CAAC,CAAC,CAAC,CAAA,0BAAA,EAA6B,QAAQ,CAAA,CAAA,CAAG,CAAC,CAAC,CAAC,CAAA,IAAA,EAAO,QAAQ,CAAA,CAAA,CAAG,EAAE,EACxI,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,uBAAuB,CAAC,CAAC,CAAC,uBAAuB,EACvE;YAAE,SAAS,EAAE,YAAY,CAAC,CAAC,CAAC,CAAA,sBAAA,EAAyB,QAAQ,EAAE,CAAC,CAAC,CAAC,QAAQ;QAAA,CAAE,CAC7E,CAAA;IACH,CAAC;IAED,aAAa,CAAC,GAAG,EAAE,YAAY,CAAC,CAAA;IAEhC,OAAO,GAAG,CAAA;AACZ,CAAC;AAWK,SAAU,eAAe,CAC7B,EAAuB,EACvB,QAAmC,EACnC,YAAiC,EACjC,YAAiC;IAEjC,IAAI,YAAY,IAAI,EAAE,CAAC,qBAAqB,IAAI,QAAQ,IAAI,EAAE,CAAC,qBAAqB,EAAE,CAAC;QACrF,OAAO,gBAAgB,CACrB,EAAE,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAClC,QAAQ,EACR,YAAY,EACZ,YAAY,CACb,CAAA;IACH,CAAC;IAED,OAAO,gBAAgB,CAAC,EAAE,CAAC,QAAQ,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,YAAY,CAAC,CAAA;AAC7E,CAAC;AAmBM,KAAK,UAAU,0BAA0B,CAC9C,EAAuB,EACvB,MAAc,EACd,oBAAgC,EAChC,UAAiE,EACjE,OAA2C;IAE3C,QAAQ,CAAC,EAAE,CAAC,CAAA;IACZ,YAAY,CAAC,MAAM,CAAC,CAAA;IAEpB,MAAM,GAAG,GAAG,eAAe,CACzB,EAAE,EACF,uCAAuC,EACvC,MAAM,CAAC,yBAAyB,EAChC,OAAO,EAAE,CAAC,qBAAqB,CAAC,KAAK,IAAI,CAC1C,CAAA;IAED,MAAM,IAAI,GAAG,IAAI,eAAe,CAAC,UAAU,CAAC,CAAA;IAC5C,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,MAAM,CAAC,SAAS,CAAC,CAAA;IAEvC,MAAM,OAAO,GAAG,cAAc,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;IAChD,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,kBAAkB,CAAC,CAAA;IAEzC,IAAI,OAAO,EAAE,IAAI,KAAK,SAAS,EAAE,CAAC;QAChC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;QACxB,MAAM,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,OAAO,EAAE,MAAM,CAAC,CAAA;IACnD,CAAC;IAED,MAAM,QAAQ,GAAG,MAAM,oBAAoB,CACzC,EAAE,EACF,MAAM,EACN,oBAAoB,EACpB,GAAG,EACH,IAAI,EACJ,OAAO,EACP,OAAO,CACR,CAAA;IACD,OAAO,EAAE,IAAI,EAAE,UAAU,CAAC,QAAQ,CAAC,CAAA;IACnC,OAAO,QAAQ,CAAA;AACjB,CAAC;AAmCD,MAAM,WAAW;KACf,MAAO,CAA6B;KACpC,UAAW,CAAW;KACtB,SAAU,CAAW;KACrB,SAAU,CAAQ;KAClB,eAAgB,CAA0B;KAC1C,GAAI,CAAsB;KAC1B,GAAI,CAAS;IAEb,YACE,MAAsC,EACtC,OAAsB,EACtB,OAAgC,CAAA;QAEhC,gBAAgB,CAAC,OAAO,EAAE,UAAU,EAAE,mBAAmB,CAAC,CAAA;QAC1D,eAAe,CAAC,OAAO,EAAE,SAAS,EAAE,kBAAkB,CAAC,CAAA;QAEvD,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC;YACnC,MAAM,cAAc,CAAC,2CAA2C,EAAE,qBAAqB,CAAC,CAAA;QAC1F,CAAC;QAED,IAAI,EAAC,eAAgB,GAAG,OAAO,EAAE,CAAC,eAAe,CAAC,CAAA;QAClD,IAAI,EAAC,SAAU,GAAG,YAAY,CAAC,MAAM,CAAC,CAAA;QACtC,IAAI,EAAC,UAAW,GAAG,OAAO,CAAC,UAAU,CAAA;QACrC,IAAI,EAAC,SAAU,GAAG,OAAO,CAAC,SAAS,CAAA;QACnC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;IACnB,CAAC;KAED,GAAI,CAAC,GAAW;QACd,IAAI,EAAC,GAAI,KAAK,IAAI,GAAG,EAAE,CAAA;QACvB,IAAI,IAAI,GAAG,IAAI,EAAC,GAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;QAC7B,IAAI,IAAI,EAAE,CAAC;YACT,IAAI,EAAC,GAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;YACrB,IAAI,EAAC,GAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,CAAA;QAC1B,CAAC;QACD,OAAO,IAAI,CAAA;IACb,CAAC;KAED,GAAI,CAAC,GAAW,EAAE,GAAW;QAC3B,IAAI,EAAC,GAAI,KAAK,IAAI,GAAG,EAAE,CAAA;QACvB,IAAI,EAAC,GAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;QACrB,IAAI,IAAI,EAAC,GAAI,CAAC,IAAI,KAAK,GAAG,EAAE,CAAC;YAC3B,IAAI,EAAC,GAAI,CAAC,MAAM,CAAC,IAAI,EAAC,GAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,KAAM,CAAC,CAAA;QAClD,CAAC;QACD,IAAI,EAAC,GAAI,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAA;IACzB,CAAC;IAED,KAAK,CAAC,mBAAmB,GAAA;QACvB,IAAI,CAAC,IAAI,EAAC,GAAI,EAAE,CAAC;YACf,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,EAAC,SAAU,CAAC,CAAA;YACjE,IAAI,UAAqB,CAAA;YACzB,OAAQ,GAAG,CAAC,GAAG,EAAE,CAAC;gBAChB,KAAK,IAAI;oBACP,UAAU,GAAG;wBAAE,GAAG,EAAE,GAAG,CAAC,GAAG;wBAAE,GAAG,EAAE,GAAG,CAAC,GAAG;wBAAE,CAAC,EAAE,GAAG,CAAC,CAAC;wBAAE,CAAC,EAAE,GAAG,CAAC,CAAC;oBAAA,CAAE,CAAA;oBAC/D,MAAK;gBACP,KAAK,KAAK;oBACR,UAAU,GAAG;wBAAE,GAAG,EAAE,GAAG,CAAC,GAAG;wBAAE,GAAG,EAAE,GAAG,CAAC,GAAG;wBAAE,CAAC,EAAE,GAAG,CAAC,CAAC;oBAAA,CAAE,CAAA;oBACrD,MAAK;gBACP,KAAK,KAAK;oBACR,UAAU,GAAG;wBAAE,CAAC,EAAE,GAAG,CAAC,CAAC;wBAAE,GAAG,EAAE,GAAG,CAAC,GAAG;wBAAE,CAAC,EAAE,GAAG,CAAC,CAAC;oBAAA,CAAE,CAAA;oBACjD,MAAK;gBACP;oBACE,MAAM,IAAI,yBAAyB,CAAC,iBAAiB,EAAE;wBAAE,KAAK,EAAE;4BAAE,GAAG;wBAAA,CAAE;oBAAA,CAAE,CAAC,CAAA;YAC9E,CAAC;YAED,IAAI,EAAC,GAAI,KAAK,IAAI,CAChB,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC;gBAAE,IAAI,EAAE,SAAS;YAAA,CAAE,EAAE,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CACjF,CAAA;QACH,CAAC;QAED,OAAO,IAAI,EAAC,GAAI,CAAA;IAClB,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,GAAQ,EAAE,OAAgB,EAAE,GAAW,EAAE,WAAoB,EAAA;QAC1E,IAAI,EAAC,MAAO,KAAK;YACf,GAAG,EAAE,QAAQ,CAAC,IAAI,EAAC,UAAW,CAAC;YAC/B,GAAG,EAAE,UAAU;YACf,GAAG,EAAE,MAAM,SAAS,CAAC,IAAI,EAAC,SAAU,CAAC;SACtC,CAAA;QAED,MAAM,KAAK,GAAG,IAAI,EAAC,GAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QAEnC,MAAM,GAAG,GAAG,SAAS,EAAE,GAAG,IAAI,EAAC,SAAU,CAAA;QACzC,MAAM,OAAO,GAAG;YACd,GAAG,EAAE,GAAG;YACR,GAAG,EAAE,WAAW,EAAE;YAClB,GAAG;YACH,KAAK;YACL,GAAG,EAAE,GAAG,GAAG,CAAC,MAAM,GAAG,GAAG,CAAC,QAAQ,EAAE;YACnC,GAAG,EAAE,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;SAC7F,CAAA;QAED,IAAI,EAAC,eAAgB,EAAE,CAAC,IAAI,EAAC,MAAO,EAAE,OAAO,CAAC,CAAA;QAE9C,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,OAAO,CAAC,IAAI,EAAC,MAAO,EAAE,OAAO,EAAE,IAAI,EAAC,UAAW,CAAC,CAAC,CAAA;IAC7E,CAAC;IAED,UAAU,CAAC,QAAkB,EAAA;QAC3B,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAA;YAChD,IAAI,KAAK,EAAE,CAAC;gBACV,IAAI,EAAC,GAAI,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;YAChD,CAAC;QACH,CAAC,CAAC,OAAM,CAAC,CAAC;IACZ,CAAC;CACF;AAQK,SAAU,gBAAgB,CAAC,GAAY;IAC3C,IAAI,GAAG,YAAY,6BAA6B,EAAE,CAAC;QACjD,MAAM,EAAE,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,GAAG,CAAC,KAAK,CAAA;QAC1C,OAAO,AACL,MAAM,KAAK,CAAC,IAAI,SAAS,CAAC,MAAM,KAAK,MAAM,IAAI,SAAS,CAAC,UAAU,CAAC,KAAK,KAAK,gBAAgB,CAC/F,CAAA;IACH,CAAC;IAED,IAAI,GAAG,YAAY,iBAAiB,EAAE,CAAC;QACrC,OAAO,GAAG,CAAC,KAAK,KAAK,gBAAgB,CAAA;IACvC,CAAC;IAED,OAAO,KAAK,CAAA;AACd,CAAC;AA2BK,SAAU,IAAI,CAClB,MAAsC,EACtC,OAAsB,EACtB,OAAgC;IAEhC,OAAO,IAAI,WAAW,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC,CAAA;AAClD,CAAC;AAqCK,MAAO,iBAAkB,SAAQ,KAAK;IAIjC,KAAK,CAAuC;IAErD,IAAI,CAA4B;IAKhC,KAAK,CAAQ;IAKb,MAAM,CAAQ;IAMd,iBAAiB,CAAS;IAM1B,QAAQ,CAAW;IAKnB,YACE,OAAe,EACf,OAGC,CAAA;QAED,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;QACvB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAA;QACjC,IAAI,CAAC,IAAI,GAAG,mBAAmB,CAAA;QAC/B,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAA;QAC1B,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAA;QAChC,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAA;QACrC,IAAI,CAAC,iBAAiB,GAAG,OAAO,CAAC,KAAK,CAAC,iBAAiB,CAAA;QACxD,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,UAAU,EAAE;YAAE,UAAU,EAAE,KAAK;YAAE,KAAK,EAAE,OAAO,CAAC,QAAQ;QAAA,CAAE,CAAC,CAAA;QAGvF,KAAK,CAAC,iBAAiB,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAA;IACnD,CAAC;CACF;AAcK,MAAO,0BAA2B,SAAQ,KAAK;IAI1C,KAAK,CAAiB;IAE/B,IAAI,CAAqC;IAKzC,KAAK,CAAQ;IAMb,iBAAiB,CAAS;IAK1B,YACE,OAAe,EACf,OAEC,CAAA;QAED,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;QACvB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAA;QACjC,IAAI,CAAC,IAAI,GAAG,4BAA4B,CAAA;QACxC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAA;QAC1B,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAE,CAAA;QACxC,IAAI,CAAC,iBAAiB,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,mBAAmB,CAAC,IAAI,SAAS,CAAA;QAG5E,KAAK,CAAC,iBAAiB,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAA;IACnD,CAAC;CACF;AAeK,MAAO,6BAA8B,SAAQ,KAAK;IAI7C,KAAK,CAA4B;IAE1C,IAAI,CAAmC;IAMvC,QAAQ,CAAU;IAKlB,MAAM,CAAQ;IAKd,YAAY,OAAe,EAAE,OAAkE,CAAA;QAC7F,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;QACvB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAA;QACjC,IAAI,CAAC,IAAI,GAAG,0BAA0B,CAAA;QACtC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAA;QAC1B,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAA;QACrC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAA;QAChC,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,UAAU,EAAE;YAAE,UAAU,EAAE,KAAK;QAAA,CAAE,CAAC,CAAA;QAG9D,KAAK,CAAC,iBAAiB,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAA;IACnD,CAAC;CACF;AAoED,MAAM,UAAU,GAAG,2CAA2C,CAAA;AAC9D,MAAM,YAAY,GAAG,sCAAsC,CAAA;AAC3D,MAAM,WAAW,GAAG,yBAAyB,CAAA;AAE7C,MAAM,kBAAkB,GAAG,GAAG,GAAG,UAAU,GAAG,YAAY,GAAG,WAAW,CAAA;AACxE,MAAM,YAAY,GAAG,GAAG,GAAG,UAAU,GAAG,aAAa,GAAG,UAAU,GAAG,GAAG,CAAA;AAExE,MAAM,QAAQ,GAAG,IAAI,MAAM,CAAC,WAAW,GAAG,UAAU,GAAG,UAAU,CAAC,CAAA;AAClE,MAAM,aAAa,GAAG,IAAI,MAAM,CAAC,UAAU,GAAG,kBAAkB,GAAG,aAAa,CAAC,CAAA;AACjF,MAAM,eAAe,GAAG,IAAI,MAAM,CAAC,UAAU,GAAG,YAAY,GAAG,aAAa,CAAC,CAAA;AAC7E,MAAM,cAAc,GAAG,IAAI,MAAM,CAAC,IAAI,GAAG,YAAY,GAAG,mBAAmB,CAAC,CAAA;AAE5E,SAAS,8BAA8B,CACrC,QAAkB;IAElB,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE,CAAC;QACzC,MAAM,cAAc,CAAC,4CAA4C,EAAE,oBAAoB,CAAC,CAAA;IAC1F,CAAC;IAED,MAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAA;IACvD,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;QACpB,OAAO,SAAS,CAAA;IAClB,CAAC;IAED,MAAM,UAAU,GAA+B,EAAE,CAAA;IAEjD,IAAI,IAAI,GAAuB,MAAM,CAAA;IACrC,MAAO,IAAI,CAAE,CAAC;QACZ,IAAI,KAAK,GAA4B,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;QACzD,MAAM,MAAM,GAAG,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC,WAAW,EAAuB,CAAA;QAC9D,IAAI,GAAG,KAAK,EAAE,CAAC,GAAG,CAAC,CAAA;QACnB,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO,SAAS,CAAA;QAClB,CAAC;QAED,MAAM,UAAU,GAA2C,CAAA,CAAE,CAAA;QAC7D,IAAI,OAA2B,CAAA;QAE/B,MAAO,IAAI,CAAE,CAAC;YACZ,IAAI,GAAW,CAAA;YACf,IAAI,KAAa,CAAA;YACjB,IAAI,AAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAE,CAAC;;gBACvC,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,GAAG,KAAK,CAAA;gBAC7B,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;oBACzB,IAAI,CAAC;wBACH,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAA,CAAA,EAAI,KAAK,CAAA,CAAA,CAAG,CAAC,CAAA;oBAClC,CAAC,CAAC,OAAM,CAAC,CAAC;gBACZ,CAAC;gBAED,UAAU,CAAC,GAAG,CAAC,WAAW,EAAuB,CAAC,GAAG,KAAK,CAAA;gBAC1D,SAAQ;YACV,CAAC;YAED,IAAI,AAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,CAAE,CAAC;;gBACzC,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,GAAG,KAAK,CAAA;gBAE7B,UAAU,CAAC,GAAG,CAAC,WAAW,EAAuB,CAAC,GAAG,KAAK,CAAA;gBAC1D,SAAQ;YACV,CAAC;YAED,IAAK,AAAD,KAAM,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,CAAE,CAAC;gBACzC,IAAI,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,EAAE,CAAC;oBACnC,MAAK;gBACP,CAAC;;gBACA,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,GAAG,KAAK,CAAA;gBAC1B,MAAK;YACP,CAAC;YAED,OAAO,SAAS,CAAA;QAClB,CAAC;QAED,MAAM,SAAS,GAA6B;YAAE,MAAM;YAAE,UAAU;QAAA,CAAE,CAAA;QAClE,IAAI,OAAO,EAAE,CAAC;YAEZ,SAAS,CAAC,OAAO,GAAG,OAAO,CAAA;QAC7B,CAAC;QACD,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;IAC5B,CAAC;IAED,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;QACvB,OAAO,SAAS,CAAA;IAClB,CAAC;IAED,OAAO,UAAU,CAAA;AACnB,CAAC;AAkBM,KAAK,UAAU,kCAAkC,CACtD,EAAuB,EACvB,MAAc,EACd,QAAkB;IAElB,QAAQ,CAAC,EAAE,CAAC,CAAA;IACZ,YAAY,CAAC,MAAM,CAAC,CAAA;IAEpB,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE,CAAC;QACzC,MAAM,cAAc,CAAC,4CAA4C,EAAE,oBAAoB,CAAC,CAAA;IAC1F,CAAC;IAED,6BAA6B,CAAC,QAAQ,CAAC,CAAA;IAEvC,MAAM,mBAAmB,CAAC,QAAQ,EAAE,GAAG,EAAE,uCAAuC,CAAC,CAAA;IAEjF,sBAAsB,CAAC,QAAQ,CAAC,CAAA;IAChC,MAAM,IAAI,GAAG,MAAM,mBAAmB,CAAyC,QAAQ,CAAC,CAAA;IAExF,YAAY,CAAC,IAAI,CAAC,WAAW,EAAE,wCAAwC,EAAE,gBAAgB,EAAE;QACzF,IAAI,EAAE,IAAI;KACX,CAAC,CAAA;IAEF,IAAI,SAAS,GACX,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAA;IACrF,YAAY,CAAC,SAAS,EAAE,KAAK,EAAE,uCAAuC,EAAE,gBAAgB,EAAE;QACxF,IAAI,EAAE,IAAI;KACX,CAAC,CAAA;IACF,IAAI,CAAC,UAAU,GAAG,SAAS,CAAA;IAE3B,OAAO,IAAI,CAAA;AACb,CAAC;AAeD,KAAK,UAAU,2BAA2B,CAAC,QAAkB;IAC3D,IAAI,QAAQ,CAAC,MAAM,GAAG,GAAG,IAAI,QAAQ,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;QACnD,sBAAsB,CAAC,QAAQ,CAAC,CAAA;QAChC,qBAAqB,CAAC,QAAQ,CAAC,CAAA;QAC/B,IAAI,CAAC;YACH,MAAM,IAAI,GAAc,MAAM,QAAQ,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAA;YACrD,IAAI,YAAY,CAAc,IAAI,CAAC,IAAI,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;gBAC3F,OAAO,IAAI,CAAA;YACb,CAAC;QACH,CAAC,CAAC,OAAM,CAAC,CAAC;IACZ,CAAC;IACD,OAAO,SAAS,CAAA;AAClB,CAAC;AAED,KAAK,UAAU,mBAAmB,CAAC,QAAkB,EAAE,QAAgB,EAAE,KAAa;IACpF,IAAI,QAAQ,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;QACjC,IAAI,GAA4B,CAAA;QAChC,IAAK,AAAD,GAAI,GAAG,MAAM,2BAA2B,CAAC,QAAQ,CAAC,CAAC,CAAE,CAAC;YACxD,MAAM,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,CAAA;YAC7B,MAAM,IAAI,iBAAiB,CAAC,qDAAqD,EAAE;gBACjF,KAAK,EAAE,GAAG;gBACV,QAAQ;aACT,CAAC,CAAA;QACJ,CAAC;QACD,MAAM,GAAG,CACP,CAAA,4BAAA,EAA+B,KAAK,CAAA,uCAAA,CAAyC,EAC7E,uBAAuB,EACvB,QAAQ,CACT,CAAA;IACH,CAAC;AACH,CAAC;AAED,SAAS,UAAU,CAAC,MAAkB;IACpC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;QACzB,MAAM,cAAc,CAAC,0CAA0C,EAAE,qBAAqB,CAAC,CAAA;IACzF,CAAC;AACH,CAAC;AAED,KAAK,UAAU,eAAe,CAC5B,WAAmB,EACnB,MAAc,EACd,GAAQ,EACR,OAAiB,EACjB,IAAmC,EACnC,OAAyC;IAEzC,YAAY,CAAC,WAAW,EAAE,eAAe,CAAC,CAAA;IAE1C,IAAI,CAAC,CAAC,GAAG,YAAY,GAAG,CAAC,EAAE,CAAC;QAC1B,MAAM,cAAc,CAAC,kCAAkC,EAAE,oBAAoB,CAAC,CAAA;IAChF,CAAC;IAED,aAAa,CAAC,GAAG,EAAE,OAAO,EAAE,CAAC,qBAAqB,CAAC,KAAK,IAAI,CAAC,CAAA;IAE7D,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC,CAAA;IAEjC,IAAI,OAAO,EAAE,IAAI,EAAE,CAAC;QAClB,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;QACxB,MAAM,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,OAAO,EAAE,MAAM,CAAC,WAAW,EAAE,EAAE,WAAW,CAAC,CAAA;IAC9E,CAAC;IAED,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,GAAG,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAA,CAAA,EAAI,WAAW,EAAE,CAAC,CAAA;IAEzF,MAAM,QAAQ,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC,WAAW,CAAC,IAAI,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE;QACjE,IAAI;QACJ,OAAO,EAAE,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;QAC9C,MAAM;QACN,QAAQ,EAAE,QAAQ;QAClB,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS;KAC7D,CAAC,CAAA;IACF,OAAO,EAAE,IAAI,EAAE,UAAU,CAAC,QAAQ,CAAC,CAAA;IACnC,OAAO,QAAQ,CAAA;AACjB,CAAC;AAqBM,KAAK,UAAU,wBAAwB,CAC5C,WAAmB,EACnB,MAAc,EACd,GAAQ,EACR,OAAiB,EACjB,IAAmC,EACnC,OAAyC;IAEzC,MAAM,QAAQ,GAAG,MAAM,eAAe,CAAC,WAAW,EAAE,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,CAAA;IACxF,6BAA6B,CAAC,QAAQ,CAAC,CAAA;IACvC,OAAO,QAAQ,CAAA;AACjB,CAAC;AAuBM,KAAK,UAAU,eAAe,CACnC,EAAuB,EACvB,MAAc,EACd,WAAmB,EACnB,OAAgC;IAEhC,QAAQ,CAAC,EAAE,CAAC,CAAA;IACZ,YAAY,CAAC,MAAM,CAAC,CAAA;IAEpB,MAAM,GAAG,GAAG,eAAe,CACzB,EAAE,EACF,mBAAmB,EACnB,MAAM,CAAC,yBAAyB,EAChC,OAAO,EAAE,CAAC,qBAAqB,CAAC,KAAK,IAAI,CAC1C,CAAA;IAED,MAAM,OAAO,GAAG,cAAc,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;IAChD,IAAI,MAAM,CAAC,4BAA4B,EAAE,CAAC;QACxC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,iBAAiB,CAAC,CAAA;IAC1C,CAAC,MAAM,CAAC;QACN,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,kBAAkB,CAAC,CAAA;QACzC,OAAO,CAAC,MAAM,CAAC,QAAQ,EAAE,iBAAiB,CAAC,CAAA;IAC7C,CAAC;IAED,OAAO,eAAe,CAAC,WAAW,EAAE,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE;QAC7D,GAAG,OAAO;QACV,CAAC,SAAS,CAAC,EAAE,YAAY,CAAC,MAAM,CAAC;KACC,CAAC,CAAA;AACvC,CAAC;AAqCD,IAAI,OAA0E,CAAA;AAS9E,SAAS,YAAY,CACnB,EAAuB,EACvB,IAAU,EACV,GAAW,EACX,KAAsB;IAEtB,OAAO,KAAK,IAAI,OAAO,EAAE,CAAA;IACzB,OAAO,CAAC,GAAG,CAAC,EAAE,EAAE;QACd,IAAI;QACJ,GAAG;QACH,IAAI,GAAG,IAAA;YACL,OAAO,SAAS,EAAE,GAAG,IAAI,CAAC,GAAG,CAAA;QAC/B,CAAC;KACF,CAAC,CAAA;IAEF,IAAI,KAAK,EAAE,CAAC;QACV,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE;YAAE,IAAI,EAAE,eAAe,CAAC,IAAI,CAAC;YAAE,GAAG;QAAA,CAAE,CAAC,CAAA;IAC5D,CAAC;AACH,CAAC;AAED,SAAS,gBAAgB,CAAC,KAAc;IACtC,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;QAChD,OAAO,KAAK,CAAA;IACd,CAAC;IAED,IAAI,CAAC,CAAC,KAAK,IAAI,KAAK,CAAC,IAAI,OAAO,KAAK,CAAC,GAAG,KAAK,QAAQ,IAAI,SAAS,EAAE,GAAG,KAAK,CAAC,GAAG,IAAI,GAAG,EAAE,CAAC;QACzF,OAAO,KAAK,CAAA;IACd,CAAC;IAED,IACE,CAAC,CAAC,MAAM,IAAI,KAAK,CAAC,IAClB,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,IACzB,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAC/B,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,YAAY,CAAC,EAC1D,CAAC;QACD,OAAO,KAAK,CAAA;IACd,CAAC;IAED,OAAO,IAAI,CAAA;AACb,CAAC;AAED,SAAS,cAAc,CAAC,EAAuB,EAAE,KAA+B;IAC9E,OAAO,EAAE,MAAM,CAAC,EAAE,CAAC,CAAA;IACnB,OAAO,KAAK,EAAE,IAAI,CAAA;IAClB,OAAO,KAAK,EAAE,GAAG,CAAA;AACnB,CAAC;AAED,KAAK,UAAU,gCAAgC,CAC7C,EAAuB,EACvB,OAAmE,EACnE,MAAkC;IAElC,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,MAAM,CAAA;IAC3B,oBAAoB,CAAC,MAAM,CAAC,CAAA;IAE5B,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,EAAE,CAAC,IAAI,gBAAgB,CAAC,OAAO,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC;QAChE,YAAY,CAAC,EAAE,EAAE,OAAO,EAAE,CAAC,SAAS,CAAC,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAA;IACvE,CAAC;IAED,IAAI,IAAU,CAAA;IACd,IAAI,GAAW,CAAA;IAEf,IAAI,OAAO,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC;;QACpB,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,OAAO,CAAC,GAAG,CAAC,EAAE,CAAE,CAAC,CAAA;QACnC,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC;YAEf,cAAc,CAAC,EAAE,EAAE,OAAO,EAAE,CAAC,SAAS,CAAC,CAAC,CAAA;YACxC,OAAO,gCAAgC,CAAC,EAAE,EAAE,OAAO,EAAE,MAAM,CAAC,CAAA;QAC9D,CAAC;IACH,CAAC,MAAM,CAAC;QACN,IAAI,GAAG,MAAM,WAAW,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAA;QAC/D,GAAG,GAAG,CAAC,CAAA;QACP,YAAY,CAAC,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,OAAO,EAAE,CAAC,SAAS,CAAC,CAAC,CAAA;IAC3D,CAAC;IAED,IAAI,GAAW,CAAA;IACf,OAAQ,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QACxB,KAAK,IAAI,CAAC;QACV,KAAK,IAAI;YACP,GAAG,GAAG,KAAK,CAAA;YACX,MAAK;QACP,KAAK,IAAI;YACP,GAAG,GAAG,IAAI,CAAA;YACV,MAAK;QACP,KAAK,IAAI;YACP,GAAG,GAAG,KAAK,CAAA;YACX,MAAK;QACP;YACE,MAAM,IAAI,yBAAyB,CAAC,2BAA2B,EAAE;gBAAE,KAAK,EAAE;oBAAE,GAAG;gBAAA,CAAE;YAAA,CAAE,CAAC,CAAA;IACxF,CAAC;IAED,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE;QAE1C,IAAI,GAAG,CAAC,GAAG,KAAK,GAAG,EAAE,CAAC;YACpB,OAAO,KAAK,CAAA;QACd,CAAC;QAGD,IAAI,GAAG,KAAK,SAAS,IAAI,GAAG,KAAK,GAAG,CAAC,GAAG,EAAE,CAAC;YACzC,OAAO,KAAK,CAAA;QACd,CAAC;QAGD,IAAI,GAAG,CAAC,GAAG,KAAK,SAAS,IAAI,GAAG,KAAK,GAAG,CAAC,GAAG,EAAE,CAAC;YAC7C,OAAO,KAAK,CAAA;QACd,CAAC;QAGD,IAAI,GAAG,CAAC,GAAG,KAAK,SAAS,IAAI,GAAG,CAAC,GAAG,KAAK,KAAK,EAAE,CAAC;YAC/C,OAAO,KAAK,CAAA;QACd,CAAC;QAGD,IAAI,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC,QAAQ,CAAC,KAAK,KAAK,EAAE,CAAC;YAC9C,OAAO,KAAK,CAAA;QACd,CAAC;QAGD,OAAQ,IAAI,EAAE,CAAC;YACb,KAAK,GAAG,KAAK,OAAO,IAAI,GAAG,CAAC,GAAG,KAAK,OAAO,CAAC;YAC5C,KAAK,GAAG,KAAK,OAAO,IAAI,GAAG,CAAC,GAAG,KAAK,OAAO,CAAC;YAC5C,KAAK,GAAG,KAAK,OAAO,IAAI,GAAG,CAAC,GAAG,KAAK,OAAO,CAAC;YAC5C,KAAK,GAAG,KAAK,SAAS,IAAI,GAAG,CAAC,GAAG,KAAK,SAAS,CAAC;YAChD,KAAK,GAAG,KAAK,OAAO,IAAI,GAAG,CAAC,GAAG,KAAK,SAAS;gBAC3C,OAAO,KAAK,CAAA;QAChB,CAAC;QAED,OAAO,IAAI,CAAA;IACb,CAAC,CAAC,CAAA;IAEF,MAAM,EAAE,CAAC,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,UAAU,CAAA;IAErC,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,IAAI,GAAG,IAAI,EAAE,EAAE,CAAC;YAEd,cAAc,CAAC,EAAE,EAAE,OAAO,EAAE,CAAC,SAAS,CAAC,CAAC,CAAA;YACxC,OAAO,gCAAgC,CAAC,EAAE,EAAE,OAAO,EAAE,MAAM,CAAC,CAAA;QAC9D,CAAC;QACD,MAAM,GAAG,CACP,uEAAuE,EACvE,aAAa,EACb;YAAE,MAAM;YAAE,UAAU;YAAE,QAAQ,EAAE,IAAI,GAAG,CAAC,EAAE,CAAC,QAAS,CAAC;QAAA,CAAE,CACxD,CAAA;IACH,CAAC;IAED,IAAI,MAAM,KAAK,CAAC,EAAE,CAAC;QACjB,MAAM,GAAG,CACP,uHAAuH,EACvH,aAAa,EACb;YAAE,MAAM;YAAE,UAAU;YAAE,QAAQ,EAAE,IAAI,GAAG,CAAC,EAAE,CAAC,QAAS,CAAC;QAAA,CAAE,CACxD,CAAA;IACH,CAAC;IAED,OAAO,SAAS,CAAC,GAAG,EAAE,GAAG,CAAC,CAAA;AAC5B,CAAC;AAYM,MAAM,gBAAgB,GAAkB,MAAM,EAAE,CAAA;AAWjD,SAAU,cAAc,CAAC,KAAyB;IACtD,OAAO,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;AACzD,CAAC;AA6BM,KAAK,UAAU,uBAAuB,CAC3C,EAAuB,EACvB,MAAc,EACd,eAAiD,EACjD,QAAkB,EAClB,OAA2B;IAE3B,QAAQ,CAAC,EAAE,CAAC,CAAA;IACZ,YAAY,CAAC,MAAM,CAAC,CAAA;IAEpB,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE,CAAC;QACzC,MAAM,cAAc,CAAC,4CAA4C,EAAE,oBAAoB,CAAC,CAAA;IAC1F,CAAC;IAED,6BAA6B,CAAC,QAAQ,CAAC,CAAA;IAEvC,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;QAC5B,MAAM,GAAG,CACP,sFAAsF,EACtF,uBAAuB,EACvB,QAAQ,CACT,CAAA;IACH,CAAC;IAED,sBAAsB,CAAC,QAAQ,CAAC,CAAA;IAEhC,IAAI,IAAgB,CAAA;IACpB,IAAI,cAAc,CAAC,QAAQ,CAAC,KAAK,iBAAiB,EAAE,CAAC;QACnD,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,MAAM,WAAW,CACvC,MAAM,QAAQ,CAAC,IAAI,EAAE,EACrB,qBAAqB,CAAC,IAAI,CACxB,SAAS,EACT,MAAM,CAAC,4BAA4B,EACnC,EAAE,CAAC,qCAAqC,EACxC,SAAS,CACV,EACD,YAAY,CAAC,MAAM,CAAC,EACpB,iBAAiB,CAAC,MAAM,CAAC,EACzB,OAAO,EAAE,CAAC,UAAU,CAAC,CACtB,CACE,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,CAChE,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,CAAA;QAEnD,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAA;QAC1B,IAAI,GAAG,MAAM,CAAA;IACf,CAAC,MAAM,CAAC;QACN,IAAI,MAAM,CAAC,4BAA4B,EAAE,CAAC;YACxC,MAAM,GAAG,CAAC,gCAAgC,EAAE,qBAAqB,EAAE,QAAQ,CAAC,CAAA;QAC9E,CAAC;QACD,IAAI,GAAG,MAAM,mBAAmB,CAAC,QAAQ,CAAC,CAAA;IAC5C,CAAC;IAED,YAAY,CAAC,IAAI,CAAC,GAAG,EAAE,gCAAgC,EAAE,gBAAgB,EAAE;QAAE,IAAI,EAAE,IAAI;IAAA,CAAE,CAAC,CAAA;IAE1F,OAAQ,eAAe,EAAE,CAAC;QACxB,KAAK,gBAAgB;YACnB,MAAK;QACP;YACE,YAAY,CAAC,eAAe,EAAE,mBAAmB,CAAC,CAAA;YAElD,IAAI,IAAI,CAAC,GAAG,KAAK,eAAe,EAAE,CAAC;gBACjC,MAAM,GAAG,CAAC,iDAAiD,EAAE,yBAAyB,EAAE;oBACtF,QAAQ,EAAE,eAAe;oBACzB,IAAI,EAAE,IAAI;oBACV,SAAS,EAAE,KAAK;iBACjB,CAAC,CAAA;YACJ,CAAC;IACL,CAAC;IAED,OAAO,IAAwB,CAAA;AACjC,CAAC;AAED,KAAK,UAAU,oBAAoB,CACjC,EAAuB,EACvB,MAAc,EACd,oBAAgC,EAChC,GAAQ,EACR,IAAqB,EACrB,OAAgB,EAChB,OAAsE;IAEtE,MAAM,oBAAoB,CAAC,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,CAAC,CAAA;IACrD,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,iDAAiD,CAAC,CAAA;IAE9E,OAAO,CAAC,OAAO,EAAE,CAAC,WAAW,CAAC,IAAI,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE;QACjD,IAAI;QACJ,OAAO,EAAE,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;QAC9C,MAAM,EAAE,MAAM;QACd,QAAQ,EAAE,QAAQ;QAClB,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS;KAC7D,CAAC,CAAA;AACJ,CAAC;AAWD,KAAK,UAAU,oBAAoB,CACjC,EAAuB,EACvB,MAAc,EACd,oBAAgC,EAChC,SAAiB,EACjB,UAA2B,EAC3B,OAAmE;IAEnE,MAAM,GAAG,GAAG,eAAe,CACzB,EAAE,EACF,gBAAgB,EAChB,MAAM,CAAC,yBAAyB,EAChC,OAAO,EAAE,CAAC,qBAAqB,CAAC,KAAK,IAAI,CAC1C,CAAA;IAED,UAAU,CAAC,GAAG,CAAC,YAAY,EAAE,SAAS,CAAC,CAAA;IACvC,MAAM,OAAO,GAAG,cAAc,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;IAChD,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,kBAAkB,CAAC,CAAA;IAEzC,IAAI,OAAO,EAAE,IAAI,KAAK,SAAS,EAAE,CAAC;QAChC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;QACxB,MAAM,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,OAAO,EAAE,MAAM,CAAC,CAAA;IACnD,CAAC;IAED,MAAM,QAAQ,GAAG,MAAM,oBAAoB,CACzC,EAAE,EACF,MAAM,EACN,oBAAoB,EACpB,GAAG,EACH,UAAU,EACV,OAAO,EACP,OAAO,CACR,CAAA;IACD,OAAO,EAAE,IAAI,EAAE,UAAU,CAAC,QAAQ,CAAC,CAAA;IACnC,OAAO,QAAQ,CAAA;AACjB,CAAC;AAmBM,KAAK,UAAU,wBAAwB,CAC5C,EAAuB,EACvB,MAAc,EACd,oBAAgC,EAChC,YAAoB,EACpB,OAAqC;IAErC,QAAQ,CAAC,EAAE,CAAC,CAAA;IACZ,YAAY,CAAC,MAAM,CAAC,CAAA;IAEpB,YAAY,CAAC,YAAY,EAAE,gBAAgB,CAAC,CAAA;IAE5C,MAAM,UAAU,GAAG,IAAI,eAAe,CAAC,OAAO,EAAE,oBAAoB,CAAC,CAAA;IACrE,UAAU,CAAC,GAAG,CAAC,eAAe,EAAE,YAAY,CAAC,CAAA;IAC7C,OAAO,oBAAoB,CACzB,EAAE,EACF,MAAM,EACN,oBAAoB,EACpB,eAAe,EACf,UAAU,EACV,OAAO,CACR,CAAA;AACH,CAAC;AAED,MAAM,aAAa,GAAG,IAAI,OAAO,EAAkC,CAAA;AACnE,MAAM,OAAO,GAAG,IAAI,OAAO,EAAoB,CAAA;AAgBzC,SAAU,yBAAyB,CAAC,GAA0B;IAClE,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;QAClB,OAAO,SAAS,CAAA;IAClB,CAAC;IAED,MAAM,MAAM,GAAG,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;IACrC,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,MAAM,cAAc,CAClB,gFAAgF,EAChF,qBAAqB,CACtB,CAAA;IACH,CAAC;IAED,OAAO,MAAM,CAAA;AACf,CAAC;AAgCM,KAAK,UAAU,iCAAiC,CACrD,EAAuB,EACvB,GAAa,EACb,OAAkC;IAElC,QAAQ,CAAC,EAAE,CAAC,CAAA;IAEZ,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;QACtB,MAAM,cAAc,CAClB,4EAA4E,EAC5E,qBAAqB,CACtB,CAAA;IACH,CAAC;IAED,MAAM,EAAE,CAAC,EAAE,eAAe,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,gBAAgB,EAAE,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,CAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;IAE5F,MAAM,MAAM,GAA+B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAA;IAEjF,IAAI,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;QAChC,MAAM,IAAI,yBAAyB,CAAC,2BAA2B,EAAE;YAAE,KAAK,EAAE;gBAAE,GAAG,EAAE,MAAM,CAAC,GAAG;YAAA,CAAE;QAAA,CAAE,CAAC,CAAA;IAClG,CAAC;IAED,IAAI,GAAe,CAAA;IACnB,GAAG,GAAG,MAAM,gCAAgC,CAAC,EAAE,EAAE,OAAO,EAAE,MAAM,CAAC,CAAA;IACjE,MAAM,oBAAoB,CAAC,eAAe,EAAE,OAAO,EAAE,GAAG,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAA;AACnF,CAAC;AAED,KAAK,UAAU,iCAAiC,CAC9C,EAAuB,EACvB,MAAc,EACd,QAAkB,EAClB,+BAA2E,EAC3E,OAAsC;IAEtC,QAAQ,CAAC,EAAE,CAAC,CAAA;IACZ,YAAY,CAAC,MAAM,CAAC,CAAA;IAEpB,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE,CAAC;QACzC,MAAM,cAAc,CAAC,4CAA4C,EAAE,oBAAoB,CAAC,CAAA;IAC1F,CAAC;IAED,6BAA6B,CAAC,QAAQ,CAAC,CAAA;IAEvC,MAAM,mBAAmB,CAAC,QAAQ,EAAE,GAAG,EAAE,gBAAgB,CAAC,CAAA;IAE1D,sBAAsB,CAAC,QAAQ,CAAC,CAAA;IAChC,MAAM,IAAI,GAAG,MAAM,mBAAmB,CAAmC,QAAQ,CAAC,CAAA;IAElF,YAAY,CAAC,IAAI,CAAC,YAAY,EAAE,yCAAyC,EAAE,gBAAgB,EAAE;QAC3F,IAAI,EAAE,IAAI;KACX,CAAC,CAAA;IAEF,YAAY,CAAC,IAAI,CAAC,UAAU,EAAE,uCAAuC,EAAE,gBAAgB,EAAE;QACvF,IAAI,EAAE,IAAI;KACX,CAAC,CAAA;IAEF,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,EAAuB,CAAA;IAEpE,IAAI,IAAI,CAAC,UAAU,KAAK,MAAM,IAAI,IAAI,CAAC,UAAU,KAAK,QAAQ,EAAE,CAAC;QAC/D,MAAM,IAAI,yBAAyB,CAAC,gCAAgC,EAAE;YAAE,KAAK,EAAE;gBAAE,IAAI,EAAE,IAAI;YAAA,CAAE;QAAA,CAAE,CAAC,CAAA;IAClG,CAAC;IAED,IAAI,IAAI,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;QAClC,IAAI,SAAS,GACX,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAA;QACrF,YAAY,CAAC,SAAS,EAAE,KAAK,EAAE,uCAAuC,EAAE,gBAAgB,EAAE;YACxF,IAAI,EAAE,IAAI;SACX,CAAC,CAAA;QACF,IAAI,CAAC,UAAU,GAAG,SAAS,CAAA;IAC7B,CAAC;IAED,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;QACrC,YAAY,CAAC,IAAI,CAAC,aAAa,EAAE,0CAA0C,EAAE,gBAAgB,EAAE;YAC7F,IAAI,EAAE,IAAI;SACX,CAAC,CAAA;IACJ,CAAC;IAGD,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,IAAI,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAE,CAAC;QAC/D,MAAM,GAAG,CAAC,mDAAmD,EAAE,gBAAgB,EAAE;YAAE,IAAI,EAAE,IAAI;QAAA,CAAE,CAAC,CAAA;IAClG,CAAC;IAED,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;QAChC,YAAY,CAAC,IAAI,CAAC,QAAQ,EAAE,qCAAqC,EAAE,gBAAgB,EAAE;YACnF,IAAI,EAAE,IAAI;SACX,CAAC,CAAA;QAEF,MAAM,cAAc,GAAmC;YAAC,KAAK;YAAE,KAAK;YAAE,KAAK;YAAE,KAAK;YAAE,KAAK;SAAC,CAAA;QAE1F,IAAI,MAAM,CAAC,iBAAiB,KAAK,IAAI,EAAE,CAAC;YACtC,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;QAClC,CAAC;QAED,IAAI,MAAM,CAAC,eAAe,KAAK,SAAS,EAAE,CAAC;YACzC,YAAY,CAAC,MAAM,CAAC,eAAe,EAAE,KAAK,EAAE,0BAA0B,CAAC,CAAA;YACvE,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;QAClC,CAAC;QAED,IAAI,+BAA+B,EAAE,MAAM,EAAE,CAAC;YAC5C,cAAc,CAAC,IAAI,CAAC,GAAG,+BAA+B,CAAC,CAAA;QACzD,CAAC;QAED,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,MAAM,WAAW,CACvC,IAAI,CAAC,QAAQ,EACb,qBAAqB,CAAC,IAAI,CACxB,SAAS,EACT,MAAM,CAAC,4BAA4B,EACnC,EAAE,CAAC,qCAAqC,EACxC,OAAO,CACR,EACD,YAAY,CAAC,MAAM,CAAC,EACpB,iBAAiB,CAAC,MAAM,CAAC,EACzB,OAAO,EAAE,CAAC,UAAU,CAAC,CACtB,CACE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC,CACtD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,CACxC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,CAAA;QAE3D,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACzD,IAAI,MAAM,CAAC,GAAG,KAAK,SAAS,EAAE,CAAC;gBAC7B,MAAM,GAAG,CACP,yEAAyE,EACzE,oBAAoB,EACpB;oBAAE,MAAM;oBAAE,KAAK,EAAE,KAAK;gBAAA,CAAE,CACzB,CAAA;YACH,CAAC;YACD,IAAI,MAAM,CAAC,GAAG,KAAK,MAAM,CAAC,SAAS,EAAE,CAAC;gBACpC,MAAM,GAAG,CACP,0DAA0D,EAC1D,oBAAoB,EACpB;oBAAE,QAAQ,EAAE,MAAM,CAAC,SAAS;oBAAE,MAAM;oBAAE,KAAK,EAAE,KAAK;gBAAA,CAAE,CACrD,CAAA;YACH,CAAC;QACH,CAAC;QAED,IAAI,MAAM,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;YACnC,YAAY,CACV,MAAM,CAAC,SAAS,EAChB,KAAK,EACL,4CAA4C,EAC5C,gBAAgB,EAChB;gBAAE,MAAM;YAAA,CAAE,CACX,CAAA;QACH,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAA;QAC1B,aAAa,CAAC,GAAG,CAAC,IAAI,EAAE,MAAiB,CAAC,CAAA;IAC5C,CAAC;IAED,OAAO,IAAI,CAAA;AACb,CAAC;AAED,SAAS,6BAA6B,CAAC,QAAkB;IACvD,IAAI,UAAkD,CAAA;IACtD,IAAI,AAAC,UAAU,GAAG,8BAA8B,CAAC,QAAQ,CAAC,CAAC,CAAE,CAAC;QAC5D,MAAM,IAAI,6BAA6B,CACrC,uEAAuE,EACvE;YAAE,KAAK,EAAE,UAAU;YAAE,QAAQ;QAAA,CAAE,CAChC,CAAA;IACH,CAAC;AACH,CAAC;AAmBM,KAAK,UAAU,2BAA2B,CAC/C,EAAuB,EACvB,MAAc,EACd,QAAkB,EAClB,OAA2B;IAE3B,OAAO,iCAAiC,CAAC,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,CAAC,CAAA;AACpF,CAAC;AAED,SAAS,wBAAwB,CAC/B,QAAgB,EAChB,MAA+C;IAE/C,IAAI,MAAM,CAAC,MAAM,CAAC,GAAG,KAAK,SAAS,EAAE,CAAC;QACpC,OAAO,gBAAgB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAA;IAC3C,CAAC;IACD,OAAO,MAAM,CAAA;AACf,CAAC;AAED,SAAS,gBAAgB,CAAC,QAAgB,EAAE,MAA+C;IACzF,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC;QACrC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC1C,MAAM,GAAG,CAAC,6CAA6C,EAAE,oBAAoB,EAAE;gBAC7E,QAAQ;gBACR,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,KAAK,EAAE,KAAK;aACb,CAAC,CAAA;QACJ,CAAC;IACH,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,GAAG,KAAK,QAAQ,EAAE,CAAC;QAC1C,MAAM,GAAG,CAAC,6CAA6C,EAAE,oBAAoB,EAAE;YAC7E,QAAQ;YACR,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,KAAK,EAAE,KAAK;SACb,CAAC,CAAA;IACJ,CAAC;IAED,OAAO,MAAM,CAAA;AACf,CAAC;AAED,SAAS,sBAAsB,CAC7B,EAAuB,EACvB,MAA+C;IAE/C,IAAI,MAAM,CAAC,MAAM,CAAC,GAAG,KAAK,SAAS,EAAE,CAAC;QACpC,OAAO,cAAc,CAAC,EAAE,EAAE,MAAM,CAAC,CAAA;IACnC,CAAC;IACD,OAAO,MAAM,CAAA;AACf,CAAC;AAED,SAAS,cAAc,CAAC,EAAuB,EAAE,MAA+C;IAE9F,MAAM,QAAQ,GAAG,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,MAAM,CAAA;IAC3D,IAAI,MAAM,CAAC,MAAM,CAAC,GAAG,KAAK,QAAQ,EAAE,CAAC;QACnC,MAAM,GAAG,CAAC,2CAA2C,EAAE,oBAAoB,EAAE;YAC3E,QAAQ;YACR,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,KAAK,EAAE,KAAK;SACb,CAAC,CAAA;IACJ,CAAC;IACD,OAAO,MAAM,CAAA;AACf,CAAC;AAED,MAAM,OAAO,GAAG,IAAI,OAAO,EAAgC,CAAA;AAC3D,SAAS,KAAK,CAAC,YAA6B;IAC1C,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAA;IACzB,OAAO,YAAY,CAAA;AACrB,CAAC;AAgBM,MAAM,MAAM,GAAkB,MAAM,EAAE,CAAA;AAyBtC,KAAK,UAAU,6BAA6B,CACjD,EAAuB,EACvB,MAAc,EACd,oBAAgC,EAChC,kBAAmC,EACnC,WAAmB,EACnB,YAAoC,EACpC,OAAqC;IAErC,QAAQ,CAAC,EAAE,CAAC,CAAA;IACZ,YAAY,CAAC,MAAM,CAAC,CAAA;IAEpB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,EAAE,CAAC;QACrC,MAAM,cAAc,CAClB,mIAAmI,EACnI,qBAAqB,CACtB,CAAA;IACH,CAAC;IAED,YAAY,CAAC,WAAW,EAAE,eAAe,CAAC,CAAA;IAE1C,MAAM,IAAI,GAAG,qBAAqB,CAAC,kBAAkB,EAAE,MAAM,CAAC,CAAA;IAC9D,IAAI,CAAC,IAAI,EAAE,CAAC;QACV,MAAM,GAAG,CAAC,+CAA+C,EAAE,gBAAgB,CAAC,CAAA;IAC9E,CAAC;IAED,MAAM,UAAU,GAAG,IAAI,eAAe,CAAC,OAAO,EAAE,oBAAoB,CAAC,CAAA;IACrE,UAAU,CAAC,GAAG,CAAC,cAAc,EAAE,WAAW,CAAC,CAAA;IAC3C,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;IAE5B,IAAI,YAAY,KAAK,MAAM,EAAE,CAAC;QAC5B,YAAY,CAAC,YAAY,EAAE,gBAAgB,CAAC,CAAA;QAC5C,UAAU,CAAC,GAAG,CAAC,eAAe,EAAE,YAAY,CAAC,CAAA;IAC/C,CAAC;IAED,OAAO,oBAAoB,CACzB,EAAE,EACF,MAAM,EACN,oBAAoB,EACpB,oBAAoB,EACpB,UAAU,EACV,OAAO,CACR,CAAA;AACH,CAAC;AA4CD,MAAM,aAAa,GAAG;IACpB,GAAG,EAAE,UAAU;IACf,MAAM,EAAE,WAAW;IACnB,SAAS,EAAE,WAAW;IACtB,GAAG,EAAE,iBAAiB;IACtB,GAAG,EAAE,WAAW;IAChB,GAAG,EAAE,QAAQ;IACb,GAAG,EAAE,QAAQ;IACb,KAAK,EAAE,OAAO;IACd,MAAM,EAAE,YAAY;IACpB,GAAG,EAAE,SAAS;IACd,GAAG,EAAE,mBAAmB;IACxB,GAAG,EAAE,aAAa;IAClB,GAAG,EAAE,UAAU;IACf,GAAG,EAAE,cAAc;IACnB,SAAS,EAAE,qBAAqB;CACjC,CAAA;AAED,SAAS,gBAAgB,CACvB,QAAwC,EACxC,MAA+C;IAE/C,KAAK,MAAM,KAAK,IAAI,QAAQ,CAAE,CAAC;QAC7B,IAAI,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,SAAS,EAAE,CAAC;YACvC,MAAM,GAAG,CAAC,CAAA,KAAA,EAAQ,KAAK,CAAA,GAAA,EAAM,aAAa,CAAC,KAAK,CAAC,CAAA,eAAA,CAAiB,EAAE,gBAAgB,EAAE;gBACpF,MAAM,EAAE,MAAM,CAAC,MAAM;aACtB,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;IACD,OAAO,MAAM,CAAA;AACf,CAAC;AAoCM,MAAM,aAAa,GAAkB,MAAM,EAAE,CAAA;AAM7C,MAAM,iBAAiB,GAAkB,MAAM,EAAE,CAAA;AAsCjD,KAAK,UAAU,gCAAgC,CACpD,EAAuB,EACvB,MAAc,EACd,QAAkB,EAClB,OAAiD;IAEjD,IACE,OAAO,OAAO,EAAE,aAAa,KAAK,QAAQ,IAC1C,OAAO,OAAO,EAAE,MAAM,KAAK,QAAQ,IACnC,OAAO,EAAE,cAAc,EACvB,CAAC;QACD,OAAO,sCAAsC,CAC3C,EAAE,EACF,MAAM,EACN,QAAQ,EACR,OAAO,CAAC,aAAa,EACrB,OAAO,CAAC,MAAM,EACd;YACE,CAAC,UAAU,CAAC,EAAE,OAAO,CAAC,UAAU,CAAC;SAClC,CACF,CAAA;IACH,CAAC;IAED,OAAO,sCAAsC,CAAC,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAA;AAC9E,CAAC;AAED,KAAK,UAAU,sCAAsC,CACnD,EAAuB,EACvB,MAAc,EACd,QAAkB,EAClB,aAAwD,EACxD,MAAqD,EACrD,OAAsC;IAEtC,MAAM,wBAAwB,GAAmC,EAAE,CAAA;IAEnE,OAAQ,aAAa,EAAE,CAAC;QACtB,KAAK,SAAS;YACZ,aAAa,GAAG,aAAa,CAAA;YAC7B,MAAK;QACP,KAAK,aAAa;YAChB,MAAK;QACP;YACE,YAAY,CAAC,aAAa,EAAE,0BAA0B,CAAC,CAAA;YACvD,wBAAwB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;IAC1C,CAAC;IAED,MAAM,KAAK,MAAM,CAAC,eAAe,CAAA;IACjC,OAAQ,MAAM,EAAE,CAAC;QACf,KAAK,SAAS;YACZ,MAAM,GAAG,iBAAiB,CAAA;YAC1B,MAAK;QACP,KAAK,iBAAiB;YACpB,MAAK;QACP;YACE,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,mBAAmB,CAAC,CAAA;YAChD,wBAAwB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;IAC9C,CAAC;IAED,MAAM,MAAM,GAAG,MAAM,iCAAiC,CACpD,EAAE,EACF,MAAM,EACN,QAAQ,EACR,wBAAwB,EACxB,OAAO,CACR,CAAA;IAED,YAAY,CAAC,MAAM,CAAC,QAAQ,EAAE,qCAAqC,EAAE,gBAAgB,EAAE;QACrF,IAAI,EAAE,MAAM;KACb,CAAC,CAAA;IAEF,MAAM,MAAM,GAAG,yBAAyB,CAAC,MAAM,CAAE,CAAA;IACjD,IAAI,MAAM,KAAK,iBAAiB,EAAE,CAAC;QACjC,MAAM,GAAG,GAAG,SAAS,EAAE,GAAG,YAAY,CAAC,MAAM,CAAC,CAAA;QAC9C,MAAM,SAAS,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAA;QAC3C,IAAI,MAAM,CAAC,SAAU,GAAG,MAAM,GAAG,GAAG,GAAG,SAAS,EAAE,CAAC;YACjD,MAAM,GAAG,CACP,kEAAkE,EAClE,mBAAmB,EACnB;gBAAE,MAAM;gBAAE,GAAG;gBAAE,SAAS;gBAAE,KAAK,EAAE,WAAW;YAAA,CAAE,CAC/C,CAAA;QACH,CAAC;IACH,CAAC;IAED,IAAI,aAAa,KAAK,aAAa,EAAE,CAAC;QACpC,IAAI,MAAM,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;YAC/B,MAAM,GAAG,CAAC,yCAAyC,EAAE,oBAAoB,EAAE;gBACzE,QAAQ,EAAE,SAAS;gBACnB,MAAM;gBACN,KAAK,EAAE,OAAO;aACf,CAAC,CAAA;QACJ,CAAC;IACH,CAAC,MAAM,IAAI,MAAM,CAAC,KAAK,KAAK,aAAa,EAAE,CAAC;QAC1C,MAAM,GAAG,CAAC,yCAAyC,EAAE,oBAAoB,EAAE;YACzE,QAAQ,EAAE,aAAa;YACvB,MAAM;YACN,KAAK,EAAE,OAAO;SACf,CAAC,CAAA;IACJ,CAAC;IAED,OAAO,MAAM,CAAA;AACf,CAAC;AAED,KAAK,UAAU,sCAAsC,CACnD,EAAuB,EACvB,MAAc,EACd,QAAkB,EAClB,OAA2B;IAE3B,MAAM,MAAM,GAAG,MAAM,iCAAiC,CAAC,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,CAAC,CAAA;IAEhG,MAAM,MAAM,GAAG,yBAAyB,CAAC,MAAM,CAAC,CAAA;IAChD,IAAI,MAAM,EAAE,CAAC;QACX,IAAI,MAAM,CAAC,eAAe,KAAK,SAAS,EAAE,CAAC;YACzC,YAAY,CAAC,MAAM,CAAC,eAAe,EAAE,KAAK,EAAE,0BAA0B,CAAC,CAAA;YACvE,MAAM,GAAG,GAAG,SAAS,EAAE,GAAG,YAAY,CAAC,MAAM,CAAC,CAAA;YAC9C,MAAM,SAAS,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAA;YAC3C,IAAI,MAAM,CAAC,SAAU,GAAG,MAAM,CAAC,eAAe,GAAG,GAAG,GAAG,SAAS,EAAE,CAAC;gBACjE,MAAM,GAAG,CACP,kEAAkE,EAClE,mBAAmB,EACnB;oBAAE,MAAM;oBAAE,GAAG;oBAAE,SAAS;oBAAE,KAAK,EAAE,WAAW;gBAAA,CAAE,CAC/C,CAAA;YACH,CAAC;QACH,CAAC;QAED,IAAI,MAAM,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;YAC/B,MAAM,GAAG,CAAC,yCAAyC,EAAE,oBAAoB,EAAE;gBACzE,QAAQ,EAAE,SAAS;gBACnB,MAAM;gBACN,KAAK,EAAE,OAAO;aACf,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;IAED,OAAO,MAAM,CAAA;AACf,CAAC;AAOM,MAAM,0BAA0B,GAAG,kCAAkC,CAAA;AAMrE,MAAM,mBAAmB,GAAG,2BAA2B,CAAA;AAMvD,MAAM,qBAAqB,GAAG,6BAA6B,CAAA;AAM3D,MAAM,4BAA4B,GAAG,oCAAoC,CAAA;AAOzE,MAAM,qBAAqB,GAAG,6BAA6B,CAAA;AAW3D,MAAM,WAAW,GAAG,mBAAmB,CAAA;AAMvC,MAAM,gBAAgB,GAAG,wBAAwB,CAAA;AAOjD,MAAM,eAAe,GAAG,uBAAuB,CAAA;AAO/C,MAAM,oBAAoB,GAAG,4BAA4B,CAAA;AAOzD,MAAM,uBAAuB,GAAG,+BAA+B,CAAA;AAO/D,MAAM,sBAAsB,GAAG,8BAA8B,CAAA;AAO7D,MAAM,0BAA0B,GAAG,kCAAkC,CAAA;AASrE,MAAM,mBAAmB,GAAG,kCAAkC,CAAA;AAS9D,MAAM,oBAAoB,GAAG,mCAAmC,CAAA;AAOhE,MAAM,yBAAyB,GAAG,wCAAwC,CAAA;AAO1E,MAAM,aAAa,GAAG,4BAA4B,CAAA;AAMlD,MAAM,uBAAuB,GAAG,+BAA+B,CAAA;AAM/D,MAAM,uBAAuB,GAAG,+BAA+B,CAAA;AAEtE,SAAS,YAAY,CAAC,QAAgB,EAAE,MAA+C;IACrF,IAAI,OAAO,MAAM,CAAC,MAAM,CAAC,GAAG,KAAK,QAAQ,IAAI,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,QAAQ,EAAE,CAAC;QAC1F,MAAM,GAAG,CAAC,6CAA6C,EAAE,gBAAgB,EAAE;YACzE,MAAM,EAAE,MAAM,CAAC,MAAM;SACtB,CAAC,CAAA;IACJ,CAAC;IAED,OAAO,MAAM,CAAA;AACf,CAAC;AAsBM,KAAK,UAAU,6BAA6B,CACjD,EAAuB,EACvB,MAAc,EACd,oBAAgC,EAChC,UAAiE,EACjE,OAA8C;IAE9C,QAAQ,CAAC,EAAE,CAAC,CAAA;IACZ,YAAY,CAAC,MAAM,CAAC,CAAA;IAEpB,OAAO,oBAAoB,CACzB,EAAE,EACF,MAAM,EACN,oBAAoB,EACpB,oBAAoB,EACpB,IAAI,eAAe,CAAC,UAAU,CAAC,EAC/B,OAAO,CACR,CAAA;AACH,CAAC;AAuBM,KAAK,UAAU,2BAA2B,CAC/C,EAAuB,EACvB,MAAc,EACd,oBAAgC,EAChC,SAAiB,EACjB,UAAiE,EACjE,OAAmE;IAEnE,QAAQ,CAAC,EAAE,CAAC,CAAA;IACZ,YAAY,CAAC,MAAM,CAAC,CAAA;IAEpB,YAAY,CAAC,SAAS,EAAE,aAAa,CAAC,CAAA;IAEtC,OAAO,oBAAoB,CACzB,EAAE,EACF,MAAM,EACN,oBAAoB,EACpB,SAAS,EACT,IAAI,eAAe,CAAC,UAAU,CAAC,EAC/B,OAAO,CACR,CAAA;AACH,CAAC;AAkBM,KAAK,UAAU,mCAAmC,CACvD,EAAuB,EACvB,MAAc,EACd,QAAkB,EAClB,OAA2B;IAE3B,OAAO,iCAAiC,CAAC,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,CAAC,CAAA;AACpF,CAAC;AAkBM,KAAK,UAAU,gCAAgC,CACpD,EAAuB,EACvB,MAAc,EACd,QAAkB,EAClB,OAA2B;IAE3B,OAAO,iCAAiC,CAAC,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,CAAC,CAAA;AACpF,CAAC;AAyBM,KAAK,UAAU,iBAAiB,CACrC,EAAuB,EACvB,MAAc,EACd,oBAAgC,EAChC,KAAa,EACb,OAAkC;IAElC,QAAQ,CAAC,EAAE,CAAC,CAAA;IACZ,YAAY,CAAC,MAAM,CAAC,CAAA;IAEpB,YAAY,CAAC,KAAK,EAAE,SAAS,CAAC,CAAA;IAE9B,MAAM,GAAG,GAAG,eAAe,CACzB,EAAE,EACF,qBAAqB,EACrB,MAAM,CAAC,yBAAyB,EAChC,OAAO,EAAE,CAAC,qBAAqB,CAAC,KAAK,IAAI,CAC1C,CAAA;IAED,MAAM,IAAI,GAAG,IAAI,eAAe,CAAC,OAAO,EAAE,oBAAoB,CAAC,CAAA;IAC/D,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,CAAA;IAExB,MAAM,OAAO,GAAG,cAAc,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;IAChD,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;IAExB,OAAO,oBAAoB,CAAC,EAAE,EAAE,MAAM,EAAE,oBAAoB,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAA;AAC5F,CAAC;AAeM,KAAK,UAAU,yBAAyB,CAAC,QAAkB;IAChE,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE,CAAC;QACzC,MAAM,cAAc,CAAC,4CAA4C,EAAE,oBAAoB,CAAC,CAAA;IAC1F,CAAC;IAED,6BAA6B,CAAC,QAAQ,CAAC,CAAA;IAEvC,MAAM,mBAAmB,CAAC,QAAQ,EAAE,GAAG,EAAE,qBAAqB,CAAC,CAAA;IAE/D,OAAO,SAAS,CAAA;AAClB,CAAC;AAmBD,SAAS,sBAAsB,CAAC,QAAkB;IAChD,IAAI,QAAQ,CAAC,QAAQ,EAAE,CAAC;QACtB,MAAM,cAAc,CAAC,uCAAuC,EAAE,qBAAqB,CAAC,CAAA;IACtF,CAAC;AACH,CAAC;AAmBM,KAAK,UAAU,oBAAoB,CACxC,EAAuB,EACvB,MAAc,EACd,oBAAgC,EAChC,KAAa,EACb,OAAqC;IAErC,QAAQ,CAAC,EAAE,CAAC,CAAA;IACZ,YAAY,CAAC,MAAM,CAAC,CAAA;IAEpB,YAAY,CAAC,KAAK,EAAE,SAAS,CAAC,CAAA;IAE9B,MAAM,GAAG,GAAG,eAAe,CACzB,EAAE,EACF,wBAAwB,EACxB,MAAM,CAAC,yBAAyB,EAChC,OAAO,EAAE,CAAC,qBAAqB,CAAC,KAAK,IAAI,CAC1C,CAAA;IAED,MAAM,IAAI,GAAG,IAAI,eAAe,CAAC,OAAO,EAAE,oBAAoB,CAAC,CAAA;IAC/D,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,CAAA;IACxB,MAAM,OAAO,GAAG,cAAc,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;IAChD,IAAI,OAAO,EAAE,kBAAkB,IAAI,MAAM,CAAC,iCAAiC,EAAE,CAAC;QAC5E,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,qCAAqC,CAAC,CAAA;IAC9D,CAAC,MAAM,CAAC;QACN,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,kBAAkB,CAAC,CAAA;IAC3C,CAAC;IAED,OAAO,oBAAoB,CAAC,EAAE,EAAE,MAAM,EAAE,oBAAoB,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAA;AAC5F,CAAC;AA8CM,KAAK,UAAU,4BAA4B,CAChD,EAAuB,EACvB,MAAc,EACd,QAAkB,EAClB,OAA2B;IAE3B,QAAQ,CAAC,EAAE,CAAC,CAAA;IACZ,YAAY,CAAC,MAAM,CAAC,CAAA;IAEpB,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE,CAAC;QACzC,MAAM,cAAc,CAAC,4CAA4C,EAAE,oBAAoB,CAAC,CAAA;IAC1F,CAAC;IAED,6BAA6B,CAAC,QAAQ,CAAC,CAAA;IAEvC,MAAM,mBAAmB,CAAC,QAAQ,EAAE,GAAG,EAAE,wBAAwB,CAAC,CAAA;IAElE,IAAI,IAAgB,CAAA;IACpB,IAAI,cAAc,CAAC,QAAQ,CAAC,KAAK,qCAAqC,EAAE,CAAC;QACvE,sBAAsB,CAAC,QAAQ,CAAC,CAAA;QAChC,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,MAAM,WAAW,CACvC,MAAM,QAAQ,CAAC,IAAI,EAAE,EACrB,qBAAqB,CAAC,IAAI,CACxB,SAAS,EACT,MAAM,CAAC,iCAAiC,EACxC,EAAE,CAAC,0CAA0C,EAC7C,OAAO,CACR,EACD,YAAY,CAAC,MAAM,CAAC,EACpB,iBAAiB,CAAC,MAAM,CAAC,EACzB,OAAO,EAAE,CAAC,UAAU,CAAC,CACtB,CACE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE,yBAAyB,CAAC,CAAC,CAC7D,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,EAAE;YAAC,KAAK;YAAE,KAAK;YAAE,KAAK;SAAC,CAAC,CAAC,CAC7D,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,CACxC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,CAAA;QAE3D,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAA;QAC1B,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,mBAAmB,CAAC,EAAE,CAAC;YAC9C,MAAM,GAAG,CAAC,uDAAuD,EAAE,gBAAgB,EAAE;gBACnF,MAAM;aACP,CAAC,CAAA;QACJ,CAAC;QACD,IAAI,GAAG,MAAM,CAAC,mBAAmB,CAAA;IACnC,CAAC,MAAM,CAAC;QACN,sBAAsB,CAAC,QAAQ,CAAC,CAAA;QAChC,IAAI,GAAG,MAAM,mBAAmB,CAAC,QAAQ,CAAC,CAAA;IAC5C,CAAC;IAED,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;QACrC,MAAM,GAAG,CAAC,qDAAqD,EAAE,gBAAgB,EAAE;YACjF,IAAI,EAAE,IAAI;SACX,CAAC,CAAA;IACJ,CAAC;IAED,OAAO,IAA6B,CAAA;AACtC,CAAC;AAED,KAAK,UAAU,WAAW,CACxB,EAAuB,EACvB,OAAmC;IAEnC,QAAQ,CAAC,EAAE,CAAC,CAAA;IAEZ,MAAM,GAAG,GAAG,eAAe,CAAC,EAAE,EAAE,UAAU,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,qBAAqB,CAAC,KAAK,IAAI,CAAC,CAAA;IAE7F,MAAM,OAAO,GAAG,cAAc,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;IAChD,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,kBAAkB,CAAC,CAAA;IACzC,OAAO,CAAC,MAAM,CAAC,QAAQ,EAAE,0BAA0B,CAAC,CAAA;IAEpD,OAAO,CAAC,OAAO,EAAE,CAAC,WAAW,CAAC,IAAI,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE;QACjD,IAAI,EAAE,SAAS;QACf,OAAO,EAAE,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;QAC9C,MAAM,EAAE,KAAK;QACb,QAAQ,EAAE,QAAQ;QAClB,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS;KAC7D,CAAC,CAAA;AACJ,CAAC;AAMD,KAAK,UAAU,mBAAmB,CAAC,QAAkB;IACnD,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE,CAAC;QACzC,MAAM,cAAc,CAAC,4CAA4C,EAAE,oBAAoB,CAAC,CAAA;IAC1F,CAAC;IAED,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;QAC5B,MAAM,GAAG,CACP,qFAAqF,EACrF,uBAAuB,EACvB,QAAQ,CACT,CAAA;IACH,CAAC;IAED,sBAAsB,CAAC,QAAQ,CAAC,CAAA;IAChC,MAAM,IAAI,GAAG,MAAM,mBAAmB,CAAO,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAChE,CADkE,iBAChD,CAAC,QAAQ,EAAE,kBAAkB,EAAE,0BAA0B,CAAC,CAC7E,CAAA;IAED,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;QAC9B,MAAM,GAAG,CAAC,kDAAkD,EAAE,gBAAgB,EAAE;YAAE,IAAI,EAAE,IAAI;QAAA,CAAE,CAAC,CAAA;IACjG,CAAC;IAED,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,YAAY,CAAC,EAAE,CAAC;QACzD,MAAM,GAAG,CACP,uEAAuE,EACvE,gBAAgB,EAChB;YAAE,IAAI,EAAE,IAAI;QAAA,CAAE,CACf,CAAA;IACH,CAAC;IAED,OAAO,IAAI,CAAA;AACb,CAAC;AAED,SAAS,SAAS,CAAC,GAAW;IAC5B,OAAQ,GAAG,EAAE,CAAC;QACZ,KAAK,OAAO,CAAC;QACb,KAAK,OAAO,CAAC;QACb,KAAK,OAAO,CAAC;QACb,KAAK,OAAO,CAAC;QACb,KAAK,OAAO,CAAC;QACb,KAAK,OAAO,CAAC;QACb,KAAK,OAAO,CAAC;QACb,KAAK,OAAO,CAAC;QACb,KAAK,OAAO,CAAC;QACb,KAAK,SAAS,CAAC;QACf,KAAK,OAAO;YACV,OAAO,IAAI,CAAA;QACb;YACE,OAAO,KAAK,CAAA;IAChB,CAAC;AACH,CAAC;AAED,SAAS,oBAAoB,CAAC,MAAkC;IAC9D,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC;QAC3B,MAAM,IAAI,yBAAyB,CAAC,kCAAkC,EAAE;YACtE,KAAK,EAAE;gBAAE,GAAG,EAAE,MAAM,CAAC,GAAG;YAAA,CAAE;SAC3B,CAAC,CAAA;IACJ,CAAC;AACH,CAAC;AAED,SAAS,oBAAoB,CAAC,GAAc;IAC1C,MAAM,EAAE,SAAS,EAAE,GAAG,GAAuD,CAAA;IAC7E,IAAI,OAAO,SAAS,CAAC,aAAa,KAAK,QAAQ,IAAI,SAAS,CAAC,aAAa,GAAG,IAAI,EAAE,CAAC;QAClF,MAAM,IAAI,yBAAyB,CAAC,CAAA,YAAA,EAAe,SAAS,CAAC,IAAI,CAAA,cAAA,CAAgB,EAAE;YACjF,KAAK,EAAE,GAAG;SACX,CAAC,CAAA;IACJ,CAAC;AACH,CAAC;AAED,SAAS,aAAa,CAAC,GAAc;IACnC,MAAM,EAAE,SAAS,EAAE,GAAG,GAAgD,CAAA;IACtE,OAAQ,SAAS,CAAC,UAAU,EAAE,CAAC;QAC7B,KAAK,OAAO;YACV,OAAO,SAAS,CAAA;QAClB,KAAK,OAAO;YACV,OAAO,SAAS,CAAA;QAClB,KAAK,OAAO;YACV,OAAO,SAAS,CAAA;QAClB;YACE,MAAM,IAAI,yBAAyB,CAAC,8BAA8B,EAAE;gBAAE,KAAK,EAAE,GAAG;YAAA,CAAE,CAAC,CAAA;IACvF,CAAC;AACH,CAAC;AAED,SAAS,WAAW,CAAC,GAAc;IACjC,OAAQ,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QAC3B,KAAK,OAAO;YACV,OAAO;gBACL,IAAI,EAAE,GAAG,CAAC,SAAS,CAAC,IAAI;gBACxB,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC;aACV,CAAA;QAClB,KAAK,SAAS,CAAC;YAAC,CAAC;gBACf,oBAAoB,CAAC,GAAG,CAAC,CAAA;gBACzB,OAAS,GAAG,CAAC,SAAmC,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;oBAC3D,KAAK,SAAS,CAAC;oBACf,KAAK,SAAS,CAAC;oBACf,KAAK,SAAS;wBACZ,OAAO;4BACL,IAAI,EAAE,GAAG,CAAC,SAAS,CAAC,IAAI;4BACxB,UAAU,EACR,QAAQ,CAAE,GAAG,CAAC,SAAmC,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC;yBAClE,CAAA;oBACnB;wBACE,MAAM,IAAI,yBAAyB,CAAC,+BAA+B,EAAE;4BAAE,KAAK,EAAE,GAAG;wBAAA,CAAE,CAAC,CAAA;gBACxF,CAAC;YACH,CAAC;QACD,KAAK,mBAAmB;YACtB,oBAAoB,CAAC,GAAG,CAAC,CAAA;YACzB,OAAO,GAAG,CAAC,SAAS,CAAC,IAAI,CAAA;QAC3B,KAAK,SAAS;YACZ,OAAO,GAAG,CAAC,SAAS,CAAC,IAAI,CAAA;IAC7B,CAAC;IACD,MAAM,IAAI,yBAAyB,CAAC,sCAAsC,EAAE;QAAE,KAAK,EAAE,GAAG;IAAA,CAAE,CAAC,CAAA;AAC7F,CAAC;AAED,KAAK,UAAU,oBAAoB,CACjC,eAAuB,EACvB,OAAe,EACf,GAAc,EACd,SAAqB;IAErB,MAAM,IAAI,GAAG,GAAG,CAAC,GAAG,eAAe,CAAA,CAAA,EAAI,OAAO,EAAE,CAAC,CAAA;IACjD,MAAM,SAAS,GAAG,WAAW,CAAC,GAAG,CAAC,CAAA;IAClC,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,GAAG,EAAE,SAAS,EAAE,IAAI,CAAC,CAAA;IAC5E,IAAI,CAAC,QAAQ,EAAE,CAAC;QACd,MAAM,GAAG,CAAC,mCAAmC,EAAE,gBAAgB,EAAE;YAC/D,GAAG;YACH,IAAI;YACJ,SAAS;YACT,SAAS;SACV,CAAC,CAAA;IACJ,CAAC;AACH,CAAC;AAOD,KAAK,UAAU,WAAW,CACxB,GAAW,EACX,QAAiD,EACjD,SAAiB,EACjB,cAAsB,EACtB,UAA0C;IAE1C,IAAI,EAAE,CAAC,EAAE,eAAe,EAAE,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;IAE/D,IAAI,MAAM,KAAK,CAAC,EAAE,CAAC;QACjB,IAAI,UAAU,KAAK,SAAS,EAAE,CAAC;YAC7B,GAAG,GAAG,MAAM,UAAU,CAAC,GAAG,CAAC,CAC1B;YAAA,CAAC,EAAE,CAAC,EAAE,eAAe,EAAE,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAA;QAChE,CAAC,MAAM,CAAC;YACN,MAAM,IAAI,yBAAyB,CAAC,kCAAkC,EAAE;gBAAE,KAAK,EAAE,GAAG;YAAA,CAAE,CAAC,CAAA;QACzF,CAAC;IACH,CAAC;IAED,IAAI,MAAM,KAAK,CAAC,EAAE,CAAC;QACjB,MAAM,GAAG,CAAC,aAAa,EAAE,gBAAgB,EAAE,GAAG,CAAC,CAAA;IACjD,CAAC;IAED,IAAI,MAAiB,CAAA;IACrB,IAAI,CAAC;QACH,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAA;IACjD,CAAC,CAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,GAAG,CAAC,2DAA2D,EAAE,WAAW,EAAE,KAAK,CAAC,CAAA;IAC5F,CAAC;IAED,IAAI,CAAC,YAAY,CAA6B,MAAM,CAAC,EAAE,CAAC;QACtD,MAAM,GAAG,CAAC,uCAAuC,EAAE,gBAAgB,EAAE,GAAG,CAAC,CAAA;IAC3E,CAAC;IAED,QAAQ,CAAC,MAAM,CAAC,CAAA;IAChB,IAAI,MAAM,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;QAC9B,MAAM,IAAI,yBAAyB,CAAC,yDAAyD,EAAE;YAC7F,KAAK,EAAE;gBAAE,MAAM;YAAA,CAAE;SAClB,CAAC,CAAA;IACJ,CAAC;IAED,IAAI,MAAiB,CAAA;IACrB,IAAI,CAAC;QACH,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA;IACzC,CAAC,CAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,GAAG,CAAC,4DAA4D,EAAE,WAAW,EAAE,KAAK,CAAC,CAAA;IAC7F,CAAC;IAED,IAAI,CAAC,YAAY,CAAa,MAAM,CAAC,EAAE,CAAC;QACtC,MAAM,GAAG,CAAC,wCAAwC,EAAE,gBAAgB,EAAE,GAAG,CAAC,CAAA;IAC5E,CAAC;IAED,MAAM,GAAG,GAAG,SAAS,EAAE,GAAG,SAAS,CAAA;IAEnC,IAAI,MAAM,CAAC,GAAG,KAAK,SAAS,EAAE,CAAC;QAC7B,IAAI,OAAO,MAAM,CAAC,GAAG,KAAK,QAAQ,EAAE,CAAC;YACnC,MAAM,GAAG,CAAC,mDAAmD,EAAE,gBAAgB,EAAE;gBAAE,MAAM;YAAA,CAAE,CAAC,CAAA;QAC9F,CAAC;QAED,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,GAAG,cAAc,EAAE,CAAC;YACvC,MAAM,GAAG,CACP,0FAA0F,EAC1F,mBAAmB,EACnB;gBAAE,MAAM;gBAAE,GAAG;gBAAE,SAAS,EAAE,cAAc;gBAAE,KAAK,EAAE,KAAK;YAAA,CAAE,CACzD,CAAA;QACH,CAAC;IACH,CAAC;IAED,IAAI,MAAM,CAAC,GAAG,KAAK,SAAS,EAAE,CAAC;QAC7B,IAAI,OAAO,MAAM,CAAC,GAAG,KAAK,QAAQ,EAAE,CAAC;YACnC,MAAM,GAAG,CAAC,6CAA6C,EAAE,gBAAgB,EAAE;gBAAE,MAAM;YAAA,CAAE,CAAC,CAAA;QACxF,CAAC;IACH,CAAC;IAED,IAAI,MAAM,CAAC,GAAG,KAAK,SAAS,EAAE,CAAC;QAC7B,IAAI,OAAO,MAAM,CAAC,GAAG,KAAK,QAAQ,EAAE,CAAC;YACnC,MAAM,GAAG,CAAC,0CAA0C,EAAE,gBAAgB,EAAE;gBAAE,MAAM;YAAA,CAAE,CAAC,CAAA;QACrF,CAAC;IACH,CAAC;IAED,IAAI,MAAM,CAAC,GAAG,KAAK,SAAS,EAAE,CAAC;QAC7B,IAAI,OAAO,MAAM,CAAC,GAAG,KAAK,QAAQ,EAAE,CAAC;YACnC,MAAM,GAAG,CAAC,8CAA8C,EAAE,gBAAgB,EAAE;gBAAE,MAAM;YAAA,CAAE,CAAC,CAAA;QACzF,CAAC;QACD,IAAI,MAAM,CAAC,GAAG,GAAG,GAAG,GAAG,cAAc,EAAE,CAAC;YACtC,MAAM,GAAG,CAAC,+CAA+C,EAAE,mBAAmB,EAAE;gBAC9E,MAAM;gBACN,GAAG;gBACH,SAAS,EAAE,cAAc;gBACzB,KAAK,EAAE,KAAK;aACb,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;IAED,IAAI,MAAM,CAAC,GAAG,KAAK,SAAS,EAAE,CAAC;QAC7B,IAAI,OAAO,MAAM,CAAC,GAAG,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC;YACjE,MAAM,GAAG,CAAC,4CAA4C,EAAE,gBAAgB,EAAE;gBAAE,MAAM;YAAA,CAAE,CAAC,CAAA;QACvF,CAAC;IACH,CAAC;IAED,OAAO;QAAE,MAAM;QAAE,MAAM;QAAE,GAAG,EAAE,GAAG;IAAA,CAAE,CAAA;AACrC,CAAC;AAqBM,KAAK,UAAU,uBAAuB,CAC3C,EAAuB,EACvB,MAAc,EACd,UAAiC,EACjC,aAAqE,EACrE,OAAsD;IAEtD,QAAQ,CAAC,EAAE,CAAC,CAAA;IACZ,YAAY,CAAC,MAAM,CAAC,CAAA;IAEpB,IAAI,UAAU,YAAY,GAAG,EAAE,CAAC;QAC9B,UAAU,GAAG,UAAU,CAAC,YAAY,CAAA;IACtC,CAAC;IAED,IAAI,CAAC,CAAC,UAAU,YAAY,eAAe,CAAC,EAAE,CAAC;QAC7C,MAAM,cAAc,CAClB,6DAA6D,EAC7D,oBAAoB,CACrB,CAAA;IACH,CAAC;IAED,MAAM,QAAQ,GAAG,qBAAqB,CAAC,UAAU,EAAE,UAAU,CAAC,CAAA;IAC9D,IAAI,CAAC,QAAQ,EAAE,CAAC;QACd,MAAM,GAAG,CAAC,+CAA+C,EAAE,gBAAgB,CAAC,CAAA;IAC9E,CAAC;IAED,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,MAAM,WAAW,CAC/C,QAAQ,EACR,qBAAqB,CAAC,IAAI,CACxB,SAAS,EACT,MAAM,CAAC,iCAAiC,EACxC,EAAE,CAAC,0CAA0C,EAC7C,OAAO,CACR,EACD,YAAY,CAAC,MAAM,CAAC,EACpB,iBAAiB,CAAC,MAAM,CAAC,EACzB,OAAO,EAAE,CAAC,UAAU,CAAC,CACtB,CACE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,EAAE;QAAC,KAAK;QAAE,KAAK;QAAE,KAAK;KAAC,CAAC,CAAC,CAC7D,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,CACxC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,CAAA;IAE3D,MAAM,EAAE,CAAC,EAAE,eAAe,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,gBAAgB,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;IAE9E,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAA;IACxC,MAAM,GAAG,GAAG,MAAM,gCAAgC,CAAC,EAAE,EAAE,OAAO,EAAE,MAAM,CAAC,CAAA;IACvE,MAAM,oBAAoB,CAAC,eAAe,EAAE,OAAO,EAAE,GAAG,EAAE,SAAS,CAAC,CAAA;IAEpE,MAAM,MAAM,GAAG,IAAI,eAAe,EAAE,CAAA;IACpC,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAE,CAAC;QAElD,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,GAAG,KAAK,KAAK,EAAE,CAAC;YAC/C,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;QACxB,CAAC;IACH,CAAC;IAED,OAAO,oBAAoB,CAAC,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,aAAa,CAAC,CAAA;AAChE,CAAC;AAED,KAAK,UAAU,WAAW,CAAC,IAAY,EAAE,MAAkC,EAAE,SAAiB;IAC5F,IAAI,SAAiB,CAAA;IACrB,OAAQ,MAAM,CAAC,GAAG,EAAE,CAAC;QACnB,KAAK,OAAO,CAAC;QACb,KAAK,OAAO,CAAC;QACb,KAAK,OAAO;YACV,SAAS,GAAG,SAAS,CAAA;YACrB,MAAK;QACP,KAAK,OAAO,CAAC;QACb,KAAK,OAAO,CAAC;QACb,KAAK,OAAO;YACV,SAAS,GAAG,SAAS,CAAA;YACrB,MAAK;QACP,KAAK,OAAO,CAAC;QACb,KAAK,OAAO,CAAC;QACb,KAAK,OAAO,CAAC;QACb,KAAK,SAAS,CAAC;QACf,KAAK,OAAO;YACV,SAAS,GAAG,SAAS,CAAA;YACrB,MAAK;QACP;YACE,MAAM,IAAI,yBAAyB,CACjC,CAAA,8BAAA,EAAiC,SAAS,CAAA,YAAA,CAAc,EACxD;gBAAE,KAAK,EAAE;oBAAE,GAAG,EAAE,MAAM,CAAC,GAAG;gBAAA,CAAE;YAAA,CAAE,CAC/B,CAAA;IACL,CAAC;IAED,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,CAAA;IAC/D,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,CAAA;AACrD,CAAC;AAED,KAAK,UAAU,kBAAkB,CAC/B,IAAY,EACZ,MAAc,EACd,MAAkC,EAClC,SAAiB;IAEjB,MAAM,QAAQ,GAAG,MAAM,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,SAAS,CAAC,CAAA;IAC3D,OAAO,MAAM,KAAK,QAAQ,CAAA;AAC5B,CAAC;AAyBM,KAAK,UAAU,iCAAiC,CACrD,EAAuB,EACvB,MAAc,EACd,UAA2C,EAC3C,aAAqB,EACrB,aAA6C,EAC7C,MAA0C,EAC1C,OAAsD;IAEtD,OAAO,sBAAsB,CAC3B,EAAE,EACF,MAAM,EACN,UAAU,EACV,aAAa,EACb,aAAa,EACb,MAAM,EACN,OAAO,EACP,IAAI,CACL,CAAA;AACH,CAAC;AAyBM,KAAK,UAAU,2BAA2B,CAC/C,EAAuB,EACvB,MAAc,EACd,UAA2C,EAC3C,aAAqB,EACrB,aAA6C,EAC7C,MAA0C,EAC1C,OAAsD;IAEtD,OAAO,sBAAsB,CAC3B,EAAE,EACF,MAAM,EACN,UAAU,EACV,aAAa,EACb,aAAa,EACb,MAAM,EACN,OAAO,EACP,KAAK,CACN,CAAA;AACH,CAAC;AAED,KAAK,UAAU,aAAa,CAAC,OAAgB;IAC3C,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;QACrB,MAAM,cAAc,CAClB,0DAA0D,EAC1D,qBAAqB,EACrB;YAAE,KAAK,EAAE,OAAO;QAAA,CAAE,CACnB,CAAA;IACH,CAAC;IAED,OAAO,OAAO,CAAC,IAAI,EAAE,CAAA;AACvB,CAAC;AAWM,KAAK,UAAU,gBAAgB,CAAC,OAAgB;IACrD,IAAI,OAAO,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;QAC9B,MAAM,cAAc,CAClB,yDAAyD,EACzD,qBAAqB,EACrB;YAAE,KAAK,EAAE,OAAO;QAAA,CAAE,CACnB,CAAA;IACH,CAAC;IAED,IAAI,cAAc,CAAC,OAAO,CAAC,KAAK,mCAAmC,EAAE,CAAC;QACpE,MAAM,cAAc,CAClB,4FAA4F,EAC5F,qBAAqB,EACrB;YAAE,KAAK,EAAE,OAAO;QAAA,CAAE,CACnB,CAAA;IACH,CAAC;IAED,OAAO,aAAa,CAAC,OAAO,CAAC,CAAA;AAC/B,CAAC;AAED,KAAK,UAAU,sBAAsB,CACnC,EAAuB,EACvB,MAAc,EACd,UAA2C,EAC3C,aAAqB,EACrB,aAAwD,EACxD,MAAqD,EACrD,OAAmE,EACnE,IAAa;IAEb,QAAQ,CAAC,EAAE,CAAC,CAAA;IACZ,YAAY,CAAC,MAAM,CAAC,CAAA;IAEpB,IAAI,UAAU,YAAY,GAAG,EAAE,CAAC;QAC9B,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YAC5B,MAAM,cAAc,CAClB,8GAA8G,EAC9G,qBAAqB,CACtB,CAAA;QACH,CAAC;QACD,UAAU,GAAG,IAAI,eAAe,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;IAC5D,CAAC,MAAM,IAAI,eAAe,CAAC,UAAU,EAAE,OAAO,CAAC,EAAE,CAAC;QAChD,UAAU,GAAG,IAAI,eAAe,CAAC,MAAM,gBAAgB,CAAC,UAAU,CAAC,CAAC,CAAA;IACtE,CAAC,MAAM,IAAI,UAAU,YAAY,eAAe,EAAE,CAAC;QACjD,UAAU,GAAG,IAAI,eAAe,CAAC,UAAU,CAAC,CAAA;IAC9C,CAAC,MAAM,CAAC;QACN,MAAM,cAAc,CAClB,uEAAuE,EACvE,oBAAoB,CACrB,CAAA;IACH,CAAC;IAED,MAAM,QAAQ,GAAG,qBAAqB,CAAC,UAAU,EAAE,UAAU,CAAC,CAAA;IAC9D,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,CAAA;IAE7B,OAAQ,aAAa,EAAE,CAAC;QACtB,KAAK,SAAS,CAAC;QACf,KAAK,aAAa;YAChB,MAAK;QACP;YACE,YAAY,CAAC,aAAa,EAAE,0BAA0B,CAAC,CAAA;IAC3D,CAAC;IAED,MAAM,MAAM,GAAG,oBAAoB,CACjC;QACE,GAAG,EAAE;QACL,8CAA8C,EAAE,KAAK;KACtD,EACD,MAAM,EACN,UAAU,EACV,aAAa,CACd,CAAA;IAED,IAAI,CAAC,QAAQ,EAAE,CAAC;QACd,MAAM,GAAG,CAAC,2CAA2C,EAAE,gBAAgB,CAAC,CAAA;IAC1E,CAAC;IACD,MAAM,IAAI,GAAG,qBAAqB,CAAC,UAAU,EAAE,MAAM,CAAC,CAAA;IACtD,IAAI,CAAC,IAAI,EAAE,CAAC;QACV,MAAM,GAAG,CAAC,qDAAqD,EAAE,gBAAgB,CAAC,CAAA;IACpF,CAAC;IAED,MAAM,cAAc,GAAmC;QACrD,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,OAAO;QACP,QAAQ;KACT,CAAA;IAED,MAAM,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;IACrC,IAAI,IAAI,IAAI,CAAC,OAAO,aAAa,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,CAAC,EAAE,CAAC;QAClE,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;IAC/B,CAAC;IAED,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;QACzB,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,mBAAmB,CAAC,CAAA;IAClD,CAAC,MAAM,IAAI,MAAM,CAAC,eAAe,KAAK,SAAS,EAAE,CAAC;QAChD,YAAY,CAAC,MAAM,CAAC,eAAe,EAAE,KAAK,EAAE,0BAA0B,CAAC,CAAA;IACzE,CAAC;IAED,MAAM,KAAK,MAAM,CAAC,eAAe,IAAI,iBAAiB,CAAA;IACtD,IAAI,MAAM,CAAC,iBAAiB,IAAI,MAAM,KAAK,iBAAiB,EAAE,CAAC;QAC7D,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;IAClC,CAAC;IAED,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,MAAM,WAAW,CAC/C,QAAQ,EACR,qBAAqB,CAAC,IAAI,CACxB,SAAS,EACT,MAAM,CAAC,4BAA4B,EACnC,EAAE,CAAC,qCAAqC,EACxC,OAAO,CACR,EACD,YAAY,CAAC,MAAM,CAAC,EACpB,iBAAiB,CAAC,MAAM,CAAC,EACzB,OAAO,EAAE,CAAC,UAAU,CAAC,CACtB,CACE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC,CACtD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,CACxC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,CAAA;IAE3D,MAAM,SAAS,GAAG,YAAY,CAAC,MAAM,CAAC,CAAA;IACtC,MAAM,GAAG,GAAG,SAAS,EAAE,GAAG,SAAS,CAAA;IACnC,IAAI,MAAM,CAAC,GAAI,GAAG,GAAG,GAAG,IAAI,EAAE,CAAC;QAC7B,MAAM,GAAG,CACP,yEAAyE,EACzE,mBAAmB,EACnB;YAAE,GAAG;YAAE,MAAM;YAAE,KAAK,EAAE,KAAK;QAAA,CAAE,CAC9B,CAAA;IACH,CAAC;IAED,YAAY,CAAC,MAAM,CAAC,MAAM,EAAE,2CAA2C,EAAE,gBAAgB,EAAE;QACzF,MAAM;KACP,CAAC,CAAA;IAEF,IAAI,MAAM,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;QACnC,YAAY,CACV,MAAM,CAAC,SAAS,EAChB,KAAK,EACL,4CAA4C,EAC5C,gBAAgB,EAChB;YAAE,MAAM;QAAA,CAAE,CACX,CAAA;IACH,CAAC;IAED,IAAI,MAAM,KAAK,iBAAiB,EAAE,CAAC;QACjC,MAAM,GAAG,GAAG,SAAS,EAAE,GAAG,YAAY,CAAC,MAAM,CAAC,CAAA;QAC9C,MAAM,SAAS,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAA;QAC3C,IAAK,MAAkB,CAAC,SAAU,GAAG,MAAM,GAAG,GAAG,GAAG,SAAS,EAAE,CAAC;YAC9D,MAAM,GAAG,CACP,kEAAkE,EAClE,mBAAmB,EACnB;gBAAE,MAAM;gBAAE,GAAG;gBAAE,SAAS;gBAAE,KAAK,EAAE,WAAW;YAAA,CAAE,CAC/C,CAAA;QACH,CAAC;IACH,CAAC;IAED,YAAY,CAAC,aAAa,EAAE,0BAA0B,CAAC,CAAA;IAEvD,IAAI,MAAM,CAAC,KAAK,KAAK,aAAa,EAAE,CAAC;QACnC,MAAM,GAAG,CAAC,yCAAyC,EAAE,oBAAoB,EAAE;YACzE,QAAQ,EAAE,aAAa;YACvB,MAAM;YACN,KAAK,EAAE,OAAO;SACf,CAAC,CAAA;IACJ,CAAC;IAED,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACzD,IAAI,MAAM,CAAC,GAAG,KAAK,SAAS,EAAE,CAAC;YAC7B,MAAM,GAAG,CACP,yEAAyE,EACzE,oBAAoB,EACpB;gBAAE,MAAM;gBAAE,KAAK,EAAE,KAAK;YAAA,CAAE,CACzB,CAAA;QACH,CAAC;QACD,IAAI,MAAM,CAAC,GAAG,KAAK,MAAM,CAAC,SAAS,EAAE,CAAC;YACpC,MAAM,GAAG,CAAC,0DAA0D,EAAE,oBAAoB,EAAE;gBAC1F,QAAQ,EAAE,MAAM,CAAC,SAAS;gBAC1B,MAAM;gBACN,KAAK,EAAE,KAAK;aACb,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;IAED,MAAM,EAAE,CAAC,EAAE,eAAe,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,gBAAgB,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;IAE9E,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAA;IACxC,MAAM,GAAG,GAAG,MAAM,gCAAgC,CAAC,EAAE,EAAE,OAAO,EAAE,MAAM,CAAC,CAAA;IACvE,MAAM,oBAAoB,CAAC,eAAe,EAAE,OAAO,EAAE,GAAG,EAAE,SAAS,CAAC,CAAA;IAEpE,IAAI,AAAC,MAAM,kBAAkB,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC,IAAK,IAAI,EAAE,CAAC;QAC/E,MAAM,GAAG,CAAC,mDAAmD,EAAE,oBAAoB,EAAE;YACnF,IAAI;YACJ,GAAG,EAAE,MAAM,CAAC,GAAG;YACf,KAAK,EAAE,QAAQ;YACf,MAAM;SACP,CAAC,CAAA;IACJ,CAAC;IAED,IAAI,AAAC,IAAI,IAAI,KAAK,KAAK,IAAI,CAAC,GAAI,MAAM,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;QAC5D,YAAY,CAAC,MAAM,CAAC,MAAM,EAAE,4CAA4C,EAAE,gBAAgB,EAAE;YAC1F,MAAM;SACP,CAAC,CAAA;QACF,YAAY,CAAC,KAAK,EAAE,4BAA4B,EAAE,gBAAgB,EAAE;YAAE,UAAU;QAAA,CAAE,CAAC,CAAA;QAEnF,IAAI,AAAC,MAAM,kBAAkB,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC,IAAK,IAAI,EAAE,CAAC;YAChF,MAAM,GAAG,CAAC,oDAAoD,EAAE,oBAAoB,EAAE;gBACpF,KAAK;gBACL,GAAG,EAAE,MAAM,CAAC,GAAG;gBACf,KAAK,EAAE,QAAQ;gBACf,MAAM;aACP,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;IAED,OAAO,MAAM,CAAA;AACf,CAAC;AAOD,SAAS,qBAAqB,CAC5B,MAAqC,EACrC,MAA4B,EAC5B,QAA0D,EAC1D,MAAkC;IAElC,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;QACzB,IAAI,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC;YACtF,MAAM,GAAG,CAAC,uCAAuC,EAAE,gBAAgB,EAAE;gBACnE,MAAM;gBACN,QAAQ,EAAE,MAAM;gBAChB,MAAM,EAAE,sBAAsB;aAC/B,CAAC,CAAA;QACJ,CAAC;QACD,OAAM;IACR,CAAC;IAED,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;QAC1B,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC;YACjC,MAAM,GAAG,CAAC,uCAAuC,EAAE,gBAAgB,EAAE;gBACnE,MAAM;gBACN,QAAQ,EAAE,MAAM;gBAChB,MAAM,EAAE,+BAA+B;aACxC,CAAC,CAAA;QACJ,CAAC;QACD,OAAM;IACR,CAAC;IAED,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;QAC3B,IACE,OAAO,QAAQ,KAAK,QAAQ,GACxB,MAAM,CAAC,GAAG,KAAK,QAAQ,GACvB,OAAO,QAAQ,KAAK,UAAU,GAC5B,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,GACrB,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,EACpC,CAAC;YACD,MAAM,GAAG,CAAC,uCAAuC,EAAE,gBAAgB,EAAE;gBACnE,MAAM;gBACN,QAAQ,EAAE,QAAQ;gBAClB,MAAM,EAAE,eAAe;aACxB,CAAC,CAAA;QACJ,CAAC;QACD,OAAM;IACR,CAAC;IAED,MAAM,GAAG,CACP,kFAAkF,EAClF,SAAS,EACT;QAAE,MAAM;QAAE,MAAM;QAAE,QAAQ;IAAA,CAAE,CAC7B,CAAA;AACH,CAAC;AAMD,SAAS,qBAAqB,CAAC,UAA2B,EAAE,IAAY;IACtE,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;IACpD,IAAI,MAAM,GAAG,CAAC,EAAE,CAAC;QACf,MAAM,GAAG,CAAC,CAAA,CAAA,EAAI,IAAI,CAAA,sCAAA,CAAwC,EAAE,gBAAgB,CAAC,CAAA;IAC/E,CAAC;IACD,OAAO,KAAK,CAAA;AACd,CAAC;AAaM,MAAM,cAAc,GAAkB,MAAM,EAAE,CAAA;AAO9C,MAAM,aAAa,GAAkB,MAAM,EAAE,CAAA;AAsB9C,SAAU,oBAAoB,CAClC,EAAuB,EACvB,MAAc,EACd,UAAiC,EACjC,aAAqE;IAErE,QAAQ,CAAC,EAAE,CAAC,CAAA;IACZ,YAAY,CAAC,MAAM,CAAC,CAAA;IAEpB,IAAI,UAAU,YAAY,GAAG,EAAE,CAAC;QAC9B,UAAU,GAAG,UAAU,CAAC,YAAY,CAAA;IACtC,CAAC;IAED,IAAI,CAAC,CAAC,UAAU,YAAY,eAAe,CAAC,EAAE,CAAC;QAC7C,MAAM,cAAc,CAClB,6DAA6D,EAC7D,oBAAoB,CACrB,CAAA;IACH,CAAC;IAED,IAAI,qBAAqB,CAAC,UAAU,EAAE,UAAU,CAAC,EAAE,CAAC;QAClD,MAAM,GAAG,CACP,wGAAwG,EACxG,gBAAgB,EAChB;YAAE,UAAU;QAAA,CAAE,CACf,CAAA;IACH,CAAC;IAED,MAAM,GAAG,GAAG,qBAAqB,CAAC,UAAU,EAAE,KAAK,CAAC,CAAA;IACpD,MAAM,KAAK,GAAG,qBAAqB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAA;IAExD,IAAI,CAAC,GAAG,IAAI,EAAE,CAAC,8CAA8C,EAAE,CAAC;QAC9D,MAAM,GAAG,CAAC,2CAA2C,EAAE,gBAAgB,EAAE;YAAE,UAAU;QAAA,CAAE,CAAC,CAAA;IAC1F,CAAC;IAED,IAAI,GAAG,IAAI,GAAG,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC;QAC7B,MAAM,GAAG,CAAC,oDAAoD,EAAE,gBAAgB,EAAE;YAChF,QAAQ,EAAE,EAAE,CAAC,MAAM;YACnB,UAAU;SACX,CAAC,CAAA;IACJ,CAAC;IAED,OAAQ,aAAa,EAAE,CAAC;QACtB,KAAK,SAAS,CAAC;QACf,KAAK,aAAa;YAChB,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;gBACxB,MAAM,GAAG,CAAC,mDAAmD,EAAE,gBAAgB,EAAE;oBAC/E,QAAQ,EAAE,SAAS;oBACnB,UAAU;iBACX,CAAC,CAAA;YACJ,CAAC;YACD,MAAK;QACP,KAAK,cAAc;YACjB,MAAK;QACP;YACE,YAAY,CAAC,aAAa,EAAE,0BAA0B,CAAC,CAAA;YAEvD,IAAI,KAAK,KAAK,aAAa,EAAE,CAAC;gBAC5B,MAAM,GAAG,CACP,KAAK,KAAK,SAAS,GACf,oCAAoC,GACpC,6CAA6C,EACjD,gBAAgB,EAChB;oBAAE,QAAQ,EAAE,aAAa;oBAAE,UAAU;gBAAA,CAAE,CACxC,CAAA;YACH,CAAC;IACL,CAAC;IAED,MAAM,KAAK,GAAG,qBAAqB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAA;IACxD,IAAI,KAAK,EAAE,CAAC;QACV,MAAM,IAAI,0BAA0B,CAAC,oDAAoD,EAAE;YACzF,KAAK,EAAE,UAAU;SAClB,CAAC,CAAA;IACJ,CAAC;IAED,MAAM,QAAQ,GAAG,qBAAqB,CAAC,UAAU,EAAE,UAAU,CAAC,CAAA;IAC9D,MAAM,KAAK,GAAG,qBAAqB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAA;IACxD,IAAI,QAAQ,KAAK,SAAS,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;QAClD,MAAM,IAAI,yBAAyB,CAAC,6CAA6C,CAAC,CAAA;IACpF,CAAC;IAED,OAAO,KAAK,CAAC,IAAI,eAAe,CAAC,UAAU,CAAC,CAAC,CAAA;AAC/C,CAAC;AAED,SAAS,WAAW,CAAC,GAAW;IAC9B,OAAQ,GAAG,EAAE,CAAC;QACZ,KAAK,OAAO,CAAC;QACb,KAAK,OAAO,CAAC;QACb,KAAK,OAAO;YACV,OAAO;gBAAE,IAAI,EAAE,SAAS;gBAAE,IAAI,EAAE,CAAA,IAAA,EAAO,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;YAAA,CAAE,CAAA;QAC1D,KAAK,OAAO,CAAC;QACb,KAAK,OAAO,CAAC;QACb,KAAK,OAAO;YACV,OAAO;gBAAE,IAAI,EAAE,mBAAmB;gBAAE,IAAI,EAAE,CAAA,IAAA,EAAO,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;YAAA,CAAE,CAAA;QACpE,KAAK,OAAO,CAAC;QACb,KAAK,OAAO;YACV,OAAO;gBAAE,IAAI,EAAE,OAAO;gBAAE,UAAU,EAAE,CAAA,EAAA,EAAK,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;YAAA,CAAE,CAAA;QAC5D,KAAK,OAAO;YACV,OAAO;gBAAE,IAAI,EAAE,OAAO;gBAAE,UAAU,EAAE,OAAO;YAAA,CAAE,CAAA;QAC/C,KAAK,SAAS,CAAC;QACf,KAAK,OAAO;YACV,OAAO,SAAS,CAAA;QAClB;YACE,MAAM,IAAI,yBAAyB,CAAC,2BAA2B,EAAE;gBAAE,KAAK,EAAE;oBAAE,GAAG;gBAAA,CAAE;YAAA,CAAE,CAAC,CAAA;IACxF,CAAC;AACH,CAAC;AAED,KAAK,UAAU,SAAS,CAAC,GAAW,EAAE,GAAQ;IAC5C,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,CAAA;IACzC,OAAO,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,GAAG,EAAE,WAAW,CAAC,GAAG,CAAC,EAAE,IAAI,EAAE;QAAC,QAAQ;KAAC,CAAC,CAAA;AAChF,CAAC;AAqBM,KAAK,UAAU,0BAA0B,CAC9C,EAAuB,EACvB,MAAc,EACd,oBAAgC,EAChC,UAAiE,EACjE,OAA2C;IAE3C,QAAQ,CAAC,EAAE,CAAC,CAAA;IACZ,YAAY,CAAC,MAAM,CAAC,CAAA;IAEpB,MAAM,GAAG,GAAG,eAAe,CACzB,EAAE,EACF,+BAA+B,EAC/B,MAAM,CAAC,yBAAyB,EAChC,OAAO,EAAE,CAAC,qBAAqB,CAAC,KAAK,IAAI,CAC1C,CAAA;IAED,MAAM,IAAI,GAAG,IAAI,eAAe,CAAC,UAAU,CAAC,CAAA;IAC5C,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,MAAM,CAAC,SAAS,CAAC,CAAA;IAEvC,MAAM,OAAO,GAAG,cAAc,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;IAChD,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,kBAAkB,CAAC,CAAA;IAEzC,OAAO,oBAAoB,CAAC,EAAE,EAAE,MAAM,EAAE,oBAAoB,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAA;AAC5F,CAAC;AAkDM,KAAK,UAAU,kCAAkC,CACtD,EAAuB,EACvB,MAAc,EACd,QAAkB;IAElB,QAAQ,CAAC,EAAE,CAAC,CAAA;IACZ,YAAY,CAAC,MAAM,CAAC,CAAA;IAEpB,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE,CAAC;QACzC,MAAM,cAAc,CAAC,4CAA4C,EAAE,oBAAoB,CAAC,CAAA;IAC1F,CAAC;IAED,6BAA6B,CAAC,QAAQ,CAAC,CAAA;IAEvC,MAAM,mBAAmB,CAAC,QAAQ,EAAE,GAAG,EAAE,+BAA+B,CAAC,CAAA;IAEzE,sBAAsB,CAAC,QAAQ,CAAC,CAAA;IAChC,MAAM,IAAI,GAAG,MAAM,mBAAmB,CAAyC,QAAQ,CAAC,CAAA;IAExF,YAAY,CAAC,IAAI,CAAC,WAAW,EAAE,wCAAwC,EAAE,gBAAgB,EAAE;QACzF,IAAI,EAAE,IAAI;KACX,CAAC,CAAA;IACF,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE,sCAAsC,EAAE,gBAAgB,EAAE;QACrF,IAAI,EAAE,IAAI;KACX,CAAC,CAAA;IACF,YAAY,CACV,IAAI,CAAC,gBAAgB,EACrB,6CAA6C,EAC7C,gBAAgB,EAChB;QAAE,IAAI,EAAE,IAAI;IAAA,CAAE,CACf,CAAA;IAED,IAAI,SAAS,GACX,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAA;IACrF,YAAY,CAAC,SAAS,EAAE,KAAK,EAAE,uCAAuC,EAAE,gBAAgB,EAAE;QACxF,IAAI,EAAE,IAAI;KACX,CAAC,CAAA;IACF,IAAI,CAAC,UAAU,GAAG,SAAS,CAAA;IAE3B,IAAI,IAAI,CAAC,yBAAyB,KAAK,SAAS,EAAE,CAAC;QACjD,YAAY,CACV,IAAI,CAAC,yBAAyB,EAC9B,sDAAsD,EACtD,gBAAgB,EAChB;YAAE,IAAI,EAAE,IAAI;QAAA,CAAE,CACf,CAAA;IACH,CAAC;IAED,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;QAChC,YAAY,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,qCAAqC,EAAE,gBAAgB,EAAE;YAC1F,IAAI,EAAE,IAAI;SACX,CAAC,CAAA;IACJ,CAAC;IAED,OAAO,IAAI,CAAA;AACb,CAAC;AAoBM,KAAK,UAAU,sBAAsB,CAC1C,EAAuB,EACvB,MAAc,EACd,oBAAgC,EAChC,UAAkB,EAClB,OAAqC;IAErC,QAAQ,CAAC,EAAE,CAAC,CAAA;IACZ,YAAY,CAAC,MAAM,CAAC,CAAA;IAEpB,YAAY,CAAC,UAAU,EAAE,cAAc,CAAC,CAAA;IAExC,MAAM,UAAU,GAAG,IAAI,eAAe,CAAC,OAAO,EAAE,oBAAoB,CAAC,CAAA;IACrE,UAAU,CAAC,GAAG,CAAC,aAAa,EAAE,UAAU,CAAC,CAAA;IACzC,OAAO,oBAAoB,CACzB,EAAE,EACF,MAAM,EACN,oBAAoB,EACpB,8CAA8C,EAC9C,UAAU,EACV,OAAO,CACR,CAAA;AACH,CAAC;AAkBM,KAAK,UAAU,yBAAyB,CAC7C,EAAuB,EACvB,MAAc,EACd,QAAkB,EAClB,OAA2B;IAE3B,OAAO,iCAAiC,CAAC,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,CAAC,CAAA;AACpF,CAAC;AAsBM,KAAK,UAAU,eAAe,CACnC,GAAW,EACX,OAAgC;IAEhC,YAAY,CAAC,GAAG,EAAE,OAAO,CAAC,CAAA;IAE1B,MAAM,SAAS,GAAiE,WAAW,CAAC,GAAG,CAAC,CAAA;IAEhG,IAAI,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;QACjD,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE;YACvB,aAAa,EAAE,OAAO,EAAE,aAAa,IAAI,IAAI;YAC7C,cAAc,EAAE,IAAI,UAAU,CAAC;gBAAC,IAAI;gBAAE,IAAI;gBAAE,IAAI;aAAC,CAAC;SACnD,CAAC,CAAA;IACJ,CAAC;IAED,OAAO,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,SAAS,EAAE,OAAO,EAAE,WAAW,IAAI,KAAK,EAAE;QACzE,MAAM;QACN,QAAQ;KACT,CAA2B,CAAA;AAC9B,CAAC;AAuCD,SAAS,YAAY,CAAC,GAAW;IAC/B,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAA;IACxB,GAAG,CAAC,MAAM,GAAG,EAAE,CAAA;IACf,GAAG,CAAC,IAAI,GAAG,EAAE,CAAA;IACb,OAAO,GAAG,CAAC,IAAI,CAAA;AACjB,CAAC;AAED,KAAK,UAAU,YAAY,CACzB,OAAgB,EAChB,WAAmB,EACnB,iBAA6B,EAC7B,OAGC;IAED,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;IAC/C,IAAI,WAAW,KAAK,IAAI,EAAE,CAAC;QACzB,MAAM,GAAG,CACP,sEAAsE,EACtE,eAAe,EACf;YAAE,OAAO,EAAE,OAAO,CAAC,OAAO;QAAA,CAAE,CAC7B,CAAA;IACH,CAAC;IAED,IAAI,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,EAAE,WAAW,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,KAAK,EAAE,CAAC;QACtF,MAAM,GAAG,CACP,CAAA,2FAAA,CAA6F,EAC7F,eAAe,EACf;YAAE,OAAO,EAAE,OAAO,CAAC,OAAO;QAAA,CAAE,CAC7B,CAAA;IACH,CAAC;IAED,IAAI,OAAO,iBAAiB,CAAC,GAAG,EAAE,GAAG,KAAK,QAAQ,EAAE,CAAC;QACnD,MAAM,GAAG,CACP,qFAAqF,EACrF,eAAe,EACf;YAAE,MAAM,EAAE,iBAAiB;QAAA,CAAE,CAC9B,CAAA;IACH,CAAC;IAED,MAAM,SAAS,GAAG,YAAY,CAAC,OAAO,CAAC,CAAA;IACvC,MAAM,KAAK,GAAG,MAAM,WAAW,CAC7B,WAAW,EACX,qBAAqB,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,EAAE,iBAAiB,EAAE,SAAS,EAAE,SAAS,CAAC,EACvF,SAAS,EACT,iBAAiB,CAAC,OAAO,CAAC,EAC1B,SAAS,CACV,CACE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC,CAC9C,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,EAAE;QAAC,KAAK;QAAE,KAAK;QAAE,KAAK;QAAE,KAAK;QAAE,KAAK;KAAC,CAAC,CAAC,CAAA;IAE9E,MAAM,GAAG,GAAG,SAAS,EAAE,GAAG,SAAS,CAAA;IACnC,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC,GAAI,CAAC,CAAA;IAC9C,IAAI,IAAI,GAAG,GAAG,EAAE,CAAC;QACf,MAAM,GAAG,CAAC,qCAAqC,EAAE,mBAAmB,EAAE;YACpE,GAAG;YACH,MAAM,EAAE,KAAK,CAAC,MAAM;YACpB,KAAK,EAAE,KAAK;SACb,CAAC,CAAA;IACJ,CAAC;IAED,IAAI,KAAK,CAAC,MAAM,CAAC,GAAG,KAAK,OAAO,CAAC,MAAM,EAAE,CAAC;QACxC,MAAM,GAAG,CAAC,yBAAyB,EAAE,oBAAoB,EAAE;YACzD,QAAQ,EAAE,OAAO,CAAC,MAAM;YACxB,MAAM,EAAE,KAAK,CAAC,MAAM;YACpB,KAAK,EAAE,KAAK;SACb,CAAC,CAAA;IACJ,CAAC;IAED,IACE,OAAO,KAAK,CAAC,MAAM,CAAC,GAAG,KAAK,QAAQ,IACpC,YAAY,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,EAC5D,CAAC;QACD,MAAM,GAAG,CAAC,yBAAyB,EAAE,oBAAoB,EAAE;YACzD,QAAQ,EAAE,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC;YACnC,MAAM,EAAE,KAAK,CAAC,MAAM;YACpB,KAAK,EAAE,KAAK;SACb,CAAC,CAAA;IACJ,CAAC;IAED,CAAC;QACC,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAA;QAE9E,IAAI,KAAK,CAAC,MAAM,CAAC,GAAG,KAAK,QAAQ,EAAE,CAAC;YAClC,MAAM,GAAG,CAAC,yBAAyB,EAAE,oBAAoB,EAAE;gBACzD,QAAQ;gBACR,MAAM,EAAE,KAAK,CAAC,MAAM;gBACpB,KAAK,EAAE,KAAK;aACb,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;IAED,CAAC;QACC,IAAI,UAAe,CAAA;QACnB,OAAQ,KAAK,CAAC,MAAM,CAAC,GAAI,CAAC,GAAG,EAAE,CAAC;YAC9B,KAAK,IAAI;gBACP,UAAU,GAAG;oBACX,GAAG,EAAE,KAAK,CAAC,MAAM,CAAC,GAAI,CAAC,GAAG;oBAC1B,GAAG,EAAE,KAAK,CAAC,MAAM,CAAC,GAAI,CAAC,GAAG;oBAC1B,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,GAAI,CAAC,CAAC;oBACtB,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,GAAI,CAAC,CAAC;iBACvB,CAAA;gBACD,MAAK;YACP,KAAK,KAAK;gBACR,UAAU,GAAG;oBACX,GAAG,EAAE,KAAK,CAAC,MAAM,CAAC,GAAI,CAAC,GAAG;oBAC1B,GAAG,EAAE,KAAK,CAAC,MAAM,CAAC,GAAI,CAAC,GAAG;oBAC1B,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,GAAI,CAAC,CAAC;iBACvB,CAAA;gBACD,MAAK;YACP,KAAK,KAAK;gBACR,UAAU,GAAG;oBACX,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,GAAI,CAAC,CAAC;oBACtB,GAAG,EAAE,KAAK,CAAC,MAAM,CAAC,GAAI,CAAC,GAAG;oBAC1B,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,GAAI,CAAC,CAAC;iBACvB,CAAA;gBACD,MAAK;YACP;gBACE,MAAM,IAAI,yBAAyB,CAAC,0BAA0B,EAAE;oBAAE,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,GAAG;gBAAA,CAAE,CAAC,CAAA;QAChG,CAAC;QACD,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAA;QAE7F,IAAI,iBAAiB,CAAC,GAAG,CAAC,GAAG,KAAK,QAAQ,EAAE,CAAC;YAC3C,MAAM,GAAG,CAAC,wCAAwC,EAAE,oBAAoB,EAAE;gBACxE,QAAQ;gBACR,MAAM,EAAE,iBAAiB;gBACzB,KAAK,EAAE,SAAS;aACjB,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;IAED,MAAM,EAAE,CAAC,EAAE,eAAe,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,gBAAgB,EAAE,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;IAEtF,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAA;IACxC,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,KAAK,CAAC,MAAM,CAAA;IACjC,IAAI,CAAC,GAAG,EAAE,CAAC;QACT,MAAM,GAAG,CAAC,gDAAgD,EAAE,eAAe,EAAE;YAC3E,MAAM,EAAE,KAAK,CAAC,MAAM;SACrB,CAAC,CAAA;IACJ,CAAC;IACD,MAAM,GAAG,GAAG,MAAM,SAAS,CAAC,GAAG,EAAE,GAAG,CAAC,CAAA;IACrC,IAAI,GAAG,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;QAC1B,MAAM,GAAG,CAAC,2DAA2D,EAAE,eAAe,EAAE;YACtF,MAAM,EAAE,KAAK,CAAC,MAAM;SACrB,CAAC,CAAA;IACJ,CAAC;IACD,MAAM,oBAAoB,CAAC,eAAe,EAAE,OAAO,EAAE,GAAG,EAAE,SAAS,CAAC,CAAA;AACtE,CAAC;AAiCM,KAAK,UAAU,sBAAsB,CAC1C,EAAuB,EACvB,OAAgB,EAChB,gBAAwB,EACxB,OAAuC;IAEvC,QAAQ,CAAC,EAAE,CAAC,CAAA;IAEZ,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC;QACvC,MAAM,cAAc,CAAC,0CAA0C,EAAE,oBAAoB,CAAC,CAAA;IACxF,CAAC;IAED,YAAY,CAAC,gBAAgB,EAAE,oBAAoB,CAAC,CAAA;IAEpD,MAAM,aAAa,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAA;IAC1D,IAAI,aAAa,KAAK,IAAI,EAAE,CAAC;QAC3B,MAAM,GAAG,CAAC,mDAAmD,EAAE,eAAe,EAAE;YAC9E,OAAO,EAAE,OAAO,CAAC,OAAO;SACzB,CAAC,CAAA;IACJ,CAAC;IACD,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,WAAW,EAAE,MAAM,EAAE,GAAG,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;IACpE,MAAM,GAAG,MAAM,CAAC,WAAW,EAAE,CAAA;IAC7B,OAAQ,MAAM,EAAE,CAAC;QACf,KAAK,MAAM,CAAC;QACZ,KAAK,QAAQ;YACX,MAAK;QACP;YACE,MAAM,IAAI,yBAAyB,CAAC,8CAA8C,EAAE;gBAClF,KAAK,EAAE;oBAAE,OAAO,EAAE,OAAO,CAAC,OAAO;gBAAA,CAAE;aACpC,CAAC,CAAA;IACN,CAAC;IAED,IAAI,MAAM,KAAK,CAAC,EAAE,CAAC;QACjB,MAAM,GAAG,CAAC,0CAA0C,EAAE,eAAe,EAAE;YACrE,OAAO,EAAE,OAAO,CAAC,OAAO;SACzB,CAAC,CAAA;IACJ,CAAC;IAED,MAAM,cAAc,GAAmC;QACrD,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,WAAW;KACZ,CAAA;IAED,IAAI,OAAO,EAAE,WAAW,IAAI,MAAM,KAAK,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;QAC7E,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;IAC5B,CAAC;IAED,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,WAAW,CAC1C,WAAW,EACX,qBAAqB,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,EAAE,iBAAiB,EAAE,SAAS,EAAE,SAAS,CAAC,EACvF,YAAY,CAAC,OAAO,CAAC,EACrB,iBAAiB,CAAC,OAAO,CAAC,EAC1B,SAAS,CACV,CACE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,CAC5C,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC,CACtD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,CACxC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,EAAE,gBAAgB,CAAC,CAAC,CACxD,KAAK,CAAC,cAAc,CAAC,CAAA;IAExB,KAAK,MAAM,KAAK,IAAI;QAAC,WAAW;QAAE,KAAK;QAAE,KAAK;KAAC,CAAE,CAAC;QAChD,IAAI,OAAO,MAAM,CAAC,KAAK,CAAC,KAAK,QAAQ,EAAE,CAAC;YACtC,MAAM,GAAG,CAAC,CAAA,gBAAA,EAAmB,KAAK,CAAA,YAAA,CAAc,EAAE,eAAe,EAAE;gBAAE,MAAM;YAAA,CAAE,CAAC,CAAA;QAChF,CAAC;IACH,CAAC;IAED,IAAI,KAAK,IAAI,MAAM,EAAE,CAAC;QACpB,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC;YAC9B,MAAM,GAAG,CAAC,iDAAiD,EAAE,eAAe,EAAE;gBAAE,MAAM;YAAA,CAAE,CAAC,CAAA;QAC3F,CAAC;QAED,MAAM,EAAE,CAAC,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;QAElD,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,MAAM,KAAK,CAAC,EAAE,CAAC;gBACjB,MAAM,IAAI,yBAAyB,CAAC,gDAAgD,EAAE;oBACpF,KAAK,EAAE;wBAAE,MAAM;oBAAA,CAAE;iBAClB,CAAC,CAAA;YACJ,CAAC;YAED,IAAI,GAAG,KAAK,KAAK,EAAE,CAAC;gBAClB,MAAM,IAAI,yBAAyB,CAAC,qCAAqC,EAAE;oBACzE,KAAK,EAAE;wBAAE,MAAM;oBAAA,CAAE;iBAClB,CAAC,CAAA;YACJ,CAAC;QACH,CAAC;IACH,CAAC;IAED,MAAM,EAAE,CAAC,EAAE,eAAe,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,gBAAgB,EAAE,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;IAEtF,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAA;IACxC,MAAM,GAAG,GAAG,MAAM,gCAAgC,CAAC,EAAE,EAAE,OAAO,EAAE,MAAM,CAAC,CAAA;IACvE,MAAM,oBAAoB,CAAC,eAAe,EAAE,OAAO,EAAE,GAAG,EAAE,SAAS,CAAC,CAAA;IAEpE,IACE,OAAO,EAAE,WAAW,IACpB,MAAM,KAAK,MAAM,IACjB,MAAM,CAAC,GAAG,EAAE,GAAG,KAAK,SAAS,IAC7B,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAC3B,CAAC;QACD,MAAM,YAAY,CAAC,OAAO,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC,KAAK,CAAC,cAAc,CAAC,CAAA;IACjF,CAAC;IAED,OAAO,MAA8B,CAAA;AACvC,CAAC;AAED,SAAS,cAAc,CAAC,GAAY;IAClC,IAAI,GAAG,YAAY,wBAAwB,IAAI,GAAG,EAAE,IAAI,KAAK,eAAe,EAAE,CAAC;QAC7E,GAAG,CAAC,IAAI,GAAG,gBAAgB,CAAA;IAC7B,CAAC;IACD,MAAM,GAAG,CAAA;AACX,CAAC;AAqBM,KAAK,UAAU,gCAAgC,CACpD,EAAuB,EACvB,MAAc,EACd,oBAAgC,EAChC,UAAiE,EACjE,OAAiD;IAEjD,QAAQ,CAAC,EAAE,CAAC,CAAA;IACZ,YAAY,CAAC,MAAM,CAAC,CAAA;IAEpB,MAAM,GAAG,GAAG,eAAe,CACzB,EAAE,EACF,qCAAqC,EACrC,MAAM,CAAC,yBAAyB,EAChC,OAAO,EAAE,CAAC,qBAAqB,CAAC,KAAK,IAAI,CAC1C,CAAA;IAED,MAAM,IAAI,GAAG,IAAI,eAAe,CAAC,UAAU,CAAC,CAAA;IAC5C,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,MAAM,CAAC,SAAS,CAAC,CAAA;IAEvC,MAAM,OAAO,GAAG,cAAc,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;IAChD,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,kBAAkB,CAAC,CAAA;IAEzC,OAAO,oBAAoB,CAAC,EAAE,EAAE,MAAM,EAAE,oBAAoB,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAA;AAC5F,CAAC;AAoCM,KAAK,UAAU,wCAAwC,CAC5D,EAAuB,EACvB,MAAc,EACd,QAAkB;IAElB,QAAQ,CAAC,EAAE,CAAC,CAAA;IACZ,YAAY,CAAC,MAAM,CAAC,CAAA;IAEpB,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE,CAAC;QACzC,MAAM,cAAc,CAAC,4CAA4C,EAAE,oBAAoB,CAAC,CAAA;IAC1F,CAAC;IAED,6BAA6B,CAAC,QAAQ,CAAC,CAAA;IAEvC,MAAM,mBAAmB,CAAC,QAAQ,EAAE,GAAG,EAAE,qCAAqC,CAAC,CAAA;IAE/E,sBAAsB,CAAC,QAAQ,CAAC,CAAA;IAChC,MAAM,IAAI,GAAG,MAAM,mBAAmB,CAA+C,QAAQ,CAAC,CAAA;IAE9F,YAAY,CAAC,IAAI,CAAC,WAAW,EAAE,wCAAwC,EAAE,gBAAgB,EAAE;QACzF,IAAI,EAAE,IAAI;KACX,CAAC,CAAA;IAEF,IAAI,SAAS,GACX,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAA;IACrF,YAAY,CAAC,SAAS,EAAE,KAAK,EAAE,uCAAuC,EAAE,gBAAgB,EAAE;QACxF,IAAI,EAAE,IAAI;KACX,CAAC,CAAA;IACF,IAAI,CAAC,UAAU,GAAG,SAAS,CAAA;IAE3B,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;QAChC,YAAY,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,qCAAqC,EAAE,gBAAgB,EAAE;YAC1F,IAAI,EAAE,IAAI;SACX,CAAC,CAAA;IACJ,CAAC;IAED,OAAO,IAAI,CAAA;AACb,CAAC;AAqBM,KAAK,UAAU,qCAAqC,CACzD,EAAuB,EACvB,MAAc,EACd,oBAAgC,EAChC,SAAiB,EACjB,OAAqC;IAErC,QAAQ,CAAC,EAAE,CAAC,CAAA;IACZ,YAAY,CAAC,MAAM,CAAC,CAAA;IAEpB,YAAY,CAAC,SAAS,EAAE,aAAa,CAAC,CAAA;IAEtC,MAAM,UAAU,GAAG,IAAI,eAAe,CAAC,OAAO,EAAE,oBAAoB,CAAC,CAAA;IACrE,UAAU,CAAC,GAAG,CAAC,aAAa,EAAE,SAAS,CAAC,CAAA;IACxC,OAAO,oBAAoB,CACzB,EAAE,EACF,MAAM,EACN,oBAAoB,EACpB,mCAAmC,EACnC,UAAU,EACV,OAAO,CACR,CAAA;AACH,CAAC;AAkBM,KAAK,UAAU,6CAA6C,CACjE,EAAuB,EACvB,MAAc,EACd,QAAkB,EAClB,OAA2B;IAE3B,OAAO,iCAAiC,CAAC,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,CAAC,CAAA;AACpF,CAAC;AAqCM,KAAK,UAAU,gCAAgC,CACpD,EAAuB,EACvB,QAA+C,EAC/C,OAAiD;IAEjD,QAAQ,CAAC,EAAE,CAAC,CAAA;IAEZ,MAAM,GAAG,GAAG,eAAe,CACzB,EAAE,EACF,uBAAuB,EACvB,QAAQ,CAAC,yBAAyB,EAClC,OAAO,EAAE,CAAC,qBAAqB,CAAC,KAAK,IAAI,CAC1C,CAAA;IAED,MAAM,OAAO,GAAG,cAAc,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;IAChD,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,kBAAkB,CAAC,CAAA;IACzC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,kBAAkB,CAAC,CAAA;IAE/C,MAAM,MAAM,GAAG,MAAM,CAAA;IAErB,IAAI,OAAO,EAAE,IAAI,EAAE,CAAC;QAClB,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;QACxB,MAAM,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,kBAAkB,CAAC,CAAA;IAC/E,CAAC;IAED,IAAI,OAAO,EAAE,kBAAkB,EAAE,CAAC;QAChC,OAAO,CAAC,GAAG,CACT,eAAe,EACf,GAAG,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAA,CAAA,EAAI,OAAO,CAAC,kBAAkB,EAAE,CAC3E,CAAA;IACH,CAAC;IAED,MAAM,QAAQ,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC,WAAW,CAAC,IAAI,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE;QACjE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;QAC9B,OAAO,EAAE,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;QAC9C,MAAM;QACN,QAAQ,EAAE,QAAQ;QAClB,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS;KAC7D,CAAC,CAAA;IACF,OAAO,EAAE,IAAI,EAAE,UAAU,CAAC,QAAQ,CAAC,CAAA;IACnC,OAAO,QAAQ,CAAA;AACjB,CAAC;AAiBM,KAAK,UAAU,wCAAwC,CAC5D,QAAkB;IAElB,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE,CAAC;QACzC,MAAM,cAAc,CAAC,4CAA4C,EAAE,oBAAoB,CAAC,CAAA;IAC1F,CAAC;IAED,6BAA6B,CAAC,QAAQ,CAAC,CAAA;IAEvC,MAAM,mBAAmB,CAAC,QAAQ,EAAE,GAAG,EAAE,sCAAsC,CAAC,CAAA;IAEhF,sBAAsB,CAAC,QAAQ,CAAC,CAAA;IAChC,MAAM,IAAI,GAAG,MAAM,mBAAmB,CAAoB,QAAQ,CAAC,CAAA;IAEnE,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE,sCAAsC,EAAE,gBAAgB,EAAE;QACrF,IAAI,EAAE,IAAI;KACX,CAAC,CAAA;IAEF,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;QACrC,YAAY,CAAC,IAAI,CAAC,aAAa,EAAE,0CAA0C,EAAE,gBAAgB,EAAE;YAC7F,IAAI,EAAE,IAAI;SACX,CAAC,CAAA;IACJ,CAAC;IAED,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;QACvB,YAAY,CACV,IAAI,CAAC,wBAAwB,EAC7B,IAAI,EACJ,qDAAqD,EACrD,gBAAgB,EAChB;YACE,IAAI,EAAE,IAAI;SACX,CACF,CAAA;IACH,CAAC;IAED,OAAO,IAAI,CAAA;AACb,CAAC;AA+GM,KAAK,UAAU,wBAAwB,CAC5C,kBAAuB,EACvB,OAAmC;IAEnC,OAAO,gBAAgB,CACrB,kBAAkB,EAClB,oBAAoB,EACpB,CAAC,GAAG,EAAE,EAAE;QACN,gBAAgB,CAAC,GAAG,EAAE,sCAAsC,EAAE,IAAI,CAAC,CAAA;QACnE,OAAO,GAAG,CAAA;IACZ,CAAC,EACD,OAAO,CACR,CAAA;AACH,CAAC;AAgBM,KAAK,UAAU,gCAAgC,CACpD,0BAA+B,EAC/B,QAAkB;IAElB,MAAM,QAAQ,GAAG,0BAA4D,CAAA;IAC7E,IAAI,CAAC,CAAC,QAAQ,YAAY,GAAG,CAAC,IAAI,QAAQ,KAAK,iBAAiB,EAAE,CAAC;QACjE,MAAM,cAAc,CAClB,yDAAyD,EACzD,oBAAoB,CACrB,CAAA;IACH,CAAC;IAED,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE,CAAC;QACzC,MAAM,cAAc,CAAC,4CAA4C,EAAE,oBAAoB,CAAC,CAAA;IAC1F,CAAC;IAED,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;QAC5B,MAAM,GAAG,CACP,6FAA6F,EAC7F,uBAAuB,EACvB,QAAQ,CACT,CAAA;IACH,CAAC;IAED,sBAAsB,CAAC,QAAQ,CAAC,CAAA;IAChC,MAAM,IAAI,GAAG,MAAM,mBAAmB,CAAiB,QAAQ,CAAC,CAAA;IAEhE,YAAY,CAAC,IAAI,CAAC,QAAQ,EAAE,qCAAqC,EAAE,gBAAgB,EAAE;QACnF,IAAI,EAAE,IAAI;KACX,CAAC,CAAA;IAEF,IAAI,QAAQ,KAAK,iBAAiB,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,IAAI,EAAE,CAAC;QACpF,MAAM,GAAG,CACP,uEAAuE,EACvE,yBAAyB,EACzB;YAAE,QAAQ,EAAE,QAAQ,CAAC,IAAI;YAAE,IAAI,EAAE,IAAI;YAAE,SAAS,EAAE,UAAU;QAAA,CAAE,CAC/D,CAAA;IACH,CAAC;IAED,OAAO,IAAI,CAAA;AACb,CAAC;AAED,KAAK,UAAU,mBAAmB,CAChC,QAAkB,EAClB,QAAsC,qBAAqB;IAE3D,IAAI,IAAe,CAAA;IACnB,IAAI,CAAC;QACH,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAA;IAC9B,CAAC,CAAC,OAAO,KAAK,EAAE,CAAC;QACf,KAAK,CAAC,QAAQ,CAAC,CAAA;QACf,MAAM,GAAG,CAAC,yCAAyC,EAAE,WAAW,EAAE,KAAK,CAAC,CAAA;IAC1E,CAAC;IAED,IAAI,CAAC,YAAY,CAAI,IAAI,CAAC,EAAE,CAAC;QAC3B,MAAM,GAAG,CAAC,4CAA4C,EAAE,gBAAgB,EAAE;YAAE,IAAI,EAAE,IAAI;QAAA,CAAE,CAAC,CAAA;IAC3F,CAAC;IAED,OAAO,IAAI,CAAA;AACb,CAAC;AAWM,MAAM,OAAO,GAAG,MAAM,CAAA;AAWtB,MAAM,iBAAiB,GAAkB,MAAM,EAAE,CAAA;AAWjD,MAAM,eAAe,GAAkB,MAAM,EAAE,CAAA", "debugId": null}}, {"offset": {"line": 5033, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/Bonkai/node_modules/next-auth/lib/env.js"], "sourcesContent": ["// @ts-expect-error Next.js does not yet correctly use the `package.json#exports` field\nimport { NextRequest } from \"next/server\";\nimport { setEnvDefaults as coreSetEnvDefaults } from \"@auth/core\";\n/** If `NEXTAUTH_URL` or `AUTH_URL` is defined, override the request's URL. */\nexport function reqWithEnvURL(req) {\n    const url = process.env.AUTH_URL ?? process.env.NEXTAUTH_URL;\n    if (!url)\n        return req;\n    const { origin: envOrigin } = new URL(url);\n    const { href, origin } = req.nextUrl;\n    return new NextRequest(href.replace(origin, envOrigin), req);\n}\n/**\n * For backwards compatibility, `next-auth` checks for `NEXTAUTH_URL`\n * and the `basePath` by default is `/api/auth` instead of `/auth`\n * (which is the default for all other Auth.js integrations).\n *\n * For the same reason, `NEXTAUTH_SECRET` is also checked.\n */\nexport function setEnvDefaults(config) {\n    try {\n        config.secret ?? (config.secret = process.env.AUTH_SECRET ?? process.env.NEXTAUTH_SECRET);\n        const url = process.env.AUTH_URL ?? process.env.NEXTAUTH_URL;\n        if (!url)\n            return;\n        const { pathname } = new URL(url);\n        if (pathname === \"/\")\n            return;\n        config.basePath || (config.basePath = pathname);\n    }\n    catch {\n        // Catching and swallowing potential URL parsing errors, we'll fall\n        // back to `/api/auth` below.\n    }\n    finally {\n        config.basePath || (config.basePath = \"/api/auth\");\n        coreSetEnvDefaults(process.env, config, true);\n    }\n}\n"], "names": [], "mappings": "AAAA,uFAAuF;;;;;AACvF;AACA;AAAA;;;AAEO,SAAS,cAAc,GAAG;IAC7B,MAAM,MAAM,QAAQ,GAAG,CAAC,QAAQ,IAAI,QAAQ,GAAG,CAAC,YAAY;IAC5D,IAAI,CAAC,KACD,OAAO;IACX,MAAM,EAAE,QAAQ,SAAS,EAAE,GAAG,IAAI,IAAI;IACtC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,IAAI,OAAO;IACpC,OAAO,IAAI,gIAAA,CAAA,cAAW,CAAC,KAAK,OAAO,CAAC,QAAQ,YAAY;AAC5D;AAQO,SAAS,eAAe,MAAM;IACjC,IAAI;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,GAAG,QAAQ,GAAG,CAAC,WAAW,IAAI,QAAQ,GAAG,CAAC,eAAe;QACxF,MAAM,MAAM,QAAQ,GAAG,CAAC,QAAQ,IAAI,QAAQ,GAAG,CAAC,YAAY;QAC5D,IAAI,CAAC,KACD;QACJ,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,IAAI;QAC7B,IAAI,aAAa,KACb;QACJ,OAAO,QAAQ,IAAI,CAAC,OAAO,QAAQ,GAAG,QAAQ;IAClD,EACA,OAAM;IACF,mEAAmE;IACnE,6BAA6B;IACjC,SACQ;QACJ,OAAO,QAAQ,IAAI,CAAC,OAAO,QAAQ,GAAG,WAAW;QACjD,CAAA,GAAA,uJAAA,CAAA,iBAAkB,AAAD,EAAE,QAAQ,GAAG,EAAE,QAAQ;IAC5C;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5072, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/Bonkai/node_modules/next-auth/lib/index.js"], "sourcesContent": ["import { Auth, createActionURL } from \"@auth/core\";\n// @ts-expect-error Next.js does not yet correctly use the `package.json#exports` field\nimport { headers } from \"next/headers\";\n// @ts-expect-error Next.js does not yet correctly use the `package.json#exports` field\nimport { NextResponse } from \"next/server\";\nimport { reqWithEnvURL } from \"./env.js\";\nasync function getSession(headers, config) {\n    const url = createActionURL(\"session\", \n    // @ts-expect-error `x-forwarded-proto` is not nullable, next.js sets it by default\n    headers.get(\"x-forwarded-proto\"), headers, process.env, config);\n    const request = new Request(url, {\n        headers: { cookie: headers.get(\"cookie\") ?? \"\" },\n    });\n    return Auth(request, {\n        ...config,\n        callbacks: {\n            ...config.callbacks,\n            // Since we are server-side, we don't need to filter out the session data\n            // See https://authjs.dev/getting-started/migrating-to-v5#authenticating-server-side\n            // TODO: Taint the session data to prevent accidental leakage to the client\n            // https://react.dev/reference/react/experimental_taintObjectReference\n            async session(...args) {\n                const session = \n                // If the user defined a custom session callback, use that instead\n                (await config.callbacks?.session?.(...args)) ?? {\n                    ...args[0].session,\n                    expires: args[0].session.expires?.toISOString?.() ??\n                        args[0].session.expires,\n                };\n                const user = args[0].user ?? args[0].token;\n                return { user, ...session };\n            },\n        },\n    });\n}\nfunction isReqWrapper(arg) {\n    return typeof arg === \"function\";\n}\nexport function initAuth(config, onLazyLoad // To set the default env vars\n) {\n    if (typeof config === \"function\") {\n        return async (...args) => {\n            if (!args.length) {\n                // React Server Components\n                const _headers = await headers();\n                const _config = await config(undefined); // Review: Should we pass headers() here instead?\n                onLazyLoad?.(_config);\n                return getSession(_headers, _config).then((r) => r.json());\n            }\n            if (args[0] instanceof Request) {\n                // middleware.ts inline\n                // export { auth as default } from \"auth\"\n                const req = args[0];\n                const ev = args[1];\n                const _config = await config(req);\n                onLazyLoad?.(_config);\n                // args[0] is supposed to be NextRequest but the instanceof check is failing.\n                return handleAuth([req, ev], _config);\n            }\n            if (isReqWrapper(args[0])) {\n                // middleware.ts wrapper/route.ts\n                // import { auth } from \"auth\"\n                // export default auth((req) => { console.log(req.auth) }})\n                const userMiddlewareOrRoute = args[0];\n                return async (...args) => {\n                    const _config = await config(args[0]);\n                    onLazyLoad?.(_config);\n                    return handleAuth(args, _config, userMiddlewareOrRoute);\n                };\n            }\n            // API Routes, getServerSideProps\n            const request = \"req\" in args[0] ? args[0].req : args[0];\n            const response = \"res\" in args[0] ? args[0].res : args[1];\n            const _config = await config(request);\n            onLazyLoad?.(_config);\n            // @ts-expect-error -- request is NextRequest\n            return getSession(new Headers(request.headers), _config).then(async (authResponse) => {\n                const auth = await authResponse.json();\n                for (const cookie of authResponse.headers.getSetCookie())\n                    if (\"headers\" in response)\n                        response.headers.append(\"set-cookie\", cookie);\n                    else\n                        response.appendHeader(\"set-cookie\", cookie);\n                return auth;\n            });\n        };\n    }\n    return (...args) => {\n        if (!args.length) {\n            // React Server Components\n            return Promise.resolve(headers()).then((h) => getSession(h, config).then((r) => r.json()));\n        }\n        if (args[0] instanceof Request) {\n            // middleware.ts inline\n            // export { auth as default } from \"auth\"\n            const req = args[0];\n            const ev = args[1];\n            return handleAuth([req, ev], config);\n        }\n        if (isReqWrapper(args[0])) {\n            // middleware.ts wrapper/route.ts\n            // import { auth } from \"auth\"\n            // export default auth((req) => { console.log(req.auth) }})\n            const userMiddlewareOrRoute = args[0];\n            return async (...args) => {\n                return handleAuth(args, config, userMiddlewareOrRoute).then((res) => {\n                    return res;\n                });\n            };\n        }\n        // API Routes, getServerSideProps\n        const request = \"req\" in args[0] ? args[0].req : args[0];\n        const response = \"res\" in args[0] ? args[0].res : args[1];\n        return getSession(\n        // @ts-expect-error\n        new Headers(request.headers), config).then(async (authResponse) => {\n            const auth = await authResponse.json();\n            for (const cookie of authResponse.headers.getSetCookie())\n                if (\"headers\" in response)\n                    response.headers.append(\"set-cookie\", cookie);\n                else\n                    response.appendHeader(\"set-cookie\", cookie);\n            return auth;\n        });\n    };\n}\nasync function handleAuth(args, config, userMiddlewareOrRoute) {\n    const request = reqWithEnvURL(args[0]);\n    const sessionResponse = await getSession(request.headers, config);\n    const auth = await sessionResponse.json();\n    let authorized = true;\n    if (config.callbacks?.authorized) {\n        authorized = await config.callbacks.authorized({ request, auth });\n    }\n    let response = NextResponse.next?.();\n    if (authorized instanceof Response) {\n        // User returned a custom response, like redirecting to a page or 401, respect it\n        response = authorized;\n        const redirect = authorized.headers.get(\"Location\");\n        const { pathname } = request.nextUrl;\n        // If the user is redirecting to the same NextAuth.js action path as the current request,\n        // don't allow the redirect to prevent an infinite loop\n        if (redirect &&\n            isSameAuthAction(pathname, new URL(redirect).pathname, config)) {\n            authorized = true;\n        }\n    }\n    else if (userMiddlewareOrRoute) {\n        // Execute user's middleware/handler with the augmented request\n        const augmentedReq = request;\n        augmentedReq.auth = auth;\n        response =\n            (await userMiddlewareOrRoute(augmentedReq, args[1])) ??\n                NextResponse.next();\n    }\n    else if (!authorized) {\n        const signInPage = config.pages?.signIn ?? `${config.basePath}/signin`;\n        if (request.nextUrl.pathname !== signInPage) {\n            // Redirect to signin page by default if not authorized\n            const signInUrl = request.nextUrl.clone();\n            signInUrl.pathname = signInPage;\n            signInUrl.searchParams.set(\"callbackUrl\", request.nextUrl.href);\n            response = NextResponse.redirect(signInUrl);\n        }\n    }\n    const finalResponse = new Response(response?.body, response);\n    // Preserve cookies from the session response\n    for (const cookie of sessionResponse.headers.getSetCookie())\n        finalResponse.headers.append(\"set-cookie\", cookie);\n    return finalResponse;\n}\nfunction isSameAuthAction(requestPath, redirectPath, config) {\n    const action = redirectPath.replace(`${requestPath}/`, \"\");\n    const pages = Object.values(config.pages ?? {});\n    return ((actions.has(action) || pages.includes(redirectPath)) &&\n        redirectPath === requestPath);\n}\nconst actions = new Set([\n    \"providers\",\n    \"session\",\n    \"csrf\",\n    \"signin\",\n    \"signout\",\n    \"callback\",\n    \"verify-request\",\n    \"error\",\n]);\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AACA,uFAAuF;AACvF;AACA,uFAAuF;AACvF;AACA;;;;;AACA,eAAe,WAAW,OAAO,EAAE,MAAM;IACrC,MAAM,MAAM,CAAA,GAAA,uJAAA,CAAA,kBAAe,AAAD,EAAE,WAC5B,mFAAmF;IACnF,QAAQ,GAAG,CAAC,sBAAsB,SAAS,QAAQ,GAAG,EAAE;IACxD,MAAM,UAAU,IAAI,QAAQ,KAAK;QAC7B,SAAS;YAAE,QAAQ,QAAQ,GAAG,CAAC,aAAa;QAAG;IACnD;IACA,OAAO,CAAA,GAAA,yJAAA,CAAA,OAAI,AAAD,EAAE,SAAS;QACjB,GAAG,MAAM;QACT,WAAW;YACP,GAAG,OAAO,SAAS;YACnB,yEAAyE;YACzE,oFAAoF;YACpF,2EAA2E;YAC3E,sEAAsE;YACtE,MAAM,SAAQ,GAAG,IAAI;gBACjB,MAAM,UACN,kEAAkE;gBACjE,MAAM,OAAO,SAAS,EAAE,aAAa,SAAU;oBAC5C,GAAG,IAAI,CAAC,EAAE,CAAC,OAAO;oBAClB,SAAS,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,mBAC9B,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO;gBAC/B;gBACA,MAAM,OAAO,IAAI,CAAC,EAAE,CAAC,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC,KAAK;gBAC1C,OAAO;oBAAE;oBAAM,GAAG,OAAO;gBAAC;YAC9B;QACJ;IACJ;AACJ;AACA,SAAS,aAAa,GAAG;IACrB,OAAO,OAAO,QAAQ;AAC1B;AACO,SAAS,SAAS,MAAM,EAAE,WAAW,8BAA8B;AAA/B;IAEvC,IAAI,OAAO,WAAW,YAAY;QAC9B,OAAO,OAAO,GAAG;YACb,IAAI,CAAC,KAAK,MAAM,EAAE;gBACd,0BAA0B;gBAC1B,MAAM,WAAW,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;gBAC7B,MAAM,UAAU,MAAM,OAAO,YAAY,iDAAiD;gBAC1F,aAAa;gBACb,OAAO,WAAW,UAAU,SAAS,IAAI,CAAC,CAAC,IAAM,EAAE,IAAI;YAC3D;YACA,IAAI,IAAI,CAAC,EAAE,YAAY,SAAS;gBAC5B,uBAAuB;gBACvB,yCAAyC;gBACzC,MAAM,MAAM,IAAI,CAAC,EAAE;gBACnB,MAAM,KAAK,IAAI,CAAC,EAAE;gBAClB,MAAM,UAAU,MAAM,OAAO;gBAC7B,aAAa;gBACb,6EAA6E;gBAC7E,OAAO,WAAW;oBAAC;oBAAK;iBAAG,EAAE;YACjC;YACA,IAAI,aAAa,IAAI,CAAC,EAAE,GAAG;gBACvB,iCAAiC;gBACjC,8BAA8B;gBAC9B,2DAA2D;gBAC3D,MAAM,wBAAwB,IAAI,CAAC,EAAE;gBACrC,OAAO,OAAO,GAAG;oBACb,MAAM,UAAU,MAAM,OAAO,IAAI,CAAC,EAAE;oBACpC,aAAa;oBACb,OAAO,WAAW,MAAM,SAAS;gBACrC;YACJ;YACA,iCAAiC;YACjC,MAAM,UAAU,SAAS,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE;YACxD,MAAM,WAAW,SAAS,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE;YACzD,MAAM,UAAU,MAAM,OAAO;YAC7B,aAAa;YACb,6CAA6C;YAC7C,OAAO,WAAW,IAAI,QAAQ,QAAQ,OAAO,GAAG,SAAS,IAAI,CAAC,OAAO;gBACjE,MAAM,OAAO,MAAM,aAAa,IAAI;gBACpC,KAAK,MAAM,UAAU,aAAa,OAAO,CAAC,YAAY,GAClD,IAAI,aAAa,UACb,SAAS,OAAO,CAAC,MAAM,CAAC,cAAc;qBAEtC,SAAS,YAAY,CAAC,cAAc;gBAC5C,OAAO;YACX;QACJ;IACJ;IACA,OAAO,CAAC,GAAG;QACP,IAAI,CAAC,KAAK,MAAM,EAAE;YACd,0BAA0B;YAC1B,OAAO,QAAQ,OAAO,CAAC,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD,KAAK,IAAI,CAAC,CAAC,IAAM,WAAW,GAAG,QAAQ,IAAI,CAAC,CAAC,IAAM,EAAE,IAAI;QAC1F;QACA,IAAI,IAAI,CAAC,EAAE,YAAY,SAAS;YAC5B,uBAAuB;YACvB,yCAAyC;YACzC,MAAM,MAAM,IAAI,CAAC,EAAE;YACnB,MAAM,KAAK,IAAI,CAAC,EAAE;YAClB,OAAO,WAAW;gBAAC;gBAAK;aAAG,EAAE;QACjC;QACA,IAAI,aAAa,IAAI,CAAC,EAAE,GAAG;YACvB,iCAAiC;YACjC,8BAA8B;YAC9B,2DAA2D;YAC3D,MAAM,wBAAwB,IAAI,CAAC,EAAE;YACrC,OAAO,OAAO,GAAG;gBACb,OAAO,WAAW,MAAM,QAAQ,uBAAuB,IAAI,CAAC,CAAC;oBACzD,OAAO;gBACX;YACJ;QACJ;QACA,iCAAiC;QACjC,MAAM,UAAU,SAAS,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE;QACxD,MAAM,WAAW,SAAS,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE;QACzD,OAAO,WACP,mBAAmB;QACnB,IAAI,QAAQ,QAAQ,OAAO,GAAG,QAAQ,IAAI,CAAC,OAAO;YAC9C,MAAM,OAAO,MAAM,aAAa,IAAI;YACpC,KAAK,MAAM,UAAU,aAAa,OAAO,CAAC,YAAY,GAClD,IAAI,aAAa,UACb,SAAS,OAAO,CAAC,MAAM,CAAC,cAAc;iBAEtC,SAAS,YAAY,CAAC,cAAc;YAC5C,OAAO;QACX;IACJ;AACJ;AACA,eAAe,WAAW,IAAI,EAAE,MAAM,EAAE,qBAAqB;IACzD,MAAM,UAAU,CAAA,GAAA,4IAAA,CAAA,gBAAa,AAAD,EAAE,IAAI,CAAC,EAAE;IACrC,MAAM,kBAAkB,MAAM,WAAW,QAAQ,OAAO,EAAE;IAC1D,MAAM,OAAO,MAAM,gBAAgB,IAAI;IACvC,IAAI,aAAa;IACjB,IAAI,OAAO,SAAS,EAAE,YAAY;QAC9B,aAAa,MAAM,OAAO,SAAS,CAAC,UAAU,CAAC;YAAE;YAAS;QAAK;IACnE;IACA,IAAI,WAAW,gIAAA,CAAA,eAAY,CAAC,IAAI;IAChC,IAAI,sBAAsB,UAAU;QAChC,iFAAiF;QACjF,WAAW;QACX,MAAM,WAAW,WAAW,OAAO,CAAC,GAAG,CAAC;QACxC,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,OAAO;QACpC,yFAAyF;QACzF,uDAAuD;QACvD,IAAI,YACA,iBAAiB,UAAU,IAAI,IAAI,UAAU,QAAQ,EAAE,SAAS;YAChE,aAAa;QACjB;IACJ,OACK,IAAI,uBAAuB;QAC5B,+DAA+D;QAC/D,MAAM,eAAe;QACrB,aAAa,IAAI,GAAG;QACpB,WACI,AAAC,MAAM,sBAAsB,cAAc,IAAI,CAAC,EAAE,KAC9C,gIAAA,CAAA,eAAY,CAAC,IAAI;IAC7B,OACK,IAAI,CAAC,YAAY;QAClB,MAAM,aAAa,OAAO,KAAK,EAAE,UAAU,GAAG,OAAO,QAAQ,CAAC,OAAO,CAAC;QACtE,IAAI,QAAQ,OAAO,CAAC,QAAQ,KAAK,YAAY;YACzC,uDAAuD;YACvD,MAAM,YAAY,QAAQ,OAAO,CAAC,KAAK;YACvC,UAAU,QAAQ,GAAG;YACrB,UAAU,YAAY,CAAC,GAAG,CAAC,eAAe,QAAQ,OAAO,CAAC,IAAI;YAC9D,WAAW,gIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;QACrC;IACJ;IACA,MAAM,gBAAgB,IAAI,SAAS,UAAU,MAAM;IACnD,6CAA6C;IAC7C,KAAK,MAAM,UAAU,gBAAgB,OAAO,CAAC,YAAY,GACrD,cAAc,OAAO,CAAC,MAAM,CAAC,cAAc;IAC/C,OAAO;AACX;AACA,SAAS,iBAAiB,WAAW,EAAE,YAAY,EAAE,MAAM;IACvD,MAAM,SAAS,aAAa,OAAO,CAAC,GAAG,YAAY,CAAC,CAAC,EAAE;IACvD,MAAM,QAAQ,OAAO,MAAM,CAAC,OAAO,KAAK,IAAI,CAAC;IAC7C,OAAQ,CAAC,QAAQ,GAAG,CAAC,WAAW,MAAM,QAAQ,CAAC,aAAa,KACxD,iBAAiB;AACzB;AACA,MAAM,UAAU,IAAI,IAAI;IACpB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5271, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/Bonkai/node_modules/next-auth/lib/actions.js"], "sourcesContent": ["import { Auth, raw, skipCSR<PERSON>heck, createActionURL } from \"@auth/core\";\n// @ts-expect-error Next.js does not yet correctly use the `package.json#exports` field\nimport { headers as nextHeaders, cookies } from \"next/headers\";\n// @ts-expect-error Next.js does not yet correctly use the `package.json#exports` field\nimport { redirect } from \"next/navigation\";\nexport async function signIn(provider, options = {}, authorizationParams, config) {\n    const headers = new Headers(await nextHeaders());\n    const { redirect: shouldRedirect = true, redirectTo, ...rest } = options instanceof FormData ? Object.fromEntries(options) : options;\n    const callbackUrl = redirectTo?.toString() ?? headers.get(\"Referer\") ?? \"/\";\n    const signInURL = createActionURL(\"signin\", \n    // @ts-expect-error `x-forwarded-proto` is not nullable, next.js sets it by default\n    headers.get(\"x-forwarded-proto\"), headers, process.env, config);\n    if (!provider) {\n        signInURL.searchParams.append(\"callbackUrl\", callbackUrl);\n        if (shouldRedirect)\n            redirect(signInURL.toString());\n        return signInURL.toString();\n    }\n    let url = `${signInURL}/${provider}?${new URLSearchParams(authorizationParams)}`;\n    let foundProvider = {};\n    for (const providerConfig of config.providers) {\n        const { options, ...defaults } = typeof providerConfig === \"function\" ? providerConfig() : providerConfig;\n        const id = options?.id ?? defaults.id;\n        if (id === provider) {\n            foundProvider = {\n                id,\n                type: options?.type ?? defaults.type,\n            };\n            break;\n        }\n    }\n    if (!foundProvider.id) {\n        const url = `${signInURL}?${new URLSearchParams({ callbackUrl })}`;\n        if (shouldRedirect)\n            redirect(url);\n        return url;\n    }\n    if (foundProvider.type === \"credentials\") {\n        url = url.replace(\"signin\", \"callback\");\n    }\n    headers.set(\"Content-Type\", \"application/x-www-form-urlencoded\");\n    const body = new URLSearchParams({ ...rest, callbackUrl });\n    const req = new Request(url, { method: \"POST\", headers, body });\n    const res = await Auth(req, { ...config, raw, skipCSRFCheck });\n    const cookieJar = await cookies();\n    for (const c of res?.cookies ?? [])\n        cookieJar.set(c.name, c.value, c.options);\n    const responseUrl = res instanceof Response ? res.headers.get(\"Location\") : res.redirect;\n    // NOTE: if for some unexpected reason the responseUrl is not set,\n    // we redirect to the original url\n    const redirectUrl = responseUrl ?? url;\n    if (shouldRedirect)\n        return redirect(redirectUrl);\n    return redirectUrl;\n}\nexport async function signOut(options, config) {\n    const headers = new Headers(await nextHeaders());\n    headers.set(\"Content-Type\", \"application/x-www-form-urlencoded\");\n    const url = createActionURL(\"signout\", \n    // @ts-expect-error `x-forwarded-proto` is not nullable, next.js sets it by default\n    headers.get(\"x-forwarded-proto\"), headers, process.env, config);\n    const callbackUrl = options?.redirectTo ?? headers.get(\"Referer\") ?? \"/\";\n    const body = new URLSearchParams({ callbackUrl });\n    const req = new Request(url, { method: \"POST\", headers, body });\n    const res = await Auth(req, { ...config, raw, skipCSRFCheck });\n    const cookieJar = await cookies();\n    for (const c of res?.cookies ?? [])\n        cookieJar.set(c.name, c.value, c.options);\n    if (options?.redirect ?? true)\n        return redirect(res.redirect);\n    return res;\n}\nexport async function update(data, config) {\n    const headers = new Headers(await nextHeaders());\n    headers.set(\"Content-Type\", \"application/json\");\n    const url = createActionURL(\"session\", \n    // @ts-expect-error `x-forwarded-proto` is not nullable, next.js sets it by default\n    headers.get(\"x-forwarded-proto\"), headers, process.env, config);\n    const body = JSON.stringify({ data });\n    const req = new Request(url, { method: \"POST\", headers, body });\n    const res = await Auth(req, { ...config, raw, skipCSRFCheck });\n    const cookieJar = await cookies();\n    for (const c of res?.cookies ?? [])\n        cookieJar.set(c.name, c.value, c.options);\n    return res.body;\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAAA;AAAA;AAAA;AACA,uFAAuF;AACvF;AACA,uFAAuF;AACvF;AAAA;;;;AACO,eAAe,OAAO,QAAQ,EAAE,UAAU,CAAC,CAAC,EAAE,mBAAmB,EAAE,MAAM;IAC5E,MAAM,UAAU,IAAI,QAAQ,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAW,AAAD;IAC5C,MAAM,EAAE,UAAU,iBAAiB,IAAI,EAAE,UAAU,EAAE,GAAG,MAAM,GAAG,mBAAmB,WAAW,OAAO,WAAW,CAAC,WAAW;IAC7H,MAAM,cAAc,YAAY,cAAc,QAAQ,GAAG,CAAC,cAAc;IACxE,MAAM,YAAY,CAAA,GAAA,uJAAA,CAAA,kBAAe,AAAD,EAAE,UAClC,mFAAmF;IACnF,QAAQ,GAAG,CAAC,sBAAsB,SAAS,QAAQ,GAAG,EAAE;IACxD,IAAI,CAAC,UAAU;QACX,UAAU,YAAY,CAAC,MAAM,CAAC,eAAe;QAC7C,IAAI,gBACA,CAAA,GAAA,uLAAA,CAAA,WAAQ,AAAD,EAAE,UAAU,QAAQ;QAC/B,OAAO,UAAU,QAAQ;IAC7B;IACA,IAAI,MAAM,GAAG,UAAU,CAAC,EAAE,SAAS,CAAC,EAAE,IAAI,gBAAgB,sBAAsB;IAChF,IAAI,gBAAgB,CAAC;IACrB,KAAK,MAAM,kBAAkB,OAAO,SAAS,CAAE;QAC3C,MAAM,EAAE,OAAO,EAAE,GAAG,UAAU,GAAG,OAAO,mBAAmB,aAAa,mBAAmB;QAC3F,MAAM,KAAK,SAAS,MAAM,SAAS,EAAE;QACrC,IAAI,OAAO,UAAU;YACjB,gBAAgB;gBACZ;gBACA,MAAM,SAAS,QAAQ,SAAS,IAAI;YACxC;YACA;QACJ;IACJ;IACA,IAAI,CAAC,cAAc,EAAE,EAAE;QACnB,MAAM,MAAM,GAAG,UAAU,CAAC,EAAE,IAAI,gBAAgB;YAAE;QAAY,IAAI;QAClE,IAAI,gBACA,CAAA,GAAA,uLAAA,CAAA,WAAQ,AAAD,EAAE;QACb,OAAO;IACX;IACA,IAAI,cAAc,IAAI,KAAK,eAAe;QACtC,MAAM,IAAI,OAAO,CAAC,UAAU;IAChC;IACA,QAAQ,GAAG,CAAC,gBAAgB;IAC5B,MAAM,OAAO,IAAI,gBAAgB;QAAE,GAAG,IAAI;QAAE;IAAY;IACxD,MAAM,MAAM,IAAI,QAAQ,KAAK;QAAE,QAAQ;QAAQ;QAAS;IAAK;IAC7D,MAAM,MAAM,MAAM,CAAA,GAAA,yJAAA,CAAA,OAAI,AAAD,EAAE,KAAK;QAAE,GAAG,MAAM;QAAE,KAAA,kJAAA,CAAA,MAAG;QAAE,eAAA,kJAAA,CAAA,gBAAa;IAAC;IAC5D,MAAM,YAAY,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAC9B,KAAK,MAAM,KAAK,KAAK,WAAW,EAAE,CAC9B,UAAU,GAAG,CAAC,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,EAAE,OAAO;IAC5C,MAAM,cAAc,eAAe,WAAW,IAAI,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,QAAQ;IACxF,kEAAkE;IAClE,kCAAkC;IAClC,MAAM,cAAc,eAAe;IACnC,IAAI,gBACA,OAAO,CAAA,GAAA,uLAAA,CAAA,WAAQ,AAAD,EAAE;IACpB,OAAO;AACX;AACO,eAAe,QAAQ,OAAO,EAAE,MAAM;IACzC,MAAM,UAAU,IAAI,QAAQ,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAW,AAAD;IAC5C,QAAQ,GAAG,CAAC,gBAAgB;IAC5B,MAAM,MAAM,CAAA,GAAA,uJAAA,CAAA,kBAAe,AAAD,EAAE,WAC5B,mFAAmF;IACnF,QAAQ,GAAG,CAAC,sBAAsB,SAAS,QAAQ,GAAG,EAAE;IACxD,MAAM,cAAc,SAAS,cAAc,QAAQ,GAAG,CAAC,cAAc;IACrE,MAAM,OAAO,IAAI,gBAAgB;QAAE;IAAY;IAC/C,MAAM,MAAM,IAAI,QAAQ,KAAK;QAAE,QAAQ;QAAQ;QAAS;IAAK;IAC7D,MAAM,MAAM,MAAM,CAAA,GAAA,yJAAA,CAAA,OAAI,AAAD,EAAE,KAAK;QAAE,GAAG,MAAM;QAAE,KAAA,kJAAA,CAAA,MAAG;QAAE,eAAA,kJAAA,CAAA,gBAAa;IAAC;IAC5D,MAAM,YAAY,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAC9B,KAAK,MAAM,KAAK,KAAK,WAAW,EAAE,CAC9B,UAAU,GAAG,CAAC,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,EAAE,OAAO;IAC5C,IAAI,SAAS,YAAY,MACrB,OAAO,CAAA,GAAA,uLAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,QAAQ;IAChC,OAAO;AACX;AACO,eAAe,OAAO,IAAI,EAAE,MAAM;IACrC,MAAM,UAAU,IAAI,QAAQ,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAW,AAAD;IAC5C,QAAQ,GAAG,CAAC,gBAAgB;IAC5B,MAAM,MAAM,CAAA,GAAA,uJAAA,CAAA,kBAAe,AAAD,EAAE,WAC5B,mFAAmF;IACnF,QAAQ,GAAG,CAAC,sBAAsB,SAAS,QAAQ,GAAG,EAAE;IACxD,MAAM,OAAO,KAAK,SAAS,CAAC;QAAE;IAAK;IACnC,MAAM,MAAM,IAAI,QAAQ,KAAK;QAAE,QAAQ;QAAQ;QAAS;IAAK;IAC7D,MAAM,MAAM,MAAM,CAAA,GAAA,yJAAA,CAAA,OAAI,AAAD,EAAE,KAAK;QAAE,GAAG,MAAM;QAAE,KAAA,kJAAA,CAAA,MAAG;QAAE,eAAA,kJAAA,CAAA,gBAAa;IAAC;IAC5D,MAAM,YAAY,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAC9B,KAAK,MAAM,KAAK,KAAK,WAAW,EAAE,CAC9B,UAAU,GAAG,CAAC,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,EAAE,OAAO;IAC5C,OAAO,IAAI,IAAI;AACnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5398, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/Bonkai/node_modules/next-auth/index.js"], "sourcesContent": ["/**\n * _If you are looking to migrate from v4, visit the [Upgrade Guide (v5)](https://authjs.dev/getting-started/migrating-to-v5)._\n *\n * ## Installation\n *\n * ```bash npm2yarn\n * npm install next-auth@beta\n * ```\n *\n * ## Environment variable inference\n *\n * `NEXTAUTH_URL` and `NEXTAUTH_SECRET` have been inferred since v4.\n *\n * Since NextAuth.js v5 can also automatically infer environment variables that are prefixed with `AUTH_`.\n *\n * For example `AUTH_GITHUB_ID` and `AUTH_GITHUB_SECRET` will be used as the `clientId` and `clientSecret` options for the GitHub provider.\n *\n * :::tip\n * The environment variable name inferring has the following format for OAuth providers: `AUTH_{PROVIDER}_{ID|SECRET}`.\n *\n * `PROVIDER` is the uppercase snake case version of the provider's id, followed by either `ID` or `SECRET` respectively.\n * :::\n *\n * `AUTH_SECRET` and `AUTH_URL` are also aliased for `NEXTAUTH_SECRET` and `NEXTAUTH_URL` for consistency.\n *\n * To add social login to your app, the configuration becomes:\n *\n * ```ts title=\"auth.ts\"\n * import NextAuth from \"next-auth\"\n * import GitHub from \"next-auth/providers/github\"\n * export const { handlers, auth } = NextAuth({ providers: [ GitHub ] })\n * ```\n *\n * And the `.env.local` file:\n *\n * ```sh title=\".env.local\"\n * AUTH_GITHUB_ID=...\n * AUTH_GITHUB_SECRET=...\n * AUTH_SECRET=...\n * ```\n *\n * :::tip\n * In production, `AUTH_SECRET` is a required environment variable - if not set, NextAuth.js will throw an error. See [MissingSecretError](https://authjs.dev/reference/core/errors#missingsecret) for more details.\n * :::\n *\n * If you need to override the default values for a provider, you can still call it as a function `GitHub({...})` as before.\n *\n * ## Lazy initialization\n * You can also initialize NextAuth.js lazily (previously known as advanced intialization), which allows you to access the request context in the configuration in some cases, like Route Handlers, Middleware, API Routes or `getServerSideProps`.\n * The above example becomes:\n *\n * ```ts title=\"auth.ts\"\n * import NextAuth from \"next-auth\"\n * import GitHub from \"next-auth/providers/github\"\n * export const { handlers, auth } = NextAuth(req => {\n *  if (req) {\n *   console.log(req) // do something with the request\n *  }\n *  return { providers: [ GitHub ] }\n * })\n * ```\n *\n * :::tip\n * This is useful if you want to customize the configuration based on the request, for example, to add a different provider in staging/dev environments.\n * :::\n *\n * @module next-auth\n */\nimport { Auth, customFetch } from \"@auth/core\";\nimport { reqWithEnvURL, setEnvDefaults } from \"./lib/env.js\";\nimport { initAuth } from \"./lib/index.js\";\nimport { signIn, signOut, update } from \"./lib/actions.js\";\nexport { AuthError, CredentialsSignin } from \"@auth/core/errors\";\nexport { customFetch };\n/**\n *  Initialize NextAuth.js.\n *\n *  @example\n * ```ts title=\"auth.ts\"\n * import NextAuth from \"next-auth\"\n * import GitHub from \"@auth/core/providers/github\"\n *\n * export const { handlers, auth } = NextAuth({ providers: [GitHub] })\n * ```\n *\n * Lazy initialization:\n *\n * @example\n * ```ts title=\"auth.ts\"\n * import NextAuth from \"next-auth\"\n * import GitHub from \"@auth/core/providers/github\"\n *\n * export const { handlers, auth } = NextAuth(async (req) => {\n *   console.log(req) // do something with the request\n *   return {\n *     providers: [GitHub],\n *   },\n * })\n * ```\n */\nexport default function NextAuth(config) {\n    if (typeof config === \"function\") {\n        const httpHandler = async (req) => {\n            const _config = await config(req);\n            setEnvDefaults(_config);\n            return Auth(reqWithEnvURL(req), _config);\n        };\n        return {\n            handlers: { GET: httpHandler, POST: httpHandler },\n            // @ts-expect-error\n            auth: initAuth(config, (c) => setEnvDefaults(c)),\n            signIn: async (provider, options, authorizationParams) => {\n                const _config = await config(undefined);\n                setEnvDefaults(_config);\n                return signIn(provider, options, authorizationParams, _config);\n            },\n            signOut: async (options) => {\n                const _config = await config(undefined);\n                setEnvDefaults(_config);\n                return signOut(options, _config);\n            },\n            unstable_update: async (data) => {\n                const _config = await config(undefined);\n                setEnvDefaults(_config);\n                return update(data, _config);\n            },\n        };\n    }\n    setEnvDefaults(config);\n    const httpHandler = (req) => Auth(reqWithEnvURL(req), config);\n    return {\n        handlers: { GET: httpHandler, POST: httpHandler },\n        // @ts-expect-error\n        auth: initAuth(config),\n        signIn: (provider, options, authorizationParams) => {\n            return signIn(provider, options, authorizationParams, config);\n        },\n        signOut: (options) => {\n            return signOut(options, config);\n        },\n        unstable_update: (data) => {\n            return update(data, config);\n        },\n    };\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAmEC;;;AACD;AAAA;AACA;AACA;AACA;AACA;;;;;;;AA4Be,SAAS,SAAS,MAAM;IACnC,IAAI,OAAO,WAAW,YAAY;QAC9B,MAAM,cAAc,OAAO;YACvB,MAAM,UAAU,MAAM,OAAO;YAC7B,CAAA,GAAA,4IAAA,CAAA,iBAAc,AAAD,EAAE;YACf,OAAO,CAAA,GAAA,yJAAA,CAAA,OAAI,AAAD,EAAE,CAAA,GAAA,4IAAA,CAAA,gBAAa,AAAD,EAAE,MAAM;QACpC;QACA,OAAO;YACH,UAAU;gBAAE,KAAK;gBAAa,MAAM;YAAY;YAChD,mBAAmB;YACnB,MAAM,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,CAAC,IAAM,CAAA,GAAA,4IAAA,CAAA,iBAAc,AAAD,EAAE;YAC7C,QAAQ,OAAO,UAAU,SAAS;gBAC9B,MAAM,UAAU,MAAM,OAAO;gBAC7B,CAAA,GAAA,4IAAA,CAAA,iBAAc,AAAD,EAAE;gBACf,OAAO,CAAA,GAAA,gJAAA,CAAA,SAAM,AAAD,EAAE,UAAU,SAAS,qBAAqB;YAC1D;YACA,SAAS,OAAO;gBACZ,MAAM,UAAU,MAAM,OAAO;gBAC7B,CAAA,GAAA,4IAAA,CAAA,iBAAc,AAAD,EAAE;gBACf,OAAO,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EAAE,SAAS;YAC5B;YACA,iBAAiB,OAAO;gBACpB,MAAM,UAAU,MAAM,OAAO;gBAC7B,CAAA,GAAA,4IAAA,CAAA,iBAAc,AAAD,EAAE;gBACf,OAAO,CAAA,GAAA,gJAAA,CAAA,SAAM,AAAD,EAAE,MAAM;YACxB;QACJ;IACJ;IACA,CAAA,GAAA,4IAAA,CAAA,iBAAc,AAAD,EAAE;IACf,MAAM,cAAc,CAAC,MAAQ,CAAA,GAAA,yJAAA,CAAA,OAAI,AAAD,EAAE,CAAA,GAAA,4IAAA,CAAA,gBAAa,AAAD,EAAE,MAAM;IACtD,OAAO;QACH,UAAU;YAAE,KAAK;YAAa,MAAM;QAAY;QAChD,mBAAmB;QACnB,MAAM,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE;QACf,QAAQ,CAAC,UAAU,SAAS;YACxB,OAAO,CAAA,GAAA,gJAAA,CAAA,SAAM,AAAD,EAAE,UAAU,SAAS,qBAAqB;QAC1D;QACA,SAAS,CAAC;YACN,OAAO,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EAAE,SAAS;QAC5B;QACA,iBAAiB,CAAC;YACd,OAAO,CAAA,GAAA,gJAAA,CAAA,SAAM,AAAD,EAAE,MAAM;QACxB;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5550, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/Bonkai/node_modules/next-auth/providers/credentials.js"], "sourcesContent": ["export * from \"@auth/core/providers/credentials\";\nexport { default } from \"@auth/core/providers/credentials\";\n"], "names": [], "mappings": ";AAAA", "ignoreList": [0], "debugId": null}}]}