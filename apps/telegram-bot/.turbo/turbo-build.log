
[0m[2m[35m$[0m [2m[1mbun build src/index.ts --outdir dist --target node[0m
[1m1 | [0m[0m[35mimport[0m { openai } [0m[35mfrom[0m [0m[32m'ai'[0m[0m[2m;[0m
             [1m[31m[1m^[0m
[31merror[0m[2m: [0m[1mNo matching export in "../../node_modules/ai/dist/index.mjs" for import "openai"[0m
    [2mat [0m[36m/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/telegram-bot/src/lib/ai.ts[0m[2m:[0m[33m1[0m[2m:[0m[33m10[0m
[0m[31merror[0m[2m:[0m script [1m"build"[0m exited with code 1[0m
