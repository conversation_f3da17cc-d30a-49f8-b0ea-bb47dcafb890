[{"/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/(auth)/actions.ts": "1", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/(auth)/api/auth/[...nextauth]/route.ts": "2", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/(auth)/api/auth/guest/route.ts": "3", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/(auth)/auth.config.ts": "4", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/(auth)/auth.ts": "5", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/(auth)/login/page-clerk.tsx": "6", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/(auth)/login/page.tsx": "7", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/(auth)/register/page-clerk.tsx": "8", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/(auth)/register/page.tsx": "9", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/(chat)/actions.ts": "10", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/(chat)/api/chat/[id]/stream/route.ts": "11", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/(chat)/api/chat/route-clerk.ts": "12", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/(chat)/api/chat/route.ts": "13", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/(chat)/api/chat/schema.ts": "14", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/(chat)/api/document/route.ts": "15", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/(chat)/api/history/route.ts": "16", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/(chat)/api/suggestions/route.ts": "17", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/(chat)/api/vote/route.ts": "18", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/(chat)/chat/[id]/page.tsx": "19", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/(chat)/layout.tsx": "20", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/(chat)/page.tsx": "21", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/api/wallet/link/route.ts": "22", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/api/wallet/unlink/route.ts": "23", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/layout.tsx": "24", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/providers.tsx": "25", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/app-sidebar.tsx": "26", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/artifact-actions.tsx": "27", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/artifact-close-button.tsx": "28", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/artifact-messages.tsx": "29", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/artifact.tsx": "30", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/auth-form.tsx": "31", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/chat-header.tsx": "32", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/chat.tsx": "33", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/code-block.tsx": "34", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/code-editor.tsx": "35", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/console.tsx": "36", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/create-artifact.tsx": "37", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/data-stream-handler.tsx": "38", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/data-stream-provider.tsx": "39", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/diffview.tsx": "40", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/document-preview.tsx": "41", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/document-skeleton.tsx": "42", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/document.tsx": "43", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/greeting.tsx": "44", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/icons.tsx": "45", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/image-editor.tsx": "46", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/markdown.tsx": "47", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/message-actions.tsx": "48", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/message-editor.tsx": "49", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/message-reasoning.tsx": "50", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/message.tsx": "51", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/messages.tsx": "52", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/model-selector.tsx": "53", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/multimodal-input.tsx": "54", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/preview-attachment.tsx": "55", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/sheet-editor.tsx": "56", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/sidebar-history-item.tsx": "57", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/sidebar-history.tsx": "58", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/sidebar-toggle.tsx": "59", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/sidebar-user-nav.tsx": "60", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/sign-out-form.tsx": "61", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/submit-button.tsx": "62", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/suggested-actions.tsx": "63", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/suggestion.tsx": "64", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/text-editor.tsx": "65", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/theme-provider.tsx": "66", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/toast.tsx": "67", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/toolbar.tsx": "68", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/version-footer.tsx": "69", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/visibility-selector.tsx": "70", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/wallet-connect-button.tsx": "71", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/wallet-provider.tsx": "72", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/weather.tsx": "73", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/ai/entitlements.ts": "74", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/ai/models.test.ts": "75", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/ai/models.ts": "76", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/ai/prompts.ts": "77", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/ai/providers-openrouter.ts": "78", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/ai/providers.ts": "79", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/ai/tools/create-document.ts": "80", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/ai/tools/get-weather.ts": "81", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/ai/tools/request-suggestions.ts": "82", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/ai/tools/update-document.ts": "83", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/artifacts/server.ts": "84", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/auth/clerk.ts": "85", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/constants.ts": "86", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/db/queries.ts": "87", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/db/utils.ts": "88", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/editor/config.ts": "89", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/editor/diff.js": "90", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/editor/functions.tsx": "91", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/editor/react-renderer.tsx": "92", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/editor/suggestions.tsx": "93", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/errors.ts": "94", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/middleware/token-gate.ts": "95", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/types.ts": "96", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/utils.ts": "97", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/api/uploadthing/core.ts": "98", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/api/uploadthing/route.ts": "99", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/web3/aura-points-display.tsx": "100", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/web3/rate-limit-indicator.tsx": "101", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/web3/staking-position-card.tsx": "102", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/web3/staking-rewards-calculator.tsx": "103", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/web3/tier-badge.tsx": "104", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/web3/tier-benefits.tsx": "105", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/web3/tier-upgrade-prompt.tsx": "106", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/web3/token-usage-chart.tsx": "107", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/web3/transaction-status.tsx": "108", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/web3/usage-stats.tsx": "109", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/web3/wallet-linking-flow.tsx": "110", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/web3/wallet-status.tsx": "111", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/convex/client.ts": "112", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/convex/hooks.ts": "113", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/convex/queries.ts": "114", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/uploadthing-utils.ts": "115", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/uploadthing.ts": "116", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/web3/token-balance-display.tsx": "117", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/(auth)/link/page.tsx": "118", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/api/telegram/link/route.ts": "119", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/web3/index.ts": "120", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/web3/transaction-history.tsx": "121", "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/web3/web3-dashboard.tsx": "122"}, {"size": 1866, "mtime": 1751967031838, "results": "123", "hashOfConfig": "124"}, {"size": 47, "mtime": 1751967031839, "results": "125", "hashOfConfig": "124"}, {"size": 643, "mtime": 1751967031839, "results": "126", "hashOfConfig": "124"}, {"size": 356, "mtime": 1751967031839, "results": "127", "hashOfConfig": "124"}, {"size": 2037, "mtime": 1751967031839, "results": "128", "hashOfConfig": "124"}, {"size": 2288, "mtime": 1751969561367, "results": "129", "hashOfConfig": "124"}, {"size": 2427, "mtime": 1751967031839, "results": "130", "hashOfConfig": "124"}, {"size": 2110, "mtime": 1751969552271, "results": "131", "hashOfConfig": "124"}, {"size": 2622, "mtime": 1751967031839, "results": "132", "hashOfConfig": "124"}, {"size": 1437, "mtime": 1751967031839, "results": "133", "hashOfConfig": "124"}, {"size": 2970, "mtime": 1751967031840, "results": "134", "hashOfConfig": "124"}, {"size": 8829, "mtime": 1751969524380, "results": "135", "hashOfConfig": "124"}, {"size": 6862, "mtime": 1751967031840, "results": "136", "hashOfConfig": "124"}, {"size": 755, "mtime": 1751967031840, "results": "137", "hashOfConfig": "124"}, {"size": 2885, "mtime": 1751967031840, "results": "138", "hashOfConfig": "124"}, {"size": 938, "mtime": 1751967031840, "results": "139", "hashOfConfig": "124"}, {"size": 932, "mtime": 1751967031840, "results": "140", "hashOfConfig": "124"}, {"size": 1775, "mtime": 1751967031841, "results": "141", "hashOfConfig": "124"}, {"size": 1888, "mtime": 1751967031841, "results": "142", "hashOfConfig": "124"}, {"size": 987, "mtime": 1751967031841, "results": "143", "hashOfConfig": "124"}, {"size": 1278, "mtime": 1751967031842, "results": "144", "hashOfConfig": "124"}, {"size": 956, "mtime": 1751969349569, "results": "145", "hashOfConfig": "124"}, {"size": 731, "mtime": 1751969358654, "results": "146", "hashOfConfig": "124"}, {"size": 2295, "mtime": 1751969192563, "results": "147", "hashOfConfig": "124"}, {"size": 2161, "mtime": 1751980446990, "results": "148", "hashOfConfig": "124"}, {"size": 2066, "mtime": 1751967031844, "results": "149", "hashOfConfig": "124"}, {"size": 2971, "mtime": 1751967031844, "results": "150", "hashOfConfig": "124"}, {"size": 827, "mtime": 1751967031844, "results": "151", "hashOfConfig": "124"}, {"size": 2755, "mtime": 1751967031844, "results": "152", "hashOfConfig": "124"}, {"size": 16283, "mtime": 1751967031844, "results": "153", "hashOfConfig": "124"}, {"size": 1352, "mtime": 1751967031845, "results": "154", "hashOfConfig": "124"}, {"size": 3322, "mtime": 1751967031845, "results": "155", "hashOfConfig": "124"}, {"size": 5228, "mtime": 1751967031845, "results": "156", "hashOfConfig": "124"}, {"size": 820, "mtime": 1751967031845, "results": "157", "hashOfConfig": "124"}, {"size": 3347, "mtime": 1751967031845, "results": "158", "hashOfConfig": "124"}, {"size": 5926, "mtime": 1751967031845, "results": "159", "hashOfConfig": "124"}, {"size": 2978, "mtime": 1751967031845, "results": "160", "hashOfConfig": "124"}, {"size": 2093, "mtime": 1751967031845, "results": "161", "hashOfConfig": "124"}, {"size": 1028, "mtime": 1751967031845, "results": "162", "hashOfConfig": "124"}, {"size": 2879, "mtime": 1751967031845, "results": "163", "hashOfConfig": "124"}, {"size": 8151, "mtime": 1751967031845, "results": "164", "hashOfConfig": "124"}, {"size": 1749, "mtime": 1751967031846, "results": "165", "hashOfConfig": "124"}, {"size": 4378, "mtime": 1751967031846, "results": "166", "hashOfConfig": "124"}, {"size": 765, "mtime": 1751967031846, "results": "167", "hashOfConfig": "124"}, {"size": 54605, "mtime": 1751967031846, "results": "168", "hashOfConfig": "124"}, {"size": 1089, "mtime": 1751967031846, "results": "169", "hashOfConfig": "124"}, {"size": 2573, "mtime": 1751967031846, "results": "170", "hashOfConfig": "124"}, {"size": 5672, "mtime": 1751967031846, "results": "171", "hashOfConfig": "124"}, {"size": 2940, "mtime": 1751967031846, "results": "172", "hashOfConfig": "124"}, {"size": 2018, "mtime": 1751967031847, "results": "173", "hashOfConfig": "124"}, {"size": 12471, "mtime": 1751967031847, "results": "174", "hashOfConfig": "124"}, {"size": 2711, "mtime": 1751967031847, "results": "175", "hashOfConfig": "124"}, {"size": 3183, "mtime": 1751967031847, "results": "176", "hashOfConfig": "124"}, {"size": 11553, "mtime": 1751981241981, "results": "177", "hashOfConfig": "124"}, {"size": 1322, "mtime": 1751967031847, "results": "178", "hashOfConfig": "124"}, {"size": 3862, "mtime": 1751967031848, "results": "179", "hashOfConfig": "124"}, {"size": 3634, "mtime": 1751967031848, "results": "180", "hashOfConfig": "124"}, {"size": 11538, "mtime": 1751967031848, "results": "181", "hashOfConfig": "124"}, {"size": 827, "mtime": 1751967031848, "results": "182", "hashOfConfig": "124"}, {"size": 3975, "mtime": 1751967031848, "results": "183", "hashOfConfig": "124"}, {"size": 446, "mtime": 1751967031849, "results": "184", "hashOfConfig": "124"}, {"size": 814, "mtime": 1751967031849, "results": "185", "hashOfConfig": "124"}, {"size": 2679, "mtime": 1751967031849, "results": "186", "hashOfConfig": "124"}, {"size": 2474, "mtime": 1751967031849, "results": "187", "hashOfConfig": "124"}, {"size": 4465, "mtime": 1751967031850, "results": "188", "hashOfConfig": "124"}, {"size": 300, "mtime": 1751967031850, "results": "189", "hashOfConfig": "124"}, {"size": 2000, "mtime": 1751967031850, "results": "190", "hashOfConfig": "124"}, {"size": 12979, "mtime": 1751967031850, "results": "191", "hashOfConfig": "124"}, {"size": 3086, "mtime": 1751967031852, "results": "192", "hashOfConfig": "124"}, {"size": 3065, "mtime": 1751967031852, "results": "193", "hashOfConfig": "124"}, {"size": 3262, "mtime": 1751969552271, "results": "194", "hashOfConfig": "124"}, {"size": 1181, "mtime": 1751969318616, "results": "195", "hashOfConfig": "124"}, {"size": 8271, "mtime": 1751967031852, "results": "196", "hashOfConfig": "124"}, {"size": 658, "mtime": 1751967031853, "results": "197", "hashOfConfig": "124"}, {"size": 2665, "mtime": 1751967031853, "results": "198", "hashOfConfig": "124"}, {"size": 415, "mtime": 1751967031853, "results": "199", "hashOfConfig": "124"}, {"size": 4206, "mtime": 1751967031853, "results": "200", "hashOfConfig": "124"}, {"size": 3001, "mtime": 1751969439365, "results": "201", "hashOfConfig": "124"}, {"size": 997, "mtime": 1751967031854, "results": "202", "hashOfConfig": "124"}, {"size": 1948, "mtime": 1751967031854, "results": "203", "hashOfConfig": "124"}, {"size": 569, "mtime": 1751967031854, "results": "204", "hashOfConfig": "124"}, {"size": 2932, "mtime": 1751967031854, "results": "205", "hashOfConfig": "124"}, {"size": 1767, "mtime": 1751967031854, "results": "206", "hashOfConfig": "124"}, {"size": 2910, "mtime": 1751967031854, "results": "207", "hashOfConfig": "124"}, {"size": 1272, "mtime": 1751969206731, "results": "208", "hashOfConfig": "124"}, {"size": 457, "mtime": 1751967031854, "results": "209", "hashOfConfig": "124"}, {"size": 5277, "mtime": 1751981206430, "results": "210", "hashOfConfig": "124"}, {"size": 379, "mtime": 1751981048287, "results": "211", "hashOfConfig": "124"}, {"size": 1449, "mtime": 1751967031861, "results": "212", "hashOfConfig": "124"}, {"size": 13136, "mtime": 1751967031862, "results": "213", "hashOfConfig": "124"}, {"size": 1744, "mtime": 1751967031863, "results": "214", "hashOfConfig": "124"}, {"size": 270, "mtime": 1751967031864, "results": "215", "hashOfConfig": "124"}, {"size": 3835, "mtime": 1751967031864, "results": "216", "hashOfConfig": "124"}, {"size": 3726, "mtime": 1751967031865, "results": "217", "hashOfConfig": "124"}, {"size": 3466, "mtime": 1751969463744, "results": "218", "hashOfConfig": "124"}, {"size": 1567, "mtime": 1751967031865, "results": "219", "hashOfConfig": "124"}, {"size": 3036, "mtime": 1751967031865, "results": "220", "hashOfConfig": "124"}, {"size": 77, "mtime": 1751980779069, "results": "221", "hashOfConfig": "124"}, {"size": 275, "mtime": 1751980784998, "results": "222", "hashOfConfig": "124"}, {"size": 10079, "mtime": 1751981078438, "results": "223", "hashOfConfig": "124"}, {"size": 7522, "mtime": 1751980918878, "results": "224", "hashOfConfig": "124"}, {"size": 10861, "mtime": 1751981031378, "results": "225", "hashOfConfig": "124"}, {"size": 11714, "mtime": 1751981129390, "results": "226", "hashOfConfig": "124"}, {"size": 2231, "mtime": 1751980699540, "results": "227", "hashOfConfig": "124"}, {"size": 4687, "mtime": 1751981230215, "results": "228", "hashOfConfig": "124"}, {"size": 5035, "mtime": 1751980728881, "results": "229", "hashOfConfig": "124"}, {"size": 10873, "mtime": 1751980884325, "results": "230", "hashOfConfig": "124"}, {"size": 9740, "mtime": 1751981182889, "results": "231", "hashOfConfig": "124"}, {"size": 16468, "mtime": 1751980975254, "results": "232", "hashOfConfig": "124"}, {"size": 10283, "mtime": 1751980829827, "results": "233", "hashOfConfig": "124"}, {"size": 6797, "mtime": 1751981230215, "results": "234", "hashOfConfig": "124"}, {"size": 342, "mtime": 1751980355344, "results": "235", "hashOfConfig": "124"}, {"size": 3556, "mtime": 1751980377705, "results": "236", "hashOfConfig": "124"}, {"size": 7386, "mtime": 1751980497807, "results": "237", "hashOfConfig": "124"}, {"size": 204, "mtime": 1751980790468, "results": "238", "hashOfConfig": "124"}, {"size": 1750, "mtime": 1751980771194, "results": "239", "hashOfConfig": "124"}, {"size": 8914, "mtime": 1751981225353, "results": "240", "hashOfConfig": "124"}, {"size": 6729, "mtime": 1751981339071, "results": "241", "hashOfConfig": "124"}, {"size": 2492, "mtime": 1751981311984, "results": "242", "hashOfConfig": "124"}, {"size": 973, "mtime": 1751981280134, "results": "243", "hashOfConfig": "124"}, {"size": 11474, "mtime": 1751981270350, "results": "244", "hashOfConfig": "124"}, {"size": 8635, "mtime": 1751981329056, "results": "245", "hashOfConfig": "124"}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "zrqczq", {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "492", "messages": "493", "suppressedMessages": "494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "495", "messages": "496", "suppressedMessages": "497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "507", "messages": "508", "suppressedMessages": "509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "510", "messages": "511", "suppressedMessages": "512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "513", "messages": "514", "suppressedMessages": "515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "516", "messages": "517", "suppressedMessages": "518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "519", "messages": "520", "suppressedMessages": "521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "522", "messages": "523", "suppressedMessages": "524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "525", "messages": "526", "suppressedMessages": "527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "528", "messages": "529", "suppressedMessages": "530", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "531", "messages": "532", "suppressedMessages": "533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "534", "messages": "535", "suppressedMessages": "536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "537", "messages": "538", "suppressedMessages": "539", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "540", "messages": "541", "suppressedMessages": "542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "543", "messages": "544", "suppressedMessages": "545", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "546", "messages": "547", "suppressedMessages": "548", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "549", "messages": "550", "suppressedMessages": "551", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "552", "messages": "553", "suppressedMessages": "554", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "555", "messages": "556", "suppressedMessages": "557", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "558", "messages": "559", "suppressedMessages": "560", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "561", "messages": "562", "suppressedMessages": "563", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "564", "messages": "565", "suppressedMessages": "566", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "567", "messages": "568", "suppressedMessages": "569", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "570", "messages": "571", "suppressedMessages": "572", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "573", "messages": "574", "suppressedMessages": "575", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "576", "messages": "577", "suppressedMessages": "578", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "579", "messages": "580", "suppressedMessages": "581", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "582", "messages": "583", "suppressedMessages": "584", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "585", "messages": "586", "suppressedMessages": "587", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "588", "messages": "589", "suppressedMessages": "590", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "591", "messages": "592", "suppressedMessages": "593", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "594", "messages": "595", "suppressedMessages": "596", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "597", "messages": "598", "suppressedMessages": "599", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 5, "source": null}, {"filePath": "600", "messages": "601", "suppressedMessages": "602", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "603", "messages": "604", "suppressedMessages": "605", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "606", "messages": "607", "suppressedMessages": "608", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "609", "messages": "610", "suppressedMessages": "611", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/(auth)/actions.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/(auth)/api/auth/[...nextauth]/route.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/(auth)/api/auth/guest/route.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/(auth)/auth.config.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/(auth)/auth.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/(auth)/login/page-clerk.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/(auth)/login/page.tsx", ["612"], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/(auth)/register/page-clerk.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/(auth)/register/page.tsx", ["613"], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/(chat)/actions.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/(chat)/api/chat/[id]/stream/route.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/(chat)/api/chat/route-clerk.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/(chat)/api/chat/route.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/(chat)/api/chat/schema.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/(chat)/api/document/route.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/(chat)/api/history/route.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/(chat)/api/suggestions/route.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/(chat)/api/vote/route.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/(chat)/chat/[id]/page.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/(chat)/layout.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/(chat)/page.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/api/wallet/link/route.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/api/wallet/unlink/route.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/layout.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/providers.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/app-sidebar.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/artifact-actions.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/artifact-close-button.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/artifact-messages.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/artifact.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/auth-form.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/chat-header.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/chat.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/code-block.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/code-editor.tsx", [], ["614"], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/console.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/create-artifact.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/data-stream-handler.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/data-stream-provider.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/diffview.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/document-preview.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/document-skeleton.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/document.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/greeting.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/icons.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/image-editor.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/markdown.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/message-actions.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/message-editor.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/message-reasoning.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/message.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/messages.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/model-selector.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/multimodal-input.tsx", [], ["615"], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/preview-attachment.tsx", [], ["616"], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/sheet-editor.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/sidebar-history-item.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/sidebar-history.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/sidebar-toggle.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/sidebar-user-nav.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/sign-out-form.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/submit-button.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/suggested-actions.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/suggestion.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/text-editor.tsx", [], ["617"], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/theme-provider.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/toast.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/toolbar.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/version-footer.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/visibility-selector.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/wallet-connect-button.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/wallet-provider.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/weather.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/ai/entitlements.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/ai/models.test.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/ai/models.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/ai/prompts.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/ai/providers-openrouter.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/ai/providers.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/ai/tools/create-document.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/ai/tools/get-weather.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/ai/tools/request-suggestions.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/ai/tools/update-document.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/artifacts/server.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/auth/clerk.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/constants.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/db/queries.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/db/utils.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/editor/config.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/editor/diff.js", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/editor/functions.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/editor/react-renderer.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/editor/suggestions.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/errors.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/middleware/token-gate.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/types.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/utils.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/api/uploadthing/core.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/api/uploadthing/route.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/web3/aura-points-display.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/web3/rate-limit-indicator.tsx", ["618", "619"], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/web3/staking-position-card.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/web3/staking-rewards-calculator.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/web3/tier-badge.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/web3/tier-benefits.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/web3/tier-upgrade-prompt.tsx", ["620"], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/web3/token-usage-chart.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/web3/transaction-status.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/web3/usage-stats.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/web3/wallet-linking-flow.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/web3/wallet-status.tsx", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/convex/client.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/convex/hooks.ts", ["621"], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/convex/queries.ts", ["622"], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/uploadthing-utils.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/lib/uploadthing.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/web3/token-balance-display.tsx", ["623"], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/(auth)/link/page.tsx", ["624", "625", "626", "627", "628", "629"], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/app/api/telegram/link/route.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/web3/index.ts", [], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/web3/transaction-history.tsx", ["630"], [], "/Users/<USER>/Downloads/Coding/Personal/Bonkai/components/web3/web3-dashboard.tsx", [], [], {"ruleId": "631", "severity": 1, "message": "632", "line": 45, "column": 6, "nodeType": "633", "endLine": 45, "endColumn": 20, "suggestions": "634"}, {"ruleId": "631", "severity": 1, "message": "632", "line": 46, "column": 6, "nodeType": "633", "endLine": 46, "endColumn": 13, "suggestions": "635"}, {"ruleId": "631", "severity": 1, "message": "636", "line": 45, "column": 6, "nodeType": "633", "endLine": 45, "endColumn": 8, "suggestions": "637", "suppressions": "638"}, {"ruleId": "631", "severity": 1, "message": "639", "line": 99, "column": 6, "nodeType": "633", "endLine": 99, "endColumn": 8, "suggestions": "640", "suppressions": "641"}, {"ruleId": "642", "severity": 1, "message": "643", "line": 20, "column": 13, "nodeType": "644", "endLine": 25, "endColumn": 15, "suppressions": "645"}, {"ruleId": "631", "severity": 1, "message": "636", "line": 77, "column": 6, "nodeType": "633", "endLine": 77, "endColumn": 8, "suggestions": "646", "suppressions": "647"}, {"ruleId": "648", "severity": 2, "message": "649", "line": 157, "column": 24, "nodeType": "650", "messageId": "651", "suggestions": "652"}, {"ruleId": "648", "severity": 2, "message": "649", "line": 166, "column": 24, "nodeType": "650", "messageId": "651", "suggestions": "653"}, {"ruleId": "648", "severity": 2, "message": "649", "line": 67, "column": 16, "nodeType": "650", "messageId": "651", "suggestions": "654"}, {"ruleId": "655", "severity": 2, "message": "656", "line": 3, "column": 20, "nodeType": "657", "endLine": 3, "endColumn": 51}, {"ruleId": "655", "severity": 2, "message": "656", "line": 4, "column": 20, "nodeType": "657", "endLine": 4, "endColumn": 51}, {"ruleId": "642", "severity": 1, "message": "643", "line": 187, "column": 25, "nodeType": "644", "endLine": 191, "endColumn": 27}, {"ruleId": "658", "severity": 1, "message": "659", "line": 71, "column": 18, "nodeType": "660", "messageId": "661", "endLine": 71, "endColumn": 50, "fix": "662"}, {"ruleId": "658", "severity": 1, "message": "663", "line": 136, "column": 30, "nodeType": "660", "messageId": "661", "endLine": 136, "endColumn": 67, "fix": "664"}, {"ruleId": "658", "severity": 1, "message": "663", "line": 150, "column": 32, "nodeType": "660", "messageId": "661", "endLine": 150, "endColumn": 73, "fix": "665"}, {"ruleId": "658", "severity": 1, "message": "663", "line": 152, "column": 28, "nodeType": "660", "messageId": "661", "endLine": 152, "endColumn": 67, "fix": "666"}, {"ruleId": "658", "severity": 1, "message": "663", "line": 168, "column": 31, "nodeType": "660", "messageId": "661", "endLine": 168, "endColumn": 55, "fix": "667"}, {"ruleId": "648", "severity": 2, "message": "649", "line": 186, "column": 19, "nodeType": "650", "messageId": "651", "suggestions": "668"}, {"ruleId": "669", "severity": 1, "message": "670", "line": 245, "column": 23, "nodeType": "660", "messageId": "671", "endLine": 245, "endColumn": 114, "fix": "672"}, "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'router' and 'updateSession'. Either include them or remove the dependency array.", "ArrayExpression", ["673"], ["674"], "React Hook useEffect has a missing dependency: 'content'. Either include it or remove the dependency array.", ["675"], ["676"], "React Hook useEffect has missing dependencies: 'localStorageInput' and 'setInput'. Either include them or remove the dependency array. If 'setInput' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["677"], ["678"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", ["679"], ["680"], ["681"], "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["682", "683", "684", "685"], ["686", "687", "688", "689"], ["690", "691", "692", "693"], "import/no-unresolved", "Unable to resolve path to module '@/convex/_generated/dataModel'.", "Literal", "tailwindcss/enforces-shorthand", "Classnames 'h-8, w-8' could be replaced by the 'size-8' shorthand!", "JSXAttribute", "shorthandCandidateDetected", {"range": "694", "text": "695"}, "Classnames 'h-4, w-4' could be replaced by the 'size-4' shorthand!", {"range": "696", "text": "697"}, {"range": "698", "text": "699"}, {"range": "700", "text": "701"}, {"range": "702", "text": "703"}, ["704", "705", "706", "707"], "tailwindcss/migration-from-tailwind-2", "Classname 'transform' is not needed in Tailwind CSS v3!", "classnameNotNeeded", {"range": "708", "text": "709"}, {"desc": "710", "fix": "711"}, {"desc": "712", "fix": "713"}, {"desc": "714", "fix": "715"}, {"kind": "716", "justification": "717"}, {"desc": "718", "fix": "719"}, {"kind": "716", "justification": "717"}, {"kind": "716", "justification": "717"}, {"desc": "714", "fix": "720"}, {"kind": "716", "justification": "717"}, {"messageId": "721", "data": "722", "fix": "723", "desc": "724"}, {"messageId": "721", "data": "725", "fix": "726", "desc": "727"}, {"messageId": "721", "data": "728", "fix": "729", "desc": "730"}, {"messageId": "721", "data": "731", "fix": "732", "desc": "733"}, {"messageId": "721", "data": "734", "fix": "735", "desc": "724"}, {"messageId": "721", "data": "736", "fix": "737", "desc": "727"}, {"messageId": "721", "data": "738", "fix": "739", "desc": "730"}, {"messageId": "721", "data": "740", "fix": "741", "desc": "733"}, {"messageId": "721", "data": "742", "fix": "743", "desc": "724"}, {"messageId": "721", "data": "744", "fix": "745", "desc": "727"}, {"messageId": "721", "data": "746", "fix": "747", "desc": "730"}, {"messageId": "721", "data": "748", "fix": "749", "desc": "733"}, [1965, 1985], "size-8 animate-spin", [4128, 4153], "size-4 animate-spin mr-2", [4612, 4641], "size-4 text-green-600 mt-0.5", [4706, 4733], "size-4 text-red-600 mt-0.5", [5287, 5299], "size-4 mr-2", {"messageId": "721", "data": "750", "fix": "751", "desc": "724"}, {"messageId": "721", "data": "752", "fix": "753", "desc": "727"}, {"messageId": "721", "data": "754", "fix": "755", "desc": "730"}, {"messageId": "721", "data": "756", "fix": "757", "desc": "733"}, [7893, 7972], "absolute left-3 top-1/2 -translate-y-1/2 size-4 text-muted-foreground", "Update the dependencies array to be: [router, state.status, updateSession]", {"range": "758", "text": "759"}, "Update the dependencies array to be: [router, state, updateSession]", {"range": "760", "text": "761"}, "Update the dependencies array to be: [content]", {"range": "762", "text": "763"}, "directive", "", "Update the dependencies array to be: [localStorageInput, setInput]", {"range": "764", "text": "765"}, {"range": "766", "text": "763"}, "replaceWithAlt", {"alt": "767"}, {"range": "768", "text": "769"}, "Replace with `&apos;`.", {"alt": "770"}, {"range": "771", "text": "772"}, "Replace with `&lsquo;`.", {"alt": "773"}, {"range": "774", "text": "775"}, "Replace with `&#39;`.", {"alt": "776"}, {"range": "777", "text": "778"}, "Replace with `&rsquo;`.", {"alt": "767"}, {"range": "779", "text": "780"}, {"alt": "770"}, {"range": "781", "text": "782"}, {"alt": "773"}, {"range": "783", "text": "784"}, {"alt": "776"}, {"range": "785", "text": "786"}, {"alt": "767"}, {"range": "787", "text": "788"}, {"alt": "770"}, {"range": "789", "text": "790"}, {"alt": "773"}, {"range": "791", "text": "792"}, {"alt": "776"}, {"range": "793", "text": "794"}, {"alt": "767"}, {"range": "795", "text": "796"}, {"alt": "770"}, {"range": "797", "text": "798"}, {"alt": "773"}, {"range": "799", "text": "800"}, {"alt": "776"}, {"range": "801", "text": "802"}, [1193, 1207], "[router, state.status, updateSession]", [1389, 1396], "[router, state, updateSession]", [1348, 1350], "[content]", [2754, 2756], "[localStorageInput, setInput]", [1973, 1975], "&apos;", [5399, 5550], "\n                    You&apos;ve reached your hourly request limit. Please wait for the next hour or upgrade your tier for higher limits.\n                  ", "&lsquo;", [5399, 5550], "\n                    You&lsquo;ve reached your hourly request limit. Please wait for the next hour or upgrade your tier for higher limits.\n                  ", "&#39;", [5399, 5550], "\n                    You&#39;ve reached your hourly request limit. Please wait for the next hour or upgrade your tier for higher limits.\n                  ", "&rsquo;", [5399, 5550], "\n                    You&rsquo;ve reached your hourly request limit. Please wait for the next hour or upgrade your tier for higher limits.\n                  ", [5868, 5901], "\n                    You&apos;ve used ", [5868, 5901], "\n                    You&lsquo;ve used ", [5868, 5901], "\n                    You&#39;ve used ", [5868, 5901], "\n                    You&rsquo;ve used ", [2108, 2193], "\n            You&apos;ve unlocked all BonKai features. Welcome to Diamond tier!\n          ", [2108, 2193], "\n            You&lsquo;ve unlocked all BonKai features. Welcome to Diamond tier!\n          ", [2108, 2193], "\n            You&#39;ve unlocked all BonKai features. Welcome to Diamond tier!\n          ", [2108, 2193], "\n            You&rsquo;ve unlocked all BonKai features. Welcome to Diamond tier!\n          ", [5772, 5798], "Don&apos;t have a linking code?", [5772, 5798], "Don&lsquo;t have a linking code?", [5772, 5798], "Don&#39;t have a linking code?", [5772, 5798], "Don&rsquo;t have a linking code?"]