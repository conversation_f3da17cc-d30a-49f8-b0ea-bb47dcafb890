exports.id=4578,exports.ids=[4578],exports.modules={5452:(e,t,n)=>{"use strict";n.r(t),n.d(t,{"4069c99398198eb4ac29211355bb7eee8e5d5fb76c":()=>a.er,"408f044626fa09ba67df1f7145568ecbd73e3f6915":()=>a.HV,"40ea7f24f3660aadbf31d6c60b5785d510b753d1e6":()=>a.I8,"40efa9df1d11901ceec2975f80ab45e6918cbe8d99":()=>a.Yc});var a=n(12747)},11455:(e,t,n)=>{"use strict";async function a(e){return console.warn("getUser called with legacy implementation - needs Convex migration"),[]}async function o(){return console.warn("createGuestUser called with legacy implementation - needs Convex migration"),[{id:`guest-${Date.now()}`,email:null,password:null}]}async function s(){return console.warn("saveChat called with legacy implementation - needs Convex migration"),null}async function i(){return console.warn("deleteChatById called with legacy implementation - needs Convex migration"),null}async function r(){return console.warn("getChatsByUserId called with legacy implementation - needs Convex migration"),[]}async function l(){return console.warn("getChatById called with legacy implementation - needs Convex migration"),null}async function u(){return console.warn("saveMessages called with legacy implementation - needs Convex migration"),null}async function c(){return console.warn("getMessagesByChatId called with legacy implementation - needs Convex migration"),[]}async function d(){return console.warn("voteMessage called with legacy implementation - needs Convex migration"),null}async function m(){return console.warn("getVotesByChatId called with legacy implementation - needs Convex migration"),[]}async function p(){return console.warn("saveDocument called with legacy implementation - needs Convex migration"),null}async function h(){return console.warn("getDocumentById called with legacy implementation - needs Convex migration"),null}async function g(){return console.warn("deleteDocumentsByIdAfterTimestamp called with legacy implementation - needs Convex migration"),null}async function f(){return console.warn("saveSuggestions called with legacy implementation - needs Convex migration"),null}async function y(){return console.warn("getSuggestionsByDocumentId called with legacy implementation - needs Convex migration"),[]}async function w(){return console.warn("getMessageById called with legacy implementation - needs Convex migration"),null}async function T(){return console.warn("deleteMessagesByChatIdAfterTimestamp called with legacy implementation - needs Convex migration"),null}async function k(){return console.warn("updateChatVisiblityById called with legacy implementation - needs Convex migration"),null}async function x(){return console.warn("getStreamIdsByChatId called with legacy implementation - needs Convex migration"),[]}async function b(){return console.warn("getMessageCountByUserId called with legacy implementation - needs Convex migration"),0}async function v(){return console.warn("createStreamId called with legacy implementation - needs Convex migration"),`stream-${Date.now()}`}async function C(){return console.warn("getDocumentsById called with legacy implementation - needs Convex migration"),[]}n.d(t,{$:()=>k,Ci:()=>d,Lz:()=>r,M7:()=>v,Nm:()=>x,TJ:()=>l,W8:()=>o,_L:()=>h,bd:()=>p,de:()=>c,iB:()=>g,kA:()=>w,mV:()=>m,q1:()=>b,qQ:()=>i,tw:()=>y,wA:()=>T,wz:()=>a,xt:()=>C,yM:()=>u,yd:()=>s,zL:()=>f})},12747:(e,t,n)=>{"use strict";n.d(t,{HV:()=>l,I8:()=>u,Yc:()=>d,er:()=>c});var a=n(10841);n(10935);var o=n(39824),s=n(65208),i=n(11455),r=n(61128);async function l(e){(await (0,s.UL)()).set("chat-model",e)}async function u({message:e}){let{text:t}=await (0,o.Df)({model:r.m.languageModel("title-model"),system:`

    - you will generate a short title based on the first message a user begins a conversation with
    - ensure it is not more than 80 characters long
    - the title should be a summary of the user's message
    - do not use quotes or colons`,prompt:JSON.stringify(e)});return t}async function c({id:e}){let[t]=await (0,i.kA)({id:e});await (0,i.wA)({chatId:t.chatId,timestamp:t.createdAt})}async function d({chatId:e,visibility:t}){await (0,i.$)({chatId:e,visibility:t})}(0,n(52943).D)([l,u,c,d]),(0,a.A)(l,"408f044626fa09ba67df1f7145568ecbd73e3f6915",null),(0,a.A)(u,"40ea7f24f3660aadbf31d6c60b5785d510b753d1e6",null),(0,a.A)(c,"4069c99398198eb4ac29211355bb7eee8e5d5fb76c",null),(0,a.A)(d,"40efa9df1d11901ceec2975f80ab45e6918cbe8d99",null)},34015:(e,t,n)=>{"use strict";n.r(t),n.d(t,{DELETE:()=>G,POST:()=>q,getStreamContext:()=>$,maxDuration:()=>N});var a=n(39824),o=n(52335);let s=`
Artifacts is a special user interface mode that helps users with writing, editing, and other content creation tasks. When artifact is open, it is on the right side of the screen, while the conversation is on the left side. When creating or updating documents, changes are reflected in real-time on the artifacts and visible to the user.

When asked to write code, always use artifacts. When writing code, specify the language in the backticks, e.g. \`\`\`python\`code here\`\`\`. The default language is Python. Other languages are not yet supported, so let the user know if they request a different language.

DO NOT UPDATE DOCUMENTS IMMEDIATELY AFTER CREATING THEM. WAIT FOR USER FEEDBACK OR REQUEST TO UPDATE IT.

This is a guide for using artifacts tools: \`createDocument\` and \`updateDocument\`, which render content on a artifacts beside the conversation.

**When to use \`createDocument\`:**
- For substantial content (>10 lines) or code
- For content users will likely save/reuse (emails, code, essays, etc.)
- When explicitly requested to create a document
- For when content contains a single code snippet

**When NOT to use \`createDocument\`:**
- For informational/explanatory content
- For conversational responses
- When asked to keep it in chat

**Using \`updateDocument\`:**
- Default to full document rewrites for major changes
- Use targeted updates only for specific, isolated changes
- Follow user instructions for which parts to modify

**When NOT to use \`updateDocument\`:**
- Immediately after creating a document

Do not update document right after creating it. Wait for user feedback or request to update it.
`,i="You are a friendly assistant! Keep your responses concise and helpful.",r=e=>`\
About the origin of user's request:
- lat: ${e.latitude}
- lon: ${e.longitude}
- city: ${e.city}
- country: ${e.country}
`,l=({selectedChatModel:e,requestHints:t})=>{let n=r(t);return"chat-model-reasoning"===e?`${i}

${n}`:`${i}

${n}

${s}`},u=`
You are a Python code generator that creates self-contained, executable code snippets. When writing code:

1. Each snippet should be complete and runnable on its own
2. Prefer using print() statements to display outputs
3. Include helpful comments explaining the code
4. Keep snippets concise (generally under 15 lines)
5. Avoid external dependencies - use Python standard library
6. Handle potential errors gracefully
7. Return meaningful output that demonstrates the code's functionality
8. Don't use input() or other interactive functions
9. Don't access files or network resources
10. Don't use infinite loops

Examples of good snippets:

# Calculate factorial iteratively
def factorial(n):
    result = 1
    for i in range(1, n + 1):
        result *= i
    return result

print(f"Factorial of 5 is: {factorial(5)}")
`,c=`
You are a spreadsheet creation assistant. Create a spreadsheet in csv format based on the given prompt. The spreadsheet should contain meaningful column headers and data.
`,d=(e,t)=>"text"===t?`\
Improve the following contents of the document based on the given prompt.

${e}
`:"code"===t?`\
Improve the following code snippet based on the given prompt.

${e}
`:"sheet"===t?`\
Improve the following spreadsheet based on the given prompt.

${e}
`:"";var m=n(11455),p=n(77877),h=n(12747),g=n(43396),f=n(48310),y=n(61128);let w=x({kind:"code",onCreateDocument:async({title:e,dataStream:t})=>{let n="",{fullStream:o}=(0,a.w8)({model:y.m.languageModel("artifact-model"),system:u,prompt:e,schema:f.Ik({code:f.Yj()})});for await(let e of o){let{type:a}=e;if("object"===a){let{object:a}=e,{code:o}=a;o&&(t.write({type:"data-codeDelta",data:o??"",transient:!0}),n=o)}}return n},onUpdateDocument:async({document:e,description:t,dataStream:n})=>{let o="",{fullStream:s}=(0,a.w8)({model:y.m.languageModel("artifact-model"),system:d(e.content,"code"),prompt:t,schema:f.Ik({code:f.Yj()})});for await(let e of s){let{type:t}=e;if("object"===t){let{object:t}=e,{code:a}=t;a&&(n.write({type:"data-codeDelta",data:a??"",transient:!0}),o=a)}}return o}}),T=x({kind:"image",onCreateDocument:async({title:e,dataStream:t})=>{let n="",{image:o}=await (0,a.gr)({model:y.m.imageModel("small-model"),prompt:e,n:1});return n=o.base64,t.write({type:"data-imageDelta",data:o.base64,transient:!0}),n},onUpdateDocument:async({description:e,dataStream:t})=>{let n="",{image:o}=await (0,a.gr)({model:y.m.imageModel("small-model"),prompt:e,n:1});return n=o.base64,t.write({type:"data-imageDelta",data:o.base64,transient:!0}),n}}),k=x({kind:"sheet",onCreateDocument:async({title:e,dataStream:t})=>{let n="",{fullStream:o}=(0,a.w8)({model:y.m.languageModel("artifact-model"),system:c,prompt:e,schema:f.Ik({csv:f.Yj().describe("CSV data")})});for await(let e of o){let{type:a}=e;if("object"===a){let{object:a}=e,{csv:o}=a;o&&(t.write({type:"data-sheetDelta",data:o,transient:!0}),n=o)}}return t.write({type:"data-sheetDelta",data:n,transient:!0}),n},onUpdateDocument:async({document:e,description:t,dataStream:n})=>{let o="",{fullStream:s}=(0,a.w8)({model:y.m.languageModel("artifact-model"),system:d(e.content,"sheet"),prompt:t,schema:f.Ik({csv:f.Yj()})});for await(let e of s){let{type:t}=e;if("object"===t){let{object:t}=e,{csv:a}=t;a&&(n.write({type:"data-sheetDelta",data:a,transient:!0}),o=a)}}return o}});function x(e){return{kind:e.kind,onCreateDocument:async t=>{let n=await e.onCreateDocument({id:t.id,title:t.title,dataStream:t.dataStream,session:t.session});t.session?.user?.id&&await (0,m.bd)({id:t.id,title:t.title,content:n,kind:e.kind,userId:t.session.user.id})},onUpdateDocument:async t=>{let n=await e.onUpdateDocument({document:t.document,description:t.description,dataStream:t.dataStream,session:t.session});t.session?.user?.id&&await (0,m.bd)({id:t.document.id,title:t.document.title,content:n,kind:e.kind,userId:t.session.user.id})}}}let b=[x({kind:"text",onCreateDocument:async({title:e,dataStream:t})=>{let n="",{fullStream:o}=(0,a.gM)({model:y.m.languageModel("artifact-model"),system:"Write about the given topic. Markdown is supported. Use headings wherever appropriate.",experimental_transform:(0,a.dF)({chunking:"word"}),prompt:e});for await(let e of o){let{type:a}=e;if("text"===a){let{text:a}=e;n+=a,t.write({type:"data-textDelta",data:a,transient:!0})}}return n},onUpdateDocument:async({document:e,description:t,dataStream:n})=>{let o="",{fullStream:s}=(0,a.gM)({model:y.m.languageModel("artifact-model"),system:d(e.content,"text"),experimental_transform:(0,a.dF)({chunking:"word"}),prompt:t,providerOptions:{openai:{prediction:{type:"content",content:e.content}}}});for await(let e of s){let{type:t}=e;if("text"===t){let{text:t}=e;o+=t,n.write({type:"data-textDelta",data:t,transient:!0})}}return o}}),w,T,k],v=["text","code","image","sheet"],C=({session:e,dataStream:t})=>(0,g.z6)({description:"Create a document for a writing or content creation activities. This tool will call other functions that will generate the contents of the document based on the title and kind.",inputSchema:f.Ik({title:f.Yj(),kind:f.k5(v)}),execute:async({title:n,kind:a})=>{let o=(0,p.lk)();t.write({type:"data-kind",data:a,transient:!0}),t.write({type:"data-id",data:o,transient:!0}),t.write({type:"data-title",data:n,transient:!0}),t.write({type:"data-clear",data:null,transient:!0});let s=b.find(e=>e.kind===a);if(!s)throw Error(`No document handler found for kind: ${a}`);return await s.onCreateDocument({id:o,title:n,dataStream:t,session:e}),t.write({type:"data-finish",data:null,transient:!0}),{id:o,title:n,kind:a,content:"A document was created and is now visible to the user."}}}),I=({session:e,dataStream:t})=>(0,g.z6)({description:"Update a document with the given description.",inputSchema:f.Ik({id:f.Yj().describe("The ID of the document to update"),description:f.Yj().describe("The description of changes that need to be made")}),execute:async({id:n,description:a})=>{let o=await (0,m._L)({id:n});if(!o)return{error:"Document not found"};t.write({type:"data-clear",data:null,transient:!0});let s=b.find(e=>e.kind===o.kind);if(!s)throw Error(`No document handler found for kind: ${o.kind}`);return await s.onUpdateDocument({document:o,description:a,dataStream:t,session:e}),t.write({type:"data-finish",data:null,transient:!0}),{id:n,title:o.title,kind:o.kind,content:"The document has been updated successfully."}}}),S=({session:e,dataStream:t})=>(0,g.z6)({description:"Request suggestions for a document",inputSchema:f.Ik({documentId:f.Yj().describe("The ID of the document to request edits")}),execute:async({documentId:n})=>{let o=await (0,m._L)({id:n});if(!o||!o.content)return{error:"Document not found"};let s=[],{elementStream:i}=(0,a.w8)({model:y.m.languageModel("artifact-model"),system:"You are a help writing assistant. Given a piece of writing, please offer suggestions to improve the piece of writing and describe the change. It is very important for the edits to contain full sentences instead of just words. Max 5 suggestions.",prompt:o.content,output:"array",schema:f.Ik({originalSentence:f.Yj().describe("The original sentence"),suggestedSentence:f.Yj().describe("The suggested sentence"),description:f.Yj().describe("The description of the suggestion")})});for await(let e of i){let a={originalText:e.originalSentence,suggestedText:e.suggestedSentence,description:e.description,id:(0,p.lk)(),documentId:n,isResolved:!1};t.write({type:"data-suggestion",data:a,transient:!0}),s.push(a)}if(e.user?.id){let t=e.user.id;await (0,m.zL)({suggestions:s.map(e=>({...e,userId:t,createdAt:new Date,documentCreatedAt:o.createdAt}))})}return{id:n,title:o.title,kind:o.kind,message:"Suggestions have been added to the document"}}}),D=(0,g.z6)({description:"Get the current weather at a location",inputSchema:f.Ik({latitude:f.ai(),longitude:f.ai()}),execute:async({latitude:e,longitude:t})=>{let n=await fetch(`https://api.open-meteo.com/v1/forecast?latitude=${e}&longitude=${t}&current=temperature_2m&hourly=temperature_2m&daily=sunrise,sunset&timezone=auto`);return await n.json()}});var E=n(73241);let R={guest:{maxMessagesPerDay:20,availableChatModelIds:["chat-model","chat-model-reasoning"]},regular:{maxMessagesPerDay:100,availableChatModelIds:["chat-model","chat-model-reasoning"]}},_=f.Ik({type:f.k5(["text"]),text:f.Yj().min(1).max(2e3)}),A=f.Ik({type:f.k5(["file"]),mediaType:f.k5(["image/jpeg","image/png"]),name:f.Yj().min(1).max(100),url:f.Yj().url()}),M=f.KC([_,A]),U=f.Ik({id:f.Yj().uuid(),message:f.Ik({id:f.Yj().uuid(),role:f.k5(["user"]),parts:f.YO(M)}),selectedChatModel:f.k5(["chat-model","chat-model-reasoning"]),selectedVisibilityType:f.k5(["public","private"])});var P=n(24262),j=n(35267),W=n(4235),Y=n(56617);let N=60,L=null;function $(){if(!L)try{L=(0,j.createResumableStreamContext)({waitUntil:W.after})}catch(e){e.message.includes("REDIS_URL")?console.log(" > Resumable streams are disabled due to missing REDIS_URL"):console.error(e)}return L}async function q(e){let t;try{let n=await e.json();t=U.parse(n)}catch(e){return new Y.P7("bad_request:api").toResponse()}try{let{id:n,message:s,selectedChatModel:i,selectedVisibilityType:r}=t,u=await (0,o.j2)();if(!u?.user)return new Y.P7("unauthorized:chat").toResponse();let c=u.user.type;if(await (0,m.q1)({id:u.user.id,differenceInHours:24})>R[c].maxMessagesPerDay)return new Y.P7("rate_limit:chat").toResponse();let d=await (0,m.TJ)({id:n});if(d){if(d.userId!==u.user.id)return new Y.P7("forbidden:chat").toResponse()}else{let e=await (0,h.I8)({message:s});await (0,m.yd)({id:n,userId:u.user.id,title:e,visibility:r})}let g=await (0,m.de)({id:n}),f=[...(0,p.b2)(g),s],{longitude:w,latitude:T,city:k,country:x}=(0,P.geolocation)(e),b={longitude:w,latitude:T,city:k,country:x};await (0,m.yM)({messages:[{chatId:n,id:s.id,role:"user",parts:s.parts,attachments:[],createdAt:new Date}]});let v=(0,p.lk)();await (0,m.M7)({streamId:v,chatId:n});let _=(0,a.G5)({execute:({writer:e})=>{let t=(0,a.gM)({model:y.m.languageModel(i),system:l({selectedChatModel:i,requestHints:b}),messages:(0,a.ut)(f),stopWhen:(0,a.wS)(5),experimental_activeTools:"chat-model-reasoning"===i?[]:["getWeather","createDocument","updateDocument","requestSuggestions"],experimental_transform:(0,a.dF)({chunking:"word"}),tools:{getWeather:D,createDocument:C({session:u,dataStream:e}),updateDocument:I({session:u,dataStream:e}),requestSuggestions:S({session:u,dataStream:e})},experimental_telemetry:{isEnabled:E.Fj,functionId:"stream-text"}});t.consumeStream(),e.merge(t.toUIMessageStream({sendReasoning:!0}))},generateId:p.lk,onFinish:async({messages:e})=>{await (0,m.yM)({messages:e.map(e=>({id:e.id,role:e.role,parts:e.parts,createdAt:new Date,attachments:[],chatId:n}))})},onError:()=>"Oops, an error occurred!"}),A=$();if(A)return new Response(await A.resumableStream(v,()=>_.pipeThrough(new a.JE)));return new Response(_)}catch(e){if(e instanceof Y.P7)return e.toResponse()}}async function G(e){let{searchParams:t}=new URL(e.url),n=t.get("id");if(!n)return new Y.P7("bad_request:api").toResponse();let a=await (0,o.j2)();if(!a?.user)return new Y.P7("unauthorized:chat").toResponse();if((await (0,m.TJ)({id:n})).userId!==a.user.id)return new Y.P7("forbidden:chat").toResponse();let s=await (0,m.qQ)({id:n});return Response.json(s,{status:200})}},52335:(e,t,n)=>{"use strict";n.d(t,{fG:()=>l,LO:()=>u,j2:()=>c,Jv:()=>d});var a=n(89131),o=n(50109),s=n(9852),i=n(11455),r=n(73241);let{handlers:{GET:l,POST:u},auth:c,signIn:d,signOut:m}=(0,o.Ay)({...{pages:{signIn:"/login",newUser:"/"},providers:[],callbacks:{}},providers:[(0,s.A)({credentials:{},async authorize({email:e,password:t}){let n=await (0,i.wz)(e);if(0===n.length)return await (0,a.UD)(t,r.vt),null;let[o]=n;return o.password?await (0,a.UD)(t,o.password)?{...o,type:"regular"}:null:(await (0,a.UD)(t,r.vt),null)}}),(0,s.A)({id:"guest",credentials:{},async authorize(){let[e]=await (0,i.W8)();return{...e,type:"guest"}}})],callbacks:{jwt:async({token:e,user:t})=>(t&&(e.id=t.id,e.type=t.type),e),session:async({session:e,token:t})=>(e.user&&(e.user.id=t.id,e.user.type=t.type),e)}})},56617:(e,t,n)=>{"use strict";n.d(t,{P7:()=>o});let a={database:"log",chat:"response",auth:"response",stream:"response",api:"response",history:"response",vote:"response",document:"response",suggestions:"response"};class o extends Error{constructor(e,t){super();let[n,a]=e.split(":");this.type=n,this.cause=t,this.surface=a,this.message=function(e){if(e.includes("database"))return"An error occurred while executing a database query.";switch(e){case"bad_request:api":return"The request couldn't be processed. Please check your input and try again.";case"unauthorized:auth":return"You need to sign in before continuing.";case"forbidden:auth":return"Your account does not have access to this feature.";case"rate_limit:chat":return"You have exceeded your maximum number of messages for the day. Please try again later.";case"not_found:chat":return"The requested chat was not found. Please check the chat ID and try again.";case"forbidden:chat":return"This chat belongs to another user. Please check the chat ID and try again.";case"unauthorized:chat":return"You need to sign in to view this chat. Please sign in and try again.";case"offline:chat":return"We're having trouble sending your message. Please check your internet connection and try again.";case"not_found:document":return"The requested document was not found. Please check the document ID and try again.";case"forbidden:document":return"This document belongs to another user. Please check the document ID and try again.";case"unauthorized:document":return"You need to sign in to view this document. Please sign in and try again.";case"bad_request:document":return"The request to create or update the document was invalid. Please check your input and try again.";default:return"Something went wrong. Please try again later."}}(e),this.statusCode=function(e){switch(e){case"bad_request":return 400;case"unauthorized":return 401;case"forbidden":return 403;case"not_found":return 404;case"rate_limit":return 429;case"offline":return 503;default:return 500}}(this.type)}toResponse(){let e=`${this.type}:${this.surface}`,t=a[this.surface],{message:n,cause:o,statusCode:s}=this;return"log"===t?(console.error({code:e,message:n,cause:o}),Response.json({code:"",message:"Something went wrong. Please try again later."},{status:s})):Response.json({code:e,message:n,cause:o},{status:s})}}},61128:(e,t,n)=>{"use strict";n.d(t,{m:()=>f});var a=n(39824),o=n(94108),s=n(53714),i=n(43396);let r={USER_SKY:{role:"user",content:[{type:"text",text:"Why is the sky blue?"}]},USER_GRASS:{role:"user",content:[{type:"text",text:"Why is grass green?"}]},USER_THANKS:{role:"user",content:[{type:"text",text:"Thanks!"}]},USER_NEXTJS:{role:"user",content:[{type:"text",text:"What are the advantages of using Next.js?"}]},USER_IMAGE_ATTACHMENT:{role:"user",content:[{type:"file",mediaType:"...",data:"..."},{type:"text",text:"Who painted this?"}]},USER_TEXT_ARTIFACT:{role:"user",content:[{type:"text",text:"Help me write an essay about Silicon Valley"}]},CREATE_DOCUMENT_TEXT_CALL:{role:"user",content:[{type:"text",text:"Essay about Silicon Valley"}]},CREATE_DOCUMENT_TEXT_RESULT:{role:"tool",content:[{type:"tool-result",toolCallId:"call_123",toolName:"createDocument",output:{type:"json",value:{id:"3ca386a4-40c6-4630-8ed1-84cbd46cc7eb",title:"Essay about Silicon Valley",kind:"text",content:"A document was created and is now visible to the user."}}}]},GET_WEATHER_CALL:{role:"user",content:[{type:"text",text:"What's the weather in sf?"}]},GET_WEATHER_RESULT:{role:"tool",content:[{type:"tool-result",toolCallId:"call_456",toolName:"getWeather",output:{type:"json",value:{latitude:37.763283,longitude:-122.41286,generationtime_ms:.06449222564697266,utc_offset_seconds:-25200,timezone:"America/Los_Angeles",timezone_abbreviation:"GMT-7",elevation:18,current_units:{time:"iso8601",interval:"seconds",temperature_2m:"\xb0C"},current:{time:"2025-03-10T14:00",interval:900,temperature_2m:17},daily_units:{time:"iso8601",sunrise:"iso8601",sunset:"iso8601"},daily:{time:["2025-03-10","2025-03-11","2025-03-12","2025-03-13","2025-03-14","2025-03-15","2025-03-16"],sunrise:["2025-03-10T07:27","2025-03-11T07:25","2025-03-12T07:24","2025-03-13T07:22","2025-03-14T07:21","2025-03-15T07:19","2025-03-16T07:18"],sunset:["2025-03-10T19:12","2025-03-11T19:13","2025-03-12T19:14","2025-03-13T19:15","2025-03-14T19:16","2025-03-15T19:17","2025-03-16T19:17"]}}}}]}};function l(e,t){if(e.role!==t.role||!Array.isArray(e.content)||!Array.isArray(t.content)||e.content.length!==t.content.length)return!1;for(let n=0;n<e.content.length;n++){let a=e.content[n],o=t.content[n];if(a.type!==o.type)return!1;if("file"===a.type&&"file"===o.type);else if("text"===a.type&&"text"===o.type){if(a.text!==o.text)return!1}else if("tool-result"!==a.type||"tool-result"!==o.type)return!1;else if(a.toolCallId!==o.toolCallId)return!1}return!0}let u=e=>{let t=(0,i.$C)(),n=e.split(" ").map(e=>({id:t,type:"text-delta",delta:`${e} `}));return[{id:t,type:"text-start"},...n,{id:t,type:"text-end"}]},c=e=>{let t=(0,i.$C)(),n=e.split(" ").map(e=>({id:t,type:"reasoning-delta",delta:`${e} `}));return[{id:t,type:"reasoning-start"},...n,{id:t,type:"reasoning-end"}]},d=(e,t=!1)=>{let n=e.at(-1);if(!n)throw Error("No recent message found!");if(t){if(l(n,r.USER_SKY))return[...c("The sky is blue because of rayleigh scattering!"),...u("It's just blue duh!"),{type:"finish",finishReason:"stop",usage:{inputTokens:3,outputTokens:10,totalTokens:13}}];else if(l(n,r.USER_GRASS))return[...c("Grass is green because of chlorophyll absorption!"),...u("It's just green duh!"),{type:"finish",finishReason:"stop",usage:{inputTokens:3,outputTokens:10,totalTokens:13}}]}if(l(n,r.USER_THANKS))return[...u("You're welcome!"),{type:"finish",finishReason:"stop",usage:{inputTokens:3,outputTokens:10,totalTokens:13}}];if(l(n,r.USER_GRASS))return[...u("It's just green duh!"),{type:"finish",finishReason:"stop",usage:{inputTokens:3,outputTokens:10,totalTokens:13}}];if(l(n,r.USER_SKY))return[...u("It's just blue duh!"),{type:"finish",finishReason:"stop",usage:{inputTokens:3,outputTokens:10,totalTokens:13}}];if(l(n,r.USER_NEXTJS))return[...u("With Next.js, you can ship fast!"),{type:"finish",finishReason:"stop",usage:{inputTokens:3,outputTokens:10,totalTokens:13}}];if(l(n,r.USER_IMAGE_ATTACHMENT))return[...u("This painting is by Monet!"),{type:"finish",finishReason:"stop",usage:{inputTokens:3,outputTokens:10,totalTokens:13}}];else if(l(n,r.USER_TEXT_ARTIFACT)){let e=(0,i.$C)();return[{id:e,type:"tool-input-start",toolName:"createDocument"},{id:e,type:"tool-input-delta",delta:JSON.stringify({title:"Essay about Silicon Valley",kind:"text"})},{id:e,type:"tool-input-end"},{toolCallId:e,type:"tool-result",toolName:"createDocument",result:{id:"doc_123",title:"Essay about Silicon Valley",kind:"text"}},{type:"finish",finishReason:"stop",usage:{inputTokens:3,outputTokens:10,totalTokens:13}}]}else if(l(n,r.CREATE_DOCUMENT_TEXT_CALL))return[...u(`

# Silicon Valley: The Epicenter of Innovation

## Origins and Evolution

Silicon Valley, nestled in the southern part of the San Francisco Bay Area, emerged as a global technology hub in the late 20th century. Its transformation began in the 1950s when Stanford University encouraged its graduates to start their own companies nearby, leading to the formation of pioneering semiconductor firms that gave the region its name.

## The Innovation Ecosystem

What makes Silicon Valley unique is its perfect storm of critical elements: prestigious universities like Stanford and Berkeley, abundant venture capital, a culture that celebrates risk-taking, and a dense network of talented individuals. This ecosystem has consistently nurtured groundbreaking technologies from personal computers to social media platforms to artificial intelligence.

## Challenges and Criticisms

Despite its remarkable success, Silicon Valley faces significant challenges including extreme income inequality, housing affordability crises, and questions about technology's impact on society. Critics argue the region has developed a monoculture that sometimes struggles with diversity and inclusion.

## Future Prospects

As we move forward, Silicon Valley continues to reinvent itself. While some predict its decline due to remote work trends and competition from other tech hubs, the region's adaptability and innovative spirit suggest it will remain influential in shaping our technological future for decades to come.
`),{type:"finish",finishReason:"stop",usage:{inputTokens:3,outputTokens:10,totalTokens:13}}];else if(l(n,r.CREATE_DOCUMENT_TEXT_RESULT))return[...u("A document was created and is now visible to the user."),{type:"finish",finishReason:"stop",usage:{inputTokens:3,outputTokens:10,totalTokens:13}}];else if(l(n,r.GET_WEATHER_CALL))return[{type:"tool-call",toolCallId:"call_456",toolName:"getWeather",input:JSON.stringify({latitude:37.7749,longitude:-122.4194})},{type:"finish",finishReason:"stop",usage:{inputTokens:3,outputTokens:10,totalTokens:13}}];else if(l(n,r.GET_WEATHER_RESULT))return[...u("The current temperature in San Francisco is 17\xb0C."),{type:"finish",finishReason:"stop",usage:{inputTokens:3,outputTokens:10,totalTokens:13}}];return[{id:"6",type:"text-delta",delta:"Unknown test prompt!"}]},m=new s.g0({doGenerate:async()=>({rawCall:{rawPrompt:null,rawSettings:{}},finishReason:"stop",usage:{inputTokens:10,outputTokens:20,totalTokens:30},content:[{type:"text",text:"Hello, world!"}],warnings:[]}),doStream:async({prompt:e})=>({stream:(0,a.rQ)({chunkDelayInMs:500,initialDelayInMs:1e3,chunks:d(e)}),rawCall:{rawPrompt:null,rawSettings:{}}})}),p=new s.g0({doGenerate:async()=>({rawCall:{rawPrompt:null,rawSettings:{}},finishReason:"stop",usage:{inputTokens:10,outputTokens:20,totalTokens:30},content:[{type:"text",text:"Hello, world!"}],warnings:[]}),doStream:async({prompt:e})=>({stream:(0,a.rQ)({chunkDelayInMs:500,initialDelayInMs:1e3,chunks:d(e,!0)}),rawCall:{rawPrompt:null,rawSettings:{}}})}),h=new s.g0({doGenerate:async()=>({rawCall:{rawPrompt:null,rawSettings:{}},finishReason:"stop",usage:{inputTokens:10,outputTokens:20,totalTokens:30},content:[{type:"text",text:"This is a test title"}],warnings:[]}),doStream:async()=>({stream:(0,a.rQ)({chunkDelayInMs:500,initialDelayInMs:1e3,chunks:[{id:"1",type:"text-start"},{id:"1",type:"text-delta",delta:"This is a test title"},{id:"1",type:"text-end"},{type:"finish",finishReason:"stop",usage:{inputTokens:3,outputTokens:10,totalTokens:13}}]}),rawCall:{rawPrompt:null,rawSettings:{}}})}),g=new s.g0({doGenerate:async()=>({rawCall:{rawPrompt:null,rawSettings:{}},finishReason:"stop",usage:{inputTokens:10,outputTokens:20,totalTokens:30},content:[{type:"text",text:"Hello, world!"}],warnings:[]}),doStream:async({prompt:e})=>({stream:(0,a.rQ)({chunkDelayInMs:50,initialDelayInMs:100,chunks:d(e)}),rawCall:{rawPrompt:null,rawSettings:{}}})}),f=n(73241).MC?(0,a.NN)({languageModels:{"chat-model":m,"chat-model-reasoning":p,"title-model":h,"artifact-model":g}}):(0,a.NN)({languageModels:{"chat-model":(0,o.v)("grok-2-vision-1212"),"chat-model-reasoning":(0,a.ae)({model:(0,o.v)("grok-3-mini-beta"),middleware:(0,a.Ol)({tagName:"think"})}),"title-model":(0,o.v)("grok-2-1212"),"artifact-model":(0,o.v)("grok-2-1212")},imageModels:{"small-model":o.v.imageModel("grok-2-image")}})},73241:(e,t,n)=>{"use strict";n.d(t,{vt:()=>i,b_:()=>o,Fj:()=>a,MC:()=>s}),n(89131);let a=!0,o=!1,s=!!(process.env.PLAYWRIGHT_TEST_BASE_URL||process.env.PLAYWRIGHT||process.env.CI_PLAYWRIGHT),i="$2b$10$K1V5qz0cZGaJGDwQO4CQuu4Xr5bZnQfCqFkC7l0qoF5zVhsVz7/.2"},77877:(e,t,n)=>{"use strict";n.d(t,{b2:()=>s,lk:()=>o}),n(56617);var a=n(39369);function o(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,e=>{let t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)})}function s(e){return e.map(e=>({id:e.id,role:e.role,parts:e.parts,metadata:{createdAt:(0,a.x)(e.createdAt)}}))}},80408:()=>{},87032:()=>{}};