(()=>{var e={};e.id=6615,e.ids=[6615],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5631:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNextRouterError",{enumerable:!0,get:function(){return a}});let s=r(22859),n=r(31903);function a(e){return(0,n.isRedirectError)(e)||(0,s.isHTTPAccessFallbackError)(e)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},11997:e=>{"use strict";e.exports=require("punycode")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19446:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(33952).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(auth)/link/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(auth)/link/page.tsx","default")},21820:e=>{"use strict";e.exports=require("os")},22859:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HTTPAccessErrorStatus:function(){return r},HTTP_ERROR_FALLBACK_ERROR_CODE:function(){return n},getAccessFallbackErrorTypeByStatus:function(){return o},getAccessFallbackHTTPStatus:function(){return i},isHTTPAccessFallbackError:function(){return a}});let r={NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401},s=new Set(Object.values(r)),n="NEXT_HTTP_ERROR_FALLBACK";function a(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r]=e.digest.split(";");return t===n&&s.has(Number(r))}function i(e){return Number(e.digest.split(";")[1])}function o(e){switch(e){case 401:return"unauthorized";case 403:return"forbidden";case 404:return"not-found";default:return}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28990:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>f,tree:()=>c});var s=r(24332),n=r(48819),a=r(67851),i=r.n(a),o=r(97540),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c={children:["",{children:["(auth)",{children:["link",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,19446)),"/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(auth)/link/page.tsx"]}]},{}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,19033,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,39956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,92341,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70196))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,34356)),"/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19033,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,39956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,92341,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70196))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(auth)/link/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},f=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/(auth)/link/page",pathname:"/link",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29727:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page-experimental.runtime.prod.js")},31903:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{REDIRECT_ERROR_CODE:function(){return n},RedirectType:function(){return a},isRedirectError:function(){return i}});let s=r(49005),n="NEXT_REDIRECT";var a=function(e){return e.push="push",e.replace="replace",e}({});function i(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let t=e.digest.split(";"),[r,a]=t,i=t.slice(2,-2).join(";"),o=Number(t.at(-2));return r===n&&("replace"===a||"push"===a)&&"string"==typeof i&&!isNaN(o)&&o in s.RedirectStatusCode}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},44989:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return l}});let s=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=n(t);if(r&&r.has(e))return r.get(e);var s={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var o=a?Object.getOwnPropertyDescriptor(e,i):null;o&&(o.get||o.set)?Object.defineProperty(s,i,o):s[i]=e[i]}return s.default=e,r&&r.set(e,s),s}(r(61365));function n(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(n=function(e){return e?r:t})(e)}let a={current:null},i="function"==typeof s.cache?s.cache:e=>e,o=console.warn;function l(e){return function(...t){o(e(...t))}}i(e=>{try{o(a.current)}finally{a.current=null}})},49005:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}});var r=function(e){return e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect",e}({});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58316:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"7f4c08c553bf967cd0b7c8f5250f46c2e73879b9bd":()=>s.ai,"7f8502d0204c4a0fa8f39abb8d058f5a7c86a9cae8":()=>s.at,"7fea155ec4cccf4e8d3a3d15a5aae1d42efed4db52":()=>s.ot,"7ff75850b73f1dfd41bbc2ede99fb927e18bc81cf0":()=>n.y});var s=r(93878),n=r(69335)},60998:(e,t,r)=>{Promise.resolve().then(r.bind(r,19446))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66037:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>k});var s=r(13486),n=r(60159),a=r(30352),i=r(2984),o=r(15984),l=r(80018),c=r(67499);let d=n.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,c.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));d.displayName="Card";let u=n.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,c.cn)("flex flex-col space-y-1.5 p-6",e),...t}));u.displayName="CardHeader";let f=n.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,c.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));f.displayName="CardTitle";let p=n.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,c.cn)("text-sm text-muted-foreground",e),...t}));p.displayName="CardDescription";let m=n.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,c.cn)("p-6 pt-0",e),...t}));m.displayName="CardContent",n.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,c.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter";let x=(0,r(76353).F)("relative w-full rounded-lg border px-4 py-3 text-sm [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground [&>svg~*]:pl-7",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),h=n.forwardRef(({className:e,variant:t,...r},n)=>(0,s.jsx)("div",{ref:n,role:"alert",className:(0,c.cn)(x({variant:t}),e),...r}));h.displayName="Alert",n.forwardRef(({className:e,...t},r)=>(0,s.jsx)("h5",{ref:r,className:(0,c.cn)("mb-1 font-medium leading-none tracking-tight",e),...t})).displayName="AlertTitle";let g=n.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,c.cn)("text-sm [&_p]:leading-relaxed",e),...t}));g.displayName="AlertDescription";var b=r(84667);let y=(0,b.A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]),v=(0,b.A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),j=(0,b.A)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]),_=(0,b.A)("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]);function k(){let{isSignedIn:e,isLoaded:t}=(0,a.d)(),r=(0,i.useSearchParams)().get("code"),[c,x]=(0,n.useState)(r||""),[b,k]=(0,n.useState)(!1),[N,w]=(0,n.useState)(null),P=async()=>{if(!c.trim())return void w({success:!1,message:"Please enter a valid linking code"});k(!0),w(null);try{let e=await fetch("/api/telegram/link",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({code:c.trim()})}),t=await e.json();e.ok&&t.success?w({success:!0,message:t.message}):w({success:!1,message:t.error||"Failed to link account"})}catch(e){w({success:!1,message:"Network error. Please try again."})}finally{k(!1)}};return t?e?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center p-4",children:(0,s.jsxs)(d,{className:"w-full max-w-md",children:[(0,s.jsxs)(u,{className:"text-center",children:[(0,s.jsxs)(f,{className:"flex items-center justify-center gap-2",children:[(0,s.jsx)("span",{children:"\uD83D\uDD17"}),"Link Telegram Account"]}),(0,s.jsx)(p,{children:"Connect your Telegram account to BonKai AI"})]}),(0,s.jsxs)(m,{className:"space-y-4",children:[!N?.success&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{htmlFor:"code",className:"text-sm font-medium",children:"Linking Code"}),(0,s.jsx)(l.p,{id:"code",type:"text",placeholder:"Enter 8-character code from bot",value:c,onChange:e=>x(e.target.value.toUpperCase()),className:"text-center font-mono",maxLength:10})]}),(0,s.jsx)(o.$,{onClick:P,disabled:b||!c.trim(),className:"w-full",children:b?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(y,{className:"size-4 animate-spin mr-2"}),"Linking Account..."]}):"Link Account"})]}),N&&(0,s.jsx)(h,{className:N.success?"border-green-200 bg-green-50":"border-red-200 bg-red-50",children:(0,s.jsxs)("div",{className:"flex items-start gap-2",children:[N.success?(0,s.jsx)(v,{className:"size-4 text-green-600 mt-0.5"}):(0,s.jsx)(j,{className:"size-4 text-red-600 mt-0.5"}),(0,s.jsx)(g,{className:N.success?"text-green-800":"text-red-800",children:N.message})]})}),N?.success&&(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)(o.$,{onClick:()=>window.open("https://t.me/bonkai_ai_bot","_blank"),className:"w-full",variant:"outline",children:[(0,s.jsx)(_,{className:"size-4 mr-2"}),"Open Telegram Bot"]}),(0,s.jsx)(o.$,{onClick:()=>{w(null),x("")},className:"w-full",variant:"ghost",children:"Link Another Account"})]}),(0,s.jsxs)("div",{className:"text-center text-sm text-gray-600",children:[(0,s.jsx)("p",{children:"Don't have a linking code?"}),(0,s.jsxs)("p",{children:["Start a chat with"," ",(0,s.jsx)("a",{href:"https://t.me/bonkai_ai_bot",target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:underline",children:"@bonkai_ai_bot"})," ","and use /link"]})]}),(0,s.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg text-xs text-gray-600 space-y-1",children:[(0,s.jsx)("p",{className:"font-semibold",children:"How to link:"}),(0,s.jsxs)("ol",{className:"list-decimal list-inside space-y-1",children:[(0,s.jsx)("li",{children:"Open Telegram and find @bonkai_ai_bot"}),(0,s.jsx)("li",{children:"Send /link command to get your code"}),(0,s.jsx)("li",{children:"Enter the code above to complete linking"}),(0,s.jsx)("li",{children:"Start chatting with BonKai AI!"})]})]})]})]})}):(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center p-4",children:(0,s.jsxs)(d,{className:"w-full max-w-md",children:[(0,s.jsxs)(u,{className:"text-center",children:[(0,s.jsx)(f,{children:"Link Telegram Account"}),(0,s.jsx)(p,{children:"Please sign in to link your Telegram account to BonKai"})]}),(0,s.jsx)(m,{children:(0,s.jsx)(h,{children:(0,s.jsx)(g,{children:"You need to be signed in to link your Telegram account. Please sign in first, then return to complete the linking process."})})})]})}):(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsx)(y,{className:"size-8 animate-spin"})})}},72610:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isPostpone",{enumerable:!0,get:function(){return s}});let r=Symbol.for("react.postpone");function s(e){return"object"==typeof e&&null!==e&&e.$$typeof===r}},73024:e=>{"use strict";e.exports=require("node:fs")},73136:e=>{"use strict";e.exports=require("node:url")},74075:e=>{"use strict";e.exports=require("zlib")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},79150:(e,t,r)=>{Promise.resolve().then(r.bind(r,66037))},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},84667:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var s=r(60159);let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=(...e)=>e.filter((e,t,r)=>!!e&&r.indexOf(e)===t).join(" ");var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,s.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:n,className:o="",children:l,iconNode:c,...d},u)=>(0,s.createElement)("svg",{ref:u,...i,width:t,height:t,stroke:e,strokeWidth:n?24*Number(r)/Number(t):r,className:a("lucide",o),...d},[...c.map(([e,t])=>(0,s.createElement)(e,t)),...Array.isArray(l)?l:[l]])),l=(e,t)=>{let r=(0,s.forwardRef)(({className:r,...i},l)=>(0,s.createElement)(o,{ref:l,iconNode:t,className:a(`lucide-${n(e)}`,r),...i}));return r.displayName=`${e}`,r}},90507:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{BailoutToCSRError:function(){return s},isBailoutToCSRError:function(){return n}});let r="BAILOUT_TO_CLIENT_SIDE_RENDERING";class s extends Error{constructor(e){super("Bail out to client-side rendering: "+e),this.reason=e,this.digest=r}}function n(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[191,2911,9445,8897,6581],()=>r(28990));module.exports=s})();