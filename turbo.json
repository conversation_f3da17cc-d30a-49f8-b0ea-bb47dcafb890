{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local"], "globalEnv": ["NODE_ENV", "NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY", "CLERK_SECRET_KEY", "OPENROUTER_API_KEY", "CONVEX_DEPLOYMENT", "NEXT_PUBLIC_CONVEX_URL", "UPLOADTHING_SECRET", "UPLOADTHING_APP_ID", "AUTH_SECRET", "NEXTAUTH_URL"], "pipeline": {"build": {"dependsOn": ["^build"], "outputs": [".next/**", "!.next/cache/**", "dist/**"], "env": ["NODE_ENV"]}, "dev": {"cache": false, "persistent": true}, "lint": {"outputs": []}, "test": {"cache": false, "env": ["PLAYWRIGHT"]}}}