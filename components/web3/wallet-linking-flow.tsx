'use client';

import { useState, useEffect } from 'react';
import { useWallet } from '@solana/wallet-adapter-react';
import { useUser } from '@clerk/nextjs';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import {
  CheckCircle,
  Wallet,
  Link,
  Shield,
  AlertCircle,
  Loader2,
} from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

interface WalletLinkingFlowProps {
  onComplete?: () => void;
  onCancel?: () => void;
  className?: string;
}

type LinkingStep = 'connect' | 'verify' | 'link' | 'complete';

const steps = [
  { id: 'connect', label: 'Connect Wallet', icon: Wallet },
  { id: 'verify', label: 'Verify Ownership', icon: Shield },
  { id: 'link', label: 'Link to Account', icon: Link },
  { id: 'complete', label: 'Complete', icon: CheckCircle },
];

export function WalletLinkingFlow({
  onComplete,
  onCancel,
  className,
}: WalletLinkingFlowProps) {
  const { publicKey, connected, disconnect, signMessage } = useWallet();
  const { user } = useUser();
  const [currentStep, setCurrentStep] = useState<LinkingStep>('connect');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Auto-advance to verify step when wallet connects
  useEffect(() => {
    if (connected && publicKey && currentStep === 'connect') {
      setCurrentStep('verify');
    }
  }, [connected, publicKey, currentStep]);

  const getCurrentStepIndex = () => {
    return steps.findIndex((step) => step.id === currentStep);
  };

  const progress = ((getCurrentStepIndex() + 1) / steps.length) * 100;

  const handleVerifyOwnership = async () => {
    if (!publicKey || !signMessage) {
      setError('Wallet does not support message signing');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Create verification message
      const message = `Verify wallet ownership for BonKai account: ${user?.emailAddresses[0]?.emailAddress}\nTimestamp: ${Date.now()}`;
      const encodedMessage = new TextEncoder().encode(message);

      // Sign the message
      const signature = await signMessage(encodedMessage);

      // Verify signature on backend (simplified for demo)
      // In production, you'd send this to your backend for verification
      console.log('Signature verified:', signature);

      setCurrentStep('link');
    } catch (error) {
      console.error('Verification failed:', error);
      setError('Failed to verify wallet ownership. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleLinkWallet = async () => {
    if (!publicKey || !user) return;

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/wallet/link', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          walletAddress: publicKey.toBase58(),
          verified: true, // Since we verified ownership in previous step
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to link wallet');
      }

      await user.reload();
      setCurrentStep('complete');
      toast.success('Wallet linked successfully!');

      // Auto-complete after a short delay
      setTimeout(() => {
        onComplete?.();
      }, 2000);
    } catch (error) {
      console.error('Error linking wallet:', error);
      setError(
        error instanceof Error
          ? error.message
          : 'Failed to link wallet. Please try again.',
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleDisconnect = () => {
    disconnect();
    setCurrentStep('connect');
    setError(null);
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 'connect':
        return (
          <div className="text-center space-y-4">
            <Wallet className="size-16 mx-auto text-muted-foreground" />
            <div>
              <h3 className="text-lg font-semibold mb-2">
                Connect Your Wallet
              </h3>
              <p className="text-muted-foreground text-sm">
                First, connect your Solana wallet to begin the linking process.
              </p>
            </div>
            {!connected && (
              <p className="text-sm text-yellow-600 dark:text-yellow-400">
                Please use the wallet connect button in the navigation bar.
              </p>
            )}
          </div>
        );

      case 'verify':
        return (
          <div className="text-center space-y-4">
            <Shield className="size-16 mx-auto text-blue-600" />
            <div>
              <h3 className="text-lg font-semibold mb-2">Verify Ownership</h3>
              <p className="text-muted-foreground text-sm mb-4">
                Sign a message to prove you own this wallet address.
              </p>
              <div className="p-3 bg-muted/50 rounded-lg">
                <p className="font-mono text-sm break-all">
                  {publicKey?.toBase58()}
                </p>
              </div>
            </div>
            <Button
              onClick={handleVerifyOwnership}
              disabled={isLoading}
              className="w-full"
            >
              {isLoading ? (
                <>
                  <Loader2 className="size-4 mr-2 animate-spin" />
                  Verifying...
                </>
              ) : (
                <>
                  <Shield className="size-4 mr-2" />
                  Sign Message
                </>
              )}
            </Button>
          </div>
        );

      case 'link':
        return (
          <div className="text-center space-y-4">
            <Link className="size-16 mx-auto text-green-600" />
            <div>
              <h3 className="text-lg font-semibold mb-2">Link to Account</h3>
              <p className="text-muted-foreground text-sm mb-4">
                Complete the linking process to unlock tier benefits and staking
                rewards.
              </p>
              <Alert className="text-left">
                <AlertCircle className="size-4" />
                <AlertDescription>
                  This will permanently link your wallet to your BonKai account.
                </AlertDescription>
              </Alert>
            </div>
            <Button
              onClick={handleLinkWallet}
              disabled={isLoading}
              className="w-full"
            >
              {isLoading ? (
                <>
                  <Loader2 className="size-4 mr-2 animate-spin" />
                  Linking...
                </>
              ) : (
                <>
                  <Link className="size-4 mr-2" />
                  Link Wallet
                </>
              )}
            </Button>
          </div>
        );

      case 'complete':
        return (
          <div className="text-center space-y-4">
            <CheckCircle className="size-16 mx-auto text-green-600" />
            <div>
              <h3 className="text-lg font-semibold mb-2 text-green-700 dark:text-green-300">
                Wallet Linked Successfully!
              </h3>
              <p className="text-muted-foreground text-sm">
                Your wallet is now linked to your BonKai account. You can now:
              </p>
              <ul className="text-sm text-muted-foreground mt-3 space-y-1">
                <li>• Stake BONK tokens for tier upgrades</li>
                <li>• Earn Aura Points and rewards</li>
                <li>• Access tier-based features</li>
              </ul>
            </div>
          </div>
        );
    }
  };

  return (
    <Card className={cn('w-full max-w-md mx-auto', className)}>
      <CardHeader>
        <CardTitle className="text-center">Link Your Wallet</CardTitle>
        <CardDescription className="text-center">
          Connect and verify your Solana wallet to unlock Web3 features
        </CardDescription>

        {/* Progress Bar */}
        <div className="space-y-2">
          <Progress value={progress} className="h-2" />
          <div className="flex justify-between text-xs text-muted-foreground">
            {steps.map((step, index) => {
              const StepIcon = step.icon;
              const isActive = index === getCurrentStepIndex();
              const isCompleted = index < getCurrentStepIndex();

              return (
                <div
                  key={step.id}
                  className={cn(
                    'flex items-center gap-1',
                    isActive && 'text-primary font-medium',
                    isCompleted && 'text-green-600',
                  )}
                >
                  <StepIcon className="size-3" />
                  <span className="hidden sm:inline">{step.label}</span>
                </div>
              );
            })}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Error Display */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="size-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Step Content */}
        {renderStepContent()}

        {/* Action Buttons */}
        <div className="flex gap-2 pt-4">
          {onCancel && currentStep !== 'complete' && (
            <Button variant="outline" onClick={onCancel} className="flex-1">
              Cancel
            </Button>
          )}

          {connected && currentStep !== 'complete' && (
            <Button
              variant="outline"
              onClick={handleDisconnect}
              className="flex-1"
            >
              Disconnect
            </Button>
          )}

          {currentStep === 'complete' && onComplete && (
            <Button onClick={onComplete} className="w-full">
              Continue
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
