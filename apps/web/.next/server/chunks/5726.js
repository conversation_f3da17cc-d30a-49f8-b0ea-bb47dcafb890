exports.id=5726,exports.ids=[5726],exports.modules={5877:(e,t,r)=>{"use strict";r.d(t,{TD:()=>tL,ks:()=>tC,Sp:()=>tJ,AA:()=>P,Bs:()=>rm,y3:()=>tI,tl:()=>tY,vH:()=>R,X9:()=>tK,Rn:()=>tD,vb:()=>tq,_:()=>tH,KV:()=>tP,lv:()=>tN,wI:()=>tM,xE:()=>tj,a4:()=>ri,nr:()=>t7});var s=r(24981),i=r(21404),n=r(68272);r(45663);var a={strict_mfa:{afterMinutes:10,level:"multi_factor"},strict:{afterMinutes:10,level:"second_factor"},moderate:{afterMinutes:60,level:"second_factor"},lax:{afterMinutes:1440,level:"second_factor"}},o=new Set(["first_factor","second_factor","multi_factor"]),l=new Set(["strict_mfa","strict","moderate","lax"]),c=e=>"number"==typeof e&&e>0,d=e=>o.has(e),u=e=>l.has(e),h=e=>e.replace(/^(org:)*/,"org:"),p=(e,t)=>{let{orgId:r,orgRole:s,orgPermissions:i}=t;return(e.role||e.permission)&&r&&s&&i?e.permission?i.includes(h(e.permission)):e.role?h(s)===h(e.role):null:null},f=(e,t)=>{let{org:r,user:s}=g(e),[i,n]=t.split(":"),a=n||i;return"org"===i?r.includes(a):"user"===i?s.includes(a):[...r,...s].includes(a)},m=(e,t)=>{let{features:r,plans:s}=t;return e.feature&&r?f(r,e.feature):e.plan&&s?f(s,e.plan):null},g=e=>{let t=e?e.split(",").map(e=>e.trim()):[];return{org:t.filter(e=>e.split(":")[0].includes("o")).map(e=>e.split(":")[1]),user:t.filter(e=>e.split(":")[0].includes("u")).map(e=>e.split(":")[1])}},y=e=>{if(!e)return!1;let t="string"==typeof e&&u(e),r="object"==typeof e&&d(e.level)&&c(e.afterMinutes);return(!!t||!!r)&&(e=>"string"==typeof e?a[e]:e).bind(null,e)},k=(e,{factorVerificationAge:t})=>{if(!e.reverification||!t)return null;let r=y(e.reverification);if(!r)return null;let{level:s,afterMinutes:i}=r(),[n,a]=t,o=-1!==n?i>n:null,l=-1!==a?i>a:null;switch(s){case"first_factor":return o;case"second_factor":return -1!==a?l:o;case"multi_factor":return -1===a?o:o&&l}},_=e=>t=>{if(!e.userId)return!1;let r=m(t,e),s=p(t,e),i=k(t,e);return[r||s,i].some(e=>null===e)?[r||s,i].some(e=>!0===e):[r||s,i].every(e=>!0===e)},v=({per:e,fpm:t})=>{if(!e||!t)return{permissions:[],featurePermissionMap:[]};let r=e.split(",").map(e=>e.trim()),s=t.split(",").map(e=>Number.parseInt(e.trim(),10)).map(e=>e.toString(2).padStart(r.length,"0").split("").map(e=>Number.parseInt(e,10)).reverse()).filter(Boolean);return{permissions:r,featurePermissionMap:s}},w=e=>{let t,r,s,i,n=e.fva??null,a=e.sts??null;if(2===e.v){if(e.o){t=e.o?.id,s=e.o?.slg,e.o?.rol&&(r=`org:${e.o?.rol}`);let{org:n}=g(e.fea),{permissions:a,featurePermissionMap:o}=v({per:e.o?.per,fpm:e.o?.fpm});i=function({features:e,permissions:t,featurePermissionMap:r}){if(!e||!t||!r)return[];let s=[];for(let i=0;i<e.length;i++){let n=e[i];if(i>=r.length)continue;let a=r[i];if(a)for(let e=0;e<a.length;e++)1===a[e]&&s.push(`org:${n}:${t[e]}`)}return s}({features:n,featurePermissionMap:o,permissions:a})}}else t=e.org_id,r=e.org_role,s=e.org_slug,i=e.org_permissions;return{sessionClaims:e,sessionId:e.sid,sessionStatus:a,actor:e.act,userId:e.sub,orgId:t,orgRole:r,orgSlug:s,orgPermissions:i,factorVerificationAge:n}},S=r(60864),b=r(86874),T=r(49480);function A(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function x(e){return e&&e.sensitive?"":"i"}var E="https://api.clerk.com",I="@clerk/backend@2.4.0",C="2025-04-10",O={Session:"__session",Refresh:"__refresh",ClientUat:"__client_uat",Handshake:"__clerk_handshake",DevBrowser:"__clerk_db_jwt",RedirectCount:"__clerk_redirect_count",HandshakeNonce:"__clerk_handshake_nonce"},U={ClerkSynced:"__clerk_synced",SuffixedCookies:"suffixed_cookies",ClerkRedirectUrl:"__clerk_redirect_url",DevBrowser:O.DevBrowser,Handshake:O.Handshake,HandshakeHelp:"__clerk_help",LegacyDevBrowser:"__dev_session",HandshakeReason:"__clerk_hs_reason",HandshakeNonce:O.HandshakeNonce,HandshakeFormat:"format"},P={Attributes:{AuthToken:"__clerkAuthToken",AuthSignature:"__clerkAuthSignature",AuthStatus:"__clerkAuthStatus",AuthReason:"__clerkAuthReason",AuthMessage:"__clerkAuthMessage",ClerkUrl:"__clerkUrl"},Cookies:O,Headers:{Accept:"accept",AuthMessage:"x-clerk-auth-message",Authorization:"authorization",AuthReason:"x-clerk-auth-reason",AuthSignature:"x-clerk-auth-signature",AuthStatus:"x-clerk-auth-status",AuthToken:"x-clerk-auth-token",CacheControl:"cache-control",ClerkRedirectTo:"x-clerk-redirect-to",ClerkRequestData:"x-clerk-request-data",ClerkUrl:"x-clerk-clerk-url",CloudFrontForwardedProto:"cloudfront-forwarded-proto",ContentType:"content-type",ContentSecurityPolicy:"content-security-policy",ContentSecurityPolicyReportOnly:"content-security-policy-report-only",EnableDebug:"x-clerk-debug",ForwardedHost:"x-forwarded-host",ForwardedPort:"x-forwarded-port",ForwardedProto:"x-forwarded-proto",Host:"host",Location:"location",Nonce:"x-nonce",Origin:"origin",Referrer:"referer",SecFetchDest:"sec-fetch-dest",SecFetchSite:"sec-fetch-site",UserAgent:"user-agent",ReportingEndpoints:"reporting-endpoints"},ContentTypes:{Json:"application/json"},QueryParameters:U},q=(e,t,r,s)=>{if(""===e)return N(t.toString(),r?.toString());let i=new URL(e),n=r?new URL(r,i):void 0,a=new URL(t,i),o=`${i.hostname}:${i.port}`!=`${a.hostname}:${a.port}`;return n&&(o&&n.searchParams.delete(P.QueryParameters.ClerkSynced),a.searchParams.set("redirect_url",n.toString())),o&&s&&a.searchParams.set(P.QueryParameters.DevBrowser,s),a.toString()},N=(e,t)=>{let r;if(e.startsWith("http"))r=new URL(e);else{if(!t||!t.startsWith("http"))throw Error("destination url or return back url should be an absolute path url!");let s=new URL(t);r=new URL(e,s.origin)}return t&&r.searchParams.set("redirect_url",t),r.toString()},R=e=>{let{publishableKey:t,redirectAdapter:r,signInUrl:i,signUpUrl:n,baseUrl:a,sessionStatus:o}=e,l=(0,s.q5)(t),c=l?.frontendApi,d=l?.instanceType==="development",u=function(e){if(!e)return"";let t=e.replace(/clerk\.accountsstage\./,"accountsstage.").replace(/clerk\.accounts\.|clerk\./,"accounts.");return`https://${t}`}(c),h="pending"===o,p=(t,{returnBackUrl:s})=>r(q(a,`${t}/tasks`,s,d?e.devBrowserToken:null));return{redirectToSignUp:({returnBackUrl:t}={})=>{n||u||s.sb.throwMissingPublishableKeyError();let o=`${u}/sign-up`,l=n||function(e){if(!e)return;let t=new URL(e,a);return t.pathname=`${t.pathname}/create`,t.toString()}(i)||o;return h?p(l,{returnBackUrl:t}):r(q(a,l,t,d?e.devBrowserToken:null))},redirectToSignIn:({returnBackUrl:t}={})=>{i||u||s.sb.throwMissingPublishableKeyError();let n=`${u}/sign-in`,o=i||n;return h?p(o,{returnBackUrl:t}):r(q(a,o,t,d?e.devBrowserToken:null))}}};function z(e,t){return Object.keys(e).reduce((e,r)=>({...e,[r]:t[r]||e[r]}),{...e})}function M(e){if(!e||"string"!=typeof e)throw Error("Missing Clerk Secret Key. Go to https://dashboard.clerk.com and get your key for your instance.")}var J=class{constructor(e,t,r){this.cookieSuffix=e,this.clerkRequest=t,this.originalFrontendApi="",this.initPublishableKeyValues(r),this.initHeaderValues(),this.initCookieValues(),this.initHandshakeValues(),Object.assign(this,r),this.clerkUrl=this.clerkRequest.clerkUrl}get sessionToken(){return this.sessionTokenInCookie||this.tokenInHeader}usesSuffixedCookies(){let e=this.getSuffixedCookie(P.Cookies.ClientUat),t=this.getCookie(P.Cookies.ClientUat),r=this.getSuffixedCookie(P.Cookies.Session)||"",s=this.getCookie(P.Cookies.Session)||"";if(s&&!this.tokenHasIssuer(s))return!1;if(s&&!this.tokenBelongsToInstance(s))return!0;if(!e&&!r)return!1;let{data:n}=(0,i.iU)(s),a=n?.payload.iat||0,{data:o}=(0,i.iU)(r),l=o?.payload.iat||0;if("0"!==e&&"0"!==t&&a>l||"0"===e&&"0"!==t)return!1;if("production"!==this.instanceType){let r=this.sessionExpired(o);if("0"!==e&&"0"===t&&r)return!1}return!!e||!r}isCrossOriginReferrer(){if(!this.referrer||!this.origin)return!1;try{if("cross-site"===this.getHeader(P.Headers.SecFetchSite))return!0;return new URL(this.referrer).origin!==this.origin}catch{return!1}}initPublishableKeyValues(e){var t;t=e.publishableKey,(0,s.q5)(t,{fatal:!0}),this.publishableKey=e.publishableKey;let r=(0,s.q5)(this.publishableKey,{fatal:!0,domain:e.domain,isSatellite:e.isSatellite});this.originalFrontendApi=r.frontendApi;let i=(0,s.q5)(this.publishableKey,{fatal:!0,proxyUrl:e.proxyUrl,domain:e.domain,isSatellite:e.isSatellite});this.instanceType=i.instanceType,this.frontendApi=i.frontendApi}initHeaderValues(){this.tokenInHeader=this.parseAuthorizationHeader(this.getHeader(P.Headers.Authorization)),this.origin=this.getHeader(P.Headers.Origin),this.host=this.getHeader(P.Headers.Host),this.forwardedHost=this.getHeader(P.Headers.ForwardedHost),this.forwardedProto=this.getHeader(P.Headers.CloudFrontForwardedProto)||this.getHeader(P.Headers.ForwardedProto),this.referrer=this.getHeader(P.Headers.Referrer),this.userAgent=this.getHeader(P.Headers.UserAgent),this.secFetchDest=this.getHeader(P.Headers.SecFetchDest),this.accept=this.getHeader(P.Headers.Accept)}initCookieValues(){this.sessionTokenInCookie=this.getSuffixedOrUnSuffixedCookie(P.Cookies.Session),this.refreshTokenInCookie=this.getSuffixedCookie(P.Cookies.Refresh),this.clientUat=Number.parseInt(this.getSuffixedOrUnSuffixedCookie(P.Cookies.ClientUat)||"")||0}initHandshakeValues(){this.devBrowserToken=this.getQueryParam(P.QueryParameters.DevBrowser)||this.getSuffixedOrUnSuffixedCookie(P.Cookies.DevBrowser),this.handshakeToken=this.getQueryParam(P.QueryParameters.Handshake)||this.getCookie(P.Cookies.Handshake),this.handshakeRedirectLoopCounter=Number(this.getCookie(P.Cookies.RedirectCount))||0,this.handshakeNonce=this.getQueryParam(P.QueryParameters.HandshakeNonce)||this.getCookie(P.Cookies.HandshakeNonce)}getQueryParam(e){return this.clerkRequest.clerkUrl.searchParams.get(e)}getHeader(e){return this.clerkRequest.headers.get(e)||void 0}getCookie(e){return this.clerkRequest.cookies.get(e)||void 0}getSuffixedCookie(e){return this.getCookie((0,s.ky)(e,this.cookieSuffix))||void 0}getSuffixedOrUnSuffixedCookie(e){return this.usesSuffixedCookies()?this.getSuffixedCookie(e):this.getCookie(e)}parseAuthorizationHeader(e){if(!e)return;let[t,r]=e.split(" ",2);return r?"Bearer"===t?r:void 0:t}tokenHasIssuer(e){let{data:t,errors:r}=(0,i.iU)(e);return!r&&!!t.payload.iss}tokenBelongsToInstance(e){if(!e)return!1;let{data:t,errors:r}=(0,i.iU)(e);if(r)return!1;let s=t.payload.iss.replace(/https?:\/\//gi,"");return this.originalFrontendApi===s}sessionExpired(e){return!!e&&e?.payload.exp<=Date.now()/1e3>>0}},j=async(e,t)=>new J(t.publishableKey?await (0,s.qS)(t.publishableKey,i.fA.crypto.subtle):"",e,t),H=RegExp("(?<!:)/{1,}","g");function F(...e){return e.filter(e=>e).join("/").replace(H,"/")}var D=class{constructor(e){this.request=e}requireId(e){if(!e)throw Error("A valid resource ID is required.")}},K="/actor_tokens",L=class extends D{async create(e){return this.request({method:"POST",path:K,bodyParams:e})}async revoke(e){return this.requireId(e),this.request({method:"POST",path:F(K,e,"revoke")})}},B="/accountless_applications",$=class extends D{async createAccountlessApplication(){return this.request({method:"POST",path:B})}async completeAccountlessApplicationOnboarding(){return this.request({method:"POST",path:F(B,"complete")})}},W="/allowlist_identifiers",G=class extends D{async getAllowlistIdentifierList(e={}){return this.request({method:"GET",path:W,queryParams:{...e,paginated:!0}})}async createAllowlistIdentifier(e){return this.request({method:"POST",path:W,bodyParams:e})}async deleteAllowlistIdentifier(e){return this.requireId(e),this.request({method:"DELETE",path:F(W,e)})}},V="/api_keys",Q=class extends D{async create(e){return this.request({method:"POST",path:V,bodyParams:e})}async revoke(e){let{apiKeyId:t,...r}=e;return this.requireId(t),this.request({method:"POST",path:F(V,t,"revoke"),bodyParams:r})}async getSecret(e){return this.requireId(e),this.request({method:"GET",path:F(V,e,"secret")})}async verifySecret(e){return this.request({method:"POST",path:F(V,"verify"),bodyParams:{secret:e}})}},X=class extends D{async changeDomain(e){return this.request({method:"POST",path:F("/beta_features","change_domain"),bodyParams:e})}},Y="/blocklist_identifiers",Z=class extends D{async getBlocklistIdentifierList(e={}){return this.request({method:"GET",path:Y,queryParams:e})}async createBlocklistIdentifier(e){return this.request({method:"POST",path:Y,bodyParams:e})}async deleteBlocklistIdentifier(e){return this.requireId(e),this.request({method:"DELETE",path:F(Y,e)})}},ee="/clients",et=class extends D{async getClientList(e={}){return this.request({method:"GET",path:ee,queryParams:{...e,paginated:!0}})}async getClient(e){return this.requireId(e),this.request({method:"GET",path:F(ee,e)})}verifyClient(e){return this.request({method:"POST",path:F(ee,"verify"),bodyParams:{token:e}})}async getHandshakePayload(e){return this.request({method:"GET",path:F(ee,"handshake_payload"),queryParams:e})}},er="/domains",es=class extends D{async list(){return this.request({method:"GET",path:er})}async add(e){return this.request({method:"POST",path:er,bodyParams:e})}async update(e){let{domainId:t,...r}=e;return this.requireId(t),this.request({method:"PATCH",path:F(er,t),bodyParams:r})}async delete(e){return this.deleteDomain(e)}async deleteDomain(e){return this.requireId(e),this.request({method:"DELETE",path:F(er,e)})}},ei="/email_addresses",en=class extends D{async getEmailAddress(e){return this.requireId(e),this.request({method:"GET",path:F(ei,e)})}async createEmailAddress(e){return this.request({method:"POST",path:ei,bodyParams:e})}async updateEmailAddress(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:F(ei,e),bodyParams:t})}async deleteEmailAddress(e){return this.requireId(e),this.request({method:"DELETE",path:F(ei,e)})}},ea=class extends D{async verifyAccessToken(e){return this.request({method:"POST",path:F("/oauth_applications/access_tokens","verify"),bodyParams:{access_token:e}})}},eo="/instance",el=class extends D{async get(){return this.request({method:"GET",path:eo})}async update(e){return this.request({method:"PATCH",path:eo,bodyParams:e})}async updateRestrictions(e){return this.request({method:"PATCH",path:F(eo,"restrictions"),bodyParams:e})}async updateOrganizationSettings(e){return this.request({method:"PATCH",path:F(eo,"organization_settings"),bodyParams:e})}},ec="/invitations",ed=class extends D{async getInvitationList(e={}){return this.request({method:"GET",path:ec,queryParams:{...e,paginated:!0}})}async createInvitation(e){return this.request({method:"POST",path:ec,bodyParams:e})}async revokeInvitation(e){return this.requireId(e),this.request({method:"POST",path:F(ec,e,"revoke")})}},eu=class extends D{async verifySecret(e){return this.request({method:"POST",path:F("/m2m_tokens","verify"),bodyParams:{secret:e}})}},eh=class extends D{async getJwks(){return this.request({method:"GET",path:"/jwks"})}},ep="/jwt_templates",ef=class extends D{async list(e={}){return this.request({method:"GET",path:ep,queryParams:{...e,paginated:!0}})}async get(e){return this.requireId(e),this.request({method:"GET",path:F(ep,e)})}async create(e){return this.request({method:"POST",path:ep,bodyParams:e})}async update(e){let{templateId:t,...r}=e;return this.requireId(t),this.request({method:"PATCH",path:F(ep,t),bodyParams:r})}async delete(e){return this.requireId(e),this.request({method:"DELETE",path:F(ep,e)})}},em="/organizations",eg=class extends D{async getOrganizationList(e){return this.request({method:"GET",path:em,queryParams:e})}async createOrganization(e){return this.request({method:"POST",path:em,bodyParams:e})}async getOrganization(e){let{includeMembersCount:t}=e,r="organizationId"in e?e.organizationId:e.slug;return this.requireId(r),this.request({method:"GET",path:F(em,r),queryParams:{includeMembersCount:t}})}async updateOrganization(e,t){return this.requireId(e),this.request({method:"PATCH",path:F(em,e),bodyParams:t})}async updateOrganizationLogo(e,t){this.requireId(e);let r=new i.fA.FormData;return r.append("file",t?.file),t?.uploaderUserId&&r.append("uploader_user_id",t?.uploaderUserId),this.request({method:"PUT",path:F(em,e,"logo"),formData:r})}async deleteOrganizationLogo(e){return this.requireId(e),this.request({method:"DELETE",path:F(em,e,"logo")})}async updateOrganizationMetadata(e,t){return this.requireId(e),this.request({method:"PATCH",path:F(em,e,"metadata"),bodyParams:t})}async deleteOrganization(e){return this.request({method:"DELETE",path:F(em,e)})}async getOrganizationMembershipList(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"GET",path:F(em,t,"memberships"),queryParams:r})}async getInstanceOrganizationMembershipList(e){return this.request({method:"GET",path:"/organization_memberships",queryParams:e})}async createOrganizationMembership(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"POST",path:F(em,t,"memberships"),bodyParams:r})}async updateOrganizationMembership(e){let{organizationId:t,userId:r,...s}=e;return this.requireId(t),this.request({method:"PATCH",path:F(em,t,"memberships",r),bodyParams:s})}async updateOrganizationMembershipMetadata(e){let{organizationId:t,userId:r,...s}=e;return this.request({method:"PATCH",path:F(em,t,"memberships",r,"metadata"),bodyParams:s})}async deleteOrganizationMembership(e){let{organizationId:t,userId:r}=e;return this.requireId(t),this.request({method:"DELETE",path:F(em,t,"memberships",r)})}async getOrganizationInvitationList(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"GET",path:F(em,t,"invitations"),queryParams:r})}async createOrganizationInvitation(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"POST",path:F(em,t,"invitations"),bodyParams:r})}async createOrganizationInvitationBulk(e,t){return this.requireId(e),this.request({method:"POST",path:F(em,e,"invitations","bulk"),bodyParams:t})}async getOrganizationInvitation(e){let{organizationId:t,invitationId:r}=e;return this.requireId(t),this.requireId(r),this.request({method:"GET",path:F(em,t,"invitations",r)})}async revokeOrganizationInvitation(e){let{organizationId:t,invitationId:r,...s}=e;return this.requireId(t),this.request({method:"POST",path:F(em,t,"invitations",r,"revoke"),bodyParams:s})}async getOrganizationDomainList(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"GET",path:F(em,t,"domains"),queryParams:r})}async createOrganizationDomain(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"POST",path:F(em,t,"domains"),bodyParams:{...r,verified:r.verified??!0}})}async updateOrganizationDomain(e){let{organizationId:t,domainId:r,...s}=e;return this.requireId(t),this.requireId(r),this.request({method:"PATCH",path:F(em,t,"domains",r),bodyParams:s})}async deleteOrganizationDomain(e){let{organizationId:t,domainId:r}=e;return this.requireId(t),this.requireId(r),this.request({method:"DELETE",path:F(em,t,"domains",r)})}},ey="/oauth_applications",ek=class extends D{async list(e={}){return this.request({method:"GET",path:ey,queryParams:e})}async get(e){return this.requireId(e),this.request({method:"GET",path:F(ey,e)})}async create(e){return this.request({method:"POST",path:ey,bodyParams:e})}async update(e){let{oauthApplicationId:t,...r}=e;return this.requireId(t),this.request({method:"PATCH",path:F(ey,t),bodyParams:r})}async delete(e){return this.requireId(e),this.request({method:"DELETE",path:F(ey,e)})}async rotateSecret(e){return this.requireId(e),this.request({method:"POST",path:F(ey,e,"rotate_secret")})}},e_="/phone_numbers",ev=class extends D{async getPhoneNumber(e){return this.requireId(e),this.request({method:"GET",path:F(e_,e)})}async createPhoneNumber(e){return this.request({method:"POST",path:e_,bodyParams:e})}async updatePhoneNumber(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:F(e_,e),bodyParams:t})}async deletePhoneNumber(e){return this.requireId(e),this.request({method:"DELETE",path:F(e_,e)})}},ew=class extends D{async verify(e){return this.request({method:"POST",path:"/proxy_checks",bodyParams:e})}},eS="/redirect_urls",eb=class extends D{async getRedirectUrlList(){return this.request({method:"GET",path:eS,queryParams:{paginated:!0}})}async getRedirectUrl(e){return this.requireId(e),this.request({method:"GET",path:F(eS,e)})}async createRedirectUrl(e){return this.request({method:"POST",path:eS,bodyParams:e})}async deleteRedirectUrl(e){return this.requireId(e),this.request({method:"DELETE",path:F(eS,e)})}},eT="/saml_connections",eA=class extends D{async getSamlConnectionList(e={}){return this.request({method:"GET",path:eT,queryParams:e})}async createSamlConnection(e){return this.request({method:"POST",path:eT,bodyParams:e})}async getSamlConnection(e){return this.requireId(e),this.request({method:"GET",path:F(eT,e)})}async updateSamlConnection(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:F(eT,e),bodyParams:t})}async deleteSamlConnection(e){return this.requireId(e),this.request({method:"DELETE",path:F(eT,e)})}},ex="/sessions",eE=class extends D{async getSessionList(e={}){return this.request({method:"GET",path:ex,queryParams:{...e,paginated:!0}})}async getSession(e){return this.requireId(e),this.request({method:"GET",path:F(ex,e)})}async createSession(e){return this.request({method:"POST",path:ex,bodyParams:e})}async revokeSession(e){return this.requireId(e),this.request({method:"POST",path:F(ex,e,"revoke")})}async verifySession(e,t){return this.requireId(e),this.request({method:"POST",path:F(ex,e,"verify"),bodyParams:{token:t}})}async getToken(e,t,r){this.requireId(e);let s={method:"POST",path:t?F(ex,e,"tokens",t):F(ex,e,"tokens")};return void 0!==r&&(s.bodyParams={expires_in_seconds:r}),this.request(s)}async refreshSession(e,t){this.requireId(e);let{suffixed_cookies:r,...s}=t;return this.request({method:"POST",path:F(ex,e,"refresh"),bodyParams:s,queryParams:{suffixed_cookies:r}})}},eI="/sign_in_tokens",eC=class extends D{async createSignInToken(e){return this.request({method:"POST",path:eI,bodyParams:e})}async revokeSignInToken(e){return this.requireId(e),this.request({method:"POST",path:F(eI,e,"revoke")})}},eO="/sign_ups",eU=class extends D{async get(e){return this.requireId(e),this.request({method:"GET",path:F(eO,e)})}async update(e){let{signUpAttemptId:t,...r}=e;return this.request({method:"PATCH",path:F(eO,t),bodyParams:r})}},eP=class extends D{async createTestingToken(){return this.request({method:"POST",path:"/testing_tokens"})}},eq="/users",eN=class extends D{async getUserList(e={}){let{limit:t,offset:r,orderBy:s,...i}=e,[n,a]=await Promise.all([this.request({method:"GET",path:eq,queryParams:e}),this.getCount(i)]);return{data:n,totalCount:a}}async getUser(e){return this.requireId(e),this.request({method:"GET",path:F(eq,e)})}async createUser(e){return this.request({method:"POST",path:eq,bodyParams:e})}async updateUser(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:F(eq,e),bodyParams:t})}async updateUserProfileImage(e,t){this.requireId(e);let r=new i.fA.FormData;return r.append("file",t?.file),this.request({method:"POST",path:F(eq,e,"profile_image"),formData:r})}async updateUserMetadata(e,t){return this.requireId(e),this.request({method:"PATCH",path:F(eq,e,"metadata"),bodyParams:t})}async deleteUser(e){return this.requireId(e),this.request({method:"DELETE",path:F(eq,e)})}async getCount(e={}){return this.request({method:"GET",path:F(eq,"count"),queryParams:e})}async getUserOauthAccessToken(e,t){this.requireId(e);let r=t.startsWith("oauth_"),i=r?t:`oauth_${t}`;return r&&(0,s.io)("getUserOauthAccessToken(userId, provider)","Remove the `oauth_` prefix from the `provider` argument."),this.request({method:"GET",path:F(eq,e,"oauth_access_tokens",i),queryParams:{paginated:!0}})}async disableUserMFA(e){return this.requireId(e),this.request({method:"DELETE",path:F(eq,e,"mfa")})}async getOrganizationMembershipList(e){let{userId:t,limit:r,offset:s}=e;return this.requireId(t),this.request({method:"GET",path:F(eq,t,"organization_memberships"),queryParams:{limit:r,offset:s}})}async getOrganizationInvitationList(e){let{userId:t,...r}=e;return this.requireId(t),this.request({method:"GET",path:F(eq,t,"organization_invitations"),queryParams:r})}async verifyPassword(e){let{userId:t,password:r}=e;return this.requireId(t),this.request({method:"POST",path:F(eq,t,"verify_password"),bodyParams:{password:r}})}async verifyTOTP(e){let{userId:t,code:r}=e;return this.requireId(t),this.request({method:"POST",path:F(eq,t,"verify_totp"),bodyParams:{code:r}})}async banUser(e){return this.requireId(e),this.request({method:"POST",path:F(eq,e,"ban")})}async unbanUser(e){return this.requireId(e),this.request({method:"POST",path:F(eq,e,"unban")})}async lockUser(e){return this.requireId(e),this.request({method:"POST",path:F(eq,e,"lock")})}async unlockUser(e){return this.requireId(e),this.request({method:"POST",path:F(eq,e,"unlock")})}async deleteUserProfileImage(e){return this.requireId(e),this.request({method:"DELETE",path:F(eq,e,"profile_image")})}async deleteUserPasskey(e){return this.requireId(e.userId),this.requireId(e.passkeyIdentificationId),this.request({method:"DELETE",path:F(eq,e.userId,"passkeys",e.passkeyIdentificationId)})}async deleteUserWeb3Wallet(e){return this.requireId(e.userId),this.requireId(e.web3WalletIdentificationId),this.request({method:"DELETE",path:F(eq,e.userId,"web3_wallets",e.web3WalletIdentificationId)})}async deleteUserExternalAccount(e){return this.requireId(e.userId),this.requireId(e.externalAccountId),this.request({method:"DELETE",path:F(eq,e.userId,"external_accounts",e.externalAccountId)})}async deleteUserBackupCodes(e){return this.requireId(e),this.request({method:"DELETE",path:F(eq,e,"backup_code")})}async deleteUserTOTP(e){return this.requireId(e),this.request({method:"DELETE",path:F(eq,e,"totp")})}},eR="/waitlist_entries",ez=class extends D{async list(e={}){return this.request({method:"GET",path:eR,queryParams:e})}async create(e){return this.request({method:"POST",path:eR,bodyParams:e})}},eM="/webhooks",eJ=class extends D{async createSvixApp(){return this.request({method:"POST",path:F(eM,"svix")})}async generateSvixAuthURL(){return this.request({method:"POST",path:F(eM,"svix_url")})}async deleteSvixApp(){return this.request({method:"DELETE",path:F(eM,"svix")})}},ej=class e{constructor(e,t,r,s){this.publishableKey=e,this.secretKey=t,this.claimUrl=r,this.apiKeysUrl=s}static fromJSON(t){return new e(t.publishable_key,t.secret_key,t.claim_url,t.api_keys_url)}},eH=class e{constructor(e,t,r,s,i,n,a,o){this.id=e,this.status=t,this.userId=r,this.actor=s,this.token=i,this.url=n,this.createdAt=a,this.updatedAt=o}static fromJSON(t){return new e(t.id,t.status,t.user_id,t.actor,t.token,t.url,t.created_at,t.updated_at)}},eF=class e{constructor(e,t,r,s,i,n,a){this.id=e,this.identifier=t,this.identifierType=r,this.createdAt=s,this.updatedAt=i,this.instanceId=n,this.invitationId=a}static fromJSON(t){return new e(t.id,t.identifier,t.identifier_type,t.created_at,t.updated_at,t.instance_id,t.invitation_id)}},eD=class e{constructor(e,t,r,s,i,n,a,o,l,c,d,u,h,p,f,m){this.id=e,this.type=t,this.name=r,this.subject=s,this.scopes=i,this.claims=n,this.revoked=a,this.revocationReason=o,this.expired=l,this.expiration=c,this.createdBy=d,this.description=u,this.lastUsedAt=h,this.createdAt=p,this.updatedAt=f,this.secret=m}static fromJSON(t){return new e(t.id,t.type,t.name,t.subject,t.scopes,t.claims,t.revoked,t.revocation_reason,t.expired,t.expiration,t.created_by,t.description,t.last_used_at,t.created_at,t.updated_at,t.secret)}},eK=class e{constructor(e,t,r,s,i,n){this.id=e,this.identifier=t,this.identifierType=r,this.createdAt=s,this.updatedAt=i,this.instanceId=n}static fromJSON(t){return new e(t.id,t.identifier,t.identifier_type,t.created_at,t.updated_at,t.instance_id)}},eL=class e{constructor(e,t,r,s,i,n,a,o){this.id=e,this.isMobile=t,this.ipAddress=r,this.city=s,this.country=i,this.browserVersion=n,this.browserName=a,this.deviceType=o}static fromJSON(t){return new e(t.id,t.is_mobile,t.ip_address,t.city,t.country,t.browser_version,t.browser_name,t.device_type)}},eB=class e{constructor(e,t,r,s,i,n,a,o,l,c,d,u=null){this.id=e,this.clientId=t,this.userId=r,this.status=s,this.lastActiveAt=i,this.expireAt=n,this.abandonAt=a,this.createdAt=o,this.updatedAt=l,this.lastActiveOrganizationId=c,this.latestActivity=d,this.actor=u}static fromJSON(t){return new e(t.id,t.client_id,t.user_id,t.status,t.last_active_at,t.expire_at,t.abandon_at,t.created_at,t.updated_at,t.last_active_organization_id,t.latest_activity&&eL.fromJSON(t.latest_activity),t.actor)}},e$=class e{constructor(e,t,r,s,i,n,a,o){this.id=e,this.sessionIds=t,this.sessions=r,this.signInId=s,this.signUpId=i,this.lastActiveSessionId=n,this.createdAt=a,this.updatedAt=o}static fromJSON(t){return new e(t.id,t.session_ids,t.sessions.map(e=>eB.fromJSON(e)),t.sign_in_id,t.sign_up_id,t.last_active_session_id,t.created_at,t.updated_at)}},eW=class e{constructor(e,t,r){this.host=e,this.value=t,this.required=r}static fromJSON(t){return new e(t.host,t.value,t.required)}},eG=class e{constructor(e){this.cookies=e}static fromJSON(t){return new e(t.cookies)}},eV=class e{constructor(e,t,r,s){this.object=e,this.id=t,this.slug=r,this.deleted=s}static fromJSON(t){return new e(t.object,t.id||null,t.slug||null,t.deleted)}},eQ=class e{constructor(e,t,r,s,i,n,a,o){this.id=e,this.name=t,this.isSatellite=r,this.frontendApiUrl=s,this.developmentOrigin=i,this.cnameTargets=n,this.accountsPortalUrl=a,this.proxyUrl=o}static fromJSON(t){return new e(t.id,t.name,t.is_satellite,t.frontend_api_url,t.development_origin,t.cname_targets&&t.cname_targets.map(e=>eW.fromJSON(e)),t.accounts_portal_url,t.proxy_url)}},eX=class e{constructor(e,t,r,s,i,n,a,o,l,c,d){this.id=e,this.fromEmailName=t,this.emailAddressId=r,this.toEmailAddress=s,this.subject=i,this.body=n,this.bodyPlain=a,this.status=o,this.slug=l,this.data=c,this.deliveredByClerk=d}static fromJSON(t){return new e(t.id,t.from_email_name,t.email_address_id,t.to_email_address,t.subject,t.body,t.body_plain,t.status,t.slug,t.data,t.delivered_by_clerk)}},eY=class e{constructor(e,t){this.id=e,this.type=t}static fromJSON(t){return new e(t.id,t.type)}},eZ=class e{constructor(e,t,r=null,s=null,i=null,n=null,a=null){this.status=e,this.strategy=t,this.externalVerificationRedirectURL=r,this.attempts=s,this.expireAt=i,this.nonce=n,this.message=a}static fromJSON(t){return new e(t.status,t.strategy,t.external_verification_redirect_url?new URL(t.external_verification_redirect_url):null,t.attempts,t.expire_at,t.nonce)}},e0=class e{constructor(e,t,r,s){this.id=e,this.emailAddress=t,this.verification=r,this.linkedTo=s}static fromJSON(t){return new e(t.id,t.email_address,t.verification&&eZ.fromJSON(t.verification),t.linked_to.map(e=>eY.fromJSON(e)))}},e1=class e{constructor(e,t,r,s,i,n,a,o,l,c,d,u={},h,p){this.id=e,this.provider=t,this.identificationId=r,this.externalId=s,this.approvedScopes=i,this.emailAddress=n,this.firstName=a,this.lastName=o,this.imageUrl=l,this.username=c,this.phoneNumber=d,this.publicMetadata=u,this.label=h,this.verification=p}static fromJSON(t){return new e(t.id,t.provider,t.identification_id,t.provider_user_id,t.approved_scopes,t.email_address,t.first_name,t.last_name,t.image_url||"",t.username,t.phone_number,t.public_metadata,t.label,t.verification&&eZ.fromJSON(t.verification))}},e2=class e{constructor(e,t,r,s,i,n,a,o,l,c,d){this.id=e,this.clientId=t,this.type=r,this.subject=s,this.scopes=i,this.revoked=n,this.revocationReason=a,this.expired=o,this.expiration=l,this.createdAt=c,this.updatedAt=d}static fromJSON(t){return new e(t.id,t.client_id,t.type,t.subject,t.scopes,t.revoked,t.revocation_reason,t.expired,t.expiration,t.created_at,t.updated_at)}},e5=class e{constructor(e,t,r){this.id=e,this.environmentType=t,this.allowedOrigins=r}static fromJSON(t){return new e(t.id,t.environment_type,t.allowed_origins)}},e4=class e{constructor(e,t,r,s,i){this.allowlist=e,this.blocklist=t,this.blockEmailSubaddresses=r,this.blockDisposableEmailDomains=s,this.ignoreDotsForGmailAddresses=i}static fromJSON(t){return new e(t.allowlist,t.blocklist,t.block_email_subaddresses,t.block_disposable_email_domains,t.ignore_dots_for_gmail_addresses)}},e6=class e{constructor(e,t,r,s,i){this.id=e,this.restrictedToAllowlist=t,this.fromEmailAddress=r,this.progressiveSignUp=s,this.enhancedEmailDeliverability=i}static fromJSON(t){return new e(t.id,t.restricted_to_allowlist,t.from_email_address,t.progressive_sign_up,t.enhanced_email_deliverability)}},e3=class e{constructor(e,t,r,s,i,n,a,o){this.id=e,this.emailAddress=t,this.publicMetadata=r,this.createdAt=s,this.updatedAt=i,this.status=n,this.url=a,this.revoked=o,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.email_address,t.public_metadata,t.created_at,t.updated_at,t.status,t.url,t.revoked);return r._raw=t,r}},e8={AccountlessApplication:"accountless_application",ActorToken:"actor_token",AllowlistIdentifier:"allowlist_identifier",ApiKey:"api_key",BlocklistIdentifier:"blocklist_identifier",Client:"client",Cookies:"cookies",Domain:"domain",Email:"email",EmailAddress:"email_address",Instance:"instance",InstanceRestrictions:"instance_restrictions",InstanceSettings:"instance_settings",Invitation:"invitation",MachineToken:"machine_to_machine_token",JwtTemplate:"jwt_template",OauthAccessToken:"oauth_access_token",IdpOAuthAccessToken:"clerk_idp_oauth_access_token",OAuthApplication:"oauth_application",Organization:"organization",OrganizationInvitation:"organization_invitation",OrganizationMembership:"organization_membership",OrganizationSettings:"organization_settings",PhoneNumber:"phone_number",ProxyCheck:"proxy_check",RedirectUrl:"redirect_url",SamlConnection:"saml_connection",Session:"session",SignInToken:"sign_in_token",SignUpAttempt:"sign_up_attempt",SmsMessage:"sms_message",User:"user",WaitlistEntry:"waitlist_entry",Token:"token",TotalCount:"total_count"},e9=class e{constructor(e,t,r,s,i,n,a,o,l,c,d,u,h){this.id=e,this.name=t,this.subject=r,this.scopes=s,this.claims=i,this.revoked=n,this.revocationReason=a,this.expired=o,this.expiration=l,this.createdBy=c,this.creationReason=d,this.createdAt=u,this.updatedAt=h}static fromJSON(t){return new e(t.id,t.name,t.subject,t.scopes,t.claims,t.revoked,t.revocation_reason,t.expired,t.expiration,t.created_by,t.creation_reason,t.created_at,t.updated_at)}},e7=class e{constructor(e,t,r,s,i,n,a,o,l){this.id=e,this.name=t,this.claims=r,this.lifetime=s,this.allowedClockSkew=i,this.customSigningKey=n,this.signingAlgorithm=a,this.createdAt=o,this.updatedAt=l}static fromJSON(t){return new e(t.id,t.name,t.claims,t.lifetime,t.allowed_clock_skew,t.custom_signing_key,t.signing_algorithm,t.created_at,t.updated_at)}},te=class e{constructor(e,t,r,s={},i,n,a,o){this.externalAccountId=e,this.provider=t,this.token=r,this.publicMetadata=s,this.label=i,this.scopes=n,this.tokenSecret=a,this.expiresAt=o}static fromJSON(t){return new e(t.external_account_id,t.provider,t.token,t.public_metadata,t.label||"",t.scopes,t.token_secret,t.expires_at)}},tt=class e{constructor(e,t,r,s,i,n,a,o,l,c,d,u,h,p,f){this.id=e,this.instanceId=t,this.name=r,this.clientId=s,this.isPublic=i,this.scopes=n,this.redirectUris=a,this.authorizeUrl=o,this.tokenFetchUrl=l,this.userInfoUrl=c,this.discoveryUrl=d,this.tokenIntrospectionUrl=u,this.createdAt=h,this.updatedAt=p,this.clientSecret=f}static fromJSON(t){return new e(t.id,t.instance_id,t.name,t.client_id,t.public,t.scopes,t.redirect_uris,t.authorize_url,t.token_fetch_url,t.user_info_url,t.discovery_url,t.token_introspection_url,t.created_at,t.updated_at,t.client_secret)}},tr=class e{constructor(e,t,r,s,i,n,a,o={},l={},c,d,u,h){this.id=e,this.name=t,this.slug=r,this.imageUrl=s,this.hasImage=i,this.createdAt=n,this.updatedAt=a,this.publicMetadata=o,this.privateMetadata=l,this.maxAllowedMemberships=c,this.adminDeleteEnabled=d,this.membersCount=u,this.createdBy=h,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.name,t.slug,t.image_url||"",t.has_image,t.created_at,t.updated_at,t.public_metadata,t.private_metadata,t.max_allowed_memberships,t.admin_delete_enabled,t.members_count,t.created_by);return r._raw=t,r}},ts=class e{constructor(e,t,r,s,i,n,a,o,l,c,d={},u={},h){this.id=e,this.emailAddress=t,this.role=r,this.roleName=s,this.organizationId=i,this.createdAt=n,this.updatedAt=a,this.expiresAt=o,this.url=l,this.status=c,this.publicMetadata=d,this.privateMetadata=u,this.publicOrganizationData=h,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.email_address,t.role,t.role_name,t.organization_id,t.created_at,t.updated_at,t.expires_at,t.url,t.status,t.public_metadata,t.private_metadata,t.public_organization_data);return r._raw=t,r}},ti=class e{constructor(e,t,r,s={},i={},n,a,o,l){this.id=e,this.role=t,this.permissions=r,this.publicMetadata=s,this.privateMetadata=i,this.createdAt=n,this.updatedAt=a,this.organization=o,this.publicUserData=l,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.role,t.permissions,t.public_metadata,t.private_metadata,t.created_at,t.updated_at,tr.fromJSON(t.organization),tn.fromJSON(t.public_user_data));return r._raw=t,r}},tn=class e{constructor(e,t,r,s,i,n){this.identifier=e,this.firstName=t,this.lastName=r,this.imageUrl=s,this.hasImage=i,this.userId=n}static fromJSON(t){return new e(t.identifier,t.first_name,t.last_name,t.image_url,t.has_image,t.user_id)}},ta=class e{constructor(e,t,r,s,i,n,a,o,l){this.enabled=e,this.maxAllowedMemberships=t,this.maxAllowedRoles=r,this.maxAllowedPermissions=s,this.creatorRole=i,this.adminDeleteEnabled=n,this.domainsEnabled=a,this.domainsEnrollmentModes=o,this.domainsDefaultRole=l}static fromJSON(t){return new e(t.enabled,t.max_allowed_memberships,t.max_allowed_roles,t.max_allowed_permissions,t.creator_role,t.admin_delete_enabled,t.domains_enabled,t.domains_enrollment_modes,t.domains_default_role)}},to=class e{constructor(e,t,r,s,i,n){this.id=e,this.phoneNumber=t,this.reservedForSecondFactor=r,this.defaultSecondFactor=s,this.verification=i,this.linkedTo=n}static fromJSON(t){return new e(t.id,t.phone_number,t.reserved_for_second_factor,t.default_second_factor,t.verification&&eZ.fromJSON(t.verification),t.linked_to.map(e=>eY.fromJSON(e)))}},tl=class e{constructor(e,t,r,s,i,n,a){this.id=e,this.domainId=t,this.lastRunAt=r,this.proxyUrl=s,this.successful=i,this.createdAt=n,this.updatedAt=a}static fromJSON(t){return new e(t.id,t.domain_id,t.last_run_at,t.proxy_url,t.successful,t.created_at,t.updated_at)}},tc=class e{constructor(e,t,r,s){this.id=e,this.url=t,this.createdAt=r,this.updatedAt=s}static fromJSON(t){return new e(t.id,t.url,t.created_at,t.updated_at)}},td=class e{constructor(e,t,r,s,i,n,a,o,l,c,d,u,h,p,f,m,g,y,k,_,v){this.id=e,this.name=t,this.domain=r,this.organizationId=s,this.idpEntityId=i,this.idpSsoUrl=n,this.idpCertificate=a,this.idpMetadataUrl=o,this.idpMetadata=l,this.acsUrl=c,this.spEntityId=d,this.spMetadataUrl=u,this.active=h,this.provider=p,this.userCount=f,this.syncUserAttributes=m,this.allowSubdomains=g,this.allowIdpInitiated=y,this.createdAt=k,this.updatedAt=_,this.attributeMapping=v}static fromJSON(t){return new e(t.id,t.name,t.domain,t.organization_id,t.idp_entity_id,t.idp_sso_url,t.idp_certificate,t.idp_metadata_url,t.idp_metadata,t.acs_url,t.sp_entity_id,t.sp_metadata_url,t.active,t.provider,t.user_count,t.sync_user_attributes,t.allow_subdomains,t.allow_idp_initiated,t.created_at,t.updated_at,t.attribute_mapping&&th.fromJSON(t.attribute_mapping))}},tu=class e{constructor(e,t,r,s,i,n,a,o,l,c){this.id=e,this.name=t,this.domain=r,this.active=s,this.provider=i,this.syncUserAttributes=n,this.allowSubdomains=a,this.allowIdpInitiated=o,this.createdAt=l,this.updatedAt=c}static fromJSON(t){return new e(t.id,t.name,t.domain,t.active,t.provider,t.sync_user_attributes,t.allow_subdomains,t.allow_idp_initiated,t.created_at,t.updated_at)}},th=class e{constructor(e,t,r,s){this.userId=e,this.emailAddress=t,this.firstName=r,this.lastName=s}static fromJSON(t){return new e(t.user_id,t.email_address,t.first_name,t.last_name)}},tp=class e{constructor(e,t,r,s,i,n,a,o,l){this.id=e,this.provider=t,this.providerUserId=r,this.active=s,this.emailAddress=i,this.firstName=n,this.lastName=a,this.verification=o,this.samlConnection=l}static fromJSON(t){return new e(t.id,t.provider,t.provider_user_id,t.active,t.email_address,t.first_name,t.last_name,t.verification&&eZ.fromJSON(t.verification),t.saml_connection&&tu.fromJSON(t.saml_connection))}},tf=class e{constructor(e,t,r,s,i,n,a){this.id=e,this.userId=t,this.token=r,this.status=s,this.url=i,this.createdAt=n,this.updatedAt=a}static fromJSON(t){return new e(t.id,t.user_id,t.token,t.status,t.url,t.created_at,t.updated_at)}},tm=class e{constructor(e,t){this.nextAction=e,this.supportedStrategies=t}static fromJSON(t){return new e(t.next_action,t.supported_strategies)}},tg=class e{constructor(e,t,r,s){this.emailAddress=e,this.phoneNumber=t,this.web3Wallet=r,this.externalAccount=s}static fromJSON(t){return new e(t.email_address&&tm.fromJSON(t.email_address),t.phone_number&&tm.fromJSON(t.phone_number),t.web3_wallet&&tm.fromJSON(t.web3_wallet),t.external_account)}},ty=class e{constructor(e,t,r,s,i,n,a,o,l,c,d,u,h,p,f,m,g,y,k,_,v,w){this.id=e,this.status=t,this.requiredFields=r,this.optionalFields=s,this.missingFields=i,this.unverifiedFields=n,this.verifications=a,this.username=o,this.emailAddress=l,this.phoneNumber=c,this.web3Wallet=d,this.passwordEnabled=u,this.firstName=h,this.lastName=p,this.customAction=f,this.externalId=m,this.createdSessionId=g,this.createdUserId=y,this.abandonAt=k,this.legalAcceptedAt=_,this.publicMetadata=v,this.unsafeMetadata=w}static fromJSON(t){return new e(t.id,t.status,t.required_fields,t.optional_fields,t.missing_fields,t.unverified_fields,t.verifications?tg.fromJSON(t.verifications):null,t.username,t.email_address,t.phone_number,t.web3_wallet,t.password_enabled,t.first_name,t.last_name,t.custom_action,t.external_id,t.created_session_id,t.created_user_id,t.abandon_at,t.legal_accepted_at,t.public_metadata,t.unsafe_metadata)}},tk=class e{constructor(e,t,r,s,i,n,a){this.id=e,this.fromPhoneNumber=t,this.toPhoneNumber=r,this.message=s,this.status=i,this.phoneNumberId=n,this.data=a}static fromJSON(t){return new e(t.id,t.from_phone_number,t.to_phone_number,t.message,t.status,t.phone_number_id,t.data)}},t_=class e{constructor(e){this.jwt=e}static fromJSON(t){return new e(t.jwt)}},tv=class e{constructor(e,t,r){this.id=e,this.web3Wallet=t,this.verification=r}static fromJSON(t){return new e(t.id,t.web3_wallet,t.verification&&eZ.fromJSON(t.verification))}},tw=class e{constructor(e,t,r,s,i,n,a,o,l,c,d,u,h,p,f,m,g,y,k,_={},v={},w={},S=[],b=[],T=[],A=[],x=[],E,I,C=null,O,U){this.id=e,this.passwordEnabled=t,this.totpEnabled=r,this.backupCodeEnabled=s,this.twoFactorEnabled=i,this.banned=n,this.locked=a,this.createdAt=o,this.updatedAt=l,this.imageUrl=c,this.hasImage=d,this.primaryEmailAddressId=u,this.primaryPhoneNumberId=h,this.primaryWeb3WalletId=p,this.lastSignInAt=f,this.externalId=m,this.username=g,this.firstName=y,this.lastName=k,this.publicMetadata=_,this.privateMetadata=v,this.unsafeMetadata=w,this.emailAddresses=S,this.phoneNumbers=b,this.web3Wallets=T,this.externalAccounts=A,this.samlAccounts=x,this.lastActiveAt=E,this.createOrganizationEnabled=I,this.createOrganizationsLimit=C,this.deleteSelfEnabled=O,this.legalAcceptedAt=U,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.password_enabled,t.totp_enabled,t.backup_code_enabled,t.two_factor_enabled,t.banned,t.locked,t.created_at,t.updated_at,t.image_url,t.has_image,t.primary_email_address_id,t.primary_phone_number_id,t.primary_web3_wallet_id,t.last_sign_in_at,t.external_id,t.username,t.first_name,t.last_name,t.public_metadata,t.private_metadata,t.unsafe_metadata,(t.email_addresses||[]).map(e=>e0.fromJSON(e)),(t.phone_numbers||[]).map(e=>to.fromJSON(e)),(t.web3_wallets||[]).map(e=>tv.fromJSON(e)),(t.external_accounts||[]).map(e=>e1.fromJSON(e)),(t.saml_accounts||[]).map(e=>tp.fromJSON(e)),t.last_active_at,t.create_organization_enabled,t.create_organizations_limit,t.delete_self_enabled,t.legal_accepted_at);return r._raw=t,r}get primaryEmailAddress(){return this.emailAddresses.find(({id:e})=>e===this.primaryEmailAddressId)??null}get primaryPhoneNumber(){return this.phoneNumbers.find(({id:e})=>e===this.primaryPhoneNumberId)??null}get primaryWeb3Wallet(){return this.web3Wallets.find(({id:e})=>e===this.primaryWeb3WalletId)??null}get fullName(){return[this.firstName,this.lastName].join(" ").trim()||null}},tS=class e{constructor(e,t,r,s,i,n,a){this.id=e,this.emailAddress=t,this.status=r,this.invitation=s,this.createdAt=i,this.updatedAt=n,this.isLocked=a}static fromJSON(t){return new e(t.id,t.email_address,t.status,t.invitation&&e3.fromJSON(t.invitation),t.created_at,t.updated_at,t.is_locked)}};function tb(e){if("string"!=typeof e&&"object"in e&&"deleted"in e)return eV.fromJSON(e);switch(e.object){case e8.AccountlessApplication:return ej.fromJSON(e);case e8.ActorToken:return eH.fromJSON(e);case e8.AllowlistIdentifier:return eF.fromJSON(e);case e8.ApiKey:return eD.fromJSON(e);case e8.BlocklistIdentifier:return eK.fromJSON(e);case e8.Client:return e$.fromJSON(e);case e8.Cookies:return eG.fromJSON(e);case e8.Domain:return eQ.fromJSON(e);case e8.EmailAddress:return e0.fromJSON(e);case e8.Email:return eX.fromJSON(e);case e8.IdpOAuthAccessToken:return e2.fromJSON(e);case e8.Instance:return e5.fromJSON(e);case e8.InstanceRestrictions:return e4.fromJSON(e);case e8.InstanceSettings:return e6.fromJSON(e);case e8.Invitation:return e3.fromJSON(e);case e8.JwtTemplate:return e7.fromJSON(e);case e8.MachineToken:return e9.fromJSON(e);case e8.OauthAccessToken:return te.fromJSON(e);case e8.OAuthApplication:return tt.fromJSON(e);case e8.Organization:return tr.fromJSON(e);case e8.OrganizationInvitation:return ts.fromJSON(e);case e8.OrganizationMembership:return ti.fromJSON(e);case e8.OrganizationSettings:return ta.fromJSON(e);case e8.PhoneNumber:return to.fromJSON(e);case e8.ProxyCheck:return tl.fromJSON(e);case e8.RedirectUrl:return tc.fromJSON(e);case e8.SamlConnection:return td.fromJSON(e);case e8.SignInToken:return tf.fromJSON(e);case e8.SignUpAttempt:return ty.fromJSON(e);case e8.Session:return eB.fromJSON(e);case e8.SmsMessage:return tk.fromJSON(e);case e8.Token:return t_.fromJSON(e);case e8.TotalCount:return e.total_count;case e8.User:return tw.fromJSON(e);case e8.WaitlistEntry:return tS.fromJSON(e);default:return e}}function tT(e){var t;return t=async t=>{let r,{secretKey:s,requireSecretKey:n=!0,apiUrl:a=E,apiVersion:o="v1",userAgent:l=I,skipApiVersionInUrl:c=!1}=e,{path:d,method:u,queryParams:h,headerParams:p,bodyParams:f,formData:m}=t;n&&M(s);let g=new URL(c?F(a,d):F(a,o,d));if(h)for(let[e,t]of Object.entries(b({...h})))t&&[t].flat().forEach(t=>g.searchParams.append(e,t));let y=new Headers({"Clerk-API-Version":C,"User-Agent":l,...p});s&&y.set("Authorization",`Bearer ${s}`);try{var k;m?r=await i.fA.fetch(g.href,{method:u,headers:y,body:m}):(y.set("Content-Type","application/json"),r=await i.fA.fetch(g.href,{method:u,headers:y,...(()=>{if(!("GET"!==u&&f&&Object.keys(f).length>0))return null;let e=e=>b(e,{deep:!1});return{body:JSON.stringify(Array.isArray(f)?f.map(e):e(f))}})()}));let e=r?.headers&&r.headers?.get(P.Headers.ContentType)===P.ContentTypes.Json,t=await (e?r.json():r.text());if(!r.ok)return{data:null,errors:tE(t),status:r?.status,statusText:r?.statusText,clerkTraceId:tA(t,r?.headers),retryAfter:tx(r?.headers)};return{...Array.isArray(t)?{data:t.map(e=>tb(e))}:(k=t)&&"object"==typeof k&&"data"in k&&Array.isArray(k.data)&&void 0!==k.data?{data:t.data.map(e=>tb(e)),totalCount:t.total_count}:{data:tb(t)},errors:null}}catch(e){if(e instanceof Error)return{data:null,errors:[{code:"unexpected_error",message:e.message||"Unexpected error"}],clerkTraceId:tA(e,r?.headers)};return{data:null,errors:tE(e),status:r?.status,statusText:r?.statusText,clerkTraceId:tA(e,r?.headers),retryAfter:tx(r?.headers)}}},async(...e)=>{let{data:r,errors:s,totalCount:i,status:n,statusText:a,clerkTraceId:o,retryAfter:l}=await t(...e);if(s){let e=new S.LR(a||"",{data:[],status:n,clerkTraceId:o,retryAfter:l});throw e.errors=s,e}return void 0!==i?{data:r,totalCount:i}:r}}function tA(e,t){return e&&"object"==typeof e&&"clerk_trace_id"in e&&"string"==typeof e.clerk_trace_id?e.clerk_trace_id:t?.get("cf-ray")||""}function tx(e){let t=e?.get("Retry-After");if(!t)return;let r=parseInt(t,10);if(!isNaN(r))return r}function tE(e){if(e&&"object"==typeof e&&"errors"in e){let t=e.errors;return t.length>0?t.map(S.u$):[]}return[]}function tI(e){let t=tT(e);return{__experimental_accountlessApplications:new $(tT({...e,requireSecretKey:!1})),actorTokens:new L(t),allowlistIdentifiers:new G(t),apiKeys:new Q(tT({...e,skipApiVersionInUrl:!0})),betaFeatures:new X(t),blocklistIdentifiers:new Z(t),clients:new et(t),domains:new es(t),emailAddresses:new en(t),idPOAuthAccessToken:new ea(tT({...e,skipApiVersionInUrl:!0})),instance:new el(t),invitations:new ed(t),jwks:new eh(t),jwtTemplates:new ef(t),machineTokens:new eu(tT({...e,skipApiVersionInUrl:!0})),oauthApplications:new ek(t),organizations:new eg(t),phoneNumbers:new ev(t),proxyChecks:new ew(t),redirectUrls:new eb(t),samlConnections:new eA(t),sessions:new eE(t),signInTokens:new eC(t),signUps:new eU(t),testingTokens:new eP(t),users:new eN(t),waitlistEntries:new ez(t),webhooks:new eJ(t)}}var tC={SessionToken:"session_token",ApiKey:"api_key",MachineToken:"machine_token",OAuthToken:"oauth_token"},tO="oat_",tU=["mt_",tO,"ak_"];function tP(e){return tU.some(t=>e.startsWith(t))}function tq(e){if(e.startsWith("mt_"))return tC.MachineToken;if(e.startsWith(tO))return tC.OAuthToken;if(e.startsWith("ak_"))return tC.ApiKey;throw Error("Unknown machine token type")}var tN=(e,t)=>!!e&&("any"===t||(Array.isArray(t)?t:[t]).includes(e)),tR=e=>()=>{let t={...e};return t.secretKey=(t.secretKey||"").substring(0,7),t.jwtKey=(t.jwtKey||"").substring(0,7),{...t}};function tz(e,t,r){let{actor:s,sessionId:i,sessionStatus:n,userId:a,orgId:o,orgRole:l,orgSlug:c,orgPermissions:d,factorVerificationAge:u}=w(r),h=tI(e),p=tF({sessionId:i,sessionToken:t,fetcher:async(e,t,r)=>(await h.sessions.getToken(e,t||"",r)).jwt});return{tokenType:tC.SessionToken,actor:s,sessionClaims:r,sessionId:i,sessionStatus:n,userId:a,orgId:o,orgRole:l,orgSlug:c,orgPermissions:d,factorVerificationAge:u,getToken:p,has:_({orgId:o,orgRole:l,orgPermissions:d,userId:a,factorVerificationAge:u,features:r.fea||"",plans:r.pla||""}),debug:tR({...e,sessionToken:t}),isAuthenticated:!0}}function tM(e,t){return{tokenType:tC.SessionToken,sessionClaims:null,sessionId:null,sessionStatus:t??null,userId:null,actor:null,orgId:null,orgRole:null,orgSlug:null,orgPermissions:null,factorVerificationAge:null,getToken:()=>Promise.resolve(null),has:()=>!1,debug:tR(e),isAuthenticated:!1}}function tJ(e,t,r,s){let i={id:r.id,subject:r.subject,getToken:()=>Promise.resolve(t),has:()=>!1,debug:tR(s),isAuthenticated:!0};switch(e){case tC.ApiKey:return{...i,tokenType:e,name:r.name,claims:r.claims,scopes:r.scopes,userId:r.subject.startsWith("user_")?r.subject:null,orgId:r.subject.startsWith("org_")?r.subject:null};case tC.MachineToken:return{...i,tokenType:e,name:r.name,claims:r.claims,scopes:r.scopes,machineId:r.subject};case tC.OAuthToken:return{...i,tokenType:e,scopes:r.scopes,userId:r.subject,clientId:r.clientId};default:throw Error(`Invalid token type: ${e}`)}}function tj(e,t){let r={id:null,subject:null,scopes:null,has:()=>!1,getToken:()=>Promise.resolve(null),debug:tR(t),isAuthenticated:!1};switch(e){case tC.ApiKey:return{...r,tokenType:e,name:null,claims:null,scopes:null,userId:null,orgId:null};case tC.MachineToken:return{...r,tokenType:e,name:null,claims:null,scopes:null,machineId:null};case tC.OAuthToken:return{...r,tokenType:e,scopes:null,userId:null,clientId:null};default:throw Error(`Invalid token type: ${e}`)}}function tH(){return{isAuthenticated:!1,tokenType:null,getToken:()=>Promise.resolve(null),has:()=>!1,debug:()=>({})}}var tF=e=>{let{fetcher:t,sessionToken:r,sessionId:s}=e||{};return async(e={})=>s?e.template||void 0!==e.expiresInSeconds?t(s,e.template,e.expiresInSeconds):r:null},tD=(e,{treatPendingAsSignedOut:t=!0,...r})=>{let s=tz(r,e.raw.text,e.payload);return t&&"pending"===s.sessionStatus?tM(r,s.sessionStatus):s},tK=({authObject:e,acceptsToken:t=tC.SessionToken})=>"any"===t?e:Array.isArray(t)?tN(e.tokenType,t)?e:tH():tN(e.tokenType,t)?e:!function(e){return e===tC.ApiKey||e===tC.MachineToken||e===tC.OAuthToken}(t)?tM(e.debug):tj(t,e.debug),tL={SignedIn:"signed-in",SignedOut:"signed-out",Handshake:"handshake"},tB={ClientUATWithoutSessionToken:"client-uat-but-no-session-token",DevBrowserMissing:"dev-browser-missing",DevBrowserSync:"dev-browser-sync",PrimaryRespondsToSyncing:"primary-responds-to-syncing",PrimaryDomainCrossOriginSync:"primary-domain-cross-origin-sync",SatelliteCookieNeedsSyncing:"satellite-needs-syncing",SessionTokenAndUATMissing:"session-token-and-uat-missing",SessionTokenMissing:"session-token-missing",SessionTokenExpired:"session-token-expired",SessionTokenIATBeforeClientUAT:"session-token-iat-before-client-uat",SessionTokenNBF:"session-token-nbf",SessionTokenIatInTheFuture:"session-token-iat-in-the-future",SessionTokenWithoutClientUAT:"session-token-but-no-client-uat",ActiveOrganizationMismatch:"active-organization-mismatch",TokenTypeMismatch:"token-type-mismatch",UnexpectedError:"unexpected-error"};function t$(e){let{authenticateContext:t,headers:r=new Headers,token:s}=e;return{status:tL.SignedIn,reason:null,message:null,proxyUrl:t.proxyUrl||"",publishableKey:t.publishableKey||"",isSatellite:t.isSatellite||!1,domain:t.domain||"",signInUrl:t.signInUrl||"",signUpUrl:t.signUpUrl||"",afterSignInUrl:t.afterSignInUrl||"",afterSignUpUrl:t.afterSignUpUrl||"",isSignedIn:!0,isAuthenticated:!0,tokenType:e.tokenType,toAuth:({treatPendingAsSignedOut:r=!0}={})=>{if(e.tokenType===tC.SessionToken){let{sessionClaims:i}=e,n=tz(t,s,i);return r&&"pending"===n.sessionStatus?tM(void 0,n.sessionStatus):n}let{machineData:i}=e;return tJ(e.tokenType,s,i,t)},headers:r,token:s}}function tW(e){let{authenticateContext:t,headers:r=new Headers,reason:s,message:i="",tokenType:n}=e;return tG({status:tL.SignedOut,reason:s,message:i,proxyUrl:t.proxyUrl||"",publishableKey:t.publishableKey||"",isSatellite:t.isSatellite||!1,domain:t.domain||"",signInUrl:t.signInUrl||"",signUpUrl:t.signUpUrl||"",afterSignInUrl:t.afterSignInUrl||"",afterSignUpUrl:t.afterSignUpUrl||"",isSignedIn:!1,isAuthenticated:!1,tokenType:n,toAuth:()=>n===tC.SessionToken?tM({...t,status:tL.SignedOut,reason:s,message:i}):tj(n,{reason:s,message:i,headers:r}),headers:r,token:null})}var tG=e=>{let t=new Headers(e.headers||{});if(e.message)try{t.set(P.Headers.AuthMessage,e.message)}catch{}if(e.reason)try{t.set(P.Headers.AuthReason,e.reason)}catch{}if(e.status)try{t.set(P.Headers.AuthStatus,e.status)}catch{}return e.headers=t,e},tV=class extends URL{isCrossOrigin(e){return this.origin!==new URL(e.toString()).origin}},tQ=(...e)=>new tV(...e),tX=class extends Request{constructor(e,t){super("string"!=typeof e&&"url"in e?e.url:String(e),t||"string"==typeof e?void 0:e),this.clerkUrl=this.deriveUrlFromHeaders(this),this.cookies=this.parseCookies(this)}toJSON(){return{url:this.clerkUrl.href,method:this.method,headers:JSON.stringify(Object.fromEntries(this.headers)),clerkUrl:this.clerkUrl.toString(),cookies:JSON.stringify(Object.fromEntries(this.cookies))}}deriveUrlFromHeaders(e){let t=new URL(e.url),r=e.headers.get(P.Headers.ForwardedProto),s=e.headers.get(P.Headers.ForwardedHost),i=e.headers.get(P.Headers.Host),n=t.protocol,a=this.getFirstValueFromHeader(s)??i,o=this.getFirstValueFromHeader(r)??n?.replace(/[:/]/,""),l=a&&o?`${o}://${a}`:t.origin;return l===t.origin?tQ(t):tQ(t.pathname+t.search,l)}getFirstValueFromHeader(e){return e?.split(",")[0]}parseCookies(e){return new Map(Object.entries((0,T.qg)(this.decodeCookieValue(e.headers.get("cookie")||""))))}decodeCookieValue(e){return e?e.replace(/(%[0-9A-Z]{2})+/g,decodeURIComponent):e}},tY=(...e)=>e[0]instanceof tX?e[0]:new tX(...e),tZ=e=>e.split(";")[0]?.split("=")[0],t0=e=>e.split(";")[0]?.split("=")[1],t1={},t2=0;function t5(e,t=!0){t1[e.kid]=e,t2=t?Date.now():-1}var t4="local";function t6(e){if(!t1[t4]){if(!e)throw new n.zF({action:n.z.SetClerkJWTKey,message:"Missing local JWK.",reason:n.jn.LocalJWKMissing});t5({kid:"local",kty:"RSA",alg:"RS256",n:e.replace(/\r\n|\n|\r/g,"").replace("-----BEGIN PUBLIC KEY-----","").replace("-----END PUBLIC KEY-----","").replace("MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA","").replace("IDAQAB","").replace(/\+/g,"-").replace(/\//g,"_"),e:"AQAB"},!1)}return t1[t4]}async function t3({secretKey:e,apiUrl:t=E,apiVersion:r="v1",kid:i,skipJwksCache:a}){if(a||function(){if(-1===t2)return!1;let e=Date.now()-t2>=3e5;return e&&(t1={}),e}()||!t1[i]){if(!e)throw new n.zF({action:n.z.ContactSupport,message:"Failed to load JWKS from Clerk Backend or Frontend API.",reason:n.jn.RemoteJWKFailedToLoad});let{keys:i}=await (0,s.L5)(()=>t8(t,e,r));if(!i||!i.length)throw new n.zF({action:n.z.ContactSupport,message:"The JWKS endpoint did not contain any signing keys. Contact <EMAIL>.",reason:n.jn.RemoteJWKFailedToLoad});i.forEach(e=>t5(e))}let o=t1[i];if(!o){let e=Object.values(t1).map(e=>e.kid).sort().join(", ");throw new n.zF({action:`Go to your Dashboard and validate your secret and public keys are correct. ${n.z.ContactSupport} if the issue persists.`,message:`Unable to find a signing key in JWKS that matches the kid='${i}' of the provided session token. Please make sure that the __session cookie or the HTTP authorization header contain a Clerk-generated session JWT. The following kid is available: ${e}`,reason:n.jn.JWKKidMismatch})}return o}async function t8(e,t,r){if(!t)throw new n.zF({action:n.z.SetClerkSecretKey,message:"Missing Clerk Secret Key or API Key. Go to https://dashboard.clerk.com and get your key for your instance.",reason:n.jn.RemoteJWKFailedToLoad});let s=new URL(e);s.pathname=F(s.pathname,r,"/jwks");let a=await i.fA.fetch(s.href,{headers:{Authorization:`Bearer ${t}`,"Clerk-API-Version":C,"Content-Type":"application/json","User-Agent":I}});if(!a.ok){let e=await a.json(),t=t9(e?.errors,n.qu.InvalidSecretKey);if(t){let e=n.jn.InvalidSecretKey;throw new n.zF({action:n.z.ContactSupport,message:t.message,reason:e})}throw new n.zF({action:n.z.ContactSupport,message:`Error loading Clerk JWKS from ${s.href} with code=${a.status}`,reason:n.jn.RemoteJWKFailedToLoad})}return a.json()}var t9=(e,t)=>e?e.find(e=>e.code===t):null;async function t7(e,t){let{data:r,errors:s}=(0,i.iU)(e);if(s)return{errors:s};let{header:a}=r,{kid:o}=a;try{let r;if(t.jwtKey)r=t6(t.jwtKey);else{if(!t.secretKey)return{errors:[new n.zF({action:n.z.SetClerkJWTKey,message:"Failed to resolve JWK during verification.",reason:n.jn.JWKFailedToResolve})]};r=await t3({...t,kid:o})}return await (0,i.J0)(e,{...t,key:r})}catch(e){return{errors:[e]}}}function re(e,t,r){if((0,S.$R)(t)){let s,i;switch(t.status){case 401:s=n.h5.InvalidSecretKey,i=t.errors[0]?.message||"Invalid secret key";break;case 404:s=n.h5.TokenInvalid,i=r;break;default:s=n.h5.UnexpectedError,i="Unexpected error"}return{data:void 0,tokenType:e,errors:[new n.sM({message:i,code:s,status:t.status})]}}return{data:void 0,tokenType:e,errors:[new n.sM({message:"Unexpected error",code:n.h5.UnexpectedError,status:t.status})]}}async function rt(e,t){try{let r=tI(t);return{data:await r.machineTokens.verifySecret(e),tokenType:tC.MachineToken,errors:void 0}}catch(e){return re(tC.MachineToken,e,"Machine token not found")}}async function rr(e,t){try{let r=tI(t);return{data:await r.idPOAuthAccessToken.verifyAccessToken(e),tokenType:tC.OAuthToken,errors:void 0}}catch(e){return re(tC.OAuthToken,e,"OAuth token not found")}}async function rs(e,t){try{let r=tI(t);return{data:await r.apiKeys.verifySecret(e),tokenType:tC.ApiKey,errors:void 0}}catch(e){return re(tC.ApiKey,e,"API key not found")}}async function ri(e,t){if(e.startsWith("mt_"))return rt(e,t);if(e.startsWith(tO))return rr(e,t);if(e.startsWith("ak_"))return rs(e,t);throw Error("Unknown machine token type")}async function rn(e,{key:t}){let{data:r,errors:s}=(0,i.iU)(e);if(s)throw s[0];let{header:a,payload:o}=r,{typ:l,alg:c}=a;(0,i.qf)(l),(0,i.l3)(c);let{data:d,errors:u}=await (0,i.nk)(r,t);if(u)throw new n.zF({reason:n.jn.TokenVerificationFailed,message:`Error verifying handshake token. ${u[0]}`});if(!d)throw new n.zF({reason:n.jn.TokenInvalidSignature,message:"Handshake signature is invalid."});return o}async function ra(e,t){let r,{secretKey:s,apiUrl:a,apiVersion:o,jwksCacheTtlInMs:l,jwtKey:c,skipJwksCache:d}=t,{data:u,errors:h}=(0,i.iU)(e);if(h)throw h[0];let{kid:p}=u.header;if(c)r=t6(c);else if(s)r=await t3({secretKey:s,apiUrl:a,apiVersion:o,kid:p,jwksCacheTtlInMs:l,skipJwksCache:d});else throw new n.zF({action:n.z.SetClerkJWTKey,message:"Failed to resolve JWK during handshake verification.",reason:n.jn.JWKFailedToResolve});return await rn(e,{key:r})}var ro=class{constructor(e,t,r){this.authenticateContext=e,this.options=t,this.organizationMatcher=r}isRequestEligibleForHandshake(){let{accept:e,secFetchDest:t}=this.authenticateContext;return!!("document"===t||"iframe"===t||!t&&e?.startsWith("text/html"))}buildRedirectToHandshake(e){if(!this.authenticateContext?.clerkUrl)throw Error("Missing clerkUrl in authenticateContext");let t=this.removeDevBrowserFromURL(this.authenticateContext.clerkUrl),r=this.authenticateContext.frontendApi.startsWith("http")?this.authenticateContext.frontendApi:`https://${this.authenticateContext.frontendApi}`,s=new URL("v1/client/handshake",r=r.replace(/\/+$/,"")+"/");s.searchParams.append("redirect_url",t?.href||""),s.searchParams.append("__clerk_api_version",C),s.searchParams.append(P.QueryParameters.SuffixedCookies,this.authenticateContext.usesSuffixedCookies().toString()),s.searchParams.append(P.QueryParameters.HandshakeReason,e),s.searchParams.append(P.QueryParameters.HandshakeFormat,"nonce"),"development"===this.authenticateContext.instanceType&&this.authenticateContext.devBrowserToken&&s.searchParams.append(P.QueryParameters.DevBrowser,this.authenticateContext.devBrowserToken);let i=this.getOrganizationSyncTarget(this.authenticateContext.clerkUrl,this.organizationMatcher);return i&&this.getOrganizationSyncQueryParams(i).forEach((e,t)=>{s.searchParams.append(t,e)}),new Headers({[P.Headers.Location]:s.href})}async getCookiesFromHandshake(){let e=[];if(this.authenticateContext.handshakeNonce)try{let t=await this.authenticateContext.apiClient?.clients.getHandshakePayload({nonce:this.authenticateContext.handshakeNonce});t&&e.push(...t.directives)}catch(e){console.error("Clerk: HandshakeService: error getting handshake payload:",e)}else if(this.authenticateContext.handshakeToken){let t=await ra(this.authenticateContext.handshakeToken,this.authenticateContext);t&&Array.isArray(t.handshake)&&e.push(...t.handshake)}return e}async resolveHandshake(){let e=new Headers({"Access-Control-Allow-Origin":"null","Access-Control-Allow-Credentials":"true"}),t=await this.getCookiesFromHandshake(),r="";if(t.forEach(t=>{e.append("Set-Cookie",t),tZ(t).startsWith(P.Cookies.Session)&&(r=t0(t))}),"development"===this.authenticateContext.instanceType){let t=new URL(this.authenticateContext.clerkUrl);t.searchParams.delete(P.QueryParameters.Handshake),t.searchParams.delete(P.QueryParameters.HandshakeHelp),e.append(P.Headers.Location,t.toString()),e.set(P.Headers.CacheControl,"no-store")}if(""===r)return tW({tokenType:tC.SessionToken,authenticateContext:this.authenticateContext,reason:tB.SessionTokenMissing,message:"",headers:e});let{data:s,errors:[i]=[]}=await t7(r,this.authenticateContext);if(s)return t$({tokenType:tC.SessionToken,authenticateContext:this.authenticateContext,sessionClaims:s,headers:e,token:r});if("development"===this.authenticateContext.instanceType&&(i?.reason===n.jn.TokenExpired||i?.reason===n.jn.TokenNotActiveYet||i?.reason===n.jn.TokenIatInTheFuture)){let t=new n.zF({action:i.action,message:i.message,reason:i.reason});t.tokenCarrier="cookie",console.error(`Clerk: Clock skew detected. This usually means that your system clock is inaccurate. Clerk will attempt to account for the clock skew in development.

To resolve this issue, make sure your system's clock is set to the correct time (e.g. turn off and on automatic time synchronization).

---

${t.getFullMessage()}`);let{data:s,errors:[a]=[]}=await t7(r,{...this.authenticateContext,clockSkewInMs:864e5});if(s)return t$({tokenType:tC.SessionToken,authenticateContext:this.authenticateContext,sessionClaims:s,headers:e,token:r});throw Error(a?.message||"Clerk: Handshake retry failed.")}throw Error(i?.message||"Clerk: Handshake failed.")}handleTokenVerificationErrorInDevelopment(e){if(e.reason===n.jn.TokenInvalidSignature)throw Error("Clerk: Handshake token verification failed due to an invalid signature. If you have switched Clerk keys locally, clear your cookies and try again.");throw Error(`Clerk: Handshake token verification failed: ${e.getFullMessage()}.`)}checkAndTrackRedirectLoop(e){if(3===this.authenticateContext.handshakeRedirectLoopCounter)return!0;let t=this.authenticateContext.handshakeRedirectLoopCounter+1,r=P.Cookies.RedirectCount;return e.append("Set-Cookie",`${r}=${t}; SameSite=Lax; HttpOnly; Max-Age=3`),!1}removeDevBrowserFromURL(e){let t=new URL(e);return t.searchParams.delete(P.QueryParameters.DevBrowser),t.searchParams.delete(P.QueryParameters.LegacyDevBrowser),t}getOrganizationSyncTarget(e,t){return t.findTarget(e)}getOrganizationSyncQueryParams(e){let t=new Map;return"personalAccount"===e.type&&t.set("organization_id",""),"organization"===e.type&&(e.organizationId&&t.set("organization_id",e.organizationId),e.organizationSlug&&t.set("organization_id",e.organizationSlug)),t}},rl=class{constructor(e){this.organizationPattern=this.createMatcher(e?.organizationPatterns),this.personalAccountPattern=this.createMatcher(e?.personalAccountPatterns)}createMatcher(e){if(!e)return null;try{return function(e,t){try{var r,s,i,n,a,o,l;return r=void 0,s=[],i=function e(t,r,s){var i;return t instanceof RegExp?function(e,t){if(!t)return e;for(var r=/\((?:\?<(.*?)>)?(?!\?)/g,s=0,i=r.exec(e.source);i;)t.push({name:i[1]||s++,prefix:"",suffix:"",modifier:"",pattern:""}),i=r.exec(e.source);return e}(t,r):Array.isArray(t)?(i=t.map(function(t){return e(t,r,s).source}),new RegExp("(?:".concat(i.join("|"),")"),x(s))):function(e,t,r){void 0===r&&(r={});for(var s=r.strict,i=void 0!==s&&s,n=r.start,a=r.end,o=r.encode,l=void 0===o?function(e){return e}:o,c=r.delimiter,d=r.endsWith,u="[".concat(A(void 0===d?"":d),"]|$"),h="[".concat(A(void 0===c?"/#?":c),"]"),p=void 0===n||n?"^":"",f=0;f<e.length;f++){var m=e[f];if("string"==typeof m)p+=A(l(m));else{var g=A(l(m.prefix)),y=A(l(m.suffix));if(m.pattern)if(t&&t.push(m),g||y)if("+"===m.modifier||"*"===m.modifier){var k="*"===m.modifier?"?":"";p+="(?:".concat(g,"((?:").concat(m.pattern,")(?:").concat(y).concat(g,"(?:").concat(m.pattern,"))*)").concat(y,")").concat(k)}else p+="(?:".concat(g,"(").concat(m.pattern,")").concat(y,")").concat(m.modifier);else{if("+"===m.modifier||"*"===m.modifier)throw TypeError('Can not repeat "'.concat(m.name,'" without a prefix and suffix'));p+="(".concat(m.pattern,")").concat(m.modifier)}else p+="(?:".concat(g).concat(y,")").concat(m.modifier)}}if(void 0===a||a)i||(p+="".concat(h,"?")),p+=r.endsWith?"(?=".concat(u,")"):"$";else{var _=e[e.length-1],v="string"==typeof _?h.indexOf(_[_.length-1])>-1:void 0===_;i||(p+="(?:".concat(h,"(?=").concat(u,"))?")),v||(p+="(?=".concat(h,"|").concat(u,")"))}return new RegExp(p,x(r))}(function(e,t){void 0===t&&(t={});for(var r=function(e){for(var t=[],r=0;r<e.length;){var s=e[r];if("*"===s||"+"===s||"?"===s){t.push({type:"MODIFIER",index:r,value:e[r++]});continue}if("\\"===s){t.push({type:"ESCAPED_CHAR",index:r++,value:e[r++]});continue}if("{"===s){t.push({type:"OPEN",index:r,value:e[r++]});continue}if("}"===s){t.push({type:"CLOSE",index:r,value:e[r++]});continue}if(":"===s){for(var i="",n=r+1;n<e.length;){var a=e.charCodeAt(n);if(a>=48&&a<=57||a>=65&&a<=90||a>=97&&a<=122||95===a){i+=e[n++];continue}break}if(!i)throw TypeError("Missing parameter name at ".concat(r));t.push({type:"NAME",index:r,value:i}),r=n;continue}if("("===s){var o=1,l="",n=r+1;if("?"===e[n])throw TypeError('Pattern cannot start with "?" at '.concat(n));for(;n<e.length;){if("\\"===e[n]){l+=e[n++]+e[n++];continue}if(")"===e[n]){if(0==--o){n++;break}}else if("("===e[n]&&(o++,"?"!==e[n+1]))throw TypeError("Capturing groups are not allowed at ".concat(n));l+=e[n++]}if(o)throw TypeError("Unbalanced pattern at ".concat(r));if(!l)throw TypeError("Missing pattern at ".concat(r));t.push({type:"PATTERN",index:r,value:l}),r=n;continue}t.push({type:"CHAR",index:r,value:e[r++]})}return t.push({type:"END",index:r,value:""}),t}(e),s=t.prefixes,i=void 0===s?"./":s,n=t.delimiter,a=void 0===n?"/#?":n,o=[],l=0,c=0,d="",u=function(e){if(c<r.length&&r[c].type===e)return r[c++].value},h=function(e){var t=u(e);if(void 0!==t)return t;var s=r[c],i=s.type,n=s.index;throw TypeError("Unexpected ".concat(i," at ").concat(n,", expected ").concat(e))},p=function(){for(var e,t="";e=u("CHAR")||u("ESCAPED_CHAR");)t+=e;return t},f=function(e){for(var t=0;t<a.length;t++){var r=a[t];if(e.indexOf(r)>-1)return!0}return!1},m=function(e){var t=o[o.length-1],r=e||(t&&"string"==typeof t?t:"");if(t&&!r)throw TypeError('Must have text between two parameters, missing text after "'.concat(t.name,'"'));return!r||f(r)?"[^".concat(A(a),"]+?"):"(?:(?!".concat(A(r),")[^").concat(A(a),"])+?")};c<r.length;){var g=u("CHAR"),y=u("NAME"),k=u("PATTERN");if(y||k){var _=g||"";-1===i.indexOf(_)&&(d+=_,_=""),d&&(o.push(d),d=""),o.push({name:y||l++,prefix:_,suffix:"",pattern:k||m(_),modifier:u("MODIFIER")||""});continue}var v=g||u("ESCAPED_CHAR");if(v){d+=v;continue}if(d&&(o.push(d),d=""),u("OPEN")){var _=p(),w=u("NAME")||"",S=u("PATTERN")||"",b=p();h("CLOSE"),o.push({name:w||(S?l++:""),pattern:w&&!S?m(_):S,prefix:_,suffix:b,modifier:u("MODIFIER")||""});continue}h("END")}return o}(t,s),r,s)}(e,s,r),n=s,a=r,void 0===a&&(a={}),o=a.decode,l=void 0===o?function(e){return e}:o,function(e){var t=i.exec(e);if(!t)return!1;for(var r=t[0],s=t.index,a=Object.create(null),o=1;o<t.length;o++)!function(e){if(void 0!==t[e]){var r=n[e-1];"*"===r.modifier||"+"===r.modifier?a[r.name]=t[e].split(r.prefix+r.suffix).map(function(e){return l(e,r)}):a[r.name]=l(t[e],r)}}(o);return{path:r,index:s,params:a}}}catch(e){throw Error(`Invalid path and options: Consult the documentation of path-to-regexp here: https://github.com/pillarjs/path-to-regexp/tree/6.x
${e.message}`)}}(e)}catch(t){throw Error(`Invalid pattern "${e}": ${t}`)}}findTarget(e){let t=this.findOrganizationTarget(e);return t||this.findPersonalAccountTarget(e)}findOrganizationTarget(e){if(!this.organizationPattern)return null;try{let t=this.organizationPattern(e.pathname);if(!t||!("params"in t))return null;let r=t.params;if(r.id)return{type:"organization",organizationId:r.id};if(r.slug)return{type:"organization",organizationSlug:r.slug};return null}catch(e){return console.error("Failed to match organization pattern:",e),null}}findPersonalAccountTarget(e){if(!this.personalAccountPattern)return null;try{return this.personalAccountPattern(e.pathname)?{type:"personalAccount"}:null}catch(e){return console.error("Failed to match personal account pattern:",e),null}}},rc={NonEligibleNoCookie:"non-eligible-no-refresh-cookie",NonEligibleNonGet:"non-eligible-non-get",InvalidSessionToken:"invalid-session-token",MissingApiClient:"missing-api-client",MissingSessionToken:"missing-session-token",MissingRefreshToken:"missing-refresh-token",ExpiredSessionTokenDecodeFailed:"expired-session-token-decode-failed",ExpiredSessionTokenMissingSidClaim:"expired-session-token-missing-sid-claim",FetchError:"fetch-error",UnexpectedSDKError:"unexpected-sdk-error",UnexpectedBAPIError:"unexpected-bapi-error"};function rd(e,t,r){return tN(e,t)?null:tW({tokenType:e,authenticateContext:r,reason:tB.TokenTypeMismatch})}var ru=async(e,t)=>{let r=await j(tY(e),t);M(r.secretKey);let a=t.acceptsToken??tC.SessionToken;if(r.isSatellite){var o=r.signInUrl,l=r.secretKey;if(!o&&(0,s.Ve)(l))throw Error("Missing signInUrl. Pass a signInUrl for dev instances if an app is satellite");if(r.signInUrl&&r.origin&&function(e,t){let r;try{r=new URL(e)}catch{throw Error("The signInUrl needs to have a absolute url format.")}if(r.origin===t)throw Error("The signInUrl needs to be on a different origin than your satellite application.")}(r.signInUrl,r.origin),!(r.proxyUrl||r.domain))throw Error("Missing domain and proxyUrl. A satellite application needs to specify a domain or a proxyUrl")}let c=new rl(t.organizationSyncOptions),d=new ro(r,{organizationSyncOptions:t.organizationSyncOptions},c);async function u(r){if(!t.apiClient)return{data:null,error:{message:"An apiClient is needed to perform token refresh.",cause:{reason:rc.MissingApiClient}}};let{sessionToken:s,refreshTokenInCookie:n}=r;if(!s)return{data:null,error:{message:"Session token must be provided.",cause:{reason:rc.MissingSessionToken}}};if(!n)return{data:null,error:{message:"Refresh token must be provided.",cause:{reason:rc.MissingRefreshToken}}};let{data:a,errors:o}=(0,i.iU)(s);if(!a||o)return{data:null,error:{message:"Unable to decode the expired session token.",cause:{reason:rc.ExpiredSessionTokenDecodeFailed,errors:o}}};if(!a?.payload?.sid)return{data:null,error:{message:"Expired session token is missing the `sid` claim.",cause:{reason:rc.ExpiredSessionTokenMissingSidClaim}}};try{return{data:(await t.apiClient.sessions.refreshSession(a.payload.sid,{format:"cookie",suffixed_cookies:r.usesSuffixedCookies(),expired_token:s||"",refresh_token:n||"",request_origin:r.clerkUrl.origin,request_headers:Object.fromEntries(Array.from(e.headers.entries()).map(([e,t])=>[e,[t]]))})).cookies,error:null}}catch(e){if(!e?.errors?.length)return{data:null,error:{message:"Unexpected Server/BAPI error",cause:{reason:rc.UnexpectedBAPIError,errors:[e]}}};if("unexpected_error"===e.errors[0].code)return{data:null,error:{message:"Fetch unexpected error",cause:{reason:rc.FetchError,errors:e.errors}}};return{data:null,error:{message:e.errors[0].code,cause:{reason:e.errors[0].code,errors:e.errors}}}}}async function h(e){let{data:t,error:r}=await u(e);if(!t||0===t.length)return{data:null,error:r};let s=new Headers,i="";t.forEach(e=>{s.append("Set-Cookie",e),tZ(e).startsWith(P.Cookies.Session)&&(i=t0(e))});let{data:n,errors:a}=await t7(i,e);return a?{data:null,error:{message:"Clerk: unable to verify refreshed session token.",cause:{reason:rc.InvalidSessionToken,errors:a}}}:{data:{jwtPayload:n,sessionToken:i,headers:s},error:null}}function p(e,t,r,s){if(!d.isRequestEligibleForHandshake())return tW({tokenType:tC.SessionToken,authenticateContext:e,reason:t,message:r});let i=s??d.buildRedirectToHandshake(t);return(i.get(P.Headers.Location)&&i.set(P.Headers.CacheControl,"no-store"),d.checkAndTrackRedirectLoop(i))?(console.log("Clerk: Refreshing the session token resulted in an infinite redirect loop. This usually means that your Clerk instance keys do not match - make sure to copy the correct publishable and secret keys from the Clerk dashboard."),tW({tokenType:tC.SessionToken,authenticateContext:e,reason:t,message:r})):function(e,t,r="",s){return tG({status:tL.Handshake,reason:t,message:r,publishableKey:e.publishableKey||"",isSatellite:e.isSatellite||!1,domain:e.domain||"",proxyUrl:e.proxyUrl||"",signInUrl:e.signInUrl||"",signUpUrl:e.signUpUrl||"",afterSignInUrl:e.afterSignInUrl||"",afterSignUpUrl:e.afterSignUpUrl||"",isSignedIn:!1,isAuthenticated:!1,tokenType:tC.SessionToken,toAuth:()=>null,headers:s,token:null})}(e,t,r,i)}async function f(){let{tokenInHeader:e}=r;try{let{data:t,errors:s}=await t7(e,r);if(s)throw s[0];return t$({tokenType:tC.SessionToken,authenticateContext:r,sessionClaims:t,headers:new Headers,token:e})}catch(e){return g(e,"header")}}async function m(){let e=r.clientUat,t=!!r.sessionTokenInCookie,s=!!r.devBrowserToken;if(r.handshakeNonce||r.handshakeToken)try{return await d.resolveHandshake()}catch(e){e instanceof n.zF&&"development"===r.instanceType?d.handleTokenVerificationErrorInDevelopment(e):console.error("Clerk: unable to resolve handshake:",e)}if("development"===r.instanceType&&r.clerkUrl.searchParams.has(P.QueryParameters.DevBrowser))return p(r,tB.DevBrowserSync,"");let a=r.isSatellite&&"document"===r.secFetchDest;if("production"===r.instanceType&&a)return p(r,tB.SatelliteCookieNeedsSyncing,"");if("development"===r.instanceType&&a&&!r.clerkUrl.searchParams.has(P.QueryParameters.ClerkSynced)){let e=new URL(r.signInUrl);e.searchParams.append(P.QueryParameters.ClerkRedirectUrl,r.clerkUrl.toString());let t=new Headers({[P.Headers.Location]:e.toString()});return p(r,tB.SatelliteCookieNeedsSyncing,"",t)}let o=new URL(r.clerkUrl).searchParams.get(P.QueryParameters.ClerkRedirectUrl);if("development"===r.instanceType&&!r.isSatellite&&o){let e=new URL(o);r.devBrowserToken&&e.searchParams.append(P.QueryParameters.DevBrowser,r.devBrowserToken),e.searchParams.append(P.QueryParameters.ClerkSynced,"true");let t=new Headers({[P.Headers.Location]:e.toString()});return p(r,tB.PrimaryRespondsToSyncing,"",t)}if("development"===r.instanceType&&!s)return p(r,tB.DevBrowserMissing,"");if(!e&&!t)return tW({tokenType:tC.SessionToken,authenticateContext:r,reason:tB.SessionTokenAndUATMissing});if(!e&&t)return p(r,tB.SessionTokenWithoutClientUAT,"");if(e&&!t)return p(r,tB.ClientUATWithoutSessionToken,"");let{data:l,errors:u}=(0,i.iU)(r.sessionTokenInCookie);if(u)return g(u[0],"cookie");if(l.payload.iat<r.clientUat)return p(r,tB.SessionTokenIATBeforeClientUAT,"");try{let{data:e,errors:t}=await t7(r.sessionTokenInCookie,r);if(t)throw t[0];let s=t$({tokenType:tC.SessionToken,authenticateContext:r,sessionClaims:e,headers:new Headers,token:r.sessionTokenInCookie});if(!r.isSatellite&&"document"===r.secFetchDest&&r.isCrossOriginReferrer())return p(r,tB.PrimaryDomainCrossOriginSync,"Cross-origin request from satellite domain requires handshake");let i=s.toAuth();if(i.userId){let e=function(e,t){let r=c.findTarget(e.clerkUrl);if(!r)return null;let s=!1;if("organization"===r.type&&(r.organizationSlug&&r.organizationSlug!==t.orgSlug&&(s=!0),r.organizationId&&r.organizationId!==t.orgId&&(s=!0)),"personalAccount"===r.type&&t.orgId&&(s=!0),!s)return null;if(e.handshakeRedirectLoopCounter>0)return console.warn("Clerk: Organization activation handshake loop detected. This is likely due to an invalid organization ID or slug. Skipping organization activation."),null;let i=p(e,tB.ActiveOrganizationMismatch,"");return"handshake"!==i.status?null:i}(r,i);if(e)return e}return s}catch(e){return g(e,"cookie")}}async function g(t,s){let i;if(!(t instanceof n.zF))return tW({tokenType:tC.SessionToken,authenticateContext:r,reason:tB.UnexpectedError});if(t.reason===n.jn.TokenExpired&&r.refreshTokenInCookie&&"GET"===e.method){let{data:e,error:t}=await h(r);if(e)return t$({tokenType:tC.SessionToken,authenticateContext:r,sessionClaims:e.jwtPayload,headers:e.headers,token:e.sessionToken});i=t?.cause?.reason?t.cause.reason:rc.UnexpectedSDKError}else i="GET"!==e.method?rc.NonEligibleNonGet:r.refreshTokenInCookie?null:rc.NonEligibleNoCookie;return(t.tokenCarrier=s,[n.jn.TokenExpired,n.jn.TokenNotActiveYet,n.jn.TokenIatInTheFuture].includes(t.reason))?p(r,rp({tokenError:t.reason,refreshError:i}),t.getFullMessage()):tW({tokenType:tC.SessionToken,authenticateContext:r,reason:t.reason,message:t.getFullMessage()})}function y(e,t){return t instanceof n.sM?tW({tokenType:e,authenticateContext:r,reason:t.code,message:t.getFullMessage()}):tW({tokenType:e,authenticateContext:r,reason:tB.UnexpectedError})}async function k(){let{tokenInHeader:e}=r;if(!e)return g(Error("Missing token in header"),"header");if(!tP(e))return tW({tokenType:a,authenticateContext:r,reason:tB.TokenTypeMismatch,message:""});let t=rd(tq(e),a,r);if(t)return t;let{data:s,tokenType:i,errors:n}=await ri(e,r);return n?y(i,n[0]):t$({tokenType:i,authenticateContext:r,machineData:s,token:e})}async function _(){let{tokenInHeader:e}=r;if(!e)return g(Error("Missing token in header"),"header");if(tP(e)){let t=rd(tq(e),a,r);if(t)return t;let{data:s,tokenType:i,errors:n}=await ri(e,r);return n?y(i,n[0]):t$({tokenType:i,authenticateContext:r,machineData:s,token:e})}let{data:t,errors:s}=await t7(e,r);return s?g(s[0],"header"):t$({tokenType:tC.SessionToken,authenticateContext:r,sessionClaims:t,token:e})}return Array.isArray(a)&&!function(e,t){let r=null,{tokenInHeader:s}=t;return s&&(r=tP(s)?tq(s):tC.SessionToken),tN(r??tC.SessionToken,e)}(a,r)?function(){let e=tH();return tG({status:tL.SignedOut,reason:tB.TokenTypeMismatch,message:"",proxyUrl:"",publishableKey:"",isSatellite:!1,domain:"",signInUrl:"",signUpUrl:"",afterSignInUrl:"",afterSignUpUrl:"",isSignedIn:!1,isAuthenticated:!1,tokenType:null,toAuth:()=>e,headers:new Headers,token:null})}():r.tokenInHeader?"any"===a?_():a===tC.SessionToken?f():k():a===tC.OAuthToken||a===tC.ApiKey||a===tC.MachineToken?tW({tokenType:a,authenticateContext:r,reason:"No token in header"}):m()},rh=e=>{let{isSignedIn:t,isAuthenticated:r,proxyUrl:s,reason:i,message:n,publishableKey:a,isSatellite:o,domain:l}=e;return{isSignedIn:t,isAuthenticated:r,proxyUrl:s,reason:i,message:n,publishableKey:a,isSatellite:o,domain:l}},rp=({tokenError:e,refreshError:t})=>{switch(e){case n.jn.TokenExpired:return`${tB.SessionTokenExpired}-refresh-${t}`;case n.jn.TokenNotActiveYet:return tB.SessionTokenNBF;case n.jn.TokenIatInTheFuture:return tB.SessionTokenIatInTheFuture;default:return tB.UnexpectedError}},rf={secretKey:"",jwtKey:"",apiUrl:void 0,apiVersion:void 0,proxyUrl:"",publishableKey:"",isSatellite:!1,domain:"",audience:""};function rm(e){let t=z(rf,e.options),r=e.apiClient;return{authenticateRequest:(e,s={})=>{let{apiUrl:i,apiVersion:n}=t,a=z(t,s);return ru(e,{...s,...a,apiUrl:i,apiVersion:n,apiClient:r})},debugRequestState:rh}}},19266:(e,t,r)=>{"use strict";r.r(t),r.d(t,{snakeCase:()=>d});var s=function(){return(s=Object.assign||function(e){for(var t,r=1,s=arguments.length;r<s;r++)for(var i in t=arguments[r])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};Object.create;Object.create;var i="function"==typeof SuppressedError&&SuppressedError,n=function(){return(n=Object.assign||function(e){for(var t,r=1,s=arguments.length;r<s;r++)for(var i in t=arguments[r])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};Object.create;function a(e){return e.toLowerCase()}Object.create,"function"==typeof SuppressedError&&SuppressedError;var o=[/([a-z0-9])([A-Z])/g,/([A-Z])([A-Z][a-z])/g],l=/[^A-Z0-9]+/gi;function c(e,t,r){return t instanceof RegExp?e.replace(t,r):t.reduce(function(e,t){return e.replace(t,r)},e)}function d(e,t){var r;return void 0===t&&(t={}),void 0===(r=s({delimiter:"_"},t))&&(r={}),function(e,t){void 0===t&&(t={});for(var r=t.splitRegexp,s=t.stripRegexp,i=t.transform,n=t.delimiter,d=c(c(e,void 0===r?o:r,"$1\0$2"),void 0===s?l:s,"\0"),u=0,h=d.length;"\0"===d.charAt(u);)u++;for(;"\0"===d.charAt(h-1);)h--;return d.slice(u,h).split("\0").map(void 0===i?a:i).join(void 0===n?" ":n)}(e,n({delimiter:"."},r))}},21404:(e,t,r)=>{"use strict";r.d(t,{l3:()=>k,qf:()=>y,r0:()=>l,iU:()=>x,hJ:()=>f,nk:()=>A,Fh:()=>T,fA:()=>o,J0:()=>E});var s=r(68272),i=r(77598),n=r(73819);r(45663);var a=fetch.bind(globalThis),o={crypto:i.webcrypto,get fetch(){return a},AbortController:globalThis.AbortController,Blob:globalThis.Blob,FormData:globalThis.FormData,Headers:globalThis.Headers,Request:globalThis.Request,Response:globalThis.Response},l={parse:(e,t)=>(function(e,t,r={}){if(!t.codes){t.codes={};for(let e=0;e<t.chars.length;++e)t.codes[t.chars[e]]=e}if(!r.loose&&e.length*t.bits&7)throw SyntaxError("Invalid padding");let s=e.length;for(;"="===e[s-1];)if(--s,!r.loose&&!((e.length-s)*t.bits&7))throw SyntaxError("Invalid padding");let i=new(r.out??Uint8Array)(s*t.bits/8|0),n=0,a=0,o=0;for(let r=0;r<s;++r){let s=t.codes[e[r]];if(void 0===s)throw SyntaxError("Invalid character "+e[r]);a=a<<t.bits|s,(n+=t.bits)>=8&&(n-=8,i[o++]=255&a>>n)}if(n>=t.bits||255&a<<8-n)throw SyntaxError("Unexpected end of data");return i})(e,c,t),stringify:(e,t)=>(function(e,t,r={}){let{pad:s=!0}=r,i=(1<<t.bits)-1,n="",a=0,o=0;for(let r=0;r<e.length;++r)for(o=o<<8|255&e[r],a+=8;a>t.bits;)a-=t.bits,n+=t.chars[i&o>>a];if(a&&(n+=t.chars[i&o<<t.bits-a]),s)for(;n.length*t.bits&7;)n+="=";return n})(e,c,t)},c={chars:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_",bits:6},d={RS256:"SHA-256",RS384:"SHA-384",RS512:"SHA-512"},u="RSASSA-PKCS1-v1_5",h={RS256:u,RS384:u,RS512:u},p=Object.keys(d);function f(e){let t=d[e],r=h[e];if(!t||!r)throw Error(`Unsupported algorithm ${e}, expected one of ${p.join(",")}.`);return{hash:{name:d[e]},name:h[e]}}var m=e=>Array.isArray(e)&&e.length>0&&e.every(e=>"string"==typeof e),g=(e,t)=>{let r=[t].flat().filter(e=>!!e),i=[e].flat().filter(e=>!!e);if(r.length>0&&i.length>0){if("string"==typeof e){if(!r.includes(e))throw new s.zF({action:s.z.EnsureClerkJWT,reason:s.jn.TokenVerificationFailed,message:`Invalid JWT audience claim (aud) ${JSON.stringify(e)}. Is not included in "${JSON.stringify(r)}".`})}else if(m(e)&&!e.some(e=>r.includes(e)))throw new s.zF({action:s.z.EnsureClerkJWT,reason:s.jn.TokenVerificationFailed,message:`Invalid JWT audience claim array (aud) ${JSON.stringify(e)}. Is not included in "${JSON.stringify(r)}".`})}},y=e=>{if(void 0!==e&&"JWT"!==e)throw new s.zF({action:s.z.EnsureClerkJWT,reason:s.jn.TokenInvalid,message:`Invalid JWT type ${JSON.stringify(e)}. Expected "JWT".`})},k=e=>{if(!p.includes(e))throw new s.zF({action:s.z.EnsureClerkJWT,reason:s.jn.TokenInvalidAlgorithm,message:`Invalid JWT algorithm ${JSON.stringify(e)}. Supported: ${p}.`})},_=e=>{if("string"!=typeof e)throw new s.zF({action:s.z.EnsureClerkJWT,reason:s.jn.TokenVerificationFailed,message:`Subject claim (sub) is required and must be a string. Received ${JSON.stringify(e)}.`})},v=(e,t)=>{if(e&&t&&0!==t.length&&!t.includes(e))throw new s.zF({reason:s.jn.TokenInvalidAuthorizedParties,message:`Invalid JWT Authorized party claim (azp) ${JSON.stringify(e)}. Expected "${t}".`})},w=(e,t)=>{if("number"!=typeof e)throw new s.zF({action:s.z.EnsureClerkJWT,reason:s.jn.TokenVerificationFailed,message:`Invalid JWT expiry date claim (exp) ${JSON.stringify(e)}. Expected number.`});let r=new Date(Date.now()),i=new Date(0);if(i.setUTCSeconds(e),i.getTime()<=r.getTime()-t)throw new s.zF({reason:s.jn.TokenExpired,message:`JWT is expired. Expiry date: ${i.toUTCString()}, Current date: ${r.toUTCString()}.`})},S=(e,t)=>{if(void 0===e)return;if("number"!=typeof e)throw new s.zF({action:s.z.EnsureClerkJWT,reason:s.jn.TokenVerificationFailed,message:`Invalid JWT not before date claim (nbf) ${JSON.stringify(e)}. Expected number.`});let r=new Date(Date.now()),i=new Date(0);if(i.setUTCSeconds(e),i.getTime()>r.getTime()+t)throw new s.zF({reason:s.jn.TokenNotActiveYet,message:`JWT cannot be used prior to not before date claim (nbf). Not before date: ${i.toUTCString()}; Current date: ${r.toUTCString()};`})},b=(e,t)=>{if(void 0===e)return;if("number"!=typeof e)throw new s.zF({action:s.z.EnsureClerkJWT,reason:s.jn.TokenVerificationFailed,message:`Invalid JWT issued at date claim (iat) ${JSON.stringify(e)}. Expected number.`});let r=new Date(Date.now()),i=new Date(0);if(i.setUTCSeconds(e),i.getTime()>r.getTime()+t)throw new s.zF({reason:s.jn.TokenIatInTheFuture,message:`JWT issued at date claim (iat) is in the future. Issued at date: ${i.toUTCString()}; Current date: ${r.toUTCString()};`})};function T(e,t,r){if("object"==typeof e)return o.crypto.subtle.importKey("jwk",e,t,!1,[r]);let s=function(e){let t=e.replace(/-----BEGIN.*?-----/g,"").replace(/-----END.*?-----/g,"").replace(/\s/g,""),r=(0,n.y)(t),s=new Uint8Array(new ArrayBuffer(r.length));for(let e=0,t=r.length;e<t;e++)s[e]=r.charCodeAt(e);return s}(e),i="sign"===r?"pkcs8":"spki";return o.crypto.subtle.importKey(i,s,t,!1,[r])}async function A(e,t){let{header:r,signature:i,raw:n}=e,a=new TextEncoder().encode([n.header,n.payload].join(".")),l=f(r.alg);try{let e=await T(t,l,"verify");return{data:await o.crypto.subtle.verify(l.name,e,i,a)}}catch(e){return{errors:[new s.zF({reason:s.jn.TokenInvalidSignature,message:e?.message})]}}}function x(e){let t=(e||"").toString().split(".");if(3!==t.length)return{errors:[new s.zF({reason:s.jn.TokenInvalid,message:"Invalid JWT form. A JWT consists of three parts separated by dots."})]};let[r,i,n]=t,a=new TextDecoder,o=JSON.parse(a.decode(l.parse(r,{loose:!0}))),c=JSON.parse(a.decode(l.parse(i,{loose:!0})));return{data:{header:o,payload:c,signature:l.parse(n,{loose:!0}),raw:{header:r,payload:i,signature:n,text:e}}}}async function E(e,t){let{audience:r,authorizedParties:i,clockSkewInMs:n,key:a}=t,o=n||5e3,{data:l,errors:c}=x(e);if(c)return{errors:c};let{header:d,payload:u}=l;try{let{typ:e,alg:t}=d;y(e),k(t);let{azp:s,sub:n,aud:a,iat:l,exp:c,nbf:h}=u;_(n),g([a],[r]),v(s,i),w(c,o),S(h,o),b(l,o)}catch(e){return{errors:[e]}}let{data:h,errors:p}=await A(l,a);return p?{errors:[new s.zF({action:s.z.EnsureClerkJWT,reason:s.jn.TokenVerificationFailed,message:`Error verifying JWT signature. ${p[0]}`})]}:h?{data:u}:{errors:[new s.zF({reason:s.jn.TokenInvalidSignature,message:"JWT signature is invalid."})]}}},23515:(e,t,r)=>{"use strict";r.d(t,{M:()=>i});var s=r(56380);let i=s.rE.startsWith("13.")||s.rE.startsWith("14.0")},24981:(e,t,r)=>{"use strict";r.d(t,{io:()=>u,sb:()=>h,qS:()=>l.qS,ky:()=>l.ky,Ve:()=>l.mC,q5:()=>l.q5,L5:()=>o}),r(45663);var s={initialDelay:125,maxDelayBetweenRetries:0,factor:2,shouldRetry:(e,t)=>t<5,retryImmediately:!1,jitter:!0},i=async e=>new Promise(t=>setTimeout(t,e)),n=(e,t)=>t?e*(1+Math.random()):e,a=e=>{let t=0,r=()=>{let r=e.initialDelay*Math.pow(e.factor,t);return r=n(r,e.jitter),Math.min(e.maxDelayBetweenRetries||r,r)};return async()=>{await i(r()),t++}},o=async(e,t={})=>{let r=0,{shouldRetry:o,initialDelay:l,maxDelayBetweenRetries:c,factor:d,retryImmediately:u,jitter:h}={...s,...t},p=a({initialDelay:l,maxDelayBetweenRetries:c,factor:d,jitter:h});for(;;)try{return await e()}catch(e){if(!o(e,++r))throw e;u&&1===r?await i(n(100,h)):await p()}},l=r(60181),c=r(69451),d=new Set,u=(e,t,r)=>{let s=(0,c.MC)()||(0,c.Fj)(),i=r??e;d.has(i)||s||(d.add(i),console.warn(`Clerk - DEPRECATION WARNING: "${e}" is deprecated and will be removed in the next major release.
${t}`))},h=(0,r(60864)._r)({packageName:"@clerk/backend"}),{isDevOrStagingUrl:p}=(0,l.RZ)()},28941:(e,t,r)=>{"use strict";r.d(t,{zz:()=>s.zz});var s=r(98950);r(45663)},30693:(e,t,r)=>{"use strict";r.d(t,{j:()=>F});var s=r(61261),i=r(42543),n=r(99657),a=r(28941),o=r(56380);let l=e=>{if(!e||"string"!=typeof e)return e;try{return(e||"").replace(/^(sk_(live|test)_)(.+?)(.{3})$/,"$1*********$4")}catch{return""}},c=e=>(Array.isArray(e)?e:[e]).map(e=>"string"==typeof e?l(e):JSON.stringify(Object.fromEntries(Object.entries(e).map(([e,t])=>[e,l(t)])),null,2)).join(", "),d=(e,t)=>()=>{let r=[],s=!1;return{enable:()=>{s=!0},debug:(...e)=>{s&&r.push(e.map(e=>"function"==typeof e?e():e))},commit:()=>{if(s){var i,n;for(let s of(console.log((i=e,`[clerk debug start: ${i}]`)),r)){let e=t(s);e=e.split("\n").map(e=>`  ${e}`).join("\n"),process.env.VERCEL&&(e=function(e,t){let r=new TextEncoder,s=new TextDecoder("utf-8"),i=r.encode(e).slice(0,4096);return s.decode(i).replace(/\uFFFD/g,"")}(e,4096)),console.log(e)}console.log((n=e,`[clerk debug end: ${n}] (@clerk/nextjs=6.23.3,next=${o.rE},timestamp=${Math.round(new Date().getTime()/1e3)})`))}}}},u=(e,t)=>(...r)=>{let s=("string"==typeof e?d(e,c):e)(),i=t(s);try{let e=i(...r);if("object"==typeof e&&"then"in e&&"function"==typeof e.then)return e.then(e=>(s.commit(),e)).catch(e=>{throw s.commit(),e});return s.commit(),e}catch(e){throw s.commit(),e}};var h=r(23515),p=r(95035),f=r(21404),m=r(68272);function g(e){let t=JSON.stringify(e),r=new TextEncoder().encode(t);return f.r0.stringify(r,{pad:!1})}async function y(e,t,r){if(!r.algorithm)throw Error("No algorithm specified");let s=new TextEncoder,i=(0,f.hJ)(r.algorithm);if(!i)return{errors:[new m.xy(`Unsupported algorithm ${r.algorithm}`)]};let n=await (0,f.Fh)(t,i,"sign"),a=r.header||{typ:"JWT"};a.alg=r.algorithm,e.iat=Math.floor(Date.now()/1e3);let o=g(a),l=g(e),c=`${o}.${l}`;try{let e=await f.fA.crypto.subtle.sign(i,n,s.encode(c));return{data:`${c}.${f.r0.stringify(new Uint8Array(e),{pad:!1})}`}}catch(e){return{errors:[new m.xy(e?.message)]}}}(0,p.C)(f.J0);var k=(0,p.R)(f.iU);(0,p.C)(y),(0,p.C)(f.nk);var _=r(35621),v=r(60751);let w=(e,{treatPendingAsSignedOut:t=!0,...r}={})=>{var i,a;let{authStatus:o,authMessage:l,authReason:c,authToken:d,authSignature:u}=T(e);null==(i=r.logger)||i.debug("headers",{authStatus:o,authMessage:l,authReason:c});let h=(0,_._b)(e,s.AA.Headers.ClerkRequestData),p=(0,v.Kk)(h),f={secretKey:(null==r?void 0:r.secretKey)||p.secretKey||n.rB,publishableKey:p.publishableKey||n.At,apiUrl:n.H$,apiVersion:n.mG,authStatus:o,authMessage:l,authReason:c,treatPendingAsSignedOut:t};if(!(0,s.lv)(s.ks.SessionToken,r.acceptsToken||s.ks.SessionToken))return(0,s.wI)(f);if(o&&o===s.TD.SignedIn){(0,v._l)(d,f.secretKey,u);let e=k(d);return null==(a=r.logger)||a.debug("jwt",e.raw),(0,s.Rn)(e,f)}return(0,s.wI)(f)},S=async(e,t,r)=>{let i=e&&(0,s.KV)(e),n=t===s.ks.SessionToken||Array.isArray(t)&&1===t.length&&t[0]===s.ks.SessionToken;if(i&&!n){let i=(0,s.vb)(e);if(Array.isArray(t)&&!t.includes(i))return(0,s._)();if(!Array.isArray(t)&&"any"!==t&&i!==t){let e=(0,s.xE)(t,r);return(0,s.X9)({authObject:e,acceptsToken:t})}let{data:n,errors:a}=await (0,s.a4)(e,r),o=a?(0,s.xE)(i,r):(0,s.Sp)(i,e,n);return(0,s.X9)({authObject:o,acceptsToken:t})}return null},b=async(e,t={})=>{var r,i;let{authStatus:a,authMessage:o,authReason:l}=T(e);null==(r=t.logger)||r.debug("headers",{authStatus:a,authMessage:o,authReason:l});let c=null==(i=(0,_._b)(e,s.AA.Headers.Authorization))?void 0:i.replace("Bearer ",""),d=t.acceptsToken||s.ks.SessionToken,u={secretKey:(null==t?void 0:t.secretKey)||n.rB,publishableKey:n.At,apiUrl:n.H$,authStatus:a,authMessage:o,authReason:l},h=await S(c,d,u);if(h)return h;if(c&&Array.isArray(d)&&!d.includes(s.ks.SessionToken))return(0,s._)();let p=w(e,t);return(0,s.X9)({authObject:p,acceptsToken:d})},T=e=>{let t=(0,_.NE)(e,"AuthStatus"),r=(0,_.NE)(e,"AuthToken"),s=(0,_.NE)(e,"AuthMessage");return{authStatus:t,authToken:r,authMessage:s,authReason:(0,_.NE)(e,"AuthReason"),authSignature:(0,_.NE)(e,"AuthSignature")}};var A=r(75360);let x=({debugLoggerName:e,noAuthStatusMessage:t})=>u(e,e=>async(i,n)=>{if((0,a.zz)((0,_._b)(i,s.AA.Headers.EnableDebug))&&e.enable(),!(0,_.Zd)(i)){h.M&&(0,v.$K)(i,t);let e=await r.e(690).then(r.bind(r,60690)).then(e=>e.suggestMiddlewareLocation()).catch(()=>void 0);if(e)throw Error(e);(0,v.$K)(i,t)}return((t,r={})=>b(t,{...r,logger:e,acceptsToken:null==r?void 0:r.acceptsToken}))(i,{...n,logger:e,acceptsToken:null==n?void 0:n.acceptsToken})});(({debugLoggerName:e,noAuthStatusMessage:t})=>u(e,e=>(r,i)=>((0,a.zz)((0,_._b)(r,s.AA.Headers.EnableDebug))&&e.enable(),(0,v.$K)(r,t),((t,r={})=>w(t,{...r,logger:e,acceptsToken:null==r?void 0:r.acceptsToken}))(r,{...i,logger:e,acceptsToken:null==i?void 0:i.acceptsToken}))))({debugLoggerName:"getAuth()",noAuthStatusMessage:(0,A.AG)()});let E={REDIRECT_TO_URL:"CLERK_PROTECT_REDIRECT_TO_URL"},I={NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401},C=new Set(Object.values(I)),O="NEXT_HTTP_ERROR_FALLBACK",U="NEXT_REDIRECT";function P(){let e=Error(O);throw e.digest=`${O};${I.UNAUTHORIZED}`,e}let q={Headers:{NextRewrite:"x-middleware-rewrite",NextResume:"x-middleware-next",NextRedirect:"Location",NextUrl:"next-url",NextAction:"next-action",NextjsData:"x-nextjs-data"}},N=e=>{if(e&&!e.unauthenticatedUrl&&!e.unauthorizedUrl&&!e.token&&(1!==Object.keys(e).length||!("token"in e)))return e},R=e=>{var t,r;return!!e.headers.get(q.Headers.NextUrl)&&((null==(t=e.headers.get(s.AA.Headers.Accept))?void 0:t.includes("text/x-component"))||(null==(r=e.headers.get(s.AA.Headers.ContentType))?void 0:r.includes("multipart/form-data"))||!!e.headers.get(q.Headers.NextAction))},z=e=>{var t;return"document"===e.headers.get(s.AA.Headers.SecFetchDest)||"iframe"===e.headers.get(s.AA.Headers.SecFetchDest)||(null==(t=e.headers.get(s.AA.Headers.Accept))?void 0:t.includes("text/html"))||M(e)||j(e)},M=e=>!!e.headers.get(q.Headers.NextUrl)&&!R(e)||J(),J=()=>{let e=globalThis.fetch;if(!function(e){return"__nextPatched"in e&&!0===e.__nextPatched}(e))return!1;let{page:t,pagePath:r}=e.__nextGetStaticStore().getStore()||{};return!!(r||t)},j=e=>!!e.headers.get(q.Headers.NextjsData);var H=r(38202);let F=async e=>{var t;r(53499);let a=await (0,H.TG)(),o=async()=>{if(h.M)return[];try{let e=await r.e(690).then(r.bind(r,60690)).then(e=>e.hasSrcAppDir());return[`Your Middleware exists at ./${e?"src/":""}middleware.(ts|js)`]}catch{return[]}},l=await x({debugLoggerName:"auth()",noAuthStatusMessage:(0,A.sd)("auth",await o())})(a,{treatPendingAsSignedOut:null==e?void 0:e.treatPendingAsSignedOut,acceptsToken:null!=(t=null==e?void 0:e.acceptsToken)?t:s.ks.SessionToken}),c=(0,_.NE)(a,"ClerkUrl"),d=(...e)=>{let{returnBackUrl:t}=e[0]||{},r=(0,s.tl)(a),o=r.clerkUrl.searchParams.get(s.AA.QueryParameters.DevBrowser)||r.cookies.get(s.AA.Cookies.DevBrowser),d=(0,_._b)(a,s.AA.Headers.ClerkRequestData),u=(0,v.Kk)(d);return[(0,s.vH)({redirectAdapter:i.redirect,devBrowserToken:o,baseUrl:r.clerkUrl.toString(),publishableKey:u.publishableKey||n.At,signInUrl:u.signInUrl||n.qW,signUpUrl:u.signUpUrl||n.sE,sessionStatus:l.tokenType===s.ks.SessionToken?l.sessionStatus:null}),null===t?"":t||(null==c?void 0:c.toString())]};return Object.assign(l,{redirectToSignIn:(e={})=>{let[t,r]=d(e);return t.redirectToSignIn({returnBackUrl:r})},redirectToSignUp:(e={})=>{let[t,r]=d(e);return t.redirectToSignUp({returnBackUrl:r})}})};F.protect=async(...e)=>{var t,n;r(53499);let a=await (0,H.TG)(),o=(null==(t=null==e?void 0:e[0])?void 0:t.token)||(null==(n=null==e?void 0:e[1])?void 0:n.token)||s.ks.SessionToken,l=await F({acceptsToken:o});return(function(e){let{redirectToSignIn:t,authObject:r,redirect:i,notFound:n,request:a,unauthorized:o}=e;return async(...e)=>{var l,c,d,u,h,p;let f=N(e[0]),m=(null==(l=e[0])?void 0:l.unauthenticatedUrl)||(null==(c=e[1])?void 0:c.unauthenticatedUrl),g=(null==(d=e[0])?void 0:d.unauthorizedUrl)||(null==(u=e[1])?void 0:u.unauthorizedUrl),y=(null==(h=e[0])?void 0:h.token)||(null==(p=e[1])?void 0:p.token)||s.ks.SessionToken,k=()=>r.tokenType!==s.ks.SessionToken?o():g?i(g):n();return(0,s.lv)(r.tokenType,y)?r.tokenType!==s.ks.SessionToken?r.isAuthenticated?r:k():"pending"!==r.sessionStatus&&r.userId?f?"function"==typeof f?f(r.has)?r:k():r.has(f)?r:k():r:m?i(m):z(a)?t():n():k()}})({request:a,authObject:l,redirectToSignIn:l.redirectToSignIn,notFound:i.notFound,redirect:i.redirect,unauthorized:P})(...e)}},35009:(e,t,r)=>{"use strict";r.d(t,{FW:()=>c,HG:()=>l,Vc:()=>o,gE:()=>i,iM:()=>s,mG:()=>n,ub:()=>a});var s=[".lcl.dev",".lclstage.dev",".lclclerk.com"],i=[".lcl.dev",".stg.dev",".lclstage.dev",".stgstage.dev",".dev.lclclerk.com",".stg.lclclerk.com",".accounts.lclclerk.com","accountsstage.dev","accounts.dev"],n=[".lcl.dev","lclstage.dev",".lclclerk.com",".accounts.lclclerk.com"],a=[".accountsstage.dev"],o="https://api.lclclerk.com",l="https://api.clerkstage.dev",c="https://api.clerk.com"},35621:(e,t,r)=>{"use strict";r.d(t,{NE:()=>i,Zd:()=>a,_b:()=>n});var s=r(61261);function i(e,t){var r;return((r=s.AA.Attributes[t])in e?e[r]:void 0)||n(e,s.AA.Headers[t])}function n(e,t){var r,s;return function(e){try{let{headers:t,nextUrl:r,cookies:s}=e||{};return"function"==typeof(null==t?void 0:t.get)&&"function"==typeof(null==r?void 0:r.searchParams.get)&&"function"==typeof(null==s?void 0:s.get)}catch{return!1}}(e)||function(e){try{let{headers:t}=e||{};return"function"==typeof(null==t?void 0:t.get)}catch{return!1}}(e)?e.headers.get(t):e.headers[t]||e.headers[t.toLowerCase()]||(null==(s=null==(r=e.socket)?void 0:r._httpMessage)?void 0:s.getHeader(t))}function a(e){return!!i(e,"AuthStatus")}},38202:(e,t,r)=>{"use strict";r.d(t,{Sz:()=>i,TG:()=>n});var s=r(4235);let i=e=>{if(!(e instanceof Error)||!("message"in e))return!1;let{message:t}=e,r=t.toLowerCase(),s=r.includes("dynamic server usage"),i=r.includes("this page needs to bail out of prerendering");return/Route .*? needs to bail out of prerendering at this point because it used .*?./.test(t)||s||i};async function n(){try{let{headers:e}=await r.e(5208).then(r.bind(r,65208)),t=await e();return new s.NextRequest("https://placeholder.com",{headers:t})}catch(e){if(e&&i(e))throw e;throw Error(`Clerk: auth(), currentUser() and clerkClient(), are only supported in App Router (/app directory).
If you're using /pages, try getAuth() instead.
Original error: ${e}`)}}},44468:(e,t,r)=>{"use strict";r.d(t,{RZ:()=>c,qS:()=>u,ky:()=>h,mC:()=>d,q5:()=>o});var s=r(73819),i=e=>"undefined"!=typeof btoa&&"function"==typeof btoa?btoa(e):"undefined"!=typeof global&&global.Buffer?new global.Buffer(e).toString("base64"):e,n=r(35009),a="pk_live_";function o(e,t={}){if(!(e=e||"")||!l(e)){if(t.fatal&&!e)throw Error("Publishable key is missing. Ensure that your publishable key is correctly configured. Double-check your environment configuration for your keys, or access them here: https://dashboard.clerk.com/last-active?path=api-keys");if(t.fatal&&!l(e))throw Error("Publishable key not valid.");return null}let r=e.startsWith(a)?"production":"development",i=(0,s.y)(e.split("_")[2]);return i=i.slice(0,-1),t.proxyUrl?i=t.proxyUrl:"development"!==r&&t.domain&&t.isSatellite&&(i=`clerk.${t.domain}`),{instanceType:r,frontendApi:i}}function l(e=""){try{let t=e.startsWith(a)||e.startsWith("pk_test_"),r=(0,s.y)(e.split("_")[2]||"").endsWith("$");return t&&r}catch{return!1}}function c(){let e=new Map;return{isDevOrStagingUrl:t=>{if(!t)return!1;let r="string"==typeof t?t:t.hostname,s=e.get(r);return void 0===s&&(s=n.gE.some(e=>r.endsWith(e)),e.set(r,s)),s}}}function d(e){return e.startsWith("test_")||e.startsWith("sk_test_")}async function u(e,t=globalThis.crypto.subtle){let r=new TextEncoder().encode(e);return i(String.fromCharCode(...new Uint8Array(await t.digest("sha-1",r)))).replace(/\+/gi,"-").replace(/\//gi,"_").substring(0,8)}var h=(e,t)=>`${e}_${t}`},45663:(e,t,r)=>{"use strict";r.d(t,{OV:()=>u,S7:()=>c,VK:()=>d,jq:()=>h});var s=Object.defineProperty,i=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,o=e=>{throw TypeError(e)},l=(e,t,r)=>t.has(e)||o("Cannot "+r),c=(e,t,r)=>(l(e,t,"read from private field"),r?r.call(e):t.get(e)),d=(e,t,r)=>t.has(e)?o("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),u=(e,t,r,s)=>(l(e,t,"write to private field"),s?s.call(e,r):t.set(e,r),r),h=(e,t,r)=>(l(e,t,"access private method"),r)},48106:(e,t,r)=>{"use strict";e.exports=r(46871)},49480:(e,t)=>{"use strict";t.qg=function(e,t){let a=new r,o=e.length;if(o<2)return a;let l=t?.decode||n,c=0;do{let t=e.indexOf("=",c);if(-1===t)break;let r=e.indexOf(";",c),n=-1===r?o:r;if(t>n){c=e.lastIndexOf(";",t-1)+1;continue}let d=s(e,c,t),u=i(e,t,d),h=e.slice(d,u);if(void 0===a[h]){let r=s(e,t+1,n),o=i(e,n,r),c=l(e.slice(r,o));a[h]=c}c=n+1}while(c<o);return a},Object.prototype.toString;let r=(()=>{let e=function(){};return e.prototype=Object.create(null),e})();function s(e,t,r){do{let r=e.charCodeAt(t);if(32!==r&&9!==r)return t}while(++t<r);return r}function i(e,t,r){for(;t>r;){let r=e.charCodeAt(--t);if(32!==r&&9!==r)return t+1}return r}function n(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}},53499:()=>{},56380:e=>{"use strict";e.exports=JSON.parse('{"rE":"15.3.0-canary.31"}')},60181:(e,t,r)=>{"use strict";r.d(t,{RZ:()=>s.RZ,ky:()=>s.ky,mC:()=>s.mC,q5:()=>s.q5,qS:()=>s.qS});var s=r(44468);r(45663)},60751:(e,t,r)=>{"use strict";r.d(t,{$K:()=>eu,_l:()=>eh,Kk:()=>ef}),r(61261),r(60181),r(45663);var s=r(69451);r(4235);var i=r(99657);let n=!r(23515).M&&(0,s.b_)()&&!i.ev;var a,o,l,c,d,u,h,p=Object.defineProperty,f=(e,t,r)=>t in e?p(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,m=(null==(a="undefined"!=typeof globalThis?globalThis:void 0)?void 0:a.crypto)||(null==(o="undefined"!=typeof global?global:void 0)?void 0:o.crypto)||(null==(l="undefined"!=typeof window?window:void 0)?void 0:l.crypto)||(null==(c="undefined"!=typeof self?self:void 0)?void 0:c.crypto)||(null==(u=null==(d="undefined"!=typeof frames?frames:void 0)?void 0:d[0])?void 0:u.crypto);h=m?e=>{let t=[];for(let r=0;r<e;r+=4)t.push(m.getRandomValues(new Uint32Array(1))[0]);return new y(t,e)}:e=>{let t=[],r=e=>{let t=e,r=0x3ade68b1;return()=>{let e=((r=36969*(65535&r)+(r>>16)&0xffffffff)<<16)+(t=18e3*(65535&t)+(t>>16)&0xffffffff)&0xffffffff;return e/=0x100000000,(e+=.5)*(Math.random()>.5?1:-1)}};for(let s=0,i;s<e;s+=4){let e=r(0x100000000*(i||Math.random()));i=0x3ade67b7*e(),t.push(0x100000000*e()|0)}return new y(t,e)};var g=class{static create(...e){return new this(...e)}mixIn(e){return Object.assign(this,e)}clone(){let e=new this.constructor;return Object.assign(e,this),e}},y=class extends g{constructor(e=[],t=4*e.length){super();let r=e;if(r instanceof ArrayBuffer&&(r=new Uint8Array(r)),(r instanceof Int8Array||r instanceof Uint8ClampedArray||r instanceof Int16Array||r instanceof Uint16Array||r instanceof Int32Array||r instanceof Uint32Array||r instanceof Float32Array||r instanceof Float64Array)&&(r=new Uint8Array(r.buffer,r.byteOffset,r.byteLength)),r instanceof Uint8Array){let e=r.byteLength,t=[];for(let s=0;s<e;s+=1)t[s>>>2]|=r[s]<<24-s%4*8;this.words=t,this.sigBytes=e}else this.words=e,this.sigBytes=t}toString(e=k){return e.stringify(this)}concat(e){let t=this.words,r=e.words,s=this.sigBytes,i=e.sigBytes;if(this.clamp(),s%4)for(let e=0;e<i;e+=1){let i=r[e>>>2]>>>24-e%4*8&255;t[s+e>>>2]|=i<<24-(s+e)%4*8}else for(let e=0;e<i;e+=4)t[s+e>>>2]=r[e>>>2];return this.sigBytes+=i,this}clamp(){let{words:e,sigBytes:t}=this;e[t>>>2]&=0xffffffff<<32-t%4*8,e.length=Math.ceil(t/4)}clone(){let e=super.clone.call(this);return e.words=this.words.slice(0),e}};((e,t,r)=>f(e,"symbol"!=typeof t?t+"":t,r))(y,"random",h);var k={stringify(e){let{words:t,sigBytes:r}=e,s=[];for(let e=0;e<r;e+=1){let r=t[e>>>2]>>>24-e%4*8&255;s.push((r>>>4).toString(16)),s.push((15&r).toString(16))}return s.join("")},parse(e){let t=e.length,r=[];for(let s=0;s<t;s+=2)r[s>>>3]|=parseInt(e.substr(s,2),16)<<24-s%8*4;return new y(r,t/2)}},_={stringify(e){let{words:t,sigBytes:r}=e,s=[];for(let e=0;e<r;e+=1){let r=t[e>>>2]>>>24-e%4*8&255;s.push(String.fromCharCode(r))}return s.join("")},parse(e){let t=e.length,r=[];for(let s=0;s<t;s+=1)r[s>>>2]|=(255&e.charCodeAt(s))<<24-s%4*8;return new y(r,t)}},v={stringify(e){try{return decodeURIComponent(escape(_.stringify(e)))}catch{throw Error("Malformed UTF-8 data")}},parse:e=>_.parse(unescape(encodeURIComponent(e)))},w=class extends g{constructor(){super(),this._minBufferSize=0}reset(){this._data=new y,this._nDataBytes=0}_append(e){let t=e;"string"==typeof t&&(t=v.parse(t)),this._data.concat(t),this._nDataBytes+=t.sigBytes}_process(e){let t,{_data:r,blockSize:s}=this,i=r.words,n=r.sigBytes,a=n/(4*s),o=(a=e?Math.ceil(a):Math.max((0|a)-this._minBufferSize,0))*s,l=Math.min(4*o,n);if(o){for(let e=0;e<o;e+=s)this._doProcessBlock(i,e);t=i.splice(0,o),r.sigBytes-=l}return new y(t,l)}clone(){let e=super.clone.call(this);return e._data=this._data.clone(),e}},S=class extends w{constructor(e){super(),this.blockSize=16,this.cfg=Object.assign(new g,e),this.reset()}static _createHelper(e){return(t,r)=>new e(r).finalize(t)}static _createHmacHelper(e){return(t,r)=>new b(e,r).finalize(t)}reset(){super.reset.call(this),this._doReset()}update(e){return this._append(e),this._process(),this}finalize(e){return e&&this._append(e),this._doFinalize()}},b=class extends g{constructor(e,t){super();let r=new e;this._hasher=r;let s=t;"string"==typeof s&&(s=v.parse(s));let i=r.blockSize,n=4*i;s.sigBytes>n&&(s=r.finalize(t)),s.clamp();let a=s.clone();this._oKey=a;let o=s.clone();this._iKey=o;let l=a.words,c=o.words;for(let e=0;e<i;e+=1)l[e]^=0x5c5c5c5c,c[e]^=0x36363636;a.sigBytes=n,o.sigBytes=n,this.reset()}reset(){let e=this._hasher;e.reset(),e.update(this._iKey)}update(e){return this._hasher.update(e),this}finalize(e){let t=this._hasher,r=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(r))}},T=(e,t,r)=>{let s=[],i=0;for(let n=0;n<t;n+=1)if(n%4){let t=r[e.charCodeAt(n-1)]<<n%4*2|r[e.charCodeAt(n)]>>>6-n%4*2;s[i>>>2]|=t<<24-i%4*8,i+=1}return y.create(s,i)},A={stringify(e){let{words:t,sigBytes:r}=e,s=this._map;e.clamp();let i=[];for(let e=0;e<r;e+=3){let n=(t[e>>>2]>>>24-e%4*8&255)<<16|(t[e+1>>>2]>>>24-(e+1)%4*8&255)<<8|t[e+2>>>2]>>>24-(e+2)%4*8&255;for(let t=0;t<4&&e+.75*t<r;t+=1)i.push(s.charAt(n>>>6*(3-t)&63))}let n=s.charAt(64);if(n)for(;i.length%4;)i.push(n);return i.join("")},parse(e){let t=e.length,r=this._map,s=this._reverseMap;if(!s){this._reverseMap=[],s=this._reverseMap;for(let e=0;e<r.length;e+=1)s[r.charCodeAt(e)]=e}let i=r.charAt(64);if(i){let r=e.indexOf(i);-1!==r&&(t=r)}return T(e,t,s)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="},x=[];for(let e=0;e<64;e+=1)x[e]=0x100000000*Math.abs(Math.sin(e+1))|0;var E=(e,t,r,s,i,n,a)=>{let o=e+(t&r|~t&s)+i+a;return(o<<n|o>>>32-n)+t},I=(e,t,r,s,i,n,a)=>{let o=e+(t&s|r&~s)+i+a;return(o<<n|o>>>32-n)+t},C=(e,t,r,s,i,n,a)=>{let o=e+(t^r^s)+i+a;return(o<<n|o>>>32-n)+t},O=(e,t,r,s,i,n,a)=>{let o=e+(r^(t|~s))+i+a;return(o<<n|o>>>32-n)+t},U=class extends S{_doReset(){this._hash=new y([0x67452301,0xefcdab89,0x98badcfe,0x10325476])}_doProcessBlock(e,t){for(let r=0;r<16;r+=1){let s=t+r,i=e[s];e[s]=(i<<8|i>>>24)&0xff00ff|(i<<24|i>>>8)&0xff00ff00}let r=this._hash.words,s=e[t+0],i=e[t+1],n=e[t+2],a=e[t+3],o=e[t+4],l=e[t+5],c=e[t+6],d=e[t+7],u=e[t+8],h=e[t+9],p=e[t+10],f=e[t+11],m=e[t+12],g=e[t+13],y=e[t+14],k=e[t+15],_=r[0],v=r[1],w=r[2],S=r[3];_=E(_,v,w,S,s,7,x[0]),S=E(S,_,v,w,i,12,x[1]),w=E(w,S,_,v,n,17,x[2]),v=E(v,w,S,_,a,22,x[3]),_=E(_,v,w,S,o,7,x[4]),S=E(S,_,v,w,l,12,x[5]),w=E(w,S,_,v,c,17,x[6]),v=E(v,w,S,_,d,22,x[7]),_=E(_,v,w,S,u,7,x[8]),S=E(S,_,v,w,h,12,x[9]),w=E(w,S,_,v,p,17,x[10]),v=E(v,w,S,_,f,22,x[11]),_=E(_,v,w,S,m,7,x[12]),S=E(S,_,v,w,g,12,x[13]),w=E(w,S,_,v,y,17,x[14]),v=E(v,w,S,_,k,22,x[15]),_=I(_,v,w,S,i,5,x[16]),S=I(S,_,v,w,c,9,x[17]),w=I(w,S,_,v,f,14,x[18]),v=I(v,w,S,_,s,20,x[19]),_=I(_,v,w,S,l,5,x[20]),S=I(S,_,v,w,p,9,x[21]),w=I(w,S,_,v,k,14,x[22]),v=I(v,w,S,_,o,20,x[23]),_=I(_,v,w,S,h,5,x[24]),S=I(S,_,v,w,y,9,x[25]),w=I(w,S,_,v,a,14,x[26]),v=I(v,w,S,_,u,20,x[27]),_=I(_,v,w,S,g,5,x[28]),S=I(S,_,v,w,n,9,x[29]),w=I(w,S,_,v,d,14,x[30]),v=I(v,w,S,_,m,20,x[31]),_=C(_,v,w,S,l,4,x[32]),S=C(S,_,v,w,u,11,x[33]),w=C(w,S,_,v,f,16,x[34]),v=C(v,w,S,_,y,23,x[35]),_=C(_,v,w,S,i,4,x[36]),S=C(S,_,v,w,o,11,x[37]),w=C(w,S,_,v,d,16,x[38]),v=C(v,w,S,_,p,23,x[39]),_=C(_,v,w,S,g,4,x[40]),S=C(S,_,v,w,s,11,x[41]),w=C(w,S,_,v,a,16,x[42]),v=C(v,w,S,_,c,23,x[43]),_=C(_,v,w,S,h,4,x[44]),S=C(S,_,v,w,m,11,x[45]),w=C(w,S,_,v,k,16,x[46]),v=C(v,w,S,_,n,23,x[47]),_=O(_,v,w,S,s,6,x[48]),S=O(S,_,v,w,d,10,x[49]),w=O(w,S,_,v,y,15,x[50]),v=O(v,w,S,_,l,21,x[51]),_=O(_,v,w,S,m,6,x[52]),S=O(S,_,v,w,a,10,x[53]),w=O(w,S,_,v,p,15,x[54]),v=O(v,w,S,_,i,21,x[55]),_=O(_,v,w,S,u,6,x[56]),S=O(S,_,v,w,k,10,x[57]),w=O(w,S,_,v,c,15,x[58]),v=O(v,w,S,_,g,21,x[59]),_=O(_,v,w,S,o,6,x[60]),S=O(S,_,v,w,f,10,x[61]),w=O(w,S,_,v,n,15,x[62]),v=O(v,w,S,_,h,21,x[63]),r[0]=r[0]+_|0,r[1]=r[1]+v|0,r[2]=r[2]+w|0,r[3]=r[3]+S|0}_doFinalize(){let e=this._data,t=e.words,r=8*this._nDataBytes,s=8*e.sigBytes;t[s>>>5]|=128<<24-s%32;let i=Math.floor(r/0x100000000);t[(s+64>>>9<<4)+15]=(i<<8|i>>>24)&0xff00ff|(i<<24|i>>>8)&0xff00ff00,t[(s+64>>>9<<4)+14]=(r<<8|r>>>24)&0xff00ff|(r<<24|r>>>8)&0xff00ff00,e.sigBytes=(t.length+1)*4,this._process();let n=this._hash,a=n.words;for(let e=0;e<4;e+=1){let t=a[e];a[e]=(t<<8|t>>>24)&0xff00ff|(t<<24|t>>>8)&0xff00ff00}return n}clone(){let e=super.clone.call(this);return e._hash=this._hash.clone(),e}};S._createHelper(U),S._createHmacHelper(U);var P=class extends g{constructor(e){super(),this.cfg=Object.assign(new g,{keySize:4,hasher:U,iterations:1},e)}compute(e,t){let r,{cfg:s}=this,i=s.hasher.create(),n=y.create(),a=n.words,{keySize:o,iterations:l}=s;for(;a.length<o;){r&&i.update(r),r=i.update(e).finalize(t),i.reset();for(let e=1;e<l;e+=1)r=i.finalize(r),i.reset();n.concat(r)}return n.sigBytes=4*o,n}},q=class extends w{constructor(e,t,r){super(),this.cfg=Object.assign(new g,r),this._xformMode=e,this._key=t,this.reset()}static createEncryptor(e,t){return this.create(this._ENC_XFORM_MODE,e,t)}static createDecryptor(e,t){return this.create(this._DEC_XFORM_MODE,e,t)}static _createHelper(e){let t=e=>"string"==typeof e?F:H;return{encrypt:(r,s,i)=>t(s).encrypt(e,r,s,i),decrypt:(r,s,i)=>t(s).decrypt(e,r,s,i)}}reset(){super.reset.call(this),this._doReset()}process(e){return this._append(e),this._process()}finalize(e){return e&&this._append(e),this._doFinalize()}};q._ENC_XFORM_MODE=1,q._DEC_XFORM_MODE=2,q.keySize=4,q.ivSize=4;var N=class extends g{constructor(e,t){super(),this._cipher=e,this._iv=t}static createEncryptor(e,t){return this.Encryptor.create(e,t)}static createDecryptor(e,t){return this.Decryptor.create(e,t)}};function R(e,t,r){let s,i=this._iv;i?(s=i,this._iv=void 0):s=this._prevBlock;for(let i=0;i<r;i+=1)e[t+i]^=s[i]}var z=class extends N{};z.Encryptor=class extends z{processBlock(e,t){let r=this._cipher,{blockSize:s}=r;R.call(this,e,t,s),r.encryptBlock(e,t),this._prevBlock=e.slice(t,t+s)}},z.Decryptor=class extends z{processBlock(e,t){let r=this._cipher,{blockSize:s}=r,i=e.slice(t,t+s);r.decryptBlock(e,t),R.call(this,e,t,s),this._prevBlock=i}};var M={pad(e,t){let r=4*t,s=r-e.sigBytes%r,i=s<<24|s<<16|s<<8|s,n=[];for(let e=0;e<s;e+=4)n.push(i);let a=y.create(n,s);e.concat(a)},unpad(e){let t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},J=class extends q{constructor(e,t,r){super(e,t,Object.assign({mode:z,padding:M},r)),this.blockSize=4}reset(){let e;super.reset.call(this);let{cfg:t}=this,{iv:r,mode:s}=t;this._xformMode===this.constructor._ENC_XFORM_MODE?e=s.createEncryptor:(e=s.createDecryptor,this._minBufferSize=1),this._mode=e.call(s,this,r&&r.words),this._mode.__creator=e}_doProcessBlock(e,t){this._mode.processBlock(e,t)}_doFinalize(){let e,{padding:t}=this.cfg;return this._xformMode===this.constructor._ENC_XFORM_MODE?(t.pad(this._data,this.blockSize),e=this._process(!0)):(e=this._process(!0),t.unpad(e)),e}},j=class extends g{constructor(e){super(),this.mixIn(e)}toString(e){return(e||this.formatter).stringify(this)}},H=class extends g{static encrypt(e,t,r,s){let i=Object.assign(new g,this.cfg,s),n=e.createEncryptor(r,i),a=n.finalize(t),o=n.cfg;return j.create({ciphertext:a,key:r,iv:o.iv,algorithm:e,mode:o.mode,padding:o.padding,blockSize:n.blockSize,formatter:i.format})}static decrypt(e,t,r,s){let i=t,n=Object.assign(new g,this.cfg,s);return i=this._parse(i,n.format),e.createDecryptor(r,n).finalize(i.ciphertext)}static _parse(e,t){return"string"==typeof e?t.parse(e,this):e}};H.cfg=Object.assign(new g,{format:{stringify(e){let t,{ciphertext:r,salt:s}=e;return(s?y.create([0x53616c74,0x65645f5f]).concat(s).concat(r):r).toString(A)},parse(e){let t,r=A.parse(e),s=r.words;return 0x53616c74===s[0]&&0x65645f5f===s[1]&&(t=y.create(s.slice(2,4)),s.splice(0,4),r.sigBytes-=16),j.create({ciphertext:r,salt:t})}}});var F=class extends H{static encrypt(e,t,r,s){let i=Object.assign(new g,this.cfg,s),n=i.kdf.execute(r,e.keySize,e.ivSize,i.salt,i.hasher);i.iv=n.iv;let a=H.encrypt.call(this,e,t,n.key,i);return a.mixIn(n),a}static decrypt(e,t,r,s){let i=t,n=Object.assign(new g,this.cfg,s);i=this._parse(i,n.format);let a=n.kdf.execute(r,e.keySize,e.ivSize,i.salt,n.hasher);return n.iv=a.iv,H.decrypt.call(this,e,i,a.key,n)}};F.cfg=Object.assign(H.cfg,{kdf:{execute(e,t,r,s,i){let n,a=s;a||(a=y.random(8)),n=i?P.create({keySize:t+r,hasher:i}).compute(e,a):P.create({keySize:t+r}).compute(e,a);let o=y.create(n.words.slice(t),4*r);return n.sigBytes=4*t,j.create({key:n,iv:o,salt:a})}}});var D=[],K=[],L=[],B=[],$=[],W=[],G=[],V=[],Q=[],X=[],Y=[];for(let e=0;e<256;e+=1)e<128?Y[e]=e<<1:Y[e]=e<<1^283;var Z=0,ee=0;for(let e=0;e<256;e+=1){let e=ee^ee<<1^ee<<2^ee<<3^ee<<4;e=e>>>8^255&e^99,D[Z]=e,K[e]=Z;let t=Y[Z],r=Y[t],s=Y[r],i=257*Y[e]^0x1010100*e;L[Z]=i<<24|i>>>8,B[Z]=i<<16|i>>>16,$[Z]=i<<8|i>>>24,W[Z]=i,i=0x1010101*s^65537*r^257*t^0x1010100*Z,G[e]=i<<24|i>>>8,V[e]=i<<16|i>>>16,Q[e]=i<<8|i>>>24,X[e]=i,Z?(Z=t^Y[Y[Y[s^t]]],ee^=Y[Y[ee]]):Z=ee=1}var et=[0,1,2,4,8,16,32,64,128,27,54],er=class extends J{_doReset(){let e;if(this._nRounds&&this._keyPriorReset===this._key)return;this._keyPriorReset=this._key;let t=this._keyPriorReset,r=t.words,s=t.sigBytes/4;this._nRounds=s+6;let i=(this._nRounds+1)*4;this._keySchedule=[];let n=this._keySchedule;for(let t=0;t<i;t+=1)t<s?n[t]=r[t]:(e=n[t-1],t%s?s>6&&t%s==4&&(e=D[e>>>24]<<24|D[e>>>16&255]<<16|D[e>>>8&255]<<8|D[255&e]):e=(D[(e=e<<8|e>>>24)>>>24]<<24|D[e>>>16&255]<<16|D[e>>>8&255]<<8|D[255&e])^et[t/s|0]<<24,n[t]=n[t-s]^e);this._invKeySchedule=[];let a=this._invKeySchedule;for(let t=0;t<i;t+=1){let r=i-t;e=t%4?n[r]:n[r-4],t<4||r<=4?a[t]=e:a[t]=G[D[e>>>24]]^V[D[e>>>16&255]]^Q[D[e>>>8&255]]^X[D[255&e]]}}encryptBlock(e,t){this._doCryptBlock(e,t,this._keySchedule,L,B,$,W,D)}decryptBlock(e,t){let r=e[t+1];e[t+1]=e[t+3],e[t+3]=r,this._doCryptBlock(e,t,this._invKeySchedule,G,V,Q,X,K),r=e[t+1],e[t+1]=e[t+3],e[t+3]=r}_doCryptBlock(e,t,r,s,i,n,a,o){let l=this._nRounds,c=e[t]^r[0],d=e[t+1]^r[1],u=e[t+2]^r[2],h=e[t+3]^r[3],p=4;for(let e=1;e<l;e+=1){let e=s[c>>>24]^i[d>>>16&255]^n[u>>>8&255]^a[255&h]^r[p];p+=1;let t=s[d>>>24]^i[u>>>16&255]^n[h>>>8&255]^a[255&c]^r[p];p+=1;let o=s[u>>>24]^i[h>>>16&255]^n[c>>>8&255]^a[255&d]^r[p];p+=1;let l=s[h>>>24]^i[c>>>16&255]^n[d>>>8&255]^a[255&u]^r[p];p+=1,c=e,d=t,u=o,h=l}let f=(o[c>>>24]<<24|o[d>>>16&255]<<16|o[u>>>8&255]<<8|o[255&h])^r[p];p+=1;let m=(o[d>>>24]<<24|o[u>>>16&255]<<16|o[h>>>8&255]<<8|o[255&c])^r[p];p+=1;let g=(o[u>>>24]<<24|o[h>>>16&255]<<16|o[c>>>8&255]<<8|o[255&d])^r[p];p+=1;let y=(o[h>>>24]<<24|o[c>>>16&255]<<16|o[d>>>8&255]<<8|o[255&u])^r[p];p+=1,e[t]=f,e[t+1]=m,e[t+2]=g,e[t+3]=y}};er.keySize=8;var es=J._createHelper(er),ei=[],en=class extends S{_doReset(){this._hash=new y([0x67452301,0xefcdab89,0x98badcfe,0x10325476,0xc3d2e1f0])}_doProcessBlock(e,t){let r=this._hash.words,s=r[0],i=r[1],n=r[2],a=r[3],o=r[4];for(let r=0;r<80;r+=1){if(r<16)ei[r]=0|e[t+r];else{let e=ei[r-3]^ei[r-8]^ei[r-14]^ei[r-16];ei[r]=e<<1|e>>>31}let l=(s<<5|s>>>27)+o+ei[r];r<20?l+=(i&n|~i&a)+0x5a827999:r<40?l+=(i^n^a)+0x6ed9eba1:r<60?l+=(i&n|i&a|n&a)-0x70e44324:l+=(i^n^a)-0x359d3e2a,o=a,a=n,n=i<<30|i>>>2,i=s,s=l}r[0]=r[0]+s|0,r[1]=r[1]+i|0,r[2]=r[2]+n|0,r[3]=r[3]+a|0,r[4]=r[4]+o|0}_doFinalize(){let e=this._data,t=e.words,r=8*this._nDataBytes,s=8*e.sigBytes;return t[s>>>5]|=128<<24-s%32,t[(s+64>>>9<<4)+14]=Math.floor(r/0x100000000),t[(s+64>>>9<<4)+15]=r,e.sigBytes=4*t.length,this._process(),this._hash}clone(){let e=super.clone.call(this);return e._hash=this._hash.clone(),e}},ea=(S._createHelper(en),S._createHmacHelper(en)),eo=r(75360),el=r(35621);let ec="x-middleware-override-headers",ed="x-middleware-request";function eu(e,t){if(!(0,el.Zd)(e))throw Error(t)}function eh(e,t,r){if(!r||ea(e,t).toString()!==r)throw Error(eo._t)}let ep="clerk_keyless_dummy_key";function ef(e){if(!e)return{};let t=(0,s.Fj)()?i.o7||i.rB:i.o7||i.rB||ep;try{return eg(e,t)}catch{if(n)try{return eg(e,ep)}catch{em()}em()}}function em(){if((0,s.Fj)())throw Error(eo.mJ);throw Error(eo.RC)}function eg(e,t){return JSON.parse(es.decrypt(e,t).toString(v))}},60864:(e,t,r)=>{"use strict";function s(e){return"clerkError"in e}r.d(t,{LR:()=>n,_r:()=>o,$R:()=>s,u$:()=>i});function i(e){return{code:e.code,message:e.message,longMessage:e.long_message,meta:{paramName:e?.meta?.param_name,sessionId:e?.meta?.session_id,emailAddresses:e?.meta?.email_addresses,identifiers:e?.meta?.identifiers,zxcvbn:e?.meta?.zxcvbn,plan:e?.meta?.plan}}}var n=class e extends Error{constructor(t,{data:r,status:s,clerkTraceId:n,retryAfter:a}){super(t),this.toString=()=>{let e=`[${this.name}]
Message:${this.message}
Status:${this.status}
Serialized errors: ${this.errors.map(e=>JSON.stringify(e))}`;return this.clerkTraceId&&(e+=`
Clerk Trace ID: ${this.clerkTraceId}`),e},Object.setPrototypeOf(this,e.prototype),this.status=s,this.message=t,this.clerkTraceId=n,this.retryAfter=a,this.clerkError=!0,this.errors=function(e=[]){return e.length>0?e.map(i):[]}(r)}},a=Object.freeze({InvalidProxyUrlErrorMessage:"The proxyUrl passed to Clerk is invalid. The expected value for proxyUrl is an absolute URL or a relative path with a leading '/'. (key={{url}})",InvalidPublishableKeyErrorMessage:"The publishableKey passed to Clerk is invalid. You can get your Publishable key at https://dashboard.clerk.com/last-active?path=api-keys. (key={{key}})",MissingPublishableKeyErrorMessage:"Missing publishableKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingSecretKeyErrorMessage:"Missing secretKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingClerkProvider:"{{source}} can only be used within the <ClerkProvider /> component. Learn more: https://clerk.com/docs/components/clerk-provider"});function o({packageName:e,customMessages:t}){let r=e,s={...a,...t};function i(e,t){if(!t)return`${r}: ${e}`;let s=e;for(let r of e.matchAll(/{{([a-zA-Z0-9-_]+)}}/g)){let e=(t[r[1]]||"").toString();s=s.replace(`{{${r[1]}}}`,e)}return`${r}: ${s}`}return{setPackageName({packageName:e}){return"string"==typeof e&&(r=e),this},setMessages({customMessages:e}){return Object.assign(s,e||{}),this},throwInvalidPublishableKeyError(e){throw Error(i(s.InvalidPublishableKeyErrorMessage,e))},throwInvalidProxyUrl(e){throw Error(i(s.InvalidProxyUrlErrorMessage,e))},throwMissingPublishableKeyError(){throw Error(i(s.MissingPublishableKeyErrorMessage))},throwMissingSecretKeyError(){throw Error(i(s.MissingSecretKeyErrorMessage))},throwMissingClerkProviderError(e){throw Error(i(s.MissingClerkProvider,e))},throw(e){throw Error(i(e))}}}r(45663)},61261:(e,t,r)=>{"use strict";r.d(t,{AA:()=>s.AA,KV:()=>s.KV,Rn:()=>s.Rn,Sp:()=>s.Sp,TD:()=>s.TD,X9:()=>s.X9,_:()=>s._,a4:()=>s.a4,ks:()=>s.ks,lv:()=>s.lv,tl:()=>s.tl,vH:()=>s.vH,vb:()=>s.vb,wI:()=>s.wI,xE:()=>s.xE});var s=r(5877);r(24981),r(21404),r(68272)},68272:(e,t,r)=>{"use strict";r.d(t,{h5:()=>l,jn:()=>i,qu:()=>s,sM:()=>c,xy:()=>o,z:()=>n,zF:()=>a});var s={InvalidSecretKey:"clerk_key_invalid"},i={TokenExpired:"token-expired",TokenInvalid:"token-invalid",TokenInvalidAlgorithm:"token-invalid-algorithm",TokenInvalidAuthorizedParties:"token-invalid-authorized-parties",TokenInvalidSignature:"token-invalid-signature",TokenNotActiveYet:"token-not-active-yet",TokenIatInTheFuture:"token-iat-in-the-future",TokenVerificationFailed:"token-verification-failed",InvalidSecretKey:"secret-key-invalid",LocalJWKMissing:"jwk-local-missing",RemoteJWKFailedToLoad:"jwk-remote-failed-to-load",RemoteJWKInvalid:"jwk-remote-invalid",RemoteJWKMissing:"jwk-remote-missing",JWKFailedToResolve:"jwk-failed-to-resolve",JWKKidMismatch:"jwk-kid-mismatch"},n={ContactSupport:"Contact <EMAIL>",EnsureClerkJWT:"Make sure that this is a valid Clerk generate JWT.",SetClerkJWTKey:"Set the CLERK_JWT_KEY environment variable.",SetClerkSecretKey:"Set the CLERK_SECRET_KEY environment variable.",EnsureClockSync:"Make sure your system clock is in sync (e.g. turn off and on automatic time synchronization)."},a=class e extends Error{constructor({action:t,message:r,reason:s}){super(r),Object.setPrototypeOf(this,e.prototype),this.reason=s,this.message=r,this.action=t}getFullMessage(){return`${[this.message,this.action].filter(e=>e).join(" ")} (reason=${this.reason}, token-carrier=${this.tokenCarrier})`}},o=class extends Error{},l={TokenInvalid:"token-invalid",InvalidSecretKey:"secret-key-invalid",UnexpectedError:"unexpected-error"},c=class e extends Error{constructor({message:t,code:r,status:s}){super(t),Object.setPrototypeOf(this,e.prototype),this.code=r,this.status=s}getFullMessage(){return`${this.message} (code=${this.code}, status=${this.status})`}}},69451:(e,t,r)=>{"use strict";r.d(t,{Fj:()=>n,MC:()=>i,b_:()=>s});var s=()=>!1,i=()=>!1,n=()=>{try{return!0}catch{}return!1}},73819:(e,t,r)=>{"use strict";r.d(t,{y:()=>s});var s=e=>"undefined"!=typeof atob&&"function"==typeof atob?atob(e):"undefined"!=typeof global&&global.Buffer?new global.Buffer(e,"base64").toString():e},75360:(e,t,r)=>{"use strict";r.d(t,{AG:()=>s,RC:()=>o,_t:()=>n,mJ:()=>a,sd:()=>i});let s=()=>i("getAuth"),i=(e="auth",t)=>`Clerk: ${e}() was called but Clerk can't detect usage of clerkMiddleware(). Please ensure the following:
- ${t?[...t,""].join("\n- "):" "}clerkMiddleware() is used in your Next.js Middleware.
- Your Middleware matcher is configured to match this route or page.
- If you are using the src directory, make sure the Middleware file is inside of it.

For more details, see https://clerk.com/err/auth-middleware
`,n="Clerk: Unable to verify request, this usually means the Clerk middleware did not run. Ensure Clerk's middleware is properly integrated and matches the current route. For more information, see: https://clerk.com/docs/references/nextjs/clerk-middleware. (code=auth_signature_invalid)",a="Clerk: Unable to decrypt request data, this usually means the encryption key is invalid. Ensure the encryption key is properly set. For more information, see: https://clerk.com/docs/references/nextjs/clerk-middleware#dynamic-keys. (code=encryption_key_invalid)",o=`Clerk: Unable to decrypt request data.

Refresh the page if your .env file was just updated. If the issue persists, ensure the encryption key is valid and properly set.

For more information, see: https://clerk.com/docs/references/nextjs/clerk-middleware#dynamic-keys. (code=encryption_key_invalid)`},86874:(e,t,r)=>{"use strict";let s=r(98433),{snakeCase:i}=r(19266),n={}.constructor;e.exports=function(e,t){if(Array.isArray(e)){if(e.some(e=>e.constructor!==n))throw Error("obj must be array of plain objects")}else if(e.constructor!==n)throw Error("obj must be an plain object");return s(e,function(e,r){var s,n,a,o,l;return[(s=t.exclude,n=e,s.some(function(e){return"string"==typeof e?e===n:e.test(n)}))?e:i(e,t.parsingOptions),r,(a=e,o=r,(l=t).shouldRecurse?{shouldRecurse:l.shouldRecurse(a,o)}:void 0)]},t=Object.assign({deep:!0,exclude:[],parsingOptions:{}},t))}},95035:(e,t,r)=>{"use strict";function s(e){return async(...t)=>{let{data:r,errors:s}=await e(...t);if(s)throw s[0];return r}}function i(e){return(...t)=>{let{data:r,errors:s}=e(...t);if(s)throw s[0];return r}}r.d(t,{C:()=>s,R:()=>i})},98433:e=>{"use strict";let t=e=>"object"==typeof e&&null!==e,r=Symbol("skip"),s=e=>t(e)&&!(e instanceof RegExp)&&!(e instanceof Error)&&!(e instanceof Date),i=(e,t,n,a=new WeakMap)=>{if(n={deep:!1,target:{},...n},a.has(e))return a.get(e);a.set(e,n.target);let{target:o}=n;delete n.target;let l=e=>e.map(e=>s(e)?i(e,t,n,a):e);if(Array.isArray(e))return l(e);for(let[c,d]of Object.entries(e)){let u=t(c,d,e);if(u===r)continue;let[h,p,{shouldRecurse:f=!0}={}]=u;"__proto__"!==h&&(n.deep&&f&&s(p)&&(p=Array.isArray(p)?l(p):i(p,t,n,a)),o[h]=p)}return o};e.exports=(e,r,s)=>{if(!t(e))throw TypeError(`Expected an object, got \`${e}\` (${typeof e})`);return i(e,r,s)},e.exports.mapObjectSkip=r},98950:(e,t,r)=>{"use strict";r.d(t,{zz:()=>i});var s=e=>{let t=r=>{if(!r)return r;if(Array.isArray(r))return r.map(e=>"object"==typeof e||Array.isArray(e)?t(e):e);let s={...r};for(let r of Object.keys(s)){let i=e(r.toString());i!==r&&(s[i]=s[r],delete s[r]),"object"==typeof s[i]&&(s[i]=t(s[i]))}return s};return t};function i(e){if("boolean"==typeof e)return e;if(null==e)return!1;if("string"==typeof e){if("true"===e.toLowerCase())return!0;if("false"===e.toLowerCase())return!1}let t=parseInt(e,10);return!isNaN(t)&&t>0}s(function(e){return e?e.replace(/[A-Z]/g,e=>`_${e.toLowerCase()}`):""}),s(function(e){return e?e.replace(/([-_][a-z])/g,e=>e.toUpperCase().replace(/-|_/,"")):""})},99657:(e,t,r)=>{"use strict";r.d(t,{H$:()=>d,mG:()=>a,V2:()=>u,o7:()=>c,fS:()=>p,ev:()=>_,Rg:()=>h,At:()=>l,tm:()=>g,rB:()=>o,qW:()=>f,sE:()=>m,Mh:()=>k,nN:()=>y});var s=r(44468),i=r(35009);r(45663);var n=r(28941);process.env.NEXT_PUBLIC_CLERK_JS_VERSION,process.env.NEXT_PUBLIC_CLERK_JS_URL;let a=process.env.CLERK_API_VERSION||"v1",o=process.env.CLERK_SECRET_KEY||"",l=process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY||"",c=process.env.CLERK_ENCRYPTION_KEY||"",d=process.env.CLERK_API_URL||(e=>{let t=(0,s.q5)(e)?.frontendApi;return t?.startsWith("clerk.")&&i.iM.some(e=>t?.endsWith(e))?i.FW:i.mG.some(e=>t?.endsWith(e))?i.Vc:i.ub.some(e=>t?.endsWith(e))?i.HG:i.FW})(l),u=process.env.NEXT_PUBLIC_CLERK_DOMAIN||"",h=process.env.NEXT_PUBLIC_CLERK_PROXY_URL||"",p=(0,n.zz)(process.env.NEXT_PUBLIC_CLERK_IS_SATELLITE)||!1,f=process.env.NEXT_PUBLIC_CLERK_SIGN_IN_URL||"",m=process.env.NEXT_PUBLIC_CLERK_SIGN_UP_URL||"",g={name:"@clerk/nextjs",version:"6.23.3",environment:"production"},y=(0,n.zz)(process.env.NEXT_PUBLIC_CLERK_TELEMETRY_DISABLED),k=(0,n.zz)(process.env.NEXT_PUBLIC_CLERK_TELEMETRY_DEBUG),_=(0,n.zz)(process.env.NEXT_PUBLIC_CLERK_KEYLESS_DISABLED)||!1}};