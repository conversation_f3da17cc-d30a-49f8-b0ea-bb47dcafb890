(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["chunks/node_modules_28cb8b9c._.js", {

"[project]/node_modules/next/dist/compiled/@opentelemetry/api/index.js [instrumentation] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
(()=>{
    "use strict";
    var e = {
        491: (e, t, r)=>{
            Object.defineProperty(t, "__esModule", {
                value: true
            });
            t.ContextAPI = void 0;
            const n = r(223);
            const a = r(172);
            const o = r(930);
            const i = "context";
            const c = new n.NoopContextManager;
            class ContextAPI {
                constructor(){}
                static getInstance() {
                    if (!this._instance) {
                        this._instance = new ContextAPI;
                    }
                    return this._instance;
                }
                setGlobalContextManager(e) {
                    return (0, a.registerGlobal)(i, e, o.DiagAPI.instance());
                }
                active() {
                    return this._getContextManager().active();
                }
                with(e, t, r, ...n) {
                    return this._getContextManager().with(e, t, r, ...n);
                }
                bind(e, t) {
                    return this._getContextManager().bind(e, t);
                }
                _getContextManager() {
                    return (0, a.getGlobal)(i) || c;
                }
                disable() {
                    this._getContextManager().disable();
                    (0, a.unregisterGlobal)(i, o.DiagAPI.instance());
                }
            }
            t.ContextAPI = ContextAPI;
        },
        930: (e, t, r)=>{
            Object.defineProperty(t, "__esModule", {
                value: true
            });
            t.DiagAPI = void 0;
            const n = r(56);
            const a = r(912);
            const o = r(957);
            const i = r(172);
            const c = "diag";
            class DiagAPI {
                constructor(){
                    function _logProxy(e) {
                        return function(...t) {
                            const r = (0, i.getGlobal)("diag");
                            if (!r) return;
                            return r[e](...t);
                        };
                    }
                    const e = this;
                    const setLogger = (t, r = {
                        logLevel: o.DiagLogLevel.INFO
                    })=>{
                        var n, c, s;
                        if (t === e) {
                            const t = new Error("Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation");
                            e.error((n = t.stack) !== null && n !== void 0 ? n : t.message);
                            return false;
                        }
                        if (typeof r === "number") {
                            r = {
                                logLevel: r
                            };
                        }
                        const u = (0, i.getGlobal)("diag");
                        const l = (0, a.createLogLevelDiagLogger)((c = r.logLevel) !== null && c !== void 0 ? c : o.DiagLogLevel.INFO, t);
                        if (u && !r.suppressOverrideMessage) {
                            const e = (s = (new Error).stack) !== null && s !== void 0 ? s : "<failed to generate stacktrace>";
                            u.warn(`Current logger will be overwritten from ${e}`);
                            l.warn(`Current logger will overwrite one already registered from ${e}`);
                        }
                        return (0, i.registerGlobal)("diag", l, e, true);
                    };
                    e.setLogger = setLogger;
                    e.disable = ()=>{
                        (0, i.unregisterGlobal)(c, e);
                    };
                    e.createComponentLogger = (e)=>new n.DiagComponentLogger(e);
                    e.verbose = _logProxy("verbose");
                    e.debug = _logProxy("debug");
                    e.info = _logProxy("info");
                    e.warn = _logProxy("warn");
                    e.error = _logProxy("error");
                }
                static instance() {
                    if (!this._instance) {
                        this._instance = new DiagAPI;
                    }
                    return this._instance;
                }
            }
            t.DiagAPI = DiagAPI;
        },
        653: (e, t, r)=>{
            Object.defineProperty(t, "__esModule", {
                value: true
            });
            t.MetricsAPI = void 0;
            const n = r(660);
            const a = r(172);
            const o = r(930);
            const i = "metrics";
            class MetricsAPI {
                constructor(){}
                static getInstance() {
                    if (!this._instance) {
                        this._instance = new MetricsAPI;
                    }
                    return this._instance;
                }
                setGlobalMeterProvider(e) {
                    return (0, a.registerGlobal)(i, e, o.DiagAPI.instance());
                }
                getMeterProvider() {
                    return (0, a.getGlobal)(i) || n.NOOP_METER_PROVIDER;
                }
                getMeter(e, t, r) {
                    return this.getMeterProvider().getMeter(e, t, r);
                }
                disable() {
                    (0, a.unregisterGlobal)(i, o.DiagAPI.instance());
                }
            }
            t.MetricsAPI = MetricsAPI;
        },
        181: (e, t, r)=>{
            Object.defineProperty(t, "__esModule", {
                value: true
            });
            t.PropagationAPI = void 0;
            const n = r(172);
            const a = r(874);
            const o = r(194);
            const i = r(277);
            const c = r(369);
            const s = r(930);
            const u = "propagation";
            const l = new a.NoopTextMapPropagator;
            class PropagationAPI {
                constructor(){
                    this.createBaggage = c.createBaggage;
                    this.getBaggage = i.getBaggage;
                    this.getActiveBaggage = i.getActiveBaggage;
                    this.setBaggage = i.setBaggage;
                    this.deleteBaggage = i.deleteBaggage;
                }
                static getInstance() {
                    if (!this._instance) {
                        this._instance = new PropagationAPI;
                    }
                    return this._instance;
                }
                setGlobalPropagator(e) {
                    return (0, n.registerGlobal)(u, e, s.DiagAPI.instance());
                }
                inject(e, t, r = o.defaultTextMapSetter) {
                    return this._getGlobalPropagator().inject(e, t, r);
                }
                extract(e, t, r = o.defaultTextMapGetter) {
                    return this._getGlobalPropagator().extract(e, t, r);
                }
                fields() {
                    return this._getGlobalPropagator().fields();
                }
                disable() {
                    (0, n.unregisterGlobal)(u, s.DiagAPI.instance());
                }
                _getGlobalPropagator() {
                    return (0, n.getGlobal)(u) || l;
                }
            }
            t.PropagationAPI = PropagationAPI;
        },
        997: (e, t, r)=>{
            Object.defineProperty(t, "__esModule", {
                value: true
            });
            t.TraceAPI = void 0;
            const n = r(172);
            const a = r(846);
            const o = r(139);
            const i = r(607);
            const c = r(930);
            const s = "trace";
            class TraceAPI {
                constructor(){
                    this._proxyTracerProvider = new a.ProxyTracerProvider;
                    this.wrapSpanContext = o.wrapSpanContext;
                    this.isSpanContextValid = o.isSpanContextValid;
                    this.deleteSpan = i.deleteSpan;
                    this.getSpan = i.getSpan;
                    this.getActiveSpan = i.getActiveSpan;
                    this.getSpanContext = i.getSpanContext;
                    this.setSpan = i.setSpan;
                    this.setSpanContext = i.setSpanContext;
                }
                static getInstance() {
                    if (!this._instance) {
                        this._instance = new TraceAPI;
                    }
                    return this._instance;
                }
                setGlobalTracerProvider(e) {
                    const t = (0, n.registerGlobal)(s, this._proxyTracerProvider, c.DiagAPI.instance());
                    if (t) {
                        this._proxyTracerProvider.setDelegate(e);
                    }
                    return t;
                }
                getTracerProvider() {
                    return (0, n.getGlobal)(s) || this._proxyTracerProvider;
                }
                getTracer(e, t) {
                    return this.getTracerProvider().getTracer(e, t);
                }
                disable() {
                    (0, n.unregisterGlobal)(s, c.DiagAPI.instance());
                    this._proxyTracerProvider = new a.ProxyTracerProvider;
                }
            }
            t.TraceAPI = TraceAPI;
        },
        277: (e, t, r)=>{
            Object.defineProperty(t, "__esModule", {
                value: true
            });
            t.deleteBaggage = t.setBaggage = t.getActiveBaggage = t.getBaggage = void 0;
            const n = r(491);
            const a = r(780);
            const o = (0, a.createContextKey)("OpenTelemetry Baggage Key");
            function getBaggage(e) {
                return e.getValue(o) || undefined;
            }
            t.getBaggage = getBaggage;
            function getActiveBaggage() {
                return getBaggage(n.ContextAPI.getInstance().active());
            }
            t.getActiveBaggage = getActiveBaggage;
            function setBaggage(e, t) {
                return e.setValue(o, t);
            }
            t.setBaggage = setBaggage;
            function deleteBaggage(e) {
                return e.deleteValue(o);
            }
            t.deleteBaggage = deleteBaggage;
        },
        993: (e, t)=>{
            Object.defineProperty(t, "__esModule", {
                value: true
            });
            t.BaggageImpl = void 0;
            class BaggageImpl {
                constructor(e){
                    this._entries = e ? new Map(e) : new Map;
                }
                getEntry(e) {
                    const t = this._entries.get(e);
                    if (!t) {
                        return undefined;
                    }
                    return Object.assign({}, t);
                }
                getAllEntries() {
                    return Array.from(this._entries.entries()).map(([e, t])=>[
                            e,
                            t
                        ]);
                }
                setEntry(e, t) {
                    const r = new BaggageImpl(this._entries);
                    r._entries.set(e, t);
                    return r;
                }
                removeEntry(e) {
                    const t = new BaggageImpl(this._entries);
                    t._entries.delete(e);
                    return t;
                }
                removeEntries(...e) {
                    const t = new BaggageImpl(this._entries);
                    for (const r of e){
                        t._entries.delete(r);
                    }
                    return t;
                }
                clear() {
                    return new BaggageImpl;
                }
            }
            t.BaggageImpl = BaggageImpl;
        },
        830: (e, t)=>{
            Object.defineProperty(t, "__esModule", {
                value: true
            });
            t.baggageEntryMetadataSymbol = void 0;
            t.baggageEntryMetadataSymbol = Symbol("BaggageEntryMetadata");
        },
        369: (e, t, r)=>{
            Object.defineProperty(t, "__esModule", {
                value: true
            });
            t.baggageEntryMetadataFromString = t.createBaggage = void 0;
            const n = r(930);
            const a = r(993);
            const o = r(830);
            const i = n.DiagAPI.instance();
            function createBaggage(e = {}) {
                return new a.BaggageImpl(new Map(Object.entries(e)));
            }
            t.createBaggage = createBaggage;
            function baggageEntryMetadataFromString(e) {
                if (typeof e !== "string") {
                    i.error(`Cannot create baggage metadata from unknown type: ${typeof e}`);
                    e = "";
                }
                return {
                    __TYPE__: o.baggageEntryMetadataSymbol,
                    toString () {
                        return e;
                    }
                };
            }
            t.baggageEntryMetadataFromString = baggageEntryMetadataFromString;
        },
        67: (e, t, r)=>{
            Object.defineProperty(t, "__esModule", {
                value: true
            });
            t.context = void 0;
            const n = r(491);
            t.context = n.ContextAPI.getInstance();
        },
        223: (e, t, r)=>{
            Object.defineProperty(t, "__esModule", {
                value: true
            });
            t.NoopContextManager = void 0;
            const n = r(780);
            class NoopContextManager {
                active() {
                    return n.ROOT_CONTEXT;
                }
                with(e, t, r, ...n) {
                    return t.call(r, ...n);
                }
                bind(e, t) {
                    return t;
                }
                enable() {
                    return this;
                }
                disable() {
                    return this;
                }
            }
            t.NoopContextManager = NoopContextManager;
        },
        780: (e, t)=>{
            Object.defineProperty(t, "__esModule", {
                value: true
            });
            t.ROOT_CONTEXT = t.createContextKey = void 0;
            function createContextKey(e) {
                return Symbol.for(e);
            }
            t.createContextKey = createContextKey;
            class BaseContext {
                constructor(e){
                    const t = this;
                    t._currentContext = e ? new Map(e) : new Map;
                    t.getValue = (e)=>t._currentContext.get(e);
                    t.setValue = (e, r)=>{
                        const n = new BaseContext(t._currentContext);
                        n._currentContext.set(e, r);
                        return n;
                    };
                    t.deleteValue = (e)=>{
                        const r = new BaseContext(t._currentContext);
                        r._currentContext.delete(e);
                        return r;
                    };
                }
            }
            t.ROOT_CONTEXT = new BaseContext;
        },
        506: (e, t, r)=>{
            Object.defineProperty(t, "__esModule", {
                value: true
            });
            t.diag = void 0;
            const n = r(930);
            t.diag = n.DiagAPI.instance();
        },
        56: (e, t, r)=>{
            Object.defineProperty(t, "__esModule", {
                value: true
            });
            t.DiagComponentLogger = void 0;
            const n = r(172);
            class DiagComponentLogger {
                constructor(e){
                    this._namespace = e.namespace || "DiagComponentLogger";
                }
                debug(...e) {
                    return logProxy("debug", this._namespace, e);
                }
                error(...e) {
                    return logProxy("error", this._namespace, e);
                }
                info(...e) {
                    return logProxy("info", this._namespace, e);
                }
                warn(...e) {
                    return logProxy("warn", this._namespace, e);
                }
                verbose(...e) {
                    return logProxy("verbose", this._namespace, e);
                }
            }
            t.DiagComponentLogger = DiagComponentLogger;
            function logProxy(e, t, r) {
                const a = (0, n.getGlobal)("diag");
                if (!a) {
                    return;
                }
                r.unshift(t);
                return a[e](...r);
            }
        },
        972: (e, t)=>{
            Object.defineProperty(t, "__esModule", {
                value: true
            });
            t.DiagConsoleLogger = void 0;
            const r = [
                {
                    n: "error",
                    c: "error"
                },
                {
                    n: "warn",
                    c: "warn"
                },
                {
                    n: "info",
                    c: "info"
                },
                {
                    n: "debug",
                    c: "debug"
                },
                {
                    n: "verbose",
                    c: "trace"
                }
            ];
            class DiagConsoleLogger {
                constructor(){
                    function _consoleFunc(e) {
                        return function(...t) {
                            if (console) {
                                let r = console[e];
                                if (typeof r !== "function") {
                                    r = console.log;
                                }
                                if (typeof r === "function") {
                                    return r.apply(console, t);
                                }
                            }
                        };
                    }
                    for(let e = 0; e < r.length; e++){
                        this[r[e].n] = _consoleFunc(r[e].c);
                    }
                }
            }
            t.DiagConsoleLogger = DiagConsoleLogger;
        },
        912: (e, t, r)=>{
            Object.defineProperty(t, "__esModule", {
                value: true
            });
            t.createLogLevelDiagLogger = void 0;
            const n = r(957);
            function createLogLevelDiagLogger(e, t) {
                if (e < n.DiagLogLevel.NONE) {
                    e = n.DiagLogLevel.NONE;
                } else if (e > n.DiagLogLevel.ALL) {
                    e = n.DiagLogLevel.ALL;
                }
                t = t || {};
                function _filterFunc(r, n) {
                    const a = t[r];
                    if (typeof a === "function" && e >= n) {
                        return a.bind(t);
                    }
                    return function() {};
                }
                return {
                    error: _filterFunc("error", n.DiagLogLevel.ERROR),
                    warn: _filterFunc("warn", n.DiagLogLevel.WARN),
                    info: _filterFunc("info", n.DiagLogLevel.INFO),
                    debug: _filterFunc("debug", n.DiagLogLevel.DEBUG),
                    verbose: _filterFunc("verbose", n.DiagLogLevel.VERBOSE)
                };
            }
            t.createLogLevelDiagLogger = createLogLevelDiagLogger;
        },
        957: (e, t)=>{
            Object.defineProperty(t, "__esModule", {
                value: true
            });
            t.DiagLogLevel = void 0;
            var r;
            (function(e) {
                e[e["NONE"] = 0] = "NONE";
                e[e["ERROR"] = 30] = "ERROR";
                e[e["WARN"] = 50] = "WARN";
                e[e["INFO"] = 60] = "INFO";
                e[e["DEBUG"] = 70] = "DEBUG";
                e[e["VERBOSE"] = 80] = "VERBOSE";
                e[e["ALL"] = 9999] = "ALL";
            })(r = t.DiagLogLevel || (t.DiagLogLevel = {}));
        },
        172: (e, t, r)=>{
            Object.defineProperty(t, "__esModule", {
                value: true
            });
            t.unregisterGlobal = t.getGlobal = t.registerGlobal = void 0;
            const n = r(200);
            const a = r(521);
            const o = r(130);
            const i = a.VERSION.split(".")[0];
            const c = Symbol.for(`opentelemetry.js.api.${i}`);
            const s = n._globalThis;
            function registerGlobal(e, t, r, n = false) {
                var o;
                const i = s[c] = (o = s[c]) !== null && o !== void 0 ? o : {
                    version: a.VERSION
                };
                if (!n && i[e]) {
                    const t = new Error(`@opentelemetry/api: Attempted duplicate registration of API: ${e}`);
                    r.error(t.stack || t.message);
                    return false;
                }
                if (i.version !== a.VERSION) {
                    const t = new Error(`@opentelemetry/api: Registration of version v${i.version} for ${e} does not match previously registered API v${a.VERSION}`);
                    r.error(t.stack || t.message);
                    return false;
                }
                i[e] = t;
                r.debug(`@opentelemetry/api: Registered a global for ${e} v${a.VERSION}.`);
                return true;
            }
            t.registerGlobal = registerGlobal;
            function getGlobal(e) {
                var t, r;
                const n = (t = s[c]) === null || t === void 0 ? void 0 : t.version;
                if (!n || !(0, o.isCompatible)(n)) {
                    return;
                }
                return (r = s[c]) === null || r === void 0 ? void 0 : r[e];
            }
            t.getGlobal = getGlobal;
            function unregisterGlobal(e, t) {
                t.debug(`@opentelemetry/api: Unregistering a global for ${e} v${a.VERSION}.`);
                const r = s[c];
                if (r) {
                    delete r[e];
                }
            }
            t.unregisterGlobal = unregisterGlobal;
        },
        130: (e, t, r)=>{
            Object.defineProperty(t, "__esModule", {
                value: true
            });
            t.isCompatible = t._makeCompatibilityCheck = void 0;
            const n = r(521);
            const a = /^(\d+)\.(\d+)\.(\d+)(-(.+))?$/;
            function _makeCompatibilityCheck(e) {
                const t = new Set([
                    e
                ]);
                const r = new Set;
                const n = e.match(a);
                if (!n) {
                    return ()=>false;
                }
                const o = {
                    major: +n[1],
                    minor: +n[2],
                    patch: +n[3],
                    prerelease: n[4]
                };
                if (o.prerelease != null) {
                    return function isExactmatch(t) {
                        return t === e;
                    };
                }
                function _reject(e) {
                    r.add(e);
                    return false;
                }
                function _accept(e) {
                    t.add(e);
                    return true;
                }
                return function isCompatible(e) {
                    if (t.has(e)) {
                        return true;
                    }
                    if (r.has(e)) {
                        return false;
                    }
                    const n = e.match(a);
                    if (!n) {
                        return _reject(e);
                    }
                    const i = {
                        major: +n[1],
                        minor: +n[2],
                        patch: +n[3],
                        prerelease: n[4]
                    };
                    if (i.prerelease != null) {
                        return _reject(e);
                    }
                    if (o.major !== i.major) {
                        return _reject(e);
                    }
                    if (o.major === 0) {
                        if (o.minor === i.minor && o.patch <= i.patch) {
                            return _accept(e);
                        }
                        return _reject(e);
                    }
                    if (o.minor <= i.minor) {
                        return _accept(e);
                    }
                    return _reject(e);
                };
            }
            t._makeCompatibilityCheck = _makeCompatibilityCheck;
            t.isCompatible = _makeCompatibilityCheck(n.VERSION);
        },
        886: (e, t, r)=>{
            Object.defineProperty(t, "__esModule", {
                value: true
            });
            t.metrics = void 0;
            const n = r(653);
            t.metrics = n.MetricsAPI.getInstance();
        },
        901: (e, t)=>{
            Object.defineProperty(t, "__esModule", {
                value: true
            });
            t.ValueType = void 0;
            var r;
            (function(e) {
                e[e["INT"] = 0] = "INT";
                e[e["DOUBLE"] = 1] = "DOUBLE";
            })(r = t.ValueType || (t.ValueType = {}));
        },
        102: (e, t)=>{
            Object.defineProperty(t, "__esModule", {
                value: true
            });
            t.createNoopMeter = t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC = t.NOOP_OBSERVABLE_GAUGE_METRIC = t.NOOP_OBSERVABLE_COUNTER_METRIC = t.NOOP_UP_DOWN_COUNTER_METRIC = t.NOOP_HISTOGRAM_METRIC = t.NOOP_COUNTER_METRIC = t.NOOP_METER = t.NoopObservableUpDownCounterMetric = t.NoopObservableGaugeMetric = t.NoopObservableCounterMetric = t.NoopObservableMetric = t.NoopHistogramMetric = t.NoopUpDownCounterMetric = t.NoopCounterMetric = t.NoopMetric = t.NoopMeter = void 0;
            class NoopMeter {
                constructor(){}
                createHistogram(e, r) {
                    return t.NOOP_HISTOGRAM_METRIC;
                }
                createCounter(e, r) {
                    return t.NOOP_COUNTER_METRIC;
                }
                createUpDownCounter(e, r) {
                    return t.NOOP_UP_DOWN_COUNTER_METRIC;
                }
                createObservableGauge(e, r) {
                    return t.NOOP_OBSERVABLE_GAUGE_METRIC;
                }
                createObservableCounter(e, r) {
                    return t.NOOP_OBSERVABLE_COUNTER_METRIC;
                }
                createObservableUpDownCounter(e, r) {
                    return t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC;
                }
                addBatchObservableCallback(e, t) {}
                removeBatchObservableCallback(e) {}
            }
            t.NoopMeter = NoopMeter;
            class NoopMetric {
            }
            t.NoopMetric = NoopMetric;
            class NoopCounterMetric extends NoopMetric {
                add(e, t) {}
            }
            t.NoopCounterMetric = NoopCounterMetric;
            class NoopUpDownCounterMetric extends NoopMetric {
                add(e, t) {}
            }
            t.NoopUpDownCounterMetric = NoopUpDownCounterMetric;
            class NoopHistogramMetric extends NoopMetric {
                record(e, t) {}
            }
            t.NoopHistogramMetric = NoopHistogramMetric;
            class NoopObservableMetric {
                addCallback(e) {}
                removeCallback(e) {}
            }
            t.NoopObservableMetric = NoopObservableMetric;
            class NoopObservableCounterMetric extends NoopObservableMetric {
            }
            t.NoopObservableCounterMetric = NoopObservableCounterMetric;
            class NoopObservableGaugeMetric extends NoopObservableMetric {
            }
            t.NoopObservableGaugeMetric = NoopObservableGaugeMetric;
            class NoopObservableUpDownCounterMetric extends NoopObservableMetric {
            }
            t.NoopObservableUpDownCounterMetric = NoopObservableUpDownCounterMetric;
            t.NOOP_METER = new NoopMeter;
            t.NOOP_COUNTER_METRIC = new NoopCounterMetric;
            t.NOOP_HISTOGRAM_METRIC = new NoopHistogramMetric;
            t.NOOP_UP_DOWN_COUNTER_METRIC = new NoopUpDownCounterMetric;
            t.NOOP_OBSERVABLE_COUNTER_METRIC = new NoopObservableCounterMetric;
            t.NOOP_OBSERVABLE_GAUGE_METRIC = new NoopObservableGaugeMetric;
            t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC = new NoopObservableUpDownCounterMetric;
            function createNoopMeter() {
                return t.NOOP_METER;
            }
            t.createNoopMeter = createNoopMeter;
        },
        660: (e, t, r)=>{
            Object.defineProperty(t, "__esModule", {
                value: true
            });
            t.NOOP_METER_PROVIDER = t.NoopMeterProvider = void 0;
            const n = r(102);
            class NoopMeterProvider {
                getMeter(e, t, r) {
                    return n.NOOP_METER;
                }
            }
            t.NoopMeterProvider = NoopMeterProvider;
            t.NOOP_METER_PROVIDER = new NoopMeterProvider;
        },
        200: function(e, t, r) {
            var n = this && this.__createBinding || (Object.create ? function(e, t, r, n) {
                if (n === undefined) n = r;
                Object.defineProperty(e, n, {
                    enumerable: true,
                    get: function() {
                        return t[r];
                    }
                });
            } : function(e, t, r, n) {
                if (n === undefined) n = r;
                e[n] = t[r];
            });
            var a = this && this.__exportStar || function(e, t) {
                for(var r in e)if (r !== "default" && !Object.prototype.hasOwnProperty.call(t, r)) n(t, e, r);
            };
            Object.defineProperty(t, "__esModule", {
                value: true
            });
            a(r(46), t);
        },
        651: (e, t)=>{
            Object.defineProperty(t, "__esModule", {
                value: true
            });
            t._globalThis = void 0;
            t._globalThis = typeof globalThis === "object" ? globalThis : global;
        },
        46: function(e, t, r) {
            var n = this && this.__createBinding || (Object.create ? function(e, t, r, n) {
                if (n === undefined) n = r;
                Object.defineProperty(e, n, {
                    enumerable: true,
                    get: function() {
                        return t[r];
                    }
                });
            } : function(e, t, r, n) {
                if (n === undefined) n = r;
                e[n] = t[r];
            });
            var a = this && this.__exportStar || function(e, t) {
                for(var r in e)if (r !== "default" && !Object.prototype.hasOwnProperty.call(t, r)) n(t, e, r);
            };
            Object.defineProperty(t, "__esModule", {
                value: true
            });
            a(r(651), t);
        },
        939: (e, t, r)=>{
            Object.defineProperty(t, "__esModule", {
                value: true
            });
            t.propagation = void 0;
            const n = r(181);
            t.propagation = n.PropagationAPI.getInstance();
        },
        874: (e, t)=>{
            Object.defineProperty(t, "__esModule", {
                value: true
            });
            t.NoopTextMapPropagator = void 0;
            class NoopTextMapPropagator {
                inject(e, t) {}
                extract(e, t) {
                    return e;
                }
                fields() {
                    return [];
                }
            }
            t.NoopTextMapPropagator = NoopTextMapPropagator;
        },
        194: (e, t)=>{
            Object.defineProperty(t, "__esModule", {
                value: true
            });
            t.defaultTextMapSetter = t.defaultTextMapGetter = void 0;
            t.defaultTextMapGetter = {
                get (e, t) {
                    if (e == null) {
                        return undefined;
                    }
                    return e[t];
                },
                keys (e) {
                    if (e == null) {
                        return [];
                    }
                    return Object.keys(e);
                }
            };
            t.defaultTextMapSetter = {
                set (e, t, r) {
                    if (e == null) {
                        return;
                    }
                    e[t] = r;
                }
            };
        },
        845: (e, t, r)=>{
            Object.defineProperty(t, "__esModule", {
                value: true
            });
            t.trace = void 0;
            const n = r(997);
            t.trace = n.TraceAPI.getInstance();
        },
        403: (e, t, r)=>{
            Object.defineProperty(t, "__esModule", {
                value: true
            });
            t.NonRecordingSpan = void 0;
            const n = r(476);
            class NonRecordingSpan {
                constructor(e = n.INVALID_SPAN_CONTEXT){
                    this._spanContext = e;
                }
                spanContext() {
                    return this._spanContext;
                }
                setAttribute(e, t) {
                    return this;
                }
                setAttributes(e) {
                    return this;
                }
                addEvent(e, t) {
                    return this;
                }
                setStatus(e) {
                    return this;
                }
                updateName(e) {
                    return this;
                }
                end(e) {}
                isRecording() {
                    return false;
                }
                recordException(e, t) {}
            }
            t.NonRecordingSpan = NonRecordingSpan;
        },
        614: (e, t, r)=>{
            Object.defineProperty(t, "__esModule", {
                value: true
            });
            t.NoopTracer = void 0;
            const n = r(491);
            const a = r(607);
            const o = r(403);
            const i = r(139);
            const c = n.ContextAPI.getInstance();
            class NoopTracer {
                startSpan(e, t, r = c.active()) {
                    const n = Boolean(t === null || t === void 0 ? void 0 : t.root);
                    if (n) {
                        return new o.NonRecordingSpan;
                    }
                    const s = r && (0, a.getSpanContext)(r);
                    if (isSpanContext(s) && (0, i.isSpanContextValid)(s)) {
                        return new o.NonRecordingSpan(s);
                    } else {
                        return new o.NonRecordingSpan;
                    }
                }
                startActiveSpan(e, t, r, n) {
                    let o;
                    let i;
                    let s;
                    if (arguments.length < 2) {
                        return;
                    } else if (arguments.length === 2) {
                        s = t;
                    } else if (arguments.length === 3) {
                        o = t;
                        s = r;
                    } else {
                        o = t;
                        i = r;
                        s = n;
                    }
                    const u = i !== null && i !== void 0 ? i : c.active();
                    const l = this.startSpan(e, o, u);
                    const g = (0, a.setSpan)(u, l);
                    return c.with(g, s, undefined, l);
                }
            }
            t.NoopTracer = NoopTracer;
            function isSpanContext(e) {
                return typeof e === "object" && typeof e["spanId"] === "string" && typeof e["traceId"] === "string" && typeof e["traceFlags"] === "number";
            }
        },
        124: (e, t, r)=>{
            Object.defineProperty(t, "__esModule", {
                value: true
            });
            t.NoopTracerProvider = void 0;
            const n = r(614);
            class NoopTracerProvider {
                getTracer(e, t, r) {
                    return new n.NoopTracer;
                }
            }
            t.NoopTracerProvider = NoopTracerProvider;
        },
        125: (e, t, r)=>{
            Object.defineProperty(t, "__esModule", {
                value: true
            });
            t.ProxyTracer = void 0;
            const n = r(614);
            const a = new n.NoopTracer;
            class ProxyTracer {
                constructor(e, t, r, n){
                    this._provider = e;
                    this.name = t;
                    this.version = r;
                    this.options = n;
                }
                startSpan(e, t, r) {
                    return this._getTracer().startSpan(e, t, r);
                }
                startActiveSpan(e, t, r, n) {
                    const a = this._getTracer();
                    return Reflect.apply(a.startActiveSpan, a, arguments);
                }
                _getTracer() {
                    if (this._delegate) {
                        return this._delegate;
                    }
                    const e = this._provider.getDelegateTracer(this.name, this.version, this.options);
                    if (!e) {
                        return a;
                    }
                    this._delegate = e;
                    return this._delegate;
                }
            }
            t.ProxyTracer = ProxyTracer;
        },
        846: (e, t, r)=>{
            Object.defineProperty(t, "__esModule", {
                value: true
            });
            t.ProxyTracerProvider = void 0;
            const n = r(125);
            const a = r(124);
            const o = new a.NoopTracerProvider;
            class ProxyTracerProvider {
                getTracer(e, t, r) {
                    var a;
                    return (a = this.getDelegateTracer(e, t, r)) !== null && a !== void 0 ? a : new n.ProxyTracer(this, e, t, r);
                }
                getDelegate() {
                    var e;
                    return (e = this._delegate) !== null && e !== void 0 ? e : o;
                }
                setDelegate(e) {
                    this._delegate = e;
                }
                getDelegateTracer(e, t, r) {
                    var n;
                    return (n = this._delegate) === null || n === void 0 ? void 0 : n.getTracer(e, t, r);
                }
            }
            t.ProxyTracerProvider = ProxyTracerProvider;
        },
        996: (e, t)=>{
            Object.defineProperty(t, "__esModule", {
                value: true
            });
            t.SamplingDecision = void 0;
            var r;
            (function(e) {
                e[e["NOT_RECORD"] = 0] = "NOT_RECORD";
                e[e["RECORD"] = 1] = "RECORD";
                e[e["RECORD_AND_SAMPLED"] = 2] = "RECORD_AND_SAMPLED";
            })(r = t.SamplingDecision || (t.SamplingDecision = {}));
        },
        607: (e, t, r)=>{
            Object.defineProperty(t, "__esModule", {
                value: true
            });
            t.getSpanContext = t.setSpanContext = t.deleteSpan = t.setSpan = t.getActiveSpan = t.getSpan = void 0;
            const n = r(780);
            const a = r(403);
            const o = r(491);
            const i = (0, n.createContextKey)("OpenTelemetry Context Key SPAN");
            function getSpan(e) {
                return e.getValue(i) || undefined;
            }
            t.getSpan = getSpan;
            function getActiveSpan() {
                return getSpan(o.ContextAPI.getInstance().active());
            }
            t.getActiveSpan = getActiveSpan;
            function setSpan(e, t) {
                return e.setValue(i, t);
            }
            t.setSpan = setSpan;
            function deleteSpan(e) {
                return e.deleteValue(i);
            }
            t.deleteSpan = deleteSpan;
            function setSpanContext(e, t) {
                return setSpan(e, new a.NonRecordingSpan(t));
            }
            t.setSpanContext = setSpanContext;
            function getSpanContext(e) {
                var t;
                return (t = getSpan(e)) === null || t === void 0 ? void 0 : t.spanContext();
            }
            t.getSpanContext = getSpanContext;
        },
        325: (e, t, r)=>{
            Object.defineProperty(t, "__esModule", {
                value: true
            });
            t.TraceStateImpl = void 0;
            const n = r(564);
            const a = 32;
            const o = 512;
            const i = ",";
            const c = "=";
            class TraceStateImpl {
                constructor(e){
                    this._internalState = new Map;
                    if (e) this._parse(e);
                }
                set(e, t) {
                    const r = this._clone();
                    if (r._internalState.has(e)) {
                        r._internalState.delete(e);
                    }
                    r._internalState.set(e, t);
                    return r;
                }
                unset(e) {
                    const t = this._clone();
                    t._internalState.delete(e);
                    return t;
                }
                get(e) {
                    return this._internalState.get(e);
                }
                serialize() {
                    return this._keys().reduce((e, t)=>{
                        e.push(t + c + this.get(t));
                        return e;
                    }, []).join(i);
                }
                _parse(e) {
                    if (e.length > o) return;
                    this._internalState = e.split(i).reverse().reduce((e, t)=>{
                        const r = t.trim();
                        const a = r.indexOf(c);
                        if (a !== -1) {
                            const o = r.slice(0, a);
                            const i = r.slice(a + 1, t.length);
                            if ((0, n.validateKey)(o) && (0, n.validateValue)(i)) {
                                e.set(o, i);
                            } else {}
                        }
                        return e;
                    }, new Map);
                    if (this._internalState.size > a) {
                        this._internalState = new Map(Array.from(this._internalState.entries()).reverse().slice(0, a));
                    }
                }
                _keys() {
                    return Array.from(this._internalState.keys()).reverse();
                }
                _clone() {
                    const e = new TraceStateImpl;
                    e._internalState = new Map(this._internalState);
                    return e;
                }
            }
            t.TraceStateImpl = TraceStateImpl;
        },
        564: (e, t)=>{
            Object.defineProperty(t, "__esModule", {
                value: true
            });
            t.validateValue = t.validateKey = void 0;
            const r = "[_0-9a-z-*/]";
            const n = `[a-z]${r}{0,255}`;
            const a = `[a-z0-9]${r}{0,240}@[a-z]${r}{0,13}`;
            const o = new RegExp(`^(?:${n}|${a})$`);
            const i = /^[ -~]{0,255}[!-~]$/;
            const c = /,|=/;
            function validateKey(e) {
                return o.test(e);
            }
            t.validateKey = validateKey;
            function validateValue(e) {
                return i.test(e) && !c.test(e);
            }
            t.validateValue = validateValue;
        },
        98: (e, t, r)=>{
            Object.defineProperty(t, "__esModule", {
                value: true
            });
            t.createTraceState = void 0;
            const n = r(325);
            function createTraceState(e) {
                return new n.TraceStateImpl(e);
            }
            t.createTraceState = createTraceState;
        },
        476: (e, t, r)=>{
            Object.defineProperty(t, "__esModule", {
                value: true
            });
            t.INVALID_SPAN_CONTEXT = t.INVALID_TRACEID = t.INVALID_SPANID = void 0;
            const n = r(475);
            t.INVALID_SPANID = "0000000000000000";
            t.INVALID_TRACEID = "00000000000000000000000000000000";
            t.INVALID_SPAN_CONTEXT = {
                traceId: t.INVALID_TRACEID,
                spanId: t.INVALID_SPANID,
                traceFlags: n.TraceFlags.NONE
            };
        },
        357: (e, t)=>{
            Object.defineProperty(t, "__esModule", {
                value: true
            });
            t.SpanKind = void 0;
            var r;
            (function(e) {
                e[e["INTERNAL"] = 0] = "INTERNAL";
                e[e["SERVER"] = 1] = "SERVER";
                e[e["CLIENT"] = 2] = "CLIENT";
                e[e["PRODUCER"] = 3] = "PRODUCER";
                e[e["CONSUMER"] = 4] = "CONSUMER";
            })(r = t.SpanKind || (t.SpanKind = {}));
        },
        139: (e, t, r)=>{
            Object.defineProperty(t, "__esModule", {
                value: true
            });
            t.wrapSpanContext = t.isSpanContextValid = t.isValidSpanId = t.isValidTraceId = void 0;
            const n = r(476);
            const a = r(403);
            const o = /^([0-9a-f]{32})$/i;
            const i = /^[0-9a-f]{16}$/i;
            function isValidTraceId(e) {
                return o.test(e) && e !== n.INVALID_TRACEID;
            }
            t.isValidTraceId = isValidTraceId;
            function isValidSpanId(e) {
                return i.test(e) && e !== n.INVALID_SPANID;
            }
            t.isValidSpanId = isValidSpanId;
            function isSpanContextValid(e) {
                return isValidTraceId(e.traceId) && isValidSpanId(e.spanId);
            }
            t.isSpanContextValid = isSpanContextValid;
            function wrapSpanContext(e) {
                return new a.NonRecordingSpan(e);
            }
            t.wrapSpanContext = wrapSpanContext;
        },
        847: (e, t)=>{
            Object.defineProperty(t, "__esModule", {
                value: true
            });
            t.SpanStatusCode = void 0;
            var r;
            (function(e) {
                e[e["UNSET"] = 0] = "UNSET";
                e[e["OK"] = 1] = "OK";
                e[e["ERROR"] = 2] = "ERROR";
            })(r = t.SpanStatusCode || (t.SpanStatusCode = {}));
        },
        475: (e, t)=>{
            Object.defineProperty(t, "__esModule", {
                value: true
            });
            t.TraceFlags = void 0;
            var r;
            (function(e) {
                e[e["NONE"] = 0] = "NONE";
                e[e["SAMPLED"] = 1] = "SAMPLED";
            })(r = t.TraceFlags || (t.TraceFlags = {}));
        },
        521: (e, t)=>{
            Object.defineProperty(t, "__esModule", {
                value: true
            });
            t.VERSION = void 0;
            t.VERSION = "1.6.0";
        }
    };
    var t = {};
    function __nccwpck_require__(r) {
        var n = t[r];
        if (n !== undefined) {
            return n.exports;
        }
        var a = t[r] = {
            exports: {}
        };
        var o = true;
        try {
            e[r].call(a.exports, a, a.exports, __nccwpck_require__);
            o = false;
        } finally{
            if (o) delete t[r];
        }
        return a.exports;
    }
    if (typeof __nccwpck_require__ !== "undefined") __nccwpck_require__.ab = __dirname + "/";
    var r = {};
    (()=>{
        var e = r;
        Object.defineProperty(e, "__esModule", {
            value: true
        });
        e.trace = e.propagation = e.metrics = e.diag = e.context = e.INVALID_SPAN_CONTEXT = e.INVALID_TRACEID = e.INVALID_SPANID = e.isValidSpanId = e.isValidTraceId = e.isSpanContextValid = e.createTraceState = e.TraceFlags = e.SpanStatusCode = e.SpanKind = e.SamplingDecision = e.ProxyTracerProvider = e.ProxyTracer = e.defaultTextMapSetter = e.defaultTextMapGetter = e.ValueType = e.createNoopMeter = e.DiagLogLevel = e.DiagConsoleLogger = e.ROOT_CONTEXT = e.createContextKey = e.baggageEntryMetadataFromString = void 0;
        var t = __nccwpck_require__(369);
        Object.defineProperty(e, "baggageEntryMetadataFromString", {
            enumerable: true,
            get: function() {
                return t.baggageEntryMetadataFromString;
            }
        });
        var n = __nccwpck_require__(780);
        Object.defineProperty(e, "createContextKey", {
            enumerable: true,
            get: function() {
                return n.createContextKey;
            }
        });
        Object.defineProperty(e, "ROOT_CONTEXT", {
            enumerable: true,
            get: function() {
                return n.ROOT_CONTEXT;
            }
        });
        var a = __nccwpck_require__(972);
        Object.defineProperty(e, "DiagConsoleLogger", {
            enumerable: true,
            get: function() {
                return a.DiagConsoleLogger;
            }
        });
        var o = __nccwpck_require__(957);
        Object.defineProperty(e, "DiagLogLevel", {
            enumerable: true,
            get: function() {
                return o.DiagLogLevel;
            }
        });
        var i = __nccwpck_require__(102);
        Object.defineProperty(e, "createNoopMeter", {
            enumerable: true,
            get: function() {
                return i.createNoopMeter;
            }
        });
        var c = __nccwpck_require__(901);
        Object.defineProperty(e, "ValueType", {
            enumerable: true,
            get: function() {
                return c.ValueType;
            }
        });
        var s = __nccwpck_require__(194);
        Object.defineProperty(e, "defaultTextMapGetter", {
            enumerable: true,
            get: function() {
                return s.defaultTextMapGetter;
            }
        });
        Object.defineProperty(e, "defaultTextMapSetter", {
            enumerable: true,
            get: function() {
                return s.defaultTextMapSetter;
            }
        });
        var u = __nccwpck_require__(125);
        Object.defineProperty(e, "ProxyTracer", {
            enumerable: true,
            get: function() {
                return u.ProxyTracer;
            }
        });
        var l = __nccwpck_require__(846);
        Object.defineProperty(e, "ProxyTracerProvider", {
            enumerable: true,
            get: function() {
                return l.ProxyTracerProvider;
            }
        });
        var g = __nccwpck_require__(996);
        Object.defineProperty(e, "SamplingDecision", {
            enumerable: true,
            get: function() {
                return g.SamplingDecision;
            }
        });
        var p = __nccwpck_require__(357);
        Object.defineProperty(e, "SpanKind", {
            enumerable: true,
            get: function() {
                return p.SpanKind;
            }
        });
        var d = __nccwpck_require__(847);
        Object.defineProperty(e, "SpanStatusCode", {
            enumerable: true,
            get: function() {
                return d.SpanStatusCode;
            }
        });
        var _ = __nccwpck_require__(475);
        Object.defineProperty(e, "TraceFlags", {
            enumerable: true,
            get: function() {
                return _.TraceFlags;
            }
        });
        var f = __nccwpck_require__(98);
        Object.defineProperty(e, "createTraceState", {
            enumerable: true,
            get: function() {
                return f.createTraceState;
            }
        });
        var b = __nccwpck_require__(139);
        Object.defineProperty(e, "isSpanContextValid", {
            enumerable: true,
            get: function() {
                return b.isSpanContextValid;
            }
        });
        Object.defineProperty(e, "isValidTraceId", {
            enumerable: true,
            get: function() {
                return b.isValidTraceId;
            }
        });
        Object.defineProperty(e, "isValidSpanId", {
            enumerable: true,
            get: function() {
                return b.isValidSpanId;
            }
        });
        var v = __nccwpck_require__(476);
        Object.defineProperty(e, "INVALID_SPANID", {
            enumerable: true,
            get: function() {
                return v.INVALID_SPANID;
            }
        });
        Object.defineProperty(e, "INVALID_TRACEID", {
            enumerable: true,
            get: function() {
                return v.INVALID_TRACEID;
            }
        });
        Object.defineProperty(e, "INVALID_SPAN_CONTEXT", {
            enumerable: true,
            get: function() {
                return v.INVALID_SPAN_CONTEXT;
            }
        });
        const O = __nccwpck_require__(67);
        Object.defineProperty(e, "context", {
            enumerable: true,
            get: function() {
                return O.context;
            }
        });
        const P = __nccwpck_require__(506);
        Object.defineProperty(e, "diag", {
            enumerable: true,
            get: function() {
                return P.diag;
            }
        });
        const N = __nccwpck_require__(886);
        Object.defineProperty(e, "metrics", {
            enumerable: true,
            get: function() {
                return N.metrics;
            }
        });
        const S = __nccwpck_require__(939);
        Object.defineProperty(e, "propagation", {
            enumerable: true,
            get: function() {
                return S.propagation;
            }
        });
        const C = __nccwpck_require__(845);
        Object.defineProperty(e, "trace", {
            enumerable: true,
            get: function() {
                return C.trace;
            }
        });
        e["default"] = {
            context: O.context,
            diag: P.diag,
            metrics: N.metrics,
            propagation: S.propagation,
            trace: C.trace
        };
    })();
    module.exports = r;
})();
}}),
"[project]/node_modules/@opentelemetry/api-logs/build/esm/platform/browser/globalThis.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ // Updates to this file should also be replicated to @opentelemetry/api and
// @opentelemetry/core too.
/**
 * - globalThis (New standard)
 * - self (Will return the current window instance for supported browsers)
 * - window (fallback for older browser implementations)
 * - global (NodeJS implementation)
 * - <object> (When all else fails)
 */ /** only globals that common to node and browsers are allowed */ // eslint-disable-next-line node/no-unsupported-features/es-builtins, no-undef
__turbopack_context__.s({
    "_globalThis": (()=>_globalThis)
});
const _globalThis = typeof globalThis === 'object' ? globalThis : typeof self === 'object' ? self : ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : typeof global === 'object' ? global : {}; //# sourceMappingURL=globalThis.js.map
}}),
"[project]/node_modules/@opentelemetry/api-logs/build/esm/internal/global-utils.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "API_BACKWARDS_COMPATIBILITY_VERSION": (()=>API_BACKWARDS_COMPATIBILITY_VERSION),
    "GLOBAL_LOGS_API_KEY": (()=>GLOBAL_LOGS_API_KEY),
    "_global": (()=>_global),
    "makeGetter": (()=>makeGetter)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$platform$2f$browser$2f$globalThis$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/api-logs/build/esm/platform/browser/globalThis.js [instrumentation] (ecmascript)");
;
const GLOBAL_LOGS_API_KEY = Symbol.for('io.opentelemetry.js.api.logs');
const _global = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$platform$2f$browser$2f$globalThis$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["_globalThis"];
function makeGetter(requiredVersion, instance, fallback) {
    return (version)=>version === requiredVersion ? instance : fallback;
}
const API_BACKWARDS_COMPATIBILITY_VERSION = 1; //# sourceMappingURL=global-utils.js.map
}}),
"[project]/node_modules/@opentelemetry/api-logs/build/esm/NoopLogger.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "NOOP_LOGGER": (()=>NOOP_LOGGER),
    "NoopLogger": (()=>NoopLogger)
});
class NoopLogger {
    emit(_logRecord) {}
}
const NOOP_LOGGER = new NoopLogger(); //# sourceMappingURL=NoopLogger.js.map
}}),
"[project]/node_modules/@opentelemetry/api-logs/build/esm/NoopLoggerProvider.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "NOOP_LOGGER_PROVIDER": (()=>NOOP_LOGGER_PROVIDER),
    "NoopLoggerProvider": (()=>NoopLoggerProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$NoopLogger$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/api-logs/build/esm/NoopLogger.js [instrumentation] (ecmascript)");
;
class NoopLoggerProvider {
    getLogger(_name, _version, _options) {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$NoopLogger$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NoopLogger"]();
    }
}
const NOOP_LOGGER_PROVIDER = new NoopLoggerProvider(); //# sourceMappingURL=NoopLoggerProvider.js.map
}}),
"[project]/node_modules/@opentelemetry/api-logs/build/esm/ProxyLogger.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "ProxyLogger": (()=>ProxyLogger)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$NoopLogger$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/api-logs/build/esm/NoopLogger.js [instrumentation] (ecmascript)");
;
class ProxyLogger {
    constructor(_provider, name, version, options){
        this._provider = _provider;
        this.name = name;
        this.version = version;
        this.options = options;
    }
    /**
     * Emit a log record. This method should only be used by log appenders.
     *
     * @param logRecord
     */ emit(logRecord) {
        this._getLogger().emit(logRecord);
    }
    /**
     * Try to get a logger from the proxy logger provider.
     * If the proxy logger provider has no delegate, return a noop logger.
     */ _getLogger() {
        if (this._delegate) {
            return this._delegate;
        }
        const logger = this._provider.getDelegateLogger(this.name, this.version, this.options);
        if (!logger) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$NoopLogger$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NOOP_LOGGER"];
        }
        this._delegate = logger;
        return this._delegate;
    }
} //# sourceMappingURL=ProxyLogger.js.map
}}),
"[project]/node_modules/@opentelemetry/api-logs/build/esm/ProxyLoggerProvider.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "ProxyLoggerProvider": (()=>ProxyLoggerProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$NoopLoggerProvider$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/api-logs/build/esm/NoopLoggerProvider.js [instrumentation] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$ProxyLogger$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/api-logs/build/esm/ProxyLogger.js [instrumentation] (ecmascript)");
;
;
class ProxyLoggerProvider {
    getLogger(name, version, options) {
        var _a;
        return (_a = this.getDelegateLogger(name, version, options)) !== null && _a !== void 0 ? _a : new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$ProxyLogger$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ProxyLogger"](this, name, version, options);
    }
    getDelegate() {
        var _a;
        return (_a = this._delegate) !== null && _a !== void 0 ? _a : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$NoopLoggerProvider$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NOOP_LOGGER_PROVIDER"];
    }
    /**
     * Set the delegate logger provider
     */ setDelegate(delegate) {
        this._delegate = delegate;
    }
    getDelegateLogger(name, version, options) {
        var _a;
        return (_a = this._delegate) === null || _a === void 0 ? void 0 : _a.getLogger(name, version, options);
    }
} //# sourceMappingURL=ProxyLoggerProvider.js.map
}}),
"[project]/node_modules/@opentelemetry/api-logs/build/esm/api/logs.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "LogsAPI": (()=>LogsAPI)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$internal$2f$global$2d$utils$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/api-logs/build/esm/internal/global-utils.js [instrumentation] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$NoopLoggerProvider$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/api-logs/build/esm/NoopLoggerProvider.js [instrumentation] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$ProxyLoggerProvider$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/api-logs/build/esm/ProxyLoggerProvider.js [instrumentation] (ecmascript)");
;
;
;
class LogsAPI {
    constructor(){
        this._proxyLoggerProvider = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$ProxyLoggerProvider$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ProxyLoggerProvider"]();
    }
    static getInstance() {
        if (!this._instance) {
            this._instance = new LogsAPI();
        }
        return this._instance;
    }
    setGlobalLoggerProvider(provider) {
        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$internal$2f$global$2d$utils$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["_global"][__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$internal$2f$global$2d$utils$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["GLOBAL_LOGS_API_KEY"]]) {
            return this.getLoggerProvider();
        }
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$internal$2f$global$2d$utils$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["_global"][__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$internal$2f$global$2d$utils$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["GLOBAL_LOGS_API_KEY"]] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$internal$2f$global$2d$utils$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["makeGetter"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$internal$2f$global$2d$utils$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["API_BACKWARDS_COMPATIBILITY_VERSION"], provider, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$NoopLoggerProvider$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NOOP_LOGGER_PROVIDER"]);
        this._proxyLoggerProvider.setDelegate(provider);
        return provider;
    }
    /**
     * Returns the global logger provider.
     *
     * @returns LoggerProvider
     */ getLoggerProvider() {
        var _a, _b;
        return (_b = (_a = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$internal$2f$global$2d$utils$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["_global"][__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$internal$2f$global$2d$utils$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["GLOBAL_LOGS_API_KEY"]]) === null || _a === void 0 ? void 0 : _a.call(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$internal$2f$global$2d$utils$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["_global"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$internal$2f$global$2d$utils$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["API_BACKWARDS_COMPATIBILITY_VERSION"])) !== null && _b !== void 0 ? _b : this._proxyLoggerProvider;
    }
    /**
     * Returns a logger from the global logger provider.
     *
     * @returns Logger
     */ getLogger(name, version, options) {
        return this.getLoggerProvider().getLogger(name, version, options);
    }
    /** Remove the global logger provider */ disable() {
        delete __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$internal$2f$global$2d$utils$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["_global"][__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$internal$2f$global$2d$utils$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["GLOBAL_LOGS_API_KEY"]];
        this._proxyLoggerProvider = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$ProxyLoggerProvider$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ProxyLoggerProvider"]();
    }
} //# sourceMappingURL=logs.js.map
}}),
"[project]/node_modules/@opentelemetry/api-logs/build/esm/index.js [instrumentation] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "logs": (()=>logs)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$api$2f$logs$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/api-logs/build/esm/api/logs.js [instrumentation] (ecmascript)");
;
;
;
;
;
;
const logs = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$api$2f$logs$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["LogsAPI"].getInstance(); //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@opentelemetry/api-logs/build/esm/index.js [instrumentation] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/api-logs/build/esm/index.js [instrumentation] (ecmascript) <locals>");
}}),
"[project]/node_modules/@opentelemetry/api-logs/build/esm/types/LogRecord.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "SeverityNumber": (()=>SeverityNumber)
});
var SeverityNumber;
(function(SeverityNumber) {
    SeverityNumber[SeverityNumber["UNSPECIFIED"] = 0] = "UNSPECIFIED";
    SeverityNumber[SeverityNumber["TRACE"] = 1] = "TRACE";
    SeverityNumber[SeverityNumber["TRACE2"] = 2] = "TRACE2";
    SeverityNumber[SeverityNumber["TRACE3"] = 3] = "TRACE3";
    SeverityNumber[SeverityNumber["TRACE4"] = 4] = "TRACE4";
    SeverityNumber[SeverityNumber["DEBUG"] = 5] = "DEBUG";
    SeverityNumber[SeverityNumber["DEBUG2"] = 6] = "DEBUG2";
    SeverityNumber[SeverityNumber["DEBUG3"] = 7] = "DEBUG3";
    SeverityNumber[SeverityNumber["DEBUG4"] = 8] = "DEBUG4";
    SeverityNumber[SeverityNumber["INFO"] = 9] = "INFO";
    SeverityNumber[SeverityNumber["INFO2"] = 10] = "INFO2";
    SeverityNumber[SeverityNumber["INFO3"] = 11] = "INFO3";
    SeverityNumber[SeverityNumber["INFO4"] = 12] = "INFO4";
    SeverityNumber[SeverityNumber["WARN"] = 13] = "WARN";
    SeverityNumber[SeverityNumber["WARN2"] = 14] = "WARN2";
    SeverityNumber[SeverityNumber["WARN3"] = 15] = "WARN3";
    SeverityNumber[SeverityNumber["WARN4"] = 16] = "WARN4";
    SeverityNumber[SeverityNumber["ERROR"] = 17] = "ERROR";
    SeverityNumber[SeverityNumber["ERROR2"] = 18] = "ERROR2";
    SeverityNumber[SeverityNumber["ERROR3"] = 19] = "ERROR3";
    SeverityNumber[SeverityNumber["ERROR4"] = 20] = "ERROR4";
    SeverityNumber[SeverityNumber["FATAL"] = 21] = "FATAL";
    SeverityNumber[SeverityNumber["FATAL2"] = 22] = "FATAL2";
    SeverityNumber[SeverityNumber["FATAL3"] = 23] = "FATAL3";
    SeverityNumber[SeverityNumber["FATAL4"] = 24] = "FATAL4";
})(SeverityNumber || (SeverityNumber = {})); //# sourceMappingURL=LogRecord.js.map
}}),
"[project]/node_modules/@opentelemetry/api-logs/build/esm/index.js [instrumentation] (ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "NOOP_LOGGER": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$NoopLogger$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NOOP_LOGGER"]),
    "NOOP_LOGGER_PROVIDER": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$NoopLoggerProvider$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NOOP_LOGGER_PROVIDER"]),
    "NoopLogger": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$NoopLogger$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NoopLogger"]),
    "NoopLoggerProvider": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$NoopLoggerProvider$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NoopLoggerProvider"]),
    "ProxyLogger": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$ProxyLogger$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ProxyLogger"]),
    "ProxyLoggerProvider": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$ProxyLoggerProvider$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ProxyLoggerProvider"]),
    "SeverityNumber": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$types$2f$LogRecord$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SeverityNumber"]),
    "logs": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$locals$3e$__["logs"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$types$2f$LogRecord$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/api-logs/build/esm/types/LogRecord.js [instrumentation] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$NoopLogger$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/api-logs/build/esm/NoopLogger.js [instrumentation] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$NoopLoggerProvider$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/api-logs/build/esm/NoopLoggerProvider.js [instrumentation] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$ProxyLogger$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/api-logs/build/esm/ProxyLogger.js [instrumentation] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$ProxyLoggerProvider$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/api-logs/build/esm/ProxyLoggerProvider.js [instrumentation] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/api-logs/build/esm/index.js [instrumentation] (ecmascript) <locals>");
}}),
"[project]/node_modules/@opentelemetry/api-logs/build/esm/index.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "NOOP_LOGGER": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["NOOP_LOGGER"]),
    "NOOP_LOGGER_PROVIDER": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["NOOP_LOGGER_PROVIDER"]),
    "NoopLogger": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["NoopLogger"]),
    "NoopLoggerProvider": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["NoopLoggerProvider"]),
    "ProxyLogger": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["ProxyLogger"]),
    "ProxyLoggerProvider": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["ProxyLoggerProvider"]),
    "SeverityNumber": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SeverityNumber"]),
    "logs": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["logs"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/api-logs/build/esm/index.js [instrumentation] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/api-logs/build/esm/index.js [instrumentation] (ecmascript) <exports>");
}}),
"[project]/node_modules/@vercel/otel/dist/edge/index.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "FetchInstrumentation": (()=>te),
    "OTLPHttpJsonTraceExporter": (()=>Qt),
    "OTLPHttpProtoTraceExporter": (()=>Bt),
    "registerOTel": (()=>RT)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f40$opentelemetry$2f$api$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/@opentelemetry/api/index.js [instrumentation] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$async_hooks__$5b$external$5d$__$28$node$3a$async_hooks$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/node:async_hooks [external] (node:async_hooks, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$events__$5b$external$5d$__$28$node$3a$events$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/node:events [external] (node:events, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/api-logs/build/esm/index.js [instrumentation] (ecmascript)");
if (globalThis.performance === undefined) {
    globalThis.performance = {
        timeOrigin: 0,
        now: ()=>Date.now()
    };
}
var Nu = Object.create;
var xr = Object.defineProperty;
var wu = Object.getOwnPropertyDescriptor;
var Mu = Object.getOwnPropertyNames;
var xu = Object.getPrototypeOf, Du = Object.prototype.hasOwnProperty;
var ci = ((e)=>("TURBOPACK compile-time value", "function") < "u" ? ("TURBOPACK member replacement", __turbopack_context__.z) : typeof Proxy < "u" ? new Proxy(e, {
        get: (t, r)=>(("TURBOPACK compile-time value", "function") < "u" ? ("TURBOPACK member replacement", __turbopack_context__.z) : t)[r]
    }) : e)(function(e) {
    if (("TURBOPACK compile-time value", "function") < "u") return ("TURBOPACK member replacement", __turbopack_context__.z).apply(this, arguments);
    throw Error('Dynamic require of "' + e + '" is not supported');
});
var Dr = (e, t)=>()=>(e && (t = e(e = 0)), t);
var _ = (e, t)=>()=>(t || e((t = {
            exports: {}
        }).exports, t), t.exports);
var Se = (e, t, r, n)=>{
    if (t && typeof t == "object" || typeof t == "function") for (let o of Mu(t))!Du.call(e, o) && o !== r && xr(e, o, {
        get: ()=>t[o],
        enumerable: !(n = wu(t, o)) || n.enumerable
    });
    return e;
}, Y = (e, t, r)=>(Se(e, t, "default"), r && Se(r, t, "default")), v = (e, t, r)=>(r = e != null ? Nu(xu(e)) : {}, Se(t || !e || !e.__esModule ? xr(r, "default", {
        value: e,
        enumerable: !0
    }) : r, e)), w = (e)=>Se(xr({}, "__esModule", {
        value: !0
    }), e);
var l = {};
;
var E = Dr(()=>{
    Y(l, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f40$opentelemetry$2f$api$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__);
});
var ie = _((ft)=>{
    "use strict";
    Object.defineProperty(ft, "__esModule", {
        value: !0
    });
    ft.isTracingSuppressed = ft.unsuppressTracing = ft.suppressTracing = void 0;
    var Uu = (E(), w(l)), Ur = (0, Uu.createContextKey)("OpenTelemetry SDK Context Key SUPPRESS_TRACING");
    function Bu(e) {
        return e.setValue(Ur, !0);
    }
    ft.suppressTracing = Bu;
    function Gu(e) {
        return e.deleteValue(Ur);
    }
    ft.unsuppressTracing = Gu;
    function Vu(e) {
        return e.getValue(Ur) === !0;
    }
    ft.isTracingSuppressed = Vu;
});
var Br = _((B)=>{
    "use strict";
    Object.defineProperty(B, "__esModule", {
        value: !0
    });
    B.BAGGAGE_MAX_TOTAL_LENGTH = B.BAGGAGE_MAX_PER_NAME_VALUE_PAIRS = B.BAGGAGE_MAX_NAME_VALUE_PAIRS = B.BAGGAGE_HEADER = B.BAGGAGE_ITEMS_SEPARATOR = B.BAGGAGE_PROPERTIES_SEPARATOR = B.BAGGAGE_KEY_PAIR_SEPARATOR = void 0;
    B.BAGGAGE_KEY_PAIR_SEPARATOR = "=";
    B.BAGGAGE_PROPERTIES_SEPARATOR = ";";
    B.BAGGAGE_ITEMS_SEPARATOR = ",";
    B.BAGGAGE_HEADER = "baggage";
    B.BAGGAGE_MAX_NAME_VALUE_PAIRS = 180;
    B.BAGGAGE_MAX_PER_NAME_VALUE_PAIRS = 4096;
    B.BAGGAGE_MAX_TOTAL_LENGTH = 8192;
});
var Gr = _((rt)=>{
    "use strict";
    Object.defineProperty(rt, "__esModule", {
        value: !0
    });
    rt.parseKeyPairsIntoRecord = rt.parsePairKeyValue = rt.getKeyPairs = rt.serializeKeyPairs = void 0;
    var Hu = (E(), w(l)), Lt = Br();
    function Fu(e) {
        return e.reduce((t, r)=>{
            let n = `${t}${t !== "" ? Lt.BAGGAGE_ITEMS_SEPARATOR : ""}${r}`;
            return n.length > Lt.BAGGAGE_MAX_TOTAL_LENGTH ? t : n;
        }, "");
    }
    rt.serializeKeyPairs = Fu;
    function ju(e) {
        return e.getAllEntries().map(([t, r])=>{
            let n = `${encodeURIComponent(t)}=${encodeURIComponent(r.value)}`;
            return r.metadata !== void 0 && (n += Lt.BAGGAGE_PROPERTIES_SEPARATOR + r.metadata.toString()), n;
        });
    }
    rt.getKeyPairs = ju;
    function li(e) {
        let t = e.split(Lt.BAGGAGE_PROPERTIES_SEPARATOR);
        if (t.length <= 0) return;
        let r = t.shift();
        if (!r) return;
        let n = r.indexOf(Lt.BAGGAGE_KEY_PAIR_SEPARATOR);
        if (n <= 0) return;
        let o = decodeURIComponent(r.substring(0, n).trim()), i = decodeURIComponent(r.substring(n + 1).trim()), s;
        return t.length > 0 && (s = (0, Hu.baggageEntryMetadataFromString)(t.join(Lt.BAGGAGE_PROPERTIES_SEPARATOR))), {
            key: o,
            value: i,
            metadata: s
        };
    }
    rt.parsePairKeyValue = li;
    function ku(e) {
        return typeof e != "string" || e.length === 0 ? {} : e.split(Lt.BAGGAGE_ITEMS_SEPARATOR).map((t)=>li(t)).filter((t)=>t !== void 0 && t.value.length > 0).reduce((t, r)=>(t[r.key] = r.value, t), {});
    }
    rt.parseKeyPairsIntoRecord = ku;
});
var fi = _((ge)=>{
    "use strict";
    Object.defineProperty(ge, "__esModule", {
        value: !0
    });
    ge.W3CBaggagePropagator = void 0;
    var Vr = (E(), w(l)), qu = ie(), Pt = Br(), Hr = Gr(), Fr = class {
        inject(t, r, n) {
            let o = Vr.propagation.getBaggage(t);
            if (!o || (0, qu.isTracingSuppressed)(t)) return;
            let i = (0, Hr.getKeyPairs)(o).filter((u)=>u.length <= Pt.BAGGAGE_MAX_PER_NAME_VALUE_PAIRS).slice(0, Pt.BAGGAGE_MAX_NAME_VALUE_PAIRS), s = (0, Hr.serializeKeyPairs)(i);
            s.length > 0 && n.set(r, Pt.BAGGAGE_HEADER, s);
        }
        extract(t, r, n) {
            let o = n.get(r, Pt.BAGGAGE_HEADER), i = Array.isArray(o) ? o.join(Pt.BAGGAGE_ITEMS_SEPARATOR) : o;
            if (!i) return t;
            let s = {};
            return i.length === 0 || (i.split(Pt.BAGGAGE_ITEMS_SEPARATOR).forEach((c)=>{
                let a = (0, Hr.parsePairKeyValue)(c);
                if (a) {
                    let f = {
                        value: a.value
                    };
                    a.metadata && (f.metadata = a.metadata), s[a.key] = f;
                }
            }), Object.entries(s).length === 0) ? t : Vr.propagation.setBaggage(t, Vr.propagation.createBaggage(s));
        }
        fields() {
            return [
                Pt.BAGGAGE_HEADER
            ];
        }
    };
    ge.W3CBaggagePropagator = Fr;
});
var pi = _((ye)=>{
    "use strict";
    Object.defineProperty(ye, "__esModule", {
        value: !0
    });
    ye.AnchoredClock = void 0;
    var jr = class {
        constructor(t, r){
            this._monotonicClock = r, this._epochMillis = t.now(), this._performanceMillis = r.now();
        }
        now() {
            let t = this._monotonicClock.now() - this._performanceMillis;
            return this._epochMillis + t;
        }
    };
    ye.AnchoredClock = jr;
});
var mi = _((pt)=>{
    "use strict";
    Object.defineProperty(pt, "__esModule", {
        value: !0
    });
    pt.isAttributeValue = pt.isAttributeKey = pt.sanitizeAttributes = void 0;
    var di = (E(), w(l));
    function Xu(e) {
        let t = {};
        if (typeof e != "object" || e == null) return t;
        for (let [r, n] of Object.entries(e)){
            if (!_i(r)) {
                di.diag.warn(`Invalid attribute key: ${r}`);
                continue;
            }
            if (!hi(n)) {
                di.diag.warn(`Invalid attribute value set for key: ${r}`);
                continue;
            }
            Array.isArray(n) ? t[r] = n.slice() : t[r] = n;
        }
        return t;
    }
    pt.sanitizeAttributes = Xu;
    function _i(e) {
        return typeof e == "string" && e.length > 0;
    }
    pt.isAttributeKey = _i;
    function hi(e) {
        return e == null ? !0 : Array.isArray(e) ? Ku(e) : Ei(e);
    }
    pt.isAttributeValue = hi;
    function Ku(e) {
        let t;
        for (let r of e)if (r != null) {
            if (!t) {
                if (Ei(r)) {
                    t = typeof r;
                    continue;
                }
                return !1;
            }
            if (typeof r !== t) return !1;
        }
        return !0;
    }
    function Ei(e) {
        switch(typeof e){
            case "number":
            case "boolean":
            case "string":
                return !0;
        }
        return !1;
    }
});
var kr = _((ve)=>{
    "use strict";
    Object.defineProperty(ve, "__esModule", {
        value: !0
    });
    ve.loggingErrorHandler = void 0;
    var Wu = (E(), w(l));
    function Yu() {
        return (e)=>{
            Wu.diag.error(zu(e));
        };
    }
    ve.loggingErrorHandler = Yu;
    function zu(e) {
        return typeof e == "string" ? e : JSON.stringify($u(e));
    }
    function $u(e) {
        let t = {}, r = e;
        for(; r !== null;)Object.getOwnPropertyNames(r).forEach((n)=>{
            if (t[n]) return;
            let o = r[n];
            o && (t[n] = String(o));
        }), r = Object.getPrototypeOf(r);
        return t;
    }
});
var qr = _((Vt)=>{
    "use strict";
    Object.defineProperty(Vt, "__esModule", {
        value: !0
    });
    Vt.globalErrorHandler = Vt.setGlobalErrorHandler = void 0;
    var Qu = kr(), Ti = (0, Qu.loggingErrorHandler)();
    function Zu(e) {
        Ti = e;
    }
    Vt.setGlobalErrorHandler = Zu;
    function Ju(e) {
        try {
            Ti(e);
        } catch  {}
    }
    Vt.globalErrorHandler = Ju;
});
var Xr = _((oe)=>{
    "use strict";
    Object.defineProperty(oe, "__esModule", {
        value: !0
    });
    oe.TracesSamplerValues = void 0;
    var tc;
    (function(e) {
        e.AlwaysOff = "always_off", e.AlwaysOn = "always_on", e.ParentBasedAlwaysOff = "parentbased_always_off", e.ParentBasedAlwaysOn = "parentbased_always_on", e.ParentBasedTraceIdRatio = "parentbased_traceidratio", e.TraceIdRatio = "traceidratio";
    })(tc = oe.TracesSamplerValues || (oe.TracesSamplerValues = {}));
});
var Re = _((Ae)=>{
    "use strict";
    Object.defineProperty(Ae, "__esModule", {
        value: !0
    });
    Ae._globalThis = void 0;
    Ae._globalThis = typeof globalThis == "object" ? globalThis : typeof self == "object" ? self : ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : typeof global == "object" ? global : {};
});
var Oe = _((L)=>{
    "use strict";
    Object.defineProperty(L, "__esModule", {
        value: !0
    });
    L.getEnvWithoutDefaults = L.parseEnvironment = L.DEFAULT_ENVIRONMENT = L.DEFAULT_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT = L.DEFAULT_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT = L.DEFAULT_ATTRIBUTE_COUNT_LIMIT = L.DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT = void 0;
    var dt = (E(), w(l)), ec = Xr(), rc = Re(), nc = ",", ic = [
        "OTEL_SDK_DISABLED"
    ];
    function oc(e) {
        return ic.indexOf(e) > -1;
    }
    var ac = [
        "OTEL_BSP_EXPORT_TIMEOUT",
        "OTEL_BSP_MAX_EXPORT_BATCH_SIZE",
        "OTEL_BSP_MAX_QUEUE_SIZE",
        "OTEL_BSP_SCHEDULE_DELAY",
        "OTEL_BLRP_EXPORT_TIMEOUT",
        "OTEL_BLRP_MAX_EXPORT_BATCH_SIZE",
        "OTEL_BLRP_MAX_QUEUE_SIZE",
        "OTEL_BLRP_SCHEDULE_DELAY",
        "OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT",
        "OTEL_ATTRIBUTE_COUNT_LIMIT",
        "OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT",
        "OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT",
        "OTEL_LOGRECORD_ATTRIBUTE_VALUE_LENGTH_LIMIT",
        "OTEL_LOGRECORD_ATTRIBUTE_COUNT_LIMIT",
        "OTEL_SPAN_EVENT_COUNT_LIMIT",
        "OTEL_SPAN_LINK_COUNT_LIMIT",
        "OTEL_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT",
        "OTEL_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT",
        "OTEL_EXPORTER_OTLP_TIMEOUT",
        "OTEL_EXPORTER_OTLP_TRACES_TIMEOUT",
        "OTEL_EXPORTER_OTLP_METRICS_TIMEOUT",
        "OTEL_EXPORTER_OTLP_LOGS_TIMEOUT",
        "OTEL_EXPORTER_JAEGER_AGENT_PORT"
    ];
    function sc(e) {
        return ac.indexOf(e) > -1;
    }
    var uc = [
        "OTEL_NO_PATCH_MODULES",
        "OTEL_PROPAGATORS"
    ];
    function cc(e) {
        return uc.indexOf(e) > -1;
    }
    L.DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT = 1 / 0;
    L.DEFAULT_ATTRIBUTE_COUNT_LIMIT = 128;
    L.DEFAULT_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT = 128;
    L.DEFAULT_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT = 128;
    L.DEFAULT_ENVIRONMENT = {
        OTEL_SDK_DISABLED: !1,
        CONTAINER_NAME: "",
        ECS_CONTAINER_METADATA_URI_V4: "",
        ECS_CONTAINER_METADATA_URI: "",
        HOSTNAME: "",
        KUBERNETES_SERVICE_HOST: "",
        NAMESPACE: "",
        OTEL_BSP_EXPORT_TIMEOUT: 3e4,
        OTEL_BSP_MAX_EXPORT_BATCH_SIZE: 512,
        OTEL_BSP_MAX_QUEUE_SIZE: 2048,
        OTEL_BSP_SCHEDULE_DELAY: 5e3,
        OTEL_BLRP_EXPORT_TIMEOUT: 3e4,
        OTEL_BLRP_MAX_EXPORT_BATCH_SIZE: 512,
        OTEL_BLRP_MAX_QUEUE_SIZE: 2048,
        OTEL_BLRP_SCHEDULE_DELAY: 5e3,
        OTEL_EXPORTER_JAEGER_AGENT_HOST: "",
        OTEL_EXPORTER_JAEGER_AGENT_PORT: 6832,
        OTEL_EXPORTER_JAEGER_ENDPOINT: "",
        OTEL_EXPORTER_JAEGER_PASSWORD: "",
        OTEL_EXPORTER_JAEGER_USER: "",
        OTEL_EXPORTER_OTLP_ENDPOINT: "",
        OTEL_EXPORTER_OTLP_TRACES_ENDPOINT: "",
        OTEL_EXPORTER_OTLP_METRICS_ENDPOINT: "",
        OTEL_EXPORTER_OTLP_LOGS_ENDPOINT: "",
        OTEL_EXPORTER_OTLP_HEADERS: "",
        OTEL_EXPORTER_OTLP_TRACES_HEADERS: "",
        OTEL_EXPORTER_OTLP_METRICS_HEADERS: "",
        OTEL_EXPORTER_OTLP_LOGS_HEADERS: "",
        OTEL_EXPORTER_OTLP_TIMEOUT: 1e4,
        OTEL_EXPORTER_OTLP_TRACES_TIMEOUT: 1e4,
        OTEL_EXPORTER_OTLP_METRICS_TIMEOUT: 1e4,
        OTEL_EXPORTER_OTLP_LOGS_TIMEOUT: 1e4,
        OTEL_EXPORTER_ZIPKIN_ENDPOINT: "http://localhost:9411/api/v2/spans",
        OTEL_LOG_LEVEL: dt.DiagLogLevel.INFO,
        OTEL_NO_PATCH_MODULES: [],
        OTEL_PROPAGATORS: [
            "tracecontext",
            "baggage"
        ],
        OTEL_RESOURCE_ATTRIBUTES: "",
        OTEL_SERVICE_NAME: "",
        OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT: L.DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT,
        OTEL_ATTRIBUTE_COUNT_LIMIT: L.DEFAULT_ATTRIBUTE_COUNT_LIMIT,
        OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT: L.DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT,
        OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT: L.DEFAULT_ATTRIBUTE_COUNT_LIMIT,
        OTEL_LOGRECORD_ATTRIBUTE_VALUE_LENGTH_LIMIT: L.DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT,
        OTEL_LOGRECORD_ATTRIBUTE_COUNT_LIMIT: L.DEFAULT_ATTRIBUTE_COUNT_LIMIT,
        OTEL_SPAN_EVENT_COUNT_LIMIT: 128,
        OTEL_SPAN_LINK_COUNT_LIMIT: 128,
        OTEL_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT: L.DEFAULT_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT,
        OTEL_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT: L.DEFAULT_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT,
        OTEL_TRACES_EXPORTER: "",
        OTEL_TRACES_SAMPLER: ec.TracesSamplerValues.ParentBasedAlwaysOn,
        OTEL_TRACES_SAMPLER_ARG: "",
        OTEL_LOGS_EXPORTER: "",
        OTEL_EXPORTER_OTLP_INSECURE: "",
        OTEL_EXPORTER_OTLP_TRACES_INSECURE: "",
        OTEL_EXPORTER_OTLP_METRICS_INSECURE: "",
        OTEL_EXPORTER_OTLP_LOGS_INSECURE: "",
        OTEL_EXPORTER_OTLP_CERTIFICATE: "",
        OTEL_EXPORTER_OTLP_TRACES_CERTIFICATE: "",
        OTEL_EXPORTER_OTLP_METRICS_CERTIFICATE: "",
        OTEL_EXPORTER_OTLP_LOGS_CERTIFICATE: "",
        OTEL_EXPORTER_OTLP_COMPRESSION: "",
        OTEL_EXPORTER_OTLP_TRACES_COMPRESSION: "",
        OTEL_EXPORTER_OTLP_METRICS_COMPRESSION: "",
        OTEL_EXPORTER_OTLP_LOGS_COMPRESSION: "",
        OTEL_EXPORTER_OTLP_CLIENT_KEY: "",
        OTEL_EXPORTER_OTLP_TRACES_CLIENT_KEY: "",
        OTEL_EXPORTER_OTLP_METRICS_CLIENT_KEY: "",
        OTEL_EXPORTER_OTLP_LOGS_CLIENT_KEY: "",
        OTEL_EXPORTER_OTLP_CLIENT_CERTIFICATE: "",
        OTEL_EXPORTER_OTLP_TRACES_CLIENT_CERTIFICATE: "",
        OTEL_EXPORTER_OTLP_METRICS_CLIENT_CERTIFICATE: "",
        OTEL_EXPORTER_OTLP_LOGS_CLIENT_CERTIFICATE: "",
        OTEL_EXPORTER_OTLP_PROTOCOL: "http/protobuf",
        OTEL_EXPORTER_OTLP_TRACES_PROTOCOL: "http/protobuf",
        OTEL_EXPORTER_OTLP_METRICS_PROTOCOL: "http/protobuf",
        OTEL_EXPORTER_OTLP_LOGS_PROTOCOL: "http/protobuf",
        OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE: "cumulative"
    };
    function lc(e, t, r) {
        if (typeof r[e] > "u") return;
        let n = String(r[e]);
        t[e] = n.toLowerCase() === "true";
    }
    function fc(e, t, r, n = -1 / 0, o = 1 / 0) {
        if (typeof r[e] < "u") {
            let i = Number(r[e]);
            isNaN(i) || (i < n ? t[e] = n : i > o ? t[e] = o : t[e] = i);
        }
    }
    function pc(e, t, r, n = nc) {
        let o = r[e];
        typeof o == "string" && (t[e] = o.split(n).map((i)=>i.trim()));
    }
    var dc = {
        ALL: dt.DiagLogLevel.ALL,
        VERBOSE: dt.DiagLogLevel.VERBOSE,
        DEBUG: dt.DiagLogLevel.DEBUG,
        INFO: dt.DiagLogLevel.INFO,
        WARN: dt.DiagLogLevel.WARN,
        ERROR: dt.DiagLogLevel.ERROR,
        NONE: dt.DiagLogLevel.NONE
    };
    function _c(e, t, r) {
        let n = r[e];
        if (typeof n == "string") {
            let o = dc[n.toUpperCase()];
            o != null && (t[e] = o);
        }
    }
    function Kr(e) {
        let t = {};
        for(let r in L.DEFAULT_ENVIRONMENT){
            let n = r;
            switch(n){
                case "OTEL_LOG_LEVEL":
                    _c(n, t, e);
                    break;
                default:
                    if (oc(n)) lc(n, t, e);
                    else if (sc(n)) fc(n, t, e);
                    else if (cc(n)) pc(n, t, e);
                    else {
                        let o = e[n];
                        typeof o < "u" && o !== null && (t[n] = String(o));
                    }
            }
        }
        return t;
    }
    L.parseEnvironment = Kr;
    function hc() {
        return typeof process < "u" && process && process.env ? Kr(process.env) : Kr(rc._globalThis);
    }
    L.getEnvWithoutDefaults = hc;
});
var gi = _((be)=>{
    "use strict";
    Object.defineProperty(be, "__esModule", {
        value: !0
    });
    be.getEnv = void 0;
    var Si = Oe(), Ec = Re();
    function mc() {
        let e = (0, Si.parseEnvironment)(Ec._globalThis);
        return Object.assign({}, Si.DEFAULT_ENVIRONMENT, e);
    }
    be.getEnv = mc;
});
var yi = _((Le)=>{
    "use strict";
    Object.defineProperty(Le, "__esModule", {
        value: !0
    });
    Le.hexToBase64 = void 0;
    function Tc(e) {
        let t = e.length, r = "";
        for(let n = 0; n < t; n += 2){
            let o = e.substring(n, n + 2), i = parseInt(o, 16);
            r += String.fromCharCode(i);
        }
        return btoa(r);
    }
    Le.hexToBase64 = Tc;
});
var Ai = _((Ie)=>{
    "use strict";
    Object.defineProperty(Ie, "__esModule", {
        value: !0
    });
    Ie.RandomIdGenerator = void 0;
    var Sc = 8, gc = 16, Wr = class {
        constructor(){
            this.generateTraceId = vi(gc), this.generateSpanId = vi(Sc);
        }
    };
    Ie.RandomIdGenerator = Wr;
    var Pe = Array(32);
    function vi(e) {
        return function() {
            for(let r = 0; r < e * 2; r++)Pe[r] = Math.floor(Math.random() * 16) + 48, Pe[r] >= 58 && (Pe[r] += 39);
            return String.fromCharCode.apply(null, Pe.slice(0, e * 2));
        };
    }
});
var Ri = _((Ce)=>{
    "use strict";
    Object.defineProperty(Ce, "__esModule", {
        value: !0
    });
    Ce.otperformance = void 0;
    Ce.otperformance = performance;
});
var Yr = _((Ne)=>{
    "use strict";
    Object.defineProperty(Ne, "__esModule", {
        value: !0
    });
    Ne.VERSION = void 0;
    Ne.VERSION = "1.19.0";
});
var Oi = _((A)=>{
    "use strict";
    Object.defineProperty(A, "__esModule", {
        value: !0
    });
    A.MessageTypeValues = A.RpcGrpcStatusCodeValues = A.MessagingOperationValues = A.MessagingDestinationKindValues = A.HttpFlavorValues = A.NetHostConnectionSubtypeValues = A.NetHostConnectionTypeValues = A.NetTransportValues = A.FaasInvokedProviderValues = A.FaasDocumentOperationValues = A.FaasTriggerValues = A.DbCassandraConsistencyLevelValues = A.DbSystemValues = A.SemanticAttributes = void 0;
    A.SemanticAttributes = {
        AWS_LAMBDA_INVOKED_ARN: "aws.lambda.invoked_arn",
        DB_SYSTEM: "db.system",
        DB_CONNECTION_STRING: "db.connection_string",
        DB_USER: "db.user",
        DB_JDBC_DRIVER_CLASSNAME: "db.jdbc.driver_classname",
        DB_NAME: "db.name",
        DB_STATEMENT: "db.statement",
        DB_OPERATION: "db.operation",
        DB_MSSQL_INSTANCE_NAME: "db.mssql.instance_name",
        DB_CASSANDRA_KEYSPACE: "db.cassandra.keyspace",
        DB_CASSANDRA_PAGE_SIZE: "db.cassandra.page_size",
        DB_CASSANDRA_CONSISTENCY_LEVEL: "db.cassandra.consistency_level",
        DB_CASSANDRA_TABLE: "db.cassandra.table",
        DB_CASSANDRA_IDEMPOTENCE: "db.cassandra.idempotence",
        DB_CASSANDRA_SPECULATIVE_EXECUTION_COUNT: "db.cassandra.speculative_execution_count",
        DB_CASSANDRA_COORDINATOR_ID: "db.cassandra.coordinator.id",
        DB_CASSANDRA_COORDINATOR_DC: "db.cassandra.coordinator.dc",
        DB_HBASE_NAMESPACE: "db.hbase.namespace",
        DB_REDIS_DATABASE_INDEX: "db.redis.database_index",
        DB_MONGODB_COLLECTION: "db.mongodb.collection",
        DB_SQL_TABLE: "db.sql.table",
        EXCEPTION_TYPE: "exception.type",
        EXCEPTION_MESSAGE: "exception.message",
        EXCEPTION_STACKTRACE: "exception.stacktrace",
        EXCEPTION_ESCAPED: "exception.escaped",
        FAAS_TRIGGER: "faas.trigger",
        FAAS_EXECUTION: "faas.execution",
        FAAS_DOCUMENT_COLLECTION: "faas.document.collection",
        FAAS_DOCUMENT_OPERATION: "faas.document.operation",
        FAAS_DOCUMENT_TIME: "faas.document.time",
        FAAS_DOCUMENT_NAME: "faas.document.name",
        FAAS_TIME: "faas.time",
        FAAS_CRON: "faas.cron",
        FAAS_COLDSTART: "faas.coldstart",
        FAAS_INVOKED_NAME: "faas.invoked_name",
        FAAS_INVOKED_PROVIDER: "faas.invoked_provider",
        FAAS_INVOKED_REGION: "faas.invoked_region",
        NET_TRANSPORT: "net.transport",
        NET_PEER_IP: "net.peer.ip",
        NET_PEER_PORT: "net.peer.port",
        NET_PEER_NAME: "net.peer.name",
        NET_HOST_IP: "net.host.ip",
        NET_HOST_PORT: "net.host.port",
        NET_HOST_NAME: "net.host.name",
        NET_HOST_CONNECTION_TYPE: "net.host.connection.type",
        NET_HOST_CONNECTION_SUBTYPE: "net.host.connection.subtype",
        NET_HOST_CARRIER_NAME: "net.host.carrier.name",
        NET_HOST_CARRIER_MCC: "net.host.carrier.mcc",
        NET_HOST_CARRIER_MNC: "net.host.carrier.mnc",
        NET_HOST_CARRIER_ICC: "net.host.carrier.icc",
        PEER_SERVICE: "peer.service",
        ENDUSER_ID: "enduser.id",
        ENDUSER_ROLE: "enduser.role",
        ENDUSER_SCOPE: "enduser.scope",
        THREAD_ID: "thread.id",
        THREAD_NAME: "thread.name",
        CODE_FUNCTION: "code.function",
        CODE_NAMESPACE: "code.namespace",
        CODE_FILEPATH: "code.filepath",
        CODE_LINENO: "code.lineno",
        HTTP_METHOD: "http.method",
        HTTP_URL: "http.url",
        HTTP_TARGET: "http.target",
        HTTP_HOST: "http.host",
        HTTP_SCHEME: "http.scheme",
        HTTP_STATUS_CODE: "http.status_code",
        HTTP_FLAVOR: "http.flavor",
        HTTP_USER_AGENT: "http.user_agent",
        HTTP_REQUEST_CONTENT_LENGTH: "http.request_content_length",
        HTTP_REQUEST_CONTENT_LENGTH_UNCOMPRESSED: "http.request_content_length_uncompressed",
        HTTP_RESPONSE_CONTENT_LENGTH: "http.response_content_length",
        HTTP_RESPONSE_CONTENT_LENGTH_UNCOMPRESSED: "http.response_content_length_uncompressed",
        HTTP_SERVER_NAME: "http.server_name",
        HTTP_ROUTE: "http.route",
        HTTP_CLIENT_IP: "http.client_ip",
        AWS_DYNAMODB_TABLE_NAMES: "aws.dynamodb.table_names",
        AWS_DYNAMODB_CONSUMED_CAPACITY: "aws.dynamodb.consumed_capacity",
        AWS_DYNAMODB_ITEM_COLLECTION_METRICS: "aws.dynamodb.item_collection_metrics",
        AWS_DYNAMODB_PROVISIONED_READ_CAPACITY: "aws.dynamodb.provisioned_read_capacity",
        AWS_DYNAMODB_PROVISIONED_WRITE_CAPACITY: "aws.dynamodb.provisioned_write_capacity",
        AWS_DYNAMODB_CONSISTENT_READ: "aws.dynamodb.consistent_read",
        AWS_DYNAMODB_PROJECTION: "aws.dynamodb.projection",
        AWS_DYNAMODB_LIMIT: "aws.dynamodb.limit",
        AWS_DYNAMODB_ATTRIBUTES_TO_GET: "aws.dynamodb.attributes_to_get",
        AWS_DYNAMODB_INDEX_NAME: "aws.dynamodb.index_name",
        AWS_DYNAMODB_SELECT: "aws.dynamodb.select",
        AWS_DYNAMODB_GLOBAL_SECONDARY_INDEXES: "aws.dynamodb.global_secondary_indexes",
        AWS_DYNAMODB_LOCAL_SECONDARY_INDEXES: "aws.dynamodb.local_secondary_indexes",
        AWS_DYNAMODB_EXCLUSIVE_START_TABLE: "aws.dynamodb.exclusive_start_table",
        AWS_DYNAMODB_TABLE_COUNT: "aws.dynamodb.table_count",
        AWS_DYNAMODB_SCAN_FORWARD: "aws.dynamodb.scan_forward",
        AWS_DYNAMODB_SEGMENT: "aws.dynamodb.segment",
        AWS_DYNAMODB_TOTAL_SEGMENTS: "aws.dynamodb.total_segments",
        AWS_DYNAMODB_COUNT: "aws.dynamodb.count",
        AWS_DYNAMODB_SCANNED_COUNT: "aws.dynamodb.scanned_count",
        AWS_DYNAMODB_ATTRIBUTE_DEFINITIONS: "aws.dynamodb.attribute_definitions",
        AWS_DYNAMODB_GLOBAL_SECONDARY_INDEX_UPDATES: "aws.dynamodb.global_secondary_index_updates",
        MESSAGING_SYSTEM: "messaging.system",
        MESSAGING_DESTINATION: "messaging.destination",
        MESSAGING_DESTINATION_KIND: "messaging.destination_kind",
        MESSAGING_TEMP_DESTINATION: "messaging.temp_destination",
        MESSAGING_PROTOCOL: "messaging.protocol",
        MESSAGING_PROTOCOL_VERSION: "messaging.protocol_version",
        MESSAGING_URL: "messaging.url",
        MESSAGING_MESSAGE_ID: "messaging.message_id",
        MESSAGING_CONVERSATION_ID: "messaging.conversation_id",
        MESSAGING_MESSAGE_PAYLOAD_SIZE_BYTES: "messaging.message_payload_size_bytes",
        MESSAGING_MESSAGE_PAYLOAD_COMPRESSED_SIZE_BYTES: "messaging.message_payload_compressed_size_bytes",
        MESSAGING_OPERATION: "messaging.operation",
        MESSAGING_CONSUMER_ID: "messaging.consumer_id",
        MESSAGING_RABBITMQ_ROUTING_KEY: "messaging.rabbitmq.routing_key",
        MESSAGING_KAFKA_MESSAGE_KEY: "messaging.kafka.message_key",
        MESSAGING_KAFKA_CONSUMER_GROUP: "messaging.kafka.consumer_group",
        MESSAGING_KAFKA_CLIENT_ID: "messaging.kafka.client_id",
        MESSAGING_KAFKA_PARTITION: "messaging.kafka.partition",
        MESSAGING_KAFKA_TOMBSTONE: "messaging.kafka.tombstone",
        RPC_SYSTEM: "rpc.system",
        RPC_SERVICE: "rpc.service",
        RPC_METHOD: "rpc.method",
        RPC_GRPC_STATUS_CODE: "rpc.grpc.status_code",
        RPC_JSONRPC_VERSION: "rpc.jsonrpc.version",
        RPC_JSONRPC_REQUEST_ID: "rpc.jsonrpc.request_id",
        RPC_JSONRPC_ERROR_CODE: "rpc.jsonrpc.error_code",
        RPC_JSONRPC_ERROR_MESSAGE: "rpc.jsonrpc.error_message",
        MESSAGE_TYPE: "message.type",
        MESSAGE_ID: "message.id",
        MESSAGE_COMPRESSED_SIZE: "message.compressed_size",
        MESSAGE_UNCOMPRESSED_SIZE: "message.uncompressed_size"
    };
    A.DbSystemValues = {
        OTHER_SQL: "other_sql",
        MSSQL: "mssql",
        MYSQL: "mysql",
        ORACLE: "oracle",
        DB2: "db2",
        POSTGRESQL: "postgresql",
        REDSHIFT: "redshift",
        HIVE: "hive",
        CLOUDSCAPE: "cloudscape",
        HSQLDB: "hsqldb",
        PROGRESS: "progress",
        MAXDB: "maxdb",
        HANADB: "hanadb",
        INGRES: "ingres",
        FIRSTSQL: "firstsql",
        EDB: "edb",
        CACHE: "cache",
        ADABAS: "adabas",
        FIREBIRD: "firebird",
        DERBY: "derby",
        FILEMAKER: "filemaker",
        INFORMIX: "informix",
        INSTANTDB: "instantdb",
        INTERBASE: "interbase",
        MARIADB: "mariadb",
        NETEZZA: "netezza",
        PERVASIVE: "pervasive",
        POINTBASE: "pointbase",
        SQLITE: "sqlite",
        SYBASE: "sybase",
        TERADATA: "teradata",
        VERTICA: "vertica",
        H2: "h2",
        COLDFUSION: "coldfusion",
        CASSANDRA: "cassandra",
        HBASE: "hbase",
        MONGODB: "mongodb",
        REDIS: "redis",
        COUCHBASE: "couchbase",
        COUCHDB: "couchdb",
        COSMOSDB: "cosmosdb",
        DYNAMODB: "dynamodb",
        NEO4J: "neo4j",
        GEODE: "geode",
        ELASTICSEARCH: "elasticsearch",
        MEMCACHED: "memcached",
        COCKROACHDB: "cockroachdb"
    };
    A.DbCassandraConsistencyLevelValues = {
        ALL: "all",
        EACH_QUORUM: "each_quorum",
        QUORUM: "quorum",
        LOCAL_QUORUM: "local_quorum",
        ONE: "one",
        TWO: "two",
        THREE: "three",
        LOCAL_ONE: "local_one",
        ANY: "any",
        SERIAL: "serial",
        LOCAL_SERIAL: "local_serial"
    };
    A.FaasTriggerValues = {
        DATASOURCE: "datasource",
        HTTP: "http",
        PUBSUB: "pubsub",
        TIMER: "timer",
        OTHER: "other"
    };
    A.FaasDocumentOperationValues = {
        INSERT: "insert",
        EDIT: "edit",
        DELETE: "delete"
    };
    A.FaasInvokedProviderValues = {
        ALIBABA_CLOUD: "alibaba_cloud",
        AWS: "aws",
        AZURE: "azure",
        GCP: "gcp"
    };
    A.NetTransportValues = {
        IP_TCP: "ip_tcp",
        IP_UDP: "ip_udp",
        IP: "ip",
        UNIX: "unix",
        PIPE: "pipe",
        INPROC: "inproc",
        OTHER: "other"
    };
    A.NetHostConnectionTypeValues = {
        WIFI: "wifi",
        WIRED: "wired",
        CELL: "cell",
        UNAVAILABLE: "unavailable",
        UNKNOWN: "unknown"
    };
    A.NetHostConnectionSubtypeValues = {
        GPRS: "gprs",
        EDGE: "edge",
        UMTS: "umts",
        CDMA: "cdma",
        EVDO_0: "evdo_0",
        EVDO_A: "evdo_a",
        CDMA2000_1XRTT: "cdma2000_1xrtt",
        HSDPA: "hsdpa",
        HSUPA: "hsupa",
        HSPA: "hspa",
        IDEN: "iden",
        EVDO_B: "evdo_b",
        LTE: "lte",
        EHRPD: "ehrpd",
        HSPAP: "hspap",
        GSM: "gsm",
        TD_SCDMA: "td_scdma",
        IWLAN: "iwlan",
        NR: "nr",
        NRNSA: "nrnsa",
        LTE_CA: "lte_ca"
    };
    A.HttpFlavorValues = {
        HTTP_1_0: "1.0",
        HTTP_1_1: "1.1",
        HTTP_2_0: "2.0",
        SPDY: "SPDY",
        QUIC: "QUIC"
    };
    A.MessagingDestinationKindValues = {
        QUEUE: "queue",
        TOPIC: "topic"
    };
    A.MessagingOperationValues = {
        RECEIVE: "receive",
        PROCESS: "process"
    };
    A.RpcGrpcStatusCodeValues = {
        OK: 0,
        CANCELLED: 1,
        UNKNOWN: 2,
        INVALID_ARGUMENT: 3,
        DEADLINE_EXCEEDED: 4,
        NOT_FOUND: 5,
        ALREADY_EXISTS: 6,
        PERMISSION_DENIED: 7,
        RESOURCE_EXHAUSTED: 8,
        FAILED_PRECONDITION: 9,
        ABORTED: 10,
        OUT_OF_RANGE: 11,
        UNIMPLEMENTED: 12,
        INTERNAL: 13,
        UNAVAILABLE: 14,
        DATA_LOSS: 15,
        UNAUTHENTICATED: 16
    };
    A.MessageTypeValues = {
        SENT: "SENT",
        RECEIVED: "RECEIVED"
    };
});
var bi = _((It)=>{
    "use strict";
    var yc = It && It.__createBinding || (Object.create ? function(e, t, r, n) {
        n === void 0 && (n = r), Object.defineProperty(e, n, {
            enumerable: !0,
            get: function() {
                return t[r];
            }
        });
    } : function(e, t, r, n) {
        n === void 0 && (n = r), e[n] = t[r];
    }), vc = It && It.__exportStar || function(e, t) {
        for(var r in e)r !== "default" && !Object.prototype.hasOwnProperty.call(t, r) && yc(t, e, r);
    };
    Object.defineProperty(It, "__esModule", {
        value: !0
    });
    vc(Oi(), It);
});
var Li = _((G)=>{
    "use strict";
    Object.defineProperty(G, "__esModule", {
        value: !0
    });
    G.TelemetrySdkLanguageValues = G.OsTypeValues = G.HostArchValues = G.AwsEcsLaunchtypeValues = G.CloudPlatformValues = G.CloudProviderValues = G.SemanticResourceAttributes = void 0;
    G.SemanticResourceAttributes = {
        CLOUD_PROVIDER: "cloud.provider",
        CLOUD_ACCOUNT_ID: "cloud.account.id",
        CLOUD_REGION: "cloud.region",
        CLOUD_AVAILABILITY_ZONE: "cloud.availability_zone",
        CLOUD_PLATFORM: "cloud.platform",
        AWS_ECS_CONTAINER_ARN: "aws.ecs.container.arn",
        AWS_ECS_CLUSTER_ARN: "aws.ecs.cluster.arn",
        AWS_ECS_LAUNCHTYPE: "aws.ecs.launchtype",
        AWS_ECS_TASK_ARN: "aws.ecs.task.arn",
        AWS_ECS_TASK_FAMILY: "aws.ecs.task.family",
        AWS_ECS_TASK_REVISION: "aws.ecs.task.revision",
        AWS_EKS_CLUSTER_ARN: "aws.eks.cluster.arn",
        AWS_LOG_GROUP_NAMES: "aws.log.group.names",
        AWS_LOG_GROUP_ARNS: "aws.log.group.arns",
        AWS_LOG_STREAM_NAMES: "aws.log.stream.names",
        AWS_LOG_STREAM_ARNS: "aws.log.stream.arns",
        CONTAINER_NAME: "container.name",
        CONTAINER_ID: "container.id",
        CONTAINER_RUNTIME: "container.runtime",
        CONTAINER_IMAGE_NAME: "container.image.name",
        CONTAINER_IMAGE_TAG: "container.image.tag",
        DEPLOYMENT_ENVIRONMENT: "deployment.environment",
        DEVICE_ID: "device.id",
        DEVICE_MODEL_IDENTIFIER: "device.model.identifier",
        DEVICE_MODEL_NAME: "device.model.name",
        FAAS_NAME: "faas.name",
        FAAS_ID: "faas.id",
        FAAS_VERSION: "faas.version",
        FAAS_INSTANCE: "faas.instance",
        FAAS_MAX_MEMORY: "faas.max_memory",
        HOST_ID: "host.id",
        HOST_NAME: "host.name",
        HOST_TYPE: "host.type",
        HOST_ARCH: "host.arch",
        HOST_IMAGE_NAME: "host.image.name",
        HOST_IMAGE_ID: "host.image.id",
        HOST_IMAGE_VERSION: "host.image.version",
        K8S_CLUSTER_NAME: "k8s.cluster.name",
        K8S_NODE_NAME: "k8s.node.name",
        K8S_NODE_UID: "k8s.node.uid",
        K8S_NAMESPACE_NAME: "k8s.namespace.name",
        K8S_POD_UID: "k8s.pod.uid",
        K8S_POD_NAME: "k8s.pod.name",
        K8S_CONTAINER_NAME: "k8s.container.name",
        K8S_REPLICASET_UID: "k8s.replicaset.uid",
        K8S_REPLICASET_NAME: "k8s.replicaset.name",
        K8S_DEPLOYMENT_UID: "k8s.deployment.uid",
        K8S_DEPLOYMENT_NAME: "k8s.deployment.name",
        K8S_STATEFULSET_UID: "k8s.statefulset.uid",
        K8S_STATEFULSET_NAME: "k8s.statefulset.name",
        K8S_DAEMONSET_UID: "k8s.daemonset.uid",
        K8S_DAEMONSET_NAME: "k8s.daemonset.name",
        K8S_JOB_UID: "k8s.job.uid",
        K8S_JOB_NAME: "k8s.job.name",
        K8S_CRONJOB_UID: "k8s.cronjob.uid",
        K8S_CRONJOB_NAME: "k8s.cronjob.name",
        OS_TYPE: "os.type",
        OS_DESCRIPTION: "os.description",
        OS_NAME: "os.name",
        OS_VERSION: "os.version",
        PROCESS_PID: "process.pid",
        PROCESS_EXECUTABLE_NAME: "process.executable.name",
        PROCESS_EXECUTABLE_PATH: "process.executable.path",
        PROCESS_COMMAND: "process.command",
        PROCESS_COMMAND_LINE: "process.command_line",
        PROCESS_COMMAND_ARGS: "process.command_args",
        PROCESS_OWNER: "process.owner",
        PROCESS_RUNTIME_NAME: "process.runtime.name",
        PROCESS_RUNTIME_VERSION: "process.runtime.version",
        PROCESS_RUNTIME_DESCRIPTION: "process.runtime.description",
        SERVICE_NAME: "service.name",
        SERVICE_NAMESPACE: "service.namespace",
        SERVICE_INSTANCE_ID: "service.instance.id",
        SERVICE_VERSION: "service.version",
        TELEMETRY_SDK_NAME: "telemetry.sdk.name",
        TELEMETRY_SDK_LANGUAGE: "telemetry.sdk.language",
        TELEMETRY_SDK_VERSION: "telemetry.sdk.version",
        TELEMETRY_AUTO_VERSION: "telemetry.auto.version",
        WEBENGINE_NAME: "webengine.name",
        WEBENGINE_VERSION: "webengine.version",
        WEBENGINE_DESCRIPTION: "webengine.description"
    };
    G.CloudProviderValues = {
        ALIBABA_CLOUD: "alibaba_cloud",
        AWS: "aws",
        AZURE: "azure",
        GCP: "gcp"
    };
    G.CloudPlatformValues = {
        ALIBABA_CLOUD_ECS: "alibaba_cloud_ecs",
        ALIBABA_CLOUD_FC: "alibaba_cloud_fc",
        AWS_EC2: "aws_ec2",
        AWS_ECS: "aws_ecs",
        AWS_EKS: "aws_eks",
        AWS_LAMBDA: "aws_lambda",
        AWS_ELASTIC_BEANSTALK: "aws_elastic_beanstalk",
        AZURE_VM: "azure_vm",
        AZURE_CONTAINER_INSTANCES: "azure_container_instances",
        AZURE_AKS: "azure_aks",
        AZURE_FUNCTIONS: "azure_functions",
        AZURE_APP_SERVICE: "azure_app_service",
        GCP_COMPUTE_ENGINE: "gcp_compute_engine",
        GCP_CLOUD_RUN: "gcp_cloud_run",
        GCP_KUBERNETES_ENGINE: "gcp_kubernetes_engine",
        GCP_CLOUD_FUNCTIONS: "gcp_cloud_functions",
        GCP_APP_ENGINE: "gcp_app_engine"
    };
    G.AwsEcsLaunchtypeValues = {
        EC2: "ec2",
        FARGATE: "fargate"
    };
    G.HostArchValues = {
        AMD64: "amd64",
        ARM32: "arm32",
        ARM64: "arm64",
        IA64: "ia64",
        PPC32: "ppc32",
        PPC64: "ppc64",
        X86: "x86"
    };
    G.OsTypeValues = {
        WINDOWS: "windows",
        LINUX: "linux",
        DARWIN: "darwin",
        FREEBSD: "freebsd",
        NETBSD: "netbsd",
        OPENBSD: "openbsd",
        DRAGONFLYBSD: "dragonflybsd",
        HPUX: "hpux",
        AIX: "aix",
        SOLARIS: "solaris",
        Z_OS: "z_os"
    };
    G.TelemetrySdkLanguageValues = {
        CPP: "cpp",
        DOTNET: "dotnet",
        ERLANG: "erlang",
        GO: "go",
        JAVA: "java",
        NODEJS: "nodejs",
        PHP: "php",
        PYTHON: "python",
        RUBY: "ruby",
        WEBJS: "webjs"
    };
});
var Pi = _((Ct)=>{
    "use strict";
    var Ac = Ct && Ct.__createBinding || (Object.create ? function(e, t, r, n) {
        n === void 0 && (n = r), Object.defineProperty(e, n, {
            enumerable: !0,
            get: function() {
                return t[r];
            }
        });
    } : function(e, t, r, n) {
        n === void 0 && (n = r), e[n] = t[r];
    }), Rc = Ct && Ct.__exportStar || function(e, t) {
        for(var r in e)r !== "default" && !Object.prototype.hasOwnProperty.call(t, r) && Ac(t, e, r);
    };
    Object.defineProperty(Ct, "__esModule", {
        value: !0
    });
    Rc(Li(), Ct);
});
var ae = _((_t)=>{
    "use strict";
    var Oc = _t && _t.__createBinding || (Object.create ? function(e, t, r, n) {
        n === void 0 && (n = r), Object.defineProperty(e, n, {
            enumerable: !0,
            get: function() {
                return t[r];
            }
        });
    } : function(e, t, r, n) {
        n === void 0 && (n = r), e[n] = t[r];
    }), Ii = _t && _t.__exportStar || function(e, t) {
        for(var r in e)r !== "default" && !Object.prototype.hasOwnProperty.call(t, r) && Oc(t, e, r);
    };
    Object.defineProperty(_t, "__esModule", {
        value: !0
    });
    Ii(bi(), _t);
    Ii(Pi(), _t);
});
var Ci = _((we)=>{
    "use strict";
    Object.defineProperty(we, "__esModule", {
        value: !0
    });
    we.SDK_INFO = void 0;
    var bc = Yr(), se = ae();
    we.SDK_INFO = {
        [se.SemanticResourceAttributes.TELEMETRY_SDK_NAME]: "opentelemetry",
        [se.SemanticResourceAttributes.PROCESS_RUNTIME_NAME]: "browser",
        [se.SemanticResourceAttributes.TELEMETRY_SDK_LANGUAGE]: se.TelemetrySdkLanguageValues.WEBJS,
        [se.SemanticResourceAttributes.TELEMETRY_SDK_VERSION]: bc.VERSION
    };
});
var Ni = _((Me)=>{
    "use strict";
    Object.defineProperty(Me, "__esModule", {
        value: !0
    });
    Me.unrefTimer = void 0;
    function Lc(e) {}
    Me.unrefTimer = Lc;
});
var zr = _((j)=>{
    "use strict";
    var Pc = j && j.__createBinding || (Object.create ? function(e, t, r, n) {
        n === void 0 && (n = r), Object.defineProperty(e, n, {
            enumerable: !0,
            get: function() {
                return t[r];
            }
        });
    } : function(e, t, r, n) {
        n === void 0 && (n = r), e[n] = t[r];
    }), Nt = j && j.__exportStar || function(e, t) {
        for(var r in e)r !== "default" && !Object.prototype.hasOwnProperty.call(t, r) && Pc(t, e, r);
    };
    Object.defineProperty(j, "__esModule", {
        value: !0
    });
    Nt(gi(), j);
    Nt(Re(), j);
    Nt(yi(), j);
    Nt(Ai(), j);
    Nt(Ri(), j);
    Nt(Ci(), j);
    Nt(Ni(), j);
});
var Di = _((O)=>{
    "use strict";
    Object.defineProperty(O, "__esModule", {
        value: !0
    });
    O.addHrTimes = O.isTimeInput = O.isTimeInputHrTime = O.hrTimeToMicroseconds = O.hrTimeToMilliseconds = O.hrTimeToNanoseconds = O.hrTimeToTimeStamp = O.hrTimeDuration = O.timeInputToHrTime = O.hrTime = O.getTimeOrigin = O.millisToHrTime = void 0;
    var $r = zr(), wi = 9, Ic = 6, Cc = Math.pow(10, Ic), xe = Math.pow(10, wi);
    function ue(e) {
        let t = e / 1e3, r = Math.trunc(t), n = Math.round(e % 1e3 * Cc);
        return [
            r,
            n
        ];
    }
    O.millisToHrTime = ue;
    function Qr() {
        let e = $r.otperformance.timeOrigin;
        if (typeof e != "number") {
            let t = $r.otperformance;
            e = t.timing && t.timing.fetchStart;
        }
        return e;
    }
    O.getTimeOrigin = Qr;
    function Mi(e) {
        let t = ue(Qr()), r = ue(typeof e == "number" ? e : $r.otperformance.now());
        return xi(t, r);
    }
    O.hrTime = Mi;
    function Nc(e) {
        if (Zr(e)) return e;
        if (typeof e == "number") return e < Qr() ? Mi(e) : ue(e);
        if (e instanceof Date) return ue(e.getTime());
        throw TypeError("Invalid input type");
    }
    O.timeInputToHrTime = Nc;
    function wc(e, t) {
        let r = t[0] - e[0], n = t[1] - e[1];
        return n < 0 && (r -= 1, n += xe), [
            r,
            n
        ];
    }
    O.hrTimeDuration = wc;
    function Mc(e) {
        let t = wi, r = `${"0".repeat(t)}${e[1]}Z`, n = r.substr(r.length - t - 1);
        return new Date(e[0] * 1e3).toISOString().replace("000Z", n);
    }
    O.hrTimeToTimeStamp = Mc;
    function xc(e) {
        return e[0] * xe + e[1];
    }
    O.hrTimeToNanoseconds = xc;
    function Dc(e) {
        return e[0] * 1e3 + e[1] / 1e6;
    }
    O.hrTimeToMilliseconds = Dc;
    function Uc(e) {
        return e[0] * 1e6 + e[1] / 1e3;
    }
    O.hrTimeToMicroseconds = Uc;
    function Zr(e) {
        return Array.isArray(e) && e.length === 2 && typeof e[0] == "number" && typeof e[1] == "number";
    }
    O.isTimeInputHrTime = Zr;
    function Bc(e) {
        return Zr(e) || typeof e == "number" || e instanceof Date;
    }
    O.isTimeInput = Bc;
    function xi(e, t) {
        let r = [
            e[0] + t[0],
            e[1] + t[1]
        ];
        return r[1] >= xe && (r[1] -= xe, r[0] += 1), r;
    }
    O.addHrTimes = xi;
});
var Bi = _((Ui)=>{
    "use strict";
    Object.defineProperty(Ui, "__esModule", {
        value: !0
    });
});
var Gi = _((ce)=>{
    "use strict";
    Object.defineProperty(ce, "__esModule", {
        value: !0
    });
    ce.ExportResultCode = void 0;
    var Gc;
    (function(e) {
        e[e.SUCCESS = 0] = "SUCCESS", e[e.FAILED = 1] = "FAILED";
    })(Gc = ce.ExportResultCode || (ce.ExportResultCode = {}));
});
var Hi = _((De)=>{
    "use strict";
    Object.defineProperty(De, "__esModule", {
        value: !0
    });
    De.CompositePropagator = void 0;
    var Vi = (E(), w(l)), Jr = class {
        constructor(t = {}){
            var r;
            this._propagators = (r = t.propagators) !== null && r !== void 0 ? r : [], this._fields = Array.from(new Set(this._propagators.map((n)=>typeof n.fields == "function" ? n.fields() : []).reduce((n, o)=>n.concat(o), [])));
        }
        inject(t, r, n) {
            for (let o of this._propagators)try {
                o.inject(t, r, n);
            } catch (i) {
                Vi.diag.warn(`Failed to inject with ${o.constructor.name}. Err: ${i.message}`);
            }
        }
        extract(t, r, n) {
            return this._propagators.reduce((o, i)=>{
                try {
                    return i.extract(o, r, n);
                } catch (s) {
                    Vi.diag.warn(`Failed to inject with ${i.constructor.name}. Err: ${s.message}`);
                }
                return o;
            }, t);
        }
        fields() {
            return this._fields.slice();
        }
    };
    De.CompositePropagator = Jr;
});
var Fi = _((Ht)=>{
    "use strict";
    Object.defineProperty(Ht, "__esModule", {
        value: !0
    });
    Ht.validateValue = Ht.validateKey = void 0;
    var tn = "[_0-9a-z-*/]", Vc = `[a-z]${tn}{0,255}`, Hc = `[a-z0-9]${tn}{0,240}@[a-z]${tn}{0,13}`, Fc = new RegExp(`^(?:${Vc}|${Hc})$`), jc = /^[ -~]{0,255}[!-~]$/, kc = /,|=/;
    function qc(e) {
        return Fc.test(e);
    }
    Ht.validateKey = qc;
    function Xc(e) {
        return jc.test(e) && !kc.test(e);
    }
    Ht.validateValue = Xc;
});
var rn = _((Ue)=>{
    "use strict";
    Object.defineProperty(Ue, "__esModule", {
        value: !0
    });
    Ue.TraceState = void 0;
    var ji = Fi(), ki = 32, Kc = 512, qi = ",", Xi = "=", en = class e {
        constructor(t){
            this._internalState = new Map, t && this._parse(t);
        }
        set(t, r) {
            let n = this._clone();
            return n._internalState.has(t) && n._internalState.delete(t), n._internalState.set(t, r), n;
        }
        unset(t) {
            let r = this._clone();
            return r._internalState.delete(t), r;
        }
        get(t) {
            return this._internalState.get(t);
        }
        serialize() {
            return this._keys().reduce((t, r)=>(t.push(r + Xi + this.get(r)), t), []).join(qi);
        }
        _parse(t) {
            t.length > Kc || (this._internalState = t.split(qi).reverse().reduce((r, n)=>{
                let o = n.trim(), i = o.indexOf(Xi);
                if (i !== -1) {
                    let s = o.slice(0, i), u = o.slice(i + 1, n.length);
                    (0, ji.validateKey)(s) && (0, ji.validateValue)(u) && r.set(s, u);
                }
                return r;
            }, new Map), this._internalState.size > ki && (this._internalState = new Map(Array.from(this._internalState.entries()).reverse().slice(0, ki))));
        }
        _keys() {
            return Array.from(this._internalState.keys()).reverse();
        }
        _clone() {
            let t = new e;
            return t._internalState = new Map(this._internalState), t;
        }
    };
    Ue.TraceState = en;
});
var Wi = _((V)=>{
    "use strict";
    Object.defineProperty(V, "__esModule", {
        value: !0
    });
    V.W3CTraceContextPropagator = V.parseTraceParent = V.TRACE_STATE_HEADER = V.TRACE_PARENT_HEADER = void 0;
    var Be = (E(), w(l)), Wc = ie(), Yc = rn();
    V.TRACE_PARENT_HEADER = "traceparent";
    V.TRACE_STATE_HEADER = "tracestate";
    var zc = "00", $c = "(?!ff)[\\da-f]{2}", Qc = "(?![0]{32})[\\da-f]{32}", Zc = "(?![0]{16})[\\da-f]{16}", Jc = "[\\da-f]{2}", tl = new RegExp(`^\\s?(${$c})-(${Qc})-(${Zc})-(${Jc})(-.*)?\\s?$`);
    function Ki(e) {
        let t = tl.exec(e);
        return !t || t[1] === "00" && t[5] ? null : {
            traceId: t[2],
            spanId: t[3],
            traceFlags: parseInt(t[4], 16)
        };
    }
    V.parseTraceParent = Ki;
    var nn = class {
        inject(t, r, n) {
            let o = Be.trace.getSpanContext(t);
            if (!o || (0, Wc.isTracingSuppressed)(t) || !(0, Be.isSpanContextValid)(o)) return;
            let i = `${zc}-${o.traceId}-${o.spanId}-0${Number(o.traceFlags || Be.TraceFlags.NONE).toString(16)}`;
            n.set(r, V.TRACE_PARENT_HEADER, i), o.traceState && n.set(r, V.TRACE_STATE_HEADER, o.traceState.serialize());
        }
        extract(t, r, n) {
            let o = n.get(r, V.TRACE_PARENT_HEADER);
            if (!o) return t;
            let i = Array.isArray(o) ? o[0] : o;
            if (typeof i != "string") return t;
            let s = Ki(i);
            if (!s) return t;
            s.isRemote = !0;
            let u = n.get(r, V.TRACE_STATE_HEADER);
            if (u) {
                let c = Array.isArray(u) ? u.join(",") : u;
                s.traceState = new Yc.TraceState(typeof c == "string" ? c : void 0);
            }
            return Be.trace.setSpanContext(t, s);
        }
        fields() {
            return [
                V.TRACE_PARENT_HEADER,
                V.TRACE_STATE_HEADER
            ];
        }
    };
    V.W3CTraceContextPropagator = nn;
});
var zi = _((Yi)=>{
    "use strict";
    Object.defineProperty(Yi, "__esModule", {
        value: !0
    });
});
var $i = _((z)=>{
    "use strict";
    Object.defineProperty(z, "__esModule", {
        value: !0
    });
    z.getRPCMetadata = z.deleteRPCMetadata = z.setRPCMetadata = z.RPCType = void 0;
    var el = (E(), w(l)), on = (0, el.createContextKey)("OpenTelemetry SDK Context Key RPC_METADATA"), rl;
    (function(e) {
        e.HTTP = "http";
    })(rl = z.RPCType || (z.RPCType = {}));
    function nl(e, t) {
        return e.setValue(on, t);
    }
    z.setRPCMetadata = nl;
    function il(e) {
        return e.deleteValue(on);
    }
    z.deleteRPCMetadata = il;
    function ol(e) {
        return e.getValue(on);
    }
    z.getRPCMetadata = ol;
});
var sn = _((Ge)=>{
    "use strict";
    Object.defineProperty(Ge, "__esModule", {
        value: !0
    });
    Ge.AlwaysOffSampler = void 0;
    var al = (E(), w(l)), an = class {
        shouldSample() {
            return {
                decision: al.SamplingDecision.NOT_RECORD
            };
        }
        toString() {
            return "AlwaysOffSampler";
        }
    };
    Ge.AlwaysOffSampler = an;
});
var cn = _((Ve)=>{
    "use strict";
    Object.defineProperty(Ve, "__esModule", {
        value: !0
    });
    Ve.AlwaysOnSampler = void 0;
    var sl = (E(), w(l)), un = class {
        shouldSample() {
            return {
                decision: sl.SamplingDecision.RECORD_AND_SAMPLED
            };
        }
        toString() {
            return "AlwaysOnSampler";
        }
    };
    Ve.AlwaysOnSampler = un;
});
var Zi = _((Fe)=>{
    "use strict";
    Object.defineProperty(Fe, "__esModule", {
        value: !0
    });
    Fe.ParentBasedSampler = void 0;
    var He = (E(), w(l)), ul = qr(), Qi = sn(), ln = cn(), fn = class {
        constructor(t){
            var r, n, o, i;
            this._root = t.root, this._root || ((0, ul.globalErrorHandler)(new Error("ParentBasedSampler must have a root sampler configured")), this._root = new ln.AlwaysOnSampler), this._remoteParentSampled = (r = t.remoteParentSampled) !== null && r !== void 0 ? r : new ln.AlwaysOnSampler, this._remoteParentNotSampled = (n = t.remoteParentNotSampled) !== null && n !== void 0 ? n : new Qi.AlwaysOffSampler, this._localParentSampled = (o = t.localParentSampled) !== null && o !== void 0 ? o : new ln.AlwaysOnSampler, this._localParentNotSampled = (i = t.localParentNotSampled) !== null && i !== void 0 ? i : new Qi.AlwaysOffSampler;
        }
        shouldSample(t, r, n, o, i, s) {
            let u = He.trace.getSpanContext(t);
            return !u || !(0, He.isSpanContextValid)(u) ? this._root.shouldSample(t, r, n, o, i, s) : u.isRemote ? u.traceFlags & He.TraceFlags.SAMPLED ? this._remoteParentSampled.shouldSample(t, r, n, o, i, s) : this._remoteParentNotSampled.shouldSample(t, r, n, o, i, s) : u.traceFlags & He.TraceFlags.SAMPLED ? this._localParentSampled.shouldSample(t, r, n, o, i, s) : this._localParentNotSampled.shouldSample(t, r, n, o, i, s);
        }
        toString() {
            return `ParentBased{root=${this._root.toString()}, remoteParentSampled=${this._remoteParentSampled.toString()}, remoteParentNotSampled=${this._remoteParentNotSampled.toString()}, localParentSampled=${this._localParentSampled.toString()}, localParentNotSampled=${this._localParentNotSampled.toString()}}`;
        }
    };
    Fe.ParentBasedSampler = fn;
});
var Ji = _((je)=>{
    "use strict";
    Object.defineProperty(je, "__esModule", {
        value: !0
    });
    je.TraceIdRatioBasedSampler = void 0;
    var pn = (E(), w(l)), dn = class {
        constructor(t = 0){
            this._ratio = t, this._ratio = this._normalize(t), this._upperBound = Math.floor(this._ratio * 4294967295);
        }
        shouldSample(t, r) {
            return {
                decision: (0, pn.isValidTraceId)(r) && this._accumulate(r) < this._upperBound ? pn.SamplingDecision.RECORD_AND_SAMPLED : pn.SamplingDecision.NOT_RECORD
            };
        }
        toString() {
            return `TraceIdRatioBased{${this._ratio}}`;
        }
        _normalize(t) {
            return typeof t != "number" || isNaN(t) ? 0 : t >= 1 ? 1 : t <= 0 ? 0 : t;
        }
        _accumulate(t) {
            let r = 0;
            for(let n = 0; n < t.length / 8; n++){
                let o = n * 8, i = parseInt(t.slice(o, o + 8), 16);
                r = (r ^ i) >>> 0;
            }
            return r;
        }
    };
    je.TraceIdRatioBasedSampler = dn;
});
var io = _((ke)=>{
    "use strict";
    Object.defineProperty(ke, "__esModule", {
        value: !0
    });
    ke.isPlainObject = void 0;
    var cl = "[object Object]", ll = "[object Null]", fl = "[object Undefined]", pl = Function.prototype, to = pl.toString, dl = to.call(Object), _l = hl(Object.getPrototypeOf, Object), eo = Object.prototype, ro = eo.hasOwnProperty, wt = Symbol ? Symbol.toStringTag : void 0, no = eo.toString;
    function hl(e, t) {
        return function(r) {
            return e(t(r));
        };
    }
    function El(e) {
        if (!ml(e) || Tl(e) !== cl) return !1;
        let t = _l(e);
        if (t === null) return !0;
        let r = ro.call(t, "constructor") && t.constructor;
        return typeof r == "function" && r instanceof r && to.call(r) === dl;
    }
    ke.isPlainObject = El;
    function ml(e) {
        return e != null && typeof e == "object";
    }
    function Tl(e) {
        return e == null ? e === void 0 ? fl : ll : wt && wt in Object(e) ? Sl(e) : gl(e);
    }
    function Sl(e) {
        let t = ro.call(e, wt), r = e[wt], n = !1;
        try {
            e[wt] = void 0, n = !0;
        } catch  {}
        let o = no.call(e);
        return n && (t ? e[wt] = r : delete e[wt]), o;
    }
    function gl(e) {
        return no.call(e);
    }
});
var co = _((Ke)=>{
    "use strict";
    Object.defineProperty(Ke, "__esModule", {
        value: !0
    });
    Ke.merge = void 0;
    var oo = io(), yl = 20;
    function vl(...e) {
        let t = e.shift(), r = new WeakMap;
        for(; e.length > 0;)t = so(t, e.shift(), 0, r);
        return t;
    }
    Ke.merge = vl;
    function _n(e) {
        return Xe(e) ? e.slice() : e;
    }
    function so(e, t, r = 0, n) {
        let o;
        if (!(r > yl)) {
            if (r++, qe(e) || qe(t) || uo(t)) o = _n(t);
            else if (Xe(e)) {
                if (o = e.slice(), Xe(t)) for(let i = 0, s = t.length; i < s; i++)o.push(_n(t[i]));
                else if (le(t)) {
                    let i = Object.keys(t);
                    for(let s = 0, u = i.length; s < u; s++){
                        let c = i[s];
                        o[c] = _n(t[c]);
                    }
                }
            } else if (le(e)) if (le(t)) {
                if (!Al(e, t)) return t;
                o = Object.assign({}, e);
                let i = Object.keys(t);
                for(let s = 0, u = i.length; s < u; s++){
                    let c = i[s], a = t[c];
                    if (qe(a)) typeof a > "u" ? delete o[c] : o[c] = a;
                    else {
                        let f = o[c], p = a;
                        if (ao(e, c, n) || ao(t, c, n)) delete o[c];
                        else {
                            if (le(f) && le(p)) {
                                let d = n.get(f) || [], m = n.get(p) || [];
                                d.push({
                                    obj: e,
                                    key: c
                                }), m.push({
                                    obj: t,
                                    key: c
                                }), n.set(f, d), n.set(p, m);
                            }
                            o[c] = so(o[c], a, r, n);
                        }
                    }
                }
            } else o = t;
            return o;
        }
    }
    function ao(e, t, r) {
        let n = r.get(e[t]) || [];
        for(let o = 0, i = n.length; o < i; o++){
            let s = n[o];
            if (s.key === t && s.obj === e) return !0;
        }
        return !1;
    }
    function Xe(e) {
        return Array.isArray(e);
    }
    function uo(e) {
        return typeof e == "function";
    }
    function le(e) {
        return !qe(e) && !Xe(e) && !uo(e) && typeof e == "object";
    }
    function qe(e) {
        return typeof e == "string" || typeof e == "number" || typeof e == "boolean" || typeof e > "u" || e instanceof Date || e instanceof RegExp || e === null;
    }
    function Al(e, t) {
        return !(!(0, oo.isPlainObject)(e) || !(0, oo.isPlainObject)(t));
    }
});
var lo = _((Ft)=>{
    "use strict";
    Object.defineProperty(Ft, "__esModule", {
        value: !0
    });
    Ft.callWithTimeout = Ft.TimeoutError = void 0;
    var We = class e extends Error {
        constructor(t){
            super(t), Object.setPrototypeOf(this, e.prototype);
        }
    };
    Ft.TimeoutError = We;
    function Rl(e, t) {
        let r, n = new Promise(function(i, s) {
            r = setTimeout(function() {
                s(new We("Operation timed out."));
            }, t);
        });
        return Promise.race([
            e,
            n
        ]).then((o)=>(clearTimeout(r), o), (o)=>{
            throw clearTimeout(r), o;
        });
    }
    Ft.callWithTimeout = Rl;
});
var po = _((jt)=>{
    "use strict";
    Object.defineProperty(jt, "__esModule", {
        value: !0
    });
    jt.isUrlIgnored = jt.urlMatches = void 0;
    function fo(e, t) {
        return typeof t == "string" ? e === t : !!e.match(t);
    }
    jt.urlMatches = fo;
    function Ol(e, t) {
        if (!t) return !1;
        for (let r of t)if (fo(e, r)) return !0;
        return !1;
    }
    jt.isUrlIgnored = Ol;
});
var _o = _((Ye)=>{
    "use strict";
    Object.defineProperty(Ye, "__esModule", {
        value: !0
    });
    Ye.isWrapped = void 0;
    function bl(e) {
        return typeof e == "function" && typeof e.__original == "function" && typeof e.__unwrap == "function" && e.__wrapped === !0;
    }
    Ye.isWrapped = bl;
});
var ho = _((ze)=>{
    "use strict";
    Object.defineProperty(ze, "__esModule", {
        value: !0
    });
    ze.Deferred = void 0;
    var hn = class {
        constructor(){
            this._promise = new Promise((t, r)=>{
                this._resolve = t, this._reject = r;
            });
        }
        get promise() {
            return this._promise;
        }
        resolve(t) {
            this._resolve(t);
        }
        reject(t) {
            this._reject(t);
        }
    };
    ze.Deferred = hn;
});
var Eo = _(($e)=>{
    "use strict";
    Object.defineProperty($e, "__esModule", {
        value: !0
    });
    $e.BindOnceFuture = void 0;
    var Ll = ho(), En = class {
        constructor(t, r){
            this._callback = t, this._that = r, this._isCalled = !1, this._deferred = new Ll.Deferred;
        }
        get isCalled() {
            return this._isCalled;
        }
        get promise() {
            return this._deferred.promise;
        }
        call(...t) {
            if (!this._isCalled) {
                this._isCalled = !0;
                try {
                    Promise.resolve(this._callback.call(this._that, ...t)).then((r)=>this._deferred.resolve(r), (r)=>this._deferred.reject(r));
                } catch (r) {
                    this._deferred.reject(r);
                }
            }
            return this._deferred.promise;
        }
    };
    $e.BindOnceFuture = En;
});
var To = _((Qe)=>{
    "use strict";
    Object.defineProperty(Qe, "__esModule", {
        value: !0
    });
    Qe._export = void 0;
    var mo = (E(), w(l)), Pl = ie();
    function Il(e, t) {
        return new Promise((r)=>{
            mo.context.with((0, Pl.suppressTracing)(mo.context.active()), ()=>{
                e.export(t, (n)=>{
                    r(n);
                });
            });
        });
    }
    Qe._export = Il;
});
var P = _((S)=>{
    "use strict";
    var Cl = S && S.__createBinding || (Object.create ? function(e, t, r, n) {
        n === void 0 && (n = r), Object.defineProperty(e, n, {
            enumerable: !0,
            get: function() {
                return t[r];
            }
        });
    } : function(e, t, r, n) {
        n === void 0 && (n = r), e[n] = t[r];
    }), R = S && S.__exportStar || function(e, t) {
        for(var r in e)r !== "default" && !Object.prototype.hasOwnProperty.call(t, r) && Cl(t, e, r);
    };
    Object.defineProperty(S, "__esModule", {
        value: !0
    });
    S.internal = S.baggageUtils = void 0;
    R(fi(), S);
    R(pi(), S);
    R(mi(), S);
    R(qr(), S);
    R(kr(), S);
    R(Di(), S);
    R(Bi(), S);
    R(Gi(), S);
    S.baggageUtils = Gr();
    R(zr(), S);
    R(Hi(), S);
    R(Wi(), S);
    R(zi(), S);
    R($i(), S);
    R(sn(), S);
    R(cn(), S);
    R(Zi(), S);
    R(Ji(), S);
    R(ie(), S);
    R(rn(), S);
    R(Oe(), S);
    R(co(), S);
    R(Xr(), S);
    R(lo(), S);
    R(po(), S);
    R(_o(), S);
    R(Eo(), S);
    R(Yr(), S);
    var Nl = To();
    S.internal = {
        _export: Nl._export
    };
});
var Vo = _((St)=>{
    "use strict";
    Object.defineProperty(St, "__esModule", {
        value: !0
    });
    St.disableInstrumentations = St.enableInstrumentations = St.parseInstrumentationOptions = void 0;
    function Go(e = []) {
        let t = [];
        for(let r = 0, n = e.length; r < n; r++){
            let o = e[r];
            if (Array.isArray(o)) {
                let i = Go(o);
                t = t.concat(i.instrumentations);
            } else typeof o == "function" ? t.push(new o) : o.instrumentationName && t.push(o);
        }
        return {
            instrumentations: t
        };
    }
    St.parseInstrumentationOptions = Go;
    function Yl(e, t, r) {
        for(let n = 0, o = e.length; n < o; n++){
            let i = e[n];
            t && i.setTracerProvider(t), r && i.setMeterProvider(r), i.getConfig().enabled || i.enable();
        }
    }
    St.enableInstrumentations = Yl;
    function zl(e) {
        e.forEach((t)=>t.disable());
    }
    St.disableInstrumentations = zl;
});
var Fo = _((nr)=>{
    "use strict";
    Object.defineProperty(nr, "__esModule", {
        value: !0
    });
    nr.registerInstrumentations = void 0;
    var Ho = (E(), w(l)), Tn = Vo();
    function $l(e) {
        let { instrumentations: t } = (0, Tn.parseInstrumentationOptions)(e.instrumentations), r = e.tracerProvider || Ho.trace.getTracerProvider(), n = e.meterProvider || Ho.metrics.getMeterProvider();
        return (0, Tn.enableInstrumentations)(t, r, n), ()=>{
            (0, Tn.disableInstrumentations)(t);
        };
    }
    nr.registerInstrumentations = $l;
});
var pe = {};
;
var Ua = Dr(()=>{
    Y(pe, __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$async_hooks__$5b$external$5d$__$28$node$3a$async_hooks$2c$__cjs$29$__);
});
var de = {};
;
var Ba = Dr(()=>{
    Y(de, __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$events__$5b$external$5d$__$28$node$3a$events$2c$__cjs$29$__);
});
var Ga = _((dr)=>{
    "use strict";
    Object.defineProperty(dr, "__esModule", {
        value: !0
    });
    dr.AbstractAsyncHooksContextManager = void 0;
    var bf = (Ba(), w(de)), Lf = [
        "addListener",
        "on",
        "once",
        "prependListener",
        "prependOnceListener"
    ], Cn = class {
        constructor(){
            this._kOtListeners = Symbol("OtListeners"), this._wrapped = !1;
        }
        bind(t, r) {
            return r instanceof bf.EventEmitter ? this._bindEventEmitter(t, r) : typeof r == "function" ? this._bindFunction(t, r) : r;
        }
        _bindFunction(t, r) {
            let n = this, o = function(...i) {
                return n.with(t, ()=>r.apply(this, i));
            };
            return Object.defineProperty(o, "length", {
                enumerable: !1,
                configurable: !0,
                writable: !1,
                value: r.length
            }), o;
        }
        _bindEventEmitter(t, r) {
            return this._getPatchMap(r) !== void 0 || (this._createPatchMap(r), Lf.forEach((o)=>{
                r[o] !== void 0 && (r[o] = this._patchAddListener(r, r[o], t));
            }), typeof r.removeListener == "function" && (r.removeListener = this._patchRemoveListener(r, r.removeListener)), typeof r.off == "function" && (r.off = this._patchRemoveListener(r, r.off)), typeof r.removeAllListeners == "function" && (r.removeAllListeners = this._patchRemoveAllListeners(r, r.removeAllListeners))), r;
        }
        _patchRemoveListener(t, r) {
            let n = this;
            return function(o, i) {
                var s;
                let u = (s = n._getPatchMap(t)) === null || s === void 0 ? void 0 : s[o];
                if (u === void 0) return r.call(this, o, i);
                let c = u.get(i);
                return r.call(this, o, c || i);
            };
        }
        _patchRemoveAllListeners(t, r) {
            let n = this;
            return function(o) {
                let i = n._getPatchMap(t);
                return i !== void 0 && (arguments.length === 0 ? n._createPatchMap(t) : i[o] !== void 0 && delete i[o]), r.apply(this, arguments);
            };
        }
        _patchAddListener(t, r, n) {
            let o = this;
            return function(i, s) {
                if (o._wrapped) return r.call(this, i, s);
                let u = o._getPatchMap(t);
                u === void 0 && (u = o._createPatchMap(t));
                let c = u[i];
                c === void 0 && (c = new WeakMap, u[i] = c);
                let a = o.bind(n, s);
                c.set(s, a), o._wrapped = !0;
                try {
                    return r.call(this, i, a);
                } finally{
                    o._wrapped = !1;
                }
            };
        }
        _createPatchMap(t) {
            let r = Object.create(null);
            return t[this._kOtListeners] = r, r;
        }
        _getPatchMap(t) {
            return t[this._kOtListeners];
        }
    };
    dr.AbstractAsyncHooksContextManager = Cn;
});
var Va = _((_r)=>{
    "use strict";
    Object.defineProperty(_r, "__esModule", {
        value: !0
    });
    _r.AsyncLocalStorageContextManager = void 0;
    var Pf = (E(), w(l)), If = (Ua(), w(pe)), Cf = Ga(), Nn = class extends Cf.AbstractAsyncHooksContextManager {
        constructor(){
            super(), this._asyncLocalStorage = new If.AsyncLocalStorage;
        }
        active() {
            var t;
            return (t = this._asyncLocalStorage.getStore()) !== null && t !== void 0 ? t : Pf.ROOT_CONTEXT;
        }
        with(t, r, n, ...o) {
            let i = n == null ? r : r.bind(n);
            return this._asyncLocalStorage.run(t, i, ...o);
        }
        enable() {
            return this;
        }
        disable() {
            return this._asyncLocalStorage.disable(), this;
        }
    };
    _r.AsyncLocalStorageContextManager = Nn;
});
var Bn = _((gt)=>{
    "use strict";
    Object.defineProperty(gt, "__esModule", {
        value: !0
    });
    gt.toAnyValue = gt.toKeyValue = gt.toAttributes = void 0;
    function Uf(e) {
        return Object.keys(e).map((t)=>Dn(t, e[t]));
    }
    gt.toAttributes = Uf;
    function Dn(e, t) {
        return {
            key: e,
            value: Un(t)
        };
    }
    gt.toKeyValue = Dn;
    function Un(e) {
        let t = typeof e;
        return t === "string" ? {
            stringValue: e
        } : t === "number" ? Number.isInteger(e) ? {
            intValue: e
        } : {
            doubleValue: e
        } : t === "boolean" ? {
            boolValue: e
        } : e instanceof Uint8Array ? {
            bytesValue: e
        } : Array.isArray(e) ? {
            arrayValue: {
                values: e.map(Un)
            }
        } : t === "object" && e != null ? {
            kvlistValue: {
                values: Object.entries(e).map(([r, n])=>Dn(r, n))
            }
        } : {};
    }
    gt.toAnyValue = Un;
});
var Ja = _((yt)=>{
    "use strict";
    Object.defineProperty(yt, "__esModule", {
        value: !0
    });
    yt.toOtlpSpanEvent = yt.toOtlpLink = yt.sdkSpanToOtlpSpan = void 0;
    var Gn = Bn();
    function Bf(e, t) {
        var r;
        let n = e.spanContext(), o = e.status;
        return {
            traceId: t.encodeSpanContext(n.traceId),
            spanId: t.encodeSpanContext(n.spanId),
            parentSpanId: t.encodeOptionalSpanContext(e.parentSpanId),
            traceState: (r = n.traceState) === null || r === void 0 ? void 0 : r.serialize(),
            name: e.name,
            kind: e.kind == null ? 0 : e.kind + 1,
            startTimeUnixNano: t.encodeHrTime(e.startTime),
            endTimeUnixNano: t.encodeHrTime(e.endTime),
            attributes: (0, Gn.toAttributes)(e.attributes),
            droppedAttributesCount: e.droppedAttributesCount,
            events: e.events.map((i)=>Za(i, t)),
            droppedEventsCount: e.droppedEventsCount,
            status: {
                code: o.code,
                message: o.message
            },
            links: e.links.map((i)=>Qa(i, t)),
            droppedLinksCount: e.droppedLinksCount
        };
    }
    yt.sdkSpanToOtlpSpan = Bf;
    function Qa(e, t) {
        var r;
        return {
            attributes: e.attributes ? (0, Gn.toAttributes)(e.attributes) : [],
            spanId: t.encodeSpanContext(e.context.spanId),
            traceId: t.encodeSpanContext(e.context.traceId),
            traceState: (r = e.context.traceState) === null || r === void 0 ? void 0 : r.serialize(),
            droppedAttributesCount: e.droppedAttributesCount || 0
        };
    }
    yt.toOtlpLink = Qa;
    function Za(e, t) {
        return {
            attributes: e.attributes ? (0, Gn.toAttributes)(e.attributes) : [],
            name: e.name,
            timeUnixNano: t.encodeHrTime(e.time),
            droppedAttributesCount: e.droppedAttributesCount || 0
        };
    }
    yt.toOtlpSpanEvent = Za;
});
var is = _((K)=>{
    "use strict";
    Object.defineProperty(K, "__esModule", {
        value: !0
    });
    K.getOtlpEncoder = K.encodeAsString = K.encodeAsLongBits = K.toLongBits = K.hrTimeToNanos = void 0;
    var Tr = P(), Gf = BigInt(1e9);
    function Vn(e) {
        return BigInt(e[0]) * Gf + BigInt(e[1]);
    }
    K.hrTimeToNanos = Vn;
    function es(e) {
        let t = Number(BigInt.asUintN(32, e)), r = Number(BigInt.asUintN(32, e >> BigInt(32)));
        return {
            low: t,
            high: r
        };
    }
    K.toLongBits = es;
    function Hn(e) {
        let t = Vn(e);
        return es(t);
    }
    K.encodeAsLongBits = Hn;
    function rs(e) {
        return Vn(e).toString();
    }
    K.encodeAsString = rs;
    var Vf = typeof BigInt < "u" ? rs : Tr.hrTimeToNanoseconds;
    function ts(e) {
        return e;
    }
    function ns(e) {
        if (e !== void 0) return (0, Tr.hexToBase64)(e);
    }
    var Hf = {
        encodeHrTime: Hn,
        encodeSpanContext: Tr.hexToBase64,
        encodeOptionalSpanContext: ns
    };
    function Ff(e) {
        var t, r;
        if (e === void 0) return Hf;
        let n = (t = e.useLongBits) !== null && t !== void 0 ? t : !0, o = (r = e.useHex) !== null && r !== void 0 ? r : !1;
        return {
            encodeHrTime: n ? Hn : Vf,
            encodeSpanContext: o ? ts : Tr.hexToBase64,
            encodeOptionalSpanContext: o ? ts : ns
        };
    }
    K.getOtlpEncoder = Ff;
});
var Fn = _((Sr)=>{
    "use strict";
    Object.defineProperty(Sr, "__esModule", {
        value: !0
    });
    Sr.createExportTraceServiceRequest = void 0;
    var jf = Bn(), kf = Ja(), qf = is();
    function Xf(e, t) {
        let r = (0, qf.getOtlpEncoder)(t);
        return {
            resourceSpans: Wf(e, r)
        };
    }
    Sr.createExportTraceServiceRequest = Xf;
    function Kf(e) {
        let t = new Map;
        for (let r of e){
            let n = t.get(r.resource);
            n || (n = new Map, t.set(r.resource, n));
            let o = `${r.instrumentationLibrary.name}@${r.instrumentationLibrary.version || ""}:${r.instrumentationLibrary.schemaUrl || ""}`, i = n.get(o);
            i || (i = [], n.set(o, i)), i.push(r);
        }
        return t;
    }
    function Wf(e, t) {
        let r = Kf(e), n = [], o = r.entries(), i = o.next();
        for(; !i.done;){
            let [s, u] = i.value, c = [], a = u.values(), f = a.next();
            for(; !f.done;){
                let d = f.value;
                if (d.length > 0) {
                    let { name: m, version: g, schemaUrl: C } = d[0].instrumentationLibrary, M = d.map((T)=>(0, kf.sdkSpanToOtlpSpan)(T, t));
                    c.push({
                        scope: {
                            name: m,
                            version: g
                        },
                        spans: M,
                        schemaUrl: C
                    });
                }
                f = a.next();
            }
            let p = {
                resource: {
                    attributes: (0, jf.toAttributes)(s.attributes),
                    droppedAttributesCount: 0
                },
                scopeSpans: c,
                schemaUrl: void 0
            };
            n.push(p), i = o.next();
        }
        return n;
    }
});
var ss = _((I)=>{
    "use strict";
    Object.defineProperty(I, "__esModule", {
        value: !0
    });
    I.parseRetryAfterToMills = I.isExportRetryable = I.invalidTimeout = I.configureExporterTimeout = I.appendRootPathToUrlIfNeeded = I.appendResourcePathToUrl = I.parseHeaders = I.DEFAULT_EXPORT_BACKOFF_MULTIPLIER = I.DEFAULT_EXPORT_MAX_BACKOFF = I.DEFAULT_EXPORT_INITIAL_BACKOFF = I.DEFAULT_EXPORT_MAX_ATTEMPTS = void 0;
    var jn = (E(), w(l)), os = P(), as = 1e4;
    I.DEFAULT_EXPORT_MAX_ATTEMPTS = 5;
    I.DEFAULT_EXPORT_INITIAL_BACKOFF = 1e3;
    I.DEFAULT_EXPORT_MAX_BACKOFF = 5e3;
    I.DEFAULT_EXPORT_BACKOFF_MULTIPLIER = 1.5;
    function Yf(e = {}) {
        let t = {};
        return Object.entries(e).forEach(([r, n])=>{
            typeof n < "u" ? t[r] = String(n) : jn.diag.warn(`Header "${r}" has wrong value and will be ignored`);
        }), t;
    }
    I.parseHeaders = Yf;
    function zf(e, t) {
        return e.endsWith("/") || (e = e + "/"), e + t;
    }
    I.appendResourcePathToUrl = zf;
    function $f(e) {
        try {
            let t = new URL(e);
            return t.pathname === "" && (t.pathname = t.pathname + "/"), t.toString();
        } catch  {
            return jn.diag.warn(`Could not parse export URL: '${e}'`), e;
        }
    }
    I.appendRootPathToUrlIfNeeded = $f;
    function Qf(e) {
        return typeof e == "number" ? e <= 0 ? kn(e, as) : e : Zf();
    }
    I.configureExporterTimeout = Qf;
    function Zf() {
        var e;
        let t = Number((e = (0, os.getEnv)().OTEL_EXPORTER_OTLP_TRACES_TIMEOUT) !== null && e !== void 0 ? e : (0, os.getEnv)().OTEL_EXPORTER_OTLP_TIMEOUT);
        return t <= 0 ? kn(t, as) : t;
    }
    function kn(e, t) {
        return jn.diag.warn("Timeout must be greater than 0", e), t;
    }
    I.invalidTimeout = kn;
    function Jf(e) {
        return [
            429,
            502,
            503,
            504
        ].includes(e);
    }
    I.isExportRetryable = Jf;
    function tp(e) {
        if (e == null) return -1;
        let t = Number.parseInt(e, 10);
        if (Number.isInteger(t)) return t > 0 ? t * 1e3 : -1;
        let r = new Date(e).getTime() - Date.now();
        return r >= 0 ? r : 0;
    }
    I.parseRetryAfterToMills = tp;
});
var cs = _((gr)=>{
    "use strict";
    Object.defineProperty(gr, "__esModule", {
        value: !0
    });
    gr.OTLPExporterBase = void 0;
    var us = (E(), w(l)), _e = P(), ep = ss(), qn = class {
        constructor(t = {}){
            this._sendingPromises = [], this.url = this.getDefaultUrl(t), typeof t.hostname == "string" && (this.hostname = t.hostname), this.shutdown = this.shutdown.bind(this), this._shutdownOnce = new _e.BindOnceFuture(this._shutdown, this), this._concurrencyLimit = typeof t.concurrencyLimit == "number" ? t.concurrencyLimit : 30, this.timeoutMillis = (0, ep.configureExporterTimeout)(t.timeoutMillis), this.onInit(t);
        }
        export(t, r) {
            if (this._shutdownOnce.isCalled) {
                r({
                    code: _e.ExportResultCode.FAILED,
                    error: new Error("Exporter has been shutdown")
                });
                return;
            }
            if (this._sendingPromises.length >= this._concurrencyLimit) {
                r({
                    code: _e.ExportResultCode.FAILED,
                    error: new Error("Concurrent export limit reached")
                });
                return;
            }
            this._export(t).then(()=>{
                r({
                    code: _e.ExportResultCode.SUCCESS
                });
            }).catch((n)=>{
                r({
                    code: _e.ExportResultCode.FAILED,
                    error: n
                });
            });
        }
        _export(t) {
            return new Promise((r, n)=>{
                try {
                    us.diag.debug("items to be sent", t), this.send(t, r, n);
                } catch (o) {
                    n(o);
                }
            });
        }
        shutdown() {
            return this._shutdownOnce.call();
        }
        forceFlush() {
            return Promise.all(this._sendingPromises).then(()=>{});
        }
        _shutdown() {
            return us.diag.debug("shutdown started"), this.onShutdown(), this.forceFlush();
        }
    };
    gr.OTLPExporterBase = qn;
});
var ds = _((um, ps)=>{
    "use strict";
    ps.exports = ip;
    function ip(e, t) {
        for(var r = new Array(arguments.length - 1), n = 0, o = 2, i = !0; o < arguments.length;)r[n++] = arguments[o++];
        return new Promise(function(u, c) {
            r[n] = function(f) {
                if (i) if (i = !1, f) c(f);
                else {
                    for(var p = new Array(arguments.length - 1), d = 0; d < p.length;)p[d++] = arguments[d];
                    u.apply(null, p);
                }
            };
            try {
                e.apply(t || null, r);
            } catch (a) {
                i && (i = !1, c(a));
            }
        });
    }
});
var ms = _((Es)=>{
    "use strict";
    var vr = Es;
    vr.length = function(t) {
        var r = t.length;
        if (!r) return 0;
        for(var n = 0; --r % 4 > 1 && t.charAt(r) === "=";)++n;
        return Math.ceil(t.length * 3) / 4 - n;
    };
    var Zt = new Array(64), hs = new Array(123);
    for(Z = 0; Z < 64;)hs[Zt[Z] = Z < 26 ? Z + 65 : Z < 52 ? Z + 71 : Z < 62 ? Z - 4 : Z - 59 | 43] = Z++;
    var Z;
    vr.encode = function(t, r, n) {
        for(var o = null, i = [], s = 0, u = 0, c; r < n;){
            var a = t[r++];
            switch(u){
                case 0:
                    i[s++] = Zt[a >> 2], c = (a & 3) << 4, u = 1;
                    break;
                case 1:
                    i[s++] = Zt[c | a >> 4], c = (a & 15) << 2, u = 2;
                    break;
                case 2:
                    i[s++] = Zt[c | a >> 6], i[s++] = Zt[a & 63], u = 0;
                    break;
            }
            s > 8191 && ((o || (o = [])).push(String.fromCharCode.apply(String, i)), s = 0);
        }
        return u && (i[s++] = Zt[c], i[s++] = 61, u === 1 && (i[s++] = 61)), o ? (s && o.push(String.fromCharCode.apply(String, i.slice(0, s))), o.join("")) : String.fromCharCode.apply(String, i.slice(0, s));
    };
    var _s = "invalid encoding";
    vr.decode = function(t, r, n) {
        for(var o = n, i = 0, s, u = 0; u < t.length;){
            var c = t.charCodeAt(u++);
            if (c === 61 && i > 1) break;
            if ((c = hs[c]) === void 0) throw Error(_s);
            switch(i){
                case 0:
                    s = c, i = 1;
                    break;
                case 1:
                    r[n++] = s << 2 | (c & 48) >> 4, s = c, i = 2;
                    break;
                case 2:
                    r[n++] = (s & 15) << 4 | (c & 60) >> 2, s = c, i = 3;
                    break;
                case 3:
                    r[n++] = (s & 3) << 6 | c, i = 0;
                    break;
            }
        }
        if (i === 1) throw Error(_s);
        return n - o;
    };
    vr.test = function(t) {
        return /^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/.test(t);
    };
});
var Ss = _((lm, Ts)=>{
    "use strict";
    Ts.exports = Ar;
    function Ar() {
        this._listeners = {};
    }
    Ar.prototype.on = function(t, r, n) {
        return (this._listeners[t] || (this._listeners[t] = [])).push({
            fn: r,
            ctx: n || this
        }), this;
    };
    Ar.prototype.off = function(t, r) {
        if (t === void 0) this._listeners = {};
        else if (r === void 0) this._listeners[t] = [];
        else for(var n = this._listeners[t], o = 0; o < n.length;)n[o].fn === r ? n.splice(o, 1) : ++o;
        return this;
    };
    Ar.prototype.emit = function(t) {
        var r = this._listeners[t];
        if (r) {
            for(var n = [], o = 1; o < arguments.length;)n.push(arguments[o++]);
            for(o = 0; o < r.length;)r[o].fn.apply(r[o++].ctx, n);
        }
        return this;
    };
});
var bs = _((fm, Os)=>{
    "use strict";
    Os.exports = gs(gs);
    function gs(e) {
        return typeof Float32Array < "u" ? function() {
            var t = new Float32Array([
                -0
            ]), r = new Uint8Array(t.buffer), n = r[3] === 128;
            function o(c, a, f) {
                t[0] = c, a[f] = r[0], a[f + 1] = r[1], a[f + 2] = r[2], a[f + 3] = r[3];
            }
            function i(c, a, f) {
                t[0] = c, a[f] = r[3], a[f + 1] = r[2], a[f + 2] = r[1], a[f + 3] = r[0];
            }
            e.writeFloatLE = n ? o : i, e.writeFloatBE = n ? i : o;
            function s(c, a) {
                return r[0] = c[a], r[1] = c[a + 1], r[2] = c[a + 2], r[3] = c[a + 3], t[0];
            }
            function u(c, a) {
                return r[3] = c[a], r[2] = c[a + 1], r[1] = c[a + 2], r[0] = c[a + 3], t[0];
            }
            e.readFloatLE = n ? s : u, e.readFloatBE = n ? u : s;
        }() : function() {
            function t(n, o, i, s) {
                var u = o < 0 ? 1 : 0;
                if (u && (o = -o), o === 0) n(1 / o > 0 ? 0 : 2147483648, i, s);
                else if (isNaN(o)) n(2143289344, i, s);
                else if (o > 34028234663852886e22) n((u << 31 | 2139095040) >>> 0, i, s);
                else if (o < 11754943508222875e-54) n((u << 31 | Math.round(o / 1401298464324817e-60)) >>> 0, i, s);
                else {
                    var c = Math.floor(Math.log(o) / Math.LN2), a = Math.round(o * Math.pow(2, -c) * 8388608) & 8388607;
                    n((u << 31 | c + 127 << 23 | a) >>> 0, i, s);
                }
            }
            e.writeFloatLE = t.bind(null, ys), e.writeFloatBE = t.bind(null, vs);
            function r(n, o, i) {
                var s = n(o, i), u = (s >> 31) * 2 + 1, c = s >>> 23 & 255, a = s & 8388607;
                return c === 255 ? a ? NaN : u * (1 / 0) : c === 0 ? u * 1401298464324817e-60 * a : u * Math.pow(2, c - 150) * (a + 8388608);
            }
            e.readFloatLE = r.bind(null, As), e.readFloatBE = r.bind(null, Rs);
        }(), typeof Float64Array < "u" ? function() {
            var t = new Float64Array([
                -0
            ]), r = new Uint8Array(t.buffer), n = r[7] === 128;
            function o(c, a, f) {
                t[0] = c, a[f] = r[0], a[f + 1] = r[1], a[f + 2] = r[2], a[f + 3] = r[3], a[f + 4] = r[4], a[f + 5] = r[5], a[f + 6] = r[6], a[f + 7] = r[7];
            }
            function i(c, a, f) {
                t[0] = c, a[f] = r[7], a[f + 1] = r[6], a[f + 2] = r[5], a[f + 3] = r[4], a[f + 4] = r[3], a[f + 5] = r[2], a[f + 6] = r[1], a[f + 7] = r[0];
            }
            e.writeDoubleLE = n ? o : i, e.writeDoubleBE = n ? i : o;
            function s(c, a) {
                return r[0] = c[a], r[1] = c[a + 1], r[2] = c[a + 2], r[3] = c[a + 3], r[4] = c[a + 4], r[5] = c[a + 5], r[6] = c[a + 6], r[7] = c[a + 7], t[0];
            }
            function u(c, a) {
                return r[7] = c[a], r[6] = c[a + 1], r[5] = c[a + 2], r[4] = c[a + 3], r[3] = c[a + 4], r[2] = c[a + 5], r[1] = c[a + 6], r[0] = c[a + 7], t[0];
            }
            e.readDoubleLE = n ? s : u, e.readDoubleBE = n ? u : s;
        }() : function() {
            function t(n, o, i, s, u, c) {
                var a = s < 0 ? 1 : 0;
                if (a && (s = -s), s === 0) n(0, u, c + o), n(1 / s > 0 ? 0 : 2147483648, u, c + i);
                else if (isNaN(s)) n(0, u, c + o), n(2146959360, u, c + i);
                else if (s > 17976931348623157e292) n(0, u, c + o), n((a << 31 | 2146435072) >>> 0, u, c + i);
                else {
                    var f;
                    if (s < 22250738585072014e-324) f = s / 5e-324, n(f >>> 0, u, c + o), n((a << 31 | f / 4294967296) >>> 0, u, c + i);
                    else {
                        var p = Math.floor(Math.log(s) / Math.LN2);
                        p === 1024 && (p = 1023), f = s * Math.pow(2, -p), n(f * 4503599627370496 >>> 0, u, c + o), n((a << 31 | p + 1023 << 20 | f * 1048576 & 1048575) >>> 0, u, c + i);
                    }
                }
            }
            e.writeDoubleLE = t.bind(null, ys, 0, 4), e.writeDoubleBE = t.bind(null, vs, 4, 0);
            function r(n, o, i, s, u) {
                var c = n(s, u + o), a = n(s, u + i), f = (a >> 31) * 2 + 1, p = a >>> 20 & 2047, d = 4294967296 * (a & 1048575) + c;
                return p === 2047 ? d ? NaN : f * (1 / 0) : p === 0 ? f * 5e-324 * d : f * Math.pow(2, p - 1075) * (d + 4503599627370496);
            }
            e.readDoubleLE = r.bind(null, As, 0, 4), e.readDoubleBE = r.bind(null, Rs, 4, 0);
        }(), e;
    }
    function ys(e, t, r) {
        t[r] = e & 255, t[r + 1] = e >>> 8 & 255, t[r + 2] = e >>> 16 & 255, t[r + 3] = e >>> 24;
    }
    function vs(e, t, r) {
        t[r] = e >>> 24, t[r + 1] = e >>> 16 & 255, t[r + 2] = e >>> 8 & 255, t[r + 3] = e & 255;
    }
    function As(e, t) {
        return (e[t] | e[t + 1] << 8 | e[t + 2] << 16 | e[t + 3] << 24) >>> 0;
    }
    function Rs(e, t) {
        return (e[t] << 24 | e[t + 1] << 16 | e[t + 2] << 8 | e[t + 3]) >>> 0;
    }
});
var Ps = _((pm, Ls)=>{
    "use strict";
    Ls.exports = op;
    function op(e) {
        return null;
    }
});
var Cs = _((Is)=>{
    "use strict";
    var Kn = Is;
    Kn.length = function(t) {
        for(var r = 0, n = 0, o = 0; o < t.length; ++o)n = t.charCodeAt(o), n < 128 ? r += 1 : n < 2048 ? r += 2 : (n & 64512) === 55296 && (t.charCodeAt(o + 1) & 64512) === 56320 ? (++o, r += 4) : r += 3;
        return r;
    };
    Kn.read = function(t, r, n) {
        var o = n - r;
        if (o < 1) return "";
        for(var i = null, s = [], u = 0, c; r < n;)c = t[r++], c < 128 ? s[u++] = c : c > 191 && c < 224 ? s[u++] = (c & 31) << 6 | t[r++] & 63 : c > 239 && c < 365 ? (c = ((c & 7) << 18 | (t[r++] & 63) << 12 | (t[r++] & 63) << 6 | t[r++] & 63) - 65536, s[u++] = 55296 + (c >> 10), s[u++] = 56320 + (c & 1023)) : s[u++] = (c & 15) << 12 | (t[r++] & 63) << 6 | t[r++] & 63, u > 8191 && ((i || (i = [])).push(String.fromCharCode.apply(String, s)), u = 0);
        return i ? (u && i.push(String.fromCharCode.apply(String, s.slice(0, u))), i.join("")) : String.fromCharCode.apply(String, s.slice(0, u));
    };
    Kn.write = function(t, r, n) {
        for(var o = n, i, s, u = 0; u < t.length; ++u)i = t.charCodeAt(u), i < 128 ? r[n++] = i : i < 2048 ? (r[n++] = i >> 6 | 192, r[n++] = i & 63 | 128) : (i & 64512) === 55296 && ((s = t.charCodeAt(u + 1)) & 64512) === 56320 ? (i = 65536 + ((i & 1023) << 10) + (s & 1023), ++u, r[n++] = i >> 18 | 240, r[n++] = i >> 12 & 63 | 128, r[n++] = i >> 6 & 63 | 128, r[n++] = i & 63 | 128) : (r[n++] = i >> 12 | 224, r[n++] = i >> 6 & 63 | 128, r[n++] = i & 63 | 128);
        return n - o;
    };
});
var ws = _((_m, Ns)=>{
    "use strict";
    Ns.exports = ap;
    function ap(e, t, r) {
        var n = r || 8192, o = n >>> 1, i = null, s = n;
        return function(c) {
            if (c < 1 || c > o) return e(c);
            s + c > n && (i = e(n), s = 0);
            var a = t.call(i, s, s += c);
            return s & 7 && (s = (s | 7) + 1), a;
        };
    }
});
var xs = _((hm, Ms)=>{
    "use strict";
    Ms.exports = D;
    var he = At();
    function D(e, t) {
        this.lo = e >>> 0, this.hi = t >>> 0;
    }
    var Dt = D.zero = new D(0, 0);
    Dt.toNumber = function() {
        return 0;
    };
    Dt.zzEncode = Dt.zzDecode = function() {
        return this;
    };
    Dt.length = function() {
        return 1;
    };
    var sp = D.zeroHash = "\0\0\0\0\0\0\0\0";
    D.fromNumber = function(t) {
        if (t === 0) return Dt;
        var r = t < 0;
        r && (t = -t);
        var n = t >>> 0, o = (t - n) / 4294967296 >>> 0;
        return r && (o = ~o >>> 0, n = ~n >>> 0, ++n > 4294967295 && (n = 0, ++o > 4294967295 && (o = 0))), new D(n, o);
    };
    D.from = function(t) {
        if (typeof t == "number") return D.fromNumber(t);
        if (he.isString(t)) if (he.Long) t = he.Long.fromString(t);
        else return D.fromNumber(parseInt(t, 10));
        return t.low || t.high ? new D(t.low >>> 0, t.high >>> 0) : Dt;
    };
    D.prototype.toNumber = function(t) {
        if (!t && this.hi >>> 31) {
            var r = ~this.lo + 1 >>> 0, n = ~this.hi >>> 0;
            return r || (n = n + 1 >>> 0), -(r + n * 4294967296);
        }
        return this.lo + this.hi * 4294967296;
    };
    D.prototype.toLong = function(t) {
        return he.Long ? new he.Long(this.lo | 0, this.hi | 0, !!t) : {
            low: this.lo | 0,
            high: this.hi | 0,
            unsigned: !!t
        };
    };
    var vt = String.prototype.charCodeAt;
    D.fromHash = function(t) {
        return t === sp ? Dt : new D((vt.call(t, 0) | vt.call(t, 1) << 8 | vt.call(t, 2) << 16 | vt.call(t, 3) << 24) >>> 0, (vt.call(t, 4) | vt.call(t, 5) << 8 | vt.call(t, 6) << 16 | vt.call(t, 7) << 24) >>> 0);
    };
    D.prototype.toHash = function() {
        return String.fromCharCode(this.lo & 255, this.lo >>> 8 & 255, this.lo >>> 16 & 255, this.lo >>> 24, this.hi & 255, this.hi >>> 8 & 255, this.hi >>> 16 & 255, this.hi >>> 24);
    };
    D.prototype.zzEncode = function() {
        var t = this.hi >> 31;
        return this.hi = ((this.hi << 1 | this.lo >>> 31) ^ t) >>> 0, this.lo = (this.lo << 1 ^ t) >>> 0, this;
    };
    D.prototype.zzDecode = function() {
        var t = -(this.lo & 1);
        return this.lo = ((this.lo >>> 1 | this.hi << 31) ^ t) >>> 0, this.hi = (this.hi >>> 1 ^ t) >>> 0, this;
    };
    D.prototype.length = function() {
        var t = this.lo, r = (this.lo >>> 28 | this.hi << 4) >>> 0, n = this.hi >>> 24;
        return n === 0 ? r === 0 ? t < 16384 ? t < 128 ? 1 : 2 : t < 2097152 ? 3 : 4 : r < 16384 ? r < 128 ? 5 : 6 : r < 2097152 ? 7 : 8 : n < 128 ? 9 : 10;
    };
});
var At = _((Wn)=>{
    "use strict";
    var h = Wn;
    h.asPromise = ds();
    h.base64 = ms();
    h.EventEmitter = Ss();
    h.float = bs();
    h.inquire = Ps();
    h.utf8 = Cs();
    h.pool = ws();
    h.LongBits = xs();
    h.isNode = !!(typeof global < "u" && global && global.process && global.process.versions && global.process.versions.node);
    h.global = h.isNode && global || "undefined" < "u" && window || typeof self < "u" && self || Wn;
    h.emptyArray = Object.freeze ? Object.freeze([]) : [];
    h.emptyObject = Object.freeze ? Object.freeze({}) : {};
    h.isInteger = Number.isInteger || function(t) {
        return typeof t == "number" && isFinite(t) && Math.floor(t) === t;
    };
    h.isString = function(t) {
        return typeof t == "string" || t instanceof String;
    };
    h.isObject = function(t) {
        return t && typeof t == "object";
    };
    h.isset = h.isSet = function(t, r) {
        var n = t[r];
        return n != null && t.hasOwnProperty(r) ? typeof n != "object" || (Array.isArray(n) ? n.length : Object.keys(n).length) > 0 : !1;
    };
    h.Buffer = function() {
        try {
            var e = h.inquire("buffer").Buffer;
            return e.prototype.utf8Write ? e : null;
        } catch  {
            return null;
        }
    }();
    h._Buffer_from = null;
    h._Buffer_allocUnsafe = null;
    h.newBuffer = function(t) {
        return typeof t == "number" ? h.Buffer ? h._Buffer_allocUnsafe(t) : new h.Array(t) : h.Buffer ? h._Buffer_from(t) : typeof Uint8Array > "u" ? t : new Uint8Array(t);
    };
    h.Array = typeof Uint8Array < "u" ? Uint8Array : Array;
    h.Long = h.global.dcodeIO && h.global.dcodeIO.Long || h.global.Long || h.inquire("long");
    h.key2Re = /^true|false|0|1$/;
    h.key32Re = /^-?(?:0|[1-9][0-9]*)$/;
    h.key64Re = /^(?:[\\x00-\\xff]{8}|-?(?:0|[1-9][0-9]*))$/;
    h.longToHash = function(t) {
        return t ? h.LongBits.from(t).toHash() : h.LongBits.zeroHash;
    };
    h.longFromHash = function(t, r) {
        var n = h.LongBits.fromHash(t);
        return h.Long ? h.Long.fromBits(n.lo, n.hi, r) : n.toNumber(!!r);
    };
    function Ds(e, t, r) {
        for(var n = Object.keys(t), o = 0; o < n.length; ++o)(e[n[o]] === void 0 || !r) && (e[n[o]] = t[n[o]]);
        return e;
    }
    h.merge = Ds;
    h.lcFirst = function(t) {
        return t.charAt(0).toLowerCase() + t.substring(1);
    };
    function Us(e) {
        function t(r, n) {
            if (!(this instanceof t)) return new t(r, n);
            Object.defineProperty(this, "message", {
                get: function() {
                    return r;
                }
            }), Error.captureStackTrace ? Error.captureStackTrace(this, t) : Object.defineProperty(this, "stack", {
                value: new Error().stack || ""
            }), n && Ds(this, n);
        }
        return t.prototype = Object.create(Error.prototype, {
            constructor: {
                value: t,
                writable: !0,
                enumerable: !1,
                configurable: !0
            },
            name: {
                get: function() {
                    return e;
                },
                set: void 0,
                enumerable: !1,
                configurable: !0
            },
            toString: {
                value: function() {
                    return this.name + ": " + this.message;
                },
                writable: !0,
                enumerable: !1,
                configurable: !0
            }
        }), t;
    }
    h.newError = Us;
    h.ProtocolError = Us("ProtocolError");
    h.oneOfGetter = function(t) {
        for(var r = {}, n = 0; n < t.length; ++n)r[t[n]] = 1;
        return function() {
            for(var o = Object.keys(this), i = o.length - 1; i > -1; --i)if (r[o[i]] === 1 && this[o[i]] !== void 0 && this[o[i]] !== null) return o[i];
        };
    };
    h.oneOfSetter = function(t) {
        return function(r) {
            for(var n = 0; n < t.length; ++n)t[n] !== r && delete this[t[n]];
        };
    };
    h.toJSONOptions = {
        longs: String,
        enums: String,
        bytes: String,
        json: !0
    };
    h._configure = function() {
        var e = h.Buffer;
        if (!e) {
            h._Buffer_from = h._Buffer_allocUnsafe = null;
            return;
        }
        h._Buffer_from = e.from !== Uint8Array.from && e.from || function(r, n) {
            return new e(r, n);
        }, h._Buffer_allocUnsafe = e.allocUnsafe || function(r) {
            return new e(r);
        };
    };
});
var ti = _((mm, Hs)=>{
    "use strict";
    Hs.exports = y;
    var W = At(), Yn, Rr = W.LongBits, Bs = W.base64, Gs = W.utf8;
    function Ee(e, t, r) {
        this.fn = e, this.len = t, this.next = void 0, this.val = r;
    }
    function $n() {}
    function up(e) {
        this.head = e.head, this.tail = e.tail, this.len = e.len, this.next = e.states;
    }
    function y() {
        this.len = 0, this.head = new Ee($n, 0, 0), this.tail = this.head, this.states = null;
    }
    var Vs = function() {
        return W.Buffer ? function() {
            return (y.create = function() {
                return new Yn;
            })();
        } : function() {
            return new y;
        };
    };
    y.create = Vs();
    y.alloc = function(t) {
        return new W.Array(t);
    };
    W.Array !== Array && (y.alloc = W.pool(y.alloc, W.Array.prototype.subarray));
    y.prototype._push = function(t, r, n) {
        return this.tail = this.tail.next = new Ee(t, r, n), this.len += r, this;
    };
    function Qn(e, t, r) {
        t[r] = e & 255;
    }
    function cp(e, t, r) {
        for(; e > 127;)t[r++] = e & 127 | 128, e >>>= 7;
        t[r] = e;
    }
    function Zn(e, t) {
        this.len = e, this.next = void 0, this.val = t;
    }
    Zn.prototype = Object.create(Ee.prototype);
    Zn.prototype.fn = cp;
    y.prototype.uint32 = function(t) {
        return this.len += (this.tail = this.tail.next = new Zn((t = t >>> 0) < 128 ? 1 : t < 16384 ? 2 : t < 2097152 ? 3 : t < 268435456 ? 4 : 5, t)).len, this;
    };
    y.prototype.int32 = function(t) {
        return t < 0 ? this._push(Jn, 10, Rr.fromNumber(t)) : this.uint32(t);
    };
    y.prototype.sint32 = function(t) {
        return this.uint32((t << 1 ^ t >> 31) >>> 0);
    };
    function Jn(e, t, r) {
        for(; e.hi;)t[r++] = e.lo & 127 | 128, e.lo = (e.lo >>> 7 | e.hi << 25) >>> 0, e.hi >>>= 7;
        for(; e.lo > 127;)t[r++] = e.lo & 127 | 128, e.lo = e.lo >>> 7;
        t[r++] = e.lo;
    }
    y.prototype.uint64 = function(t) {
        var r = Rr.from(t);
        return this._push(Jn, r.length(), r);
    };
    y.prototype.int64 = y.prototype.uint64;
    y.prototype.sint64 = function(t) {
        var r = Rr.from(t).zzEncode();
        return this._push(Jn, r.length(), r);
    };
    y.prototype.bool = function(t) {
        return this._push(Qn, 1, t ? 1 : 0);
    };
    function zn(e, t, r) {
        t[r] = e & 255, t[r + 1] = e >>> 8 & 255, t[r + 2] = e >>> 16 & 255, t[r + 3] = e >>> 24;
    }
    y.prototype.fixed32 = function(t) {
        return this._push(zn, 4, t >>> 0);
    };
    y.prototype.sfixed32 = y.prototype.fixed32;
    y.prototype.fixed64 = function(t) {
        var r = Rr.from(t);
        return this._push(zn, 4, r.lo)._push(zn, 4, r.hi);
    };
    y.prototype.sfixed64 = y.prototype.fixed64;
    y.prototype.float = function(t) {
        return this._push(W.float.writeFloatLE, 4, t);
    };
    y.prototype.double = function(t) {
        return this._push(W.float.writeDoubleLE, 8, t);
    };
    var lp = W.Array.prototype.set ? function(t, r, n) {
        r.set(t, n);
    } : function(t, r, n) {
        for(var o = 0; o < t.length; ++o)r[n + o] = t[o];
    };
    y.prototype.bytes = function(t) {
        var r = t.length >>> 0;
        if (!r) return this._push(Qn, 1, 0);
        if (W.isString(t)) {
            var n = y.alloc(r = Bs.length(t));
            Bs.decode(t, n, 0), t = n;
        }
        return this.uint32(r)._push(lp, r, t);
    };
    y.prototype.string = function(t) {
        var r = Gs.length(t);
        return r ? this.uint32(r)._push(Gs.write, r, t) : this._push(Qn, 1, 0);
    };
    y.prototype.fork = function() {
        return this.states = new up(this), this.head = this.tail = new Ee($n, 0, 0), this.len = 0, this;
    };
    y.prototype.reset = function() {
        return this.states ? (this.head = this.states.head, this.tail = this.states.tail, this.len = this.states.len, this.states = this.states.next) : (this.head = this.tail = new Ee($n, 0, 0), this.len = 0), this;
    };
    y.prototype.ldelim = function() {
        var t = this.head, r = this.tail, n = this.len;
        return this.reset().uint32(n), n && (this.tail.next = t.next, this.tail = r, this.len += n), this;
    };
    y.prototype.finish = function() {
        for(var t = this.head.next, r = this.constructor.alloc(this.len), n = 0; t;)t.fn(t.val, r, n), n += t.len, t = t.next;
        return r;
    };
    y._configure = function(e) {
        Yn = e, y.create = Vs(), Yn._configure();
    };
});
var ks = _((Tm, js)=>{
    "use strict";
    js.exports = it;
    var Fs = ti();
    (it.prototype = Object.create(Fs.prototype)).constructor = it;
    var Rt = At();
    function it() {
        Fs.call(this);
    }
    it._configure = function() {
        it.alloc = Rt._Buffer_allocUnsafe, it.writeBytesBuffer = Rt.Buffer && Rt.Buffer.prototype instanceof Uint8Array && Rt.Buffer.prototype.set.name === "set" ? function(t, r, n) {
            r.set(t, n);
        } : function(t, r, n) {
            if (t.copy) t.copy(r, n, 0, t.length);
            else for(var o = 0; o < t.length;)r[n++] = t[o++];
        };
    };
    it.prototype.bytes = function(t) {
        Rt.isString(t) && (t = Rt._Buffer_from(t, "base64"));
        var r = t.length >>> 0;
        return this.uint32(r), r && this._push(it.writeBytesBuffer, r, t), this;
    };
    function fp(e, t, r) {
        e.length < 40 ? Rt.utf8.write(e, t, r) : t.utf8Write ? t.utf8Write(e, r) : t.write(e, r);
    }
    it.prototype.string = function(t) {
        var r = Rt.Buffer.byteLength(t);
        return this.uint32(r), r && this._push(fp, r, t), this;
    };
    it._configure();
});
var ni = _((Sm, Ys)=>{
    "use strict";
    Ys.exports = N;
    var J = At(), ri, Ks = J.LongBits, pp = J.utf8;
    function tt(e, t) {
        return RangeError("index out of range: " + e.pos + " + " + (t || 1) + " > " + e.len);
    }
    function N(e) {
        this.buf = e, this.pos = 0, this.len = e.length;
    }
    var qs = typeof Uint8Array < "u" ? function(t) {
        if (t instanceof Uint8Array || Array.isArray(t)) return new N(t);
        throw Error("illegal buffer");
    } : function(t) {
        if (Array.isArray(t)) return new N(t);
        throw Error("illegal buffer");
    }, Ws = function() {
        return J.Buffer ? function(r) {
            return (N.create = function(o) {
                return J.Buffer.isBuffer(o) ? new ri(o) : qs(o);
            })(r);
        } : qs;
    };
    N.create = Ws();
    N.prototype._slice = J.Array.prototype.subarray || J.Array.prototype.slice;
    N.prototype.uint32 = function() {
        var t = 4294967295;
        return function() {
            if (t = (this.buf[this.pos] & 127) >>> 0, this.buf[this.pos++] < 128 || (t = (t | (this.buf[this.pos] & 127) << 7) >>> 0, this.buf[this.pos++] < 128) || (t = (t | (this.buf[this.pos] & 127) << 14) >>> 0, this.buf[this.pos++] < 128) || (t = (t | (this.buf[this.pos] & 127) << 21) >>> 0, this.buf[this.pos++] < 128) || (t = (t | (this.buf[this.pos] & 15) << 28) >>> 0, this.buf[this.pos++] < 128)) return t;
            if ((this.pos += 5) > this.len) throw this.pos = this.len, tt(this, 10);
            return t;
        };
    }();
    N.prototype.int32 = function() {
        return this.uint32() | 0;
    };
    N.prototype.sint32 = function() {
        var t = this.uint32();
        return t >>> 1 ^ -(t & 1) | 0;
    };
    function ei() {
        var e = new Ks(0, 0), t = 0;
        if (this.len - this.pos > 4) {
            for(; t < 4; ++t)if (e.lo = (e.lo | (this.buf[this.pos] & 127) << t * 7) >>> 0, this.buf[this.pos++] < 128) return e;
            if (e.lo = (e.lo | (this.buf[this.pos] & 127) << 28) >>> 0, e.hi = (e.hi | (this.buf[this.pos] & 127) >> 4) >>> 0, this.buf[this.pos++] < 128) return e;
            t = 0;
        } else {
            for(; t < 3; ++t){
                if (this.pos >= this.len) throw tt(this);
                if (e.lo = (e.lo | (this.buf[this.pos] & 127) << t * 7) >>> 0, this.buf[this.pos++] < 128) return e;
            }
            return e.lo = (e.lo | (this.buf[this.pos++] & 127) << t * 7) >>> 0, e;
        }
        if (this.len - this.pos > 4) {
            for(; t < 5; ++t)if (e.hi = (e.hi | (this.buf[this.pos] & 127) << t * 7 + 3) >>> 0, this.buf[this.pos++] < 128) return e;
        } else for(; t < 5; ++t){
            if (this.pos >= this.len) throw tt(this);
            if (e.hi = (e.hi | (this.buf[this.pos] & 127) << t * 7 + 3) >>> 0, this.buf[this.pos++] < 128) return e;
        }
        throw Error("invalid varint encoding");
    }
    N.prototype.bool = function() {
        return this.uint32() !== 0;
    };
    function Or(e, t) {
        return (e[t - 4] | e[t - 3] << 8 | e[t - 2] << 16 | e[t - 1] << 24) >>> 0;
    }
    N.prototype.fixed32 = function() {
        if (this.pos + 4 > this.len) throw tt(this, 4);
        return Or(this.buf, this.pos += 4);
    };
    N.prototype.sfixed32 = function() {
        if (this.pos + 4 > this.len) throw tt(this, 4);
        return Or(this.buf, this.pos += 4) | 0;
    };
    function Xs() {
        if (this.pos + 8 > this.len) throw tt(this, 8);
        return new Ks(Or(this.buf, this.pos += 4), Or(this.buf, this.pos += 4));
    }
    N.prototype.float = function() {
        if (this.pos + 4 > this.len) throw tt(this, 4);
        var t = J.float.readFloatLE(this.buf, this.pos);
        return this.pos += 4, t;
    };
    N.prototype.double = function() {
        if (this.pos + 8 > this.len) throw tt(this, 4);
        var t = J.float.readDoubleLE(this.buf, this.pos);
        return this.pos += 8, t;
    };
    N.prototype.bytes = function() {
        var t = this.uint32(), r = this.pos, n = this.pos + t;
        if (n > this.len) throw tt(this, t);
        if (this.pos += t, Array.isArray(this.buf)) return this.buf.slice(r, n);
        if (r === n) {
            var o = J.Buffer;
            return o ? o.alloc(0) : new this.buf.constructor(0);
        }
        return this._slice.call(this.buf, r, n);
    };
    N.prototype.string = function() {
        var t = this.bytes();
        return pp.read(t, 0, t.length);
    };
    N.prototype.skip = function(t) {
        if (typeof t == "number") {
            if (this.pos + t > this.len) throw tt(this, t);
            this.pos += t;
        } else do if (this.pos >= this.len) throw tt(this);
        while (this.buf[this.pos++] & 128)
        return this;
    };
    N.prototype.skipType = function(e) {
        switch(e){
            case 0:
                this.skip();
                break;
            case 1:
                this.skip(8);
                break;
            case 2:
                this.skip(this.uint32());
                break;
            case 3:
                for(; (e = this.uint32() & 7) !== 4;)this.skipType(e);
                break;
            case 5:
                this.skip(4);
                break;
            default:
                throw Error("invalid wire type " + e + " at offset " + this.pos);
        }
        return this;
    };
    N._configure = function(e) {
        ri = e, N.create = Ws(), ri._configure();
        var t = J.Long ? "toLong" : "toNumber";
        J.merge(N.prototype, {
            int64: function() {
                return ei.call(this)[t](!1);
            },
            uint64: function() {
                return ei.call(this)[t](!0);
            },
            sint64: function() {
                return ei.call(this).zzDecode()[t](!1);
            },
            fixed64: function() {
                return Xs.call(this)[t](!0);
            },
            sfixed64: function() {
                return Xs.call(this)[t](!1);
            }
        });
    };
});
var Zs = _((gm, Qs)=>{
    "use strict";
    Qs.exports = Ut;
    var $s = ni();
    (Ut.prototype = Object.create($s.prototype)).constructor = Ut;
    var zs = At();
    function Ut(e) {
        $s.call(this, e);
    }
    Ut._configure = function() {
        zs.Buffer && (Ut.prototype._slice = zs.Buffer.prototype.slice);
    };
    Ut.prototype.string = function() {
        var t = this.uint32();
        return this.buf.utf8Slice ? this.buf.utf8Slice(this.pos, this.pos = Math.min(this.pos + t, this.len)) : this.buf.toString("utf-8", this.pos, this.pos = Math.min(this.pos + t, this.len));
    };
    Ut._configure();
});
var tu = _((ym, Js)=>{
    "use strict";
    Js.exports = me;
    var ii = At();
    (me.prototype = Object.create(ii.EventEmitter.prototype)).constructor = me;
    function me(e, t, r) {
        if (typeof e != "function") throw TypeError("rpcImpl must be a function");
        ii.EventEmitter.call(this), this.rpcImpl = e, this.requestDelimited = !!t, this.responseDelimited = !!r;
    }
    me.prototype.rpcCall = function e(t, r, n, o, i) {
        if (!o) throw TypeError("request must be specified");
        var s = this;
        if (!i) return ii.asPromise(e, s, t, r, n, o);
        if (!s.rpcImpl) {
            setTimeout(function() {
                i(Error("already ended"));
            }, 0);
            return;
        }
        try {
            return s.rpcImpl(t, r[s.requestDelimited ? "encodeDelimited" : "encode"](o).finish(), function(c, a) {
                if (c) return s.emit("error", c, t), i(c);
                if (a === null) {
                    s.end(!0);
                    return;
                }
                if (!(a instanceof n)) try {
                    a = n[s.responseDelimited ? "decodeDelimited" : "decode"](a);
                } catch (f) {
                    return s.emit("error", f, t), i(f);
                }
                return s.emit("data", a, t), i(null, a);
            });
        } catch (u) {
            s.emit("error", u, t), setTimeout(function() {
                i(u);
            }, 0);
            return;
        }
    };
    me.prototype.end = function(t) {
        return this.rpcImpl && (t || this.rpcImpl(null, null, null), this.rpcImpl = null, this.emit("end").off()), this;
    };
});
var ru = _((eu)=>{
    "use strict";
    var dp = eu;
    dp.Service = tu();
});
var iu = _((Am, nu)=>{
    "use strict";
    nu.exports = {};
});
var su = _((au)=>{
    "use strict";
    var F = au;
    F.build = "minimal";
    F.Writer = ti();
    F.BufferWriter = ks();
    F.Reader = ni();
    F.BufferReader = Zs();
    F.util = At();
    F.rpc = ru();
    F.roots = iu();
    F.configure = ou;
    function ou() {
        F.util._configure(), F.Writer._configure(F.BufferWriter), F.Reader._configure(F.BufferReader);
    }
    ou();
});
var cu = _((Om, uu)=>{
    "use strict";
    uu.exports = su();
});
E();
var Kt = v(P());
E();
var b = v(P()), ht = v(ae());
var So = "exception";
var wl = function(e) {
    var t = typeof Symbol == "function" && Symbol.iterator, r = t && e[t], n = 0;
    if (r) return r.call(e);
    if (e && typeof e.length == "number") return {
        next: function() {
            return e && n >= e.length && (e = void 0), {
                value: e && e[n++],
                done: !e
            };
        }
    };
    throw new TypeError(t ? "Object is not iterable." : "Symbol.iterator is not defined.");
}, Ml = function(e, t) {
    var r = typeof Symbol == "function" && e[Symbol.iterator];
    if (!r) return e;
    var n = r.call(e), o, i = [], s;
    try {
        for(; (t === void 0 || t-- > 0) && !(o = n.next()).done;)i.push(o.value);
    } catch (u) {
        s = {
            error: u
        };
    } finally{
        try {
            o && !o.done && (r = n.return) && r.call(n);
        } finally{
            if (s) throw s.error;
        }
    }
    return i;
}, go = function() {
    function e(t, r, n, o, i, s, u, c, a, f) {
        u === void 0 && (u = []), this.attributes = {}, this.links = [], this.events = [], this._droppedAttributesCount = 0, this._droppedEventsCount = 0, this._droppedLinksCount = 0, this.status = {
            code: l.SpanStatusCode.UNSET
        }, this.endTime = [
            0,
            0
        ], this._ended = !1, this._duration = [
            -1,
            -1
        ], this.name = n, this._spanContext = o, this.parentSpanId = s, this.kind = i, this.links = u;
        var p = Date.now();
        this._performanceStartTime = b.otperformance.now(), this._performanceOffset = p - (this._performanceStartTime + (0, b.getTimeOrigin)()), this._startTimeProvided = c != null, this.startTime = this._getTime(c ?? p), this.resource = t.resource, this.instrumentationLibrary = t.instrumentationLibrary, this._spanLimits = t.getSpanLimits(), f != null && this.setAttributes(f), this._spanProcessor = t.getActiveSpanProcessor(), this._spanProcessor.onStart(this, r), this._attributeValueLengthLimit = this._spanLimits.attributeValueLengthLimit || 0;
    }
    return e.prototype.spanContext = function() {
        return this._spanContext;
    }, e.prototype.setAttribute = function(t, r) {
        return r == null || this._isSpanEnded() ? this : t.length === 0 ? (l.diag.warn("Invalid attribute key: " + t), this) : (0, b.isAttributeValue)(r) ? Object.keys(this.attributes).length >= this._spanLimits.attributeCountLimit && !Object.prototype.hasOwnProperty.call(this.attributes, t) ? (this._droppedAttributesCount++, this) : (this.attributes[t] = this._truncateToSize(r), this) : (l.diag.warn("Invalid attribute value set for key: " + t), this);
    }, e.prototype.setAttributes = function(t) {
        var r, n;
        try {
            for(var o = wl(Object.entries(t)), i = o.next(); !i.done; i = o.next()){
                var s = Ml(i.value, 2), u = s[0], c = s[1];
                this.setAttribute(u, c);
            }
        } catch (a) {
            r = {
                error: a
            };
        } finally{
            try {
                i && !i.done && (n = o.return) && n.call(o);
            } finally{
                if (r) throw r.error;
            }
        }
        return this;
    }, e.prototype.addEvent = function(t, r, n) {
        if (this._isSpanEnded()) return this;
        if (this._spanLimits.eventCountLimit === 0) return l.diag.warn("No events allowed."), this._droppedEventsCount++, this;
        this.events.length >= this._spanLimits.eventCountLimit && (l.diag.warn("Dropping extra events."), this.events.shift(), this._droppedEventsCount++), (0, b.isTimeInput)(r) && ((0, b.isTimeInput)(n) || (n = r), r = void 0);
        var o = (0, b.sanitizeAttributes)(r);
        return this.events.push({
            name: t,
            attributes: o,
            time: this._getTime(n),
            droppedAttributesCount: 0
        }), this;
    }, e.prototype.setStatus = function(t) {
        return this._isSpanEnded() ? this : (this.status = t, this);
    }, e.prototype.updateName = function(t) {
        return this._isSpanEnded() ? this : (this.name = t, this);
    }, e.prototype.end = function(t) {
        if (this._isSpanEnded()) {
            l.diag.error(this.name + " " + this._spanContext.traceId + "-" + this._spanContext.spanId + " - You can only call end() on a span once.");
            return;
        }
        this._ended = !0, this.endTime = this._getTime(t), this._duration = (0, b.hrTimeDuration)(this.startTime, this.endTime), this._duration[0] < 0 && (l.diag.warn("Inconsistent start and end time, startTime > endTime. Setting span duration to 0ms.", this.startTime, this.endTime), this.endTime = this.startTime.slice(), this._duration = [
            0,
            0
        ]), this._spanProcessor.onEnd(this);
    }, e.prototype._getTime = function(t) {
        if (typeof t == "number" && t < b.otperformance.now()) return (0, b.hrTime)(t + this._performanceOffset);
        if (typeof t == "number") return (0, b.millisToHrTime)(t);
        if (t instanceof Date) return (0, b.millisToHrTime)(t.getTime());
        if ((0, b.isTimeInputHrTime)(t)) return t;
        if (this._startTimeProvided) return (0, b.millisToHrTime)(Date.now());
        var r = b.otperformance.now() - this._performanceStartTime;
        return (0, b.addHrTimes)(this.startTime, (0, b.millisToHrTime)(r));
    }, e.prototype.isRecording = function() {
        return this._ended === !1;
    }, e.prototype.recordException = function(t, r) {
        var n = {};
        typeof t == "string" ? n[ht.SemanticAttributes.EXCEPTION_MESSAGE] = t : t && (t.code ? n[ht.SemanticAttributes.EXCEPTION_TYPE] = t.code.toString() : t.name && (n[ht.SemanticAttributes.EXCEPTION_TYPE] = t.name), t.message && (n[ht.SemanticAttributes.EXCEPTION_MESSAGE] = t.message), t.stack && (n[ht.SemanticAttributes.EXCEPTION_STACKTRACE] = t.stack)), n[ht.SemanticAttributes.EXCEPTION_TYPE] || n[ht.SemanticAttributes.EXCEPTION_MESSAGE] ? this.addEvent(So, n, r) : l.diag.warn("Failed to record an exception " + t);
    }, Object.defineProperty(e.prototype, "duration", {
        get: function() {
            return this._duration;
        },
        enumerable: !1,
        configurable: !0
    }), Object.defineProperty(e.prototype, "ended", {
        get: function() {
            return this._ended;
        },
        enumerable: !1,
        configurable: !0
    }), Object.defineProperty(e.prototype, "droppedAttributesCount", {
        get: function() {
            return this._droppedAttributesCount;
        },
        enumerable: !1,
        configurable: !0
    }), Object.defineProperty(e.prototype, "droppedEventsCount", {
        get: function() {
            return this._droppedEventsCount;
        },
        enumerable: !1,
        configurable: !0
    }), Object.defineProperty(e.prototype, "droppedLinksCount", {
        get: function() {
            return this._droppedLinksCount;
        },
        enumerable: !1,
        configurable: !0
    }), e.prototype._isSpanEnded = function() {
        return this._ended && l.diag.warn("Can not execute the operation on ended Span {traceId: " + this._spanContext.traceId + ", spanId: " + this._spanContext.spanId + "}"), this._ended;
    }, e.prototype._truncateToLimitUtil = function(t, r) {
        return t.length <= r ? t : t.substr(0, r);
    }, e.prototype._truncateToSize = function(t) {
        var r = this, n = this._attributeValueLengthLimit;
        return n <= 0 ? (l.diag.warn("Attribute value limit must be positive, got " + n), t) : typeof t == "string" ? this._truncateToLimitUtil(t, n) : Array.isArray(t) ? t.map(function(o) {
            return typeof o == "string" ? r._truncateToLimitUtil(o, n) : o;
        }) : t;
    }, e;
}();
E();
var x = v(P());
var ot;
(function(e) {
    e[e.NOT_RECORD = 0] = "NOT_RECORD", e[e.RECORD = 1] = "RECORD", e[e.RECORD_AND_SAMPLED = 2] = "RECORD_AND_SAMPLED";
})(ot || (ot = {}));
var at = function() {
    function e() {}
    return e.prototype.shouldSample = function() {
        return {
            decision: ot.NOT_RECORD
        };
    }, e.prototype.toString = function() {
        return "AlwaysOffSampler";
    }, e;
}();
var X = function() {
    function e() {}
    return e.prototype.shouldSample = function() {
        return {
            decision: ot.RECORD_AND_SAMPLED
        };
    }, e.prototype.toString = function() {
        return "AlwaysOnSampler";
    }, e;
}();
E();
var yo = v(P());
var Et = function() {
    function e(t) {
        var r, n, o, i;
        this._root = t.root, this._root || ((0, yo.globalErrorHandler)(new Error("ParentBasedSampler must have a root sampler configured")), this._root = new X), this._remoteParentSampled = (r = t.remoteParentSampled) !== null && r !== void 0 ? r : new X, this._remoteParentNotSampled = (n = t.remoteParentNotSampled) !== null && n !== void 0 ? n : new at, this._localParentSampled = (o = t.localParentSampled) !== null && o !== void 0 ? o : new X, this._localParentNotSampled = (i = t.localParentNotSampled) !== null && i !== void 0 ? i : new at;
    }
    return e.prototype.shouldSample = function(t, r, n, o, i, s) {
        var u = l.trace.getSpanContext(t);
        return !u || !(0, l.isSpanContextValid)(u) ? this._root.shouldSample(t, r, n, o, i, s) : u.isRemote ? u.traceFlags & l.TraceFlags.SAMPLED ? this._remoteParentSampled.shouldSample(t, r, n, o, i, s) : this._remoteParentNotSampled.shouldSample(t, r, n, o, i, s) : u.traceFlags & l.TraceFlags.SAMPLED ? this._localParentSampled.shouldSample(t, r, n, o, i, s) : this._localParentNotSampled.shouldSample(t, r, n, o, i, s);
    }, e.prototype.toString = function() {
        return "ParentBased{root=" + this._root.toString() + ", remoteParentSampled=" + this._remoteParentSampled.toString() + ", remoteParentNotSampled=" + this._remoteParentNotSampled.toString() + ", localParentSampled=" + this._localParentSampled.toString() + ", localParentNotSampled=" + this._localParentNotSampled.toString() + "}";
    }, e;
}();
E();
var kt = function() {
    function e(t) {
        t === void 0 && (t = 0), this._ratio = t, this._ratio = this._normalize(t), this._upperBound = Math.floor(this._ratio * 4294967295);
    }
    return e.prototype.shouldSample = function(t, r) {
        return {
            decision: (0, l.isValidTraceId)(r) && this._accumulate(r) < this._upperBound ? ot.RECORD_AND_SAMPLED : ot.NOT_RECORD
        };
    }, e.prototype.toString = function() {
        return "TraceIdRatioBased{" + this._ratio + "}";
    }, e.prototype._normalize = function(t) {
        return typeof t != "number" || isNaN(t) ? 0 : t >= 1 ? 1 : t <= 0 ? 0 : t;
    }, e.prototype._accumulate = function(t) {
        for(var r = 0, n = 0; n < t.length / 8; n++){
            var o = n * 8, i = parseInt(t.slice(o, o + 8), 16);
            r = (r ^ i) >>> 0;
        }
        return r;
    }, e;
}();
var xl = (0, x.getEnv)(), Dl = x.TracesSamplerValues.AlwaysOn, qt = 1;
function Ze() {
    return {
        sampler: mn(xl),
        forceFlushTimeoutMillis: 3e4,
        generalLimits: {
            attributeValueLengthLimit: (0, x.getEnv)().OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT,
            attributeCountLimit: (0, x.getEnv)().OTEL_ATTRIBUTE_COUNT_LIMIT
        },
        spanLimits: {
            attributeValueLengthLimit: (0, x.getEnv)().OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT,
            attributeCountLimit: (0, x.getEnv)().OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT,
            linkCountLimit: (0, x.getEnv)().OTEL_SPAN_LINK_COUNT_LIMIT,
            eventCountLimit: (0, x.getEnv)().OTEL_SPAN_EVENT_COUNT_LIMIT,
            attributePerEventCountLimit: (0, x.getEnv)().OTEL_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT,
            attributePerLinkCountLimit: (0, x.getEnv)().OTEL_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT
        }
    };
}
function mn(e) {
    switch(e === void 0 && (e = (0, x.getEnv)()), e.OTEL_TRACES_SAMPLER){
        case x.TracesSamplerValues.AlwaysOn:
            return new X;
        case x.TracesSamplerValues.AlwaysOff:
            return new at;
        case x.TracesSamplerValues.ParentBasedAlwaysOn:
            return new Et({
                root: new X
            });
        case x.TracesSamplerValues.ParentBasedAlwaysOff:
            return new Et({
                root: new at
            });
        case x.TracesSamplerValues.TraceIdRatio:
            return new kt(vo(e));
        case x.TracesSamplerValues.ParentBasedTraceIdRatio:
            return new Et({
                root: new kt(vo(e))
            });
        default:
            return l.diag.error('OTEL_TRACES_SAMPLER value "' + e.OTEL_TRACES_SAMPLER + " invalid, defaulting to " + Dl + '".'), new X;
    }
}
function vo(e) {
    if (e.OTEL_TRACES_SAMPLER_ARG === void 0 || e.OTEL_TRACES_SAMPLER_ARG === "") return l.diag.error("OTEL_TRACES_SAMPLER_ARG is blank, defaulting to " + qt + "."), qt;
    var t = Number(e.OTEL_TRACES_SAMPLER_ARG);
    return isNaN(t) ? (l.diag.error("OTEL_TRACES_SAMPLER_ARG=" + e.OTEL_TRACES_SAMPLER_ARG + " was given, but it is invalid, defaulting to " + qt + "."), qt) : t < 0 || t > 1 ? (l.diag.error("OTEL_TRACES_SAMPLER_ARG=" + e.OTEL_TRACES_SAMPLER_ARG + " was given, but it is out of range ([0..1]), defaulting to " + qt + "."), qt) : t;
}
var Xt = v(P());
function Ao(e) {
    var t = {
        sampler: mn()
    }, r = Ze(), n = Object.assign({}, r, t, e);
    return n.generalLimits = Object.assign({}, r.generalLimits, e.generalLimits || {}), n.spanLimits = Object.assign({}, r.spanLimits, e.spanLimits || {}), n;
}
function Ro(e) {
    var t, r, n, o, i, s, u, c, a, f, p, d, m = Object.assign({}, e.spanLimits), g = (0, Xt.getEnvWithoutDefaults)();
    return m.attributeCountLimit = (s = (i = (o = (r = (t = e.spanLimits) === null || t === void 0 ? void 0 : t.attributeCountLimit) !== null && r !== void 0 ? r : (n = e.generalLimits) === null || n === void 0 ? void 0 : n.attributeCountLimit) !== null && o !== void 0 ? o : g.OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT) !== null && i !== void 0 ? i : g.OTEL_ATTRIBUTE_COUNT_LIMIT) !== null && s !== void 0 ? s : Xt.DEFAULT_ATTRIBUTE_COUNT_LIMIT, m.attributeValueLengthLimit = (d = (p = (f = (c = (u = e.spanLimits) === null || u === void 0 ? void 0 : u.attributeValueLengthLimit) !== null && c !== void 0 ? c : (a = e.generalLimits) === null || a === void 0 ? void 0 : a.attributeValueLengthLimit) !== null && f !== void 0 ? f : g.OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT) !== null && p !== void 0 ? p : g.OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT) !== null && d !== void 0 ? d : Xt.DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT, Object.assign({}, e, {
        spanLimits: m
    });
}
E();
var k = v(P()), Oo = function() {
    function e(t, r) {
        this._exporter = t, this._isExporting = !1, this._finishedSpans = [], this._droppedSpansCount = 0;
        var n = (0, k.getEnv)();
        this._maxExportBatchSize = typeof r?.maxExportBatchSize == "number" ? r.maxExportBatchSize : n.OTEL_BSP_MAX_EXPORT_BATCH_SIZE, this._maxQueueSize = typeof r?.maxQueueSize == "number" ? r.maxQueueSize : n.OTEL_BSP_MAX_QUEUE_SIZE, this._scheduledDelayMillis = typeof r?.scheduledDelayMillis == "number" ? r.scheduledDelayMillis : n.OTEL_BSP_SCHEDULE_DELAY, this._exportTimeoutMillis = typeof r?.exportTimeoutMillis == "number" ? r.exportTimeoutMillis : n.OTEL_BSP_EXPORT_TIMEOUT, this._shutdownOnce = new k.BindOnceFuture(this._shutdown, this), this._maxExportBatchSize > this._maxQueueSize && (l.diag.warn("BatchSpanProcessor: maxExportBatchSize must be smaller or equal to maxQueueSize, setting maxExportBatchSize to match maxQueueSize"), this._maxExportBatchSize = this._maxQueueSize);
    }
    return e.prototype.forceFlush = function() {
        return this._shutdownOnce.isCalled ? this._shutdownOnce.promise : this._flushAll();
    }, e.prototype.onStart = function(t, r) {}, e.prototype.onEnd = function(t) {
        this._shutdownOnce.isCalled || t.spanContext().traceFlags & l.TraceFlags.SAMPLED && this._addToBuffer(t);
    }, e.prototype.shutdown = function() {
        return this._shutdownOnce.call();
    }, e.prototype._shutdown = function() {
        var t = this;
        return Promise.resolve().then(function() {
            return t.onShutdown();
        }).then(function() {
            return t._flushAll();
        }).then(function() {
            return t._exporter.shutdown();
        });
    }, e.prototype._addToBuffer = function(t) {
        if (this._finishedSpans.length >= this._maxQueueSize) {
            this._droppedSpansCount === 0 && l.diag.debug("maxQueueSize reached, dropping spans"), this._droppedSpansCount++;
            return;
        }
        this._droppedSpansCount > 0 && (l.diag.warn("Dropped " + this._droppedSpansCount + " spans because maxQueueSize reached"), this._droppedSpansCount = 0), this._finishedSpans.push(t), this._maybeStartTimer();
    }, e.prototype._flushAll = function() {
        var t = this;
        return new Promise(function(r, n) {
            for(var o = [], i = Math.ceil(t._finishedSpans.length / t._maxExportBatchSize), s = 0, u = i; s < u; s++)o.push(t._flushOneBatch());
            Promise.all(o).then(function() {
                r();
            }).catch(n);
        });
    }, e.prototype._flushOneBatch = function() {
        var t = this;
        return this._clearTimer(), this._finishedSpans.length === 0 ? Promise.resolve() : new Promise(function(r, n) {
            var o = setTimeout(function() {
                n(new Error("Timeout"));
            }, t._exportTimeoutMillis);
            l.context.with((0, k.suppressTracing)(l.context.active()), function() {
                var i = t._finishedSpans.splice(0, t._maxExportBatchSize), s = function() {
                    return t._exporter.export(i, function(c) {
                        var a;
                        clearTimeout(o), c.code === k.ExportResultCode.SUCCESS ? r() : n((a = c.error) !== null && a !== void 0 ? a : new Error("BatchSpanProcessor: span export failed"));
                    });
                }, u = i.map(function(c) {
                    return c.resource;
                }).filter(function(c) {
                    return c.asyncAttributesPending;
                });
                u.length === 0 ? s() : Promise.all(u.map(function(c) {
                    var a;
                    return (a = c.waitForAsyncAttributes) === null || a === void 0 ? void 0 : a.call(c);
                })).then(s, function(c) {
                    (0, k.globalErrorHandler)(c), n(c);
                });
            });
        });
    }, e.prototype._maybeStartTimer = function() {
        var t = this;
        if (!this._isExporting) {
            var r = function() {
                t._isExporting = !0, t._flushOneBatch().then(function() {
                    t._isExporting = !1, t._finishedSpans.length > 0 && (t._clearTimer(), t._maybeStartTimer());
                }).catch(function(n) {
                    t._isExporting = !1, (0, k.globalErrorHandler)(n);
                });
            };
            if (this._finishedSpans.length >= this._maxExportBatchSize) return r();
            this._timer === void 0 && (this._timer = setTimeout(function() {
                return r();
            }, this._scheduledDelayMillis), (0, k.unrefTimer)(this._timer));
        }
    }, e.prototype._clearTimer = function() {
        this._timer !== void 0 && (clearTimeout(this._timer), this._timer = void 0);
    }, e;
}();
var Ul = function() {
    var e = function(t, r) {
        return e = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(n, o) {
            n.__proto__ = o;
        } || function(n, o) {
            for(var i in o)Object.prototype.hasOwnProperty.call(o, i) && (n[i] = o[i]);
        }, e(t, r);
    };
    return function(t, r) {
        if (typeof r != "function" && r !== null) throw new TypeError("Class extends value " + String(r) + " is not a constructor or null");
        e(t, r);
        function n() {
            this.constructor = t;
        }
        t.prototype = r === null ? Object.create(r) : (n.prototype = r.prototype, new n);
    };
}(), Mt = function(e) {
    Ul(t, e);
    function t(r, n) {
        var o = e.call(this, r, n) || this;
        return o.onInit(n), o;
    }
    return t.prototype.onInit = function(r) {
        var n = this;
        r?.disableAutoFlushOnDocumentHide !== !0 && typeof document < "u" && (this._visibilityChangeListener = function() {
            document.visibilityState === "hidden" && n.forceFlush();
        }, this._pageHideListener = function() {
            n.forceFlush();
        }, document.addEventListener("visibilitychange", this._visibilityChangeListener), document.addEventListener("pagehide", this._pageHideListener));
    }, t.prototype.onShutdown = function() {
        typeof document < "u" && (this._visibilityChangeListener && document.removeEventListener("visibilitychange", this._visibilityChangeListener), this._pageHideListener && document.removeEventListener("pagehide", this._pageHideListener));
    }, t;
}(Oo);
var Bl = 8, Gl = 16, tr = function() {
    function e() {
        this.generateTraceId = bo(Gl), this.generateSpanId = bo(Bl);
    }
    return e;
}();
var Je = Array(32);
function bo(e) {
    return function() {
        for(var r = 0; r < e * 2; r++)Je[r] = Math.floor(Math.random() * 16) + 48, Je[r] >= 58 && (Je[r] += 39);
        return String.fromCharCode.apply(null, Je.slice(0, e * 2));
    };
}
var Lo = function() {
    function e(t, r, n) {
        this._tracerProvider = n;
        var o = Ao(r);
        this._sampler = o.sampler, this._generalLimits = o.generalLimits, this._spanLimits = o.spanLimits, this._idGenerator = r.idGenerator || new tr, this.resource = n.resource, this.instrumentationLibrary = t;
    }
    return e.prototype.startSpan = function(t, r, n) {
        var o, i, s;
        r === void 0 && (r = {}), n === void 0 && (n = l.context.active()), r.root && (n = l.trace.deleteSpan(n));
        var u = l.trace.getSpan(n);
        if ((0, Kt.isTracingSuppressed)(n)) {
            l.diag.debug("Instrumentation suppressed, returning Noop Span");
            var c = l.trace.wrapSpanContext(l.INVALID_SPAN_CONTEXT);
            return c;
        }
        var a = u?.spanContext(), f = this._idGenerator.generateSpanId(), p, d, m;
        !a || !l.trace.isSpanContextValid(a) ? p = this._idGenerator.generateTraceId() : (p = a.traceId, d = a.traceState, m = a.spanId);
        var g = (o = r.kind) !== null && o !== void 0 ? o : l.SpanKind.INTERNAL, C = ((i = r.links) !== null && i !== void 0 ? i : []).map(function(bt) {
            return {
                context: bt.context,
                attributes: (0, Kt.sanitizeAttributes)(bt.attributes)
            };
        }), M = (0, Kt.sanitizeAttributes)(r.attributes), T = this._sampler.shouldSample(n, p, t, g, M, C);
        d = (s = T.traceState) !== null && s !== void 0 ? s : d;
        var et = T.decision === l.SamplingDecision.RECORD_AND_SAMPLED ? l.TraceFlags.SAMPLED : l.TraceFlags.NONE, q = {
            traceId: p,
            spanId: f,
            traceFlags: et,
            traceState: d
        };
        if (T.decision === l.SamplingDecision.NOT_RECORD) {
            l.diag.debug("Recording is off, propagating context in a non-recording span");
            var c = l.trace.wrapSpanContext(q);
            return c;
        }
        var U = (0, Kt.sanitizeAttributes)(Object.assign(M, T.attributes)), wr = new go(this, n, t, q, g, m, C, r.startTime, void 0, U);
        return wr;
    }, e.prototype.startActiveSpan = function(t, r, n, o) {
        var i, s, u;
        if (!(arguments.length < 2)) {
            arguments.length === 2 ? u = r : arguments.length === 3 ? (i = r, u = n) : (i = r, s = n, u = o);
            var c = s ?? l.context.active(), a = this.startSpan(t, i, c), f = l.trace.setSpan(c, a);
            return l.context.with(f, u, void 0, a);
        }
    }, e.prototype.getGeneralLimits = function() {
        return this._generalLimits;
    }, e.prototype.getSpanLimits = function() {
        return this._spanLimits;
    }, e.prototype.getActiveSpanProcessor = function() {
        return this._tracerProvider.getActiveSpanProcessor();
    }, e;
}();
E();
var $ = v(P());
E();
var mt = v(ae()), er = v(P());
function Po() {
    return "unknown_service";
}
var Tt = function() {
    return Tt = Object.assign || function(e) {
        for(var t, r = 1, n = arguments.length; r < n; r++){
            t = arguments[r];
            for(var o in t)Object.prototype.hasOwnProperty.call(t, o) && (e[o] = t[o]);
        }
        return e;
    }, Tt.apply(this, arguments);
}, Vl = function(e, t, r, n) {
    function o(i) {
        return i instanceof r ? i : new r(function(s) {
            s(i);
        });
    }
    return new (r || (r = Promise))(function(i, s) {
        function u(f) {
            try {
                a(n.next(f));
            } catch (p) {
                s(p);
            }
        }
        function c(f) {
            try {
                a(n.throw(f));
            } catch (p) {
                s(p);
            }
        }
        function a(f) {
            f.done ? i(f.value) : o(f.value).then(u, c);
        }
        a((n = n.apply(e, t || [])).next());
    });
}, Hl = function(e, t) {
    var r = {
        label: 0,
        sent: function() {
            if (i[0] & 1) throw i[1];
            return i[1];
        },
        trys: [],
        ops: []
    }, n, o, i, s;
    return s = {
        next: u(0),
        throw: u(1),
        return: u(2)
    }, typeof Symbol == "function" && (s[Symbol.iterator] = function() {
        return this;
    }), s;
    "TURBOPACK unreachable";
    function u(a) {
        return function(f) {
            return c([
                a,
                f
            ]);
        };
    }
    function c(a) {
        if (n) throw new TypeError("Generator is already executing.");
        for(; r;)try {
            if (n = 1, o && (i = a[0] & 2 ? o.return : a[0] ? o.throw || ((i = o.return) && i.call(o), 0) : o.next) && !(i = i.call(o, a[1])).done) return i;
            switch(o = 0, i && (a = [
                a[0] & 2,
                i.value
            ]), a[0]){
                case 0:
                case 1:
                    i = a;
                    break;
                case 4:
                    return r.label++, {
                        value: a[1],
                        done: !1
                    };
                case 5:
                    r.label++, o = a[1], a = [
                        0
                    ];
                    continue;
                case 7:
                    a = r.ops.pop(), r.trys.pop();
                    continue;
                default:
                    if (i = r.trys, !(i = i.length > 0 && i[i.length - 1]) && (a[0] === 6 || a[0] === 2)) {
                        r = 0;
                        continue;
                    }
                    if (a[0] === 3 && (!i || a[1] > i[0] && a[1] < i[3])) {
                        r.label = a[1];
                        break;
                    }
                    if (a[0] === 6 && r.label < i[1]) {
                        r.label = i[1], i = a;
                        break;
                    }
                    if (i && r.label < i[2]) {
                        r.label = i[2], r.ops.push(a);
                        break;
                    }
                    i[2] && r.ops.pop(), r.trys.pop();
                    continue;
            }
            a = t.call(e, r);
        } catch (f) {
            a = [
                6,
                f
            ], o = 0;
        } finally{
            n = i = 0;
        }
        if (a[0] & 5) throw a[1];
        return {
            value: a[0] ? a[1] : void 0,
            done: !0
        };
    }
}, Fl = function(e, t) {
    var r = typeof Symbol == "function" && e[Symbol.iterator];
    if (!r) return e;
    var n = r.call(e), o, i = [], s;
    try {
        for(; (t === void 0 || t-- > 0) && !(o = n.next()).done;)i.push(o.value);
    } catch (u) {
        s = {
            error: u
        };
    } finally{
        try {
            o && !o.done && (r = n.return) && r.call(n);
        } finally{
            if (s) throw s.error;
        }
    }
    return i;
}, H = function() {
    function e(t, r) {
        var n = this, o;
        this._attributes = t, this.asyncAttributesPending = r != null, this._syncAttributes = (o = this._attributes) !== null && o !== void 0 ? o : {}, this._asyncAttributesPromise = r?.then(function(i) {
            return n._attributes = Object.assign({}, n._attributes, i), n.asyncAttributesPending = !1, i;
        }, function(i) {
            return l.diag.debug("a resource's async attributes promise rejected: %s", i), n.asyncAttributesPending = !1, {};
        });
    }
    return e.empty = function() {
        return e.EMPTY;
    }, e.default = function() {
        var t;
        return new e((t = {}, t[mt.SemanticResourceAttributes.SERVICE_NAME] = Po(), t[mt.SemanticResourceAttributes.TELEMETRY_SDK_LANGUAGE] = er.SDK_INFO[mt.SemanticResourceAttributes.TELEMETRY_SDK_LANGUAGE], t[mt.SemanticResourceAttributes.TELEMETRY_SDK_NAME] = er.SDK_INFO[mt.SemanticResourceAttributes.TELEMETRY_SDK_NAME], t[mt.SemanticResourceAttributes.TELEMETRY_SDK_VERSION] = er.SDK_INFO[mt.SemanticResourceAttributes.TELEMETRY_SDK_VERSION], t));
    }, Object.defineProperty(e.prototype, "attributes", {
        get: function() {
            var t;
            return this.asyncAttributesPending && l.diag.error("Accessing resource attributes before async attributes settled"), (t = this._attributes) !== null && t !== void 0 ? t : {};
        },
        enumerable: !1,
        configurable: !0
    }), e.prototype.waitForAsyncAttributes = function() {
        return Vl(this, void 0, void 0, function() {
            return Hl(this, function(t) {
                switch(t.label){
                    case 0:
                        return this.asyncAttributesPending ? [
                            4,
                            this._asyncAttributesPromise
                        ] : [
                            3,
                            2
                        ];
                    case 1:
                        t.sent(), t.label = 2;
                    case 2:
                        return [
                            2
                        ];
                }
            });
        });
    }, e.prototype.merge = function(t) {
        var r = this, n;
        if (!t) return this;
        var o = Tt(Tt({}, this._syncAttributes), (n = t._syncAttributes) !== null && n !== void 0 ? n : t.attributes);
        if (!this._asyncAttributesPromise && !t._asyncAttributesPromise) return new e(o);
        var i = Promise.all([
            this._asyncAttributesPromise,
            t._asyncAttributesPromise
        ]).then(function(s) {
            var u, c = Fl(s, 2), a = c[0], f = c[1];
            return Tt(Tt(Tt(Tt({}, r._syncAttributes), a), (u = t._syncAttributes) !== null && u !== void 0 ? u : t.attributes), f);
        });
        return new e(o, i);
    }, e.EMPTY = new e({}), e;
}();
E();
var Io = v(P()), Co = v(ae());
var jl = function(e) {
    var t = typeof Symbol == "function" && Symbol.iterator, r = t && e[t], n = 0;
    if (r) return r.call(e);
    if (e && typeof e.length == "number") return {
        next: function() {
            return e && n >= e.length && (e = void 0), {
                value: e && e[n++],
                done: !e
            };
        }
    };
    throw new TypeError(t ? "Object is not iterable." : "Symbol.iterator is not defined.");
}, kl = function(e, t) {
    var r = typeof Symbol == "function" && e[Symbol.iterator];
    if (!r) return e;
    var n = r.call(e), o, i = [], s;
    try {
        for(; (t === void 0 || t-- > 0) && !(o = n.next()).done;)i.push(o.value);
    } catch (u) {
        s = {
            error: u
        };
    } finally{
        try {
            o && !o.done && (r = n.return) && r.call(n);
        } finally{
            if (s) throw s.error;
        }
    }
    return i;
}, ql = function() {
    function e() {
        this._MAX_LENGTH = 255, this._COMMA_SEPARATOR = ",", this._LABEL_KEY_VALUE_SPLITTER = "=", this._ERROR_MESSAGE_INVALID_CHARS = "should be a ASCII string with a length greater than 0 and not exceed " + this._MAX_LENGTH + " characters.", this._ERROR_MESSAGE_INVALID_VALUE = "should be a ASCII string with a length not exceed " + this._MAX_LENGTH + " characters.";
    }
    return e.prototype.detect = function(t) {
        var r = {}, n = (0, Io.getEnv)(), o = n.OTEL_RESOURCE_ATTRIBUTES, i = n.OTEL_SERVICE_NAME;
        if (o) try {
            var s = this._parseResourceAttributes(o);
            Object.assign(r, s);
        } catch (u) {
            l.diag.debug("EnvDetector failed: " + u.message);
        }
        return i && (r[Co.SemanticResourceAttributes.SERVICE_NAME] = i), new H(r);
    }, e.prototype._parseResourceAttributes = function(t) {
        var r, n;
        if (!t) return {};
        var o = {}, i = t.split(this._COMMA_SEPARATOR, -1);
        try {
            for(var s = jl(i), u = s.next(); !u.done; u = s.next()){
                var c = u.value, a = c.split(this._LABEL_KEY_VALUE_SPLITTER, -1);
                if (a.length === 2) {
                    var f = kl(a, 2), p = f[0], d = f[1];
                    if (p = p.trim(), d = d.trim().split(/^"|"$/).join(""), !this._isValidAndNotEmpty(p)) throw new Error("Attribute key " + this._ERROR_MESSAGE_INVALID_CHARS);
                    if (!this._isValid(d)) throw new Error("Attribute value " + this._ERROR_MESSAGE_INVALID_VALUE);
                    o[p] = decodeURIComponent(d);
                }
            }
        } catch (m) {
            r = {
                error: m
            };
        } finally{
            try {
                u && !u.done && (n = s.return) && n.call(s);
            } finally{
                if (r) throw r.error;
            }
        }
        return o;
    }, e.prototype._isValid = function(t) {
        return t.length <= this._MAX_LENGTH && this._isBaggageOctetString(t);
    }, e.prototype._isBaggageOctetString = function(t) {
        for(var r = 0; r < t.length; r++){
            var n = t.charCodeAt(r);
            if (n < 33 || n === 44 || n === 59 || n === 92 || n > 126) return !1;
        }
        return !0;
    }, e.prototype._isValidAndNotEmpty = function(t) {
        return t.length > 0 && this._isValid(t);
    }, e;
}(), No = new ql;
E();
var wo = function(e) {
    return e !== null && typeof e == "object" && typeof e.then == "function";
};
var Xl = function(e, t, r, n) {
    function o(i) {
        return i instanceof r ? i : new r(function(s) {
            s(i);
        });
    }
    return new (r || (r = Promise))(function(i, s) {
        function u(f) {
            try {
                a(n.next(f));
            } catch (p) {
                s(p);
            }
        }
        function c(f) {
            try {
                a(n.throw(f));
            } catch (p) {
                s(p);
            }
        }
        function a(f) {
            f.done ? i(f.value) : o(f.value).then(u, c);
        }
        a((n = n.apply(e, t || [])).next());
    });
}, Kl = function(e, t) {
    var r = {
        label: 0,
        sent: function() {
            if (i[0] & 1) throw i[1];
            return i[1];
        },
        trys: [],
        ops: []
    }, n, o, i, s;
    return s = {
        next: u(0),
        throw: u(1),
        return: u(2)
    }, typeof Symbol == "function" && (s[Symbol.iterator] = function() {
        return this;
    }), s;
    "TURBOPACK unreachable";
    function u(a) {
        return function(f) {
            return c([
                a,
                f
            ]);
        };
    }
    function c(a) {
        if (n) throw new TypeError("Generator is already executing.");
        for(; r;)try {
            if (n = 1, o && (i = a[0] & 2 ? o.return : a[0] ? o.throw || ((i = o.return) && i.call(o), 0) : o.next) && !(i = i.call(o, a[1])).done) return i;
            switch(o = 0, i && (a = [
                a[0] & 2,
                i.value
            ]), a[0]){
                case 0:
                case 1:
                    i = a;
                    break;
                case 4:
                    return r.label++, {
                        value: a[1],
                        done: !1
                    };
                case 5:
                    r.label++, o = a[1], a = [
                        0
                    ];
                    continue;
                case 7:
                    a = r.ops.pop(), r.trys.pop();
                    continue;
                default:
                    if (i = r.trys, !(i = i.length > 0 && i[i.length - 1]) && (a[0] === 6 || a[0] === 2)) {
                        r = 0;
                        continue;
                    }
                    if (a[0] === 3 && (!i || a[1] > i[0] && a[1] < i[3])) {
                        r.label = a[1];
                        break;
                    }
                    if (a[0] === 6 && r.label < i[1]) {
                        r.label = i[1], i = a;
                        break;
                    }
                    if (i && r.label < i[2]) {
                        r.label = i[2], r.ops.push(a);
                        break;
                    }
                    i[2] && r.ops.pop(), r.trys.pop();
                    continue;
            }
            a = t.call(e, r);
        } catch (f) {
            a = [
                6,
                f
            ], o = 0;
        } finally{
            n = i = 0;
        }
        if (a[0] & 5) throw a[1];
        return {
            value: a[0] ? a[1] : void 0,
            done: !0
        };
    }
};
var Mo = function(e) {
    var t;
    e === void 0 && (e = {});
    var r = ((t = e.detectors) !== null && t !== void 0 ? t : []).map(function(o) {
        try {
            var i = o.detect(e), s;
            if (wo(i)) {
                var u = function() {
                    return Xl(void 0, void 0, void 0, function() {
                        var c;
                        return Kl(this, function(a) {
                            switch(a.label){
                                case 0:
                                    return [
                                        4,
                                        i
                                    ];
                                case 1:
                                    return c = a.sent(), [
                                        2,
                                        c.attributes
                                    ];
                            }
                        });
                    });
                };
                s = new H({}, u());
            } else s = i;
            return s.waitForAsyncAttributes ? s.waitForAsyncAttributes().then(function() {
                return l.diag.debug(o.constructor.name + " found resource.", s);
            }) : l.diag.debug(o.constructor.name + " found resource.", s), s;
        } catch (c) {
            return l.diag.error(o.constructor.name + " failed: " + c.message), H.empty();
        }
    }), n = r.reduce(function(o, i) {
        return o.merge(i);
    }, H.empty());
    return n.waitForAsyncAttributes && n.waitForAsyncAttributes().then(function() {
        Wl(r);
    }), n;
}, Wl = function(e) {
    e.forEach(function(t) {
        if (Object.keys(t.attributes).length > 0) {
            var r = JSON.stringify(t.attributes, null, 4);
            l.diag.verbose(r);
        }
    });
};
var xo = v(P()), rr = function(e) {
    var t = typeof Symbol == "function" && Symbol.iterator, r = t && e[t], n = 0;
    if (r) return r.call(e);
    if (e && typeof e.length == "number") return {
        next: function() {
            return e && n >= e.length && (e = void 0), {
                value: e && e[n++],
                done: !e
            };
        }
    };
    throw new TypeError(t ? "Object is not iterable." : "Symbol.iterator is not defined.");
}, Do = function() {
    function e(t) {
        this._spanProcessors = t;
    }
    return e.prototype.forceFlush = function() {
        var t, r, n = [];
        try {
            for(var o = rr(this._spanProcessors), i = o.next(); !i.done; i = o.next()){
                var s = i.value;
                n.push(s.forceFlush());
            }
        } catch (u) {
            t = {
                error: u
            };
        } finally{
            try {
                i && !i.done && (r = o.return) && r.call(o);
            } finally{
                if (t) throw t.error;
            }
        }
        return new Promise(function(u) {
            Promise.all(n).then(function() {
                u();
            }).catch(function(c) {
                (0, xo.globalErrorHandler)(c || new Error("MultiSpanProcessor: forceFlush failed")), u();
            });
        });
    }, e.prototype.onStart = function(t, r) {
        var n, o;
        try {
            for(var i = rr(this._spanProcessors), s = i.next(); !s.done; s = i.next()){
                var u = s.value;
                u.onStart(t, r);
            }
        } catch (c) {
            n = {
                error: c
            };
        } finally{
            try {
                s && !s.done && (o = i.return) && o.call(i);
            } finally{
                if (n) throw n.error;
            }
        }
    }, e.prototype.onEnd = function(t) {
        var r, n;
        try {
            for(var o = rr(this._spanProcessors), i = o.next(); !i.done; i = o.next()){
                var s = i.value;
                s.onEnd(t);
            }
        } catch (u) {
            r = {
                error: u
            };
        } finally{
            try {
                i && !i.done && (n = o.return) && n.call(o);
            } finally{
                if (r) throw r.error;
            }
        }
    }, e.prototype.shutdown = function() {
        var t, r, n = [];
        try {
            for(var o = rr(this._spanProcessors), i = o.next(); !i.done; i = o.next()){
                var s = i.value;
                n.push(s.shutdown());
            }
        } catch (u) {
            t = {
                error: u
            };
        } finally{
            try {
                i && !i.done && (r = o.return) && r.call(o);
            } finally{
                if (t) throw t.error;
            }
        }
        return new Promise(function(u, c) {
            Promise.all(n).then(function() {
                u();
            }, c);
        });
    }, e;
}();
var Uo = function() {
    function e() {}
    return e.prototype.onStart = function(t, r) {}, e.prototype.onEnd = function(t) {}, e.prototype.shutdown = function() {
        return Promise.resolve();
    }, e.prototype.forceFlush = function() {
        return Promise.resolve();
    }, e;
}();
var xt;
(function(e) {
    e[e.resolved = 0] = "resolved", e[e.timeout = 1] = "timeout", e[e.error = 2] = "error", e[e.unresolved = 3] = "unresolved";
})(xt || (xt = {}));
var Bo = function() {
    function e(t) {
        t === void 0 && (t = {});
        var r;
        this._registeredSpanProcessors = [], this._tracers = new Map;
        var n = (0, $.merge)({}, Ze(), Ro(t));
        this.resource = (r = n.resource) !== null && r !== void 0 ? r : H.empty(), this.resource = H.default().merge(this.resource), this._config = Object.assign({}, n, {
            resource: this.resource
        });
        var o = this._buildExporterFromEnv();
        if (o !== void 0) {
            var i = new Mt(o);
            this.activeSpanProcessor = i;
        } else this.activeSpanProcessor = new Uo;
    }
    return e.prototype.getTracer = function(t, r, n) {
        var o = t + "@" + (r || "") + ":" + (n?.schemaUrl || "");
        return this._tracers.has(o) || this._tracers.set(o, new Lo({
            name: t,
            version: r,
            schemaUrl: n?.schemaUrl
        }, this._config, this)), this._tracers.get(o);
    }, e.prototype.addSpanProcessor = function(t) {
        this._registeredSpanProcessors.length === 0 && this.activeSpanProcessor.shutdown().catch(function(r) {
            return l.diag.error("Error while trying to shutdown current span processor", r);
        }), this._registeredSpanProcessors.push(t), this.activeSpanProcessor = new Do(this._registeredSpanProcessors);
    }, e.prototype.getActiveSpanProcessor = function() {
        return this.activeSpanProcessor;
    }, e.prototype.register = function(t) {
        t === void 0 && (t = {}), l.trace.setGlobalTracerProvider(this), t.propagator === void 0 && (t.propagator = this._buildPropagatorFromEnv()), t.contextManager && l.context.setGlobalContextManager(t.contextManager), t.propagator && l.propagation.setGlobalPropagator(t.propagator);
    }, e.prototype.forceFlush = function() {
        var t = this._config.forceFlushTimeoutMillis, r = this._registeredSpanProcessors.map(function(n) {
            return new Promise(function(o) {
                var i, s = setTimeout(function() {
                    o(new Error("Span processor did not completed within timeout period of " + t + " ms")), i = xt.timeout;
                }, t);
                n.forceFlush().then(function() {
                    clearTimeout(s), i !== xt.timeout && (i = xt.resolved, o(i));
                }).catch(function(u) {
                    clearTimeout(s), i = xt.error, o(u);
                });
            });
        });
        return new Promise(function(n, o) {
            Promise.all(r).then(function(i) {
                var s = i.filter(function(u) {
                    return u !== xt.resolved;
                });
                s.length > 0 ? o(s) : n();
            }).catch(function(i) {
                return o([
                    i
                ]);
            });
        });
    }, e.prototype.shutdown = function() {
        return this.activeSpanProcessor.shutdown();
    }, e.prototype._getPropagator = function(t) {
        var r;
        return (r = this.constructor._registeredPropagators.get(t)) === null || r === void 0 ? void 0 : r();
    }, e.prototype._getSpanExporter = function(t) {
        var r;
        return (r = this.constructor._registeredExporters.get(t)) === null || r === void 0 ? void 0 : r();
    }, e.prototype._buildPropagatorFromEnv = function() {
        var t = this, r = Array.from(new Set((0, $.getEnv)().OTEL_PROPAGATORS)), n = r.map(function(i) {
            var s = t._getPropagator(i);
            return s || l.diag.warn('Propagator "' + i + '" requested through environment variable is unavailable.'), s;
        }), o = n.reduce(function(i, s) {
            return s && i.push(s), i;
        }, []);
        if (o.length !== 0) return r.length === 1 ? o[0] : new $.CompositePropagator({
            propagators: o
        });
    }, e.prototype._buildExporterFromEnv = function() {
        var t = (0, $.getEnv)().OTEL_TRACES_EXPORTER;
        if (!(t === "none" || t === "")) {
            var r = this._getSpanExporter(t);
            return r || l.diag.error('Exporter "' + t + '" requested through environment variable is unavailable.'), r;
        }
    }, e._registeredPropagators = new Map([
        [
            "tracecontext",
            function() {
                return new $.W3CTraceContextPropagator;
            }
        ],
        [
            "baggage",
            function() {
                return new $.W3CBaggagePropagator;
            }
        ]
    ]), e._registeredExporters = new Map, e;
}();
E();
var st = {};
Y(st, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__);
;
var Pu = v(Fo(), 1);
E();
var ir = v(P());
E();
E();
E();
var Wt = v(P()), Ql = function(e) {
    var t = typeof Symbol == "function" && Symbol.iterator, r = t && e[t], n = 0;
    if (r) return r.call(e);
    if (e && typeof e.length == "number") return {
        next: function() {
            return e && n >= e.length && (e = void 0), {
                value: e && e[n++],
                done: !e
            };
        }
    };
    throw new TypeError(t ? "Object is not iterable." : "Symbol.iterator is not defined.");
}, Zl = function(e, t) {
    var r = typeof Symbol == "function" && e[Symbol.iterator];
    if (!r) return e;
    var n = r.call(e), o, i = [], s;
    try {
        for(; (t === void 0 || t-- > 0) && !(o = n.next()).done;)i.push(o.value);
    } catch (u) {
        s = {
            error: u
        };
    } finally{
        try {
            o && !o.done && (r = n.return) && r.call(n);
        } finally{
            if (s) throw s.error;
        }
    }
    return i;
}, jo = function() {
    function e(t, r, n) {
        this.attributes = {}, this.totalAttributesCount = 0, this._isReadonly = !1;
        var o = n.timestamp, i = n.observedTimestamp, s = n.severityNumber, u = n.severityText, c = n.body, a = n.attributes, f = a === void 0 ? {} : a, p = n.context, d = Date.now();
        if (this.hrTime = (0, Wt.timeInputToHrTime)(o ?? d), this.hrTimeObserved = (0, Wt.timeInputToHrTime)(i ?? d), p) {
            var m = l.trace.getSpanContext(p);
            m && l.isSpanContextValid(m) && (this.spanContext = m);
        }
        this.severityNumber = s, this.severityText = u, this.body = c, this.resource = t.resource, this.instrumentationScope = r, this._logRecordLimits = t.logRecordLimits, this.setAttributes(f);
    }
    return Object.defineProperty(e.prototype, "severityText", {
        get: function() {
            return this._severityText;
        },
        set: function(t) {
            this._isLogRecordReadonly() || (this._severityText = t);
        },
        enumerable: !1,
        configurable: !0
    }), Object.defineProperty(e.prototype, "severityNumber", {
        get: function() {
            return this._severityNumber;
        },
        set: function(t) {
            this._isLogRecordReadonly() || (this._severityNumber = t);
        },
        enumerable: !1,
        configurable: !0
    }), Object.defineProperty(e.prototype, "body", {
        get: function() {
            return this._body;
        },
        set: function(t) {
            this._isLogRecordReadonly() || (this._body = t);
        },
        enumerable: !1,
        configurable: !0
    }), Object.defineProperty(e.prototype, "droppedAttributesCount", {
        get: function() {
            return this.totalAttributesCount - Object.keys(this.attributes).length;
        },
        enumerable: !1,
        configurable: !0
    }), e.prototype.setAttribute = function(t, r) {
        return this._isLogRecordReadonly() ? this : r === null ? this : t.length === 0 ? (l.diag.warn("Invalid attribute key: " + t), this) : !(0, Wt.isAttributeValue)(r) && !(typeof r == "object" && !Array.isArray(r) && Object.keys(r).length > 0) ? (l.diag.warn("Invalid attribute value set for key: " + t), this) : (this.totalAttributesCount += 1, Object.keys(this.attributes).length >= this._logRecordLimits.attributeCountLimit && !Object.prototype.hasOwnProperty.call(this.attributes, t) ? this : ((0, Wt.isAttributeValue)(r) ? this.attributes[t] = this._truncateToSize(r) : this.attributes[t] = r, this));
    }, e.prototype.setAttributes = function(t) {
        var r, n;
        try {
            for(var o = Ql(Object.entries(t)), i = o.next(); !i.done; i = o.next()){
                var s = Zl(i.value, 2), u = s[0], c = s[1];
                this.setAttribute(u, c);
            }
        } catch (a) {
            r = {
                error: a
            };
        } finally{
            try {
                i && !i.done && (n = o.return) && n.call(o);
            } finally{
                if (r) throw r.error;
            }
        }
        return this;
    }, e.prototype.setBody = function(t) {
        return this.body = t, this;
    }, e.prototype.setSeverityNumber = function(t) {
        return this.severityNumber = t, this;
    }, e.prototype.setSeverityText = function(t) {
        return this.severityText = t, this;
    }, e.prototype._makeReadonly = function() {
        this._isReadonly = !0;
    }, e.prototype._truncateToSize = function(t) {
        var r = this, n = this._logRecordLimits.attributeValueLengthLimit;
        return n <= 0 ? (l.diag.warn("Attribute value limit must be positive, got " + n), t) : typeof t == "string" ? this._truncateToLimitUtil(t, n) : Array.isArray(t) ? t.map(function(o) {
            return typeof o == "string" ? r._truncateToLimitUtil(o, n) : o;
        }) : t;
    }, e.prototype._truncateToLimitUtil = function(t, r) {
        return t.length <= r ? t : t.substring(0, r);
    }, e.prototype._isLogRecordReadonly = function() {
        return this._isReadonly && l.diag.warn("Can not execute the operation on emitted log record"), this._isReadonly;
    }, e;
}();
var Sn = function() {
    return Sn = Object.assign || function(e) {
        for(var t, r = 1, n = arguments.length; r < n; r++){
            t = arguments[r];
            for(var o in t)Object.prototype.hasOwnProperty.call(t, o) && (e[o] = t[o]);
        }
        return e;
    }, Sn.apply(this, arguments);
}, ko = function() {
    function e(t, r) {
        this.instrumentationScope = t, this._sharedState = r;
    }
    return e.prototype.emit = function(t) {
        var r = t.context || l.context.active(), n = new jo(this._sharedState, this.instrumentationScope, Sn({
            context: r
        }, t));
        this._sharedState.activeProcessor.onEmit(n, r), n._makeReadonly();
    }, e;
}();
var ut = v(P());
function qo() {
    return {
        forceFlushTimeoutMillis: 3e4,
        logRecordLimits: {
            attributeValueLengthLimit: (0, ut.getEnv)().OTEL_LOGRECORD_ATTRIBUTE_VALUE_LENGTH_LIMIT,
            attributeCountLimit: (0, ut.getEnv)().OTEL_LOGRECORD_ATTRIBUTE_COUNT_LIMIT
        },
        includeTraceContext: !0
    };
}
function Xo(e) {
    var t, r, n, o, i, s, u = (0, ut.getEnvWithoutDefaults)();
    return {
        attributeCountLimit: (n = (r = (t = e.attributeCountLimit) !== null && t !== void 0 ? t : u.OTEL_LOGRECORD_ATTRIBUTE_COUNT_LIMIT) !== null && r !== void 0 ? r : u.OTEL_ATTRIBUTE_COUNT_LIMIT) !== null && n !== void 0 ? n : ut.DEFAULT_ATTRIBUTE_COUNT_LIMIT,
        attributeValueLengthLimit: (s = (i = (o = e.attributeValueLengthLimit) !== null && o !== void 0 ? o : u.OTEL_LOGRECORD_ATTRIBUTE_VALUE_LENGTH_LIMIT) !== null && i !== void 0 ? i : u.OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT) !== null && s !== void 0 ? s : ut.DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT
    };
}
var Yo = v(P()), Ko = function(e, t, r, n) {
    function o(i) {
        return i instanceof r ? i : new r(function(s) {
            s(i);
        });
    }
    return new (r || (r = Promise))(function(i, s) {
        function u(f) {
            try {
                a(n.next(f));
            } catch (p) {
                s(p);
            }
        }
        function c(f) {
            try {
                a(n.throw(f));
            } catch (p) {
                s(p);
            }
        }
        function a(f) {
            f.done ? i(f.value) : o(f.value).then(u, c);
        }
        a((n = n.apply(e, t || [])).next());
    });
}, Wo = function(e, t) {
    var r = {
        label: 0,
        sent: function() {
            if (i[0] & 1) throw i[1];
            return i[1];
        },
        trys: [],
        ops: []
    }, n, o, i, s;
    return s = {
        next: u(0),
        throw: u(1),
        return: u(2)
    }, typeof Symbol == "function" && (s[Symbol.iterator] = function() {
        return this;
    }), s;
    "TURBOPACK unreachable";
    function u(a) {
        return function(f) {
            return c([
                a,
                f
            ]);
        };
    }
    function c(a) {
        if (n) throw new TypeError("Generator is already executing.");
        for(; r;)try {
            if (n = 1, o && (i = a[0] & 2 ? o.return : a[0] ? o.throw || ((i = o.return) && i.call(o), 0) : o.next) && !(i = i.call(o, a[1])).done) return i;
            switch(o = 0, i && (a = [
                a[0] & 2,
                i.value
            ]), a[0]){
                case 0:
                case 1:
                    i = a;
                    break;
                case 4:
                    return r.label++, {
                        value: a[1],
                        done: !1
                    };
                case 5:
                    r.label++, o = a[1], a = [
                        0
                    ];
                    continue;
                case 7:
                    a = r.ops.pop(), r.trys.pop();
                    continue;
                default:
                    if (i = r.trys, !(i = i.length > 0 && i[i.length - 1]) && (a[0] === 6 || a[0] === 2)) {
                        r = 0;
                        continue;
                    }
                    if (a[0] === 3 && (!i || a[1] > i[0] && a[1] < i[3])) {
                        r.label = a[1];
                        break;
                    }
                    if (a[0] === 6 && r.label < i[1]) {
                        r.label = i[1], i = a;
                        break;
                    }
                    if (i && r.label < i[2]) {
                        r.label = i[2], r.ops.push(a);
                        break;
                    }
                    i[2] && r.ops.pop(), r.trys.pop();
                    continue;
            }
            a = t.call(e, r);
        } catch (f) {
            a = [
                6,
                f
            ], o = 0;
        } finally{
            n = i = 0;
        }
        if (a[0] & 5) throw a[1];
        return {
            value: a[0] ? a[1] : void 0,
            done: !0
        };
    }
}, zo = function() {
    function e(t, r) {
        this.processors = t, this.forceFlushTimeoutMillis = r;
    }
    return e.prototype.forceFlush = function() {
        return Ko(this, void 0, void 0, function() {
            var t;
            return Wo(this, function(r) {
                switch(r.label){
                    case 0:
                        return t = this.forceFlushTimeoutMillis, [
                            4,
                            Promise.all(this.processors.map(function(n) {
                                return (0, Yo.callWithTimeout)(n.forceFlush(), t);
                            }))
                        ];
                    case 1:
                        return r.sent(), [
                            2
                        ];
                }
            });
        });
    }, e.prototype.onEmit = function(t, r) {
        this.processors.forEach(function(n) {
            return n.onEmit(t, r);
        });
    }, e.prototype.shutdown = function() {
        return Ko(this, void 0, void 0, function() {
            return Wo(this, function(t) {
                switch(t.label){
                    case 0:
                        return [
                            4,
                            Promise.all(this.processors.map(function(r) {
                                return r.shutdown();
                            }))
                        ];
                    case 1:
                        return t.sent(), [
                            2
                        ];
                }
            });
        });
    }, e;
}();
var $o = function() {
    function e() {}
    return e.prototype.forceFlush = function() {
        return Promise.resolve();
    }, e.prototype.onEmit = function(t, r) {}, e.prototype.shutdown = function() {
        return Promise.resolve();
    }, e;
}();
var Qo = function() {
    function e(t, r, n) {
        this.resource = t, this.forceFlushTimeoutMillis = r, this.logRecordLimits = n, this.loggers = new Map, this.registeredLogRecordProcessors = [], this.activeProcessor = new $o;
    }
    return e;
}();
var Jl = "unknown", gn = function() {
    function e(t) {
        t === void 0 && (t = {});
        var r = (0, ir.merge)({}, qo(), t), n = r.resource, o = n === void 0 ? H.default() : n, i = r.logRecordLimits, s = r.forceFlushTimeoutMillis;
        this._sharedState = new Qo(o, s, Xo(i)), this._shutdownOnce = new ir.BindOnceFuture(this._shutdown, this);
    }
    return e.prototype.getLogger = function(t, r, n) {
        if (this._shutdownOnce.isCalled) return l.diag.warn("A shutdown LoggerProvider cannot provide a Logger"), st.NOOP_LOGGER;
        t || l.diag.warn("Logger requested without instrumentation scope name.");
        var o = t || Jl, i = o + "@" + (r || "") + ":" + (n?.schemaUrl || "");
        return this._sharedState.loggers.has(i) || this._sharedState.loggers.set(i, new ko({
            name: o,
            version: r,
            schemaUrl: n?.schemaUrl
        }, this._sharedState)), this._sharedState.loggers.get(i);
    }, e.prototype.addLogRecordProcessor = function(t) {
        this._sharedState.registeredLogRecordProcessors.length === 0 && this._sharedState.activeProcessor.shutdown().catch(function(r) {
            return l.diag.error("Error while trying to shutdown current log record processor", r);
        }), this._sharedState.registeredLogRecordProcessors.push(t), this._sharedState.activeProcessor = new zo(this._sharedState.registeredLogRecordProcessors, this._sharedState.forceFlushTimeoutMillis);
    }, e.prototype.forceFlush = function() {
        return this._shutdownOnce.isCalled ? (l.diag.warn("invalid attempt to force flush after LoggerProvider shutdown"), this._shutdownOnce.promise) : this._sharedState.activeProcessor.forceFlush();
    }, e.prototype.shutdown = function() {
        return this._shutdownOnce.isCalled ? (l.diag.warn("shutdown may only be called once per LoggerProvider"), this._shutdownOnce.promise) : this._shutdownOnce.call();
    }, e.prototype._shutdown = function() {
        return this._sharedState.activeProcessor.shutdown();
    }, e;
}();
var or;
(function(e) {
    e[e.DELTA = 0] = "DELTA", e[e.CUMULATIVE = 1] = "CUMULATIVE";
})(or || (or = {}));
var tf = function() {
    var e = function(t, r) {
        return e = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(n, o) {
            n.__proto__ = o;
        } || function(n, o) {
            for(var i in o)Object.prototype.hasOwnProperty.call(o, i) && (n[i] = o[i]);
        }, e(t, r);
    };
    return function(t, r) {
        if (typeof r != "function" && r !== null) throw new TypeError("Class extends value " + String(r) + " is not a constructor or null");
        e(t, r);
        function n() {
            this.constructor = t;
        }
        t.prototype = r === null ? Object.create(r) : (n.prototype = r.prototype, new n);
    };
}(), Zo = function(e, t, r, n) {
    function o(i) {
        return i instanceof r ? i : new r(function(s) {
            s(i);
        });
    }
    return new (r || (r = Promise))(function(i, s) {
        function u(f) {
            try {
                a(n.next(f));
            } catch (p) {
                s(p);
            }
        }
        function c(f) {
            try {
                a(n.throw(f));
            } catch (p) {
                s(p);
            }
        }
        function a(f) {
            f.done ? i(f.value) : o(f.value).then(u, c);
        }
        a((n = n.apply(e, t || [])).next());
    });
}, Jo = function(e, t) {
    var r = {
        label: 0,
        sent: function() {
            if (i[0] & 1) throw i[1];
            return i[1];
        },
        trys: [],
        ops: []
    }, n, o, i, s;
    return s = {
        next: u(0),
        throw: u(1),
        return: u(2)
    }, typeof Symbol == "function" && (s[Symbol.iterator] = function() {
        return this;
    }), s;
    "TURBOPACK unreachable";
    function u(a) {
        return function(f) {
            return c([
                a,
                f
            ]);
        };
    }
    function c(a) {
        if (n) throw new TypeError("Generator is already executing.");
        for(; r;)try {
            if (n = 1, o && (i = a[0] & 2 ? o.return : a[0] ? o.throw || ((i = o.return) && i.call(o), 0) : o.next) && !(i = i.call(o, a[1])).done) return i;
            switch(o = 0, i && (a = [
                a[0] & 2,
                i.value
            ]), a[0]){
                case 0:
                case 1:
                    i = a;
                    break;
                case 4:
                    return r.label++, {
                        value: a[1],
                        done: !1
                    };
                case 5:
                    r.label++, o = a[1], a = [
                        0
                    ];
                    continue;
                case 7:
                    a = r.ops.pop(), r.trys.pop();
                    continue;
                default:
                    if (i = r.trys, !(i = i.length > 0 && i[i.length - 1]) && (a[0] === 6 || a[0] === 2)) {
                        r = 0;
                        continue;
                    }
                    if (a[0] === 3 && (!i || a[1] > i[0] && a[1] < i[3])) {
                        r.label = a[1];
                        break;
                    }
                    if (a[0] === 6 && r.label < i[1]) {
                        r.label = i[1], i = a;
                        break;
                    }
                    if (i && r.label < i[2]) {
                        r.label = i[2], r.ops.push(a);
                        break;
                    }
                    i[2] && r.ops.pop(), r.trys.pop();
                    continue;
            }
            a = t.call(e, r);
        } catch (f) {
            a = [
                6,
                f
            ], o = 0;
        } finally{
            n = i = 0;
        }
        if (a[0] & 5) throw a[1];
        return {
            value: a[0] ? a[1] : void 0,
            done: !0
        };
    }
};
var ef = function(e) {
    var t = typeof Symbol == "function" && Symbol.iterator, r = t && e[t], n = 0;
    if (r) return r.call(e);
    if (e && typeof e.length == "number") return {
        next: function() {
            return e && n >= e.length && (e = void 0), {
                value: e && e[n++],
                done: !e
            };
        }
    };
    throw new TypeError(t ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
function ta(e) {
    return e != null;
}
function ea(e) {
    var t = Object.keys(e);
    return t.length === 0 ? "" : (t = t.sort(), JSON.stringify(t.map(function(r) {
        return [
            r,
            e[r]
        ];
    })));
}
function ra(e) {
    var t, r;
    return e.name + ":" + ((t = e.version) !== null && t !== void 0 ? t : "") + ":" + ((r = e.schemaUrl) !== null && r !== void 0 ? r : "");
}
var rf = function(e) {
    tf(t, e);
    function t(r) {
        var n = e.call(this, r) || this;
        return Object.setPrototypeOf(n, t.prototype), n;
    }
    return t;
}(Error);
function yn(e, t) {
    var r, n = new Promise(function(i, s) {
        r = setTimeout(function() {
            s(new rf("Operation timed out."));
        }, t);
    });
    return Promise.race([
        e,
        n
    ]).then(function(o) {
        return clearTimeout(r), o;
    }, function(o) {
        throw clearTimeout(r), o;
    });
}
function na(e) {
    return Zo(this, void 0, void 0, function() {
        var t = this;
        return Jo(this, function(r) {
            return [
                2,
                Promise.all(e.map(function(n) {
                    return Zo(t, void 0, void 0, function() {
                        var o, i;
                        return Jo(this, function(s) {
                            switch(s.label){
                                case 0:
                                    return s.trys.push([
                                        0,
                                        2,
                                        ,
                                        3
                                    ]), [
                                        4,
                                        n
                                    ];
                                case 1:
                                    return o = s.sent(), [
                                        2,
                                        {
                                            status: "fulfilled",
                                            value: o
                                        }
                                    ];
                                case 2:
                                    return i = s.sent(), [
                                        2,
                                        {
                                            status: "rejected",
                                            reason: i
                                        }
                                    ];
                                case 3:
                                    return [
                                        2
                                    ];
                            }
                        });
                    });
                }))
            ];
        });
    });
}
function ia(e) {
    return e.status === "rejected";
}
function oa(e, t) {
    var r, n;
    if (e.size !== t.size) return !1;
    try {
        for(var o = ef(e), i = o.next(); !i.done; i = o.next()){
            var s = i.value;
            if (!t.has(s)) return !1;
        }
    } catch (u) {
        r = {
            error: u
        };
    } finally{
        try {
            i && !i.done && (n = o.return) && n.call(o);
        } finally{
            if (r) throw r.error;
        }
    }
    return !0;
}
function aa(e, t) {
    return e.toLowerCase() === t.toLowerCase();
}
E();
var ct;
(function(e) {
    e.COUNTER = "COUNTER", e.HISTOGRAM = "HISTOGRAM", e.UP_DOWN_COUNTER = "UP_DOWN_COUNTER", e.OBSERVABLE_COUNTER = "OBSERVABLE_COUNTER", e.OBSERVABLE_GAUGE = "OBSERVABLE_GAUGE", e.OBSERVABLE_UP_DOWN_COUNTER = "OBSERVABLE_UP_DOWN_COUNTER";
})(ct || (ct = {}));
function lt(e, t, r) {
    var n, o, i, s;
    return of(e) || l.diag.warn('Invalid metric name: "' + e + '". The metric name should be a ASCII string with a length no greater than 255 characters.'), {
        name: e,
        type: t,
        description: (n = r?.description) !== null && n !== void 0 ? n : "",
        unit: (o = r?.unit) !== null && o !== void 0 ? o : "",
        valueType: (i = r?.valueType) !== null && i !== void 0 ? i : l.ValueType.DOUBLE,
        advice: (s = r?.advice) !== null && s !== void 0 ? s : {}
    };
}
function sa(e, t) {
    var r, n;
    return {
        name: (r = e.name) !== null && r !== void 0 ? r : t.name,
        description: (n = e.description) !== null && n !== void 0 ? n : t.description,
        type: t.type,
        unit: t.unit,
        valueType: t.valueType,
        advice: t.advice
    };
}
function ua(e, t) {
    return aa(e.name, t.name) && e.unit === t.unit && e.type === t.type && e.valueType === t.valueType;
}
var nf = /^[a-z][a-z0-9_.\-/]{0,254}$/i;
function of(e) {
    return e.match(nf) != null;
}
E();
var ca = function() {
    function e() {
        this._registeredViews = [];
    }
    return e.prototype.addView = function(t) {
        this._registeredViews.push(t);
    }, e.prototype.findViews = function(t, r) {
        var n = this, o = this._registeredViews.filter(function(i) {
            return n._matchInstrument(i.instrumentSelector, t) && n._matchMeter(i.meterSelector, r);
        });
        return o;
    }, e.prototype._matchInstrument = function(t, r) {
        return (t.getType() === void 0 || r.type === t.getType()) && t.getNameFilter().match(r.name) && t.getUnitFilter().match(r.unit);
    }, e.prototype._matchMeter = function(t, r) {
        return t.getNameFilter().match(r.name) && (r.version === void 0 || t.getVersionFilter().match(r.version)) && (r.schemaUrl === void 0 || t.getSchemaUrlFilter().match(r.schemaUrl));
    }, e;
}();
E();
var la = v(P()), Yt = function() {
    var e = function(t, r) {
        return e = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(n, o) {
            n.__proto__ = o;
        } || function(n, o) {
            for(var i in o)Object.prototype.hasOwnProperty.call(o, i) && (n[i] = o[i]);
        }, e(t, r);
    };
    return function(t, r) {
        if (typeof r != "function" && r !== null) throw new TypeError("Class extends value " + String(r) + " is not a constructor or null");
        e(t, r);
        function n() {
            this.constructor = t;
        }
        t.prototype = r === null ? Object.create(r) : (n.prototype = r.prototype, new n);
    };
}(), vn = function() {
    function e(t, r) {
        this._writableMetricStorage = t, this._descriptor = r;
    }
    return e.prototype._record = function(t, r, n) {
        if (r === void 0 && (r = {}), n === void 0 && (n = l.context.active()), typeof t != "number") {
            l.diag.warn("non-number value provided to metric " + this._descriptor.name + ": " + t);
            return;
        }
        this._descriptor.valueType === l.ValueType.INT && !Number.isInteger(t) && (l.diag.warn("INT value type cannot accept a floating-point value for " + this._descriptor.name + ", ignoring the fractional digits."), t = Math.trunc(t), !Number.isInteger(t)) || this._writableMetricStorage.record(t, r, n, (0, la.millisToHrTime)(Date.now()));
    }, e;
}();
var fa = function(e) {
    Yt(t, e);
    function t() {
        return e !== null && e.apply(this, arguments) || this;
    }
    return t.prototype.add = function(r, n, o) {
        this._record(r, n, o);
    }, t;
}(vn);
var pa = function(e) {
    Yt(t, e);
    function t() {
        return e !== null && e.apply(this, arguments) || this;
    }
    return t.prototype.add = function(r, n, o) {
        if (r < 0) {
            l.diag.warn("negative value provided to counter " + this._descriptor.name + ": " + r);
            return;
        }
        this._record(r, n, o);
    }, t;
}(vn);
var da = function(e) {
    Yt(t, e);
    function t() {
        return e !== null && e.apply(this, arguments) || this;
    }
    return t.prototype.record = function(r, n, o) {
        if (r < 0) {
            l.diag.warn("negative value provided to histogram " + this._descriptor.name + ": " + r);
            return;
        }
        this._record(r, n, o);
    }, t;
}(vn);
var ar = function() {
    function e(t, r, n) {
        this._observableRegistry = n, this._descriptor = t, this._metricStorages = r;
    }
    return e.prototype.addCallback = function(t) {
        this._observableRegistry.addCallback(t, this);
    }, e.prototype.removeCallback = function(t) {
        this._observableRegistry.removeCallback(t, this);
    }, e;
}();
var _a = function(e) {
    Yt(t, e);
    function t() {
        return e !== null && e.apply(this, arguments) || this;
    }
    return t;
}(ar);
var ha = function(e) {
    Yt(t, e);
    function t() {
        return e !== null && e.apply(this, arguments) || this;
    }
    return t;
}(ar);
var Ea = function(e) {
    Yt(t, e);
    function t() {
        return e !== null && e.apply(this, arguments) || this;
    }
    return t;
}(ar);
function fe(e) {
    return e instanceof ar;
}
var ma = function() {
    function e(t) {
        this._meterSharedState = t;
    }
    return e.prototype.createHistogram = function(t, r) {
        var n = lt(t, ct.HISTOGRAM, r), o = this._meterSharedState.registerMetricStorage(n);
        return new da(o, n);
    }, e.prototype.createCounter = function(t, r) {
        var n = lt(t, ct.COUNTER, r), o = this._meterSharedState.registerMetricStorage(n);
        return new pa(o, n);
    }, e.prototype.createUpDownCounter = function(t, r) {
        var n = lt(t, ct.UP_DOWN_COUNTER, r), o = this._meterSharedState.registerMetricStorage(n);
        return new fa(o, n);
    }, e.prototype.createObservableGauge = function(t, r) {
        var n = lt(t, ct.OBSERVABLE_GAUGE, r), o = this._meterSharedState.registerAsyncMetricStorage(n);
        return new ha(n, o, this._meterSharedState.observableRegistry);
    }, e.prototype.createObservableCounter = function(t, r) {
        var n = lt(t, ct.OBSERVABLE_COUNTER, r), o = this._meterSharedState.registerAsyncMetricStorage(n);
        return new _a(n, o, this._meterSharedState.observableRegistry);
    }, e.prototype.createObservableUpDownCounter = function(t, r) {
        var n = lt(t, ct.OBSERVABLE_UP_DOWN_COUNTER, r), o = this._meterSharedState.registerAsyncMetricStorage(n);
        return new Ea(n, o, this._meterSharedState.observableRegistry);
    }, e.prototype.addBatchObservableCallback = function(t, r) {
        this._meterSharedState.observableRegistry.addBatchCallback(t, r);
    }, e.prototype.removeBatchObservableCallback = function(t, r) {
        this._meterSharedState.observableRegistry.removeBatchCallback(t, r);
    }, e;
}();
var sr = function() {
    function e(t) {
        this._instrumentDescriptor = t;
    }
    return e.prototype.getInstrumentDescriptor = function() {
        return this._instrumentDescriptor;
    }, e.prototype.updateDescription = function(t) {
        this._instrumentDescriptor = lt(this._instrumentDescriptor.name, this._instrumentDescriptor.type, {
            description: t,
            valueType: this._instrumentDescriptor.valueType,
            unit: this._instrumentDescriptor.unit,
            advice: this._instrumentDescriptor.advice
        });
    }, e;
}();
var af = function() {
    var e = function(t, r) {
        return e = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(n, o) {
            n.__proto__ = o;
        } || function(n, o) {
            for(var i in o)Object.prototype.hasOwnProperty.call(o, i) && (n[i] = o[i]);
        }, e(t, r);
    };
    return function(t, r) {
        if (typeof r != "function" && r !== null) throw new TypeError("Class extends value " + String(r) + " is not a constructor or null");
        e(t, r);
        function n() {
            this.constructor = t;
        }
        t.prototype = r === null ? Object.create(r) : (n.prototype = r.prototype, new n);
    };
}(), Ta = function(e, t) {
    var r = {
        label: 0,
        sent: function() {
            if (i[0] & 1) throw i[1];
            return i[1];
        },
        trys: [],
        ops: []
    }, n, o, i, s;
    return s = {
        next: u(0),
        throw: u(1),
        return: u(2)
    }, typeof Symbol == "function" && (s[Symbol.iterator] = function() {
        return this;
    }), s;
    "TURBOPACK unreachable";
    function u(a) {
        return function(f) {
            return c([
                a,
                f
            ]);
        };
    }
    function c(a) {
        if (n) throw new TypeError("Generator is already executing.");
        for(; r;)try {
            if (n = 1, o && (i = a[0] & 2 ? o.return : a[0] ? o.throw || ((i = o.return) && i.call(o), 0) : o.next) && !(i = i.call(o, a[1])).done) return i;
            switch(o = 0, i && (a = [
                a[0] & 2,
                i.value
            ]), a[0]){
                case 0:
                case 1:
                    i = a;
                    break;
                case 4:
                    return r.label++, {
                        value: a[1],
                        done: !1
                    };
                case 5:
                    r.label++, o = a[1], a = [
                        0
                    ];
                    continue;
                case 7:
                    a = r.ops.pop(), r.trys.pop();
                    continue;
                default:
                    if (i = r.trys, !(i = i.length > 0 && i[i.length - 1]) && (a[0] === 6 || a[0] === 2)) {
                        r = 0;
                        continue;
                    }
                    if (a[0] === 3 && (!i || a[1] > i[0] && a[1] < i[3])) {
                        r.label = a[1];
                        break;
                    }
                    if (a[0] === 6 && r.label < i[1]) {
                        r.label = i[1], i = a;
                        break;
                    }
                    if (i && r.label < i[2]) {
                        r.label = i[2], r.ops.push(a);
                        break;
                    }
                    i[2] && r.ops.pop(), r.trys.pop();
                    continue;
            }
            a = t.call(e, r);
        } catch (f) {
            a = [
                6,
                f
            ], o = 0;
        } finally{
            n = i = 0;
        }
        if (a[0] & 5) throw a[1];
        return {
            value: a[0] ? a[1] : void 0,
            done: !0
        };
    }
}, sf = function() {
    function e(t) {
        this._hash = t, this._valueMap = new Map, this._keyMap = new Map;
    }
    return e.prototype.get = function(t, r) {
        return r ?? (r = this._hash(t)), this._valueMap.get(r);
    }, e.prototype.getOrDefault = function(t, r) {
        var n = this._hash(t);
        if (this._valueMap.has(n)) return this._valueMap.get(n);
        var o = r();
        return this._keyMap.has(n) || this._keyMap.set(n, t), this._valueMap.set(n, o), o;
    }, e.prototype.set = function(t, r, n) {
        n ?? (n = this._hash(t)), this._keyMap.has(n) || this._keyMap.set(n, t), this._valueMap.set(n, r);
    }, e.prototype.has = function(t, r) {
        return r ?? (r = this._hash(t)), this._valueMap.has(r);
    }, e.prototype.keys = function() {
        var t, r;
        return Ta(this, function(n) {
            switch(n.label){
                case 0:
                    t = this._keyMap.entries(), r = t.next(), n.label = 1;
                case 1:
                    return r.done === !0 ? [
                        3,
                        3
                    ] : [
                        4,
                        [
                            r.value[1],
                            r.value[0]
                        ]
                    ];
                case 2:
                    return n.sent(), r = t.next(), [
                        3,
                        1
                    ];
                case 3:
                    return [
                        2
                    ];
            }
        });
    }, e.prototype.entries = function() {
        var t, r;
        return Ta(this, function(n) {
            switch(n.label){
                case 0:
                    t = this._valueMap.entries(), r = t.next(), n.label = 1;
                case 1:
                    return r.done === !0 ? [
                        3,
                        3
                    ] : [
                        4,
                        [
                            this._keyMap.get(r.value[0]),
                            r.value[1],
                            r.value[0]
                        ]
                    ];
                case 2:
                    return n.sent(), r = t.next(), [
                        3,
                        1
                    ];
                case 3:
                    return [
                        2
                    ];
            }
        });
    }, Object.defineProperty(e.prototype, "size", {
        get: function() {
            return this._valueMap.size;
        },
        enumerable: !1,
        configurable: !0
    }), e;
}();
var Q = function(e) {
    af(t, e);
    function t() {
        return e.call(this, ea) || this;
    }
    return t;
}(sf);
var uf = function(e, t) {
    var r = typeof Symbol == "function" && e[Symbol.iterator];
    if (!r) return e;
    var n = r.call(e), o, i = [], s;
    try {
        for(; (t === void 0 || t-- > 0) && !(o = n.next()).done;)i.push(o.value);
    } catch (u) {
        s = {
            error: u
        };
    } finally{
        try {
            o && !o.done && (r = n.return) && r.call(n);
        } finally{
            if (s) throw s.error;
        }
    }
    return i;
}, ur = function() {
    function e(t) {
        this._aggregator = t, this._activeCollectionStorage = new Q, this._cumulativeMemoStorage = new Q;
    }
    return e.prototype.record = function(t, r, n, o) {
        var i = this, s = this._activeCollectionStorage.getOrDefault(r, function() {
            return i._aggregator.createAccumulation(o);
        });
        s?.record(t);
    }, e.prototype.batchCumulate = function(t, r) {
        var n = this;
        Array.from(t.entries()).forEach(function(o) {
            var i = uf(o, 3), s = i[0], u = i[1], c = i[2], a = n._aggregator.createAccumulation(r);
            a?.record(u);
            var f = a;
            if (n._cumulativeMemoStorage.has(s, c)) {
                var p = n._cumulativeMemoStorage.get(s, c);
                f = n._aggregator.diff(p, a);
            }
            if (n._activeCollectionStorage.has(s, c)) {
                var d = n._activeCollectionStorage.get(s, c);
                f = n._aggregator.merge(d, f);
            }
            n._cumulativeMemoStorage.set(s, a, c), n._activeCollectionStorage.set(s, f, c);
        });
    }, e.prototype.collect = function() {
        var t = this._activeCollectionStorage;
        return this._activeCollectionStorage = new Q, t;
    }, e;
}();
var An = function(e) {
    var t = typeof Symbol == "function" && Symbol.iterator, r = t && e[t], n = 0;
    if (r) return r.call(e);
    if (e && typeof e.length == "number") return {
        next: function() {
            return e && n >= e.length && (e = void 0), {
                value: e && e[n++],
                done: !e
            };
        }
    };
    throw new TypeError(t ? "Object is not iterable." : "Symbol.iterator is not defined.");
}, Sa = function(e, t) {
    var r = typeof Symbol == "function" && e[Symbol.iterator];
    if (!r) return e;
    var n = r.call(e), o, i = [], s;
    try {
        for(; (t === void 0 || t-- > 0) && !(o = n.next()).done;)i.push(o.value);
    } catch (u) {
        s = {
            error: u
        };
    } finally{
        try {
            o && !o.done && (r = n.return) && r.call(n);
        } finally{
            if (s) throw s.error;
        }
    }
    return i;
}, cr = function() {
    function e(t, r) {
        var n = this;
        this._aggregator = t, this._unreportedAccumulations = new Map, this._reportHistory = new Map, r.forEach(function(o) {
            n._unreportedAccumulations.set(o, []);
        });
    }
    return e.prototype.buildMetrics = function(t, r, n, o) {
        this._stashAccumulations(n);
        var i = this._getMergedUnreportedAccumulations(t), s = i, u;
        if (this._reportHistory.has(t)) {
            var c = this._reportHistory.get(t), a = c.collectionTime;
            u = c.aggregationTemporality, u === or.CUMULATIVE ? s = e.merge(c.accumulations, i, this._aggregator) : s = e.calibrateStartTime(c.accumulations, i, a);
        } else u = t.selectAggregationTemporality(r.type);
        this._reportHistory.set(t, {
            accumulations: s,
            collectionTime: o,
            aggregationTemporality: u
        });
        var f = cf(s);
        if (f.length !== 0) return this._aggregator.toMetricData(r, u, f, o);
    }, e.prototype._stashAccumulations = function(t) {
        var r, n, o = this._unreportedAccumulations.keys();
        try {
            for(var i = An(o), s = i.next(); !s.done; s = i.next()){
                var u = s.value, c = this._unreportedAccumulations.get(u);
                c === void 0 && (c = [], this._unreportedAccumulations.set(u, c)), c.push(t);
            }
        } catch (a) {
            r = {
                error: a
            };
        } finally{
            try {
                s && !s.done && (n = i.return) && n.call(i);
            } finally{
                if (r) throw r.error;
            }
        }
    }, e.prototype._getMergedUnreportedAccumulations = function(t) {
        var r, n, o = new Q, i = this._unreportedAccumulations.get(t);
        if (this._unreportedAccumulations.set(t, []), i === void 0) return o;
        try {
            for(var s = An(i), u = s.next(); !u.done; u = s.next()){
                var c = u.value;
                o = e.merge(o, c, this._aggregator);
            }
        } catch (a) {
            r = {
                error: a
            };
        } finally{
            try {
                u && !u.done && (n = s.return) && n.call(s);
            } finally{
                if (r) throw r.error;
            }
        }
        return o;
    }, e.merge = function(t, r, n) {
        for(var o = t, i = r.entries(), s = i.next(); s.done !== !0;){
            var u = Sa(s.value, 3), c = u[0], a = u[1], f = u[2];
            if (t.has(c, f)) {
                var p = t.get(c, f), d = n.merge(p, a);
                o.set(c, d, f);
            } else o.set(c, a, f);
            s = i.next();
        }
        return o;
    }, e.calibrateStartTime = function(t, r, n) {
        var o, i;
        try {
            for(var s = An(t.keys()), u = s.next(); !u.done; u = s.next()){
                var c = Sa(u.value, 2), a = c[0], f = c[1], p = r.get(a, f);
                p?.setStartTime(n);
            }
        } catch (d) {
            o = {
                error: d
            };
        } finally{
            try {
                u && !u.done && (i = s.return) && i.call(s);
            } finally{
                if (o) throw o.error;
            }
        }
        return r;
    }, e;
}();
function cf(e) {
    return Array.from(e.entries());
}
var lf = function() {
    var e = function(t, r) {
        return e = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(n, o) {
            n.__proto__ = o;
        } || function(n, o) {
            for(var i in o)Object.prototype.hasOwnProperty.call(o, i) && (n[i] = o[i]);
        }, e(t, r);
    };
    return function(t, r) {
        if (typeof r != "function" && r !== null) throw new TypeError("Class extends value " + String(r) + " is not a constructor or null");
        e(t, r);
        function n() {
            this.constructor = t;
        }
        t.prototype = r === null ? Object.create(r) : (n.prototype = r.prototype, new n);
    };
}(), ff = function(e, t) {
    var r = typeof Symbol == "function" && e[Symbol.iterator];
    if (!r) return e;
    var n = r.call(e), o, i = [], s;
    try {
        for(; (t === void 0 || t-- > 0) && !(o = n.next()).done;)i.push(o.value);
    } catch (u) {
        s = {
            error: u
        };
    } finally{
        try {
            o && !o.done && (r = n.return) && r.call(n);
        } finally{
            if (s) throw s.error;
        }
    }
    return i;
}, ga = function(e) {
    lf(t, e);
    function t(r, n, o, i) {
        var s = e.call(this, r) || this;
        return s._attributesProcessor = o, s._deltaMetricStorage = new ur(n), s._temporalMetricStorage = new cr(n, i), s;
    }
    return t.prototype.record = function(r, n) {
        var o = this, i = new Q;
        Array.from(r.entries()).forEach(function(s) {
            var u = ff(s, 2), c = u[0], a = u[1];
            i.set(o._attributesProcessor.process(c), a);
        }), this._deltaMetricStorage.batchCumulate(i, n);
    }, t.prototype.collect = function(r, n) {
        var o = this._deltaMetricStorage.collect();
        return this._temporalMetricStorage.buildMetrics(r, this._instrumentDescriptor, o, n);
    }, t;
}(sr);
E();
function Rn(e, t) {
    var r = "";
    return e.unit !== t.unit && (r += "	- Unit '" + e.unit + "' does not match '" + t.unit + `'
`), e.type !== t.type && (r += "	- Type '" + e.type + "' does not match '" + t.type + `'
`), e.valueType !== t.valueType && (r += "	- Value Type '" + e.valueType + "' does not match '" + t.valueType + `'
`), e.description !== t.description && (r += "	- Description '" + e.description + "' does not match '" + t.description + `'
`), r;
}
function pf(e, t) {
    return "	- use valueType '" + e.valueType + "' on instrument creation or use an instrument name other than '" + t.name + "'";
}
function df(e, t) {
    return "	- use unit '" + e.unit + "' on instrument creation or use an instrument name other than '" + t.name + "'";
}
function _f(e, t) {
    var r = {
        name: t.name,
        type: t.type,
        unit: t.unit
    }, n = JSON.stringify(r);
    return "	- create a new view with a name other than '" + e.name + "' and InstrumentSelector '" + n + "'";
}
function hf(e, t) {
    var r = {
        name: t.name,
        type: t.type,
        unit: t.unit
    }, n = JSON.stringify(r);
    return "	- create a new view with a name other than '" + e.name + "' and InstrumentSelector '" + n + `'
    	- OR - create a new view with the name ` + e.name + " and description '" + e.description + "' and InstrumentSelector " + n + `
    	- OR - create a new view with the name ` + t.name + " and description '" + e.description + "' and InstrumentSelector " + n;
}
function On(e, t) {
    return e.valueType !== t.valueType ? pf(e, t) : e.unit !== t.unit ? df(e, t) : e.type !== t.type ? _f(e, t) : e.description !== t.description ? hf(e, t) : "";
}
var bn = function(e) {
    var t = typeof Symbol == "function" && Symbol.iterator, r = t && e[t], n = 0;
    if (r) return r.call(e);
    if (e && typeof e.length == "number") return {
        next: function() {
            return e && n >= e.length && (e = void 0), {
                value: e && e[n++],
                done: !e
            };
        }
    };
    throw new TypeError(t ? "Object is not iterable." : "Symbol.iterator is not defined.");
}, ya = function() {
    function e() {
        this._sharedRegistry = new Map, this._perCollectorRegistry = new Map;
    }
    return e.create = function() {
        return new e;
    }, e.prototype.getStorages = function(t) {
        var r, n, o, i, s = [];
        try {
            for(var u = bn(this._sharedRegistry.values()), c = u.next(); !c.done; c = u.next()){
                var a = c.value;
                s = s.concat(a);
            }
        } catch (m) {
            r = {
                error: m
            };
        } finally{
            try {
                c && !c.done && (n = u.return) && n.call(u);
            } finally{
                if (r) throw r.error;
            }
        }
        var f = this._perCollectorRegistry.get(t);
        if (f != null) try {
            for(var p = bn(f.values()), d = p.next(); !d.done; d = p.next()){
                var a = d.value;
                s = s.concat(a);
            }
        } catch (m) {
            o = {
                error: m
            };
        } finally{
            try {
                d && !d.done && (i = p.return) && i.call(p);
            } finally{
                if (o) throw o.error;
            }
        }
        return s;
    }, e.prototype.register = function(t) {
        this._registerStorage(t, this._sharedRegistry);
    }, e.prototype.registerForCollector = function(t, r) {
        var n = this._perCollectorRegistry.get(t);
        n == null && (n = new Map, this._perCollectorRegistry.set(t, n)), this._registerStorage(r, n);
    }, e.prototype.findOrUpdateCompatibleStorage = function(t) {
        var r = this._sharedRegistry.get(t.name);
        return r === void 0 ? null : this._findOrUpdateCompatibleStorage(t, r);
    }, e.prototype.findOrUpdateCompatibleCollectorStorage = function(t, r) {
        var n = this._perCollectorRegistry.get(t);
        if (n === void 0) return null;
        var o = n.get(r.name);
        return o === void 0 ? null : this._findOrUpdateCompatibleStorage(r, o);
    }, e.prototype._registerStorage = function(t, r) {
        var n = t.getInstrumentDescriptor(), o = r.get(n.name);
        if (o === void 0) {
            r.set(n.name, [
                t
            ]);
            return;
        }
        o.push(t);
    }, e.prototype._findOrUpdateCompatibleStorage = function(t, r) {
        var n, o, i = null;
        try {
            for(var s = bn(r), u = s.next(); !u.done; u = s.next()){
                var c = u.value, a = c.getInstrumentDescriptor();
                ua(a, t) ? (a.description !== t.description && (t.description.length > a.description.length && c.updateDescription(t.description), l.diag.warn("A view or instrument with the name ", t.name, ` has already been registered, but has a different description and is incompatible with another registered view.
`, `Details:
`, Rn(a, t), `The longer description will be used.
To resolve the conflict:`, On(a, t))), i = c) : l.diag.warn("A view or instrument with the name ", t.name, ` has already been registered and is incompatible with another registered view.
`, `Details:
`, Rn(a, t), `To resolve the conflict:
`, On(a, t));
            }
        } catch (f) {
            n = {
                error: f
            };
        } finally{
            try {
                u && !u.done && (o = s.return) && o.call(s);
            } finally{
                if (n) throw n.error;
            }
        }
        return i;
    }, e;
}();
var va = function() {
    function e(t) {
        this._backingStorages = t;
    }
    return e.prototype.record = function(t, r, n, o) {
        this._backingStorages.forEach(function(i) {
            i.record(t, r, n, o);
        });
    }, e;
}();
E();
E();
var Aa = function() {
    function e(t, r) {
        this._instrumentName = t, this._valueType = r, this._buffer = new Q;
    }
    return e.prototype.observe = function(t, r) {
        if (r === void 0 && (r = {}), typeof t != "number") {
            l.diag.warn("non-number value provided to metric " + this._instrumentName + ": " + t);
            return;
        }
        this._valueType === l.ValueType.INT && !Number.isInteger(t) && (l.diag.warn("INT value type cannot accept a floating-point value for " + this._instrumentName + ", ignoring the fractional digits."), t = Math.trunc(t), !Number.isInteger(t)) || this._buffer.set(r, t);
    }, e;
}();
var Ra = function() {
    function e() {
        this._buffer = new Map;
    }
    return e.prototype.observe = function(t, r, n) {
        if (n === void 0 && (n = {}), !!fe(t)) {
            var o = this._buffer.get(t);
            if (o == null && (o = new Q, this._buffer.set(t, o)), typeof r != "number") {
                l.diag.warn("non-number value provided to metric " + t._descriptor.name + ": " + r);
                return;
            }
            t._descriptor.valueType === l.ValueType.INT && !Number.isInteger(r) && (l.diag.warn("INT value type cannot accept a floating-point value for " + t._descriptor.name + ", ignoring the fractional digits."), r = Math.trunc(r), !Number.isInteger(r)) || o.set(n, r);
        }
    }, e;
}();
var Ln = function(e, t, r, n) {
    function o(i) {
        return i instanceof r ? i : new r(function(s) {
            s(i);
        });
    }
    return new (r || (r = Promise))(function(i, s) {
        function u(f) {
            try {
                a(n.next(f));
            } catch (p) {
                s(p);
            }
        }
        function c(f) {
            try {
                a(n.throw(f));
            } catch (p) {
                s(p);
            }
        }
        function a(f) {
            f.done ? i(f.value) : o(f.value).then(u, c);
        }
        a((n = n.apply(e, t || [])).next());
    });
}, Pn = function(e, t) {
    var r = {
        label: 0,
        sent: function() {
            if (i[0] & 1) throw i[1];
            return i[1];
        },
        trys: [],
        ops: []
    }, n, o, i, s;
    return s = {
        next: u(0),
        throw: u(1),
        return: u(2)
    }, typeof Symbol == "function" && (s[Symbol.iterator] = function() {
        return this;
    }), s;
    "TURBOPACK unreachable";
    function u(a) {
        return function(f) {
            return c([
                a,
                f
            ]);
        };
    }
    function c(a) {
        if (n) throw new TypeError("Generator is already executing.");
        for(; r;)try {
            if (n = 1, o && (i = a[0] & 2 ? o.return : a[0] ? o.throw || ((i = o.return) && i.call(o), 0) : o.next) && !(i = i.call(o, a[1])).done) return i;
            switch(o = 0, i && (a = [
                a[0] & 2,
                i.value
            ]), a[0]){
                case 0:
                case 1:
                    i = a;
                    break;
                case 4:
                    return r.label++, {
                        value: a[1],
                        done: !1
                    };
                case 5:
                    r.label++, o = a[1], a = [
                        0
                    ];
                    continue;
                case 7:
                    a = r.ops.pop(), r.trys.pop();
                    continue;
                default:
                    if (i = r.trys, !(i = i.length > 0 && i[i.length - 1]) && (a[0] === 6 || a[0] === 2)) {
                        r = 0;
                        continue;
                    }
                    if (a[0] === 3 && (!i || a[1] > i[0] && a[1] < i[3])) {
                        r.label = a[1];
                        break;
                    }
                    if (a[0] === 6 && r.label < i[1]) {
                        r.label = i[1], i = a;
                        break;
                    }
                    if (i && r.label < i[2]) {
                        r.label = i[2], r.ops.push(a);
                        break;
                    }
                    i[2] && r.ops.pop(), r.trys.pop();
                    continue;
            }
            a = t.call(e, r);
        } catch (f) {
            a = [
                6,
                f
            ], o = 0;
        } finally{
            n = i = 0;
        }
        if (a[0] & 5) throw a[1];
        return {
            value: a[0] ? a[1] : void 0,
            done: !0
        };
    }
}, Oa = function(e, t) {
    var r = typeof Symbol == "function" && e[Symbol.iterator];
    if (!r) return e;
    var n = r.call(e), o, i = [], s;
    try {
        for(; (t === void 0 || t-- > 0) && !(o = n.next()).done;)i.push(o.value);
    } catch (u) {
        s = {
            error: u
        };
    } finally{
        try {
            o && !o.done && (r = n.return) && r.call(n);
        } finally{
            if (s) throw s.error;
        }
    }
    return i;
}, ba = function(e, t, r) {
    if (r || arguments.length === 2) for(var n = 0, o = t.length, i; n < o; n++)(i || !(n in t)) && (i || (i = Array.prototype.slice.call(t, 0, n)), i[n] = t[n]);
    return e.concat(i || Array.prototype.slice.call(t));
}, La = function() {
    function e() {
        this._callbacks = [], this._batchCallbacks = [];
    }
    return e.prototype.addCallback = function(t, r) {
        var n = this._findCallback(t, r);
        n >= 0 || this._callbacks.push({
            callback: t,
            instrument: r
        });
    }, e.prototype.removeCallback = function(t, r) {
        var n = this._findCallback(t, r);
        n < 0 || this._callbacks.splice(n, 1);
    }, e.prototype.addBatchCallback = function(t, r) {
        var n = new Set(r.filter(fe));
        if (n.size === 0) {
            l.diag.error("BatchObservableCallback is not associated with valid instruments", r);
            return;
        }
        var o = this._findBatchCallback(t, n);
        o >= 0 || this._batchCallbacks.push({
            callback: t,
            instruments: n
        });
    }, e.prototype.removeBatchCallback = function(t, r) {
        var n = new Set(r.filter(fe)), o = this._findBatchCallback(t, n);
        o < 0 || this._batchCallbacks.splice(o, 1);
    }, e.prototype.observe = function(t, r) {
        return Ln(this, void 0, void 0, function() {
            var n, o, i, s;
            return Pn(this, function(u) {
                switch(u.label){
                    case 0:
                        return n = this._observeCallbacks(t, r), o = this._observeBatchCallbacks(t, r), [
                            4,
                            na(ba(ba([], Oa(n), !1), Oa(o), !1))
                        ];
                    case 1:
                        return i = u.sent(), s = i.filter(ia).map(function(c) {
                            return c.reason;
                        }), [
                            2,
                            s
                        ];
                }
            });
        });
    }, e.prototype._observeCallbacks = function(t, r) {
        var n = this;
        return this._callbacks.map(function(o) {
            var i = o.callback, s = o.instrument;
            return Ln(n, void 0, void 0, function() {
                var u, c;
                return Pn(this, function(a) {
                    switch(a.label){
                        case 0:
                            return u = new Aa(s._descriptor.name, s._descriptor.valueType), c = Promise.resolve(i(u)), r != null && (c = yn(c, r)), [
                                4,
                                c
                            ];
                        case 1:
                            return a.sent(), s._metricStorages.forEach(function(f) {
                                f.record(u._buffer, t);
                            }), [
                                2
                            ];
                    }
                });
            });
        });
    }, e.prototype._observeBatchCallbacks = function(t, r) {
        var n = this;
        return this._batchCallbacks.map(function(o) {
            var i = o.callback, s = o.instruments;
            return Ln(n, void 0, void 0, function() {
                var u, c;
                return Pn(this, function(a) {
                    switch(a.label){
                        case 0:
                            return u = new Ra, c = Promise.resolve(i(u)), r != null && (c = yn(c, r)), [
                                4,
                                c
                            ];
                        case 1:
                            return a.sent(), s.forEach(function(f) {
                                var p = u._buffer.get(f);
                                p != null && f._metricStorages.forEach(function(d) {
                                    d.record(p, t);
                                });
                            }), [
                                2
                            ];
                    }
                });
            });
        });
    }, e.prototype._findCallback = function(t, r) {
        return this._callbacks.findIndex(function(n) {
            return n.callback === t && n.instrument === r;
        });
    }, e.prototype._findBatchCallback = function(t, r) {
        return this._batchCallbacks.findIndex(function(n) {
            return n.callback === t && oa(n.instruments, r);
        });
    }, e;
}();
var Ef = function() {
    var e = function(t, r) {
        return e = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(n, o) {
            n.__proto__ = o;
        } || function(n, o) {
            for(var i in o)Object.prototype.hasOwnProperty.call(o, i) && (n[i] = o[i]);
        }, e(t, r);
    };
    return function(t, r) {
        if (typeof r != "function" && r !== null) throw new TypeError("Class extends value " + String(r) + " is not a constructor or null");
        e(t, r);
        function n() {
            this.constructor = t;
        }
        t.prototype = r === null ? Object.create(r) : (n.prototype = r.prototype, new n);
    };
}(), Pa = function(e) {
    Ef(t, e);
    function t(r, n, o, i) {
        var s = e.call(this, r) || this;
        return s._attributesProcessor = o, s._deltaMetricStorage = new ur(n), s._temporalMetricStorage = new cr(n, i), s;
    }
    return t.prototype.record = function(r, n, o, i) {
        n = this._attributesProcessor.process(n, o), this._deltaMetricStorage.record(r, n, o, i);
    }, t.prototype.collect = function(r, n) {
        var o = this._deltaMetricStorage.collect();
        return this._temporalMetricStorage.buildMetrics(r, this._instrumentDescriptor, o, n);
    }, t;
}(sr);
var Ia = function() {
    var e = function(t, r) {
        return e = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(n, o) {
            n.__proto__ = o;
        } || function(n, o) {
            for(var i in o)Object.prototype.hasOwnProperty.call(o, i) && (n[i] = o[i]);
        }, e(t, r);
    };
    return function(t, r) {
        if (typeof r != "function" && r !== null) throw new TypeError("Class extends value " + String(r) + " is not a constructor or null");
        e(t, r);
        function n() {
            this.constructor = t;
        }
        t.prototype = r === null ? Object.create(r) : (n.prototype = r.prototype, new n);
    };
}(), lr = function() {
    function e() {}
    return e.Noop = function() {
        return Tf;
    }, e;
}();
var mf = function(e) {
    Ia(t, e);
    function t() {
        return e !== null && e.apply(this, arguments) || this;
    }
    return t.prototype.process = function(r, n) {
        return r;
    }, t;
}(lr);
var uE = function(e) {
    Ia(t, e);
    function t(r) {
        var n = e.call(this) || this;
        return n._allowedAttributeNames = r, n;
    }
    return t.prototype.process = function(r, n) {
        var o = this, i = {};
        return Object.keys(r).filter(function(s) {
            return o._allowedAttributeNames.includes(s);
        }).forEach(function(s) {
            return i[s] = r[s];
        }), i;
    }, t;
}(lr);
var Tf = new mf;
var Sf = function(e, t, r, n) {
    function o(i) {
        return i instanceof r ? i : new r(function(s) {
            s(i);
        });
    }
    return new (r || (r = Promise))(function(i, s) {
        function u(f) {
            try {
                a(n.next(f));
            } catch (p) {
                s(p);
            }
        }
        function c(f) {
            try {
                a(n.throw(f));
            } catch (p) {
                s(p);
            }
        }
        function a(f) {
            f.done ? i(f.value) : o(f.value).then(u, c);
        }
        a((n = n.apply(e, t || [])).next());
    });
}, gf = function(e, t) {
    var r = {
        label: 0,
        sent: function() {
            if (i[0] & 1) throw i[1];
            return i[1];
        },
        trys: [],
        ops: []
    }, n, o, i, s;
    return s = {
        next: u(0),
        throw: u(1),
        return: u(2)
    }, typeof Symbol == "function" && (s[Symbol.iterator] = function() {
        return this;
    }), s;
    "TURBOPACK unreachable";
    function u(a) {
        return function(f) {
            return c([
                a,
                f
            ]);
        };
    }
    function c(a) {
        if (n) throw new TypeError("Generator is already executing.");
        for(; r;)try {
            if (n = 1, o && (i = a[0] & 2 ? o.return : a[0] ? o.throw || ((i = o.return) && i.call(o), 0) : o.next) && !(i = i.call(o, a[1])).done) return i;
            switch(o = 0, i && (a = [
                a[0] & 2,
                i.value
            ]), a[0]){
                case 0:
                case 1:
                    i = a;
                    break;
                case 4:
                    return r.label++, {
                        value: a[1],
                        done: !1
                    };
                case 5:
                    r.label++, o = a[1], a = [
                        0
                    ];
                    continue;
                case 7:
                    a = r.ops.pop(), r.trys.pop();
                    continue;
                default:
                    if (i = r.trys, !(i = i.length > 0 && i[i.length - 1]) && (a[0] === 6 || a[0] === 2)) {
                        r = 0;
                        continue;
                    }
                    if (a[0] === 3 && (!i || a[1] > i[0] && a[1] < i[3])) {
                        r.label = a[1];
                        break;
                    }
                    if (a[0] === 6 && r.label < i[1]) {
                        r.label = i[1], i = a;
                        break;
                    }
                    if (i && r.label < i[2]) {
                        r.label = i[2], r.ops.push(a);
                        break;
                    }
                    i[2] && r.ops.pop(), r.trys.pop();
                    continue;
            }
            a = t.call(e, r);
        } catch (f) {
            a = [
                6,
                f
            ], o = 0;
        } finally{
            n = i = 0;
        }
        if (a[0] & 5) throw a[1];
        return {
            value: a[0] ? a[1] : void 0,
            done: !0
        };
    }
}, yf = function(e, t) {
    var r = typeof Symbol == "function" && e[Symbol.iterator];
    if (!r) return e;
    var n = r.call(e), o, i = [], s;
    try {
        for(; (t === void 0 || t-- > 0) && !(o = n.next()).done;)i.push(o.value);
    } catch (u) {
        s = {
            error: u
        };
    } finally{
        try {
            o && !o.done && (r = n.return) && r.call(n);
        } finally{
            if (s) throw s.error;
        }
    }
    return i;
}, Ca = function() {
    function e(t, r) {
        this._meterProviderSharedState = t, this._instrumentationScope = r, this.metricStorageRegistry = new ya, this.observableRegistry = new La, this.meter = new ma(this);
    }
    return e.prototype.registerMetricStorage = function(t) {
        var r = this._registerMetricStorage(t, Pa);
        return r.length === 1 ? r[0] : new va(r);
    }, e.prototype.registerAsyncMetricStorage = function(t) {
        var r = this._registerMetricStorage(t, ga);
        return r;
    }, e.prototype.collect = function(t, r, n) {
        return Sf(this, void 0, void 0, function() {
            var o, i, s;
            return gf(this, function(u) {
                switch(u.label){
                    case 0:
                        return [
                            4,
                            this.observableRegistry.observe(r, n?.timeoutMillis)
                        ];
                    case 1:
                        return o = u.sent(), i = this.metricStorageRegistry.getStorages(t), i.length === 0 ? [
                            2,
                            null
                        ] : (s = i.map(function(c) {
                            return c.collect(t, r);
                        }).filter(ta), s.length === 0 ? [
                            2,
                            {
                                errors: o
                            }
                        ] : [
                            2,
                            {
                                scopeMetrics: {
                                    scope: this._instrumentationScope,
                                    metrics: s
                                },
                                errors: o
                            }
                        ]);
                }
            });
        });
    }, e.prototype._registerMetricStorage = function(t, r) {
        var n = this, o = this._meterProviderSharedState.viewRegistry.findViews(t, this._instrumentationScope), i = o.map(function(c) {
            var a = sa(c, t), f = n.metricStorageRegistry.findOrUpdateCompatibleStorage(a);
            if (f != null) return f;
            var p = c.aggregation.createAggregator(a), d = new r(a, p, c.attributesProcessor, n._meterProviderSharedState.metricCollectors);
            return n.metricStorageRegistry.register(d), d;
        });
        if (i.length === 0) {
            var s = this._meterProviderSharedState.selectAggregations(t.type), u = s.map(function(c) {
                var a = yf(c, 2), f = a[0], p = a[1], d = n.metricStorageRegistry.findOrUpdateCompatibleCollectorStorage(f, t);
                if (d != null) return d;
                var m = p.createAggregator(t), g = new r(t, m, lr.Noop(), [
                    f
                ]);
                return n.metricStorageRegistry.registerForCollector(f, g), g;
            });
            i = i.concat(u);
        }
        return i;
    }, e;
}();
var vf = function(e) {
    var t = typeof Symbol == "function" && Symbol.iterator, r = t && e[t], n = 0;
    if (r) return r.call(e);
    if (e && typeof e.length == "number") return {
        next: function() {
            return e && n >= e.length && (e = void 0), {
                value: e && e[n++],
                done: !e
            };
        }
    };
    throw new TypeError(t ? "Object is not iterable." : "Symbol.iterator is not defined.");
}, Na = function() {
    function e(t) {
        this.resource = t, this.viewRegistry = new ca, this.metricCollectors = [], this.meterSharedStates = new Map;
    }
    return e.prototype.getMeterSharedState = function(t) {
        var r = ra(t), n = this.meterSharedStates.get(r);
        return n == null && (n = new Ca(this, t), this.meterSharedStates.set(r, n)), n;
    }, e.prototype.selectAggregations = function(t) {
        var r, n, o = [];
        try {
            for(var i = vf(this.metricCollectors), s = i.next(); !s.done; s = i.next()){
                var u = s.value;
                o.push([
                    u,
                    u.selectAggregation(t)
                ]);
            }
        } catch (c) {
            r = {
                error: c
            };
        } finally{
            try {
                s && !s.done && (n = i.return) && n.call(i);
            } finally{
                if (r) throw r.error;
            }
        }
        return o;
    }, e;
}();
var wa = v(P()), fr = function(e, t, r, n) {
    function o(i) {
        return i instanceof r ? i : new r(function(s) {
            s(i);
        });
    }
    return new (r || (r = Promise))(function(i, s) {
        function u(f) {
            try {
                a(n.next(f));
            } catch (p) {
                s(p);
            }
        }
        function c(f) {
            try {
                a(n.throw(f));
            } catch (p) {
                s(p);
            }
        }
        function a(f) {
            f.done ? i(f.value) : o(f.value).then(u, c);
        }
        a((n = n.apply(e, t || [])).next());
    });
}, pr = function(e, t) {
    var r = {
        label: 0,
        sent: function() {
            if (i[0] & 1) throw i[1];
            return i[1];
        },
        trys: [],
        ops: []
    }, n, o, i, s;
    return s = {
        next: u(0),
        throw: u(1),
        return: u(2)
    }, typeof Symbol == "function" && (s[Symbol.iterator] = function() {
        return this;
    }), s;
    "TURBOPACK unreachable";
    function u(a) {
        return function(f) {
            return c([
                a,
                f
            ]);
        };
    }
    function c(a) {
        if (n) throw new TypeError("Generator is already executing.");
        for(; r;)try {
            if (n = 1, o && (i = a[0] & 2 ? o.return : a[0] ? o.throw || ((i = o.return) && i.call(o), 0) : o.next) && !(i = i.call(o, a[1])).done) return i;
            switch(o = 0, i && (a = [
                a[0] & 2,
                i.value
            ]), a[0]){
                case 0:
                case 1:
                    i = a;
                    break;
                case 4:
                    return r.label++, {
                        value: a[1],
                        done: !1
                    };
                case 5:
                    r.label++, o = a[1], a = [
                        0
                    ];
                    continue;
                case 7:
                    a = r.ops.pop(), r.trys.pop();
                    continue;
                default:
                    if (i = r.trys, !(i = i.length > 0 && i[i.length - 1]) && (a[0] === 6 || a[0] === 2)) {
                        r = 0;
                        continue;
                    }
                    if (a[0] === 3 && (!i || a[1] > i[0] && a[1] < i[3])) {
                        r.label = a[1];
                        break;
                    }
                    if (a[0] === 6 && r.label < i[1]) {
                        r.label = i[1], i = a;
                        break;
                    }
                    if (i && r.label < i[2]) {
                        r.label = i[2], r.ops.push(a);
                        break;
                    }
                    i[2] && r.ops.pop(), r.trys.pop();
                    continue;
            }
            a = t.call(e, r);
        } catch (f) {
            a = [
                6,
                f
            ], o = 0;
        } finally{
            n = i = 0;
        }
        if (a[0] & 5) throw a[1];
        return {
            value: a[0] ? a[1] : void 0,
            done: !0
        };
    }
}, Af = function(e, t) {
    var r = typeof Symbol == "function" && e[Symbol.iterator];
    if (!r) return e;
    var n = r.call(e), o, i = [], s;
    try {
        for(; (t === void 0 || t-- > 0) && !(o = n.next()).done;)i.push(o.value);
    } catch (u) {
        s = {
            error: u
        };
    } finally{
        try {
            o && !o.done && (r = n.return) && r.call(n);
        } finally{
            if (s) throw s.error;
        }
    }
    return i;
}, Rf = function(e, t, r) {
    if (r || arguments.length === 2) for(var n = 0, o = t.length, i; n < o; n++)(i || !(n in t)) && (i || (i = Array.prototype.slice.call(t, 0, n)), i[n] = t[n]);
    return e.concat(i || Array.prototype.slice.call(t));
}, Ma = function() {
    function e(t, r) {
        this._sharedState = t, this._metricReader = r;
    }
    return e.prototype.collect = function(t) {
        return fr(this, void 0, void 0, function() {
            var r, n, o, i, s = this;
            return pr(this, function(u) {
                switch(u.label){
                    case 0:
                        return r = (0, wa.millisToHrTime)(Date.now()), n = [], o = [], i = Array.from(this._sharedState.meterSharedStates.values()).map(function(c) {
                            return fr(s, void 0, void 0, function() {
                                var a;
                                return pr(this, function(f) {
                                    switch(f.label){
                                        case 0:
                                            return [
                                                4,
                                                c.collect(this, r, t)
                                            ];
                                        case 1:
                                            return a = f.sent(), a?.scopeMetrics != null && n.push(a.scopeMetrics), a?.errors != null && o.push.apply(o, Rf([], Af(a.errors), !1)), [
                                                2
                                            ];
                                    }
                                });
                            });
                        }), [
                            4,
                            Promise.all(i)
                        ];
                    case 1:
                        return u.sent(), [
                            2,
                            {
                                resourceMetrics: {
                                    resource: this._sharedState.resource,
                                    scopeMetrics: n
                                },
                                errors: o
                            }
                        ];
                }
            });
        });
    }, e.prototype.forceFlush = function(t) {
        return fr(this, void 0, void 0, function() {
            return pr(this, function(r) {
                switch(r.label){
                    case 0:
                        return [
                            4,
                            this._metricReader.forceFlush(t)
                        ];
                    case 1:
                        return r.sent(), [
                            2
                        ];
                }
            });
        });
    }, e.prototype.shutdown = function(t) {
        return fr(this, void 0, void 0, function() {
            return pr(this, function(r) {
                switch(r.label){
                    case 0:
                        return [
                            4,
                            this._metricReader.shutdown(t)
                        ];
                    case 1:
                        return r.sent(), [
                            2
                        ];
                }
            });
        });
    }, e.prototype.selectAggregationTemporality = function(t) {
        return this._metricReader.selectAggregationTemporality(t);
    }, e.prototype.selectAggregation = function(t) {
        return this._metricReader.selectAggregation(t);
    }, e;
}();
var xa = function(e, t, r, n) {
    function o(i) {
        return i instanceof r ? i : new r(function(s) {
            s(i);
        });
    }
    return new (r || (r = Promise))(function(i, s) {
        function u(f) {
            try {
                a(n.next(f));
            } catch (p) {
                s(p);
            }
        }
        function c(f) {
            try {
                a(n.throw(f));
            } catch (p) {
                s(p);
            }
        }
        function a(f) {
            f.done ? i(f.value) : o(f.value).then(u, c);
        }
        a((n = n.apply(e, t || [])).next());
    });
}, Da = function(e, t) {
    var r = {
        label: 0,
        sent: function() {
            if (i[0] & 1) throw i[1];
            return i[1];
        },
        trys: [],
        ops: []
    }, n, o, i, s;
    return s = {
        next: u(0),
        throw: u(1),
        return: u(2)
    }, typeof Symbol == "function" && (s[Symbol.iterator] = function() {
        return this;
    }), s;
    "TURBOPACK unreachable";
    function u(a) {
        return function(f) {
            return c([
                a,
                f
            ]);
        };
    }
    function c(a) {
        if (n) throw new TypeError("Generator is already executing.");
        for(; r;)try {
            if (n = 1, o && (i = a[0] & 2 ? o.return : a[0] ? o.throw || ((i = o.return) && i.call(o), 0) : o.next) && !(i = i.call(o, a[1])).done) return i;
            switch(o = 0, i && (a = [
                a[0] & 2,
                i.value
            ]), a[0]){
                case 0:
                case 1:
                    i = a;
                    break;
                case 4:
                    return r.label++, {
                        value: a[1],
                        done: !1
                    };
                case 5:
                    r.label++, o = a[1], a = [
                        0
                    ];
                    continue;
                case 7:
                    a = r.ops.pop(), r.trys.pop();
                    continue;
                default:
                    if (i = r.trys, !(i = i.length > 0 && i[i.length - 1]) && (a[0] === 6 || a[0] === 2)) {
                        r = 0;
                        continue;
                    }
                    if (a[0] === 3 && (!i || a[1] > i[0] && a[1] < i[3])) {
                        r.label = a[1];
                        break;
                    }
                    if (a[0] === 6 && r.label < i[1]) {
                        r.label = i[1], i = a;
                        break;
                    }
                    if (i && r.label < i[2]) {
                        r.label = i[2], r.ops.push(a);
                        break;
                    }
                    i[2] && r.ops.pop(), r.trys.pop();
                    continue;
            }
            a = t.call(e, r);
        } catch (f) {
            a = [
                6,
                f
            ], o = 0;
        } finally{
            n = i = 0;
        }
        if (a[0] & 5) throw a[1];
        return {
            value: a[0] ? a[1] : void 0,
            done: !0
        };
    }
}, Of = function(e) {
    var t = typeof Symbol == "function" && Symbol.iterator, r = t && e[t], n = 0;
    if (r) return r.call(e);
    if (e && typeof e.length == "number") return {
        next: function() {
            return e && n >= e.length && (e = void 0), {
                value: e && e[n++],
                done: !e
            };
        }
    };
    throw new TypeError(t ? "Object is not iterable." : "Symbol.iterator is not defined.");
}, In = function() {
    function e(t) {
        var r, n, o;
        this._shutdown = !1;
        var i = H.default().merge((o = t?.resource) !== null && o !== void 0 ? o : H.empty());
        if (this._sharedState = new Na(i), t?.views != null && t.views.length > 0) try {
            for(var s = Of(t.views), u = s.next(); !u.done; u = s.next()){
                var c = u.value;
                this._sharedState.viewRegistry.addView(c);
            }
        } catch (a) {
            r = {
                error: a
            };
        } finally{
            try {
                u && !u.done && (n = s.return) && n.call(s);
            } finally{
                if (r) throw r.error;
            }
        }
    }
    return e.prototype.getMeter = function(t, r, n) {
        return r === void 0 && (r = ""), n === void 0 && (n = {}), this._shutdown ? (l.diag.warn("A shutdown MeterProvider cannot provide a Meter"), (0, l.createNoopMeter)()) : this._sharedState.getMeterSharedState({
            name: t,
            version: r,
            schemaUrl: n.schemaUrl
        }).meter;
    }, e.prototype.addMetricReader = function(t) {
        var r = new Ma(this._sharedState, t);
        t.setMetricProducer(r), this._sharedState.metricCollectors.push(r);
    }, e.prototype.shutdown = function(t) {
        return xa(this, void 0, void 0, function() {
            return Da(this, function(r) {
                switch(r.label){
                    case 0:
                        return this._shutdown ? (l.diag.warn("shutdown may only be called once per MeterProvider"), [
                            2
                        ]) : (this._shutdown = !0, [
                            4,
                            Promise.all(this._sharedState.metricCollectors.map(function(n) {
                                return n.shutdown(t);
                            }))
                        ]);
                    case 1:
                        return r.sent(), [
                            2
                        ];
                }
            });
        });
    }, e.prototype.forceFlush = function(t) {
        return xa(this, void 0, void 0, function() {
            return Da(this, function(r) {
                switch(r.label){
                    case 0:
                        return this._shutdown ? (l.diag.warn("invalid attempt to force flush after MeterProvider shutdown"), [
                            2
                        ]) : [
                            4,
                            Promise.all(this._sharedState.metricCollectors.map(function(n) {
                                return n.forceFlush(t);
                            }))
                        ];
                    case 1:
                        return r.sent(), [
                            2
                        ];
                }
            });
        });
    }, e;
}();
var Nr = v(Oe(), 1), Iu = v(Va(), 1), Ot = v(P(), 1);
var Ha = "http.method", Fa = "http.url", hr = "http.host", ja = "http.scheme", wn = "http.status_code", ka = "http.user_agent", Mn = "http.response_content_length_uncompressed", qa = "net.peer.port", Xa = "net.peer.name";
var Ka = "service.name", Wa = "service.version";
E();
var Nf = Symbol.for("@vercel/request-context");
function nt() {
    return globalThis[Nf]?.get();
}
function Er(e) {
    return Object.fromEntries(Object.entries(e).filter(([t, r])=>r !== void 0));
}
function Ya(e) {
    return e ? e.split("::").at(-1) : void 0;
}
function za(e = nt(), t) {
    if (!e) return;
    let r = t ? Mf(t, e.headers) : void 0;
    return Er({
        [hr]: e.headers.host,
        [ka]: e.headers["user-agent"],
        "http.referer": e.headers.referer,
        "vercel.request_id": Ya(e.headers["x-vercel-id"]),
        "vercel.matched_path": e.headers["x-matched-path"],
        "vercel.edge_region": process.env.VERCEL_REGION,
        ...r
    });
}
var wf = {
    keys (e) {
        return [];
    },
    get (e, t) {
        return e[t.toLocaleLowerCase()];
    }
};
function Mf(e, t) {
    if (typeof e == "function") return e(t, wf);
    let r = {};
    for (let [n, o] of Object.entries(e)){
        let i = t[o.toLocaleLowerCase()];
        i !== void 0 && (r[n] = i);
    }
    return r;
}
E();
function zt(e) {
    return (e & l.TraceFlags.SAMPLED) !== 0;
}
var mr = class {
    constructor(t, r){
        this.processors = t;
        this.attributesFromHeaders = r;
        this.rootSpanIds = new Map;
        this.waitSpanEnd = new Map;
    }
    forceFlush() {
        return Promise.all(this.processors.map((t)=>t.forceFlush().catch((r)=>{
                l.diag.error("@vercel/otel: forceFlush failed:", r);
            }))).then(()=>{});
    }
    shutdown() {
        return Promise.all(this.processors.map((t)=>t.shutdown().catch(()=>{}))).then(()=>{});
    }
    onStart(t, r) {
        let { traceId: n, spanId: o, traceFlags: i } = t.spanContext(), s = !t.parentSpanId || !this.rootSpanIds.has(n);
        if (s ? this.rootSpanIds.set(n, {
            rootSpanId: o,
            open: []
        }) : this.rootSpanIds.get(n)?.open.push(t), s && zt(i)) {
            let u = nt(), c = za(u, this.attributesFromHeaders);
            c && t.setAttributes(c), u && u.waitUntil(async ()=>{
                if (this.rootSpanIds.has(n)) {
                    let a = new Promise((p)=>{
                        this.waitSpanEnd.set(n, p);
                    }), f;
                    await Promise.race([
                        a,
                        new Promise((p)=>{
                            f = setTimeout(()=>{
                                this.waitSpanEnd.delete(n), p(void 0);
                            }, 50);
                        })
                    ]), f && clearTimeout(f);
                }
                return this.forceFlush();
            });
        }
        for (let u of this.processors)u.onStart(t, r);
    }
    onEnd(t) {
        let { traceId: r, spanId: n, traceFlags: o } = t.spanContext(), i = zt(o), s = this.rootSpanIds.get(r), u = s?.rootSpanId === n;
        if (i) {
            let c = Df(t);
            c && Object.assign(t.attributes, c);
        }
        if (u) {
            if (this.rootSpanIds.delete(r), s.open.length > 0) {
                for (let c of s.open)if (!c.ended && c.spanContext().spanId !== n) try {
                    c.end();
                } catch (a) {
                    l.diag.error("@vercel/otel: onEnd failed:", a);
                }
            }
        } else if (s) for(let c = 0; c < s.open.length; c++)s.open[c]?.spanContext().spanId === n && s.open.splice(c, 1);
        for (let c of this.processors)c.onEnd(t);
        if (u) {
            let c = this.waitSpanEnd.get(r);
            c && (this.waitSpanEnd.delete(r), c());
        }
    }
}, xf = {
    [l.SpanKind.INTERNAL]: "internal",
    [l.SpanKind.SERVER]: "server",
    [l.SpanKind.CLIENT]: "client",
    [l.SpanKind.PRODUCER]: "producer",
    [l.SpanKind.CONSUMER]: "consumer"
};
function Df(e) {
    let { kind: t, attributes: r } = e, { "operation.name": n, "resource.name": o, "span.type": i, "next.span_type": s, "http.method": u, "http.route": c } = r;
    if (n) return;
    let a = o ?? (u && typeof u == "string" && c && typeof c == "string" ? `${u} ${c}` : c);
    if (e.kind === l.SpanKind.SERVER && u && c && typeof u == "string" && typeof c == "string") return {
        "operation.name": "web.request",
        "resource.name": a
    };
    let f = e.instrumentationLibrary.name, p = s ?? i;
    if (p && typeof p == "string") {
        let d = $a(f, p);
        return c ? {
            "operation.name": d,
            "resource.name": a
        } : {
            "operation.name": d
        };
    }
    return {
        "operation.name": $a(f, t === l.SpanKind.INTERNAL ? "" : xf[t])
    };
}
function $a(e, t) {
    if (!e) return t;
    let r = e.replace(/[ @./]/g, "_");
    return r.startsWith("_") && (r = r.slice(1)), t ? `${r}.${t}` : r;
}
var fs = v(Fn(), 1);
var ls = v(cs(), 1);
E();
var $t = class extends ls.OTLPExporterBase {
    constructor(t = {}){
        super(t), t.headers && (this._headers = t.headers);
    }
    onShutdown() {
        l.diag.debug("@vercel/otel/otlp: onShutdown");
    }
    onInit() {
        l.diag.debug("@vercel/otel/otlp: onInit");
    }
    send(t, r, n) {
        if (this._shutdownOnce.isCalled) {
            l.diag.debug("@vercel/otel/otlp: Shutdown already started. Cannot send objects");
            return;
        }
        let o = this.convert(t), i, s, u;
        try {
            ({ body: i, contentType: s, headers: u } = this.toMessage(o));
        } catch (a) {
            l.diag.warn("@vercel/otel/otlp: no proto", a);
            return;
        }
        let c = fetch(this.url, {
            method: "POST",
            body: i,
            headers: {
                ...this._headers,
                ...u,
                "Content-Type": s,
                "User-Agent": "OTel-OTLP-Exporter-JavaScript/0.46.0"
            },
            next: {
                internal: !0
            }
        }).then((a)=>{
            l.diag.debug("@vercel/otel/otlp: onSuccess", a.status, a.statusText), r(), a.arrayBuffer().catch(()=>{});
        }).catch((a)=>{
            l.diag.error("@vercel/otel/otlp: onError", a), n(a);
        }).finally(()=>{
            let a = this._sendingPromises.indexOf(c);
            this._sendingPromises.splice(a, 1);
        });
        this._sendingPromises.push(c);
    }
    getDefaultUrl(t) {
        throw new Error("Method not implemented.");
    }
};
var rp = "v1/traces", np = `http://localhost:4318/${rp}`;
function yr(e) {
    return typeof e.url == "string" ? e.url : np;
}
var Qt = class {
    constructor(t = {}){
        this.impl = new Xn(t);
    }
    export(t, r) {
        this.impl.export(t, r);
    }
    shutdown() {
        return this.impl.shutdown();
    }
    forceFlush() {
        return this.impl.forceFlush();
    }
}, Xn = class extends $t {
    convert(t) {
        return (0, fs.createExportTraceServiceRequest)(t, {
            useHex: !0,
            useLongBits: !1
        });
    }
    toMessage(t) {
        return {
            body: JSON.stringify(t),
            contentType: "application/json"
        };
    }
    getDefaultUrl(t) {
        return yr(t);
    }
};
var du = v(Fn(), 1);
var lu = v(cu(), 1);
function fu(e) {
    let t = new lu.Writer;
    return _p(e, t), t.finish();
}
function _p(e, t) {
    if (e.resourceSpans != null && e.resourceSpans.length) for(let r = 0; r < e.resourceSpans.length; ++r)hp(e.resourceSpans[r], t.uint32(10).fork()).ldelim();
    return t;
}
function hp(e, t) {
    if (e.resource != null && Ep(e.resource, t.uint32(10).fork()).ldelim(), e.scopeSpans != null && e.scopeSpans.length) for(let r = 0; r < e.scopeSpans.length; ++r)mp(e.scopeSpans[r], t.uint32(18).fork()).ldelim();
    return e.schemaUrl != null && t.uint32(26).string(e.schemaUrl), t;
}
function Ep(e, t) {
    if (e.attributes != null && e.attributes.length) for(let r = 0; r < e.attributes.length; ++r)Jt(e.attributes[r], t.uint32(10).fork()).ldelim();
    return e.droppedAttributesCount != null && t.uint32(16).uint32(e.droppedAttributesCount), t;
}
function mp(e, t) {
    if (e.scope != null && gp(e.scope, t.uint32(10).fork()).ldelim(), e.spans != null && e.spans.length) for(let r = 0; r < e.spans.length; ++r)yp(e.spans[r], t.uint32(18).fork()).ldelim();
    return e.schemaUrl != null && t.uint32(26).string(e.schemaUrl), t;
}
function Jt(e, t) {
    return e.key != null && t.uint32(10).string(e.key), e.value != null && pu(e.value, t.uint32(18).fork()).ldelim(), t;
}
function pu(e, t) {
    return e.stringValue != null && t.uint32(10).string(e.stringValue), e.boolValue != null && t.uint32(16).bool(e.boolValue), e.intValue != null && t.uint32(24).int64(e.intValue), e.doubleValue != null && t.uint32(33).double(e.doubleValue), e.arrayValue != null && Tp(e.arrayValue, t.uint32(42).fork()).ldelim(), e.kvlistValue != null && Sp(e.kvlistValue, t.uint32(50).fork()).ldelim(), e.bytesValue != null && t.uint32(58).bytes(e.bytesValue), t;
}
function Tp(e, t) {
    if (e.values != null && e.values.length) for(let r = 0; r < e.values.length; ++r)pu(e.values[r], t.uint32(10).fork()).ldelim();
    return t;
}
function Sp(e, t) {
    if (e.values != null && e.values.length) for(let r = 0; r < e.values.length; ++r)Jt(e.values[r], t.uint32(10).fork()).ldelim();
    return t;
}
function gp(e, t) {
    if (e.name != null && t.uint32(10).string(e.name), e.version != null && t.uint32(18).string(e.version), e.attributes != null && e.attributes.length) for(let r = 0; r < e.attributes.length; ++r)Jt(e.attributes[r], t.uint32(26).fork()).ldelim();
    return e.droppedAttributesCount != null && t.uint32(32).uint32(e.droppedAttributesCount), t;
}
function yp(e, t) {
    if (e.traceId != null && t.uint32(10).bytes(e.traceId), e.spanId != null && t.uint32(18).bytes(e.spanId), e.traceState != null && t.uint32(26).string(e.traceState), e.parentSpanId != null && t.uint32(34).bytes(e.parentSpanId), e.name != null && t.uint32(42).string(e.name), e.kind != null && t.uint32(48).int32(e.kind), e.startTimeUnixNano != null && t.uint32(57).fixed64(e.startTimeUnixNano), e.endTimeUnixNano != null && t.uint32(65).fixed64(e.endTimeUnixNano), e.attributes != null && e.attributes.length) for(let r = 0; r < e.attributes.length; ++r)Jt(e.attributes[r], t.uint32(74).fork()).ldelim();
    if (e.droppedAttributesCount != null && t.uint32(80).uint32(e.droppedAttributesCount), e.events != null && e.events.length) for(let r = 0; r < e.events.length; ++r)Ap(e.events[r], t.uint32(90).fork()).ldelim();
    if (e.droppedEventsCount != null && t.uint32(96).uint32(e.droppedEventsCount), e.links != null && e.links.length) for(let r = 0; r < e.links.length; ++r)Rp(e.links[r], t.uint32(106).fork()).ldelim();
    return e.droppedLinksCount != null && t.uint32(112).uint32(e.droppedLinksCount), e.status != null && vp(e.status, t.uint32(122).fork()).ldelim(), t;
}
function vp(e, t) {
    return e.message != null && t.uint32(18).string(e.message), e.code != null && t.uint32(24).int32(e.code), t;
}
function Ap(e, t) {
    if (e.timeUnixNano != null && t.uint32(9).fixed64(e.timeUnixNano), e.name != null && t.uint32(18).string(e.name), e.attributes != null && e.attributes.length) for(let r = 0; r < e.attributes.length; ++r)Jt(e.attributes[r], t.uint32(26).fork()).ldelim();
    return e.droppedAttributesCount != null && t.uint32(32).uint32(e.droppedAttributesCount), t;
}
function Rp(e, t) {
    if (e.traceId != null && t.uint32(10).bytes(e.traceId), e.spanId != null && t.uint32(18).bytes(e.spanId), e.traceState != null && t.uint32(26).string(e.traceState), e.attributes != null && e.attributes.length) for(let r = 0; r < e.attributes.length; ++r)Jt(e.attributes[r], t.uint32(34).fork()).ldelim();
    return e.droppedAttributesCount != null && t.uint32(40).uint32(e.droppedAttributesCount), t;
}
var Bt = class {
    constructor(t = {}){
        this.impl = new oi(t);
    }
    export(t, r) {
        this.impl.export(t, r);
    }
    shutdown() {
        return this.impl.shutdown();
    }
    forceFlush() {
        return this.impl.forceFlush();
    }
}, oi = class extends $t {
    convert(t) {
        return (0, du.createExportTraceServiceRequest)(t, void 0);
    }
    toMessage(t) {
        return {
            body: fu(t),
            contentType: "application/x-protobuf",
            headers: {
                accept: "application/x-protobuf"
            }
        };
    }
    getDefaultUrl(t) {
        return yr(t);
    }
};
E();
function _u(e, t) {
    return e.replace(/\{(?<temp1>[^{}]+)\}/g, (r, n)=>{
        let o = t[n];
        return o !== void 0 ? String(o) : r;
    });
}
var te = class {
    constructor(t = {}){
        this.instrumentationName = "@vercel/otel/fetch";
        this.instrumentationVersion = "1.0.0";
        this.config = t;
    }
    getConfig() {
        return this.config;
    }
    setConfig() {}
    setTracerProvider(t) {
        this.tracerProvider = t;
    }
    setMeterProvider() {}
    shouldIgnore(t, r) {
        let n = this.config.ignoreUrls ?? [];
        if (r?.opentelemetry?.ignore !== void 0) return r.opentelemetry.ignore;
        if (n.length === 0) return !1;
        let o = t.toString();
        return n.some((i)=>typeof i == "string" ? i === "*" ? !0 : o.startsWith(i) : i.test(o));
    }
    shouldPropagate(t, r) {
        let n = process.env.VERCEL_URL || process.env.NEXT_PUBLIC_VERCEL_URL || null, o = process.env.VERCEL_BRANCH_URL || process.env.NEXT_PUBLIC_VERCEL_BRANCH_URL || null, i = this.config.propagateContextUrls ?? [], s = this.config.dontPropagateContextUrls ?? [];
        if (r?.opentelemetry?.propagateContext) return r.opentelemetry.propagateContext;
        let u = t.toString();
        return s.length > 0 && s.some((c)=>typeof c == "string" ? c === "*" ? !0 : u.startsWith(c) : c.test(u)) ? !1 : n && t.protocol === "https:" && (t.host === n || t.host === o || t.host === nt()?.headers.host) || !n && t.protocol === "http:" && t.hostname === "localhost" ? !0 : i.some((c)=>typeof c == "string" ? c === "*" ? !0 : u.startsWith(c) : c.test(u));
    }
    startSpan({ tracer: t, url: r, fetchType: n, method: o = "GET", name: i, attributes: s = {} }) {
        let u = this.config.resourceNameTemplate, c = {
            [Ha]: o,
            [Fa]: r.toString(),
            [hr]: r.host,
            [ja]: r.protocol.replace(":", ""),
            [Xa]: r.hostname,
            [qa]: r.port
        }, a = u ? _u(u, c) : Lp(r.toString()), f = i ?? `${n} ${o} ${r.toString()}`, p = l.context.active();
        return t.startSpan(f, {
            kind: l.SpanKind.CLIENT,
            attributes: {
                ...c,
                "operation.name": `${n}.${o}`,
                "http.client.name": n,
                "resource.name": a,
                ...s
            }
        }, p);
    }
    instrumentHttp(t, r) {
        let { tracerProvider: n } = this;
        if (!n) return;
        let o = n.getTracer(this.instrumentationName, this.instrumentationVersion), { attributesFromRequestHeaders: i, attributesFromResponseHeaders: s } = this.config, u = t.request, c = t.get, a = (f)=>(p, d, m)=>{
                let g, C = {}, M;
                if (typeof p == "string" || p instanceof URL ? (g = new URL(p.toString()), typeof d == "function" ? M = d : d && typeof m == "function" ? (C = d, M = m) : d && (C = d)) : (C = p, typeof d == "function" && (M = d), g = Ip(C, r)), this.shouldIgnore(g)) return f.apply(this, [
                    g,
                    C,
                    M
                ]);
                let T = this.startSpan({
                    tracer: o,
                    url: g,
                    fetchType: "http",
                    method: C.method || "GET"
                });
                if (!T.isRecording() || !zt(T.spanContext().traceFlags)) return T.end(), f.apply(this, [
                    g,
                    C,
                    M
                ]);
                if (this.shouldPropagate(g)) {
                    let et = l.context.active(), q = l.trace.setSpan(et, T);
                    l.propagation.inject(q, C.headers || {}, bp);
                }
                i && br(T, i, C.headers || {}, Eu);
                try {
                    let et = Date.now(), q = f.apply(this, [
                        g,
                        C,
                        M
                    ]);
                    return q.prependListener("response", (U)=>{
                        let wr = Date.now() - et;
                        T.setAttribute("http.response_time", wr), U.statusCode !== void 0 ? (T.setAttribute(wn, U.statusCode), U.statusCode >= 500 && Gt(T, `Status: ${U.statusCode}`)) : Gt(T, "Response status code is undefined"), s && br(T, s, U.headers, Eu), q.listenerCount("response") <= 1 && U.resume(), U.on("end", ()=>{
                            let bt, Mr = U.statusCode;
                            U.aborted && !U.complete ? bt = {
                                code: l.SpanStatusCode.ERROR
                            } : Mr && Mr >= 100 && Mr < 500 ? bt = {
                                code: l.SpanStatusCode.UNSET
                            } : bt = {
                                code: l.SpanStatusCode.ERROR
                            }, T.setStatus(bt), T.isRecording() && (U.headers["content-length"] && T.setAttribute(Mn, U.headers["content-length"]), T.end());
                        });
                    }), q.on("error", (U)=>{
                        T.isRecording() && (Gt(T, U), T.end());
                    }), q.on("close", ()=>{
                        T.isRecording() && T.end();
                    }), q;
                } catch (et) {
                    throw Gt(T, et), T.end(), et;
                }
            };
        t.request = a(u), t.get = a(c);
    }
    instrumentFetch() {
        let { tracerProvider: t } = this;
        if (!t) return;
        let r = t.getTracer(this.instrumentationName, this.instrumentationVersion), { attributesFromRequestHeaders: n, attributesFromResponseHeaders: o } = this.config;
        process.env.NEXT_OTEL_FETCH_DISABLED = "1";
        let i = globalThis.fetch;
        this.originalFetch = i;
        let s = async (u, c)=>{
            let a = c;
            if (a?.next?.internal) return i(u, a);
            let f = new Request(u instanceof Request ? u.clone() : u, a), p = new URL(f.url);
            if (this.shouldIgnore(p, a)) return i(u, a);
            let d = this.startSpan({
                tracer: r,
                url: p,
                fetchType: "fetch",
                method: f.method,
                name: a?.opentelemetry?.spanName,
                attributes: a?.opentelemetry?.attributes
            });
            if (!d.isRecording() || !zt(d.spanContext().traceFlags)) return d.end(), i(u, a);
            if (this.shouldPropagate(p, a)) {
                let m = l.context.active(), g = l.trace.setSpan(m, d);
                l.propagation.inject(g, f.headers, Op);
            }
            n && br(d, n, f.headers, hu);
            try {
                let m = Date.now();
                a?.body && a.body instanceof FormData && f.headers.delete("content-type");
                let g = await i(u, {
                    ...a,
                    headers: f.headers
                }), C = Date.now() - m;
                return d.setAttribute(wn, g.status), d.setAttribute("http.response_time", C), o && br(d, o, g.headers, hu), g.status >= 500 && Gt(d, `Status: ${g.status} (${g.statusText})`), g.body ? Pp(g).then((M)=>{
                    d.isRecording() && (d.setAttribute(Mn, M), d.end());
                }, (M)=>{
                    d.isRecording() && (Gt(d, M), d.end());
                }) : d.end(), g;
            } catch (m) {
                throw Gt(d, m), d.end(), m;
            }
        };
        globalThis.fetch = s;
    }
    enable() {
        this.disable(), this.instrumentFetch();
        try {
            let t = ci("node:http"), r = ci("node:https");
            this.instrumentHttp(t, "http:"), this.instrumentHttp(r, "https:");
        } catch  {}
    }
    disable() {
        this.originalFetch && (globalThis.fetch = this.originalFetch);
    }
}, Op = {
    set (e, t, r) {
        e.set(t, r);
    }
}, hu = {
    get (e, t) {
        let r = e.get(t);
        if (r !== null) return r.includes(",") ? r.split(",").map((n)=>n.trimStart()) : r;
    },
    keys (e) {
        let t = [];
        return e.forEach((r, n)=>{
            t.push(n);
        }), t;
    }
}, bp = {
    set (e, t, r) {
        e[t.toLowerCase()] = r;
    }
}, Eu = {
    get (e, t) {
        return e[t.toLowerCase()];
    },
    keys (e) {
        return Object.keys(e);
    }
};
function Lp(e) {
    let t = e.indexOf("?");
    return t === -1 ? e : e.substring(0, t);
}
function Pp(e) {
    let t = 0, n = e.clone().body?.getReader();
    if (!n) return Promise.resolve(0);
    let o = ()=>n.read().then(({ done: i, value: s })=>{
            if (!i) return t += s.byteLength, o();
        });
    return o().then(()=>t);
}
function Gt(e, t) {
    if (t instanceof Error) e.recordException(t), e.setStatus({
        code: l.SpanStatusCode.ERROR,
        message: t.message
    });
    else {
        let r = String(t);
        e.setStatus({
            code: l.SpanStatusCode.ERROR,
            message: r
        });
    }
}
function br(e, t, r, n) {
    for (let [o, i] of Object.entries(t)){
        let s = n.get(r, i);
        s !== void 0 && e.setAttribute(o, s);
    }
}
function Ip(e, t) {
    if (e.socketPath) throw new Error("Cannot construct a network URL: options.socketPath is specified, indicating a Unix domain socket.");
    let r = e.protocol ?? t;
    r && !r.endsWith(":") && (r += ":");
    let n = e.hostname, o = e.port ?? e.defaultPort;
    if (!n && e.host) {
        let a = e.host.split(":");
        n = a[0];
        let f = a[1];
        if (a.length > 1 && f && o === void 0) {
            let p = parseInt(f, 10);
            isNaN(p) || (o = p);
        }
    }
    n || (n = "localhost");
    let i;
    if (o !== void 0 && o !== "") {
        let a = parseInt(String(o), 10);
        isNaN(a) ? i = r === "https:" ? 443 : 80 : i = a;
    } else i = r === "https:" ? 443 : 80;
    let s = e.path || "/", u = `${r}//${n}:${i}`, c = new URL(s, u);
    if (e.auth) {
        let a = e.auth.split(":");
        c.username = decodeURIComponent(a[0] || ""), a.length > 1 && (c.password = decodeURIComponent(a[1] || ""));
    }
    return c;
}
E();
var mu = v(P(), 1), Cp = "00", ai = "traceparent", si = "tracestate", Te = class {
    fields() {
        return [
            ai,
            si
        ];
    }
    inject(t, r, n) {
        let o = l.trace.getSpanContext(t);
        if (!o || (0, mu.isTracingSuppressed)(t) || !(0, l.isSpanContextValid)(o)) return;
        let i = `${Cp}-${o.traceId}-${o.spanId}-0${Number(o.traceFlags || 0).toString(16)}`;
        n.set(r, ai, i), o.traceState && n.set(r, si, o.traceState.serialize());
    }
    extract(t, r, n) {
        let o = n.get(r, ai);
        if (!o) return t;
        let i = Array.isArray(o) ? o[0] : o;
        if (typeof i != "string") return t;
        let s = Np(i);
        if (!s) return t;
        s.isRemote = !0;
        let u = n.get(r, si);
        if (u) {
            let c = Array.isArray(u) ? u.join(",") : u;
            s.traceState = (0, l.createTraceState)(typeof c == "string" ? c : void 0);
        }
        return l.trace.setSpanContext(t, s);
    }
};
function Np(e) {
    let [t, r, n, o, i] = e.split("-");
    return !t || !r || !n || !o || t.length !== 2 || r.length !== 32 || n.length !== 16 || o.length !== 2 || t === "00" && i ? null : {
        traceId: r,
        spanId: n,
        traceFlags: parseInt(o, 16)
    };
}
E();
var Lr = class {
    fields() {
        return [];
    }
    inject() {}
    extract(t) {
        let r = nt();
        if (!r?.telemetry) return l.diag.warn("@vercel/otel: Vercel telemetry extension not found."), t;
        let { rootSpanContext: n } = r.telemetry;
        return n ? (l.diag.debug("@vercel/otel: Extracted root SpanContext from Vercel request context.", n), l.trace.setSpanContext(t, {
            ...n,
            isRemote: !0,
            traceFlags: n.traceFlags || l.TraceFlags.SAMPLED
        })) : t;
    }
};
E();
var Pr = v(P(), 1);
var ee = v(P()), wp = BigInt(1e9);
function Su(e) {
    return BigInt(e[0]) * wp + BigInt(e[1]);
}
function Mp(e) {
    var t = Number(BigInt.asUintN(32, e)), r = Number(BigInt.asUintN(32, e >> BigInt(32)));
    return {
        low: t,
        high: r
    };
}
function gu(e) {
    var t = Su(e);
    return Mp(t);
}
function xp(e) {
    var t = Su(e);
    return t.toString();
}
var Dp = typeof BigInt < "u" ? xp : ee.hrTimeToNanoseconds;
function Tu(e) {
    return e;
}
function yu(e) {
    if (e !== void 0) return (0, ee.hexToBase64)(e);
}
var Up = {
    encodeHrTime: gu,
    encodeSpanContext: ee.hexToBase64,
    encodeOptionalSpanContext: yu
};
function vu(e) {
    var t, r;
    if (e === void 0) return Up;
    var n = (t = e.useLongBits) !== null && t !== void 0 ? t : !0, o = (r = e.useHex) !== null && r !== void 0 ? r : !1;
    return {
        encodeHrTime: n ? gu : Dp,
        encodeSpanContext: o ? Tu : ee.hexToBase64,
        encodeOptionalSpanContext: o ? Tu : yu
    };
}
var Bp = function(e, t) {
    var r = typeof Symbol == "function" && e[Symbol.iterator];
    if (!r) return e;
    var n = r.call(e), o, i = [], s;
    try {
        for(; (t === void 0 || t-- > 0) && !(o = n.next()).done;)i.push(o.value);
    } catch (u) {
        s = {
            error: u
        };
    } finally{
        try {
            o && !o.done && (r = n.return) && r.call(n);
        } finally{
            if (s) throw s.error;
        }
    }
    return i;
};
function re(e) {
    return Object.keys(e).map(function(t) {
        return Au(t, e[t]);
    });
}
function Au(e, t) {
    return {
        key: e,
        value: Ru(t)
    };
}
function Ru(e) {
    var t = typeof e;
    return t === "string" ? {
        stringValue: e
    } : t === "number" ? Number.isInteger(e) ? {
        intValue: e
    } : {
        doubleValue: e
    } : t === "boolean" ? {
        boolValue: e
    } : e instanceof Uint8Array ? {
        bytesValue: e
    } : Array.isArray(e) ? {
        arrayValue: {
            values: e.map(Ru)
        }
    } : t === "object" && e != null ? {
        kvlistValue: {
            values: Object.entries(e).map(function(r) {
                var n = Bp(r, 2), o = n[0], i = n[1];
                return Au(o, i);
            })
        }
    } : {};
}
function Ou(e, t) {
    var r, n = e.spanContext(), o = e.status;
    return {
        traceId: t.encodeSpanContext(n.traceId),
        spanId: t.encodeSpanContext(n.spanId),
        parentSpanId: t.encodeOptionalSpanContext(e.parentSpanId),
        traceState: (r = n.traceState) === null || r === void 0 ? void 0 : r.serialize(),
        name: e.name,
        kind: e.kind == null ? 0 : e.kind + 1,
        startTimeUnixNano: t.encodeHrTime(e.startTime),
        endTimeUnixNano: t.encodeHrTime(e.endTime),
        attributes: re(e.attributes),
        droppedAttributesCount: e.droppedAttributesCount,
        events: e.events.map(function(i) {
            return Vp(i, t);
        }),
        droppedEventsCount: e.droppedEventsCount,
        status: {
            code: o.code,
            message: o.message
        },
        links: e.links.map(function(i) {
            return Gp(i, t);
        }),
        droppedLinksCount: e.droppedLinksCount
    };
}
function Gp(e, t) {
    var r;
    return {
        attributes: e.attributes ? re(e.attributes) : [],
        spanId: t.encodeSpanContext(e.context.spanId),
        traceId: t.encodeSpanContext(e.context.traceId),
        traceState: (r = e.context.traceState) === null || r === void 0 ? void 0 : r.serialize(),
        droppedAttributesCount: e.droppedAttributesCount || 0
    };
}
function Vp(e, t) {
    return {
        attributes: e.attributes ? re(e.attributes) : [],
        name: e.name,
        timeUnixNano: t.encodeHrTime(e.time),
        droppedAttributesCount: e.droppedAttributesCount || 0
    };
}
var Hp = function(e) {
    var t = typeof Symbol == "function" && Symbol.iterator, r = t && e[t], n = 0;
    if (r) return r.call(e);
    if (e && typeof e.length == "number") return {
        next: function() {
            return e && n >= e.length && (e = void 0), {
                value: e && e[n++],
                done: !e
            };
        }
    };
    throw new TypeError(t ? "Object is not iterable." : "Symbol.iterator is not defined.");
}, Fp = function(e, t) {
    var r = typeof Symbol == "function" && e[Symbol.iterator];
    if (!r) return e;
    var n = r.call(e), o, i = [], s;
    try {
        for(; (t === void 0 || t-- > 0) && !(o = n.next()).done;)i.push(o.value);
    } catch (u) {
        s = {
            error: u
        };
    } finally{
        try {
            o && !o.done && (r = n.return) && r.call(n);
        } finally{
            if (s) throw s.error;
        }
    }
    return i;
};
function ui(e, t) {
    var r = vu(t);
    return {
        resourceSpans: kp(e, r)
    };
}
function jp(e) {
    var t, r, n = new Map;
    try {
        for(var o = Hp(e), i = o.next(); !i.done; i = o.next()){
            var s = i.value, u = n.get(s.resource);
            u || (u = new Map, n.set(s.resource, u));
            var c = s.instrumentationLibrary.name + "@" + (s.instrumentationLibrary.version || "") + ":" + (s.instrumentationLibrary.schemaUrl || ""), a = u.get(c);
            a || (a = [], u.set(c, a)), a.push(s);
        }
    } catch (f) {
        t = {
            error: f
        };
    } finally{
        try {
            i && !i.done && (r = o.return) && r.call(o);
        } finally{
            if (t) throw t.error;
        }
    }
    return n;
}
function kp(e, t) {
    for(var r = jp(e), n = [], o = r.entries(), i = o.next(); !i.done;){
        for(var s = Fp(i.value, 2), u = s[0], c = s[1], a = [], f = c.values(), p = f.next(); !p.done;){
            var d = p.value;
            if (d.length > 0) {
                var m = d[0].instrumentationLibrary, g = m.name, C = m.version, M = m.schemaUrl, T = d.map(function(q) {
                    return Ou(q, t);
                });
                a.push({
                    scope: {
                        name: g,
                        version: C
                    },
                    spans: T,
                    schemaUrl: M
                });
            }
            p = f.next();
        }
        var et = {
            resource: {
                attributes: re(u.attributes),
                droppedAttributesCount: 0
            },
            scopeSpans: a,
            schemaUrl: void 0
        };
        n.push(et), i = o.next();
    }
    return n;
}
var Ir = class {
    export(t, r) {
        let n = nt();
        if (!n?.telemetry) {
            l.diag.warn("@vercel/otel: no telemetry context found"), r({
                code: Pr.ExportResultCode.SUCCESS,
                error: void 0
            });
            return;
        }
        try {
            let o = ui(t, {
                useHex: !0,
                useLongBits: !1
            });
            n.telemetry.reportSpans(o), r({
                code: Pr.ExportResultCode.SUCCESS,
                error: void 0
            });
        } catch (o) {
            r({
                code: Pr.ExportResultCode.FAILED,
                error: o instanceof Error ? o : new Error(String(o))
            });
        }
    }
    shutdown() {
        return Promise.resolve();
    }
    forceFlush() {
        return Promise.resolve();
    }
};
var qp = {
    ALL: l.DiagLogLevel.ALL,
    VERBOSE: l.DiagLogLevel.VERBOSE,
    DEBUG: l.DiagLogLevel.DEBUG,
    INFO: l.DiagLogLevel.INFO,
    WARN: l.DiagLogLevel.WARN,
    ERROR: l.DiagLogLevel.ERROR,
    NONE: l.DiagLogLevel.NONE
}, Cr = class {
    constructor(t = {}){
        this.configuration = t;
    }
    start() {
        let t = Xp(), r = this.configuration, n = ("TURBOPACK compile-time value", "edge") || "nodejs", o = !!t.OTEL_SDK_DISABLED;
        if (process.env.OTEL_LOG_LEVEL && l.diag.setLogger(new l.DiagConsoleLogger, {
            logLevel: qp[process.env.OTEL_LOG_LEVEL.toUpperCase()]
        }), o) return;
        let i = r.idGenerator ?? new tr, s = r.contextManager ?? new Iu.AsyncLocalStorageContextManager;
        s.enable(), this.contextManager = s;
        let u = t.OTEL_SERVICE_NAME || r.serviceName || "app", c = new H(Er({
            [Ka]: u,
            "node.ci": process.env.CI ? !0 : void 0,
            "node.env": "production",
            env: process.env.VERCEL_ENV || process.env.NEXT_PUBLIC_VERCEL_ENV,
            "vercel.region": process.env.VERCEL_REGION,
            "vercel.runtime": n,
            "vercel.sha": process.env.VERCEL_GIT_COMMIT_SHA || process.env.NEXT_PUBLIC_VERCEL_GIT_COMMIT_SHA,
            "vercel.host": process.env.VERCEL_URL || process.env.NEXT_PUBLIC_VERCEL_URL || void 0,
            "vercel.branch_host": process.env.VERCEL_BRANCH_URL || process.env.NEXT_PUBLIC_VERCEL_BRANCH_URL || void 0,
            "vercel.deployment_id": process.env.VERCEL_DEPLOYMENT_ID || void 0,
            [Wa]: process.env.VERCEL_DEPLOYMENT_ID,
            ...r.attributes
        })), a = r.resourceDetectors ?? [
            No
        ];
        if (r.autoDetectResources ?? !0) {
            let T = {
                detectors: a
            };
            c = c.merge(Mo(T));
        }
        let p = Wp(r.propagators, r, t), d = Yp(r.traceSampler, t), m = zp(r.spanProcessors, r, t);
        m.length === 0 && l.diag.warn("@vercel/otel: No span processors configured. No spans will be exported.");
        let g = r.spanLimits, C = new Bo({
            resource: c,
            idGenerator: i,
            sampler: d,
            spanLimits: g
        });
        if (C.addSpanProcessor(new mr(m, r.attributesFromHeaders)), C.register({
            contextManager: s,
            propagator: new Ot.CompositePropagator({
                propagators: p
            })
        }), this.tracerProvider = C, r.logRecordProcessor) {
            let T = new gn({
                resource: c
            });
            this.loggerProvider = T, T.addLogRecordProcessor(r.logRecordProcessor), st.logs.setGlobalLoggerProvider(T);
        }
        if (r.metricReader || r.views) {
            let T = new In({
                resource: c,
                views: r.views ?? []
            });
            r.metricReader && T.addMetricReader(r.metricReader), l.metrics.setGlobalMeterProvider(T), this.meterProvider = T;
        }
        let M = Kp(r.instrumentations, r.instrumentationConfig);
        this.disableInstrumentations = (0, Pu.registerInstrumentations)({
            instrumentations: M
        }), l.diag.info("@vercel/otel: started", u, n);
    }
    async shutdown() {
        let t = [];
        this.tracerProvider && t.push(this.tracerProvider.shutdown()), this.loggerProvider && t.push(this.loggerProvider.shutdown()), this.meterProvider && t.push(this.meterProvider.shutdown()), l.diag.info("@vercel/otel: shutting down", t.length, ("TURBOPACK compile-time value", "edge")), await Promise.all(t), this.contextManager && this.contextManager.disable();
        let { disableInstrumentations: r } = this;
        r && r();
    }
};
function Xp() {
    let e = (0, Nr.parseEnvironment)(process.env);
    return {
        ...Nr.DEFAULT_ENVIRONMENT,
        ...e
    };
}
function Kp(e, t) {
    return (e ?? [
        "auto"
    ]).map((r)=>r === "auto" ? (l.diag.debug("@vercel/otel: Configure instrumentations: fetch", t?.fetch), [
            new te(t?.fetch)
        ]) : r === "fetch" ? (l.diag.debug("@vercel/otel: Configure instrumentations: fetch", t?.fetch), new te(t?.fetch)) : r).flat();
}
function Wp(e, t, r) {
    let n = process.env.OTEL_PROPAGATORS && r.OTEL_PROPAGATORS && r.OTEL_PROPAGATORS.length > 0 ? r.OTEL_PROPAGATORS : void 0;
    return (e ?? n ?? [
        "auto"
    ]).map((o)=>{
        if (o === "none") return [];
        if (o === "auto") {
            let i = [];
            return i.push({
                name: "tracecontext",
                propagator: new Te
            }), i.push({
                name: "baggage",
                propagator: new Ot.W3CBaggagePropagator
            }), i.push({
                name: "vercel-runtime",
                propagator: new Lr
            }), l.diag.debug(`@vercel/otel: Configure propagators: ${i.map((s)=>s.name).join(", ")}`), i.map((s)=>s.propagator);
        }
        if (o === "tracecontext") return l.diag.debug("@vercel/otel: Configure propagator: tracecontext"), new Te;
        if (o === "baggage") return l.diag.debug("@vercel/otel: Configure propagator: baggage"), new Ot.W3CBaggagePropagator;
        if (typeof o == "string") throw new Error(`Unknown propagator: "${o}"`);
        return o;
    }).flat();
}
var bu = "always_on", ne = 1;
function Yp(e, t) {
    if (e && typeof e != "string") return e;
    let r = e && e !== "auto" ? e : t.OTEL_TRACES_SAMPLER || bu;
    switch(l.diag.debug("@vercel/otel: Configure sampler: ", r), r){
        case "always_on":
            return new X;
        case "always_off":
            return new at;
        case "parentbased_always_on":
            return new Et({
                root: new X
            });
        case "parentbased_always_off":
            return new Et({
                root: new at
            });
        case "traceidratio":
            return new kt(Lu(t));
        case "parentbased_traceidratio":
            return new Et({
                root: new kt(Lu(t))
            });
        default:
            return l.diag.error(`@vercel/otel: OTEL_TRACES_SAMPLER value "${String(t.OTEL_TRACES_SAMPLER)} invalid, defaulting to ${bu}".`), new X;
    }
}
function Lu(e) {
    if (e.OTEL_TRACES_SAMPLER_ARG === void 0 || e.OTEL_TRACES_SAMPLER_ARG === "") return l.diag.error(`@vercel/otel: OTEL_TRACES_SAMPLER_ARG is blank, defaulting to ${ne}.`), ne;
    l.diag.debug("@vercel/otel: Configure sampler probability: ", e.OTEL_TRACES_SAMPLER_ARG);
    let t = Number(e.OTEL_TRACES_SAMPLER_ARG);
    return isNaN(t) ? (l.diag.error(`@vercel/otel: OTEL_TRACES_SAMPLER_ARG=${e.OTEL_TRACES_SAMPLER_ARG} was given, but it is invalid, defaulting to ${ne}.`), ne) : t < 0 || t > 1 ? (l.diag.error(`@vercel/otel: OTEL_TRACES_SAMPLER_ARG=${e.OTEL_TRACES_SAMPLER_ARG} was given, but it is out of range ([0..1]), defaulting to ${ne}.`), ne) : t;
}
function zp(e, t, r) {
    return [
        ...(e ?? [
            "auto"
        ]).flatMap((n)=>{
            if (n === "auto") {
                let o = [
                    new Mt(new Ir)
                ];
                if (process.env.VERCEL_OTEL_ENDPOINTS) {
                    let i = process.env.VERCEL_OTEL_ENDPOINTS_PORT || "4318", s = process.env.VERCEL_OTEL_ENDPOINTS_PROTOCOL || "http/protobuf";
                    l.diag.debug("@vercel/otel: Configure vercel otel collector on port: ", i, s);
                    let u = {
                        url: `http://localhost:${i}/v1/traces`,
                        headers: {}
                    }, c = s === "http/protobuf" ? new Bt(u) : new Qt(u);
                    o.push(new Mt(c));
                } else (!t.traceExporter || t.traceExporter === "auto" || r.OTEL_EXPORTER_OTLP_TRACES_ENDPOINT || r.OTEL_EXPORTER_OTLP_ENDPOINT) && o.push(new Mt($p(r)));
                return o;
            }
            return n;
        }).filter(Jp),
        ...t.traceExporter && t.traceExporter !== "auto" ? [
            new Mt(t.traceExporter)
        ] : []
    ];
}
function $p(e) {
    let t = process.env.OTEL_EXPORTER_OTLP_TRACES_PROTOCOL ?? process.env.OTEL_EXPORTER_OTLP_PROTOCOL ?? "http/protobuf", r = Zp(e), n = {
        ...Ot.baggageUtils.parseKeyPairsIntoRecord(e.OTEL_EXPORTER_OTLP_HEADERS),
        ...Ot.baggageUtils.parseKeyPairsIntoRecord(e.OTEL_EXPORTER_OTLP_TRACES_HEADERS)
    };
    switch(l.diag.debug("@vercel/otel: Configure trace exporter: ", t, r, `headers: ${Object.keys(n).join(",") || "<none>"}`), t){
        case "http/json":
            return new Qt({
                url: r,
                headers: n
            });
        case "http/protobuf":
            return new Bt({
                url: r,
                headers: n
            });
        default:
            return l.diag.warn(`@vercel/otel: Unsupported OTLP traces protocol: ${t}. Using http/protobuf.`), new Bt;
    }
}
var Cu = "v1/traces", Qp = `http://localhost:4318/${Cu}`;
function Zp(e) {
    let t = e.OTEL_EXPORTER_OTLP_TRACES_ENDPOINT;
    if (t && typeof t == "string") return t;
    let r = e.OTEL_EXPORTER_OTLP_ENDPOINT;
    return r && typeof r == "string" ? `${r}/${Cu}` : Qp;
}
function Jp(e) {
    return e != null;
}
function RT(e) {
    let t;
    e ? typeof e == "string" ? t = {
        serviceName: e
    } : t = e : t = {}, new Cr(t).start();
}
;
 //# sourceMappingURL=index.js.map
}}),
}]);

//# sourceMappingURL=node_modules_28cb8b9c._.js.map