"use strict";exports.id=8108,exports.ids=[8108],exports.modules={28108:(e,l,s)=>{s.r(l),s.d(l,{KeylessCreatorOrReader:()=>c});var t=s(2984),r=s(60159),i=s.n(r),n=s(70358);let a=(0,n.createServerReference)("7f4c08c553bf967cd0b7c8f5250f46c2e73879b9bd",n.callServer,void 0,n.findSourceMapURL,"createOrReadKeylessAction"),c=e=>{var l;let{children:s}=e,n=(null==(l=(0,t.useSelectedLayoutSegments)()[0])?void 0:l.startsWith("/_not-found"))||!1,[c,o]=i().useActionState(a,null);return((0,r.useEffect)(()=>{n||i().startTransition(()=>{o()})},[n]),i().isValidElement(s))?i().cloneElement(s,{key:null==c?void 0:c.publishableKey,publishableKey:null==c?void 0:c.publishableKey,__internal_keyless_claimKeylessApplicationUrl:null==c?void 0:c.claimUrl,__internal_keyless_copyInstanceKeysUrl:null==c?void 0:c.apiKeysUrl,__internal_bypassMissingPublishableKey:!0}):s}}};