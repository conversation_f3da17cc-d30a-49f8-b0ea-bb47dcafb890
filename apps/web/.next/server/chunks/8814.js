"use strict";exports.id=8814,exports.ids=[8814],exports.modules={9852:(e,t,r)=>{r.d(t,{A:()=>a});function a(e){return{id:"credentials",name:"Credentials",type:"credentials",credentials:{},authorize:()=>null,options:e}}},11868:(e,t,r)=>{r.d(t,{CM:()=>c,Hj:()=>P,Lx:()=>T,OZ:()=>y,Oy:()=>o,P8:()=>E,PM:()=>l,QU:()=>v,SW:()=>h,WS:()=>W,XP:()=>w,_2:()=>I,_z:()=>_,dy:()=>R,eH:()=>S,gs:()=>f,i8:()=>b,jo:()=>m,k9:()=>p,lR:()=>a,me:()=>d,nd:()=>H,o6:()=>C,om:()=>i,rk:()=>g,s5:()=>$,t3:()=>s,tP:()=>U,u$:()=>x,w2:()=>j,xm:()=>D,xz:()=>u});class a extends Error{constructor(e,t){e instanceof Error?super(void 0,{cause:{err:e,...e.cause,...t}}):"string"==typeof e?(t instanceof Error&&(t={err:t,...t.cause}),super(e,t)):super(void 0,e),this.name=this.constructor.name,this.type=this.constructor.type??"AuthError",this.kind=this.constructor.kind??"error",Error.captureStackTrace?.(this,this.constructor);let r=`https://errors.authjs.dev#${this.type.toLowerCase()}`;this.message+=`${this.message?". ":""}Read more at ${r}`}}class n extends a{}n.kind="signIn";class i extends a{}i.type="AdapterError";class o extends a{}o.type="AccessDenied";class s extends a{}s.type="CallbackRouteError";class c extends a{}c.type="ErrorPageLoop";class l extends a{}l.type="EventError";class d extends a{}d.type="InvalidCallbackUrl";class u extends n{constructor(){super(...arguments),this.code="credentials"}}u.type="CredentialsSignin";class f extends a{}f.type="InvalidEndpoints";class p extends a{}p.type="InvalidCheck";class h extends a{}h.type="JWTSessionError";class y extends a{}y.type="MissingAdapter";class b extends a{}b.type="MissingAdapterMethods";class x extends a{}x.type="MissingAuthorize";class m extends a{}m.type="MissingSecret";class w extends n{}w.type="OAuthAccountNotLinked";class g extends n{}g.type="OAuthCallbackError";class _ extends a{}_.type="OAuthProfileParseError";class v extends a{}v.type="SessionTokenError";class k extends n{}k.type="OAuthSignInError";class A extends n{}A.type="EmailSignInError";class S extends a{}S.type="SignOutError";class E extends a{}E.type="UnknownAction";class T extends a{}T.type="UnsupportedStrategy";class P extends a{}P.type="InvalidProvider";class U extends a{}U.type="UntrustedHost";class C extends a{}C.type="Verification";class R extends n{}R.type="MissingCSRF";let O=new Set(["CredentialsSignin","OAuthAccountNotLinked","OAuthCallbackError","AccessDenied","Verification","MissingCSRF","AccountNotLinked","WebAuthnVerificationError"]);function I(e){return e instanceof a&&O.has(e.type)}class $ extends a{}$.type="DuplicateConditionalUI";class H extends a{}H.type="MissingWebAuthnAutocomplete";class j extends a{}j.type="WebAuthnVerificationError";class W extends n{}W.type="AccountNotLinked";class D extends a{}D.type="ExperimentalFeatureNotEnabled"},24223:(e,t,r)=>{r.d(t,{X:()=>u,c:()=>f});var a,n,i,o,s,c,l=function(e,t,r,a,n){if("m"===a)throw TypeError("Private method is not writable");if("a"===a&&!n)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===a?n.call(e,r):n?n.value=r:t.set(e,r),r},d=function(e,t,r,a){if("a"===r&&!a)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!a:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?a:"a"===r?a.call(e):a?a.value:t.get(e)};function u(e){let t=e?"__Secure-":"";return{sessionToken:{name:`${t}authjs.session-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},callbackUrl:{name:`${t}authjs.callback-url`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},csrfToken:{name:`${e?"__Host-":""}authjs.csrf-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},pkceCodeVerifier:{name:`${t}authjs.pkce.code_verifier`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}},state:{name:`${t}authjs.state`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}},nonce:{name:`${t}authjs.nonce`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},webauthnChallenge:{name:`${t}authjs.challenge`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}}}}class f{constructor(e,t,r){if(a.add(this),n.set(this,{}),i.set(this,void 0),o.set(this,void 0),l(this,o,r,"f"),l(this,i,e,"f"),!t)return;let{name:s}=e;for(let[e,r]of Object.entries(t))e.startsWith(s)&&r&&(d(this,n,"f")[e]=r)}get value(){return Object.keys(d(this,n,"f")).sort((e,t)=>parseInt(e.split(".").pop()||"0")-parseInt(t.split(".").pop()||"0")).map(e=>d(this,n,"f")[e]).join("")}chunk(e,t){let r=d(this,a,"m",c).call(this);for(let n of d(this,a,"m",s).call(this,{name:d(this,i,"f").name,value:e,options:{...d(this,i,"f").options,...t}}))r[n.name]=n;return Object.values(r)}clean(){return Object.values(d(this,a,"m",c).call(this))}}n=new WeakMap,i=new WeakMap,o=new WeakMap,a=new WeakSet,s=function(e){let t=Math.ceil(e.value.length/3936);if(1===t)return d(this,n,"f")[e.name]=e.value,[e];let r=[];for(let a=0;a<t;a++){let t=`${e.name}.${a}`,i=e.value.substr(3936*a,3936);r.push({...e,name:t,value:i}),d(this,n,"f")[t]=i}return d(this,o,"f").debug("CHUNKING_SESSION_COOKIE",{message:"Session cookie exceeds allowed 4096 bytes.",emptyCookieSize:160,valueSize:e.value.length,chunks:r.map(e=>e.value.length+160)}),r},c=function(){let e={};for(let t in d(this,n,"f"))delete d(this,n,"f")?.[t],e[t]={name:t,value:"",options:{...d(this,i,"f").options,maxAge:0}};return e}},30005:(e,t,r)=>{r.d(t,{D4:()=>o,lF:()=>i});var a=r(4573),n=r(36448);let i=e=>a.Buffer.from(e).toString("base64url"),o=e=>new Uint8Array(a.Buffer.from(function(e){let t=e;return t instanceof Uint8Array&&(t=n.D0.decode(t)),t}(e),"base64url"))},36448:(e,t,r)=>{r.d(t,{D0:()=>i,Kp:()=>u,MT:()=>s,Rd:()=>n,VS:()=>d,mx:()=>l,xW:()=>o,yI:()=>f});var a=r(90337);let n=new TextEncoder,i=new TextDecoder;function o(...e){let t=new Uint8Array(e.reduce((e,{length:t})=>e+t,0)),r=0;for(let a of e)t.set(a,r),r+=a.length;return t}function s(e,t){return o(n.encode(e),new Uint8Array([0]),t)}function c(e,t,r){if(t<0||t>=0x100000000)throw RangeError(`value must be >= 0 and <= ${0x100000000-1}. Received ${t}`);e.set([t>>>24,t>>>16,t>>>8,255&t],r)}function l(e){let t=Math.floor(e/0x100000000),r=new Uint8Array(8);return c(r,t,0),c(r,e%0x100000000,4),r}function d(e){let t=new Uint8Array(4);return c(t,e),t}function u(e){return o(d(e.length),e)}async function f(e,t,r){let n=Math.ceil((t>>3)/32),i=new Uint8Array(32*n);for(let t=0;t<n;t++){let n=new Uint8Array(4+e.length+r.length);n.set(d(t+1)),n.set(e,4),n.set(r,4+e.length),i.set(await (0,a.A)("sha256",n),32*t)}return i.slice(0,t>>3)}},50109:(e,t,r)=>{let a,n,i,o,s;r.d(t,{Ay:()=>aj});var c=r(24223),l=r(11868);let d=!1;function u(e,t){try{return/^https?:/.test(new URL(e,e.startsWith("/")?t:void 0).protocol)}catch{return!1}}let f=!1,p=!1,h=!1,y=["createVerificationToken","useVerificationToken","getUserByEmail"],b=["createUser","getUser","getUserByEmail","getUserByAccount","updateUser","linkAccount","createSession","getSessionAndUser","updateSession","deleteSession"],x=["createUser","getUser","linkAccount","getAccount","getAuthenticator","createAuthenticator","listAuthenticatorsByUserId","updateAuthenticatorCounter"];var m=r(63418);async function w({options:e,paramValue:t,cookieValue:r}){let{url:a,callbacks:n}=e,i=a.origin;return t?i=await n.redirect({url:t,baseUrl:a.origin}):r&&(i=await n.redirect({url:r,baseUrl:a.origin})),{callbackUrl:i,callbackUrlCookie:i!==r?i:void 0}}var g=r(58319);let _="\x1b[31m",v="\x1b[0m",k={error(e){let t=e instanceof l.lR?e.type:e.name;if(console.error(`${_}[auth][error]${v} ${t}: ${e.message}`),e.cause&&"object"==typeof e.cause&&"err"in e.cause&&e.cause.err instanceof Error){let{err:t,...r}=e.cause;console.error(`${_}[auth][cause]${v}:`,t.stack),r&&console.error(`${_}[auth][details]${v}:`,JSON.stringify(r,null,2))}else e.stack&&console.error(e.stack.replace(/.*/,"").substring(1))},warn(e){let t=`https://warnings.authjs.dev#${e}`;console.warn(`\x1b[33m[auth][warn][${e}]${v}`,`Read more: ${t}`)},debug(e,t){console.log(`\x1b[90m[auth][debug]:${v} ${e}`,JSON.stringify(t,null,2))}};function A(e){let t={...k};return e.debug||(t.debug=()=>{}),e.logger?.error&&(t.error=e.logger.error),e.logger?.warn&&(t.warn=e.logger.warn),e.logger?.debug&&(t.debug=e.logger.debug),e.logger??(e.logger=t),t}let S=["providers","session","csrf","signin","signout","callback","verify-request","error","webauthn-options"];async function E(e){if(!("body"in e)||!e.body||"POST"!==e.method)return;let t=e.headers.get("content-type");return t?.includes("application/json")?await e.json():t?.includes("application/x-www-form-urlencoded")?Object.fromEntries(new URLSearchParams(await e.text())):void 0}async function T(e,t){try{if("GET"!==e.method&&"POST"!==e.method)throw new l.P8("Only GET and POST requests are supported");t.basePath??(t.basePath="/auth");let r=new URL(e.url),{action:a,providerId:n}=function(e,t){let r=e.match(RegExp(`^${t}(.+)`));if(null===r)throw new l.P8(`Cannot parse action at ${e}`);let a=r.at(-1).replace(/^\//,"").split("/").filter(Boolean);if(1!==a.length&&2!==a.length)throw new l.P8(`Cannot parse action at ${e}`);let[n,i]=a;if(!S.includes(n)||i&&!["signin","callback","webauthn-options"].includes(n))throw new l.P8(`Cannot parse action at ${e}`);return{action:n,providerId:i}}(r.pathname,t.basePath);return{url:r,action:a,providerId:n,method:e.method,headers:Object.fromEntries(e.headers),body:e.body?await E(e):void 0,cookies:(0,g.q)(e.headers.get("cookie")??"")??{},error:r.searchParams.get("error")??void 0,query:Object.fromEntries(r.searchParams)}}catch(a){let r=A(t);r.error(a),r.debug("request",e)}}function P(e){let t=new Headers(e.headers);e.cookies?.forEach(e=>{let{name:r,value:a,options:n}=e,i=(0,g.l)(r,a,n);t.has("Set-Cookie")?t.append("Set-Cookie",i):t.set("Set-Cookie",i)});let r=e.body;"application/json"===t.get("content-type")?r=JSON.stringify(e.body):"application/x-www-form-urlencoded"===t.get("content-type")&&(r=new URLSearchParams(e.body).toString());let a=new Response(r,{headers:t,status:e.redirect?302:e.status??200});return e.redirect&&a.headers.set("Location",e.redirect),a}async function U(e){let t=new TextEncoder().encode(e);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",t))).map(e=>e.toString(16).padStart(2,"0")).join("").toString()}function C(e){let t=e=>("0"+e.toString(16)).slice(-2);return Array.from(crypto.getRandomValues(new Uint8Array(e))).reduce((e,r)=>e+t(r),"")}async function R({options:e,cookieValue:t,isPost:r,bodyValue:a}){if(t){let[n,i]=t.split("|");if(i===await U(`${n}${e.secret}`))return{csrfTokenVerified:r&&n===a,csrfToken:n}}let n=C(32),i=await U(`${n}${e.secret}`);return{cookie:`${n}|${i}`,csrfToken:n}}function O(e,t){if(!t)throw new l.dy(`CSRF token was missing during an action ${e}`)}function I(e){return null!==e&&"object"==typeof e}function $(e,...t){if(!t.length)return e;let r=t.shift();if(I(e)&&I(r))for(let t in r)I(r[t])?(I(e[t])||(e[t]=Array.isArray(r[t])?[]:{}),$(e[t],r[t])):void 0!==r[t]&&(e[t]=r[t]);return $(e,...t)}let H=Symbol("skip-csrf-check"),j=Symbol("return-type-raw"),W=Symbol("custom-fetch"),D=Symbol("conform-internal"),L=e=>J({id:e.sub??e.id??crypto.randomUUID(),name:e.name??e.nickname??e.preferred_username,email:e.email,image:e.picture}),K=e=>J({access_token:e.access_token,id_token:e.id_token,refresh_token:e.refresh_token,expires_at:e.expires_at,scope:e.scope,token_type:e.token_type,session_state:e.session_state});function J(e){let t={};for(let[r,a]of Object.entries(e))void 0!==a&&(t[r]=a);return t}function N(e,t){if(!e&&t)return;if("string"==typeof e)return{url:new URL(e)};let r=new URL(e?.url??"https://authjs.dev");if(e?.params!=null)for(let[t,a]of Object.entries(e.params))"claims"===t&&(a=JSON.stringify(a)),r.searchParams.set(t,String(a));return{url:r,request:e?.request,conform:e?.conform,...e?.clientPrivateKey?{clientPrivateKey:e?.clientPrivateKey}:null}}let M={signIn:()=>!0,redirect:({url:e,baseUrl:t})=>e.startsWith("/")?`${t}${e}`:new URL(e).origin===t?e:t,session:({session:e})=>({user:{name:e.user?.name,email:e.user?.email,image:e.user?.image},expires:e.expires?.toISOString?.()??e.expires}),jwt:({token:e})=>e};async function F({authOptions:e,providerId:t,action:r,url:a,cookies:n,callbackUrl:i,csrfToken:o,csrfDisabled:s,isPost:d}){var u,f;let p=A(e),{providers:h,provider:y}=function(e){let{providerId:t,config:r}=e,a=new URL(r.basePath??"/auth",e.url.origin),n=r.providers.map(e=>{let t="function"==typeof e?e():e,{options:n,...i}=t,o=n?.id??i.id,s=$(i,n,{signinUrl:`${a}/signin/${o}`,callbackUrl:`${a}/callback/${o}`});if("oauth"===t.type||"oidc"===t.type){s.redirectProxyUrl??(s.redirectProxyUrl=n?.redirectProxyUrl??r.redirectProxyUrl);let e=function(e){e.issuer&&(e.wellKnown??(e.wellKnown=`${e.issuer}/.well-known/openid-configuration`));let t=N(e.authorization,e.issuer);t&&!t.url?.searchParams.has("scope")&&t.url.searchParams.set("scope","openid profile email");let r=N(e.token,e.issuer),a=N(e.userinfo,e.issuer),n=e.checks??["pkce"];return e.redirectProxyUrl&&(n.includes("state")||n.push("state"),e.redirectProxyUrl=`${e.redirectProxyUrl}/callback/${e.id}`),{...e,authorization:t,token:r,checks:n,userinfo:a,profile:e.profile??L,account:e.account??K}}(s);return e.authorization?.url.searchParams.get("response_mode")==="form_post"&&delete e.redirectProxyUrl,e[W]??(e[W]=n?.[W]),e}return s});return{providers:n,provider:n.find(({id:e})=>e===t)}}({url:a,providerId:t,config:e}),b=!1;if((y?.type==="oauth"||y?.type==="oidc")&&y.redirectProxyUrl)try{b=new URL(y.redirectProxyUrl).origin===a.origin}catch{throw TypeError(`redirectProxyUrl must be a valid URL. Received: ${y.redirectProxyUrl}`)}let x={debug:!1,pages:{},theme:{colorScheme:"auto",logo:"",brandColor:"",buttonText:""},...e,url:a,action:r,provider:y,cookies:$(c.X(e.useSecureCookies??"https:"===a.protocol),e.cookies),providers:h,session:{strategy:e.adapter?"database":"jwt",maxAge:2592e3,updateAge:86400,generateSessionToken:()=>crypto.randomUUID(),...e.session},jwt:{secret:e.secret,maxAge:e.session?.maxAge??2592e3,encode:m.lF,decode:m.D4,...e.jwt},events:(u=e.events??{},f=p,Object.keys(u).reduce((e,t)=>(e[t]=async(...e)=>{try{let r=u[t];return await r(...e)}catch(e){f.error(new l.PM(e))}},e),{})),adapter:function(e,t){if(e)return Object.keys(e).reduce((r,a)=>(r[a]=async(...r)=>{try{t.debug(`adapter_${a}`,{args:r});let n=e[a];return await n(...r)}catch(r){let e=new l.om(r);throw t.error(e),e}},r),{})}(e.adapter,p),callbacks:{...M,...e.callbacks},logger:p,callbackUrl:a.origin,isOnRedirectProxy:b,experimental:{...e.experimental}},g=[];if(s)x.csrfTokenVerified=!0;else{let{csrfToken:e,cookie:t,csrfTokenVerified:r}=await R({options:x,cookieValue:n?.[x.cookies.csrfToken.name],isPost:d,bodyValue:o});x.csrfToken=e,x.csrfTokenVerified=r,t&&g.push({name:x.cookies.csrfToken.name,value:t,options:x.cookies.csrfToken.options})}let{callbackUrl:_,callbackUrlCookie:v}=await w({options:x,cookieValue:n?.[x.cookies.callbackUrl.name],paramValue:i});return x.callbackUrl=_,v&&g.push({name:x.cookies.callbackUrl.name,value:v,options:x.cookies.callbackUrl.options}),{options:x,cookies:g}}var z,B,V,q,G,X={},Y=[],Z=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;function Q(e,t){for(var r in t)e[r]=t[r];return e}function ee(e){var t=e.parentNode;t&&t.removeChild(e)}function et(e,t,r,a,n){var i={type:e,props:t,key:r,ref:a,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:null==n?++V:n};return null==n&&null!=B.vnode&&B.vnode(i),i}function er(e){return e.children}function ea(e,t){this.props=e,this.context=t}function en(e,t){if(null==t)return e.__?en(e.__,e.__.__k.indexOf(e)+1):null;for(var r;t<e.__k.length;t++)if(null!=(r=e.__k[t])&&null!=r.__e)return r.__e;return"function"==typeof e.type?en(e):null}function ei(e){(!e.__d&&(e.__d=!0)&&q.push(e)&&!eo.__r++||G!==B.debounceRendering)&&((G=B.debounceRendering)||setTimeout)(eo)}function eo(){for(var e;eo.__r=q.length;)e=q.sort(function(e,t){return e.__v.__b-t.__v.__b}),q=[],e.some(function(e){var t,r,a,n,i;e.__d&&(n=(a=e.__v).__e,(i=e.__P)&&(t=[],(r=Q({},a)).__v=a.__v+1,ep(i,a,r,e.__n,void 0!==i.ownerSVGElement,null!=a.__h?[n]:null,t,null==n?en(a):n,a.__h),eh(t,a),a.__e!=n&&function e(t){var r,a;if(null!=(t=t.__)&&null!=t.__c){for(t.__e=t.__c.base=null,r=0;r<t.__k.length;r++)if(null!=(a=t.__k[r])&&null!=a.__e){t.__e=t.__c.base=a.__e;break}return e(t)}}(a)))})}function es(e,t,r,a,n,i,o,s,c,l){var d,u,f,p,h,y,b,x=a&&a.__k||Y,m=x.length;for(r.__k=[],d=0;d<t.length;d++)if(null!=(p=r.__k[d]=null==(p=t[d])||"boolean"==typeof p?null:"string"==typeof p||"number"==typeof p||"bigint"==typeof p?et(null,p,null,null,p):Array.isArray(p)?et(er,{children:p},null,null,null):p.__b>0?et(p.type,p.props,p.key,p.ref?p.ref:null,p.__v):p)){if(p.__=r,p.__b=r.__b+1,null===(f=x[d])||f&&p.key==f.key&&p.type===f.type)x[d]=void 0;else for(u=0;u<m;u++){if((f=x[u])&&p.key==f.key&&p.type===f.type){x[u]=void 0;break}f=null}ep(e,p,f=f||X,n,i,o,s,c,l),h=p.__e,(u=p.ref)&&f.ref!=u&&(b||(b=[]),f.ref&&b.push(f.ref,null,p),b.push(u,p.__c||h,p)),null!=h?(null==y&&(y=h),"function"==typeof p.type&&p.__k===f.__k?p.__d=c=function e(t,r,a){for(var n,i=t.__k,o=0;i&&o<i.length;o++)(n=i[o])&&(n.__=t,r="function"==typeof n.type?e(n,r,a):ec(a,n,n,i,n.__e,r));return r}(p,c,e):c=ec(e,p,f,x,h,c),"function"==typeof r.type&&(r.__d=c)):c&&f.__e==c&&c.parentNode!=e&&(c=en(f))}for(r.__e=y,d=m;d--;)null!=x[d]&&function e(t,r,a){var n,i;if(B.unmount&&B.unmount(t),(n=t.ref)&&(n.current&&n.current!==t.__e||ey(n,null,r)),null!=(n=t.__c)){if(n.componentWillUnmount)try{n.componentWillUnmount()}catch(e){B.__e(e,r)}n.base=n.__P=null,t.__c=void 0}if(n=t.__k)for(i=0;i<n.length;i++)n[i]&&e(n[i],r,a||"function"!=typeof t.type);a||null==t.__e||ee(t.__e),t.__=t.__e=t.__d=void 0}(x[d],x[d]);if(b)for(d=0;d<b.length;d++)ey(b[d],b[++d],b[++d])}function ec(e,t,r,a,n,i){var o,s,c;if(void 0!==t.__d)o=t.__d,t.__d=void 0;else if(null==r||n!=i||null==n.parentNode)e:if(null==i||i.parentNode!==e)e.appendChild(n),o=null;else{for(s=i,c=0;(s=s.nextSibling)&&c<a.length;c+=1)if(s==n)break e;e.insertBefore(n,i),o=i}return void 0!==o?o:n.nextSibling}function el(e,t,r){"-"===t[0]?e.setProperty(t,r):e[t]=null==r?"":"number"!=typeof r||Z.test(t)?r:r+"px"}function ed(e,t,r,a,n){var i;e:if("style"===t)if("string"==typeof r)e.style.cssText=r;else{if("string"==typeof a&&(e.style.cssText=a=""),a)for(t in a)r&&t in r||el(e.style,t,"");if(r)for(t in r)a&&r[t]===a[t]||el(e.style,t,r[t])}else if("o"===t[0]&&"n"===t[1])i=t!==(t=t.replace(/Capture$/,"")),t=t.toLowerCase()in e?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+i]=r,r?a||e.addEventListener(t,i?ef:eu,i):e.removeEventListener(t,i?ef:eu,i);else if("dangerouslySetInnerHTML"!==t){if(n)t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("href"!==t&&"list"!==t&&"form"!==t&&"tabIndex"!==t&&"download"!==t&&t in e)try{e[t]=null==r?"":r;break e}catch(e){}"function"==typeof r||(null==r||!1===r&&-1==t.indexOf("-")?e.removeAttribute(t):e.setAttribute(t,r))}}function eu(e){this.l[e.type+!1](B.event?B.event(e):e)}function ef(e){this.l[e.type+!0](B.event?B.event(e):e)}function ep(e,t,r,a,n,i,o,s,c){var l,d,u,f,p,h,y,b,x,m,w,g,_,v,k,A=t.type;if(void 0!==t.constructor)return null;null!=r.__h&&(c=r.__h,s=t.__e=r.__e,t.__h=null,i=[s]),(l=B.__b)&&l(t);try{e:if("function"==typeof A){if(b=t.props,x=(l=A.contextType)&&a[l.__c],m=l?x?x.props.value:l.__:a,r.__c?y=(d=t.__c=r.__c).__=d.__E:("prototype"in A&&A.prototype.render?t.__c=d=new A(b,m):(t.__c=d=new ea(b,m),d.constructor=A,d.render=eb),x&&x.sub(d),d.props=b,d.state||(d.state={}),d.context=m,d.__n=a,u=d.__d=!0,d.__h=[],d._sb=[]),null==d.__s&&(d.__s=d.state),null!=A.getDerivedStateFromProps&&(d.__s==d.state&&(d.__s=Q({},d.__s)),Q(d.__s,A.getDerivedStateFromProps(b,d.__s))),f=d.props,p=d.state,u)null==A.getDerivedStateFromProps&&null!=d.componentWillMount&&d.componentWillMount(),null!=d.componentDidMount&&d.__h.push(d.componentDidMount);else{if(null==A.getDerivedStateFromProps&&b!==f&&null!=d.componentWillReceiveProps&&d.componentWillReceiveProps(b,m),!d.__e&&null!=d.shouldComponentUpdate&&!1===d.shouldComponentUpdate(b,d.__s,m)||t.__v===r.__v){for(d.props=b,d.state=d.__s,t.__v!==r.__v&&(d.__d=!1),d.__v=t,t.__e=r.__e,t.__k=r.__k,t.__k.forEach(function(e){e&&(e.__=t)}),w=0;w<d._sb.length;w++)d.__h.push(d._sb[w]);d._sb=[],d.__h.length&&o.push(d);break e}null!=d.componentWillUpdate&&d.componentWillUpdate(b,d.__s,m),null!=d.componentDidUpdate&&d.__h.push(function(){d.componentDidUpdate(f,p,h)})}if(d.context=m,d.props=b,d.__v=t,d.__P=e,g=B.__r,_=0,"prototype"in A&&A.prototype.render){for(d.state=d.__s,d.__d=!1,g&&g(t),l=d.render(d.props,d.state,d.context),v=0;v<d._sb.length;v++)d.__h.push(d._sb[v]);d._sb=[]}else do d.__d=!1,g&&g(t),l=d.render(d.props,d.state,d.context),d.state=d.__s;while(d.__d&&++_<25);d.state=d.__s,null!=d.getChildContext&&(a=Q(Q({},a),d.getChildContext())),u||null==d.getSnapshotBeforeUpdate||(h=d.getSnapshotBeforeUpdate(f,p)),k=null!=l&&l.type===er&&null==l.key?l.props.children:l,es(e,Array.isArray(k)?k:[k],t,r,a,n,i,o,s,c),d.base=t.__e,t.__h=null,d.__h.length&&o.push(d),y&&(d.__E=d.__=null),d.__e=!1}else null==i&&t.__v===r.__v?(t.__k=r.__k,t.__e=r.__e):t.__e=function(e,t,r,a,n,i,o,s){var c,l,d,u=r.props,f=t.props,p=t.type,h=0;if("svg"===p&&(n=!0),null!=i){for(;h<i.length;h++)if((c=i[h])&&"setAttribute"in c==!!p&&(p?c.localName===p:3===c.nodeType)){e=c,i[h]=null;break}}if(null==e){if(null===p)return document.createTextNode(f);e=n?document.createElementNS("http://www.w3.org/2000/svg",p):document.createElement(p,f.is&&f),i=null,s=!1}if(null===p)u===f||s&&e.data===f||(e.data=f);else{if(i=i&&z.call(e.childNodes),l=(u=r.props||X).dangerouslySetInnerHTML,d=f.dangerouslySetInnerHTML,!s){if(null!=i)for(u={},h=0;h<e.attributes.length;h++)u[e.attributes[h].name]=e.attributes[h].value;(d||l)&&(d&&(l&&d.__html==l.__html||d.__html===e.innerHTML)||(e.innerHTML=d&&d.__html||""))}if(function(e,t,r,a,n){var i;for(i in r)"children"===i||"key"===i||i in t||ed(e,i,null,r[i],a);for(i in t)n&&"function"!=typeof t[i]||"children"===i||"key"===i||"value"===i||"checked"===i||r[i]===t[i]||ed(e,i,t[i],r[i],a)}(e,f,u,n,s),d)t.__k=[];else if(es(e,Array.isArray(h=t.props.children)?h:[h],t,r,a,n&&"foreignObject"!==p,i,o,i?i[0]:r.__k&&en(r,0),s),null!=i)for(h=i.length;h--;)null!=i[h]&&ee(i[h]);s||("value"in f&&void 0!==(h=f.value)&&(h!==e.value||"progress"===p&&!h||"option"===p&&h!==u.value)&&ed(e,"value",h,u.value,!1),"checked"in f&&void 0!==(h=f.checked)&&h!==e.checked&&ed(e,"checked",h,u.checked,!1))}return e}(r.__e,t,r,a,n,i,o,c);(l=B.diffed)&&l(t)}catch(e){t.__v=null,(c||null!=i)&&(t.__e=s,t.__h=!!c,i[i.indexOf(s)]=null),B.__e(e,t,r)}}function eh(e,t){B.__c&&B.__c(t,e),e.some(function(t){try{e=t.__h,t.__h=[],e.some(function(e){e.call(t)})}catch(e){B.__e(e,t.__v)}})}function ey(e,t,r){try{"function"==typeof e?e(t):e.current=t}catch(e){B.__e(e,r)}}function eb(e,t,r){return this.constructor(e,r)}function ex(e,t){var r,a,n,i;r=e,B.__&&B.__(r,t),n=(a="function"==typeof ex)?null:ex&&ex.__k||t.__k,i=[],ep(t,r=(!a&&ex||t).__k=function(e,t,r){var a,n,i,o={};for(i in t)"key"==i?a=t[i]:"ref"==i?n=t[i]:o[i]=t[i];if(arguments.length>2&&(o.children=arguments.length>3?z.call(arguments,2):r),"function"==typeof e&&null!=e.defaultProps)for(i in e.defaultProps)void 0===o[i]&&(o[i]=e.defaultProps[i]);return et(e,o,a,n,null)}(er,null,[r]),n||X,X,void 0!==t.ownerSVGElement,!a&&ex?[ex]:n?null:t.firstChild?z.call(t.childNodes):null,i,!a&&ex?ex:n?n.__e:t.firstChild,a),eh(i,r)}z=Y.slice,B={__e:function(e,t,r,a){for(var n,i,o;t=t.__;)if((n=t.__c)&&!n.__)try{if((i=n.constructor)&&null!=i.getDerivedStateFromError&&(n.setState(i.getDerivedStateFromError(e)),o=n.__d),null!=n.componentDidCatch&&(n.componentDidCatch(e,a||{}),o=n.__d),o)return n.__E=n}catch(t){e=t}throw e}},V=0,ea.prototype.setState=function(e,t){var r;r=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=Q({},this.state),"function"==typeof e&&(e=e(Q({},r),this.props)),e&&Q(r,e),null!=e&&this.__v&&(t&&this._sb.push(t),ei(this))},ea.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),ei(this))},ea.prototype.render=er,q=[],eo.__r=0;var em=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|^--/i,ew=/^(area|base|br|col|embed|hr|img|input|link|meta|param|source|track|wbr)$/,eg=/[\s\n\\/='"\0<>]/,e_=/^xlink:?./,ev=/["&<]/;function ek(e){if(!1===ev.test(e+=""))return e;for(var t=0,r=0,a="",n="";r<e.length;r++){switch(e.charCodeAt(r)){case 34:n="&quot;";break;case 38:n="&amp;";break;case 60:n="&lt;";break;default:continue}r!==t&&(a+=e.slice(t,r)),a+=n,t=r+1}return r!==t&&(a+=e.slice(t,r)),a}var eA=function(e,t){return String(e).replace(/(\n+)/g,"$1"+(t||"	"))},eS=function(e,t,r){return String(e).length>(t||40)||!r&&-1!==String(e).indexOf("\n")||-1!==String(e).indexOf("<")},eE={},eT=/([A-Z])/g;function eP(e){var t="";for(var r in e){var a=e[r];null!=a&&""!==a&&(t&&(t+=" "),t+="-"==r[0]?r:eE[r]||(eE[r]=r.replace(eT,"-$1").toLowerCase()),t="number"==typeof a&&!1===em.test(r)?t+": "+a+"px;":t+": "+a+";")}return t||void 0}function eU(e,t){return Array.isArray(t)?t.reduce(eU,e):null!=t&&!1!==t&&e.push(t),e}function eC(){this.__d=!0}function eR(e,t){return{__v:e,context:t,props:e.props,setState:eC,forceUpdate:eC,__d:!0,__h:[]}}function eO(e,t){var r=e.contextType,a=r&&t[r.__c];return null!=r?a?a.props.value:r.__:t}var eI=[],e$={shallow:!0};ej.render=ej;var eH=[];function ej(e,t,r){t=t||{};var a,n=B.__s;return B.__s=!0,a=r&&(r.pretty||r.voidElements||r.sortAttributes||r.shallow||r.allAttributes||r.xml||r.attributeHook)?function e(t,r,a,n,i,o){if(null==t||"boolean"==typeof t)return"";if("object"!=typeof t)return ek(t);var s=a.pretty,c=s&&"string"==typeof s?s:"	";if(Array.isArray(t)){for(var l="",d=0;d<t.length;d++)s&&d>0&&(l+="\n"),l+=e(t[d],r,a,n,i,o);return l}var u,f=t.type,p=t.props,h=!1;if("function"==typeof f){if(h=!0,!a.shallow||!n&&!1!==a.renderRootComponent){if(f===er){var y=[];return eU(y,t.props.children),e(y,r,a,!1!==a.shallowHighOrder,i,o)}var b,x=t.__c=eR(t,r);B.__b&&B.__b(t);var m=B.__r;if(f.prototype&&"function"==typeof f.prototype.render){var w=eO(f,r);(x=t.__c=new f(p,w)).__v=t,x._dirty=x.__d=!0,x.props=p,null==x.state&&(x.state={}),null==x._nextState&&null==x.__s&&(x._nextState=x.__s=x.state),x.context=w,f.getDerivedStateFromProps?x.state=Object.assign({},x.state,f.getDerivedStateFromProps(x.props,x.state)):x.componentWillMount&&(x.componentWillMount(),x.state=x._nextState!==x.state?x._nextState:x.__s!==x.state?x.__s:x.state),m&&m(t),b=x.render(x.props,x.state,x.context)}else for(var g=eO(f,r),_=0;x.__d&&_++<25;)x.__d=!1,m&&m(t),b=f.call(t.__c,p,g);return x.getChildContext&&(r=Object.assign({},r,x.getChildContext())),B.diffed&&B.diffed(t),e(b,r,a,!1!==a.shallowHighOrder,i,o)}f=(u=f).displayName||u!==Function&&u.name||function(e){var t=(Function.prototype.toString.call(e).match(/^\s*function\s+([^( ]+)/)||"")[1];if(!t){for(var r=-1,a=eI.length;a--;)if(eI[a]===e){r=a;break}r<0&&(r=eI.push(e)-1),t="UnnamedComponent"+r}return t}(u)}var v,k,A="<"+f;if(p){var S=Object.keys(p);a&&!0===a.sortAttributes&&S.sort();for(var E=0;E<S.length;E++){var T=S[E],P=p[T];if("children"!==T){if(!eg.test(T)&&(a&&a.allAttributes||"key"!==T&&"ref"!==T&&"__self"!==T&&"__source"!==T)){if("defaultValue"===T)T="value";else if("defaultChecked"===T)T="checked";else if("defaultSelected"===T)T="selected";else if("className"===T){if(void 0!==p.class)continue;T="class"}else i&&e_.test(T)&&(T=T.toLowerCase().replace(/^xlink:?/,"xlink:"));if("htmlFor"===T){if(p.for)continue;T="for"}"style"===T&&P&&"object"==typeof P&&(P=eP(P)),"a"===T[0]&&"r"===T[1]&&"boolean"==typeof P&&(P=String(P));var U=a.attributeHook&&a.attributeHook(T,P,r,a,h);if(U||""===U)A+=U;else if("dangerouslySetInnerHTML"===T)k=P&&P.__html;else if("textarea"===f&&"value"===T)v=P;else if((P||0===P||""===P)&&"function"!=typeof P){if(!(!0!==P&&""!==P||(P=T,a&&a.xml))){A=A+" "+T;continue}if("value"===T){if("select"===f){o=P;continue}"option"===f&&o==P&&void 0===p.selected&&(A+=" selected")}A=A+" "+T+'="'+ek(P)+'"'}}}else v=P}}if(s){var C=A.replace(/\n\s*/," ");C===A||~C.indexOf("\n")?s&&~A.indexOf("\n")&&(A+="\n"):A=C}if(A+=">",eg.test(f))throw Error(f+" is not a valid HTML tag name in "+A);var R,O=ew.test(f)||a.voidElements&&a.voidElements.test(f),I=[];if(k)s&&eS(k)&&(k="\n"+c+eA(k,c)),A+=k;else if(null!=v&&eU(R=[],v).length){for(var $=s&&~A.indexOf("\n"),H=!1,j=0;j<R.length;j++){var W=R[j];if(null!=W&&!1!==W){var D=e(W,r,a,!0,"svg"===f||"foreignObject"!==f&&i,o);if(s&&!$&&eS(D)&&($=!0),D)if(s){var L=D.length>0&&"<"!=D[0];H&&L?I[I.length-1]+=D:I.push(D),H=L}else I.push(D)}}if(s&&$)for(var K=I.length;K--;)I[K]="\n"+c+eA(I[K],c)}if(I.length||k)A+=I.join("");else if(a&&a.xml)return A.substring(0,A.length-1)+" />";return!O||R||k?(s&&~A.indexOf("\n")&&(A+="\n"),A=A+"</"+f+">"):A=A.replace(/>$/," />"),A}(e,t,r):function e(t,r,a,n){if(null==t||!0===t||!1===t||""===t)return"";if("object"!=typeof t)return ek(t);if(eW(t)){for(var i="",o=0;o<t.length;o++)i+=e(t[o],r,a,n);return i}B.__b&&B.__b(t);var s=t.type,c=t.props;if("function"==typeof s){if(s===er)return e(t.props.children,r,a,n);var l,d,u,f,p,h=s.prototype&&"function"==typeof s.prototype.render?(l=r,u=eO(d=t.type,l),f=new d(t.props,u),t.__c=f,f.__v=t,f.__d=!0,f.props=t.props,null==f.state&&(f.state={}),null==f.__s&&(f.__s=f.state),f.context=u,d.getDerivedStateFromProps?f.state=eD({},f.state,d.getDerivedStateFromProps(f.props,f.state)):f.componentWillMount&&(f.componentWillMount(),f.state=f.__s!==f.state?f.__s:f.state),(p=B.__r)&&p(t),f.render(f.props,f.state,f.context)):function(e,t){var r,a=eR(e,t),n=eO(e.type,t);e.__c=a;for(var i=B.__r,o=0;a.__d&&o++<25;)a.__d=!1,i&&i(e),r=e.type.call(a,e.props,n);return r}(t,r),y=t.__c;y.getChildContext&&(r=eD({},r,y.getChildContext()));var b=e(h,r,a,n);return B.diffed&&B.diffed(t),b}var x,m,w="<";if(w+=s,c)for(var g in x=c.children,c){var _,v,k,A=c[g];if(!("key"===g||"ref"===g||"__self"===g||"__source"===g||"children"===g||"className"===g&&"class"in c||"htmlFor"===g&&"for"in c||eg.test(g))){if(v=g="className"===(_=g)?"class":"htmlFor"===_?"for":"defaultValue"===_?"value":"defaultChecked"===_?"checked":"defaultSelected"===_?"selected":a&&e_.test(_)?_.toLowerCase().replace(/^xlink:?/,"xlink:"):_,k=A,A="style"===v&&null!=k&&"object"==typeof k?eP(k):"a"===v[0]&&"r"===v[1]&&"boolean"==typeof k?String(k):k,"dangerouslySetInnerHTML"===g)m=A&&A.__html;else if("textarea"===s&&"value"===g)x=A;else if((A||0===A||""===A)&&"function"!=typeof A){if(!0===A||""===A){A=g,w=w+" "+g;continue}if("value"===g){if("select"===s){n=A;continue}"option"!==s||n!=A||"selected"in c||(w+=" selected")}w=w+" "+g+'="'+ek(A)+'"'}}}var S=w;if(w+=">",eg.test(s))throw Error(s+" is not a valid HTML tag name in "+w);var E="",T=!1;if(m)E+=m,T=!0;else if("string"==typeof x)E+=ek(x),T=!0;else if(eW(x))for(var P=0;P<x.length;P++){var U=x[P];if(null!=U&&!1!==U){var C=e(U,r,"svg"===s||"foreignObject"!==s&&a,n);C&&(E+=C,T=!0)}}else if(null!=x&&!1!==x&&!0!==x){var R=e(x,r,"svg"===s||"foreignObject"!==s&&a,n);R&&(E+=R,T=!0)}if(B.diffed&&B.diffed(t),T)w+=E;else if(ew.test(s))return S+" />";return w+"</"+s+">"}(e,t,!1,void 0),B.__c&&B.__c(e,eH),B.__s=n,eH.length=0,a}var eW=Array.isArray,eD=Object.assign;ej.shallowRender=function(e,t){return ej(e,t,e$)};var eL=0;function eK(e,t,r,a,n){var i,o,s={};for(o in t)"ref"==o?i=t[o]:s[o]=t[o];var c={type:e,props:s,key:r,ref:i,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:--eL,__source:n,__self:a};if("function"==typeof e&&(i=e.defaultProps))for(o in i)void 0===s[o]&&(s[o]=i[o]);return B.vnode&&B.vnode(c),c}async function eJ(e,t){let r=window.SimpleWebAuthnBrowser;async function a(r){let a=new URL(`${e}/webauthn-options/${t}`);r&&a.searchParams.append("action",r),i().forEach(e=>{a.searchParams.append(e.name,e.value)});let n=await fetch(a);return n.ok?n.json():void console.error("Failed to fetch options",n)}function n(){let e=`#${t}-form`,r=document.querySelector(e);if(!r)throw Error(`Form '${e}' not found`);return r}function i(){return Array.from(n().querySelectorAll("input[data-form-field]"))}async function o(e,t){let r=n();if(e){let t=document.createElement("input");t.type="hidden",t.name="action",t.value=e,r.appendChild(t)}if(t){let e=document.createElement("input");e.type="hidden",e.name="data",e.value=JSON.stringify(t),r.appendChild(e)}return r.submit()}async function s(e,t){let a=await r.startAuthentication(e,t);return await o("authenticate",a)}async function c(e){i().forEach(e=>{if(e.required&&!e.value)throw Error(`Missing required field: ${e.name}`)});let t=await r.startRegistration(e);return await o("register",t)}async function l(){if(!r.browserSupportsWebAuthnAutofill())return;let e=await a("authenticate");if(!e)return void console.error("Failed to fetch option for autofill authentication");try{await s(e.options,!0)}catch(e){console.error(e)}}(async function(){let e=n();if(!r.browserSupportsWebAuthn()){e.style.display="none";return}e&&e.addEventListener("submit",async e=>{e.preventDefault();let t=await a(void 0);if(!t)return void console.error("Failed to fetch options for form submission");if("authenticate"===t.action)try{await s(t.options,!1)}catch(e){console.error(e)}else if("register"===t.action)try{await c(t.options)}catch(e){console.error(e)}})})(),l()}let eN={default:"Unable to sign in.",Signin:"Try signing in with a different account.",OAuthSignin:"Try signing in with a different account.",OAuthCallbackError:"Try signing in with a different account.",OAuthCreateAccount:"Try signing in with a different account.",EmailCreateAccount:"Try signing in with a different account.",Callback:"Try signing in with a different account.",OAuthAccountNotLinked:"To confirm your identity, sign in with the same account you used originally.",EmailSignin:"The e-mail could not be sent.",CredentialsSignin:"Sign in failed. Check the details you provided are correct.",SessionRequired:"Please sign in to access this page."},eM=`:root {
  --border-width: 1px;
  --border-radius: 0.5rem;
  --color-error: #c94b4b;
  --color-info: #157efb;
  --color-info-hover: #0f6ddb;
  --color-info-text: #fff;
}

.__next-auth-theme-auto,
.__next-auth-theme-light {
  --color-background: #ececec;
  --color-background-hover: rgba(236, 236, 236, 0.8);
  --color-background-card: #fff;
  --color-text: #000;
  --color-primary: #444;
  --color-control-border: #bbb;
  --color-button-active-background: #f9f9f9;
  --color-button-active-border: #aaa;
  --color-separator: #ccc;
}

.__next-auth-theme-dark {
  --color-background: #161b22;
  --color-background-hover: rgba(22, 27, 34, 0.8);
  --color-background-card: #0d1117;
  --color-text: #fff;
  --color-primary: #ccc;
  --color-control-border: #555;
  --color-button-active-background: #060606;
  --color-button-active-border: #666;
  --color-separator: #444;
}

@media (prefers-color-scheme: dark) {
  .__next-auth-theme-auto {
    --color-background: #161b22;
    --color-background-hover: rgba(22, 27, 34, 0.8);
    --color-background-card: #0d1117;
    --color-text: #fff;
    --color-primary: #ccc;
    --color-control-border: #555;
    --color-button-active-background: #060606;
    --color-button-active-border: #666;
    --color-separator: #444;
  }

  button,
  a.button {
    color: var(--provider-dark-color, var(--color-primary)) !important;
    background-color: var(
      --provider-dark-bg,
      var(--color-background)
    ) !important;
  }

    :is(button,a.button):hover {
      background-color: var(
        --provider-dark-bg-hover,
        var(--color-background-hover)
      ) !important;
    }

    :is(button,a.button) span {
      color: var(--provider-dark-bg) !important;
    }
}

html {
  box-sizing: border-box;
}

*,
*:before,
*:after {
  box-sizing: inherit;
  margin: 0;
  padding: 0;
}

body {
  background-color: var(--color-background);
  margin: 0;
  padding: 0;
  font-family:
    ui-sans-serif,
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Roboto,
    "Helvetica Neue",
    Arial,
    "Noto Sans",
    sans-serif,
    "Apple Color Emoji",
    "Segoe UI Emoji",
    "Segoe UI Symbol",
    "Noto Color Emoji";
}

h1 {
  margin-bottom: 1.5rem;
  padding: 0 1rem;
  font-weight: 400;
  color: var(--color-text);
}

p {
  margin-bottom: 1.5rem;
  padding: 0 1rem;
  color: var(--color-text);
}

form {
  margin: 0;
  padding: 0;
}

label {
  font-weight: 500;
  text-align: left;
  margin-bottom: 0.25rem;
  display: block;
  color: var(--color-text);
}

input[type] {
  box-sizing: border-box;
  display: block;
  width: 100%;
  padding: 0.5rem 1rem;
  border: var(--border-width) solid var(--color-control-border);
  background: var(--color-background-card);
  font-size: 1rem;
  border-radius: var(--border-radius);
  color: var(--color-text);
}

p {
  font-size: 1.1rem;
  line-height: 2rem;
}

a.button {
  text-decoration: none;
  line-height: 1rem;
}

a.button:link,
  a.button:visited {
    background-color: var(--color-background);
    color: var(--color-primary);
  }

button,
a.button {
  padding: 0.75rem 1rem;
  color: var(--provider-color, var(--color-primary));
  background-color: var(--provider-bg, var(--color-background));
  border: 1px solid #00000031;
  font-size: 0.9rem;
  height: 50px;
  border-radius: var(--border-radius);
  transition: background-color 250ms ease-in-out;
  font-weight: 300;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

:is(button,a.button):hover {
    background-color: var(--provider-bg-hover, var(--color-background-hover));
    cursor: pointer;
  }

:is(button,a.button):active {
    cursor: pointer;
  }

:is(button,a.button) span {
    color: #fff;
  }

#submitButton {
  color: var(--button-text-color, var(--color-info-text));
  background-color: var(--brand-color, var(--color-info));
  width: 100%;
}

#submitButton:hover {
    background-color: var(
      --button-hover-bg,
      var(--color-info-hover)
    ) !important;
  }

a.site {
  color: var(--color-primary);
  text-decoration: none;
  font-size: 1rem;
  line-height: 2rem;
}

a.site:hover {
    text-decoration: underline;
  }

.page {
  position: absolute;
  width: 100%;
  height: 100%;
  display: grid;
  place-items: center;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.page > div {
    text-align: center;
  }

.error a.button {
    padding-left: 2rem;
    padding-right: 2rem;
    margin-top: 0.5rem;
  }

.error .message {
    margin-bottom: 1.5rem;
  }

.signin input[type="text"] {
    margin-left: auto;
    margin-right: auto;
    display: block;
  }

.signin hr {
    display: block;
    border: 0;
    border-top: 1px solid var(--color-separator);
    margin: 2rem auto 1rem auto;
    overflow: visible;
  }

.signin hr::before {
      content: "or";
      background: var(--color-background-card);
      color: #888;
      padding: 0 0.4rem;
      position: relative;
      top: -0.7rem;
    }

.signin .error {
    background: #f5f5f5;
    font-weight: 500;
    border-radius: 0.3rem;
    background: var(--color-error);
  }

.signin .error p {
      text-align: left;
      padding: 0.5rem 1rem;
      font-size: 0.9rem;
      line-height: 1.2rem;
      color: var(--color-info-text);
    }

.signin > div,
  .signin form {
    display: block;
  }

.signin > div input[type], .signin form input[type] {
      margin-bottom: 0.5rem;
    }

.signin > div button, .signin form button {
      width: 100%;
    }

.signin .provider + .provider {
    margin-top: 1rem;
  }

.logo {
  display: inline-block;
  max-width: 150px;
  margin: 1.25rem 0;
  max-height: 70px;
}

.card {
  background-color: var(--color-background-card);
  border-radius: 1rem;
  padding: 1.25rem 2rem;
}

.card .header {
    color: var(--color-primary);
  }

.card input[type]::-moz-placeholder {
    color: color-mix(
      in srgb,
      var(--color-text) 20%,
      var(--color-button-active-background)
    );
  }

.card input[type]::placeholder {
    color: color-mix(
      in srgb,
      var(--color-text) 20%,
      var(--color-button-active-background)
    );
  }

.card input[type] {
    background: color-mix(in srgb, var(--color-background-card) 95%, black);
  }

.section-header {
  color: var(--color-text);
}

@media screen and (min-width: 450px) {
  .card {
    margin: 2rem 0;
    width: 368px;
  }
}

@media screen and (max-width: 450px) {
  .card {
    margin: 1rem 0;
    width: 343px;
  }
}
`;function eF({html:e,title:t,status:r,cookies:a,theme:n,headTags:i}){return{cookies:a,status:r,headers:{"Content-Type":"text/html"},body:`<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width, initial-scale=1.0"><style>${eM}</style><title>${t}</title>${i??""}</head><body class="__next-auth-theme-${n?.colorScheme??"auto"}"><div class="page">${ej(e)}</div></body></html>`}}function ez(e){let{url:t,theme:r,query:a,cookies:n,pages:i,providers:o}=e;return{csrf:(e,t,r)=>e?(t.logger.warn("csrf-disabled"),r.push({name:t.cookies.csrfToken.name,value:"",options:{...t.cookies.csrfToken.options,maxAge:0}}),{status:404,cookies:r}):{headers:{"Content-Type":"application/json"},body:{csrfToken:t.csrfToken},cookies:r},providers:e=>({headers:{"Content-Type":"application/json"},body:e.reduce((e,{id:t,name:r,type:a,signinUrl:n,callbackUrl:i})=>(e[t]={id:t,name:r,type:a,signinUrl:n,callbackUrl:i},e),{})}),signin(t,s){if(t)throw new l.P8("Unsupported action");if(i?.signIn){let t=`${i.signIn}${i.signIn.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:e.callbackUrl??"/"})}`;return s&&(t=`${t}&${new URLSearchParams({error:s})}`),{redirect:t,cookies:n}}let c=o?.find(e=>"webauthn"===e.type&&e.enableConditionalUI&&!!e.simpleWebAuthnBrowserVersion),d="";if(c){let{simpleWebAuthnBrowserVersion:e}=c;d=`<script src="https://unpkg.com/@simplewebauthn/browser@${e}/dist/bundle/index.umd.min.js" crossorigin="anonymous"></script>`}return eF({cookies:n,theme:r,html:function(e){let{csrfToken:t,providers:r=[],callbackUrl:a,theme:n,email:i,error:o}=e;"undefined"!=typeof document&&n?.brandColor&&document.documentElement.style.setProperty("--brand-color",n.brandColor),"undefined"!=typeof document&&n?.buttonText&&document.documentElement.style.setProperty("--button-text-color",n.buttonText);let s=o&&(eN[o]??eN.default),c=r.find(e=>"webauthn"===e.type&&e.enableConditionalUI)?.id;return eK("div",{className:"signin",children:[n?.brandColor&&eK("style",{dangerouslySetInnerHTML:{__html:`:root {--brand-color: ${n.brandColor}}`}}),n?.buttonText&&eK("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --button-text-color: ${n.buttonText}
        }
      `}}),eK("div",{className:"card",children:[s&&eK("div",{className:"error",children:eK("p",{children:s})}),n?.logo&&eK("img",{src:n.logo,alt:"Logo",className:"logo"}),r.map((e,n)=>{let o,s,c;("oauth"===e.type||"oidc"===e.type)&&({bg:o="#fff",brandColor:s,logo:c=`https://authjs.dev/img/providers/${e.id}.svg`}=e.style??{});let l=s??o??"#fff";return eK("div",{className:"provider",children:["oauth"===e.type||"oidc"===e.type?eK("form",{action:e.signinUrl,method:"POST",children:[eK("input",{type:"hidden",name:"csrfToken",value:t}),a&&eK("input",{type:"hidden",name:"callbackUrl",value:a}),eK("button",{type:"submit",className:"button",style:{"--provider-bg":"#fff","--provider-bg-hover":`color-mix(in srgb, ${l} 30%, #fff)`,"--provider-dark-bg":"#161b22","--provider-dark-bg-hover":`color-mix(in srgb, ${l} 30%, #000)`},tabIndex:0,children:[eK("span",{style:{filter:"invert(1) grayscale(1) brightness(1.3) contrast(9000)","mix-blend-mode":"luminosity",opacity:.95},children:["Sign in with ",e.name]}),c&&eK("img",{loading:"lazy",height:24,src:c})]})]}):null,("email"===e.type||"credentials"===e.type||"webauthn"===e.type)&&n>0&&"email"!==r[n-1].type&&"credentials"!==r[n-1].type&&"webauthn"!==r[n-1].type&&eK("hr",{}),"email"===e.type&&eK("form",{action:e.signinUrl,method:"POST",children:[eK("input",{type:"hidden",name:"csrfToken",value:t}),eK("label",{className:"section-header",htmlFor:`input-email-for-${e.id}-provider`,children:"Email"}),eK("input",{id:`input-email-for-${e.id}-provider`,autoFocus:!0,type:"email",name:"email",value:i,placeholder:"<EMAIL>",required:!0}),eK("button",{id:"submitButton",type:"submit",tabIndex:0,children:["Sign in with ",e.name]})]}),"credentials"===e.type&&eK("form",{action:e.callbackUrl,method:"POST",children:[eK("input",{type:"hidden",name:"csrfToken",value:t}),Object.keys(e.credentials).map(t=>eK("div",{children:[eK("label",{className:"section-header",htmlFor:`input-${t}-for-${e.id}-provider`,children:e.credentials[t].label??t}),eK("input",{name:t,id:`input-${t}-for-${e.id}-provider`,type:e.credentials[t].type??"text",placeholder:e.credentials[t].placeholder??"",...e.credentials[t]})]},`input-group-${e.id}`)),eK("button",{id:"submitButton",type:"submit",tabIndex:0,children:["Sign in with ",e.name]})]}),"webauthn"===e.type&&eK("form",{action:e.callbackUrl,method:"POST",id:`${e.id}-form`,children:[eK("input",{type:"hidden",name:"csrfToken",value:t}),Object.keys(e.formFields).map(t=>eK("div",{children:[eK("label",{className:"section-header",htmlFor:`input-${t}-for-${e.id}-provider`,children:e.formFields[t].label??t}),eK("input",{name:t,"data-form-field":!0,id:`input-${t}-for-${e.id}-provider`,type:e.formFields[t].type??"text",placeholder:e.formFields[t].placeholder??"",...e.formFields[t]})]},`input-group-${e.id}`)),eK("button",{id:`submitButton-${e.id}`,type:"submit",tabIndex:0,children:["Sign in with ",e.name]})]}),("email"===e.type||"credentials"===e.type||"webauthn"===e.type)&&n+1<r.length&&eK("hr",{})]},e.id)})]}),c&&eK(er,{children:eK("script",{dangerouslySetInnerHTML:{__html:`
const currentURL = window.location.href;
const authURL = currentURL.substring(0, currentURL.lastIndexOf('/'));
(${eJ})(authURL, "${c}");
`}})})]})}({csrfToken:e.csrfToken,providers:e.providers?.filter(e=>["email","oauth","oidc"].includes(e.type)||"credentials"===e.type&&e.credentials||"webauthn"===e.type&&e.formFields||!1),callbackUrl:e.callbackUrl,theme:e.theme,error:s,...a}),title:"Sign In",headTags:d})},signout:()=>i?.signOut?{redirect:i.signOut,cookies:n}:eF({cookies:n,theme:r,html:function(e){let{url:t,csrfToken:r,theme:a}=e;return eK("div",{className:"signout",children:[a?.brandColor&&eK("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${a.brandColor}
        }
      `}}),a?.buttonText&&eK("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --button-text-color: ${a.buttonText}
        }
      `}}),eK("div",{className:"card",children:[a?.logo&&eK("img",{src:a.logo,alt:"Logo",className:"logo"}),eK("h1",{children:"Signout"}),eK("p",{children:"Are you sure you want to sign out?"}),eK("form",{action:t?.toString(),method:"POST",children:[eK("input",{type:"hidden",name:"csrfToken",value:r}),eK("button",{id:"submitButton",type:"submit",children:"Sign out"})]})]})]})}({csrfToken:e.csrfToken,url:t,theme:r}),title:"Sign Out"}),verifyRequest:e=>i?.verifyRequest?{redirect:i.verifyRequest,cookies:n}:eF({cookies:n,theme:r,html:function(e){let{url:t,theme:r}=e;return eK("div",{className:"verify-request",children:[r.brandColor&&eK("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${r.brandColor}
        }
      `}}),eK("div",{className:"card",children:[r.logo&&eK("img",{src:r.logo,alt:"Logo",className:"logo"}),eK("h1",{children:"Check your email"}),eK("p",{children:"A sign in link has been sent to your email address."}),eK("p",{children:eK("a",{className:"site",href:t.origin,children:t.host})})]})]})}({url:t,theme:r,...e}),title:"Verify Request"}),error:e=>i?.error?{redirect:`${i.error}${i.error.includes("?")?"&":"?"}error=${e}`,cookies:n}:eF({cookies:n,theme:r,...function(e){let{url:t,error:r="default",theme:a}=e,n=`${t}/signin`,i={default:{status:200,heading:"Error",message:eK("p",{children:eK("a",{className:"site",href:t?.origin,children:t?.host})})},Configuration:{status:500,heading:"Server error",message:eK("div",{children:[eK("p",{children:"There is a problem with the server configuration."}),eK("p",{children:"Check the server logs for more information."})]})},AccessDenied:{status:403,heading:"Access Denied",message:eK("div",{children:[eK("p",{children:"You do not have permission to sign in."}),eK("p",{children:eK("a",{className:"button",href:n,children:"Sign in"})})]})},Verification:{status:403,heading:"Unable to sign in",message:eK("div",{children:[eK("p",{children:"The sign in link is no longer valid."}),eK("p",{children:"It may have been used already or it may have expired."})]}),signin:eK("a",{className:"button",href:n,children:"Sign in"})}},{status:o,heading:s,message:c,signin:l}=i[r]??i.default;return{status:o,html:eK("div",{className:"error",children:[a?.brandColor&&eK("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${a?.brandColor}
        }
      `}}),eK("div",{className:"card",children:[a?.logo&&eK("img",{src:a?.logo,alt:"Logo",className:"logo"}),eK("h1",{children:s}),eK("div",{className:"message",children:c}),l]})]})}}({url:t,theme:r,error:e}),title:"Error"})}}function eB(e,t=Date.now()){return new Date(t+1e3*e)}async function eV(e,t,r,a){if(!r?.providerAccountId||!r.type)throw Error("Missing or invalid provider account");if(!["email","oauth","oidc","webauthn"].includes(r.type))throw Error("Provider not supported");let{adapter:n,jwt:i,events:o,session:{strategy:s,generateSessionToken:c}}=a;if(!n)return{user:t,account:r};let d=r,{createUser:u,updateUser:f,getUser:p,getUserByAccount:h,getUserByEmail:y,linkAccount:b,createSession:x,getSessionAndUser:m,deleteSession:w}=n,g=null,_=null,v=!1,k="jwt"===s;if(e)if(k)try{let t=a.cookies.sessionToken.name;(g=await i.decode({...i,token:e,salt:t}))&&"sub"in g&&g.sub&&(_=await p(g.sub))}catch{}else{let t=await m(e);t&&(g=t.session,_=t.user)}if("email"===d.type){let r=await y(t.email);return r?(_?.id!==r.id&&!k&&e&&await w(e),_=await f({id:r.id,emailVerified:new Date}),await o.updateUser?.({user:_})):(_=await u({...t,emailVerified:new Date}),await o.createUser?.({user:_}),v=!0),{session:g=k?{}:await x({sessionToken:c(),userId:_.id,expires:eB(a.session.maxAge)}),user:_,isNewUser:v}}if("webauthn"===d.type){let e=await h({providerAccountId:d.providerAccountId,provider:d.provider});if(e){if(_){if(e.id===_.id){let e={...d,userId:_.id};return{session:g,user:_,isNewUser:v,account:e}}throw new l.WS("The account is already associated with another user",{provider:d.provider})}g=k?{}:await x({sessionToken:c(),userId:e.id,expires:eB(a.session.maxAge)});let t={...d,userId:e.id};return{session:g,user:e,isNewUser:v,account:t}}{if(_){await b({...d,userId:_.id}),await o.linkAccount?.({user:_,account:d,profile:t});let e={...d,userId:_.id};return{session:g,user:_,isNewUser:v,account:e}}if(t.email?await y(t.email):null)throw new l.WS("Another account already exists with the same e-mail address",{provider:d.provider});_=await u({...t}),await o.createUser?.({user:_}),await b({...d,userId:_.id}),await o.linkAccount?.({user:_,account:d,profile:t}),g=k?{}:await x({sessionToken:c(),userId:_.id,expires:eB(a.session.maxAge)});let e={...d,userId:_.id};return{session:g,user:_,isNewUser:!0,account:e}}}let A=await h({providerAccountId:d.providerAccountId,provider:d.provider});if(A){if(_){if(A.id===_.id)return{session:g,user:_,isNewUser:v};throw new l.XP("The account is already associated with another user",{provider:d.provider})}return{session:g=k?{}:await x({sessionToken:c(),userId:A.id,expires:eB(a.session.maxAge)}),user:A,isNewUser:v}}{let{provider:e}=a,{type:r,provider:n,providerAccountId:i,userId:s,...f}=d;if(d=Object.assign(e.account(f)??{},{providerAccountId:i,provider:n,type:r,userId:s}),_)return await b({...d,userId:_.id}),await o.linkAccount?.({user:_,account:d,profile:t}),{session:g,user:_,isNewUser:v};let p=t.email?await y(t.email):null;if(p){let e=a.provider;if(e?.allowDangerousEmailAccountLinking)_=p,v=!1;else throw new l.XP("Another account already exists with the same e-mail address",{provider:d.provider})}else _=await u({...t,emailVerified:null}),v=!0;return await o.createUser?.({user:_}),await b({...d,userId:_.id}),await o.linkAccount?.({user:_,account:d,profile:t}),{session:g=k?{}:await x({sessionToken:c(),userId:_.id,expires:eB(a.session.maxAge)}),user:_,isNewUser:v}}}function eq(e,t){if(null==e)return!1;try{return e instanceof t||Object.getPrototypeOf(e)[Symbol.toStringTag]===t.prototype[Symbol.toStringTag]}catch{return!1}}"undefined"!=typeof navigator&&navigator.userAgent?.startsWith?.("Mozilla/5.0 ")||(a="oauth4webapi/v3.5.5");let eG="ERR_INVALID_ARG_VALUE",eX="ERR_INVALID_ARG_TYPE";function eY(e,t,r){let a=TypeError(e,{cause:r});return Object.assign(a,{code:t}),a}let eZ=Symbol(),eQ=Symbol(),e0=Symbol(),e1=Symbol(),e2=Symbol(),e5=Symbol(),e6=Symbol(),e3=new TextEncoder,e8=new TextDecoder;function e4(e){return"string"==typeof e?e3.encode(e):e8.decode(e)}function e9(e){return"string"==typeof e?i(e):n(e)}n=Uint8Array.prototype.toBase64?e=>(e instanceof ArrayBuffer&&(e=new Uint8Array(e)),e.toBase64({alphabet:"base64url",omitPadding:!0})):e=>{e instanceof ArrayBuffer&&(e=new Uint8Array(e));let t=[];for(let r=0;r<e.byteLength;r+=32768)t.push(String.fromCharCode.apply(null,e.subarray(r,r+32768)));return btoa(t.join("")).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")},i=Uint8Array.fromBase64?e=>{try{return Uint8Array.fromBase64(e,{alphabet:"base64url"})}catch(e){throw eY("The input to be decoded is not correctly encoded.",eG,e)}}:e=>{try{let t=atob(e.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"")),r=new Uint8Array(t.length);for(let e=0;e<t.length;e++)r[e]=t.charCodeAt(e);return r}catch(e){throw eY("The input to be decoded is not correctly encoded.",eG,e)}};class e7 extends Error{code;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=rl,Error.captureStackTrace?.(this,this.constructor)}}class te extends Error{code;constructor(e,t){super(e,t),this.name=this.constructor.name,t?.code&&(this.code=t?.code),Error.captureStackTrace?.(this,this.constructor)}}function tt(e,t,r){return new te(e,{code:t,cause:r})}function tr(e,t){if(!(e instanceof CryptoKey))throw eY(`${t} must be a CryptoKey`,eX)}function ta(e,t){if(tr(e,t),"private"!==e.type)throw eY(`${t} must be a private CryptoKey`,eG)}function tn(e){return!(null===e||"object"!=typeof e||Array.isArray(e))}function ti(e){eq(e,Headers)&&(e=Object.fromEntries(e.entries()));let t=new Headers(e??{});if(a&&!t.has("user-agent")&&t.set("user-agent",a),t.has("authorization"))throw eY('"options.headers" must not include the "authorization" header name',eG);return t}function to(e){if("function"==typeof e&&(e=e()),!(e instanceof AbortSignal))throw eY('"options.signal" must return or be an instance of AbortSignal',eX);return e}function ts(e){return e.includes("//")?e.replace("//","/"):e}async function tc(e,t,r,a){if(!(e instanceof URL))throw eY(`"${t}" must be an instance of URL`,eX);tP(e,a?.[eZ]!==!0);let n=r(new URL(e.href)),i=ti(a?.headers);return i.set("accept","application/json"),(a?.[e1]||fetch)(n.href,{body:void 0,headers:Object.fromEntries(i.entries()),method:"GET",redirect:"manual",signal:a?.signal?to(a.signal):void 0})}async function tl(e,t){return tc(e,"issuerIdentifier",e=>{switch(t?.algorithm){case void 0:case"oidc":e.pathname=ts(`${e.pathname}/.well-known/openid-configuration`);break;case"oauth2":!function(e,t,r=!1){"/"===e.pathname?e.pathname=t:e.pathname=ts(`${t}/${r?e.pathname:e.pathname.replace(/(\/)$/,"")}`)}(e,".well-known/oauth-authorization-server");break;default:throw eY('"options.algorithm" must be "oidc" (default), or "oauth2"',eG)}return e},t)}function td(e,t,r,a,n){try{if("number"!=typeof e||!Number.isFinite(e))throw eY(`${r} must be a number`,eX,n);if(e>0)return;if(t){if(0!==e)throw eY(`${r} must be a non-negative number`,eG,n);return}throw eY(`${r} must be a positive number`,eG,n)}catch(e){if(a)throw tt(e.message,a,n);throw e}}function tu(e,t,r,a){try{if("string"!=typeof e)throw eY(`${t} must be a string`,eX,a);if(0===e.length)throw eY(`${t} must not be empty`,eG,a)}catch(e){if(r)throw tt(e.message,r,a);throw e}}async function tf(e,t){if(!(e instanceof URL)&&e!==rB)throw eY('"expectedIssuerIdentifier" must be an instance of URL',eX);if(!eq(t,Response))throw eY('"response" must be an instance of Response',eX);if(200!==t.status)throw tt('"response" is not a conform Authorization Server Metadata response (unexpected HTTP status code)',rb,t);rE(t);let r=await rz(t);if(tu(r.issuer,'"response" body "issuer" property',rp,{body:r}),e!==rB&&new URL(r.issuer).href!==e.href)throw tt('"response" body "issuer" property does not match the expected value',r_,{expected:e.href,body:r,attribute:"issuer"});return r}function tp(e){var t=e,r="application/json";if(tq(t)!==r)throw th(t,r)}function th(e,...t){let r='"response" content-type must be ';if(t.length>2){let e=t.pop();r+=`${t.join(", ")}, or ${e}`}else 2===t.length?r+=`${t[0]} or ${t[1]}`:r+=t[0];return tt(r,ry,e)}function ty(){return e9(crypto.getRandomValues(new Uint8Array(32)))}async function tb(e){return tu(e,"codeVerifier"),e9(await crypto.subtle.digest("SHA-256",e4(e)))}function tx(e){switch(e.algorithm.name){case"RSA-PSS":switch(e.algorithm.hash.name){case"SHA-256":return"PS256";case"SHA-384":return"PS384";case"SHA-512":return"PS512";default:throw new e7("unsupported RsaHashedKeyAlgorithm hash name",{cause:e})}case"RSASSA-PKCS1-v1_5":switch(e.algorithm.hash.name){case"SHA-256":return"RS256";case"SHA-384":return"RS384";case"SHA-512":return"RS512";default:throw new e7("unsupported RsaHashedKeyAlgorithm hash name",{cause:e})}case"ECDSA":switch(e.algorithm.namedCurve){case"P-256":return"ES256";case"P-384":return"ES384";case"P-521":return"ES512";default:throw new e7("unsupported EcKeyAlgorithm namedCurve",{cause:e})}case"Ed25519":case"EdDSA":return"Ed25519";default:throw new e7("unsupported CryptoKey algorithm name",{cause:e})}}function tm(e){let t=e?.[eQ];return"number"==typeof t&&Number.isFinite(t)?t:0}function tw(e){let t=e?.[e0];return"number"==typeof t&&Number.isFinite(t)&&-1!==Math.sign(t)?t:30}function tg(){return Math.floor(Date.now()/1e3)}function t_(e){if("object"!=typeof e||null===e)throw eY('"as" must be an object',eX);tu(e.issuer,'"as.issuer"')}function tv(e){if("object"!=typeof e||null===e)throw eY('"client" must be an object',eX);tu(e.client_id,'"client.client_id"')}function tk(e,t){let r=tg()+tm(t);return{jti:ty(),aud:e.issuer,exp:r+60,iat:r,nbf:r,iss:t.client_id,sub:t.client_id}}async function tA(e,t,r){if(!r.usages.includes("sign"))throw eY('CryptoKey instances used for signing assertions must include "sign" in their "usages"',eG);let a=`${e9(e4(JSON.stringify(e)))}.${e9(e4(JSON.stringify(t)))}`,n=e9(await crypto.subtle.sign(rR(r),r,e4(a)));return`${a}.${n}`}async function tS(e){let{kty:t,e:r,n:a,x:n,y:i,crv:s}=await crypto.subtle.exportKey("jwk",e),c={kty:t,e:r,n:a,x:n,y:i,crv:s};return o.set(e,c),c}async function tE(e){return(o||=new WeakMap).get(e)||tS(e)}let tT=URL.parse?(e,t)=>URL.parse(e,t):(e,t)=>{try{return new URL(e,t)}catch{return null}};function tP(e,t){if(t&&"https:"!==e.protocol)throw tt("only requests to HTTPS are allowed",rx,e);if("https:"!==e.protocol&&"http:"!==e.protocol)throw tt("only HTTP and HTTPS requests are allowed",rm,e)}function tU(e,t,r,a){let n;if("string"!=typeof e||!(n=tT(e)))throw tt(`authorization server metadata does not contain a valid ${r?`"as.mtls_endpoint_aliases.${t}"`:`"as.${t}"`}`,void 0===e?rk:rA,{attribute:r?`mtls_endpoint_aliases.${t}`:t});return tP(n,a),n}function tC(e,t,r,a){return r&&e.mtls_endpoint_aliases&&t in e.mtls_endpoint_aliases?tU(e.mtls_endpoint_aliases[t],t,r,a):tU(e[t],t,r,a)}class tR extends Error{cause;code;error;status;error_description;response;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=rc,this.cause=t.cause,this.error=t.cause.error,this.status=t.response.status,this.error_description=t.cause.error_description,Object.defineProperty(this,"response",{enumerable:!1,value:t.response}),Error.captureStackTrace?.(this,this.constructor)}}class tO extends Error{cause;code;error;error_description;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=rd,this.cause=t.cause,this.error=t.cause.get("error"),this.error_description=t.cause.get("error_description")??void 0,Error.captureStackTrace?.(this,this.constructor)}}class tI extends Error{cause;code;response;status;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=rs,this.cause=t.cause,this.status=t.response.status,this.response=t.response,Object.defineProperty(this,"response",{enumerable:!1}),Error.captureStackTrace?.(this,this.constructor)}}let t$="[a-zA-Z0-9!#$%&\\'\\*\\+\\-\\.\\^_`\\|~]+",tH=RegExp("^[,\\s]*("+t$+")\\s(.*)"),tj=RegExp("^[,\\s]*("+t$+')\\s*=\\s*"((?:[^"\\\\]|\\\\.)*)"[,\\s]*(.*)'),tW=RegExp("^[,\\s]*"+("("+t$+")\\s*=\\s*(")+t$+")[,\\s]*(.*)"),tD=RegExp("^([a-zA-Z0-9\\-\\._\\~\\+\\/]+[=]{0,2})(?:$|[,\\s])(.*)");async function tL(e){if(e.status>399&&e.status<500){rE(e),tp(e);try{let t=await e.clone().json();if(tn(t)&&"string"==typeof t.error&&t.error.length)return t}catch{}}}async function tK(e,t,r){if(e.status!==t){let t;if(t=await tL(e))throw await e.body?.cancel(),new tR("server responded with an error in the response body",{cause:t,response:e});throw tt(`"response" is not a conform ${r} response (unexpected HTTP status code)`,rb,e)}}function tJ(e){if(!t4.has(e))throw eY('"options.DPoP" is not a valid DPoPHandle',eG)}async function tN(e,t,r,a,n,i){if(tu(e,'"accessToken"'),!(r instanceof URL))throw eY('"url" must be an instance of URL',eX);tP(r,i?.[eZ]!==!0),a=ti(a),i?.DPoP&&(tJ(i.DPoP),await i.DPoP.addProof(r,a,t.toUpperCase(),e)),a.set("authorization",`${a.has("dpop")?"DPoP":"Bearer"} ${e}`);let o=await (i?.[e1]||fetch)(r.href,{body:n,headers:Object.fromEntries(a.entries()),method:t,redirect:"manual",signal:i?.signal?to(i.signal):void 0});return i?.DPoP?.cacheNonce(o),o}async function tM(e,t,r,a){t_(e),tv(t);let n=tC(e,"userinfo_endpoint",t.use_mtls_endpoint_aliases,a?.[eZ]!==!0),i=ti(a?.headers);return t.userinfo_signed_response_alg?i.set("accept","application/jwt"):(i.set("accept","application/json"),i.append("accept","application/jwt")),tN(r,"GET",n,i,null,{...a,[eQ]:tm(t)})}function tF(e,t,r,a){(s||=new WeakMap).set(e,{jwks:t,uat:r,get age(){return tg()-this.uat}}),a&&Object.assign(a,{jwks:structuredClone(t),uat:r})}function tz(e,t){s?.delete(e),delete t?.jwks,delete t?.uat}async function tB(e,t,r){var a;let n,i,o,{alg:c,kid:l}=r;if(function(e){if(!rU(e.alg))throw new e7('unsupported JWS "alg" identifier',{cause:{alg:e.alg}})}(r),!s?.has(e)&&!("object"!=typeof(a=t?.[e6])||null===a||!("uat"in a)||"number"!=typeof a.uat||tg()-a.uat>=300)&&"jwks"in a&&tn(a.jwks)&&Array.isArray(a.jwks.keys)&&Array.prototype.every.call(a.jwks.keys,tn)&&tF(e,t?.[e6].jwks,t?.[e6].uat),s?.has(e)){if({jwks:n,age:i}=s.get(e),i>=300)return tz(e,t?.[e6]),tB(e,t,r)}else n=await rT(e,t).then(rP),i=0,tF(e,n,tg(),t?.[e6]);switch(c.slice(0,2)){case"RS":case"PS":o="RSA";break;case"ES":o="EC";break;case"Ed":o="OKP";break;default:throw new e7("unsupported JWS algorithm",{cause:{alg:c}})}let d=n.keys.filter(e=>{if(e.kty!==o||void 0!==l&&l!==e.kid||void 0!==e.alg&&c!==e.alg||void 0!==e.use&&"sig"!==e.use||e.key_ops?.includes("verify")===!1)return!1;switch(!0){case"ES256"===c&&"P-256"!==e.crv:case"ES384"===c&&"P-384"!==e.crv:case"ES512"===c&&"P-521"!==e.crv:case"Ed25519"===c&&"Ed25519"!==e.crv:case"EdDSA"===c&&"Ed25519"!==e.crv:return!1}return!0}),{0:u,length:f}=d;if(!f){if(i>=60)return tz(e,t?.[e6]),tB(e,t,r);throw tt("error when selecting a JWT verification key, no applicable keys found",rv,{header:r,candidates:d,jwks_uri:new URL(e.jwks_uri)})}if(1!==f)throw tt('error when selecting a JWT verification key, multiple applicable keys found, a "kid" JWT Header Parameter is required',rv,{header:r,candidates:d,jwks_uri:new URL(e.jwks_uri)});return rM(c,u)}let tV=Symbol();function tq(e){return e.headers.get("content-type")?.split(";")[0]}async function tG(e,t,r,a,n){let i;if(t_(e),tv(t),!eq(a,Response))throw eY('"response" must be an instance of Response',eX);if(t2(a),200!==a.status)throw tt('"response" is not a conform UserInfo Endpoint response (unexpected HTTP status code)',rb,a);if(rE(a),"application/jwt"===tq(a)){let{claims:r,jwt:o}=await rI(await a.text(),rD.bind(void 0,t.userinfo_signed_response_alg,e.userinfo_signing_alg_values_supported,void 0),tm(t),tw(t),n?.[e5]).then(t5.bind(void 0,t.client_id)).then(t3.bind(void 0,e));tQ.set(a,o),i=r}else{if(t.userinfo_signed_response_alg)throw tt("JWT UserInfo Response expected",ru,a);i=await rz(a)}if(tu(i.sub,'"response" body "sub" property',rp,{body:i}),r===tV);else if(tu(r,'"expectedSubject"'),i.sub!==r)throw tt('unexpected "response" body "sub" property value',r_,{expected:r,body:i,attribute:"sub"});return i}async function tX(e,t,r,a,n,i,o){return await r(e,t,n,i),i.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"),(o?.[e1]||fetch)(a.href,{body:n,headers:Object.fromEntries(i.entries()),method:"POST",redirect:"manual",signal:o?.signal?to(o.signal):void 0})}async function tY(e,t,r,a,n,i){let o=tC(e,"token_endpoint",t.use_mtls_endpoint_aliases,i?.[eZ]!==!0);n.set("grant_type",a);let s=ti(i?.headers);s.set("accept","application/json"),i?.DPoP!==void 0&&(tJ(i.DPoP),await i.DPoP.addProof(o,s,"POST"));let c=await tX(e,t,r,o,n,s,i);return i?.DPoP?.cacheNonce(c),c}let tZ=new WeakMap,tQ=new WeakMap;function t0(e){if(!e.id_token)return;let t=tZ.get(e);if(!t)throw eY('"ref" was already garbage collected or did not resolve from the proper sources',eG);return t}async function t1(e,t,r,a,n){if(t_(e),tv(t),!eq(r,Response))throw eY('"response" must be an instance of Response',eX);t2(r),await tK(r,200,"Token Endpoint"),rE(r);let i=await rz(r);if(tu(i.access_token,'"response" body "access_token" property',rp,{body:i}),tu(i.token_type,'"response" body "token_type" property',rp,{body:i}),i.token_type=i.token_type.toLowerCase(),"dpop"!==i.token_type&&"bearer"!==i.token_type)throw new e7("unsupported `token_type` value",{cause:{body:i}});if(void 0!==i.expires_in){let e="number"!=typeof i.expires_in?parseFloat(i.expires_in):i.expires_in;td(e,!1,'"response" body "expires_in" property',rp,{body:i}),i.expires_in=e}if(void 0!==i.refresh_token&&tu(i.refresh_token,'"response" body "refresh_token" property',rp,{body:i}),void 0!==i.scope&&"string"!=typeof i.scope)throw tt('"response" body "scope" property must be a string',rp,{body:i});if(void 0!==i.id_token){tu(i.id_token,'"response" body "id_token" property',rp,{body:i});let o=["aud","exp","iat","iss","sub"];!0===t.require_auth_time&&o.push("auth_time"),void 0!==t.default_max_age&&(td(t.default_max_age,!1,'"client.default_max_age"'),o.push("auth_time")),a?.length&&o.push(...a);let{claims:s,jwt:c}=await rI(i.id_token,rD.bind(void 0,t.id_token_signed_response_alg,e.id_token_signing_alg_values_supported,"RS256"),tm(t),tw(t),n?.[e5]).then(rt.bind(void 0,o)).then(t8.bind(void 0,e)).then(t6.bind(void 0,t.client_id));if(Array.isArray(s.aud)&&1!==s.aud.length){if(void 0===s.azp)throw tt('ID Token "aud" (audience) claim includes additional untrusted audiences',rg,{claims:s,claim:"aud"});if(s.azp!==t.client_id)throw tt('unexpected ID Token "azp" (authorized party) claim value',rg,{expected:t.client_id,claims:s,claim:"azp"})}void 0!==s.auth_time&&td(s.auth_time,!1,'ID Token "auth_time" (authentication time)',rp,{claims:s}),tQ.set(r,c),tZ.set(i,s)}return i}function t2(e){let t;if(t=function(e){if(!eq(e,Response))throw eY('"response" must be an instance of Response',eX);let t=e.headers.get("www-authenticate");if(null===t)return;let r=[],a=t;for(;a;){let e,t=a.match(tH),n=t?.["1"].toLowerCase();if(a=t?.["2"],!n)return;let i={};for(;a;){let r,n;if(t=a.match(tj)){if([,r,n,a]=t,n.includes("\\"))try{n=JSON.parse(`"${n}"`)}catch{}i[r.toLowerCase()]=n;continue}if(t=a.match(tW)){[,r,n,a]=t,i[r.toLowerCase()]=n;continue}if(t=a.match(tD)){if(Object.keys(i).length)break;[,e,a]=t;break}return}let o={scheme:n,parameters:i};e&&(o.token68=e),r.push(o)}if(r.length)return r}(e))throw new tI("server responded with a challenge in the WWW-Authenticate HTTP Header",{cause:t,response:e})}function t5(e,t){return void 0!==t.claims.aud?t6(e,t):t}function t6(e,t){if(Array.isArray(t.claims.aud)){if(!t.claims.aud.includes(e))throw tt('unexpected JWT "aud" (audience) claim value',rg,{expected:e,claims:t.claims,claim:"aud"})}else if(t.claims.aud!==e)throw tt('unexpected JWT "aud" (audience) claim value',rg,{expected:e,claims:t.claims,claim:"aud"});return t}function t3(e,t){return void 0!==t.claims.iss?t8(e,t):t}function t8(e,t){let r=e[rV]?.(t)??e.issuer;if(t.claims.iss!==r)throw tt('unexpected JWT "iss" (issuer) claim value',rg,{expected:r,claims:t.claims,claim:"iss"});return t}let t4=new WeakSet,t9=Symbol();async function t7(e,t,r,a,n,i,o){if(t_(e),tv(t),!t4.has(a))throw eY('"callbackParameters" must be an instance of URLSearchParams obtained from "validateAuthResponse()", or "validateJwtAuthResponse()',eG);tu(n,'"redirectUri"');let s=rL(a,"code");if(!s)throw tt('no authorization code in "callbackParameters"',rp);let c=new URLSearchParams(o?.additionalParameters);return c.set("redirect_uri",n),c.set("code",s),i!==t9&&(tu(i,'"codeVerifier"'),c.set("code_verifier",i)),tY(e,t,r,"authorization_code",c,o)}let re={aud:"audience",c_hash:"code hash",client_id:"client id",exp:"expiration time",iat:"issued at",iss:"issuer",jti:"jwt id",nonce:"nonce",s_hash:"state hash",sub:"subject",ath:"access token hash",htm:"http method",htu:"http uri",cnf:"confirmation",auth_time:"authentication time"};function rt(e,t){for(let r of e)if(void 0===t.claims[r])throw tt(`JWT "${r}" (${re[r]}) claim missing`,rp,{claims:t.claims});return t}let rr=Symbol(),ra=Symbol();async function rn(e,t,r,a){return"string"==typeof a?.expectedNonce||"number"==typeof a?.maxAge||a?.requireIdToken?ri(e,t,r,a.expectedNonce,a.maxAge,{[e5]:a[e5]}):ro(e,t,r,a)}async function ri(e,t,r,a,n,i){let o=[];switch(a){case void 0:a=rr;break;case rr:break;default:tu(a,'"expectedNonce" argument'),o.push("nonce")}switch(n??=t.default_max_age){case void 0:n=ra;break;case ra:break;default:td(n,!1,'"maxAge" argument'),o.push("auth_time")}let s=await t1(e,t,r,o,i);tu(s.id_token,'"response" body "id_token" property',rp,{body:s});let c=t0(s);if(n!==ra){let e=tg()+tm(t),r=tw(t);if(c.auth_time+n<e-r)throw tt("too much time has elapsed since the last End-User authentication",rw,{claims:c,now:e,tolerance:r,claim:"auth_time"})}if(a===rr){if(void 0!==c.nonce)throw tt('unexpected ID Token "nonce" claim value',rg,{expected:void 0,claims:c,claim:"nonce"})}else if(c.nonce!==a)throw tt('unexpected ID Token "nonce" claim value',rg,{expected:a,claims:c,claim:"nonce"});return s}async function ro(e,t,r,a){let n=await t1(e,t,r,void 0,a),i=t0(n);if(i){if(void 0!==t.default_max_age){td(t.default_max_age,!1,'"client.default_max_age"');let e=tg()+tm(t),r=tw(t);if(i.auth_time+t.default_max_age<e-r)throw tt("too much time has elapsed since the last End-User authentication",rw,{claims:i,now:e,tolerance:r,claim:"auth_time"})}if(void 0!==i.nonce)throw tt('unexpected ID Token "nonce" claim value',rg,{expected:void 0,claims:i,claim:"nonce"})}return n}let rs="OAUTH_WWW_AUTHENTICATE_CHALLENGE",rc="OAUTH_RESPONSE_BODY_ERROR",rl="OAUTH_UNSUPPORTED_OPERATION",rd="OAUTH_AUTHORIZATION_RESPONSE_ERROR",ru="OAUTH_JWT_USERINFO_EXPECTED",rf="OAUTH_PARSE_ERROR",rp="OAUTH_INVALID_RESPONSE",rh="OAUTH_INVALID_REQUEST",ry="OAUTH_RESPONSE_IS_NOT_JSON",rb="OAUTH_RESPONSE_IS_NOT_CONFORM",rx="OAUTH_HTTP_REQUEST_FORBIDDEN",rm="OAUTH_REQUEST_PROTOCOL_FORBIDDEN",rw="OAUTH_JWT_TIMESTAMP_CHECK_FAILED",rg="OAUTH_JWT_CLAIM_COMPARISON_FAILED",r_="OAUTH_JSON_ATTRIBUTE_COMPARISON_FAILED",rv="OAUTH_KEY_SELECTION_FAILED",rk="OAUTH_MISSING_SERVER_METADATA",rA="OAUTH_INVALID_SERVER_METADATA";function rS(e,t){if("string"!=typeof t.header.typ||t.header.typ.toLowerCase().replace(/^application\//,"")!==e)throw tt('unexpected JWT "typ" header parameter value',rp,{header:t.header});return t}function rE(e){if(e.bodyUsed)throw eY('"response" body has been used already',eG)}async function rT(e,t){t_(e);let r=tC(e,"jwks_uri",!1,t?.[eZ]!==!0),a=ti(t?.headers);return a.set("accept","application/json"),a.append("accept","application/jwk-set+json"),(t?.[e1]||fetch)(r.href,{body:void 0,headers:Object.fromEntries(a.entries()),method:"GET",redirect:"manual",signal:t?.signal?to(t.signal):void 0})}async function rP(e){if(!eq(e,Response))throw eY('"response" must be an instance of Response',eX);if(200!==e.status)throw tt('"response" is not a conform JSON Web Key Set response (unexpected HTTP status code)',rb,e);rE(e);let t=await rz(e,e=>(function(e,...t){if(!t.includes(tq(e)))throw th(e,...t)})(e,"application/json","application/jwk-set+json"));if(!Array.isArray(t.keys))throw tt('"response" body "keys" property must be an array',rp,{body:t});if(!Array.prototype.every.call(t.keys,tn))throw tt('"response" body "keys" property members must be JWK formatted objects',rp,{body:t});return t}function rU(e){switch(e){case"PS256":case"ES256":case"RS256":case"PS384":case"ES384":case"RS384":case"PS512":case"ES512":case"RS512":case"Ed25519":case"EdDSA":return!0;default:return!1}}function rC(e){let{algorithm:t}=e;if("number"!=typeof t.modulusLength||t.modulusLength<2048)throw new e7(`unsupported ${t.name} modulusLength`,{cause:e})}function rR(e){switch(e.algorithm.name){case"ECDSA":return{name:e.algorithm.name,hash:function(e){let{algorithm:t}=e;switch(t.namedCurve){case"P-256":return"SHA-256";case"P-384":return"SHA-384";case"P-521":return"SHA-512";default:throw new e7("unsupported ECDSA namedCurve",{cause:e})}}(e)};case"RSA-PSS":switch(rC(e),e.algorithm.hash.name){case"SHA-256":case"SHA-384":case"SHA-512":return{name:e.algorithm.name,saltLength:parseInt(e.algorithm.hash.name.slice(-3),10)>>3};default:throw new e7("unsupported RSA-PSS hash name",{cause:e})}case"RSASSA-PKCS1-v1_5":return rC(e),e.algorithm.name;case"Ed25519":return e.algorithm.name}throw new e7("unsupported CryptoKey algorithm name",{cause:e})}async function rO(e,t,r,a){let n=e4(`${e}.${t}`),i=rR(r);if(!await crypto.subtle.verify(i,r,a,n))throw tt("JWT signature verification failed",rp,{key:r,data:n,signature:a,algorithm:i})}async function rI(e,t,r,a,n){let i,o,{0:s,1:c,length:l}=e.split(".");if(5===l)if(void 0!==n)e=await n(e),{0:s,1:c,length:l}=e.split(".");else throw new e7("JWE decryption is not configured",{cause:e});if(3!==l)throw tt("Invalid JWT",rp,e);try{i=JSON.parse(e4(e9(s)))}catch(e){throw tt("failed to parse JWT Header body as base64url encoded JSON",rf,e)}if(!tn(i))throw tt("JWT Header must be a top level object",rp,e);if(t(i),void 0!==i.crit)throw new e7('no JWT "crit" header parameter extensions are supported',{cause:{header:i}});try{o=JSON.parse(e4(e9(c)))}catch(e){throw tt("failed to parse JWT Payload body as base64url encoded JSON",rf,e)}if(!tn(o))throw tt("JWT Payload must be a top level object",rp,e);let d=tg()+r;if(void 0!==o.exp){if("number"!=typeof o.exp)throw tt('unexpected JWT "exp" (expiration time) claim type',rp,{claims:o});if(o.exp<=d-a)throw tt('unexpected JWT "exp" (expiration time) claim value, expiration is past current timestamp',rw,{claims:o,now:d,tolerance:a,claim:"exp"})}if(void 0!==o.iat&&"number"!=typeof o.iat)throw tt('unexpected JWT "iat" (issued at) claim type',rp,{claims:o});if(void 0!==o.iss&&"string"!=typeof o.iss)throw tt('unexpected JWT "iss" (issuer) claim type',rp,{claims:o});if(void 0!==o.nbf){if("number"!=typeof o.nbf)throw tt('unexpected JWT "nbf" (not before) claim type',rp,{claims:o});if(o.nbf>d+a)throw tt('unexpected JWT "nbf" (not before) claim value',rw,{claims:o,now:d,tolerance:a,claim:"nbf"})}if(void 0!==o.aud&&"string"!=typeof o.aud&&!Array.isArray(o.aud))throw tt('unexpected JWT "aud" (audience) claim type',rp,{claims:o});return{header:i,claims:o,jwt:e}}async function r$(e,t,r){let a;switch(t.alg){case"RS256":case"PS256":case"ES256":a="SHA-256";break;case"RS384":case"PS384":case"ES384":a="SHA-384";break;case"RS512":case"PS512":case"ES512":case"Ed25519":case"EdDSA":a="SHA-512";break;default:throw new e7(`unsupported JWS algorithm for ${r} calculation`,{cause:{alg:t.alg}})}let n=await crypto.subtle.digest(a,e4(e));return e9(n.slice(0,n.byteLength/2))}async function rH(e,t,r,a){return t===await r$(e,r,a)}async function rj(e){if(e.bodyUsed)throw eY("form_post Request instances must contain a readable body",eG,{cause:e});return e.text()}async function rW(e){if("POST"!==e.method)throw eY("form_post responses are expected to use the POST method",eG,{cause:e});if("application/x-www-form-urlencoded"!==tq(e))throw eY("form_post responses are expected to use the application/x-www-form-urlencoded content-type",eG,{cause:e});return rj(e)}function rD(e,t,r,a){if(void 0!==e){if("string"==typeof e?a.alg!==e:!e.includes(a.alg))throw tt('unexpected JWT "alg" header parameter',rp,{header:a,expected:e,reason:"client configuration"});return}if(Array.isArray(t)){if(!t.includes(a.alg))throw tt('unexpected JWT "alg" header parameter',rp,{header:a,expected:t,reason:"authorization server metadata"});return}if(void 0!==r){if("string"==typeof r?a.alg!==r:"function"==typeof r?!r(a.alg):!r.includes(a.alg))throw tt('unexpected JWT "alg" header parameter',rp,{header:a,expected:r,reason:"default value"});return}throw tt('missing client or server configuration to verify used JWT "alg" header parameter',void 0,{client:e,issuer:t,fallback:r})}function rL(e,t){let{0:r,length:a}=e.getAll(t);if(a>1)throw tt(`"${t}" parameter must be provided only once`,rp);return r}let rK=Symbol(),rJ=Symbol();function rN(e,t,r,a){var n;if(t_(e),tv(t),r instanceof URL&&(r=r.searchParams),!(r instanceof URLSearchParams))throw eY('"parameters" must be an instance of URLSearchParams, or URL',eX);if(rL(r,"response"))throw tt('"parameters" contains a JARM response, use validateJwtAuthResponse() instead of validateAuthResponse()',rp,{parameters:r});let i=rL(r,"iss"),o=rL(r,"state");if(!i&&e.authorization_response_iss_parameter_supported)throw tt('response parameter "iss" (issuer) missing',rp,{parameters:r});if(i&&i!==e.issuer)throw tt('unexpected "iss" (issuer) response parameter value',rp,{expected:e.issuer,parameters:r});switch(a){case void 0:case rJ:if(void 0!==o)throw tt('unexpected "state" response parameter encountered',rp,{expected:void 0,parameters:r});break;case rK:break;default:if(tu(a,'"expectedState" argument'),o!==a)throw tt(void 0===o?'response parameter "state" missing':'unexpected "state" response parameter value',rp,{expected:a,parameters:r})}if(rL(r,"error"))throw new tO("authorization response from the server is an error",{cause:r});let s=rL(r,"id_token"),c=rL(r,"token");if(void 0!==s||void 0!==c)throw new e7("implicit and hybrid flows are not supported");return n=new URLSearchParams(r),t4.add(n),n}async function rM(e,t){let{ext:r,key_ops:a,use:n,...i}=t;return crypto.subtle.importKey("jwk",i,function(e){switch(e){case"PS256":case"PS384":case"PS512":return{name:"RSA-PSS",hash:`SHA-${e.slice(-3)}`};case"RS256":case"RS384":case"RS512":return{name:"RSASSA-PKCS1-v1_5",hash:`SHA-${e.slice(-3)}`};case"ES256":case"ES384":return{name:"ECDSA",namedCurve:`P-${e.slice(-3)}`};case"ES512":return{name:"ECDSA",namedCurve:"P-521"};case"Ed25519":case"EdDSA":return"Ed25519";default:throw new e7("unsupported JWS algorithm",{cause:{alg:e}})}}(e),!0,["verify"])}function rF(e){let t=new URL(e);return t.search="",t.hash="",t.href}async function rz(e,t=tp){let r;try{r=await e.json()}catch(r){throw t(e),tt('failed to parse "response" body as JSON',rf,r)}if(!tn(r))throw tt('"response" body must be a top level object',rp,{body:r});return r}let rB=Symbol(),rV=Symbol();async function rq(e,t,r){let{cookies:a,logger:n}=r,i=a[e],o=new Date;o.setTime(o.getTime()+9e5),n.debug(`CREATE_${e.toUpperCase()}`,{name:i.name,payload:t,COOKIE_TTL:900,expires:o});let s=await (0,m.lF)({...r.jwt,maxAge:900,token:{value:t},salt:i.name}),c={...i.options,expires:o};return{name:i.name,value:s,options:c}}async function rG(e,t,r){try{let{logger:a,cookies:n,jwt:i}=r;if(a.debug(`PARSE_${e.toUpperCase()}`,{cookie:t}),!t)throw new l.k9(`${e} cookie was missing`);let o=await (0,m.D4)({...i,token:t,salt:n[e].name});if(o?.value)return o.value;throw Error("Invalid cookie")}catch(t){throw new l.k9(`${e} value could not be parsed`,{cause:t})}}function rX(e,t,r){let{logger:a,cookies:n}=t,i=n[e];a.debug(`CLEAR_${e.toUpperCase()}`,{cookie:i}),r.push({name:i.name,value:"",options:{...n[e].options,maxAge:0}})}function rY(e,t){return async function(r,a,n){let{provider:i,logger:o}=n;if(!i?.checks?.includes(e))return;let s=r?.[n.cookies[t].name];o.debug(`USE_${t.toUpperCase()}`,{value:s});let c=await rG(t,s,n);return rX(t,n,a),c}}let rZ={async create(e){let t=ty(),r=await tb(t);return{cookie:await rq("pkceCodeVerifier",t,e),value:r}},use:rY("pkce","pkceCodeVerifier")},rQ="encodedState",r0={async create(e,t){let{provider:r}=e;if(!r.checks.includes("state")){if(t)throw new l.k9("State data was provided but the provider is not configured to use state");return}let a={origin:t,random:ty()},n=await (0,m.lF)({secret:e.jwt.secret,token:a,salt:rQ,maxAge:900});return{cookie:await rq("state",n,e),value:n}},use:rY("state","state"),async decode(e,t){try{t.logger.debug("DECODE_STATE",{state:e});let r=await (0,m.D4)({secret:t.jwt.secret,token:e,salt:rQ});if(r)return r;throw Error("Invalid state")}catch(e){throw new l.k9("State could not be decoded",{cause:e})}}},r1={async create(e){if(!e.provider.checks.includes("nonce"))return;let t=ty();return{cookie:await rq("nonce",t,e),value:t}},use:rY("nonce","nonce")},r2="encodedWebauthnChallenge",r5={create:async(e,t,r)=>({cookie:await rq("webauthnChallenge",await (0,m.lF)({secret:e.jwt.secret,token:{challenge:t,registerData:r},salt:r2,maxAge:900}),e)}),async use(e,t,r){let a=t?.[e.cookies.webauthnChallenge.name],n=await rG("webauthnChallenge",a,e),i=await (0,m.D4)({secret:e.jwt.secret,token:n,salt:r2});if(rX("webauthnChallenge",e,r),!i)throw new l.k9("WebAuthn challenge was missing");return i}};var r6=r(74771),r3=r(36448),r8=r(82896),r4=r(63748);function r9(e){return encodeURIComponent(e).replace(/%20/g,"+")}async function r7(e,t,r){let a,n,i,{logger:o,provider:s}=r,{token:c,userinfo:d}=s;if(c?.url&&"authjs.dev"!==c.url.host||d?.url&&"authjs.dev"!==d.url.host)a={issuer:s.issuer??"https://authjs.dev",token_endpoint:c?.url.toString(),userinfo_endpoint:d?.url.toString()};else{let e=new URL(s.issuer),t=await tl(e,{[eZ]:!0,[e1]:s[W]});if(!(a=await tf(e,t)).token_endpoint)throw TypeError("TODO: Authorization server did not provide a token endpoint.");if(!a.userinfo_endpoint)throw TypeError("TODO: Authorization server did not provide a userinfo endpoint.")}let u={client_id:s.clientId,...s.client};switch(u.token_endpoint_auth_method){case void 0:case"client_secret_basic":n=(e,t,r,a)=>{a.set("authorization",function(e,t){let r=r9(e),a=r9(t),n=btoa(`${r}:${a}`);return`Basic ${n}`}(s.clientId,s.clientSecret))};break;case"client_secret_post":var f;tu(f=s.clientSecret,'"clientSecret"'),n=(e,t,r,a)=>{r.set("client_id",t.client_id),r.set("client_secret",f)};break;case"client_secret_jwt":n=function(e,t){let r;tu(e,'"clientSecret"');let a=void 0;return async(t,n,i,o)=>{r||=await crypto.subtle.importKey("raw",e4(e),{hash:"SHA-256",name:"HMAC"},!1,["sign"]);let s={alg:"HS256"},c=tk(t,n);a?.(s,c);let l=`${e9(e4(JSON.stringify(s)))}.${e9(e4(JSON.stringify(c)))}`,d=await crypto.subtle.sign(r.algorithm,r,e4(l));i.set("client_id",n.client_id),i.set("client_assertion_type","urn:ietf:params:oauth:client-assertion-type:jwt-bearer"),i.set("client_assertion",`${l}.${e9(new Uint8Array(d))}`)}}(s.clientSecret);break;case"private_key_jwt":n=function(e,t){var r;let{key:a,kid:n}=(r=e)instanceof CryptoKey?{key:r}:r?.key instanceof CryptoKey?(void 0!==r.kid&&tu(r.kid,'"kid"'),{key:r.key,kid:r.kid}):{};return ta(a,'"clientPrivateKey.key"'),async(e,r,i,o)=>{let s={alg:tx(a),kid:n},c=tk(e,r);t?.[e2]?.(s,c),i.set("client_id",r.client_id),i.set("client_assertion_type","urn:ietf:params:oauth:client-assertion-type:jwt-bearer"),i.set("client_assertion",await tA(s,c,a))}}(s.token.clientPrivateKey,{[e2](e,t){t.aud=[a.issuer,a.token_endpoint]}});break;case"none":n=(e,t,r,a)=>{r.set("client_id",t.client_id)};break;default:throw Error("unsupported client authentication method")}let p=[],h=await r0.use(t,p,r);try{i=rN(a,u,new URLSearchParams(e),s.checks.includes("state")?h:rK)}catch(e){if(e instanceof tO){let t={providerId:s.id,...Object.fromEntries(e.cause.entries())};throw o.debug("OAuthCallbackError",t),new l.rk("OAuth Provider returned an error",t)}throw e}let y=await rZ.use(t,p,r),b=s.callbackUrl;!r.isOnRedirectProxy&&s.redirectProxyUrl&&(b=s.redirectProxyUrl);let x=await t7(a,u,n,i,b,y??"decoy",{[eZ]:!0,[e1]:(...e)=>(s.checks.includes("pkce")||e[1].body.delete("code_verifier"),(s[W]??fetch)(...e))});s.token?.conform&&(x=await s.token.conform(x.clone())??x);let m={},w="oidc"===s.type;if(s[D])switch(s.id){case"microsoft-entra-id":case"azure-ad":{let{tid:e}=function(e){let t,r;if("string"!=typeof e)throw new r4.Dp("JWTs must use Compact JWS serialization, JWT must be a string");let{1:a,length:n}=e.split(".");if(5===n)throw new r4.Dp("Only JWTs using Compact JWS serialization can be decoded");if(3!==n)throw new r4.Dp("Invalid JWT");if(!a)throw new r4.Dp("JWTs must contain a payload");try{t=(0,r6.D)(a)}catch{throw new r4.Dp("Failed to base64url decode the payload")}try{r=JSON.parse(r3.D0.decode(t))}catch{throw new r4.Dp("Failed to parse the decoded payload as JSON")}if(!(0,r8.A)(r))throw new r4.Dp("Invalid JWT Claims Set");return r}((await x.clone().json()).id_token);if("string"==typeof e){let t=a.issuer?.match(/microsoftonline\.com\/(\w+)\/v2\.0/)?.[1]??"common",r=new URL(a.issuer.replace(t,e)),n=await tl(r,{[e1]:s[W]});a=await tf(r,n)}}}let g=await rn(a,u,x,{expectedNonce:await r1.use(t,p,r),requireIdToken:w});if(w){let t=t0(g);if(m=t,s[D]&&"apple"===s.id)try{m.user=JSON.parse(e?.user)}catch{}if(!1===s.idToken){let e=await tM(a,u,g.access_token,{[e1]:s[W],[eZ]:!0});m=await tG(a,u,t.sub,e)}}else if(d?.request){let e=await d.request({tokens:g,provider:s});e instanceof Object&&(m=e)}else if(d?.url){let e=await tM(a,u,g.access_token,{[e1]:s[W]});m=await e.json()}else throw TypeError("No userinfo endpoint configured");return g.expires_in&&(g.expires_at=Math.floor(Date.now()/1e3)+Number(g.expires_in)),{...await ae(m,s,g,o),profile:m,cookies:p}}async function ae(e,t,r,a){try{let a=await t.profile(e,r);return{user:{...a,id:crypto.randomUUID(),email:a.email?.toLowerCase()},account:{...r,provider:t.id,type:t.type,providerAccountId:a.id??crypto.randomUUID()}}}catch(r){a.debug("getProfile error details",e),a.error(new l._z(r,{provider:t.id}))}}async function at(e,t,r,a){let n=await ao(e,t,r),{cookie:i}=await r5.create(e,n.challenge,r);return{status:200,cookies:[...a??[],i],body:{action:"register",options:n},headers:{"Content-Type":"application/json"}}}async function ar(e,t,r,a){let n=await ai(e,t,r),{cookie:i}=await r5.create(e,n.challenge);return{status:200,cookies:[...a??[],i],body:{action:"authenticate",options:n},headers:{"Content-Type":"application/json"}}}async function aa(e,t,r){let a,{adapter:n,provider:i}=e,o=t.body&&"string"==typeof t.body.data?JSON.parse(t.body.data):void 0;if(!o||"object"!=typeof o||!("id"in o)||"string"!=typeof o.id)throw new l.lR("Invalid WebAuthn Authentication response");let s=al(ac(o.id)),c=await n.getAuthenticator(s);if(!c)throw new l.lR(`WebAuthn authenticator not found in database: ${JSON.stringify({credentialID:s})}`);let{challenge:d}=await r5.use(e,t.cookies,r);try{var u;let r=i.getRelayingParty(e,t);a=await i.simpleWebAuthn.verifyAuthenticationResponse({...i.verifyAuthenticationOptions,expectedChallenge:d,response:o,authenticator:{...u=c,credentialDeviceType:u.credentialDeviceType,transports:ad(u.transports),credentialID:ac(u.credentialID),credentialPublicKey:ac(u.credentialPublicKey)},expectedOrigin:r.origin,expectedRPID:r.id})}catch(e){throw new l.w2(e)}let{verified:f,authenticationInfo:p}=a;if(!f)throw new l.w2("WebAuthn authentication response could not be verified");try{let{newCounter:e}=p;await n.updateAuthenticatorCounter(c.credentialID,e)}catch(e){throw new l.om(`Failed to update authenticator counter. This may cause future authentication attempts to fail. ${JSON.stringify({credentialID:s,oldCounter:c.counter,newCounter:p.newCounter})}`,e)}let h=await n.getAccount(c.providerAccountId,i.id);if(!h)throw new l.lR(`WebAuthn account not found in database: ${JSON.stringify({credentialID:s,providerAccountId:c.providerAccountId})}`);let y=await n.getUser(h.userId);if(!y)throw new l.lR(`WebAuthn user not found in database: ${JSON.stringify({credentialID:s,providerAccountId:c.providerAccountId,userID:h.userId})}`);return{account:h,user:y}}async function an(e,t,r){var a;let n,{provider:i}=e,o=t.body&&"string"==typeof t.body.data?JSON.parse(t.body.data):void 0;if(!o||"object"!=typeof o||!("id"in o)||"string"!=typeof o.id)throw new l.lR("Invalid WebAuthn Registration response");let{challenge:s,registerData:c}=await r5.use(e,t.cookies,r);if(!c)throw new l.lR("Missing user registration data in WebAuthn challenge cookie");try{let r=i.getRelayingParty(e,t);n=await i.simpleWebAuthn.verifyRegistrationResponse({...i.verifyRegistrationOptions,expectedChallenge:s,response:o,expectedOrigin:r.origin,expectedRPID:r.id})}catch(e){throw new l.w2(e)}if(!n.verified||!n.registrationInfo)throw new l.w2("WebAuthn registration response could not be verified");let d={providerAccountId:al(n.registrationInfo.credentialID),provider:e.provider.id,type:i.type},u={providerAccountId:d.providerAccountId,counter:n.registrationInfo.counter,credentialID:al(n.registrationInfo.credentialID),credentialPublicKey:al(n.registrationInfo.credentialPublicKey),credentialBackedUp:n.registrationInfo.credentialBackedUp,credentialDeviceType:n.registrationInfo.credentialDeviceType,transports:(a=o.response.transports,a?.join(","))};return{user:c,account:d,authenticator:u}}async function ai(e,t,r){let{provider:a,adapter:n}=e,i=r&&r.id?await n.listAuthenticatorsByUserId(r.id):null,o=a.getRelayingParty(e,t);return await a.simpleWebAuthn.generateAuthenticationOptions({...a.authenticationOptions,rpID:o.id,allowCredentials:i?.map(e=>({id:ac(e.credentialID),type:"public-key",transports:ad(e.transports)}))})}async function ao(e,t,r){let{provider:a,adapter:n}=e,i=r.id?await n.listAuthenticatorsByUserId(r.id):null,o=C(32),s=a.getRelayingParty(e,t);return await a.simpleWebAuthn.generateRegistrationOptions({...a.registrationOptions,userID:o,userName:r.email,userDisplayName:r.name??void 0,rpID:s.id,rpName:s.name,excludeCredentials:i?.map(e=>({id:ac(e.credentialID),type:"public-key",transports:ad(e.transports)}))})}function as(e){let{provider:t,adapter:r}=e;if(!r)throw new l.OZ("An adapter is required for the WebAuthn provider");if(!t||"webauthn"!==t.type)throw new l.Hj("Provider must be WebAuthn");return{...e,provider:t,adapter:r}}function ac(e){return new Uint8Array(Buffer.from(e,"base64"))}function al(e){return Buffer.from(e).toString("base64")}function ad(e){return e?e.split(","):void 0}async function au(e,t,r,a){if(!t.provider)throw new l.Hj("Callback route called without provider");let{query:n,body:i,method:o,headers:s}=e,{provider:c,adapter:d,url:u,callbackUrl:f,pages:p,jwt:h,events:y,callbacks:b,session:{strategy:x,maxAge:m},logger:w}=t,g="jwt"===x;try{if("oauth"===c.type||"oidc"===c.type){let o,s=c.authorization?.url.searchParams.get("response_mode")==="form_post"?i:n;if(t.isOnRedirectProxy&&s?.state){let e=await r0.decode(s.state,t);if(e?.origin&&new URL(e.origin).origin!==t.url.origin){let t=`${e.origin}?${new URLSearchParams(s)}`;return w.debug("Proxy redirecting to",t),{redirect:t,cookies:a}}}let l=await r7(s,e.cookies,t);l.cookies.length&&a.push(...l.cookies),w.debug("authorization result",l);let{user:x,account:_,profile:v}=l;if(!x||!_||!v)return{redirect:`${u}/signin`,cookies:a};if(d){let{getUserByAccount:e}=d;o=await e({providerAccountId:_.providerAccountId,provider:c.id})}let k=await af({user:o??x,account:_,profile:v},t);if(k)return{redirect:k,cookies:a};let{user:A,session:S,isNewUser:E}=await eV(r.value,x,_,t);if(g){let e={name:A.name,email:A.email,picture:A.image,sub:A.id?.toString()},n=await b.jwt({token:e,user:A,account:_,profile:v,isNewUser:E,trigger:E?"signUp":"signIn"});if(null===n)a.push(...r.clean());else{let e=t.cookies.sessionToken.name,i=await h.encode({...h,token:n,salt:e}),o=new Date;o.setTime(o.getTime()+1e3*m);let s=r.chunk(i,{expires:o});a.push(...s)}}else a.push({name:t.cookies.sessionToken.name,value:S.sessionToken,options:{...t.cookies.sessionToken.options,expires:S.expires}});if(await y.signIn?.({user:A,account:_,profile:v,isNewUser:E}),E&&p.newUser)return{redirect:`${p.newUser}${p.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:f})}`,cookies:a};return{redirect:f,cookies:a}}if("email"===c.type){let e=n?.token,i=n?.email;if(!e){let t=TypeError("Missing token. The sign-in URL was manually opened without token or the link was not sent correctly in the email.",{cause:{hasToken:!!e}});throw t.name="Configuration",t}let o=c.secret??t.secret,s=await d.useVerificationToken({identifier:i,token:await U(`${e}${o}`)}),u=!!s,x=u&&s.expires.valueOf()<Date.now();if(!u||x||i&&s.identifier!==i)throw new l.o6({hasInvite:u,expired:x});let{identifier:w}=s,_=await d.getUserByEmail(w)??{id:crypto.randomUUID(),email:w,emailVerified:null},v={providerAccountId:_.email,userId:_.id,type:"email",provider:c.id},k=await af({user:_,account:v},t);if(k)return{redirect:k,cookies:a};let{user:A,session:S,isNewUser:E}=await eV(r.value,_,v,t);if(g){let e={name:A.name,email:A.email,picture:A.image,sub:A.id?.toString()},n=await b.jwt({token:e,user:A,account:v,isNewUser:E,trigger:E?"signUp":"signIn"});if(null===n)a.push(...r.clean());else{let e=t.cookies.sessionToken.name,i=await h.encode({...h,token:n,salt:e}),o=new Date;o.setTime(o.getTime()+1e3*m);let s=r.chunk(i,{expires:o});a.push(...s)}}else a.push({name:t.cookies.sessionToken.name,value:S.sessionToken,options:{...t.cookies.sessionToken.options,expires:S.expires}});if(await y.signIn?.({user:A,account:v,isNewUser:E}),E&&p.newUser)return{redirect:`${p.newUser}${p.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:f})}`,cookies:a};return{redirect:f,cookies:a}}if("credentials"===c.type&&"POST"===o){let e=i??{};Object.entries(n??{}).forEach(([e,t])=>u.searchParams.set(e,t));let d=await c.authorize(e,new Request(u,{headers:s,method:o,body:JSON.stringify(i)}));if(d)d.id=d.id?.toString()??crypto.randomUUID();else throw new l.xz;let p={providerAccountId:d.id,type:"credentials",provider:c.id},x=await af({user:d,account:p,credentials:e},t);if(x)return{redirect:x,cookies:a};let w={name:d.name,email:d.email,picture:d.image,sub:d.id},g=await b.jwt({token:w,user:d,account:p,isNewUser:!1,trigger:"signIn"});if(null===g)a.push(...r.clean());else{let e=t.cookies.sessionToken.name,n=await h.encode({...h,token:g,salt:e}),i=new Date;i.setTime(i.getTime()+1e3*m);let o=r.chunk(n,{expires:i});a.push(...o)}return await y.signIn?.({user:d,account:p}),{redirect:f,cookies:a}}else if("webauthn"===c.type&&"POST"===o){let n,i,o,s=e.body?.action;if("string"!=typeof s||"authenticate"!==s&&"register"!==s)throw new l.lR("Invalid action parameter");let c=as(t);switch(s){case"authenticate":{let t=await aa(c,e,a);n=t.user,i=t.account;break}case"register":{let r=await an(t,e,a);n=r.user,i=r.account,o=r.authenticator}}await af({user:n,account:i},t);let{user:d,isNewUser:u,session:x,account:w}=await eV(r.value,n,i,t);if(!w)throw new l.lR("Error creating or finding account");if(o&&d.id&&await c.adapter.createAuthenticator({...o,userId:d.id}),g){let e={name:d.name,email:d.email,picture:d.image,sub:d.id?.toString()},n=await b.jwt({token:e,user:d,account:w,isNewUser:u,trigger:u?"signUp":"signIn"});if(null===n)a.push(...r.clean());else{let e=t.cookies.sessionToken.name,i=await h.encode({...h,token:n,salt:e}),o=new Date;o.setTime(o.getTime()+1e3*m);let s=r.chunk(i,{expires:o});a.push(...s)}}else a.push({name:t.cookies.sessionToken.name,value:x.sessionToken,options:{...t.cookies.sessionToken.options,expires:x.expires}});if(await y.signIn?.({user:d,account:w,isNewUser:u}),u&&p.newUser)return{redirect:`${p.newUser}${p.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:f})}`,cookies:a};return{redirect:f,cookies:a}}throw new l.Hj(`Callback for provider type (${c.type}) is not supported`)}catch(t){if(t instanceof l.lR)throw t;let e=new l.t3(t,{provider:c.id});throw w.debug("callback route error details",{method:o,query:n,body:i}),e}}async function af(e,t){let r,{signIn:a,redirect:n}=t.callbacks;try{r=await a(e)}catch(e){if(e instanceof l.lR)throw e;throw new l.Oy(e)}if(!r)throw new l.Oy("AccessDenied");if("string"==typeof r)return await n({url:r,baseUrl:t.url.origin})}async function ap(e,t,r,a,n){let{adapter:i,jwt:o,events:s,callbacks:c,logger:d,session:{strategy:u,maxAge:f}}=e,p={body:null,headers:{"Content-Type":"application/json"},cookies:r},h=t.value;if(!h)return p;if("jwt"===u){try{let r=e.cookies.sessionToken.name,i=await o.decode({...o,token:h,salt:r});if(!i)throw Error("Invalid JWT");let l=await c.jwt({token:i,...a&&{trigger:"update"},session:n}),d=eB(f);if(null!==l){let e={user:{name:l.name,email:l.email,image:l.picture},expires:d.toISOString()},a=await c.session({session:e,token:l});p.body=a;let n=await o.encode({...o,token:l,salt:r}),i=t.chunk(n,{expires:d});p.cookies?.push(...i),await s.session?.({session:a,token:l})}else p.cookies?.push(...t.clean())}catch(e){d.error(new l.SW(e)),p.cookies?.push(...t.clean())}return p}try{let{getSessionAndUser:r,deleteSession:o,updateSession:l}=i,d=await r(h);if(d&&d.session.expires.valueOf()<Date.now()&&(await o(h),d=null),d){let{user:t,session:r}=d,i=e.session.updateAge,o=r.expires.valueOf()-1e3*f+1e3*i,u=eB(f);o<=Date.now()&&await l({sessionToken:h,expires:u});let y=await c.session({session:{...r,user:t},user:t,newSession:n,...a?{trigger:"update"}:{}});p.body=y,p.cookies?.push({name:e.cookies.sessionToken.name,value:h,options:{...e.cookies.sessionToken.options,expires:u}}),await s.session?.({session:y})}else h&&p.cookies?.push(...t.clean())}catch(e){d.error(new l.QU(e))}return p}async function ah(e,t){let r,a,{logger:n,provider:i}=t,o=i.authorization?.url;if(!o||"authjs.dev"===o.host){let e=new URL(i.issuer),t=await tl(e,{[e1]:i[W],[eZ]:!0}),r=await tf(e,t);if(!r.authorization_endpoint)throw TypeError("Authorization server did not provide an authorization endpoint.");o=new URL(r.authorization_endpoint)}let s=o.searchParams,c=i.callbackUrl;!t.isOnRedirectProxy&&i.redirectProxyUrl&&(c=i.redirectProxyUrl,a=i.callbackUrl,n.debug("using redirect proxy",{redirect_uri:c,data:a}));let l=Object.assign({response_type:"code",client_id:i.clientId,redirect_uri:c,...i.authorization?.params},Object.fromEntries(i.authorization?.url.searchParams??[]),e);for(let e in l)s.set(e,l[e]);let d=[];i.authorization?.url.searchParams.get("response_mode")==="form_post"&&(t.cookies.state.options.sameSite="none",t.cookies.state.options.secure=!0,t.cookies.nonce.options.sameSite="none",t.cookies.nonce.options.secure=!0);let u=await r0.create(t,a);if(u&&(s.set("state",u.value),d.push(u.cookie)),i.checks?.includes("pkce"))if(r&&!r.code_challenge_methods_supported?.includes("S256"))"oidc"===i.type&&(i.checks=["nonce"]);else{let{value:e,cookie:r}=await rZ.create(t);s.set("code_challenge",e),s.set("code_challenge_method","S256"),d.push(r)}let f=await r1.create(t);return f&&(s.set("nonce",f.value),d.push(f.cookie)),"oidc"!==i.type||o.searchParams.has("scope")||o.searchParams.set("scope","openid profile email"),n.debug("authorization url is ready",{url:o,cookies:d,provider:i}),{redirect:o.toString(),cookies:d}}async function ay(e,t){let r,{body:a}=e,{provider:n,callbacks:i,adapter:o}=t,s=(n.normalizeIdentifier??function(e){if(!e)throw Error("Missing email from request body.");let[t,r]=e.toLowerCase().trim().split("@");return r=r.split(",")[0],`${t}@${r}`})(a?.email),c={id:crypto.randomUUID(),email:s,emailVerified:null},d=await o.getUserByEmail(s)??c,u={providerAccountId:s,userId:d.id,type:"email",provider:n.id};try{r=await i.signIn({user:d,account:u,email:{verificationRequest:!0}})}catch(e){throw new l.Oy(e)}if(!r)throw new l.Oy("AccessDenied");if("string"==typeof r)return{redirect:await i.redirect({url:r,baseUrl:t.url.origin})};let{callbackUrl:f,theme:p}=t,h=await n.generateVerificationToken?.()??C(32),y=new Date(Date.now()+(n.maxAge??86400)*1e3),b=n.secret??t.secret,x=new URL(t.basePath,t.url.origin),m=n.sendVerificationRequest({identifier:s,token:h,expires:y,url:`${x}/callback/${n.id}?${new URLSearchParams({callbackUrl:f,token:h,email:s})}`,provider:n,theme:p,request:new Request(e.url,{headers:e.headers,method:e.method,body:"POST"===e.method?JSON.stringify(e.body??{}):void 0})}),w=o.createVerificationToken?.({identifier:s,token:await U(`${h}${b}`),expires:y});return await Promise.all([m,w]),{redirect:`${x}/verify-request?${new URLSearchParams({provider:n.id,type:n.type})}`}}async function ab(e,t,r){let a=`${r.url.origin}${r.basePath}/signin`;if(!r.provider)return{redirect:a,cookies:t};switch(r.provider.type){case"oauth":case"oidc":{let{redirect:a,cookies:n}=await ah(e.query,r);return n&&t.push(...n),{redirect:a,cookies:t}}case"email":return{...await ay(e,r),cookies:t};default:return{redirect:a,cookies:t}}}async function ax(e,t,r){let{jwt:a,events:n,callbackUrl:i,logger:o,session:s}=r,c=t.value;if(!c)return{redirect:i,cookies:e};try{if("jwt"===s.strategy){let e=r.cookies.sessionToken.name,t=await a.decode({...a,token:c,salt:e});await n.signOut?.({token:t})}else{let e=await r.adapter?.deleteSession(c);await n.signOut?.({session:e})}}catch(e){o.error(new l.eH(e))}return e.push(...t.clean()),{redirect:i,cookies:e}}async function am(e,t){let{adapter:r,jwt:a,session:{strategy:n}}=e,i=t.value;if(!i)return null;if("jwt"===n){let t=e.cookies.sessionToken.name,r=await a.decode({...a,token:i,salt:t});if(r&&r.sub)return{id:r.sub,name:r.name,email:r.email,image:r.picture}}else{let e=await r?.getSessionAndUser(i);if(e)return e.user}return null}async function aw(e,t,r,a){let n=as(t),{provider:i}=n,{action:o}=e.query??{};if("register"!==o&&"authenticate"!==o&&void 0!==o)return{status:400,body:{error:"Invalid action"},cookies:a,headers:{"Content-Type":"application/json"}};let s=await am(t,r),c=s?{user:s,exists:!0}:await i.getUserInfo(t,e),l=c?.user;switch(function(e,t,r){let{user:a,exists:n=!1}=r??{};switch(e){case"authenticate":return"authenticate";case"register":if(a&&t===n)return"register";break;case void 0:if(!t)if(!a)return"authenticate";else if(n)return"authenticate";else return"register"}return null}(o,!!s,c)){case"authenticate":return ar(n,e,l,a);case"register":if("string"==typeof l?.email)return at(n,e,l,a);break;default:return{status:400,body:{error:"Invalid request"},cookies:a,headers:{"Content-Type":"application/json"}}}}async function ag(e,t){let{action:r,providerId:a,error:n,method:i}=e,o=t.skipCSRFCheck===H,{options:s,cookies:d}=await F({authOptions:t,action:r,providerId:a,url:e.url,callbackUrl:e.body?.callbackUrl??e.query?.callbackUrl,csrfToken:e.body?.csrfToken,cookies:e.cookies,isPost:"POST"===i,csrfDisabled:o}),u=new c.c(s.cookies.sessionToken,e.cookies,s.logger);if("GET"===i){let t=ez({...s,query:e.query,cookies:d});switch(r){case"callback":return await au(e,s,u,d);case"csrf":return t.csrf(o,s,d);case"error":return t.error(n);case"providers":return t.providers(s.providers);case"session":return await ap(s,u,d);case"signin":return t.signin(a,n);case"signout":return t.signout();case"verify-request":return t.verifyRequest();case"webauthn-options":return await aw(e,s,u,d)}}else{let{csrfTokenVerified:t}=s;switch(r){case"callback":return"credentials"===s.provider.type&&O(r,t),await au(e,s,u,d);case"session":return O(r,t),await ap(s,u,d,!0,e.body?.data);case"signin":return O(r,t),await ab(e,d,s);case"signout":return O(r,t),await ax(d,u,s)}}throw new l.P8(`Cannot handle action: ${r}`)}function a_(e,t,r,a,n){let i,o=n?.basePath,s=a.AUTH_URL??a.NEXTAUTH_URL;if(s)i=new URL(s),o&&"/"!==o&&"/"!==i.pathname&&(i.pathname!==o&&A(n).warn("env-url-basepath-mismatch"),i.pathname="/");else{let e=r.get("x-forwarded-host")??r.get("host"),a=r.get("x-forwarded-proto")??t??"https",n=a.endsWith(":")?a:a+":";i=new URL(`${n}//${e}`)}let c=i.toString().replace(/\/$/,"");if(o){let t=o?.replace(/(^\/|\/$)/g,"")??"";return new URL(`${c}/${t}/${e}`)}return new URL(`${c}/${e}`)}async function av(e,t){let r=A(t),a=await T(e,t);if(!a)return Response.json("Bad request.",{status:400});let n=function(e,t){let{url:r}=e,a=[];if(!d&&t.debug&&a.push("debug-enabled"),!t.trustHost)return new l.tP(`Host must be trusted. URL was: ${e.url}`);if(!t.secret?.length)return new l.jo("Please define a `secret`");let n=e.query?.callbackUrl;if(n&&!u(n,r.origin))return new l.me(`Invalid callback URL. Received: ${n}`);let{callbackUrl:i}=(0,c.X)(t.useSecureCookies??"https:"===r.protocol),o=e.cookies?.[t.cookies?.callbackUrl?.name??i.name];if(o&&!u(o,r.origin))return new l.me(`Invalid callback URL. Received: ${o}`);let s=!1;for(let e of t.providers){let t="function"==typeof e?e():e;if(("oauth"===t.type||"oidc"===t.type)&&!(t.issuer??t.options?.issuer)){let e,{authorization:r,token:a,userinfo:n}=t;if("string"==typeof r||r?.url?"string"==typeof a||a?.url?"string"==typeof n||n?.url||(e="userinfo"):e="token":e="authorization",e)return new l.gs(`Provider "${t.id}" is missing both \`issuer\` and \`${e}\` endpoint config. At least one of them is required`)}if("credentials"===t.type)f=!0;else if("email"===t.type)p=!0;else if("webauthn"===t.type){var m;if(h=!0,t.simpleWebAuthnBrowserVersion&&(m=t.simpleWebAuthnBrowserVersion,!/^v\d+(?:\.\d+){0,2}$/.test(m)))return new l.lR(`Invalid provider config for "${t.id}": simpleWebAuthnBrowserVersion "${t.simpleWebAuthnBrowserVersion}" must be a valid semver string.`);if(t.enableConditionalUI){if(s)return new l.s5("Multiple webauthn providers have 'enableConditionalUI' set to True. Only one provider can have this option enabled at a time");if(s=!0,!Object.values(t.formFields).some(e=>e.autocomplete&&e.autocomplete.toString().indexOf("webauthn")>-1))return new l.nd(`Provider "${t.id}" has 'enableConditionalUI' set to True, but none of its formFields have 'webauthn' in their autocomplete param`)}}}if(f){let e=t.session?.strategy==="database",r=!t.providers.some(e=>"credentials"!==("function"==typeof e?e():e).type);if(e&&r)return new l.Lx("Signing in with credentials only supported if JWT strategy is enabled");if(t.providers.some(e=>{let t="function"==typeof e?e():e;return"credentials"===t.type&&!t.authorize}))return new l.u$("Must define an authorize() handler to use credentials authentication provider")}let{adapter:w,session:g}=t,_=[];if(p||g?.strategy==="database"||!g?.strategy&&w)if(p){if(!w)return new l.OZ("Email login requires an adapter");_.push(...y)}else{if(!w)return new l.OZ("Database session requires an adapter");_.push(...b)}if(h){if(!t.experimental?.enableWebAuthn)return new l.xm("WebAuthn is an experimental feature. To enable it, set `experimental.enableWebAuthn` to `true` in your config");if(a.push("experimental-webauthn"),!w)return new l.OZ("WebAuthn requires an adapter");_.push(...x)}if(w){let e=_.filter(e=>!(e in w));if(e.length)return new l.i8(`Required adapter methods were missing: ${e.join(", ")}`)}return d||(d=!0),a}(a,t);if(Array.isArray(n))n.forEach(r.warn);else if(n){if(r.error(n),!new Set(["signin","signout","error","verify-request"]).has(a.action)||"GET"!==a.method)return Response.json({message:"There was a problem with the server configuration. Check the server logs for more information."},{status:500});let{pages:e,theme:i}=t,o=e?.error&&a.url.searchParams.get("callbackUrl")?.startsWith(e.error);if(!e?.error||o)return o&&r.error(new l.CM(`The error page ${e?.error} should not require authentication`)),P(ez({theme:i}).error("Configuration"));let s=`${a.url.origin}${e.error}?error=Configuration`;return Response.redirect(s)}let i=e.headers?.has("X-Auth-Return-Redirect"),o=t.raw===j;try{let e=await ag(a,t);if(o)return e;let r=P(e),n=r.headers.get("Location");if(!i||!n)return r;return Response.json({url:n},{headers:r.headers})}catch(f){r.error(f);let n=f instanceof l.lR;if(n&&o&&!i)throw f;if("POST"===e.method&&"session"===a.action)return Response.json(null,{status:400});let s=new URLSearchParams({error:(0,l._2)(f)?f.type:"Configuration"});f instanceof l.xz&&s.set("code",f.code);let c=n&&f.kind||"error",d=t.pages?.[c]??`${t.basePath}/${c.toLowerCase()}`,u=`${a.url.origin}${d}?${s}`;if(i)return Response.json({url:u});return Response.redirect(u)}}var ak=r(4235);function aA(e){let t=process.env.AUTH_URL??process.env.NEXTAUTH_URL;if(!t)return e;let{origin:r}=new URL(t),{href:a,origin:n}=e.nextUrl;return new ak.NextRequest(a.replace(n,r),e)}function aS(e){try{e.secret??(e.secret=process.env.AUTH_SECRET??process.env.NEXTAUTH_SECRET);let t=process.env.AUTH_URL??process.env.NEXTAUTH_URL;if(!t)return;let{pathname:r}=new URL(t);if("/"===r)return;e.basePath||(e.basePath=r)}catch{}finally{e.basePath||(e.basePath="/api/auth"),function(e,t,r=!1){try{let a=e.AUTH_URL;a&&(t.basePath?r||A(t).warn("env-url-basepath-redundant"):t.basePath=new URL(a).pathname)}catch{}finally{t.basePath??(t.basePath="/auth")}if(!t.secret?.length){t.secret=[];let r=e.AUTH_SECRET;for(let a of(r&&t.secret.push(r),[1,2,3])){let r=e[`AUTH_SECRET_${a}`];r&&t.secret.unshift(r)}}t.redirectProxyUrl??(t.redirectProxyUrl=e.AUTH_REDIRECT_PROXY_URL),t.trustHost??(t.trustHost=!!(e.AUTH_URL??e.AUTH_TRUST_HOST??e.VERCEL??e.CF_PAGES??"production"!==e.NODE_ENV)),t.providers=t.providers.map(t=>{let{id:r}="function"==typeof t?t({}):t,a=r.toUpperCase().replace(/-/g,"_"),n=e[`AUTH_${a}_ID`],i=e[`AUTH_${a}_SECRET`],o=e[`AUTH_${a}_ISSUER`],s=e[`AUTH_${a}_KEY`],c="function"==typeof t?t({clientId:n,clientSecret:i,issuer:o,apiKey:s}):t;return"oauth"===c.type||"oidc"===c.type?(c.clientId??(c.clientId=n),c.clientSecret??(c.clientSecret=i),c.issuer??(c.issuer=o)):"email"===c.type&&(c.apiKey??(c.apiKey=s)),c})}(process.env,e,!0)}}var aE=r(65208);async function aT(e,t){return av(new Request(a_("session",e.get("x-forwarded-proto"),e,process.env,t),{headers:{cookie:e.get("cookie")??""}}),{...t,callbacks:{...t.callbacks,async session(...e){let r=await t.callbacks?.session?.(...e)??{...e[0].session,expires:e[0].session.expires?.toISOString?.()??e[0].session.expires};return{user:e[0].user??e[0].token,...r}}}})}function aP(e){return"function"==typeof e}function aU(e,t){return"function"==typeof e?async(...r)=>{if(!r.length){let r=await (0,aE.headers)(),a=await e(void 0);return t?.(a),aT(r,a).then(e=>e.json())}if(r[0]instanceof Request){let a=r[0],n=r[1],i=await e(a);return t?.(i),aC([a,n],i)}if(aP(r[0])){let a=r[0];return async(...r)=>{let n=await e(r[0]);return t?.(n),aC(r,n,a)}}let a="req"in r[0]?r[0].req:r[0],n="res"in r[0]?r[0].res:r[1],i=await e(a);return t?.(i),aT(new Headers(a.headers),i).then(async e=>{let t=await e.json();for(let t of e.headers.getSetCookie())"headers"in n?n.headers.append("set-cookie",t):n.appendHeader("set-cookie",t);return t})}:(...t)=>{if(!t.length)return Promise.resolve((0,aE.headers)()).then(t=>aT(t,e).then(e=>e.json()));if(t[0]instanceof Request)return aC([t[0],t[1]],e);if(aP(t[0])){let r=t[0];return async(...t)=>aC(t,e,r).then(e=>e)}let r="req"in t[0]?t[0].req:t[0],a="res"in t[0]?t[0].res:t[1];return aT(new Headers(r.headers),e).then(async e=>{let t=await e.json();for(let t of e.headers.getSetCookie())"headers"in a?a.headers.append("set-cookie",t):a.appendHeader("set-cookie",t);return t})}}async function aC(e,t,r){let a=aA(e[0]),n=await aT(a.headers,t),i=await n.json(),o=!0;t.callbacks?.authorized&&(o=await t.callbacks.authorized({request:a,auth:i}));let s=ak.NextResponse.next?.();if(o instanceof Response){s=o;let e=o.headers.get("Location"),{pathname:r}=a.nextUrl;e&&function(e,t,r){let a=t.replace(`${e}/`,""),n=Object.values(r.pages??{});return(aR.has(a)||n.includes(t))&&t===e}(r,new URL(e).pathname,t)&&(o=!0)}else if(r)a.auth=i,s=await r(a,e[1])??ak.NextResponse.next();else if(!o){let e=t.pages?.signIn??`${t.basePath}/signin`;if(a.nextUrl.pathname!==e){let t=a.nextUrl.clone();t.pathname=e,t.searchParams.set("callbackUrl",a.nextUrl.href),s=ak.NextResponse.redirect(t)}}let c=new Response(s?.body,s);for(let e of n.headers.getSetCookie())c.headers.append("set-cookie",e);return c}let aR=new Set(["providers","session","csrf","signin","signout","callback","verify-request","error"]);var aO=r(42543);async function aI(e,t={},r,a){let n=new Headers(await (0,aE.headers)()),{redirect:i=!0,redirectTo:o,...s}=t instanceof FormData?Object.fromEntries(t):t,c=o?.toString()??n.get("Referer")??"/",l=a_("signin",n.get("x-forwarded-proto"),n,process.env,a);if(!e)return l.searchParams.append("callbackUrl",c),i&&(0,aO.redirect)(l.toString()),l.toString();let d=`${l}/${e}?${new URLSearchParams(r)}`,u={};for(let t of a.providers){let{options:r,...a}="function"==typeof t?t():t,n=r?.id??a.id;if(n===e){u={id:n,type:r?.type??a.type};break}}if(!u.id){let e=`${l}?${new URLSearchParams({callbackUrl:c})}`;return i&&(0,aO.redirect)(e),e}"credentials"===u.type&&(d=d.replace("signin","callback")),n.set("Content-Type","application/x-www-form-urlencoded");let f=new Request(d,{method:"POST",headers:n,body:new URLSearchParams({...s,callbackUrl:c})}),p=await av(f,{...a,raw:j,skipCSRFCheck:H}),h=await (0,aE.UL)();for(let e of p?.cookies??[])h.set(e.name,e.value,e.options);let y=(p instanceof Response?p.headers.get("Location"):p.redirect)??d;return i?(0,aO.redirect)(y):y}async function a$(e,t){let r=new Headers(await (0,aE.headers)());r.set("Content-Type","application/x-www-form-urlencoded");let a=a_("signout",r.get("x-forwarded-proto"),r,process.env,t),n=new URLSearchParams({callbackUrl:e?.redirectTo??r.get("Referer")??"/"}),i=new Request(a,{method:"POST",headers:r,body:n}),o=await av(i,{...t,raw:j,skipCSRFCheck:H}),s=await (0,aE.UL)();for(let e of o?.cookies??[])s.set(e.name,e.value,e.options);return e?.redirect??!0?(0,aO.redirect)(o.redirect):o}async function aH(e,t){let r=new Headers(await (0,aE.headers)());r.set("Content-Type","application/json");let a=new Request(a_("session",r.get("x-forwarded-proto"),r,process.env,t),{method:"POST",headers:r,body:JSON.stringify({data:e})}),n=await av(a,{...t,raw:j,skipCSRFCheck:H}),i=await (0,aE.UL)();for(let e of n?.cookies??[])i.set(e.name,e.value,e.options);return n.body}function aj(e){if("function"==typeof e){let t=async t=>{let r=await e(t);return aS(r),av(aA(t),r)};return{handlers:{GET:t,POST:t},auth:aU(e,e=>aS(e)),signIn:async(t,r,a)=>{let n=await e(void 0);return aS(n),aI(t,r,a,n)},signOut:async t=>{let r=await e(void 0);return aS(r),a$(t,r)},unstable_update:async t=>{let r=await e(void 0);return aS(r),aH(t,r)}}}aS(e);let t=t=>av(aA(t),e);return{handlers:{GET:t,POST:t},auth:aU(e),signIn:(t,r,a)=>aI(t,r,a,e),signOut:t=>a$(t,e),unstable_update:t=>aH(t,e)}}},58319:(e,t)=>{t.q=function(e,t){if("string"!=typeof e)throw TypeError("argument str must be a string");var r={},a=e.length;if(a<2)return r;var n=t&&t.decode||l,i=0,o=0,d=0;do{if(-1===(o=e.indexOf("=",i)))break;if(-1===(d=e.indexOf(";",i)))d=a;else if(o>d){i=e.lastIndexOf(";",o-1)+1;continue}var u=s(e,i,o),f=c(e,o,u),p=e.slice(u,f);if(!r.hasOwnProperty(p)){var h=s(e,o+1,d),y=c(e,d,h);34===e.charCodeAt(h)&&34===e.charCodeAt(y-1)&&(h++,y--);var b=e.slice(h,y);r[p]=function(e,t){try{return t(e)}catch(t){return e}}(b,n)}i=d+1}while(i<a);return r},t.l=function(e,t,s){var c=s&&s.encode||encodeURIComponent;if("function"!=typeof c)throw TypeError("option encode is invalid");if(!a.test(e))throw TypeError("argument name is invalid");var l=c(t);if(!n.test(l))throw TypeError("argument val is invalid");var d=e+"="+l;if(!s)return d;if(null!=s.maxAge){var u=Math.floor(s.maxAge);if(!isFinite(u))throw TypeError("option maxAge is invalid");d+="; Max-Age="+u}if(s.domain){if(!i.test(s.domain))throw TypeError("option domain is invalid");d+="; Domain="+s.domain}if(s.path){if(!o.test(s.path))throw TypeError("option path is invalid");d+="; Path="+s.path}if(s.expires){var f,p=s.expires;if(f=p,"[object Date]"!==r.call(f)||isNaN(p.valueOf()))throw TypeError("option expires is invalid");d+="; Expires="+p.toUTCString()}if(s.httpOnly&&(d+="; HttpOnly"),s.secure&&(d+="; Secure"),s.partitioned&&(d+="; Partitioned"),s.priority)switch("string"==typeof s.priority?s.priority.toLowerCase():s.priority){case"low":d+="; Priority=Low";break;case"medium":d+="; Priority=Medium";break;case"high":d+="; Priority=High";break;default:throw TypeError("option priority is invalid")}if(s.sameSite)switch("string"==typeof s.sameSite?s.sameSite.toLowerCase():s.sameSite){case!0:case"strict":d+="; SameSite=Strict";break;case"lax":d+="; SameSite=Lax";break;case"none":d+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return d};var r=Object.prototype.toString,a=/^[!#$%&'*+\-.^_`|~0-9A-Za-z]+$/,n=/^("?)[\u0021\u0023-\u002B\u002D-\u003A\u003C-\u005B\u005D-\u007E]*\1$/,i=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,o=/^[\u0020-\u003A\u003D-\u007E]*$/;function s(e,t,r){do{var a=e.charCodeAt(t);if(32!==a&&9!==a)return t}while(++t<r);return r}function c(e,t,r){for(;t>r;){var a=e.charCodeAt(--t);if(32!==a&&9!==a)return t+1}return r}function l(e){return -1!==e.indexOf("%")?decodeURIComponent(e):e}},63418:(e,t,r)=>{let a,n;r.d(t,{D4:()=>eZ,lF:()=>eY,gf:()=>eQ});var i=r(55511);let o=(e,t,r,a,n)=>{let o=parseInt(e.substr(3),10)>>3||20,s=(0,i.createHmac)(e,r.byteLength?r:new Uint8Array(o)).update(t).digest(),c=Math.ceil(n/o),l=new Uint8Array(o*c+a.byteLength+1),d=0,u=0;for(let t=1;t<=c;t++)l.set(a,u),l[u+a.byteLength]=t,l.set((0,i.createHmac)(e,s).update(l.subarray(d,u+a.byteLength+1)).digest(),u),d=u,u+=o;return l.slice(0,n)};"function"!=typeof i.hkdf||process.versions.electron||(a=async(...e)=>new Promise((t,r)=>{i.hkdf(...e,(e,a)=>{e?r(e):t(new Uint8Array(a))})}));let s=async(e,t,r,n,i)=>(a||o)(e,t,r,n,i);function c(e,t){if("string"==typeof e)return new TextEncoder().encode(e);if(!(e instanceof Uint8Array))throw TypeError(`"${t}"" must be an instance of Uint8Array or a string`);return e}async function l(e,t,r,a,n){return s(function(e){switch(e){case"sha256":case"sha384":case"sha512":case"sha1":return e;default:throw TypeError('unsupported "digest" value')}}(e),function(e){let t=c(e,"ikm");if(!t.byteLength)throw TypeError('"ikm" must be at least one byte in length');return t}(t),c(r,"salt"),function(e){let t=c(e,"info");if(t.byteLength>1024)throw TypeError('"info" must not contain more than 1024 bytes');return t}(a),function(e,t){if("number"!=typeof e||!Number.isInteger(e)||e<1)throw TypeError('"keylen" must be a positive integer');if(e>255*(parseInt(t.substr(3),10)>>3||20))throw TypeError('"keylen" too large');return e}(n,e))}var d=r(90337),u=r(30005),f=r(63748),p=r(36448),h=r(82896);let y=(e,t)=>{if("string"!=typeof e||!e)throw new f._L(`${t} missing or invalid`)};async function b(e,t){let r;if(!(0,h.A)(e))throw TypeError("JWK must be an object");if("sha256"!==(t??="sha256")&&"sha384"!==t&&"sha512"!==t)throw TypeError('digestAlgorithm must one of "sha256", "sha384", or "sha512"');switch(e.kty){case"EC":y(e.crv,'"crv" (Curve) Parameter'),y(e.x,'"x" (X Coordinate) Parameter'),y(e.y,'"y" (Y Coordinate) Parameter'),r={crv:e.crv,kty:e.kty,x:e.x,y:e.y};break;case"OKP":y(e.crv,'"crv" (Subtype of Key Pair) Parameter'),y(e.x,'"x" (Public Key) Parameter'),r={crv:e.crv,kty:e.kty,x:e.x};break;case"RSA":y(e.e,'"e" (Exponent) Parameter'),y(e.n,'"n" (Modulus) Parameter'),r={e:e.e,kty:e.kty,n:e.n};break;case"oct":y(e.k,'"k" (Key Value) Parameter'),r={k:e.k,kty:e.kty};break;default:throw new f.T0('"kty" (Key Type) Parameter missing or unsupported')}let a=p.Rd.encode(JSON.stringify(r));return(0,u.lF)(await (0,d.A)(t,a))}var x=r(74771);let m=Symbol();var w=r(77598);function g(e){switch(e){case"A128GCM":case"A128GCMKW":case"A192GCM":case"A192GCMKW":case"A256GCM":case"A256GCMKW":return 96;case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return 128;default:throw new f.T0(`Unsupported JWE Algorithm: ${e}`)}}let _=e=>(0,w.randomFillSync)(new Uint8Array(g(e)>>3)),v=(e,t)=>{if(t.length<<3!==g(e))throw new f.aA("Invalid Initialization Vector length")};var k=r(57975);let A=e=>k.types.isKeyObject(e),S=(e,t)=>{let r;switch(e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":r=parseInt(e.slice(-3),10);break;case"A128GCM":case"A192GCM":case"A256GCM":r=parseInt(e.slice(1,4),10);break;default:throw new f.T0(`Content Encryption Algorithm ${e} is not supported either by JOSE or your javascript runtime`)}if(t instanceof Uint8Array){let e=t.byteLength<<3;if(e!==r)throw new f.aA(`Invalid Content Encryption Key length. Expected ${r} bits, got ${e} bits`);return}if(A(t)&&"secret"===t.type){let e=t.symmetricKeySize<<3;if(e!==r)throw new f.aA(`Invalid Content Encryption Key length. Expected ${r} bits, got ${e} bits`);return}throw TypeError("Invalid Content Encryption Key type")};function E(e,t,r,a,n,i){let o=(0,p.xW)(e,t,r,(0,p.mx)(e.length<<3)),s=(0,w.createHmac)(`sha${a}`,n);return s.update(o),s.digest().slice(0,i>>3)}let T=w.webcrypto,P=e=>k.types.isCryptoKey(e);function U(e,t="algorithm.name"){return TypeError(`CryptoKey does not support this operation, its ${t} must be ${e}`)}function C(e,t){return e.name===t}function R(e,t,...r){switch(t){case"A128GCM":case"A192GCM":case"A256GCM":{if(!C(e.algorithm,"AES-GCM"))throw U("AES-GCM");let r=parseInt(t.slice(1,4),10);if(e.algorithm.length!==r)throw U(r,"algorithm.length");break}case"A128KW":case"A192KW":case"A256KW":{if(!C(e.algorithm,"AES-KW"))throw U("AES-KW");let r=parseInt(t.slice(1,4),10);if(e.algorithm.length!==r)throw U(r,"algorithm.length");break}case"ECDH":switch(e.algorithm.name){case"ECDH":case"X25519":case"X448":break;default:throw U("ECDH, X25519, or X448")}break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":if(!C(e.algorithm,"PBKDF2"))throw U("PBKDF2");break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":{if(!C(e.algorithm,"RSA-OAEP"))throw U("RSA-OAEP");let r=parseInt(t.slice(9),10)||1;if(parseInt(e.algorithm.hash.name.slice(4),10)!==r)throw U(`SHA-${r}`,"algorithm.hash");break}default:throw TypeError("CryptoKey does not support this operation")}var a=e,n=r;if(n.length&&!n.some(e=>a.usages.includes(e))){let e="CryptoKey does not support this operation, its usages must include ";if(n.length>2){let t=n.pop();e+=`one of ${n.join(", ")}, or ${t}.`}else 2===n.length?e+=`one of ${n[0]} or ${n[1]}.`:e+=`${n[0]}.`;throw TypeError(e)}}function O(e,t,...r){if((r=r.filter(Boolean)).length>2){let t=r.pop();e+=`one of type ${r.join(", ")}, or ${t}.`}else 2===r.length?e+=`one of type ${r[0]} or ${r[1]}.`:e+=`of type ${r[0]}.`;return null==t?e+=` Received ${t}`:"function"==typeof t&&t.name?e+=` Received function ${t.name}`:"object"==typeof t&&null!=t&&t.constructor?.name&&(e+=` Received an instance of ${t.constructor.name}`),e}let I=(e,...t)=>O("Key must be ",e,...t);function $(e,t,...r){return O(`Key for the ${e} algorithm must be `,t,...r)}let H=e=>(n||=new Set((0,w.getCiphers)())).has(e),j=e=>A(e)||P(e),W=["KeyObject"];(globalThis.CryptoKey||T?.CryptoKey)&&W.push("CryptoKey");let D=(e,t,r,a,n)=>{let i;if(P(r))R(r,e,"encrypt"),i=w.KeyObject.from(r);else if(r instanceof Uint8Array||A(r))i=r;else throw TypeError(I(r,...W,"Uint8Array"));switch(S(e,i),a?v(e,a):a=_(e),e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return function(e,t,r,a,n){let i=parseInt(e.slice(1,4),10);A(r)&&(r=r.export());let o=r.subarray(i>>3),s=r.subarray(0,i>>3),c=`aes-${i}-cbc`;if(!H(c))throw new f.T0(`alg ${e} is not supported by your javascript runtime`);let l=(0,w.createCipheriv)(c,o,a),d=(0,p.xW)(l.update(t),l.final()),u=E(n,a,d,parseInt(e.slice(-3),10),s,i);return{ciphertext:d,tag:u,iv:a}}(e,t,i,a,n);case"A128GCM":case"A192GCM":case"A256GCM":return function(e,t,r,a,n){let i=parseInt(e.slice(1,4),10),o=`aes-${i}-gcm`;if(!H(o))throw new f.T0(`alg ${e} is not supported by your javascript runtime`);let s=(0,w.createCipheriv)(o,r,a,{authTagLength:16});n.byteLength&&s.setAAD(n,{plaintextLength:t.length});let c=s.update(t);return s.final(),{ciphertext:c,tag:s.getAuthTag(),iv:a}}(e,t,i,a,n);default:throw new f.T0("Unsupported JWE Content Encryption Algorithm")}};var L=r(4573);function K(e,t){if(e.symmetricKeySize<<3!==parseInt(t.slice(1,4),10))throw TypeError(`Invalid key size for alg: ${t}`)}function J(e,t,r){if(A(e))return e;if(e instanceof Uint8Array)return(0,w.createSecretKey)(e);if(P(e))return R(e,t,r),w.KeyObject.from(e);throw TypeError(I(e,...W,"Uint8Array"))}let N=(e,t,r)=>{let a=parseInt(e.slice(1,4),10),n=`aes${a}-wrap`;if(!H(n))throw new f.T0(`alg ${e} is not supported either by JOSE or your javascript runtime`);let i=J(t,e,"wrapKey");K(i,e);let o=(0,w.createCipheriv)(n,i,L.Buffer.alloc(8,166));return(0,p.xW)(o.update(r),o.final())},M=(e,t,r)=>{let a=parseInt(e.slice(1,4),10),n=`aes${a}-wrap`;if(!H(n))throw new f.T0(`alg ${e} is not supported either by JOSE or your javascript runtime`);let i=J(t,e,"unwrapKey");K(i,e);let o=(0,w.createDecipheriv)(n,i,L.Buffer.alloc(8,166));return(0,p.xW)(o.update(r),o.final())};function F(e){return(0,h.A)(e)&&"string"==typeof e.kty}new WeakMap;let z=e=>{switch(e){case"prime256v1":return"P-256";case"secp384r1":return"P-384";case"secp521r1":return"P-521";case"secp256k1":return"secp256k1";default:throw new f.T0("Unsupported key curve for this operation")}},B=(e,t)=>{let r;if(P(e))r=w.KeyObject.from(e);else if(A(e))r=e;else if(F(e))return e.crv;else throw TypeError(I(e,...W));if("secret"===r.type)throw TypeError('only "private" or "public" type keys can be used for this operation');switch(r.asymmetricKeyType){case"ed25519":case"ed448":return`Ed${r.asymmetricKeyType.slice(2)}`;case"x25519":case"x448":return`X${r.asymmetricKeyType.slice(1)}`;case"ec":{let e=r.asymmetricKeyDetails.namedCurve;if(t)return e;return z(e)}default:throw TypeError("Invalid asymmetric key type for this operation")}},V=(0,k.promisify)(w.generateKeyPair);async function q(e,t,r,a,n=new Uint8Array(0),i=new Uint8Array(0)){let o,s;if(P(e))R(e,"ECDH"),o=w.KeyObject.from(e);else if(A(e))o=e;else throw TypeError(I(e,...W));if(P(t))R(t,"ECDH","deriveBits"),s=w.KeyObject.from(t);else if(A(t))s=t;else throw TypeError(I(t,...W));let c=(0,p.xW)((0,p.Kp)(p.Rd.encode(r)),(0,p.Kp)(n),(0,p.Kp)(i),(0,p.VS)(a)),l=(0,w.diffieHellman)({privateKey:s,publicKey:o});return(0,p.yI)(l,a,c)}async function G(e){let t;if(P(e))t=w.KeyObject.from(e);else if(A(e))t=e;else throw TypeError(I(e,...W));switch(t.asymmetricKeyType){case"x25519":return V("x25519");case"x448":return V("x448");case"ec":return V("ec",{namedCurve:B(t)});default:throw new f.T0("Invalid or unsupported EPK")}}let X=e=>["P-256","P-384","P-521","X25519","X448"].includes(B(e));function Y(e){if(!(e instanceof Uint8Array)||e.length<8)throw new f.aA("PBES2 Salt Input must be 8 or more octets")}let Z=(0,k.promisify)(w.pbkdf2);function Q(e,t){if(A(e))return e.export();if(e instanceof Uint8Array)return e;if(P(e))return R(e,t,"deriveBits","deriveKey"),w.KeyObject.from(e).export();throw TypeError(I(e,...W,"Uint8Array"))}let ee=async(e,t,r,a=2048,n=(0,w.randomFillSync)(new Uint8Array(16)))=>{Y(n);let i=(0,p.MT)(e,n),o=parseInt(e.slice(13,16),10)>>3,s=Q(t,e),c=await Z(s,i,a,o,`sha${e.slice(8,11)}`);return{encryptedKey:await N(e.slice(-6),c,r),p2c:a,p2s:(0,u.lF)(n)}},et=async(e,t,r,a,n)=>{Y(n);let i=(0,p.MT)(e,n),o=parseInt(e.slice(13,16),10)>>3,s=Q(t,e),c=await Z(s,i,a,o,`sha${e.slice(8,11)}`);return M(e.slice(-6),c,r)},er=(e,t)=>{let r;try{r=e instanceof w.KeyObject?e.asymmetricKeyDetails?.modulusLength:Buffer.from(e.n,"base64url").byteLength<<3}catch{}if("number"!=typeof r||r<2048)throw TypeError(`${t} requires key modulusLength to be 2048 bits or larger`)},ea=(e,t)=>{if("rsa"!==e.asymmetricKeyType)throw TypeError("Invalid key for this operation, its asymmetricKeyType must be rsa");er(e,t)},en=(0,k.deprecate)(()=>w.constants.RSA_PKCS1_PADDING,'The RSA1_5 "alg" (JWE Algorithm) is deprecated and will be removed in the next major revision.'),ei=e=>{switch(e){case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":return w.constants.RSA_PKCS1_OAEP_PADDING;case"RSA1_5":return en();default:return}},eo=e=>{switch(e){case"RSA-OAEP":return"sha1";case"RSA-OAEP-256":return"sha256";case"RSA-OAEP-384":return"sha384";case"RSA-OAEP-512":return"sha512";default:return}};function es(e,t,...r){if(A(e))return e;if(P(e))return R(e,t,...r),w.KeyObject.from(e);throw TypeError(I(e,...W))}let ec=(e,t,r)=>{let a=ei(e),n=eo(e),i=es(t,e,"wrapKey","encrypt");return ea(i,e),(0,w.publicEncrypt)({key:i,oaepHash:n,padding:a},r)},el=(e,t,r)=>{let a=ei(e),n=eo(e),i=es(t,e,"unwrapKey","decrypt");return ea(i,e),(0,w.privateDecrypt)({key:i,oaepHash:n,padding:a},r)},ed={};function eu(e){switch(e){case"A128GCM":return 128;case"A192GCM":return 192;case"A256GCM":case"A128CBC-HS256":return 256;case"A192CBC-HS384":return 384;case"A256CBC-HS512":return 512;default:throw new f.T0(`Unsupported JWE Algorithm: ${e}`)}}let ef=e=>(0,w.randomFillSync)(new Uint8Array(eu(e)>>3)),ep=e=>{let t;if(P(e)){if(!e.extractable)throw TypeError("CryptoKey is not extractable");t=w.KeyObject.from(e)}else if(A(e))t=e;else if(e instanceof Uint8Array)return{kty:"oct",k:(0,u.lF)(e)};else throw TypeError(I(e,...W,"Uint8Array"));if("secret"!==t.type&&!["rsa","ec","ed25519","x25519","ed448","x448"].includes(t.asymmetricKeyType))throw new f.T0("Unsupported key asymmetricKeyType");return t.export({format:"jwk"})};async function eh(e){return ep(e)}let ey=e=>e?.[Symbol.toStringTag],eb=(e,t,r)=>{if(void 0!==t.use&&"sig"!==t.use)throw TypeError("Invalid key for this operation, when present its use must be sig");if(void 0!==t.key_ops&&t.key_ops.includes?.(r)!==!0)throw TypeError(`Invalid key for this operation, when present its key_ops must include ${r}`);if(void 0!==t.alg&&t.alg!==e)throw TypeError(`Invalid key for this operation, when present its alg must be ${e}`);return!0},ex=(e,t,r,a)=>{if(!(t instanceof Uint8Array)){if(a&&F(t)){if(function(e){return F(e)&&"oct"===e.kty&&"string"==typeof e.k}(t)&&eb(e,t,r))return;throw TypeError('JSON Web Key for symmetric algorithms must have JWK "kty" (Key Type) equal to "oct" and the JWK "k" (Key Value) present')}if(!j(t))throw TypeError($(e,t,...W,"Uint8Array",a?"JSON Web Key":null));if("secret"!==t.type)throw TypeError(`${ey(t)} instances for symmetric algorithms must be of type "secret"`)}},em=(e,t,r,a)=>{if(a&&F(t))switch(r){case"sign":if(function(e){return"oct"!==e.kty&&"string"==typeof e.d}(t)&&eb(e,t,r))return;throw TypeError("JSON Web Key for this operation be a private JWK");case"verify":if(function(e){return"oct"!==e.kty&&void 0===e.d}(t)&&eb(e,t,r))return;throw TypeError("JSON Web Key for this operation be a public JWK")}if(!j(t))throw TypeError($(e,t,...W,a?"JSON Web Key":null));if("secret"===t.type)throw TypeError(`${ey(t)} instances for asymmetric algorithms must not be of type "secret"`);if("sign"===r&&"public"===t.type)throw TypeError(`${ey(t)} instances for asymmetric algorithm signing must be of type "private"`);if("decrypt"===r&&"public"===t.type)throw TypeError(`${ey(t)} instances for asymmetric algorithm decryption must be of type "private"`);if(t.algorithm&&"verify"===r&&"private"===t.type)throw TypeError(`${ey(t)} instances for asymmetric algorithm verifying must be of type "public"`);if(t.algorithm&&"encrypt"===r&&"private"===t.type)throw TypeError(`${ey(t)} instances for asymmetric algorithm encryption must be of type "public"`)};function ew(e,t,r,a){t.startsWith("HS")||"dir"===t||t.startsWith("PBES2")||/^A\d{3}(?:GCM)?KW$/.test(t)?ex(t,r,a,e):em(t,r,a,e)}let eg=ew.bind(void 0,!1);ew.bind(void 0,!0);let e_=w.timingSafeEqual,ev=(e,t,r,a,n,i)=>{let o;if(P(t))R(t,e,"decrypt"),o=w.KeyObject.from(t);else if(t instanceof Uint8Array||A(t))o=t;else throw TypeError(I(t,...W,"Uint8Array"));if(!a)throw new f.aA("JWE Initialization Vector missing");if(!n)throw new f.aA("JWE Authentication Tag missing");switch(S(e,o),v(e,a),e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return function(e,t,r,a,n,i){let o,s,c=parseInt(e.slice(1,4),10);A(t)&&(t=t.export());let l=t.subarray(c>>3),d=t.subarray(0,c>>3),u=parseInt(e.slice(-3),10),h=`aes-${c}-cbc`;if(!H(h))throw new f.T0(`alg ${e} is not supported by your javascript runtime`);let y=E(i,a,r,u,d,c);try{o=e_(n,y)}catch{}if(!o)throw new f.xO;try{let e=(0,w.createDecipheriv)(h,l,a);s=(0,p.xW)(e.update(r),e.final())}catch{}if(!s)throw new f.xO;return s}(e,o,r,a,n,i);case"A128GCM":case"A192GCM":case"A256GCM":return function(e,t,r,a,n,i){let o=parseInt(e.slice(1,4),10),s=`aes-${o}-gcm`;if(!H(s))throw new f.T0(`alg ${e} is not supported by your javascript runtime`);try{let e=(0,w.createDecipheriv)(s,t,a,{authTagLength:16});e.setAuthTag(n),i.byteLength&&e.setAAD(i,{plaintextLength:r.length});let o=e.update(r);return e.final(),o}catch{throw new f.xO}}(e,o,r,a,n,i);default:throw new f.T0("Unsupported JWE Content Encryption Algorithm")}};async function ek(e,t,r,a){let n=e.slice(0,7),i=await D(n,r,t,a,new Uint8Array(0));return{encryptedKey:i.ciphertext,iv:(0,u.lF)(i.iv),tag:(0,u.lF)(i.tag)}}async function eA(e,t,r,a,n){return ev(e.slice(0,7),t,r,a,n,new Uint8Array(0))}async function eS(e,t,r,a,n={}){let i,o,s;switch(eg(e,r,"encrypt"),r=await ed.normalizePublicKey?.(r,e)||r,e){case"dir":s=r;break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{if(!X(r))throw new f.T0("ECDH with the provided key is not allowed or not supported by your javascript runtime");let{apu:c,apv:l}=n,{epk:d}=n;d||=(await G(r)).privateKey;let{x:p,y:h,crv:y,kty:b}=await eh(d),x=await q(r,d,"ECDH-ES"===e?t:e,"ECDH-ES"===e?eu(t):parseInt(e.slice(-5,-2),10),c,l);if(o={epk:{x:p,crv:y,kty:b}},"EC"===b&&(o.epk.y=h),c&&(o.apu=(0,u.lF)(c)),l&&(o.apv=(0,u.lF)(l)),"ECDH-ES"===e){s=x;break}s=a||ef(t);let m=e.slice(-6);i=await N(m,x,s);break}case"RSA1_5":case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":s=a||ef(t),i=await ec(e,r,s);break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{s=a||ef(t);let{p2c:c,p2s:l}=n;({encryptedKey:i,...o}=await ee(e,r,s,c,l));break}case"A128KW":case"A192KW":case"A256KW":s=a||ef(t),i=await N(e,r,s);break;case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{s=a||ef(t);let{iv:c}=n;({encryptedKey:i,...o}=await ek(e,r,s,c));break}default:throw new f.T0('Invalid or unsupported "alg" (JWE Algorithm) header value')}return{cek:s,encryptedKey:i,parameters:o}}let eE=(...e)=>{let t,r=e.filter(Boolean);if(0===r.length||1===r.length)return!0;for(let e of r){let r=Object.keys(e);if(!t||0===t.size){t=new Set(r);continue}for(let e of r){if(t.has(e))return!1;t.add(e)}}return!0},eT=function(e,t,r,a,n){let i;if(void 0!==n.crit&&a?.crit===void 0)throw new e('"crit" (Critical) Header Parameter MUST be integrity protected');if(!a||void 0===a.crit)return new Set;if(!Array.isArray(a.crit)||0===a.crit.length||a.crit.some(e=>"string"!=typeof e||0===e.length))throw new e('"crit" (Critical) Header Parameter MUST be an array of non-empty strings when present');for(let o of(i=void 0!==r?new Map([...Object.entries(r),...t.entries()]):t,a.crit)){if(!i.has(o))throw new f.T0(`Extension Header Parameter "${o}" is not recognized`);if(void 0===n[o])throw new e(`Extension Header Parameter "${o}" is missing`);if(i.get(o)&&void 0===a[o])throw new e(`Extension Header Parameter "${o}" MUST be integrity protected`)}return new Set(a.crit)};class eP{_plaintext;_protectedHeader;_sharedUnprotectedHeader;_unprotectedHeader;_aad;_cek;_iv;_keyManagementParameters;constructor(e){if(!(e instanceof Uint8Array))throw TypeError("plaintext must be an instance of Uint8Array");this._plaintext=e}setKeyManagementParameters(e){if(this._keyManagementParameters)throw TypeError("setKeyManagementParameters can only be called once");return this._keyManagementParameters=e,this}setProtectedHeader(e){if(this._protectedHeader)throw TypeError("setProtectedHeader can only be called once");return this._protectedHeader=e,this}setSharedUnprotectedHeader(e){if(this._sharedUnprotectedHeader)throw TypeError("setSharedUnprotectedHeader can only be called once");return this._sharedUnprotectedHeader=e,this}setUnprotectedHeader(e){if(this._unprotectedHeader)throw TypeError("setUnprotectedHeader can only be called once");return this._unprotectedHeader=e,this}setAdditionalAuthenticatedData(e){return this._aad=e,this}setContentEncryptionKey(e){if(this._cek)throw TypeError("setContentEncryptionKey can only be called once");return this._cek=e,this}setInitializationVector(e){if(this._iv)throw TypeError("setInitializationVector can only be called once");return this._iv=e,this}async encrypt(e,t){let r,a,n,i,o;if(!this._protectedHeader&&!this._unprotectedHeader&&!this._sharedUnprotectedHeader)throw new f.aA("either setProtectedHeader, setUnprotectedHeader, or sharedUnprotectedHeader must be called before #encrypt()");if(!eE(this._protectedHeader,this._unprotectedHeader,this._sharedUnprotectedHeader))throw new f.aA("JWE Protected, JWE Shared Unprotected and JWE Per-Recipient Header Parameter names must be disjoint");let s={...this._protectedHeader,...this._unprotectedHeader,...this._sharedUnprotectedHeader};if(eT(f.aA,new Map,t?.crit,this._protectedHeader,s),void 0!==s.zip)throw new f.T0('JWE "zip" (Compression Algorithm) Header Parameter is not supported.');let{alg:c,enc:l}=s;if("string"!=typeof c||!c)throw new f.aA('JWE "alg" (Algorithm) Header Parameter missing or invalid');if("string"!=typeof l||!l)throw new f.aA('JWE "enc" (Encryption Algorithm) Header Parameter missing or invalid');if(this._cek&&("dir"===c||"ECDH-ES"===c))throw TypeError(`setContentEncryptionKey cannot be called with JWE "alg" (Algorithm) Header ${c}`);{let n;({cek:a,encryptedKey:r,parameters:n}=await eS(c,l,e,this._cek,this._keyManagementParameters)),n&&(t&&m in t?this._unprotectedHeader?this._unprotectedHeader={...this._unprotectedHeader,...n}:this.setUnprotectedHeader(n):this._protectedHeader?this._protectedHeader={...this._protectedHeader,...n}:this.setProtectedHeader(n))}i=this._protectedHeader?p.Rd.encode((0,u.lF)(JSON.stringify(this._protectedHeader))):p.Rd.encode(""),this._aad?(o=(0,u.lF)(this._aad),n=(0,p.xW)(i,p.Rd.encode("."),p.Rd.encode(o))):n=i;let{ciphertext:d,tag:h,iv:y}=await D(l,this._plaintext,a,this._iv,n),b={ciphertext:(0,u.lF)(d)};return y&&(b.iv=(0,u.lF)(y)),h&&(b.tag=(0,u.lF)(h)),r&&(b.encrypted_key=(0,u.lF)(r)),o&&(b.aad=o),this._protectedHeader&&(b.protected=p.D0.decode(i)),this._sharedUnprotectedHeader&&(b.unprotected=this._sharedUnprotectedHeader),this._unprotectedHeader&&(b.header=this._unprotectedHeader),b}}class eU{_flattened;constructor(e){this._flattened=new eP(e)}setContentEncryptionKey(e){return this._flattened.setContentEncryptionKey(e),this}setInitializationVector(e){return this._flattened.setInitializationVector(e),this}setProtectedHeader(e){return this._flattened.setProtectedHeader(e),this}setKeyManagementParameters(e){return this._flattened.setKeyManagementParameters(e),this}async encrypt(e,t){let r=await this._flattened.encrypt(e,t);return[r.protected,r.encrypted_key,r.iv,r.ciphertext,r.tag].join(".")}}let eC=e=>Math.floor(e.getTime()/1e3),eR=/^(\+|\-)? ?(\d+|\d+\.\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i,eO=e=>{let t,r=eR.exec(e);if(!r||r[4]&&r[1])throw TypeError("Invalid time period format");let a=parseFloat(r[2]);switch(r[3].toLowerCase()){case"sec":case"secs":case"second":case"seconds":case"s":t=Math.round(a);break;case"minute":case"minutes":case"min":case"mins":case"m":t=Math.round(60*a);break;case"hour":case"hours":case"hr":case"hrs":case"h":t=Math.round(3600*a);break;case"day":case"days":case"d":t=Math.round(86400*a);break;case"week":case"weeks":case"w":t=Math.round(604800*a);break;default:t=Math.round(0x1e187e0*a)}return"-"===r[1]||"ago"===r[4]?-t:t};function eI(e,t){if(!Number.isFinite(t))throw TypeError(`Invalid ${e} input`);return t}class e${_payload;constructor(e={}){if(!(0,h.A)(e))throw TypeError("JWT Claims Set MUST be an object");this._payload=e}setIssuer(e){return this._payload={...this._payload,iss:e},this}setSubject(e){return this._payload={...this._payload,sub:e},this}setAudience(e){return this._payload={...this._payload,aud:e},this}setJti(e){return this._payload={...this._payload,jti:e},this}setNotBefore(e){return"number"==typeof e?this._payload={...this._payload,nbf:eI("setNotBefore",e)}:e instanceof Date?this._payload={...this._payload,nbf:eI("setNotBefore",eC(e))}:this._payload={...this._payload,nbf:eC(new Date)+eO(e)},this}setExpirationTime(e){return"number"==typeof e?this._payload={...this._payload,exp:eI("setExpirationTime",e)}:e instanceof Date?this._payload={...this._payload,exp:eI("setExpirationTime",eC(e))}:this._payload={...this._payload,exp:eC(new Date)+eO(e)},this}setIssuedAt(e){return void 0===e?this._payload={...this._payload,iat:eC(new Date)}:e instanceof Date?this._payload={...this._payload,iat:eI("setIssuedAt",eC(e))}:"string"==typeof e?this._payload={...this._payload,iat:eI("setIssuedAt",eC(new Date)+eO(e))}:this._payload={...this._payload,iat:eI("setIssuedAt",e)},this}}class eH extends e${_cek;_iv;_keyManagementParameters;_protectedHeader;_replicateIssuerAsHeader;_replicateSubjectAsHeader;_replicateAudienceAsHeader;setProtectedHeader(e){if(this._protectedHeader)throw TypeError("setProtectedHeader can only be called once");return this._protectedHeader=e,this}setKeyManagementParameters(e){if(this._keyManagementParameters)throw TypeError("setKeyManagementParameters can only be called once");return this._keyManagementParameters=e,this}setContentEncryptionKey(e){if(this._cek)throw TypeError("setContentEncryptionKey can only be called once");return this._cek=e,this}setInitializationVector(e){if(this._iv)throw TypeError("setInitializationVector can only be called once");return this._iv=e,this}replicateIssuerAsHeader(){return this._replicateIssuerAsHeader=!0,this}replicateSubjectAsHeader(){return this._replicateSubjectAsHeader=!0,this}replicateAudienceAsHeader(){return this._replicateAudienceAsHeader=!0,this}async encrypt(e,t){let r=new eU(p.Rd.encode(JSON.stringify(this._payload)));return this._replicateIssuerAsHeader&&(this._protectedHeader={...this._protectedHeader,iss:this._payload.iss}),this._replicateSubjectAsHeader&&(this._protectedHeader={...this._protectedHeader,sub:this._payload.sub}),this._replicateAudienceAsHeader&&(this._protectedHeader={...this._protectedHeader,aud:this._payload.aud}),r.setProtectedHeader(this._protectedHeader),this._iv&&r.setInitializationVector(this._iv),this._cek&&r.setContentEncryptionKey(this._cek),this._keyManagementParameters&&r.setKeyManagementParameters(this._keyManagementParameters),r.encrypt(e,t)}}let ej=e=>e.d?(0,w.createPrivateKey)({format:"jwk",key:e}):(0,w.createPublicKey)({format:"jwk",key:e});async function eW(e,t){if(!(0,h.A)(e))throw TypeError("JWK must be an object");switch(t||=e.alg,e.kty){case"oct":if("string"!=typeof e.k||!e.k)throw TypeError('missing "k" (Key Value) Parameter value');return(0,u.D4)(e.k);case"RSA":if("oth"in e&&void 0!==e.oth)throw new f.T0('RSA JWK "oth" (Other Primes Info) Parameter value is not supported');case"EC":case"OKP":return ej({...e,alg:t});default:throw new f.T0('Unsupported "kty" (Key Type) Parameter value')}}async function eD(e,t,r,a,n){switch(eg(e,t,"decrypt"),t=await ed.normalizePrivateKey?.(t,e)||t,e){case"dir":if(void 0!==r)throw new f.aA("Encountered unexpected JWE Encrypted Key");return t;case"ECDH-ES":if(void 0!==r)throw new f.aA("Encountered unexpected JWE Encrypted Key");case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{let n,i;if(!(0,h.A)(a.epk))throw new f.aA('JOSE Header "epk" (Ephemeral Public Key) missing or invalid');if(!X(t))throw new f.T0("ECDH with the provided key is not allowed or not supported by your javascript runtime");let o=await eW(a.epk,e);if(void 0!==a.apu){if("string"!=typeof a.apu)throw new f.aA('JOSE Header "apu" (Agreement PartyUInfo) invalid');try{n=(0,u.D4)(a.apu)}catch{throw new f.aA("Failed to base64url decode the apu")}}if(void 0!==a.apv){if("string"!=typeof a.apv)throw new f.aA('JOSE Header "apv" (Agreement PartyVInfo) invalid');try{i=(0,u.D4)(a.apv)}catch{throw new f.aA("Failed to base64url decode the apv")}}let s=await q(o,t,"ECDH-ES"===e?a.enc:e,"ECDH-ES"===e?eu(a.enc):parseInt(e.slice(-5,-2),10),n,i);if("ECDH-ES"===e)return s;if(void 0===r)throw new f.aA("JWE Encrypted Key missing");return M(e.slice(-6),s,r)}case"RSA1_5":case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":if(void 0===r)throw new f.aA("JWE Encrypted Key missing");return el(e,t,r);case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{let i;if(void 0===r)throw new f.aA("JWE Encrypted Key missing");if("number"!=typeof a.p2c)throw new f.aA('JOSE Header "p2c" (PBES2 Count) missing or invalid');let o=n?.maxPBES2Count||1e4;if(a.p2c>o)throw new f.aA('JOSE Header "p2c" (PBES2 Count) out is of acceptable bounds');if("string"!=typeof a.p2s)throw new f.aA('JOSE Header "p2s" (PBES2 Salt) missing or invalid');try{i=(0,u.D4)(a.p2s)}catch{throw new f.aA("Failed to base64url decode the p2s")}return et(e,t,r,a.p2c,i)}case"A128KW":case"A192KW":case"A256KW":if(void 0===r)throw new f.aA("JWE Encrypted Key missing");return M(e,t,r);case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{let n,i;if(void 0===r)throw new f.aA("JWE Encrypted Key missing");if("string"!=typeof a.iv)throw new f.aA('JOSE Header "iv" (Initialization Vector) missing or invalid');if("string"!=typeof a.tag)throw new f.aA('JOSE Header "tag" (Authentication Tag) missing or invalid');try{n=(0,u.D4)(a.iv)}catch{throw new f.aA("Failed to base64url decode the iv")}try{i=(0,u.D4)(a.tag)}catch{throw new f.aA("Failed to base64url decode the tag")}return eA(e,t,r,n,i)}default:throw new f.T0('Invalid or unsupported "alg" (JWE Algorithm) header value')}}let eL=(e,t)=>{if(void 0!==t&&(!Array.isArray(t)||t.some(e=>"string"!=typeof e)))throw TypeError(`"${e}" option must be an array of strings`);if(t)return new Set(t)};async function eK(e,t,r){let a,n,i,o,s,c,l;if(!(0,h.A)(e))throw new f.aA("Flattened JWE must be an object");if(void 0===e.protected&&void 0===e.header&&void 0===e.unprotected)throw new f.aA("JOSE Header missing");if(void 0!==e.iv&&"string"!=typeof e.iv)throw new f.aA("JWE Initialization Vector incorrect type");if("string"!=typeof e.ciphertext)throw new f.aA("JWE Ciphertext missing or incorrect type");if(void 0!==e.tag&&"string"!=typeof e.tag)throw new f.aA("JWE Authentication Tag incorrect type");if(void 0!==e.protected&&"string"!=typeof e.protected)throw new f.aA("JWE Protected Header incorrect type");if(void 0!==e.encrypted_key&&"string"!=typeof e.encrypted_key)throw new f.aA("JWE Encrypted Key incorrect type");if(void 0!==e.aad&&"string"!=typeof e.aad)throw new f.aA("JWE AAD incorrect type");if(void 0!==e.header&&!(0,h.A)(e.header))throw new f.aA("JWE Shared Unprotected Header incorrect type");if(void 0!==e.unprotected&&!(0,h.A)(e.unprotected))throw new f.aA("JWE Per-Recipient Unprotected Header incorrect type");if(e.protected)try{let t=(0,u.D4)(e.protected);a=JSON.parse(p.D0.decode(t))}catch{throw new f.aA("JWE Protected Header is invalid")}if(!eE(a,e.header,e.unprotected))throw new f.aA("JWE Protected, JWE Unprotected Header, and JWE Per-Recipient Unprotected Header Parameter names must be disjoint");let d={...a,...e.header,...e.unprotected};if(eT(f.aA,new Map,r?.crit,a,d),void 0!==d.zip)throw new f.T0('JWE "zip" (Compression Algorithm) Header Parameter is not supported.');let{alg:y,enc:b}=d;if("string"!=typeof y||!y)throw new f.aA("missing JWE Algorithm (alg) in JWE Header");if("string"!=typeof b||!b)throw new f.aA("missing JWE Encryption Algorithm (enc) in JWE Header");let x=r&&eL("keyManagementAlgorithms",r.keyManagementAlgorithms),m=r&&eL("contentEncryptionAlgorithms",r.contentEncryptionAlgorithms);if(x&&!x.has(y)||!x&&y.startsWith("PBES2"))throw new f.Rb('"alg" (Algorithm) Header Parameter value not allowed');if(m&&!m.has(b))throw new f.Rb('"enc" (Encryption Algorithm) Header Parameter value not allowed');if(void 0!==e.encrypted_key)try{n=(0,u.D4)(e.encrypted_key)}catch{throw new f.aA("Failed to base64url decode the encrypted_key")}let w=!1;"function"==typeof t&&(t=await t(a,e),w=!0);try{i=await eD(y,t,n,d,r)}catch(e){if(e instanceof TypeError||e instanceof f.aA||e instanceof f.T0)throw e;i=ef(b)}if(void 0!==e.iv)try{o=(0,u.D4)(e.iv)}catch{throw new f.aA("Failed to base64url decode the iv")}if(void 0!==e.tag)try{s=(0,u.D4)(e.tag)}catch{throw new f.aA("Failed to base64url decode the tag")}let g=p.Rd.encode(e.protected??"");c=void 0!==e.aad?(0,p.xW)(g,p.Rd.encode("."),p.Rd.encode(e.aad)):g;try{l=(0,u.D4)(e.ciphertext)}catch{throw new f.aA("Failed to base64url decode the ciphertext")}let _={plaintext:await ev(b,i,l,o,s,c)};if(void 0!==e.protected&&(_.protectedHeader=a),void 0!==e.aad)try{_.additionalAuthenticatedData=(0,u.D4)(e.aad)}catch{throw new f.aA("Failed to base64url decode the aad")}return(void 0!==e.unprotected&&(_.sharedUnprotectedHeader=e.unprotected),void 0!==e.header&&(_.unprotectedHeader=e.header),w)?{..._,key:t}:_}async function eJ(e,t,r){if(e instanceof Uint8Array&&(e=p.D0.decode(e)),"string"!=typeof e)throw new f.aA("Compact JWE must be a string or Uint8Array");let{0:a,1:n,2:i,3:o,4:s,length:c}=e.split(".");if(5!==c)throw new f.aA("Invalid Compact JWE");let l=await eK({ciphertext:o,iv:i||void 0,protected:a,tag:s||void 0,encrypted_key:n||void 0},t,r),d={plaintext:l.plaintext,protectedHeader:l.protectedHeader};return"function"==typeof t?{...d,key:l.key}:d}let eN=e=>e.toLowerCase().replace(/^application\//,""),eM=(e,t)=>"string"==typeof e?t.includes(e):!!Array.isArray(e)&&t.some(Set.prototype.has.bind(new Set(e))),eF=(e,t,r={})=>{let a,n;try{a=JSON.parse(p.D0.decode(t))}catch{}if(!(0,h.A)(a))throw new f.Dp("JWT Claims Set must be a top-level JSON object");let{typ:i}=r;if(i&&("string"!=typeof e.typ||eN(e.typ)!==eN(i)))throw new f.ie('unexpected "typ" JWT header value',a,"typ","check_failed");let{requiredClaims:o=[],issuer:s,subject:c,audience:l,maxTokenAge:d}=r,u=[...o];for(let e of(void 0!==d&&u.push("iat"),void 0!==l&&u.push("aud"),void 0!==c&&u.push("sub"),void 0!==s&&u.push("iss"),new Set(u.reverse())))if(!(e in a))throw new f.ie(`missing required "${e}" claim`,a,e,"missing");if(s&&!(Array.isArray(s)?s:[s]).includes(a.iss))throw new f.ie('unexpected "iss" claim value',a,"iss","check_failed");if(c&&a.sub!==c)throw new f.ie('unexpected "sub" claim value',a,"sub","check_failed");if(l&&!eM(a.aud,"string"==typeof l?[l]:l))throw new f.ie('unexpected "aud" claim value',a,"aud","check_failed");switch(typeof r.clockTolerance){case"string":n=eO(r.clockTolerance);break;case"number":n=r.clockTolerance;break;case"undefined":n=0;break;default:throw TypeError("Invalid clockTolerance option type")}let{currentDate:y}=r,b=eC(y||new Date);if((void 0!==a.iat||d)&&"number"!=typeof a.iat)throw new f.ie('"iat" claim must be a number',a,"iat","invalid");if(void 0!==a.nbf){if("number"!=typeof a.nbf)throw new f.ie('"nbf" claim must be a number',a,"nbf","invalid");if(a.nbf>b+n)throw new f.ie('"nbf" claim timestamp check failed',a,"nbf","check_failed")}if(void 0!==a.exp){if("number"!=typeof a.exp)throw new f.ie('"exp" claim must be a number',a,"exp","invalid");if(a.exp<=b-n)throw new f.n('"exp" claim timestamp check failed',a,"exp","check_failed")}if(d){let e=b-a.iat;if(e-n>("number"==typeof d?d:eO(d)))throw new f.n('"iat" claim timestamp check failed (too far in the past)',a,"iat","check_failed");if(e<0-n)throw new f.ie('"iat" claim timestamp check failed (it should be in the past)',a,"iat","check_failed")}return a};async function ez(e,t,r){let a=await eJ(e,t,r),n=eF(a.protectedHeader,a.plaintext,r),{protectedHeader:i}=a;if(void 0!==i.iss&&i.iss!==n.iss)throw new f.ie('replicated "iss" claim header parameter mismatch',n,"iss","mismatch");if(void 0!==i.sub&&i.sub!==n.sub)throw new f.ie('replicated "sub" claim header parameter mismatch',n,"sub","mismatch");if(void 0!==i.aud&&JSON.stringify(i.aud)!==JSON.stringify(n.aud))throw new f.ie('replicated "aud" claim header parameter mismatch',n,"aud","mismatch");let o={payload:n,protectedHeader:i};return"function"==typeof t?{...o,key:a.key}:o}var eB=r(24223),eV=r(11868),eq=r(58319);let eG=()=>Date.now()/1e3|0,eX="A256CBC-HS512";async function eY(e){let{token:t={},secret:r,maxAge:a=2592e3,salt:n}=e,i=Array.isArray(r)?r:[r],o=await e0(eX,i[0],n),s=await b({kty:"oct",k:x.l(o)},`sha${o.byteLength<<3}`);return await new eH(t).setProtectedHeader({alg:"dir",enc:eX,kid:s}).setIssuedAt().setExpirationTime(eG()+a).setJti(crypto.randomUUID()).encrypt(o)}async function eZ(e){let{token:t,secret:r,salt:a}=e,n=Array.isArray(r)?r:[r];if(!t)return null;let{payload:i}=await ez(t,async({kid:e,enc:t})=>{for(let r of n){let n=await e0(t,r,a);if(void 0===e||e===await b({kty:"oct",k:x.l(n)},`sha${n.byteLength<<3}`))return n}throw Error("no matching decryption secret")},{clockTolerance:15,keyManagementAlgorithms:["dir"],contentEncryptionAlgorithms:[eX,"A256GCM"]});return i}async function eQ(e){let{secureCookie:t,cookieName:r=(0,eB.X)(t??!1).sessionToken.name,decode:a=eZ,salt:n=r,secret:i,logger:o=console,raw:s,req:c}=e;if(!c)throw Error("Must pass `req` to JWT getToken()");let l=c.headers instanceof Headers?c.headers:new Headers(c.headers),d=new eB.c({name:r,options:{secure:t}},(0,eq.q)(l.get("cookie")??""),o).value,u=l.get("authorization");if(d||u?.split(" ")[0]!=="Bearer"||(d=decodeURIComponent(u.split(" ")[1])),!d)return null;if(s)return d;if(!i)throw new eV.jo("Must pass `secret` if not set to JWT getToken()");try{return await a({token:d,secret:i,salt:n})}catch{return null}}async function e0(e,t,r){let a;switch(e){case"A256CBC-HS512":a=64;break;case"A256GCM":a=32;break;default:throw Error("Unsupported JWT Content Encryption Algorithm")}return await l("sha256",t,r,`Auth.js Generated Encryption Key (${r})`,a)}},63748:(e,t,r)=>{r.d(t,{Dp:()=>d,Rb:()=>o,T0:()=>s,_L:()=>u,aA:()=>l,ie:()=>n,n:()=>i,xO:()=>c});class a extends Error{static code="ERR_JOSE_GENERIC";code="ERR_JOSE_GENERIC";constructor(e,t){super(e,t),this.name=this.constructor.name,Error.captureStackTrace?.(this,this.constructor)}}class n extends a{static code="ERR_JWT_CLAIM_VALIDATION_FAILED";code="ERR_JWT_CLAIM_VALIDATION_FAILED";claim;reason;payload;constructor(e,t,r="unspecified",a="unspecified"){super(e,{cause:{claim:r,reason:a,payload:t}}),this.claim=r,this.reason=a,this.payload=t}}class i extends a{static code="ERR_JWT_EXPIRED";code="ERR_JWT_EXPIRED";claim;reason;payload;constructor(e,t,r="unspecified",a="unspecified"){super(e,{cause:{claim:r,reason:a,payload:t}}),this.claim=r,this.reason=a,this.payload=t}}class o extends a{static code="ERR_JOSE_ALG_NOT_ALLOWED";code="ERR_JOSE_ALG_NOT_ALLOWED"}class s extends a{static code="ERR_JOSE_NOT_SUPPORTED";code="ERR_JOSE_NOT_SUPPORTED"}class c extends a{static code="ERR_JWE_DECRYPTION_FAILED";code="ERR_JWE_DECRYPTION_FAILED";constructor(e="decryption operation failed",t){super(e,t)}}class l extends a{static code="ERR_JWE_INVALID";code="ERR_JWE_INVALID"}class d extends a{static code="ERR_JWT_INVALID";code="ERR_JWT_INVALID"}class u extends a{static code="ERR_JWK_INVALID";code="ERR_JWK_INVALID"}class f extends a{[Symbol.asyncIterator];static code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";constructor(e="multiple matching keys found in the JSON Web Key Set",t){super(e,t)}}},74771:(e,t,r)=>{r.d(t,{D:()=>i,l:()=>n});var a=r(30005);let n=a.lF,i=a.D4},82896:(e,t,r)=>{r.d(t,{A:()=>a});function a(e){if("object"!=typeof e||null===e||"[object Object]"!==Object.prototype.toString.call(e))return!1;if(null===Object.getPrototypeOf(e))return!0;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}},89131:(e,t,r)=>{r.d(t,{UD:()=>P});var a=r(77598);let n=10,i="./ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),o=Array.from({length:64},(e,t)=>t),s=e=>Array(e).fill(-1),c=[...s(46),0,1,...o.slice(54,64),...s(7),...o.slice(2,28),...s(6),...o.slice(28,54),...s(5)],l=[0x243f6a88,0x85a308d3,0x13198a2e,0x3707344,0xa4093822,0x299f31d0,0x82efa98,0xec4e6c89,0x452821e6,0x38d01377,0xbe5466cf,0x34e90c6c,0xc0ac29b7,0xc97c50dd,0x3f84d5b5,0xb5470917,0x9216d5d9,0x8979fb1b],d=[0xd1310ba6,0x98dfb5ac,0x2ffd72db,0xd01adfb7,0xb8e1afed,0x6a267e96,0xba7c9045,0xf12c7f99,0x24a19947,0xb3916cf7,0x801f2e2,0x858efc16,0x636920d8,0x71574e69,0xa458fea3,0xf4933d7e,0xd95748f,0x728eb658,0x718bcd58,0x82154aee,0x7b54a41d,0xc25a59b5,0x9c30d539,0x2af26013,0xc5d1b023,0x286085f0,0xca417918,0xb8db38ef,0x8e79dcb0,0x603a180e,0x6c9e0e8b,0xb01e8a3e,0xd71577c1,0xbd314b27,0x78af2fda,0x55605c60,0xe65525f3,0xaa55ab94,0x57489862,0x63e81440,0x55ca396a,0x2aab10b6,0xb4cc5c34,0x1141e8ce,0xa15486af,0x7c72e993,0xb3ee1411,0x636fbc2a,0x2ba9c55d,0x741831f6,0xce5c3e16,0x9b87931e,0xafd6ba33,0x6c24cf5c,0x7a325381,0x28958677,0x3b8f4898,0x6b4bb9af,0xc4bfe81b,0x66282193,0x61d809cc,0xfb21a991,0x487cac60,0x5dec8032,0xef845d5d,0xe98575b1,0xdc262302,0xeb651b88,0x23893e81,0xd396acc5,0xf6d6ff3,0x83f44239,0x2e0b4482,0xa4842004,0x69c8f04a,0x9e1f9b5e,0x21c66842,0xf6e96c9a,0x670c9c61,0xabd388f0,0x6a51a0d2,0xd8542f68,0x960fa728,0xab5133a3,0x6eef0b6c,0x137a3be4,0xba3bf050,0x7efb2a98,0xa1f1651d,0x39af0176,0x66ca593e,0x82430e88,0x8cee8619,0x456f9fb4,0x7d84a5c3,0x3b8b5ebe,0xe06f75d8,0x85c12073,0x401a449f,0x56c16aa6,0x4ed3aa62,0x363f7706,0x1bfedf72,0x429b023d,0x37d0d724,0xd00a1248,0xdb0fead3,0x49f1c09b,0x75372c9,0x80991b7b,0x25d479d8,0xf6e8def7,0xe3fe501a,0xb6794c3b,0x976ce0bd,0x4c006ba,0xc1a94fb6,0x409f60c4,0x5e5c9ec2,0x196a2463,0x68fb6faf,0x3e6c53b5,0x1339b2eb,0x3b52ec6f,0x6dfc511f,0x9b30952c,0xcc814544,0xaf5ebd09,0xbee3d004,0xde334afd,0x660f2807,0x192e4bb3,0xc0cba857,0x45c8740f,0xd20b5f39,0xb9d3fbdb,0x5579c0bd,0x1a60320a,0xd6a100c6,0x402c7279,0x679f25fe,0xfb1fa3cc,0x8ea5e9f8,0xdb3222f8,0x3c7516df,0xfd616b15,0x2f501ec8,0xad0552ab,0x323db5fa,0xfd238760,0x53317b48,0x3e00df82,0x9e5c57bb,0xca6f8ca0,0x1a87562e,0xdf1769db,0xd542a8f6,0x287effc3,0xac6732c6,0x8c4f5573,0x695b27b0,0xbbca58c8,0xe1ffa35d,0xb8f011a0,0x10fa3d98,0xfd2183b8,0x4afcb56c,0x2dd1d35b,0x9a53e479,0xb6f84565,0xd28e49bc,0x4bfb9790,0xe1ddf2da,0xa4cb7e33,0x62fb1341,0xcee4c6e8,0xef20cada,0x36774c01,0xd07e9efe,0x2bf11fb4,0x95dbda4d,0xae909198,0xeaad8e71,0x6b93d5a0,0xd08ed1d0,0xafc725e0,0x8e3c5b2f,0x8e7594b7,0x8ff6e2fb,0xf2122b64,0x8888b812,0x900df01c,0x4fad5ea0,0x688fc31c,0xd1cff191,0xb3a8c1ad,0x2f2f2218,0xbe0e1777,0xea752dfe,0x8b021fa1,0xe5a0cc0f,0xb56f74e8,0x18acf3d6,0xce89e299,0xb4a84fe0,0xfd13e0b7,0x7cc43b81,0xd2ada8d9,0x165fa266,0x80957705,0x93cc7314,0x211a1477,0xe6ad2065,0x77b5fa86,0xc75442f5,0xfb9d35cf,0xebcdaf0c,0x7b3e89a0,0xd6411bd3,0xae1e7e49,2428461,0x2071b35e,0x226800bb,0x57b8e0af,0x2464369b,0xf009b91e,0x5563911d,0x59dfa6aa,0x78c14389,0xd95a537f,0x207d5ba2,0x2e5b9c5,0x83260376,0x6295cfa9,0x11c81968,0x4e734a41,0xb3472dca,0x7b14a94a,0x1b510052,0x9a532915,0xd60f573f,0xbc9bc6e4,0x2b60a476,0x81e67400,0x8ba6fb5,0x571be91f,0xf296ec6b,0x2a0dd915,0xb6636521,0xe7b9f9b6,0xff34052e,0xc5855664,0x53b02d5d,0xa99f8fa1,0x8ba4799,0x6e85076a,0x4b7a70e9,0xb5b32944,0xdb75092e,0xc4192623,290971e4,0x49a7df7d,0x9cee60b8,0x8fedb266,0xecaa8c71,0x699a17ff,0x5664526c,0xc2b19ee1,0x193602a5,0x75094c29,0xa0591340,0xe4183a3e,0x3f54989a,0x5b429d65,0x6b8fe4d6,0x99f73fd6,0xa1d29c07,0xefe830f5,0x4d2d38e6,0xf0255dc1,0x4cdd2086,0x8470eb26,0x6382e9c6,0x21ecc5e,0x9686b3f,0x3ebaefc9,0x3c971814,0x6b6a70a1,0x687f3584,0x52a0e286,0xb79c5305,0xaa500737,0x3e07841c,0x7fdeae5c,0x8e7d44ec,0x5716f2b8,0xb03ada37,0xf0500c0d,0xf01c1f04,0x200b3ff,0xae0cf51a,0x3cb574b2,0x25837a58,0xdc0921bd,0xd19113f9,0x7ca92ff6,0x94324773,0x22f54701,0x3ae5e581,0x37c2dadc,0xc8b57634,0x9af3dda7,0xa9446146,0xfd0030e,0xecc8c73e,0xa4751e41,0xe238cd99,0x3bea0e2f,0x3280bba1,0x183eb331,0x4e548b38,0x4f6db908,0x6f420d03,0xf60a04bf,0x2cb81290,0x24977c79,0x5679b072,0xbcaf89af,0xde9a771f,0xd9930810,0xb38bae12,0xdccf3f2e,0x5512721f,0x2e6b7124,0x501adde6,0x9f84cd87,0x7a584718,0x7408da17,0xbc9f9abc,0xe94b7d8c,0xec7aec3a,0xdb851dfa,0x63094366,0xc464c3d2,0xef1c1847,0x3215d908,0xdd433b37,0x24c2ba16,0x12a14d43,0x2a65c451,0x50940002,0x133ae4dd,0x71dff89e,0x10314e55,0x81ac77d6,0x5f11199b,0x43556f1,0xd7a3c76b,0x3c11183b,0x5924a509,0xf28fe6ed,0x97f1fbfa,0x9ebabf2c,0x1e153c6e,0x86e34570,0xeae96fb1,0x860e5e0a,0x5a3e2ab3,0x771fe71c,0x4e3d06fa,0x2965dcb9,0x99e71d0f,0x803e89d6,0x5266c825,0x2e4cc978,0x9c10b36a,0xc6150eba,0x94e2ea78,0xa5fc3c53,0x1e0a2df4,0xf2f74ea7,0x361d2b3d,0x1939260f,0x19c27960,0x5223a708,0xf71312b6,0xebadfe6e,0xeac31f66,0xe3bc4595,0xa67bc883,0xb17f37d1,0x18cff28,0xc332ddef,0xbe6c5aa5,0x65582185,0x68ab9802,0xeecea50f,0xdb2f953b,0x2aef7dad,0x5b6e2f84,0x1521b628,0x29076170,0xecdd4775,0x619f1510,0x13cca830,0xeb61bd96,0x334fe1e,0xaa0363cf,0xb5735c90,0x4c70a239,0xd59e9e0b,0xcbaade14,0xeecc86bc,0x60622ca7,0x9cab5cab,0xb2f3846e,0x648b1eaf,0x19bdf0ca,0xa02369b9,0x655abb50,0x40685a32,0x3c2ab4b3,0x319ee9d5,0xc021b8f7,0x9b540b19,0x875fa099,0x95f7997e,0x623d7da8,0xf837889a,0x97e32d77,0x11ed935f,0x16681281,0xe358829,0xc7e61fd6,0x96dedfa1,0x7858ba99,0x57f584a5,0x1b227263,0x9b83c3ff,0x1ac24696,0xcdb30aeb,0x532e3054,0x8fd948e4,0x6dbc3128,0x58ebf2ef,0x34c6ffea,0xfe28ed61,0xee7c3c73,0x5d4a14d9,0xe864b7e3,0x42105d14,0x203e13e0,0x45eee2b6,0xa3aaabea,0xdb6c4f15,0xfacb4fd0,0xc742f442,0xef6abbb5,0x654f3b1d,0x41cd2105,0xd81e799e,0x86854dc7,0xe44b476a,0x3d816250,0xcf62a1f2,0x5b8d2646,0xfc8883a0,0xc1c7b6a3,0x7f1524c3,0x69cb7492,0x47848a0b,0x5692b285,0x95bbf00,0xad19489d,0x1462b174,0x23820e00,0x58428d2a,0xc55f5ea,0x1dadf43e,0x233f7061,0x3372f092,0x8d937e41,0xd65fecf1,0x6c223bdb,0x7cde3759,0xcbee7460,0x4085f2a7,0xce77326e,0xa6078084,0x19f8509e,0xe8efd855,0x61d99735,0xa969a7aa,0xc50c06c2,0x5a04abfc,0x800bcadc,0x9e447a2e,0xc3453484,0xfdd56705,0xe1e9ec9,0xdb73dbd3,0x105588cd,0x675fda79,0xe3674340,0xc5c43465,0x713e38d8,0x3d28f89e,0xf16dff20,0x153e21e7,0x8fb03d4a,0xe6e39f2b,0xdb83adf7,0xe93d5a68,0x948140f7,0xf64c261c,0x94692934,0x411520f7,0x7602d4f7,0xbcf46b2e,0xd4a20068,0xd4082471,0x3320f46a,0x43b7d4b7,0x500061af,0x1e39f62e,0x97244546,0x14214f74,0xbf8b8840,0x4d95fc1d,0x96b591af,0x70f4ddd3,0x66a02f45,0xbfbc09ec,0x3bd9785,0x7fac6dd0,0x31cb8504,0x96eb27b3,0x55fd3941,0xda2547e6,0xabca0a9a,0x28507825,0x530429f4,0xa2c86da,0xe9b66dfb,0x68dc1462,0xd7486900,0x680ec0a4,0x27a18dee,0x4f3ffea2,0xe887ad8c,0xb58ce006,0x7af4d6b6,0xaace1e7c,0xd3375fec,0xce78a399,0x406b2a42,0x20fe9e35,0xd9f385b9,0xee39d7ab,0x3b124e8b,0x1dc9faf7,0x4b6d1856,0x26a36631,0xeae397b2,0x3a6efa74,0xdd5b4332,0x6841e7f7,0xca7820fb,0xfb0af54e,0xd8feb397,0x454056ac,0xba489527,0x55533a3a,0x20838d87,0xfe6ba9b7,0xd096954b,0x55a867bc,0xa1159a58,0xcca92963,0x99e1db33,0xa62a4a56,0x3f3125f9,0x5ef47e1c,0x9029317c,0xfdf8e802,0x4272f70,0x80bb155c,0x5282ce3,0x95c11548,0xe4c66d22,0x48c1133f,0xc70f86dc,0x7f9c9ee,0x41041f0f,0x404779a4,0x5d886e17,0x325f51eb,0xd59bc0d1,0xf2bcc18f,0x41113564,0x257b7834,0x602a9c60,0xdff8e8a3,0x1f636c1b,0xe12b4c2,0x2e1329e,0xaf664fd1,0xcad18115,0x6b2395e0,0x333e92e1,0x3b240b62,0xeebeb922,0x85b2a20e,0xe6ba0d99,0xde720c8c,0x2da2f728,0xd0127845,0x95b794fd,0x647d0862,0xe7ccf5f0,0x5449a36f,0x877d48fa,0xc39dfd27,0xf33e8d1e,0xa476341,0x992eff74,0x3a6f6eab,0xf4f8fd37,0xa812dc60,0xa1ebddf8,0x991be14c,0xdb6e6b0d,0xc67b5510,0x6d672c37,0x2765d43b,0xdcd0e804,0xf1290dc7,0xcc00ffa3,0xb5390f92,0x690fed0b,0x667b9ffb,0xcedb7d9c,0xa091cf0b,0xd9155ea3,0xbb132f88,0x515bad24,0x7b9479bf,0x763bd6eb,0x37392eb3,0xcc115979,0x8026e297,0xf42e312d,0x6842ada7,0xc66a2b3b,0x12754ccc,0x782ef11c,0x6a124237,0xb79251e7,0x6a1bbe6,0x4bfb6350,0x1a6b1018,0x11caedfa,0x3d25bdd8,0xe2e1c3c9,0x44421659,0xa121386,0xd90cec6e,0xd5abea2a,0x64af674e,0xda86a85f,0xbebfe988,0x64e4c3fe,0x9dbc8057,0xf0f7c086,0x60787bf8,0x6003604d,0xd1fd8346,0xf6381fb0,0x7745ae04,0xd736fccc,0x83426b33,0xf01eab71,0xb0804187,0x3c005e5f,0x77a057be,0xbde8ae24,0x55464299,0xbf582e61,0x4e58f48f,0xf2ddfda2,0xf474ef38,0x8789bdc2,0x5366f9c3,0xc8b38e74,0xb475f255,0x46fcd9b9,0x7aeb2661,0x8b1ddf84,0x846a0e79,0x915f95e2,0x466e598e,0x20b45770,0x8cd55591,0xc902de4c,0xb90bace1,0xbb8205d0,0x11a86248,0x7574a99e,0xb77f19b6,0xe0a9dc09,0x662d09a1,0xc4324633,0xe85a1f02,0x9f0be8c,0x4a99a025,0x1d6efe10,0x1ab93d1d,0xba5a4df,0xa186f20f,0x2868f169,0xdcb7da83,0x573906fe,0xa1e2ce9b,0x4fcd7f52,0x50115e01,0xa70683fa,0xa002b5c4,0xde6d027,0x9af88c27,0x773f8641,0xc3604c06,0x61a806b5,0xf0177a28,0xc0f586e0,6314154,0x30dc7d62,0x11e69ed7,0x2338ea63,0x53c2dd94,0xc2c21634,0xbbcbee56,0x90bcb6de,0xebfc7da1,0xce591d76,0x6f05e409,0x4b7c0188,0x39720a3d,0x7c927c24,0x86e3725f,0x724d9db9,0x1ac15bb4,0xd39eb8fc,0xed545578,0x8fca5b5,0xd83d7cd3,0x4dad0fc4,0x1e50ef5e,0xb161e6f8,0xa28514d9,0x6c51133c,0x6fd5c7e7,0x56e14ec4,0x362abfce,0xddc6c837,0xd79a3234,0x92638212,0x670efa8e,0x406000e0,0x3a39ce37,0xd3faf5cf,0xabc27737,0x5ac52d1b,0x5cb0679e,0x4fa33742,0xd3822740,0x99bc9bbe,0xd5118e9d,0xbf0f7315,0xd62d1c7e,0xc700c47b,0xb78c1b6b,0x21a19045,0xb26eb1be,0x6a366eb4,0x5748ab2f,0xbc946e79,0xc6a376d2,0x6549c2c8,0x530ff8ee,0x468dde7d,0xd5730a1d,0x4cd04dc6,0x2939bbdb,0xa9ba4650,0xac9526e8,0xbe5ee304,0xa1fad5f0,0x6a2d519a,0x63ef8ce2,0x9a86ee22,0xc089c2b8,0x43242ef6,0xa51e03aa,0x9cf2d0a4,0x83c061ba,0x9be96a4d,0x8fe51550,0xba645bd6,0x2826a2f9,0xa73a3ae1,0x4ba99586,0xef5562e9,0xc72fefd3,0xf752f7da,0x3f046f69,0x77fa0a59,0x80e4a915,0x87b08601,0x9b09e6ad,0x3b3ee593,0xe990fd5a,0x9e34d797,0x2cf0b7d9,0x22b8b51,0x96d5ac3a,0x17da67d,0xd1cf3ed6,0x7c7d2d28,0x1f9f25cf,0xadf2b89b,0x5ad6b472,0x5a88f54c,0xe029ac71,0xe019a5e6,0x47b0acfd,0xed93fa9b,0xe8d3c48d,0x283b57cc,0xf8d56629,0x79132e28,0x785f0191,0xed756055,0xf7960e44,0xe3d35e8c,0x15056dd4,0x88f46dba,0x3a16125,0x564f0bd,0xc3eb9e15,0x3c9057a2,0x97271aec,0xa93a072a,0x1b3f6d9b,0x1e6321f5,0xf59c66fb,0x26dcf319,0x7533d928,0xb155fdf5,0x3563482,0x8aba3cbb,0x28517711,0xc20ad9f8,0xabcc5167,0xccad925f,0x4de81751,0x3830dc8e,0x379d5862,0x9320f991,0xea7a90c2,0xfb3e7bce,0x5121ce64,0x774fbe32,0xa8b6e37e,0xc3293d46,0x48de5369,0x6413e680,0xa2ae0810,0xdd6db224,0x69852dfd,0x9072166,0xb39a460a,0x6445c0dd,0x586cdecf,0x1c20c8ae,0x5bbef7dd,0x1b588d40,0xccd2017f,0x6bb4e3bb,0xdda26a7e,0x3a59ff45,0x3e350a44,0xbcb4cdd5,0x72eacea8,0xfa6484bb,0x8d6612ae,0xbf3c6f47,0xd29be463,0x542f5d9e,0xaec2771b,0xf64e6370,0x740e0d8d,0xe75b1357,0xf8721671,0xaf537d5d,0x4040cb08,0x4eb4e2cc,0x34d2466a,0x115af84,3786409e3,0x95983a1d,0x6b89fb4,0xce6ea048,0x6f3f3b82,0x3520ab82,0x11a1d4b,0x277227f8,0x611560b1,0xe7933fdc,0xbb3a792b,0x344525bd,0xa08839e1,0x51ce794b,0x2f32c9b7,0xa01fbac9,0xe01cc87e,0xbcc7d1f6,0xcf0111c3,0xa1e8aac7,0x1a908749,0xd44fbd9a,0xd0dadecb,0xd50ada38,0x339c32a,0xc6913667,0x8df9317c,0xe0b12b4f,0xf79e59b7,0x43f5bb3a,0xf2d519ff,0x27d9459c,0xbf97222c,0x15e6fc2a,0xf91fc71,0x9b941525,0xfae59361,0xceb69ceb,0xc2a86459,0x12baa8d1,0xb6c1075e,0xe3056a0c,0x10d25065,0xcb03a442,0xe0ec6e0e,0x1698db3b,0x4c98a0be,0x3278e964,0x9f1f9532,0xe0d392df,0xd3a0342b,0x8971f21e,0x1b0a7441,0x4ba3348c,0xc5be7120,0xc37632d8,0xdf359f8d,0x9b992f2e,0xe60b6f47,0xfe3f11d,0xe54cda54,0x1edad891,0xce6279cf,0xcd3e7e6f,0x1618b166,0xfd2c1d05,0x848fd2c5,0xf6fb2299,0xf523f357,0xa6327623,0x93a83531,0x56cccd02,0xacf08162,0x5a75ebb5,0x6e163697,0x88d273cc,0xde966292,0x81b949d0,0x4c50901b,0x71c65614,0xe6c6c7bd,0x327a140a,0x45e1d006,0xc3f27b9a,0xc9aa53fd,0x62a80f00,0xbb25bfe2,0x35bdd2f6,0x71126905,0xb2040222,0xb6cbcf7c,0xcd769c2b,0x53113ec0,0x1640e3d3,0x38abbd60,0x2547adf0,0xba38209c,0xf746ce76,0x77afa1c5,0x20756060,0x85cbfe4e,0x8ae88dd8,0x7aaaf9b0,0x4cf9aa7e,0x1948c25c,0x2fb8a8c,0x1c36ae4,0xd6ebe1f9,0x90d4f869,0xa65cdea0,0x3f09252d,0xc208e69f,0xb74e6132,0xce77e25b,0x578fdfe3,0x3ac372e6],u=[0x4f727068,0x65616e42,0x65686f6c,0x64657253,0x63727944,0x6f756274],f=(e,t)=>{if(t<=0||t>e.length)throw Error(`Illegal len: ${t}`);let r=0,a,n,o=[];for(;r<t;){if(a=255&e[r++],o.push(i[a>>2&63]),a=(3&a)<<4,r>=t||(a|=(n=255&e[r++])>>4&15,o.push(i[63&a]),a=(15&n)<<2,r>=t)){o.push(i[63&a]);break}a|=(n=255&e[r++])>>6&3,o.push(i[63&a]),o.push(i[63&n])}return o.join("")},p=(e,t)=>{let r=e.length,a=0,n=0,i,o,s,l,d,u=[];for(;a<r-1&&n<t&&(i=(d=e.charCodeAt(a++))<c.length?c[d]:-1,o=(d=e.charCodeAt(a++))<c.length?c[d]:-1,!(-1==i||-1==o||(u.push(String.fromCharCode(i<<2>>>0|(48&o)>>4)),++n>=t||a>=r)||-1==(s=(d=e.charCodeAt(a++))<c.length?c[d]:-1)||(u.push(String.fromCharCode((15&o)<<4>>>0|(60&s)>>2)),++n>=t||a>=r)));)u.push(String.fromCharCode((3&s)<<6>>>0|((d=e.charCodeAt(a++))<c.length?c[d]:-1))),++n;return u.map(e=>e.charCodeAt(0))},h=(e,t)=>{let r=null;for("number"==typeof e&&(r=e,e=()=>null);null!==r||null!==(r=e());)r<128?t(127&r):(r<2048?t(r>>6&31|192):(r<65536?t(r>>12&15|224):(t(r>>18&7|240),t(r>>12&63|128)),t(r>>6&63|128)),t(63&r|128)),r=null},y=(e,t)=>{let r,a=null;for(;null!==(r=null!==a?a:e());){if(r>=55296&&r<=57343&&null!==(a=e())&&a>=56320&&a<=57343){t((r-55296)*1024+a-56320+65536),a=null;continue}t(r)}null!==a&&t(a)},b=(e,t)=>y(e,e=>{h(e,t)}),x="function"==typeof setImmediate?setImmediate:"object"==typeof process&&"function"==typeof process.nextTick?process.nextTick:setTimeout,m=e=>{let t=0,r=[];return b(()=>t<e.length?e.charCodeAt(t++):null,e=>{r.push(e)}),r},w=(e,t,r,a)=>{let n,i=e[t],o=e[t+1];return i^=r[0],o^=(a[i>>>24]+a[256|i>>16&255]^a[512|i>>8&255])+a[768|255&i]^r[1],i^=(a[o>>>24]+a[256|o>>16&255]^a[512|o>>8&255])+a[768|255&o]^r[2],o^=(a[i>>>24]+a[256|i>>16&255]^a[512|i>>8&255])+a[768|255&i]^r[3],i^=(a[o>>>24]+a[256|o>>16&255]^a[512|o>>8&255])+a[768|255&o]^r[4],o^=(a[i>>>24]+a[256|i>>16&255]^a[512|i>>8&255])+a[768|255&i]^r[5],i^=(a[o>>>24]+a[256|o>>16&255]^a[512|o>>8&255])+a[768|255&o]^r[6],o^=(a[i>>>24]+a[256|i>>16&255]^a[512|i>>8&255])+a[768|255&i]^r[7],i^=(a[o>>>24]+a[256|o>>16&255]^a[512|o>>8&255])+a[768|255&o]^r[8],o^=(a[i>>>24]+a[256|i>>16&255]^a[512|i>>8&255])+a[768|255&i]^r[9],i^=(a[o>>>24]+a[256|o>>16&255]^a[512|o>>8&255])+a[768|255&o]^r[10],o^=(a[i>>>24]+a[256|i>>16&255]^a[512|i>>8&255])+a[768|255&i]^r[11],i^=(a[o>>>24]+a[256|o>>16&255]^a[512|o>>8&255])+a[768|255&o]^r[12],o^=(a[i>>>24]+a[256|i>>16&255]^a[512|i>>8&255])+a[768|255&i]^r[13],i^=(a[o>>>24]+a[256|o>>16&255]^a[512|o>>8&255])+a[768|255&o]^r[14],o^=(a[i>>>24]+a[256|i>>16&255]^a[512|i>>8&255])+a[768|255&i]^r[15],i^=(a[o>>>24]+a[256|o>>16&255]^a[512|o>>8&255])+a[768|255&o]^r[16],e[t]=o^r[17],e[t+1]=i,e},g=(e,t)=>{let r=0;for(let a=0;a<4;++a)r=r<<8|255&e[t],t=(t+1)%e.length;return{key:r,offp:t}},_=(e,t,r)=>{let a=t.length,n=r.length,i=0,o=[0,0],s;for(let r=0;r<a;r++)i=(s=g(e,i)).offp,t[r]=t[r]^s.key;for(let e=0;e<a;e+=2)o=w(o,0,t,r),t[e]=o[0],t[e+1]=o[1];for(let e=0;e<n;e+=2)o=w(o,0,t,r),r[e]=o[0],r[e+1]=o[1]},v=(e,t,r,a)=>{let n=r.length,i=a.length,o=0,s=[0,0],c;for(let e=0;e<n;e++)o=(c=g(t,o)).offp,r[e]=r[e]^c.key;o=0;for(let t=0;t<n;t+=2)o=(c=g(e,o)).offp,s[0]^=c.key,o=(c=g(e,o)).offp,s[1]^=c.key,s=w(s,0,r,a),r[t]=s[0],r[t+1]=s[1];for(let t=0;t<i;t+=2)o=(c=g(e,o)).offp,s[0]^=c.key,o=(c=g(e,o)).offp,s[1]^=c.key,s=w(s,0,r,a),a[t]=s[0],a[t+1]=s[1]},k=(e,t,r,a,n)=>{let i=u.slice(),o=i.length;if(r<4||r>31){let e=Error(`Illegal number of rounds (4-31): ${r}`);if(!1===a)return Promise.reject(e);throw e}if(16!==t.length){let e=Error(`Illegal salt length: ${t.length} != 16`);if(!1===a)return Promise.reject(e);throw e}r=1<<r>>>0;let s,c,f=0,p;Int32Array?(s=new Int32Array(l),c=new Int32Array(d)):(s=l.slice(),c=d.slice()),v(t,e,s,c);let h=()=>{if(n&&n(f/r),f<r){let a=Date.now();for(;f<r&&(f+=1,_(e,s,c),_(t,s,c),!(Date.now()-a>100)););}else{for(f=0;f<64;f++)for(p=0;p<o>>1;p++)w(i,p<<1,s,c);let e=[];for(f=0;f<o;f++)e.push((i[f]>>24&255)>>>0),e.push((i[f]>>16&255)>>>0),e.push((i[f]>>8&255)>>>0),e.push((255&i[f])>>>0);return!1===a?Promise.resolve(e):e}if(!1===a)return new Promise(e=>x(()=>{h().then(e)}))};if(!1===a)return h();{let e;for(;;)if("u">typeof(e=h()))return e||[]}},A=e=>(0,a.randomBytes)(e),S=(e=n)=>{if("number"!=typeof e)throw Error("Illegal arguments: "+typeof e);e<4?e=4:e>31&&(e=31);let t=[];return t.push("$2a$"),e<10&&t.push("0"),t.push(e.toString()),t.push("$"),t.push(f(A(16),16)),t.join("")},E=(e=n)=>{if("number"!=typeof e)throw Error("illegal arguments: "+typeof e);return new Promise((t,r)=>x(()=>{try{t(S(e))}catch(e){r(e)}}))};function T(e,t,r,a){let n,i;if("string"!=typeof e||"string"!=typeof t){let e=Error("Invalid string / salt: Not a string");if(!1===r)return Promise.reject(e);throw e}if("$"!==t.charAt(0)||"2"!==t.charAt(1)){let e=Error("Invalid salt version: "+t.substring(0,2));if(!1===r)return Promise.reject(e);throw e}if("$"===t.charAt(2))n="\0",i=3;else{if("a"!==(n=t.charAt(2))&&"b"!==n&&"y"!==n||"$"!==t.charAt(3)){let e=Error("Invalid salt revision: "+t.substring(2,4));if(!1===r)return Promise.reject(e);throw e}i=4}if(t.charAt(i+2)>"$"){let e=Error("Missing salt rounds");if(!1===r)return Promise.reject(e);throw e}let o=10*parseInt(t.substring(i,i+1),10)+parseInt(t.substring(i+1,i+2),10),s=t.substring(i+3,i+25),c=m(e+=n>="a"?"\0":""),l=p(s,16),d=e=>{let t=[];return t.push("$2"),n>="a"&&t.push(n),t.push("$"),o<10&&t.push("0"),t.push(o.toString()),t.push("$"),t.push(f(l,l.length)),t.push(f(e,4*u.length-1)),t.join("")};return!1===r?k(c,l,o,!1,a).then(e=>d(e)):d(k(c,l,o,!0,a))}let P=(e,t,r)=>new Promise((a,n)=>"string"!=typeof e||"string"!=typeof t?void x(()=>n(Error(`Illegal arguments: ${typeof e}, ${typeof t}`))):60!==t.length?void x(()=>n(Error("Illegal hash: hash length should be 60"))):void(function(e,t,r){return"string"==typeof e&&"number"==typeof t?E(t).then(t=>T(e,t,!1,r)):"string"==typeof e&&"string"==typeof t?T(e,t,!1,r):Promise.reject(Error(`Illegal arguments: ${typeof e}, ${typeof t}`))})(e,t.substring(0,29),r).then(e=>a(e===t)).catch(e=>n(e)))},90337:(e,t,r)=>{r.d(t,{A:()=>n});var a=r(77598);let n=(e,t)=>(0,a.createHash)(e).update(t).digest()}};