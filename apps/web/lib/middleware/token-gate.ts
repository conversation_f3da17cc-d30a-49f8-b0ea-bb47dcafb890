import { auth, currentUser } from '@clerk/nextjs/server';
import { Connection, PublicKey } from '@solana/web3.js';
import { NextRequest, NextResponse } from 'next/server';
import { UserTier } from '@/lib/ai/providers-openrouter';

// Token balance thresholds for each tier
const TIER_THRESHOLDS = {
  [UserTier.BRONZE]: 20,
  [UserTier.SILVER]: 50,
  [UserTier.DIAMOND]: 100,
};

// Initialize Solana connection
const connection = new Connection(
  process.env.NEXT_PUBLIC_SOLANA_RPC_URL || 'https://api.devnet.solana.com',
);

export async function getUserTier(walletAddress?: string): Promise<UserTier> {
  if (!walletAddress) {
    return UserTier.FREE;
  }

  try {
    const balance = await getTokenBalance(walletAddress);

    if (balance >= TIER_THRESHOLDS[UserTier.DIAMOND]) {
      return UserTier.DIAMOND;
    } else if (balance >= TIER_THRESHOLDS[UserTier.SILVER]) {
      return UserTier.SILVER;
    } else if (balance >= TIER_THRESHOLDS[UserTier.BRONZE]) {
      return UserTier.BRONZE;
    }

    return UserTier.FREE;
  } catch (error) {
    console.error('Error getting user tier:', error);
    return UserTier.FREE;
  }
}

export async function getTokenBalance(walletAddress: string): Promise<number> {
  try {
    const tokenAddress = process.env.NEXT_PUBLIC_BONKAI_TOKEN_ADDRESS;

    if (!tokenAddress) {
      console.warn('BonKai token address not configured');
      return 0;
    }

    // For now, return mock balance for development
    // TODO: Implement actual SPL token balance check
    const mockBalances: Record<string, number> = {
      mock_bronze_wallet: 25,
      mock_silver_wallet: 60,
      mock_diamond_wallet: 150,
    };

    return mockBalances[walletAddress] || 0;
  } catch (error) {
    console.error('Error getting token balance:', error);
    return 0;
  }
}

export async function tokenGateMiddleware(
  req: NextRequest,
  requiredTier: UserTier = UserTier.FREE,
): Promise<NextResponse | null> {
  const { userId } = await auth();

  if (!userId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const user = await currentUser();
  const walletAddress = user?.publicMetadata?.walletAddress as
    | string
    | undefined;
  const userTier = await getUserTier(walletAddress);

  // Check if user meets the required tier
  const tierValues = {
    [UserTier.FREE]: 0,
    [UserTier.BRONZE]: 1,
    [UserTier.SILVER]: 2,
    [UserTier.DIAMOND]: 3,
  };

  if (tierValues[userTier] < tierValues[requiredTier]) {
    return NextResponse.json(
      {
        error: 'Insufficient tier access',
        requiredTier,
        userTier,
        message: `This feature requires ${requiredTier} tier. You currently have ${userTier} tier.`,
      },
      { status: 403 },
    );
  }

  // Add tier information to headers for downstream use
  const response = NextResponse.next();
  response.headers.set('x-user-tier', userTier);

  return null;
}

// Rate limiting check
export async function checkRateLimit(
  userId: string,
  userTier: UserTier,
): Promise<{ allowed: boolean; remaining: number }> {
  // TODO: Implement Redis-based rate limiting
  // For now, return mock data
  return {
    allowed: true,
    remaining: 100,
  };
}

// Token usage tracking
export async function trackTokenUsage(
  userId: string,
  tokens: number,
): Promise<void> {
  // TODO: Implement token usage tracking in database
  console.log(`User ${userId} used ${tokens} tokens`);
}
