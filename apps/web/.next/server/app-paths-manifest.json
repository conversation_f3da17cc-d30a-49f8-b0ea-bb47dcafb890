{"/(auth)/api/auth/[...nextauth]/route": "app/(auth)/api/auth/[...nextauth]/route.js", "/(auth)/api/auth/guest/route": "app/(auth)/api/auth/guest/route.js", "/(chat)/api/chat/[id]/stream/route": "app/(chat)/api/chat/[id]/stream/route.js", "/(chat)/api/history/route": "app/(chat)/api/history/route.js", "/(chat)/api/document/route": "app/(chat)/api/document/route.js", "/(chat)/api/vote/route": "app/(chat)/api/vote/route.js", "/api/telegram/link/route": "app/api/telegram/link/route.js", "/api/wallet/link/route": "app/api/wallet/link/route.js", "/api/wallet/unlink/route": "app/api/wallet/unlink/route.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/(chat)/opengraph-image-12cog0.png/route": "app/(chat)/opengraph-image-12cog0.png/route.js", "/(chat)/twitter-image-12cog0.png/route": "app/(chat)/twitter-image-12cog0.png/route.js", "/(chat)/api/suggestions/route": "app/(chat)/api/suggestions/route.js", "/_not-found/page": "app/_not-found/page.js", "/api/uploadthing/route": "app/api/uploadthing/route.js", "/(chat)/api/chat/route": "app/(chat)/api/chat/route.js", "/(auth)/link/page": "app/(auth)/link/page.js", "/(auth)/login/page": "app/(auth)/login/page.js", "/(auth)/register/page": "app/(auth)/register/page.js", "/(chat)/page": "app/(chat)/page.js", "/(chat)/chat/[id]/page": "app/(chat)/chat/[id]/page.js"}