"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6808],{6808:(e,t,s)=>{s.d(t,{CI:()=>et,wV:()=>Z}),s(44995);var n,r,a,o,l,i=s(41987),c=s.t(i,2);class d extends Error{constructor(e,t){e instanceof Error?super(void 0,{cause:{err:e,...e.cause,...t}}):"string"==typeof e?(t instanceof Error&&(t={err:t,...t.cause}),super(e,t)):super(void 0,e),this.name=this.constructor.name,this.type=this.constructor.type??"AuthError",this.kind=this.constructor.kind??"error",Error.captureStackTrace?.(this,this.constructor);let s=`https://errors.authjs.dev#${this.type.toLowerCase()}`;this.message+=`${this.message?". ":""}Read more at ${s}`}}class u extends d{}u.kind="signIn";class p extends d{}p.type="AdapterError";class h extends d{}h.type="AccessDenied";class y extends d{}y.type="CallbackRouteError";class v extends d{}v.type="ErrorPageLoop";class x extends d{}x.type="EventError";class E extends d{}E.type="InvalidCallbackUrl";class g extends u{constructor(){super(...arguments),this.code="credentials"}}g.type="CredentialsSignin";class w extends d{}w.type="InvalidEndpoints";class f extends d{}f.type="InvalidCheck";class U extends d{}U.type="JWTSessionError";class b extends d{}b.type="MissingAdapter";class A extends d{}A.type="MissingAdapterMethods";class k extends d{}k.type="MissingAuthorize";class R extends d{}R.type="MissingSecret";class L extends u{}L.type="OAuthAccountNotLinked";class S extends u{}S.type="OAuthCallbackError";class T extends d{}T.type="OAuthProfileParseError";class C extends d{}C.type="SessionTokenError";class m extends u{}m.type="OAuthSignInError";class _ extends u{}_.type="EmailSignInError";class N extends d{}N.type="SignOutError";class P extends d{}P.type="UnknownAction";class I extends d{}I.type="UnsupportedStrategy";class M extends d{}M.type="InvalidProvider";class H extends d{}H.type="UntrustedHost";class O extends d{}O.type="Verification";class X extends u{}X.type="MissingCSRF";class V extends d{}V.type="DuplicateConditionalUI";class j extends d{}j.type="MissingWebAuthnAutocomplete";class W extends d{}W.type="WebAuthnVerificationError";class $ extends u{}$.type="AccountNotLinked";class B extends d{}B.type="ExperimentalFeatureNotEnabled";class D extends d{}async function F(e,t,s){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};let r="".concat(t.basePath,"/").concat(e);try{var a;let e={headers:{"Content-Type":"application/json",...(null==n||null==(a=n.headers)?void 0:a.cookie)?{cookie:n.headers.cookie}:{}}};(null==n?void 0:n.body)&&(e.body=JSON.stringify(n.body),e.method="POST");let t=await fetch(r,e),s=await t.json();if(!t.ok)throw s;return s}catch(e){return s.error(new D(e.message,e)),null}}function J(e){let t=new URL("http://localhost:3000/api/auth");e&&!e.startsWith("http")&&(e="https://".concat(e));let s=new URL(e||t),n=("/"===s.pathname?t.pathname:s.pathname).replace(/\/$/,""),r="".concat(s.origin).concat(n);return{origin:s.origin,host:s.host,path:n,base:r,toString:()=>r}}var q=s(40459);let z={baseUrl:J(null!=(r=q.env.NEXTAUTH_URL)?r:q.env.VERCEL_URL).origin,basePath:J(q.env.NEXTAUTH_URL).path,baseUrlServer:J(null!=(o=null!=(a=q.env.NEXTAUTH_URL_INTERNAL)?a:q.env.NEXTAUTH_URL)?o:q.env.VERCEL_URL).origin,basePathServer:J(null!=(l=q.env.NEXTAUTH_URL_INTERNAL)?l:q.env.NEXTAUTH_URL).path,_lastSync:0,_session:void 0,_getSession:()=>{}},G=null;function K(){return new BroadcastChannel("next-auth")}let Q={debug:console.debug,error:console.error,warn:console.warn},Y=null==(n=i.createContext)?void 0:n.call(c,void 0);function Z(e){if(!Y)throw Error("React Context is unavailable in Server Components");let t=i.useContext(Y),{required:s,onUnauthenticated:n}=null!=e?e:{},r=s&&"unauthenticated"===t.status;return(i.useEffect(()=>{if(r){let e="".concat(z.basePath,"/signin?").concat(new URLSearchParams({error:"SessionRequired",callbackUrl:window.location.href}));n?n():window.location.href=e}},[r,n]),r)?{data:t.data,update:t.update,status:"loading"}:t}async function ee(){var e;let t=await F("csrf",z,Q);return null!=(e=null==t?void 0:t.csrfToken)?e:""}async function et(e){var t,s,n,r;let a=null!=(s=null!=(t=null==e?void 0:e.redirectTo)?t:null==e?void 0:e.callbackUrl)?s:window.location.href,o=z.basePath,l=await ee(),i=await fetch("".concat(o,"/signout"),{method:"post",headers:{"Content-Type":"application/x-www-form-urlencoded","X-Auth-Return-Redirect":"1"},body:new URLSearchParams({csrfToken:l,callbackUrl:a})}),c=await i.json();if(("undefined"==typeof BroadcastChannel?{postMessage:()=>{},addEventListener:()=>{},removeEventListener:()=>{}}:(null===G&&(G=K()),G)).postMessage({event:"session",data:{trigger:"signout"}}),null==(n=null==e?void 0:e.redirect)||n){let e=null!=(r=c.url)?r:a;window.location.href=e,e.includes("#")&&window.location.reload();return}return await z._getSession({event:"storage"}),c}}}]);