(()=>{var e={};e.id=1981,e.ids=[1981],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27707:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>v,routeModule:()=>g,serverHooks:()=>j,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>k});var s={};t.r(s),t.d(s,{GET:()=>x,POST:()=>l});var n=t(48106),o=t(48819),a=t(12050),i=t(4235),u=t(30693),p=t(48310),c=t(81061);let d=p.Ik({code:p.Yj().min(6).max(10)});async function l(e){try{let{userId:r}=await (0,u.j)();if(!r)return i.NextResponse.json({error:"Authentication required"},{status:401});let t=await e.json(),{code:s}=d.parse(t);if(await new Promise(e=>setTimeout(e,1e3)),Math.random()>.2)return i.NextResponse.json({success:!0,message:"Telegram account linked successfully! You can now use the bot."});return i.NextResponse.json({success:!1,error:"Invalid or expired linking code. Please generate a new code from the bot."},{status:400})}catch(e){if(console.error("Error linking Telegram account:",e),e instanceof c.G)return i.NextResponse.json({error:"Invalid request data",details:e.errors},{status:400});return i.NextResponse.json({error:"Internal server error"},{status:500})}}async function x(e){let{searchParams:r}=new URL(e.url);return r.get("code")?i.NextResponse.json({valid:!0,expired:!1,used:!1,instructions:["Log in to your BonKai account","Confirm the linking request","Return to Telegram to start chatting"]}):i.NextResponse.json({error:"Linking code is required"},{status:400})}let g=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/telegram/link/route",pathname:"/api/telegram/link",filename:"route",bundlePath:"app/api/telegram/link/route"},resolvedPagePath:"/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/api/telegram/link/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:k,serverHooks:j}=g;function v(){return(0,a.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:k})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29727:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page-experimental.runtime.prod.js")},46871:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route-experimental.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},80408:()=>{},87032:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[191,4235,2543,5726,8310],()=>t(27707));module.exports=s})();