{"version": 3, "file": "literal-union.d.ts", "sourceRoot": "", "sources": ["../../src/literal-union.ts"], "names": [], "mappings": "AAAA,OAAO,EAKH,cAAc,EACd,gBAAgB,EAChB,gBAAgB,EAGhB,iBAAiB,EACjB,mBAAmB,EACnB,mBAAmB,EACtB,MAAM,qBAAqB,CAAC;AAC7B,OAAO,EACH,oBAAoB,EACpB,sBAAsB,EACtB,sBAAsB,EAGtB,WAAW,EACX,aAAa,EACb,aAAa,EAChB,MAAM,wBAAwB,CAAC;AAOhC;;;;;;;;GAQG;AACH,MAAM,MAAM,uBAAuB,CAAC,cAAc,GAAG,WAAW,GAAG,aAAa,GAAG,aAAa,IAAI;IAChG;;;OAGG;IACH,IAAI,CAAC,EAAE,cAAc,CAAC;CACzB,CAAC;AAEF,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO,GAAG,MAAM,GAAG,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;AACrE,KAAK,mBAAmB,CAAC,SAAS,SAAS,SAAS,OAAO,EAAE,IAAI,SAAS,CAAC,MAAM,CAAC,CAAC;AAEnF;;;;;;;;;;;;;;;;;;;;;;;;;;GA0BG;AACH,wBAAgB,sBAAsB,CAAC,KAAK,CAAC,SAAS,SAAS,SAAS,OAAO,EAAE,EAC7E,QAAQ,EAAE,SAAS,GACpB,gBAAgB,CAAC,mBAAmB,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;AACvD,wBAAgB,sBAAsB,CAAC,KAAK,CAAC,SAAS,SAAS,SAAS,OAAO,EAAE,EAAE,KAAK,SAAS,MAAM,EACnG,QAAQ,EAAE,SAAS,EACnB,MAAM,EAAE,uBAAuB,CAAC,aAAa,CAAC,GAAG;IAAE,IAAI,EAAE,sBAAsB,CAAC,KAAK,CAAC,CAAA;CAAE,GACzF,gBAAgB,CAAC,mBAAmB,CAAC,SAAS,CAAC,EAAE,KAAK,CAAC,CAAC;AAC3D,wBAAgB,sBAAsB,CAAC,KAAK,CAAC,SAAS,SAAS,SAAS,OAAO,EAAE,EAC7E,QAAQ,EAAE,SAAS,EACnB,MAAM,CAAC,EAAE,uBAAuB,CAAC,aAAa,CAAC,GAChD,mBAAmB,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC,CAAC;AAkBvD;;;;;;;;;;;;;;;;;;;;;;;;;;GA0BG;AACH,wBAAgB,sBAAsB,CAAC,KAAK,CAAC,SAAS,SAAS,SAAS,OAAO,EAAE,EAC7E,QAAQ,EAAE,SAAS,GACpB,gBAAgB,CAAC,mBAAmB,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;AACvD,wBAAgB,sBAAsB,CAAC,KAAK,CAAC,SAAS,SAAS,SAAS,OAAO,EAAE,EAAE,KAAK,SAAS,MAAM,EACnG,QAAQ,EAAE,SAAS,EACnB,MAAM,EAAE,uBAAuB,CAAC,aAAa,CAAC,GAAG;IAAE,IAAI,EAAE,sBAAsB,CAAC,KAAK,CAAC,CAAA;CAAE,GACzF,gBAAgB,CAAC,mBAAmB,CAAC,SAAS,CAAC,EAAE,KAAK,CAAC,CAAC;AAC3D,wBAAgB,sBAAsB,CAAC,KAAK,CAAC,SAAS,SAAS,SAAS,OAAO,EAAE,EAC7E,QAAQ,EAAE,SAAS,EACnB,MAAM,CAAC,EAAE,uBAAuB,CAAC,aAAa,CAAC,GAChD,mBAAmB,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC,CAAC;AAkBvD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAuEG;AACH,wBAAgB,oBAAoB,CAAC,KAAK,CAAC,SAAS,SAAS,SAAS,OAAO,EAAE,EAC3E,QAAQ,EAAE,SAAS,GACpB,cAAc,CAAC,mBAAmB,CAAC,SAAS,CAAC,EAAE,mBAAmB,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;AACrF,wBAAgB,oBAAoB,CAAC,KAAK,CAAC,SAAS,SAAS,SAAS,OAAO,EAAE,EAAE,KAAK,SAAS,MAAM,EACjG,QAAQ,EAAE,SAAS,EACnB,MAAM,EAAE,uBAAuB,CAAC,WAAW,CAAC,GAAG;IAAE,IAAI,EAAE,oBAAoB,CAAC,KAAK,CAAC,CAAA;CAAE,GACrF,cAAc,CAAC,mBAAmB,CAAC,SAAS,CAAC,EAAE,mBAAmB,CAAC,SAAS,CAAC,EAAE,KAAK,CAAC,CAAC;AACzF,wBAAgB,oBAAoB,CAAC,KAAK,CAAC,SAAS,SAAS,SAAS,OAAO,EAAE,EAC3E,QAAQ,EAAE,SAAS,EACnB,MAAM,CAAC,EAAE,uBAAuB,CAAC,WAAW,CAAC,GAC9C,iBAAiB,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC,CAAC"}