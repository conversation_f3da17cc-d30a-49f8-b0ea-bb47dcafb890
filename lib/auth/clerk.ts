import { auth, currentUser } from '@clerk/nextjs/server';
import { redirect } from 'next/navigation';

export async function requireAuth() {
  const { userId } = await auth();

  if (!userId) {
    redirect('/login');
  }

  return userId;
}

export async function getUser() {
  const user = await currentUser();

  if (!user) {
    return null;
  }

  return {
    id: user.id,
    email: user.emailAddresses[0]?.emailAddress || '',
    name: user.firstName
      ? `${user.firstName} ${user.lastName || ''}`.trim()
      : user.username || '',
    image: user.imageUrl,
    walletAddress: user.publicMetadata?.walletAddress as string | undefined,
  };
}

export async function getUserOrRedirect() {
  const user = await getUser();

  if (!user) {
    redirect('/login');
  }

  return user;
}

export async function getWalletAddress() {
  const user = await currentUser();
  return user?.publicMetadata?.walletAddress as string | undefined;
}

export async function linkWalletToUser(walletAddress: string) {
  const { userId } = await auth();

  if (!userId) {
    throw new Error('User not authenticated');
  }

  // This would be called from a server action or API route
  // The actual update would happen through Clerk's backend API
  return { success: true };
}
