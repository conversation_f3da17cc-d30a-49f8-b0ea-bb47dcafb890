exports.id=331,exports.ids=[331],exports.modules={14:(e,t,s)=>{"use strict";s.d(t,{AppSidebar:()=>y});var a=s(13486),n=s(2984),i=s(44743),r=s(76869),o=s(25704),l=s(49933),d=s(54293),c=s(48961),u=s(22479),m=s(72957),p=s(95712);s(40397),process.env.PLAYWRIGHT_TEST_BASE_URL||process.env.PLAYWRIGHT||process.env.CI_PLAYWRIGHT;let f=/^guest-\d+$/;function h({user:e}){let t=(0,n.useRouter)(),{data:s,status:r}=(0,d.wV)(),{setTheme:h,resolvedTheme:x}=(0,c.D)(),g=f.test(s?.user?.email??"");return(0,a.jsx)(m.wZ,{children:(0,a.jsx)(m.FX,{children:(0,a.jsxs)(u.rI,{children:[(0,a.jsx)(u.ty,{asChild:!0,children:"loading"===r?(0,a.jsxs)(m.Uj,{className:"data-[state=open]:bg-sidebar-accent bg-background data-[state=open]:text-sidebar-accent-foreground h-10 justify-between",children:[(0,a.jsxs)("div",{className:"flex flex-row gap-2",children:[(0,a.jsx)("div",{className:"size-6 bg-zinc-500/30 rounded-full animate-pulse"}),(0,a.jsx)("span",{className:"bg-zinc-500/30 text-transparent rounded-md animate-pulse",children:"Loading auth status"})]}),(0,a.jsx)("div",{className:"animate-spin text-zinc-500",children:(0,a.jsx)(i.hz,{})})]}):(0,a.jsxs)(m.Uj,{"data-testid":"user-nav-button",className:"data-[state=open]:bg-sidebar-accent bg-background data-[state=open]:text-sidebar-accent-foreground h-10",children:[(0,a.jsx)(l.default,{src:`https://avatar.vercel.sh/${e.email}`,alt:e.email??"User Avatar",width:24,height:24,className:"rounded-full"}),(0,a.jsx)("span",{"data-testid":"user-email",className:"truncate",children:g?"Guest":e?.email}),(0,a.jsx)(o.A,{className:"ml-auto"})]})}),(0,a.jsxs)(u.SQ,{"data-testid":"user-nav-menu",side:"top",className:"w-[--radix-popper-anchor-width]",children:[(0,a.jsx)(u._2,{"data-testid":"user-nav-item-theme",className:"cursor-pointer",onSelect:()=>h("dark"===x?"light":"dark"),children:`Toggle ${"light"===x?"dark":"light"} mode`}),(0,a.jsx)(u.mB,{}),(0,a.jsx)(u._2,{asChild:!0,"data-testid":"user-nav-item-auth",children:(0,a.jsx)("button",{type:"button",className:"w-full cursor-pointer",onClick:()=>{if("loading"===r)return void(0,p.o)({type:"error",description:"Checking authentication status, please try again!"});g?t.push("/login"):(0,d.CI)({redirectTo:"/"})},children:g?"Login to your account":"Sign out"})})]})]})})})}var x=s(15984),g=s(49989),b=s.n(g),v=s(71583);function y({user:e}){let t=(0,n.useRouter)(),{setOpenMobile:s}=(0,m.cL)();return(0,a.jsxs)(m.Bx,{className:"group-data-[side=left]:border-r-0",children:[(0,a.jsx)(m.Gh,{children:(0,a.jsx)(m.wZ,{children:(0,a.jsxs)("div",{className:"flex flex-row justify-between items-center",children:[(0,a.jsx)(b(),{href:"/",onClick:()=>{s(!1)},className:"flex flex-row gap-3 items-center",children:(0,a.jsx)("span",{className:"text-lg font-semibold px-2 hover:bg-muted rounded-md cursor-pointer",children:"Chatbot"})}),(0,a.jsxs)(v.m_,{children:[(0,a.jsx)(v.k$,{asChild:!0,children:(0,a.jsx)(x.$,{variant:"ghost",type:"button",className:"p-2 h-fit",onClick:()=>{s(!1),t.push("/"),t.refresh()},children:(0,a.jsx)(i.c1,{})})}),(0,a.jsx)(v.ZI,{align:"end",children:"New Chat"})]})]})})}),(0,a.jsx)(m.Yv,{children:(0,a.jsx)(r.b,{user:e})}),(0,a.jsx)(m.CG,{children:e&&(0,a.jsx)(h,{user:e})})]})}},7112:(e,t,s)=>{"use strict";s.d(t,{P:()=>n,_:()=>i});var a=s(13486);let n=({artifactKind:e})=>"image"===e?(0,a.jsx)("div",{className:"flex flex-col gap-4 w-full justify-center items-center h-[calc(100dvh-60px)]",children:(0,a.jsx)("div",{className:"animate-pulse rounded-lg bg-muted-foreground/20 size-96"})}):(0,a.jsxs)("div",{className:"flex flex-col gap-4 w-full",children:[(0,a.jsx)("div",{className:"animate-pulse rounded-lg h-12 bg-muted-foreground/20 w-1/2"}),(0,a.jsx)("div",{className:"animate-pulse rounded-lg h-5 bg-muted-foreground/20 w-full"}),(0,a.jsx)("div",{className:"animate-pulse rounded-lg h-5 bg-muted-foreground/20 w-full"}),(0,a.jsx)("div",{className:"animate-pulse rounded-lg h-5 bg-muted-foreground/20 w-1/3"}),(0,a.jsx)("div",{className:"animate-pulse rounded-lg h-5 bg-transparent w-52"}),(0,a.jsx)("div",{className:"animate-pulse rounded-lg h-8 bg-muted-foreground/20 w-52"}),(0,a.jsx)("div",{className:"animate-pulse rounded-lg h-5 bg-muted-foreground/20 w-2/3"})]}),i=()=>(0,a.jsxs)("div",{className:"flex flex-col gap-4 w-full",children:[(0,a.jsx)("div",{className:"animate-pulse rounded-lg h-4 bg-muted-foreground/20 w-48"}),(0,a.jsx)("div",{className:"animate-pulse rounded-lg h-4 bg-muted-foreground/20 w-3/4"}),(0,a.jsx)("div",{className:"animate-pulse rounded-lg h-4 bg-muted-foreground/20 w-1/2"}),(0,a.jsx)("div",{className:"animate-pulse rounded-lg h-4 bg-muted-foreground/20 w-64"}),(0,a.jsx)("div",{className:"animate-pulse rounded-lg h-4 bg-muted-foreground/20 w-40"}),(0,a.jsx)("div",{className:"animate-pulse rounded-lg h-4 bg-muted-foreground/20 w-36"}),(0,a.jsx)("div",{className:"animate-pulse rounded-lg h-4 bg-muted-foreground/20 w-64"})]})},11455:(e,t,s)=>{"use strict";async function a(e){return console.warn("getUser called with legacy implementation - needs Convex migration"),[]}async function n(){return console.warn("createGuestUser called with legacy implementation - needs Convex migration"),[{id:`guest-${Date.now()}`,email:null,password:null}]}async function i(){return console.warn("saveChat called with legacy implementation - needs Convex migration"),null}async function r(){return console.warn("deleteChatById called with legacy implementation - needs Convex migration"),null}async function o(){return console.warn("getChatsByUserId called with legacy implementation - needs Convex migration"),[]}async function l(){return console.warn("getChatById called with legacy implementation - needs Convex migration"),null}async function d(){return console.warn("saveMessages called with legacy implementation - needs Convex migration"),null}async function c(){return console.warn("getMessagesByChatId called with legacy implementation - needs Convex migration"),[]}async function u(){return console.warn("voteMessage called with legacy implementation - needs Convex migration"),null}async function m(){return console.warn("getVotesByChatId called with legacy implementation - needs Convex migration"),[]}async function p(){return console.warn("saveDocument called with legacy implementation - needs Convex migration"),null}async function f(){return console.warn("getDocumentById called with legacy implementation - needs Convex migration"),null}async function h(){return console.warn("deleteDocumentsByIdAfterTimestamp called with legacy implementation - needs Convex migration"),null}async function x(){return console.warn("saveSuggestions called with legacy implementation - needs Convex migration"),null}async function g(){return console.warn("getSuggestionsByDocumentId called with legacy implementation - needs Convex migration"),[]}async function b(){return console.warn("getMessageById called with legacy implementation - needs Convex migration"),null}async function v(){return console.warn("deleteMessagesByChatIdAfterTimestamp called with legacy implementation - needs Convex migration"),null}async function y(){return console.warn("updateChatVisiblityById called with legacy implementation - needs Convex migration"),null}async function w(){return console.warn("getStreamIdsByChatId called with legacy implementation - needs Convex migration"),[]}async function j(){return console.warn("getMessageCountByUserId called with legacy implementation - needs Convex migration"),0}async function k(){return console.warn("createStreamId called with legacy implementation - needs Convex migration"),`stream-${Date.now()}`}async function C(){return console.warn("getDocumentsById called with legacy implementation - needs Convex migration"),[]}s.d(t,{$:()=>y,Ci:()=>u,Lz:()=>o,M7:()=>k,Nm:()=>w,TJ:()=>l,W8:()=>n,_L:()=>f,bd:()=>p,de:()=>c,iB:()=>h,kA:()=>b,mV:()=>m,q1:()=>j,qQ:()=>r,tw:()=>g,wA:()=>v,wz:()=>a,xt:()=>C,yM:()=>d,yd:()=>i,zL:()=>x})},13348:(e,t,s)=>{"use strict";s.d(t,{SidebarInset:()=>n,SidebarProvider:()=>i});var a=s(33952);(0,a.registerClientReference)(function(){throw Error("Attempted to call Sidebar() from the server but Sidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/ui/sidebar.tsx","Sidebar"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarContent() from the server but SidebarContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/ui/sidebar.tsx","SidebarContent"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarFooter() from the server but SidebarFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/ui/sidebar.tsx","SidebarFooter"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarGroup() from the server but SidebarGroup is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/ui/sidebar.tsx","SidebarGroup"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarGroupAction() from the server but SidebarGroupAction is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/ui/sidebar.tsx","SidebarGroupAction"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarGroupContent() from the server but SidebarGroupContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/ui/sidebar.tsx","SidebarGroupContent"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarGroupLabel() from the server but SidebarGroupLabel is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/ui/sidebar.tsx","SidebarGroupLabel"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarHeader() from the server but SidebarHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/ui/sidebar.tsx","SidebarHeader"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarInput() from the server but SidebarInput is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/ui/sidebar.tsx","SidebarInput");let n=(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarInset() from the server but SidebarInset is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/ui/sidebar.tsx","SidebarInset");(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenu() from the server but SidebarMenu is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/ui/sidebar.tsx","SidebarMenu"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuAction() from the server but SidebarMenuAction is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/ui/sidebar.tsx","SidebarMenuAction"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuBadge() from the server but SidebarMenuBadge is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/ui/sidebar.tsx","SidebarMenuBadge"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuButton() from the server but SidebarMenuButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/ui/sidebar.tsx","SidebarMenuButton"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuItem() from the server but SidebarMenuItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/ui/sidebar.tsx","SidebarMenuItem"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuSkeleton() from the server but SidebarMenuSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/ui/sidebar.tsx","SidebarMenuSkeleton"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuSub() from the server but SidebarMenuSub is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/ui/sidebar.tsx","SidebarMenuSub"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuSubButton() from the server but SidebarMenuSubButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/ui/sidebar.tsx","SidebarMenuSubButton"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuSubItem() from the server but SidebarMenuSubItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/ui/sidebar.tsx","SidebarMenuSubItem");let i=(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarProvider() from the server but SidebarProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/ui/sidebar.tsx","SidebarProvider");(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarRail() from the server but SidebarRail is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/ui/sidebar.tsx","SidebarRail"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarSeparator() from the server but SidebarSeparator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/ui/sidebar.tsx","SidebarSeparator"),(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarTrigger() from the server but SidebarTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/ui/sidebar.tsx","SidebarTrigger"),(0,a.registerClientReference)(function(){throw Error("Attempted to call useSidebar() from the server but useSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/ui/sidebar.tsx","useSidebar")},18146:(e,t,s)=>{Promise.resolve().then(s.bind(s,14)),Promise.resolve().then(s.bind(s,55232)),Promise.resolve().then(s.bind(s,72957)),Promise.resolve().then(s.t.bind(s,87552,23))},19216:(e,t,s)=>{"use strict";s.d(t,{I:()=>c});var a=s(60159),n=s(98894),i=s(87038),r=s(29572),o=s(70358);let l=(0,o.createServerReference)("40efa9df1d11901ceec2975f80ab45e6918cbe8d99",o.callServer,void 0,o.findSourceMapURL,"updateChatVisibility");var d=s(76869);function c({chatId:e,initialVisibilityType:t}){let{mutate:s,cache:o}=(0,n.iX)(),c=o.get("/api/history")?.data,{data:u,mutate:m}=(0,i.default)(`${e}-visibility`,null,{fallbackData:t});return{visibilityType:(0,a.useMemo)(()=>{if(!c)return u;let t=c.chats.find(t=>t.id===e);return t?t.visibility:"private"},[c,e,u]),setVisibilityType:t=>{m(t),s((0,r.WI)(d.i)),l({chatId:e,visibility:t})}}}},19298:(e,t,s)=>{"use strict";s.d(t,{n:()=>o});var a=s(13486),n=s(44743),i=s(86135),r=s.n(i);function o({title:e,content:t,status:s,isInline:i}){return(0,a.jsx)("div",{className:r()("flex flex-row items-center justify-center w-full",{"h-[calc(100dvh-60px)]":!i,"h-[200px]":i}),children:"streaming"===s?(0,a.jsxs)("div",{className:"flex flex-row gap-4 items-center",children:[!i&&(0,a.jsx)("div",{className:"animate-spin",children:(0,a.jsx)(n.hz,{})}),(0,a.jsx)("div",{children:"Generating Image..."})]}):(0,a.jsx)("picture",{children:(0,a.jsx)("img",{className:r()("w-full h-fit max-w-[800px]",{"p-0 md:p-20":!i}),src:`data:image/png;base64,${t}`,alt:e})})})}},20285:(e,t,s)=>{"use strict";s.d(t,{o:()=>i});var a=s(60159),n=s(56724);function i({chatId:e,status:t}){let{containerRef:s,endRef:i,isAtBottom:r,scrollToBottom:o,onViewportEnter:l,onViewportLeave:d}=(0,n.R)(),[c,u]=(0,a.useState)(!1);return{containerRef:s,endRef:i,isAtBottom:r,scrollToBottom:o,onViewportEnter:l,onViewportLeave:d,hasSentMessage:c}}},21876:(e,t,s)=>{"use strict";s.d(t,{y:()=>d});var a=s(13486),n=s(60159),i=s(80952),r=s(33577),o=s(48961),l=s(67499);s(81719);let d=(0,n.memo)(({content:e,saveContent:t,status:s,isCurrentVersion:d})=>{let{resolvedTheme:c}=(0,o.D)(),u=(0,n.useMemo)(()=>{if(!e)return Array(50).fill(Array(26).fill(""));let t=(0,r.parse)(e,{skipEmptyLines:!0}).data.map(e=>{let t=[...e];for(;t.length<26;)t.push("");return t});for(;t.length<50;)t.push(Array(26).fill(""));return t},[e]),m=(0,n.useMemo)(()=>[{key:"rowNumber",name:"",frozen:!0,width:50,renderCell:({rowIdx:e})=>e+1,cellClass:"border-t border-r dark:bg-zinc-950 dark:text-zinc-50",headerCellClass:"border-t border-r dark:bg-zinc-900 dark:text-zinc-50"},...Array.from({length:26},(e,t)=>({key:t.toString(),name:String.fromCharCode(65+t),renderEditCell:i.jE,width:120,cellClass:(0,l.cn)("border-t dark:bg-zinc-950 dark:text-zinc-50",{"border-l":0!==t}),headerCellClass:(0,l.cn)("border-t dark:bg-zinc-900 dark:text-zinc-50",{"border-l":0!==t})}))],[]),p=(0,n.useMemo)(()=>u.map((e,t)=>{let s={id:t,rowNumber:t+1};return m.slice(1).forEach((t,a)=>{s[t.key]=e[a]||""}),s}),[u,m]),[f,h]=(0,n.useState)(p);(0,n.useEffect)(()=>{h(p)},[p]);let x=e=>(0,r.unparse)(e);return(0,a.jsx)(i.Ay,{className:"dark"===c?"rdg-dark":"rdg-light",columns:m,rows:f,enableVirtualization:!0,onRowsChange:e=>{h(e),t(x(e.map(e=>m.slice(1).map(t=>e[t.key]||""))),!0)},onCellClick:e=>{"rowNumber"!==e.column.key&&e.selectCell(!0)},style:{height:"100%"},defaultColumnOptions:{resizable:!0,sortable:!0}})},function(e,t){return e.currentVersionIndex===t.currentVersionIndex&&e.isCurrentVersion===t.isCurrentVersion&&("streaming"!==e.status||"streaming"!==t.status)&&e.content===t.content&&e.saveContent===t.saveContent})},22479:(e,t,s)=>{"use strict";s.d(t,{M5:()=>h,SQ:()=>x,_2:()=>g,dc:()=>m,lv:()=>p,mB:()=>b,nV:()=>f,rI:()=>c,ty:()=>u});var a=s(13486),n=s(60159),i=s(61463),r=s(43967),o=s(49391),l=s(72283),d=s(67499);let c=i.bL,u=i.l9;i.YJ;let m=i.ZL,p=i.Pb;i.z6;let f=n.forwardRef(({className:e,inset:t,children:s,...n},o)=>(0,a.jsxs)(i.ZP,{ref:o,className:(0,d.cn)("flex cursor-default gap-2 select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",t&&"pl-8",e),...n,children:[s,(0,a.jsx)(r.A,{className:"ml-auto"})]}));f.displayName=i.ZP.displayName;let h=n.forwardRef(({className:e,...t},s)=>(0,a.jsx)(i.G5,{ref:s,className:(0,d.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...t}));h.displayName=i.G5.displayName;let x=n.forwardRef(({className:e,sideOffset:t=4,...s},n)=>(0,a.jsx)(i.ZL,{children:(0,a.jsx)(i.UC,{ref:n,sideOffset:t,className:(0,d.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...s})}));x.displayName=i.UC.displayName;let g=n.forwardRef(({className:e,inset:t,...s},n)=>(0,a.jsx)(i.q7,{ref:n,className:(0,d.cn)("relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",t&&"pl-8",e),...s}));g.displayName=i.q7.displayName,n.forwardRef(({className:e,children:t,checked:s,...n},r)=>(0,a.jsxs)(i.H_,{ref:r,className:(0,d.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),checked:s,...n,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(i.VF,{children:(0,a.jsx)(o.A,{className:"h-4 w-4"})})}),t]})).displayName=i.H_.displayName,n.forwardRef(({className:e,children:t,...s},n)=>(0,a.jsxs)(i.hN,{ref:n,className:(0,d.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...s,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(i.VF,{children:(0,a.jsx)(l.A,{className:"h-2 w-2 fill-current"})})}),t]})).displayName=i.hN.displayName,n.forwardRef(({className:e,inset:t,...s},n)=>(0,a.jsx)(i.JU,{ref:n,className:(0,d.cn)("px-2 py-1.5 text-sm font-semibold",t&&"pl-8",e),...s})).displayName=i.JU.displayName;let b=n.forwardRef(({className:e,...t},s)=>(0,a.jsx)(i.wv,{ref:s,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",e),...t}));b.displayName=i.wv.displayName},23378:(e,t,s)=>{"use strict";s.d(t,{B:()=>i});var a=s(13486);s(98364),s(12888),s(44677),s(77418),s(36288);var n=s(60159);let i=(0,n.memo)(function({content:e,onSaveContent:t,status:s}){let i=(0,n.useRef)(null);return(0,n.useRef)(null),(0,a.jsx)("div",{className:"relative not-prose w-full pb-[calc(80dvh)] text-sm",ref:i})},function(e,t){return e.suggestions===t.suggestions&&e.currentVersionIndex===t.currentVersionIndex&&e.isCurrentVersion===t.isCurrentVersion&&("streaming"!==e.status||"streaming"!==t.status)&&e.content===t.content})},25129:(e,t,s)=>{"use strict";s.d(t,{AppSidebar:()=>a});let a=(0,s(33952).registerClientReference)(function(){throw Error("Attempted to call AppSidebar() from the server but AppSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/app-sidebar.tsx","AppSidebar")},28335:(e,t,s)=>{"use strict";s.d(t,{q:()=>i});var a=s(13486),n=s(44743);let i=({attachment:e,isUploading:t=!1})=>{let{name:s,url:i,contentType:r}=e;return(0,a.jsxs)("div",{"data-testid":"input-attachment-preview",className:"flex flex-col gap-2",children:[(0,a.jsxs)("div",{className:"w-20 h-16 aspect-video bg-muted rounded-md relative flex flex-col items-center justify-center",children:[r&&r.startsWith("image")?(0,a.jsx)("img",{src:i,alt:s??"An image attachment",className:"rounded-md size-full object-cover"},i):(0,a.jsx)("div",{className:""}),t&&(0,a.jsx)("div",{"data-testid":"input-attachment-loader",className:"animate-spin absolute text-zinc-500",children:(0,a.jsx)(n.hz,{})})]}),(0,a.jsx)("div",{className:"text-xs text-zinc-500 max-w-16 truncate",children:s})]})}},30921:(e,t,s)=>{"use strict";s.d(t,{Chat:()=>a});let a=(0,s(33952).registerClientReference)(function(){throw Error("Attempted to call Chat() from the server but Chat is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/chat.tsx","Chat")},31730:(e,t,s)=>{"use strict";s.d(t,{T:()=>r});var a=s(13486),n=s(60159),i=s(67499);let r=n.forwardRef(({className:e,...t},s)=>(0,a.jsx)("textarea",{className:(0,i.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:s,...t}));r.displayName="Textarea"},34040:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var a=s(41253);let n=async e=>[{type:"image/png",width:2400,height:1256,url:(0,a.fillMetadataSegment)("/(chat)",await e.params,"opengraph-image.png")+"?267bca02e356444c"}]},35361:(e,t,s)=>{"use strict";s.d(t,{j:()=>a});let a="chat-model"},36784:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u,experimental_ppr:()=>c});var a=s(38828),n=s(65208),i=s(25129),r=s(13348),o=s(52335),l=s(57935),d=s(93318);let c=!0;async function u({children:e}){let[t,s]=await Promise.all([(0,o.j2)(),(0,n.UL)()]),c=s.get("sidebar:state")?.value!=="true";return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(l.default,{src:"https://cdn.jsdelivr.net/pyodide/v0.23.4/full/pyodide.js",strategy:"beforeInteractive"}),(0,a.jsx)(d.DataStreamProvider,{children:(0,a.jsxs)(r.SidebarProvider,{defaultOpen:!c,children:[(0,a.jsx)(i.AppSidebar,{user:t?.user}),(0,a.jsx)(r.SidebarInset,{children:e})]})})]})}},37358:(e,t,s)=>{Promise.resolve().then(s.bind(s,48768)),Promise.resolve().then(s.bind(s,94247))},48768:(e,t,s)=>{"use strict";s.d(t,{Chat:()=>H});var a=s(13486),n=s(92059),i=s(54772),r=s(60159),o=s(98894),l=s(87038),d=s(49989),c=s.n(d),u=s(2984),m=s(17915),p=s(70358);let f=(0,p.createServerReference)("408f044626fa09ba67df1f7145568ecbd73e3f6915",p.callServer,void 0,p.findSourceMapURL,"saveChatModelAsCookie");var h=s(15984),x=s(22479);let g=[{id:"chat-model",name:"Chat model",description:"Primary model for all-purpose chat"},{id:"chat-model-reasoning",name:"Reasoning model",description:"Uses advanced reasoning"}];var b=s(67499),v=s(44743);let y={guest:{maxMessagesPerDay:20,availableChatModelIds:["chat-model","chat-model-reasoning"]},regular:{maxMessagesPerDay:100,availableChatModelIds:["chat-model","chat-model-reasoning"]}};function w({session:e,selectedModelId:t,className:s}){let[n,i]=(0,r.useState)(!1),[o,l]=(0,r.useOptimistic)(t),{availableChatModelIds:d}=y[e.user.type],c=g.filter(e=>d.includes(e.id)),u=(0,r.useMemo)(()=>c.find(e=>e.id===o),[o,c]);return(0,a.jsxs)(x.rI,{open:n,onOpenChange:i,children:[(0,a.jsx)(x.ty,{asChild:!0,className:(0,b.cn)("w-fit data-[state=open]:bg-accent data-[state=open]:text-accent-foreground",s),children:(0,a.jsxs)(h.$,{"data-testid":"model-selector",variant:"outline",className:"md:px-2 md:h-[34px]",children:[u?.name,(0,a.jsx)(v.D3,{})]})}),(0,a.jsx)(x.SQ,{align:"start",className:"min-w-[300px]",children:c.map(e=>{let{id:t}=e;return(0,a.jsx)(x._2,{"data-testid":`model-selector-item-${t}`,onSelect:()=>{i(!1),(0,r.startTransition)(()=>{l(t),f(t)})},"data-active":t===o,asChild:!0,children:(0,a.jsxs)("button",{type:"button",className:"gap-4 group/item flex flex-row justify-between items-center w-full",children:[(0,a.jsxs)("div",{className:"flex flex-col gap-1 items-start",children:[(0,a.jsx)("div",{children:e.name}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:e.description})]}),(0,a.jsx)("div",{className:"text-foreground dark:text-foreground opacity-0 group-data-[active=true]/item:opacity-100",children:(0,a.jsx)(v.PW,{})})]})},t)})})]})}var j=s(72957),k=s(71583);function C({className:e}){let{toggleSidebar:t}=(0,j.cL)();return(0,a.jsxs)(k.m_,{children:[(0,a.jsx)(k.k$,{asChild:!0,children:(0,a.jsx)(h.$,{"data-testid":"sidebar-toggle-button",onClick:t,variant:"outline",className:"md:px-2 md:h-fit",children:(0,a.jsx)(v.j0,{size:16})})}),(0,a.jsx)(k.ZI,{align:"start",children:"Toggle Sidebar"})]})}var N=s(19216);let S=[{id:"private",label:"Private",description:"Only you can access this chat",icon:(0,a.jsx)(v.XA,{})},{id:"public",label:"Public",description:"Anyone with the link can access this chat",icon:(0,a.jsx)(v.fC,{})}];function T({chatId:e,className:t,selectedVisibilityType:s}){let[n,i]=(0,r.useState)(!1),{visibilityType:o,setVisibilityType:l}=(0,N.I)({chatId:e,initialVisibilityType:s}),d=(0,r.useMemo)(()=>S.find(e=>e.id===o),[o]);return(0,a.jsxs)(x.rI,{open:n,onOpenChange:i,children:[(0,a.jsx)(x.ty,{asChild:!0,className:(0,b.cn)("w-fit data-[state=open]:bg-accent data-[state=open]:text-accent-foreground",t),children:(0,a.jsxs)(h.$,{"data-testid":"visibility-selector",variant:"outline",className:"hidden md:flex md:px-2 md:h-[34px]",children:[d?.icon,d?.label,(0,a.jsx)(v.D3,{})]})}),(0,a.jsx)(x.SQ,{align:"start",className:"min-w-[300px]",children:S.map(e=>(0,a.jsxs)(x._2,{"data-testid":`visibility-selector-item-${e.id}`,onSelect:()=>{l(e.id),i(!1)},className:"gap-4 group/item flex flex-row justify-between items-center","data-active":e.id===o,children:[(0,a.jsxs)("div",{className:"flex flex-col gap-1 items-start",children:[e.label,e.description&&(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:e.description})]}),(0,a.jsx)("div",{className:"text-foreground dark:text-foreground opacity-0 group-data-[active=true]/item:opacity-100",children:(0,a.jsx)(v.PW,{})})]},e.id))})]})}let I=(0,r.memo)(function({chatId:e,selectedModelId:t,selectedVisibilityType:s,isReadonly:n,session:i}){let r=(0,u.useRouter)(),{open:o}=(0,j.cL)(),{width:l}=(0,m.lW)();return(0,a.jsxs)("header",{className:"flex sticky top-0 bg-background py-1.5 items-center px-2 md:px-2 gap-2",children:[(0,a.jsx)(C,{}),(!o||l<768)&&(0,a.jsxs)(k.m_,{children:[(0,a.jsx)(k.k$,{asChild:!0,children:(0,a.jsxs)(h.$,{variant:"outline",className:"order-2 md:order-1 md:px-2 px-2 md:h-fit ml-auto md:ml-0",onClick:()=>{r.push("/"),r.refresh()},children:[(0,a.jsx)(v.c1,{}),(0,a.jsx)("span",{className:"md:sr-only",children:"New Chat"})]})}),(0,a.jsx)(k.ZI,{children:"New Chat"})]}),!n&&(0,a.jsx)(w,{session:i,selectedModelId:t,className:"order-1 md:order-2"}),!n&&(0,a.jsx)(T,{chatId:e,selectedVisibilityType:s,className:"order-1 md:order-3"}),(0,a.jsx)(h.$,{className:"bg-zinc-900 dark:bg-zinc-100 hover:bg-zinc-800 dark:hover:bg-zinc-200 text-zinc-50 dark:text-zinc-900 hidden md:flex py-1.5 px-2 h-fit md:h-[34px] order-4 md:ml-auto",asChild:!0,children:(0,a.jsxs)(c(),{href:'https://vercel.com/new/clone?repository-url=https://github.com/vercel/ai-chatbot&env=AUTH_SECRET&envDescription=Learn more about how to get the API Keys for the application&envLink=https://github.com/vercel/ai-chatbot/blob/main/.env.example&demo-title=AI Chatbot&demo-description=An Open-Source AI Chatbot Template Built With Next.js and the AI SDK by Vercel.&demo-url=https://chat.vercel.ai&products=[{"type":"integration","protocol":"ai","productSlug":"grok","integrationSlug":"xai"},{"type":"integration","protocol":"storage","productSlug":"neon","integrationSlug":"neon"},{"type":"integration","protocol":"storage","productSlug":"upstash-kv","integrationSlug":"upstash"},{"type":"blob"}]',target:"_noblank",children:[(0,a.jsx)(v.PY,{size:16}),"Deploy with Vercel"]})})]})},(e,t)=>e.selectedModelId===t.selectedModelId);var z=s(89514),R=s(50425),A=s(88364),_=s(89215);let D=()=>(0,a.jsxs)("div",{className:"max-w-3xl mx-auto md:mt-20 px-8 size-full flex flex-col justify-center",children:[(0,a.jsx)(_.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},exit:{opacity:0,y:10},transition:{delay:.5},className:"text-2xl font-semibold",children:"Hello there!"}),(0,a.jsx)(_.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},exit:{opacity:0,y:10},transition:{delay:.6},className:"text-2xl text-zinc-500",children:"How can I help you today?"})]},"overview");var P=s(62779),M=s.n(P),E=s(20285),V=s(55232);let U=(0,r.memo)(function({chatId:e,status:t,votes:s,messages:n,setMessages:i,regenerate:r,isReadonly:o}){let{containerRef:l,endRef:d,onViewportEnter:c,onViewportLeave:u,hasSentMessage:m}=(0,E.o)({chatId:e,status:t});return(0,V.u)(),(0,a.jsxs)("div",{ref:l,className:"flex flex-col min-w-0 gap-6 flex-1 overflow-y-scroll pt-4 relative",children:[0===n.length&&(0,a.jsx)(D,{}),n.map((l,d)=>(0,a.jsx)(A.e,{chatId:e,message:l,isLoading:"streaming"===t&&n.length-1===d,vote:s?s.find(e=>e.messageId===l.id):void 0,setMessages:i,regenerate:r,isReadonly:o,requiresScrollPadding:m&&d===n.length-1},l.id)),"submitted"===t&&n.length>0&&"user"===n[n.length-1].role&&(0,a.jsx)(A.q,{}),(0,a.jsx)(_.P.div,{ref:d,className:"shrink-0 min-w-[24px] min-h-[24px]",onViewportLeave:u,onViewportEnter:c})]})},(e,t)=>!!e.isArtifactVisible&&!!t.isArtifactVisible||e.status===t.status&&e.messages.length===t.messages.length&&!!M()(e.messages,t.messages)&&(!M()(e.votes,t.votes),!1));var $=s(95921),B=s(29572),L=s(76869),G=s(95712),O=s(91139);function H({id:e,initialMessages:t,initialChatModel:s,initialVisibilityType:d,isReadonly:c,session:m,autoResume:p}){let{visibilityType:f}=(0,N.I)({chatId:e,initialVisibilityType:d}),{mutate:h}=(0,o.iX)(),{setDataStream:x}=(0,V.u)(),[g,v]=(0,r.useState)(""),{messages:y,setMessages:w,sendMessage:j,status:k,stop:C,regenerate:S,resumeStream:T}=(0,i.Y_)({id:e,messages:t,experimental_throttle:100,generateId:b.lk,transport:new n.rL({api:"/api/chat",fetch:b.qz,prepareSendMessagesRequest:({messages:e,id:t,body:a})=>({body:{id:t,message:e.at(-1),selectedChatModel:s,selectedVisibilityType:f,...a}})}),onData:e=>{x(t=>t?[...t,e]:[])},onFinish:()=>{h((0,B.WI)(L.i))},onError:e=>{e instanceof O.P7&&(0,G.o)({type:"error",description:e.message})}});(0,u.useSearchParams)().get("query");let[A,_]=(0,r.useState)(!1),{data:D}=(0,l.default)(y.length>=2?`/api/vote?chatId=${e}`:null,b.GO),[P,M]=(0,r.useState)([]),E=(0,$.HO)(e=>e.isVisible);return!function({autoResume:e,initialMessages:t,resumeStream:s,setMessages:a}){let{dataStream:n}=(0,V.u)()}({autoResume:p,initialMessages:t,resumeStream:T,setMessages:w}),(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"flex flex-col min-w-0 h-dvh bg-background",children:[(0,a.jsx)(I,{chatId:e,selectedModelId:s,selectedVisibilityType:d,isReadonly:c,session:m}),(0,a.jsx)(U,{chatId:e,status:k,votes:D,messages:y,setMessages:w,regenerate:S,isReadonly:c,isArtifactVisible:E}),(0,a.jsx)("form",{className:"flex mx-auto px-4 bg-background pb-4 md:pb-6 gap-2 w-full md:max-w-3xl",children:!c&&(0,a.jsx)(R.Z,{chatId:e,input:g,setInput:v,status:k,stop:C,attachments:P,setAttachments:M,messages:y,setMessages:w,sendMessage:j,selectedVisibilityType:f})})]}),(0,a.jsx)(z.F,{chatId:e,input:g,setInput:v,status:k,stop:C,attachments:P,setAttachments:M,sendMessage:j,messages:y,setMessages:w,regenerate:S,votes:D,isReadonly:c,selectedVisibilityType:f})]})}},50425:(e,t,s)=>{"use strict";s.d(t,{Z:()=>j});var a=s(13486),n=s(86135),i=s.n(n),r=s(60159),o=s(81604),l=s(17915);let{useUploadThing:d,uploadFiles:c}=(0,s(1425).KB)();var u=s(44743),m=s(28335),p=s(15984),f=s(31730),h=s(89215);let x=(0,r.memo)(function({chatId:e,sendMessage:t,selectedVisibilityType:s}){return(0,a.jsx)("div",{"data-testid":"suggested-actions",className:"grid sm:grid-cols-2 gap-2 w-full",children:[{title:"What are the advantages",label:"of using Next.js?",action:"What are the advantages of using Next.js?"},{title:"Write code to",label:"demonstrate djikstra's algorithm",action:"Write code to demonstrate djikstra's algorithm"},{title:"Help me write an essay",label:"about silicon valley",action:"Help me write an essay about silicon valley"},{title:"What is the weather",label:"in San Francisco?",action:"What is the weather in San Francisco?"}].map((s,n)=>(0,a.jsx)(h.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:20},transition:{delay:.05*n},className:n>1?"hidden sm:block":"block",children:(0,a.jsxs)(p.$,{variant:"ghost",onClick:async()=>{window.history.replaceState({},"",`/chat/${e}`),t({role:"user",parts:[{type:"text",text:s.action}]})},className:"text-left border rounded-xl px-4 py-3.5 text-sm flex-1 gap-1 sm:flex-col w-full h-auto justify-start items-start",children:[(0,a.jsx)("span",{className:"font-medium",children:s.title}),(0,a.jsx)("span",{className:"text-muted-foreground",children:s.label})]})},`suggested-action-${s.title}-${n}`))})},(e,t)=>e.chatId===t.chatId&&e.selectedVisibilityType===t.selectedVisibilityType);var g=s(62779),b=s.n(g),v=s(4653),y=s(29839),w=s(56724);let j=(0,r.memo)(function({chatId:e,input:t,setInput:s,status:n,stop:c,attachments:u,setAttachments:g,messages:b,setMessages:j,sendMessage:S,className:T,selectedVisibilityType:I}){let z=(0,r.useRef)(null),{width:R}=(0,l.lW)(),A=()=>{z.current&&(z.current.style.height="auto",z.current.style.height=`${z.current.scrollHeight+2}px`)},_=()=>{z.current&&(z.current.style.height="auto",z.current.style.height="98px")},[D,P]=(0,l.Mj)("input",""),M=(0,r.useRef)(null),[E,V]=(0,r.useState)([]),{startUpload:U,isUploading:$}=d("mediaUploader",{onClientUploadComplete:e=>{console.log("Upload completed:",e)},onUploadError:e=>{console.error("Upload error:",e),o.o.error(`Upload failed: ${e.message}`)},onUploadBegin:()=>{console.log("Upload begun")}}),B=(0,r.useCallback)(()=>{window.history.replaceState({},"",`/chat/${e}`),S({role:"user",parts:[...u.map(e=>({type:"file",url:e.url,name:e.name,mediaType:e.contentType})),{type:"text",text:t}]}),g([]),P(""),_(),s(""),R&&R>768&&z.current?.focus()},[t,s,u,S,g,P,R,e]),L=(0,r.useCallback)(async e=>{try{let t=await U([e]);if(t&&t.length>0){let s=t[0];return{url:s.url,name:s.name||e.name,contentType:e.type}}throw Error("Upload failed")}catch(e){throw console.error("Upload error:",e),o.o.error("Failed to upload file, please try again!"),e}},[U]),G=(0,r.useCallback)(async e=>{let t=Array.from(e.target.files||[]);V(t.map(e=>e.name));try{let e=t.map(e=>L(e)),s=(await Promise.all(e)).filter(e=>void 0!==e);g(e=>[...e,...s])}catch(e){console.error("Error uploading files!",e)}finally{V([])}},[g,L]),{isAtBottom:O,scrollToBottom:H}=(0,w.R)();return(0,a.jsxs)("div",{className:"relative w-full flex flex-col gap-4",children:[(0,a.jsx)(v.N,{children:!O&&(0,a.jsx)(h.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},exit:{opacity:0,y:10},transition:{type:"spring",stiffness:300,damping:20},className:"absolute left-1/2 bottom-28 -translate-x-1/2 z-50",children:(0,a.jsx)(p.$,{"data-testid":"scroll-to-bottom-button",className:"rounded-full",size:"icon",variant:"outline",onClick:e=>{e.preventDefault(),H()},children:(0,a.jsx)(y.A,{})})})}),0===b.length&&0===u.length&&0===E.length&&(0,a.jsx)(x,{sendMessage:S,chatId:e,selectedVisibilityType:I}),(0,a.jsx)("input",{type:"file",className:"fixed -top-4 -left-4 size-0.5 opacity-0 pointer-events-none",ref:M,multiple:!0,accept:"image/*,application/pdf",onChange:G,tabIndex:-1}),(u.length>0||E.length>0)&&(0,a.jsxs)("div",{"data-testid":"attachments-preview",className:"flex flex-row gap-2 overflow-x-scroll items-end",children:[u.map(e=>(0,a.jsx)(m.q,{attachment:e},e.url)),E.map(e=>(0,a.jsx)(m.q,{attachment:{url:"",name:e,contentType:""},isUploading:!0},e))]}),(0,a.jsx)(f.T,{"data-testid":"multimodal-input",ref:z,placeholder:"Send a message...",value:t,onChange:e=>{s(e.target.value),A()},className:i()("min-h-[24px] max-h-[calc(75dvh)] overflow-hidden resize-none rounded-2xl !text-base bg-muted pb-10 dark:border-zinc-700",T),rows:2,autoFocus:!0,onKeyDown:e=>{"Enter"!==e.key||e.shiftKey||e.nativeEvent.isComposing||(e.preventDefault(),"ready"!==n?o.o.error("Please wait for the model to finish its response!"):B())}}),(0,a.jsx)("div",{className:"absolute bottom-0 p-2 w-fit flex flex-row justify-start",children:(0,a.jsx)(k,{fileInputRef:M,status:n})}),(0,a.jsx)("div",{className:"absolute bottom-0 right-0 p-2 w-fit flex flex-row justify-end",children:"submitted"===n?(0,a.jsx)(C,{stop:c,setMessages:j}):(0,a.jsx)(N,{input:t,submitForm:B,uploadQueue:E})})]})},(e,t)=>e.input===t.input&&e.status===t.status&&!!b()(e.attachments,t.attachments)&&e.selectedVisibilityType===t.selectedVisibilityType),k=(0,r.memo)(function({fileInputRef:e,status:t}){return(0,a.jsx)(p.$,{"data-testid":"attachments-button",className:"rounded-md rounded-bl-lg p-[7px] h-fit dark:border-zinc-700 hover:dark:bg-zinc-900 hover:bg-zinc-200",onClick:t=>{t.preventDefault(),e.current?.click()},disabled:"ready"!==t,variant:"ghost",children:(0,a.jsx)(u.Au,{size:14})})}),C=(0,r.memo)(function({stop:e,setMessages:t}){return(0,a.jsx)(p.$,{"data-testid":"stop-button",className:"rounded-full p-1.5 h-fit border dark:border-zinc-600",onClick:s=>{s.preventDefault(),e(),t(e=>e)},children:(0,a.jsx)(u.wF,{size:14})})}),N=(0,r.memo)(function({submitForm:e,input:t,uploadQueue:s}){return(0,a.jsx)(p.$,{"data-testid":"send-button",className:"rounded-full p-1.5 h-fit border dark:border-zinc-600",onClick:t=>{t.preventDefault(),e()},disabled:0===t.length||s.length>0,children:(0,a.jsx)(u.Kp,{size:14})})},(e,t)=>e.uploadQueue.length===t.uploadQueue.length&&e.input===t.input)},51160:(e,t,s)=>{"use strict";s.d(t,{K:()=>g});var a=s(13486);s(75626),s(54394);var n=s(3764),i=s(29496),r=s(60159),o=s(49516),l=s(56007),d=s(9021);s(50465),s(46516),s(93323),s(53416);var c=s(4653),u=s(89215),m=s(17915),p=s(44743),f=s(15984),h=s(67499);let x=new n.hs("suggestions");new n.k_({key:x,state:{init:()=>({decorations:i.zF.empty,selected:null}),apply(e,t){let s=e.getMeta(x);return s||{decorations:t.decorations.map(e.mapping,e.doc),selected:t.selected}}},props:{decorations(e){return this.getState(e)?.decorations??i.zF.empty}}}),new o.Sj({nodes:(0,d.ZW)(l.wQ.spec.nodes,"paragraph block*","block"),marks:l.wQ.spec.marks});let g=(0,r.memo)(function({content:e,onSaveContent:t,suggestions:s,status:n}){let i=(0,r.useRef)(null);return(0,r.useRef)(null),(0,a.jsx)("div",{className:"relative prose dark:prose-invert",ref:i})},function(e,t){return e.suggestions===t.suggestions&&e.currentVersionIndex===t.currentVersionIndex&&e.isCurrentVersion===t.isCurrentVersion&&("streaming"!==e.status||"streaming"!==t.status)&&e.content===t.content&&e.onSaveContent===t.onSaveContent})},51270:(e,t,s)=>{Promise.resolve().then(s.bind(s,30921)),Promise.resolve().then(s.bind(s,96945))},52335:(e,t,s)=>{"use strict";s.d(t,{fG:()=>l,LO:()=>d,j2:()=>c,Jv:()=>u});var a=s(89131),n=s(50109),i=s(9852),r=s(11455),o=s(73241);let{handlers:{GET:l,POST:d},auth:c,signIn:u,signOut:m}=(0,n.Ay)({...{pages:{signIn:"/login",newUser:"/"},providers:[],callbacks:{}},providers:[(0,i.A)({credentials:{},async authorize({email:e,password:t}){let s=await (0,r.wz)(e);if(0===s.length)return await (0,a.UD)(t,o.vt),null;let[n]=s;return n.password?await (0,a.UD)(t,n.password)?{...n,type:"regular"}:null:(await (0,a.UD)(t,o.vt),null)}}),(0,i.A)({id:"guest",credentials:{},async authorize(){let[e]=await (0,r.W8)();return{...e,type:"guest"}}})],callbacks:{jwt:async({token:e,user:t})=>(t&&(e.id=t.id,e.type=t.type),e),session:async({session:e,token:t})=>(e.user&&(e.user.id=t.id,e.user.type=t.type),e)}})},55232:(e,t,s)=>{"use strict";s.d(t,{DataStreamProvider:()=>r,u:()=>o});var a=s(13486),n=s(60159);let i=(0,n.createContext)(null);function r({children:e}){let[t,s]=(0,n.useState)([]),r=(0,n.useMemo)(()=>({dataStream:t,setDataStream:s}),[t]);return(0,a.jsx)(i.Provider,{value:r,children:e})}function o(){let e=(0,n.useContext)(i);if(!e)throw Error("useDataStream must be used within a DataStreamProvider");return e}},56617:(e,t,s)=>{"use strict";s.d(t,{P7:()=>n});let a={database:"log",chat:"response",auth:"response",stream:"response",api:"response",history:"response",vote:"response",document:"response",suggestions:"response"};class n extends Error{constructor(e,t){super();let[s,a]=e.split(":");this.type=s,this.cause=t,this.surface=a,this.message=function(e){if(e.includes("database"))return"An error occurred while executing a database query.";switch(e){case"bad_request:api":return"The request couldn't be processed. Please check your input and try again.";case"unauthorized:auth":return"You need to sign in before continuing.";case"forbidden:auth":return"Your account does not have access to this feature.";case"rate_limit:chat":return"You have exceeded your maximum number of messages for the day. Please try again later.";case"not_found:chat":return"The requested chat was not found. Please check the chat ID and try again.";case"forbidden:chat":return"This chat belongs to another user. Please check the chat ID and try again.";case"unauthorized:chat":return"You need to sign in to view this chat. Please sign in and try again.";case"offline:chat":return"We're having trouble sending your message. Please check your internet connection and try again.";case"not_found:document":return"The requested document was not found. Please check the document ID and try again.";case"forbidden:document":return"This document belongs to another user. Please check the document ID and try again.";case"unauthorized:document":return"You need to sign in to view this document. Please sign in and try again.";case"bad_request:document":return"The request to create or update the document was invalid. Please check your input and try again.";default:return"Something went wrong. Please try again later."}}(e),this.statusCode=function(e){switch(e){case"bad_request":return 400;case"unauthorized":return 401;case"forbidden":return 403;case"not_found":return 404;case"rate_limit":return 429;case"offline":return 503;default:return 500}}(this.type)}toResponse(){let e=`${this.type}:${this.surface}`,t=a[this.surface],{message:s,cause:n,statusCode:i}=this;return"log"===t?(console.error({code:e,message:s,cause:n}),Response.json({code:"",message:"Something went wrong. Please try again later."},{status:i})):Response.json({code:e,message:s,cause:n},{status:i})}}},56724:(e,t,s)=>{"use strict";s.d(t,{R:()=>i});var a=s(87038),n=s(60159);function i(){let e=(0,n.useRef)(null),t=(0,n.useRef)(null),{data:s=!1,mutate:i}=(0,a.default)("messages:is-at-bottom",null,{fallbackData:!1}),{data:r=!1,mutate:o}=(0,a.default)("messages:should-scroll",null,{fallbackData:!1});return{containerRef:e,endRef:t,isAtBottom:s,scrollToBottom:(0,n.useCallback)((e="smooth")=>{o(e)},[o]),onViewportEnter:function(){i(!0)},onViewportLeave:function(){i(!1)}}}},71583:(e,t,s)=>{"use strict";s.d(t,{Bc:()=>o,ZI:()=>c,k$:()=>d,m_:()=>l});var a=s(13486),n=s(60159),i=s(21743),r=s(67499);let o=i.Kq,l=i.bL,d=i.l9,c=n.forwardRef(({className:e,sideOffset:t=4,...s},n)=>(0,a.jsx)(i.UC,{ref:n,sideOffset:t,className:(0,r.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...s}));c.displayName=i.UC.displayName},72957:(e,t,s)=>{"use strict";s.d(t,{Bx:()=>N,Yv:()=>z,CG:()=>I,Cn:()=>R,rQ:()=>A,Gh:()=>T,SidebarInset:()=>S,wZ:()=>_,e7:()=>E,Uj:()=>M,FX:()=>D,SidebarProvider:()=>C,cL:()=>k});var a=s(13486),n=s(60159),i=s(90691),r=s(76353),o=s(55595),l=s(67499),d=s(15984),c=s(80018),u=s(68610);let m=n.forwardRef(({className:e,orientation:t="horizontal",decorative:s=!0,...n},i)=>(0,a.jsx)(u.b,{ref:i,decorative:s,orientation:t,className:(0,l.cn)("shrink-0 bg-border","horizontal"===t?"h-[1px] w-full":"h-full w-[1px]",e),...n}));m.displayName=u.b.displayName;var p=s(58467),f=s(1039);let h=p.bL;p.l9,p.bm;let x=p.ZL,g=n.forwardRef(({className:e,...t},s)=>(0,a.jsx)(p.hJ,{className:(0,l.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t,ref:s}));g.displayName=p.hJ.displayName;let b=(0,r.F)("fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500",{variants:{side:{top:"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",bottom:"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm",right:"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm"}},defaultVariants:{side:"right"}}),v=n.forwardRef(({side:e="right",className:t,children:s,...n},i)=>(0,a.jsxs)(x,{children:[(0,a.jsx)(g,{}),(0,a.jsxs)(p.UC,{ref:i,className:(0,l.cn)(b({side:e}),t),...n,children:[s,(0,a.jsxs)(p.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary",children:[(0,a.jsx)(f.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));function y({className:e,...t}){return(0,a.jsx)("div",{className:(0,l.cn)("animate-pulse rounded-md bg-muted",e),...t})}v.displayName=p.UC.displayName,n.forwardRef(({className:e,...t},s)=>(0,a.jsx)(p.hE,{ref:s,className:(0,l.cn)("text-lg font-semibold text-foreground",e),...t})).displayName=p.hE.displayName,n.forwardRef(({className:e,...t},s)=>(0,a.jsx)(p.VY,{ref:s,className:(0,l.cn)("text-sm text-muted-foreground",e),...t})).displayName=p.VY.displayName;var w=s(71583);let j=n.createContext(null);function k(){let e=n.useContext(j);if(!e)throw Error("useSidebar must be used within a SidebarProvider.");return e}let C=n.forwardRef(({defaultOpen:e=!0,open:t,onOpenChange:s,className:i,style:r,children:o,...d},c)=>{let u=function(){let[e,t]=n.useState(void 0);return n.useEffect(()=>{let e=window.matchMedia("(max-width: 767px)"),s=()=>{t(window.innerWidth<768)};return e.addEventListener("change",s),t(window.innerWidth<768),()=>e.removeEventListener("change",s)},[]),!!e}(),[m,p]=n.useState(!1),[f,h]=n.useState(e),x=t??f,g=n.useCallback(e=>{let t="function"==typeof e?e(x):e;s?s(t):h(t),document.cookie=`sidebar:state=${t}; path=/; max-age=604800`},[s,x]),b=n.useCallback(()=>u?p(e=>!e):g(e=>!e),[u,g,p]);n.useEffect(()=>{let e=e=>{"b"===e.key&&(e.metaKey||e.ctrlKey)&&(e.preventDefault(),b())};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[b]);let v=x?"expanded":"collapsed",y=n.useMemo(()=>({state:v,open:x,setOpen:g,isMobile:u,openMobile:m,setOpenMobile:p,toggleSidebar:b}),[v,x,g,u,m,p,b]);return(0,a.jsx)(j.Provider,{value:y,children:(0,a.jsx)(w.Bc,{delayDuration:0,children:(0,a.jsx)("div",{style:{"--sidebar-width":"16rem","--sidebar-width-icon":"3rem",...r},className:(0,l.cn)("group/sidebar-wrapper flex min-h-svh w-full has-[[data-variant=inset]]:bg-sidebar",i),ref:c,...d,children:o})})})});C.displayName="SidebarProvider";let N=n.forwardRef(({side:e="left",variant:t="sidebar",collapsible:s="offcanvas",className:n,children:i,...r},o)=>{let{isMobile:d,state:c,openMobile:u,setOpenMobile:m}=k();return"none"===s?(0,a.jsx)("div",{className:(0,l.cn)("flex h-full w-[--sidebar-width] flex-col bg-sidebar text-sidebar-foreground",n),ref:o,...r,children:i}):d?(0,a.jsx)(h,{open:u,onOpenChange:m,...r,children:(0,a.jsx)(v,{"data-sidebar":"sidebar","data-mobile":"true",className:"w-[--sidebar-width] bg-sidebar p-0 text-sidebar-foreground [&>button]:hidden",style:{"--sidebar-width":"18rem"},side:e,children:(0,a.jsx)("div",{className:"flex h-full w-full flex-col",children:i})})}):(0,a.jsxs)("div",{ref:o,className:"group peer hidden md:block text-sidebar-foreground","data-state":c,"data-collapsible":"collapsed"===c?s:"","data-variant":t,"data-side":e,children:[(0,a.jsx)("div",{className:(0,l.cn)("duration-200 relative h-svh w-[--sidebar-width] bg-transparent transition-[width] ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180","floating"===t||"inset"===t?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4))]":"group-data-[collapsible=icon]:w-[--sidebar-width-icon]")}),(0,a.jsx)("div",{className:(0,l.cn)("duration-200 fixed inset-y-0 z-10 hidden h-svh w-[--sidebar-width] transition-[left,right,width] ease-linear md:flex","left"===e?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]","floating"===t||"inset"===t?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4)_+2px)]":"group-data-[collapsible=icon]:w-[--sidebar-width-icon] group-data-[side=left]:border-r group-data-[side=right]:border-l",n),...r,children:(0,a.jsx)("div",{"data-sidebar":"sidebar",className:"flex h-full w-full flex-col bg-sidebar group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:border-sidebar-border group-data-[variant=floating]:shadow",children:i})})]})});N.displayName="Sidebar",n.forwardRef(({className:e,onClick:t,...s},n)=>{let{toggleSidebar:i}=k();return(0,a.jsxs)(d.$,{ref:n,"data-sidebar":"trigger",variant:"ghost",size:"icon",className:(0,l.cn)("h-7 w-7",e),onClick:e=>{t?.(e),i()},...s,children:[(0,a.jsx)(o.A,{}),(0,a.jsx)("span",{className:"sr-only",children:"Toggle Sidebar"})]})}).displayName="SidebarTrigger",n.forwardRef(({className:e,...t},s)=>{let{toggleSidebar:n}=k();return(0,a.jsx)("button",{ref:s,"data-sidebar":"rail","aria-label":"Toggle Sidebar",tabIndex:-1,onClick:n,title:"Toggle Sidebar",className:(0,l.cn)("absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] hover:after:bg-sidebar-border group-data-[side=left]:-right-4 group-data-[side=right]:left-0 sm:flex","[[data-side=left]_&]:cursor-w-resize [[data-side=right]_&]:cursor-e-resize","[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize","group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full group-data-[collapsible=offcanvas]:hover:bg-sidebar","[[data-side=left][data-collapsible=offcanvas]_&]:-right-2","[[data-side=right][data-collapsible=offcanvas]_&]:-left-2",e),...t})}).displayName="SidebarRail";let S=n.forwardRef(({className:e,...t},s)=>(0,a.jsx)("main",{ref:s,className:(0,l.cn)("relative flex min-h-svh flex-1 flex-col bg-background","peer-data-[variant=inset]:min-h-[calc(100svh-theme(spacing.4))] md:peer-data-[variant=inset]:m-2 md:peer-data-[state=collapsed]:peer-data-[variant=inset]:ml-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow",e),...t}));S.displayName="SidebarInset",n.forwardRef(({className:e,...t},s)=>(0,a.jsx)(c.p,{ref:s,"data-sidebar":"input",className:(0,l.cn)("h-8 w-full bg-background shadow-none focus-visible:ring-2 focus-visible:ring-sidebar-ring",e),...t})).displayName="SidebarInput";let T=n.forwardRef(({className:e,...t},s)=>(0,a.jsx)("div",{ref:s,"data-sidebar":"header",className:(0,l.cn)("flex flex-col gap-2 p-2",e),...t}));T.displayName="SidebarHeader";let I=n.forwardRef(({className:e,...t},s)=>(0,a.jsx)("div",{ref:s,"data-sidebar":"footer",className:(0,l.cn)("flex flex-col gap-2 p-2",e),...t}));I.displayName="SidebarFooter",n.forwardRef(({className:e,...t},s)=>(0,a.jsx)(m,{ref:s,"data-sidebar":"separator",className:(0,l.cn)("mx-2 w-auto bg-sidebar-border",e),...t})).displayName="SidebarSeparator";let z=n.forwardRef(({className:e,...t},s)=>(0,a.jsx)("div",{ref:s,"data-sidebar":"content",className:(0,l.cn)("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",e),...t}));z.displayName="SidebarContent";let R=n.forwardRef(({className:e,...t},s)=>(0,a.jsx)("div",{ref:s,"data-sidebar":"group",className:(0,l.cn)("relative flex w-full min-w-0 flex-col p-2",e),...t}));R.displayName="SidebarGroup",n.forwardRef(({className:e,asChild:t=!1,...s},n)=>{let r=t?i.DX:"div";return(0,a.jsx)(r,{ref:n,"data-sidebar":"group-label",className:(0,l.cn)("duration-200 flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium text-sidebar-foreground/70 outline-none ring-sidebar-ring transition-[margin,opa] ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",e),...s})}).displayName="SidebarGroupLabel",n.forwardRef(({className:e,asChild:t=!1,...s},n)=>{let r=t?i.DX:"button";return(0,a.jsx)(r,{ref:n,"data-sidebar":"group-action",className:(0,l.cn)("absolute right-3 top-3.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","after:absolute after:-inset-2 after:md:hidden","group-data-[collapsible=icon]:hidden",e),...s})}).displayName="SidebarGroupAction";let A=n.forwardRef(({className:e,...t},s)=>(0,a.jsx)("div",{ref:s,"data-sidebar":"group-content",className:(0,l.cn)("w-full text-sm",e),...t}));A.displayName="SidebarGroupContent";let _=n.forwardRef(({className:e,...t},s)=>(0,a.jsx)("ul",{ref:s,"data-sidebar":"menu",className:(0,l.cn)("flex w-full min-w-0 flex-col gap-1",e),...t}));_.displayName="SidebarMenu";let D=n.forwardRef(({className:e,...t},s)=>(0,a.jsx)("li",{ref:s,"data-sidebar":"menu-item",className:(0,l.cn)("group/menu-item relative",e),...t}));D.displayName="SidebarMenuItem";let P=(0,r.F)("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-none ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-[[data-sidebar=menu-action]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:!size-8 group-data-[collapsible=icon]:!p-2 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:!p-0"}},defaultVariants:{variant:"default",size:"default"}}),M=n.forwardRef(({asChild:e=!1,isActive:t=!1,variant:s="default",size:n="default",tooltip:r,className:o,...d},c)=>{let u=e?i.DX:"button",{isMobile:m,state:p}=k(),f=(0,a.jsx)(u,{ref:c,"data-sidebar":"menu-button","data-size":n,"data-active":t,className:(0,l.cn)(P({variant:s,size:n}),o),...d});return r?("string"==typeof r&&(r={children:r}),(0,a.jsxs)(w.m_,{children:[(0,a.jsx)(w.k$,{asChild:!0,children:f}),(0,a.jsx)(w.ZI,{side:"right",align:"center",hidden:"collapsed"!==p||m,...r})]})):f});M.displayName="SidebarMenuButton";let E=n.forwardRef(({className:e,asChild:t=!1,showOnHover:s=!1,...n},r)=>{let o=t?i.DX:"button";return(0,a.jsx)(o,{ref:r,"data-sidebar":"menu-action",className:(0,l.cn)("absolute right-1 top-1.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 peer-hover/menu-button:text-sidebar-accent-foreground [&>svg]:size-4 [&>svg]:shrink-0","after:absolute after:-inset-2 after:md:hidden","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",s&&"group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 peer-data-[active=true]/menu-button:text-sidebar-accent-foreground md:opacity-0",e),...n})});E.displayName="SidebarMenuAction",n.forwardRef(({className:e,...t},s)=>(0,a.jsx)("div",{ref:s,"data-sidebar":"menu-badge",className:(0,l.cn)("absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums text-sidebar-foreground select-none pointer-events-none","peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",e),...t})).displayName="SidebarMenuBadge",n.forwardRef(({className:e,showIcon:t=!1,...s},i)=>{let r=n.useMemo(()=>`${Math.floor(40*Math.random())+50}%`,[]);return(0,a.jsxs)("div",{ref:i,"data-sidebar":"menu-skeleton",className:(0,l.cn)("rounded-md h-8 flex gap-2 px-2 items-center",e),...s,children:[t&&(0,a.jsx)(y,{className:"size-4 rounded-md","data-sidebar":"menu-skeleton-icon"}),(0,a.jsx)(y,{className:"h-4 flex-1 max-w-[--skeleton-width]","data-sidebar":"menu-skeleton-text",style:{"--skeleton-width":r}})]})}).displayName="SidebarMenuSkeleton",n.forwardRef(({className:e,...t},s)=>(0,a.jsx)("ul",{ref:s,"data-sidebar":"menu-sub",className:(0,l.cn)("mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l border-sidebar-border px-2.5 py-0.5","group-data-[collapsible=icon]:hidden",e),...t})).displayName="SidebarMenuSub",n.forwardRef(({...e},t)=>(0,a.jsx)("li",{ref:t,...e})).displayName="SidebarMenuSubItem",n.forwardRef(({asChild:e=!1,size:t="md",isActive:s,className:n,...r},o)=>{let d=e?i.DX:"a";return(0,a.jsx)(d,{ref:o,"data-sidebar":"menu-sub-button","data-size":t,"data-active":s,className:(0,l.cn)("flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 text-sidebar-foreground outline-none ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0 [&>svg]:text-sidebar-accent-foreground","data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground","sm"===t&&"text-xs","md"===t&&"text-sm","group-data-[collapsible=icon]:hidden",n),...r})}).displayName="SidebarMenuSubButton"},73241:(e,t,s)=>{"use strict";s.d(t,{vt:()=>r,b_:()=>n,Fj:()=>a,MC:()=>i}),s(89131);let a=!0,n=!1,i=!!(process.env.PLAYWRIGHT_TEST_BASE_URL||process.env.PLAYWRIGHT||process.env.CI_PLAYWRIGHT),r="$2b$10$K1V5qz0cZGaJGDwQO4CQuu4Xr5bZnQfCqFkC7l0qoF5zVhsVz7/.2"},76869:(e,t,s)=>{"use strict";s.d(t,{b:()=>M,i:()=>P});var a=s(13486),n=s(47878),i=s(39414),r=s(12580),o=s(46488),l=s(2984),d=s(60159),c=s(81604),u=s(89215),m=s(81506),p=s(67499),f=s(15984);let h=m.bL;m.l9;let x=m.ZL,g=d.forwardRef(({className:e,...t},s)=>(0,a.jsx)(m.hJ,{className:(0,p.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t,ref:s}));g.displayName=m.hJ.displayName;let b=d.forwardRef(({className:e,...t},s)=>(0,a.jsxs)(x,{children:[(0,a.jsx)(g,{}),(0,a.jsx)(m.UC,{ref:s,className:(0,p.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...t})]}));b.displayName=m.UC.displayName;let v=({className:e,...t})=>(0,a.jsx)("div",{className:(0,p.cn)("flex flex-col space-y-2 text-center sm:text-left",e),...t});v.displayName="AlertDialogHeader";let y=({className:e,...t})=>(0,a.jsx)("div",{className:(0,p.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});y.displayName="AlertDialogFooter";let w=d.forwardRef(({className:e,...t},s)=>(0,a.jsx)(m.hE,{ref:s,className:(0,p.cn)("text-lg font-semibold",e),...t}));w.displayName=m.hE.displayName;let j=d.forwardRef(({className:e,...t},s)=>(0,a.jsx)(m.VY,{ref:s,className:(0,p.cn)("text-sm text-muted-foreground",e),...t}));j.displayName=m.VY.displayName;let k=d.forwardRef(({className:e,...t},s)=>(0,a.jsx)(m.rc,{ref:s,className:(0,p.cn)((0,f.r)(),e),...t}));k.displayName=m.rc.displayName;let C=d.forwardRef(({className:e,...t},s)=>(0,a.jsx)(m.ZD,{ref:s,className:(0,p.cn)((0,f.r)({variant:"outline"}),"mt-2 sm:mt-0",e),...t}));C.displayName=m.ZD.displayName;var N=s(72957),S=s(49989),T=s.n(S),I=s(22479),z=s(44743),R=s(19216);let A=(0,d.memo)(({chat:e,isActive:t,onDelete:s,setOpenMobile:n})=>{let{visibilityType:i,setVisibilityType:r}=(0,R.I)({chatId:e.id,initialVisibilityType:e.visibility});return(0,a.jsxs)(N.FX,{children:[(0,a.jsx)(N.Uj,{asChild:!0,isActive:t,children:(0,a.jsx)(T(),{href:`/chat/${e.id}`,onClick:()=>n(!1),children:(0,a.jsx)("span",{children:e.title})})}),(0,a.jsxs)(I.rI,{modal:!0,children:[(0,a.jsx)(I.ty,{asChild:!0,children:(0,a.jsxs)(N.e7,{className:"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground mr-0.5",showOnHover:!t,children:[(0,a.jsx)(z.jN,{}),(0,a.jsx)("span",{className:"sr-only",children:"More"})]})}),(0,a.jsxs)(I.SQ,{side:"bottom",align:"end",children:[(0,a.jsxs)(I.lv,{children:[(0,a.jsxs)(I.nV,{className:"cursor-pointer",children:[(0,a.jsx)(z.li,{}),(0,a.jsx)("span",{children:"Share"})]}),(0,a.jsx)(I.dc,{children:(0,a.jsxs)(I.M5,{children:[(0,a.jsxs)(I._2,{className:"cursor-pointer flex-row justify-between",onClick:()=>{r("private")},children:[(0,a.jsxs)("div",{className:"flex flex-row gap-2 items-center",children:[(0,a.jsx)(z.XA,{size:12}),(0,a.jsx)("span",{children:"Private"})]}),"private"===i?(0,a.jsx)(z.PW,{}):null]}),(0,a.jsxs)(I._2,{className:"cursor-pointer flex-row justify-between",onClick:()=>{r("public")},children:[(0,a.jsxs)("div",{className:"flex flex-row gap-2 items-center",children:[(0,a.jsx)(z.fC,{}),(0,a.jsx)("span",{children:"Public"})]}),"public"===i?(0,a.jsx)(z.PW,{}):null]})]})})]}),(0,a.jsxs)(I._2,{className:"cursor-pointer text-destructive focus:bg-destructive/15 focus:text-destructive dark:text-red-500",onSelect:()=>s(e.id),children:[(0,a.jsx)(z.uc,{}),(0,a.jsx)("span",{children:"Delete"})]})]})]})]})},(e,t)=>e.isActive===t.isActive);var _=s(29572);let D=e=>{let t=new Date,s=(0,n.k)(t,1),a=(0,i.a)(t,1);return e.reduce((e,t)=>{let n=new Date(t.createdAt);return(0,r.c)(n)?e.today.push(t):(0,o.P)(n)?e.yesterday.push(t):n>s?e.lastWeek.push(t):n>a?e.lastMonth.push(t):e.older.push(t),e},{today:[],yesterday:[],lastWeek:[],lastMonth:[],older:[]})};function P(e,t){if(t&&!1===t.hasMore)return null;if(0===e)return"/api/history?limit=20";let s=t.chats.at(-1);return s?`/api/history?ending_before=${s.id}&limit=20`:null}function M({user:e}){let{setOpenMobile:t}=(0,N.cL)(),{id:s}=(0,l.useParams)(),{data:n,setSize:i,isValidating:r,isLoading:o,mutate:m}=(0,_.Ay)(P,p.GO,{fallbackData:[]}),f=(0,l.useRouter)(),[x,g]=(0,d.useState)(null),[S,T]=(0,d.useState)(!1),I=!!n&&n.some(e=>!1===e.hasMore),R=!!n&&n.every(e=>0===e.chats.length),M=async()=>{let e=fetch(`/api/chat?id=${x}`,{method:"DELETE"});c.o.promise(e,{loading:"Deleting chat...",success:()=>(m(e=>{if(e)return e.map(e=>({...e,chats:e.chats.filter(e=>e.id!==x)}))}),"Chat deleted successfully"),error:"Failed to delete chat"}),T(!1),x===s&&f.push("/")};return e?o?(0,a.jsxs)(N.Cn,{children:[(0,a.jsx)("div",{className:"px-2 py-1 text-xs text-sidebar-foreground/50",children:"Today"}),(0,a.jsx)(N.rQ,{children:(0,a.jsx)("div",{className:"flex flex-col",children:[44,32,28,64,52].map(e=>(0,a.jsx)("div",{className:"rounded-md h-8 flex gap-2 px-2 items-center",children:(0,a.jsx)("div",{className:"h-4 rounded-md flex-1 max-w-[--skeleton-width] bg-sidebar-accent-foreground/10",style:{"--skeleton-width":`${e}%`}})},e))})})]}):R?(0,a.jsx)(N.Cn,{children:(0,a.jsx)(N.rQ,{children:(0,a.jsx)("div",{className:"px-2 text-zinc-500 w-full flex flex-row justify-center items-center text-sm gap-2",children:"Your conversations will appear here once you start chatting!"})})}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(N.Cn,{children:(0,a.jsxs)(N.rQ,{children:[(0,a.jsx)(N.wZ,{children:n&&(()=>{let e=D(n.flatMap(e=>e.chats));return(0,a.jsxs)("div",{className:"flex flex-col gap-6",children:[e.today.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"px-2 py-1 text-xs text-sidebar-foreground/50",children:"Today"}),e.today.map(e=>(0,a.jsx)(A,{chat:e,isActive:e.id===s,onDelete:e=>{g(e),T(!0)},setOpenMobile:t},e.id))]}),e.yesterday.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"px-2 py-1 text-xs text-sidebar-foreground/50",children:"Yesterday"}),e.yesterday.map(e=>(0,a.jsx)(A,{chat:e,isActive:e.id===s,onDelete:e=>{g(e),T(!0)},setOpenMobile:t},e.id))]}),e.lastWeek.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"px-2 py-1 text-xs text-sidebar-foreground/50",children:"Last 7 days"}),e.lastWeek.map(e=>(0,a.jsx)(A,{chat:e,isActive:e.id===s,onDelete:e=>{g(e),T(!0)},setOpenMobile:t},e.id))]}),e.lastMonth.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"px-2 py-1 text-xs text-sidebar-foreground/50",children:"Last 30 days"}),e.lastMonth.map(e=>(0,a.jsx)(A,{chat:e,isActive:e.id===s,onDelete:e=>{g(e),T(!0)},setOpenMobile:t},e.id))]}),e.older.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"px-2 py-1 text-xs text-sidebar-foreground/50",children:"Older than last month"}),e.older.map(e=>(0,a.jsx)(A,{chat:e,isActive:e.id===s,onDelete:e=>{g(e),T(!0)},setOpenMobile:t},e.id))]})]})})()}),(0,a.jsx)(u.P.div,{onViewportEnter:()=>{r||I||i(e=>e+1)}}),I?(0,a.jsx)("div",{className:"px-2 text-zinc-500 w-full flex flex-row justify-center items-center text-sm gap-2 mt-8",children:"You have reached the end of your chat history."}):(0,a.jsxs)("div",{className:"p-2 text-zinc-500 dark:text-zinc-400 flex flex-row gap-2 items-center mt-8",children:[(0,a.jsx)("div",{className:"animate-spin",children:(0,a.jsx)(z.hz,{})}),(0,a.jsx)("div",{children:"Loading Chats..."})]})]})}),(0,a.jsx)(h,{open:S,onOpenChange:T,children:(0,a.jsxs)(b,{children:[(0,a.jsxs)(v,{children:[(0,a.jsx)(w,{children:"Are you absolutely sure?"}),(0,a.jsx)(j,{children:"This action cannot be undone. This will permanently delete your chat and remove it from our servers."})]}),(0,a.jsxs)(y,{children:[(0,a.jsx)(C,{children:"Cancel"}),(0,a.jsx)(k,{onClick:M,children:"Continue"})]})]})})]}):(0,a.jsx)(N.Cn,{children:(0,a.jsx)(N.rQ,{children:(0,a.jsx)("div",{className:"px-2 text-zinc-500 w-full flex flex-row justify-center items-center text-sm gap-2",children:"Login to save and revisit previous chats!"})})})}},77877:(e,t,s)=>{"use strict";s.d(t,{b2:()=>i,lk:()=>n}),s(56617);var a=s(39369);function n(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,e=>{let t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)})}function i(e){return e.map(e=>({id:e.id,role:e.role,parts:e.parts,metadata:{createdAt:(0,a.x)(e.createdAt)}}))}},78270:(e,t,s)=>{"use strict";s.r(t),s.d(t,{"4069c99398198eb4ac29211355bb7eee8e5d5fb76c":()=>N,"40721b01082ba2a7f2716b984edc803a3f7f203933":()=>T,"408f044626fa09ba67df1f7145568ecbd73e3f6915":()=>k,"40ea7f24f3660aadbf31d6c60b5785d510b753d1e6":()=>C,"40efa9df1d11901ceec2975f80ab45e6918cbe8d99":()=>S,"7f4c08c553bf967cd0b7c8f5250f46c2e73879b9bd":()=>a.ai,"7f8502d0204c4a0fa8f39abb8d058f5a7c86a9cae8":()=>a.at,"7fea155ec4cccf4e8d3a3d15a5aae1d42efed4db52":()=>a.ot,"7ff75850b73f1dfd41bbc2ede99fb927e18bc81cf0":()=>n.y});var a=s(93878),n=s(69335),i=s(3742);s(93200);var r=s(93275),o=s(27785),l=s(63160),d=s(77949),c=s(87679),u=s(95008);let m={USER_SKY:{role:"user",content:[{type:"text",text:"Why is the sky blue?"}]},USER_GRASS:{role:"user",content:[{type:"text",text:"Why is grass green?"}]},USER_THANKS:{role:"user",content:[{type:"text",text:"Thanks!"}]},USER_NEXTJS:{role:"user",content:[{type:"text",text:"What are the advantages of using Next.js?"}]},USER_IMAGE_ATTACHMENT:{role:"user",content:[{type:"file",mediaType:"...",data:"..."},{type:"text",text:"Who painted this?"}]},USER_TEXT_ARTIFACT:{role:"user",content:[{type:"text",text:"Help me write an essay about Silicon Valley"}]},CREATE_DOCUMENT_TEXT_CALL:{role:"user",content:[{type:"text",text:"Essay about Silicon Valley"}]},CREATE_DOCUMENT_TEXT_RESULT:{role:"tool",content:[{type:"tool-result",toolCallId:"call_123",toolName:"createDocument",output:{type:"json",value:{id:"3ca386a4-40c6-4630-8ed1-84cbd46cc7eb",title:"Essay about Silicon Valley",kind:"text",content:"A document was created and is now visible to the user."}}}]},GET_WEATHER_CALL:{role:"user",content:[{type:"text",text:"What's the weather in sf?"}]},GET_WEATHER_RESULT:{role:"tool",content:[{type:"tool-result",toolCallId:"call_456",toolName:"getWeather",output:{type:"json",value:{latitude:37.763283,longitude:-122.41286,generationtime_ms:.06449222564697266,utc_offset_seconds:-25200,timezone:"America/Los_Angeles",timezone_abbreviation:"GMT-7",elevation:18,current_units:{time:"iso8601",interval:"seconds",temperature_2m:"\xb0C"},current:{time:"2025-03-10T14:00",interval:900,temperature_2m:17},daily_units:{time:"iso8601",sunrise:"iso8601",sunset:"iso8601"},daily:{time:["2025-03-10","2025-03-11","2025-03-12","2025-03-13","2025-03-14","2025-03-15","2025-03-16"],sunrise:["2025-03-10T07:27","2025-03-11T07:25","2025-03-12T07:24","2025-03-13T07:22","2025-03-14T07:21","2025-03-15T07:19","2025-03-16T07:18"],sunset:["2025-03-10T19:12","2025-03-11T19:13","2025-03-12T19:14","2025-03-13T19:15","2025-03-14T19:16","2025-03-15T19:17","2025-03-16T19:17"]}}}}]}};function p(e,t){if(e.role!==t.role||!Array.isArray(e.content)||!Array.isArray(t.content)||e.content.length!==t.content.length)return!1;for(let s=0;s<e.content.length;s++){let a=e.content[s],n=t.content[s];if(a.type!==n.type)return!1;if("file"===a.type&&"file"===n.type);else if("text"===a.type&&"text"===n.type){if(a.text!==n.text)return!1}else if("tool-result"!==a.type||"tool-result"!==n.type)return!1;else if(a.toolCallId!==n.toolCallId)return!1}return!0}let f=e=>{let t=(0,u.$C)(),s=e.split(" ").map(e=>({id:t,type:"text-delta",delta:`${e} `}));return[{id:t,type:"text-start"},...s,{id:t,type:"text-end"}]},h=e=>{let t=(0,u.$C)(),s=e.split(" ").map(e=>({id:t,type:"reasoning-delta",delta:`${e} `}));return[{id:t,type:"reasoning-start"},...s,{id:t,type:"reasoning-end"}]},x=(e,t=!1)=>{let s=e.at(-1);if(!s)throw Error("No recent message found!");if(t){if(p(s,m.USER_SKY))return[...h("The sky is blue because of rayleigh scattering!"),...f("It's just blue duh!"),{type:"finish",finishReason:"stop",usage:{inputTokens:3,outputTokens:10,totalTokens:13}}];else if(p(s,m.USER_GRASS))return[...h("Grass is green because of chlorophyll absorption!"),...f("It's just green duh!"),{type:"finish",finishReason:"stop",usage:{inputTokens:3,outputTokens:10,totalTokens:13}}]}if(p(s,m.USER_THANKS))return[...f("You're welcome!"),{type:"finish",finishReason:"stop",usage:{inputTokens:3,outputTokens:10,totalTokens:13}}];if(p(s,m.USER_GRASS))return[...f("It's just green duh!"),{type:"finish",finishReason:"stop",usage:{inputTokens:3,outputTokens:10,totalTokens:13}}];if(p(s,m.USER_SKY))return[...f("It's just blue duh!"),{type:"finish",finishReason:"stop",usage:{inputTokens:3,outputTokens:10,totalTokens:13}}];if(p(s,m.USER_NEXTJS))return[...f("With Next.js, you can ship fast!"),{type:"finish",finishReason:"stop",usage:{inputTokens:3,outputTokens:10,totalTokens:13}}];if(p(s,m.USER_IMAGE_ATTACHMENT))return[...f("This painting is by Monet!"),{type:"finish",finishReason:"stop",usage:{inputTokens:3,outputTokens:10,totalTokens:13}}];else if(p(s,m.USER_TEXT_ARTIFACT)){let e=(0,u.$C)();return[{id:e,type:"tool-input-start",toolName:"createDocument"},{id:e,type:"tool-input-delta",delta:JSON.stringify({title:"Essay about Silicon Valley",kind:"text"})},{id:e,type:"tool-input-end"},{toolCallId:e,type:"tool-result",toolName:"createDocument",result:{id:"doc_123",title:"Essay about Silicon Valley",kind:"text"}},{type:"finish",finishReason:"stop",usage:{inputTokens:3,outputTokens:10,totalTokens:13}}]}else if(p(s,m.CREATE_DOCUMENT_TEXT_CALL))return[...f(`

# Silicon Valley: The Epicenter of Innovation

## Origins and Evolution

Silicon Valley, nestled in the southern part of the San Francisco Bay Area, emerged as a global technology hub in the late 20th century. Its transformation began in the 1950s when Stanford University encouraged its graduates to start their own companies nearby, leading to the formation of pioneering semiconductor firms that gave the region its name.

## The Innovation Ecosystem

What makes Silicon Valley unique is its perfect storm of critical elements: prestigious universities like Stanford and Berkeley, abundant venture capital, a culture that celebrates risk-taking, and a dense network of talented individuals. This ecosystem has consistently nurtured groundbreaking technologies from personal computers to social media platforms to artificial intelligence.

## Challenges and Criticisms

Despite its remarkable success, Silicon Valley faces significant challenges including extreme income inequality, housing affordability crises, and questions about technology's impact on society. Critics argue the region has developed a monoculture that sometimes struggles with diversity and inclusion.

## Future Prospects

As we move forward, Silicon Valley continues to reinvent itself. While some predict its decline due to remote work trends and competition from other tech hubs, the region's adaptability and innovative spirit suggest it will remain influential in shaping our technological future for decades to come.
`),{type:"finish",finishReason:"stop",usage:{inputTokens:3,outputTokens:10,totalTokens:13}}];else if(p(s,m.CREATE_DOCUMENT_TEXT_RESULT))return[...f("A document was created and is now visible to the user."),{type:"finish",finishReason:"stop",usage:{inputTokens:3,outputTokens:10,totalTokens:13}}];else if(p(s,m.GET_WEATHER_CALL))return[{type:"tool-call",toolCallId:"call_456",toolName:"getWeather",input:JSON.stringify({latitude:37.7749,longitude:-122.4194})},{type:"finish",finishReason:"stop",usage:{inputTokens:3,outputTokens:10,totalTokens:13}}];else if(p(s,m.GET_WEATHER_RESULT))return[...f("The current temperature in San Francisco is 17\xb0C."),{type:"finish",finishReason:"stop",usage:{inputTokens:3,outputTokens:10,totalTokens:13}}];return[{id:"6",type:"text-delta",delta:"Unknown test prompt!"}]},g=new c.g0({doGenerate:async()=>({rawCall:{rawPrompt:null,rawSettings:{}},finishReason:"stop",usage:{inputTokens:10,outputTokens:20,totalTokens:30},content:[{type:"text",text:"Hello, world!"}],warnings:[]}),doStream:async({prompt:e})=>({stream:(0,r.rQ)({chunkDelayInMs:500,initialDelayInMs:1e3,chunks:x(e)}),rawCall:{rawPrompt:null,rawSettings:{}}})}),b=new c.g0({doGenerate:async()=>({rawCall:{rawPrompt:null,rawSettings:{}},finishReason:"stop",usage:{inputTokens:10,outputTokens:20,totalTokens:30},content:[{type:"text",text:"Hello, world!"}],warnings:[]}),doStream:async({prompt:e})=>({stream:(0,r.rQ)({chunkDelayInMs:500,initialDelayInMs:1e3,chunks:x(e,!0)}),rawCall:{rawPrompt:null,rawSettings:{}}})}),v=new c.g0({doGenerate:async()=>({rawCall:{rawPrompt:null,rawSettings:{}},finishReason:"stop",usage:{inputTokens:10,outputTokens:20,totalTokens:30},content:[{type:"text",text:"This is a test title"}],warnings:[]}),doStream:async()=>({stream:(0,r.rQ)({chunkDelayInMs:500,initialDelayInMs:1e3,chunks:[{id:"1",type:"text-start"},{id:"1",type:"text-delta",delta:"This is a test title"},{id:"1",type:"text-end"},{type:"finish",finishReason:"stop",usage:{inputTokens:3,outputTokens:10,totalTokens:13}}]}),rawCall:{rawPrompt:null,rawSettings:{}}})}),y=new c.g0({doGenerate:async()=>({rawCall:{rawPrompt:null,rawSettings:{}},finishReason:"stop",usage:{inputTokens:10,outputTokens:20,totalTokens:30},content:[{type:"text",text:"Hello, world!"}],warnings:[]}),doStream:async({prompt:e})=>({stream:(0,r.rQ)({chunkDelayInMs:50,initialDelayInMs:100,chunks:x(e)}),rawCall:{rawPrompt:null,rawSettings:{}}})}),w=s(53816).MC?(0,r.NN)({languageModels:{"chat-model":g,"chat-model-reasoning":b,"title-model":v,"artifact-model":y}}):(0,r.NN)({languageModels:{"chat-model":(0,d.v)("grok-2-vision-1212"),"chat-model-reasoning":(0,r.ae)({model:(0,d.v)("grok-3-mini-beta"),middleware:(0,r.Ol)({tagName:"think"})}),"title-model":(0,d.v)("grok-2-1212"),"artifact-model":(0,d.v)("grok-2-1212")},imageModels:{"small-model":d.v.imageModel("grok-2-image")}});var j=s(77256);async function k(e){(await (0,o.UL)()).set("chat-model",e)}async function C({message:e}){let{text:t}=await (0,r.Df)({model:w.languageModel("title-model"),system:`

    - you will generate a short title based on the first message a user begins a conversation with
    - ensure it is not more than 80 characters long
    - the title should be a summary of the user's message
    - do not use quotes or colons`,prompt:JSON.stringify(e)});return t}async function N({id:e}){let[t]=await (0,l.kA)({id:e});await (0,l.wA)({chatId:t.chatId,timestamp:t.createdAt})}async function S({chatId:e,visibility:t}){await (0,l.$)({chatId:e,visibility:t})}async function T({documentId:e}){return await (0,l.tw)({documentId:e})??[]}(0,j.D)([k,C,N,S]),(0,i.A)(k,"408f044626fa09ba67df1f7145568ecbd73e3f6915",null),(0,i.A)(C,"40ea7f24f3660aadbf31d6c60b5785d510b753d1e6",null),(0,i.A)(N,"4069c99398198eb4ac29211355bb7eee8e5d5fb76c",null),(0,i.A)(S,"40efa9df1d11901ceec2975f80ab45e6918cbe8d99",null),(0,j.D)([T]),(0,i.A)(T,"40721b01082ba2a7f2716b984edc803a3f7f203933",null)},83866:(e,t,s)=>{Promise.resolve().then(s.bind(s,25129)),Promise.resolve().then(s.bind(s,93318)),Promise.resolve().then(s.bind(s,13348)),Promise.resolve().then(s.t.bind(s,4150,23))},88364:(e,t,s)=>{"use strict";s.d(t,{e:()=>q,q:()=>Z});var a=s(13486),n=s(86135),i=s.n(n),r=s(4653),o=s(89215),l=s(60159),d=s(44743),c=s(81604),u=s(95921);let m=(e,t)=>{switch(e){case"create":return"present"===t?"Creating":"Created";case"update":return"present"===t?"Updating":"Updated";case"request-suggestions":return"present"===t?"Adding suggestions":"Added suggestions to";default:return null}},p=(0,l.memo)(function({type:e,result:t,isReadonly:s}){let{setArtifact:n}=(0,u.ST)();return(0,a.jsxs)("button",{type:"button",className:"bg-background cursor-pointer border py-2 px-3 rounded-xl w-fit flex flex-row gap-3 items-start",onClick:e=>{if(s)return void c.o.error("Viewing files in shared chats is currently not supported.");let a=e.currentTarget.getBoundingClientRect(),i={top:a.top,left:a.left,width:a.width,height:a.height};n({documentId:t.id,kind:t.kind,content:"",title:t.title,isVisible:!0,status:"idle",boundingBox:i})},children:[(0,a.jsx)("div",{className:"text-muted-foreground mt-1",children:"create"===e?(0,a.jsx)(d.oS,{}):"update"===e?(0,a.jsx)(d.vL,{}):"request-suggestions"===e?(0,a.jsx)(d.b1,{}):null}),(0,a.jsx)("div",{className:"text-left",children:`${m(e,"past")} "${t.title}"`})]})},()=>!0),f=(0,l.memo)(function({type:e,args:t,isReadonly:s}){let{setArtifact:n}=(0,u.ST)();return(0,a.jsxs)("button",{type:"button",className:"cursor pointer w-fit border py-2 px-3 rounded-xl flex flex-row items-start justify-between gap-3",onClick:e=>{if(s)return void c.o.error("Viewing files in shared chats is currently not supported.");let t=e.currentTarget.getBoundingClientRect(),a={top:t.top,left:t.left,width:t.width,height:t.height};n(e=>({...e,isVisible:!0,boundingBox:a}))},children:[(0,a.jsxs)("div",{className:"flex flex-row gap-3 items-start",children:[(0,a.jsx)("div",{className:"text-zinc-500 mt-1",children:"create"===e?(0,a.jsx)(d.oS,{}):"update"===e?(0,a.jsx)(d.vL,{}):"request-suggestions"===e?(0,a.jsx)(d.b1,{}):null}),(0,a.jsx)("div",{className:"text-left",children:`${m(e,"present")} ${"create"===e&&"title"in t&&t.title?`"${t.title}"`:"update"===e&&"description"in t?`"${t.description}"`:"request-suggestions"===e?"for document":""}`})]}),(0,a.jsx)("div",{className:"animate-spin mt-1",children:(0,a.jsx)(d.hz,{})})]})},()=>!0);var h=s(93323),x=s(98894),g=s(17915),b=s(15984),v=s(71583),y=s(62779),w=s.n(y);let j=(0,l.memo)(function({chatId:e,message:t,vote:s,isLoading:n}){let{mutate:i}=(0,x.iX)(),[r,o]=(0,g.Cj)();return n||"user"===t.role?null:(0,a.jsx)(v.Bc,{delayDuration:0,children:(0,a.jsxs)("div",{className:"flex flex-row gap-2",children:[(0,a.jsxs)(v.m_,{children:[(0,a.jsx)(v.k$,{asChild:!0,children:(0,a.jsx)(b.$,{className:"py-1 px-2 h-fit text-muted-foreground",variant:"outline",onClick:async()=>{let e=t.parts?.filter(e=>"text"===e.type).map(e=>e.text).join("\n").trim();if(!e)return void c.o.error("There's no text to copy!");await o(e),c.o.success("Copied to clipboard!")},children:(0,a.jsx)(d.Td,{})})}),(0,a.jsx)(v.ZI,{children:"Copy"})]}),(0,a.jsxs)(v.m_,{children:[(0,a.jsx)(v.k$,{asChild:!0,children:(0,a.jsx)(b.$,{"data-testid":"message-upvote",className:"py-1 px-2 h-fit text-muted-foreground !pointer-events-auto",disabled:s?.isUpvoted,variant:"outline",onClick:async()=>{let s=fetch("/api/vote",{method:"PATCH",body:JSON.stringify({chatId:e,messageId:t.id,type:"up"})});c.o.promise(s,{loading:"Upvoting Response...",success:()=>(i(`/api/vote?chatId=${e}`,s=>s?[...s.filter(e=>e.messageId!==t.id),{chatId:e,messageId:t.id,isUpvoted:!0}]:[],{revalidate:!1}),"Upvoted Response!"),error:"Failed to upvote response."})},children:(0,a.jsx)(d.$U,{})})}),(0,a.jsx)(v.ZI,{children:"Upvote Response"})]}),(0,a.jsxs)(v.m_,{children:[(0,a.jsx)(v.k$,{asChild:!0,children:(0,a.jsx)(b.$,{"data-testid":"message-downvote",className:"py-1 px-2 h-fit text-muted-foreground !pointer-events-auto",variant:"outline",disabled:s&&!s.isUpvoted,onClick:async()=>{let s=fetch("/api/vote",{method:"PATCH",body:JSON.stringify({chatId:e,messageId:t.id,type:"down"})});c.o.promise(s,{loading:"Downvoting Response...",success:()=>(i(`/api/vote?chatId=${e}`,s=>s?[...s.filter(e=>e.messageId!==t.id),{chatId:e,messageId:t.id,isUpvoted:!1}]:[],{revalidate:!1}),"Downvoted Response!"),error:"Failed to downvote response."})},children:(0,a.jsx)(d.EA,{})})}),(0,a.jsx)(v.ZI,{children:"Downvote Response"})]})]})})},(e,t)=>!!w()(e.vote,t.vote)&&e.isLoading===t.isLoading);var k=s(28335),C=s(85971),N=s(30213);let S={latitude:37.763283,longitude:-122.41286,generationtime_ms:.027894973754882813,utc_offset_seconds:0,timezone:"GMT",timezone_abbreviation:"GMT",elevation:18,current_units:{time:"iso8601",interval:"seconds",temperature_2m:"\xb0C"},current:{time:"2024-10-07T19:30",interval:900,temperature_2m:29.3},hourly_units:{time:"iso8601",temperature_2m:"\xb0C"},hourly:{time:["2024-10-07T00:00","2024-10-07T01:00","2024-10-07T02:00","2024-10-07T03:00","2024-10-07T04:00","2024-10-07T05:00","2024-10-07T06:00","2024-10-07T07:00","2024-10-07T08:00","2024-10-07T09:00","2024-10-07T10:00","2024-10-07T11:00","2024-10-07T12:00","2024-10-07T13:00","2024-10-07T14:00","2024-10-07T15:00","2024-10-07T16:00","2024-10-07T17:00","2024-10-07T18:00","2024-10-07T19:00","2024-10-07T20:00","2024-10-07T21:00","2024-10-07T22:00","2024-10-07T23:00","2024-10-08T00:00","2024-10-08T01:00","2024-10-08T02:00","2024-10-08T03:00","2024-10-08T04:00","2024-10-08T05:00","2024-10-08T06:00","2024-10-08T07:00","2024-10-08T08:00","2024-10-08T09:00","2024-10-08T10:00","2024-10-08T11:00","2024-10-08T12:00","2024-10-08T13:00","2024-10-08T14:00","2024-10-08T15:00","2024-10-08T16:00","2024-10-08T17:00","2024-10-08T18:00","2024-10-08T19:00","2024-10-08T20:00","2024-10-08T21:00","2024-10-08T22:00","2024-10-08T23:00","2024-10-09T00:00","2024-10-09T01:00","2024-10-09T02:00","2024-10-09T03:00","2024-10-09T04:00","2024-10-09T05:00","2024-10-09T06:00","2024-10-09T07:00","2024-10-09T08:00","2024-10-09T09:00","2024-10-09T10:00","2024-10-09T11:00","2024-10-09T12:00","2024-10-09T13:00","2024-10-09T14:00","2024-10-09T15:00","2024-10-09T16:00","2024-10-09T17:00","2024-10-09T18:00","2024-10-09T19:00","2024-10-09T20:00","2024-10-09T21:00","2024-10-09T22:00","2024-10-09T23:00","2024-10-10T00:00","2024-10-10T01:00","2024-10-10T02:00","2024-10-10T03:00","2024-10-10T04:00","2024-10-10T05:00","2024-10-10T06:00","2024-10-10T07:00","2024-10-10T08:00","2024-10-10T09:00","2024-10-10T10:00","2024-10-10T11:00","2024-10-10T12:00","2024-10-10T13:00","2024-10-10T14:00","2024-10-10T15:00","2024-10-10T16:00","2024-10-10T17:00","2024-10-10T18:00","2024-10-10T19:00","2024-10-10T20:00","2024-10-10T21:00","2024-10-10T22:00","2024-10-10T23:00","2024-10-11T00:00","2024-10-11T01:00","2024-10-11T02:00","2024-10-11T03:00"],temperature_2m:[36.6,32.8,29.5,28.6,29.2,28.2,27.5,26.6,26.5,26,25,23.5,23.9,24.2,22.9,21,24,28.1,31.4,33.9,32.1,28.9,26.9,25.2,23,21.1,19.6,18.6,17.7,16.8,16.2,15.5,14.9,14.4,14.2,13.7,13.3,12.9,12.5,13.5,15.8,17.7,19.6,21,21.9,22.3,22,20.7,18.9,17.9,17.3,17,16.7,16.2,15.6,15.2,15,15,15.1,14.8,14.8,14.9,14.7,14.8,15.3,16.2,17.9,19.6,20.5,21.6,21,20.7,19.3,18.7,18.4,17.9,17.3,17,17,16.8,16.4,16.2,16,15.8,15.7,15.4,15.4,16.1,16.7,17,18.6,19,19.5,19.4,18.5,17.9,17.5,16.7,16.3,16.1]},daily_units:{time:"iso8601",sunrise:"iso8601",sunset:"iso8601"},daily:{time:["2024-10-07","2024-10-08","2024-10-09","2024-10-10","2024-10-11"],sunrise:["2024-10-07T07:15","2024-10-08T07:16","2024-10-09T07:17","2024-10-10T07:18","2024-10-11T07:19"],sunset:["2024-10-07T19:00","2024-10-08T18:58","2024-10-09T18:57","2024-10-10T18:55","2024-10-11T18:54"]}};function T(e){return Math.ceil(e)}function I({weatherAtLocation:e=S}){let t=Math.max(...e.hourly.temperature_2m.slice(0,24)),s=Math.min(...e.hourly.temperature_2m.slice(0,24)),n=(0,C.v)(new Date(e.current.time),{start:new Date(e.daily.sunrise[0]),end:new Date(e.daily.sunset[0])}),[r,o]=(0,l.useState)(!1),d=r?5:6,c=e.hourly.time.findIndex(t=>new Date(t)>=new Date(e.current.time)),u=e.hourly.time.slice(c,c+d),m=e.hourly.temperature_2m.slice(c,c+d);return(0,a.jsxs)("div",{className:i()("flex flex-col gap-4 rounded-2xl p-4 skeleton-bg max-w-[500px]",{"bg-blue-400":n},{"bg-indigo-900":!n}),children:[(0,a.jsxs)("div",{className:"flex flex-row justify-between items-center",children:[(0,a.jsxs)("div",{className:"flex flex-row gap-2 items-center",children:[(0,a.jsx)("div",{className:i()("size-10 rounded-full skeleton-div",{"bg-yellow-300":n},{"bg-indigo-100":!n})}),(0,a.jsxs)("div",{className:"text-4xl font-medium text-blue-50",children:[T(e.current.temperature_2m),e.current_units.temperature_2m]})]}),(0,a.jsx)("div",{className:"text-blue-50",children:`H:${T(t)}\xb0 L:${T(s)}\xb0`})]}),(0,a.jsx)("div",{className:"flex flex-row justify-between",children:u.map((t,s)=>(0,a.jsxs)("div",{className:"flex flex-col items-center gap-1",children:[(0,a.jsx)("div",{className:"text-blue-100 text-xs",children:(0,N.GP)(new Date(t),"ha")}),(0,a.jsx)("div",{className:i()("size-6 rounded-full skeleton-div",{"bg-yellow-300":n},{"bg-indigo-200":!n})}),(0,a.jsxs)("div",{className:"text-blue-50 text-sm",children:[T(m[s]),e.hourly_units.temperature_2m]})]},t))})]})}var z=s(67499),R=s(31730),A=s(70358);let _=(0,A.createServerReference)("4069c99398198eb4ac29211355bb7eee8e5d5fb76c",A.callServer,void 0,A.findSourceMapURL,"deleteTrailingMessages");function D({message:e,setMode:t,setMessages:s,regenerate:n}){let[i,r]=(0,l.useState)(!1),[o,d]=(0,l.useState)((0,z.JZ)(e)),c=(0,l.useRef)(null),u=()=>{c.current&&(c.current.style.height="auto",c.current.style.height=`${c.current.scrollHeight+2}px`)};return(0,a.jsxs)("div",{className:"flex flex-col gap-2 w-full",children:[(0,a.jsx)(R.T,{"data-testid":"message-editor",ref:c,className:"bg-transparent outline-none overflow-hidden resize-none !text-base rounded-xl w-full",value:o,onChange:e=>{d(e.target.value),u()}}),(0,a.jsxs)("div",{className:"flex flex-row gap-2 justify-end",children:[(0,a.jsx)(b.$,{variant:"outline",className:"h-fit py-2 px-3",onClick:()=>{t("view")},children:"Cancel"}),(0,a.jsx)(b.$,{"data-testid":"message-editor-send-button",variant:"default",className:"h-fit py-2 px-3",disabled:i,onClick:async()=>{r(!0),await _({id:e.id}),s(t=>{let s=t.findIndex(t=>t.id===e.id);if(-1!==s){let a={...e,parts:[{type:"text",text:o}]};return[...t.slice(0,s),a]}return t}),t("view"),n()},children:i?"Sending...":"Send"})]})]})}var P=s(7112),M=s(87038),E=s(51160),V=s(23378),U=s(21876),$=s(19298);function B({isReadonly:e,result:t,args:s}){let{artifact:n,setArtifact:i}=(0,u.ST)(),{data:r,isLoading:o}=(0,M.default)(t?`/api/document?id=${t.id}`:null,z.GO),d=(0,l.useMemo)(()=>r?.[0],[r]),c=(0,l.useRef)(null);if(n.isVisible){if(t)return(0,a.jsx)(p,{type:"create",result:{id:t.id,title:t.title,kind:t.kind},isReadonly:e});if(s)return(0,a.jsx)(f,{type:"create",args:{title:s.title,kind:s.kind},isReadonly:e})}if(o)return(0,a.jsx)(L,{artifactKind:t.kind??s.kind});let m=d||("streaming"===n.status?{title:n.title,kind:n.kind,content:n.content,id:n.documentId,createdAt:new Date,userId:"noop"}:null);return m?(0,a.jsxs)("div",{className:"relative w-full cursor-pointer",children:[(0,a.jsx)(G,{hitboxRef:c,result:t,setArtifact:i}),(0,a.jsx)(O,{title:m.title,kind:m.kind,isStreaming:"streaming"===n.status}),(0,a.jsx)(H,{document:m})]}):(0,a.jsx)(L,{artifactKind:n.kind})}let L=({artifactKind:e})=>(0,a.jsxs)("div",{className:"w-full",children:[(0,a.jsxs)("div",{className:"p-4 border rounded-t-2xl flex flex-row gap-2 items-center justify-between dark:bg-muted h-[57px] dark:border-zinc-700 border-b-0",children:[(0,a.jsxs)("div",{className:"flex flex-row items-center gap-3",children:[(0,a.jsx)("div",{className:"text-muted-foreground",children:(0,a.jsx)("div",{className:"animate-pulse rounded-md size-4 bg-muted-foreground/20"})}),(0,a.jsx)("div",{className:"animate-pulse rounded-lg h-4 bg-muted-foreground/20 w-24"})]}),(0,a.jsx)("div",{children:(0,a.jsx)(d.P7,{})})]}),"image"===e?(0,a.jsx)("div",{className:"overflow-y-scroll border rounded-b-2xl bg-muted border-t-0 dark:border-zinc-700",children:(0,a.jsx)("div",{className:"animate-pulse h-[257px] bg-muted-foreground/20 w-full"})}):(0,a.jsx)("div",{className:"overflow-y-scroll border rounded-b-2xl p-8 pt-4 bg-muted border-t-0 dark:border-zinc-700",children:(0,a.jsx)(P._,{})})]}),G=(0,l.memo)(({hitboxRef:e,result:t,setArtifact:s})=>{let n=(0,l.useCallback)(e=>{let a=e.currentTarget.getBoundingClientRect();s(e=>"streaming"===e.status?{...e,isVisible:!0}:{...e,title:t.title,documentId:t.id,kind:t.kind,isVisible:!0,boundingBox:{left:a.x,top:a.y,width:a.width,height:a.height}})},[s,t]);return(0,a.jsx)("div",{className:"size-full absolute top-0 left-0 rounded-xl z-10",ref:e,onClick:n,role:"presentation","aria-hidden":"true",children:(0,a.jsx)("div",{className:"w-full p-4 flex justify-end items-center",children:(0,a.jsx)("div",{className:"absolute right-[9px] top-[13px] p-2 hover:dark:bg-zinc-700 rounded-md hover:bg-zinc-100",children:(0,a.jsx)(d.P7,{})})})})},(e,t)=>!!w()(e.result,t.result)),O=(0,l.memo)(({title:e,kind:t,isStreaming:s})=>(0,a.jsxs)("div",{className:"p-4 border rounded-t-2xl flex flex-row gap-2 items-start sm:items-center justify-between dark:bg-muted border-b-0 dark:border-zinc-700",children:[(0,a.jsxs)("div",{className:"flex flex-row items-start sm:items-center gap-3",children:[(0,a.jsx)("div",{className:"text-muted-foreground",children:s?(0,a.jsx)("div",{className:"animate-spin",children:(0,a.jsx)(d.hz,{})}):"image"===t?(0,a.jsx)(d.xf,{}):(0,a.jsx)(d.oS,{})}),(0,a.jsx)("div",{className:"-translate-y-1 sm:translate-y-0 font-medium",children:e})]}),(0,a.jsx)("div",{className:"w-8"})]}),(e,t)=>e.title===t.title&&e.isStreaming===t.isStreaming),H=({document:e})=>{let{artifact:t}=(0,u.ST)(),s=(0,z.cn)("h-[257px] overflow-y-scroll border rounded-b-2xl dark:bg-muted border-t-0 dark:border-zinc-700",{"p-4 sm:px-14 sm:py-16":"text"===e.kind,"p-0":"code"===e.kind}),n={content:e.content??"",isCurrentVersion:!0,currentVersionIndex:0,status:t.status,saveContent:()=>{},suggestions:[]};return(0,a.jsx)("div",{className:s,children:"text"===e.kind?(0,a.jsx)(E.K,{...n,onSaveContent:()=>{}}):"code"===e.kind?(0,a.jsx)("div",{className:"flex flex-1 relative w-full",children:(0,a.jsx)("div",{className:"absolute inset-0",children:(0,a.jsx)(V.B,{...n,onSaveContent:()=>{}})})}):"sheet"===e.kind?(0,a.jsx)("div",{className:"flex flex-1 relative size-full p-4",children:(0,a.jsx)("div",{className:"absolute inset-0",children:(0,a.jsx)(U.y,{...n})})}):"image"===e.kind?(0,a.jsx)($.n,{title:e.title,content:e.content??"",isCurrentVersion:!0,currentVersionIndex:0,status:t.status,isInline:!0}):null})};function W({isLoading:e,reasoning:t}){let[s,n]=(0,l.useState)(!0);return(0,a.jsxs)("div",{className:"flex flex-col",children:[e?(0,a.jsxs)("div",{className:"flex flex-row gap-2 items-center",children:[(0,a.jsx)("div",{className:"font-medium",children:"Reasoning"}),(0,a.jsx)("div",{className:"animate-spin",children:(0,a.jsx)(d.hz,{})})]}):(0,a.jsxs)("div",{className:"flex flex-row gap-2 items-center",children:[(0,a.jsx)("div",{className:"font-medium",children:"Reasoned for a few seconds"}),(0,a.jsx)("button",{"data-testid":"message-reasoning-toggle",type:"button",className:"cursor-pointer",onClick:()=>{n(!s)},children:(0,a.jsx)(d.D3,{})})]}),(0,a.jsx)(r.N,{initial:!1,children:s&&(0,a.jsx)(o.P.div,{"data-testid":"message-reasoning",initial:"collapsed",animate:"expanded",exit:"collapsed",variants:{collapsed:{height:0,opacity:0,marginTop:0,marginBottom:0},expanded:{height:"auto",opacity:1,marginTop:"1rem",marginBottom:"0.5rem"}},transition:{duration:.2,ease:"easeInOut"},style:{overflow:"hidden"},className:"pl-4 text-zinc-600 dark:text-zinc-400 border-l flex flex-col gap-4",children:(0,a.jsx)(h.o,{children:t})},"content")})]})}var F=s(55232);let q=(0,l.memo)(({chatId:e,message:t,vote:s,isLoading:n,setMessages:i,regenerate:c,isReadonly:u,requiresScrollPadding:m})=>{let[x,g]=(0,l.useState)("view"),y=t.parts.filter(e=>"file"===e.type);return(0,F.u)(),(0,a.jsx)(r.N,{children:(0,a.jsx)(o.P.div,{"data-testid":`message-${t.role}`,className:"w-full mx-auto max-w-3xl px-4 group/message",initial:{y:5,opacity:0},animate:{y:0,opacity:1},"data-role":t.role,children:(0,a.jsxs)("div",{className:(0,z.cn)("flex gap-4 w-full group-data-[role=user]/message:ml-auto group-data-[role=user]/message:max-w-2xl",{"w-full":"edit"===x,"group-data-[role=user]/message:w-fit":"edit"!==x}),children:["assistant"===t.role&&(0,a.jsx)("div",{className:"size-8 flex items-center rounded-full justify-center ring-1 shrink-0 ring-border bg-background",children:(0,a.jsx)("div",{className:"translate-y-px",children:(0,a.jsx)(d.BZ,{size:14})})}),(0,a.jsxs)("div",{className:(0,z.cn)("flex flex-col gap-4 w-full",{"min-h-96":"assistant"===t.role&&m}),children:[y.length>0&&(0,a.jsx)("div",{"data-testid":"message-attachments",className:"flex flex-row justify-end gap-2",children:y.map(e=>(0,a.jsx)(k.q,{attachment:{name:e.filename??"file",contentType:e.mediaType,url:e.url}},e.url))}),t.parts?.map((e,s)=>{let{type:r}=e,o=`message-${t.id}-part-${s}`;if("reasoning"===r&&e.text?.trim().length>0)return(0,a.jsx)(W,{isLoading:n,reasoning:e.text},o);if("text"===r){if("view"===x)return(0,a.jsxs)("div",{className:"flex flex-row gap-2 items-start",children:["user"===t.role&&!u&&(0,a.jsxs)(v.m_,{children:[(0,a.jsx)(v.k$,{asChild:!0,children:(0,a.jsx)(b.$,{"data-testid":"message-edit-button",variant:"ghost",className:"px-2 h-fit rounded-full text-muted-foreground opacity-0 group-hover/message:opacity-100",onClick:()=>{g("edit")},children:(0,a.jsx)(d.vL,{})})}),(0,a.jsx)(v.ZI,{children:"Edit message"})]}),(0,a.jsx)("div",{"data-testid":"message-content",className:(0,z.cn)("flex flex-col gap-4",{"bg-primary text-primary-foreground px-3 py-2 rounded-xl":"user"===t.role}),children:(0,a.jsx)(h.o,{children:(0,z.jZ)(e.text)})})]},o);if("edit"===x)return(0,a.jsxs)("div",{className:"flex flex-row gap-2 items-start",children:[(0,a.jsx)("div",{className:"size-8"}),(0,a.jsx)(D,{message:t,setMode:g,setMessages:i,regenerate:c},t.id)]},o)}if("tool-getWeather"===r){let{toolCallId:t,state:s}=e;if("input-available"===s)return(0,a.jsx)("div",{className:"skeleton",children:(0,a.jsx)(I,{})},t);if("output-available"===s){let{output:s}=e;return(0,a.jsx)("div",{children:(0,a.jsx)(I,{weatherAtLocation:s})},t)}}if("tool-createDocument"===r){let{toolCallId:t,state:s}=e;if("input-available"===s){let{input:s}=e;return(0,a.jsx)("div",{children:(0,a.jsx)(B,{isReadonly:u,args:s})},t)}if("output-available"===s){let{output:s}=e;return"error"in s?(0,a.jsxs)("div",{className:"text-red-500 p-2 border rounded",children:["Error: ",String(s.error)]},t):(0,a.jsx)("div",{children:(0,a.jsx)(B,{isReadonly:u,result:s})},t)}}if("tool-updateDocument"===r){let{toolCallId:t,state:s}=e;if("input-available"===s){let{input:s}=e;return(0,a.jsx)("div",{children:(0,a.jsx)(f,{type:"update",args:s,isReadonly:u})},t)}if("output-available"===s){let{output:s}=e;return"error"in s?(0,a.jsxs)("div",{className:"text-red-500 p-2 border rounded",children:["Error: ",String(s.error)]},t):(0,a.jsx)("div",{children:(0,a.jsx)(p,{type:"update",result:s,isReadonly:u})},t)}}if("tool-requestSuggestions"===r){let{toolCallId:t,state:s}=e;if("input-available"===s){let{input:s}=e;return(0,a.jsx)("div",{children:(0,a.jsx)(f,{type:"request-suggestions",args:s,isReadonly:u})},t)}if("output-available"===s){let{output:s}=e;return"error"in s?(0,a.jsxs)("div",{className:"text-red-500 p-2 border rounded",children:["Error: ",String(s.error)]},t):(0,a.jsx)("div",{children:(0,a.jsx)(p,{type:"request-suggestions",result:s,isReadonly:u})},t)}}}),!u&&(0,a.jsx)(j,{chatId:e,message:t,vote:s,isLoading:n},`action-${t.id}`)]})]})})})},(e,t)=>e.isLoading===t.isLoading&&e.message.id===t.message.id&&e.requiresScrollPadding===t.requiresScrollPadding&&!!w()(e.message.parts,t.message.parts)&&(!w()(e.vote,t.vote),!1)),Z=()=>(0,a.jsx)(o.P.div,{"data-testid":"message-assistant-loading",className:"w-full mx-auto max-w-3xl px-4 group/message min-h-96",initial:{y:5,opacity:0},animate:{y:0,opacity:1,transition:{delay:1}},"data-role":"assistant",children:(0,a.jsxs)("div",{className:i()("flex gap-4 group-data-[role=user]/message:px-3 w-full group-data-[role=user]/message:w-fit group-data-[role=user]/message:ml-auto group-data-[role=user]/message:max-w-2xl group-data-[role=user]/message:py-2 rounded-xl",{"group-data-[role=user]/message:bg-muted":!0}),children:[(0,a.jsx)("div",{className:"size-8 flex items-center rounded-full justify-center ring-1 shrink-0 ring-border",children:(0,a.jsx)(d.BZ,{size:14})}),(0,a.jsx)("div",{className:"flex flex-col gap-2 w-full",children:(0,a.jsx)("div",{className:"flex flex-col gap-4 text-muted-foreground",children:"Hmm..."})})]})})},89514:(e,t,s)=>{"use strict";s.d(t,{F:()=>eP,R:()=>eD});var a=s(13486),n=s(6794),i=s(4653),r=s(89215),o=s(60159),l=s(87038),d=s(98894),c=s(17915),u=s(67499),m=s(50425),p=s(86135),f=s.n(p),h=s(57312),x=s(52966),g=s(41840),b=s(71583),v=s(44743);let y=({description:e,icon:t,selectedTool:s,setSelectedTool:n,isToolbarVisible:i,setIsToolbarVisible:l,isAnimating:d,sendMessage:c,onClick:u})=>{let[m,p]=(0,o.useState)(!1);(0,o.useEffect)(()=>{s!==e&&p(!1)},[s,e]);let h=()=>{if(!i&&l)return void l(!0);if(!s){p(!0),n(e);return}s!==e?n(e):(n(null),u({sendMessage:c}))};return(0,a.jsxs)(b.m_,{open:m&&!d,children:[(0,a.jsx)(b.k$,{asChild:!0,children:(0,a.jsx)(r.P.div,{className:f()("p-3 rounded-full",{"bg-primary !text-primary-foreground":s===e}),onHoverStart:()=>{p(!0)},onHoverEnd:()=>{s!==e&&p(!1)},onKeyDown:e=>{"Enter"===e.key&&h()},initial:{scale:1,opacity:0},animate:{opacity:1,transition:{delay:.1}},whileHover:{scale:1.1},whileTap:{scale:.95},exit:{scale:.9,opacity:0,transition:{duration:.1}},onClick:()=>{h()},children:s===e?(0,a.jsx)(v.Kp,{}):t})}),(0,a.jsx)(b.ZI,{side:"left",sideOffset:16,className:"bg-foreground text-background rounded-2xl p-3 px-4",children:e})]})},w=[...Array(6)].map(e=>(0,g.Ak)(5)),j=({setSelectedTool:e,sendMessage:t,isAnimating:s})=>{let n=["Elementary","Middle School","Keep current level","High School","College","Graduate"],i=(0,h.d)(-80),l=(0,x.G)(i,[0,-202],[0,5]),[d,c]=(0,o.useState)(2),[u,m]=(0,o.useState)(!1);return(0,o.useEffect)(()=>{let e=l.on("change",e=>{c(Math.min(5,Math.max(0,Math.round(Math.abs(e)))))});return()=>e()},[l]),(0,a.jsxs)("div",{className:"relative flex flex-col justify-end items-center",children:[w.map(e=>(0,a.jsx)(r.P.div,{className:"size-[40px] flex flex-row items-center justify-center",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{delay:.1},children:(0,a.jsx)("div",{className:"size-2 rounded-full bg-muted-foreground/40"})},e)),(0,a.jsx)(b.Bc,{children:(0,a.jsxs)(b.m_,{open:!s,children:[(0,a.jsx)(b.k$,{asChild:!0,children:(0,a.jsx)(r.P.div,{className:f()("absolute bg-background p-3 border rounded-full flex flex-row items-center",{"bg-primary text-primary-foreground":2!==d,"bg-background text-foreground":2===d}),style:{y:i},drag:"y",dragElastic:0,dragMomentum:!1,whileHover:{scale:1.05},whileTap:{scale:.95},transition:{duration:.1},dragConstraints:{top:-202,bottom:0},onDragStart:()=>{m(!1)},onDragEnd:()=>{2===d?e(null):m(!0)},onClick:()=>{2!==d&&u&&(t({role:"user",parts:[{type:"text",text:`Please adjust the reading level to ${n[d]} level.`}]}),e(null))},children:2===d?(0,a.jsx)(v.hD,{}):(0,a.jsx)(v.Kp,{})})}),(0,a.jsx)(b.ZI,{side:"left",sideOffset:16,className:"bg-foreground text-background text-sm rounded-2xl p-3 px-4",children:n[d]})]})})]})},k=({isToolbarVisible:e,selectedTool:t,setSelectedTool:s,sendMessage:n,isAnimating:o,setIsToolbarVisible:l,tools:d})=>{let[c,...u]=d;return(0,a.jsxs)(r.P.div,{className:"flex flex-col gap-1.5",initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.95},children:[(0,a.jsx)(i.N,{children:e&&u.map(e=>(0,a.jsx)(y,{description:e.description,icon:e.icon,selectedTool:t,setSelectedTool:s,sendMessage:n,isAnimating:o,onClick:e.onClick},e.description))}),(0,a.jsx)(y,{description:c.description,icon:c.icon,selectedTool:t,setSelectedTool:s,isToolbarVisible:e,setIsToolbarVisible:l,sendMessage:n,isAnimating:o,onClick:c.onClick})]})},C=(0,o.memo)(({isToolbarVisible:e,setIsToolbarVisible:t,sendMessage:s,status:n,stop:i,setMessages:l,artifactKind:d})=>{let u=(0,o.useRef)(null),m=(0,o.useRef)(),[p,f]=(0,o.useState)(null),[h,x]=(0,o.useState)(!1);(0,c.Wr)(u,()=>{t(!1),f(null)});let g=()=>{m.current&&clearTimeout(m.current),m.current=setTimeout(()=>{f(null),t(!1)},2e3)},y=()=>{m.current&&clearTimeout(m.current)};(0,o.useEffect)(()=>()=>{m.current&&clearTimeout(m.current)},[]),(0,o.useEffect)(()=>{"streaming"===n&&t(!1)},[n,t]);let w=eD.find(e=>e.kind===d);if(!w)throw Error("Artifact definition not found!");let C=w.toolbar;return 0===C.length?null:(0,a.jsx)(b.Bc,{delayDuration:0,children:(0,a.jsx)(r.P.div,{className:"cursor-pointer absolute right-6 bottom-6 p-1.5 border rounded-full shadow-lg bg-background flex flex-col justify-end",initial:{opacity:0,y:-20,scale:1},animate:e?"adjust-reading-level"===p?{opacity:1,y:0,height:258,transition:{delay:0},scale:.95}:{opacity:1,y:0,height:50*C.length,transition:{delay:0},scale:1}:{opacity:1,y:0,height:54,transition:{delay:0}},exit:{opacity:0,y:-20,transition:{duration:.1}},transition:{type:"spring",stiffness:300,damping:25},onHoverStart:()=>{"streaming"!==n&&(y(),t(!0))},onHoverEnd:()=>{"streaming"!==n&&g()},onAnimationStart:()=>{x(!0)},onAnimationComplete:()=>{x(!1)},ref:u,children:"streaming"===n?(0,a.jsx)(r.P.div,{initial:{scale:1},animate:{scale:1.4},exit:{scale:1},className:"p-3",onClick:()=>{i(),l(e=>e)},children:(0,a.jsx)(v.wF,{})},"stop-icon"):"adjust-reading-level"===p?(0,a.jsx)(j,{sendMessage:s,setSelectedTool:f,isAnimating:h},"reading-level-selector"):(0,a.jsx)(k,{sendMessage:s,isAnimating:h,isToolbarVisible:e,selectedTool:p,setIsToolbarVisible:t,setSelectedTool:f,tools:C},"tools")})})},(e,t)=>e.status===t.status&&e.isToolbarVisible===t.isToolbarVisible&&e.artifactKind===t.artifactKind);var N=s(90575),S=s(15984),T=s(95921);let I=({handleVersionChange:e,documents:t,currentVersionIndex:s})=>{let{artifact:n}=(0,T.ST)(),{width:i}=(0,c.lW)(),l=i<768,{mutate:m}=(0,d.iX)(),[p,f]=(0,o.useState)(!1);if(t)return(0,a.jsxs)(r.P.div,{className:"absolute flex flex-col gap-4 lg:flex-row bottom-0 bg-background p-4 w-full border-t z-50 justify-between",initial:{y:l?200:77},animate:{y:0},exit:{y:l?200:77},transition:{type:"spring",stiffness:140,damping:20},children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{children:"You are viewing a previous version"}),(0,a.jsx)("div",{className:"text-muted-foreground text-sm",children:"Restore this version to make edits"})]}),(0,a.jsxs)("div",{className:"flex flex-row gap-4",children:[(0,a.jsxs)(S.$,{disabled:p,onClick:async()=>{f(!0),m(`/api/document?id=${n.documentId}`,await fetch(`/api/document?id=${n.documentId}&timestamp=${(0,u.Dn)(t,s)}`,{method:"DELETE"}),{optimisticData:t?[...t.filter(e=>(0,N.d)(new Date(e.createdAt),new Date((0,u.Dn)(t,s))))]:[]})},children:[(0,a.jsx)("div",{children:"Restore this version"}),p&&(0,a.jsx)("div",{className:"animate-spin",children:(0,a.jsx)(v.hz,{})})]}),(0,a.jsx)(S.$,{variant:"outline",onClick:()=>{e("latest")},children:"Back to latest version"})]})]})};var z=s(81604);let R=(0,o.memo)(function({artifact:e,handleVersionChange:t,currentVersionIndex:s,isCurrentVersion:n,mode:i,metadata:r,setMetadata:l}){let[d,c]=(0,o.useState)(!1),m=eD.find(t=>t.kind===e.kind);if(!m)throw Error("Artifact definition not found!");let p={content:e.content,handleVersionChange:t,currentVersionIndex:s,isCurrentVersion:n,mode:i,metadata:r,setMetadata:l};return(0,a.jsx)("div",{className:"flex flex-row gap-1",children:m.actions.map(t=>(0,a.jsxs)(b.m_,{children:[(0,a.jsx)(b.k$,{asChild:!0,children:(0,a.jsxs)(S.$,{variant:"outline",className:(0,u.cn)("h-fit dark:hover:bg-zinc-700",{"p-2":!t.label,"py-1.5 px-2":t.label}),onClick:async()=>{c(!0);try{await Promise.resolve(t.onClick(p))}catch(e){z.o.error("Failed to execute action")}finally{c(!1)}},disabled:!!d||"streaming"===e.status||!!t.isDisabled&&t.isDisabled(p),children:[t.icon,t.label]})}),(0,a.jsx)(b.ZI,{children:t.description})]},t.description))})},(e,t)=>e.artifact.status===t.artifact.status&&e.currentVersionIndex===t.currentVersionIndex&&e.isCurrentVersion===t.isCurrentVersion&&e.artifact.content===t.artifact.content),A=(0,o.memo)(function(){let{setArtifact:e}=(0,T.ST)();return(0,a.jsx)(S.$,{"data-testid":"artifact-close-button",variant:"outline",className:"h-fit p-2 dark:hover:bg-zinc-700",onClick:()=>{e(e=>"streaming"===e.status?{...e,isVisible:!1}:{...T.ls,status:"idle"})},children:(0,a.jsx)(v.w0,{size:18})})},()=>!0);var _=s(88364),D=s(62779),P=s.n(D),M=s(20285);let E=(0,o.memo)(function({chatId:e,status:t,votes:s,messages:n,setMessages:i,regenerate:o,isReadonly:l}){let{containerRef:d,endRef:c,onViewportEnter:u,onViewportLeave:m,hasSentMessage:p}=(0,M.o)({chatId:e,status:t});return(0,a.jsxs)("div",{ref:d,className:"flex flex-col gap-4 h-full items-center overflow-y-scroll px-4 pt-20",children:[n.map((r,d)=>(0,a.jsx)(_.e,{chatId:e,message:r,isLoading:"streaming"===t&&d===n.length-1,vote:s?s.find(e=>e.messageId===r.id):void 0,setMessages:i,regenerate:o,isReadonly:l,requiresScrollPadding:p&&d===n.length-1},r.id)),"submitted"===t&&n.length>0&&"user"===n[n.length-1].role&&(0,a.jsx)(_.q,{}),(0,a.jsx)(r.P.div,{ref:c,className:"shrink-0 min-w-[24px] min-h-[24px]",onViewportLeave:m,onViewportEnter:u})]})},function(e,t){return"streaming"===e.artifactStatus&&"streaming"===t.artifactStatus||e.status===t.status&&(!e.status||!t.status)&&e.messages.length===t.messages.length&&!!P()(e.votes,t.votes)});var V=s(72957);class U{constructor(e){this.kind=e.kind,this.description=e.description,this.content=e.content,this.actions=e.actions||[],this.toolbar=e.toolbar||[],this.initialize=e.initialize||(async()=>({})),this.onStreamPart=e.onStreamPart}}let $=new U({kind:"image",description:"Useful for image generation",onStreamPart:({streamPart:e,setArtifact:t})=>{"data-imageDelta"===e.type&&t(t=>({...t,content:e.data,isVisible:!0,status:"streaming"}))},content:s(19298).n,actions:[{icon:(0,a.jsx)(v.ej,{size:18}),description:"View Previous version",onClick:({handleVersionChange:e})=>{e("prev")},isDisabled:({currentVersionIndex:e})=>0===e},{icon:(0,a.jsx)(v.Uf,{size:18}),description:"View Next version",onClick:({handleVersionChange:e})=>{e("next")},isDisabled:({isCurrentVersion:e})=>!!e},{icon:(0,a.jsx)(v.Td,{size:18}),description:"Copy image to clipboard",onClick:({content:e})=>{let t=new Image;t.src=`data:image/png;base64,${e}`,t.onload=()=>{let e=document.createElement("canvas");e.width=t.width,e.height=t.height;let s=e.getContext("2d");s?.drawImage(t,0,0),e.toBlob(e=>{e&&navigator.clipboard.write([new ClipboardItem({"image/png":e})])},"image/png")},z.o.success("Copied image to clipboard!")}}],toolbar:[]});var B=s(23378);function L({consoleOutputs:e,setConsoleOutputs:t}){let[s,n]=(0,o.useState)(300),[i,r]=(0,o.useState)(!1),l=(0,o.useRef)(null);(0,T.HO)(e=>e.isVisible);let d=(0,o.useCallback)(()=>{r(!0)},[]);return(0,o.useCallback)(()=>{r(!1)},[]),(0,o.useCallback)(e=>{if(i){let t=window.innerHeight-e.clientY;t>=100&&t<=800&&n(t)}},[i]),e.length>0?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"h-2 w-full fixed cursor-ns-resize z-50",onMouseDown:d,style:{bottom:s-4},role:"slider","aria-valuenow":100}),(0,a.jsxs)("div",{className:(0,u.cn)("fixed flex flex-col bottom-0 dark:bg-zinc-900 bg-zinc-50 w-full border-t z-40 overflow-y-scroll overflow-x-hidden dark:border-zinc-700 border-zinc-200",{"select-none":i}),style:{height:s},children:[(0,a.jsxs)("div",{className:"flex flex-row justify-between items-center w-full h-fit border-b dark:border-zinc-700 border-zinc-200 px-2 py-1 sticky top-0 z-50 bg-muted",children:[(0,a.jsxs)("div",{className:"text-sm pl-2 dark:text-zinc-50 text-zinc-800 flex flex-row gap-3 items-center",children:[(0,a.jsx)("div",{className:"text-muted-foreground",children:(0,a.jsx)(v.uO,{})}),(0,a.jsx)("div",{children:"Console"})]}),(0,a.jsx)(S.$,{variant:"ghost",className:"size-fit p-1 hover:dark:bg-zinc-700 hover:bg-zinc-200",size:"icon",onClick:()=>t([]),children:(0,a.jsx)(v.Fj,{})})]}),(0,a.jsxs)("div",{children:[e.map((e,t)=>(0,a.jsxs)("div",{className:"px-4 py-2 flex flex-row text-sm border-b dark:border-zinc-700 border-zinc-200 dark:bg-zinc-900 bg-zinc-50 font-mono",children:[(0,a.jsxs)("div",{className:(0,u.cn)("w-12 shrink-0",{"text-muted-foreground":["in_progress","loading_packages"].includes(e.status),"text-emerald-500":"completed"===e.status,"text-red-400":"failed"===e.status}),children:["[",t+1,"]"]}),["in_progress","loading_packages"].includes(e.status)?(0,a.jsxs)("div",{className:"flex flex-row gap-2",children:[(0,a.jsx)("div",{className:"animate-spin size-fit self-center mb-auto mt-0.5",children:(0,a.jsx)(v.hz,{})}),(0,a.jsx)("div",{className:"text-muted-foreground",children:"in_progress"===e.status?"Initializing...":"loading_packages"===e.status?e.contents.map(e=>"text"===e.type?e.value:null):null})]}):(0,a.jsx)("div",{className:"dark:text-zinc-50 text-zinc-900 w-full flex flex-col gap-2 overflow-x-scroll",children:e.contents.map((t,s)=>"image"===t.type?(0,a.jsx)("picture",{children:(0,a.jsx)("img",{src:t.value,alt:"output",className:"rounded-md max-w-screen-toast-mobile w-full"})},`${e.id}-${s}`):(0,a.jsx)("div",{className:"whitespace-pre-line break-words w-full",children:t.value},`${e.id}-${s}`))})]},e.id)),(0,a.jsx)("div",{ref:l})]})]})]}):null}let G={matplotlib:`
    import io
    import base64
    from matplotlib import pyplot as plt

    # Clear any existing plots
    plt.clf()
    plt.close('all')

    # Switch to agg backend
    plt.switch_backend('agg')

    def setup_matplotlib_output():
        def custom_show():
            if plt.gcf().get_size_inches().prod() * plt.gcf().dpi ** 2 > 25_000_000:
                print("Warning: Plot size too large, reducing quality")
                plt.gcf().set_dpi(100)

            png_buf = io.BytesIO()
            plt.savefig(png_buf, format='png')
            png_buf.seek(0)
            png_base64 = base64.b64encode(png_buf.read()).decode('utf-8')
            print(f'data:image/png;base64,{png_base64}')
            png_buf.close()

            plt.clf()
            plt.close('all')

        plt.show = custom_show
  `,basic:`
    # Basic output capture setup
  `},O=new U({kind:"code",description:"Useful for code generation; Code execution is only available for python code.",initialize:async({setMetadata:e})=>{e({outputs:[]})},onStreamPart:({streamPart:e,setArtifact:t})=>{"data-codeDelta"===e.type&&t(t=>({...t,content:e.data,isVisible:"streaming"===t.status&&t.content.length>300&&t.content.length<310||t.isVisible,status:"streaming"}))},content:({metadata:e,setMetadata:t,...s})=>(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"px-1",children:(0,a.jsx)(B.B,{...s})}),e?.outputs&&(0,a.jsx)(L,{consoleOutputs:e.outputs,setConsoleOutputs:()=>{t({...e,outputs:[]})}})]}),actions:[{icon:(0,a.jsx)(v.ud,{size:18}),label:"Run",description:"Execute code",onClick:async({content:e,setMetadata:t})=>{let s=(0,u.lk)(),a=[];t(e=>({...e,outputs:[...e.outputs,{id:s,contents:[],status:"in_progress"}]}));try{let n=await globalThis.loadPyodide({indexURL:"https://cdn.jsdelivr.net/pyodide/v0.23.4/full/"});for(let i of(n.setStdout({batched:e=>{a.push({type:e.startsWith("data:image/png;base64")?"image":"text",value:e})}}),await n.loadPackagesFromImports(e,{messageCallback:e=>{t(t=>({...t,outputs:[...t.outputs.filter(e=>e.id!==s),{id:s,contents:[{type:"text",value:e}],status:"loading_packages"}]}))}}),function(e){let t=["basic"];return(e.includes("matplotlib")||e.includes("plt."))&&t.push("matplotlib"),t}(e)))G[i]&&(await n.runPythonAsync(G[i]),"matplotlib"===i&&await n.runPythonAsync("setup_matplotlib_output()"));await n.runPythonAsync(e),t(e=>({...e,outputs:[...e.outputs.filter(e=>e.id!==s),{id:s,contents:a,status:"completed"}]}))}catch(e){t(t=>({...t,outputs:[...t.outputs.filter(e=>e.id!==s),{id:s,contents:[{type:"text",value:e.message}],status:"failed"}]}))}}},{icon:(0,a.jsx)(v.ej,{size:18}),description:"View Previous version",onClick:({handleVersionChange:e})=>{e("prev")},isDisabled:({currentVersionIndex:e})=>0===e},{icon:(0,a.jsx)(v.Uf,{size:18}),description:"View Next version",onClick:({handleVersionChange:e})=>{e("next")},isDisabled:({isCurrentVersion:e})=>!!e},{icon:(0,a.jsx)(v.Td,{size:18}),description:"Copy code to clipboard",onClick:({content:e})=>{navigator.clipboard.writeText(e),z.o.success("Copied to clipboard!")}}],toolbar:[{icon:(0,a.jsx)(v.b1,{}),description:"Add comments",onClick:({sendMessage:e})=>{e({role:"user",parts:[{type:"text",text:"Add comments to the code snippet for understanding"}]})}},{icon:(0,a.jsx)(v.Dk,{}),description:"Add logs",onClick:({sendMessage:e})=>{e({role:"user",parts:[{type:"text",text:"Add logs to the code snippet for debugging"}]})}}]});var H=s(21876),W=s(33577);let F=new U({kind:"sheet",description:"Useful for working with spreadsheets",initialize:async()=>{},onStreamPart:({setArtifact:e,streamPart:t})=>{"data-sheetDelta"===t.type&&e(e=>({...e,content:t.data,isVisible:!0,status:"streaming"}))},content:({content:e,currentVersionIndex:t,isCurrentVersion:s,onSaveContent:n,status:i})=>(0,a.jsx)(H.y,{content:e,currentVersionIndex:t,isCurrentVersion:s,saveContent:n,status:i}),actions:[{icon:(0,a.jsx)(v.ej,{size:18}),description:"View Previous version",onClick:({handleVersionChange:e})=>{e("prev")},isDisabled:({currentVersionIndex:e})=>0===e},{icon:(0,a.jsx)(v.Uf,{size:18}),description:"View Next version",onClick:({handleVersionChange:e})=>{e("next")},isDisabled:({isCurrentVersion:e})=>!!e},{icon:(0,a.jsx)(v.Td,{}),description:"Copy as .csv",onClick:({content:e})=>{let t=(0,W.parse)(e,{skipEmptyLines:!0}).data.filter(e=>e.some(e=>""!==e.trim())),s=(0,W.unparse)(t);navigator.clipboard.writeText(s),z.o.success("Copied csv to clipboard!")}}],toolbar:[{description:"Format and clean data",icon:(0,a.jsx)(v.BZ,{}),onClick:({sendMessage:e})=>{e({role:"user",parts:[{type:"text",text:"Can you please format and clean the data?"}]})}},{description:"Analyze and visualize data",icon:(0,a.jsx)(v.gT,{}),onClick:({sendMessage:e})=>{e({role:"user",parts:[{type:"text",text:"Can you please analyze and visualize the data by creating a new code artifact in python?"}]})}}]});var q=s(58937),Z=s(49516),K=s(56007),Y=s(9021),Q=s(3764),X=s(29496),J=s(46516),ee=s(2031),et=s(94219);let es={Unchanged:0,Deleted:-1,Inserted:1},ea=(e,t,s)=>{eu(t,s);let a=[],n=[],i=ef(t),r=ef(s),o=i.length,l=r.length,d=Math.min(o,l),c=0,u=0;for(;c<d;c++){let e=i[c];if(!ep(e,r[c]))break;a.push(...em(e))}for(;u+c+1<d;u++){let e=i[o-u-1];if(!ep(e,r[l-u-1]))break;n.unshift(...em(e))}let m=i.slice(c,o-u),p=r.slice(c,l-u);if(m.length&&p.length){let t=en(e,m,p).sort((e,t)=>t.count-e.count)[0];if(t){let{oldStartIndex:s,newStartIndex:i,oldEndIndex:r,newEndIndex:o}=t,l=m.slice(0,s),d=p.slice(0,i);a.push(...er(e,l,d)),a.push(...m.slice(s,r));let c=m.slice(r),u=p.slice(o);n.unshift(...er(e,c,u))}else a.push(...er(e,m,p))}else a.push(...er(e,m,p));return ej(t,[...a,...n])},en=(e,t,s)=>{let a=[];for(let e=0;e<t.length;e++){let n=ei(s,t[e]);if(-1!==n){let i=e+1,r=n+1;for(;i<t.length&&r<s.length;i++,r++){let e=t[i];if(!ep(s[r],e))break}a.push({oldStartIndex:e,newStartIndex:n,oldEndIndex:i,newEndIndex:r,count:r-n})}}return a},ei=(e,t,s=0)=>{for(let a=s;a<e.length;a++)if(ep(e[a],t))return a;return -1},er=(e,t,s)=>{let a=[],n=[],i=t.length,r=s.length,o=0,l=0;for(;i-o-l>0&&r-o-l>0;){let d=t[o],c=s[o],u=t[i-l-1],m=s[r-l-1],p=!ey(d)&&ew(d,c),f=!ey(u)&&ew(u,m);if(Array.isArray(d)&&Array.isArray(c)){a.push(...eo(e,d,c)),o+=1;continue}p&&f&&(ec(d,c)<ec(u,m)?p=!1:f=!1),p?(a.push(ea(e,d,c)),o+=1):f?(n.unshift(ea(e,u,m)),l+=1):(a.push(ek(e,d,es.Deleted)),a.push(ek(e,c,es.Inserted)),o+=1)}let d=i-o-l,c=r-o-l;return d&&a.push(...t.slice(o,o+d).flat().map(t=>ek(e,t,es.Deleted))),c&&n.unshift(...s.slice(o,o+c).flat().map(t=>ek(e,t,es.Inserted))),[...a,...n]},eo=(e,t,s)=>{let a=new et.diff_match_patch,n=t.map(e=>ev(e)).join(""),i=s.map(e=>ev(e)).join(""),{chars1:r,chars2:o,lineArray:l}=ed(el(n),el(i)),d=a.diff_main(r,o,!1);return(d=d.map(([e,t])=>[e,t.split("").map(e=>l[e.charCodeAt(0)])])).flatMap(([t,s])=>s.map(s=>eN(e,s,t!==es.Unchanged?[eC(e,t)]:[])))},el=e=>e.match(/[^.!?]+[.!?]*\s*/g)||[],ed=(e,t)=>{let s=[],a={},n=0;return{chars1:e.map(e=>(e in a||(a[e]=n,s[n]=e,n++),String.fromCharCode(a[e]))).join(""),chars2:t.map(e=>(e in a||(a[e]=n,s[n]=e,n++),String.fromCharCode(a[e]))).join(""),lineArray:s}},ec=(e,t)=>0,eu=(e,t)=>{if(eh(e,"type")!==eh(t,"type"))throw Error(`node type not equal: ${e.type} !== ${t.type}`)},em=e=>Array.isArray(e)?e:[e],ep=(e,t)=>{let s=Array.isArray(e);if(s!==Array.isArray(t))return!1;if(s)return e.length===t.length&&e.every((e,s)=>ep(e,t[s]));if(eh(e,"type")!==eh(t,"type"))return!1;if(ey(e)&&eh(e,"text")!==eh(t,"text"))return!1;let a=ex(e),n=ex(t);for(let e of[...new Set([...Object.keys(a),...Object.keys(n)])])if(a[e]!==n[e])return!1;let i=eg(e),r=eg(t);if(i.length!==r.length)return!1;for(let e=0;e<i.length;e++)if(!ep(i[e],r[e]))return!1;let o=eb(e),l=eb(t);if(o.length!==l.length)return!1;for(let e=0;e<o.length;e++)if(!ep(o[e],l[e]))return!1;return!0},ef=e=>{let t=eb(e)??[],s=[];for(let e=0;e<t.length;e++){let a=t[e];if(ey(a)){let a=[];for(let s=t[e];e<t.length&&ey(s);s=t[++e])a.push(s);e--,s.push(a)}else s.push(a)}return s},eh=(e,t)=>"type"===t?e.type?.name:e[t],ex=e=>e.attrs?e.attrs:{},eg=e=>e.marks??[],eb=e=>e.content?.content??[],ev=e=>e.text,ey=e=>e.type?.name==="text",ew=(e,t)=>e.type?.name===t.type?.name||Array.isArray(e)&&Array.isArray(t),ej=(e,t)=>{if(!e.type)throw Error("oldNode.type is undefined");return new Z.bP(e.type,e.attrs,Z.FK.fromArray(t),e.marks)},ek=(e,t,s)=>(function e(t,s){let a=t.copy(Z.FK.from(t.content.content.map(t=>e(t,s)).filter(e=>e)));return s(a)||a})(t,t=>ey(t)?eN(e,ev(t),[...t.marks||[],eC(e,s)]):t),eC=(e,t)=>{if(t===es.Inserted||t===es.Deleted)return e.mark("diffMark",{type:t});throw Error("type is not valid")},eN=(e,t,s=[])=>e.text(t,s),eS=(e,t,s)=>{let a=Z.bP.fromJSON(e,t),n=Z.bP.fromJSON(e,s);return ea(e,a,n)},eT=new Z.Sj({nodes:(0,Y.ZW)(K.wQ.spec.nodes,"paragraph block*","block"),marks:q.A.from({...K.wQ.spec.marks.toObject(),diffMark:{attrs:{type:{default:""}},toDOM(e){let t="";switch(e.attrs.type){case es.Inserted:t="bg-green-100 text-green-700 dark:bg-green-500/70 dark:text-green-300";break;case es.Deleted:t="bg-red-100 line-through text-red-600 dark:bg-red-500/70 dark:text-red-300";break;default:t=""}return["span",{class:t},0]}}})}),eI=({oldContent:e,newContent:t})=>{let s=(0,o.useRef)(null),n=(0,o.useRef)(null);return(0,o.useEffect)(()=>{if(s.current&&!n.current){let i=Z.S4.fromSchema(eT),r=(0,J.renderToString)((0,a.jsx)(ee.oz,{children:e})),o=(0,J.renderToString)((0,a.jsx)(ee.oz,{children:t})),l=document.createElement("div");l.innerHTML=r;let d=document.createElement("div");d.innerHTML=o;let c=i.parse(l),u=i.parse(d),m=eS(eT,c.toJSON(),u.toJSON()),p=Q.$t.create({doc:m,plugins:[]});n.current=new X.Lz(s.current,{state:p,editable:()=>!1})}return()=>{n.current&&(n.current.destroy(),n.current=null)}},[e,t]),(0,a.jsx)("div",{className:"diff-editor",ref:s})};var ez=s(7112),eR=s(51160),eA=s(70358);let e_=(0,eA.createServerReference)("40721b01082ba2a7f2716b984edc803a3f7f203933",eA.callServer,void 0,eA.findSourceMapURL,"getSuggestions"),eD=[new U({kind:"text",description:"Useful for text content, like drafting essays and emails.",initialize:async({documentId:e,setMetadata:t})=>{t({suggestions:await e_({documentId:e})})},onStreamPart:({streamPart:e,setMetadata:t,setArtifact:s})=>{"data-suggestion"===e.type&&t(t=>({suggestions:[...t.suggestions,e.data]})),"data-textDelta"===e.type&&s(t=>({...t,content:t.content+e.data,isVisible:"streaming"===t.status&&t.content.length>400&&t.content.length<450||t.isVisible,status:"streaming"}))},content:({mode:e,status:t,content:s,isCurrentVersion:n,currentVersionIndex:i,onSaveContent:r,getDocumentContentById:o,isLoading:l,metadata:d})=>{if(l)return(0,a.jsx)(ez.P,{artifactKind:"text"});if("diff"===e){let e=o(i-1),t=o(i);return(0,a.jsx)(eI,{oldContent:e,newContent:t})}return(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)("div",{className:"flex flex-row py-8 md:p-20 px-4",children:[(0,a.jsx)(eR.K,{content:s,suggestions:d?d.suggestions:[],isCurrentVersion:n,currentVersionIndex:i,status:t,onSaveContent:r}),d?.suggestions&&d.suggestions.length>0?(0,a.jsx)("div",{className:"md:hidden h-dvh w-12 shrink-0"}):null]})})},actions:[{icon:(0,a.jsx)(v.Qe,{size:18}),description:"View changes",onClick:({handleVersionChange:e})=>{e("toggle")},isDisabled:({currentVersionIndex:e,setMetadata:t})=>0===e},{icon:(0,a.jsx)(v.ej,{size:18}),description:"View Previous version",onClick:({handleVersionChange:e})=>{e("prev")},isDisabled:({currentVersionIndex:e})=>0===e},{icon:(0,a.jsx)(v.Uf,{size:18}),description:"View Next version",onClick:({handleVersionChange:e})=>{e("next")},isDisabled:({isCurrentVersion:e})=>!!e},{icon:(0,a.jsx)(v.Td,{size:18}),description:"Copy to clipboard",onClick:({content:e})=>{navigator.clipboard.writeText(e),z.o.success("Copied to clipboard!")}}],toolbar:[{icon:(0,a.jsx)(v.rZ,{}),description:"Add final polish",onClick:({sendMessage:e})=>{e({role:"user",parts:[{type:"text",text:"Please add final polish and check for grammar, add section titles for better structure, and ensure everything reads smoothly."}]})}},{icon:(0,a.jsx)(v.b1,{}),description:"Request suggestions",onClick:({sendMessage:e})=>{e({role:"user",parts:[{type:"text",text:"Please add suggestions you have that could improve the writing."}]})}}]}),O,$,F],eP=(0,o.memo)(function({chatId:e,input:t,setInput:s,status:p,stop:f,attachments:h,setAttachments:x,sendMessage:g,messages:b,setMessages:v,regenerate:y,votes:w,isReadonly:j,selectedVisibilityType:k}){let{artifact:N,setArtifact:S,metadata:z,setMetadata:_}=(0,T.ST)(),{data:D,isLoading:P,mutate:M}=(0,l.default)("init"!==N.documentId&&"streaming"!==N.status?`/api/document?id=${N.documentId}`:null,u.GO),[U,$]=(0,o.useState)("edit"),[B,L]=(0,o.useState)(null),[G,O]=(0,o.useState)(-1),{open:H}=(0,V.cL)(),{mutate:W}=(0,d.iX)(),[F,q]=(0,o.useState)(!1),Z=(0,o.useCallback)(e=>{N&&W(`/api/document?id=${N.documentId}`,async t=>{if(!t)return;let s=t.at(-1);return s&&s.content?s.content!==e?(await fetch(`/api/document?id=${N.documentId}`,{method:"POST",body:JSON.stringify({title:N.title,content:e,kind:N.kind})}),q(!1),[...t,{...s,content:e,createdAt:new Date}]):t:(q(!1),t)},{revalidate:!1})},[N,W]),K=(0,c._6)(Z,2e3),Y=(0,o.useCallback)((e,t)=>{B&&e!==B.content&&(q(!0),t?K(e):Z(e))},[B,K,Z]);function Q(e){return D&&D[e]?D[e].content??"":""}let X=e=>{D&&("latest"===e&&(O(D.length-1),$("edit")),"toggle"===e&&$(e=>"edit"===e?"diff":"edit"),"prev"===e?G>0&&O(e=>e-1):"next"===e&&G<D.length-1&&O(e=>e+1))},[J,ee]=(0,o.useState)(!1),et=!D||!(D.length>0)||G===D.length-1,{width:es,height:ea}=(0,c.lW)(),en=!!es&&es<768,ei=eD.find(e=>e.kind===N.kind);if(!ei)throw Error("Artifact definition not found!");return(0,a.jsx)(i.N,{children:N.isVisible&&(0,a.jsxs)(r.P.div,{"data-testid":"artifact",className:"flex flex-row h-dvh w-dvw fixed top-0 left-0 z-50 bg-transparent",initial:{opacity:1},animate:{opacity:1},exit:{opacity:0,transition:{delay:.4}},children:[!en&&(0,a.jsx)(r.P.div,{className:"fixed bg-background h-dvh",initial:{width:H?es-256:es,right:0},animate:{width:es,right:0},exit:{width:H?es-256:es,right:0}}),!en&&(0,a.jsxs)(r.P.div,{className:"relative w-[400px] bg-muted dark:bg-background h-dvh shrink-0",initial:{opacity:0,x:10,scale:1},animate:{opacity:1,x:0,scale:1,transition:{delay:.2,type:"spring",stiffness:200,damping:30}},exit:{opacity:0,x:0,scale:1,transition:{duration:0}},children:[(0,a.jsx)(i.N,{children:!et&&(0,a.jsx)(r.P.div,{className:"left-0 absolute h-dvh w-[400px] top-0 bg-zinc-900/50 z-50",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0}})}),(0,a.jsxs)("div",{className:"flex flex-col h-full justify-between items-center",children:[(0,a.jsx)(E,{chatId:e,status:p,votes:w,messages:b,setMessages:v,regenerate:y,isReadonly:j,artifactStatus:N.status}),(0,a.jsx)("form",{className:"flex flex-row gap-2 relative items-end w-full px-4 pb-4",children:(0,a.jsx)(m.Z,{chatId:e,input:t,setInput:s,status:p,stop:f,attachments:h,setAttachments:x,messages:b,sendMessage:g,className:"bg-background dark:bg-muted",setMessages:v,selectedVisibilityType:k})})]})]}),(0,a.jsxs)(r.P.div,{className:"fixed dark:bg-muted bg-background h-dvh flex flex-col overflow-y-scroll md:border-l dark:border-zinc-700 border-zinc-200",initial:{opacity:1,x:N.boundingBox.left,y:N.boundingBox.top,height:N.boundingBox.height,width:N.boundingBox.width,borderRadius:50},animate:en?{opacity:1,x:0,y:0,height:ea,width:es||"calc(100dvw)",borderRadius:0,transition:{delay:0,type:"spring",stiffness:200,damping:30,duration:5e3}}:{opacity:1,x:400,y:0,height:ea,width:es?es-400:"calc(100dvw-400px)",borderRadius:0,transition:{delay:0,type:"spring",stiffness:200,damping:30,duration:5e3}},exit:{opacity:0,scale:.5,transition:{delay:.1,type:"spring",stiffness:600,damping:30}},children:[(0,a.jsxs)("div",{className:"p-2 flex flex-row justify-between items-start",children:[(0,a.jsxs)("div",{className:"flex flex-row gap-4 items-start",children:[(0,a.jsx)(A,{}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("div",{className:"font-medium",children:N.title}),F?(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Saving changes..."}):B?(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:`Updated ${(0,n.B)(new Date(B.createdAt),new Date,{addSuffix:!0})}`}):(0,a.jsx)("div",{className:"w-32 h-3 mt-2 bg-muted-foreground/20 rounded-md animate-pulse"})]})]}),(0,a.jsx)(R,{artifact:N,currentVersionIndex:G,handleVersionChange:X,isCurrentVersion:et,mode:U,metadata:z,setMetadata:_})]}),(0,a.jsxs)("div",{className:"dark:bg-muted bg-background h-full overflow-y-scroll !max-w-full items-center",children:[(0,a.jsx)(ei.content,{title:N.title,content:et?N.content:Q(G),mode:U,status:N.status,currentVersionIndex:G,suggestions:[],onSaveContent:Y,isInline:!1,isCurrentVersion:et,getDocumentContentById:Q,isLoading:P&&!N.content,metadata:z,setMetadata:_}),(0,a.jsx)(i.N,{children:et&&(0,a.jsx)(C,{isToolbarVisible:J,setIsToolbarVisible:ee,sendMessage:g,status:p,stop:f,setMessages:v,artifactKind:N.kind})})]}),(0,a.jsx)(i.N,{children:!et&&(0,a.jsx)(I,{currentVersionIndex:G,documents:D,handleVersionChange:X})})]})]})})},(e,t)=>e.status===t.status&&!!P()(e.votes,t.votes)&&e.input===t.input&&!!P()(e.messages,t.messages.length)&&e.selectedVisibilityType===t.selectedVisibilityType)},93318:(e,t,s)=>{"use strict";s.d(t,{DataStreamProvider:()=>n});var a=s(33952);let n=(0,a.registerClientReference)(function(){throw Error("Attempted to call DataStreamProvider() from the server but DataStreamProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/data-stream-provider.tsx","DataStreamProvider");(0,a.registerClientReference)(function(){throw Error("Attempted to call useDataStream() from the server but useDataStream is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/data-stream-provider.tsx","useDataStream")},93323:(e,t,s)=>{"use strict";s.d(t,{o:()=>u});var a=s(13486),n=s(49989),i=s.n(n),r=s(60159),o=s(2031),l=s(62086);let d={code:function({node:e,inline:t,className:s,children:n,...i}){return t?(0,a.jsx)("code",{className:`${s} text-sm bg-zinc-100 dark:bg-zinc-800 py-0.5 px-1 rounded-md`,...i,children:n}):(0,a.jsx)("div",{className:"not-prose flex flex-col",children:(0,a.jsx)("pre",{...i,className:"text-sm w-full overflow-x-auto dark:bg-zinc-900 p-4 border border-zinc-200 dark:border-zinc-700 rounded-xl dark:text-zinc-50 text-zinc-900",children:(0,a.jsx)("code",{className:"whitespace-pre-wrap break-words",children:n})})})},pre:({children:e})=>(0,a.jsx)(a.Fragment,{children:e}),ol:({node:e,children:t,...s})=>(0,a.jsx)("ol",{className:"list-decimal list-outside ml-4",...s,children:t}),li:({node:e,children:t,...s})=>(0,a.jsx)("li",{className:"py-1",...s,children:t}),ul:({node:e,children:t,...s})=>(0,a.jsx)("ul",{className:"list-decimal list-outside ml-4",...s,children:t}),strong:({node:e,children:t,...s})=>(0,a.jsx)("span",{className:"font-semibold",...s,children:t}),a:({node:e,children:t,...s})=>(0,a.jsx)(i(),{className:"text-blue-500 hover:underline",target:"_blank",rel:"noreferrer",...s,children:t}),h1:({node:e,children:t,...s})=>(0,a.jsx)("h1",{className:"text-3xl font-semibold mt-6 mb-2",...s,children:t}),h2:({node:e,children:t,...s})=>(0,a.jsx)("h2",{className:"text-2xl font-semibold mt-6 mb-2",...s,children:t}),h3:({node:e,children:t,...s})=>(0,a.jsx)("h3",{className:"text-xl font-semibold mt-6 mb-2",...s,children:t}),h4:({node:e,children:t,...s})=>(0,a.jsx)("h4",{className:"text-lg font-semibold mt-6 mb-2",...s,children:t}),h5:({node:e,children:t,...s})=>(0,a.jsx)("h5",{className:"text-base font-semibold mt-6 mb-2",...s,children:t}),h6:({node:e,children:t,...s})=>(0,a.jsx)("h6",{className:"text-sm font-semibold mt-6 mb-2",...s,children:t})},c=[l.A],u=(0,r.memo)(({children:e})=>(0,a.jsx)(o.oz,{remarkPlugins:c,components:d,children:e}),(e,t)=>e.children===t.children)},94247:(e,t,s)=>{"use strict";s.d(t,{DataStreamHandler:()=>r});var a=s(60159);s(89514);var n=s(95921),i=s(55232);function r(){let{dataStream:e}=(0,i.u)(),{artifact:t,setArtifact:s,setMetadata:r}=(0,n.ST)();return(0,a.useRef)(-1),null}},95921:(e,t,s)=>{"use strict";s.d(t,{HO:()=>r,ST:()=>o,ls:()=>i});var a=s(87038),n=s(60159);let i={documentId:"init",content:"",kind:"text",title:"",status:"idle",isVisible:!1,boundingBox:{top:0,left:0,width:0,height:0}};function r(e){let{data:t}=(0,a.default)("artifact",null,{fallbackData:i});return(0,n.useMemo)(()=>t?e(t):e(i),[t,e])}function o(){let{data:e,mutate:t}=(0,a.default)("artifact",null,{fallbackData:i}),s=(0,n.useMemo)(()=>e||i,[e]),r=(0,n.useCallback)(e=>{t(t=>{let s=t||i;return"function"==typeof e?e(s):e})},[t]),{data:o,mutate:l}=(0,a.default)(()=>s.documentId?`artifact-metadata-${s.documentId}`:null,null,{fallbackData:null});return(0,n.useMemo)(()=>({artifact:s,setArtifact:r,metadata:o,setMetadata:l}),[s,r,o,l])}},96478:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var a=s(41253);let n=async e=>[{type:"image/png",width:2400,height:1256,url:(0,a.fillMetadataSegment)("/(chat)",await e.params,"twitter-image.png")+"?2503f1158996a16f"}]},96945:(e,t,s)=>{"use strict";s.d(t,{DataStreamHandler:()=>a});let a=(0,s(33952).registerClientReference)(function(){throw Error("Attempted to call DataStreamHandler() from the server but DataStreamHandler is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/components/data-stream-handler.tsx","DataStreamHandler")}};