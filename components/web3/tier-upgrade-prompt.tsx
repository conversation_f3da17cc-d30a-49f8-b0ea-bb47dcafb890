'use client';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { ArrowUp, Coins, Star, Gem, Crown } from 'lucide-react';
import { TierBadge, type UserTier } from './tier-badge';
import { cn } from '@/lib/utils';

interface TierUpgradePromptProps {
  currentTier: UserTier;
  currentStakeAmount?: number;
  onUpgrade?: () => void;
  className?: string;
}

const tierRequirements = {
  FREE: { stakingRequirement: 0, label: 'Free Tier' },
  BRONZE: { stakingRequirement: 1000, label: 'Bronze Tier' },
  SILVER: { stakingRequirement: 5000, label: 'Silver Tier' },
  DIAMOND: { stakingRequirement: 25000, label: 'Diamond Tier' },
};

const tierBenefits = {
  BRONZE: [
    '50 requests per hour',
    'Priority support',
    'Discord access',
    '2x Aura Points earning',
  ],
  SILVER: [
    '200 requests per hour',
    'Advanced AI models',
    'Early feature access',
    '5x Aura Points earning',
  ],
  DIAMOND: [
    '1000 requests per hour',
    'All premium features',
    'Direct team access',
    '10x Aura Points earning',
  ],
};

const tierOrder: UserTier[] = ['FREE', 'BRONZE', 'SILVER', 'DIAMOND'];

export function TierUpgradePrompt({
  currentTier,
  currentStakeAmount = 0,
  onUpgrade,
  className,
}: TierUpgradePromptProps) {
  const currentIndex = tierOrder.indexOf(currentTier);
  const nextTier = tierOrder[currentIndex + 1];

  if (!nextTier) {
    return (
      <Card
        className={cn(
          'border-purple-200 bg-gradient-to-br from-purple-50 to-indigo-50 dark:from-purple-950/20 dark:to-indigo-950/20 dark:border-purple-800/50',
          className,
        )}
      >
        <CardHeader className="text-center">
          <div className="mx-auto mb-2">
            <Crown className="size-8 text-purple-600 dark:text-purple-400" />
          </div>
          <CardTitle className="text-purple-900 dark:text-purple-100">
            Maximum Tier Reached!
          </CardTitle>
          <CardDescription className="text-purple-700 dark:text-purple-300">
            You've unlocked all BonKai features. Welcome to Diamond tier!
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  const nextTierRequirement = tierRequirements[nextTier].stakingRequirement;
  const progress = Math.min(
    (currentStakeAmount / nextTierRequirement) * 100,
    100,
  );
  const remainingTokens = Math.max(nextTierRequirement - currentStakeAmount, 0);

  const getTierIcon = (tier: UserTier) => {
    switch (tier) {
      case 'BRONZE':
        return <Coins className="size-4" />;
      case 'SILVER':
        return <Gem className="size-4" />;
      case 'DIAMOND':
        return <Crown className="size-4" />;
      default:
        return <Star className="size-4" />;
    }
  };

  return (
    <Card
      className={cn(
        'border-primary/20 bg-gradient-to-br from-primary/5 to-secondary/10',
        className,
      )}
    >
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <ArrowUp className="size-5 text-primary" />
              Upgrade to {tierRequirements[nextTier].label}
            </CardTitle>
            <CardDescription className="mt-1">
              Stake more BONK tokens to unlock additional features
            </CardDescription>
          </div>
          <TierBadge tier={nextTier} size="sm" />
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Progress Section */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Staking Progress</span>
            <span className="font-medium">
              {currentStakeAmount.toLocaleString()} /{' '}
              {nextTierRequirement.toLocaleString()} BONK
            </span>
          </div>
          <Progress value={progress} className="h-2" />
          {remainingTokens > 0 && (
            <p className="text-sm text-muted-foreground">
              Stake {remainingTokens.toLocaleString()} more BONK to upgrade
            </p>
          )}
        </div>

        {/* Benefits Section */}
        <div className="space-y-3">
          <h4 className="font-semibold text-sm text-foreground">
            {nextTier} Tier Benefits:
          </h4>
          <div className="grid gap-2">
            {tierBenefits[nextTier]?.map((benefit, index) => (
              <div key={index} className="flex items-center gap-2 text-sm">
                {getTierIcon(nextTier)}
                <span className="text-muted-foreground">{benefit}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Action Button */}
        {onUpgrade && (
          <Button onClick={onUpgrade} className="w-full" size="sm">
            <ArrowUp className="size-4 mr-2" />
            Stake BONK Tokens
          </Button>
        )}
      </CardContent>
    </Card>
  );
}
