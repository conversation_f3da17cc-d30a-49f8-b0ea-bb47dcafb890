"use strict";(()=>{var exports={};exports.id=6183,exports.ids=[6183],exports.modules={8086:e=>{e.exports=require("module")},10640:(e,t,r)=>{var n;r.r(t),r.d(t,{NOOP_LOGGER:()=>o,NOOP_LOGGER_PROVIDER:()=>a,NoopLogger:()=>i,NoopLoggerProvider:()=>s,ProxyLogger:()=>u,ProxyLoggerProvider:()=>c,SeverityNumber:()=>n,logs:()=>f}),function(e){e[e.UNSPECIFIED=0]="UNSPECIFIED",e[e.TRACE=1]="TRACE",e[e.TRACE2=2]="TRACE2",e[e.TRACE3=3]="TRACE3",e[e.TRACE4=4]="TRACE4",e[e.DEBUG=5]="DEBUG",e[e.DEBUG2=6]="DEBUG2",e[e.DEBUG3=7]="DEBUG3",e[e.DEBUG4=8]="DEBUG4",e[e.INFO=9]="INFO",e[e.INFO2=10]="INFO2",e[e.INFO3=11]="INFO3",e[e.INFO4=12]="INFO4",e[e.WARN=13]="WARN",e[e.WARN2=14]="WARN2",e[e.WARN3=15]="WARN3",e[e.WARN4=16]="WARN4",e[e.ERROR=17]="ERROR",e[e.ERROR2=18]="ERROR2",e[e.ERROR3=19]="ERROR3",e[e.ERROR4=20]="ERROR4",e[e.FATAL=21]="FATAL",e[e.FATAL2=22]="FATAL2",e[e.FATAL3=23]="FATAL3",e[e.FATAL4=24]="FATAL4"}(n||(n={}));class i{emit(e){}}let o=new i;class s{getLogger(e,t,r){return new i}}let a=new s;class u{constructor(e,t,r,n){this._provider=e,this.name=t,this.version=r,this.options=n}emit(e){this._getLogger().emit(e)}_getLogger(){if(this._delegate)return this._delegate;let e=this._provider.getDelegateLogger(this.name,this.version,this.options);return e?(this._delegate=e,this._delegate):o}}class c{getLogger(e,t,r){var n;return null!=(n=this.getDelegateLogger(e,t,r))?n:new u(this,e,t,r)}getDelegate(){var e;return null!=(e=this._delegate)?e:a}setDelegate(e){this._delegate=e}getDelegateLogger(e,t,r){var n;return null==(n=this._delegate)?void 0:n.getLogger(e,t,r)}}let l="object"==typeof globalThis?globalThis:global,d=Symbol.for("io.opentelemetry.js.api.logs"),p=l;function _(e,t,r){return n=>n===e?t:r}let h=1;class g{constructor(){this._proxyLoggerProvider=new c}static getInstance(){return this._instance||(this._instance=new g),this._instance}setGlobalLoggerProvider(e){return p[d]?this.getLoggerProvider():(p[d]=_(h,e,a),this._proxyLoggerProvider.setDelegate(e),e)}getLoggerProvider(){var e,t;return null!=(t=null==(e=p[d])?void 0:e.call(p,h))?t:this._proxyLoggerProvider}getLogger(e,t,r){return this.getLoggerProvider().getLogger(e,t,r)}disable(){delete p[d],this._proxyLoggerProvider=new c}}let f=g.getInstance()},33873:e=>{e.exports=require("path")},42359:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{Mq:()=>xv});var url__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__(79551),module__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__(8086),path__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__(33873),_opentelemetry_api__WEBPACK_IMPORTED_MODULE_3__=__webpack_require__(56442),_opentelemetry_api_logs__WEBPACK_IMPORTED_MODULE_4__=__webpack_require__(10640);let require=(0,module__WEBPACK_IMPORTED_MODULE_1__.createRequire)("file:///Users/<USER>/Downloads/Coding/Personal/Bonkai/node_modules/@vercel/otel/dist/node/index.js"),__filename=(0,url__WEBPACK_IMPORTED_MODULE_0__.fileURLToPath)("file:///Users/<USER>/Downloads/Coding/Personal/Bonkai/node_modules/@vercel/otel/dist/node/index.js"),__dirname=path__WEBPACK_IMPORTED_MODULE_2__.dirname(__filename);var _f=Object.create,js=Object.defineProperty,pf=Object.getOwnPropertyDescriptor,hf=Object.getOwnPropertyNames,ff=Object.getPrototypeOf,Ef=Object.prototype.hasOwnProperty,z=(e=>"u">typeof require?require:"u">typeof Proxy?new Proxy(e,{get:(e,t)=>("u">typeof require?require:e)[t]}):e)(function(e){if("u">typeof require)return require.apply(this,arguments);throw Error('Dynamic require of "'+e+'" is not supported')}),dc=(e,t)=>()=>(e&&(t=e(e=0)),t),l=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),$r=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let i of hf(t))Ef.call(e,i)||i===r||js(e,i,{get:()=>t[i],enumerable:!(n=pf(t,i))||n.enumerable});return e},ht=(e,t,r)=>($r(e,t,"default"),r&&$r(r,t,"default")),Y=(e,t,r)=>(r=null!=e?_f(ff(e)):{},$r(!t&&e&&e.__esModule?r:js(r,"default",{value:e,enumerable:!0}),e)),m=e=>$r(js({},"__esModule",{value:!0}),e),d={},h=dc(()=>{ht(d,_opentelemetry_api__WEBPACK_IMPORTED_MODULE_3__)}),dr=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.isTracingSuppressed=e.unsuppressTracing=e.suppressTracing=void 0;var t=(0,(h(),m(d)).createContextKey)("OpenTelemetry SDK Context Key SUPPRESS_TRACING");e.suppressTracing=function(e){return e.setValue(t,!0)},e.unsuppressTracing=function(e){return e.deleteValue(t)},e.isTracingSuppressed=function(e){return!0===e.getValue(t)}}),ks=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.BAGGAGE_MAX_TOTAL_LENGTH=e.BAGGAGE_MAX_PER_NAME_VALUE_PAIRS=e.BAGGAGE_MAX_NAME_VALUE_PAIRS=e.BAGGAGE_HEADER=e.BAGGAGE_ITEMS_SEPARATOR=e.BAGGAGE_PROPERTIES_SEPARATOR=e.BAGGAGE_KEY_PAIR_SEPARATOR=void 0,e.BAGGAGE_KEY_PAIR_SEPARATOR="=",e.BAGGAGE_PROPERTIES_SEPARATOR=";",e.BAGGAGE_ITEMS_SEPARATOR=",",e.BAGGAGE_HEADER="baggage",e.BAGGAGE_MAX_NAME_VALUE_PAIRS=180,e.BAGGAGE_MAX_PER_NAME_VALUE_PAIRS=4096,e.BAGGAGE_MAX_TOTAL_LENGTH=8192}),$s=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.parseKeyPairsIntoRecord=e.parsePairKeyValue=e.getKeyPairs=e.serializeKeyPairs=void 0;var t=(h(),m(d)),r=ks();function n(e){let n=e.split(r.BAGGAGE_PROPERTIES_SEPARATOR);if(n.length<=0)return;let i=n.shift();if(!i)return;let o=i.indexOf(r.BAGGAGE_KEY_PAIR_SEPARATOR);if(o<=0)return;let s=decodeURIComponent(i.substring(0,o).trim()),a=decodeURIComponent(i.substring(o+1).trim()),u;return n.length>0&&(u=(0,t.baggageEntryMetadataFromString)(n.join(r.BAGGAGE_PROPERTIES_SEPARATOR))),{key:s,value:a,metadata:u}}e.serializeKeyPairs=function(e){return e.reduce((e,t)=>{let n=`${e}${""!==e?r.BAGGAGE_ITEMS_SEPARATOR:""}${t}`;return n.length>r.BAGGAGE_MAX_TOTAL_LENGTH?e:n},"")},e.getKeyPairs=function(e){return e.getAllEntries().map(([e,t])=>{let n=`${encodeURIComponent(e)}=${encodeURIComponent(t.value)}`;return void 0!==t.metadata&&(n+=r.BAGGAGE_PROPERTIES_SEPARATOR+t.metadata.toString()),n})},e.parsePairKeyValue=n,e.parseKeyPairsIntoRecord=function(e){return"string"!=typeof e||0===e.length?{}:e.split(r.BAGGAGE_ITEMS_SEPARATOR).map(e=>n(e)).filter(e=>void 0!==e&&e.value.length>0).reduce((e,t)=>(e[t.key]=t.value,e),{})}}),pc=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.W3CBaggagePropagator=void 0;var t=(h(),m(d)),r=dr(),n=ks(),i=$s();e.W3CBaggagePropagator=class{inject(e,o,s){let a=t.propagation.getBaggage(e);if(!a||(0,r.isTracingSuppressed)(e))return;let u=(0,i.getKeyPairs)(a).filter(e=>e.length<=n.BAGGAGE_MAX_PER_NAME_VALUE_PAIRS).slice(0,n.BAGGAGE_MAX_NAME_VALUE_PAIRS),c=(0,i.serializeKeyPairs)(u);c.length>0&&s.set(o,n.BAGGAGE_HEADER,c)}extract(e,r,o){let s=o.get(r,n.BAGGAGE_HEADER),a=Array.isArray(s)?s.join(n.BAGGAGE_ITEMS_SEPARATOR):s;if(!a)return e;let u={};return 0===a.length||(a.split(n.BAGGAGE_ITEMS_SEPARATOR).forEach(e=>{let t=(0,i.parsePairKeyValue)(e);if(t){let e={value:t.value};t.metadata&&(e.metadata=t.metadata),u[t.key]=e}}),0===Object.entries(u).length)?e:t.propagation.setBaggage(e,t.propagation.createBaggage(u))}fields(){return[n.BAGGAGE_HEADER]}}}),hc=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.AnchoredClock=void 0,e.AnchoredClock=class{constructor(e,t){this._monotonicClock=t,this._epochMillis=e.now(),this._performanceMillis=t.now()}now(){let e=this._monotonicClock.now()-this._performanceMillis;return this._epochMillis+e}}}),Tc=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.isAttributeValue=e.isAttributeKey=e.sanitizeAttributes=void 0;var t=(h(),m(d));function r(e){return"string"==typeof e&&e.length>0}function n(e){return null==e||(Array.isArray(e)?i(e):o(e))}function i(e){let t;for(let r of e)if(null!=r){if(!t){if(o(r)){t=typeof r;continue}return!1}if(typeof r!==t)return!1}return!0}function o(e){switch(typeof e){case"number":case"boolean":case"string":return!0}return!1}e.sanitizeAttributes=function(e){let i={};if("object"!=typeof e||null==e)return i;for(let[o,s]of Object.entries(e)){if(!r(o)){t.diag.warn(`Invalid attribute key: ${o}`);continue}if(!n(s)){t.diag.warn(`Invalid attribute value set for key: ${o}`);continue}Array.isArray(s)?i[o]=s.slice():i[o]=s}return i},e.isAttributeKey=r,e.isAttributeValue=n}),Ys=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.loggingErrorHandler=void 0;var t=(h(),m(d));function r(e){return"string"==typeof e?e:JSON.stringify(n(e))}function n(e){let t={},r=e;for(;null!==r;)Object.getOwnPropertyNames(r).forEach(e=>{if(t[e])return;let n=r[e];n&&(t[e]=String(n))}),r=Object.getPrototypeOf(r);return t}e.loggingErrorHandler=function(){return e=>{t.diag.error(r(e))}}}),Qs=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.globalErrorHandler=e.setGlobalErrorHandler=void 0;var t=(0,Ys().loggingErrorHandler)();e.setGlobalErrorHandler=function(e){t=e},e.globalErrorHandler=function(e){try{t(e)}catch{}}}),Zs=l(e=>{var t;Object.defineProperty(e,"__esModule",{value:!0}),e.TracesSamplerValues=void 0,function(e){e.AlwaysOff="always_off",e.AlwaysOn="always_on",e.ParentBasedAlwaysOff="parentbased_always_off",e.ParentBasedAlwaysOn="parentbased_always_on",e.ParentBasedTraceIdRatio="parentbased_traceidratio",e.TraceIdRatio="traceidratio"}(t=e.TracesSamplerValues||(e.TracesSamplerValues={}))}),Ac=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e._globalThis=void 0,e._globalThis="object"==typeof globalThis?globalThis:"object"==typeof self?self:"object"==typeof window?window:"object"==typeof global?global:{}}),Yr=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.getEnvWithoutDefaults=e.parseEnvironment=e.DEFAULT_ENVIRONMENT=e.DEFAULT_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT=e.DEFAULT_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT=e.DEFAULT_ATTRIBUTE_COUNT_LIMIT=e.DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT=void 0;var t=(h(),m(d)),r=Zs(),n=Ac(),i=",",o=["OTEL_SDK_DISABLED"];function s(e){return o.indexOf(e)>-1}var a=["OTEL_BSP_EXPORT_TIMEOUT","OTEL_BSP_MAX_EXPORT_BATCH_SIZE","OTEL_BSP_MAX_QUEUE_SIZE","OTEL_BSP_SCHEDULE_DELAY","OTEL_BLRP_EXPORT_TIMEOUT","OTEL_BLRP_MAX_EXPORT_BATCH_SIZE","OTEL_BLRP_MAX_QUEUE_SIZE","OTEL_BLRP_SCHEDULE_DELAY","OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT","OTEL_ATTRIBUTE_COUNT_LIMIT","OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT","OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT","OTEL_LOGRECORD_ATTRIBUTE_VALUE_LENGTH_LIMIT","OTEL_LOGRECORD_ATTRIBUTE_COUNT_LIMIT","OTEL_SPAN_EVENT_COUNT_LIMIT","OTEL_SPAN_LINK_COUNT_LIMIT","OTEL_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT","OTEL_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT","OTEL_EXPORTER_OTLP_TIMEOUT","OTEL_EXPORTER_OTLP_TRACES_TIMEOUT","OTEL_EXPORTER_OTLP_METRICS_TIMEOUT","OTEL_EXPORTER_OTLP_LOGS_TIMEOUT","OTEL_EXPORTER_JAEGER_AGENT_PORT"];function u(e){return a.indexOf(e)>-1}var c=["OTEL_NO_PATCH_MODULES","OTEL_PROPAGATORS"];function l(e){return c.indexOf(e)>-1}function p(e,t,r){if(typeof r[e]>"u")return;let n=String(r[e]);t[e]="true"===n.toLowerCase()}function _(e,t,r,n=-1/0,i=1/0){if("u">typeof r[e]){let o=Number(r[e]);isNaN(o)||(o<n?t[e]=n:o>i?t[e]=i:t[e]=o)}}function g(e,t,r,n=i){let o=r[e];"string"==typeof o&&(t[e]=o.split(n).map(e=>e.trim()))}e.DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT=1/0,e.DEFAULT_ATTRIBUTE_COUNT_LIMIT=128,e.DEFAULT_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT=128,e.DEFAULT_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT=128,e.DEFAULT_ENVIRONMENT={OTEL_SDK_DISABLED:!1,CONTAINER_NAME:"",ECS_CONTAINER_METADATA_URI_V4:"",ECS_CONTAINER_METADATA_URI:"",HOSTNAME:"",KUBERNETES_SERVICE_HOST:"",NAMESPACE:"",OTEL_BSP_EXPORT_TIMEOUT:3e4,OTEL_BSP_MAX_EXPORT_BATCH_SIZE:512,OTEL_BSP_MAX_QUEUE_SIZE:2048,OTEL_BSP_SCHEDULE_DELAY:5e3,OTEL_BLRP_EXPORT_TIMEOUT:3e4,OTEL_BLRP_MAX_EXPORT_BATCH_SIZE:512,OTEL_BLRP_MAX_QUEUE_SIZE:2048,OTEL_BLRP_SCHEDULE_DELAY:5e3,OTEL_EXPORTER_JAEGER_AGENT_HOST:"",OTEL_EXPORTER_JAEGER_AGENT_PORT:6832,OTEL_EXPORTER_JAEGER_ENDPOINT:"",OTEL_EXPORTER_JAEGER_PASSWORD:"",OTEL_EXPORTER_JAEGER_USER:"",OTEL_EXPORTER_OTLP_ENDPOINT:"",OTEL_EXPORTER_OTLP_TRACES_ENDPOINT:"",OTEL_EXPORTER_OTLP_METRICS_ENDPOINT:"",OTEL_EXPORTER_OTLP_LOGS_ENDPOINT:"",OTEL_EXPORTER_OTLP_HEADERS:"",OTEL_EXPORTER_OTLP_TRACES_HEADERS:"",OTEL_EXPORTER_OTLP_METRICS_HEADERS:"",OTEL_EXPORTER_OTLP_LOGS_HEADERS:"",OTEL_EXPORTER_OTLP_TIMEOUT:1e4,OTEL_EXPORTER_OTLP_TRACES_TIMEOUT:1e4,OTEL_EXPORTER_OTLP_METRICS_TIMEOUT:1e4,OTEL_EXPORTER_OTLP_LOGS_TIMEOUT:1e4,OTEL_EXPORTER_ZIPKIN_ENDPOINT:"http://localhost:9411/api/v2/spans",OTEL_LOG_LEVEL:t.DiagLogLevel.INFO,OTEL_NO_PATCH_MODULES:[],OTEL_PROPAGATORS:["tracecontext","baggage"],OTEL_RESOURCE_ATTRIBUTES:"",OTEL_SERVICE_NAME:"",OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT:e.DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT,OTEL_ATTRIBUTE_COUNT_LIMIT:e.DEFAULT_ATTRIBUTE_COUNT_LIMIT,OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT:e.DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT,OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT:e.DEFAULT_ATTRIBUTE_COUNT_LIMIT,OTEL_LOGRECORD_ATTRIBUTE_VALUE_LENGTH_LIMIT:e.DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT,OTEL_LOGRECORD_ATTRIBUTE_COUNT_LIMIT:e.DEFAULT_ATTRIBUTE_COUNT_LIMIT,OTEL_SPAN_EVENT_COUNT_LIMIT:128,OTEL_SPAN_LINK_COUNT_LIMIT:128,OTEL_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT:e.DEFAULT_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT,OTEL_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT:e.DEFAULT_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT,OTEL_TRACES_EXPORTER:"",OTEL_TRACES_SAMPLER:r.TracesSamplerValues.ParentBasedAlwaysOn,OTEL_TRACES_SAMPLER_ARG:"",OTEL_LOGS_EXPORTER:"",OTEL_EXPORTER_OTLP_INSECURE:"",OTEL_EXPORTER_OTLP_TRACES_INSECURE:"",OTEL_EXPORTER_OTLP_METRICS_INSECURE:"",OTEL_EXPORTER_OTLP_LOGS_INSECURE:"",OTEL_EXPORTER_OTLP_CERTIFICATE:"",OTEL_EXPORTER_OTLP_TRACES_CERTIFICATE:"",OTEL_EXPORTER_OTLP_METRICS_CERTIFICATE:"",OTEL_EXPORTER_OTLP_LOGS_CERTIFICATE:"",OTEL_EXPORTER_OTLP_COMPRESSION:"",OTEL_EXPORTER_OTLP_TRACES_COMPRESSION:"",OTEL_EXPORTER_OTLP_METRICS_COMPRESSION:"",OTEL_EXPORTER_OTLP_LOGS_COMPRESSION:"",OTEL_EXPORTER_OTLP_CLIENT_KEY:"",OTEL_EXPORTER_OTLP_TRACES_CLIENT_KEY:"",OTEL_EXPORTER_OTLP_METRICS_CLIENT_KEY:"",OTEL_EXPORTER_OTLP_LOGS_CLIENT_KEY:"",OTEL_EXPORTER_OTLP_CLIENT_CERTIFICATE:"",OTEL_EXPORTER_OTLP_TRACES_CLIENT_CERTIFICATE:"",OTEL_EXPORTER_OTLP_METRICS_CLIENT_CERTIFICATE:"",OTEL_EXPORTER_OTLP_LOGS_CLIENT_CERTIFICATE:"",OTEL_EXPORTER_OTLP_PROTOCOL:"http/protobuf",OTEL_EXPORTER_OTLP_TRACES_PROTOCOL:"http/protobuf",OTEL_EXPORTER_OTLP_METRICS_PROTOCOL:"http/protobuf",OTEL_EXPORTER_OTLP_LOGS_PROTOCOL:"http/protobuf",OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE:"cumulative"};var f={ALL:t.DiagLogLevel.ALL,VERBOSE:t.DiagLogLevel.VERBOSE,DEBUG:t.DiagLogLevel.DEBUG,INFO:t.DiagLogLevel.INFO,WARN:t.DiagLogLevel.WARN,ERROR:t.DiagLogLevel.ERROR,NONE:t.DiagLogLevel.NONE};function E(e,t,r){let n=r[e];if("string"==typeof n){let r=f[n.toUpperCase()];null!=r&&(t[e]=r)}}function T(t){let r={};for(let n in e.DEFAULT_ENVIRONMENT){let e=n;if("OTEL_LOG_LEVEL"===e)E(e,r,t);else if(s(e))p(e,r,t);else if(u(e))_(e,r,t);else if(l(e))g(e,r,t);else{let n=t[e];"u">typeof n&&null!==n&&(r[e]=String(n))}}return r}e.parseEnvironment=T,e.getEnvWithoutDefaults=function(){return"u">typeof process&&process&&process.env?T(process.env):T(n._globalThis)}}),Rc=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.getEnv=void 0;var t=z("os"),r=Yr();e.getEnv=function(){let e=(0,r.parseEnvironment)(process.env);return Object.assign({HOSTNAME:t.hostname()},r.DEFAULT_ENVIRONMENT,e)}}),bc=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e._globalThis=void 0,e._globalThis="object"==typeof globalThis?globalThis:global}),yc=l(e=>{function t(e){return e>=48&&e<=57?e-48:e>=97&&e<=102?e-87:e-55}Object.defineProperty(e,"__esModule",{value:!0}),e.hexToBase64=void 0;var r=Buffer.alloc(8),n=Buffer.alloc(16);e.hexToBase64=function(e){let i;i=16===e.length?r:32===e.length?n:Buffer.alloc(e.length/2);let o=0;for(let r=0;r<e.length;r+=2){let n=t(e.charCodeAt(r)),s=t(e.charCodeAt(r+1));i.writeUInt8(n<<4|s,o++)}return i.toString("base64")}}),Lc=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.RandomIdGenerator=void 0;var t=8,r=16;e.RandomIdGenerator=class{constructor(){this.generateTraceId=i(r),this.generateSpanId=i(t)}};var n=Buffer.allocUnsafe(r);function i(e){return function(){for(let t=0;t<e/4;t++)n.writeUInt32BE(0x100000000*Math.random()>>>0,4*t);for(let t=0;t<e&&!(n[t]>0);t++)t===e-1&&(n[e-1]=1);return n.toString("hex",0,e)}}}),Mc=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.otperformance=void 0,e.otperformance=z("perf_hooks").performance}),to=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.VERSION=void 0,e.VERSION="1.19.0"}),Nc=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.MessageTypeValues=e.RpcGrpcStatusCodeValues=e.MessagingOperationValues=e.MessagingDestinationKindValues=e.HttpFlavorValues=e.NetHostConnectionSubtypeValues=e.NetHostConnectionTypeValues=e.NetTransportValues=e.FaasInvokedProviderValues=e.FaasDocumentOperationValues=e.FaasTriggerValues=e.DbCassandraConsistencyLevelValues=e.DbSystemValues=e.SemanticAttributes=void 0,e.SemanticAttributes={AWS_LAMBDA_INVOKED_ARN:"aws.lambda.invoked_arn",DB_SYSTEM:"db.system",DB_CONNECTION_STRING:"db.connection_string",DB_USER:"db.user",DB_JDBC_DRIVER_CLASSNAME:"db.jdbc.driver_classname",DB_NAME:"db.name",DB_STATEMENT:"db.statement",DB_OPERATION:"db.operation",DB_MSSQL_INSTANCE_NAME:"db.mssql.instance_name",DB_CASSANDRA_KEYSPACE:"db.cassandra.keyspace",DB_CASSANDRA_PAGE_SIZE:"db.cassandra.page_size",DB_CASSANDRA_CONSISTENCY_LEVEL:"db.cassandra.consistency_level",DB_CASSANDRA_TABLE:"db.cassandra.table",DB_CASSANDRA_IDEMPOTENCE:"db.cassandra.idempotence",DB_CASSANDRA_SPECULATIVE_EXECUTION_COUNT:"db.cassandra.speculative_execution_count",DB_CASSANDRA_COORDINATOR_ID:"db.cassandra.coordinator.id",DB_CASSANDRA_COORDINATOR_DC:"db.cassandra.coordinator.dc",DB_HBASE_NAMESPACE:"db.hbase.namespace",DB_REDIS_DATABASE_INDEX:"db.redis.database_index",DB_MONGODB_COLLECTION:"db.mongodb.collection",DB_SQL_TABLE:"db.sql.table",EXCEPTION_TYPE:"exception.type",EXCEPTION_MESSAGE:"exception.message",EXCEPTION_STACKTRACE:"exception.stacktrace",EXCEPTION_ESCAPED:"exception.escaped",FAAS_TRIGGER:"faas.trigger",FAAS_EXECUTION:"faas.execution",FAAS_DOCUMENT_COLLECTION:"faas.document.collection",FAAS_DOCUMENT_OPERATION:"faas.document.operation",FAAS_DOCUMENT_TIME:"faas.document.time",FAAS_DOCUMENT_NAME:"faas.document.name",FAAS_TIME:"faas.time",FAAS_CRON:"faas.cron",FAAS_COLDSTART:"faas.coldstart",FAAS_INVOKED_NAME:"faas.invoked_name",FAAS_INVOKED_PROVIDER:"faas.invoked_provider",FAAS_INVOKED_REGION:"faas.invoked_region",NET_TRANSPORT:"net.transport",NET_PEER_IP:"net.peer.ip",NET_PEER_PORT:"net.peer.port",NET_PEER_NAME:"net.peer.name",NET_HOST_IP:"net.host.ip",NET_HOST_PORT:"net.host.port",NET_HOST_NAME:"net.host.name",NET_HOST_CONNECTION_TYPE:"net.host.connection.type",NET_HOST_CONNECTION_SUBTYPE:"net.host.connection.subtype",NET_HOST_CARRIER_NAME:"net.host.carrier.name",NET_HOST_CARRIER_MCC:"net.host.carrier.mcc",NET_HOST_CARRIER_MNC:"net.host.carrier.mnc",NET_HOST_CARRIER_ICC:"net.host.carrier.icc",PEER_SERVICE:"peer.service",ENDUSER_ID:"enduser.id",ENDUSER_ROLE:"enduser.role",ENDUSER_SCOPE:"enduser.scope",THREAD_ID:"thread.id",THREAD_NAME:"thread.name",CODE_FUNCTION:"code.function",CODE_NAMESPACE:"code.namespace",CODE_FILEPATH:"code.filepath",CODE_LINENO:"code.lineno",HTTP_METHOD:"http.method",HTTP_URL:"http.url",HTTP_TARGET:"http.target",HTTP_HOST:"http.host",HTTP_SCHEME:"http.scheme",HTTP_STATUS_CODE:"http.status_code",HTTP_FLAVOR:"http.flavor",HTTP_USER_AGENT:"http.user_agent",HTTP_REQUEST_CONTENT_LENGTH:"http.request_content_length",HTTP_REQUEST_CONTENT_LENGTH_UNCOMPRESSED:"http.request_content_length_uncompressed",HTTP_RESPONSE_CONTENT_LENGTH:"http.response_content_length",HTTP_RESPONSE_CONTENT_LENGTH_UNCOMPRESSED:"http.response_content_length_uncompressed",HTTP_SERVER_NAME:"http.server_name",HTTP_ROUTE:"http.route",HTTP_CLIENT_IP:"http.client_ip",AWS_DYNAMODB_TABLE_NAMES:"aws.dynamodb.table_names",AWS_DYNAMODB_CONSUMED_CAPACITY:"aws.dynamodb.consumed_capacity",AWS_DYNAMODB_ITEM_COLLECTION_METRICS:"aws.dynamodb.item_collection_metrics",AWS_DYNAMODB_PROVISIONED_READ_CAPACITY:"aws.dynamodb.provisioned_read_capacity",AWS_DYNAMODB_PROVISIONED_WRITE_CAPACITY:"aws.dynamodb.provisioned_write_capacity",AWS_DYNAMODB_CONSISTENT_READ:"aws.dynamodb.consistent_read",AWS_DYNAMODB_PROJECTION:"aws.dynamodb.projection",AWS_DYNAMODB_LIMIT:"aws.dynamodb.limit",AWS_DYNAMODB_ATTRIBUTES_TO_GET:"aws.dynamodb.attributes_to_get",AWS_DYNAMODB_INDEX_NAME:"aws.dynamodb.index_name",AWS_DYNAMODB_SELECT:"aws.dynamodb.select",AWS_DYNAMODB_GLOBAL_SECONDARY_INDEXES:"aws.dynamodb.global_secondary_indexes",AWS_DYNAMODB_LOCAL_SECONDARY_INDEXES:"aws.dynamodb.local_secondary_indexes",AWS_DYNAMODB_EXCLUSIVE_START_TABLE:"aws.dynamodb.exclusive_start_table",AWS_DYNAMODB_TABLE_COUNT:"aws.dynamodb.table_count",AWS_DYNAMODB_SCAN_FORWARD:"aws.dynamodb.scan_forward",AWS_DYNAMODB_SEGMENT:"aws.dynamodb.segment",AWS_DYNAMODB_TOTAL_SEGMENTS:"aws.dynamodb.total_segments",AWS_DYNAMODB_COUNT:"aws.dynamodb.count",AWS_DYNAMODB_SCANNED_COUNT:"aws.dynamodb.scanned_count",AWS_DYNAMODB_ATTRIBUTE_DEFINITIONS:"aws.dynamodb.attribute_definitions",AWS_DYNAMODB_GLOBAL_SECONDARY_INDEX_UPDATES:"aws.dynamodb.global_secondary_index_updates",MESSAGING_SYSTEM:"messaging.system",MESSAGING_DESTINATION:"messaging.destination",MESSAGING_DESTINATION_KIND:"messaging.destination_kind",MESSAGING_TEMP_DESTINATION:"messaging.temp_destination",MESSAGING_PROTOCOL:"messaging.protocol",MESSAGING_PROTOCOL_VERSION:"messaging.protocol_version",MESSAGING_URL:"messaging.url",MESSAGING_MESSAGE_ID:"messaging.message_id",MESSAGING_CONVERSATION_ID:"messaging.conversation_id",MESSAGING_MESSAGE_PAYLOAD_SIZE_BYTES:"messaging.message_payload_size_bytes",MESSAGING_MESSAGE_PAYLOAD_COMPRESSED_SIZE_BYTES:"messaging.message_payload_compressed_size_bytes",MESSAGING_OPERATION:"messaging.operation",MESSAGING_CONSUMER_ID:"messaging.consumer_id",MESSAGING_RABBITMQ_ROUTING_KEY:"messaging.rabbitmq.routing_key",MESSAGING_KAFKA_MESSAGE_KEY:"messaging.kafka.message_key",MESSAGING_KAFKA_CONSUMER_GROUP:"messaging.kafka.consumer_group",MESSAGING_KAFKA_CLIENT_ID:"messaging.kafka.client_id",MESSAGING_KAFKA_PARTITION:"messaging.kafka.partition",MESSAGING_KAFKA_TOMBSTONE:"messaging.kafka.tombstone",RPC_SYSTEM:"rpc.system",RPC_SERVICE:"rpc.service",RPC_METHOD:"rpc.method",RPC_GRPC_STATUS_CODE:"rpc.grpc.status_code",RPC_JSONRPC_VERSION:"rpc.jsonrpc.version",RPC_JSONRPC_REQUEST_ID:"rpc.jsonrpc.request_id",RPC_JSONRPC_ERROR_CODE:"rpc.jsonrpc.error_code",RPC_JSONRPC_ERROR_MESSAGE:"rpc.jsonrpc.error_message",MESSAGE_TYPE:"message.type",MESSAGE_ID:"message.id",MESSAGE_COMPRESSED_SIZE:"message.compressed_size",MESSAGE_UNCOMPRESSED_SIZE:"message.uncompressed_size"},e.DbSystemValues={OTHER_SQL:"other_sql",MSSQL:"mssql",MYSQL:"mysql",ORACLE:"oracle",DB2:"db2",POSTGRESQL:"postgresql",REDSHIFT:"redshift",HIVE:"hive",CLOUDSCAPE:"cloudscape",HSQLDB:"hsqldb",PROGRESS:"progress",MAXDB:"maxdb",HANADB:"hanadb",INGRES:"ingres",FIRSTSQL:"firstsql",EDB:"edb",CACHE:"cache",ADABAS:"adabas",FIREBIRD:"firebird",DERBY:"derby",FILEMAKER:"filemaker",INFORMIX:"informix",INSTANTDB:"instantdb",INTERBASE:"interbase",MARIADB:"mariadb",NETEZZA:"netezza",PERVASIVE:"pervasive",POINTBASE:"pointbase",SQLITE:"sqlite",SYBASE:"sybase",TERADATA:"teradata",VERTICA:"vertica",H2:"h2",COLDFUSION:"coldfusion",CASSANDRA:"cassandra",HBASE:"hbase",MONGODB:"mongodb",REDIS:"redis",COUCHBASE:"couchbase",COUCHDB:"couchdb",COSMOSDB:"cosmosdb",DYNAMODB:"dynamodb",NEO4J:"neo4j",GEODE:"geode",ELASTICSEARCH:"elasticsearch",MEMCACHED:"memcached",COCKROACHDB:"cockroachdb"},e.DbCassandraConsistencyLevelValues={ALL:"all",EACH_QUORUM:"each_quorum",QUORUM:"quorum",LOCAL_QUORUM:"local_quorum",ONE:"one",TWO:"two",THREE:"three",LOCAL_ONE:"local_one",ANY:"any",SERIAL:"serial",LOCAL_SERIAL:"local_serial"},e.FaasTriggerValues={DATASOURCE:"datasource",HTTP:"http",PUBSUB:"pubsub",TIMER:"timer",OTHER:"other"},e.FaasDocumentOperationValues={INSERT:"insert",EDIT:"edit",DELETE:"delete"},e.FaasInvokedProviderValues={ALIBABA_CLOUD:"alibaba_cloud",AWS:"aws",AZURE:"azure",GCP:"gcp"},e.NetTransportValues={IP_TCP:"ip_tcp",IP_UDP:"ip_udp",IP:"ip",UNIX:"unix",PIPE:"pipe",INPROC:"inproc",OTHER:"other"},e.NetHostConnectionTypeValues={WIFI:"wifi",WIRED:"wired",CELL:"cell",UNAVAILABLE:"unavailable",UNKNOWN:"unknown"},e.NetHostConnectionSubtypeValues={GPRS:"gprs",EDGE:"edge",UMTS:"umts",CDMA:"cdma",EVDO_0:"evdo_0",EVDO_A:"evdo_a",CDMA2000_1XRTT:"cdma2000_1xrtt",HSDPA:"hsdpa",HSUPA:"hsupa",HSPA:"hspa",IDEN:"iden",EVDO_B:"evdo_b",LTE:"lte",EHRPD:"ehrpd",HSPAP:"hspap",GSM:"gsm",TD_SCDMA:"td_scdma",IWLAN:"iwlan",NR:"nr",NRNSA:"nrnsa",LTE_CA:"lte_ca"},e.HttpFlavorValues={HTTP_1_0:"1.0",HTTP_1_1:"1.1",HTTP_2_0:"2.0",SPDY:"SPDY",QUIC:"QUIC"},e.MessagingDestinationKindValues={QUEUE:"queue",TOPIC:"topic"},e.MessagingOperationValues={RECEIVE:"receive",PROCESS:"process"},e.RpcGrpcStatusCodeValues={OK:0,CANCELLED:1,UNKNOWN:2,INVALID_ARGUMENT:3,DEADLINE_EXCEEDED:4,NOT_FOUND:5,ALREADY_EXISTS:6,PERMISSION_DENIED:7,RESOURCE_EXHAUSTED:8,FAILED_PRECONDITION:9,ABORTED:10,OUT_OF_RANGE:11,UNIMPLEMENTED:12,INTERNAL:13,UNAVAILABLE:14,DATA_LOSS:15,UNAUTHENTICATED:16},e.MessageTypeValues={SENT:"SENT",RECEIVED:"RECEIVED"}}),Cc=l(e=>{var t=e&&e.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),r=e&&e.__exportStar||function(e,r){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(r,n)||t(r,e,n)};Object.defineProperty(e,"__esModule",{value:!0}),r(Nc(),e)}),wc=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.TelemetrySdkLanguageValues=e.OsTypeValues=e.HostArchValues=e.AwsEcsLaunchtypeValues=e.CloudPlatformValues=e.CloudProviderValues=e.SemanticResourceAttributes=void 0,e.SemanticResourceAttributes={CLOUD_PROVIDER:"cloud.provider",CLOUD_ACCOUNT_ID:"cloud.account.id",CLOUD_REGION:"cloud.region",CLOUD_AVAILABILITY_ZONE:"cloud.availability_zone",CLOUD_PLATFORM:"cloud.platform",AWS_ECS_CONTAINER_ARN:"aws.ecs.container.arn",AWS_ECS_CLUSTER_ARN:"aws.ecs.cluster.arn",AWS_ECS_LAUNCHTYPE:"aws.ecs.launchtype",AWS_ECS_TASK_ARN:"aws.ecs.task.arn",AWS_ECS_TASK_FAMILY:"aws.ecs.task.family",AWS_ECS_TASK_REVISION:"aws.ecs.task.revision",AWS_EKS_CLUSTER_ARN:"aws.eks.cluster.arn",AWS_LOG_GROUP_NAMES:"aws.log.group.names",AWS_LOG_GROUP_ARNS:"aws.log.group.arns",AWS_LOG_STREAM_NAMES:"aws.log.stream.names",AWS_LOG_STREAM_ARNS:"aws.log.stream.arns",CONTAINER_NAME:"container.name",CONTAINER_ID:"container.id",CONTAINER_RUNTIME:"container.runtime",CONTAINER_IMAGE_NAME:"container.image.name",CONTAINER_IMAGE_TAG:"container.image.tag",DEPLOYMENT_ENVIRONMENT:"deployment.environment",DEVICE_ID:"device.id",DEVICE_MODEL_IDENTIFIER:"device.model.identifier",DEVICE_MODEL_NAME:"device.model.name",FAAS_NAME:"faas.name",FAAS_ID:"faas.id",FAAS_VERSION:"faas.version",FAAS_INSTANCE:"faas.instance",FAAS_MAX_MEMORY:"faas.max_memory",HOST_ID:"host.id",HOST_NAME:"host.name",HOST_TYPE:"host.type",HOST_ARCH:"host.arch",HOST_IMAGE_NAME:"host.image.name",HOST_IMAGE_ID:"host.image.id",HOST_IMAGE_VERSION:"host.image.version",K8S_CLUSTER_NAME:"k8s.cluster.name",K8S_NODE_NAME:"k8s.node.name",K8S_NODE_UID:"k8s.node.uid",K8S_NAMESPACE_NAME:"k8s.namespace.name",K8S_POD_UID:"k8s.pod.uid",K8S_POD_NAME:"k8s.pod.name",K8S_CONTAINER_NAME:"k8s.container.name",K8S_REPLICASET_UID:"k8s.replicaset.uid",K8S_REPLICASET_NAME:"k8s.replicaset.name",K8S_DEPLOYMENT_UID:"k8s.deployment.uid",K8S_DEPLOYMENT_NAME:"k8s.deployment.name",K8S_STATEFULSET_UID:"k8s.statefulset.uid",K8S_STATEFULSET_NAME:"k8s.statefulset.name",K8S_DAEMONSET_UID:"k8s.daemonset.uid",K8S_DAEMONSET_NAME:"k8s.daemonset.name",K8S_JOB_UID:"k8s.job.uid",K8S_JOB_NAME:"k8s.job.name",K8S_CRONJOB_UID:"k8s.cronjob.uid",K8S_CRONJOB_NAME:"k8s.cronjob.name",OS_TYPE:"os.type",OS_DESCRIPTION:"os.description",OS_NAME:"os.name",OS_VERSION:"os.version",PROCESS_PID:"process.pid",PROCESS_EXECUTABLE_NAME:"process.executable.name",PROCESS_EXECUTABLE_PATH:"process.executable.path",PROCESS_COMMAND:"process.command",PROCESS_COMMAND_LINE:"process.command_line",PROCESS_COMMAND_ARGS:"process.command_args",PROCESS_OWNER:"process.owner",PROCESS_RUNTIME_NAME:"process.runtime.name",PROCESS_RUNTIME_VERSION:"process.runtime.version",PROCESS_RUNTIME_DESCRIPTION:"process.runtime.description",SERVICE_NAME:"service.name",SERVICE_NAMESPACE:"service.namespace",SERVICE_INSTANCE_ID:"service.instance.id",SERVICE_VERSION:"service.version",TELEMETRY_SDK_NAME:"telemetry.sdk.name",TELEMETRY_SDK_LANGUAGE:"telemetry.sdk.language",TELEMETRY_SDK_VERSION:"telemetry.sdk.version",TELEMETRY_AUTO_VERSION:"telemetry.auto.version",WEBENGINE_NAME:"webengine.name",WEBENGINE_VERSION:"webengine.version",WEBENGINE_DESCRIPTION:"webengine.description"},e.CloudProviderValues={ALIBABA_CLOUD:"alibaba_cloud",AWS:"aws",AZURE:"azure",GCP:"gcp"},e.CloudPlatformValues={ALIBABA_CLOUD_ECS:"alibaba_cloud_ecs",ALIBABA_CLOUD_FC:"alibaba_cloud_fc",AWS_EC2:"aws_ec2",AWS_ECS:"aws_ecs",AWS_EKS:"aws_eks",AWS_LAMBDA:"aws_lambda",AWS_ELASTIC_BEANSTALK:"aws_elastic_beanstalk",AZURE_VM:"azure_vm",AZURE_CONTAINER_INSTANCES:"azure_container_instances",AZURE_AKS:"azure_aks",AZURE_FUNCTIONS:"azure_functions",AZURE_APP_SERVICE:"azure_app_service",GCP_COMPUTE_ENGINE:"gcp_compute_engine",GCP_CLOUD_RUN:"gcp_cloud_run",GCP_KUBERNETES_ENGINE:"gcp_kubernetes_engine",GCP_CLOUD_FUNCTIONS:"gcp_cloud_functions",GCP_APP_ENGINE:"gcp_app_engine"},e.AwsEcsLaunchtypeValues={EC2:"ec2",FARGATE:"fargate"},e.HostArchValues={AMD64:"amd64",ARM32:"arm32",ARM64:"arm64",IA64:"ia64",PPC32:"ppc32",PPC64:"ppc64",X86:"x86"},e.OsTypeValues={WINDOWS:"windows",LINUX:"linux",DARWIN:"darwin",FREEBSD:"freebsd",NETBSD:"netbsd",OPENBSD:"openbsd",DRAGONFLYBSD:"dragonflybsd",HPUX:"hpux",AIX:"aix",SOLARIS:"solaris",Z_OS:"z_os"},e.TelemetrySdkLanguageValues={CPP:"cpp",DOTNET:"dotnet",ERLANG:"erlang",GO:"go",JAVA:"java",NODEJS:"nodejs",PHP:"php",PYTHON:"python",RUBY:"ruby",WEBJS:"webjs"}}),xc=l(e=>{var t=e&&e.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),r=e&&e.__exportStar||function(e,r){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(r,n)||t(r,e,n)};Object.defineProperty(e,"__esModule",{value:!0}),r(wc(),e)}),Ae=l(e=>{var t=e&&e.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),r=e&&e.__exportStar||function(e,r){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(r,n)||t(r,e,n)};Object.defineProperty(e,"__esModule",{value:!0}),r(Cc(),e),r(xc(),e)}),Uc=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.SDK_INFO=void 0;var t=to(),r=Ae();e.SDK_INFO={[r.SemanticResourceAttributes.TELEMETRY_SDK_NAME]:"opentelemetry",[r.SemanticResourceAttributes.PROCESS_RUNTIME_NAME]:"node",[r.SemanticResourceAttributes.TELEMETRY_SDK_LANGUAGE]:r.TelemetrySdkLanguageValues.NODEJS,[r.SemanticResourceAttributes.TELEMETRY_SDK_VERSION]:t.VERSION}}),Bc=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.unrefTimer=void 0,e.unrefTimer=function(e){e.unref()}}),qc=l(e=>{var t=e&&e.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),r=e&&e.__exportStar||function(e,r){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(r,n)||t(r,e,n)};Object.defineProperty(e,"__esModule",{value:!0}),r(Rc(),e),r(bc(),e),r(yc(),e),r(Lc(),e),r(Mc(),e),r(Uc(),e),r(Bc(),e)}),ro=l(e=>{var t=e&&e.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),r=e&&e.__exportStar||function(e,r){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(r,n)||t(r,e,n)};Object.defineProperty(e,"__esModule",{value:!0}),r(qc(),e)}),jc=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.addHrTimes=e.isTimeInput=e.isTimeInputHrTime=e.hrTimeToMicroseconds=e.hrTimeToMilliseconds=e.hrTimeToNanoseconds=e.hrTimeToTimeStamp=e.hrTimeDuration=e.timeInputToHrTime=e.hrTime=e.getTimeOrigin=e.millisToHrTime=void 0;var t=ro(),r=9,n=1e6,i=1e9;function o(e){return[Math.trunc(e/1e3),Math.round(e%1e3*n)]}function s(){let e=t.otperformance.timeOrigin;if("number"!=typeof e){let r=t.otperformance;e=r.timing&&r.timing.fetchStart}return e}function a(e){return c(o(s()),o("number"==typeof e?e:t.otperformance.now()))}function u(e){return Array.isArray(e)&&2===e.length&&"number"==typeof e[0]&&"number"==typeof e[1]}function c(e,t){let r=[e[0]+t[0],e[1]+t[1]];return r[1]>=i&&(r[1]-=i,r[0]+=1),r}e.millisToHrTime=o,e.getTimeOrigin=s,e.hrTime=a,e.timeInputToHrTime=function(e){if(u(e))return e;if("number"==typeof e)return e<s()?a(e):o(e);if(e instanceof Date)return o(e.getTime());throw TypeError("Invalid input type")},e.hrTimeDuration=function(e,t){let r=t[0]-e[0],n=t[1]-e[1];return n<0&&(r-=1,n+=i),[r,n]},e.hrTimeToTimeStamp=function(e){let t=r,n=`${"0".repeat(t)}${e[1]}Z`,i=n.substr(n.length-t-1);return new Date(1e3*e[0]).toISOString().replace("000Z",i)},e.hrTimeToNanoseconds=function(e){return e[0]*i+e[1]},e.hrTimeToMilliseconds=function(e){return 1e3*e[0]+e[1]/1e6},e.hrTimeToMicroseconds=function(e){return 1e6*e[0]+e[1]/1e3},e.isTimeInputHrTime=u,e.isTimeInput=function(e){return u(e)||"number"==typeof e||e instanceof Date},e.addHrTimes=c}),kc=l(e=>{Object.defineProperty(e,"__esModule",{value:!0})}),$c=l(e=>{var t;Object.defineProperty(e,"__esModule",{value:!0}),e.ExportResultCode=void 0,function(e){e[e.SUCCESS=0]="SUCCESS",e[e.FAILED=1]="FAILED"}(t=e.ExportResultCode||(e.ExportResultCode={}))}),Wc=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.CompositePropagator=void 0;var t=(h(),m(d));e.CompositePropagator=class{constructor(e={}){var t;this._propagators=null!=(t=e.propagators)?t:[],this._fields=Array.from(new Set(this._propagators.map(e=>"function"==typeof e.fields?e.fields():[]).reduce((e,t)=>e.concat(t),[])))}inject(e,r,n){for(let i of this._propagators)try{i.inject(e,r,n)}catch(e){t.diag.warn(`Failed to inject with ${i.constructor.name}. Err: ${e.message}`)}}extract(e,r,n){return this._propagators.reduce((e,i)=>{try{return i.extract(e,r,n)}catch(e){t.diag.warn(`Failed to inject with ${i.constructor.name}. Err: ${e.message}`)}return e},e)}fields(){return this._fields.slice()}}}),Kc=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.validateValue=e.validateKey=void 0;var t="[_0-9a-z-*/]",r=`[a-z]${t}{0,255}`,n=`[a-z0-9]${t}{0,240}@[a-z]${t}{0,13}`,i=RegExp(`^(?:${r}|${n})$`),o=/^[ -~]{0,255}[!-~]$/,s=/,|=/;e.validateKey=function(e){return i.test(e)},e.validateValue=function(e){return o.test(e)&&!s.test(e)}}),co=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.TraceState=void 0;var t=Kc(),r=32,n=512,i=",",o="=";e.TraceState=class e{constructor(e){this._internalState=new Map,e&&this._parse(e)}set(e,t){let r=this._clone();return r._internalState.has(e)&&r._internalState.delete(e),r._internalState.set(e,t),r}unset(e){let t=this._clone();return t._internalState.delete(e),t}get(e){return this._internalState.get(e)}serialize(){return this._keys().reduce((e,t)=>(e.push(t+o+this.get(t)),e),[]).join(i)}_parse(e){e.length>n||(this._internalState=e.split(i).reverse().reduce((e,r)=>{let n=r.trim(),i=n.indexOf(o);if(-1!==i){let o=n.slice(0,i),s=n.slice(i+1,r.length);(0,t.validateKey)(o)&&(0,t.validateValue)(s)&&e.set(o,s)}return e},new Map),this._internalState.size>r&&(this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,r))))}_keys(){return Array.from(this._internalState.keys()).reverse()}_clone(){let t=new e;return t._internalState=new Map(this._internalState),t}}}),el=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.W3CTraceContextPropagator=e.parseTraceParent=e.TRACE_STATE_HEADER=e.TRACE_PARENT_HEADER=void 0;var t=(h(),m(d)),r=dr(),n=co();e.TRACE_PARENT_HEADER="traceparent",e.TRACE_STATE_HEADER="tracestate";var i="00",o="(?!ff)[\\da-f]{2}",s="(?![0]{32})[\\da-f]{32}",a="(?![0]{16})[\\da-f]{16}",u="[\\da-f]{2}",c=RegExp(`^\\s?(${o})-(${s})-(${a})-(${u})(-.*)?\\s?$`);function l(e){let t=c.exec(e);return!t||"00"===t[1]&&t[5]?null:{traceId:t[2],spanId:t[3],traceFlags:parseInt(t[4],16)}}e.parseTraceParent=l;var p=class{inject(n,o,s){let a=t.trace.getSpanContext(n);if(!a||(0,r.isTracingSuppressed)(n)||!(0,t.isSpanContextValid)(a))return;let u=`${i}-${a.traceId}-${a.spanId}-0${Number(a.traceFlags||t.TraceFlags.NONE).toString(16)}`;s.set(o,e.TRACE_PARENT_HEADER,u),a.traceState&&s.set(o,e.TRACE_STATE_HEADER,a.traceState.serialize())}extract(r,i,o){let s=o.get(i,e.TRACE_PARENT_HEADER);if(!s)return r;let a=Array.isArray(s)?s[0]:s;if("string"!=typeof a)return r;let u=l(a);if(!u)return r;u.isRemote=!0;let c=o.get(i,e.TRACE_STATE_HEADER);if(c){let e=Array.isArray(c)?c.join(","):c;u.traceState=new n.TraceState("string"==typeof e?e:void 0)}return t.trace.setSpanContext(r,u)}fields(){return[e.TRACE_PARENT_HEADER,e.TRACE_STATE_HEADER]}};e.W3CTraceContextPropagator=p}),rl=l(e=>{Object.defineProperty(e,"__esModule",{value:!0})}),nl=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.getRPCMetadata=e.deleteRPCMetadata=e.setRPCMetadata=e.RPCType=void 0;var t,r=(0,(h(),m(d)).createContextKey)("OpenTelemetry SDK Context Key RPC_METADATA");!function(e){e.HTTP="http"}(t=e.RPCType||(e.RPCType={})),e.setRPCMetadata=function(e,t){return e.setValue(r,t)},e.deleteRPCMetadata=function(e){return e.deleteValue(r)},e.getRPCMetadata=function(e){return e.getValue(r)}}),ho=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.AlwaysOffSampler=void 0;var t=(h(),m(d));e.AlwaysOffSampler=class{shouldSample(){return{decision:t.SamplingDecision.NOT_RECORD}}toString(){return"AlwaysOffSampler"}}}),Eo=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.AlwaysOnSampler=void 0;var t=(h(),m(d));e.AlwaysOnSampler=class{shouldSample(){return{decision:t.SamplingDecision.RECORD_AND_SAMPLED}}toString(){return"AlwaysOnSampler"}}}),sl=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.ParentBasedSampler=void 0;var t=(h(),m(d)),r=Qs(),n=ho(),i=Eo();e.ParentBasedSampler=class{constructor(e){var t,o,s,a;this._root=e.root,this._root||((0,r.globalErrorHandler)(Error("ParentBasedSampler must have a root sampler configured")),this._root=new i.AlwaysOnSampler),this._remoteParentSampled=null!=(t=e.remoteParentSampled)?t:new i.AlwaysOnSampler,this._remoteParentNotSampled=null!=(o=e.remoteParentNotSampled)?o:new n.AlwaysOffSampler,this._localParentSampled=null!=(s=e.localParentSampled)?s:new i.AlwaysOnSampler,this._localParentNotSampled=null!=(a=e.localParentNotSampled)?a:new n.AlwaysOffSampler}shouldSample(e,r,n,i,o,s){let a=t.trace.getSpanContext(e);return a&&(0,t.isSpanContextValid)(a)?a.isRemote?a.traceFlags&t.TraceFlags.SAMPLED?this._remoteParentSampled.shouldSample(e,r,n,i,o,s):this._remoteParentNotSampled.shouldSample(e,r,n,i,o,s):a.traceFlags&t.TraceFlags.SAMPLED?this._localParentSampled.shouldSample(e,r,n,i,o,s):this._localParentNotSampled.shouldSample(e,r,n,i,o,s):this._root.shouldSample(e,r,n,i,o,s)}toString(){return`ParentBased{root=${this._root.toString()}, remoteParentSampled=${this._remoteParentSampled.toString()}, remoteParentNotSampled=${this._remoteParentNotSampled.toString()}, localParentSampled=${this._localParentSampled.toString()}, localParentNotSampled=${this._localParentNotSampled.toString()}}`}}}),ol=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.TraceIdRatioBasedSampler=void 0;var t=(h(),m(d));e.TraceIdRatioBasedSampler=class{constructor(e=0){this._ratio=e,this._ratio=this._normalize(e),this._upperBound=Math.floor(0xffffffff*this._ratio)}shouldSample(e,r){return{decision:(0,t.isValidTraceId)(r)&&this._accumulate(r)<this._upperBound?t.SamplingDecision.RECORD_AND_SAMPLED:t.SamplingDecision.NOT_RECORD}}toString(){return`TraceIdRatioBased{${this._ratio}}`}_normalize(e){return"number"!=typeof e||isNaN(e)?0:e>=1?1:e<=0?0:e}_accumulate(e){let t=0;for(let r=0;r<e.length/8;r++){let n=8*r;t=(t^parseInt(e.slice(n,n+8),16))>>>0}return t}}}),dl=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.isPlainObject=void 0;var t="[object Object]",r="[object Null]",n="[object Undefined]",i=Function.prototype.toString,o=i.call(Object),s=d(Object.getPrototypeOf,Object),a=Object.prototype,u=a.hasOwnProperty,c=Symbol?Symbol.toStringTag:void 0,l=a.toString;function d(e,t){return function(r){return e(t(r))}}function p(e){return null!=e&&"object"==typeof e}function _(e){return null==e?void 0===e?n:r:c&&c in Object(e)?h(e):g(e)}function h(e){let t=u.call(e,c),r=e[c],n=!1;try{e[c]=void 0,n=!0}catch{}let i=l.call(e);return n&&(t?e[c]=r:delete e[c]),i}function g(e){return l.call(e)}e.isPlainObject=function(e){if(!p(e)||_(e)!==t)return!1;let r=s(e);if(null===r)return!0;let n=u.call(r,"constructor")&&r.constructor;return"function"==typeof n&&n instanceof n&&i.call(n)===o}}),El=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.merge=void 0;var t=dl(),r=20;function n(e){return s(e)?e.slice():e}function i(e,t,d=0,p){let _;if(!(d>r)){if(d++,c(e)||c(t)||a(t))_=n(t);else if(s(e)){if(_=e.slice(),s(t))for(let e=0,r=t.length;e<r;e++)_.push(n(t[e]));else if(u(t)){let e=Object.keys(t);for(let r=0,i=e.length;r<i;r++){let i=e[r];_[i]=n(t[i])}}}else if(u(e))if(u(t)){if(!l(e,t))return t;_=Object.assign({},e);let r=Object.keys(t);for(let n=0,s=r.length;n<s;n++){let s=r[n],a=t[s];if(c(a))typeof a>"u"?delete _[s]:_[s]=a;else{let r=_[s],n=a;if(o(e,s,p)||o(t,s,p))delete _[s];else{if(u(r)&&u(n)){let i=p.get(r)||[],o=p.get(n)||[];i.push({obj:e,key:s}),o.push({obj:t,key:s}),p.set(r,i),p.set(n,o)}_[s]=i(_[s],a,d,p)}}}}else _=t;return _}}function o(e,t,r){let n=r.get(e[t])||[];for(let r=0,i=n.length;r<i;r++){let i=n[r];if(i.key===t&&i.obj===e)return!0}return!1}function s(e){return Array.isArray(e)}function a(e){return"function"==typeof e}function u(e){return!c(e)&&!s(e)&&!a(e)&&"object"==typeof e}function c(e){return"string"==typeof e||"number"==typeof e||"boolean"==typeof e||typeof e>"u"||e instanceof Date||e instanceof RegExp||null===e}function l(e,r){return!(!(0,t.isPlainObject)(e)||!(0,t.isPlainObject)(r))}e.merge=function(...e){let t=e.shift(),r=new WeakMap;for(;e.length>0;)t=i(t,e.shift(),0,r);return t}}),ml=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.callWithTimeout=e.TimeoutError=void 0;var t=class e extends Error{constructor(t){super(t),Object.setPrototypeOf(this,e.prototype)}};e.TimeoutError=t,e.callWithTimeout=function(e,r){let n;return Promise.race([e,new Promise(function(e,i){n=setTimeout(function(){i(new t("Operation timed out."))},r)})]).then(e=>(clearTimeout(n),e),e=>{throw clearTimeout(n),e})}}),Tl=l(e=>{function t(e,t){return"string"==typeof t?e===t:!!e.match(t)}Object.defineProperty(e,"__esModule",{value:!0}),e.isUrlIgnored=e.urlMatches=void 0,e.urlMatches=t,e.isUrlIgnored=function(e,r){if(!r)return!1;for(let n of r)if(t(e,n))return!0;return!1}}),Sl=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.isWrapped=void 0,e.isWrapped=function(e){return"function"==typeof e&&"function"==typeof e.__original&&"function"==typeof e.__unwrap&&!0===e.__wrapped}}),Al=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.Deferred=void 0,e.Deferred=class{constructor(){this._promise=new Promise((e,t)=>{this._resolve=e,this._reject=t})}get promise(){return this._promise}resolve(e){this._resolve(e)}reject(e){this._reject(e)}}}),Ol=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.BindOnceFuture=void 0;var t=Al();e.BindOnceFuture=class{constructor(e,r){this._callback=e,this._that=r,this._isCalled=!1,this._deferred=new t.Deferred}get isCalled(){return this._isCalled}get promise(){return this._deferred.promise}call(...e){if(!this._isCalled){this._isCalled=!0;try{Promise.resolve(this._callback.call(this._that,...e)).then(e=>this._deferred.resolve(e),e=>this._deferred.reject(e))}catch(e){this._deferred.reject(e)}}return this._deferred.promise}}}),bl=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e._export=void 0;var t=(h(),m(d)),r=dr();e._export=function(e,n){return new Promise(i=>{t.context.with((0,r.suppressTracing)(t.context.active()),()=>{e.export(n,e=>{i(e)})})})}}),A=l(e=>{var t=e&&e.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),r=e&&e.__exportStar||function(e,r){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(r,n)||t(r,e,n)};Object.defineProperty(e,"__esModule",{value:!0}),e.internal=e.baggageUtils=void 0,r(pc(),e),r(hc(),e),r(Tc(),e),r(Qs(),e),r(Ys(),e),r(jc(),e),r(kc(),e),r($c(),e),e.baggageUtils=$s(),r(ro(),e),r(Wc(),e),r(el(),e),r(rl(),e),r(nl(),e),r(ho(),e),r(Eo(),e),r(sl(),e),r(ol(),e),r(dr(),e),r(co(),e),r(Yr(),e),r(El(),e),r(Zs(),e),r(ml(),e),r(Tl(),e),r(Sl(),e),r(Ol(),e),r(to(),e),e.internal={_export:bl()._export}}),Pl=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.ExceptionEventName=void 0,e.ExceptionEventName="exception"}),Po=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.Span=void 0;var t=(h(),m(d)),r=A(),n=Ae(),i=Pl();e.Span=class{constructor(e,n,i,o,s,a,u=[],c,l,p){this.attributes={},this.links=[],this.events=[],this._droppedAttributesCount=0,this._droppedEventsCount=0,this._droppedLinksCount=0,this.status={code:t.SpanStatusCode.UNSET},this.endTime=[0,0],this._ended=!1,this._duration=[-1,-1],this.name=i,this._spanContext=o,this.parentSpanId=a,this.kind=s,this.links=u;let _=Date.now();this._performanceStartTime=r.otperformance.now(),this._performanceOffset=_-(this._performanceStartTime+(0,r.getTimeOrigin)()),this._startTimeProvided=null!=c,this.startTime=this._getTime(c??_),this.resource=e.resource,this.instrumentationLibrary=e.instrumentationLibrary,this._spanLimits=e.getSpanLimits(),null!=p&&this.setAttributes(p),this._spanProcessor=e.getActiveSpanProcessor(),this._spanProcessor.onStart(this,n),this._attributeValueLengthLimit=this._spanLimits.attributeValueLengthLimit||0}spanContext(){return this._spanContext}setAttribute(e,n){return null==n||this._isSpanEnded()||(0===e.length?t.diag.warn(`Invalid attribute key: ${e}`):(0,r.isAttributeValue)(n)?Object.keys(this.attributes).length>=this._spanLimits.attributeCountLimit&&!Object.prototype.hasOwnProperty.call(this.attributes,e)?this._droppedAttributesCount++:this.attributes[e]=this._truncateToSize(n):t.diag.warn(`Invalid attribute value set for key: ${e}`)),this}setAttributes(e){for(let[t,r]of Object.entries(e))this.setAttribute(t,r);return this}addEvent(e,n,i){if(this._isSpanEnded())return this;if(0===this._spanLimits.eventCountLimit)return t.diag.warn("No events allowed."),this._droppedEventsCount++,this;this.events.length>=this._spanLimits.eventCountLimit&&(t.diag.warn("Dropping extra events."),this.events.shift(),this._droppedEventsCount++),(0,r.isTimeInput)(n)&&((0,r.isTimeInput)(i)||(i=n),n=void 0);let o=(0,r.sanitizeAttributes)(n);return this.events.push({name:e,attributes:o,time:this._getTime(i),droppedAttributesCount:0}),this}setStatus(e){return this._isSpanEnded()||(this.status=e),this}updateName(e){return this._isSpanEnded()||(this.name=e),this}end(e){if(this._isSpanEnded())return void t.diag.error(`${this.name} ${this._spanContext.traceId}-${this._spanContext.spanId} - You can only call end() on a span once.`);this._ended=!0,this.endTime=this._getTime(e),this._duration=(0,r.hrTimeDuration)(this.startTime,this.endTime),this._duration[0]<0&&(t.diag.warn("Inconsistent start and end time, startTime > endTime. Setting span duration to 0ms.",this.startTime,this.endTime),this.endTime=this.startTime.slice(),this._duration=[0,0]),this._spanProcessor.onEnd(this)}_getTime(e){if("number"==typeof e&&e<r.otperformance.now())return(0,r.hrTime)(e+this._performanceOffset);if("number"==typeof e)return(0,r.millisToHrTime)(e);if(e instanceof Date)return(0,r.millisToHrTime)(e.getTime());if((0,r.isTimeInputHrTime)(e))return e;if(this._startTimeProvided)return(0,r.millisToHrTime)(Date.now());let t=r.otperformance.now()-this._performanceStartTime;return(0,r.addHrTimes)(this.startTime,(0,r.millisToHrTime)(t))}isRecording(){return!1===this._ended}recordException(e,r){let o={};"string"==typeof e?o[n.SemanticAttributes.EXCEPTION_MESSAGE]=e:e&&(e.code?o[n.SemanticAttributes.EXCEPTION_TYPE]=e.code.toString():e.name&&(o[n.SemanticAttributes.EXCEPTION_TYPE]=e.name),e.message&&(o[n.SemanticAttributes.EXCEPTION_MESSAGE]=e.message),e.stack&&(o[n.SemanticAttributes.EXCEPTION_STACKTRACE]=e.stack)),o[n.SemanticAttributes.EXCEPTION_TYPE]||o[n.SemanticAttributes.EXCEPTION_MESSAGE]?this.addEvent(i.ExceptionEventName,o,r):t.diag.warn(`Failed to record an exception ${e}`)}get duration(){return this._duration}get ended(){return this._ended}get droppedAttributesCount(){return this._droppedAttributesCount}get droppedEventsCount(){return this._droppedEventsCount}get droppedLinksCount(){return this._droppedLinksCount}_isSpanEnded(){return this._ended&&t.diag.warn(`Can not execute the operation on ended Span {traceId: ${this._spanContext.traceId}, spanId: ${this._spanContext.spanId}}`),this._ended}_truncateToLimitUtil(e,t){return e.length<=t?e:e.substr(0,t)}_truncateToSize(e){let r=this._attributeValueLengthLimit;return r<=0?(t.diag.warn(`Attribute value limit must be positive, got ${r}`),e):"string"==typeof e?this._truncateToLimitUtil(e,r):Array.isArray(e)?e.map(e=>"string"==typeof e?this._truncateToLimitUtil(e,r):e):e}}}),gr=l(e=>{var t;Object.defineProperty(e,"__esModule",{value:!0}),e.SamplingDecision=void 0,function(e){e[e.NOT_RECORD=0]="NOT_RECORD",e[e.RECORD=1]="RECORD",e[e.RECORD_AND_SAMPLED=2]="RECORD_AND_SAMPLED"}(t=e.SamplingDecision||(e.SamplingDecision={}))}),In=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.AlwaysOffSampler=void 0;var t=gr();e.AlwaysOffSampler=class{shouldSample(){return{decision:t.SamplingDecision.NOT_RECORD}}toString(){return"AlwaysOffSampler"}}}),Mn=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.AlwaysOnSampler=void 0;var t=gr();e.AlwaysOnSampler=class{shouldSample(){return{decision:t.SamplingDecision.RECORD_AND_SAMPLED}}toString(){return"AlwaysOnSampler"}}}),Mo=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.ParentBasedSampler=void 0;var t=(h(),m(d)),r=A(),n=In(),i=Mn();e.ParentBasedSampler=class{constructor(e){var t,o,s,a;this._root=e.root,this._root||((0,r.globalErrorHandler)(Error("ParentBasedSampler must have a root sampler configured")),this._root=new i.AlwaysOnSampler),this._remoteParentSampled=null!=(t=e.remoteParentSampled)?t:new i.AlwaysOnSampler,this._remoteParentNotSampled=null!=(o=e.remoteParentNotSampled)?o:new n.AlwaysOffSampler,this._localParentSampled=null!=(s=e.localParentSampled)?s:new i.AlwaysOnSampler,this._localParentNotSampled=null!=(a=e.localParentNotSampled)?a:new n.AlwaysOffSampler}shouldSample(e,r,n,i,o,s){let a=t.trace.getSpanContext(e);return a&&(0,t.isSpanContextValid)(a)?a.isRemote?a.traceFlags&t.TraceFlags.SAMPLED?this._remoteParentSampled.shouldSample(e,r,n,i,o,s):this._remoteParentNotSampled.shouldSample(e,r,n,i,o,s):a.traceFlags&t.TraceFlags.SAMPLED?this._localParentSampled.shouldSample(e,r,n,i,o,s):this._localParentNotSampled.shouldSample(e,r,n,i,o,s):this._root.shouldSample(e,r,n,i,o,s)}toString(){return`ParentBased{root=${this._root.toString()}, remoteParentSampled=${this._remoteParentSampled.toString()}, remoteParentNotSampled=${this._remoteParentNotSampled.toString()}, localParentSampled=${this._localParentSampled.toString()}, localParentNotSampled=${this._localParentNotSampled.toString()}}`}}}),Co=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.TraceIdRatioBasedSampler=void 0;var t=(h(),m(d)),r=gr();e.TraceIdRatioBasedSampler=class{constructor(e=0){this._ratio=e,this._ratio=this._normalize(e),this._upperBound=Math.floor(0xffffffff*this._ratio)}shouldSample(e,n){return{decision:(0,t.isValidTraceId)(n)&&this._accumulate(n)<this._upperBound?r.SamplingDecision.RECORD_AND_SAMPLED:r.SamplingDecision.NOT_RECORD}}toString(){return`TraceIdRatioBased{${this._ratio}}`}_normalize(e){return"number"!=typeof e||isNaN(e)?0:e>=1?1:e<=0?0:e}_accumulate(e){let t=0;for(let r=0;r<e.length/8;r++){let n=8*r;t=(t^parseInt(e.slice(n,n+8),16))>>>0}return t}}}),Do=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.buildSamplerFromEnv=e.loadDefaultConfig=void 0;var t=(h(),m(d)),r=A(),n=In(),i=Mn(),o=Mo(),s=Co(),a=(0,r.getEnv)(),u=r.TracesSamplerValues.AlwaysOn,c=1;function l(e=(0,r.getEnv)()){switch(e.OTEL_TRACES_SAMPLER){case r.TracesSamplerValues.AlwaysOn:return new i.AlwaysOnSampler;case r.TracesSamplerValues.AlwaysOff:return new n.AlwaysOffSampler;case r.TracesSamplerValues.ParentBasedAlwaysOn:return new o.ParentBasedSampler({root:new i.AlwaysOnSampler});case r.TracesSamplerValues.ParentBasedAlwaysOff:return new o.ParentBasedSampler({root:new n.AlwaysOffSampler});case r.TracesSamplerValues.TraceIdRatio:return new s.TraceIdRatioBasedSampler(p(e));case r.TracesSamplerValues.ParentBasedTraceIdRatio:return new o.ParentBasedSampler({root:new s.TraceIdRatioBasedSampler(p(e))});default:return t.diag.error(`OTEL_TRACES_SAMPLER value "${e.OTEL_TRACES_SAMPLER} invalid, defaulting to ${u}".`),new i.AlwaysOnSampler}}function p(e){if(void 0===e.OTEL_TRACES_SAMPLER_ARG||""===e.OTEL_TRACES_SAMPLER_ARG)return t.diag.error(`OTEL_TRACES_SAMPLER_ARG is blank, defaulting to ${c}.`),c;let r=Number(e.OTEL_TRACES_SAMPLER_ARG);return isNaN(r)?(t.diag.error(`OTEL_TRACES_SAMPLER_ARG=${e.OTEL_TRACES_SAMPLER_ARG} was given, but it is invalid, defaulting to ${c}.`),c):r<0||r>1?(t.diag.error(`OTEL_TRACES_SAMPLER_ARG=${e.OTEL_TRACES_SAMPLER_ARG} was given, but it is out of range ([0..1]), defaulting to ${c}.`),c):r}e.loadDefaultConfig=function(){return{sampler:l(a),forceFlushTimeoutMillis:3e4,generalLimits:{attributeValueLengthLimit:(0,r.getEnv)().OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT,attributeCountLimit:(0,r.getEnv)().OTEL_ATTRIBUTE_COUNT_LIMIT},spanLimits:{attributeValueLengthLimit:(0,r.getEnv)().OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT,attributeCountLimit:(0,r.getEnv)().OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT,linkCountLimit:(0,r.getEnv)().OTEL_SPAN_LINK_COUNT_LIMIT,eventCountLimit:(0,r.getEnv)().OTEL_SPAN_EVENT_COUNT_LIMIT,attributePerEventCountLimit:(0,r.getEnv)().OTEL_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT,attributePerLinkCountLimit:(0,r.getEnv)().OTEL_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT}}},e.buildSamplerFromEnv=l}),Bo=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.reconfigureLimits=e.mergeConfig=void 0;var t=Do(),r=A();e.mergeConfig=function(e){let r={sampler:(0,t.buildSamplerFromEnv)()},n=(0,t.loadDefaultConfig)(),i=Object.assign({},n,r,e);return i.generalLimits=Object.assign({},n.generalLimits,e.generalLimits||{}),i.spanLimits=Object.assign({},n.spanLimits,e.spanLimits||{}),i},e.reconfigureLimits=function(e){var t,n,i,o,s,a,u,c,l,d,p,_;let h=Object.assign({},e.spanLimits),g=(0,r.getEnvWithoutDefaults)();return h.attributeCountLimit=null!=(a=null!=(s=null!=(o=null!=(n=null==(t=e.spanLimits)?void 0:t.attributeCountLimit)?n:null==(i=e.generalLimits)?void 0:i.attributeCountLimit)?o:g.OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT)?s:g.OTEL_ATTRIBUTE_COUNT_LIMIT)?a:r.DEFAULT_ATTRIBUTE_COUNT_LIMIT,h.attributeValueLengthLimit=null!=(_=null!=(p=null!=(d=null!=(c=null==(u=e.spanLimits)?void 0:u.attributeValueLengthLimit)?c:null==(l=e.generalLimits)?void 0:l.attributeValueLengthLimit)?d:g.OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT)?p:g.OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT)?_:r.DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT,Object.assign({},e,{spanLimits:h})}}),wl=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.BatchSpanProcessorBase=void 0;var t=(h(),m(d)),r=A();e.BatchSpanProcessorBase=class{constructor(e,n){this._exporter=e,this._isExporting=!1,this._finishedSpans=[],this._droppedSpansCount=0;let i=(0,r.getEnv)();this._maxExportBatchSize="number"==typeof n?.maxExportBatchSize?n.maxExportBatchSize:i.OTEL_BSP_MAX_EXPORT_BATCH_SIZE,this._maxQueueSize="number"==typeof n?.maxQueueSize?n.maxQueueSize:i.OTEL_BSP_MAX_QUEUE_SIZE,this._scheduledDelayMillis="number"==typeof n?.scheduledDelayMillis?n.scheduledDelayMillis:i.OTEL_BSP_SCHEDULE_DELAY,this._exportTimeoutMillis="number"==typeof n?.exportTimeoutMillis?n.exportTimeoutMillis:i.OTEL_BSP_EXPORT_TIMEOUT,this._shutdownOnce=new r.BindOnceFuture(this._shutdown,this),this._maxExportBatchSize>this._maxQueueSize&&(t.diag.warn("BatchSpanProcessor: maxExportBatchSize must be smaller or equal to maxQueueSize, setting maxExportBatchSize to match maxQueueSize"),this._maxExportBatchSize=this._maxQueueSize)}forceFlush(){return this._shutdownOnce.isCalled?this._shutdownOnce.promise:this._flushAll()}onStart(e,t){}onEnd(e){this._shutdownOnce.isCalled||e.spanContext().traceFlags&t.TraceFlags.SAMPLED&&this._addToBuffer(e)}shutdown(){return this._shutdownOnce.call()}_shutdown(){return Promise.resolve().then(()=>this.onShutdown()).then(()=>this._flushAll()).then(()=>this._exporter.shutdown())}_addToBuffer(e){if(this._finishedSpans.length>=this._maxQueueSize){0===this._droppedSpansCount&&t.diag.debug("maxQueueSize reached, dropping spans"),this._droppedSpansCount++;return}this._droppedSpansCount>0&&(t.diag.warn(`Dropped ${this._droppedSpansCount} spans because maxQueueSize reached`),this._droppedSpansCount=0),this._finishedSpans.push(e),this._maybeStartTimer()}_flushAll(){return new Promise((e,t)=>{let r=[],n=Math.ceil(this._finishedSpans.length/this._maxExportBatchSize);for(let e=0,t=n;e<t;e++)r.push(this._flushOneBatch());Promise.all(r).then(()=>{e()}).catch(t)})}_flushOneBatch(){return this._clearTimer(),0===this._finishedSpans.length?Promise.resolve():new Promise((e,n)=>{let i=setTimeout(()=>{n(Error("Timeout"))},this._exportTimeoutMillis);t.context.with((0,r.suppressTracing)(t.context.active()),()=>{let t=this._finishedSpans.splice(0,this._maxExportBatchSize),o=()=>this._exporter.export(t,t=>{var o;clearTimeout(i),t.code===r.ExportResultCode.SUCCESS?e():n(null!=(o=t.error)?o:Error("BatchSpanProcessor: span export failed"))}),s=t.map(e=>e.resource).filter(e=>e.asyncAttributesPending);0===s.length?o():Promise.all(s.map(e=>{var t;return null==(t=e.waitForAsyncAttributes)?void 0:t.call(e)})).then(o,e=>{(0,r.globalErrorHandler)(e),n(e)})})})}_maybeStartTimer(){if(this._isExporting)return;let e=()=>{this._isExporting=!0,this._flushOneBatch().then(()=>{this._isExporting=!1,this._finishedSpans.length>0&&(this._clearTimer(),this._maybeStartTimer())}).catch(e=>{this._isExporting=!1,(0,r.globalErrorHandler)(e)})};if(this._finishedSpans.length>=this._maxExportBatchSize)return e();void 0===this._timer&&(this._timer=setTimeout(()=>e(),this._scheduledDelayMillis),(0,r.unrefTimer)(this._timer))}_clearTimer(){void 0!==this._timer&&(clearTimeout(this._timer),this._timer=void 0)}}}),xl=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.BatchSpanProcessor=void 0;var t=wl();e.BatchSpanProcessor=class extends t.BatchSpanProcessorBase{onShutdown(){}}}),Bl=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.RandomIdGenerator=void 0;var t=8,r=16;e.RandomIdGenerator=class{constructor(){this.generateTraceId=i(r),this.generateSpanId=i(t)}};var n=Buffer.allocUnsafe(r);function i(e){return function(){for(let t=0;t<e/4;t++)n.writeUInt32BE(0x100000000*Math.random()>>>0,4*t);for(let t=0;t<e&&!(n[t]>0);t++)t===e-1&&(n[e-1]=1);return n.toString("hex",0,e)}}}),Gl=l(e=>{var t=e&&e.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),r=e&&e.__exportStar||function(e,r){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(r,n)||t(r,e,n)};Object.defineProperty(e,"__esModule",{value:!0}),r(xl(),e),r(Bl(),e)}),Gn=l(e=>{var t=e&&e.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),r=e&&e.__exportStar||function(e,r){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(r,n)||t(r,e,n)};Object.defineProperty(e,"__esModule",{value:!0}),r(Gl(),e)}),Vl=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.Tracer=void 0;var t=(h(),m(d)),r=A(),n=Po(),i=Bo(),o=Gn();e.Tracer=class{constructor(e,t,r){this._tracerProvider=r;let n=(0,i.mergeConfig)(t);this._sampler=n.sampler,this._generalLimits=n.generalLimits,this._spanLimits=n.spanLimits,this._idGenerator=t.idGenerator||new o.RandomIdGenerator,this.resource=r.resource,this.instrumentationLibrary=e}startSpan(e,i={},o=t.context.active()){var s,a,u;i.root&&(o=t.trace.deleteSpan(o));let c=t.trace.getSpan(o);if((0,r.isTracingSuppressed)(o))return t.diag.debug("Instrumentation suppressed, returning Noop Span"),t.trace.wrapSpanContext(t.INVALID_SPAN_CONTEXT);let l=c?.spanContext(),p=this._idGenerator.generateSpanId(),_,g,f;l&&t.trace.isSpanContextValid(l)?(_=l.traceId,g=l.traceState,f=l.spanId):_=this._idGenerator.generateTraceId();let E=null!=(s=i.kind)?s:t.SpanKind.INTERNAL,T=(null!=(a=i.links)?a:[]).map(e=>({context:e.context,attributes:(0,r.sanitizeAttributes)(e.attributes)})),S=(0,r.sanitizeAttributes)(i.attributes),v=this._sampler.shouldSample(o,_,e,E,S,T);g=null!=(u=v.traceState)?u:g;let O={traceId:_,spanId:p,traceFlags:v.decision===t.SamplingDecision.RECORD_AND_SAMPLED?t.TraceFlags.SAMPLED:t.TraceFlags.NONE,traceState:g};if(v.decision===t.SamplingDecision.NOT_RECORD)return t.diag.debug("Recording is off, propagating context in a non-recording span"),t.trace.wrapSpanContext(O);let b=(0,r.sanitizeAttributes)(Object.assign(S,v.attributes));return new n.Span(this,o,e,O,E,f,T,i.startTime,void 0,b)}startActiveSpan(e,r,n,i){let o,s,a;if(arguments.length<2)return;2==arguments.length?a=r:3==arguments.length?(o=r,a=n):(o=r,s=n,a=i);let u=s??t.context.active(),c=this.startSpan(e,o,u),l=t.trace.setSpan(u,c);return t.context.with(l,a,void 0,c)}getGeneralLimits(){return this._generalLimits}getSpanLimits(){return this._spanLimits}getActiveSpanProcessor(){return this._tracerProvider.getActiveSpanProcessor()}}}),Hl=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.defaultServiceName=void 0,e.defaultServiceName=function(){return`unknown_service:${process.argv0}`}}),jo=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.normalizeType=e.normalizeArch=void 0,e.normalizeArch=e=>{switch(e){case"arm":return"arm32";case"ppc":return"ppc32";case"x64":return"amd64";default:return e}},e.normalizeType=e=>{switch(e){case"sunos":return"solaris";case"win32":return"windows";default:return e}}}),kn=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.execAsync=void 0;var t=z("child_process");e.execAsync=z("util").promisify(t.exec)}),jl=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.getMachineId=void 0;var t=kn(),r=(h(),m(d));e.getMachineId=async function(){try{let e=(await (0,t.execAsync)('ioreg -rd1 -c "IOPlatformExpertDevice"')).stdout.split(`
`).find(e=>e.includes("IOPlatformUUID"));if(!e)return"";let r=e.split('" = "');if(2===r.length)return r[1].slice(0,-1)}catch(e){r.diag.debug(`error reading machine id: ${e}`)}return""}}),Fl=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.getMachineId=void 0;var t=z("fs"),r=(h(),m(d));e.getMachineId=async function(){for(let e of["/etc/machine-id","/var/lib/dbus/machine-id"])try{return(await t.promises.readFile(e,{encoding:"utf8"})).trim()}catch(e){r.diag.debug(`error reading machine id: ${e}`)}return""}}),$l=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.getMachineId=void 0;var t=z("fs"),r=kn(),n=(h(),m(d));e.getMachineId=async function(){try{return(await t.promises.readFile("/etc/hostid",{encoding:"utf8"})).trim()}catch(e){n.diag.debug(`error reading machine id: ${e}`)}try{return(await (0,r.execAsync)("kenv -q smbios.system.uuid")).stdout.trim()}catch(e){n.diag.debug(`error reading machine id: ${e}`)}return""}}),Wl=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.getMachineId=void 0;var t=z("process"),r=kn(),n=(h(),m(d));e.getMachineId=async function(){let e="QUERY HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Cryptography /v MachineGuid",i="%windir%\\System32\\REG.exe";"ia32"===t.arch&&"PROCESSOR_ARCHITEW6432"in t.env&&(i="%windir%\\sysnative\\cmd.exe /c "+i);try{let t=(await (0,r.execAsync)(`${i} ${e}`)).stdout.split("REG_SZ");if(2===t.length)return t[1].trim()}catch(e){n.diag.debug(`error reading machine id: ${e}`)}return""}}),Kl=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.getMachineId=void 0;var t=(h(),m(d));e.getMachineId=async function(){return t.diag.debug("could not read machine-id: unsupported platform"),""}}),zl=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.getMachineId=void 0;var t,r=z("process");switch(e.getMachineId=t,r.platform){case"darwin":e.getMachineId=t=jl().getMachineId;break;case"linux":e.getMachineId=t=Fl().getMachineId;break;case"freebsd":e.getMachineId=t=$l().getMachineId;break;case"win32":e.getMachineId=t=Wl().getMachineId;break;default:e.getMachineId=t=Kl().getMachineId}}),$o=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.hostDetectorSync=void 0;var t=Ae(),r=Ze(),n=z("os"),i=jo(),o=zl();e.hostDetectorSync=new class{detect(e){let o={[t.SemanticResourceAttributes.HOST_NAME]:(0,n.hostname)(),[t.SemanticResourceAttributes.HOST_ARCH]:(0,i.normalizeArch)((0,n.arch)())};return new r.Resource(o,this._getAsyncAttributes())}_getAsyncAttributes(){return(0,o.getMachineId)().then(e=>{let r={};return e&&(r[t.SemanticResourceAttributes.HOST_ID]=e),r})}}}),Ql=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.hostDetector=void 0;var t=$o();e.hostDetector=new class{detect(e){return Promise.resolve(t.hostDetectorSync.detect(e))}}}),Ko=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.osDetectorSync=void 0;var t=Ae(),r=Ze(),n=z("os"),i=jo();e.osDetectorSync=new class{detect(e){let o={[t.SemanticResourceAttributes.OS_TYPE]:(0,i.normalizeType)((0,n.platform)()),[t.SemanticResourceAttributes.OS_VERSION]:(0,n.release)()};return new r.Resource(o)}}}),ed=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.osDetector=void 0;var t=Ko();e.osDetector=new class{detect(e){return Promise.resolve(t.osDetectorSync.detect(e))}}}),Qo=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.processDetectorSync=void 0;var t=(h(),m(d)),r=Ae(),n=Ze(),i=z("os");e.processDetectorSync=new class{detect(e){let o={[r.SemanticResourceAttributes.PROCESS_PID]:process.pid,[r.SemanticResourceAttributes.PROCESS_EXECUTABLE_NAME]:process.title,[r.SemanticResourceAttributes.PROCESS_EXECUTABLE_PATH]:process.execPath,[r.SemanticResourceAttributes.PROCESS_COMMAND_ARGS]:[process.argv[0],...process.execArgv,...process.argv.slice(1)],[r.SemanticResourceAttributes.PROCESS_RUNTIME_VERSION]:process.versions.node,[r.SemanticResourceAttributes.PROCESS_RUNTIME_NAME]:"nodejs",[r.SemanticResourceAttributes.PROCESS_RUNTIME_DESCRIPTION]:"Node.js"};process.argv.length>1&&(o[r.SemanticResourceAttributes.PROCESS_COMMAND]=process.argv[1]);try{let e=i.userInfo();o[r.SemanticResourceAttributes.PROCESS_OWNER]=e.username}catch(e){t.diag.debug(`error obtaining process owner: ${e}`)}return new n.Resource(o)}}}),td=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.processDetector=void 0;var t=Qo();e.processDetector=new class{detect(e){return Promise.resolve(t.processDetectorSync.detect(e))}}}),rd=l(e=>{var t=e&&e.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),r=e&&e.__exportStar||function(e,r){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(r,n)||t(r,e,n)};Object.defineProperty(e,"__esModule",{value:!0}),r(Hl(),e),r(Ql(),e),r(ed(),e),r($o(),e),r(Ko(),e),r(td(),e),r(Qo(),e)}),Jo=l(e=>{var t=e&&e.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),r=e&&e.__exportStar||function(e,r){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(r,n)||t(r,e,n)};Object.defineProperty(e,"__esModule",{value:!0}),r(rd(),e)}),Ze=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.Resource=void 0;var t=(h(),m(d)),r=Ae(),n=A(),i=Jo(),o=class e{constructor(e,r){var n;this._attributes=e,this.asyncAttributesPending=null!=r,this._syncAttributes=null!=(n=this._attributes)?n:{},this._asyncAttributesPromise=r?.then(e=>(this._attributes=Object.assign({},this._attributes,e),this.asyncAttributesPending=!1,e),e=>(t.diag.debug("a resource's async attributes promise rejected: %s",e),this.asyncAttributesPending=!1,{}))}static empty(){return e.EMPTY}static default(){return new e({[r.SemanticResourceAttributes.SERVICE_NAME]:(0,i.defaultServiceName)(),[r.SemanticResourceAttributes.TELEMETRY_SDK_LANGUAGE]:n.SDK_INFO[r.SemanticResourceAttributes.TELEMETRY_SDK_LANGUAGE],[r.SemanticResourceAttributes.TELEMETRY_SDK_NAME]:n.SDK_INFO[r.SemanticResourceAttributes.TELEMETRY_SDK_NAME],[r.SemanticResourceAttributes.TELEMETRY_SDK_VERSION]:n.SDK_INFO[r.SemanticResourceAttributes.TELEMETRY_SDK_VERSION]})}get attributes(){var e;return this.asyncAttributesPending&&t.diag.error("Accessing resource attributes before async attributes settled"),null!=(e=this._attributes)?e:{}}async waitForAsyncAttributes(){this.asyncAttributesPending&&await this._asyncAttributesPromise}merge(t){var r;if(!t)return this;let n=Object.assign(Object.assign({},this._syncAttributes),null!=(r=t._syncAttributes)?r:t.attributes);return this._asyncAttributesPromise||t._asyncAttributesPromise?new e(n,Promise.all([this._asyncAttributesPromise,t._asyncAttributesPromise]).then(([e,r])=>{var n;return Object.assign(Object.assign(Object.assign(Object.assign({},this._syncAttributes),e),null!=(n=t._syncAttributes)?n:t.attributes),r)})):new e(n)}};e.Resource=o,o.EMPTY=new o({})}),sd=l(e=>{Object.defineProperty(e,"__esModule",{value:!0})}),ad=l(e=>{Object.defineProperty(e,"__esModule",{value:!0})}),cd=l(e=>{Object.defineProperty(e,"__esModule",{value:!0})}),ld=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.browserDetector=void 0;var t=rt();e.browserDetector=new class{detect(e){return Promise.resolve(t.browserDetectorSync.detect(e))}}}),na=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.envDetectorSync=void 0;var t=(h(),m(d)),r=A(),n=Ae(),i=Ze();e.envDetectorSync=new class{constructor(){this._MAX_LENGTH=255,this._COMMA_SEPARATOR=",",this._LABEL_KEY_VALUE_SPLITTER="=",this._ERROR_MESSAGE_INVALID_CHARS="should be a ASCII string with a length greater than 0 and not exceed "+this._MAX_LENGTH+" characters.",this._ERROR_MESSAGE_INVALID_VALUE="should be a ASCII string with a length not exceed "+this._MAX_LENGTH+" characters."}detect(e){let o={},s=(0,r.getEnv)(),a=s.OTEL_RESOURCE_ATTRIBUTES,u=s.OTEL_SERVICE_NAME;if(a)try{let e=this._parseResourceAttributes(a);Object.assign(o,e)}catch(e){t.diag.debug(`EnvDetector failed: ${e.message}`)}return u&&(o[n.SemanticResourceAttributes.SERVICE_NAME]=u),new i.Resource(o)}_parseResourceAttributes(e){if(!e)return{};let t={};for(let r of e.split(this._COMMA_SEPARATOR,-1)){let e=r.split(this._LABEL_KEY_VALUE_SPLITTER,-1);if(2!==e.length)continue;let[n,i]=e;if(n=n.trim(),i=i.trim().split(/^"|"$/).join(""),!this._isValidAndNotEmpty(n))throw Error(`Attribute key ${this._ERROR_MESSAGE_INVALID_CHARS}`);if(!this._isValid(i))throw Error(`Attribute value ${this._ERROR_MESSAGE_INVALID_VALUE}`);t[n]=decodeURIComponent(i)}return t}_isValid(e){return e.length<=this._MAX_LENGTH&&this._isBaggageOctetString(e)}_isBaggageOctetString(e){for(let t=0;t<e.length;t++){let r=e.charCodeAt(t);if(r<33||44===r||59===r||92===r||r>126)return!1}return!0}_isValidAndNotEmpty(e){return e.length>0&&this._isValid(e)}}}),dd=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.envDetector=void 0;var t=na();e.envDetector=new class{detect(e){return Promise.resolve(t.envDetectorSync.detect(e))}}}),_d=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.browserDetectorSync=void 0;var t=Ae(),r=rt(),n=(h(),m(d));e.browserDetectorSync=new class{detect(e){if(!("u">typeof navigator))return r.Resource.empty();let n={[t.SemanticResourceAttributes.PROCESS_RUNTIME_NAME]:"browser",[t.SemanticResourceAttributes.PROCESS_RUNTIME_DESCRIPTION]:"Web Browser",[t.SemanticResourceAttributes.PROCESS_RUNTIME_VERSION]:navigator.userAgent};return this._getResourceAttributes(n,e)}_getResourceAttributes(e,i){return""===e[t.SemanticResourceAttributes.PROCESS_RUNTIME_VERSION]?(n.diag.debug("BrowserDetector failed: Unable to find required browser resources. "),r.Resource.empty()):new r.Resource(Object.assign({},e))}}}),pd=l(e=>{var t=e&&e.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),r=e&&e.__exportStar||function(e,r){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(r,n)||t(r,e,n)};Object.defineProperty(e,"__esModule",{value:!0}),r(ld(),e),r(dd(),e),r(_d(),e),r(na(),e)}),hd=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.isPromiseLike=void 0,e.isPromiseLike=e=>null!==e&&"object"==typeof e&&"function"==typeof e.then}),Ed=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.detectResourcesSync=e.detectResources=void 0;var t=Ze(),r=(h(),m(d)),n=hd();e.detectResources=async(e={})=>{let n=await Promise.all((e.detectors||[]).map(async n=>{try{let t=await n.detect(e);return r.diag.debug(`${n.constructor.name} found resource.`,t),t}catch(e){return r.diag.debug(`${n.constructor.name} failed: ${e.message}`),t.Resource.empty()}}));return i(n),n.reduce((e,t)=>e.merge(t),t.Resource.empty())},e.detectResourcesSync=(e={})=>{var o;let s=(null!=(o=e.detectors)?o:[]).map(i=>{try{let o=i.detect(e),s;if((0,n.isPromiseLike)(o)){let e=async()=>(await o).attributes;s=new t.Resource({},e())}else s=o;return s.waitForAsyncAttributes?s.waitForAsyncAttributes().then(()=>r.diag.debug(`${i.constructor.name} found resource.`,s)):r.diag.debug(`${i.constructor.name} found resource.`,s),s}catch(e){return r.diag.error(`${i.constructor.name} failed: ${e.message}`),t.Resource.empty()}}),a=s.reduce((e,t)=>e.merge(t),t.Resource.empty());return a.waitForAsyncAttributes&&a.waitForAsyncAttributes().then(()=>{i(s)}),a};var i=e=>{e.forEach(e=>{if(Object.keys(e.attributes).length>0){let t=JSON.stringify(e.attributes,null,4);r.diag.verbose(t)}})}}),rt=l(e=>{var t=e&&e.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),r=e&&e.__exportStar||function(e,r){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(r,n)||t(r,e,n)};Object.defineProperty(e,"__esModule",{value:!0}),r(Ze(),e),r(sd(),e),r(Jo(),e),r(ad(),e),r(cd(),e),r(pd(),e),r(Ed(),e)}),md=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.MultiSpanProcessor=void 0;var t=A();e.MultiSpanProcessor=class{constructor(e){this._spanProcessors=e}forceFlush(){let e=[];for(let t of this._spanProcessors)e.push(t.forceFlush());return new Promise(r=>{Promise.all(e).then(()=>{r()}).catch(e=>{(0,t.globalErrorHandler)(e||Error("MultiSpanProcessor: forceFlush failed")),r()})})}onStart(e,t){for(let r of this._spanProcessors)r.onStart(e,t)}onEnd(e){for(let t of this._spanProcessors)t.onEnd(e)}shutdown(){let e=[];for(let t of this._spanProcessors)e.push(t.shutdown());return new Promise((t,r)=>{Promise.all(e).then(()=>{t()},r)})}}}),ca=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.NoopSpanProcessor=void 0,e.NoopSpanProcessor=class{onStart(e,t){}onEnd(e){}shutdown(){return Promise.resolve()}forceFlush(){return Promise.resolve()}}}),Td=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.BasicTracerProvider=e.ForceFlushState=void 0;var t,r=(h(),m(d)),n=A(),i=rt(),o=la(),s=Do(),a=md(),u=ca(),c=Gn(),l=Bo();!function(e){e[e.resolved=0]="resolved",e[e.timeout=1]="timeout",e[e.error=2]="error",e[e.unresolved=3]="unresolved"}(t=e.ForceFlushState||(e.ForceFlushState={}));var p=class{constructor(e={}){var t;this._registeredSpanProcessors=[],this._tracers=new Map;let r=(0,n.merge)({},(0,s.loadDefaultConfig)(),(0,l.reconfigureLimits)(e));this.resource=null!=(t=r.resource)?t:i.Resource.empty(),this.resource=i.Resource.default().merge(this.resource),this._config=Object.assign({},r,{resource:this.resource});let o=this._buildExporterFromEnv();if(void 0!==o){let e=new c.BatchSpanProcessor(o);this.activeSpanProcessor=e}else this.activeSpanProcessor=new u.NoopSpanProcessor}getTracer(e,t,r){let n=`${e}@${t||""}:${r?.schemaUrl||""}`;return this._tracers.has(n)||this._tracers.set(n,new o.Tracer({name:e,version:t,schemaUrl:r?.schemaUrl},this._config,this)),this._tracers.get(n)}addSpanProcessor(e){0===this._registeredSpanProcessors.length&&this.activeSpanProcessor.shutdown().catch(e=>r.diag.error("Error while trying to shutdown current span processor",e)),this._registeredSpanProcessors.push(e),this.activeSpanProcessor=new a.MultiSpanProcessor(this._registeredSpanProcessors)}getActiveSpanProcessor(){return this.activeSpanProcessor}register(e={}){r.trace.setGlobalTracerProvider(this),void 0===e.propagator&&(e.propagator=this._buildPropagatorFromEnv()),e.contextManager&&r.context.setGlobalContextManager(e.contextManager),e.propagator&&r.propagation.setGlobalPropagator(e.propagator)}forceFlush(){let e=this._config.forceFlushTimeoutMillis,r=this._registeredSpanProcessors.map(r=>new Promise(n=>{let i,o=setTimeout(()=>{n(Error(`Span processor did not completed within timeout period of ${e} ms`)),i=t.timeout},e);r.forceFlush().then(()=>{clearTimeout(o),i!==t.timeout&&n(i=t.resolved)}).catch(e=>{clearTimeout(o),i=t.error,n(e)})}));return new Promise((e,n)=>{Promise.all(r).then(r=>{let i=r.filter(e=>e!==t.resolved);i.length>0?n(i):e()}).catch(e=>n([e]))})}shutdown(){return this.activeSpanProcessor.shutdown()}_getPropagator(e){var t;return null==(t=this.constructor._registeredPropagators.get(e))?void 0:t()}_getSpanExporter(e){var t;return null==(t=this.constructor._registeredExporters.get(e))?void 0:t()}_buildPropagatorFromEnv(){let e=Array.from(new Set((0,n.getEnv)().OTEL_PROPAGATORS)),t=e.map(e=>{let t=this._getPropagator(e);return t||r.diag.warn(`Propagator "${e}" requested through environment variable is unavailable.`),t}).reduce((e,t)=>(t&&e.push(t),e),[]);if(0!==t.length)return 1===e.length?t[0]:new n.CompositePropagator({propagators:t})}_buildExporterFromEnv(){let e=(0,n.getEnv)().OTEL_TRACES_EXPORTER;if("none"===e||""===e)return;let t=this._getSpanExporter(e);return t||r.diag.error(`Exporter "${e}" requested through environment variable is unavailable.`),t}};e.BasicTracerProvider=p,p._registeredPropagators=new Map([["tracecontext",()=>new n.W3CTraceContextPropagator],["baggage",()=>new n.W3CBaggagePropagator]]),p._registeredExporters=new Map}),Sd=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.ConsoleSpanExporter=void 0;var t=A();e.ConsoleSpanExporter=class{export(e,t){return this._sendSpans(e,t)}shutdown(){return this._sendSpans([]),this.forceFlush()}forceFlush(){return Promise.resolve()}_exportInfo(e){var r;return{traceId:e.spanContext().traceId,parentId:e.parentSpanId,traceState:null==(r=e.spanContext().traceState)?void 0:r.serialize(),name:e.name,id:e.spanContext().spanId,kind:e.kind,timestamp:(0,t.hrTimeToMicroseconds)(e.startTime),duration:(0,t.hrTimeToMicroseconds)(e.duration),attributes:e.attributes,status:e.status,events:e.events,links:e.links}}_sendSpans(e,r){for(let t of e)console.dir(this._exportInfo(t),{depth:3});if(r)return r({code:t.ExportResultCode.SUCCESS})}}}),Od=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.InMemorySpanExporter=void 0;var t=A();e.InMemorySpanExporter=class{constructor(){this._finishedSpans=[],this._stopped=!1}export(e,r){if(this._stopped)return r({code:t.ExportResultCode.FAILED,error:Error("Exporter has been stopped")});this._finishedSpans.push(...e),setTimeout(()=>r({code:t.ExportResultCode.SUCCESS}),0)}shutdown(){return this._stopped=!0,this._finishedSpans=[],this.forceFlush()}forceFlush(){return Promise.resolve()}reset(){this._finishedSpans=[]}getFinishedSpans(){return this._finishedSpans}}}),bd=l(e=>{Object.defineProperty(e,"__esModule",{value:!0})}),Pd=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.SimpleSpanProcessor=void 0;var t=(h(),m(d)),r=A();e.SimpleSpanProcessor=class{constructor(e){this._exporter=e,this._shutdownOnce=new r.BindOnceFuture(this._shutdown,this),this._unresolvedExports=new Set}async forceFlush(){await Promise.all(Array.from(this._unresolvedExports)),this._exporter.forceFlush&&await this._exporter.forceFlush()}onStart(e,t){}onEnd(e){var n,i;if(this._shutdownOnce.isCalled||!(e.spanContext().traceFlags&t.TraceFlags.SAMPLED))return;let o=()=>r.internal._export(this._exporter,[e]).then(e=>{var t;e.code!==r.ExportResultCode.SUCCESS&&(0,r.globalErrorHandler)(null!=(t=e.error)?t:Error(`SimpleSpanProcessor: span export failed (status ${e})`))}).catch(e=>{(0,r.globalErrorHandler)(e)});if(e.resource.asyncAttributesPending){let t=null==(i=(n=e.resource).waitForAsyncAttributes)?void 0:i.call(n).then(()=>(null!=t&&this._unresolvedExports.delete(t),o()),e=>(0,r.globalErrorHandler)(e));null!=t&&this._unresolvedExports.add(t)}else o()}shutdown(){return this._shutdownOnce.call()}_shutdown(){return this._exporter.shutdown()}}}),vd=l(e=>{Object.defineProperty(e,"__esModule",{value:!0})}),Ld=l(e=>{Object.defineProperty(e,"__esModule",{value:!0})}),Nd=l(e=>{Object.defineProperty(e,"__esModule",{value:!0})}),wd=l(e=>{Object.defineProperty(e,"__esModule",{value:!0})}),Dd=l(e=>{Object.defineProperty(e,"__esModule",{value:!0})}),la=l(e=>{var t=e&&e.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),r=e&&e.__exportStar||function(e,r){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(r,n)||t(r,e,n)};Object.defineProperty(e,"__esModule",{value:!0}),r(Vl(),e),r(Td(),e),r(Gn(),e),r(Sd(),e),r(Od(),e),r(bd(),e),r(Pd(),e),r(vd(),e),r(ca(),e),r(In(),e),r(Mn(),e),r(Mo(),e),r(Co(),e),r(gr(),e),r(Po(),e),r(Ld(),e),r(Nd(),e),r(wd(),e),r(Dd(),e)}),st={},fa=dc(()=>{ht(st,_opentelemetry_api_logs__WEBPACK_IMPORTED_MODULE_4__)}),Bd=l(e=>{function t(e=[]){let r=[];for(let n=0,i=e.length;n<i;n++){let i=e[n];if(Array.isArray(i)){let e=t(i);r=r.concat(e.instrumentations)}else"function"==typeof i?r.push(new i):i.instrumentationName&&r.push(i)}return{instrumentations:r}}Object.defineProperty(e,"__esModule",{value:!0}),e.disableInstrumentations=e.enableInstrumentations=e.parseInstrumentationOptions=void 0,e.parseInstrumentationOptions=t,e.enableInstrumentations=function(e,t,r){for(let n=0,i=e.length;n<i;n++){let i=e[n];t&&i.setTracerProvider(t),r&&i.setMeterProvider(r),i.getConfig().enabled||i.enable()}},e.disableInstrumentations=function(e){e.forEach(e=>e.disable())}}),Gd=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.registerInstrumentations=void 0;var t=(h(),m(d)),r=Bd();e.registerInstrumentations=function(e){let{instrumentations:n}=(0,r.parseInstrumentationOptions)(e.instrumentations),i=e.tracerProvider||t.trace.getTracerProvider(),o=e.meterProvider||t.metrics.getMeterProvider();return(0,r.enableInstrumentations)(n,i,o),()=>{(0,r.disableInstrumentations)(n)}}}),ga=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.LogRecord=void 0;var t=(h(),m(d)),r=(h(),m(d)),n=A();e.LogRecord=class{constructor(e,t,i){this.attributes={},this.totalAttributesCount=0,this._isReadonly=!1;let{timestamp:o,observedTimestamp:s,severityNumber:a,severityText:u,body:c,attributes:l={},context:p}=i,_=Date.now();if(this.hrTime=(0,n.timeInputToHrTime)(o??_),this.hrTimeObserved=(0,n.timeInputToHrTime)(s??_),p){let e=r.trace.getSpanContext(p);e&&r.isSpanContextValid(e)&&(this.spanContext=e)}this.severityNumber=a,this.severityText=u,this.body=c,this.resource=e.resource,this.instrumentationScope=t,this._logRecordLimits=e.logRecordLimits,this.setAttributes(l)}set severityText(e){this._isLogRecordReadonly()||(this._severityText=e)}get severityText(){return this._severityText}set severityNumber(e){this._isLogRecordReadonly()||(this._severityNumber=e)}get severityNumber(){return this._severityNumber}set body(e){this._isLogRecordReadonly()||(this._body=e)}get body(){return this._body}get droppedAttributesCount(){return this.totalAttributesCount-Object.keys(this.attributes).length}setAttribute(e,t){return this._isLogRecordReadonly()||null===t||(0===e.length?r.diag.warn(`Invalid attribute key: ${e}`):(0,n.isAttributeValue)(t)||"object"==typeof t&&!Array.isArray(t)&&Object.keys(t).length>0?(this.totalAttributesCount+=1,Object.keys(this.attributes).length>=this._logRecordLimits.attributeCountLimit&&!Object.prototype.hasOwnProperty.call(this.attributes,e)||((0,n.isAttributeValue)(t)?this.attributes[e]=this._truncateToSize(t):this.attributes[e]=t)):r.diag.warn(`Invalid attribute value set for key: ${e}`)),this}setAttributes(e){for(let[t,r]of Object.entries(e))this.setAttribute(t,r);return this}setBody(e){return this.body=e,this}setSeverityNumber(e){return this.severityNumber=e,this}setSeverityText(e){return this.severityText=e,this}_makeReadonly(){this._isReadonly=!0}_truncateToSize(e){let t=this._logRecordLimits.attributeValueLengthLimit;return t<=0?(r.diag.warn(`Attribute value limit must be positive, got ${t}`),e):"string"==typeof e?this._truncateToLimitUtil(e,t):Array.isArray(e)?e.map(e=>"string"==typeof e?this._truncateToLimitUtil(e,t):e):e}_truncateToLimitUtil(e,t){return e.length<=t?e:e.substring(0,t)}_isLogRecordReadonly(){return this._isReadonly&&t.diag.warn("Can not execute the operation on emitted log record"),this._isReadonly}}}),Vd=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.Logger=void 0;var t=(h(),m(d)),r=ga();e.Logger=class{constructor(e,t){this.instrumentationScope=e,this._sharedState=t}emit(e){let n=e.context||t.context.active(),i=new r.LogRecord(this._sharedState,this.instrumentationScope,Object.assign({context:n},e));this._sharedState.activeProcessor.onEmit(i,n),i._makeReadonly()}}}),Hd=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.reconfigureLimits=e.loadDefaultConfig=void 0;var t=A();e.loadDefaultConfig=function(){return{forceFlushTimeoutMillis:3e4,logRecordLimits:{attributeValueLengthLimit:(0,t.getEnv)().OTEL_LOGRECORD_ATTRIBUTE_VALUE_LENGTH_LIMIT,attributeCountLimit:(0,t.getEnv)().OTEL_LOGRECORD_ATTRIBUTE_COUNT_LIMIT},includeTraceContext:!0}},e.reconfigureLimits=function(e){var r,n,i,o,s,a;let u=(0,t.getEnvWithoutDefaults)();return{attributeCountLimit:null!=(i=null!=(n=null!=(r=e.attributeCountLimit)?r:u.OTEL_LOGRECORD_ATTRIBUTE_COUNT_LIMIT)?n:u.OTEL_ATTRIBUTE_COUNT_LIMIT)?i:t.DEFAULT_ATTRIBUTE_COUNT_LIMIT,attributeValueLengthLimit:null!=(a=null!=(s=null!=(o=e.attributeValueLengthLimit)?o:u.OTEL_LOGRECORD_ATTRIBUTE_VALUE_LENGTH_LIMIT)?s:u.OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT)?a:t.DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT}}}),jd=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.MultiLogRecordProcessor=void 0;var t=A();e.MultiLogRecordProcessor=class{constructor(e,t){this.processors=e,this.forceFlushTimeoutMillis=t}async forceFlush(){let e=this.forceFlushTimeoutMillis;await Promise.all(this.processors.map(r=>(0,t.callWithTimeout)(r.forceFlush(),e)))}onEmit(e,t){this.processors.forEach(r=>r.onEmit(e,t))}async shutdown(){await Promise.all(this.processors.map(e=>e.shutdown()))}}}),Oa=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.NoopLogRecordProcessor=void 0,e.NoopLogRecordProcessor=class{forceFlush(){return Promise.resolve()}onEmit(e,t){}shutdown(){return Promise.resolve()}}}),Fd=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.LoggerProviderSharedState=void 0;var t=Oa();e.LoggerProviderSharedState=class{constructor(e,r,n){this.resource=e,this.forceFlushTimeoutMillis=r,this.logRecordLimits=n,this.loggers=new Map,this.registeredLogRecordProcessors=[],this.activeProcessor=new t.NoopLogRecordProcessor}}}),Xd=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.LoggerProvider=e.DEFAULT_LOGGER_NAME=void 0;var t=(h(),m(d)),r=(fa(),m(st)),n=rt(),i=A(),o=Vd(),s=Hd(),a=jd(),u=Fd();e.DEFAULT_LOGGER_NAME="unknown";var c=class{constructor(e={}){let{resource:t=n.Resource.default(),logRecordLimits:r,forceFlushTimeoutMillis:o}=(0,i.merge)({},(0,s.loadDefaultConfig)(),e);this._sharedState=new u.LoggerProviderSharedState(t,o,(0,s.reconfigureLimits)(r)),this._shutdownOnce=new i.BindOnceFuture(this._shutdown,this)}getLogger(n,i,s){if(this._shutdownOnce.isCalled)return t.diag.warn("A shutdown LoggerProvider cannot provide a Logger"),r.NOOP_LOGGER;n||t.diag.warn("Logger requested without instrumentation scope name.");let a=n||e.DEFAULT_LOGGER_NAME,u=`${a}@${i||""}:${s?.schemaUrl||""}`;return this._sharedState.loggers.has(u)||this._sharedState.loggers.set(u,new o.Logger({name:a,version:i,schemaUrl:s?.schemaUrl},this._sharedState)),this._sharedState.loggers.get(u)}addLogRecordProcessor(e){0===this._sharedState.registeredLogRecordProcessors.length&&this._sharedState.activeProcessor.shutdown().catch(e=>t.diag.error("Error while trying to shutdown current log record processor",e)),this._sharedState.registeredLogRecordProcessors.push(e),this._sharedState.activeProcessor=new a.MultiLogRecordProcessor(this._sharedState.registeredLogRecordProcessors,this._sharedState.forceFlushTimeoutMillis)}forceFlush(){return this._shutdownOnce.isCalled?(t.diag.warn("invalid attempt to force flush after LoggerProvider shutdown"),this._shutdownOnce.promise):this._sharedState.activeProcessor.forceFlush()}shutdown(){return this._shutdownOnce.isCalled?(t.diag.warn("shutdown may only be called once per LoggerProvider"),this._shutdownOnce.promise):this._shutdownOnce.call()}_shutdown(){return this._sharedState.activeProcessor.shutdown()}};e.LoggerProvider=c}),Wd=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.ConsoleLogRecordExporter=void 0;var t=A(),r=A();e.ConsoleLogRecordExporter=class{export(e,t){this._sendLogRecords(e,t)}shutdown(){return Promise.resolve()}_exportInfo(e){var r,n,i;return{timestamp:(0,t.hrTimeToMicroseconds)(e.hrTime),traceId:null==(r=e.spanContext)?void 0:r.traceId,spanId:null==(n=e.spanContext)?void 0:n.spanId,traceFlags:null==(i=e.spanContext)?void 0:i.traceFlags,severityText:e.severityText,severityNumber:e.severityNumber,body:e.body,attributes:e.attributes}}_sendLogRecords(e,t){for(let t of e)console.dir(this._exportInfo(t),{depth:3});t?.({code:r.ExportResultCode.SUCCESS})}}}),Kd=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.SimpleLogRecordProcessor=void 0;var t=A();e.SimpleLogRecordProcessor=class{constructor(e){this._exporter=e,this._shutdownOnce=new t.BindOnceFuture(this._shutdown,this)}onEmit(e){this._shutdownOnce.isCalled||this._exporter.export([e],e=>{var r;if(e.code!==t.ExportResultCode.SUCCESS)return void(0,t.globalErrorHandler)(null!=(r=e.error)?r:Error(`SimpleLogRecordProcessor: log record export failed (status ${e})`))})}forceFlush(){return Promise.resolve()}shutdown(){return this._shutdownOnce.call()}_shutdown(){return this._exporter.shutdown()}}}),Yd=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.InMemoryLogRecordExporter=void 0;var t=A();e.InMemoryLogRecordExporter=class{constructor(){this._finishedLogRecords=[],this._stopped=!1}export(e,r){if(this._stopped)return r({code:t.ExportResultCode.FAILED,error:Error("Exporter has been stopped")});this._finishedLogRecords.push(...e),r({code:t.ExportResultCode.SUCCESS})}shutdown(){return this._stopped=!0,this.reset(),Promise.resolve()}getFinishedLogRecords(){return this._finishedLogRecords}reset(){this._finishedLogRecords=[]}}}),Qd=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.BatchLogRecordProcessorBase=void 0;var t=(h(),m(d)),r=A();e.BatchLogRecordProcessorBase=class{constructor(e,n){var i,o,s,a;this._exporter=e,this._finishedLogRecords=[];let u=(0,r.getEnv)();this._maxExportBatchSize=null!=(i=n?.maxExportBatchSize)?i:u.OTEL_BLRP_MAX_EXPORT_BATCH_SIZE,this._maxQueueSize=null!=(o=n?.maxQueueSize)?o:u.OTEL_BLRP_MAX_QUEUE_SIZE,this._scheduledDelayMillis=null!=(s=n?.scheduledDelayMillis)?s:u.OTEL_BLRP_SCHEDULE_DELAY,this._exportTimeoutMillis=null!=(a=n?.exportTimeoutMillis)?a:u.OTEL_BLRP_EXPORT_TIMEOUT,this._shutdownOnce=new r.BindOnceFuture(this._shutdown,this),this._maxExportBatchSize>this._maxQueueSize&&(t.diag.warn("BatchLogRecordProcessor: maxExportBatchSize must be smaller or equal to maxQueueSize, setting maxExportBatchSize to match maxQueueSize"),this._maxExportBatchSize=this._maxQueueSize)}onEmit(e){this._shutdownOnce.isCalled||this._addToBuffer(e)}forceFlush(){return this._shutdownOnce.isCalled?this._shutdownOnce.promise:this._flushAll()}shutdown(){return this._shutdownOnce.call()}async _shutdown(){this.onShutdown(),await this._flushAll(),await this._exporter.shutdown()}_addToBuffer(e){this._finishedLogRecords.length>=this._maxQueueSize||(this._finishedLogRecords.push(e),this._maybeStartTimer())}_flushAll(){return new Promise((e,t)=>{let r=[],n=Math.ceil(this._finishedLogRecords.length/this._maxExportBatchSize);for(let e=0;e<n;e++)r.push(this._flushOneBatch());Promise.all(r).then(()=>{e()}).catch(t)})}_flushOneBatch(){return this._clearTimer(),0===this._finishedLogRecords.length?Promise.resolve():new Promise((e,t)=>{(0,r.callWithTimeout)(this._export(this._finishedLogRecords.splice(0,this._maxExportBatchSize)),this._exportTimeoutMillis).then(()=>e()).catch(t)})}_maybeStartTimer(){void 0===this._timer&&(this._timer=setTimeout(()=>{this._flushOneBatch().then(()=>{this._finishedLogRecords.length>0&&(this._clearTimer(),this._maybeStartTimer())}).catch(e=>{(0,r.globalErrorHandler)(e)})},this._scheduledDelayMillis),(0,r.unrefTimer)(this._timer))}_clearTimer(){void 0!==this._timer&&(clearTimeout(this._timer),this._timer=void 0)}_export(e){return new Promise((t,n)=>{this._exporter.export(e,e=>{var i;if(e.code!==r.ExportResultCode.SUCCESS)return void n(null!=(i=e.error)?i:Error(`BatchLogRecordProcessorBase: log record export failed (status ${e})`));t(e)})})}}}),Zd=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.BatchLogRecordProcessor=void 0;var t=Qd();e.BatchLogRecordProcessor=class extends t.BatchLogRecordProcessorBase{onShutdown(){}}}),Jd=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.BatchLogRecordProcessor=void 0;var t=Zd();Object.defineProperty(e,"BatchLogRecordProcessor",{enumerable:!0,get:function(){return t.BatchLogRecordProcessor}})}),e_=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.BatchLogRecordProcessor=void 0;var t=Jd();Object.defineProperty(e,"BatchLogRecordProcessor",{enumerable:!0,get:function(){return t.BatchLogRecordProcessor}})}),t_=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.BatchLogRecordProcessor=e.InMemoryLogRecordExporter=e.SimpleLogRecordProcessor=e.ConsoleLogRecordExporter=e.NoopLogRecordProcessor=e.LogRecord=e.LoggerProvider=void 0;var t=Xd();Object.defineProperty(e,"LoggerProvider",{enumerable:!0,get:function(){return t.LoggerProvider}});var r=ga();Object.defineProperty(e,"LogRecord",{enumerable:!0,get:function(){return r.LogRecord}});var n=Oa();Object.defineProperty(e,"NoopLogRecordProcessor",{enumerable:!0,get:function(){return n.NoopLogRecordProcessor}});var i=Wd();Object.defineProperty(e,"ConsoleLogRecordExporter",{enumerable:!0,get:function(){return i.ConsoleLogRecordExporter}});var o=Kd();Object.defineProperty(e,"SimpleLogRecordProcessor",{enumerable:!0,get:function(){return o.SimpleLogRecordProcessor}});var s=Yd();Object.defineProperty(e,"InMemoryLogRecordExporter",{enumerable:!0,get:function(){return s.InMemoryLogRecordExporter}});var a=e_();Object.defineProperty(e,"BatchLogRecordProcessor",{enumerable:!0,get:function(){return a.BatchLogRecordProcessor}})}),Li=l(e=>{var t;Object.defineProperty(e,"__esModule",{value:!0}),e.AggregationTemporality=void 0,function(e){e[e.DELTA=0]="DELTA",e[e.CUMULATIVE=1]="CUMULATIVE"}(t=e.AggregationTemporality||(e.AggregationTemporality={}))}),wt=l(e=>{var t;Object.defineProperty(e,"__esModule",{value:!0}),e.DataPointType=void 0,function(e){e[e.HISTOGRAM=0]="HISTOGRAM",e[e.EXPONENTIAL_HISTOGRAM=1]="EXPONENTIAL_HISTOGRAM",e[e.GAUGE=2]="GAUGE",e[e.SUM=3]="SUM"}(t=e.DataPointType||(e.DataPointType={}))}),fe=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.equalsCaseInsensitive=e.binarySearchLB=e.setEquals=e.FlatMap=e.isPromiseAllSettledRejectionResult=e.PromiseAllSettled=e.callWithTimeout=e.TimeoutError=e.instrumentationScopeId=e.hashAttributes=e.isNotNullish=void 0,e.isNotNullish=function(e){return null!=e},e.hashAttributes=function(e){let t=Object.keys(e);return 0===t.length?"":JSON.stringify((t=t.sort()).map(t=>[t,e[t]]))},e.instrumentationScopeId=function(e){var t,r;return`${e.name}:${null!=(t=e.version)?t:""}:${null!=(r=e.schemaUrl)?r:""}`};var t=class e extends Error{constructor(t){super(t),Object.setPrototypeOf(this,e.prototype)}};e.TimeoutError=t,e.callWithTimeout=function(e,r){let n;return Promise.race([e,new Promise(function(e,i){n=setTimeout(function(){i(new t("Operation timed out."))},r)})]).then(e=>(clearTimeout(n),e),e=>{throw clearTimeout(n),e})},e.PromiseAllSettled=async function(e){return Promise.all(e.map(async e=>{try{return{status:"fulfilled",value:await e}}catch(e){return{status:"rejected",reason:e}}}))},e.isPromiseAllSettledRejectionResult=function(e){return"rejected"===e.status},e.FlatMap=function(e,t){let r=[];return e.forEach(e=>{r.push(...t(e))}),r},e.setEquals=function(e,t){if(e.size!==t.size)return!1;for(let r of e)if(!t.has(r))return!1;return!0},e.binarySearchLB=function(e,t){let r=0,n=e.length-1;for(;n-r>1;){let i=Math.trunc((n+r)/2);e[i]<=t?r=i:n=i-1}return e[n]<=t?n:e[r]<=t?r:-1},e.equalsCaseInsensitive=function(e,t){return e.toLowerCase()===t.toLowerCase()}}),xt=l(e=>{var t;Object.defineProperty(e,"__esModule",{value:!0}),e.AggregatorKind=void 0,function(e){e[e.DROP=0]="DROP",e[e.SUM=1]="SUM",e[e.LAST_VALUE=2]="LAST_VALUE",e[e.HISTOGRAM=3]="HISTOGRAM",e[e.EXPONENTIAL_HISTOGRAM=4]="EXPONENTIAL_HISTOGRAM"}(t=e.AggregatorKind||(e.AggregatorKind={}))}),r_=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.DropAggregator=void 0;var t=xt();e.DropAggregator=class{constructor(){this.kind=t.AggregatorKind.DROP}createAccumulation(){}merge(e,t){}diff(e,t){}toMetricData(e,t,r,n){}}}),be=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.isValidName=e.isDescriptorCompatibleWith=e.createInstrumentDescriptorWithView=e.createInstrumentDescriptor=e.InstrumentType=void 0;var t,r=(h(),m(d)),n=fe();!function(e){e.COUNTER="COUNTER",e.HISTOGRAM="HISTOGRAM",e.UP_DOWN_COUNTER="UP_DOWN_COUNTER",e.OBSERVABLE_COUNTER="OBSERVABLE_COUNTER",e.OBSERVABLE_GAUGE="OBSERVABLE_GAUGE",e.OBSERVABLE_UP_DOWN_COUNTER="OBSERVABLE_UP_DOWN_COUNTER"}(t=e.InstrumentType||(e.InstrumentType={})),e.createInstrumentDescriptor=function(e,t,n){var i,s,a,u;return o(e)||r.diag.warn(`Invalid metric name: "${e}". The metric name should be a ASCII string with a length no greater than 255 characters.`),{name:e,type:t,description:null!=(i=n?.description)?i:"",unit:null!=(s=n?.unit)?s:"",valueType:null!=(a=n?.valueType)?a:r.ValueType.DOUBLE,advice:null!=(u=n?.advice)?u:{}}},e.createInstrumentDescriptorWithView=function(e,t){var r,n;return{name:null!=(r=e.name)?r:t.name,description:null!=(n=e.description)?n:t.description,type:t.type,unit:t.unit,valueType:t.valueType,advice:t.advice}},e.isDescriptorCompatibleWith=function(e,t){return(0,n.equalsCaseInsensitive)(e.name,t.name)&&e.unit===t.unit&&e.type===t.type&&e.valueType===t.valueType};var i=/^[a-z][a-z0-9_.\-/]{0,254}$/i;function o(e){return null!=e.match(i)}e.isValidName=o}),s_=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.HistogramAggregator=e.HistogramAccumulation=void 0;var t=xt(),r=wt(),n=be(),i=fe();function o(e){let t=e.map(()=>0);return t.push(0),{buckets:{boundaries:e,counts:t},sum:0,count:0,hasMinMax:!1,min:1/0,max:-1/0}}var s=class{constructor(e,t,r=!0,n=o(t)){this.startTime=e,this._boundaries=t,this._recordMinMax=r,this._current=n}record(e){this._current.count+=1,this._current.sum+=e,this._recordMinMax&&(this._current.min=Math.min(e,this._current.min),this._current.max=Math.max(e,this._current.max),this._current.hasMinMax=!0);let t=(0,i.binarySearchLB)(this._boundaries,e);this._current.buckets.counts[t+1]+=1}setStartTime(e){this.startTime=e}toPointValue(){return this._current}};e.HistogramAccumulation=s,e.HistogramAggregator=class{constructor(e,r){this._boundaries=e,this._recordMinMax=r,this.kind=t.AggregatorKind.HISTOGRAM}createAccumulation(e){return new s(e,this._boundaries,this._recordMinMax)}merge(e,t){let r=e.toPointValue(),n=t.toPointValue(),i=r.buckets.counts,o=n.buckets.counts,a=Array(i.length);for(let e=0;e<i.length;e++)a[e]=i[e]+o[e];let u=1/0,c=-1/0;return this._recordMinMax&&(r.hasMinMax&&n.hasMinMax?(u=Math.min(r.min,n.min),c=Math.max(r.max,n.max)):r.hasMinMax?(u=r.min,c=r.max):n.hasMinMax&&(u=n.min,c=n.max)),new s(e.startTime,r.buckets.boundaries,this._recordMinMax,{buckets:{boundaries:r.buckets.boundaries,counts:a},count:r.count+n.count,sum:r.sum+n.sum,hasMinMax:this._recordMinMax&&(r.hasMinMax||n.hasMinMax),min:u,max:c})}diff(e,t){let r=e.toPointValue(),n=t.toPointValue(),i=r.buckets.counts,o=n.buckets.counts,a=Array(i.length);for(let e=0;e<i.length;e++)a[e]=o[e]-i[e];return new s(t.startTime,r.buckets.boundaries,this._recordMinMax,{buckets:{boundaries:r.buckets.boundaries,counts:a},count:n.count-r.count,sum:n.sum-r.sum,hasMinMax:!1,min:1/0,max:-1/0})}toMetricData(e,t,i,o){return{descriptor:e,aggregationTemporality:t,dataPointType:r.DataPointType.HISTOGRAM,dataPoints:i.map(([t,r])=>{let i=r.toPointValue(),s=e.type===n.InstrumentType.UP_DOWN_COUNTER||e.type===n.InstrumentType.OBSERVABLE_GAUGE||e.type===n.InstrumentType.OBSERVABLE_UP_DOWN_COUNTER;return{attributes:t,startTime:r.startTime,endTime:o,value:{min:i.hasMinMax?i.min:void 0,max:i.hasMinMax?i.max:void 0,sum:s?void 0:i.sum,buckets:i.buckets,count:i.count}}})}}}}),o_=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.Buckets=void 0,e.Buckets=class e{constructor(e=new t,r=0,n=0,i=0){this.backing=e,this.indexBase=r,this.indexStart=n,this.indexEnd=i}get offset(){return this.indexStart}get length(){return 0===this.backing.length||this.indexEnd===this.indexStart&&0===this.at(0)?0:this.indexEnd-this.indexStart+1}counts(){return Array.from({length:this.length},(e,t)=>this.at(t))}at(e){let t=this.indexBase-this.indexStart;return e<t&&(e+=this.backing.length),e-=t,this.backing.countAt(e)}incrementBucket(e,t){this.backing.increment(e,t)}decrementBucket(e,t){this.backing.decrement(e,t)}trim(){for(let e=0;e<this.length;e++)if(0!==this.at(e)){this.indexStart+=e;break}else if(e===this.length-1){this.indexStart=this.indexEnd=this.indexBase=0;return}for(let e=this.length-1;e>=0;e--)if(0!==this.at(e)){this.indexEnd-=this.length-e-1;break}this._rotate()}downscale(e){this._rotate();let t=1+this.indexEnd-this.indexStart,r=1<<e,n=0,i=0;for(let e=this.indexStart;e<=this.indexEnd;){let o=e%r;o<0&&(o+=r);for(let s=o;s<r&&n<t;s++)this._relocateBucket(i,n),n++,e++;i++}this.indexStart>>=e,this.indexEnd>>=e,this.indexBase=this.indexStart}clone(){return new e(this.backing.clone(),this.indexBase,this.indexStart,this.indexEnd)}_rotate(){let e=this.indexBase-this.indexStart;0!==e&&(e>0?(this.backing.reverse(0,this.backing.length),this.backing.reverse(0,e),this.backing.reverse(e,this.backing.length)):(this.backing.reverse(0,this.backing.length),this.backing.reverse(0,this.backing.length+e)),this.indexBase=this.indexStart)}_relocateBucket(e,t){e!==t&&this.incrementBucket(e,this.backing.emptyBucket(t))}};var t=class e{constructor(e=[0]){this._counts=e}get length(){return this._counts.length}countAt(e){return this._counts[e]}growTo(e,t,r){let n=Array(e).fill(0);n.splice(r,this._counts.length-t,...this._counts.slice(t)),n.splice(0,t,...this._counts.slice(0,t)),this._counts=n}reverse(e,t){let r=Math.floor((e+t)/2)-e;for(let n=0;n<r;n++){let r=this._counts[e+n];this._counts[e+n]=this._counts[t-n-1],this._counts[t-n-1]=r}}emptyBucket(e){let t=this._counts[e];return this._counts[e]=0,t}increment(e,t){this._counts[e]+=t}decrement(e,t){this._counts[e]>=t?this._counts[e]-=t:this._counts[e]=0}clone(){return new e([...this._counts])}}}),Ba=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.getSignificand=e.getNormalBase2=e.MIN_VALUE=e.MAX_NORMAL_EXPONENT=e.MIN_NORMAL_EXPONENT=e.SIGNIFICAND_WIDTH=void 0,e.SIGNIFICAND_WIDTH=52;var t=0x7ff00000,r=1048575,n=1023;e.MIN_NORMAL_EXPONENT=-1022,e.MAX_NORMAL_EXPONENT=n,e.MIN_VALUE=22250738585072014e-324,e.getNormalBase2=function(e){let r=new DataView(new ArrayBuffer(8));return r.setFloat64(0,e),((r.getUint32(0)&t)>>20)-n},e.getSignificand=function(e){let t=new DataView(new ArrayBuffer(8));return t.setFloat64(0,e),(t.getUint32(0)&r)*0x100000000+t.getUint32(4)}}),wi=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.nextGreaterSquare=e.ldexp=void 0,e.ldexp=function(e,t){return 0===e||e===Number.POSITIVE_INFINITY||e===Number.NEGATIVE_INFINITY||Number.isNaN(e)?e:e*Math.pow(2,t)},e.nextGreaterSquare=function(e){return e--,e|=e>>1,e|=e>>2,e|=e>>4,e|=e>>8,e|=e>>16,++e}}),Di=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.MappingError=void 0,e.MappingError=class extends Error{}}),u_=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.ExponentMapping=void 0;var t=Ba(),r=wi(),n=Di();e.ExponentMapping=class{constructor(e){this._shift=-e}mapToIndex(e){return e<t.MIN_VALUE?this._minNormalLowerBoundaryIndex():t.getNormalBase2(e)+this._rightShift(t.getSignificand(e)-1,t.SIGNIFICAND_WIDTH)>>this._shift}lowerBoundary(e){let t=this._minNormalLowerBoundaryIndex();if(e<t)throw new n.MappingError(`underflow: ${e} is < minimum lower boundary: ${t}`);let i=this._maxNormalLowerBoundaryIndex();if(e>i)throw new n.MappingError(`overflow: ${e} is > maximum lower boundary: ${i}`);return r.ldexp(1,e<<this._shift)}get scale(){return 0===this._shift?0:-this._shift}_minNormalLowerBoundaryIndex(){let e=t.MIN_NORMAL_EXPONENT>>this._shift;return this._shift<2&&e--,e}_maxNormalLowerBoundaryIndex(){return t.MAX_NORMAL_EXPONENT>>this._shift}_rightShift(e,t){return Math.floor(e*Math.pow(2,-t))}}}),d_=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.LogarithmMapping=void 0;var t=Ba(),r=wi(),n=Di();e.LogarithmMapping=class{constructor(e){this._scale=e,this._scaleFactor=r.ldexp(Math.LOG2E,e),this._inverseFactor=r.ldexp(Math.LN2,-e)}mapToIndex(e){if(e<=t.MIN_VALUE)return this._minNormalLowerBoundaryIndex()-1;if(0===t.getSignificand(e))return(t.getNormalBase2(e)<<this._scale)-1;let r=Math.floor(Math.log(e)*this._scaleFactor),n=this._maxNormalLowerBoundaryIndex();return r>=n?n:r}lowerBoundary(e){let r=this._maxNormalLowerBoundaryIndex();if(e>=r){if(e===r)return 2*Math.exp((e-(1<<this._scale))/this._scaleFactor);throw new n.MappingError(`overflow: ${e} is > maximum lower boundary: ${r}`)}let i=this._minNormalLowerBoundaryIndex();if(e<=i){if(e===i)return t.MIN_VALUE;if(e===i-1)return Math.exp((e+(1<<this._scale))/this._scaleFactor)/2;throw new n.MappingError(`overflow: ${e} is < minimum lower boundary: ${i}`)}return Math.exp(e*this._inverseFactor)}get scale(){return this._scale}_minNormalLowerBoundaryIndex(){return t.MIN_NORMAL_EXPONENT<<this._scale}_maxNormalLowerBoundaryIndex(){return(t.MAX_NORMAL_EXPONENT+1<<this._scale)-1}}}),h_=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.getMapping=void 0;var t=u_(),r=d_(),n=Di(),i=-10,o=20,s=Array.from({length:31},(e,n)=>n>10?new r.LogarithmMapping(n-10):new t.ExponentMapping(n-10));e.getMapping=function(e){if(e>o||e<i)throw new n.MappingError(`expected scale >= ${i} && <= ${o}, got: ${e}`);return s[e+10]}}),m_=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.ExponentialHistogramAggregator=e.ExponentialHistogramAccumulation=void 0;var t=xt(),r=wt(),n=(h(),m(d)),i=be(),o=o_(),s=h_(),a=wi(),u=class e{constructor(e,t){this.low=e,this.high=t}static combine(t,r){return new e(Math.min(t.low,r.low),Math.max(t.high,r.high))}},c=20,l=160,p=2,_=class e{constructor(e=e,t=l,r=!0,i=0,a=0,u=0,_=Number.POSITIVE_INFINITY,g=Number.NEGATIVE_INFINITY,f=new o.Buckets,E=new o.Buckets,T=(0,s.getMapping)(c)){this.startTime=e,this._maxSize=t,this._recordMinMax=r,this._sum=i,this._count=a,this._zeroCount=u,this._min=_,this._max=g,this._positive=f,this._negative=E,this._mapping=T,this._maxSize<p&&(n.diag.warn(`Exponential Histogram Max Size set to ${this._maxSize},                 changing to the minimum size of: ${p}`),this._maxSize=p)}record(e){this.updateByIncrement(e,1)}setStartTime(e){this.startTime=e}toPointValue(){return{hasMinMax:this._recordMinMax,min:this.min,max:this.max,sum:this.sum,positive:{offset:this.positive.offset,bucketCounts:this.positive.counts()},negative:{offset:this.negative.offset,bucketCounts:this.negative.counts()},count:this.count,scale:this.scale,zeroCount:this.zeroCount}}get sum(){return this._sum}get min(){return this._min}get max(){return this._max}get count(){return this._count}get zeroCount(){return this._zeroCount}get scale(){return this._count===this._zeroCount?0:this._mapping.scale}get positive(){return this._positive}get negative(){return this._negative}updateByIncrement(e,t){if(e>this._max&&(this._max=e),e<this._min&&(this._min=e),this._count+=t,0===e){this._zeroCount+=t;return}this._sum+=e*t,e>0?this._updateBuckets(this._positive,e,t):this._updateBuckets(this._negative,-e,t)}merge(e){0===this._count?(this._min=e.min,this._max=e.max):0!==e.count&&(e.min<this.min&&(this._min=e.min),e.max>this.max&&(this._max=e.max)),this.startTime=e.startTime,this._sum+=e.sum,this._count+=e.count,this._zeroCount+=e.zeroCount;let t=this._minScale(e);this._downscale(this.scale-t),this._mergeBuckets(this.positive,e,e.positive,t),this._mergeBuckets(this.negative,e,e.negative,t)}diff(e){this._min=1/0,this._max=-1/0,this._sum-=e.sum,this._count-=e.count,this._zeroCount-=e.zeroCount;let t=this._minScale(e);this._downscale(this.scale-t),this._diffBuckets(this.positive,e,e.positive,t),this._diffBuckets(this.negative,e,e.negative,t)}clone(){return new e(this.startTime,this._maxSize,this._recordMinMax,this._sum,this._count,this._zeroCount,this._min,this._max,this.positive.clone(),this.negative.clone(),this._mapping)}_updateBuckets(e,t,r){let n=this._mapping.mapToIndex(t),i=!1,o=0,s=0;if(0===e.length?(e.indexStart=n,e.indexEnd=e.indexStart,e.indexBase=e.indexStart):n<e.indexStart&&e.indexEnd-n>=this._maxSize?(i=!0,s=n,o=e.indexEnd):n>e.indexEnd&&n-e.indexStart>=this._maxSize&&(i=!0,s=e.indexStart,o=n),i){let e=this._changeScale(o,s);this._downscale(e),n=this._mapping.mapToIndex(t)}this._incrementIndexBy(e,n,r)}_incrementIndexBy(e,t,r){if(0===r)return;if(t<e.indexStart){let r=e.indexEnd-t;r>=e.backing.length&&this._grow(e,r+1),e.indexStart=t}else if(t>e.indexEnd){let r=t-e.indexStart;r>=e.backing.length&&this._grow(e,r+1),e.indexEnd=t}let n=t-e.indexBase;n<0&&(n+=e.backing.length),e.incrementBucket(n,r)}_grow(e,t){let r=e.backing.length,n=e.indexBase-e.indexStart,i=r-n,o=(0,a.nextGreaterSquare)(t);o>this._maxSize&&(o=this._maxSize);let s=o-n;e.backing.growTo(o,i,s)}_changeScale(e,t){let r=0;for(;e-t>=this._maxSize;)e>>=1,t>>=1,r++;return r}_downscale(e){if(0===e)return;if(e<0)throw Error(`impossible change of scale: ${this.scale}`);let t=this._mapping.scale-e;this._positive.downscale(e),this._negative.downscale(e),this._mapping=(0,s.getMapping)(t)}_minScale(e){let t=Math.min(this.scale,e.scale),r=u.combine(this._highLowAtScale(this.positive,this.scale,t),this._highLowAtScale(e.positive,e.scale,t)),n=u.combine(this._highLowAtScale(this.negative,this.scale,t),this._highLowAtScale(e.negative,e.scale,t));return Math.min(t-this._changeScale(r.high,r.low),t-this._changeScale(n.high,n.low))}_highLowAtScale(e,t,r){if(0===e.length)return new u(0,-1);let n=t-r;return new u(e.indexStart>>n,e.indexEnd>>n)}_mergeBuckets(e,t,r,n){let i=r.offset,o=t.scale-n;for(let t=0;t<r.length;t++)this._incrementIndexBy(e,i+t>>o,r.at(t))}_diffBuckets(e,t,r,n){let i=r.offset,o=t.scale-n;for(let t=0;t<r.length;t++){let n=(i+t>>o)-e.indexBase;n<0&&(n+=e.backing.length),e.decrementBucket(n,r.at(t))}e.trim()}};e.ExponentialHistogramAccumulation=_,e.ExponentialHistogramAggregator=class{constructor(e,r){this._maxSize=e,this._recordMinMax=r,this.kind=t.AggregatorKind.EXPONENTIAL_HISTOGRAM}createAccumulation(e){return new _(e,this._maxSize,this._recordMinMax)}merge(e,t){let r=t.clone();return r.merge(e),r}diff(e,t){let r=t.clone();return r.diff(e),r}toMetricData(e,t,n,o){return{descriptor:e,aggregationTemporality:t,dataPointType:r.DataPointType.EXPONENTIAL_HISTOGRAM,dataPoints:n.map(([t,r])=>{let n=r.toPointValue(),s=e.type===i.InstrumentType.UP_DOWN_COUNTER||e.type===i.InstrumentType.OBSERVABLE_GAUGE||e.type===i.InstrumentType.OBSERVABLE_UP_DOWN_COUNTER;return{attributes:t,startTime:r.startTime,endTime:o,value:{min:n.hasMinMax?n.min:void 0,max:n.hasMinMax?n.max:void 0,sum:s?void 0:n.sum,positive:{offset:n.positive.offset,bucketCounts:n.positive.bucketCounts},negative:{offset:n.negative.offset,bucketCounts:n.negative.bucketCounts},count:n.count,scale:n.scale,zeroCount:n.zeroCount}}})}}}}),g_=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.LastValueAggregator=e.LastValueAccumulation=void 0;var t=xt(),r=A(),n=wt(),i=class{constructor(e,t=0,r=[0,0]){this.startTime=e,this._current=t,this.sampleTime=r}record(e){this._current=e,this.sampleTime=(0,r.millisToHrTime)(Date.now())}setStartTime(e){this.startTime=e}toPointValue(){return this._current}};e.LastValueAccumulation=i,e.LastValueAggregator=class{constructor(){this.kind=t.AggregatorKind.LAST_VALUE}createAccumulation(e){return new i(e)}merge(e,t){let n=(0,r.hrTimeToMicroseconds)(t.sampleTime)>=(0,r.hrTimeToMicroseconds)(e.sampleTime)?t:e;return new i(e.startTime,n.toPointValue(),n.sampleTime)}diff(e,t){let n=(0,r.hrTimeToMicroseconds)(t.sampleTime)>=(0,r.hrTimeToMicroseconds)(e.sampleTime)?t:e;return new i(t.startTime,n.toPointValue(),n.sampleTime)}toMetricData(e,t,r,i){return{descriptor:e,aggregationTemporality:t,dataPointType:n.DataPointType.GAUGE,dataPoints:r.map(([e,t])=>({attributes:e,startTime:t.startTime,endTime:i,value:t.toPointValue()}))}}}}),T_=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.SumAggregator=e.SumAccumulation=void 0;var t=xt(),r=wt(),n=class{constructor(e,t,r=0,n=!1){this.startTime=e,this.monotonic=t,this._current=r,this.reset=n}record(e){this.monotonic&&e<0||(this._current+=e)}setStartTime(e){this.startTime=e}toPointValue(){return this._current}};e.SumAccumulation=n,e.SumAggregator=class{constructor(e){this.monotonic=e,this.kind=t.AggregatorKind.SUM}createAccumulation(e){return new n(e,this.monotonic)}merge(e,t){let r=e.toPointValue(),i=t.toPointValue();return t.reset?new n(t.startTime,this.monotonic,i,t.reset):new n(e.startTime,this.monotonic,r+i)}diff(e,t){let r=e.toPointValue(),i=t.toPointValue();return this.monotonic&&r>i?new n(t.startTime,this.monotonic,i,!0):new n(t.startTime,this.monotonic,i-r)}toMetricData(e,t,n,i){return{descriptor:e,aggregationTemporality:t,dataPointType:r.DataPointType.SUM,dataPoints:n.map(([e,t])=>({attributes:e,startTime:t.startTime,endTime:i,value:t.toPointValue()})),isMonotonic:this.monotonic}}}}),S_=l(e=>{var t=e&&e.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),r=e&&e.__exportStar||function(e,r){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(r,n)||t(r,e,n)};Object.defineProperty(e,"__esModule",{value:!0}),r(r_(),e),r(s_(),e),r(m_(),e),r(g_(),e),r(T_(),e)}),Fi=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.DefaultAggregation=e.ExponentialHistogramAggregation=e.ExplicitBucketHistogramAggregation=e.HistogramAggregation=e.LastValueAggregation=e.SumAggregation=e.DropAggregation=e.Aggregation=void 0;var t=(h(),m(d)),r=S_(),n=be(),i=class{static Drop(){return _}static Sum(){return g}static LastValue(){return f}static Histogram(){return E}static ExponentialHistogram(){return T}static Default(){return S}};e.Aggregation=i;var o=class e extends i{createAggregator(t){return e.DEFAULT_INSTANCE}};e.DropAggregation=o,o.DEFAULT_INSTANCE=new r.DropAggregator;var s=class e extends i{createAggregator(t){switch(t.type){case n.InstrumentType.COUNTER:case n.InstrumentType.OBSERVABLE_COUNTER:case n.InstrumentType.HISTOGRAM:return e.MONOTONIC_INSTANCE;default:return e.NON_MONOTONIC_INSTANCE}}};e.SumAggregation=s,s.MONOTONIC_INSTANCE=new r.SumAggregator(!0),s.NON_MONOTONIC_INSTANCE=new r.SumAggregator(!1);var a=class e extends i{createAggregator(t){return e.DEFAULT_INSTANCE}};e.LastValueAggregation=a,a.DEFAULT_INSTANCE=new r.LastValueAggregator;var u=class e extends i{createAggregator(t){return e.DEFAULT_INSTANCE}};e.HistogramAggregation=u,u.DEFAULT_INSTANCE=new r.HistogramAggregator([0,5,10,25,50,75,100,250,500,750,1e3,2500,5e3,7500,1e4],!0);var c=class extends i{constructor(e,t=!0){if(super(),this._recordMinMax=t,void 0===e||0===e.length)throw Error("HistogramAggregator should be created with boundaries.");let r=(e=(e=e.concat()).sort((e,t)=>e-t)).lastIndexOf(-1/0),n=e.indexOf(1/0);-1===n&&(n=void 0),this._boundaries=e.slice(r+1,n)}createAggregator(e){return new r.HistogramAggregator(this._boundaries,this._recordMinMax)}};e.ExplicitBucketHistogramAggregation=c;var l=class extends i{constructor(e=160,t=!0){super(),this._maxSize=e,this._recordMinMax=t}createAggregator(e){return new r.ExponentialHistogramAggregator(this._maxSize,this._recordMinMax)}};e.ExponentialHistogramAggregation=l;var p=class extends i{_resolve(e){switch(e.type){case n.InstrumentType.COUNTER:case n.InstrumentType.UP_DOWN_COUNTER:case n.InstrumentType.OBSERVABLE_COUNTER:case n.InstrumentType.OBSERVABLE_UP_DOWN_COUNTER:return g;case n.InstrumentType.OBSERVABLE_GAUGE:return f;case n.InstrumentType.HISTOGRAM:return e.advice.explicitBucketBoundaries?new c(e.advice.explicitBucketBoundaries):E}return t.diag.warn(`Unable to recognize instrument type: ${e.type}`),_}createAggregator(e){return this._resolve(e).createAggregator(e)}};e.DefaultAggregation=p;var _=new o,g=new s,f=new a,E=new u,T=new l,S=new p}),Xa=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.DEFAULT_AGGREGATION_TEMPORALITY_SELECTOR=e.DEFAULT_AGGREGATION_SELECTOR=void 0;var t=Fi(),r=Li();e.DEFAULT_AGGREGATION_SELECTOR=e=>t.Aggregation.Default(),e.DEFAULT_AGGREGATION_TEMPORALITY_SELECTOR=e=>r.AggregationTemporality.CUMULATIVE}),Ka=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.MetricReader=void 0;var t=(h(),m(d)),r=fe(),n=Xa();e.MetricReader=class{constructor(e){var t,r,i;this._shutdown=!1,this._aggregationSelector=null!=(t=e?.aggregationSelector)?t:n.DEFAULT_AGGREGATION_SELECTOR,this._aggregationTemporalitySelector=null!=(r=e?.aggregationTemporalitySelector)?r:n.DEFAULT_AGGREGATION_TEMPORALITY_SELECTOR,this._metricProducers=null!=(i=e?.metricProducers)?i:[]}setMetricProducer(e){if(this._sdkMetricProducer)throw Error("MetricReader can not be bound to a MeterProvider again.");this._sdkMetricProducer=e,this.onInitialized()}selectAggregation(e){return this._aggregationSelector(e)}selectAggregationTemporality(e){return this._aggregationTemporalitySelector(e)}onInitialized(){}async collect(e){if(void 0===this._sdkMetricProducer)throw Error("MetricReader is not bound to a MetricProducer");if(this._shutdown)throw Error("MetricReader is shutdown");let[t,...n]=await Promise.all([this._sdkMetricProducer.collect({timeoutMillis:e?.timeoutMillis}),...this._metricProducers.map(t=>t.collect({timeoutMillis:e?.timeoutMillis}))]),i=t.errors.concat((0,r.FlatMap)(n,e=>e.errors));return{resourceMetrics:{resource:t.resourceMetrics.resource,scopeMetrics:t.resourceMetrics.scopeMetrics.concat((0,r.FlatMap)(n,e=>e.resourceMetrics.scopeMetrics))},errors:i}}async shutdown(e){if(this._shutdown)return void t.diag.error("Cannot call shutdown twice.");e?.timeoutMillis==null?await this.onShutdown():await (0,r.callWithTimeout)(this.onShutdown(),e.timeoutMillis),this._shutdown=!0}async forceFlush(e){return this._shutdown?void t.diag.warn("Cannot forceFlush on already shutdown MetricReader."):e?.timeoutMillis==null?void await this.onForceFlush():void await (0,r.callWithTimeout)(this.onForceFlush(),e.timeoutMillis)}}}),L_=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.PeriodicExportingMetricReader=void 0;var t=(h(),m(d)),r=A(),n=Ka(),i=fe(),o=(h(),m(d));e.PeriodicExportingMetricReader=class extends n.MetricReader{constructor(e){var t,r,n,i;if(super({aggregationSelector:null==(t=e.exporter.selectAggregation)?void 0:t.bind(e.exporter),aggregationTemporalitySelector:null==(r=e.exporter.selectAggregationTemporality)?void 0:r.bind(e.exporter),metricProducers:e.metricProducers}),void 0!==e.exportIntervalMillis&&e.exportIntervalMillis<=0)throw Error("exportIntervalMillis must be greater than 0");if(void 0!==e.exportTimeoutMillis&&e.exportTimeoutMillis<=0)throw Error("exportTimeoutMillis must be greater than 0");if(void 0!==e.exportTimeoutMillis&&void 0!==e.exportIntervalMillis&&e.exportIntervalMillis<e.exportTimeoutMillis)throw Error("exportIntervalMillis must be greater than or equal to exportTimeoutMillis");this._exportInterval=null!=(n=e.exportIntervalMillis)?n:6e4,this._exportTimeout=null!=(i=e.exportTimeoutMillis)?i:3e4,this._exporter=e.exporter}async _runOnce(){try{await (0,i.callWithTimeout)(this._doRun(),this._exportTimeout)}catch(e){if(e instanceof i.TimeoutError)return void t.diag.error("Export took longer than %s milliseconds and timed out.",this._exportTimeout);(0,r.globalErrorHandler)(e)}}async _doRun(){var e,n;let{resourceMetrics:i,errors:s}=await this.collect({timeoutMillis:this._exportTimeout});s.length>0&&t.diag.error("PeriodicExportingMetricReader: metrics collection errors",...s);let a=async()=>{let e=await r.internal._export(this._exporter,i);if(e.code!==r.ExportResultCode.SUCCESS)throw Error(`PeriodicExportingMetricReader: metrics export failed (error ${e.error})`)};i.resource.asyncAttributesPending?null==(n=(e=i.resource).waitForAsyncAttributes)||n.call(e).then(a,e=>o.diag.debug("Error while resolving async portion of resource: ",e)):await a()}onInitialized(){this._interval=setInterval(()=>{this._runOnce()},this._exportInterval),(0,r.unrefTimer)(this._interval)}async onForceFlush(){await this._runOnce(),await this._exporter.forceFlush()}async onShutdown(){this._interval&&clearInterval(this._interval),await this._exporter.shutdown()}}}),N_=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.InMemoryMetricExporter=void 0;var t=A();e.InMemoryMetricExporter=class{constructor(e){this._shutdown=!1,this._metrics=[],this._aggregationTemporality=e}export(e,r){if(this._shutdown)return void setTimeout(()=>r({code:t.ExportResultCode.FAILED}),0);this._metrics.push(e),setTimeout(()=>r({code:t.ExportResultCode.SUCCESS}),0)}getMetrics(){return this._metrics}forceFlush(){return Promise.resolve()}reset(){this._metrics=[]}selectAggregationTemporality(e){return this._aggregationTemporality}shutdown(){return this._shutdown=!0,Promise.resolve()}}}),w_=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.ConsoleMetricExporter=void 0;var t=A(),r=Xa();e.ConsoleMetricExporter=class e{constructor(e){var t;this._shutdown=!1,this._temporalitySelector=null!=(t=e?.temporalitySelector)?t:r.DEFAULT_AGGREGATION_TEMPORALITY_SELECTOR}export(r,n){return this._shutdown?void setImmediate(n,{code:t.ExportResultCode.FAILED}):e._sendMetrics(r,n)}forceFlush(){return Promise.resolve()}selectAggregationTemporality(e){return this._temporalitySelector(e)}shutdown(){return this._shutdown=!0,Promise.resolve()}static _sendMetrics(e,r){for(let t of e.scopeMetrics)for(let e of t.metrics)console.dir({descriptor:e.descriptor,dataPointType:e.dataPointType,dataPoints:e.dataPoints});r({code:t.ExportResultCode.SUCCESS})}}}),x_=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.ViewRegistry=void 0,e.ViewRegistry=class{constructor(){this._registeredViews=[]}addView(e){this._registeredViews.push(e)}findViews(e,t){return this._registeredViews.filter(r=>this._matchInstrument(r.instrumentSelector,e)&&this._matchMeter(r.meterSelector,t))}_matchInstrument(e,t){return(void 0===e.getType()||t.type===e.getType())&&e.getNameFilter().match(t.name)&&e.getUnitFilter().match(t.unit)}_matchMeter(e,t){return e.getNameFilter().match(t.name)&&(void 0===t.version||e.getVersionFilter().match(t.version))&&(void 0===t.schemaUrl||e.getSchemaUrlFilter().match(t.schemaUrl))}}}),Qi=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.isObservableInstrument=e.ObservableUpDownCounterInstrument=e.ObservableGaugeInstrument=e.ObservableCounterInstrument=e.ObservableInstrument=e.HistogramInstrument=e.CounterInstrument=e.UpDownCounterInstrument=e.SyncInstrument=void 0;var t=(h(),m(d)),r=A(),n=class{constructor(e,t){this._writableMetricStorage=e,this._descriptor=t}_record(e,n={},i=t.context.active()){if("number"!=typeof e)return void t.diag.warn(`non-number value provided to metric ${this._descriptor.name}: ${e}`);(this._descriptor.valueType!==t.ValueType.INT||Number.isInteger(e)||(t.diag.warn(`INT value type cannot accept a floating-point value for ${this._descriptor.name}, ignoring the fractional digits.`),Number.isInteger(e=Math.trunc(e))))&&this._writableMetricStorage.record(e,n,i,(0,r.millisToHrTime)(Date.now()))}};e.SyncInstrument=n,e.UpDownCounterInstrument=class extends n{add(e,t,r){this._record(e,t,r)}},e.CounterInstrument=class extends n{add(e,r,n){if(e<0)return void t.diag.warn(`negative value provided to counter ${this._descriptor.name}: ${e}`);this._record(e,r,n)}},e.HistogramInstrument=class extends n{record(e,r,n){if(e<0)return void t.diag.warn(`negative value provided to histogram ${this._descriptor.name}: ${e}`);this._record(e,r,n)}};var i=class{constructor(e,t,r){this._observableRegistry=r,this._descriptor=e,this._metricStorages=t}addCallback(e){this._observableRegistry.addCallback(e,this)}removeCallback(e){this._observableRegistry.removeCallback(e,this)}};e.ObservableInstrument=i,e.ObservableCounterInstrument=class extends i{},e.ObservableGaugeInstrument=class extends i{},e.ObservableUpDownCounterInstrument=class extends i{},e.isObservableInstrument=function(e){return e instanceof i}}),D_=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.Meter=void 0;var t=be(),r=Qi();e.Meter=class{constructor(e){this._meterSharedState=e}createHistogram(e,n){let i=(0,t.createInstrumentDescriptor)(e,t.InstrumentType.HISTOGRAM,n),o=this._meterSharedState.registerMetricStorage(i);return new r.HistogramInstrument(o,i)}createCounter(e,n){let i=(0,t.createInstrumentDescriptor)(e,t.InstrumentType.COUNTER,n),o=this._meterSharedState.registerMetricStorage(i);return new r.CounterInstrument(o,i)}createUpDownCounter(e,n){let i=(0,t.createInstrumentDescriptor)(e,t.InstrumentType.UP_DOWN_COUNTER,n),o=this._meterSharedState.registerMetricStorage(i);return new r.UpDownCounterInstrument(o,i)}createObservableGauge(e,n){let i=(0,t.createInstrumentDescriptor)(e,t.InstrumentType.OBSERVABLE_GAUGE,n),o=this._meterSharedState.registerAsyncMetricStorage(i);return new r.ObservableGaugeInstrument(i,o,this._meterSharedState.observableRegistry)}createObservableCounter(e,n){let i=(0,t.createInstrumentDescriptor)(e,t.InstrumentType.OBSERVABLE_COUNTER,n),o=this._meterSharedState.registerAsyncMetricStorage(i);return new r.ObservableCounterInstrument(i,o,this._meterSharedState.observableRegistry)}createObservableUpDownCounter(e,n){let i=(0,t.createInstrumentDescriptor)(e,t.InstrumentType.OBSERVABLE_UP_DOWN_COUNTER,n),o=this._meterSharedState.registerAsyncMetricStorage(i);return new r.ObservableUpDownCounterInstrument(i,o,this._meterSharedState.observableRegistry)}addBatchObservableCallback(e,t){this._meterSharedState.observableRegistry.addBatchCallback(e,t)}removeBatchObservableCallback(e,t){this._meterSharedState.observableRegistry.removeBatchCallback(e,t)}}}),au=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.MetricStorage=void 0;var t=be();e.MetricStorage=class{constructor(e){this._instrumentDescriptor=e}getInstrumentDescriptor(){return this._instrumentDescriptor}updateDescription(e){this._instrumentDescriptor=(0,t.createInstrumentDescriptor)(this._instrumentDescriptor.name,this._instrumentDescriptor.type,{description:e,valueType:this._instrumentDescriptor.valueType,unit:this._instrumentDescriptor.unit,advice:this._instrumentDescriptor.advice})}}}),wr=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.AttributeHashMap=e.HashMap=void 0;var t=fe(),r=class{constructor(e){this._hash=e,this._valueMap=new Map,this._keyMap=new Map}get(e,t){return t??(t=this._hash(e)),this._valueMap.get(t)}getOrDefault(e,t){let r=this._hash(e);if(this._valueMap.has(r))return this._valueMap.get(r);let n=t();return this._keyMap.has(r)||this._keyMap.set(r,e),this._valueMap.set(r,n),n}set(e,t,r){r??(r=this._hash(e)),this._keyMap.has(r)||this._keyMap.set(r,e),this._valueMap.set(r,t)}has(e,t){return t??(t=this._hash(e)),this._valueMap.has(t)}*keys(){let e=this._keyMap.entries(),t=e.next();for(;!0!==t.done;)yield[t.value[1],t.value[0]],t=e.next()}*entries(){let e=this._valueMap.entries(),t=e.next();for(;!0!==t.done;)yield[this._keyMap.get(t.value[0]),t.value[1],t.value[0]],t=e.next()}get size(){return this._valueMap.size}};e.HashMap=r,e.AttributeHashMap=class extends r{constructor(){super(t.hashAttributes)}}}),du=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.DeltaMetricProcessor=void 0;var t=wr();e.DeltaMetricProcessor=class{constructor(e){this._aggregator=e,this._activeCollectionStorage=new t.AttributeHashMap,this._cumulativeMemoStorage=new t.AttributeHashMap}record(e,t,r,n){let i=this._activeCollectionStorage.getOrDefault(t,()=>this._aggregator.createAccumulation(n));i?.record(e)}batchCumulate(e,t){Array.from(e.entries()).forEach(([e,r,n])=>{let i=this._aggregator.createAccumulation(t);i?.record(r);let o=i;if(this._cumulativeMemoStorage.has(e,n)){let t=this._cumulativeMemoStorage.get(e,n);o=this._aggregator.diff(t,i)}if(this._activeCollectionStorage.has(e,n)){let t=this._activeCollectionStorage.get(e,n);o=this._aggregator.merge(t,o)}this._cumulativeMemoStorage.set(e,i,n),this._activeCollectionStorage.set(e,o,n)})}collect(){let e=this._activeCollectionStorage;return this._activeCollectionStorage=new t.AttributeHashMap,e}}}),pu=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.TemporalMetricProcessor=void 0;var t=Li(),r=wr();function n(e){return Array.from(e.entries())}e.TemporalMetricProcessor=class e{constructor(e,t){this._aggregator=e,this._unreportedAccumulations=new Map,this._reportHistory=new Map,t.forEach(e=>{this._unreportedAccumulations.set(e,[])})}buildMetrics(r,i,o,s){this._stashAccumulations(o);let a=this._getMergedUnreportedAccumulations(r),u=a,c;if(this._reportHistory.has(r)){let n=this._reportHistory.get(r),i=n.collectionTime;u=(c=n.aggregationTemporality)===t.AggregationTemporality.CUMULATIVE?e.merge(n.accumulations,a,this._aggregator):e.calibrateStartTime(n.accumulations,a,i)}else c=r.selectAggregationTemporality(i.type);this._reportHistory.set(r,{accumulations:u,collectionTime:s,aggregationTemporality:c});let l=n(u);if(0!==l.length)return this._aggregator.toMetricData(i,c,l,s)}_stashAccumulations(e){for(let t of this._unreportedAccumulations.keys()){let r=this._unreportedAccumulations.get(t);void 0===r&&(r=[],this._unreportedAccumulations.set(t,r)),r.push(e)}}_getMergedUnreportedAccumulations(t){let n=new r.AttributeHashMap,i=this._unreportedAccumulations.get(t);if(this._unreportedAccumulations.set(t,[]),void 0===i)return n;for(let t of i)n=e.merge(n,t,this._aggregator);return n}static merge(e,t,r){let n=e,i=t.entries(),o=i.next();for(;!0!==o.done;){let[t,s,a]=o.value;if(e.has(t,a)){let i=e.get(t,a),o=r.merge(i,s);n.set(t,o,a)}else n.set(t,s,a);o=i.next()}return n}static calibrateStartTime(e,t,r){for(let[n,i]of e.keys()){let e=t.get(n,i);e?.setStartTime(r)}return t}}}),U_=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.AsyncMetricStorage=void 0;var t=au(),r=du(),n=pu(),i=wr();e.AsyncMetricStorage=class extends t.MetricStorage{constructor(e,t,i,o){super(e),this._attributesProcessor=i,this._deltaMetricStorage=new r.DeltaMetricProcessor(t),this._temporalMetricStorage=new n.TemporalMetricProcessor(t,o)}record(e,t){let r=new i.AttributeHashMap;Array.from(e.entries()).forEach(([e,t])=>{r.set(this._attributesProcessor.process(e),t)}),this._deltaMetricStorage.batchCumulate(r,t)}collect(e,t){let r=this._deltaMetricStorage.collect();return this._temporalMetricStorage.buildMetrics(e,this._instrumentDescriptor,r,t)}}}),H_=l(e=>{function t(e,t){return`	- use valueType '${e.valueType}' on instrument creation or use an instrument name other than '${t.name}'`}function r(e,t){return`	- use unit '${e.unit}' on instrument creation or use an instrument name other than '${t.name}'`}function n(e,t){let r=JSON.stringify({name:t.name,type:t.type,unit:t.unit});return`	- create a new view with a name other than '${e.name}' and InstrumentSelector '${r}'`}function i(e,t){let r=JSON.stringify({name:t.name,type:t.type,unit:t.unit});return`	- create a new view with a name other than '${e.name}' and InstrumentSelector '${r}'
    	- OR - create a new view with the name ${e.name} and description '${e.description}' and InstrumentSelector ${r}
    	- OR - create a new view with the name ${t.name} and description '${e.description}' and InstrumentSelector ${r}`}Object.defineProperty(e,"__esModule",{value:!0}),e.getConflictResolutionRecipe=e.getDescriptionResolutionRecipe=e.getTypeConflictResolutionRecipe=e.getUnitConflictResolutionRecipe=e.getValueTypeConflictResolutionRecipe=e.getIncompatibilityDetails=void 0,e.getIncompatibilityDetails=function(e,t){let r="";return e.unit!==t.unit&&(r+=`	- Unit '${e.unit}' does not match '${t.unit}'
`),e.type!==t.type&&(r+=`	- Type '${e.type}' does not match '${t.type}'
`),e.valueType!==t.valueType&&(r+=`	- Value Type '${e.valueType}' does not match '${t.valueType}'
`),e.description!==t.description&&(r+=`	- Description '${e.description}' does not match '${t.description}'
`),r},e.getValueTypeConflictResolutionRecipe=t,e.getUnitConflictResolutionRecipe=r,e.getTypeConflictResolutionRecipe=n,e.getDescriptionResolutionRecipe=i,e.getConflictResolutionRecipe=function(e,o){return e.valueType!==o.valueType?t(e,o):e.unit!==o.unit?r(e,o):e.type!==o.type?n(e,o):e.description!==o.description?i(e,o):""}}),F_=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.MetricStorageRegistry=void 0;var t=be(),r=(h(),m(d)),n=H_();e.MetricStorageRegistry=class e{constructor(){this._sharedRegistry=new Map,this._perCollectorRegistry=new Map}static create(){return new e}getStorages(e){let t=[];for(let e of this._sharedRegistry.values())t=t.concat(e);let r=this._perCollectorRegistry.get(e);if(null!=r)for(let e of r.values())t=t.concat(e);return t}register(e){this._registerStorage(e,this._sharedRegistry)}registerForCollector(e,t){let r=this._perCollectorRegistry.get(e);null==r&&(r=new Map,this._perCollectorRegistry.set(e,r)),this._registerStorage(t,r)}findOrUpdateCompatibleStorage(e){let t=this._sharedRegistry.get(e.name);return void 0===t?null:this._findOrUpdateCompatibleStorage(e,t)}findOrUpdateCompatibleCollectorStorage(e,t){let r=this._perCollectorRegistry.get(e);if(void 0===r)return null;let n=r.get(t.name);return void 0===n?null:this._findOrUpdateCompatibleStorage(t,n)}_registerStorage(e,t){let r=e.getInstrumentDescriptor(),n=t.get(r.name);if(void 0===n)return void t.set(r.name,[e]);n.push(e)}_findOrUpdateCompatibleStorage(e,i){let o=null;for(let s of i){let i=s.getInstrumentDescriptor();(0,t.isDescriptorCompatibleWith)(i,e)?(i.description!==e.description&&(e.description.length>i.description.length&&s.updateDescription(e.description),r.diag.warn("A view or instrument with the name ",e.name,` has already been registered, but has a different description and is incompatible with another registered view.
`,`Details:
`,(0,n.getIncompatibilityDetails)(i,e),`The longer description will be used.
To resolve the conflict:`,(0,n.getConflictResolutionRecipe)(i,e))),o=s):r.diag.warn("A view or instrument with the name ",e.name,` has already been registered and is incompatible with another registered view.
`,`Details:
`,(0,n.getIncompatibilityDetails)(i,e),`To resolve the conflict:
`,(0,n.getConflictResolutionRecipe)(i,e))}return o}}}),k_=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.MultiMetricStorage=void 0,e.MultiMetricStorage=class{constructor(e){this._backingStorages=e}record(e,t,r,n){this._backingStorages.forEach(i=>{i.record(e,t,r,n)})}}}),X_=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.BatchObservableResultImpl=e.ObservableResultImpl=void 0;var t=(h(),m(d)),r=wr(),n=Qi();e.ObservableResultImpl=class{constructor(e,t){this._instrumentName=e,this._valueType=t,this._buffer=new r.AttributeHashMap}observe(e,r={}){if("number"!=typeof e)return void t.diag.warn(`non-number value provided to metric ${this._instrumentName}: ${e}`);(this._valueType!==t.ValueType.INT||Number.isInteger(e)||(t.diag.warn(`INT value type cannot accept a floating-point value for ${this._instrumentName}, ignoring the fractional digits.`),Number.isInteger(e=Math.trunc(e))))&&this._buffer.set(r,e)}},e.BatchObservableResultImpl=class{constructor(){this._buffer=new Map}observe(e,i,o={}){if(!(0,n.isObservableInstrument)(e))return;let s=this._buffer.get(e);if(null==s&&(s=new r.AttributeHashMap,this._buffer.set(e,s)),"number"!=typeof i)return void t.diag.warn(`non-number value provided to metric ${e._descriptor.name}: ${i}`);(e._descriptor.valueType!==t.ValueType.INT||Number.isInteger(i)||(t.diag.warn(`INT value type cannot accept a floating-point value for ${e._descriptor.name}, ignoring the fractional digits.`),Number.isInteger(i=Math.trunc(i))))&&s.set(o,i)}}}),z_=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.ObservableRegistry=void 0;var t=(h(),m(d)),r=Qi(),n=X_(),i=fe();e.ObservableRegistry=class{constructor(){this._callbacks=[],this._batchCallbacks=[]}addCallback(e,t){this._findCallback(e,t)>=0||this._callbacks.push({callback:e,instrument:t})}removeCallback(e,t){let r=this._findCallback(e,t);r<0||this._callbacks.splice(r,1)}addBatchCallback(e,n){let i=new Set(n.filter(r.isObservableInstrument));if(0===i.size)return void t.diag.error("BatchObservableCallback is not associated with valid instruments",n);this._findBatchCallback(e,i)>=0||this._batchCallbacks.push({callback:e,instruments:i})}removeBatchCallback(e,t){let n=new Set(t.filter(r.isObservableInstrument)),i=this._findBatchCallback(e,n);i<0||this._batchCallbacks.splice(i,1)}async observe(e,t){let r=this._observeCallbacks(e,t),n=this._observeBatchCallbacks(e,t);return(await (0,i.PromiseAllSettled)([...r,...n])).filter(i.isPromiseAllSettledRejectionResult).map(e=>e.reason)}_observeCallbacks(e,t){return this._callbacks.map(async({callback:r,instrument:o})=>{let s=new n.ObservableResultImpl(o._descriptor.name,o._descriptor.valueType),a=Promise.resolve(r(s));null!=t&&(a=(0,i.callWithTimeout)(a,t)),await a,o._metricStorages.forEach(t=>{t.record(s._buffer,e)})})}_observeBatchCallbacks(e,t){return this._batchCallbacks.map(async({callback:r,instruments:o})=>{let s=new n.BatchObservableResultImpl,a=Promise.resolve(r(s));null!=t&&(a=(0,i.callWithTimeout)(a,t)),await a,o.forEach(t=>{let r=s._buffer.get(t);null!=r&&t._metricStorages.forEach(t=>{t.record(r,e)})})})}_findCallback(e,t){return this._callbacks.findIndex(r=>r.callback===e&&r.instrument===t)}_findBatchCallback(e,t){return this._batchCallbacks.findIndex(r=>r.callback===e&&(0,i.setEquals)(r.instruments,t))}}}),Y_=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.SyncMetricStorage=void 0;var t=au(),r=du(),n=pu();e.SyncMetricStorage=class extends t.MetricStorage{constructor(e,t,i,o){super(e),this._attributesProcessor=i,this._deltaMetricStorage=new r.DeltaMetricProcessor(t),this._temporalMetricStorage=new n.TemporalMetricProcessor(t,o)}record(e,t,r,n){t=this._attributesProcessor.process(t,r),this._deltaMetricStorage.record(e,t,r,n)}collect(e,t){let r=this._deltaMetricStorage.collect();return this._temporalMetricStorage.buildMetrics(e,this._instrumentDescriptor,r,t)}}}),Ou=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.FilteringAttributesProcessor=e.NoopAttributesProcessor=e.AttributesProcessor=void 0;var t=class{static Noop(){return n}};e.AttributesProcessor=t;var r=class extends t{process(e,t){return e}};e.NoopAttributesProcessor=r,e.FilteringAttributesProcessor=class extends t{constructor(e){super(),this._allowedAttributeNames=e}process(e,t){let r={};return Object.keys(e).filter(e=>this._allowedAttributeNames.includes(e)).forEach(t=>r[t]=e[t]),r}};var n=new r}),Q_=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.MeterSharedState=void 0;var t=be(),r=D_(),n=fe(),i=U_(),o=F_(),s=k_(),a=z_(),u=Y_(),c=Ou();e.MeterSharedState=class{constructor(e,t){this._meterProviderSharedState=e,this._instrumentationScope=t,this.metricStorageRegistry=new o.MetricStorageRegistry,this.observableRegistry=new a.ObservableRegistry,this.meter=new r.Meter(this)}registerMetricStorage(e){let t=this._registerMetricStorage(e,u.SyncMetricStorage);return 1===t.length?t[0]:new s.MultiMetricStorage(t)}registerAsyncMetricStorage(e){return this._registerMetricStorage(e,i.AsyncMetricStorage)}async collect(e,t,r){let i=await this.observableRegistry.observe(t,r?.timeoutMillis),o=this.metricStorageRegistry.getStorages(e);if(0===o.length)return null;let s=o.map(r=>r.collect(e,t)).filter(n.isNotNullish);return 0===s.length?{errors:i}:{scopeMetrics:{scope:this._instrumentationScope,metrics:s},errors:i}}_registerMetricStorage(e,r){let n=this._meterProviderSharedState.viewRegistry.findViews(e,this._instrumentationScope).map(n=>{let i=(0,t.createInstrumentDescriptorWithView)(n,e),o=this.metricStorageRegistry.findOrUpdateCompatibleStorage(i);if(null!=o)return o;let s=n.aggregation.createAggregator(i),a=new r(i,s,n.attributesProcessor,this._meterProviderSharedState.metricCollectors);return this.metricStorageRegistry.register(a),a});if(0===n.length){let t=this._meterProviderSharedState.selectAggregations(e.type).map(([t,n])=>{let i=this.metricStorageRegistry.findOrUpdateCompatibleCollectorStorage(t,e);if(null!=i)return i;let o=n.createAggregator(e),s=new r(e,o,c.AttributesProcessor.Noop(),[t]);return this.metricStorageRegistry.registerForCollector(t,s),s});n=n.concat(t)}return n}}}),Z_=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.MeterProviderSharedState=void 0;var t=fe(),r=x_(),n=Q_();e.MeterProviderSharedState=class{constructor(e){this.resource=e,this.viewRegistry=new r.ViewRegistry,this.metricCollectors=[],this.meterSharedStates=new Map}getMeterSharedState(e){let r=(0,t.instrumentationScopeId)(e),i=this.meterSharedStates.get(r);return null==i&&(i=new n.MeterSharedState(this,e),this.meterSharedStates.set(r,i)),i}selectAggregations(e){let t=[];for(let r of this.metricCollectors)t.push([r,r.selectAggregation(e)]);return t}}}),J_=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.MetricCollector=void 0;var t=A();e.MetricCollector=class{constructor(e,t){this._sharedState=e,this._metricReader=t}async collect(e){let r=(0,t.millisToHrTime)(Date.now()),n=[],i=[],o=Array.from(this._sharedState.meterSharedStates.values()).map(async t=>{let o=await t.collect(this,r,e);o?.scopeMetrics!=null&&n.push(o.scopeMetrics),o?.errors!=null&&i.push(...o.errors)});return await Promise.all(o),{resourceMetrics:{resource:this._sharedState.resource,scopeMetrics:n},errors:i}}async forceFlush(e){await this._metricReader.forceFlush(e)}async shutdown(e){await this._metricReader.shutdown(e)}selectAggregationTemporality(e){return this._metricReader.selectAggregationTemporality(e)}selectAggregation(e){return this._metricReader.selectAggregation(e)}}}),tp=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.MeterProvider=void 0;var t=(h(),m(d)),r=rt(),n=Z_(),i=J_();e.MeterProvider=class{constructor(e){var t;this._shutdown=!1;let i=r.Resource.default().merge(null!=(t=e?.resource)?t:r.Resource.empty());if(this._sharedState=new n.MeterProviderSharedState(i),e?.views!=null&&e.views.length>0)for(let t of e.views)this._sharedState.viewRegistry.addView(t)}getMeter(e,r="",n={}){return this._shutdown?(t.diag.warn("A shutdown MeterProvider cannot provide a Meter"),(0,t.createNoopMeter)()):this._sharedState.getMeterSharedState({name:e,version:r,schemaUrl:n.schemaUrl}).meter}addMetricReader(e){let t=new i.MetricCollector(this._sharedState,e);e.setMetricProducer(t),this._sharedState.metricCollectors.push(t)}async shutdown(e){if(this._shutdown)return void t.diag.warn("shutdown may only be called once per MeterProvider");this._shutdown=!0,await Promise.all(this._sharedState.metricCollectors.map(t=>t.shutdown(e)))}async forceFlush(e){if(this._shutdown)return void t.diag.warn("invalid attempt to force flush after MeterProvider shutdown");await Promise.all(this._sharedState.metricCollectors.map(t=>t.forceFlush(e)))}}}),fs=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.ExactPredicate=e.PatternPredicate=void 0;var t=/[\^$\\.+?()[\]{}|]/g;e.PatternPredicate=class e{constructor(t){"*"===t?(this._matchAll=!0,this._regexp=/.*/):(this._matchAll=!1,this._regexp=new RegExp(e.escapePattern(t)))}match(e){return!!this._matchAll||this._regexp.test(e)}static escapePattern(e){return`^${e.replace(t,"\\$&").replace("*",".*")}$`}static hasWildcard(e){return e.includes("*")}},e.ExactPredicate=class{constructor(e){this._matchAll=void 0===e,this._pattern=e}match(e){return!!(this._matchAll||e===this._pattern)}}}),np=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.InstrumentSelector=void 0;var t=fs();e.InstrumentSelector=class{constructor(e){var r;this._nameFilter=new t.PatternPredicate(null!=(r=e?.name)?r:"*"),this._type=e?.type,this._unitFilter=new t.ExactPredicate(e?.unit)}getType(){return this._type}getNameFilter(){return this._nameFilter}getUnitFilter(){return this._unitFilter}}}),ip=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.MeterSelector=void 0;var t=fs();e.MeterSelector=class{constructor(e){this._nameFilter=new t.ExactPredicate(e?.name),this._versionFilter=new t.ExactPredicate(e?.version),this._schemaUrlFilter=new t.ExactPredicate(e?.schemaUrl)}getNameFilter(){return this._nameFilter}getVersionFilter(){return this._versionFilter}getSchemaUrlFilter(){return this._schemaUrlFilter}}}),op=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.View=void 0;var t=fs(),r=Ou(),n=np(),i=ip(),o=Fi();function s(e){return null==e.instrumentName&&null==e.instrumentType&&null==e.instrumentUnit&&null==e.meterName&&null==e.meterVersion&&null==e.meterSchemaUrl}e.View=class{constructor(e){var a;if(s(e))throw Error("Cannot create view with no selector arguments supplied");if(null!=e.name&&(e?.instrumentName==null||t.PatternPredicate.hasWildcard(e.instrumentName)))throw Error("Views with a specified name must be declared with an instrument selector that selects at most one instrument per meter.");null!=e.attributeKeys?this.attributesProcessor=new r.FilteringAttributesProcessor(e.attributeKeys):this.attributesProcessor=r.AttributesProcessor.Noop(),this.name=e.name,this.description=e.description,this.aggregation=null!=(a=e.aggregation)?a:o.Aggregation.Default(),this.instrumentSelector=new n.InstrumentSelector({name:e.instrumentName,type:e.instrumentType,unit:e.instrumentUnit}),this.meterSelector=new i.MeterSelector({name:e.meterName,version:e.meterVersion,schemaUrl:e.meterSchemaUrl})}}}),wu=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.TimeoutError=e.View=e.Aggregation=e.SumAggregation=e.LastValueAggregation=e.HistogramAggregation=e.DropAggregation=e.ExponentialHistogramAggregation=e.ExplicitBucketHistogramAggregation=e.DefaultAggregation=e.MeterProvider=e.InstrumentType=e.ConsoleMetricExporter=e.InMemoryMetricExporter=e.PeriodicExportingMetricReader=e.MetricReader=e.DataPointType=e.AggregationTemporality=void 0;var t=Li();Object.defineProperty(e,"AggregationTemporality",{enumerable:!0,get:function(){return t.AggregationTemporality}});var r=wt();Object.defineProperty(e,"DataPointType",{enumerable:!0,get:function(){return r.DataPointType}});var n=Ka();Object.defineProperty(e,"MetricReader",{enumerable:!0,get:function(){return n.MetricReader}});var i=L_();Object.defineProperty(e,"PeriodicExportingMetricReader",{enumerable:!0,get:function(){return i.PeriodicExportingMetricReader}});var o=N_();Object.defineProperty(e,"InMemoryMetricExporter",{enumerable:!0,get:function(){return o.InMemoryMetricExporter}});var s=w_();Object.defineProperty(e,"ConsoleMetricExporter",{enumerable:!0,get:function(){return s.ConsoleMetricExporter}});var a=be();Object.defineProperty(e,"InstrumentType",{enumerable:!0,get:function(){return a.InstrumentType}});var u=tp();Object.defineProperty(e,"MeterProvider",{enumerable:!0,get:function(){return u.MeterProvider}});var c=Fi();Object.defineProperty(e,"DefaultAggregation",{enumerable:!0,get:function(){return c.DefaultAggregation}}),Object.defineProperty(e,"ExplicitBucketHistogramAggregation",{enumerable:!0,get:function(){return c.ExplicitBucketHistogramAggregation}}),Object.defineProperty(e,"ExponentialHistogramAggregation",{enumerable:!0,get:function(){return c.ExponentialHistogramAggregation}}),Object.defineProperty(e,"DropAggregation",{enumerable:!0,get:function(){return c.DropAggregation}}),Object.defineProperty(e,"HistogramAggregation",{enumerable:!0,get:function(){return c.HistogramAggregation}}),Object.defineProperty(e,"LastValueAggregation",{enumerable:!0,get:function(){return c.LastValueAggregation}}),Object.defineProperty(e,"SumAggregation",{enumerable:!0,get:function(){return c.SumAggregation}}),Object.defineProperty(e,"Aggregation",{enumerable:!0,get:function(){return c.Aggregation}});var l=op();Object.defineProperty(e,"View",{enumerable:!0,get:function(){return l.View}});var d=fe();Object.defineProperty(e,"TimeoutError",{enumerable:!0,get:function(){return d.TimeoutError}})}),ap=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.AbstractAsyncHooksContextManager=void 0;var t=z("events"),r=["addListener","on","once","prependListener","prependOnceListener"];e.AbstractAsyncHooksContextManager=class{constructor(){this._kOtListeners=Symbol("OtListeners"),this._wrapped=!1}bind(e,r){return r instanceof t.EventEmitter?this._bindEventEmitter(e,r):"function"==typeof r?this._bindFunction(e,r):r}_bindFunction(e,t){let r=this,n=function(...n){return r.with(e,()=>t.apply(this,n))};return Object.defineProperty(n,"length",{enumerable:!1,configurable:!0,writable:!1,value:t.length}),n}_bindEventEmitter(e,t){return void 0!==this._getPatchMap(t)||(this._createPatchMap(t),r.forEach(r=>{void 0!==t[r]&&(t[r]=this._patchAddListener(t,t[r],e))}),"function"==typeof t.removeListener&&(t.removeListener=this._patchRemoveListener(t,t.removeListener)),"function"==typeof t.off&&(t.off=this._patchRemoveListener(t,t.off)),"function"==typeof t.removeAllListeners&&(t.removeAllListeners=this._patchRemoveAllListeners(t,t.removeAllListeners))),t}_patchRemoveListener(e,t){let r=this;return function(n,i){var o;let s=null==(o=r._getPatchMap(e))?void 0:o[n];if(void 0===s)return t.call(this,n,i);let a=s.get(i);return t.call(this,n,a||i)}}_patchRemoveAllListeners(e,t){let r=this;return function(n){let i=r._getPatchMap(e);return void 0!==i&&(0==arguments.length?r._createPatchMap(e):void 0!==i[n]&&delete i[n]),t.apply(this,arguments)}}_patchAddListener(e,t,r){let n=this;return function(i,o){if(n._wrapped)return t.call(this,i,o);let s=n._getPatchMap(e);void 0===s&&(s=n._createPatchMap(e));let a=s[i];void 0===a&&(a=new WeakMap,s[i]=a);let u=n.bind(r,o);a.set(o,u),n._wrapped=!0;try{return t.call(this,i,u)}finally{n._wrapped=!1}}}_createPatchMap(e){let t=Object.create(null);return e[this._kOtListeners]=t,t}_getPatchMap(e){return e[this._kOtListeners]}}}),up=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.AsyncLocalStorageContextManager=void 0;var t=(h(),m(d)),r=z("async_hooks"),n=ap();e.AsyncLocalStorageContextManager=class extends n.AbstractAsyncHooksContextManager{constructor(){super(),this._asyncLocalStorage=new r.AsyncLocalStorage}active(){var e;return null!=(e=this._asyncLocalStorage.getStore())?e:t.ROOT_CONTEXT}with(e,t,r,...n){let i=null==r?t:t.bind(r);return this._asyncLocalStorage.run(e,i,...n)}enable(){return this}disable(){return this._asyncLocalStorage.disable(),this}}}),Ur=l(e=>{function t(e,t){return{key:e,value:r(t)}}function r(e){let n=typeof e;return"string"===n?{stringValue:e}:"number"===n?Number.isInteger(e)?{intValue:e}:{doubleValue:e}:"boolean"===n?{boolValue:e}:e instanceof Uint8Array?{bytesValue:e}:Array.isArray(e)?{arrayValue:{values:e.map(r)}}:"object"===n&&null!=e?{kvlistValue:{values:Object.entries(e).map(([e,r])=>t(e,r))}}:{}}Object.defineProperty(e,"__esModule",{value:!0}),e.toAnyValue=e.toKeyValue=e.toAttributes=void 0,e.toAttributes=function(e){return Object.keys(e).map(r=>t(r,e[r]))},e.toKeyValue=t,e.toAnyValue=r}),Op=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.toOtlpSpanEvent=e.toOtlpLink=e.sdkSpanToOtlpSpan=void 0;var t=Ur();function r(e,r){var n;return{attributes:e.attributes?(0,t.toAttributes)(e.attributes):[],spanId:r.encodeSpanContext(e.context.spanId),traceId:r.encodeSpanContext(e.context.traceId),traceState:null==(n=e.context.traceState)?void 0:n.serialize(),droppedAttributesCount:e.droppedAttributesCount||0}}function n(e,r){return{attributes:e.attributes?(0,t.toAttributes)(e.attributes):[],name:e.name,timeUnixNano:r.encodeHrTime(e.time),droppedAttributesCount:e.droppedAttributesCount||0}}e.sdkSpanToOtlpSpan=function(e,i){var o;let s=e.spanContext(),a=e.status;return{traceId:i.encodeSpanContext(s.traceId),spanId:i.encodeSpanContext(s.spanId),parentSpanId:i.encodeOptionalSpanContext(e.parentSpanId),traceState:null==(o=s.traceState)?void 0:o.serialize(),name:e.name,kind:null==e.kind?0:e.kind+1,startTimeUnixNano:i.encodeHrTime(e.startTime),endTimeUnixNano:i.encodeHrTime(e.endTime),attributes:(0,t.toAttributes)(e.attributes),droppedAttributesCount:e.droppedAttributesCount,events:e.events.map(e=>n(e,i)),droppedEventsCount:e.droppedEventsCount,status:{code:a.code,message:a.message},links:e.links.map(e=>r(e,i)),droppedLinksCount:e.droppedLinksCount}},e.toOtlpLink=r,e.toOtlpSpanEvent=n}),Br=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.getOtlpEncoder=e.encodeAsString=e.encodeAsLongBits=e.toLongBits=e.hrTimeToNanos=void 0;var t=A(),r=BigInt(1e9);function n(e){return BigInt(e[0])*r+BigInt(e[1])}function i(e){return{low:Number(BigInt.asUintN(32,e)),high:Number(BigInt.asUintN(32,e>>BigInt(32)))}}function o(e){return i(n(e))}function s(e){return n(e).toString()}e.hrTimeToNanos=n,e.toLongBits=i,e.encodeAsLongBits=o,e.encodeAsString=s;var a="u">typeof BigInt?s:t.hrTimeToNanoseconds;function u(e){return e}function c(e){if(void 0!==e)return(0,t.hexToBase64)(e)}var l={encodeHrTime:o,encodeSpanContext:t.hexToBase64,encodeOptionalSpanContext:c};e.getOtlpEncoder=function(e){var r,n;if(void 0===e)return l;let i=null==(r=e.useLongBits)||r,s=null!=(n=e.useHex)&&n;return{encodeHrTime:i?o:a,encodeSpanContext:s?u:t.hexToBase64,encodeOptionalSpanContext:s?u:c}}}),ys=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.createExportTraceServiceRequest=void 0;var t=Ur(),r=Op(),n=Br();function i(e){let t=new Map;for(let r of e){let e=t.get(r.resource);e||(e=new Map,t.set(r.resource,e));let n=`${r.instrumentationLibrary.name}@${r.instrumentationLibrary.version||""}:${r.instrumentationLibrary.schemaUrl||""}`,i=e.get(n);i||(i=[],e.set(n,i)),i.push(r)}return t}function o(e,n){let o=i(e),s=[],a=o.entries(),u=a.next();for(;!u.done;){let[e,i]=u.value,o=[],c=i.values(),l=c.next();for(;!l.done;){let e=l.value;if(e.length>0){let{name:t,version:i,schemaUrl:s}=e[0].instrumentationLibrary,a=e.map(e=>(0,r.sdkSpanToOtlpSpan)(e,n));o.push({scope:{name:t,version:i},spans:a,schemaUrl:s})}l=c.next()}let d={resource:{attributes:(0,t.toAttributes)(e.attributes),droppedAttributesCount:0},scopeSpans:o,schemaUrl:void 0};s.push(d),u=a.next()}return s}e.createExportTraceServiceRequest=function(e,t){return{resourceSpans:o(e,(0,n.getOtlpEncoder)(t))}}}),Lp=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.parseRetryAfterToMills=e.isExportRetryable=e.invalidTimeout=e.configureExporterTimeout=e.appendRootPathToUrlIfNeeded=e.appendResourcePathToUrl=e.parseHeaders=e.DEFAULT_EXPORT_BACKOFF_MULTIPLIER=e.DEFAULT_EXPORT_MAX_BACKOFF=e.DEFAULT_EXPORT_INITIAL_BACKOFF=e.DEFAULT_EXPORT_MAX_ATTEMPTS=void 0;var t=(h(),m(d)),r=A(),n=1e4;function i(){var e;let t=Number(null!=(e=(0,r.getEnv)().OTEL_EXPORTER_OTLP_TRACES_TIMEOUT)?e:(0,r.getEnv)().OTEL_EXPORTER_OTLP_TIMEOUT);return t<=0?o(t,n):t}function o(e,r){return t.diag.warn("Timeout must be greater than 0",e),r}e.DEFAULT_EXPORT_MAX_ATTEMPTS=5,e.DEFAULT_EXPORT_INITIAL_BACKOFF=1e3,e.DEFAULT_EXPORT_MAX_BACKOFF=5e3,e.DEFAULT_EXPORT_BACKOFF_MULTIPLIER=1.5,e.parseHeaders=function(e={}){let r={};return Object.entries(e).forEach(([e,n])=>{"u">typeof n?r[e]=String(n):t.diag.warn(`Header "${e}" has wrong value and will be ignored`)}),r},e.appendResourcePathToUrl=function(e,t){return e.endsWith("/")||(e+="/"),e+t},e.appendRootPathToUrlIfNeeded=function(e){try{let t=new URL(e);return""===t.pathname&&(t.pathname=t.pathname+"/"),t.toString()}catch{return t.diag.warn(`Could not parse export URL: '${e}'`),e}},e.configureExporterTimeout=function(e){return"number"==typeof e?e<=0?o(e,n):e:i()},e.invalidTimeout=o,e.isExportRetryable=function(e){return[429,502,503,504].includes(e)},e.parseRetryAfterToMills=function(e){if(null==e)return -1;let t=Number.parseInt(e,10);if(Number.isInteger(t))return t>0?1e3*t:-1;let r=new Date(e).getTime()-Date.now();return r>=0?r:0}}),Np=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.OTLPExporterBase=void 0;var t=(h(),m(d)),r=A(),n=Lp();e.OTLPExporterBase=class{constructor(e={}){this._sendingPromises=[],this.url=this.getDefaultUrl(e),"string"==typeof e.hostname&&(this.hostname=e.hostname),this.shutdown=this.shutdown.bind(this),this._shutdownOnce=new r.BindOnceFuture(this._shutdown,this),this._concurrencyLimit="number"==typeof e.concurrencyLimit?e.concurrencyLimit:30,this.timeoutMillis=(0,n.configureExporterTimeout)(e.timeoutMillis),this.onInit(e)}export(e,t){return this._shutdownOnce.isCalled?void t({code:r.ExportResultCode.FAILED,error:Error("Exporter has been shutdown")}):this._sendingPromises.length>=this._concurrencyLimit?void t({code:r.ExportResultCode.FAILED,error:Error("Concurrent export limit reached")}):void this._export(e).then(()=>{t({code:r.ExportResultCode.SUCCESS})}).catch(e=>{t({code:r.ExportResultCode.FAILED,error:e})})}_export(e){return new Promise((r,n)=>{try{t.diag.debug("items to be sent",e),this.send(e,r,n)}catch(e){n(e)}})}shutdown(){return this._shutdownOnce.call()}forceFlush(){return Promise.all(this._sendingPromises).then(()=>{})}_shutdown(){return t.diag.debug("shutdown started"),this.onShutdown(),this.forceFlush()}}}),Dp=l((e,t)=>{t.exports=function e(e,t){for(var r=Array(arguments.length-1),n=0,i=2,o=!0;i<arguments.length;)r[n++]=arguments[i++];return new Promise(function(i,s){r[n]=function(e){if(o)if(o=!1,e)s(e);else{for(var t=Array(arguments.length-1),r=0;r<t.length;)t[r++]=arguments[r];i.apply(null,t)}};try{e.apply(t||null,r)}catch(e){o&&(o=!1,s(e))}})}}),Gp=l(e=>{var t,r=e;r.length=function(e){var t=e.length;if(!t)return 0;for(var r=0;--t%4>1&&"="===e.charAt(t);)++r;return Math.ceil(3*e.length)/4-r};var n=Array(64),i=Array(123);for(t=0;t<64;)i[n[t]=t<26?t+65:t<52?t+71:t<62?t-4:t-59|43]=t++;r.encode=function(e,t,r){for(var i,o=null,s=[],a=0,u=0;t<r;){var c=e[t++];switch(u){case 0:s[a++]=n[c>>2],i=(3&c)<<4,u=1;break;case 1:s[a++]=n[i|c>>4],i=(15&c)<<2,u=2;break;case 2:s[a++]=n[i|c>>6],s[a++]=n[63&c],u=0}a>8191&&((o||(o=[])).push(String.fromCharCode.apply(String,s)),a=0)}return u&&(s[a++]=n[i],s[a++]=61,1===u&&(s[a++]=61)),o?(a&&o.push(String.fromCharCode.apply(String,s.slice(0,a))),o.join("")):String.fromCharCode.apply(String,s.slice(0,a))};var o="invalid encoding";r.decode=function(e,t,r){for(var n,s=r,a=0,u=0;u<e.length;){var c=e.charCodeAt(u++);if(61===c&&a>1)break;if(void 0===(c=i[c]))throw Error(o);switch(a){case 0:n=c,a=1;break;case 1:t[r++]=n<<2|(48&c)>>4,n=c,a=2;break;case 2:t[r++]=(15&n)<<4|(60&c)>>2,n=c,a=3;break;case 3:t[r++]=(3&n)<<6|c,a=0}}if(1===a)throw Error(o);return r-s},r.test=function(e){return/^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/.test(e)}}),Hp=l((e,t)=>{function r(){this._listeners={}}t.exports=r,r.prototype.on=function(e,t,r){return(this._listeners[e]||(this._listeners[e]=[])).push({fn:t,ctx:r||this}),this},r.prototype.off=function(e,t){if(void 0===e)this._listeners={};else if(void 0===t)this._listeners[e]=[];else for(var r=this._listeners[e],n=0;n<r.length;)r[n].fn===t?r.splice(n,1):++n;return this},r.prototype.emit=function(e){var t=this._listeners[e];if(t){for(var r=[],n=1;n<arguments.length;)r.push(arguments[n++]);for(n=0;n<t.length;)t[n].fn.apply(t[n++].ctx,r)}return this}}),Kp=l((e,t)=>{function r(e){return"u">typeof Float32Array?function(){var t=new Float32Array([-0]),r=new Uint8Array(t.buffer),n=128===r[3];function i(e,n,i){t[0]=e,n[i]=r[0],n[i+1]=r[1],n[i+2]=r[2],n[i+3]=r[3]}function o(e,n,i){t[0]=e,n[i]=r[3],n[i+1]=r[2],n[i+2]=r[1],n[i+3]=r[0]}function s(e,n){return r[0]=e[n],r[1]=e[n+1],r[2]=e[n+2],r[3]=e[n+3],t[0]}function a(e,n){return r[3]=e[n],r[2]=e[n+1],r[1]=e[n+2],r[0]=e[n+3],t[0]}e.writeFloatLE=n?i:o,e.writeFloatBE=n?o:i,e.readFloatLE=n?s:a,e.readFloatBE=n?a:s}():function(){function t(e,t,r,n){var i=+(t<0);if(i&&(t=-t),0===t)e(1/t>0?0:0x80000000,r,n);else if(isNaN(t))e(0x7fc00000,r,n);else if(t>34028234663852886e22)e((i<<31|0x7f800000)>>>0,r,n);else if(t<11754943508222875e-54)e((i<<31|Math.round(t/1401298464324817e-60))>>>0,r,n);else{var o=Math.floor(Math.log(t)/Math.LN2),s=8388607&Math.round(t*Math.pow(2,-o)*8388608);e((i<<31|o+127<<23|s)>>>0,r,n)}}function r(e,t,r){var n=e(t,r),i=(n>>31)*2+1,o=n>>>23&255,s=8388607&n;return 255===o?s?NaN:1/0*i:0===o?1401298464324817e-60*i*s:i*Math.pow(2,o-150)*(s+8388608)}e.writeFloatLE=t.bind(null,n),e.writeFloatBE=t.bind(null,i),e.readFloatLE=r.bind(null,o),e.readFloatBE=r.bind(null,s)}(),"u">typeof Float64Array?function(){var t=new Float64Array([-0]),r=new Uint8Array(t.buffer),n=128===r[7];function i(e,n,i){t[0]=e,n[i]=r[0],n[i+1]=r[1],n[i+2]=r[2],n[i+3]=r[3],n[i+4]=r[4],n[i+5]=r[5],n[i+6]=r[6],n[i+7]=r[7]}function o(e,n,i){t[0]=e,n[i]=r[7],n[i+1]=r[6],n[i+2]=r[5],n[i+3]=r[4],n[i+4]=r[3],n[i+5]=r[2],n[i+6]=r[1],n[i+7]=r[0]}function s(e,n){return r[0]=e[n],r[1]=e[n+1],r[2]=e[n+2],r[3]=e[n+3],r[4]=e[n+4],r[5]=e[n+5],r[6]=e[n+6],r[7]=e[n+7],t[0]}function a(e,n){return r[7]=e[n],r[6]=e[n+1],r[5]=e[n+2],r[4]=e[n+3],r[3]=e[n+4],r[2]=e[n+5],r[1]=e[n+6],r[0]=e[n+7],t[0]}e.writeDoubleLE=n?i:o,e.writeDoubleBE=n?o:i,e.readDoubleLE=n?s:a,e.readDoubleBE=n?a:s}():function(){function t(e,t,r,n,i,o){var s,a=+(n<0);if(a&&(n=-n),0===n)e(0,i,o+t),e(1/n>0?0:0x80000000,i,o+r);else if(isNaN(n))e(0,i,o+t),e(0x7ff80000,i,o+r);else if(n>17976931348623157e292)e(0,i,o+t),e((a<<31|0x7ff00000)>>>0,i,o+r);else if(n<22250738585072014e-324)e((s=n/5e-324)>>>0,i,o+t),e((a<<31|s/0x100000000)>>>0,i,o+r);else{var u=Math.floor(Math.log(n)/Math.LN2);1024===u&&(u=1023),e(0x10000000000000*(s=n*Math.pow(2,-u))>>>0,i,o+t),e((a<<31|u+1023<<20|1048576*s&1048575)>>>0,i,o+r)}}function r(e,t,r,n,i){var o=e(n,i+t),s=e(n,i+r),a=(s>>31)*2+1,u=s>>>20&2047,c=0x100000000*(1048575&s)+o;return 2047===u?c?NaN:1/0*a:0===u?5e-324*a*c:a*Math.pow(2,u-1075)*(c+0x10000000000000)}e.writeDoubleLE=t.bind(null,n,0,4),e.writeDoubleBE=t.bind(null,i,4,0),e.readDoubleLE=r.bind(null,o,0,4),e.readDoubleBE=r.bind(null,s,4,0)}(),e}function n(e,t,r){t[r]=255&e,t[r+1]=e>>>8&255,t[r+2]=e>>>16&255,t[r+3]=e>>>24}function i(e,t,r){t[r]=e>>>24,t[r+1]=e>>>16&255,t[r+2]=e>>>8&255,t[r+3]=255&e}function o(e,t){return(e[t]|e[t+1]<<8|e[t+2]<<16|e[t+3]<<24)>>>0}function s(e,t){return(e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3])>>>0}t.exports=r(r)}),zp=l((exports,module)=>{function inquire(moduleName){try{var mod=eval("quire".replace(/^/,"re"))(moduleName);if(mod&&(mod.length||Object.keys(mod).length))return mod}catch(r){}return null}module.exports=inquire}),Qp=l(e=>{var t=e;t.length=function(e){for(var t=0,r=0,n=0;n<e.length;++n)(r=e.charCodeAt(n))<128?t+=1:r<2048?t+=2:(64512&r)==55296&&(64512&e.charCodeAt(n+1))==56320?(++n,t+=4):t+=3;return t},t.read=function(e,t,r){if(r-t<1)return"";for(var n,i=null,o=[],s=0;t<r;)(n=e[t++])<128?o[s++]=n:n>191&&n<224?o[s++]=(31&n)<<6|63&e[t++]:n>239&&n<365?(n=((7&n)<<18|(63&e[t++])<<12|(63&e[t++])<<6|63&e[t++])-65536,o[s++]=55296+(n>>10),o[s++]=56320+(1023&n)):o[s++]=(15&n)<<12|(63&e[t++])<<6|63&e[t++],s>8191&&((i||(i=[])).push(String.fromCharCode.apply(String,o)),s=0);return i?(s&&i.push(String.fromCharCode.apply(String,o.slice(0,s))),i.join("")):String.fromCharCode.apply(String,o.slice(0,s))},t.write=function(e,t,r){for(var n,i,o=r,s=0;s<e.length;++s)(n=e.charCodeAt(s))<128?t[r++]=n:(n<2048?t[r++]=n>>6|192:((64512&n)==55296&&(64512&(i=e.charCodeAt(s+1)))==56320?(n=65536+((1023&n)<<10)+(1023&i),++s,t[r++]=n>>18|240,t[r++]=n>>12&63|128):t[r++]=n>>12|224,t[r++]=n>>6&63|128),t[r++]=63&n|128);return r-o}}),Jp=l((e,t)=>{t.exports=function e(e,t,r){var n=r||8192,i=n>>>1,o=null,s=n;return function(r){if(r<1||r>i)return e(r);s+r>n&&(o=e(n),s=0);var a=t.call(o,s,s+=r);return 7&s&&(s=(7|s)+1),a}}}),th=l((e,t)=>{t.exports=n;var r=qe();function n(e,t){this.lo=e>>>0,this.hi=t>>>0}var i=n.zero=new n(0,0);i.toNumber=function(){return 0},i.zzEncode=i.zzDecode=function(){return this},i.length=function(){return 1};var o=n.zeroHash="\0\0\0\0\0\0\0\0";n.fromNumber=function(e){if(0===e)return i;var t=e<0;t&&(e=-e);var r=e>>>0,o=(e-r)/0x100000000>>>0;return t&&(o=~o>>>0,r=~r>>>0,++r>0xffffffff&&(r=0,++o>0xffffffff&&(o=0))),new n(r,o)},n.from=function(e){if("number"==typeof e)return n.fromNumber(e);if(r.isString(e))if(!r.Long)return n.fromNumber(parseInt(e,10));else e=r.Long.fromString(e);return e.low||e.high?new n(e.low>>>0,e.high>>>0):i},n.prototype.toNumber=function(e){if(!e&&this.hi>>>31){var t=~this.lo+1>>>0,r=~this.hi>>>0;return t||(r=r+1>>>0),-(t+0x100000000*r)}return this.lo+0x100000000*this.hi},n.prototype.toLong=function(e){return r.Long?new r.Long(0|this.lo,0|this.hi,!!e):{low:0|this.lo,high:0|this.hi,unsigned:!!e}};var s=String.prototype.charCodeAt;n.fromHash=function(e){return e===o?i:new n((s.call(e,0)|s.call(e,1)<<8|s.call(e,2)<<16|s.call(e,3)<<24)>>>0,(s.call(e,4)|s.call(e,5)<<8|s.call(e,6)<<16|s.call(e,7)<<24)>>>0)},n.prototype.toHash=function(){return String.fromCharCode(255&this.lo,this.lo>>>8&255,this.lo>>>16&255,this.lo>>>24,255&this.hi,this.hi>>>8&255,this.hi>>>16&255,this.hi>>>24)},n.prototype.zzEncode=function(){var e=this.hi>>31;return this.hi=((this.hi<<1|this.lo>>>31)^e)>>>0,this.lo=(this.lo<<1^e)>>>0,this},n.prototype.zzDecode=function(){var e=-(1&this.lo);return this.lo=((this.lo>>>1|this.hi<<31)^e)>>>0,this.hi=(this.hi>>>1^e)>>>0,this},n.prototype.length=function(){var e=this.lo,t=(this.lo>>>28|this.hi<<4)>>>0,r=this.hi>>>24;return 0===r?0===t?e<16384?e<128?1:2:e<2097152?3:4:t<16384?t<128?5:6:t<2097152?7:8:r<128?9:10}}),qe=l(e=>{var t=e;function r(e,t,r){for(var n=Object.keys(t),i=0;i<n.length;++i)void 0!==e[n[i]]&&r||(e[n[i]]=t[n[i]]);return e}function n(e){function t(e,n){if(!(this instanceof t))return new t(e,n);Object.defineProperty(this,"message",{get:function(){return e}}),Error.captureStackTrace?Error.captureStackTrace(this,t):Object.defineProperty(this,"stack",{value:Error().stack||""}),n&&r(this,n)}return t.prototype=Object.create(Error.prototype,{constructor:{value:t,writable:!0,enumerable:!1,configurable:!0},name:{get:function(){return e},set:void 0,enumerable:!1,configurable:!0},toString:{value:function(){return this.name+": "+this.message},writable:!0,enumerable:!1,configurable:!0}}),t}t.asPromise=Dp(),t.base64=Gp(),t.EventEmitter=Hp(),t.float=Kp(),t.inquire=zp(),t.utf8=Qp(),t.pool=Jp(),t.LongBits=th(),t.isNode=!!("u">typeof global&&global&&global.process&&global.process.versions&&global.process.versions.node),t.global=t.isNode&&global||"u">typeof window&&window||"u">typeof self&&self||e,t.emptyArray=Object.freeze?Object.freeze([]):[],t.emptyObject=Object.freeze?Object.freeze({}):{},t.isInteger=Number.isInteger||function(e){return"number"==typeof e&&isFinite(e)&&Math.floor(e)===e},t.isString=function(e){return"string"==typeof e||e instanceof String},t.isObject=function(e){return e&&"object"==typeof e},t.isset=t.isSet=function(e,t){var r=e[t];return!!(null!=r&&e.hasOwnProperty(t))&&("object"!=typeof r||(Array.isArray(r)?r.length:Object.keys(r).length)>0)},t.Buffer=function(){try{var e=t.inquire("buffer").Buffer;return e.prototype.utf8Write?e:null}catch{return null}}(),t._Buffer_from=null,t._Buffer_allocUnsafe=null,t.newBuffer=function(e){return"number"==typeof e?t.Buffer?t._Buffer_allocUnsafe(e):new t.Array(e):t.Buffer?t._Buffer_from(e):typeof Uint8Array>"u"?e:new Uint8Array(e)},t.Array="u">typeof Uint8Array?Uint8Array:Array,t.Long=t.global.dcodeIO&&t.global.dcodeIO.Long||t.global.Long||t.inquire("long"),t.key2Re=/^true|false|0|1$/,t.key32Re=/^-?(?:0|[1-9][0-9]*)$/,t.key64Re=/^(?:[\\x00-\\xff]{8}|-?(?:0|[1-9][0-9]*))$/,t.longToHash=function(e){return e?t.LongBits.from(e).toHash():t.LongBits.zeroHash},t.longFromHash=function(e,r){var n=t.LongBits.fromHash(e);return t.Long?t.Long.fromBits(n.lo,n.hi,r):n.toNumber(!!r)},t.merge=r,t.lcFirst=function(e){return e.charAt(0).toLowerCase()+e.substring(1)},t.newError=n,t.ProtocolError=n("ProtocolError"),t.oneOfGetter=function(e){for(var t={},r=0;r<e.length;++r)t[e[r]]=1;return function(){for(var e=Object.keys(this),r=e.length-1;r>-1;--r)if(1===t[e[r]]&&void 0!==this[e[r]]&&null!==this[e[r]])return e[r]}},t.oneOfSetter=function(e){return function(t){for(var r=0;r<e.length;++r)e[r]!==t&&delete this[e[r]]}},t.toJSONOptions={longs:String,enums:String,bytes:String,json:!0},t._configure=function(){var e=t.Buffer;if(!e){t._Buffer_from=t._Buffer_allocUnsafe=null;return}t._Buffer_from=e.from!==Uint8Array.from&&e.from||function(t,r){return new e(t,r)},t._Buffer_allocUnsafe=e.allocUnsafe||function(t){return new e(t)}}}),rc=l((e,t)=>{t.exports=l;var r,n=qe(),i=n.LongBits,o=n.base64,s=n.utf8;function a(e,t,r){this.fn=e,this.len=t,this.next=void 0,this.val=r}function u(){}function c(e){this.head=e.head,this.tail=e.tail,this.len=e.len,this.next=e.states}function l(){this.len=0,this.head=new a(u,0,0),this.tail=this.head,this.states=null}var d=function(){return n.Buffer?function(){return(l.create=function(){return new r})()}:function(){return new l}};function p(e,t,r){t[r]=255&e}function _(e,t,r){for(;e>127;)t[r++]=127&e|128,e>>>=7;t[r]=e}function h(e,t){this.len=e,this.next=void 0,this.val=t}function g(e,t,r){for(;e.hi;)t[r++]=127&e.lo|128,e.lo=(e.lo>>>7|e.hi<<25)>>>0,e.hi>>>=7;for(;e.lo>127;)t[r++]=127&e.lo|128,e.lo=e.lo>>>7;t[r++]=e.lo}function f(e,t,r){t[r]=255&e,t[r+1]=e>>>8&255,t[r+2]=e>>>16&255,t[r+3]=e>>>24}l.create=d(),l.alloc=function(e){return new n.Array(e)},n.Array!==Array&&(l.alloc=n.pool(l.alloc,n.Array.prototype.subarray)),l.prototype._push=function(e,t,r){return this.tail=this.tail.next=new a(e,t,r),this.len+=t,this},h.prototype=Object.create(a.prototype),h.prototype.fn=_,l.prototype.uint32=function(e){return this.len+=(this.tail=this.tail.next=new h((e>>>=0)<128?1:e<16384?2:e<2097152?3:e<0x10000000?4:5,e)).len,this},l.prototype.int32=function(e){return e<0?this._push(g,10,i.fromNumber(e)):this.uint32(e)},l.prototype.sint32=function(e){return this.uint32((e<<1^e>>31)>>>0)},l.prototype.uint64=function(e){var t=i.from(e);return this._push(g,t.length(),t)},l.prototype.int64=l.prototype.uint64,l.prototype.sint64=function(e){var t=i.from(e).zzEncode();return this._push(g,t.length(),t)},l.prototype.bool=function(e){return this._push(p,1,+!!e)},l.prototype.fixed32=function(e){return this._push(f,4,e>>>0)},l.prototype.sfixed32=l.prototype.fixed32,l.prototype.fixed64=function(e){var t=i.from(e);return this._push(f,4,t.lo)._push(f,4,t.hi)},l.prototype.sfixed64=l.prototype.fixed64,l.prototype.float=function(e){return this._push(n.float.writeFloatLE,4,e)},l.prototype.double=function(e){return this._push(n.float.writeDoubleLE,8,e)};var E=n.Array.prototype.set?function(e,t,r){t.set(e,r)}:function(e,t,r){for(var n=0;n<e.length;++n)t[r+n]=e[n]};l.prototype.bytes=function(e){var t=e.length>>>0;if(!t)return this._push(p,1,0);if(n.isString(e)){var r=l.alloc(t=o.length(e));o.decode(e,r,0),e=r}return this.uint32(t)._push(E,t,e)},l.prototype.string=function(e){var t=s.length(e);return t?this.uint32(t)._push(s.write,t,e):this._push(p,1,0)},l.prototype.fork=function(){return this.states=new c(this),this.head=this.tail=new a(u,0,0),this.len=0,this},l.prototype.reset=function(){return this.states?(this.head=this.states.head,this.tail=this.states.tail,this.len=this.states.len,this.states=this.states.next):(this.head=this.tail=new a(u,0,0),this.len=0),this},l.prototype.ldelim=function(){var e=this.head,t=this.tail,r=this.len;return this.reset().uint32(r),r&&(this.tail.next=e.next,this.tail=t,this.len+=r),this},l.prototype.finish=function(){for(var e=this.head.next,t=this.constructor.alloc(this.len),r=0;e;)e.fn(e.val,t,r),r+=e.len,e=e.next;return t},l._configure=function(e){r=e,l.create=d(),r._configure()}}),lh=l((e,t)=>{t.exports=i;var r=rc();(i.prototype=Object.create(r.prototype)).constructor=i;var n=qe();function i(){r.call(this)}function o(e,t,r){e.length<40?n.utf8.write(e,t,r):t.utf8Write?t.utf8Write(e,r):t.write(e,r)}i._configure=function(){i.alloc=n._Buffer_allocUnsafe,i.writeBytesBuffer=n.Buffer&&n.Buffer.prototype instanceof Uint8Array&&"set"===n.Buffer.prototype.set.name?function(e,t,r){t.set(e,r)}:function(e,t,r){if(e.copy)e.copy(t,r,0,e.length);else for(var n=0;n<e.length;)t[r++]=e[n++]}},i.prototype.bytes=function(e){n.isString(e)&&(e=n._Buffer_from(e,"base64"));var t=e.length>>>0;return this.uint32(t),t&&this._push(i.writeBytesBuffer,t,e),this},i.prototype.string=function(e){var t=n.Buffer.byteLength(e);return this.uint32(t),t&&this._push(o,t,e),this},i._configure()}),sc=l((e,t)=>{t.exports=a;var r,n=qe(),i=n.LongBits,o=n.utf8;function s(e,t){return RangeError("index out of range: "+e.pos+" + "+(t||1)+" > "+e.len)}function a(e){this.buf=e,this.pos=0,this.len=e.length}var u="u">typeof Uint8Array?function(e){if(e instanceof Uint8Array||Array.isArray(e))return new a(e);throw Error("illegal buffer")}:function(e){if(Array.isArray(e))return new a(e);throw Error("illegal buffer")},c=function(){return n.Buffer?function(e){return(a.create=function(e){return n.Buffer.isBuffer(e)?new r(e):u(e)})(e)}:u};function l(){var e=new i(0,0),t=0;if(this.len-this.pos>4){for(;t<4;++t)if(e.lo=(e.lo|(127&this.buf[this.pos])<<7*t)>>>0,this.buf[this.pos++]<128)return e;if(e.lo=(e.lo|(127&this.buf[this.pos])<<28)>>>0,e.hi=(e.hi|(127&this.buf[this.pos])>>4)>>>0,this.buf[this.pos++]<128)return e;t=0}else{for(;t<3;++t){if(this.pos>=this.len)throw s(this);if(e.lo=(e.lo|(127&this.buf[this.pos])<<7*t)>>>0,this.buf[this.pos++]<128)return e}return e.lo=(e.lo|(127&this.buf[this.pos++])<<7*t)>>>0,e}if(this.len-this.pos>4){for(;t<5;++t)if(e.hi=(e.hi|(127&this.buf[this.pos])<<7*t+3)>>>0,this.buf[this.pos++]<128)return e}else for(;t<5;++t){if(this.pos>=this.len)throw s(this);if(e.hi=(e.hi|(127&this.buf[this.pos])<<7*t+3)>>>0,this.buf[this.pos++]<128)return e}throw Error("invalid varint encoding")}function d(e,t){return(e[t-4]|e[t-3]<<8|e[t-2]<<16|e[t-1]<<24)>>>0}function p(){if(this.pos+8>this.len)throw s(this,8);return new i(d(this.buf,this.pos+=4),d(this.buf,this.pos+=4))}a.create=c(),a.prototype._slice=n.Array.prototype.subarray||n.Array.prototype.slice,a.prototype.uint32=function(){var e=0xffffffff;return function(){if(e=(127&this.buf[this.pos])>>>0,this.buf[this.pos++]<128||(e=(e|(127&this.buf[this.pos])<<7)>>>0,this.buf[this.pos++]<128)||(e=(e|(127&this.buf[this.pos])<<14)>>>0,this.buf[this.pos++]<128)||(e=(e|(127&this.buf[this.pos])<<21)>>>0,this.buf[this.pos++]<128)||(e=(e|(15&this.buf[this.pos])<<28)>>>0,this.buf[this.pos++]<128))return e;if((this.pos+=5)>this.len)throw this.pos=this.len,s(this,10);return e}}(),a.prototype.int32=function(){return 0|this.uint32()},a.prototype.sint32=function(){var e=this.uint32();return e>>>1^-(1&e)|0},a.prototype.bool=function(){return 0!==this.uint32()},a.prototype.fixed32=function(){if(this.pos+4>this.len)throw s(this,4);return d(this.buf,this.pos+=4)},a.prototype.sfixed32=function(){if(this.pos+4>this.len)throw s(this,4);return 0|d(this.buf,this.pos+=4)},a.prototype.float=function(){if(this.pos+4>this.len)throw s(this,4);var e=n.float.readFloatLE(this.buf,this.pos);return this.pos+=4,e},a.prototype.double=function(){if(this.pos+8>this.len)throw s(this,4);var e=n.float.readDoubleLE(this.buf,this.pos);return this.pos+=8,e},a.prototype.bytes=function(){var e=this.uint32(),t=this.pos,r=this.pos+e;if(r>this.len)throw s(this,e);if(this.pos+=e,Array.isArray(this.buf))return this.buf.slice(t,r);if(t===r){var i=n.Buffer;return i?i.alloc(0):new this.buf.constructor(0)}return this._slice.call(this.buf,t,r)},a.prototype.string=function(){var e=this.bytes();return o.read(e,0,e.length)},a.prototype.skip=function(e){if("number"==typeof e){if(this.pos+e>this.len)throw s(this,e);this.pos+=e}else do if(this.pos>=this.len)throw s(this);while(128&this.buf[this.pos++]);return this},a.prototype.skipType=function(e){switch(e){case 0:this.skip();break;case 1:this.skip(8);break;case 2:this.skip(this.uint32());break;case 3:for(;4!=(e=7&this.uint32());)this.skipType(e);break;case 5:this.skip(4);break;default:throw Error("invalid wire type "+e+" at offset "+this.pos)}return this},a._configure=function(e){r=e,a.create=c(),r._configure();var t=n.Long?"toLong":"toNumber";n.merge(a.prototype,{int64:function(){return l.call(this)[t](!1)},uint64:function(){return l.call(this)[t](!0)},sint64:function(){return l.call(this).zzDecode()[t](!1)},fixed64:function(){return p.call(this)[t](!0)},sfixed64:function(){return p.call(this)[t](!1)}})}}),Th=l((e,t)=>{t.exports=i;var r=sc();(i.prototype=Object.create(r.prototype)).constructor=i;var n=qe();function i(e){r.call(this,e)}i._configure=function(){n.Buffer&&(i.prototype._slice=n.Buffer.prototype.slice)},i.prototype.string=function(){var e=this.uint32();return this.buf.utf8Slice?this.buf.utf8Slice(this.pos,this.pos=Math.min(this.pos+e,this.len)):this.buf.toString("utf-8",this.pos,this.pos=Math.min(this.pos+e,this.len))},i._configure()}),Ah=l((e,t)=>{t.exports=n;var r=qe();function n(e,t,n){if("function"!=typeof e)throw TypeError("rpcImpl must be a function");r.EventEmitter.call(this),this.rpcImpl=e,this.requestDelimited=!!t,this.responseDelimited=!!n}(n.prototype=Object.create(r.EventEmitter.prototype)).constructor=n,n.prototype.rpcCall=function e(t,n,i,o,s){if(!o)throw TypeError("request must be specified");var a=this;if(!s)return r.asPromise(e,a,t,n,i,o);if(!a.rpcImpl)return void setTimeout(function(){s(Error("already ended"))},0);try{return a.rpcImpl(t,n[a.requestDelimited?"encodeDelimited":"encode"](o).finish(),function(e,r){if(e)return a.emit("error",e,t),s(e);if(null===r)return void a.end(!0);if(!(r instanceof i))try{r=i[a.responseDelimited?"decodeDelimited":"decode"](r)}catch(e){return a.emit("error",e,t),s(e)}return a.emit("data",r,t),s(null,r)})}catch(e){a.emit("error",e,t),setTimeout(function(){s(e)},0);return}},n.prototype.end=function(e){return this.rpcImpl&&(e||this.rpcImpl(null,null,null),this.rpcImpl=null,this.emit("end").off()),this}}),Rh=l(e=>{e.Service=Ah()}),Ph=l((e,t)=>{t.exports={}}),Ih=l(e=>{var t=e;function r(){t.util._configure(),t.Writer._configure(t.BufferWriter),t.Reader._configure(t.BufferReader)}t.build="minimal",t.Writer=rc(),t.BufferWriter=lh(),t.Reader=sc(),t.BufferReader=Th(),t.util=qe(),t.rpc=Rh(),t.roots=Ph(),t.configure=r,r()}),Mh=l((e,t)=>{t.exports=Ih()}),Vh=l(e=>{Object.defineProperty(e,"__esModule",{value:!0})}),jh=l(e=>{Object.defineProperty(e,"__esModule",{value:!0})}),kh=l(e=>{Object.defineProperty(e,"__esModule",{value:!0})}),$h=l(e=>{var t;Object.defineProperty(e,"__esModule",{value:!0}),e.ESpanKind=void 0,function(e){e[e.SPAN_KIND_UNSPECIFIED=0]="SPAN_KIND_UNSPECIFIED",e[e.SPAN_KIND_INTERNAL=1]="SPAN_KIND_INTERNAL",e[e.SPAN_KIND_SERVER=2]="SPAN_KIND_SERVER",e[e.SPAN_KIND_CLIENT=3]="SPAN_KIND_CLIENT",e[e.SPAN_KIND_PRODUCER=4]="SPAN_KIND_PRODUCER",e[e.SPAN_KIND_CONSUMER=5]="SPAN_KIND_CONSUMER"}(t=e.ESpanKind||(e.ESpanKind={}))}),Wh=l(e=>{Object.defineProperty(e,"__esModule",{value:!0})}),Zh=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.toMetric=e.toScopeMetrics=e.toResourceMetrics=void 0;var t=(h(),m(d)),r=wu(),n=Ur(),i=Br();function o(e,t){return Array.from(e.map(e=>({scope:{name:e.scope.name,version:e.scope.version},metrics:e.metrics.map(e=>s(e,t)),schemaUrl:e.scope.schemaUrl})))}function s(e,t){let n={name:e.descriptor.name,description:e.descriptor.description,unit:e.descriptor.unit},i=p(e.aggregationTemporality);switch(e.dataPointType){case r.DataPointType.SUM:n.sum={aggregationTemporality:i,isMonotonic:e.isMonotonic,dataPoints:u(e,t)};break;case r.DataPointType.GAUGE:n.gauge={dataPoints:u(e,t)};break;case r.DataPointType.HISTOGRAM:n.histogram={aggregationTemporality:i,dataPoints:c(e,t)};break;case r.DataPointType.EXPONENTIAL_HISTOGRAM:n.exponentialHistogram={aggregationTemporality:i,dataPoints:l(e,t)}}return n}function a(e,r,i){let o={attributes:(0,n.toAttributes)(e.attributes),startTimeUnixNano:i.encodeHrTime(e.startTime),timeUnixNano:i.encodeHrTime(e.endTime)};switch(r){case t.ValueType.INT:o.asInt=e.value;break;case t.ValueType.DOUBLE:o.asDouble=e.value}return o}function u(e,t){return e.dataPoints.map(r=>a(r,e.descriptor.valueType,t))}function c(e,t){return e.dataPoints.map(e=>{let r=e.value;return{attributes:(0,n.toAttributes)(e.attributes),bucketCounts:r.buckets.counts,explicitBounds:r.buckets.boundaries,count:r.count,sum:r.sum,min:r.min,max:r.max,startTimeUnixNano:t.encodeHrTime(e.startTime),timeUnixNano:t.encodeHrTime(e.endTime)}})}function l(e,t){return e.dataPoints.map(e=>{let r=e.value;return{attributes:(0,n.toAttributes)(e.attributes),count:r.count,min:r.min,max:r.max,sum:r.sum,positive:{offset:r.positive.offset,bucketCounts:r.positive.bucketCounts},negative:{offset:r.negative.offset,bucketCounts:r.negative.bucketCounts},scale:r.scale,zeroCount:r.zeroCount,startTimeUnixNano:t.encodeHrTime(e.startTime),timeUnixNano:t.encodeHrTime(e.endTime)}})}function p(e){switch(e){case r.AggregationTemporality.DELTA:return 1;case r.AggregationTemporality.CUMULATIVE:return 2}}e.toResourceMetrics=function(e,t){let r=(0,i.getOtlpEncoder)(t);return{resource:{attributes:(0,n.toAttributes)(e.resource.attributes),droppedAttributesCount:0},schemaUrl:void 0,scopeMetrics:o(e.scopeMetrics,r)}},e.toScopeMetrics=o,e.toMetric=s}),Jh=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.createExportMetricsServiceRequest=void 0;var t=Zh();e.createExportMetricsServiceRequest=function(e,r){return{resourceMetrics:e.map(e=>(0,t.toResourceMetrics)(e,r))}}}),tf=l(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.toLogAttributes=e.createExportLogsServiceRequest=void 0;var t=Br(),r=Ur();function n(e){let t=new Map;for(let r of e){let{resource:e,instrumentationScope:{name:n,version:i="",schemaUrl:o=""}}=r,s=t.get(e);s||(s=new Map,t.set(e,s));let a=`${n}@${i}:${o}`,u=s.get(a);u||(u=[],s.set(a,u)),u.push(r)}return t}function i(e,t){return Array.from(n(e),([e,n])=>({resource:{attributes:(0,r.toAttributes)(e.attributes),droppedAttributesCount:0},scopeLogs:Array.from(n,([,e])=>{let{instrumentationScope:{name:r,version:n,schemaUrl:i}}=e[0];return{scope:{name:r,version:n},logRecords:e.map(e=>o(e,t)),schemaUrl:i}}),schemaUrl:void 0}))}function o(e,t){var n,i,o;return{timeUnixNano:t.encodeHrTime(e.hrTime),observedTimeUnixNano:t.encodeHrTime(e.hrTimeObserved),severityNumber:e.severityNumber,severityText:e.severityText,body:(0,r.toAnyValue)(e.body),attributes:s(e.attributes),droppedAttributesCount:e.droppedAttributesCount,flags:null==(n=e.spanContext)?void 0:n.traceFlags,traceId:t.encodeOptionalSpanContext(null==(i=e.spanContext)?void 0:i.traceId),spanId:t.encodeOptionalSpanContext(null==(o=e.spanContext)?void 0:o.spanId)}}function s(e){return Object.keys(e).map(t=>(0,r.toKeyValue)(t,e[t]))}e.createExportLogsServiceRequest=function(e,r){return{resourceLogs:i(e,(0,t.getOtlpEncoder)(r))}},e.toLogAttributes=s}),rf=l(e=>{var t=e&&e.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),r=e&&e.__exportStar||function(e,r){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(r,n)||t(r,e,n)};Object.defineProperty(e,"__esModule",{value:!0}),e.createExportLogsServiceRequest=e.createExportMetricsServiceRequest=e.createExportTraceServiceRequest=void 0,r(Vh(),e),r(Br(),e),r(jh(),e),r(kh(),e),r($h(),e),r(Wh(),e);var n=ys();Object.defineProperty(e,"createExportTraceServiceRequest",{enumerable:!0,get:function(){return n.createExportTraceServiceRequest}});var i=Jh();Object.defineProperty(e,"createExportMetricsServiceRequest",{enumerable:!0,get:function(){return i.createExportMetricsServiceRequest}});var o=tf();Object.defineProperty(e,"createExportLogsServiceRequest",{enumerable:!0,get:function(){return o.createExportLogsServiceRequest}})}),C=Y(la(),1);h(),fa();var af=Y(Gd(),1),lr=Y(rt(),1),uf=Y(t_(),1),cf=Y(wu(),1),Vs=Y(Yr(),1),lf=Y(up(),1),He=Y(A(),1),cp="http.method",lp="http.url",As="http.host",dp="http.scheme",Uu="http.status_code",_p="http.user_agent",Bu="http.response_content_length_uncompressed",pp="net.peer.port",hp="net.peer.name",fp="service.name",Ep="service.version";h();var gA=Symbol.for("@vercel/request-context");function me(){return globalThis[gA]?.get()}function Os(e){return Object.fromEntries(Object.entries(e).filter(([e,t])=>void 0!==t))}function mp(e){return e?e.split("::").at(-1):void 0}function gp(e=me(),t){if(!e)return;let r=t?SA(t,e.headers):void 0;return Os({[As]:e.headers.host,[_p]:e.headers["user-agent"],"http.referer":e.headers.referer,"vercel.request_id":mp(e.headers["x-vercel-id"]),"vercel.matched_path":e.headers["x-matched-path"],"vercel.edge_region":process.env.VERCEL_REGION,...r})}var TA={keys:e=>[],get:(e,t)=>e[t.toLocaleLowerCase()]};function SA(e,t){if("function"==typeof e)return e(t,TA);let r={};for(let[n,i]of Object.entries(e)){let e=t[i.toLocaleLowerCase()];void 0!==e&&(r[n]=e)}return r}function er(e){return(e&d.TraceFlags.SAMPLED)!=0}h();var Rs=class{constructor(e,t){this.processors=e,this.attributesFromHeaders=t,this.rootSpanIds=new Map,this.waitSpanEnd=new Map}forceFlush(){return Promise.all(this.processors.map(e=>e.forceFlush().catch(e=>{d.diag.error("@vercel/otel: forceFlush failed:",e)}))).then(()=>{})}shutdown(){return Promise.all(this.processors.map(e=>e.shutdown().catch(()=>{}))).then(()=>{})}onStart(e,t){let{traceId:r,spanId:n,traceFlags:i}=e.spanContext(),o=!e.parentSpanId||!this.rootSpanIds.has(r);if(o?this.rootSpanIds.set(r,{rootSpanId:n,open:[]}):this.rootSpanIds.get(r)?.open.push(e),o&&er(i)){let t=me(),n=gp(t,this.attributesFromHeaders);n&&e.setAttributes(n),t&&t.waitUntil(async()=>{if(this.rootSpanIds.has(r)){let e=new Promise(e=>{this.waitSpanEnd.set(r,e)}),t;await Promise.race([e,new Promise(e=>{t=setTimeout(()=>{this.waitSpanEnd.delete(r),e(void 0)},50)})]),t&&clearTimeout(t)}return this.forceFlush()})}for(let r of this.processors)r.onStart(e,t)}onEnd(e){let{traceId:t,spanId:r,traceFlags:n}=e.spanContext(),i=er(n),o=this.rootSpanIds.get(t),s=o?.rootSpanId===r;if(i){let t=OA(e);t&&Object.assign(e.attributes,t)}if(s){if(this.rootSpanIds.delete(t),o.open.length>0){for(let e of o.open)if(!e.ended&&e.spanContext().spanId!==r)try{e.end()}catch(e){d.diag.error("@vercel/otel: onEnd failed:",e)}}}else if(o)for(let e=0;e<o.open.length;e++)o.open[e]?.spanContext().spanId===r&&o.open.splice(e,1);for(let t of this.processors)t.onEnd(e);if(s){let e=this.waitSpanEnd.get(t);e&&(this.waitSpanEnd.delete(t),e())}}},AA={[d.SpanKind.INTERNAL]:"internal",[d.SpanKind.SERVER]:"server",[d.SpanKind.CLIENT]:"client",[d.SpanKind.PRODUCER]:"producer",[d.SpanKind.CONSUMER]:"consumer"};function OA(e){let{kind:t,attributes:r}=e,{"operation.name":n,"resource.name":i,"span.type":o,"next.span_type":s,"http.method":a,"http.route":u}=r;if(n)return;let c=i??(a&&"string"==typeof a&&u&&"string"==typeof u?`${a} ${u}`:u);if(e.kind===d.SpanKind.SERVER&&a&&u&&"string"==typeof a&&"string"==typeof u)return{"operation.name":"web.request","resource.name":c};let l=e.instrumentationLibrary.name,p=s??o;if(p&&"string"==typeof p){let e=Tp(l,p);return u?{"operation.name":e,"resource.name":c}:{"operation.name":e}}return{"operation.name":Tp(l,t===d.SpanKind.INTERNAL?"":AA[t])}}function Tp(e,t){if(!e)return t;let r=e.replace(/[ @./]/g,"_");return r.startsWith("_")&&(r=r.slice(1)),t?`${r}.${t}`:r}var wp=Y(ys(),1),Cp=Y(Np(),1);h();var tr=class extends Cp.OTLPExporterBase{constructor(e={}){super(e),e.headers&&(this._headers=e.headers)}onShutdown(){d.diag.debug("@vercel/otel/otlp: onShutdown")}onInit(){d.diag.debug("@vercel/otel/otlp: onInit")}send(e,t,r){if(this._shutdownOnce.isCalled)return void d.diag.debug("@vercel/otel/otlp: Shutdown already started. Cannot send objects");let n=this.convert(e),i,o,s;try{({body:i,contentType:o,headers:s}=this.toMessage(n))}catch(e){d.diag.warn("@vercel/otel/otlp: no proto",e);return}let a=fetch(this.url,{method:"POST",body:i,headers:{...this._headers,...s,"Content-Type":o,"User-Agent":"OTel-OTLP-Exporter-JavaScript/0.46.0"},next:{internal:!0}}).then(e=>{d.diag.debug("@vercel/otel/otlp: onSuccess",e.status,e.statusText),t(),e.arrayBuffer().catch(()=>{})}).catch(e=>{d.diag.error("@vercel/otel/otlp: onError",e),r(e)}).finally(()=>{let e=this._sendingPromises.indexOf(a);this._sendingPromises.splice(e,1)});this._sendingPromises.push(a)}getDefaultUrl(e){throw Error("Method not implemented.")}},FA="v1/traces",kA=`http://localhost:4318/${FA}`;function Is(e){return"string"==typeof e.url?e.url:kA}var rr=class{constructor(e={}){this.impl=new Wu(e)}export(e,t){this.impl.export(e,t)}shutdown(){return this.impl.shutdown()}forceFlush(){return this.impl.forceFlush()}},Wu=class extends tr{convert(e){return(0,wp.createExportTraceServiceRequest)(e,{useHex:!0,useLongBits:!1})}toMessage(e){return{body:JSON.stringify(e),contentType:"application/json"}}getDefaultUrl(e){return Is(e)}},xh=Y(ys(),1),Nh=Y(Mh(),1);function Ch(e){let t=new Nh.Writer;return eO(e,t),t.finish()}function eO(e,t){if(null!=e.resourceSpans&&e.resourceSpans.length)for(let r=0;r<e.resourceSpans.length;++r)tO(e.resourceSpans[r],t.uint32(10).fork()).ldelim();return t}function tO(e,t){if(null!=e.resource&&rO(e.resource,t.uint32(10).fork()).ldelim(),null!=e.scopeSpans&&e.scopeSpans.length)for(let r=0;r<e.scopeSpans.length;++r)nO(e.scopeSpans[r],t.uint32(18).fork()).ldelim();return null!=e.schemaUrl&&t.uint32(26).string(e.schemaUrl),t}function rO(e,t){if(null!=e.attributes&&e.attributes.length)for(let r=0;r<e.attributes.length;++r)ir(e.attributes[r],t.uint32(10).fork()).ldelim();return null!=e.droppedAttributesCount&&t.uint32(16).uint32(e.droppedAttributesCount),t}function nO(e,t){if(null!=e.scope&&oO(e.scope,t.uint32(10).fork()).ldelim(),null!=e.spans&&e.spans.length)for(let r=0;r<e.spans.length;++r)aO(e.spans[r],t.uint32(18).fork()).ldelim();return null!=e.schemaUrl&&t.uint32(26).string(e.schemaUrl),t}function ir(e,t){return null!=e.key&&t.uint32(10).string(e.key),null!=e.value&&wh(e.value,t.uint32(18).fork()).ldelim(),t}function wh(e,t){return null!=e.stringValue&&t.uint32(10).string(e.stringValue),null!=e.boolValue&&t.uint32(16).bool(e.boolValue),null!=e.intValue&&t.uint32(24).int64(e.intValue),null!=e.doubleValue&&t.uint32(33).double(e.doubleValue),null!=e.arrayValue&&iO(e.arrayValue,t.uint32(42).fork()).ldelim(),null!=e.kvlistValue&&sO(e.kvlistValue,t.uint32(50).fork()).ldelim(),null!=e.bytesValue&&t.uint32(58).bytes(e.bytesValue),t}function iO(e,t){if(null!=e.values&&e.values.length)for(let r=0;r<e.values.length;++r)wh(e.values[r],t.uint32(10).fork()).ldelim();return t}function sO(e,t){if(null!=e.values&&e.values.length)for(let r=0;r<e.values.length;++r)ir(e.values[r],t.uint32(10).fork()).ldelim();return t}function oO(e,t){if(null!=e.name&&t.uint32(10).string(e.name),null!=e.version&&t.uint32(18).string(e.version),null!=e.attributes&&e.attributes.length)for(let r=0;r<e.attributes.length;++r)ir(e.attributes[r],t.uint32(26).fork()).ldelim();return null!=e.droppedAttributesCount&&t.uint32(32).uint32(e.droppedAttributesCount),t}function aO(e,t){if(null!=e.traceId&&t.uint32(10).bytes(e.traceId),null!=e.spanId&&t.uint32(18).bytes(e.spanId),null!=e.traceState&&t.uint32(26).string(e.traceState),null!=e.parentSpanId&&t.uint32(34).bytes(e.parentSpanId),null!=e.name&&t.uint32(42).string(e.name),null!=e.kind&&t.uint32(48).int32(e.kind),null!=e.startTimeUnixNano&&t.uint32(57).fixed64(e.startTimeUnixNano),null!=e.endTimeUnixNano&&t.uint32(65).fixed64(e.endTimeUnixNano),null!=e.attributes&&e.attributes.length)for(let r=0;r<e.attributes.length;++r)ir(e.attributes[r],t.uint32(74).fork()).ldelim();if(null!=e.droppedAttributesCount&&t.uint32(80).uint32(e.droppedAttributesCount),null!=e.events&&e.events.length)for(let r=0;r<e.events.length;++r)cO(e.events[r],t.uint32(90).fork()).ldelim();if(null!=e.droppedEventsCount&&t.uint32(96).uint32(e.droppedEventsCount),null!=e.links&&e.links.length)for(let r=0;r<e.links.length;++r)lO(e.links[r],t.uint32(106).fork()).ldelim();return null!=e.droppedLinksCount&&t.uint32(112).uint32(e.droppedLinksCount),null!=e.status&&uO(e.status,t.uint32(122).fork()).ldelim(),t}function uO(e,t){return null!=e.message&&t.uint32(18).string(e.message),null!=e.code&&t.uint32(24).int32(e.code),t}function cO(e,t){if(null!=e.timeUnixNano&&t.uint32(9).fixed64(e.timeUnixNano),null!=e.name&&t.uint32(18).string(e.name),null!=e.attributes&&e.attributes.length)for(let r=0;r<e.attributes.length;++r)ir(e.attributes[r],t.uint32(26).fork()).ldelim();return null!=e.droppedAttributesCount&&t.uint32(32).uint32(e.droppedAttributesCount),t}function lO(e,t){if(null!=e.traceId&&t.uint32(10).bytes(e.traceId),null!=e.spanId&&t.uint32(18).bytes(e.spanId),null!=e.traceState&&t.uint32(26).string(e.traceState),null!=e.attributes&&e.attributes.length)for(let r=0;r<e.attributes.length;++r)ir(e.attributes[r],t.uint32(34).fork()).ldelim();return null!=e.droppedAttributesCount&&t.uint32(40).uint32(e.droppedAttributesCount),t}var dt=class{constructor(e={}){this.impl=new ac(e)}export(e,t){this.impl.export(e,t)}shutdown(){return this.impl.shutdown()}forceFlush(){return this.impl.forceFlush()}},ac=class extends tr{convert(e){return(0,xh.createExportTraceServiceRequest)(e,void 0)}toMessage(e){return{body:Ch(e),contentType:"application/x-protobuf",headers:{accept:"application/x-protobuf"}}}getDefaultUrl(e){return Is(e)}};function Dh(e,t){return e.replace(/\{(?<temp1>[^{}]+)\}/g,(e,r)=>{let n=t[r];return void 0!==n?String(n):e})}h();var sr=class{constructor(e={}){this.instrumentationName="@vercel/otel/fetch",this.instrumentationVersion="1.0.0",this.config=e}getConfig(){return this.config}setConfig(){}setTracerProvider(e){this.tracerProvider=e}setMeterProvider(){}shouldIgnore(e,t){let r=this.config.ignoreUrls??[];if(t?.opentelemetry?.ignore!==void 0)return t.opentelemetry.ignore;if(0===r.length)return!1;let n=e.toString();return r.some(e=>"string"==typeof e?"*"===e||n.startsWith(e):e.test(n))}shouldPropagate(e,t){let r=process.env.VERCEL_URL||process.env.NEXT_PUBLIC_VERCEL_URL||null,n=process.env.VERCEL_BRANCH_URL||process.env.NEXT_PUBLIC_VERCEL_BRANCH_URL||null,i=this.config.propagateContextUrls??[],o=this.config.dontPropagateContextUrls??[];if(t?.opentelemetry?.propagateContext)return t.opentelemetry.propagateContext;let s=e.toString();return!(o.length>0&&o.some(e=>"string"==typeof e?"*"===e||s.startsWith(e):e.test(s)))&&(!!r&&"https:"===e.protocol&&(e.host===r||e.host===n||e.host===me()?.headers.host)||!r&&"http:"===e.protocol&&"localhost"===e.hostname||i.some(e=>"string"==typeof e?"*"===e||s.startsWith(e):e.test(s)))}startSpan({tracer:e,url:t,fetchType:r,method:n="GET",name:i,attributes:o={}}){let s=this.config.resourceNameTemplate,a={[cp]:n,[lp]:t.toString(),[As]:t.host,[dp]:t.protocol.replace(":",""),[hp]:t.hostname,[pp]:t.port},u=s?Dh(s,a):pO(t.toString()),c=i??`${r} ${n} ${t.toString()}`,l=d.context.active();return e.startSpan(c,{kind:d.SpanKind.CLIENT,attributes:{...a,"operation.name":`${r}.${n}`,"http.client.name":r,"resource.name":u,...o}},l)}instrumentHttp(e,t){let{tracerProvider:r}=this;if(!r)return;let n=r.getTracer(this.instrumentationName,this.instrumentationVersion),{attributesFromRequestHeaders:i,attributesFromResponseHeaders:o}=this.config,s=e.request,a=e.get,u=e=>(r,s,a)=>{let u,c={},l;if("string"==typeof r||r instanceof URL?(u=new URL(r.toString()),"function"==typeof s?l=s:s&&"function"==typeof a?(c=s,l=a):s&&(c=s)):(c=r,"function"==typeof s&&(l=s),u=fO(c,t)),this.shouldIgnore(u))return e.apply(this,[u,c,l]);let p=this.startSpan({tracer:n,url:u,fetchType:"http",method:c.method||"GET"});if(!p.isRecording()||!er(p.spanContext().traceFlags))return p.end(),e.apply(this,[u,c,l]);if(this.shouldPropagate(u)){let e=d.context.active(),t=d.trace.setSpan(e,p);d.propagation.inject(t,c.headers||{},_O)}i&&ws(p,i,c.headers||{},Bh);try{let t=Date.now(),r=e.apply(this,[u,c,l]);return r.prependListener("response",e=>{let n=Date.now()-t;p.setAttribute("http.response_time",n),void 0!==e.statusCode?(p.setAttribute(Uu,e.statusCode),e.statusCode>=500&&_t(p,`Status: ${e.statusCode}`)):_t(p,"Response status code is undefined"),o&&ws(p,o,e.headers,Bh),1>=r.listenerCount("response")&&e.resume(),e.on("end",()=>{let t,r=e.statusCode;t=e.aborted&&!e.complete?{code:d.SpanStatusCode.ERROR}:r&&r>=100&&r<500?{code:d.SpanStatusCode.UNSET}:{code:d.SpanStatusCode.ERROR},p.setStatus(t),p.isRecording()&&(e.headers["content-length"]&&p.setAttribute(Bu,e.headers["content-length"]),p.end())})}),r.on("error",e=>{p.isRecording()&&(_t(p,e),p.end())}),r.on("close",()=>{p.isRecording()&&p.end()}),r}catch(e){throw _t(p,e),p.end(),e}};e.request=u(s),e.get=u(a)}instrumentFetch(){let{tracerProvider:e}=this;if(!e)return;let t=e.getTracer(this.instrumentationName,this.instrumentationVersion),{attributesFromRequestHeaders:r,attributesFromResponseHeaders:n}=this.config;process.env.NEXT_OTEL_FETCH_DISABLED="1";let i=globalThis.fetch;this.originalFetch=i;let o=async(e,o)=>{let s=o;if(s?.next?.internal)return i(e,s);let a=new Request(e instanceof Request?e.clone():e,s),u=new URL(a.url);if(this.shouldIgnore(u,s))return i(e,s);let c=this.startSpan({tracer:t,url:u,fetchType:"fetch",method:a.method,name:s?.opentelemetry?.spanName,attributes:s?.opentelemetry?.attributes});if(!c.isRecording()||!er(c.spanContext().traceFlags))return c.end(),i(e,s);if(this.shouldPropagate(u,s)){let e=d.context.active(),t=d.trace.setSpan(e,c);d.propagation.inject(t,a.headers,dO)}r&&ws(c,r,a.headers,Uh);try{let t=Date.now();s?.body&&s.body instanceof FormData&&a.headers.delete("content-type");let r=await i(e,{...s,headers:a.headers}),o=Date.now()-t;return c.setAttribute(Uu,r.status),c.setAttribute("http.response_time",o),n&&ws(c,n,r.headers,Uh),r.status>=500&&_t(c,`Status: ${r.status} (${r.statusText})`),r.body?hO(r).then(e=>{c.isRecording()&&(c.setAttribute(Bu,e),c.end())},e=>{c.isRecording()&&(_t(c,e),c.end())}):c.end(),r}catch(e){throw _t(c,e),c.end(),e}};globalThis.fetch=o}enable(){this.disable(),this.instrumentFetch();try{let e=z("node:http"),t=z("node:https");this.instrumentHttp(e,"http:"),this.instrumentHttp(t,"https:")}catch{}}disable(){this.originalFetch&&(globalThis.fetch=this.originalFetch)}},dO={set(e,t,r){e.set(t,r)}},Uh={get(e,t){let r=e.get(t);if(null!==r)return r.includes(",")?r.split(",").map(e=>e.trimStart()):r},keys(e){let t=[];return e.forEach((e,r)=>{t.push(r)}),t}},_O={set(e,t,r){e[t.toLowerCase()]=r}},Bh={get:(e,t)=>e[t.toLowerCase()],keys:e=>Object.keys(e)};function pO(e){let t=e.indexOf("?");return -1===t?e:e.substring(0,t)}function hO(e){let t=0,r=e.clone().body?.getReader();if(!r)return Promise.resolve(0);let n=()=>r.read().then(({done:e,value:r})=>{if(!e)return t+=r.byteLength,n()});return n().then(()=>t)}function _t(e,t){if(t instanceof Error)e.recordException(t),e.setStatus({code:d.SpanStatusCode.ERROR,message:t.message});else{let r=String(t);e.setStatus({code:d.SpanStatusCode.ERROR,message:r})}}function ws(e,t,r,n){for(let[i,o]of Object.entries(t)){let t=n.get(r,o);void 0!==t&&e.setAttribute(i,t)}}function fO(e,t){let r;if(e.socketPath)throw Error("Cannot construct a network URL: options.socketPath is specified, indicating a Unix domain socket.");let n=e.protocol??t;n&&!n.endsWith(":")&&(n+=":");let i=e.hostname,o=e.port??e.defaultPort;if(!i&&e.host){let t=e.host.split(":");i=t[0];let r=t[1];if(t.length>1&&r&&void 0===o){let e=parseInt(r,10);isNaN(e)||(o=e)}}if(i||(i="localhost"),void 0!==o&&""!==o){let e=parseInt(String(o),10);r=isNaN(e)?"https:"===n?443:80:e}else r="https:"===n?443:80;let s=new URL(e.path||"/",`${n}//${i}:${r}`);if(e.auth){let t=e.auth.split(":");s.username=decodeURIComponent(t[0]||""),t.length>1&&(s.password=decodeURIComponent(t[1]||""))}return s}h();var qh=Y(A(),1),EO="00",uc="traceparent",cc="tracestate",jr=class{fields(){return[uc,cc]}inject(e,t,r){let n=d.trace.getSpanContext(e);if(!n||(0,qh.isTracingSuppressed)(e)||!(0,d.isSpanContextValid)(n))return;let i=`${EO}-${n.traceId}-${n.spanId}-0${Number(n.traceFlags||0).toString(16)}`;r.set(t,uc,i),n.traceState&&r.set(t,cc,n.traceState.serialize())}extract(e,t,r){let n=r.get(t,uc);if(!n)return e;let i=Array.isArray(n)?n[0]:n;if("string"!=typeof i)return e;let o=mO(i);if(!o)return e;o.isRemote=!0;let s=r.get(t,cc);if(s){let e=Array.isArray(s)?s.join(","):s;o.traceState=(0,d.createTraceState)("string"==typeof e?e:void 0)}return d.trace.setSpanContext(e,o)}};function mO(e){let[t,r,n,i,o]=e.split("-");return t&&r&&n&&i&&2===t.length&&32===r.length&&16===n.length&&2===i.length&&("00"!==t||!o)?{traceId:r,spanId:n,traceFlags:parseInt(i,16)}:null}h();var xs=class{fields(){return[]}inject(){}extract(e){let t=me();if(!t?.telemetry)return d.diag.warn("@vercel/otel: Vercel telemetry extension not found."),e;let{rootSpanContext:r}=t.telemetry;return r?(d.diag.debug("@vercel/otel: Extracted root SpanContext from Vercel request context.",r),d.trace.setSpanContext(e,{...r,isRemote:!0,traceFlags:r.traceFlags||d.TraceFlags.SAMPLED})):e}};h();var Bs=Y(A(),1),nf=Y(rf(),1),qs=class{export(e,t){let r=me();if(!r?.telemetry){d.diag.warn("@vercel/otel: no telemetry context found"),t({code:Bs.ExportResultCode.SUCCESS,error:void 0});return}try{let n=(0,nf.createExportTraceServiceRequest)(e,{useHex:!0,useLongBits:!1});r.telemetry.reportSpans(n),t({code:Bs.ExportResultCode.SUCCESS,error:void 0})}catch(e){t({code:Bs.ExportResultCode.FAILED,error:e instanceof Error?e:Error(String(e))})}}shutdown(){return Promise.resolve()}forceFlush(){return Promise.resolve()}},UO={ALL:d.DiagLogLevel.ALL,VERBOSE:d.DiagLogLevel.VERBOSE,DEBUG:d.DiagLogLevel.DEBUG,INFO:d.DiagLogLevel.INFO,WARN:d.DiagLogLevel.WARN,ERROR:d.DiagLogLevel.ERROR,NONE:d.DiagLogLevel.NONE},Gs=class{constructor(e={}){this.configuration=e}start(){let e=BO(),t=this.configuration,r="nodejs",n=!!e.OTEL_SDK_DISABLED;if(process.env.OTEL_LOG_LEVEL&&d.diag.setLogger(new d.DiagConsoleLogger,{logLevel:UO[process.env.OTEL_LOG_LEVEL.toUpperCase()]}),n)return;let i=t.idGenerator??new C.RandomIdGenerator,o=t.contextManager??new lf.AsyncLocalStorageContextManager;o.enable(),this.contextManager=o;let s=e.OTEL_SERVICE_NAME||t.serviceName||"app",a=new lr.Resource(Os({[fp]:s,"node.ci":!!process.env.CI||void 0,"node.env":"production",env:process.env.VERCEL_ENV||process.env.NEXT_PUBLIC_VERCEL_ENV,"vercel.region":process.env.VERCEL_REGION,"vercel.runtime":r,"vercel.sha":process.env.VERCEL_GIT_COMMIT_SHA||process.env.NEXT_PUBLIC_VERCEL_GIT_COMMIT_SHA,"vercel.host":process.env.VERCEL_URL||process.env.NEXT_PUBLIC_VERCEL_URL||void 0,"vercel.branch_host":process.env.VERCEL_BRANCH_URL||process.env.NEXT_PUBLIC_VERCEL_BRANCH_URL||void 0,"vercel.deployment_id":process.env.VERCEL_DEPLOYMENT_ID||void 0,[Ep]:process.env.VERCEL_DEPLOYMENT_ID,...t.attributes})),u=t.resourceDetectors??[lr.envDetectorSync];if(t.autoDetectResources??!0){let e={detectors:u};a=a.merge((0,lr.detectResourcesSync)(e))}let c=GO(t.propagators,t,e),l=VO(t.traceSampler,e),p=HO(t.spanProcessors,t,e);0===p.length&&d.diag.warn("@vercel/otel: No span processors configured. No spans will be exported.");let _=t.spanLimits,h=new C.BasicTracerProvider({resource:a,idGenerator:i,sampler:l,spanLimits:_});if(h.addSpanProcessor(new Rs(p,t.attributesFromHeaders)),h.register({contextManager:o,propagator:new He.CompositePropagator({propagators:c})}),this.tracerProvider=h,t.logRecordProcessor){let e=new uf.LoggerProvider({resource:a});this.loggerProvider=e,e.addLogRecordProcessor(t.logRecordProcessor),st.logs.setGlobalLoggerProvider(e)}if(t.metricReader||t.views){let e=new cf.MeterProvider({resource:a,views:t.views??[]});t.metricReader&&e.addMetricReader(t.metricReader),d.metrics.setGlobalMeterProvider(e),this.meterProvider=e}let g=qO(t.instrumentations,t.instrumentationConfig);this.disableInstrumentations=(0,af.registerInstrumentations)({instrumentations:g}),d.diag.info("@vercel/otel: started",s,r)}async shutdown(){let e=[];this.tracerProvider&&e.push(this.tracerProvider.shutdown()),this.loggerProvider&&e.push(this.loggerProvider.shutdown()),this.meterProvider&&e.push(this.meterProvider.shutdown()),d.diag.info("@vercel/otel: shutting down",e.length,"nodejs"),await Promise.all(e),this.contextManager&&this.contextManager.disable();let{disableInstrumentations:t}=this;t&&t()}};function BO(){let e=(0,Vs.parseEnvironment)(process.env);return{...Vs.DEFAULT_ENVIRONMENT,...e}}function qO(e,t){return(e??["auto"]).map(e=>"auto"===e?(d.diag.debug("@vercel/otel: Configure instrumentations: fetch",t?.fetch),[new sr(t?.fetch)]):"fetch"===e?(d.diag.debug("@vercel/otel: Configure instrumentations: fetch",t?.fetch),new sr(t?.fetch)):e).flat()}function GO(e,t,r){let n=process.env.OTEL_PROPAGATORS&&r.OTEL_PROPAGATORS&&r.OTEL_PROPAGATORS.length>0?r.OTEL_PROPAGATORS:void 0;return(e??n??["auto"]).map(e=>{if("none"===e)return[];if("auto"===e){let e=[];return e.push({name:"tracecontext",propagator:new jr}),e.push({name:"baggage",propagator:new He.W3CBaggagePropagator}),e.push({name:"vercel-runtime",propagator:new xs}),d.diag.debug(`@vercel/otel: Configure propagators: ${e.map(e=>e.name).join(", ")}`),e.map(e=>e.propagator)}if("tracecontext"===e)return d.diag.debug("@vercel/otel: Configure propagator: tracecontext"),new jr;if("baggage"===e)return d.diag.debug("@vercel/otel: Configure propagator: baggage"),new He.W3CBaggagePropagator;if("string"==typeof e)throw Error(`Unknown propagator: "${e}"`);return e}).flat()}var sf="always_on",cr=1;function VO(e,t){if(e&&"string"!=typeof e)return e;let r=e&&"auto"!==e?e:t.OTEL_TRACES_SAMPLER||sf;switch(d.diag.debug("@vercel/otel: Configure sampler: ",r),r){case"always_on":return new C.AlwaysOnSampler;case"always_off":return new C.AlwaysOffSampler;case"parentbased_always_on":return new C.ParentBasedSampler({root:new C.AlwaysOnSampler});case"parentbased_always_off":return new C.ParentBasedSampler({root:new C.AlwaysOffSampler});case"traceidratio":return new C.TraceIdRatioBasedSampler(of(t));case"parentbased_traceidratio":return new C.ParentBasedSampler({root:new C.TraceIdRatioBasedSampler(of(t))});default:return d.diag.error(`@vercel/otel: OTEL_TRACES_SAMPLER value "${String(t.OTEL_TRACES_SAMPLER)} invalid, defaulting to ${sf}".`),new C.AlwaysOnSampler}}function of(e){if(void 0===e.OTEL_TRACES_SAMPLER_ARG||""===e.OTEL_TRACES_SAMPLER_ARG)return d.diag.error(`@vercel/otel: OTEL_TRACES_SAMPLER_ARG is blank, defaulting to ${cr}.`),cr;d.diag.debug("@vercel/otel: Configure sampler probability: ",e.OTEL_TRACES_SAMPLER_ARG);let t=Number(e.OTEL_TRACES_SAMPLER_ARG);return isNaN(t)?(d.diag.error(`@vercel/otel: OTEL_TRACES_SAMPLER_ARG=${e.OTEL_TRACES_SAMPLER_ARG} was given, but it is invalid, defaulting to ${cr}.`),cr):t<0||t>1?(d.diag.error(`@vercel/otel: OTEL_TRACES_SAMPLER_ARG=${e.OTEL_TRACES_SAMPLER_ARG} was given, but it is out of range ([0..1]), defaulting to ${cr}.`),cr):t}function HO(e,t,r){return[...(e??["auto"]).flatMap(e=>{if("auto"===e){let e=[new C.BatchSpanProcessor(new qs)];if(process.env.VERCEL_OTEL_ENDPOINTS){let t=process.env.VERCEL_OTEL_ENDPOINTS_PORT||"4318",r=process.env.VERCEL_OTEL_ENDPOINTS_PROTOCOL||"http/protobuf";d.diag.debug("@vercel/otel: Configure vercel otel collector on port: ",t,r);let n={url:`http://localhost:${t}/v1/traces`,headers:{}},i="http/protobuf"===r?new dt(n):new rr(n);e.push(new C.BatchSpanProcessor(i))}else(!t.traceExporter||"auto"===t.traceExporter||r.OTEL_EXPORTER_OTLP_TRACES_ENDPOINT||r.OTEL_EXPORTER_OTLP_ENDPOINT)&&e.push(new C.BatchSpanProcessor(jO(r)));return e}return e}).filter($O),...t.traceExporter&&"auto"!==t.traceExporter?[new C.BatchSpanProcessor(t.traceExporter)]:[]]}function jO(e){let t=process.env.OTEL_EXPORTER_OTLP_TRACES_PROTOCOL??process.env.OTEL_EXPORTER_OTLP_PROTOCOL??"http/protobuf",r=kO(e),n={...He.baggageUtils.parseKeyPairsIntoRecord(e.OTEL_EXPORTER_OTLP_HEADERS),...He.baggageUtils.parseKeyPairsIntoRecord(e.OTEL_EXPORTER_OTLP_TRACES_HEADERS)};switch(d.diag.debug("@vercel/otel: Configure trace exporter: ",t,r,`headers: ${Object.keys(n).join(",")||"<none>"}`),t){case"http/json":return new rr({url:r,headers:n});case"http/protobuf":return new dt({url:r,headers:n});default:return d.diag.warn(`@vercel/otel: Unsupported OTLP traces protocol: ${t}. Using http/protobuf.`),new dt}}var df="v1/traces",FO=`http://localhost:4318/${df}`;function kO(e){let t=e.OTEL_EXPORTER_OTLP_TRACES_ENDPOINT;if(t&&"string"==typeof t)return t;let r=e.OTEL_EXPORTER_OTLP_ENDPOINT;return r&&"string"==typeof r?`${r}/${df}`:FO}function $O(e){return null!=e}function xv(e){let t;new Gs(t=e?"string"==typeof e?{serviceName:e}:e:{}).start()}},56442:(e,t,r)=>{r.r(t),r.d(t,{DiagConsoleLogger:()=>B,DiagLogLevel:()=>n,INVALID_SPANID:()=>eh,INVALID_SPAN_CONTEXT:()=>ef,INVALID_TRACEID:()=>eg,ProxyTracer:()=>eD,ProxyTracerProvider:()=>eB,ROOT_CONTEXT:()=>D,SamplingDecision:()=>s,SpanKind:()=>a,SpanStatusCode:()=>u,TraceFlags:()=>o,ValueType:()=>i,baggageEntryMetadataFromString:()=>w,context:()=>eq,createContextKey:()=>x,createNoopMeter:()=>en,createTraceState:()=>eY,default:()=>tr,defaultTextMapGetter:()=>ei,defaultTextMapSetter:()=>eo,diag:()=>eQ,isSpanContextValid:()=>eM,isValidSpanId:()=>eL,isValidTraceId:()=>eP,metrics:()=>e0,propagation:()=>e9,trace:()=>tt});var n,i,o,s,a,u,c="object"==typeof globalThis?globalThis:global,l="1.9.0",d=/^(\d+)\.(\d+)\.(\d+)(-(.+))?$/,p=function(e){var t=new Set([e]),r=new Set,n=e.match(d);if(!n)return function(){return!1};var i={major:+n[1],minor:+n[2],patch:+n[3],prerelease:n[4]};if(null!=i.prerelease)return function(t){return t===e};function o(e){return r.add(e),!1}function s(e){return t.add(e),!0}return function(e){if(t.has(e))return!0;if(r.has(e))return!1;var n=e.match(d);if(!n)return o(e);var a={major:+n[1],minor:+n[2],patch:+n[3],prerelease:n[4]};return null!=a.prerelease||i.major!==a.major?o(e):0===i.major?i.minor===a.minor&&i.patch<=a.patch?s(e):o(e):i.minor<=a.minor?s(e):o(e)}}(l),_=Symbol.for("opentelemetry.js.api."+l.split(".")[0]),h=c;function g(e,t,r,n){void 0===n&&(n=!1);var i,o=h[_]=null!=(i=h[_])?i:{version:l};if(!n&&o[e]){var s=Error("@opentelemetry/api: Attempted duplicate registration of API: "+e);return r.error(s.stack||s.message),!1}if(o.version!==l){var s=Error("@opentelemetry/api: Registration of version v"+o.version+" for "+e+" does not match previously registered API v"+l);return r.error(s.stack||s.message),!1}return o[e]=t,r.debug("@opentelemetry/api: Registered a global for "+e+" v"+l+"."),!0}function f(e){var t,r,n=null==(t=h[_])?void 0:t.version;if(n&&p(n))return null==(r=h[_])?void 0:r[e]}function E(e,t){t.debug("@opentelemetry/api: Unregistering a global for "+e+" v"+l+".");var r=h[_];r&&delete r[e]}var m=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(n=o.next()).done;)s.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return s},T=function(e,t,r){if(r||2==arguments.length)for(var n,i=0,o=t.length;i<o;i++)!n&&i in t||(n||(n=Array.prototype.slice.call(t,0,i)),n[i]=t[i]);return e.concat(n||Array.prototype.slice.call(t))},S=function(){function e(e){this._namespace=e.namespace||"DiagComponentLogger"}return e.prototype.debug=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return v("debug",this._namespace,e)},e.prototype.error=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return v("error",this._namespace,e)},e.prototype.info=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return v("info",this._namespace,e)},e.prototype.warn=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return v("warn",this._namespace,e)},e.prototype.verbose=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return v("verbose",this._namespace,e)},e}();function v(e,t,r){var n=f("diag");if(n)return r.unshift(t),n[e].apply(n,T([],m(r),!1))}function A(e,t){function r(r,n){var i=t[r];return"function"==typeof i&&e>=n?i.bind(t):function(){}}return e<n.NONE?e=n.NONE:e>n.ALL&&(e=n.ALL),t=t||{},{error:r("error",n.ERROR),warn:r("warn",n.WARN),info:r("info",n.INFO),debug:r("debug",n.DEBUG),verbose:r("verbose",n.VERBOSE)}}!function(e){e[e.NONE=0]="NONE",e[e.ERROR=30]="ERROR",e[e.WARN=50]="WARN",e[e.INFO=60]="INFO",e[e.DEBUG=70]="DEBUG",e[e.VERBOSE=80]="VERBOSE",e[e.ALL=9999]="ALL"}(n||(n={}));var O=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(n=o.next()).done;)s.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return s},b=function(e,t,r){if(r||2==arguments.length)for(var n,i=0,o=t.length;i<o;i++)!n&&i in t||(n||(n=Array.prototype.slice.call(t,0,i)),n[i]=t[i]);return e.concat(n||Array.prototype.slice.call(t))},y="diag",R=function(){function e(){function e(e){return function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var n=f("diag");if(n)return n[e].apply(n,b([],O(t),!1))}}var t=this,r=function(e,r){if(void 0===r&&(r={logLevel:n.INFO}),e===t){var i,o,s,a=Error("Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation");return t.error(null!=(i=a.stack)?i:a.message),!1}"number"==typeof r&&(r={logLevel:r});var u=f("diag"),c=A(null!=(o=r.logLevel)?o:n.INFO,e);if(u&&!r.suppressOverrideMessage){var l=null!=(s=Error().stack)?s:"<failed to generate stacktrace>";u.warn("Current logger will be overwritten from "+l),c.warn("Current logger will overwrite one already registered from "+l)}return g("diag",c,t,!0)};t.setLogger=r,t.disable=function(){E(y,t)},t.createComponentLogger=function(e){return new S(e)},t.verbose=e("verbose"),t.debug=e("debug"),t.info=e("info"),t.warn=e("warn"),t.error=e("error")}return e.instance=function(){return this._instance||(this._instance=new e),this._instance},e}(),P=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(n=o.next()).done;)s.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return s},L=function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},M=function(){function e(e){this._entries=e?new Map(e):new Map}return e.prototype.getEntry=function(e){var t=this._entries.get(e);if(t)return Object.assign({},t)},e.prototype.getAllEntries=function(){return Array.from(this._entries.entries()).map(function(e){var t=P(e,2);return[t[0],t[1]]})},e.prototype.setEntry=function(t,r){var n=new e(this._entries);return n._entries.set(t,r),n},e.prototype.removeEntry=function(t){var r=new e(this._entries);return r._entries.delete(t),r},e.prototype.removeEntries=function(){for(var t,r,n=[],i=0;i<arguments.length;i++)n[i]=arguments[i];var o=new e(this._entries);try{for(var s=L(n),a=s.next();!a.done;a=s.next()){var u=a.value;o._entries.delete(u)}}catch(e){t={error:e}}finally{try{a&&!a.done&&(r=s.return)&&r.call(s)}finally{if(t)throw t.error}}return o},e.prototype.clear=function(){return new e},e}(),I=Symbol("BaggageEntryMetadata"),N=R.instance();function C(e){return void 0===e&&(e={}),new M(new Map(Object.entries(e)))}function w(e){return"string"!=typeof e&&(N.error("Cannot create baggage metadata from unknown type: "+typeof e),e=""),{__TYPE__:I,toString:function(){return e}}}function x(e){return Symbol.for(e)}var D=new(function(){function e(t){var r=this;r._currentContext=t?new Map(t):new Map,r.getValue=function(e){return r._currentContext.get(e)},r.setValue=function(t,n){var i=new e(r._currentContext);return i._currentContext.set(t,n),i},r.deleteValue=function(t){var n=new e(r._currentContext);return n._currentContext.delete(t),n}}return e}()),U=[{n:"error",c:"error"},{n:"warn",c:"warn"},{n:"info",c:"info"},{n:"debug",c:"debug"},{n:"verbose",c:"trace"}],B=function(){return function(){function e(e){return function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];if(console){var n=console[e];if("function"!=typeof n&&(n=console.log),"function"==typeof n)return n.apply(console,t)}}}for(var t=0;t<U.length;t++)this[U[t].n]=e(U[t].c)}}(),j=function(){var e=function(t,r){return(e=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(t,r)};return function(t,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),G=function(){function e(){}return e.prototype.createGauge=function(e,t){return Q},e.prototype.createHistogram=function(e,t){return Z},e.prototype.createCounter=function(e,t){return q},e.prototype.createUpDownCounter=function(e,t){return J},e.prototype.createObservableGauge=function(e,t){return et},e.prototype.createObservableCounter=function(e,t){return ee},e.prototype.createObservableUpDownCounter=function(e,t){return er},e.prototype.addBatchObservableCallback=function(e,t){},e.prototype.removeBatchObservableCallback=function(e){},e}(),V=function(){return function(){}}(),k=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return j(t,e),t.prototype.add=function(e,t){},t}(V),F=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return j(t,e),t.prototype.add=function(e,t){},t}(V),H=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return j(t,e),t.prototype.record=function(e,t){},t}(V),$=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return j(t,e),t.prototype.record=function(e,t){},t}(V),X=function(){function e(){}return e.prototype.addCallback=function(e){},e.prototype.removeCallback=function(e){},e}(),K=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return j(t,e),t}(X),z=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return j(t,e),t}(X),W=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return j(t,e),t}(X),Y=new G,q=new k,Q=new H,Z=new $,J=new F,ee=new K,et=new z,er=new W;function en(){return Y}!function(e){e[e.INT=0]="INT",e[e.DOUBLE=1]="DOUBLE"}(i||(i={}));var ei={get:function(e,t){if(null!=e)return e[t]},keys:function(e){return null==e?[]:Object.keys(e)}},eo={set:function(e,t,r){null!=e&&(e[t]=r)}},es=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(n=o.next()).done;)s.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return s},ea=function(e,t,r){if(r||2==arguments.length)for(var n,i=0,o=t.length;i<o;i++)!n&&i in t||(n||(n=Array.prototype.slice.call(t,0,i)),n[i]=t[i]);return e.concat(n||Array.prototype.slice.call(t))},eu=function(){function e(){}return e.prototype.active=function(){return D},e.prototype.with=function(e,t,r){for(var n=[],i=3;i<arguments.length;i++)n[i-3]=arguments[i];return t.call.apply(t,ea([r],es(n),!1))},e.prototype.bind=function(e,t){return t},e.prototype.enable=function(){return this},e.prototype.disable=function(){return this},e}(),ec=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(n=o.next()).done;)s.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return s},el=function(e,t,r){if(r||2==arguments.length)for(var n,i=0,o=t.length;i<o;i++)!n&&i in t||(n||(n=Array.prototype.slice.call(t,0,i)),n[i]=t[i]);return e.concat(n||Array.prototype.slice.call(t))},ed="context",ep=new eu,e_=function(){function e(){}return e.getInstance=function(){return this._instance||(this._instance=new e),this._instance},e.prototype.setGlobalContextManager=function(e){return g(ed,e,R.instance())},e.prototype.active=function(){return this._getContextManager().active()},e.prototype.with=function(e,t,r){for(var n,i=[],o=3;o<arguments.length;o++)i[o-3]=arguments[o];return(n=this._getContextManager()).with.apply(n,el([e,t,r],ec(i),!1))},e.prototype.bind=function(e,t){return this._getContextManager().bind(e,t)},e.prototype._getContextManager=function(){return f(ed)||ep},e.prototype.disable=function(){this._getContextManager().disable(),E(ed,R.instance())},e}();!function(e){e[e.NONE=0]="NONE",e[e.SAMPLED=1]="SAMPLED"}(o||(o={}));var eh="0000000000000000",eg="00000000000000000000000000000000",ef={traceId:eg,spanId:eh,traceFlags:o.NONE},eE=function(){function e(e){void 0===e&&(e=ef),this._spanContext=e}return e.prototype.spanContext=function(){return this._spanContext},e.prototype.setAttribute=function(e,t){return this},e.prototype.setAttributes=function(e){return this},e.prototype.addEvent=function(e,t){return this},e.prototype.addLink=function(e){return this},e.prototype.addLinks=function(e){return this},e.prototype.setStatus=function(e){return this},e.prototype.updateName=function(e){return this},e.prototype.end=function(e){},e.prototype.isRecording=function(){return!1},e.prototype.recordException=function(e,t){},e}(),em=x("OpenTelemetry Context Key SPAN");function eT(e){return e.getValue(em)||void 0}function eS(){return eT(e_.getInstance().active())}function ev(e,t){return e.setValue(em,t)}function eA(e){return e.deleteValue(em)}function eO(e,t){return ev(e,new eE(t))}function eb(e){var t;return null==(t=eT(e))?void 0:t.spanContext()}var ey=/^([0-9a-f]{32})$/i,eR=/^[0-9a-f]{16}$/i;function eP(e){return ey.test(e)&&e!==eg}function eL(e){return eR.test(e)&&e!==eh}function eM(e){return eP(e.traceId)&&eL(e.spanId)}function eI(e){return new eE(e)}var eN=e_.getInstance(),eC=function(){function e(){}return e.prototype.startSpan=function(e,t,r){if(void 0===r&&(r=eN.active()),null==t?void 0:t.root)return new eE;var n=r&&eb(r);return ew(n)&&eM(n)?new eE(n):new eE},e.prototype.startActiveSpan=function(e,t,r,n){if(!(arguments.length<2)){2==arguments.length?s=t:3==arguments.length?(i=t,s=r):(i=t,o=r,s=n);var i,o,s,a=null!=o?o:eN.active(),u=this.startSpan(e,i,a),c=ev(a,u);return eN.with(c,s,void 0,u)}},e}();function ew(e){return"object"==typeof e&&"string"==typeof e.spanId&&"string"==typeof e.traceId&&"number"==typeof e.traceFlags}var ex=new eC,eD=function(){function e(e,t,r,n){this._provider=e,this.name=t,this.version=r,this.options=n}return e.prototype.startSpan=function(e,t,r){return this._getTracer().startSpan(e,t,r)},e.prototype.startActiveSpan=function(e,t,r,n){var i=this._getTracer();return Reflect.apply(i.startActiveSpan,i,arguments)},e.prototype._getTracer=function(){if(this._delegate)return this._delegate;var e=this._provider.getDelegateTracer(this.name,this.version,this.options);return e?(this._delegate=e,this._delegate):ex},e}(),eU=new(function(){function e(){}return e.prototype.getTracer=function(e,t,r){return new eC},e}()),eB=function(){function e(){}return e.prototype.getTracer=function(e,t,r){var n;return null!=(n=this.getDelegateTracer(e,t,r))?n:new eD(this,e,t,r)},e.prototype.getDelegate=function(){var e;return null!=(e=this._delegate)?e:eU},e.prototype.setDelegate=function(e){this._delegate=e},e.prototype.getDelegateTracer=function(e,t,r){var n;return null==(n=this._delegate)?void 0:n.getTracer(e,t,r)},e}();!function(e){e[e.NOT_RECORD=0]="NOT_RECORD",e[e.RECORD=1]="RECORD",e[e.RECORD_AND_SAMPLED=2]="RECORD_AND_SAMPLED"}(s||(s={})),function(e){e[e.INTERNAL=0]="INTERNAL",e[e.SERVER=1]="SERVER",e[e.CLIENT=2]="CLIENT",e[e.PRODUCER=3]="PRODUCER",e[e.CONSUMER=4]="CONSUMER"}(a||(a={})),function(e){e[e.UNSET=0]="UNSET",e[e.OK=1]="OK",e[e.ERROR=2]="ERROR"}(u||(u={}));var ej="[_0-9a-z-*/]",eG=RegExp("^(?:[a-z]"+ej+"{0,255}|"+("[a-z0-9]"+ej+"{0,240}@[a-z]"+ej)+"{0,13})$"),eV=/^[ -~]{0,255}[!-~]$/,ek=/,|=/;function eF(e){return eG.test(e)}function eH(e){return eV.test(e)&&!ek.test(e)}var e$=32,eX=512,eK=",",ez="=",eW=function(){function e(e){this._internalState=new Map,e&&this._parse(e)}return e.prototype.set=function(e,t){var r=this._clone();return r._internalState.has(e)&&r._internalState.delete(e),r._internalState.set(e,t),r},e.prototype.unset=function(e){var t=this._clone();return t._internalState.delete(e),t},e.prototype.get=function(e){return this._internalState.get(e)},e.prototype.serialize=function(){var e=this;return this._keys().reduce(function(t,r){return t.push(r+ez+e.get(r)),t},[]).join(eK)},e.prototype._parse=function(e){!(e.length>eX)&&(this._internalState=e.split(eK).reverse().reduce(function(e,t){var r=t.trim(),n=r.indexOf(ez);if(-1!==n){var i=r.slice(0,n),o=r.slice(n+1,t.length);eF(i)&&eH(o)&&e.set(i,o)}return e},new Map),this._internalState.size>e$&&(this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,e$))))},e.prototype._keys=function(){return Array.from(this._internalState.keys()).reverse()},e.prototype._clone=function(){var t=new e;return t._internalState=new Map(this._internalState),t},e}();function eY(e){return new eW(e)}var eq=e_.getInstance(),eQ=R.instance(),eZ=new(function(){function e(){}return e.prototype.getMeter=function(e,t,r){return Y},e}()),eJ="metrics",e0=(function(){function e(){}return e.getInstance=function(){return this._instance||(this._instance=new e),this._instance},e.prototype.setGlobalMeterProvider=function(e){return g(eJ,e,R.instance())},e.prototype.getMeterProvider=function(){return f(eJ)||eZ},e.prototype.getMeter=function(e,t,r){return this.getMeterProvider().getMeter(e,t,r)},e.prototype.disable=function(){E(eJ,R.instance())},e})().getInstance(),e1=function(){function e(){}return e.prototype.inject=function(e,t){},e.prototype.extract=function(e,t){return e},e.prototype.fields=function(){return[]},e}(),e2=x("OpenTelemetry Baggage Key");function e3(e){return e.getValue(e2)||void 0}function e4(){return e3(e_.getInstance().active())}function e8(e,t){return e.setValue(e2,t)}function e6(e){return e.deleteValue(e2)}var e5="propagation",e7=new e1,e9=(function(){function e(){this.createBaggage=C,this.getBaggage=e3,this.getActiveBaggage=e4,this.setBaggage=e8,this.deleteBaggage=e6}return e.getInstance=function(){return this._instance||(this._instance=new e),this._instance},e.prototype.setGlobalPropagator=function(e){return g(e5,e,R.instance())},e.prototype.inject=function(e,t,r){return void 0===r&&(r=eo),this._getGlobalPropagator().inject(e,t,r)},e.prototype.extract=function(e,t,r){return void 0===r&&(r=ei),this._getGlobalPropagator().extract(e,t,r)},e.prototype.fields=function(){return this._getGlobalPropagator().fields()},e.prototype.disable=function(){E(e5,R.instance())},e.prototype._getGlobalPropagator=function(){return f(e5)||e7},e})().getInstance(),te="trace",tt=(function(){function e(){this._proxyTracerProvider=new eB,this.wrapSpanContext=eI,this.isSpanContextValid=eM,this.deleteSpan=eA,this.getSpan=eT,this.getActiveSpan=eS,this.getSpanContext=eb,this.setSpan=ev,this.setSpanContext=eO}return e.getInstance=function(){return this._instance||(this._instance=new e),this._instance},e.prototype.setGlobalTracerProvider=function(e){var t=g(te,this._proxyTracerProvider,R.instance());return t&&this._proxyTracerProvider.setDelegate(e),t},e.prototype.getTracerProvider=function(){return f(te)||this._proxyTracerProvider},e.prototype.getTracer=function(e,t){return this.getTracerProvider().getTracer(e,t)},e.prototype.disable=function(){E(te,R.instance()),this._proxyTracerProvider=new eB},e})().getInstance();let tr={context:eq,diag:eQ,metrics:e0,propagation:e9,trace:tt}},79551:e=>{e.exports=require("url")},84356:(e,t,r)=>{r.r(t),r.d(t,{register:()=>i});var n=r(42359);function i(){(0,n.Mq)({serviceName:"ai-chatbot"})}}};var __webpack_require__=require("./webpack-runtime.js");__webpack_require__.C(exports);var __webpack_exec__=e=>__webpack_require__(__webpack_require__.s=e),__webpack_exports__=__webpack_exec__(84356);module.exports=__webpack_exports__})();