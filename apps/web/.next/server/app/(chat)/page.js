"use strict";(()=>{var e={};e.id=7342,e.ids=[7342],e.modules={1708:e=>{e.exports=require("node:process")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{e.exports=require("node:buffer")},11997:e=>{e.exports=require("punycode")},12412:e=>{e.exports=require("assert")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29727:e=>{e.exports=require("next/dist/compiled/next-server/app-page-experimental.runtime.prod.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},50092:(e,r,t)=>{t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>x,tree:()=>p});var o=t(24332),s=t(48819),i=t(67851),n=t.n(i),a=t(97540),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);t.d(r,d);let p={children:["",{children:["(chat)",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,59719)),"/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(chat)/page.tsx"],metadata:{icon:[],apple:[],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,34040))).default(e)],twitter:[async e=>(await Promise.resolve().then(t.bind(t,96478))).default(e)],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,36784)),"/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(chat)/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19033,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,39956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,92341,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70196))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,34356)),"/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19033,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,39956,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,92341,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70196))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(chat)/page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},x=new o.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/(chat)/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},57975:e=>{e.exports=require("node:util")},59719:(e,r,t)=>{t.r(r),t.d(r,{default:()=>u});var o=t(38828),s=t(65208),i=t(30921),n=t(35361),a=t(77877),d=t(96945),p=t(52335),l=t(42543);async function u(){let e=await (0,p.j2)();e||(0,l.redirect)("/api/auth/guest");let r=(0,a.lk)(),t=(await (0,s.UL)()).get("chat-model");return t?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(i.Chat,{id:r,initialMessages:[],initialChatModel:t.value,initialVisibilityType:"private",isReadonly:!1,session:e,autoResume:!1},r),(0,o.jsx)(d.DataStreamHandler,{})]}):(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(i.Chat,{id:r,initialMessages:[],initialChatModel:n.j,initialVisibilityType:"private",isReadonly:!1,session:e,autoResume:!1},r),(0,o.jsx)(d.DataStreamHandler,{})]})}},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{e.exports=require("node:fs")},73136:e=>{e.exports=require("node:url")},74075:e=>{e.exports=require("zlib")},76760:e=>{e.exports=require("node:path")},77598:e=>{e.exports=require("node:crypto")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},84297:e=>{e.exports=require("async_hooks")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[191,4235,2543,5208,8814,2911,9445,8897,3637,7195,6581,6824,331],()=>t(50092));module.exports=o})();