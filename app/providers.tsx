'use client';

import { Clerk<PERSON>rov<PERSON>, useAuth } from '@clerk/nextjs';
import { dark } from '@clerk/themes';
import { useTheme } from 'next-themes';
import { ConvexProviderWithClerk } from 'convex/react-clerk';
import { ConvexReactClient } from 'convex/react';
import { ThemeProvider } from '@/components/theme-provider';
import { SolanaWalletProvider } from '@/components/wallet-provider';

const convex = new ConvexReactClient(process.env.NEXT_PUBLIC_CONVEX_URL!);

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange
    >
      <ConvexProviderWithClerk client={convex} useAuth={useAuth}>
        <ClerkProviderWithTheme>
          <SolanaWalletProvider>{children}</SolanaWalletProvider>
        </ClerkProviderWithTheme>
      </ConvexProviderWithClerk>
    </ThemeProvider>
  );
}

function ClerkProviderWithTheme({ children }: { children: React.ReactNode }) {
  const { resolvedTheme } = useTheme();

  return (
    <ClerkProvider
      appearance={{
        baseTheme: resolvedTheme === 'dark' ? dark : undefined,
        variables: {
          colorPrimary: 'oklch(0.6397 0.1720 36.4421)',
          colorText:
            resolvedTheme === 'dark'
              ? 'oklch(0.9219 0 0)'
              : 'oklch(0.3211 0 0)',
          colorBackground:
            resolvedTheme === 'dark'
              ? 'oklch(0.2598 0.0306 262.6666)'
              : 'oklch(0.9383 0.0042 236.4993)',
          colorInputBackground:
            resolvedTheme === 'dark'
              ? 'oklch(0.3843 0.0301 269.7337)'
              : 'oklch(0.9700 0.0029 264.5420)',
          colorInputText:
            resolvedTheme === 'dark'
              ? 'oklch(0.9219 0 0)'
              : 'oklch(0.3211 0 0)',
          borderRadius: '0.75rem',
          fontFamily: 'Inter, sans-serif',
        },
        elements: {
          formButtonPrimary:
            'bg-primary text-primary-foreground hover:bg-primary/90',
          card: 'bg-card text-card-foreground rounded-lg border border-border shadow-sm',
          formFieldInput: 'bg-input border-input',
          footerActionLink: 'text-primary hover:text-primary/80',
        },
      }}
    >
      {children}
    </ClerkProvider>
  );
}
