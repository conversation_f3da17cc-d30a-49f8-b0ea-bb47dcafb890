'use client';

import { useState, useMemo } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Calculator,
  TrendingUp,
  Calendar,
  Coins,
  Zap,
  Award,
  Target,
} from 'lucide-react';
import { TierBadge, type UserTier } from './tier-badge';
import { cn } from '@/lib/utils';

interface StakingRewardsCalculatorProps {
  currentStake?: number;
  currentTier?: UserTier;
  onStakeAmount?: (amount: number) => void;
  className?: string;
}

const tierThresholds = {
  FREE: 0,
  BRONZE: 1000,
  SILVER: 5000,
  DIAMOND: 25000,
};

const tierAPY = {
  FREE: 0,
  BRONZE: 5,
  SILVER: 8,
  DIAMOND: 12,
};

const tierAuraMultipliers = {
  FREE: 1,
  BRONZE: 2,
  SILVER: 5,
  DIAMOND: 10,
};

const getTierFromAmount = (amount: number): UserTier => {
  if (amount >= tierThresholds.DIAMOND) return 'DIAMOND';
  if (amount >= tierThresholds.SILVER) return 'SILVER';
  if (amount >= tierThresholds.BRONZE) return 'BRONZE';
  return 'FREE';
};

const formatNumber = (num: number) => {
  if (num >= 1000000) return `${(num / 1000000).toFixed(2)}M`;
  if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
  return num.toLocaleString();
};

export function StakingRewardsCalculator({
  currentStake = 0,
  currentTier = 'FREE',
  onStakeAmount,
  className,
}: StakingRewardsCalculatorProps) {
  const [stakeAmount, setStakeAmount] = useState(currentStake || 1000);
  const [timeframe, setTimeframe] = useState<'day' | 'month' | 'year'>('year');

  const calculatedTier = getTierFromAmount(stakeAmount);
  const apy = tierAPY[calculatedTier];
  const auraMultiplier = tierAuraMultipliers[calculatedTier];

  const rewards = useMemo(() => {
    const annualReward = stakeAmount * (apy / 100);
    const dailyReward = annualReward / 365;
    const monthlyReward = annualReward / 12;

    const dailyAura = 24 * auraMultiplier; // Base aura points per day
    const monthlyAura = dailyAura * 30;
    const yearlyAura = dailyAura * 365;

    return {
      daily: {
        bonk: dailyReward,
        aura: dailyAura,
      },
      monthly: {
        bonk: monthlyReward,
        aura: monthlyAura,
      },
      yearly: {
        bonk: annualReward,
        aura: yearlyAura,
      },
    };
  }, [stakeAmount, apy, auraMultiplier]);

  const currentRewards = rewards[timeframe];

  const handleStakeAmountChange = (value: string) => {
    const num = parseFloat(value) || 0;
    setStakeAmount(num);
  };

  const handleSliderChange = (value: number[]) => {
    setStakeAmount(value[0]);
  };

  const presetAmounts = [1000, 5000, 25000, 100000];

  return (
    <Card className={cn('w-full', className)}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calculator className="size-5" />
          Staking Rewards Calculator
        </CardTitle>
        <CardDescription>
          Calculate your potential earnings from staking BONK tokens
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Input Section */}
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="stake-amount">Stake Amount (BONK)</Label>
            <Input
              id="stake-amount"
              type="number"
              value={stakeAmount}
              onChange={(e) => handleStakeAmountChange(e.target.value)}
              placeholder="Enter BONK amount"
              className="text-lg"
            />
          </div>

          {/* Slider */}
          <div className="space-y-3">
            <div className="flex justify-between text-sm text-muted-foreground">
              <span>0</span>
              <span>100K</span>
            </div>
            <Slider
              value={[stakeAmount]}
              onValueChange={handleSliderChange}
              max={100000}
              min={0}
              step={100}
              className="w-full"
            />
          </div>

          {/* Preset Amounts */}
          <div className="flex flex-wrap gap-2">
            {presetAmounts.map((amount) => (
              <Button
                key={amount}
                variant="outline"
                size="sm"
                onClick={() => setStakeAmount(amount)}
                className={cn(
                  'transition-all',
                  stakeAmount === amount &&
                    'bg-primary text-primary-foreground',
                )}
              >
                {formatNumber(amount)}
              </Button>
            ))}
          </div>
        </div>

        {/* Tier Display */}
        <div className="flex items-center justify-between p-4 bg-muted/50 rounded-lg">
          <div className="space-y-1">
            <p className="text-sm text-muted-foreground">Resulting Tier</p>
            <TierBadge tier={calculatedTier} />
          </div>
          <div className="text-right space-y-1">
            <p className="text-sm text-muted-foreground">APY</p>
            <p className="text-2xl font-bold text-green-600">{apy}%</p>
          </div>
        </div>

        {/* Timeframe Selector */}
        <div className="space-y-3">
          <Label>Rewards Timeframe</Label>
          <Tabs
            value={timeframe}
            onValueChange={(value) => setTimeframe(value as typeof timeframe)}
          >
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="day">Daily</TabsTrigger>
              <TabsTrigger value="month">Monthly</TabsTrigger>
              <TabsTrigger value="year">Yearly</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>

        {/* Rewards Display */}
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* BONK Rewards */}
            <div className="p-4 bg-gradient-to-br from-orange-50 to-yellow-50 dark:from-orange-950/20 dark:to-yellow-950/20 border border-orange-200 dark:border-orange-800/30 rounded-lg">
              <div className="flex items-center gap-2 mb-3">
                <Coins className="size-5 text-orange-600" />
                <span className="font-semibold text-orange-800 dark:text-orange-200">
                  BONK Rewards
                </span>
              </div>
              <p className="text-3xl font-bold text-orange-700 dark:text-orange-300">
                {formatNumber(currentRewards.bonk)}
              </p>
              <p className="text-sm text-orange-600 dark:text-orange-400">
                per{' '}
                {timeframe === 'day'
                  ? 'day'
                  : timeframe === 'month'
                    ? 'month'
                    : 'year'}
              </p>
            </div>

            {/* Aura Points */}
            <div className="p-4 bg-gradient-to-br from-purple-50 to-blue-50 dark:from-purple-950/20 dark:to-blue-950/20 border border-purple-200 dark:border-purple-800/30 rounded-lg">
              <div className="flex items-center gap-2 mb-3">
                <Zap className="size-5 text-purple-600" />
                <span className="font-semibold text-purple-800 dark:text-purple-200">
                  Aura Points
                </span>
              </div>
              <p className="text-3xl font-bold text-purple-700 dark:text-purple-300">
                {formatNumber(currentRewards.aura)}
              </p>
              <p className="text-sm text-purple-600 dark:text-purple-400">
                per{' '}
                {timeframe === 'day'
                  ? 'day'
                  : timeframe === 'month'
                    ? 'month'
                    : 'year'}
              </p>
            </div>
          </div>

          {/* Detailed Breakdown */}
          <Tabs defaultValue="breakdown" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="breakdown">Breakdown</TabsTrigger>
              <TabsTrigger value="comparison">Tier Comparison</TabsTrigger>
            </TabsList>

            <TabsContent value="breakdown" className="space-y-4 mt-6">
              <div className="space-y-3">
                <div className="flex justify-between items-center p-3 bg-muted/50 rounded-lg">
                  <span className="text-sm">Stake Amount</span>
                  <span className="font-medium">
                    {formatNumber(stakeAmount)} BONK
                  </span>
                </div>
                <div className="flex justify-between items-center p-3 bg-muted/50 rounded-lg">
                  <span className="text-sm">Annual Percentage Yield</span>
                  <span className="font-medium text-green-600">{apy}%</span>
                </div>
                <div className="flex justify-between items-center p-3 bg-muted/50 rounded-lg">
                  <span className="text-sm">Aura Points Multiplier</span>
                  <span className="font-medium text-purple-600">
                    {auraMultiplier}x
                  </span>
                </div>
                <div className="flex justify-between items-center p-3 bg-muted/50 rounded-lg">
                  <span className="text-sm">Current Tier</span>
                  <TierBadge tier={calculatedTier} size="sm" />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="comparison" className="space-y-4 mt-6">
              <div className="space-y-3">
                {(Object.keys(tierThresholds) as UserTier[]).map((tier) => {
                  const tierRewards = stakeAmount * (tierAPY[tier] / 100);
                  const tierAura = 24 * 365 * tierAuraMultipliers[tier];
                  const isCurrentTier = tier === calculatedTier;

                  return (
                    <div
                      key={tier}
                      className={cn(
                        'p-3 border rounded-lg transition-all',
                        isCurrentTier && 'bg-primary/5 border-primary/30',
                      )}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <TierBadge tier={tier} size="sm" />
                        {isCurrentTier && (
                          <Badge variant="outline" className="text-xs">
                            Current
                          </Badge>
                        )}
                      </div>
                      <div className="grid grid-cols-3 gap-3 text-sm">
                        <div>
                          <p className="text-muted-foreground">Min Stake</p>
                          <p className="font-medium">
                            {formatNumber(tierThresholds[tier])}
                          </p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">Annual BONK</p>
                          <p className="font-medium">
                            {formatNumber(tierRewards)}
                          </p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">Annual Aura</p>
                          <p className="font-medium">
                            {formatNumber(tierAura)}
                          </p>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </TabsContent>
          </Tabs>
        </div>

        {/* Action Button */}
        {onStakeAmount && (
          <Button
            onClick={() => onStakeAmount(stakeAmount)}
            className="w-full"
            size="lg"
          >
            <Target className="size-4 mr-2" />
            Stake {formatNumber(stakeAmount)} BONK
          </Button>
        )}
      </CardContent>
    </Card>
  );
}
