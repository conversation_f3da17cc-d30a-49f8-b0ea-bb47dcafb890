import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { z } from 'zod';

// In a real implementation, you'd share this logic with the telegram bot
// For now, we'll create a simple API endpoint

const linkSchema = z.object({
  code: z.string().min(6).max(10),
});

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 },
      );
    }

    const body = await request.json();
    const { code } = linkSchema.parse(body);

    // Here you would normally verify the code with the telegram bot
    // For now, we'll simulate the verification

    // In production, this would:
    // 1. Check if the code exists and is valid
    // 2. Link the Clerk user ID with the Telegram user
    // 3. Update the Convex database
    // 4. Return success/failure

    // Simulate verification delay
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // Mock successful linking
    const success = Math.random() > 0.2; // 80% success rate for demo

    if (success) {
      return NextResponse.json({
        success: true,
        message:
          'Telegram account linked successfully! You can now use the bot.',
      });
    } else {
      return NextResponse.json(
        {
          success: false,
          error:
            'Invalid or expired linking code. Please generate a new code from the bot.',
        },
        { status: 400 },
      );
    }
  } catch (error) {
    console.error('Error linking Telegram account:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 },
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
}

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const code = searchParams.get('code');

  if (!code) {
    return NextResponse.json(
      { error: 'Linking code is required' },
      { status: 400 },
    );
  }

  // Check code status
  // In production, this would check with the telegram bot
  return NextResponse.json({
    valid: true,
    expired: false,
    used: false,
    instructions: [
      'Log in to your BonKai account',
      'Confirm the linking request',
      'Return to Telegram to start chatting',
    ],
  });
}
