'use client';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  BarChart3,
  TrendingUp,
  Calendar,
  Zap,
  Clock,
  Award,
  ExternalLink,
} from 'lucide-react';
import { TierBadge, type UserTier } from './tier-badge';
import { cn } from '@/lib/utils';

interface UsageStatsData {
  tier: UserTier;
  monthlyTokens: {
    current: number;
    limit: number;
    trend: number; // percentage change from previous month
  };
  hourlyRequests: {
    used: number;
    limit: number;
    remaining: number;
  };
  totalUsage: {
    allTimeTokens: number;
    totalRequests: number;
    daysActive: number;
  };
  achievements: {
    id: string;
    name: string;
    description: string;
    unlockedAt?: string;
  }[];
}

interface UsageStatsProps {
  data: UsageStatsData;
  onViewDetails?: () => void;
  onUpgrade?: () => void;
  className?: string;
}

const formatNumber = (num: number) => {
  if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
  if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
  return num.toString();
};

const getTrendIcon = (trend: number) => {
  if (trend > 0) return <TrendingUp className="size-3 text-green-600" />;
  if (trend < 0)
    return <TrendingUp className="size-3 text-red-600 rotate-180" />;
  return <TrendingUp className="size-3 text-gray-600" />;
};

const getTrendColor = (trend: number) => {
  if (trend > 0) return 'text-green-600 dark:text-green-400';
  if (trend < 0) return 'text-red-600 dark:text-red-400';
  return 'text-gray-600 dark:text-gray-400';
};

export function UsageStats({
  data,
  onViewDetails,
  onUpgrade,
  className,
}: UsageStatsProps) {
  const monthlyUsagePercentage =
    (data.monthlyTokens.current / data.monthlyTokens.limit) * 100;
  const hourlyUsagePercentage =
    (data.hourlyRequests.used / data.hourlyRequests.limit) * 100;

  return (
    <div className={cn('space-y-6', className)}>
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Monthly Tokens */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <Zap className="size-4 text-primary" />
                <span className="text-sm font-medium">Monthly Tokens</span>
              </div>
              <TierBadge tier={data.tier} size="sm" showIcon={false} />
            </div>
            <div className="space-y-2">
              <p className="text-2xl font-bold">
                {formatNumber(data.monthlyTokens.current)}
              </p>
              <div className="flex items-center justify-between text-xs">
                <span className="text-muted-foreground">
                  of {formatNumber(data.monthlyTokens.limit)} limit
                </span>
                <div
                  className={cn(
                    'flex items-center gap-1',
                    getTrendColor(data.monthlyTokens.trend),
                  )}
                >
                  {getTrendIcon(data.monthlyTokens.trend)}
                  <span>{Math.abs(data.monthlyTokens.trend).toFixed(1)}%</span>
                </div>
              </div>
              <div className="w-full bg-muted rounded-full h-1.5">
                <div
                  className="h-1.5 bg-primary rounded-full transition-all duration-300"
                  style={{ width: `${Math.min(monthlyUsagePercentage, 100)}%` }}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Hourly Requests */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2 mb-2">
              <Clock className="size-4 text-blue-600" />
              <span className="text-sm font-medium">Hourly Requests</span>
            </div>
            <div className="space-y-2">
              <p className="text-2xl font-bold">
                {data.hourlyRequests.remaining}
              </p>
              <div className="flex items-center justify-between text-xs">
                <span className="text-muted-foreground">
                  remaining this hour
                </span>
                <Badge variant="outline" className="text-xs">
                  {data.hourlyRequests.used}/{data.hourlyRequests.limit}
                </Badge>
              </div>
              <div className="w-full bg-muted rounded-full h-1.5">
                <div
                  className={cn(
                    'h-1.5 rounded-full transition-all duration-300',
                    hourlyUsagePercentage >= 80
                      ? 'bg-yellow-500'
                      : 'bg-blue-500',
                  )}
                  style={{ width: `${Math.min(hourlyUsagePercentage, 100)}%` }}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Total Usage */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2 mb-2">
              <BarChart3 className="size-4 text-green-600" />
              <span className="text-sm font-medium">All-Time Usage</span>
            </div>
            <div className="space-y-1">
              <p className="text-2xl font-bold">
                {formatNumber(data.totalUsage.allTimeTokens)}
              </p>
              <p className="text-xs text-muted-foreground">tokens processed</p>
              <div className="pt-1 space-y-1">
                <div className="flex justify-between text-xs">
                  <span className="text-muted-foreground">Requests:</span>
                  <span>{formatNumber(data.totalUsage.totalRequests)}</span>
                </div>
                <div className="flex justify-between text-xs">
                  <span className="text-muted-foreground">Active days:</span>
                  <span>{data.totalUsage.daysActive}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Achievements */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2 mb-2">
              <Award className="size-4 text-purple-600" />
              <span className="text-sm font-medium">Achievements</span>
            </div>
            <div className="space-y-1">
              <p className="text-2xl font-bold">
                {data.achievements.filter((a) => a.unlockedAt).length}
              </p>
              <p className="text-xs text-muted-foreground">
                of {data.achievements.length} unlocked
              </p>
              <div className="pt-1">
                {data.achievements.slice(0, 2).map((achievement, index) => (
                  <div key={index} className="flex items-center gap-1 text-xs">
                    <Award className="size-3" />
                    <span
                      className={
                        achievement.unlockedAt
                          ? 'text-foreground'
                          : 'text-muted-foreground'
                      }
                    >
                      {achievement.name}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed View */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="size-5" />
                Usage Analytics
              </CardTitle>
              <CardDescription>
                Detailed breakdown of your BonKai platform usage
              </CardDescription>
            </div>
            {onViewDetails && (
              <Button variant="outline" size="sm" onClick={onViewDetails}>
                <ExternalLink className="size-4 mr-2" />
                View Details
              </Button>
            )}
          </div>
        </CardHeader>

        <CardContent>
          <Tabs defaultValue="overview" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="limits">Limits</TabsTrigger>
              <TabsTrigger value="achievements">Achievements</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4 mt-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h4 className="font-semibold flex items-center gap-2">
                    <Calendar className="size-4" />
                    Monthly Summary
                  </h4>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center p-3 bg-muted/50 rounded-lg">
                      <span className="text-sm">Tokens Used</span>
                      <span className="font-medium">
                        {formatNumber(data.monthlyTokens.current)}
                      </span>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-muted/50 rounded-lg">
                      <span className="text-sm">Monthly Limit</span>
                      <span className="font-medium">
                        {formatNumber(data.monthlyTokens.limit)}
                      </span>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-muted/50 rounded-lg">
                      <span className="text-sm">Usage Trend</span>
                      <div
                        className={cn(
                          'flex items-center gap-1',
                          getTrendColor(data.monthlyTokens.trend),
                        )}
                      >
                        {getTrendIcon(data.monthlyTokens.trend)}
                        <span className="font-medium">
                          {Math.abs(data.monthlyTokens.trend).toFixed(1)}%
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-semibold flex items-center gap-2">
                    <Clock className="size-4" />
                    Request Limits
                  </h4>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center p-3 bg-muted/50 rounded-lg">
                      <span className="text-sm">Hourly Used</span>
                      <span className="font-medium">
                        {data.hourlyRequests.used}
                      </span>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-muted/50 rounded-lg">
                      <span className="text-sm">Hourly Limit</span>
                      <span className="font-medium">
                        {data.hourlyRequests.limit}
                      </span>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-muted/50 rounded-lg">
                      <span className="text-sm">Remaining</span>
                      <span className="font-medium text-green-600">
                        {data.hourlyRequests.remaining}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="limits" className="space-y-4 mt-6">
              <div className="space-y-4">
                <h4 className="font-semibold">Current Tier Limits</h4>
                <div className="grid gap-4">
                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-3">
                      <span className="font-medium">Monthly Token Limit</span>
                      <TierBadge tier={data.tier} size="sm" />
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-muted-foreground">
                          Current Usage
                        </span>
                        <span>
                          {formatNumber(data.monthlyTokens.current)} tokens
                        </span>
                      </div>
                      <div className="w-full bg-muted rounded-full h-2">
                        <div
                          className="h-2 bg-primary rounded-full"
                          style={{
                            width: `${Math.min(monthlyUsagePercentage, 100)}%`,
                          }}
                        />
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-muted-foreground">Limit</span>
                        <span>
                          {formatNumber(data.monthlyTokens.limit)} tokens
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-3">
                      <span className="font-medium">Hourly Request Limit</span>
                      <Badge variant="outline">
                        {data.hourlyRequests.remaining} remaining
                      </Badge>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-muted-foreground">
                          Used This Hour
                        </span>
                        <span>{data.hourlyRequests.used} requests</span>
                      </div>
                      <div className="w-full bg-muted rounded-full h-2">
                        <div
                          className={cn(
                            'h-2 rounded-full',
                            hourlyUsagePercentage >= 80
                              ? 'bg-yellow-500'
                              : 'bg-blue-500',
                          )}
                          style={{
                            width: `${Math.min(hourlyUsagePercentage, 100)}%`,
                          }}
                        />
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-muted-foreground">
                          Hourly Limit
                        </span>
                        <span>{data.hourlyRequests.limit} requests</span>
                      </div>
                    </div>
                  </div>
                </div>

                {onUpgrade && (
                  <div className="p-4 bg-primary/5 border border-primary/20 rounded-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">Want Higher Limits?</p>
                        <p className="text-sm text-muted-foreground">
                          Upgrade your tier for increased capacity
                        </p>
                      </div>
                      <Button onClick={onUpgrade}>Upgrade Tier</Button>
                    </div>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="achievements" className="space-y-4 mt-6">
              <div className="grid gap-3">
                {data.achievements.map((achievement, index) => (
                  <div
                    key={index}
                    className={cn(
                      'flex items-center gap-3 p-3 rounded-lg border',
                      achievement.unlockedAt
                        ? 'bg-green-50 border-green-200 dark:bg-green-950/20 dark:border-green-800/30'
                        : 'bg-muted/50 border-muted',
                    )}
                  >
                    <Award
                      className={cn(
                        'size-6',
                        achievement.unlockedAt
                          ? 'text-green-600'
                          : 'text-muted-foreground',
                      )}
                    />
                    <div className="flex-1">
                      <p
                        className={cn(
                          'font-medium',
                          achievement.unlockedAt
                            ? 'text-green-800 dark:text-green-200'
                            : 'text-muted-foreground',
                        )}
                      >
                        {achievement.name}
                      </p>
                      <p
                        className={cn(
                          'text-sm',
                          achievement.unlockedAt
                            ? 'text-green-700 dark:text-green-300'
                            : 'text-muted-foreground',
                        )}
                      >
                        {achievement.description}
                      </p>
                    </div>
                    {achievement.unlockedAt && (
                      <Badge variant="secondary" className="text-xs">
                        Unlocked
                      </Badge>
                    )}
                  </div>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
