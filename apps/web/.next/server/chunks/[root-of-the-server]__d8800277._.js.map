{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/db/queries.ts"], "sourcesContent": ["// Temporary stub functions for legacy compatibility\n// These should be replaced with Convex queries in the full migration\n\nexport async function getUser(email: string): Promise<any[]> {\n  // TODO: Replace with Convex query\n  console.warn(\n    'getUser called with legacy implementation - needs Convex migration',\n  );\n  return [];\n}\n\nexport async function createGuestUser(): Promise<any[]> {\n  // TODO: Replace with Convex mutation\n  console.warn(\n    'createGuestU<PERSON> called with legacy implementation - needs Convex migration',\n  );\n  return [\n    {\n      id: `guest-${Date.now()}`,\n      email: null,\n      password: null,\n    },\n  ];\n}\n\nexport async function saveChat(): Promise<any> {\n  // TODO: Replace with Convex mutation\n  console.warn(\n    'saveChat called with legacy implementation - needs Convex migration',\n  );\n  return null;\n}\n\nexport async function deleteChatById(): Promise<any> {\n  // TODO: Replace with Convex mutation\n  console.warn(\n    'deleteChatById called with legacy implementation - needs Convex migration',\n  );\n  return null;\n}\n\nexport async function getChatsByUserId(): Promise<any[]> {\n  // TODO: Replace with Convex query\n  console.warn(\n    'getChatsByUserId called with legacy implementation - needs Convex migration',\n  );\n  return [];\n}\n\nexport async function getChatById(): Promise<any> {\n  // TODO: Replace with Convex query\n  console.warn(\n    'getChatById called with legacy implementation - needs Convex migration',\n  );\n  return null;\n}\n\nexport async function saveMessages(): Promise<any> {\n  // TODO: Replace with Convex mutation\n  console.warn(\n    'saveMessages called with legacy implementation - needs Convex migration',\n  );\n  return null;\n}\n\nexport async function getMessagesByChatId(): Promise<any[]> {\n  // TODO: Replace with Convex query\n  console.warn(\n    'getMessagesByChatId called with legacy implementation - needs Convex migration',\n  );\n  return [];\n}\n\nexport async function voteMessage(): Promise<any> {\n  // TODO: Replace with Convex mutation\n  console.warn(\n    'voteMessage called with legacy implementation - needs Convex migration',\n  );\n  return null;\n}\n\nexport async function getVotesByChatId(): Promise<any[]> {\n  // TODO: Replace with Convex query\n  console.warn(\n    'getVotesByChatId called with legacy implementation - needs Convex migration',\n  );\n  return [];\n}\n\nexport async function saveDocument(): Promise<any> {\n  // TODO: Replace with Convex mutation\n  console.warn(\n    'saveDocument called with legacy implementation - needs Convex migration',\n  );\n  return null;\n}\n\nexport async function getDocumentById(): Promise<any> {\n  // TODO: Replace with Convex query\n  console.warn(\n    'getDocumentById called with legacy implementation - needs Convex migration',\n  );\n  return null;\n}\n\nexport async function getDocumentsByUserId(): Promise<any[]> {\n  // TODO: Replace with Convex query\n  console.warn(\n    'getDocumentsByUserId called with legacy implementation - needs Convex migration',\n  );\n  return [];\n}\n\nexport async function deleteDocumentsByIdAfterTimestamp(): Promise<any> {\n  // TODO: Replace with Convex mutation\n  console.warn(\n    'deleteDocumentsByIdAfterTimestamp called with legacy implementation - needs Convex migration',\n  );\n  return null;\n}\n\nexport async function saveSuggestions(): Promise<any> {\n  // TODO: Replace with Convex mutation\n  console.warn(\n    'saveSuggestions called with legacy implementation - needs Convex migration',\n  );\n  return null;\n}\n\nexport async function getSuggestionsByDocumentId(): Promise<any[]> {\n  // TODO: Replace with Convex query\n  console.warn(\n    'getSuggestionsByDocumentId called with legacy implementation - needs Convex migration',\n  );\n  return [];\n}\n\nexport async function createUser(): Promise<any> {\n  // TODO: Replace with Convex mutation\n  console.warn(\n    'createUser called with legacy implementation - needs Convex migration',\n  );\n  return null;\n}\n\nexport async function getMessageById(): Promise<any> {\n  // TODO: Replace with Convex query\n  console.warn(\n    'getMessageById called with legacy implementation - needs Convex migration',\n  );\n  return null;\n}\n\nexport async function deleteMessagesByChatIdAfterTimestamp(): Promise<any> {\n  // TODO: Replace with Convex mutation\n  console.warn(\n    'deleteMessagesByChatIdAfterTimestamp called with legacy implementation - needs Convex migration',\n  );\n  return null;\n}\n\nexport async function updateChatVisiblityById(): Promise<any> {\n  // TODO: Replace with Convex mutation\n  console.warn(\n    'updateChatVisiblityById called with legacy implementation - needs Convex migration',\n  );\n  return null;\n}\n\nexport async function getStreamIdsByChatId(): Promise<any[]> {\n  // TODO: Replace with Convex query\n  console.warn(\n    'getStreamIdsByChatId called with legacy implementation - needs Convex migration',\n  );\n  return [];\n}\n\nexport async function getMessageCountByUserId(): Promise<number> {\n  // TODO: Replace with Convex query\n  console.warn(\n    'getMessageCountByUserId called with legacy implementation - needs Convex migration',\n  );\n  return 0;\n}\n\nexport async function createStreamId(): Promise<string> {\n  // TODO: Replace with Convex mutation\n  console.warn(\n    'createStreamId called with legacy implementation - needs Convex migration',\n  );\n  return `stream-${Date.now()}`;\n}\n\nexport async function getDocumentsById(): Promise<any[]> {\n  // TODO: Replace with Convex query\n  console.warn(\n    'getDocumentsById called with legacy implementation - needs Convex migration',\n  );\n  return [];\n}\n"], "names": [], "mappings": "AAAA,oDAAoD;AACpD,qEAAqE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE9D,eAAe,QAAQ,KAAa;IACzC,kCAAkC;IAClC,QAAQ,IAAI,CACV;IAEF,OAAO,EAAE;AACX;AAEO,eAAe;IACpB,qCAAqC;IACrC,QAAQ,IAAI,CACV;IAEF,OAAO;QACL;YACE,IAAI,CAAC,MAAM,EAAE,KAAK,GAAG,IAAI;YACzB,OAAO;YACP,UAAU;QACZ;KACD;AACH;AAEO,eAAe;IACpB,qCAAqC;IACrC,QAAQ,IAAI,CACV;IAEF,OAAO;AACT;AAEO,eAAe;IACpB,qCAAqC;IACrC,QAAQ,IAAI,CACV;IAEF,OAAO;AACT;AAEO,eAAe;IACpB,kCAAkC;IAClC,QAAQ,IAAI,CACV;IAEF,OAAO,EAAE;AACX;AAEO,eAAe;IACpB,kCAAkC;IAClC,QAAQ,IAAI,CACV;IAEF,OAAO;AACT;AAEO,eAAe;IACpB,qCAAqC;IACrC,QAAQ,IAAI,CACV;IAEF,OAAO;AACT;AAEO,eAAe;IACpB,kCAAkC;IAClC,QAAQ,IAAI,CACV;IAEF,OAAO,EAAE;AACX;AAEO,eAAe;IACpB,qCAAqC;IACrC,QAAQ,IAAI,CACV;IAEF,OAAO;AACT;AAEO,eAAe;IACpB,kCAAkC;IAClC,QAAQ,IAAI,CACV;IAEF,OAAO,EAAE;AACX;AAEO,eAAe;IACpB,qCAAqC;IACrC,QAAQ,IAAI,CACV;IAEF,OAAO;AACT;AAEO,eAAe;IACpB,kCAAkC;IAClC,QAAQ,IAAI,CACV;IAEF,OAAO;AACT;AAEO,eAAe;IACpB,kCAAkC;IAClC,QAAQ,IAAI,CACV;IAEF,OAAO,EAAE;AACX;AAEO,eAAe;IACpB,qCAAqC;IACrC,QAAQ,IAAI,CACV;IAEF,OAAO;AACT;AAEO,eAAe;IACpB,qCAAqC;IACrC,QAAQ,IAAI,CACV;IAEF,OAAO;AACT;AAEO,eAAe;IACpB,kCAAkC;IAClC,QAAQ,IAAI,CACV;IAEF,OAAO,EAAE;AACX;AAEO,eAAe;IACpB,qCAAqC;IACrC,QAAQ,IAAI,CACV;IAEF,OAAO;AACT;AAEO,eAAe;IACpB,kCAAkC;IAClC,QAAQ,IAAI,CACV;IAEF,OAAO;AACT;AAEO,eAAe;IACpB,qCAAqC;IACrC,QAAQ,IAAI,CACV;IAEF,OAAO;AACT;AAEO,eAAe;IACpB,qCAAqC;IACrC,QAAQ,IAAI,CACV;IAEF,OAAO;AACT;AAEO,eAAe;IACpB,kCAAkC;IAClC,QAAQ,IAAI,CACV;IAEF,OAAO,EAAE;AACX;AAEO,eAAe;IACpB,kCAAkC;IAClC,QAAQ,IAAI,CACV;IAEF,OAAO;AACT;AAEO,eAAe;IACpB,qCAAqC;IACrC,QAAQ,IAAI,CACV;IAEF,OAAO,CAAC,OAAO,EAAE,KAAK,GAAG,IAAI;AAC/B;AAEO,eAAe;IACpB,kCAAkC;IAClC,QAAQ,IAAI,CACV;IAEF,OAAO,EAAE;AACX", "debugId": null}}, {"offset": {"line": 270, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/%28auth%29/auth.config.ts"], "sourcesContent": ["import type { NextAuthConfig } from 'next-auth';\n\nexport const authConfig = {\n  pages: {\n    signIn: '/login',\n    newUser: '/',\n  },\n  providers: [\n    // added later in auth.ts since it requires bcrypt which is only compatible with Node.js\n    // while this file is also used in non-Node.js environments\n  ],\n  callbacks: {},\n} satisfies NextAuthConfig;\n"], "names": [], "mappings": ";;;AAEO,MAAM,aAAa;IACxB,OAAO;QACL,QAAQ;QACR,SAAS;IACX;IACA,WAAW,EAGV;IACD,WAAW,CAAC;AACd", "debugId": null}}, {"offset": {"line": 287, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/db/utils.ts"], "sourcesContent": ["import { genSalt, hash } from 'bcrypt-ts';\n\nexport function generateDummyPassword(): string {\n  // Generate a dummy password hash for timing attack protection\n  return '$2b$10$K1V5qz0cZGaJGDwQO4CQuu4Xr5bZnQfCqFkC7l0qoF5zVhsVz7/.2';\n}\n\nexport async function hashPassword(password: string): Promise<string> {\n  const salt = await genSalt(10);\n  return await hash(password, salt);\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEO,SAAS;IACd,8DAA8D;IAC9D,OAAO;AACT;AAEO,eAAe,aAAa,QAAgB;IACjD,MAAM,OAAO,MAAM,CAAA,GAAA,+IAAA,CAAA,UAAO,AAAD,EAAE;IAC3B,OAAO,MAAM,CAAA,GAAA,+IAAA,CAAA,OAAI,AAAD,EAAE,UAAU;AAC9B", "debugId": null}}, {"offset": {"line": 307, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/lib/constants.ts"], "sourcesContent": ["import { generateDummyPassword } from './db/utils';\n\nexport const isProductionEnvironment = process.env.NODE_ENV === 'production';\nexport const isDevelopmentEnvironment = process.env.NODE_ENV === 'development';\nexport const isTestEnvironment = Boolean(\n  process.env.PLAYWRIGHT_TEST_BASE_URL ||\n    process.env.PLAYWRIGHT ||\n    process.env.CI_PLAYWRIGHT,\n);\n\nexport const guestRegex = /^guest-\\d+$/;\n\nexport const DUMMY_PASSWORD = generateDummyPassword();\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAEO,MAAM,0BAA0B,oDAAyB;AACzD,MAAM,2BAA2B,oDAAyB;AAC1D,MAAM,oBAAoB,QAC/B,QAAQ,GAAG,CAAC,wBAAwB,IAClC,QAAQ,GAAG,CAAC,UAAU,IACtB,QAAQ,GAAG,CAAC,aAAa;AAGtB,MAAM,aAAa;AAEnB,MAAM,iBAAiB,CAAA,GAAA,mIAAA,CAAA,wBAAqB,AAAD", "debugId": null}}, {"offset": {"line": 327, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/%28auth%29/auth.ts"], "sourcesContent": ["import { compare } from 'bcrypt-ts';\nimport NextAuth, { type DefaultSession } from 'next-auth';\nimport Credentials from 'next-auth/providers/credentials';\nimport { createGuestUser, getUser } from '@/lib/db/queries';\nimport { authConfig } from './auth.config';\nimport { DUMMY_PASSWORD } from '@/lib/constants';\nimport type { DefaultJWT } from 'next-auth/jwt';\n\nexport type UserType = 'guest' | 'regular';\n\ndeclare module 'next-auth' {\n  interface Session extends DefaultSession {\n    user: {\n      id: string;\n      type: UserType;\n    } & DefaultSession['user'];\n  }\n\n  interface User {\n    id?: string;\n    email?: string | null;\n    type: UserType;\n  }\n}\n\ndeclare module 'next-auth/jwt' {\n  interface JWT extends DefaultJWT {\n    id: string;\n    type: UserType;\n  }\n}\n\nexport const {\n  handlers: { GET, POST },\n  auth,\n  signIn,\n  signOut,\n} = NextAuth({\n  ...authConfig,\n  providers: [\n    Credentials({\n      credentials: {},\n      async authorize({ email, password }: any) {\n        const users = await getUser(email);\n\n        if (users.length === 0) {\n          await compare(password, DUMMY_PASSWORD);\n          return null;\n        }\n\n        const [user] = users;\n\n        if (!user.password) {\n          await compare(password, DUMMY_PASSWORD);\n          return null;\n        }\n\n        const passwordsMatch = await compare(password, user.password);\n\n        if (!passwordsMatch) return null;\n\n        return { ...user, type: 'regular' };\n      },\n    }),\n    Credentials({\n      id: 'guest',\n      credentials: {},\n      async authorize() {\n        const [guestUser] = await createGuestUser();\n        return { ...guestUser, type: 'guest' };\n      },\n    }),\n  ],\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.id = user.id as string;\n        token.type = user.type;\n      }\n\n      return token;\n    },\n    async session({ session, token }) {\n      if (session.user) {\n        session.user.id = token.id;\n        session.user.type = token.type;\n      }\n\n      return session;\n    },\n  },\n});\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;;;;;;;AA2BO,MAAM,EACX,UAAU,EAAE,GAAG,EAAE,IAAI,EAAE,EACvB,IAAI,EACJ,MAAM,EACN,OAAO,EACR,GAAG,CAAA,GAAA,uJAAA,CAAA,UAAQ,AAAD,EAAE;IACX,GAAG,kJAAA,CAAA,aAAU;IACb,WAAW;QACT,CAAA,GAAA,4JAAA,CAAA,UAAW,AAAD,EAAE;YACV,aAAa,CAAC;YACd,MAAM,WAAU,EAAE,KAAK,EAAE,QAAQ,EAAO;gBACtC,MAAM,QAAQ,MAAM,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD,EAAE;gBAE5B,IAAI,MAAM,MAAM,KAAK,GAAG;oBACtB,MAAM,CAAA,GAAA,+IAAA,CAAA,UAAO,AAAD,EAAE,UAAU,iIAAA,CAAA,iBAAc;oBACtC,OAAO;gBACT;gBAEA,MAAM,CAAC,KAAK,GAAG;gBAEf,IAAI,CAAC,KAAK,QAAQ,EAAE;oBAClB,MAAM,CAAA,GAAA,+IAAA,CAAA,UAAO,AAAD,EAAE,UAAU,iIAAA,CAAA,iBAAc;oBACtC,OAAO;gBACT;gBAEA,MAAM,iBAAiB,MAAM,CAAA,GAAA,+IAAA,CAAA,UAAO,AAAD,EAAE,UAAU,KAAK,QAAQ;gBAE5D,IAAI,CAAC,gBAAgB,OAAO;gBAE5B,OAAO;oBAAE,GAAG,IAAI;oBAAE,MAAM;gBAAU;YACpC;QACF;QACA,CAAA,GAAA,4JAAA,CAAA,UAAW,AAAD,EAAE;YACV,IAAI;YACJ,aAAa,CAAC;YACd,MAAM;gBACJ,MAAM,CAAC,UAAU,GAAG,MAAM,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;gBACxC,OAAO;oBAAE,GAAG,SAAS;oBAAE,MAAM;gBAAQ;YACvC;QACF;KACD;IACD,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,EAAE,GAAG,KAAK,EAAE;gBAClB,MAAM,IAAI,GAAG,KAAK,IAAI;YACxB;YAEA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,QAAQ,IAAI,EAAE;gBAChB,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,EAAE;gBAC1B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;YAChC;YAEA,OAAO;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 407, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/%28auth%29/api/auth/%5B...nextauth%5D/route.ts"], "sourcesContent": ["export { GET, POST } from '@/app/(auth)/auth';\n"], "names": [], "mappings": ";AAAA", "debugId": null}}]}