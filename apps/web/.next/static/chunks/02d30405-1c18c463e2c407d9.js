"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1971],{81801:(e,t,r)=>{r.d(t,{rL:()=>eS,vl:()=>eM});var a,s,o,n,i,l,u,c,p,d,h,m,f,g,y=r(90326),v=r(38654),b=r(49262),I=Object.defineProperty,E="AI_InvalidArgumentError",w=`vercel.ai.error.${E}`,T=Symbol.for(w),x=class extends y.bD{constructor({parameter:e,value:t,message:r}){super({name:E,message:`Invalid argument for parameter ${e}: ${r}`}),this[a]=!0,this.parameter=e,this.value=t}static isInstance(e){return y.bD.hasMarker(e,w)}};a=T;var S=Symbol.for("vercel.ai.error.AI_InvalidStreamPartError");y.bD;var A="AI_InvalidToolInputError",R=`vercel.ai.error.${A}`,j=Symbol.for(R),k=class extends y.bD{constructor({toolInput:e,toolName:t,cause:r,message:a=`Invalid input for tool ${t}: ${(0,y.u1)(r)}`}){super({name:A,message:a,cause:r}),this[s]=!0,this.toolInput=e,this.toolName=t}static isInstance(e){return y.bD.hasMarker(e,R)}};s=j;var _="vercel.ai.error.AI_MCPClientError",C=Symbol.for(_),N=class extends y.bD{constructor({name:e="MCPClientError",message:t,cause:r}){super({name:e,message:t,cause:r}),this[o]=!0}static isInstance(e){return y.bD.hasMarker(e,_)}};o=C;var O="AI_NoImageGeneratedError",M=`vercel.ai.error.${O}`,D=Symbol.for(M);y.bD,n=D;var Y="AI_NoObjectGeneratedError",P=`vercel.ai.error.${Y}`,$=Symbol.for(P),U=class extends y.bD{constructor({message:e="No object generated.",cause:t,text:r,response:a,usage:s,finishReason:o}){super({name:Y,message:e,cause:t}),this[i]=!0,this.text=r,this.response=a,this.usage=s,this.finishReason=o}static isInstance(e){return y.bD.hasMarker(e,P)}};i=$;var F="AI_NoOutputSpecifiedError",L=`vercel.ai.error.${F}`,q=Symbol.for(L),B=class extends y.bD{constructor({message:e="No output specified."}={}){super({name:F,message:e}),this[l]=!0}static isInstance(e){return y.bD.hasMarker(e,L)}};l=q;var J="AI_NoSuchToolError",K=`vercel.ai.error.${J}`,V=Symbol.for(K),z=class extends y.bD{constructor({toolName:e,availableTools:t,message:r=`Model tried to call unavailable tool '${e}'. ${void 0===t?"No tools are available.":`Available tools: ${t.join(", ")}.`}`}){super({name:J,message:r}),this[u]=!0,this.toolName=e,this.availableTools=t}static isInstance(e){return y.bD.hasMarker(e,K)}};u=V;var G="AI_ToolCallRepairError",W=`vercel.ai.error.${G}`,Z=Symbol.for(W),H=class extends y.bD{constructor({cause:e,originalError:t,message:r=`Error repairing tool call: ${(0,y.u1)(e)}`}){super({name:G,message:r,cause:e}),this[c]=!0,this.originalError=t}static isInstance(e){return y.bD.hasMarker(e,W)}};c=Z;var Q="AI_InvalidDataContentError",X=`vercel.ai.error.${Q}`,ee=Symbol.for(X);y.bD,p=ee;var et="AI_InvalidMessageRoleError",er=`vercel.ai.error.${et}`,ea=Symbol.for(er),es=class extends y.bD{constructor({role:e,message:t=`Invalid message role: '${e}'. Must be one of: "system", "user", "assistant", "tool".`}){super({name:et,message:t}),this[d]=!0,this.role=e}static isInstance(e){return y.bD.hasMarker(e,er)}};d=ea;var eo="AI_MessageConversionError",en=`vercel.ai.error.${eo}`,ei=Symbol.for(en);y.bD,h=ei;var el="AI_DownloadError",eu=`vercel.ai.error.${el}`,ec=Symbol.for(eu),ep=class extends y.bD{constructor({url:e,statusCode:t,statusText:r,cause:a,message:s=null==a?`Failed to download ${e}: ${t} ${r}`:`Failed to download ${e}: ${a}`}){super({name:el,message:s,cause:a}),this[m]=!0,this.url=e,this.statusCode=t,this.statusText=r}static isInstance(e){return y.bD.hasMarker(e,eu)}};m=ec;var ed="AI_RetryError",eh=`vercel.ai.error.${ed}`,em=Symbol.for(eh),ef=class extends y.bD{constructor({message:e,reason:t,errors:r}){super({name:ed,message:e}),this[f]=!0,this.reason=t,this.errors=r,this.lastError=r[r.length-1]}static isInstance(e){return y.bD.hasMarker(e,eh)}};function eg(e,t){let r=new Headers(null!=e?e:{});for(let[e,a]of Object.entries(t))r.has(e)||r.set(e,a);return r}function ey({status:e,statusText:t,headers:r,textStream:a}){return new Response(a.pipeThrough(new TextEncoderStream),{status:null!=e?e:200,statusText:t,headers:eg(r,{"content-type":"text/plain; charset=utf-8"})})}function ev({response:e,status:t,statusText:r,headers:a,stream:s}){e.writeHead(null!=t?t:200,r,a);let o=s.getReader();(async()=>{try{for(;;){let{done:t,value:r}=await o.read();if(t)break;e.write(r)}}catch(e){throw e}finally{e.end()}})()}function eb({response:e,status:t,statusText:r,headers:a,textStream:s}){ev({response:e,status:t,statusText:r,headers:Object.fromEntries(eg(a,{"content-type":"text/plain; charset=utf-8"}).entries()),stream:s.pipeThrough(new TextEncoderStream)})}f=em;var eI=v.KC([v.re({type:v.eu("text-start"),id:v.Yj()}),v.re({type:v.eu("text-delta"),id:v.Yj(),delta:v.Yj()}),v.re({type:v.eu("text-end"),id:v.Yj()}),v.re({type:v.eu("error"),errorText:v.Yj()}),v.re({type:v.eu("tool-input-start"),toolCallId:v.Yj(),toolName:v.Yj(),providerExecuted:v.zM().optional()}),v.re({type:v.eu("tool-input-delta"),toolCallId:v.Yj(),inputTextDelta:v.Yj()}),v.re({type:v.eu("tool-input-available"),toolCallId:v.Yj(),toolName:v.Yj(),input:v.L5(),providerExecuted:v.zM().optional()}),v.re({type:v.eu("tool-output-available"),toolCallId:v.Yj(),output:v.L5(),providerExecuted:v.zM().optional()}),v.re({type:v.eu("tool-output-error"),toolCallId:v.Yj(),errorText:v.Yj(),providerExecuted:v.zM().optional()}),v.re({type:v.eu("reasoning"),text:v.Yj(),providerMetadata:v.g1(v.Yj(),v.bz()).optional()}),v.re({type:v.eu("reasoning-start"),id:v.Yj(),providerMetadata:v.g1(v.Yj(),v.bz()).optional()}),v.re({type:v.eu("reasoning-delta"),id:v.Yj(),delta:v.Yj(),providerMetadata:v.g1(v.Yj(),v.bz()).optional()}),v.re({type:v.eu("reasoning-end"),id:v.Yj(),providerMetadata:v.g1(v.Yj(),v.bz()).optional()}),v.re({type:v.eu("reasoning-part-finish")}),v.re({type:v.eu("source-url"),sourceId:v.Yj(),url:v.Yj(),title:v.Yj().optional(),providerMetadata:v.bz().optional()}),v.re({type:v.eu("source-document"),sourceId:v.Yj(),mediaType:v.Yj(),title:v.Yj(),filename:v.Yj().optional(),providerMetadata:v.bz().optional()}),v.re({type:v.eu("file"),url:v.Yj(),mediaType:v.Yj()}),v.re({type:v.Yj().startsWith("data-"),id:v.Yj().optional(),data:v.L5(),transient:v.zM().optional()}),v.re({type:v.eu("start-step")}),v.re({type:v.eu("finish-step")}),v.re({type:v.eu("start"),messageId:v.Yj().optional(),messageMetadata:v.L5().optional()}),v.re({type:v.eu("finish"),messageMetadata:v.L5().optional()}),v.re({type:v.eu("message-metadata"),messageMetadata:v.L5()})]);async function eE({stream:e,onError:t}){let r=e.getReader();try{for(;;){let{done:e}=await r.read();if(e)break}}catch(e){null==t||t(e)}finally{r.releaseLock()}}var ew=class{constructor(){this.queue=[],this.isProcessing=!1}async processQueue(){if(!this.isProcessing){for(this.isProcessing=!0;this.queue.length>0;)await this.queue[0](),this.queue.shift();this.isProcessing=!1}}async run(e){return new Promise((t,r)=>{this.queue.push(async()=>{try{await e(),t()}catch(e){r(e)}}),this.processQueue()})}};async function eT(e){if(null==e)return[];if(!globalThis.FileList||!(e instanceof globalThis.FileList))throw Error("FileList is not supported in the current environment");return Promise.all(Array.from(e).map(async e=>{let{name:t,type:r}=e;return{type:"file",mediaType:r,filename:t,url:await new Promise((t,r)=>{let a=new FileReader;a.onload=e=>{var r;t(null==(r=e.target)?void 0:r.result)},a.onerror=e=>r(e),a.readAsDataURL(e)})}}))}var ex=class{constructor({api:e="/api/chat",credentials:t,headers:r,body:a,fetch:s,prepareSendMessagesRequest:o,prepareReconnectToStreamRequest:n}){this.api=e,this.credentials=t,this.headers=r,this.body=a,this.fetch=s,this.prepareSendMessagesRequest=o,this.prepareReconnectToStreamRequest=n}async sendMessages({abortSignal:e,...t}){var r,a,s,o,n;let i=await (null==(r=this.prepareSendMessagesRequest)?void 0:r.call(this,{api:this.api,id:t.chatId,messages:t.messages,body:{...this.body,...t.body},headers:{...this.headers,...t.headers},credentials:this.credentials,requestMetadata:t.metadata,trigger:t.trigger,messageId:t.messageId})),l=null!=(a=null==i?void 0:i.api)?a:this.api,u=(null==i?void 0:i.headers)!==void 0?i.headers:{...this.headers,...t.headers},c=(null==i?void 0:i.body)!==void 0?i.body:{...this.body,...t.body,id:t.chatId,messages:t.messages,trigger:t.trigger,messageId:t.messageId},p=null!=(s=null==i?void 0:i.credentials)?s:this.credentials,d=null!=(o=this.fetch)?o:globalThis.fetch,h=await d(l,{method:"POST",headers:{"Content-Type":"application/json",...u},body:JSON.stringify(c),credentials:p,signal:e});if(!h.ok)throw Error(null!=(n=await h.text())?n:"Failed to fetch the chat response.");if(!h.body)throw Error("The response body is empty.");return this.processResponseStream(h.body)}async reconnectToStream(e){var t,r,a,s,o;let n=await (null==(t=this.prepareReconnectToStreamRequest)?void 0:t.call(this,{api:this.api,id:e.chatId,body:{...this.body,...e.body},headers:{...this.headers,...e.headers},credentials:this.credentials,requestMetadata:e.metadata})),i=null!=(r=null==n?void 0:n.api)?r:`${this.api}/${e.chatId}/stream`,l=(null==n?void 0:n.headers)!==void 0?n.headers:{...this.headers,...e.headers},u=null!=(a=null==n?void 0:n.credentials)?a:this.credentials,c=null!=(s=this.fetch)?s:globalThis.fetch,p=await c(i,{method:"GET",headers:l,credentials:u});if(204===p.status)return null;if(!p.ok)throw Error(null!=(o=await p.text())?o:"Failed to fetch the chat response.");if(!p.body)throw Error("The response body is empty.");return this.processResponseStream(p.body)}},eS=class extends ex{constructor(e={}){super(e)}processResponseStream(e){return(0,b._Z)({stream:e,schema:eI}).pipeThrough(new TransformStream({async transform(e,t){if(!e.success)throw e.error;t.enqueue(e.value)}}))}};function eA(e,t){if(void 0===e&&void 0===t)return;if(void 0===e)return t;if(void 0===t)return e;let r={...e};for(let a in t)if(Object.prototype.hasOwnProperty.call(t,a)){let s=t[a];if(void 0===s)continue;let o=a in e?e[a]:void 0,n=null!==s&&"object"==typeof s&&!Array.isArray(s)&&!(s instanceof Date)&&!(s instanceof RegExp),i=null!=o&&"object"==typeof o&&!Array.isArray(o)&&!(o instanceof Date)&&!(o instanceof RegExp);n&&i?r[a]=eA(o,s):r[a]=s}return r}async function eR(e){if(void 0===e)return{value:void 0,state:"undefined-input"};let t=await (0,b.N8)({text:e});return t.success?{value:t.value,state:"successful-parse"}:(t=await (0,b.N8)({text:function(e){let t=["ROOT"],r=-1,a=null;function s(e,s,o){switch(e){case'"':r=s,t.pop(),t.push(o),t.push("INSIDE_STRING");break;case"f":case"t":case"n":r=s,a=s,t.pop(),t.push(o),t.push("INSIDE_LITERAL");break;case"-":t.pop(),t.push(o),t.push("INSIDE_NUMBER");break;case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":r=s,t.pop(),t.push(o),t.push("INSIDE_NUMBER");break;case"{":r=s,t.pop(),t.push(o),t.push("INSIDE_OBJECT_START");break;case"[":r=s,t.pop(),t.push(o),t.push("INSIDE_ARRAY_START")}}function o(e,a){switch(e){case",":t.pop(),t.push("INSIDE_OBJECT_AFTER_COMMA");break;case"}":r=a,t.pop()}}function n(e,a){switch(e){case",":t.pop(),t.push("INSIDE_ARRAY_AFTER_COMMA");break;case"]":r=a,t.pop()}}for(let i=0;i<e.length;i++){let l=e[i];switch(t[t.length-1]){case"ROOT":s(l,i,"FINISH");break;case"INSIDE_OBJECT_START":switch(l){case'"':t.pop(),t.push("INSIDE_OBJECT_KEY");break;case"}":r=i,t.pop()}break;case"INSIDE_OBJECT_AFTER_COMMA":'"'===l&&(t.pop(),t.push("INSIDE_OBJECT_KEY"));break;case"INSIDE_OBJECT_KEY":'"'===l&&(t.pop(),t.push("INSIDE_OBJECT_AFTER_KEY"));break;case"INSIDE_OBJECT_AFTER_KEY":":"===l&&(t.pop(),t.push("INSIDE_OBJECT_BEFORE_VALUE"));break;case"INSIDE_OBJECT_BEFORE_VALUE":s(l,i,"INSIDE_OBJECT_AFTER_VALUE");break;case"INSIDE_OBJECT_AFTER_VALUE":o(l,i);break;case"INSIDE_STRING":switch(l){case'"':t.pop(),r=i;break;case"\\":t.push("INSIDE_STRING_ESCAPE");break;default:r=i}break;case"INSIDE_ARRAY_START":"]"===l?(r=i,t.pop()):(r=i,s(l,i,"INSIDE_ARRAY_AFTER_VALUE"));break;case"INSIDE_ARRAY_AFTER_VALUE":switch(l){case",":t.pop(),t.push("INSIDE_ARRAY_AFTER_COMMA");break;case"]":r=i,t.pop();break;default:r=i}break;case"INSIDE_ARRAY_AFTER_COMMA":s(l,i,"INSIDE_ARRAY_AFTER_VALUE");break;case"INSIDE_STRING_ESCAPE":t.pop(),r=i;break;case"INSIDE_NUMBER":switch(l){case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":r=i;break;case"e":case"E":case"-":case".":break;case",":t.pop(),"INSIDE_ARRAY_AFTER_VALUE"===t[t.length-1]&&n(l,i),"INSIDE_OBJECT_AFTER_VALUE"===t[t.length-1]&&o(l,i);break;case"}":t.pop(),"INSIDE_OBJECT_AFTER_VALUE"===t[t.length-1]&&o(l,i);break;case"]":t.pop(),"INSIDE_ARRAY_AFTER_VALUE"===t[t.length-1]&&n(l,i);break;default:t.pop()}break;case"INSIDE_LITERAL":{let s=e.substring(a,i+1);"false".startsWith(s)||"true".startsWith(s)||"null".startsWith(s)?r=i:(t.pop(),"INSIDE_OBJECT_AFTER_VALUE"===t[t.length-1]?o(l,i):"INSIDE_ARRAY_AFTER_VALUE"===t[t.length-1]&&n(l,i))}}}let i=e.slice(0,r+1);for(let r=t.length-1;r>=0;r--)switch(t[r]){case"INSIDE_STRING":i+='"';break;case"INSIDE_OBJECT_KEY":case"INSIDE_OBJECT_AFTER_KEY":case"INSIDE_OBJECT_AFTER_COMMA":case"INSIDE_OBJECT_START":case"INSIDE_OBJECT_BEFORE_VALUE":case"INSIDE_OBJECT_AFTER_VALUE":i+="}";break;case"INSIDE_ARRAY_START":case"INSIDE_ARRAY_AFTER_COMMA":case"INSIDE_ARRAY_AFTER_VALUE":i+="]";break;case"INSIDE_LITERAL":{let t=e.substring(a,e.length);"true".startsWith(t)?i+="true".slice(t.length):"false".startsWith(t)?i+="false".slice(t.length):"null".startsWith(t)&&(i+="null".slice(t.length))}}return i}(e)})).success?{value:t.value,state:"repaired-parse"}:{value:void 0,state:"failed-parse"}}function ej(e){return e.type.startsWith("tool-")}function ek(e){return e.type.split("-")[1]}function e_({lastMessage:e,messageId:t}){return{message:(null==e?void 0:e.role)==="assistant"?e:{id:t,metadata:void 0,role:"assistant",parts:[]},activeTextParts:{},activeReasoningParts:{},partialToolCalls:{}}}function eC({stream:e,onToolCall:t,messageMetadataSchema:r,dataPartSchemas:a,runUpdateMessageJob:s,onError:o,onData:n}){return e.pipeThrough(new TransformStream({async transform(e,a){await s(async({state:s,write:i})=>{var l,u;function c(e){var t;let r=s.message.parts.find(t=>ej(t)&&t.toolCallId===e.toolCallId);null!=r?(r.state=e.state,r.input=e.input,r.output=e.output,r.errorText=e.errorText,r.providerExecuted=null!=(t=e.providerExecuted)?t:r.providerExecuted):s.message.parts.push({type:`tool-${e.toolName}`,toolCallId:e.toolCallId,state:e.state,input:e.input,output:e.output,errorText:e.errorText,providerExecuted:e.providerExecuted})}async function p(e){if(null!=e){let t=null!=s.message.metadata?eA(s.message.metadata,e):e;null!=r&&await (0,b.k5)({value:t,schema:r}),s.message.metadata=t}}switch(e.type){case"text-start":{let t={type:"text",text:"",state:"streaming"};s.activeTextParts[e.id]=t,s.message.parts.push(t),i();break}case"text-delta":s.activeTextParts[e.id].text+=e.delta,i();break;case"text-end":s.activeTextParts[e.id].state="done",delete s.activeTextParts[e.id],i();break;case"reasoning-start":{let t={type:"reasoning",text:"",providerMetadata:e.providerMetadata,state:"streaming"};s.activeReasoningParts[e.id]=t,s.message.parts.push(t),i();break}case"reasoning-delta":{let t=s.activeReasoningParts[e.id];t.text+=e.delta,t.providerMetadata=null!=(l=e.providerMetadata)?l:t.providerMetadata,i();break}case"reasoning-end":{let t=s.activeReasoningParts[e.id];t.providerMetadata=null!=(u=e.providerMetadata)?u:t.providerMetadata,t.state="done",delete s.activeReasoningParts[e.id],i();break}case"file":s.message.parts.push({type:"file",mediaType:e.mediaType,url:e.url}),i();break;case"source-url":s.message.parts.push({type:"source-url",sourceId:e.sourceId,url:e.url,title:e.title,providerMetadata:e.providerMetadata}),i();break;case"source-document":s.message.parts.push({type:"source-document",sourceId:e.sourceId,mediaType:e.mediaType,title:e.title,filename:e.filename,providerMetadata:e.providerMetadata}),i();break;case"tool-input-start":{let t=s.message.parts.filter(ej);s.partialToolCalls[e.toolCallId]={text:"",toolName:e.toolName,index:t.length},c({toolCallId:e.toolCallId,toolName:e.toolName,state:"input-streaming",input:void 0,providerExecuted:e.providerExecuted}),i();break}case"tool-input-delta":{let t=s.partialToolCalls[e.toolCallId];t.text+=e.inputTextDelta;let{value:r}=await eR(t.text);c({toolCallId:e.toolCallId,toolName:t.toolName,state:"input-streaming",input:r}),i();break}case"tool-input-available":if(c({toolCallId:e.toolCallId,toolName:e.toolName,state:"input-available",input:e.input,providerExecuted:e.providerExecuted}),i(),t&&!e.providerExecuted){let r=await t({toolCall:e});null!=r&&(c({toolCallId:e.toolCallId,toolName:e.toolName,state:"output-available",input:e.input,output:r}),i())}break;case"tool-output-available":{let t=s.message.parts.filter(ej);if(null==t)throw Error("tool_result must be preceded by a tool_call");let r=t.findIndex(t=>t.toolCallId===e.toolCallId);if(-1===r)throw Error("tool_result must be preceded by a tool_call with the same toolCallId");let a=ek(t[r]);c({toolCallId:e.toolCallId,toolName:a,state:"output-available",input:t[r].input,output:e.output,providerExecuted:e.providerExecuted}),i();break}case"tool-output-error":{let t=s.message.parts.filter(ej);if(null==t)throw Error("tool_result must be preceded by a tool_call");let r=t.findIndex(t=>t.toolCallId===e.toolCallId);if(-1===r)throw Error("tool_result must be preceded by a tool_call with the same toolCallId");let a=ek(t[r]);c({toolCallId:e.toolCallId,toolName:a,state:"output-error",input:t[r].input,errorText:e.errorText,providerExecuted:e.providerExecuted}),i();break}case"start-step":s.message.parts.push({type:"step-start"});break;case"finish-step":s.activeTextParts={},s.activeReasoningParts={};break;case"start":null!=e.messageId&&(s.message.id=e.messageId),await p(e.messageMetadata),(null!=e.messageId||null!=e.messageMetadata)&&i();break;case"finish":case"message-metadata":await p(e.messageMetadata),null!=e.messageMetadata&&i();break;case"error":null==o||o(Error(e.errorText));break;default:if(e.type.startsWith("data-")){if(e.transient){null==n||n(e);break}let t=null!=e.id?s.message.parts.find(t=>e.type===t.type&&e.id===t.id):void 0;null!=t?t.data=eN(t.data)&&eN(e.data)?eA(t.data,e.data):e.data:s.message.parts.push(e),null==n||n(e),i()}}a.enqueue(e)})}}))}function eN(e){return"object"==typeof e&&null!==e}function eO(e){if(!e||"assistant"!==e.role)return!1;let t=e.parts.reduce((e,t,r)=>"step-start"===t.type?r:e,-1),r=e.parts.slice(t+1).filter(ej);return r.length>0&&r.every(e=>"output-available"===e.state)}var eM=class{constructor({generateId:e=b.$C,id:t=e(),transport:r=new eS,maxSteps:a=1,messageMetadataSchema:s,dataPartSchemas:o,state:n,onError:i,onToolCall:l,onFinish:u,onData:c}){this.activeResponse=void 0,this.jobExecutor=new ew,this.sendMessage=async(e,t={})=>{var r,a,s;let o;if(o="text"in e||"files"in e?{parts:[...Array.isArray(e.files)?e.files:await eT(e.files),..."text"in e&&null!=e.text?[{type:"text",text:e.text}]:[]]}:e,null!=e.messageId){let t=this.state.messages.findIndex(t=>t.id===e.messageId);if(-1===t)throw Error(`message with id ${e.messageId} not found`);if("user"!==this.state.messages[t].role)throw Error(`message with id ${e.messageId} is not a user message`);this.state.messages=this.state.messages.slice(0,t+1),this.state.replaceMessage(t,{...o,id:e.messageId,role:null!=(r=o.role)?r:"user",metadata:e.metadata})}else this.state.pushMessage({...o,id:null!=(a=o.id)?a:this.generateId(),role:null!=(s=o.role)?s:"user",metadata:e.metadata});await this.makeRequest({trigger:"submit-user-message",messageId:e.messageId,...t})},this.regenerate=async({messageId:e,...t}={})=>{let r=null==e?this.state.messages.length-1:this.state.messages.findIndex(t=>t.id===e);if(-1===r)throw Error(`message ${e} not found`);this.state.messages=this.state.messages.slice(0,"assistant"===this.messages[r].role?r:r+1),await this.makeRequest({trigger:"regenerate-assistant-message",messageId:e,...t})},this.resumeStream=async(e={})=>{await this.makeRequest({trigger:"resume-stream",...e})},this.addToolResult=async({toolCallId:e,output:t})=>{this.jobExecutor.run(async()=>{(function({messages:e,toolCallId:t,output:r}){let a=e[e.length-1].parts.find(e=>ej(e)&&e.toolCallId===t);null!=a&&(a.state="output-available",a.output=r)})({messages:this.state.messages,toolCallId:e,output:t}),this.messages=this.state.messages,"submitted"!==this.status&&"streaming"!==this.status&&eO(this.lastMessage)&&this.makeRequest({trigger:"submit-tool-result"})})},this.stop=async()=>{var e;("streaming"===this.status||"submitted"===this.status)&&(null==(e=this.activeResponse)?void 0:e.abortController)&&this.activeResponse.abortController.abort()},this.id=t,this.maxSteps=a,this.transport=r,this.generateId=e,this.messageMetadataSchema=s,this.dataPartSchemas=o,this.state=n,this.onError=i,this.onToolCall=l,this.onFinish=u,this.onData=c}get status(){return this.state.status}setStatus({status:e,error:t}){this.status!==e&&(this.state.status=e,this.state.error=t)}get error(){return this.state.error}get messages(){return this.state.messages}get lastMessage(){return this.state.messages[this.state.messages.length-1]}set messages(e){this.state.messages=e}async makeRequest({trigger:e,metadata:t,headers:r,body:a,messageId:s}){var o,n;this.setStatus({status:"submitted",error:void 0});let i=this.state.messages.length,l=this.lastMessage,u=null!=(o=null==l?void 0:l.parts.filter(e=>"step-start"===e.type).length)?o:0;try{let o,i={state:e_({lastMessage:this.state.snapshot(l),messageId:this.generateId()}),abortController:new AbortController};if(this.activeResponse=i,"resume-stream"===e){let e=await this.transport.reconnectToStream({chatId:this.id,metadata:t,headers:r,body:a});if(null==e)return;o=e}else o=await this.transport.sendMessages({chatId:this.id,messages:this.state.messages,abortSignal:i.abortController.signal,metadata:t,headers:r,body:a,trigger:e,messageId:s});await eE({stream:eC({stream:o,onToolCall:this.onToolCall,onData:this.onData,messageMetadataSchema:this.messageMetadataSchema,dataPartSchemas:this.dataPartSchemas,runUpdateMessageJob:e=>this.jobExecutor.run(()=>e({state:i.state,write:()=>{var e;this.setStatus({status:"streaming"}),i.state.message.id===(null==(e=this.lastMessage)?void 0:e.id)?this.state.replaceMessage(this.state.messages.length-1,i.state.message):this.state.pushMessage(i.state.message)}})),onError:e=>{throw e}}),onError:e=>{throw e}}),null==(n=this.onFinish)||n.call(this,{message:i.state.message}),this.setStatus({status:"ready"})}catch(e){if("AbortError"===e.name)return this.setStatus({status:"ready"}),null;this.onError&&e instanceof Error&&this.onError(e),this.setStatus({status:"error",error:e})}finally{this.activeResponse=void 0}(function({originalMaxToolInvocationStep:e,originalMessageCount:t,maxSteps:r,messages:a}){let s=a[a.length-1],o=s.parts.filter(e=>"step-start"===e.type).length;return r>1&&null!=s&&(a.length>t||o!==e)&&eO(s)&&o<r})({originalMaxToolInvocationStep:u,originalMessageCount:i,maxSteps:this.maxSteps,messages:this.state.messages})&&await this.makeRequest({metadata:t,headers:r,body:a,trigger:"submit-tool-result"})}};function eD({output:e,tool:t,errorMode:r}){return"text"===r?{type:"error-text",value:getErrorMessage3(e)}:"json"===r?{type:"error-json",value:e}:(null==t?void 0:t.toModelOutput)?t.toModelOutput(e):"string"==typeof e?{type:"text",value:e}:{type:"json",value:e}}var eY=class extends TransformStream{constructor(){super({transform(e,t){t.enqueue(`data: ${JSON.stringify(e)}

`)},flush(e){e.enqueue("data: [DONE]\n\n")}})}},eP={"content-type":"text/event-stream","cache-control":"no-cache",connection:"keep-alive","x-vercel-ai-ui-message-stream":"v1","x-accel-buffering":"no"};function e$(e,t){if(e===t)return!0;if(null==e||null==t)return!1;if("object"!=typeof e&&"object"!=typeof t)return e===t;if(e.constructor!==t.constructor)return!1;if(e instanceof Date&&t instanceof Date)return e.getTime()===t.getTime();if(Array.isArray(e)){if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++)if(!e$(e[r],t[r]))return!1;return!0}let r=Object.keys(e),a=Object.keys(t);if(r.length!==a.length)return!1;for(let s of r)if(!a.includes(s)||!e$(e[s],t[s]))return!1;return!0}var eU=({maxRetries:e=2,initialDelayInMs:t=2e3,backoffFactor:r=2}={})=>async a=>eF(a,{maxRetries:e,delayInMs:t,backoffFactor:r});async function eF(e,{maxRetries:t,delayInMs:r,backoffFactor:a},s=[]){try{return await e()}catch(l){if(isAbortError(l)||0===t)throw l;let o=getErrorMessage5(l),n=[...s,l],i=n.length;if(i>t)throw new ef({message:`Failed after ${i} attempts. Last error: ${o}`,reason:"maxRetriesExceeded",errors:n});if(l instanceof Error&&APICallError2.isInstance(l)&&!0===l.isRetryable&&i<=t)return await delay(r),eF(e,{maxRetries:t,delayInMs:a*r,backoffFactor:a},n);if(1===i)throw l;throw new ef({message:`Failed after ${i} attempts with non-retryable error: '${o}'`,reason:"errorNotRetryable",errors:n})}}function eL({maxRetries:e}){if(null!=e){if(!Number.isInteger(e))throw new x({parameter:"maxRetries",value:e,message:"maxRetries must be an integer"});if(e<0)throw new x({parameter:"maxRetries",value:e,message:"maxRetries must be >= 0"})}let t=null!=e?e:2;return{maxRetries:t,retry:eU({maxRetries:t})}}function eq({operationId:e,telemetry:t}){return{"operation.name":`${e}${(null==t?void 0:t.functionId)!=null?` ${t.functionId}`:""}`,"resource.name":null==t?void 0:t.functionId,"ai.operationId":e,"ai.telemetry.functionId":null==t?void 0:t.functionId}}function eB({model:e,settings:t,telemetry:r,headers:a}){var s;return{"ai.model.provider":e.provider,"ai.model.id":e.modelId,...Object.entries(t).reduce((e,[t,r])=>(e[`ai.settings.${t}`]=r,e),{}),...Object.entries(null!=(s=null==r?void 0:r.metadata)?s:{}).reduce((e,[t,r])=>(e[`ai.telemetry.metadata.${t}`]=r,e),{}),...Object.entries(null!=a?a:{}).reduce((e,[t,r])=>(void 0!==r&&(e[`ai.request.headers.${t}`]=r),e),{})}}var eJ={startSpan:()=>eK,startActiveSpan:(e,t,r,a)=>"function"==typeof t?t(eK):"function"==typeof r?r(eK):"function"==typeof a?a(eK):void 0},eK={spanContext:()=>eV,setAttribute(){return this},setAttributes(){return this},addEvent(){return this},addLink(){return this},addLinks(){return this},setStatus(){return this},updateName(){return this},end(){return this},isRecording:()=>!1,recordException(){return this}},eV={traceId:"",spanId:"",traceFlags:0};function ez({isEnabled:e=!1,tracer:t}={}){return e?t||trace.getTracer("ai"):eJ}function eG({name:e,tracer:t,attributes:r,fn:a,endWhenDone:s=!0}){return t.startActiveSpan(e,{attributes:r},async e=>{try{let t=await a(e);return s&&e.end(),t}catch(t){try{t instanceof Error?(e.recordException({name:t.name,message:t.message,stack:t.stack}),e.setStatus({code:SpanStatusCode.ERROR,message:t.message})):e.setStatus({code:SpanStatusCode.ERROR})}finally{e.end()}throw t}})}function eW({telemetry:e,attributes:t}){return(null==e?void 0:e.isEnabled)!==!0?{}:Object.entries(t).reduce((t,[r,a])=>{if(void 0===a)return t;if("object"==typeof a&&"input"in a&&"function"==typeof a.input){if((null==e?void 0:e.recordInputs)===!1)return t;let s=a.input();return void 0===s?t:{...t,[r]:s}}if("object"==typeof a&&"output"in a&&"function"==typeof a.output){if((null==e?void 0:e.recordOutputs)===!1)return t;let s=a.output();return void 0===s?t:{...t,[r]:s}}return{...t,[r]:a}},{})}var eZ=[{mediaType:"image/gif",bytesPrefix:[71,73,70],base64Prefix:"R0lG"},{mediaType:"image/png",bytesPrefix:[137,80,78,71],base64Prefix:"iVBORw"},{mediaType:"image/jpeg",bytesPrefix:[255,216],base64Prefix:"/9j/"},{mediaType:"image/webp",bytesPrefix:[82,73,70,70],base64Prefix:"UklGRg"},{mediaType:"image/bmp",bytesPrefix:[66,77],base64Prefix:"Qk"},{mediaType:"image/tiff",bytesPrefix:[73,73,42,0],base64Prefix:"SUkqAA"},{mediaType:"image/tiff",bytesPrefix:[77,77,0,42],base64Prefix:"TU0AKg"},{mediaType:"image/avif",bytesPrefix:[0,0,0,32,102,116,121,112,97,118,105,102],base64Prefix:"AAAAIGZ0eXBhdmlm"},{mediaType:"image/heic",bytesPrefix:[0,0,0,32,102,116,121,112,104,101,105,99],base64Prefix:"AAAAIGZ0eXBoZWlj"}],eH=e=>{let t="string"==typeof e?convertBase64ToUint8Array(e):e,r=(127&t[6])<<21|(127&t[7])<<14|(127&t[8])<<7|127&t[9];return t.slice(r+10)},eQ=class{constructor({data:e,mediaType:t}){let r=e instanceof Uint8Array;this.base64Data=r?void 0:e,this.uint8ArrayData=r?e:void 0,this.mediaType=t}get base64(){return null==this.base64Data&&(this.base64Data=(0,b.n_)(this.uint8ArrayData)),this.base64Data}get uint8Array(){return null==this.uint8ArrayData&&(this.uint8ArrayData=(0,b.Z9)(this.base64Data)),this.uint8ArrayData}},eX=class extends eQ{constructor(e){super(e),this.type="file"}};async function e0({url:e}){var t;let r=e.toString();try{let e=await fetch(r);if(!e.ok)throw new ep({url:r,statusCode:e.status,statusText:e.statusText});return{data:new Uint8Array(await e.arrayBuffer()),mediaType:null!=(t=e.headers.get("content-type"))?t:void 0}}catch(e){if(ep.isInstance(e))throw e;throw new ep({url:r,cause:e})}}var e1=v.KC([v.Yj(),v.Nl(Uint8Array),v.Nl(ArrayBuffer),v.Ie(e=>{var t,r;return null!=(r=null==(t=globalThis.Buffer)?void 0:t.isBuffer(e))&&r},{message:"Must be a Buffer"})]);function e2(e){if(e instanceof Uint8Array)return{data:e,mediaType:void 0};if(e instanceof ArrayBuffer)return{data:new Uint8Array(e),mediaType:void 0};if("string"==typeof e)try{e=new URL(e)}catch(e){}if(e instanceof URL&&"data:"===e.protocol){let{mediaType:t,base64Content:r}=function(e){try{let[t,r]=e.split(",");return{mediaType:t.split(";")[0].split(":")[1],base64Content:r}}catch(e){return{mediaType:void 0,base64Content:void 0}}}(e.toString());if(null==t||null==r)throw new AISDKError16({name:"InvalidDataContentError",message:`Invalid data URL format in content ${e.toString()}`});return{data:r,mediaType:t}}return{data:e,mediaType:void 0}}async function e4({prompt:e,supportedUrls:t,downloadImplementation:r=e0}){let a=await e6(e.messages,r,t);return[...null!=e.system?[{role:"system",content:e.system}]:[],...e.messages.map(e=>(function({message:e,downloadedAssets:t}){let r=e.role;switch(r){case"system":return{role:"system",content:e.content,providerOptions:e.providerOptions};case"user":if("string"==typeof e.content)return{role:"user",content:[{type:"text",text:e.content}],providerOptions:e.providerOptions};return{role:"user",content:e.content.map(e=>(function(e,t){var r;let a;if("text"===e.type)return{type:"text",text:e.text,providerOptions:e.providerOptions};let s=e.type;switch(s){case"image":a=e.image;break;case"file":a=e.data;break;default:throw Error(`Unsupported part type: ${s}`)}let{data:o,mediaType:n}=e2(a),i=null!=n?n:e.mediaType,l=o;if(l instanceof URL){let e=t[l.toString()];e&&(l=e.data,null!=i||(i=e.mediaType))}switch(s){case"image":return(l instanceof Uint8Array||"string"==typeof l)&&(i=null!=(r=function({data:e,signatures:t}){let r="string"==typeof e&&e.startsWith("SUQz")||"string"!=typeof e&&e.length>10&&73===e[0]&&68===e[1]&&51===e[2]?eH(e):e;for(let e of t)if("string"==typeof r?r.startsWith(e.base64Prefix):r.length>=e.bytesPrefix.length&&e.bytesPrefix.every((e,t)=>r[t]===e))return e.mediaType}({data:l,signatures:eZ}))?r:i),{type:"file",mediaType:null!=i?i:"image/*",filename:void 0,data:l,providerOptions:e.providerOptions};case"file":if(null==i)throw Error("Media type is missing for file part");return{type:"file",mediaType:i,filename:e.filename,data:l,providerOptions:e.providerOptions}}})(e,t)).filter(e=>"text"!==e.type||""!==e.text),providerOptions:e.providerOptions};case"assistant":if("string"==typeof e.content)return{role:"assistant",content:[{type:"text",text:e.content}],providerOptions:e.providerOptions};return{role:"assistant",content:e.content.filter(e=>"text"!==e.type||""!==e.text).map(e=>{let t=e.providerOptions;switch(e.type){case"file":{let{data:r,mediaType:a}=e2(e.data);return{type:"file",data:r,filename:e.filename,mediaType:null!=a?a:e.mediaType,providerOptions:t}}case"reasoning":return{type:"reasoning",text:e.text,providerOptions:t};case"text":return{type:"text",text:e.text,providerOptions:t};case"tool-call":return{type:"tool-call",toolCallId:e.toolCallId,toolName:e.toolName,input:e.input,providerExecuted:e.providerExecuted,providerOptions:t};case"tool-result":return{type:"tool-result",toolCallId:e.toolCallId,toolName:e.toolName,output:e.output,providerOptions:t}}}),providerOptions:e.providerOptions};case"tool":return{role:"tool",content:e.content.map(e=>({type:"tool-result",toolCallId:e.toolCallId,toolName:e.toolName,output:e.output,providerOptions:e.providerOptions})),providerOptions:e.providerOptions};default:throw new es({role:r})}})({message:e,downloadedAssets:a}))]}async function e6(e,t,r){let a=e.filter(e=>"user"===e.role).map(e=>e.content).filter(e=>Array.isArray(e)).flat().filter(e=>"image"===e.type||"file"===e.type).map(e=>{var t;let r=null!=(t=e.mediaType)?t:"image"===e.type?"image/*":void 0,a="image"===e.type?e.image:e.data;if("string"==typeof a)try{a=new URL(a)}catch(e){}return{mediaType:r,data:a}}).filter(e=>e.data instanceof URL&&null!=e.mediaType&&!isUrlSupported({url:e.data.toString(),mediaType:e.mediaType,supportedUrls:r})).map(e=>e.data);return Object.fromEntries((await Promise.all(a.map(async e=>({url:e,data:await t({url:e})})))).map(({url:e,data:t})=>[e.toString(),t]))}function e8({maxOutputTokens:e,temperature:t,topP:r,topK:a,presencePenalty:s,frequencyPenalty:o,seed:n,stopSequences:i}){if(null!=e){if(!Number.isInteger(e))throw new x({parameter:"maxOutputTokens",value:e,message:"maxOutputTokens must be an integer"});if(e<1)throw new x({parameter:"maxOutputTokens",value:e,message:"maxOutputTokens must be >= 1"})}if(null!=t&&"number"!=typeof t)throw new x({parameter:"temperature",value:t,message:"temperature must be a number"});if(null!=r&&"number"!=typeof r)throw new x({parameter:"topP",value:r,message:"topP must be a number"});if(null!=a&&"number"!=typeof a)throw new x({parameter:"topK",value:a,message:"topK must be a number"});if(null!=s&&"number"!=typeof s)throw new x({parameter:"presencePenalty",value:s,message:"presencePenalty must be a number"});if(null!=o&&"number"!=typeof o)throw new x({parameter:"frequencyPenalty",value:o,message:"frequencyPenalty must be a number"});if(null!=n&&!Number.isInteger(n))throw new x({parameter:"seed",value:n,message:"seed must be an integer"});return{maxOutputTokens:e,temperature:t,topP:r,topK:a,presencePenalty:s,frequencyPenalty:o,stopSequences:i,seed:n}}function e7(e){if("string"!=typeof e)return e;let t=globalThis.AI_SDK_DEFAULT_PROVIDER;return(null!=t?t:gateway).languageModel(e)}var e5=v.RZ(()=>v.KC([v.ch(),v.Yj(),v.ai(),v.zM(),v.g1(v.Yj(),e5),v.YO(e5)])),e3=v.g1(v.Yj(),v.g1(v.Yj(),e5)),e9=v.Ik({type:v.eu("text"),text:v.Yj(),providerOptions:e3.optional()}),te=v.Ik({type:v.eu("image"),image:v.KC([e1,v.Nl(URL)]),mediaType:v.Yj().optional(),providerOptions:e3.optional()}),tt=v.Ik({type:v.eu("file"),data:v.KC([e1,v.Nl(URL)]),filename:v.Yj().optional(),mediaType:v.Yj(),providerOptions:e3.optional()}),tr=v.Ik({type:v.eu("reasoning"),text:v.Yj(),providerOptions:e3.optional()}),ta=v.Ik({type:v.eu("tool-call"),toolCallId:v.Yj(),toolName:v.Yj(),input:v.L5(),providerOptions:e3.optional(),providerExecuted:v.zM().optional()}),ts=v.gM("type",[v.Ik({type:v.eu("text"),value:v.Yj()}),v.Ik({type:v.eu("json"),value:e5}),v.Ik({type:v.eu("error-text"),value:v.Yj()}),v.Ik({type:v.eu("error-json"),value:e5}),v.Ik({type:v.eu("content"),value:v.YO(v.KC([v.Ik({type:v.eu("text"),text:v.Yj()}),v.Ik({type:v.eu("media"),data:v.Yj(),mediaType:v.Yj()})]))})]),to=v.Ik({type:v.eu("tool-result"),toolCallId:v.Yj(),toolName:v.Yj(),output:ts,providerOptions:e3.optional()}),tn=v.Ik({role:v.eu("system"),content:v.Yj(),providerOptions:e3.optional()}),ti=v.Ik({role:v.eu("user"),content:v.KC([v.Yj(),v.YO(v.KC([e9,te,tt]))]),providerOptions:e3.optional()}),tl=v.Ik({role:v.eu("assistant"),content:v.KC([v.Yj(),v.YO(v.KC([e9,tt,tr,ta,to]))]),providerOptions:e3.optional()}),tu=v.Ik({role:v.eu("tool"),content:v.YO(to),providerOptions:e3.optional()}),tc=v.KC([tn,ti,tl,tu]);async function tp(e){let t;if(null==e.prompt&&null==e.messages)throw new InvalidPromptError2({prompt:e,message:"prompt or messages must be defined"});if(null!=e.prompt&&null!=e.messages)throw new InvalidPromptError2({prompt:e,message:"prompt and messages cannot be defined at the same time"});if(null!=e.system&&"string"!=typeof e.system)throw new InvalidPromptError2({prompt:e,message:"system must be a string"});if(null!=e.prompt&&"string"==typeof e.prompt)t=[{role:"user",content:e.prompt}];else if(null!=e.prompt&&Array.isArray(e.prompt))t=e.prompt;else if(null!=e.messages)t=e.messages;else throw new InvalidPromptError2({prompt:e,message:"prompt or messages must be defined"});if(0===t.length)throw new InvalidPromptError2({prompt:e,message:"messages must not be empty"});let r=await safeValidateTypes({value:t,schema:z7.array(tc)});if(!r.success)throw new InvalidPromptError2({prompt:e,message:"The messages must be a ModelMessage[]. If you have passed a UIMessage[], you can use convertToModelMessages to convert them.",cause:r.error});return{messages:t,system:e.system}}function td(e){return GatewayAuthenticationError.isInstance(e)||GatewayModelNotFoundError.isInstance(e)?new AISDKError17({name:"GatewayError",message:"Vercel AI Gateway access failed. If you want to use AI SDK providers directly, use the providers, e.g. @ai-sdk/openai, or register a different global default provider.",cause:e}):e}function th(e){return JSON.stringify(e.map(e=>({...e,content:"string"==typeof e.content?e.content:e.content.map(e=>{var t;return"file"===e.type?{...e,data:e.data instanceof Uint8Array?"string"==typeof(t=e.data)?t:t instanceof ArrayBuffer?convertUint8ArrayToBase642(new Uint8Array(t)):convertUint8ArrayToBase642(t):e.data}:e})})))}function tm(e){let t=e.pipeThrough(new TransformStream);return t[Symbol.asyncIterator]=()=>{let e=t.getReader();return{async next(){let{done:t,value:r}=await e.read();return t?{done:!0,value:void 0}:{done:!1,value:r}}}},t}var tf={type:"no-schema",jsonSchema:void 0,validatePartialResult:async({value:e,textDelta:t})=>({success:!0,value:{partial:e,textDelta:t}}),validateFinalResult:async(e,t)=>void 0===e?{success:!1,error:new U({message:"No object generated: response did not match schema.",text:t.text,response:t.response,usage:t.usage,finishReason:t.finishReason})}:{success:!0,value:e},createElementStream(){throw new y.b8({functionality:"element streams in no-schema mode"})}},tg=e=>({type:"object",jsonSchema:e.jsonSchema,validatePartialResult:async({value:e,textDelta:t})=>({success:!0,value:{partial:e,textDelta:t}}),validateFinalResult:async t=>safeValidateTypes2({value:t,schema:e}),createElementStream(){throw new UnsupportedFunctionalityError2({functionality:"element streams in object mode"})}}),ty=e=>{let{$schema:t,...r}=e.jsonSchema;return{type:"enum",jsonSchema:{$schema:"http://json-schema.org/draft-07/schema#",type:"object",properties:{elements:{type:"array",items:r}},required:["elements"],additionalProperties:!1},async validatePartialResult({value:t,latestObject:r,isFirstDelta:a,isFinalDelta:s}){var o;if(!isJSONObject(t)||!isJSONArray(t.elements))return{success:!1,error:new TypeValidationError2({value:t,cause:"value must be an object that contains an array of elements"})};let n=t.elements,i=[];for(let t=0;t<n.length;t++){let r=n[t],a=await safeValidateTypes2({value:r,schema:e});if(t!==n.length-1||s){if(!a.success)return a;i.push(a.value)}}let l=null!=(o=null==r?void 0:r.length)?o:0,u="";return a&&(u+="["),l>0&&(u+=","),u+=i.slice(l).map(e=>JSON.stringify(e)).join(","),s&&(u+="]"),{success:!0,value:{partial:i,textDelta:u}}},async validateFinalResult(t){if(!isJSONObject(t)||!isJSONArray(t.elements))return{success:!1,error:new TypeValidationError2({value:t,cause:"value must be an object that contains an array of elements"})};let r=t.elements;for(let t of r){let r=await safeValidateTypes2({value:t,schema:e});if(!r.success)return r}return{success:!0,value:r}},createElementStream(e){let t=0;return tm(e.pipeThrough(new TransformStream({transform(e,r){switch(e.type){case"object":{let a=e.object;for(;t<a.length;t++)r.enqueue(a[t]);break}case"text-delta":case"finish":case"error":break;default:throw Error(`Unsupported chunk type: ${e}`)}}})))}}},tv=e=>({type:"enum",jsonSchema:{$schema:"http://json-schema.org/draft-07/schema#",type:"object",properties:{result:{type:"string",enum:e}},required:["result"],additionalProperties:!1},async validateFinalResult(t){if(!isJSONObject(t)||"string"!=typeof t.result)return{success:!1,error:new TypeValidationError2({value:t,cause:'value must be an object that contains a string in the "result" property.'})};let r=t.result;return e.includes(r)?{success:!0,value:r}:{success:!1,error:new TypeValidationError2({value:t,cause:"value must be a string in the enum"})}},async validatePartialResult({value:t,textDelta:r}){if(!isJSONObject(t)||"string"!=typeof t.result)return{success:!1,error:new TypeValidationError2({value:t,cause:'value must be an object that contains a string in the "result" property.'})};let a=t.result,s=e.filter(e=>e.startsWith(a));return 0===t.result.length||0===s.length?{success:!1,error:new TypeValidationError2({value:t,cause:"value must be a string in the enum"})}:{success:!0,value:{partial:s.length>1?a:s[0],textDelta:r}}},createElementStream(){throw new UnsupportedFunctionalityError2({functionality:"element streams in enum mode"})}});function tb(){let e,t;return{promise:new Promise((r,a)=>{e=r,t=a}),resolve:e,reject:t}}function tI(){let e=[],t=null,r=!1,a=tb(),s=async()=>{if(r&&0===e.length){null==t||t.close();return}if(0===e.length)return a=tb(),await a.promise,s();try{let{value:a,done:o}=await e[0].read();o?(e.shift(),e.length>0?await s():r&&(null==t||t.close())):null==t||t.enqueue(a)}catch(a){null==t||t.error(a),e.shift(),r&&0===e.length&&(null==t||t.close())}};return{stream:new ReadableStream({start(e){t=e},pull:s,async cancel(){for(let t of e)await t.cancel();e=[],r=!0}}),addStream:t=>{if(r)throw Error("Cannot add inner stream: outer stream is closed");e.push(t.getReader()),a.resolve()},close:()=>{r=!0,a.resolve(),0===e.length&&(null==t||t.close())},terminate:()=>{r=!0,a.resolve(),e.forEach(e=>e.cancel()),e=[],null==t||t.close()}}}(0,b.hK)({prefix:"aiobj",size:24});var tE=class{constructor(){this.status={type:"pending"},this._resolve=void 0,this._reject=void 0}get promise(){return this._promise||(this._promise=new Promise((e,t)=>{"resolved"===this.status.type?e(this.status.value):"rejected"===this.status.type&&t(this.status.error),this._resolve=e,this._reject=t})),this._promise}resolve(e){var t;this.status={type:"resolved",value:e},this._promise&&(null==(t=this._resolve)||t.call(this,e))}reject(e){var t;this.status={type:"rejected",error:e},this._promise&&(null==(t=this._reject)||t.call(this,e))}};(0,b.hK)({prefix:"aiobj",size:24}),y.bD;function tw(e,t){return{inputTokens:tT(e.inputTokens,t.inputTokens),outputTokens:tT(e.outputTokens,t.outputTokens),totalTokens:tT(e.totalTokens,t.totalTokens),reasoningTokens:tT(e.reasoningTokens,t.reasoningTokens),cachedInputTokens:tT(e.cachedInputTokens,t.cachedInputTokens)}}function tT(e,t){return null==e&&null==t?void 0:(null!=e?e:0)+(null!=t?t:0)}async function tx({toolCall:e,tools:t,repairToolCall:r,system:a,messages:s}){if(null==t)throw new z({toolName:e.toolName});try{return await tS({toolCall:e,tools:t})}catch(n){if(null==r||!(z.isInstance(n)||k.isInstance(n)))throw n;let o=null;try{o=await r({toolCall:e,tools:t,inputSchema:({toolName:e})=>{let{inputSchema:r}=t[e];return asSchema3(r).jsonSchema},system:a,messages:s,error:n})}catch(e){throw new H({cause:e,originalError:n})}if(null==o)throw n;return await tS({toolCall:o,tools:t})}}async function tS({toolCall:e,tools:t}){let r=e.toolName,a=t[r];if(null==a)throw new z({toolName:e.toolName,availableTools:Object.keys(t)});let s=asSchema3(a.inputSchema),o=""===e.input.trim()?await safeValidateTypes3({value:{},schema:s}):await safeParseJSON3({text:e.input,schema:s});if(!1===o.success)throw new k({toolName:r,toolInput:e.input,cause:o.error});return{type:"tool-call",toolCallId:e.toolCallId,toolName:r,input:o.value,providerExecuted:e.providerExecuted}}var tA=class{constructor({content:e,finishReason:t,usage:r,warnings:a,request:s,response:o,providerMetadata:n}){this.content=e,this.finishReason=t,this.usage=r,this.warnings=a,this.request=s,this.response=o,this.providerMetadata=n}get text(){return this.content.filter(e=>"text"===e.type).map(e=>e.text).join("")}get reasoning(){return this.content.filter(e=>"reasoning"===e.type)}get reasoningText(){return 0===this.reasoning.length?void 0:this.reasoning.map(e=>e.text).join("")}get files(){return this.content.filter(e=>"file"===e.type).map(e=>e.file)}get sources(){return this.content.filter(e=>"source"===e.type)}get toolCalls(){return this.content.filter(e=>"tool-call"===e.type)}get toolResults(){return this.content.filter(e=>"tool-result"===e.type)}};async function tR({stopConditions:e,steps:t}){return(await Promise.all(e.map(e=>e({steps:t})))).some(e=>e)}function tj({content:e,tools:t}){let r=[],a=e.filter(e=>"source"!==e.type).filter(e=>("tool-result"!==e.type||e.providerExecuted)&&("tool-error"!==e.type||e.providerExecuted)).filter(e=>"text"!==e.type||e.text.length>0).map(e=>{switch(e.type){case"text":return e;case"reasoning":return{type:"reasoning",text:e.text,providerOptions:e.providerMetadata};case"file":return{type:"file",data:e.file.base64,mediaType:e.file.mediaType};case"tool-call":return{type:"tool-call",toolCallId:e.toolCallId,toolName:e.toolName,input:e.input,providerExecuted:e.providerExecuted};case"tool-result":return{type:"tool-result",toolCallId:e.toolCallId,toolName:e.toolName,output:eD({tool:null==t?void 0:t[e.toolName],output:e.output,errorMode:"none"}),providerExecuted:!0};case"tool-error":return{type:"tool-result",toolCallId:e.toolCallId,toolName:e.toolName,output:eD({tool:null==t?void 0:t[e.toolName],output:e.error,errorMode:"json"})}}});a.length>0&&r.push({role:"assistant",content:a});let s=e.filter(e=>"tool-result"===e.type||"tool-error"===e.type).filter(e=>!e.providerExecuted).map(e=>({type:"tool-result",toolCallId:e.toolCallId,toolName:e.toolName,output:eD({tool:null==t?void 0:t[e.toolName],output:"tool-result"===e.type?e.output:e.error,errorMode:"tool-error"===e.type?"text":"none"})}));return s.length>0&&r.push({role:"tool",content:s}),r}(0,b.hK)({prefix:"aitxt",size:24});((e,t)=>{for(var r in t)I(e,r,{get:t[r],enumerable:!0})})({},{object:()=>t_,text:()=>tk});var tk=()=>({type:"text",responseFormat:{type:"text"},parsePartial:async({text:e})=>({partial:e}),parseOutput:async({text:e})=>e}),t_=({schema:e})=>{let t=(0,b.mD)(e);return{type:"object",responseFormat:{type:"json",schema:t.jsonSchema},async parsePartial({text:e}){let t=await eR(e);switch(t.state){case"failed-parse":case"undefined-input":return;case"repaired-parse":case"successful-parse":return{partial:t.value};default:{let e=t.state;throw Error(`Unsupported parse state: ${e}`)}}},async parseOutput({text:e},r){let a=await (0,b.N8)({text:e});if(!a.success)throw new U({message:"No object generated: could not parse the response.",cause:a.error,text:e,response:r.response,usage:r.usage,finishReason:r.finishReason});let s=await (0,b.ZZ)({value:a.value,schema:t});if(!s.success)throw new U({message:"No object generated: response did not match schema.",cause:s.error,text:e,response:r.response,usage:r.usage,finishReason:r.finishReason});return s.value}}},tC=((0,b.hK)({prefix:"aitxt",size:24}),"AI_NoSuchProviderError"),tN=`vercel.ai.error.${tC}`,tO=Symbol.for(tN),tM=class extends y.eM{constructor({modelId:e,modelType:t,providerId:r,availableProviders:a,message:s=`No such provider: ${r} (available providers: ${a.join()})`}){super({errorName:tC,modelId:e,modelType:t,message:s}),this[g]=!0,this.providerId=r,this.availableProviders=a}static isInstance(e){return y.bD.hasMarker(e,tN)}};g=tO;var tD="2025-26-03",tY=[tD,"2024-11-05"],tP=v.Ik({name:v.Yj(),version:v.Yj()}).passthrough(),t$=v.Ik({_meta:v.lq(v.Ik({}).passthrough())}).passthrough(),tU=v.Ik({method:v.Yj(),params:v.lq(t$)}),tF=v.Ik({experimental:v.lq(v.Ik({}).passthrough()),logging:v.lq(v.Ik({}).passthrough()),prompts:v.lq(v.Ik({listChanged:v.lq(v.zM())}).passthrough()),resources:v.lq(v.Ik({subscribe:v.lq(v.zM()),listChanged:v.lq(v.zM())}).passthrough()),tools:v.lq(v.Ik({listChanged:v.lq(v.zM())}).passthrough())}).passthrough(),tL=t$.extend({protocolVersion:v.Yj(),capabilities:tF,serverInfo:tP,instructions:v.lq(v.Yj())}),tq=t$.extend({nextCursor:v.lq(v.Yj())}),tB=v.Ik({name:v.Yj(),description:v.lq(v.Yj()),inputSchema:v.Ik({type:v.eu("object"),properties:v.lq(v.Ik({}).passthrough())}).passthrough()}).passthrough(),tJ=tq.extend({tools:v.YO(tB)}),tK=v.Ik({type:v.eu("text"),text:v.Yj()}).passthrough(),tV=v.Ik({type:v.eu("image"),data:v.Yj().base64(),mimeType:v.Yj()}).passthrough(),tz=v.Ik({uri:v.Yj(),mimeType:v.lq(v.Yj())}).passthrough(),tG=tz.extend({text:v.Yj()}),tW=tz.extend({blob:v.Yj().base64()}),tZ=v.Ik({type:v.eu("resource"),resource:v.KC([tG,tW])}).passthrough(),tH=t$.extend({content:v.YO(v.KC([tK,tV,tZ])),isError:v.zM().default(!1).optional()}).or(t$.extend({toolResult:v.L5()})),tQ=v.Ik({jsonrpc:v.eu("2.0"),id:v.KC([v.Yj(),v.ai().int()])}).merge(tU).strict(),tX=v.Ik({jsonrpc:v.eu("2.0"),id:v.KC([v.Yj(),v.ai().int()]),result:t$}).strict(),t0=v.Ik({jsonrpc:v.eu("2.0"),id:v.KC([v.Yj(),v.ai().int()]),error:v.Ik({code:v.ai().int(),message:v.Yj(),data:v.lq(v.L5())})}).strict(),t1=v.Ik({jsonrpc:v.eu("2.0")}).merge(v.Ik({method:v.Yj(),params:v.lq(t$)})).strict(),t2=v.KC([tQ,t1,tX,t0]),t4=class{constructor({url:e,headers:t}){this.connected=!1,this.url=new URL(e),this.headers=t}async start(){return new Promise((e,t)=>{if(this.connected)return e();this.abortController=new AbortController,(async()=>{var r,a,s;try{let s=new Headers(this.headers);s.set("Accept","text/event-stream");let o=await fetch(this.url.href,{headers:s,signal:null==(r=this.abortController)?void 0:r.signal});if(!o.ok||!o.body){let e=new N({message:`MCP SSE Transport Error: ${o.status} ${o.statusText}`});return null==(a=this.onerror)||a.call(this,e),t(e)}let n=o.body.pipeThrough(new TextDecoderStream).pipeThrough(new EventSourceParserStream).getReader(),i=async()=>{var r,a,s;try{for(;;){let{done:t,value:s}=await n.read();if(t){if(this.connected)throw this.connected=!1,new N({message:"MCP SSE Transport Error: Connection closed unexpectedly"});return}let{event:o,data:i}=s;if("endpoint"===o){if(this.endpoint=new URL(i,this.url),this.endpoint.origin!==this.url.origin)throw new N({message:`MCP SSE Transport Error: Endpoint origin does not match connection origin: ${this.endpoint.origin}`});this.connected=!0,e()}else if("message"===o)try{let e=t2.parse(JSON.parse(i));null==(r=this.onmessage)||r.call(this,e)}catch(t){let e=new N({message:"MCP SSE Transport Error: Failed to parse message",cause:t});null==(a=this.onerror)||a.call(this,e)}}}catch(e){if(e instanceof Error&&"AbortError"===e.name)return;null==(s=this.onerror)||s.call(this,e),t(e)}};this.sseConnection={close:()=>n.cancel()},i()}catch(e){if(e instanceof Error&&"AbortError"===e.name)return;null==(s=this.onerror)||s.call(this,e),t(e)}})()})}async close(){var e,t,r;this.connected=!1,null==(e=this.sseConnection)||e.close(),null==(t=this.abortController)||t.abort(),null==(r=this.onclose)||r.call(this)}async send(e){var t,r,a;if(!this.endpoint||!this.connected)throw new N({message:"MCP SSE Transport Error: Not connected"});try{let a=new Headers(this.headers);a.set("Content-Type","application/json");let s={method:"POST",headers:a,body:JSON.stringify(e),signal:null==(t=this.abortController)?void 0:t.signal},o=await fetch(this.endpoint,s);if(!o.ok){let e=await o.text().catch(()=>null),t=new N({message:`MCP SSE Transport Error: POSTing to endpoint (HTTP ${o.status}): ${e}`});null==(r=this.onerror)||r.call(this,t);return}}catch(e){null==(a=this.onerror)||a.call(this,e);return}}};y.bD}}]);