"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6517],{86517:(e,l,s)=>{s.r(l),s.d(l,{KeylessCreatorOrReader:()=>r});var n=s(62942),a=s(41987),t=s(25316);let i=(0,t.createServerReference)("7f4c08c553bf967cd0b7c8f5250f46c2e73879b9bd",t.callServer,void 0,t.findSourceMapURL,"createOrReadKeylessAction"),r=e=>{var l;let{children:s}=e,t=(null==(l=(0,n.useSelectedLayoutSegments)()[0])?void 0:l.startsWith("/_not-found"))||!1,[r,c]=a.useActionState(i,null);return((0,a.useEffect)(()=>{t||a.startTransition(()=>{c()})},[t]),a.isValidElement(s))?a.cloneElement(s,{key:null==r?void 0:r.publishableKey,publishableKey:null==r?void 0:r.publishableKey,__internal_keyless_claimKeylessApplicationUrl:null==r?void 0:r.claimUrl,__internal_keyless_copyInstanceKeysUrl:null==r?void 0:r.apiKeysUrl,__internal_bypassMissingPublishableKey:!0}):s}}}]);