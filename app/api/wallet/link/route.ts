import { auth, clerkClient } from '@clerk/nextjs/server';
import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { walletAddress } = await request.json();

    if (!walletAddress) {
      return NextResponse.json(
        { error: 'Wallet address is required' },
        { status: 400 },
      );
    }

    // Update user's public metadata with wallet address
    await clerkClient.users.updateUser(userId, {
      publicMetadata: {
        walletAddress,
        walletLinkedAt: new Date().toISOString(),
      },
    });

    return NextResponse.json({ success: true, walletAddress });
  } catch (error) {
    console.error('Error linking wallet:', error);
    return NextResponse.json(
      { error: 'Failed to link wallet' },
      { status: 500 },
    );
  }
}
