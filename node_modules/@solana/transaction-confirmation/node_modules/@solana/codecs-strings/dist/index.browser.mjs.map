{"version": 3, "sources": ["../src/assertions.ts", "../src/baseX.ts", "../src/base10.ts", "../src/base16.ts", "../src/base58.ts", "../src/baseX-reslice.ts", "../src/base64.ts", "../src/null-characters.ts", "../../text-encoding-impl/src/index.browser.ts", "../src/utf8.ts"], "names": ["alphabet", "createEncoder", "SolanaError", "SOLANA_ERROR__CODECS__INVALID_STRING_FOR_BASE", "createDecoder", "combineCodec", "TextDecoder", "TextEncoder"], "mappings": ";;;;AAsBO,SAAS,qBAAsBA,CAAAA,SAAAA,EAAkB,SAAmB,EAAA,UAAA,GAAa,SAAW,EAAA;AAC/F,EAAI,IAAA,CAAC,UAAU,KAAM,CAAA,IAAI,OAAO,CAAKA,EAAAA,EAAAA,SAAQ,CAAK,GAAA,CAAA,CAAC,CAAG,EAAA;AAClD,IAAM,MAAA,IAAI,YAAY,6CAA+C,EAAA;AAAA,MACjE,QAAAA,EAAAA,SAAAA;AAAA,MACA,MAAMA,SAAS,CAAA,MAAA;AAAA,MACf,KAAO,EAAA;AAAA,KACV,CAAA;AAAA;AAET;ACEa,IAAA,eAAA,GAAkB,CAACA,SAAkD,KAAA;AAC9E,EAAA,OAAO,aAAc,CAAA;AAAA,IACjB,gBAAA,EAAkB,CAAC,KAA0B,KAAA;AACzC,MAAM,MAAA,CAAC,eAAe,SAAS,CAAA,GAAI,uBAAuB,KAAOA,EAAAA,SAAAA,CAAS,CAAC,CAAC,CAAA;AAC5E,MAAI,IAAA,CAAC,SAAW,EAAA,OAAO,KAAM,CAAA,MAAA;AAE7B,MAAM,MAAA,YAAA,GAAe,kBAAmB,CAAA,SAAA,EAAWA,SAAQ,CAAA;AAC3D,MAAO,OAAA,aAAA,CAAc,SAAS,IAAK,CAAA,IAAA,CAAK,aAAa,QAAS,CAAA,EAAE,CAAE,CAAA,MAAA,GAAS,CAAC,CAAA;AAAA,KAChF;AAAA,IACA,KAAA,CAAM,KAAe,EAAA,KAAA,EAAO,MAAQ,EAAA;AAEhC,MAAA,qBAAA,CAAsBA,WAAU,KAAK,CAAA;AACrC,MAAI,IAAA,KAAA,KAAU,IAAW,OAAA,MAAA;AAGzB,MAAM,MAAA,CAAC,eAAe,SAAS,CAAA,GAAI,uBAAuB,KAAOA,EAAAA,SAAAA,CAAS,CAAC,CAAC,CAAA;AAC5E,MAAA,IAAI,CAAC,SAAW,EAAA;AACZ,QAAM,KAAA,CAAA,GAAA,CAAI,IAAI,UAAW,CAAA,aAAA,CAAc,MAAM,CAAE,CAAA,IAAA,CAAK,CAAC,CAAA,EAAG,MAAM,CAAA;AAC9D,QAAA,OAAO,SAAS,aAAc,CAAA,MAAA;AAAA;AAIlC,MAAI,IAAA,YAAA,GAAe,kBAAmB,CAAA,SAAA,EAAWA,SAAQ,CAAA;AAGzD,MAAA,MAAM,YAAsB,EAAC;AAC7B,MAAA,OAAO,eAAe,EAAI,EAAA;AACtB,QAAA,SAAA,CAAU,OAAQ,CAAA,MAAA,CAAO,YAAe,GAAA,IAAI,CAAC,CAAA;AAC7C,QAAgB,YAAA,IAAA,IAAA;AAAA;AAGpB,MAAM,MAAA,UAAA,GAAa,CAAC,GAAG,KAAM,CAAA,aAAA,CAAc,MAAM,CAAA,CAAE,IAAK,CAAA,CAAC,CAAG,EAAA,GAAG,SAAS,CAAA;AACxE,MAAM,KAAA,CAAA,GAAA,CAAI,YAAY,MAAM,CAAA;AAC5B,MAAA,OAAO,SAAS,UAAW,CAAA,MAAA;AAAA;AAC/B,GACH,CAAA;AACL;AAuBa,IAAA,eAAA,GAAkB,CAACA,SAAkD,KAAA;AAC9E,EAAA,OAAO,aAAc,CAAA;AAAA,IACjB,IAAA,CAAK,UAAU,MAA0B,EAAA;AACrC,MAAA,MAAM,QAAQ,MAAW,KAAA,CAAA,GAAI,QAAW,GAAA,QAAA,CAAS,MAAM,MAAM,CAAA;AAC7D,MAAA,IAAI,MAAM,MAAW,KAAA,CAAA,EAAU,OAAA,CAAC,IAAI,CAAC,CAAA;AAGrC,MAAA,IAAI,UAAa,GAAA,KAAA,CAAM,SAAU,CAAA,CAAA,CAAA,KAAK,MAAM,CAAC,CAAA;AAC7C,MAAa,UAAA,GAAA,UAAA,KAAe,EAAK,GAAA,KAAA,CAAM,MAAS,GAAA,UAAA;AAChD,MAAA,MAAM,aAAgBA,GAAAA,SAAAA,CAAS,CAAC,CAAA,CAAE,OAAO,UAAU,CAAA;AACnD,MAAA,IAAI,eAAe,KAAM,CAAA,MAAA,SAAe,CAAC,aAAA,EAAe,SAAS,MAAM,CAAA;AAGvE,MAAA,MAAM,YAAe,GAAA,KAAA,CAAM,KAAM,CAAA,UAAU,EAAE,MAAO,CAAA,CAAC,GAAK,EAAA,IAAA,KAAS,GAAM,GAAA,IAAA,GAAO,MAAO,CAAA,IAAI,GAAG,EAAE,CAAA;AAGhG,MAAM,MAAA,SAAA,GAAY,kBAAmB,CAAA,YAAA,EAAcA,SAAQ,CAAA;AAE3D,MAAA,OAAO,CAAC,aAAA,GAAgB,SAAW,EAAA,QAAA,CAAS,MAAM,CAAA;AAAA;AACtD,GACH,CAAA;AACL;AA+Ca,IAAA,aAAA,GAAgB,CAACA,SAC1B,KAAA,YAAA,CAAa,gBAAgBA,SAAQ,CAAA,EAAG,eAAgBA,CAAAA,SAAQ,CAAC;AAErE,SAAS,sBAAA,CACL,OACA,aACqD,EAAA;AACrD,EAAM,MAAA,CAAC,YAAc,EAAA,SAAS,CAAI,GAAA,KAAA,CAAM,KAAM,CAAA,IAAI,MAAO,CAAA,CAAA,IAAA,EAAO,aAAa,CAAA,IAAA,CAAM,CAAC,CAAA;AACpF,EAAO,OAAA,CAAC,cAAc,SAAS,CAAA;AACnC;AAEA,SAAS,kBAAA,CAAmB,OAAeA,SAA0B,EAAA;AACjE,EAAM,MAAA,IAAA,GAAO,MAAOA,CAAAA,SAAAA,CAAS,MAAM,CAAA;AACnC,EAAA,IAAI,GAAM,GAAA,EAAA;AACV,EAAA,KAAA,MAAW,QAAQ,KAAO,EAAA;AACtB,IAAO,GAAA,IAAA,IAAA;AACP,IAAA,GAAA,IAAO,MAAOA,CAAAA,SAAAA,CAAS,OAAQ,CAAA,IAAI,CAAC,CAAA;AAAA;AAExC,EAAO,OAAA,GAAA;AACX;AAEA,SAAS,kBAAA,CAAmB,OAAeA,SAA0B,EAAA;AACjE,EAAM,MAAA,IAAA,GAAO,MAAOA,CAAAA,SAAAA,CAAS,MAAM,CAAA;AACnC,EAAA,MAAM,YAAY,EAAC;AACnB,EAAA,OAAO,QAAQ,EAAI,EAAA;AACf,IAAA,SAAA,CAAU,QAAQA,SAAS,CAAA,MAAA,CAAO,KAAQ,GAAA,IAAI,CAAC,CAAC,CAAA;AAChD,IAAS,KAAA,IAAA,IAAA;AAAA;AAEb,EAAO,OAAA,SAAA,CAAU,KAAK,EAAE,CAAA;AAC5B;;;AC1LA,IAAM,QAAW,GAAA,YAAA;AAqBJ,IAAA,gBAAA,GAAmB,MAAM,eAAA,CAAgB,QAAQ;AAoBjD,IAAA,gBAAA,GAAmB,MAAM,eAAA,CAAgB,QAAQ;AA2CjD,IAAA,cAAA,GAAiB,MAAM,aAAA,CAAc,QAAQ;ACnE1D,IAAM,gCAAmC,GAAA;AAAA,EACrC,QAAU,EAAA,kBAAA;AAAA,EACV,IAAM,EAAA;AACV,CAAA;AAEA,SAAS,iBAAiB,IAAc,EAAA;AACpC,EAAA,IAAI,IAAQ,IAAA,EAAA,eAAa,IAAQ,IAAA,EAAA,oBAAkB,IAAO,GAAA,EAAA;AAC1D,EAAA,IAAI,QAAQ,EAAa,eAAA,IAAA,IAAQ,EAAW,aAAA,OAAO,QAAQ,EAAY,cAAA,EAAA,CAAA;AACvE,EAAA,IAAI,QAAQ,EAAa,eAAA,IAAA,IAAQ,GAAW,aAAA,OAAO,QAAQ,EAAY,cAAA,EAAA,CAAA;AAC3E;AAqBa,IAAA,gBAAA,GAAmB,MAC5BC,aAAc,CAAA;AAAA,EACV,kBAAkB,CAAC,KAAA,KAAkB,KAAK,IAAK,CAAA,KAAA,CAAM,SAAS,CAAC,CAAA;AAAA,EAC/D,KAAA,CAAM,KAAe,EAAA,KAAA,EAAO,MAAQ,EAAA;AAChC,IAAA,MAAM,MAAM,KAAM,CAAA,MAAA;AAClB,IAAA,MAAM,KAAK,GAAM,GAAA,CAAA;AACjB,IAAA,IAAI,QAAQ,CAAG,EAAA;AACX,MAAM,MAAA,CAAA,GAAI,KAAM,CAAA,UAAA,CAAW,CAAC,CAAA;AAC5B,MAAM,MAAA,CAAA,GAAI,iBAAiB,CAAC,CAAA;AAC5B,MAAA,IAAI,MAAM,MAAW,EAAA;AACjB,QAAM,MAAA,IAAIC,YAAYC,6CAA+C,EAAA;AAAA,UACjE,GAAG,gCAAA;AAAA,UACH;AAAA,SACH,CAAA;AAAA;AAEL,MAAA,KAAA,CAAM,GAAI,CAAA,CAAC,CAAC,CAAA,EAAG,MAAM,CAAA;AACrB,MAAA,OAAO,CAAI,GAAA,MAAA;AAAA;AAEf,IAAM,MAAA,QAAA,GAAW,IAAI,UAAA,CAAW,EAAE,CAAA;AAClC,IAAA,KAAA,IAAS,IAAI,CAAG,EAAA,CAAA,GAAI,CAAG,EAAA,CAAA,GAAI,IAAI,CAAK,EAAA,EAAA;AAChC,MAAM,MAAA,EAAA,GAAK,KAAM,CAAA,UAAA,CAAW,CAAG,EAAA,CAAA;AAC/B,MAAM,MAAA,EAAA,GAAK,KAAM,CAAA,UAAA,CAAW,CAAG,EAAA,CAAA;AAE/B,MAAM,MAAA,EAAA,GAAK,iBAAiB,EAAE,CAAA;AAC9B,MAAM,MAAA,EAAA,GAAK,iBAAiB,EAAE,CAAA;AAC9B,MAAI,IAAA,EAAA,KAAO,UAAc,EAAO,KAAA,MAAA,IAAa,CAAC,MAAO,CAAA,KAAA,CAAM,EAAE,CAAI,EAAA;AAC7D,QAAM,MAAA,IAAID,YAAYC,6CAA+C,EAAA;AAAA,UACjE,GAAG,gCAAA;AAAA,UACH;AAAA,SACH,CAAA;AAAA;AAEL,MAAS,QAAA,CAAA,CAAC,CAAI,GAAA,CAAC,MAAO,CAAA,KAAA,CAAM,EAAE,CAAK,GAAA,EAAA,IAAM,CAAM,IAAA,EAAA,IAAM,CAAK,CAAA,GAAA,EAAA;AAAA;AAG9D,IAAM,KAAA,CAAA,GAAA,CAAI,UAAU,MAAM,CAAA;AAC1B,IAAA,OAAO,SAAS,MAAS,GAAA,MAAA;AAAA;AAEjC,CAAC;AAoBQ,IAAA,gBAAA,GAAmB,MAC5BC,aAAc,CAAA;AAAA,EACV,IAAA,CAAK,OAAO,MAAQ,EAAA;AAChB,IAAA,MAAM,QAAQ,KAAM,CAAA,KAAA,CAAM,MAAM,CAAE,CAAA,MAAA,CAAO,CAAC,GAAK,EAAA,IAAA,KAAS,GAAM,GAAA,IAAA,CAAK,SAAS,EAAE,CAAA,CAAE,SAAS,CAAG,EAAA,GAAG,GAAG,EAAE,CAAA;AACpG,IAAO,OAAA,CAAC,KAAO,EAAA,KAAA,CAAM,MAAM,CAAA;AAAA;AAEnC,CAAC;AA2CE,IAAM,iBAAiB,MAAiCC,YAAAA,CAAa,gBAAiB,EAAA,EAAG,kBAAkB;;;ACzJlH,IAAML,SAAW,GAAA,4DAAA;AAqBJ,IAAA,gBAAA,GAAmB,MAAM,eAAA,CAAgBA,SAAQ;AAoBjD,IAAA,gBAAA,GAAmB,MAAM,eAAA,CAAgBA,SAAQ;AA2CjD,IAAA,cAAA,GAAiB,MAAM,aAAA,CAAcA,SAAQ;ACpDnD,IAAM,sBAAyB,GAAA,CAACA,SAAkB,EAAA,IAAA,KACrDC,aAAc,CAAA;AAAA,EACV,gBAAA,EAAkB,CAAC,KAAkB,KAAA,IAAA,CAAK,MAAO,KAAM,CAAA,MAAA,GAAS,OAAQ,CAAC,CAAA;AAAA,EACzE,KAAA,CAAM,KAAe,EAAA,KAAA,EAAO,MAAQ,EAAA;AAChC,IAAA,qBAAA,CAAsBD,WAAU,KAAK,CAAA;AACrC,IAAI,IAAA,KAAA,KAAU,IAAW,OAAA,MAAA;AACzB,IAAM,MAAA,WAAA,GAAc,CAAC,GAAG,KAAK,CAAA,CAAE,IAAI,CAAKA,CAAAA,KAAAA,SAAAA,CAAS,OAAQ,CAAA,CAAC,CAAC,CAAA;AAC3D,IAAA,MAAM,aAAgB,GAAA,OAAA,CAAQ,WAAa,EAAA,IAAA,EAAM,GAAG,KAAK,CAAA;AACzD,IAAM,KAAA,CAAA,GAAA,CAAI,eAAe,MAAM,CAAA;AAC/B,IAAA,OAAO,cAAc,MAAS,GAAA,MAAA;AAAA;AAEtC,CAAC;AAyBE,IAAM,sBAAyB,GAAA,CAACA,SAAkB,EAAA,IAAA,KACrDI,aAAc,CAAA;AAAA,EACV,IAAA,CAAK,QAAU,EAAA,MAAA,GAAS,CAAqB,EAAA;AACzC,IAAA,MAAM,QAAQ,MAAW,KAAA,CAAA,GAAI,QAAW,GAAA,QAAA,CAAS,MAAM,MAAM,CAAA;AAC7D,IAAA,IAAI,MAAM,MAAW,KAAA,CAAA,SAAU,CAAC,EAAA,EAAI,SAAS,MAAM,CAAA;AACnD,IAAM,MAAA,WAAA,GAAc,QAAQ,CAAC,GAAG,KAAK,CAAG,EAAA,CAAA,EAAG,MAAM,IAAI,CAAA;AACrD,IAAA,OAAO,CAAC,WAAA,CAAY,GAAI,CAAA,CAAA,CAAA,KAAKJ,SAAS,CAAA,CAAC,CAAC,CAAA,CAAE,IAAK,CAAA,EAAE,CAAG,EAAA,QAAA,CAAS,MAAM,CAAA;AAAA;AAE3E,CAAC;AA+CE,IAAM,oBAAuB,GAAA,CAACA,SAAkB,EAAA,IAAA,KACnDK,YAAa,CAAA,sBAAA,CAAuBL,SAAU,EAAA,IAAI,CAAG,EAAA,sBAAA,CAAuBA,SAAU,EAAA,IAAI,CAAC;AAG/F,SAAS,OAAQ,CAAA,KAAA,EAAiB,SAAmB,EAAA,UAAA,EAAoB,YAAiC,EAAA;AACtG,EAAA,MAAM,SAAS,EAAC;AAChB,EAAA,IAAI,WAAc,GAAA,CAAA;AAClB,EAAA,IAAI,iBAAoB,GAAA,CAAA;AACxB,EAAM,MAAA,IAAA,GAAA,CAAQ,KAAK,UAAc,IAAA,CAAA;AACjC,EAAA,KAAA,MAAW,SAAS,KAAO,EAAA;AACvB,IAAA,WAAA,GAAe,eAAe,SAAa,GAAA,KAAA;AAC3C,IAAqB,iBAAA,IAAA,SAAA;AACrB,IAAA,OAAO,qBAAqB,UAAY,EAAA;AACpC,MAAqB,iBAAA,IAAA,UAAA;AACrB,MAAO,MAAA,CAAA,IAAA,CAAM,WAAe,IAAA,iBAAA,GAAqB,IAAI,CAAA;AAAA;AACzD;AAEJ,EAAI,IAAA,YAAA,IAAgB,oBAAoB,CAAG,EAAA;AACvC,IAAA,MAAA,CAAO,IAAM,CAAA,WAAA,IAAgB,UAAa,GAAA,iBAAA,GAAsB,IAAI,CAAA;AAAA;AAExE,EAAO,OAAA,MAAA;AACX;;;ACnIA,IAAMA,SAAW,GAAA,kEAAA;AAqBV,IAAM,mBAAmB,MAAmC;AAC/D,EAAiB;AACb,IAAA,OAAOC,aAAc,CAAA;AAAA,MACjB,gBAAA,EAAkB,CAAC,KAAkB,KAAA;AACjC,QAAI,IAAA;AACA,UAAQ,OAAA,IAAA,CAAwB,KAAK,CAAE,CAAA,MAAA;AAAA,SACnC,CAAA,MAAA;AACJ,UAAM,MAAA,IAAIC,YAAYC,6CAA+C,EAAA;AAAA,YACjE,QAAAH,EAAAA,SAAAA;AAAA,YACA,IAAM,EAAA,EAAA;AAAA,YACN;AAAA,WACH,CAAA;AAAA;AACL,OACJ;AAAA,MACA,KAAA,CAAM,KAAe,EAAA,KAAA,EAAO,MAAQ,EAAA;AAChC,QAAI,IAAA;AACA,UAAA,MAAM,UAAc,GAAA,IAAA,CAAwB,KAAK,CAAA,CAC5C,KAAM,CAAA,EAAE,CACR,CAAA,GAAA,CAAI,CAAK,CAAA,KAAA,CAAA,CAAE,UAAW,CAAA,CAAC,CAAC,CAAA;AAC7B,UAAM,KAAA,CAAA,GAAA,CAAI,YAAY,MAAM,CAAA;AAC5B,UAAA,OAAO,WAAW,MAAS,GAAA,MAAA;AAAA,SACvB,CAAA,MAAA;AACJ,UAAM,MAAA,IAAIE,YAAYC,6CAA+C,EAAA;AAAA,YACjE,QAAAH,EAAAA,SAAAA;AAAA,YACA,IAAM,EAAA,EAAA;AAAA,YACN;AAAA,WACH,CAAA;AAAA;AACL;AACJ,KACH,CAAA;AAAA;AAgBT;AAoBO,IAAM,mBAAmB,MAAmC;AAC/D,EAAiB;AACb,IAAA,OAAOI,aAAc,CAAA;AAAA,MACjB,IAAA,CAAK,KAAO,EAAA,MAAA,GAAS,CAAG,EAAA;AACpB,QAAM,MAAA,KAAA,GAAQ,KAAM,CAAA,KAAA,CAAM,MAAM,CAAA;AAChC,QAAA,MAAM,QAAS,IAAwB,CAAA,MAAA,CAAO,YAAa,CAAA,GAAG,KAAK,CAAC,CAAA;AACpE,QAAO,OAAA,CAAC,KAAO,EAAA,KAAA,CAAM,MAAM,CAAA;AAAA;AAC/B,KACH,CAAA;AAAA;AAYT;AA2CO,IAAM,iBAAiB,MAAiCC,YAAAA,CAAa,gBAAiB,EAAA,EAAG,kBAAkB;;;ACrJ3G,IAAM,uBAAuB,CAAC,KAAA;AAAA;AAAA,EAEjC,KAAA,CAAM,OAAQ,CAAA,SAAA,EAAW,EAAE;AAAA;AAkBxB,IAAM,oBAAoB,CAAC,KAAA,EAAe,UAAkB,KAAM,CAAA,MAAA,CAAO,OAAO,IAAQ;;;ACnCxF,IAAMC,IAAc,UAAW,CAAA,WAAA;AAA/B,IACMC,IAAc,UAAW,CAAA,WAAA;;;AC8B/B,IAAM,iBAAiB,MAAmC;AAC7D,EAAI,IAAA,WAAA;AACJ,EAAA,OAAON,aAAc,CAAA;AAAA,IACjB,gBAAA,EAAkB,YAAU,WAAgB,KAAA,IAAI,GAAe,EAAA,MAAA,CAAO,KAAK,CAAE,CAAA,MAAA;AAAA,IAC7E,KAAO,EAAA,CAAC,KAAe,EAAA,KAAA,EAAO,MAAW,KAAA;AACrC,MAAA,MAAM,cAAc,WAAgB,KAAA,IAAI,CAAY,EAAA,EAAG,OAAO,KAAK,CAAA;AACnE,MAAM,KAAA,CAAA,GAAA,CAAI,YAAY,MAAM,CAAA;AAC5B,MAAA,OAAO,SAAS,UAAW,CAAA,MAAA;AAAA;AAC/B,GACH,CAAA;AACL;AAqBO,IAAM,iBAAiB,MAAmC;AAC7D,EAAI,IAAA,WAAA;AACJ,EAAA,OAAOG,aAAc,CAAA;AAAA,IACjB,IAAA,CAAK,OAAO,MAAQ,EAAA;AAChB,MAAM,MAAA,KAAA,GAAA,CAAS,gBAAgB,IAAI,CAAA,IAAe,MAAO,CAAA,KAAA,CAAM,KAAM,CAAA,MAAM,CAAC,CAAA;AAC5E,MAAA,OAAO,CAAC,oBAAA,CAAqB,KAAK,CAAA,EAAG,MAAM,MAAM,CAAA;AAAA;AACrD,GACH,CAAA;AACL;AA2CO,IAAM,eAAe,MAAiCC,YAAAA,CAAa,cAAe,EAAA,EAAG,gBAAgB", "file": "index.browser.mjs", "sourcesContent": ["import { SOLANA_ERROR__CODECS__INVALID_STRING_FOR_BASE, SolanaError } from '@solana/errors';\n\n/**\n * Asserts that a given string contains only characters from the specified alphabet.\n *\n * This function validates whether a string consists exclusively of characters\n * from the provided `alphabet`. If the validation fails, it throws an error\n * indicating the invalid base string.\n *\n * @param alphabet - The allowed set of characters for the base encoding.\n * @param testValue - The string to validate against the given alphabet.\n * @param givenValue - The original string provided by the user (defaults to `testValue`).\n *\n * @throws {SolanaError} If `testValue` contains characters not present in `alphabet`.\n *\n * @example\n * Validating a base-8 encoded string.\n * ```ts\n * assertValidBaseString('01234567', '123047'); // Passes\n * assertValidBaseString('01234567', '128');    // Throws error\n * ```\n */\nexport function assertValidBaseString(alphabet: string, testValue: string, givenValue = testValue) {\n    if (!testValue.match(new RegExp(`^[${alphabet}]*$`))) {\n        throw new SolanaError(SOLANA_ERROR__CODECS__INVALID_STRING_FOR_BASE, {\n            alphabet,\n            base: alphabet.length,\n            value: givenValue,\n        });\n    }\n}\n", "import {\n    combineCodec,\n    createDecoder,\n    createEncoder,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\n\nimport { assertValidBaseString } from './assertions';\n\n/**\n * Returns an encoder for base-X encoded strings.\n *\n * This encoder serializes strings using a custom alphabet, treating the length of the alphabet as the base.\n * The encoding process involves converting the input string to a numeric value in base-X, then\n * encoding that value into bytes while preserving leading zeroes.\n *\n * For more details, see {@link getBaseXCodec}.\n *\n * @param alphabet - The set of characters defining the base-X encoding.\n * @returns A `VariableSizeEncoder<string>` for encoding base-X strings.\n *\n * @example\n * Encoding a base-X string using a custom alphabet.\n * ```ts\n * const encoder = getBaseXEncoder('0123456789abcdef');\n * const bytes = encoder.encode('deadface'); // 0xdeadface\n * ```\n *\n * @see {@link getBaseXCodec}\n */\nexport const getBaseXEncoder = (alphabet: string): VariableSizeEncoder<string> => {\n    return createEncoder({\n        getSizeFromValue: (value: string): number => {\n            const [leadingZeroes, tailChars] = partitionLeadingZeroes(value, alphabet[0]);\n            if (!tailChars) return value.length;\n\n            const base10Number = getBigIntFromBaseX(tailChars, alphabet);\n            return leadingZeroes.length + Math.ceil(base10Number.toString(16).length / 2);\n        },\n        write(value: string, bytes, offset) {\n            // Check if the value is valid.\n            assertValidBaseString(alphabet, value);\n            if (value === '') return offset;\n\n            // Handle leading zeroes.\n            const [leadingZeroes, tailChars] = partitionLeadingZeroes(value, alphabet[0]);\n            if (!tailChars) {\n                bytes.set(new Uint8Array(leadingZeroes.length).fill(0), offset);\n                return offset + leadingZeroes.length;\n            }\n\n            // From baseX to base10.\n            let base10Number = getBigIntFromBaseX(tailChars, alphabet);\n\n            // From base10 to bytes.\n            const tailBytes: number[] = [];\n            while (base10Number > 0n) {\n                tailBytes.unshift(Number(base10Number % 256n));\n                base10Number /= 256n;\n            }\n\n            const bytesToAdd = [...Array(leadingZeroes.length).fill(0), ...tailBytes];\n            bytes.set(bytesToAdd, offset);\n            return offset + bytesToAdd.length;\n        },\n    });\n};\n\n/**\n * Returns a decoder for base-X encoded strings.\n *\n * This decoder deserializes base-X encoded strings from a byte array using a custom alphabet.\n * The decoding process converts the byte array into a numeric value in base-10, then\n * maps that value back to characters in the specified base-X alphabet.\n *\n * For more details, see {@link getBaseXCodec}.\n *\n * @param alphabet - The set of characters defining the base-X encoding.\n * @returns A `VariableSizeDecoder<string>` for decoding base-X strings.\n *\n * @example\n * Decoding a base-X string using a custom alphabet.\n * ```ts\n * const decoder = getBaseXDecoder('0123456789abcdef');\n * const value = decoder.decode(new Uint8Array([0xde, 0xad, 0xfa, 0xce])); // \"deadface\"\n * ```\n *\n * @see {@link getBaseXCodec}\n */\nexport const getBaseXDecoder = (alphabet: string): VariableSizeDecoder<string> => {\n    return createDecoder({\n        read(rawBytes, offset): [string, number] {\n            const bytes = offset === 0 ? rawBytes : rawBytes.slice(offset);\n            if (bytes.length === 0) return ['', 0];\n\n            // Handle leading zeroes.\n            let trailIndex = bytes.findIndex(n => n !== 0);\n            trailIndex = trailIndex === -1 ? bytes.length : trailIndex;\n            const leadingZeroes = alphabet[0].repeat(trailIndex);\n            if (trailIndex === bytes.length) return [leadingZeroes, rawBytes.length];\n\n            // From bytes to base10.\n            const base10Number = bytes.slice(trailIndex).reduce((sum, byte) => sum * 256n + BigInt(byte), 0n);\n\n            // From base10 to baseX.\n            const tailChars = getBaseXFromBigInt(base10Number, alphabet);\n\n            return [leadingZeroes + tailChars, rawBytes.length];\n        },\n    });\n};\n\n/**\n * Returns a codec for encoding and decoding base-X strings.\n *\n * This codec serializes strings using a custom alphabet, treating the length of the alphabet as the base.\n * The encoding process converts the input string into a numeric value in base-X, which is then encoded as bytes.\n * The decoding process reverses this transformation to reconstruct the original string.\n *\n * This codec supports leading zeroes by treating the first character of the alphabet as the zero character.\n *\n * @param alphabet - The set of characters defining the base-X encoding.\n * @returns A `VariableSizeCodec<string>` for encoding and decoding base-X strings.\n *\n * @example\n * Encoding and decoding a base-X string using a custom alphabet.\n * ```ts\n * const codec = getBaseXCodec('0123456789abcdef');\n * const bytes = codec.encode('deadface'); // 0xdeadface\n * const value = codec.decode(bytes);      // \"deadface\"\n * ```\n *\n * @remarks\n * This codec does not enforce a size boundary. It will encode and decode all bytes necessary to represent the string.\n *\n * If you need a fixed-size base-X codec, consider using {@link fixCodecSize}.\n *\n * ```ts\n * const codec = fixCodecSize(getBaseXCodec('0123456789abcdef'), 8);\n * ```\n *\n * If you need a size-prefixed base-X codec, consider using {@link addCodecSizePrefix}.\n *\n * ```ts\n * const codec = addCodecSizePrefix(getBaseXCodec('0123456789abcdef'), getU32Codec());\n * ```\n *\n * Separate {@link getBaseXEncoder} and {@link getBaseXDecoder} functions are available.\n *\n * ```ts\n * const bytes = getBaseXEncoder('0123456789abcdef').encode('deadface');\n * const value = getBaseXDecoder('0123456789abcdef').decode(bytes);\n * ```\n *\n * @see {@link getBaseXEncoder}\n * @see {@link getBaseXDecoder}\n */\nexport const getBaseXCodec = (alphabet: string): VariableSizeCodec<string> =>\n    combineCodec(getBaseXEncoder(alphabet), getBaseXDecoder(alphabet));\n\nfunction partitionLeadingZeroes(\n    value: string,\n    zeroCharacter: string,\n): [leadingZeros: string, tailChars: string | undefined] {\n    const [leadingZeros, tailChars] = value.split(new RegExp(`((?!${zeroCharacter}).*)`));\n    return [leadingZeros, tailChars];\n}\n\nfunction getBigIntFromBaseX(value: string, alphabet: string): bigint {\n    const base = BigInt(alphabet.length);\n    let sum = 0n;\n    for (const char of value) {\n        sum *= base;\n        sum += BigInt(alphabet.indexOf(char));\n    }\n    return sum;\n}\n\nfunction getBaseXFromBigInt(value: bigint, alphabet: string): string {\n    const base = BigInt(alphabet.length);\n    const tailChars = [];\n    while (value > 0n) {\n        tailChars.unshift(alphabet[Number(value % base)]);\n        value /= base;\n    }\n    return tailChars.join('');\n}\n", "import { getBaseXCodec, getBaseXDecoder, getBaseXEncoder } from './baseX';\n\nconst alphabet = '0123456789';\n\n/**\n * Returns an encoder for base-10 strings.\n *\n * This encoder serializes strings using a base-10 encoding scheme.\n * The output consists of bytes representing the numerical values of the input string.\n *\n * For more details, see {@link getBase10Codec}.\n *\n * @returns A `VariableSizeEncoder<string>` for encoding base-10 strings.\n *\n * @example\n * Encoding a base-10 string.\n * ```ts\n * const encoder = getBase10Encoder();\n * const bytes = encoder.encode('1024'); // 0x0400\n * ```\n *\n * @see {@link getBase10Codec}\n */\nexport const getBase10Encoder = () => getBaseXEncoder(alphabet);\n\n/**\n * Returns a decoder for base-10 strings.\n *\n * This decoder deserializes base-10 encoded strings from a byte array.\n *\n * For more details, see {@link getBase10Codec}.\n *\n * @returns A `VariableSizeDecoder<string>` for decoding base-10 strings.\n *\n * @example\n * Decoding a base-10 string.\n * ```ts\n * const decoder = getBase10Decoder();\n * const value = decoder.decode(new Uint8Array([0x04, 0x00])); // \"1024\"\n * ```\n *\n * @see {@link getBase10Codec}\n */\nexport const getBase10Decoder = () => getBaseXDecoder(alphabet);\n\n/**\n * Returns a codec for encoding and decoding base-10 strings.\n *\n * This codec serializes strings using a base-10 encoding scheme.\n * The output consists of bytes representing the numerical values of the input string.\n *\n * @returns A `VariableSizeCodec<string>` for encoding and decoding base-10 strings.\n *\n * @example\n * Encoding and decoding a base-10 string.\n * ```ts\n * const codec = getBase10Codec();\n * const bytes = codec.encode('1024'); // 0x0400\n * const value = codec.decode(bytes);  // \"1024\"\n * ```\n *\n * @remarks\n * This codec does not enforce a size boundary. It will encode and decode all bytes necessary to represent the string.\n *\n * If you need a fixed-size base-10 codec, consider using {@link fixCodecSize}.\n *\n * ```ts\n * const codec = fixCodecSize(getBase10Codec(), 5);\n * ```\n *\n * If you need a size-prefixed base-10 codec, consider using {@link addCodecSizePrefix}.\n *\n * ```ts\n * const codec = addCodecSizePrefix(getBase10Codec(), getU32Codec());\n * ```\n *\n * Separate {@link getBase10Encoder} and {@link getBase10Decoder} functions are available.\n *\n * ```ts\n * const bytes = getBase10Encoder().encode('1024');\n * const value = getBase10Decoder().decode(bytes);\n * ```\n *\n * @see {@link getBase10Encoder}\n * @see {@link getBase10Decoder}\n */\nexport const getBase10Codec = () => getBaseXCodec(alphabet);\n", "import {\n    combineCodec,\n    createDecoder,\n    createEncoder,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\nimport { SOLANA_ERROR__CODECS__INVALID_STRING_FOR_BASE, SolanaError } from '@solana/errors';\n\nconst enum HexC {\n    ZERO = 48, // 0\n    NINE = 57, // 9\n    A_UP = 65, // A\n    F_UP = 70, // F\n    A_LO = 97, // a\n    F_LO = 102, // f\n}\n\nconst INVALID_STRING_ERROR_BASE_CONFIG = {\n    alphabet: '0123456789abcdef',\n    base: 16,\n} as const;\n\nfunction charCodeToBase16(char: number) {\n    if (char >= HexC.ZERO && char <= HexC.NINE) return char - HexC.ZERO;\n    if (char >= HexC.A_UP && char <= HexC.F_UP) return char - (HexC.A_UP - 10);\n    if (char >= HexC.A_LO && char <= HexC.F_LO) return char - (HexC.A_LO - 10);\n}\n\n/**\n * Returns an encoder for base-16 (hexadecimal) strings.\n *\n * This encoder serializes strings using a base-16 encoding scheme.\n * The output consists of bytes representing the hexadecimal values of the input string.\n *\n * For more details, see {@link getBase16Codec}.\n *\n * @returns A `VariableSizeEncoder<string>` for encoding base-16 strings.\n *\n * @example\n * Encoding a base-16 string.\n * ```ts\n * const encoder = getBase16Encoder();\n * const bytes = encoder.encode('deadface'); // 0xdeadface\n * ```\n *\n * @see {@link getBase16Codec}\n */\nexport const getBase16Encoder = (): VariableSizeEncoder<string> =>\n    createEncoder({\n        getSizeFromValue: (value: string) => Math.ceil(value.length / 2),\n        write(value: string, bytes, offset) {\n            const len = value.length;\n            const al = len / 2;\n            if (len === 1) {\n                const c = value.charCodeAt(0);\n                const n = charCodeToBase16(c);\n                if (n === undefined) {\n                    throw new SolanaError(SOLANA_ERROR__CODECS__INVALID_STRING_FOR_BASE, {\n                        ...INVALID_STRING_ERROR_BASE_CONFIG,\n                        value,\n                    });\n                }\n                bytes.set([n], offset);\n                return 1 + offset;\n            }\n            const hexBytes = new Uint8Array(al);\n            for (let i = 0, j = 0; i < al; i++) {\n                const c1 = value.charCodeAt(j++);\n                const c2 = value.charCodeAt(j++);\n\n                const n1 = charCodeToBase16(c1);\n                const n2 = charCodeToBase16(c2);\n                if (n1 === undefined || (n2 === undefined && !Number.isNaN(c2))) {\n                    throw new SolanaError(SOLANA_ERROR__CODECS__INVALID_STRING_FOR_BASE, {\n                        ...INVALID_STRING_ERROR_BASE_CONFIG,\n                        value,\n                    });\n                }\n                hexBytes[i] = !Number.isNaN(c2) ? (n1 << 4) | (n2 ?? 0) : n1;\n            }\n\n            bytes.set(hexBytes, offset);\n            return hexBytes.length + offset;\n        },\n    });\n\n/**\n * Returns a decoder for base-16 (hexadecimal) strings.\n *\n * This decoder deserializes base-16 encoded strings from a byte array.\n *\n * For more details, see {@link getBase16Codec}.\n *\n * @returns A `VariableSizeDecoder<string>` for decoding base-16 strings.\n *\n * @example\n * Decoding a base-16 string.\n * ```ts\n * const decoder = getBase16Decoder();\n * const value = decoder.decode(new Uint8Array([0xde, 0xad, 0xfa, 0xce])); // \"deadface\"\n * ```\n *\n * @see {@link getBase16Codec}\n */\nexport const getBase16Decoder = (): VariableSizeDecoder<string> =>\n    createDecoder({\n        read(bytes, offset) {\n            const value = bytes.slice(offset).reduce((str, byte) => str + byte.toString(16).padStart(2, '0'), '');\n            return [value, bytes.length];\n        },\n    });\n\n/**\n * Returns a codec for encoding and decoding base-16 (hexadecimal) strings.\n *\n * This codec serializes strings using a base-16 encoding scheme.\n * The output consists of bytes representing the hexadecimal values of the input string.\n *\n * @returns A `VariableSizeCodec<string>` for encoding and decoding base-16 strings.\n *\n * @example\n * Encoding and decoding a base-16 string.\n * ```ts\n * const codec = getBase16Codec();\n * const bytes = codec.encode('deadface'); // 0xdeadface\n * const value = codec.decode(bytes);      // \"deadface\"\n * ```\n *\n * @remarks\n * This codec does not enforce a size boundary. It will encode and decode all bytes necessary to represent the string.\n *\n * If you need a fixed-size base-16 codec, consider using {@link fixCodecSize}.\n *\n * ```ts\n * const codec = fixCodecSize(getBase16Codec(), 8);\n * ```\n *\n * If you need a size-prefixed base-16 codec, consider using {@link addCodecSizePrefix}.\n *\n * ```ts\n * const codec = addCodecSizePrefix(getBase16Codec(), getU32Codec());\n * ```\n *\n * Separate {@link getBase16Encoder} and {@link getBase16Decoder} functions are available.\n *\n * ```ts\n * const bytes = getBase16Encoder().encode('deadface');\n * const value = getBase16Decoder().decode(bytes);\n * ```\n *\n * @see {@link getBase16Encoder}\n * @see {@link getBase16Decoder}\n */\nexport const getBase16Codec = (): VariableSizeCodec<string> => combineCodec(getBase16Encoder(), getBase16Decoder());\n", "import { getBaseXCodec, getBaseXDecoder, getBaseXEncoder } from './baseX';\n\nconst alphabet = '**********************************************************';\n\n/**\n * Returns an encoder for base-58 strings.\n *\n * This encoder serializes strings using a base-58 encoding scheme,\n * commonly used in cryptocurrency addresses and other compact representations.\n *\n * For more details, see {@link getBase58Codec}.\n *\n * @returns A `VariableSizeEncoder<string>` for encoding base-58 strings.\n *\n * @example\n * Encoding a base-58 string.\n * ```ts\n * const encoder = getBase58Encoder();\n * const bytes = encoder.encode('heLLo'); // 0x1b6a3070\n * ```\n *\n * @see {@link getBase58Codec}\n */\nexport const getBase58Encoder = () => getBaseXEncoder(alphabet);\n\n/**\n * Returns a decoder for base-58 strings.\n *\n * This decoder deserializes base-58 encoded strings from a byte array.\n *\n * For more details, see {@link getBase58Codec}.\n *\n * @returns A `VariableSizeDecoder<string>` for decoding base-58 strings.\n *\n * @example\n * Decoding a base-58 string.\n * ```ts\n * const decoder = getBase58Decoder();\n * const value = decoder.decode(new Uint8Array([0x1b, 0x6a, 0x30, 0x70])); // \"heLLo\"\n * ```\n *\n * @see {@link getBase58Codec}\n */\nexport const getBase58Decoder = () => getBaseXDecoder(alphabet);\n\n/**\n * Returns a codec for encoding and decoding base-58 strings.\n *\n * This codec serializes strings using a base-58 encoding scheme,\n * commonly used in cryptocurrency addresses and other compact representations.\n *\n * @returns A `VariableSizeCodec<string>` for encoding and decoding base-58 strings.\n *\n * @example\n * Encoding and decoding a base-58 string.\n * ```ts\n * const codec = getBase58Codec();\n * const bytes = codec.encode('heLLo'); // 0x1b6a3070\n * const value = codec.decode(bytes);   // \"heLLo\"\n * ```\n *\n * @remarks\n * This codec does not enforce a size boundary. It will encode and decode all bytes necessary to represent the string.\n *\n * If you need a fixed-size base-58 codec, consider using {@link fixCodecSize}.\n *\n * ```ts\n * const codec = fixCodecSize(getBase58Codec(), 8);\n * ```\n *\n * If you need a size-prefixed base-58 codec, consider using {@link addCodecSizePrefix}.\n *\n * ```ts\n * const codec = addCodecSizePrefix(getBase58Codec(), getU32Codec());\n * ```\n *\n * Separate {@link getBase58Encoder} and {@link getBase58Decoder} functions are available.\n *\n * ```ts\n * const bytes = getBase58Encoder().encode('heLLo');\n * const value = getBase58Decoder().decode(bytes);\n * ```\n *\n * @see {@link getBase58Encoder}\n * @see {@link getBase58Decoder}\n */\nexport const getBase58Codec = () => getBaseXCodec(alphabet);\n", "import {\n    combineCodec,\n    createDecode<PERSON>,\n    createEncoder,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\n\nimport { assertValidBaseString } from './assertions';\n\n/**\n * Returns an encoder for base-X encoded strings using bit re-slicing.\n *\n * This encoder serializes strings by dividing the input into custom-sized bit chunks,\n * mapping them to an alphabet, and encoding the result into a byte array.\n * This approach is commonly used for encoding schemes where the alphabet's length is a power of 2,\n * such as base-16 or base-64.\n *\n * For more details, see {@link getBaseXResliceCodec}.\n *\n * @param alphabet - The set of characters defining the base-X encoding.\n * @param bits - The number of bits per encoded chunk, typically `log2(alphabet.length)`.\n * @returns A `VariableSizeEncoder<string>` for encoding base-X strings using bit re-slicing.\n *\n * @example\n * Encoding a base-X string using bit re-slicing.\n * ```ts\n * const encoder = getBaseXResliceEncoder('elho', 2);\n * const bytes = encoder.encode('hellolol'); // 0x4aee\n * ```\n *\n * @see {@link getBaseXResliceCodec}\n */\nexport const getBaseXResliceEncoder = (alphabet: string, bits: number): VariableSizeEncoder<string> =>\n    createEncoder({\n        getSizeFromValue: (value: string) => Math.floor((value.length * bits) / 8),\n        write(value: string, bytes, offset) {\n            assertValidBaseString(alphabet, value);\n            if (value === '') return offset;\n            const charIndices = [...value].map(c => alphabet.indexOf(c));\n            const reslicedBytes = reslice(charIndices, bits, 8, false);\n            bytes.set(reslicedBytes, offset);\n            return reslicedBytes.length + offset;\n        },\n    });\n\n/**\n * Returns a decoder for base-X encoded strings using bit re-slicing.\n *\n * This decoder deserializes base-X encoded strings by re-slicing the bits of a byte array into\n * custom-sized chunks and mapping them to a specified alphabet.\n * This is typically used for encoding schemes where the alphabet's length is a power of 2,\n * such as base-16 or base-64.\n *\n * For more details, see {@link getBaseXResliceCodec}.\n *\n * @param alphabet - The set of characters defining the base-X encoding.\n * @param bits - The number of bits per encoded chunk, typically `log2(alphabet.length)`.\n * @returns A `VariableSizeDecoder<string>` for decoding base-X strings using bit re-slicing.\n *\n * @example\n * Decoding a base-X string using bit re-slicing.\n * ```ts\n * const decoder = getBaseXResliceDecoder('elho', 2);\n * const value = decoder.decode(new Uint8Array([0x4a, 0xee])); // \"hellolol\"\n * ```\n *\n * @see {@link getBaseXResliceCodec}\n */\nexport const getBaseXResliceDecoder = (alphabet: string, bits: number): VariableSizeDecoder<string> =>\n    createDecoder({\n        read(rawBytes, offset = 0): [string, number] {\n            const bytes = offset === 0 ? rawBytes : rawBytes.slice(offset);\n            if (bytes.length === 0) return ['', rawBytes.length];\n            const charIndices = reslice([...bytes], 8, bits, true);\n            return [charIndices.map(i => alphabet[i]).join(''), rawBytes.length];\n        },\n    });\n\n/**\n * Returns a codec for encoding and decoding base-X strings using bit re-slicing.\n *\n * This codec serializes strings by dividing the input into custom-sized bit chunks,\n * mapping them to a given alphabet, and encoding the result into bytes.\n * It is particularly suited for encoding schemes where the alphabet's length is a power of 2,\n * such as base-16 or base-64.\n *\n * @param alphabet - The set of characters defining the base-X encoding.\n * @param bits - The number of bits per encoded chunk, typically `log2(alphabet.length)`.\n * @returns A `VariableSizeCodec<string>` for encoding and decoding base-X strings using bit re-slicing.\n *\n * @example\n * Encoding and decoding a base-X string using bit re-slicing.\n * ```ts\n * const codec = getBaseXResliceCodec('elho', 2);\n * const bytes = codec.encode('hellolol'); // 0x4aee\n * const value = codec.decode(bytes);      // \"hellolol\"\n * ```\n *\n * @remarks\n * This codec does not enforce a size boundary. It will encode and decode all bytes necessary to represent the string.\n *\n * If you need a fixed-size base-X codec, consider using {@link fixCodecSize}.\n *\n * ```ts\n * const codec = fixCodecSize(getBaseXResliceCodec('elho', 2), 8);\n * ```\n *\n * If you need a size-prefixed base-X codec, consider using {@link addCodecSizePrefix}.\n *\n * ```ts\n * const codec = addCodecSizePrefix(getBaseXResliceCodec('elho', 2), getU32Codec());\n * ```\n *\n * Separate {@link getBaseXResliceEncoder} and {@link getBaseXResliceDecoder} functions are available.\n *\n * ```ts\n * const bytes = getBaseXResliceEncoder('elho', 2).encode('hellolol');\n * const value = getBaseXResliceDecoder('elho', 2).decode(bytes);\n * ```\n *\n * @see {@link getBaseXResliceEncoder}\n * @see {@link getBaseXResliceDecoder}\n */\nexport const getBaseXResliceCodec = (alphabet: string, bits: number): VariableSizeCodec<string> =>\n    combineCodec(getBaseXResliceEncoder(alphabet, bits), getBaseXResliceDecoder(alphabet, bits));\n\n/** Helper function to reslice the bits inside bytes. */\nfunction reslice(input: number[], inputBits: number, outputBits: number, useRemainder: boolean): number[] {\n    const output = [];\n    let accumulator = 0;\n    let bitsInAccumulator = 0;\n    const mask = (1 << outputBits) - 1;\n    for (const value of input) {\n        accumulator = (accumulator << inputBits) | value;\n        bitsInAccumulator += inputBits;\n        while (bitsInAccumulator >= outputBits) {\n            bitsInAccumulator -= outputBits;\n            output.push((accumulator >> bitsInAccumulator) & mask);\n        }\n    }\n    if (useRemainder && bitsInAccumulator > 0) {\n        output.push((accumulator << (outputBits - bitsInAccumulator)) & mask);\n    }\n    return output;\n}\n", "import {\n    combineCodec,\n    createDecoder,\n    createEncoder,\n    transformDecoder,\n    transformEncoder,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\nimport { SOLANA_ERROR__CODECS__INVALID_STRING_FOR_BASE, SolanaError } from '@solana/errors';\n\nimport { assertValidBaseString } from './assertions';\nimport { getBaseXResliceDecoder, getBaseXResliceEncoder } from './baseX-reslice';\n\nconst alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n\n/**\n * Returns an encoder for base-64 strings.\n *\n * This encoder serializes strings using a base-64 encoding scheme,\n * commonly used for data encoding in URLs, cryptographic keys, and binary-to-text encoding.\n *\n * For more details, see {@link getBase64Codec}.\n *\n * @returns A `VariableSizeEncoder<string>` for encoding base-64 strings.\n *\n * @example\n * Encoding a base-64 string.\n * ```ts\n * const encoder = getBase64Encoder();\n * const bytes = encoder.encode('hello+world'); // 0x85e965a3ec28ae57\n * ```\n *\n * @see {@link getBase64Codec}\n */\nexport const getBase64Encoder = (): VariableSizeEncoder<string> => {\n    if (__BROWSER__) {\n        return createEncoder({\n            getSizeFromValue: (value: string) => {\n                try {\n                    return (atob as Window['atob'])(value).length;\n                } catch {\n                    throw new SolanaError(SOLANA_ERROR__CODECS__INVALID_STRING_FOR_BASE, {\n                        alphabet,\n                        base: 64,\n                        value,\n                    });\n                }\n            },\n            write(value: string, bytes, offset) {\n                try {\n                    const bytesToAdd = (atob as Window['atob'])(value)\n                        .split('')\n                        .map(c => c.charCodeAt(0));\n                    bytes.set(bytesToAdd, offset);\n                    return bytesToAdd.length + offset;\n                } catch {\n                    throw new SolanaError(SOLANA_ERROR__CODECS__INVALID_STRING_FOR_BASE, {\n                        alphabet,\n                        base: 64,\n                        value,\n                    });\n                }\n            },\n        });\n    }\n\n    if (__NODEJS__) {\n        return createEncoder({\n            getSizeFromValue: (value: string) => Buffer.from(value, 'base64').length,\n            write(value: string, bytes, offset) {\n                assertValidBaseString(alphabet, value.replace(/=/g, ''));\n                const buffer = Buffer.from(value, 'base64');\n                bytes.set(buffer, offset);\n                return buffer.length + offset;\n            },\n        });\n    }\n\n    return transformEncoder(getBaseXResliceEncoder(alphabet, 6), (value: string): string => value.replace(/=/g, ''));\n};\n\n/**\n * Returns a decoder for base-64 strings.\n *\n * This decoder deserializes base-64 encoded strings from a byte array.\n *\n * For more details, see {@link getBase64Codec}.\n *\n * @returns A `VariableSizeDecoder<string>` for decoding base-64 strings.\n *\n * @example\n * Decoding a base-64 string.\n * ```ts\n * const decoder = getBase64Decoder();\n * const value = decoder.decode(new Uint8Array([0x85, 0xe9, 0x65, 0xa3, 0xec, 0x28, 0xae, 0x57])); // \"hello+world\"\n * ```\n *\n * @see {@link getBase64Codec}\n */\nexport const getBase64Decoder = (): VariableSizeDecoder<string> => {\n    if (__BROWSER__) {\n        return createDecoder({\n            read(bytes, offset = 0) {\n                const slice = bytes.slice(offset);\n                const value = (btoa as Window['btoa'])(String.fromCharCode(...slice));\n                return [value, bytes.length];\n            },\n        });\n    }\n\n    if (__NODEJS__) {\n        return createDecoder({\n            read: (bytes, offset = 0) => [Buffer.from(bytes, offset).toString('base64'), bytes.length],\n        });\n    }\n\n    return transformDecoder(getBaseXResliceDecoder(alphabet, 6), (value: string): string =>\n        value.padEnd(Math.ceil(value.length / 4) * 4, '='),\n    );\n};\n\n/**\n * Returns a codec for encoding and decoding base-64 strings.\n *\n * This codec serializes strings using a base-64 encoding scheme,\n * commonly used for data encoding in URLs, cryptographic keys, and binary-to-text encoding.\n *\n * @returns A `VariableSizeCodec<string>` for encoding and decoding base-64 strings.\n *\n * @example\n * Encoding and decoding a base-64 string.\n * ```ts\n * const codec = getBase64Codec();\n * const bytes = codec.encode('hello+world'); // 0x85e965a3ec28ae57\n * const value = codec.decode(bytes);         // \"hello+world\"\n * ```\n *\n * @remarks\n * This codec does not enforce a size boundary. It will encode and decode all bytes necessary to represent the string.\n *\n * If you need a fixed-size base-64 codec, consider using {@link fixCodecSize}.\n *\n * ```ts\n * const codec = fixCodecSize(getBase64Codec(), 8);\n * ```\n *\n * If you need a size-prefixed base-64 codec, consider using {@link addCodecSizePrefix}.\n *\n * ```ts\n * const codec = addCodecSizePrefix(getBase64Codec(), getU32Codec());\n * ```\n *\n * Separate {@link getBase64Encoder} and {@link getBase64Decoder} functions are available.\n *\n * ```ts\n * const bytes = getBase64Encoder().encode('hello+world');\n * const value = getBase64Decoder().decode(bytes);\n * ```\n *\n * @see {@link getBase64Encoder}\n * @see {@link getBase64Decoder}\n */\nexport const getBase64Codec = (): VariableSizeCodec<string> => combineCodec(getBase64Encoder(), getBase64Decoder());\n", "/**\n * Removes all null characters (`\\u0000`) from a string.\n *\n * This function cleans a string by stripping out any null characters,\n * which are often used as padding in fixed-size string encodings.\n *\n * @param value - The string to process.\n * @returns The input string with all null characters removed.\n *\n * @example\n * Removing null characters from a string.\n * ```ts\n * removeNullCharacters('hello\\u0000\\u0000'); // \"hello\"\n * ```\n */\nexport const removeNullCharacters = (value: string) =>\n    // eslint-disable-next-line no-control-regex\n    value.replace(/\\u0000/g, '');\n\n/**\n * Pads a string with null characters (`\\u0000`) at the end to reach a fixed length.\n *\n * If the input string is shorter than the specified length, it is padded with null characters\n * until it reaches the desired size. If it is already long enough, it remains unchanged.\n *\n * @param value - The string to pad.\n * @param chars - The total length of the resulting string, including padding.\n * @returns The input string padded with null characters up to the specified length.\n *\n * @example\n * Padding a string with null characters.\n * ```ts\n * padNull<PERSON>haracters('hello', 8); // \"hello\\u0000\\u0000\\u0000\"\n * ```\n */\nexport const padNullCharacters = (value: string, chars: number) => value.padEnd(chars, '\\u0000');\n", "export const TextDecoder = globalThis.TextDecoder;\nexport const TextEncoder = globalThis.TextEncoder;\n", "import {\n    combineCodec,\n    createDecoder,\n    createEncoder,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\nimport { TextDecoder, TextEncoder } from '@solana/text-encoding-impl';\n\nimport { removeNull<PERSON><PERSON>cters } from './null-characters';\n\n/**\n * Returns an encoder for UTF-8 strings.\n *\n * This encoder serializes strings using UTF-8 encoding.\n * The encoded output contains as many bytes as needed to represent the string.\n *\n * For more details, see {@link getUtf8Codec}.\n *\n * @returns A `VariableSizeEncoder<string>` for encoding UTF-8 strings.\n *\n * @example\n * Encoding a UTF-8 string.\n * ```ts\n * const encoder = getUtf8Encoder();\n * const bytes = encoder.encode('hello'); // 0x68656c6c6f\n * ```\n *\n * @see {@link getUtf8Codec}\n */\nexport const getUtf8Encoder = (): VariableSizeEncoder<string> => {\n    let textEncoder: TextEncoder;\n    return createEncoder({\n        getSizeFromValue: value => (textEncoder ||= new TextEncoder()).encode(value).length,\n        write: (value: string, bytes, offset) => {\n            const bytesToAdd = (textEncoder ||= new TextEncoder()).encode(value);\n            bytes.set(bytesToAdd, offset);\n            return offset + bytesToAdd.length;\n        },\n    });\n};\n\n/**\n * Returns a decoder for UTF-8 strings.\n *\n * This decoder deserializes UTF-8 encoded strings from a byte array.\n * It reads all available bytes starting from the given offset.\n *\n * For more details, see {@link getUtf8Codec}.\n *\n * @returns A `VariableSizeDecoder<string>` for decoding UTF-8 strings.\n *\n * @example\n * Decoding a UTF-8 string.\n * ```ts\n * const decoder = getUtf8Decoder();\n * const value = decoder.decode(new Uint8Array([0x68, 0x65, 0x6c, 0x6c, 0x6f])); // \"hello\"\n * ```\n *\n * @see {@link getUtf8Codec}\n */\nexport const getUtf8Decoder = (): VariableSizeDecoder<string> => {\n    let textDecoder: TextDecoder;\n    return createDecoder({\n        read(bytes, offset) {\n            const value = (textDecoder ||= new TextDecoder()).decode(bytes.slice(offset));\n            return [removeNullCharacters(value), bytes.length];\n        },\n    });\n};\n\n/**\n * Returns a codec for encoding and decoding UTF-8 strings.\n *\n * This codec serializes strings using UTF-8 encoding.\n * The encoded output contains as many bytes as needed to represent the string.\n *\n * @returns A `VariableSizeCodec<string>` for encoding and decoding UTF-8 strings.\n *\n * @example\n * Encoding and decoding a UTF-8 string.\n * ```ts\n * const codec = getUtf8Codec();\n * const bytes = codec.encode('hello'); // 0x68656c6c6f\n * const value = codec.decode(bytes);   // \"hello\"\n * ```\n *\n * @remarks\n * This codec does not enforce a size boundary. It will encode and decode all bytes necessary to represent the string.\n *\n * If you need a fixed-size UTF-8 codec, consider using {@link fixCodecSize}.\n *\n * ```ts\n * const codec = fixCodecSize(getUtf8Codec(), 5);\n * ```\n *\n * If you need a size-prefixed UTF-8 codec, consider using {@link addCodecSizePrefix}.\n *\n * ```ts\n * const codec = addCodecSizePrefix(getUtf8Codec(), getU32Codec());\n * ```\n *\n * Separate {@link getUtf8Encoder} and {@link getUtf8Decoder} functions are available.\n *\n * ```ts\n * const bytes = getUtf8Encoder().encode('hello');\n * const value = getUtf8Decoder().decode(bytes);\n * ```\n *\n * @see {@link getUtf8Encoder}\n * @see {@link getUtf8Decoder}\n */\nexport const getUtf8Codec = (): VariableSizeCodec<string> => combineCodec(getUtf8Encoder(), getUtf8Decoder());\n"]}