{"extends": "../../tsconfig.base.json", "compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "bundler", "outDir": "dist", "rootDir": "src", "noEmit": false, "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "types": ["node"], "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/convex/*": ["../../convex/*"], "@/lib/*": ["../../lib/*"]}}, "include": ["src/**/*", "../../convex/**/*", "../../lib/**/*"], "exclude": ["node_modules", "dist"]}