const CHUNK_PUBLIC_PATH = "server/app/(auth)/api/auth/guest/route.js";
const runtime = require("../../../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules_next_0893760a._.js");
runtime.loadChunk("server/chunks/node_modules_@auth_core_8fd8d1ce._.js");
runtime.loadChunk("server/chunks/node_modules_jose_dist_node_esm_69b56690._.js");
runtime.loadChunk("server/chunks/node_modules_bdc51c4a._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__7c5de47c._.js");
runtime.getOrInstantiateRuntimeModule("[project]/apps/web/.next-internal/server/app/(auth)/api/auth/guest/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/apps/web/app/(auth)/api/auth/guest/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/apps/web/app/(auth)/api/auth/guest/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
