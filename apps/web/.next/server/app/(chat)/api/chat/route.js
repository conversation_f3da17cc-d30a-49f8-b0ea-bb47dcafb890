"use strict";(()=>{var e={};e.id=8820,e.ids=[8820],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{e.exports=require("node:buffer")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20896:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>x,routeModule:()=>p,serverHooks:()=>d,workAsyncStorage:()=>i,workUnitAsyncStorage:()=>u});var o=t(48106),s=t(48819),a=t(12050),n=t(34015);let p=new o.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/(chat)/api/chat/route",pathname:"/api/chat",filename:"route",bundlePath:"app/(chat)/api/chat/route"},resolvedPagePath:"/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(chat)/api/chat/route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:i,workUnitAsyncStorage:u,serverHooks:d}=p;function x(){return(0,a.patchFetch)({workAsyncStorage:i,workUnitAsyncStorage:u})}},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29727:e=>{e.exports=require("next/dist/compiled/next-server/app-page-experimental.runtime.prod.js")},41692:e=>{e.exports=require("node:tls")},46871:e=>{e.exports=require("next/dist/compiled/next-server/app-route-experimental.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},57975:e=>{e.exports=require("node:util")},58500:e=>{e.exports=require("node:timers/promises")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{e.exports=require("node:url")},77030:e=>{e.exports=require("node:net")},77598:e=>{e.exports=require("node:crypto")},78474:e=>{e.exports=require("node:events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[191,4235,2543,5208,8814,2911,8310,4294,4578],()=>t(20896));module.exports=o})();