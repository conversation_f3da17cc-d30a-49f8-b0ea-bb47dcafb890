import { Bot, session, GrammyError, HttpError } from 'grammy';
import { limit } from '@grammyjs/rate-limiter';
import { conversations } from '@grammyjs/conversations';
import { BotContext, SessionData } from './types/index.js';
import { env, isDevelopment } from './config/env.js';
import { logger } from './lib/logger.js';

// Middleware imports
import { authMiddleware, sessionCleanup } from './middleware/auth.js';
import {
  rateLimitMiddleware,
  tokenTrackingMiddleware,
} from './middleware/rateLimit.js';

// Handler imports
import { commandsComposer } from './handlers/commands.js';
import { callbacksComposer } from './handlers/callbacks.js';
import { messagesComposer } from './handlers/messages.js';

// Create bot instance
export const bot = new Bot<BotContext>(env.TELEGRAM_BOT_TOKEN);

// Error handling
bot.catch((err) => {
  const ctx = err.ctx;
  logger.error(
    `Error while handling update ${ctx.update.update_id}:`,
    err.error,
  );

  const e = err.error;
  if (e instanceof GrammyError) {
    logger.error('Error in request:', e.description);
  } else if (e instanceof HttpError) {
    logger.error('Could not contact Telegram:', e);
  } else {
    logger.error('Unknown error:', e);
  }
});

// Session configuration
function initial(): SessionData {
  return {
    step: undefined,
    userId: undefined,
    tier: undefined,
    lastActivity: Date.now(),
  };
}

// Apply middleware
bot.use(session({ initial }));
bot.use(conversations());

// Rate limiting (global limit to prevent abuse)
bot.use(
  limit({
    timeFrame: 60000, // 1 minute
    limit: 30, // 30 messages per minute per user
    storageAdapter: {
      read: async (key: string) => {
        // In production, use Redis or another persistent storage
        return undefined;
      },
      write: async (key: string, value: number) => {
        // In production, implement proper storage
      },
    },
    onLimitExceeded: async (ctx) => {
      await ctx.reply(
        '⚠️ **Rate Limit Exceeded**\n\n' +
          "You're sending messages too quickly. Please wait a moment before trying again.\n\n" +
          'Rate limits help ensure fair usage for all users.',
        { parse_mode: 'Markdown' },
      );
    },
    keyGenerator: (ctx) => ctx.from?.id.toString() || 'anonymous',
  }),
);

// Custom middleware
bot.use(sessionCleanup);
bot.use(authMiddleware);
bot.use(rateLimitMiddleware);
bot.use(tokenTrackingMiddleware);

// Register handlers
bot.use(commandsComposer);
bot.use(callbacksComposer);
bot.use(messagesComposer);

// Set bot commands
export async function setBotCommands() {
  try {
    await bot.api.setMyCommands([
      { command: 'start', description: '🚀 Welcome & setup guide' },
      { command: 'link', description: '🔗 Link your BonKai account' },
      { command: 'status', description: '📊 Check your account status' },
      { command: 'tier', description: '🏆 View tier information' },
      { command: 'wallet', description: '👛 Wallet connection status' },
      { command: 'usage', description: '📈 Token usage statistics' },
      { command: 'premium', description: '💎 Premium features (Diamond tier)' },
      { command: 'help', description: '🆘 Help & support' },
    ]);

    logger.info('Bot commands set successfully');
  } catch (error) {
    logger.error('Failed to set bot commands:', error);
  }
}

// Bot info logging
export async function logBotInfo() {
  try {
    const me = await bot.api.getMe();
    logger.info('Bot started successfully', {
      username: me.username,
      name: me.first_name,
      id: me.id,
      canJoinGroups: me.can_join_groups,
      canReadAllGroupMessages: me.can_read_all_group_messages,
      supportsInlineQueries: me.supports_inline_queries,
    });
  } catch (error) {
    logger.error('Failed to get bot info:', error);
  }
}

// Graceful shutdown
export async function stopBot() {
  try {
    await bot.stop();
    logger.info('Bot stopped gracefully');
  } catch (error) {
    logger.error('Error stopping bot:', error);
  }
}

// Handle uncaught errors
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', { promise, reason });
});

process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

// Graceful shutdown on signals
process.on('SIGINT', async () => {
  logger.info('Received SIGINT, shutting down gracefully...');
  await stopBot();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  logger.info('Received SIGTERM, shutting down gracefully...');
  await stopBot();
  process.exit(0);
});
