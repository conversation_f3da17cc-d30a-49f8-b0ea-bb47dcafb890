@layer rdg{@layer Defaults,
    FocusSink,
    CheckboxInput,
    CheckboxIcon,
    CheckboxLabel,
    Cell,
    HeaderCell,
    SummaryCell,
    EditCell,
    Row,
    HeaderRow,
    SummaryRow,
    GroupedRow,
    Root}@layer rdg.MeasuringCell{.mlln6zg7-0-0-beta-47{contain:strict;grid-row:1;visibility:hidden}}@layer rdg.Cell{.cj343x07-0-0-beta-47{position:relative;padding-block:0;padding-inline:8px;border-inline-end:1px solid var(--rdg-border-color);border-block-end:1px solid var(--rdg-border-color);grid-row-start:var(--rdg-grid-row-start);align-content:center;background-color:inherit;white-space:nowrap;overflow:clip;text-overflow:ellipsis;outline:none}.cj343x07-0-0-beta-47[aria-selected=true]{outline:2px solid var(--rdg-selection-color);outline-offset:-2px}.csofj7r7-0-0-beta-47{position:sticky;z-index:1}.csofj7r7-0-0-beta-47:nth-last-child(1 of .csofj7r7-0-0-beta-47){box-shadow:var(--rdg-cell-frozen-box-shadow)}}@layer rdg.Cell{}@layer rdg.CheckboxInput{.c1bn88vv7-0-0-beta-47{display:block;margin:auto;inline-size:20px;block-size:20px}.c1bn88vv7-0-0-beta-47:focus-visible{outline:2px solid var(--rdg-checkbox-focus-color);outline-offset:-3px}.c1bn88vv7-0-0-beta-47:enabled{cursor:pointer}}@layer rdg.GroupCellContent{.g1s9ylgp7-0-0-beta-47{outline:none}}@layer rdg.GroupCellCaret{.cz54e4y7-0-0-beta-47{margin-inline-start:4px;stroke:currentColor;stroke-width:1.5px;fill:transparent;vertical-align:middle}.cz54e4y7-0-0-beta-47>path{transition:d .1s}}@layer rdg.DragHandle{.c1w9bbhr7-0-0-beta-47{--rdg-drag-handle-size:8px;z-index:0;cursor:move;inline-size:var(--rdg-drag-handle-size);block-size:var(--rdg-drag-handle-size);background-color:var(--rdg-selection-color);place-self:end}.c1w9bbhr7-0-0-beta-47:hover{--rdg-drag-handle-size:16px;border:2px solid var(--rdg-selection-color);background-color:var(--rdg-background-color)}.c1creorc7-0-0-beta-47{z-index:1;position:sticky}}@layer rdg.DragHandle{}@layer rdg.EditCell{.cis5rrm7-0-0-beta-47{padding:0}}@layer rdg.SortableHeaderCell{.h44jtk67-0-0-beta-47{display:flex}}@layer rdg.SortableHeaderCellName{.hcgkhxz7-0-0-beta-47{flex-grow:1;overflow:clip;text-overflow:ellipsis}}@layer rdg.HeaderCell{.c6l2wv17-0-0-beta-47{cursor:pointer}.c1kqdw7y7-0-0-beta-47{touch-action:none}.r1y6ywlx7-0-0-beta-47{cursor:col-resize;position:absolute;inset-block-start:0;inset-inline-end:0;inset-block-end:0;inline-size:10px}}@layer rdg.HeaderCell{}@layer rdg.HeaderCell{}.c1bezg5o7-0-0-beta-47{opacity:.5}.c1vc96037-0-0-beta-47{background-color:var(--rdg-header-draggable-background-color)}@layer rdg.Row{.r1upfr807-0-0-beta-47{display:contents;background-color:var(--rdg-background-color)}.r1upfr807-0-0-beta-47:hover{background-color:var(--rdg-row-hover-background-color)}.r1upfr807-0-0-beta-47[aria-selected=true]{background-color:var(--rdg-row-selected-background-color)}.r1upfr807-0-0-beta-47[aria-selected=true]:hover{background-color:var(--rdg-row-selected-hover-background-color)}}@layer rdg.FocusSink{.r190mhd37-0-0-beta-47{outline:2px solid var(--rdg-selection-color);outline-offset:-2px}.r139qu9m7-0-0-beta-47:before{content:"";display:inline-block;block-size:100%;position:sticky;inset-inline-start:0;border-inline-start:2px solid var(--rdg-selection-color)}}@layer rdg.FocusSink{}@layer rdg.HeaderRow{.h10tskcx7-0-0-beta-47{display:contents;background-color:var(--rdg-header-background-color);font-weight:700}.h10tskcx7-0-0-beta-47>.cj343x07-0-0-beta-47{z-index:2;position:sticky}.h10tskcx7-0-0-beta-47>.csofj7r7-0-0-beta-47{z-index:3}}@layer rdg.Cell{.c6ra8a37-0-0-beta-47,.cq910m07-0-0-beta-47{background-color:#ccccff}.cq910m07-0-0-beta-47.c6ra8a37-0-0-beta-47{background-color:#9999ff}}@layer rdg.Cell{}@layer rdg.SortIcon{.a3ejtar7-0-0-beta-47{fill:currentColor}.a3ejtar7-0-0-beta-47>path{transition:d .1s}}@layer rdg.Defaults{.rnvodz57-0-0-beta-47 *,.rnvodz57-0-0-beta-47 :after,.rnvodz57-0-0-beta-47 :before{box-sizing:inherit}}@layer rdg.Root{.rnvodz57-0-0-beta-47{--rdg-color:#000;--rdg-border-color:#ddd;--rdg-summary-border-color:#aaa;--rdg-background-color:hsl(0deg 0% 100%);--rdg-header-background-color:hsl(0deg 0% 97.5%);--rdg-header-draggable-background-color:hsl(0deg 0% 90.5%);--rdg-row-hover-background-color:hsl(0deg 0% 96%);--rdg-row-selected-background-color:hsl(207deg 76% 92%);--rdg-row-selected-hover-background-color:hsl(207deg 76% 88%);--rdg-checkbox-focus-color:hsl(207deg 100% 69%);--rdg-selection-color:#66afe9;--rdg-font-size:14px;--rdg-cell-frozen-box-shadow:2px 0 5px -2px rgba(136,136,136,0.3);display:grid;color-scheme:var(--rdg-color-scheme,light dark);accent-color:light-dark(hsl(207deg 100% 29%),hsl(207deg 100% 79%));contain:content;content-visibility:auto;block-size:350px;border:1px solid var(--rdg-border-color);box-sizing:border-box;overflow:auto;background-color:var(--rdg-background-color);color:var(--rdg-color);font-size:var(--rdg-font-size)}.rnvodz57-0-0-beta-47:dir(rtl){--rdg-cell-frozen-box-shadow:-2px 0 5px -2px rgba(136,136,136,0.3)}.rnvodz57-0-0-beta-47:before{content:"";grid-column:1/-1;grid-row:1/-1}.rnvodz57-0-0-beta-47.rdg-dark{--rdg-color-scheme:dark;--rdg-color:#ddd;--rdg-border-color:#444;--rdg-summary-border-color:#555;--rdg-background-color:hsl(0deg 0% 13%);--rdg-header-background-color:hsl(0deg 0% 10.5%);--rdg-header-draggable-background-color:hsl(0deg 0% 17.5%);--rdg-row-hover-background-color:hsl(0deg 0% 9%);--rdg-row-selected-background-color:hsl(207deg 76% 42%);--rdg-row-selected-hover-background-color:hsl(207deg 76% 38%);--rdg-checkbox-focus-color:hsl(207deg 100% 89%)}.rnvodz57-0-0-beta-47.rdg-light{--rdg-color-scheme:light}@media (prefers-color-scheme:dark){.rnvodz57-0-0-beta-47:not(.rdg-light){--rdg-color:#ddd;--rdg-border-color:#444;--rdg-summary-border-color:#555;--rdg-background-color:hsl(0deg 0% 13%);--rdg-header-background-color:hsl(0deg 0% 10.5%);--rdg-header-draggable-background-color:hsl(0deg 0% 17.5%);--rdg-row-hover-background-color:hsl(0deg 0% 9%);--rdg-row-selected-background-color:hsl(207deg 76% 42%);--rdg-row-selected-hover-background-color:hsl(207deg 76% 38%);--rdg-checkbox-focus-color:hsl(207deg 100% 89%)}}.rnvodz57-0-0-beta-47>:nth-last-child(1 of .rdg-top-summary-row)>.cj343x07-0-0-beta-47{border-block-end:2px solid var(--rdg-summary-border-color)}.rnvodz57-0-0-beta-47>:nth-child(1 of .rdg-bottom-summary-row)>.cj343x07-0-0-beta-47{border-block-start:2px solid var(--rdg-summary-border-color)}.vlqv91k7-0-0-beta-47{user-select:none}.vlqv91k7-0-0-beta-47 .r1upfr807-0-0-beta-47{cursor:move}}@layer rdg.Root{}@layer rdg.FocusSink{.f1lsfrzw7-0-0-beta-47{grid-column:1/-1;pointer-events:none;z-index:1}.f1cte0lg7-0-0-beta-47{z-index:3}}@layer rdg.FocusSink{}@layer rdg.SummaryCell{.s8wc6fl7-0-0-beta-47{inset-block-start:var(--rdg-summary-row-top);inset-block-end:var(--rdg-summary-row-bottom)}}@layer rdg.SummaryRow{.skuhp557-0-0-beta-47>.cj343x07-0-0-beta-47{position:sticky}.tf8l5ub7-0-0-beta-47>.cj343x07-0-0-beta-47{z-index:2}.tf8l5ub7-0-0-beta-47>.csofj7r7-0-0-beta-47{z-index:3}}@layer rdg.SummaryRow{}@layer rdg.GroupedRow{.g1yxluv37-0-0-beta-47:not([aria-selected=true]){background-color:var(--rdg-header-background-color)}.g1yxluv37-0-0-beta-47>.cj343x07-0-0-beta-47:not(:last-child,.csofj7r7-0-0-beta-47),.g1yxluv37-0-0-beta-47>:nth-last-child(n+2 of .csofj7r7-0-0-beta-47){border-inline-end:none}}@layer rdg.TextEditor{.t7vyx3i7-0-0-beta-47{appearance:none;box-sizing:border-box;inline-size:100%;block-size:100%;padding-block:0;padding-inline:6px;border:2px solid #ccc;vertical-align:top;color:var(--rdg-color);background-color:var(--rdg-background-color);font-family:inherit;font-size:var(--rdg-font-size)}.t7vyx3i7-0-0-beta-47:focus{border-color:var(--rdg-selection-color);outline:none}.t7vyx3i7-0-0-beta-47::placeholder{color:#999;opacity:1}}