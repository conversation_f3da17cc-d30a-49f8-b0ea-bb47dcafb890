{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/node_modules_28cb8b9c._.js", "server/edge/chunks/[root-of-the-server]__512dc2bc._.js", "server/edge/chunks/apps_web_edge-wrapper_a35ad2ef.js", "server/edge/chunks/_a4252161._.js", "server/edge/chunks/[root-of-the-server]__e229addb._.js", "server/edge/chunks/apps_web_edge-wrapper_3030d334.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(\\/?index|\\/?index\\\\.json))?[\\/#\\?]?$", "originalSource": "/"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/chat(?:\\/([^\\/#\\?]+?))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/chat/:id"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/api/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/login(\\\\.json)?[\\/#\\?]?$", "originalSource": "/login"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/register(\\\\.json)?[\\/#\\?]?$", "originalSource": "/register"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|sitemap.xml|robots.txt).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|sitemap.xml|robots.txt).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "mdSJObfmL7JlC3BFKd3vCazZNR59iMsYc8eTyTS6n3g=", "__NEXT_PREVIEW_MODE_ID": "9a8ab419e2e8ee007f2f79f22fa7cbbf", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "26cc84602dcb0e4e24850447dbaf291d829daff09463f97215b8efa976f1a252", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "802a15e5be645d137a90cf115d3ded055fe83ec695d8204c186e98397ba1a9e2"}}}, "sortedMiddleware": ["/"], "functions": {}}