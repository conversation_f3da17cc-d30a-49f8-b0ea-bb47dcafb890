exports.id=6581,exports.ids=[6581],exports.modules={3171:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,30385,23)),Promise.resolve().then(r.t.bind(r,33737,23)),Promise.resolve().then(r.t.bind(r,86081,23)),Promise.resolve().then(r.t.bind(r,1904,23)),Promise.resolve().then(r.t.bind(r,35856,23)),Promise.resolve().then(r.t.bind(r,55492,23)),Promise.resolve().then(r.t.bind(r,89082,23)),Promise.resolve().then(r.t.bind(r,45812,23))},3851:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,69355,23)),Promise.resolve().then(r.t.bind(r,54439,23)),Promise.resolve().then(r.t.bind(r,67851,23)),Promise.resolve().then(r.t.bind(r,94730,23)),Promise.resolve().then(r.t.bind(r,19774,23)),Promise.resolve().then(r.t.bind(r,53170,23)),Promise.resolve().then(r.t.bind(r,20968,23)),Promise.resolve().then(r.t.bind(r,78298,23))},15984:(e,t,r)=>{"use strict";r.d(t,{$:()=>c,r:()=>d});var n=r(13486),o=r(60159),s=r(90691),a=r(76353),i=r(67499);let d=(0,a.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=o.forwardRef(({className:e,variant:t,size:r,asChild:o=!1,...a},c)=>{let u=o?s.DX:"button";return(0,n.jsx)(u,{className:(0,i.cn)(d({variant:t,size:r,className:e})),ref:c,...a})});c.displayName="Button"},18330:(e,t,r)=>{Promise.resolve().then(r.bind(r,84347)),Promise.resolve().then(r.bind(r,81604))},21971:()=>{},34356:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m,metadata:()=>u,viewport:()=>l});var n=r(38828),o=r(52434),s=r(10534),a=r.n(s),i=r(90369),d=r.n(i),c=r(66614);r(21971);let u={metadataBase:new URL("https://bonkai.vercel.app"),title:"BonKai - Web3 AI Ecosystem",description:"AI-powered Web3 platform with token-gated access on Solana."},l={maximumScale:1},h=`\
(function() {
  var html = document.documentElement;
  var meta = document.querySelector('meta[name="theme-color"]');
  if (!meta) {
    meta = document.createElement('meta');
    meta.setAttribute('name', 'theme-color');
    document.head.appendChild(meta);
  }
  function updateThemeColor() {
    var isDark = html.classList.contains('dark');
    meta.setAttribute('content', isDark ? 'hsl(240deg 10% 3.92%)' : 'hsl(0 0% 100%)');
  }
  var observer = new MutationObserver(updateThemeColor);
  observer.observe(html, { attributes: true, attributeFilter: ['class'] });
  updateThemeColor();
})();`;async function m({children:e}){return(0,n.jsxs)("html",{lang:"en",suppressHydrationWarning:!0,className:`${a().variable} ${d().variable}`,children:[(0,n.jsx)("head",{children:(0,n.jsx)("script",{dangerouslySetInnerHTML:{__html:h}})}),(0,n.jsx)("body",{className:"antialiased",children:(0,n.jsxs)(c.Providers,{children:[(0,n.jsx)(o.Toaster,{position:"top-center"}),e]})})]})}},52073:(e,t,r)=>{"use strict";r.d(t,{y:()=>o});var n=r(70358);let o=(0,n.createServerReference)("7ff75850b73f1dfd41bbc2ede99fb927e18bc81cf0",n.callServer,void 0,n.findSourceMapURL,"invalidateCacheAction")},55122:(e,t,r)=>{Promise.resolve().then(r.bind(r,66614)),Promise.resolve().then(r.bind(r,52434))},66614:(e,t,r)=>{"use strict";r.d(t,{Providers:()=>n});let n=(0,r(33952).registerClientReference)(function(){throw Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/providers.tsx","Providers")},67499:(e,t,r)=>{"use strict";r.d(t,{Dn:()=>u,GO:()=>i,JZ:()=>h,cn:()=>a,jZ:()=>l,lk:()=>c,qz:()=>d});var n=r(4627),o=r(55855),s=r(91139);function a(...e){return(0,o.QP)((0,n.$)(e))}let i=async e=>{let t=await fetch(e);if(!t.ok){let{code:e,cause:r}=await t.json();throw new s.P7(e,r)}return t.json()};async function d(e,t){try{let r=await fetch(e,t);if(!r.ok){let{code:e,cause:t}=await r.json();throw new s.P7(e,t)}return r}catch(e){if("undefined"!=typeof navigator&&!navigator.onLine)throw new s.P7("offline:chat");throw e}}function c(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,e=>{let t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)})}function u(e,t){return!e||t>e.length?new Date:e[t].createdAt}function l(e){return e.replace("<has_function_call>","")}function h(e){return e.parts.filter(e=>"text"===e.type).map(e=>e.text).join("")}},70196:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var n=r(41253);let o=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},80018:(e,t,r)=>{"use strict";r.d(t,{p:()=>a});var n=r(13486),o=r(60159),s=r(67499);let a=o.forwardRef(({className:e,type:t,...r},o)=>(0,n.jsx)("input",{type:t,className:(0,s.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:o,...r}));a.displayName="Input"},84347:(e,t,r)=>{"use strict";r.d(t,{Providers:()=>P});var n=r(13486),o=r(30352),s=r(6105),a=r(7608),i=r(48961),d=r(87940),c=r(95680);function u({children:e,...t}){return(0,n.jsx)(i.N,{...t,children:e})}var l=r(60159),h=r(22180),m=r(65133),f=r(74525),p=r(8120),b=r(87695),v=r(7668),g=r(8157);function x({children:e}){let t=process.env.NEXT_PUBLIC_SOLANA_NETWORK||"devnet",r=(0,l.useMemo)(()=>process.env.NEXT_PUBLIC_SOLANA_RPC_URL?process.env.NEXT_PUBLIC_SOLANA_RPC_URL:(0,g.Kw)(t),[t]),o=(0,l.useMemo)(()=>[new p.c,new b.d,new v.BackpackWalletAdapter],[]);return(0,n.jsx)(h.S,{endpoint:r,children:(0,n.jsx)(m.r,{wallets:o,autoConnect:!0,children:(0,n.jsx)(f.I,{children:e})})})}let y=new c.eH(process.env.NEXT_PUBLIC_CONVEX_URL);function P({children:e}){return(0,n.jsx)(u,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:(0,n.jsx)(d.q,{client:y,useAuth:o.d,children:(0,n.jsx)(w,{children:(0,n.jsx)(x,{children:e})})})})}function w({children:e}){let{resolvedTheme:t}=(0,i.D)();return(0,n.jsx)(s.lJ,{appearance:{baseTheme:"dark"===t?a.dark:void 0,variables:{colorPrimary:"oklch(0.6397 0.1720 36.4421)",colorText:"dark"===t?"oklch(0.9219 0 0)":"oklch(0.3211 0 0)",colorBackground:"dark"===t?"oklch(0.2598 0.0306 262.6666)":"oklch(0.9383 0.0042 236.4993)",colorInputBackground:"dark"===t?"oklch(0.3843 0.0301 269.7337)":"oklch(0.9700 0.0029 264.5420)",colorInputText:"dark"===t?"oklch(0.9219 0 0)":"oklch(0.3211 0 0)",borderRadius:"0.75rem",fontFamily:"Inter, sans-serif"},elements:{formButtonPrimary:"bg-primary text-primary-foreground hover:bg-primary/90",card:"bg-card text-card-foreground rounded-lg border border-border shadow-sm",formFieldInput:"bg-input border-input",footerActionLink:"text-primary hover:text-primary/80"}},children:e})}},91139:(e,t,r)=>{"use strict";r.d(t,{P7:()=>o});let n={database:"log",chat:"response",auth:"response",stream:"response",api:"response",history:"response",vote:"response",document:"response",suggestions:"response"};class o extends Error{constructor(e,t){super();let[r,n]=e.split(":");this.type=r,this.cause=t,this.surface=n,this.message=function(e){if(e.includes("database"))return"An error occurred while executing a database query.";switch(e){case"bad_request:api":return"The request couldn't be processed. Please check your input and try again.";case"unauthorized:auth":return"You need to sign in before continuing.";case"forbidden:auth":return"Your account does not have access to this feature.";case"rate_limit:chat":return"You have exceeded your maximum number of messages for the day. Please try again later.";case"not_found:chat":return"The requested chat was not found. Please check the chat ID and try again.";case"forbidden:chat":return"This chat belongs to another user. Please check the chat ID and try again.";case"unauthorized:chat":return"You need to sign in to view this chat. Please sign in and try again.";case"offline:chat":return"We're having trouble sending your message. Please check your internet connection and try again.";case"not_found:document":return"The requested document was not found. Please check the document ID and try again.";case"forbidden:document":return"This document belongs to another user. Please check the document ID and try again.";case"unauthorized:document":return"You need to sign in to view this document. Please sign in and try again.";case"bad_request:document":return"The request to create or update the document was invalid. Please check your input and try again.";default:return"Something went wrong. Please try again later."}}(e),this.statusCode=function(e){switch(e){case"bad_request":return 400;case"unauthorized":return 401;case"forbidden":return 403;case"not_found":return 404;case"rate_limit":return 429;case"offline":return 503;default:return 500}}(this.type)}toResponse(){let e=`${this.type}:${this.surface}`,t=n[this.surface],{message:r,cause:o,statusCode:s}=this;return"log"===t?(console.error({code:e,message:r,cause:o}),Response.json({code:"",message:"Something went wrong. Please try again later."},{status:s})):Response.json({code:e,message:r,cause:o},{status:s})}}}};