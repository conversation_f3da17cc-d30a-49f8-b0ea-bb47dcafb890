'use client';

import { useState } from 'react';
import {
  useCurrentUser,
  useTokenUsage,
  useRateLimit,
  useStakingPosition,
} from '@/lib/convex/hooks';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Wallet,
  BarChart3,
  Coins,
  History,
  Settings,
  TrendingUp,
} from 'lucide-react';

// Import all Web3 components
import {
  WalletStatus,
  WalletLinkingFlow,
  TierBadge,
  TierUpgradePrompt,
  TierBenefits,
  TokenUsageChart,
  RateLimitIndicator,
  UsageStats,
  StakingPositionCard,
  AuraPointsDisplay,
  StakingRewardsCalculator,
  TransactionStatus,
  TokenBalanceDisplay,
  TransactionHistory,
  type UserTier,
  type TransactionStatusType,
} from './index';

import { cn } from '@/lib/utils';

interface Web3DashboardProps {
  className?: string;
}

// Mock data for demonstration - in production, this would come from your API/Convex
const mockTokenUsageData = [
  {
    month: '2024-01',
    totalTokens: 15000,
    byModel: { 'gpt-4o': 8000, 'claude-3.5': 5000, 'gemini-2.0': 2000 },
    details: [],
  },
  {
    month: '2024-02',
    totalTokens: 25000,
    byModel: { 'gpt-4o': 12000, 'claude-3.5': 8000, 'gemini-2.0': 5000 },
    details: [],
  },
];

const mockRateLimitData = {
  tier: 'SILVER' as UserTier,
  limit: 200,
  used: 45,
  remaining: 155,
  allowed: true,
};

const mockUsageStatsData = {
  tier: 'SILVER' as UserTier,
  monthlyTokens: {
    current: 25000,
    limit: 200000,
    trend: 15.5,
  },
  hourlyRequests: {
    used: 45,
    limit: 200,
    remaining: 155,
  },
  totalUsage: {
    allTimeTokens: 450000,
    totalRequests: 1250,
    daysActive: 45,
  },
  achievements: [
    {
      id: '1',
      name: 'First Stake',
      description: 'Make your first BONK stake',
      unlockedAt: '2024-01-15',
    },
    {
      id: '2',
      name: 'Power User',
      description: 'Use 100K tokens in a month',
      unlockedAt: '2024-02-01',
    },
    {
      id: '3',
      name: 'Diamond Hands',
      description: 'Stake for 30 consecutive days',
    },
  ],
};

const mockStakingPosition = {
  amount: '15000',
  tier: 'SILVER' as UserTier,
  startTime: '2024-01-15T10:00:00Z',
  lastClaimTime: '2024-02-01T10:00:00Z',
  auraPoints: 2500,
  isActive: true,
  apy: 8,
};

const mockAuraPointsData = {
  currentPoints: 2500,
  totalEarned: 5000,
  lastClaimTime: '2024-02-01T10:00:00Z',
  nextClaimTime: '2024-02-02T10:00:00Z',
  pointsPerHour: 5,
  tier: 'SILVER' as UserTier,
  canClaim: true,
  pendingPoints: 120,
};

const mockTokenBalances = [
  {
    symbol: 'BONK',
    amount: '15000000000', // 15K BONK with 9 decimals
    decimals: 9,
    usdValue: 150.75,
    change24h: 5.2,
  },
  {
    symbol: 'SOL',
    amount: '2500000000', // 2.5 SOL with 9 decimals
    decimals: 9,
    usdValue: 437.5,
    change24h: -2.1,
  },
  {
    symbol: 'USDC',
    amount: '1000000000', // 1000 USDC with 6 decimals
    decimals: 6,
    usdValue: 1000.0,
    change24h: 0.1,
  },
];

const mockTransactions = [
  {
    id: '1',
    txHash: '5KJvHdNx9DFFxQ2i7H8FjxZQZ8DYMYvKd6TGHQqGhvxY',
    type: 'stake' as const,
    status: 'confirmed' as const,
    amount: '5000',
    token: 'BONK',
    timestamp: '2024-02-01T14:30:00Z',
    fee: '0.001',
    blockHeight: 245123456,
  },
  {
    id: '2',
    txHash: '7mBGtWvAJfvxP3K8QRtN2LzM5DYWp9RtKhGvFdE2xYZ3',
    type: 'claim' as const,
    status: 'confirmed' as const,
    amount: '120',
    token: 'AURA',
    timestamp: '2024-02-01T12:15:00Z',
    fee: '0.001',
    blockHeight: 245123400,
  },
  {
    id: '3',
    txHash: '9PcDtN2vKFgH8JyW4LxQ6mR7TpE3VhUfGsJ2KdF5XyZ8',
    type: 'receive' as const,
    status: 'pending' as const,
    amount: '1000',
    token: 'BONK',
    timestamp: '2024-02-01T16:45:00Z',
    fee: '0.001',
  },
];

export function Web3Dashboard({ className }: Web3DashboardProps) {
  const [showLinkingFlow, setShowLinkingFlow] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  // In production, you would use real Convex hooks
  // const user = useCurrentUser();
  // const tokenUsage = useTokenUsage(user?._id);
  // const rateLimit = useRateLimit(user?._id);
  // const stakingPosition = useStakingPosition(user?._id);

  const handleStakeAmount = (amount: number) => {
    console.log('Stake amount:', amount);
    // Implement staking logic
  };

  const handleClaimAuraPoints = async () => {
    console.log('Claiming aura points...');
    // Implement claim logic
  };

  const handleUpgrade = () => {
    console.log('Upgrade tier');
    // Implement upgrade logic
  };

  if (showLinkingFlow) {
    return (
      <div className={cn('max-w-md mx-auto', className)}>
        <WalletLinkingFlow
          onComplete={() => setShowLinkingFlow(false)}
          onCancel={() => setShowLinkingFlow(false)}
        />
      </div>
    );
  }

  return (
    <div className={cn('w-full space-y-6', className)}>
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Wallet className="size-6" />
            Web3 Dashboard
          </CardTitle>
          <CardDescription>
            Manage your wallet, staking, and BonKai tier benefits
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <WalletStatus compact />
            <div className="flex items-center justify-between">
              <TierBadge tier="SILVER" />
              <Button onClick={() => setShowLinkingFlow(true)} size="sm">
                Link Wallet
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="usage">Usage</TabsTrigger>
          <TabsTrigger value="staking">Staking</TabsTrigger>
          <TabsTrigger value="wallet">Wallet</TabsTrigger>
          <TabsTrigger value="history">History</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6 mt-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <TierUpgradePrompt
              currentTier="SILVER"
              currentStakeAmount={15000}
              onUpgrade={handleUpgrade}
            />
            <RateLimitIndicator
              data={mockRateLimitData}
              onUpgrade={handleUpgrade}
            />
          </div>

          <UsageStats
            data={mockUsageStatsData}
            onViewDetails={() => setActiveTab('usage')}
            onUpgrade={handleUpgrade}
          />
        </TabsContent>

        <TabsContent value="usage" className="space-y-6 mt-6">
          <TokenUsageChart data={mockTokenUsageData} userTier="SILVER" />
        </TabsContent>

        <TabsContent value="staking" className="space-y-6 mt-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <StakingPositionCard
              position={mockStakingPosition}
              onClaim={handleClaimAuraPoints}
              onStake={() => console.log('Stake more')}
              onUnstake={() => console.log('Unstake')}
            />
            <AuraPointsDisplay
              data={mockAuraPointsData}
              onClaim={handleClaimAuraPoints}
              showHistory
            />
          </div>

          <StakingRewardsCalculator
            currentStake={15000}
            currentTier="SILVER"
            onStakeAmount={handleStakeAmount}
          />
        </TabsContent>

        <TabsContent value="wallet" className="space-y-6 mt-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <WalletStatus showBalance />
            <TokenBalanceDisplay
              balances={mockTokenBalances}
              totalUsdValue={1588.25}
              onRefresh={() => console.log('Refresh balances')}
            />
          </div>

          <TierBenefits userTier="SILVER" showAllTiers />
        </TabsContent>

        <TabsContent value="history" className="space-y-6 mt-6">
          <TransactionHistory
            transactions={mockTransactions}
            onRefresh={() => console.log('Refresh history')}
            hasMore
            onLoadMore={() => console.log('Load more')}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}
