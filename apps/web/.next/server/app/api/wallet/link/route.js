(()=>{var e={};e.id=3860,e.ids=[3860],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5898:(e,t,s)=>{"use strict";s.d(t,{$:()=>z});var r,i,n,a,o,l,d,u,c,p,h,S,k,f,y,w,g,m,v,b=s(61261),x=s(38202),K=s(5877);s(24981);var q=s(95035);s(21404),s(68272);var j=s(98950),R=s(44468),V=s(45663),E=class{constructor(){(0,V.VK)(this,n),(0,V.VK)(this,r,"clerk_telemetry_throttler"),(0,V.VK)(this,i,864e5)}isEventThrottled(e){if(!(0,V.S7)(this,n,l))return!1;let t=Date.now(),s=(0,V.jq)(this,n,a).call(this,e),d=(0,V.S7)(this,n,o)?.[s];if(!d){let e={...(0,V.S7)(this,n,o),[s]:t};localStorage.setItem((0,V.S7)(this,r),JSON.stringify(e))}if(d&&t-d>(0,V.S7)(this,i)){let e=(0,V.S7)(this,n,o);delete e[s],localStorage.setItem((0,V.S7)(this,r),JSON.stringify(e))}return!!d}};r=new WeakMap,i=new WeakMap,n=new WeakSet,a=function(e){let{sk:t,pk:s,payload:r,...i}=e,n={...r,...i};return JSON.stringify(Object.keys({...r,...i}).sort().map(e=>n[e]))},o=function(){let e=localStorage.getItem((0,V.S7)(this,r));return e?JSON.parse(e):{}},l=function(){if("undefined"==typeof window)return!1;let e=window.localStorage;if(!e)return!1;try{let t="test";return e.setItem(t,t),e.removeItem(t),!0}catch(t){return t instanceof DOMException&&("QuotaExceededError"===t.name||"NS_ERROR_DOM_QUOTA_REACHED"===t.name)&&e.length>0&&e.removeItem((0,V.S7)(this,r)),!1}};var M={samplingRate:1,maxBufferSize:5,endpoint:"https://clerk-telemetry.com"},O=class{constructor(e){(0,V.VK)(this,S),(0,V.VK)(this,d),(0,V.VK)(this,u),(0,V.VK)(this,c,{}),(0,V.VK)(this,p,[]),(0,V.VK)(this,h),(0,V.OV)(this,d,{maxBufferSize:e.maxBufferSize??M.maxBufferSize,samplingRate:e.samplingRate??M.samplingRate,disabled:e.disabled??!1,debug:e.debug??!1,endpoint:M.endpoint}),e.clerkVersion||"undefined"!=typeof window?(0,V.S7)(this,c).clerkVersion=e.clerkVersion??"":(0,V.S7)(this,c).clerkVersion="",(0,V.S7)(this,c).sdk=e.sdk,(0,V.S7)(this,c).sdkVersion=e.sdkVersion,(0,V.S7)(this,c).publishableKey=e.publishableKey??"";let t=(0,R.q5)(e.publishableKey);t&&((0,V.S7)(this,c).instanceType=t.instanceType),e.secretKey&&((0,V.S7)(this,c).secretKey=e.secretKey.substring(0,16)),(0,V.OV)(this,u,new E)}get isEnabled(){return!("development"!==(0,V.S7)(this,c).instanceType||(0,V.S7)(this,d).disabled||"undefined"!=typeof process&&process.env&&(0,j.zz)(process.env.CLERK_TELEMETRY_DISABLED)||"undefined"!=typeof window&&window?.navigator?.webdriver)}get isDebug(){return(0,V.S7)(this,d).debug||"undefined"!=typeof process&&process.env&&(0,j.zz)(process.env.CLERK_TELEMETRY_DEBUG)}record(e){let t=(0,V.jq)(this,S,v).call(this,e.event,e.payload);(0,V.jq)(this,S,g).call(this,t.event,t),(0,V.jq)(this,S,k).call(this,t,e.eventSamplingRate)&&((0,V.S7)(this,p).push(t),(0,V.jq)(this,S,y).call(this))}};d=new WeakMap,u=new WeakMap,c=new WeakMap,p=new WeakMap,h=new WeakMap,S=new WeakSet,k=function(e,t){return this.isEnabled&&!this.isDebug&&(0,V.jq)(this,S,f).call(this,e,t)},f=function(e,t){let s=Math.random();return!!(s<=(0,V.S7)(this,d).samplingRate&&(void 0===t||s<=t))&&!(0,V.S7)(this,u).isEventThrottled(e)},y=function(){if("undefined"==typeof window)return void(0,V.jq)(this,S,w).call(this);if((0,V.S7)(this,p).length>=(0,V.S7)(this,d).maxBufferSize){(0,V.S7)(this,h)&&("undefined"!=typeof cancelIdleCallback?cancelIdleCallback:clearTimeout)((0,V.S7)(this,h)),(0,V.jq)(this,S,w).call(this);return}(0,V.S7)(this,h)||("requestIdleCallback"in window?(0,V.OV)(this,h,requestIdleCallback(()=>{(0,V.jq)(this,S,w).call(this)})):(0,V.OV)(this,h,setTimeout(()=>{(0,V.jq)(this,S,w).call(this)},0)))},w=function(){fetch(new URL("/v1/event",(0,V.S7)(this,d).endpoint),{method:"POST",body:JSON.stringify({events:(0,V.S7)(this,p)}),headers:{"Content-Type":"application/json"}}).catch(()=>void 0).then(()=>{(0,V.OV)(this,p,[])}).catch(()=>void 0)},g=function(e,t){this.isDebug&&(void 0!==console.groupCollapsed?(console.groupCollapsed("[clerk/telemetry]",e),console.log(t),console.groupEnd()):console.log("[clerk/telemetry]",e,t))},m=function(){let e={name:(0,V.S7)(this,c).sdk,version:(0,V.S7)(this,c).sdkVersion};return"undefined"!=typeof window&&window.Clerk&&(e={...e,...window.Clerk.constructor.sdkMetadata}),e},v=function(e,t){let s=(0,V.jq)(this,S,m).call(this);return{event:e,cv:(0,V.S7)(this,c).clerkVersion??"",it:(0,V.S7)(this,c).instanceType??"",sdk:s.name,sdkv:s.version,...(0,V.S7)(this,c).publishableKey?{pk:(0,V.S7)(this,c).publishableKey}:{},...(0,V.S7)(this,c).secretKey?{sk:(0,V.S7)(this,c).secretKey}:{},payload:t}};(0,q.C)(K.nr);var T=s(99657);let C={secretKey:T.rB,publishableKey:T.At,apiUrl:T.H$,apiVersion:T.mG,userAgent:"@clerk/nextjs@6.23.3",proxyUrl:T.Rg,domain:T.V2,isSatellite:T.fS,sdkMetadata:T.tm,telemetry:{disabled:T.nN,debug:T.Mh}},D=e=>(function(e){let t={...e},s=(0,K.y3)(t),r=(0,K.Bs)({options:t,apiClient:s}),i=new O({...e.telemetry,publishableKey:t.publishableKey,secretKey:t.secretKey,samplingRate:.1,...t.sdkMetadata?{sdk:t.sdkMetadata.name,sdkVersion:t.sdkMetadata.version}:{}});return{...s,...r,telemetry:i}})({...C,...e});var A=s(35621);let _=new(require("node:async_hooks")).AsyncLocalStorage;var I=s(60751);let z=async()=>{var e,t;let s;try{let e=await (0,x.TG)(),t=(0,A._b)(e,b.AA.Headers.ClerkRequestData);s=(0,I.Kk)(t)}catch(e){if(e&&(0,x.Sz)(e))throw e}let r=null!=(t=null==(e=_.getStore())?void 0:e.get("requestData"))?t:s;return(null==r?void 0:r.secretKey)||(null==r?void 0:r.publishableKey)?D(r):D({})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29727:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page-experimental.runtime.prod.js")},33595:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>k,routeModule:()=>c,serverHooks:()=>S,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>h});var r={};s.r(r),s.d(r,{POST:()=>u});var i=s(48106),n=s(48819),a=s(12050),o=s(30693),l=s(5898),d=s(4235);async function u(e){try{let{userId:t}=await (0,o.j)();if(!t)return d.NextResponse.json({error:"Unauthorized"},{status:401});let{walletAddress:s}=await e.json();if(!s)return d.NextResponse.json({error:"Wallet address is required"},{status:400});return await l.$.users.updateUser(t,{publicMetadata:{walletAddress:s,walletLinkedAt:new Date().toISOString()}}),d.NextResponse.json({success:!0,walletAddress:s})}catch(e){return console.error("Error linking wallet:",e),d.NextResponse.json({error:"Failed to link wallet"},{status:500})}}let c=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/wallet/link/route",pathname:"/api/wallet/link",filename:"route",bundlePath:"app/api/wallet/link/route"},resolvedPagePath:"/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/api/wallet/link/route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:p,workUnitAsyncStorage:h,serverHooks:S}=c;function k(){return(0,a.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:h})}},46871:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route-experimental.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},80408:()=>{},87032:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[191,4235,2543,5726],()=>s(33595));module.exports=r})();