"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5082],{18654:(e,r,t)=>{t.d(r,{o:()=>c});var o=t(44995),n=t(41987),l=t(51874),i=t(34109),s=t(99749);let d={success:(0,o.jsx)(i.PW,{}),error:(0,o.jsx)(i.id,{})};function c(e){return l.o.custom(r=>(0,o.jsx)(u,{id:r,type:e.type,description:e.description}))}function u(e){let{id:r,type:t,description:l}=e,i=(0,n.useRef)(null),[c,u]=(0,n.useState)(!1);return(0,n.useEffect)(()=>{let e=i.current;if(!e)return;let r=()=>{let r=Number.parseFloat(getComputedStyle(e).lineHeight);u(Math.round(e.scrollHeight/r)>1)};r();let t=new ResizeObserver(r);return t.observe(e),()=>t.disconnect()},[l]),(0,o.jsx)("div",{className:"flex w-full toast-mobile:w-[356px] justify-center",children:(0,o.jsxs)("div",{"data-testid":"toast",className:(0,s.cn)("bg-zinc-100 p-3 rounded-lg w-full toast-mobile:w-fit flex flex-row gap-3",c?"items-start":"items-center"),children:[(0,o.jsx)("div",{"data-type":t,className:(0,s.cn)("data-[type=error]:text-red-600 data-[type=success]:text-green-600",{"pt-1":c}),children:d[t]}),(0,o.jsx)("div",{ref:i,className:"text-zinc-950 text-sm",children:l})]},r)})}},22710:(e,r,t)=>{t.d(r,{$:()=>c,r:()=>d});var o=t(44995),n=t(41987),l=t(79649),i=t(615),s=t(99749);let d=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=n.forwardRef((e,r)=>{let{className:t,variant:n,size:i,asChild:c=!1,...u}=e,C=c?l.DX:"button";return(0,o.jsx)(C,{className:(0,s.cn)(d({variant:n,size:i,className:t})),ref:r,...u})});c.displayName="Button"},25118:(e,r,t)=>{t.d(r,{p:()=>i});var o=t(44995),n=t(41987),l=t(99749);let i=n.forwardRef((e,r)=>{let{className:t,type:n,...i}=e;return(0,o.jsx)("input",{type:n,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),ref:r,...i})});i.displayName="Input"},34109:(e,r,t)=>{t.d(r,{$U:()=>w,Au:()=>C,BZ:()=>Z,D3:()=>y,Dk:()=>N,EA:()=>M,Fj:()=>H,Kp:()=>c,P7:()=>z,PW:()=>k,PY:()=>n,Qe:()=>_,Td:()=>j,Uf:()=>x,XA:()=>m,b1:()=>h,c1:()=>g,ej:()=>V,fC:()=>R,gT:()=>D,hD:()=>f,hz:()=>i,id:()=>q,j0:()=>p,jN:()=>a,li:()=>b,oS:()=>l,rZ:()=>v,uO:()=>P,uc:()=>d,ud:()=>B,vL:()=>s,w0:()=>L,wF:()=>u,xf:()=>W});var o=t(44995);let n=e=>{let{size:r=17}=e;return(0,o.jsx)("svg",{height:r,strokeLinejoin:"round",viewBox:"0 0 16 16",width:r,style:{color:"currentcolor"},children:(0,o.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M8 1L16 15H0L8 1Z",fill:"currentColor"})})},l=e=>{let{size:r=16}=e;return(0,o.jsx)("svg",{height:r,strokeLinejoin:"round",viewBox:"0 0 16 16",width:r,style:{color:"currentcolor"},children:(0,o.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M14.5 13.5V6.5V5.41421C14.5 5.149 14.3946 4.89464 14.2071 4.70711L9.79289 0.292893C9.60536 0.105357 9.351 0 9.08579 0H8H3H1.5V1.5V13.5C1.5 14.8807 2.61929 16 4 16H12C13.3807 16 14.5 14.8807 14.5 13.5ZM13 13.5V6.5H9.5H8V5V1.5H3V13.5C3 14.0523 3.44772 14.5 4 14.5H12C12.5523 14.5 13 14.0523 13 13.5ZM9.5 5V2.12132L12.3787 5H9.5ZM5.13 5.00062H4.505V6.25062H5.13H6H6.625V5.00062H6H5.13ZM4.505 8H5.13H11H11.625V9.25H11H5.13H4.505V8ZM5.13 11H4.505V12.25H5.13H11H11.625V11H11H5.13Z",fill:"currentColor"})})},i=e=>{let{size:r=16}=e;return(0,o.jsxs)("svg",{height:r,strokeLinejoin:"round",viewBox:"0 0 16 16",width:r,style:{color:"currentcolor"},children:[(0,o.jsxs)("g",{clipPath:"url(#clip0_2393_1490)",children:[(0,o.jsx)("path",{d:"M8 0V4",stroke:"currentColor",strokeWidth:"1.5"}),(0,o.jsx)("path",{opacity:"0.5",d:"M8 16V12",stroke:"currentColor",strokeWidth:"1.5"}),(0,o.jsx)("path",{opacity:"0.9",d:"M3.29773 1.52783L5.64887 4.7639",stroke:"currentColor",strokeWidth:"1.5"}),(0,o.jsx)("path",{opacity:"0.1",d:"M12.7023 1.52783L10.3511 4.7639",stroke:"currentColor",strokeWidth:"1.5"}),(0,o.jsx)("path",{opacity:"0.4",d:"M12.7023 14.472L10.3511 11.236",stroke:"currentColor",strokeWidth:"1.5"}),(0,o.jsx)("path",{opacity:"0.6",d:"M3.29773 14.472L5.64887 11.236",stroke:"currentColor",strokeWidth:"1.5"}),(0,o.jsx)("path",{opacity:"0.2",d:"M15.6085 5.52783L11.8043 6.7639",stroke:"currentColor",strokeWidth:"1.5"}),(0,o.jsx)("path",{opacity:"0.7",d:"M0.391602 10.472L4.19583 9.23598",stroke:"currentColor",strokeWidth:"1.5"}),(0,o.jsx)("path",{opacity:"0.3",d:"M15.6085 10.4722L11.8043 9.2361",stroke:"currentColor",strokeWidth:"1.5"}),(0,o.jsx)("path",{opacity:"0.8",d:"M0.391602 5.52783L4.19583 6.7639",stroke:"currentColor",strokeWidth:"1.5"})]}),(0,o.jsx)("defs",{children:(0,o.jsx)("clipPath",{id:"clip0_2393_1490",children:(0,o.jsx)("rect",{width:"16",height:"16",fill:"white"})})})]})},s=e=>{let{size:r=16}=e;return(0,o.jsx)("svg",{height:r,strokeLinejoin:"round",viewBox:"0 0 16 16",width:r,style:{color:"currentcolor"},children:(0,o.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M11.75 0.189331L12.2803 0.719661L15.2803 3.71966L15.8107 4.24999L15.2803 4.78032L5.15901 14.9016C4.45575 15.6049 3.50192 16 2.50736 16H0.75H0V15.25V13.4926C0 12.4981 0.395088 11.5442 1.09835 10.841L11.2197 0.719661L11.75 0.189331ZM11.75 2.31065L9.81066 4.24999L11.75 6.18933L13.6893 4.24999L11.75 2.31065ZM2.15901 11.9016L8.75 5.31065L10.6893 7.24999L4.09835 13.841C3.67639 14.2629 3.1041 14.5 2.50736 14.5H1.5V13.4926C1.5 12.8959 1.73705 12.3236 2.15901 11.9016ZM9 16H16V14.5H9V16Z",fill:"currentColor"})})},d=e=>{let{size:r=16}=e;return(0,o.jsx)("svg",{height:r,strokeLinejoin:"round",viewBox:"0 0 16 16",width:r,style:{color:"currentcolor"},children:(0,o.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6.75 2.75C6.75 2.05964 7.30964 1.5 8 1.5C8.69036 1.5 9.25 2.05964 9.25 2.75V3H6.75V2.75ZM5.25 3V2.75C5.25 1.23122 6.48122 0 8 0C9.51878 0 10.75 1.23122 10.75 2.75V3H12.9201H14.25H15V4.5H14.25H13.8846L13.1776 13.6917C13.0774 14.9942 11.9913 16 10.6849 16H5.31508C4.00874 16 2.92263 14.9942 2.82244 13.6917L2.11538 4.5H1.75H1V3H1.75H3.07988H5.25ZM4.31802 13.5767L3.61982 4.5H12.3802L11.682 13.5767C11.6419 14.0977 11.2075 14.5 10.6849 14.5H5.31508C4.79254 14.5 4.3581 14.0977 4.31802 13.5767Z",fill:"currentColor"})})},c=e=>{let{size:r=16}=e;return(0,o.jsx)("svg",{height:r,strokeLinejoin:"round",viewBox:"0 0 16 16",width:r,style:{color:"currentcolor"},children:(0,o.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M8.70711 1.39644C8.31659 1.00592 7.68342 1.00592 7.2929 1.39644L2.21968 6.46966L1.68935 6.99999L2.75001 8.06065L3.28034 7.53032L7.25001 3.56065V14.25V15H8.75001V14.25V3.56065L12.7197 7.53032L13.25 8.06065L14.3107 6.99999L13.7803 6.46966L8.70711 1.39644Z",fill:"currentColor"})})},u=e=>{let{size:r=16}=e;return(0,o.jsx)("svg",{height:r,viewBox:"0 0 16 16",width:r,style:{color:"currentcolor"},children:(0,o.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3 3H13V13H3V3Z",fill:"currentColor"})})},C=e=>{let{size:r=16}=e;return(0,o.jsx)("svg",{height:r,strokeLinejoin:"round",viewBox:"0 0 16 16",width:r,style:{color:"currentcolor"},className:"-rotate-45",children:(0,o.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10.8591 1.70735C10.3257 1.70735 9.81417 1.91925 9.437 2.29643L3.19455 8.53886C2.56246 9.17095 2.20735 10.0282 2.20735 10.9222C2.20735 11.8161 2.56246 12.6734 3.19455 13.3055C3.82665 13.9376 4.68395 14.2927 5.57786 14.2927C6.47178 14.2927 7.32908 13.9376 7.96117 13.3055L14.2036 7.06304L14.7038 6.56287L15.7041 7.56321L15.204 8.06337L8.96151 14.3058C8.06411 15.2032 6.84698 15.7074 5.57786 15.7074C4.30875 15.7074 3.09162 15.2032 2.19422 14.3058C1.29682 13.4084 0.792664 12.1913 0.792664 10.9222C0.792664 9.65305 1.29682 8.43592 2.19422 7.53852L8.43666 1.29609C9.07914 0.653606 9.95054 0.292664 10.8591 0.292664C11.7678 0.292664 12.6392 0.653606 13.2816 1.29609C13.9241 1.93857 14.2851 2.80997 14.2851 3.71857C14.2851 4.62718 13.9241 5.49858 13.2816 6.14106L13.2814 6.14133L7.0324 12.3835C7.03231 12.3836 7.03222 12.3837 7.03213 12.3838C6.64459 12.7712 6.11905 12.9888 5.57107 12.9888C5.02297 12.9888 4.49731 12.7711 4.10974 12.3835C3.72217 11.9959 3.50444 11.4703 3.50444 10.9222C3.50444 10.3741 3.72217 9.8484 4.10974 9.46084L4.11004 9.46054L9.877 3.70039L10.3775 3.20051L11.3772 4.20144L10.8767 4.70131L5.11008 10.4612C5.11005 10.4612 5.11003 10.4612 5.11 10.4613C4.98779 10.5835 4.91913 10.7493 4.91913 10.9222C4.91913 11.0951 4.98782 11.2609 5.11008 11.3832C5.23234 11.5054 5.39817 11.5741 5.57107 11.5741C5.74398 11.5741 5.9098 11.5054 6.03206 11.3832L6.03233 11.3829L12.2813 5.14072C12.2814 5.14063 12.2815 5.14054 12.2816 5.14045C12.6586 4.7633 12.8704 4.25185 12.8704 3.71857C12.8704 3.18516 12.6585 2.6736 12.2813 2.29643C11.9041 1.91925 11.3926 1.70735 10.8591 1.70735Z",fill:"currentColor"})})},a=e=>{let{size:r=16}=e;return(0,o.jsx)("svg",{height:r,strokeLinejoin:"round",viewBox:"0 0 16 16",width:r,style:{color:"currentcolor"},children:(0,o.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4 8C4 8.82843 3.32843 9.5 2.5 9.5C1.67157 9.5 1 8.82843 1 8C1 7.17157 1.67157 6.5 2.5 6.5C3.32843 6.5 4 7.17157 4 8ZM9.5 8C9.5 8.82843 8.82843 9.5 8 9.5C7.17157 9.5 6.5 8.82843 6.5 8C6.5 7.17157 7.17157 6.5 8 6.5C8.82843 6.5 9.5 7.17157 9.5 8ZM13.5 9.5C14.3284 9.5 15 8.82843 15 8C15 7.17157 14.3284 6.5 13.5 6.5C12.6716 6.5 12 7.17157 12 8C12 8.82843 12.6716 9.5 13.5 9.5Z",fill:"currentColor"})})},h=e=>{let{size:r=16}=e;return(0,o.jsx)("svg",{height:r,strokeLinejoin:"round",viewBox:"0 0 16 16",width:r,style:{color:"currentcolor"},children:(0,o.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M2.8914 10.4028L2.98327 10.6318C3.22909 11.2445 3.5 12.1045 3.5 13C3.5 13.3588 3.4564 13.7131 3.38773 14.0495C3.69637 13.9446 4.01409 13.8159 4.32918 13.6584C4.87888 13.3835 5.33961 13.0611 5.70994 12.7521L6.22471 12.3226L6.88809 12.4196C7.24851 12.4724 7.61994 12.5 8 12.5C11.7843 12.5 14.5 9.85569 14.5 7C14.5 4.14431 11.7843 1.5 8 1.5C4.21574 1.5 1.5 4.14431 1.5 7C1.5 8.18175 1.94229 9.29322 2.73103 10.2153L2.8914 10.4028ZM2.8135 15.7653C1.76096 16 1 16 1 16C1 16 1.43322 15.3097 1.72937 14.4367C1.88317 13.9834 2 13.4808 2 13C2 12.3826 1.80733 11.7292 1.59114 11.1903C0.591845 10.0221 0 8.57152 0 7C0 3.13401 3.58172 0 8 0C12.4183 0 16 3.13401 16 7C16 10.866 12.4183 14 8 14C7.54721 14 7.10321 13.9671 6.67094 13.9038C6.22579 14.2753 5.66881 14.6656 5 15C4.23366 15.3832 3.46733 15.6195 2.8135 15.7653Z",fill:"currentColor"})})},L=e=>{let{size:r=16}=e;return(0,o.jsx)("svg",{height:r,strokeLinejoin:"round",viewBox:"0 0 16 16",width:r,style:{color:"currentcolor"},children:(0,o.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12.4697 13.5303L13 14.0607L14.0607 13L13.5303 12.4697L9.06065 7.99999L13.5303 3.53032L14.0607 2.99999L13 1.93933L12.4697 2.46966L7.99999 6.93933L3.53032 2.46966L2.99999 1.93933L1.93933 2.99999L2.46966 3.53032L6.93933 7.99999L2.46966 12.4697L1.93933 13L2.99999 14.0607L3.53032 13.5303L7.99999 9.06065L12.4697 13.5303Z",fill:"currentColor"})})},H=e=>{let{size:r=16}=e;return(0,o.jsx)("svg",{height:r,strokeLinejoin:"round",viewBox:"0 0 16 16",width:r,style:{color:"currentcolor"},children:(0,o.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M9.96966 11.0303L10.5 11.5607L11.5607 10.5L11.0303 9.96966L9.06065 7.99999L11.0303 6.03032L11.5607 5.49999L10.5 4.43933L9.96966 4.96966L7.99999 6.93933L6.03032 4.96966L5.49999 4.43933L4.43933 5.49999L4.96966 6.03032L6.93933 7.99999L4.96966 9.96966L4.43933 10.5L5.49999 11.5607L6.03032 11.0303L7.99999 9.06065L9.96966 11.0303Z",fill:"currentColor"})})},V=e=>{let{size:r=16}=e;return(0,o.jsx)("svg",{height:r,strokeLinejoin:"round",viewBox:"0 0 16 16",width:r,style:{color:"currentcolor"},children:(0,o.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M13.5 8C13.5 4.96643 11.0257 2.5 7.96452 2.5C5.42843 2.5 3.29365 4.19393 2.63724 6.5H5.25H6V8H5.25H0.75C0.335787 8 0 7.66421 0 7.25V2.75V2H1.5V2.75V5.23347C2.57851 2.74164 5.06835 1 7.96452 1C11.8461 1 15 4.13001 15 8C15 11.87 11.8461 15 7.96452 15C5.62368 15 3.54872 13.8617 2.27046 12.1122L1.828 11.5066L3.03915 10.6217L3.48161 11.2273C4.48831 12.6051 6.12055 13.5 7.96452 13.5C11.0257 13.5 13.5 11.0336 13.5 8Z",fill:"currentColor"})})},x=e=>{let{size:r=16}=e;return(0,o.jsx)("svg",{height:r,strokeLinejoin:"round",viewBox:"0 0 16 16",width:r,style:{color:"currentcolor"},children:(0,o.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M2.5 8C2.5 4.96643 4.97431 2.5 8.03548 2.5C10.5716 2.5 12.7064 4.19393 13.3628 6.5H10.75H10V8H10.75H15.25C15.6642 8 16 7.66421 16 7.25V2.75V2H14.5V2.75V5.23347C13.4215 2.74164 10.9316 1 8.03548 1C4.1539 1 1 4.13001 1 8C1 11.87 4.1539 15 8.03548 15C10.3763 15 12.4513 13.8617 13.7295 12.1122L14.172 11.5066L12.9609 10.6217L12.5184 11.2273C11.5117 12.6051 9.87945 13.5 8.03548 13.5C4.97431 13.5 2.5 11.0336 2.5 8Z",fill:"currentColor"})})},v=e=>{let{size:r=16}=e;return(0,o.jsx)("svg",{height:r,strokeLinejoin:"round",viewBox:"0 0 16 16",width:r,style:{color:"currentcolor"},children:(0,o.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M8.75 0.189331L9.28033 0.719661L15.2803 6.71966L15.8107 7.24999L15.2803 7.78032L13.7374 9.32322C13.1911 9.8696 12.3733 9.97916 11.718 9.65188L9.54863 13.5568C8.71088 15.0648 7.12143 16 5.39639 16H0.75H0V15.25V10.6036C0 8.87856 0.935237 7.28911 2.4432 6.45136L6.34811 4.28196C6.02084 3.62674 6.13039 2.80894 6.67678 2.26255L8.21967 0.719661L8.75 0.189331ZM7.3697 5.43035L10.5696 8.63029L8.2374 12.8283C7.6642 13.8601 6.57668 14.5 5.39639 14.5H2.56066L5.53033 11.5303L4.46967 10.4697L1.5 13.4393V10.6036C1.5 9.42331 2.1399 8.33579 3.17166 7.76259L7.3697 5.43035ZM12.6768 8.26256C12.5791 8.36019 12.4209 8.36019 12.3232 8.26255L12.0303 7.96966L8.03033 3.96966L7.73744 3.67677C7.63981 3.57914 7.63981 3.42085 7.73744 3.32321L8.75 2.31065L13.6893 7.24999L12.6768 8.26256Z",fill:"currentColor"})})},f=e=>{let{size:r=16}=e;return(0,o.jsx)("svg",{height:r,strokeLinejoin:"round",viewBox:"0 0 16 16",width:r,style:{color:"currentcolor"},children:(0,o.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M1.75 12H1V10.5H1.75H5.25H6V12H5.25H1.75ZM1.75 7.75H1V6.25H1.75H4.25H5V7.75H4.25H1.75ZM1.75 3.5H1V2H1.75H7.25H8V3.5H7.25H1.75ZM12.5303 14.7803C12.2374 15.0732 11.7626 15.0732 11.4697 14.7803L9.21967 12.5303L8.68934 12L9.75 10.9393L10.2803 11.4697L11.25 12.4393V2.75V2H12.75V2.75V12.4393L13.7197 11.4697L14.25 10.9393L15.3107 12L14.7803 12.5303L12.5303 14.7803Z",fill:"currentColor"})})},p=e=>{let{size:r=16}=e;return(0,o.jsx)("svg",{height:r,strokeLinejoin:"round",viewBox:"0 0 16 16",width:r,style:{color:"currentcolor"},children:(0,o.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6.245 2.5H14.5V12.5C14.5 13.0523 14.0523 13.5 13.5 13.5H6.245V2.5ZM4.995 2.5H1.5V12.5C1.5 13.0523 1.94772 13.5 2.5 13.5H4.995V2.5ZM0 1H1.5H14.5H16V2.5V12.5C16 13.8807 14.8807 15 13.5 15H2.5C1.11929 15 0 13.8807 0 12.5V2.5V1Z",fill:"currentColor"})})},g=e=>{let{size:r=16}=e;return(0,o.jsx)("svg",{height:r,strokeLinejoin:"round",viewBox:"0 0 16 16",width:r,style:{color:"currentcolor"},children:(0,o.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M 8.75,1 H7.25 V7.25 H1.5 V8.75 H7.25 V15 H8.75 V8.75 H14.5 V7.25 H8.75 V1.75 Z",fill:"currentColor"})})},j=e=>{let{size:r=16}=e;return(0,o.jsx)("svg",{height:r,strokeLinejoin:"round",viewBox:"0 0 16 16",width:r,style:{color:"currentcolor"},children:(0,o.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M2.75 0.5C1.7835 0.5 1 1.2835 1 2.25V9.75C1 10.7165 1.7835 11.5 2.75 11.5H3.75H4.5V10H3.75H2.75C2.61193 10 2.5 9.88807 2.5 9.75V2.25C2.5 2.11193 2.61193 2 2.75 2H8.25C8.38807 2 8.5 2.11193 8.5 2.25V3H10V2.25C10 1.2835 9.2165 0.5 8.25 0.5H2.75ZM7.75 4.5C6.7835 4.5 6 5.2835 6 6.25V13.75C6 14.7165 6.7835 15.5 7.75 15.5H13.25C14.2165 15.5 15 14.7165 15 13.75V6.25C15 5.2835 14.2165 4.5 13.25 4.5H7.75ZM7.5 6.25C7.5 6.11193 7.61193 6 7.75 6H13.25C13.3881 6 13.5 6.11193 13.5 6.25V13.75C13.5 13.8881 13.3881 14 13.25 14H7.75C7.61193 14 7.5 13.8881 7.5 13.75V6.25Z",fill:"currentColor"})})},w=e=>{let{size:r=16}=e;return(0,o.jsx)("svg",{height:r,strokeLinejoin:"round",viewBox:"0 0 16 16",width:r,style:{color:"currentcolor"},children:(0,o.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6.89531 2.23972C6.72984 2.12153 6.5 2.23981 6.5 2.44315V5.25001C6.5 6.21651 5.7165 7.00001 4.75 7.00001H2.5V13.5H12.1884C12.762 13.5 13.262 13.1096 13.4011 12.5532L14.4011 8.55318C14.5984 7.76425 14.0017 7.00001 13.1884 7.00001H9.25H8.5V6.25001V3.51458C8.5 3.43384 8.46101 3.35807 8.39531 3.31114L6.89531 2.23972ZM5 2.44315C5 1.01975 6.6089 0.191779 7.76717 1.01912L9.26717 2.09054C9.72706 2.41904 10 2.94941 10 3.51458V5.50001H13.1884C14.9775 5.50001 16.2903 7.18133 15.8563 8.91698L14.8563 12.917C14.5503 14.1412 13.4503 15 12.1884 15H1.75H1V14.25V6.25001V5.50001H1.75H4.75C4.88807 5.50001 5 5.38808 5 5.25001V2.44315Z",fill:"currentColor"})})},M=e=>{let{size:r=16}=e;return(0,o.jsx)("svg",{height:r,strokeLinejoin:"round",viewBox:"0 0 16 16",width:r,style:{color:"currentcolor"},children:(0,o.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6.89531 13.7603C6.72984 13.8785 6.5 13.7602 6.5 13.5569V10.75C6.5 9.7835 5.7165 9 4.75 9H2.5V2.5H12.1884C12.762 2.5 13.262 2.89037 13.4011 3.44683L14.4011 7.44683C14.5984 8.23576 14.0017 9 13.1884 9H9.25H8.5V9.75V12.4854C8.5 12.5662 8.46101 12.6419 8.39531 12.6889L6.89531 13.7603ZM5 13.5569C5 14.9803 6.6089 15.8082 7.76717 14.9809L9.26717 13.9095C9.72706 13.581 10 13.0506 10 12.4854V10.5H13.1884C14.9775 10.5 16.2903 8.81868 15.8563 7.08303L14.8563 3.08303C14.5503 1.85882 13.4503 1 12.1884 1H1.75H1V1.75V9.75V10.5H1.75H4.75C4.88807 10.5 5 10.6119 5 10.75V13.5569Z",fill:"currentColor"})})},y=e=>{let{size:r=16}=e;return(0,o.jsx)("svg",{height:r,strokeLinejoin:"round",viewBox:"0 0 16 16",width:r,style:{color:"currentcolor"},children:(0,o.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12.0607 6.74999L11.5303 7.28032L8.7071 10.1035C8.31657 10.4941 7.68341 10.4941 7.29288 10.1035L4.46966 7.28032L3.93933 6.74999L4.99999 5.68933L5.53032 6.21966L7.99999 8.68933L10.4697 6.21966L11 5.68933L12.0607 6.74999Z",fill:"currentColor"})})},Z=e=>{let{size:r=16}=e;return(0,o.jsxs)("svg",{height:r,strokeLinejoin:"round",viewBox:"0 0 16 16",width:r,style:{color:"currentcolor"},children:[(0,o.jsx)("path",{d:"M2.5 0.5V0H3.5V0.5C3.5 1.60457 4.39543 2.5 5.5 2.5H6V3V3.5H5.5C4.39543 3.5 3.5 4.39543 3.5 5.5V6H3H2.5V5.5C2.5 4.39543 1.60457 3.5 0.5 3.5H0V3V2.5H0.5C1.60457 2.5 2.5 1.60457 2.5 0.5Z",fill:"currentColor"}),(0,o.jsx)("path",{d:"M14.5 4.5V5H13.5V4.5C13.5 3.94772 13.0523 3.5 12.5 3.5H12V3V2.5H12.5C13.0523 2.5 13.5 2.05228 13.5 1.5V1H14H14.5V1.5C14.5 2.05228 14.9477 2.5 15.5 2.5H16V3V3.5H15.5C14.9477 3.5 14.5 3.94772 14.5 4.5Z",fill:"currentColor"}),(0,o.jsx)("path",{d:"M8.40706 4.92939L8.5 4H9.5L9.59294 4.92939C9.82973 7.29734 11.7027 9.17027 14.0706 9.40706L15 9.5V10.5L14.0706 10.5929C11.7027 10.8297 9.82973 12.7027 9.59294 15.0706L9.5 16H8.5L8.40706 15.0706C8.17027 12.7027 6.29734 10.8297 3.92939 10.5929L3 10.5V9.5L3.92939 9.40706C6.29734 9.17027 8.17027 7.29734 8.40706 4.92939Z",fill:"currentColor"})]})},k=e=>{let{size:r=16}=e;return(0,o.jsx)("svg",{height:r,strokeLinejoin:"round",viewBox:"0 0 16 16",width:r,style:{color:"currentcolor"},children:(0,o.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M16 8C16 12.4183 12.4183 16 8 16C3.58172 16 0 12.4183 0 8C0 3.58172 3.58172 0 8 0C12.4183 0 16 3.58172 16 8ZM11.5303 6.53033L12.0607 6L11 4.93934L10.4697 5.46967L6.5 9.43934L5.53033 8.46967L5 7.93934L3.93934 9L4.46967 9.53033L5.96967 11.0303C6.26256 11.3232 6.73744 11.3232 7.03033 11.0303L11.5303 6.53033Z",fill:"currentColor"})})},R=e=>{let{size:r=16}=e;return(0,o.jsx)("svg",{height:r,strokeLinejoin:"round",viewBox:"0 0 16 16",width:r,style:{color:"currentcolor"},children:(0,o.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10.268 14.0934C11.9051 13.4838 13.2303 12.2333 13.9384 10.6469C13.1192 10.7941 12.2138 10.9111 11.2469 10.9925C11.0336 12.2005 10.695 13.2621 10.268 14.0934ZM8 16C12.4183 16 16 12.4183 16 8C16 3.58172 12.4183 0 8 0C3.58172 0 0 3.58172 0 8C0 12.4183 3.58172 16 8 16ZM8.48347 14.4823C8.32384 14.494 8.16262 14.5 8 14.5C7.83738 14.5 7.67616 14.494 7.51654 14.4823C7.5132 14.4791 7.50984 14.4759 7.50647 14.4726C7.2415 14.2165 6.94578 13.7854 6.67032 13.1558C6.41594 12.5744 6.19979 11.8714 6.04101 11.0778C6.67605 11.1088 7.33104 11.125 8 11.125C8.66896 11.125 9.32395 11.1088 9.95899 11.0778C9.80021 11.8714 9.58406 12.5744 9.32968 13.1558C9.05422 13.7854 8.7585 14.2165 8.49353 14.4726C8.49016 14.4759 8.4868 14.4791 8.48347 14.4823ZM11.4187 9.72246C12.5137 9.62096 13.5116 9.47245 14.3724 9.28806C14.4561 8.87172 14.5 8.44099 14.5 8C14.5 7.55901 14.4561 7.12828 14.3724 6.71194C13.5116 6.52755 12.5137 6.37904 11.4187 6.27753C11.4719 6.83232 11.5 7.40867 11.5 8C11.5 8.59133 11.4719 9.16768 11.4187 9.72246ZM10.1525 6.18401C10.2157 6.75982 10.25 7.36805 10.25 8C10.25 8.63195 10.2157 9.24018 10.1525 9.81598C9.46123 9.85455 8.7409 9.875 8 9.875C7.25909 9.875 6.53877 9.85455 5.84749 9.81598C5.7843 9.24018 5.75 8.63195 5.75 8C5.75 7.36805 5.7843 6.75982 5.84749 6.18401C6.53877 6.14545 7.25909 6.125 8 6.125C8.74091 6.125 9.46123 6.14545 10.1525 6.18401ZM11.2469 5.00748C12.2138 5.08891 13.1191 5.20593 13.9384 5.35306C13.2303 3.7667 11.9051 2.51622 10.268 1.90662C10.695 2.73788 11.0336 3.79953 11.2469 5.00748ZM8.48347 1.51771C8.4868 1.52089 8.49016 1.52411 8.49353 1.52737C8.7585 1.78353 9.05422 2.21456 9.32968 2.84417C9.58406 3.42562 9.80021 4.12856 9.95899 4.92219C9.32395 4.89118 8.66896 4.875 8 4.875C7.33104 4.875 6.67605 4.89118 6.04101 4.92219C6.19978 4.12856 6.41594 3.42562 6.67032 2.84417C6.94578 2.21456 7.2415 1.78353 7.50647 1.52737C7.50984 1.52411 7.51319 1.52089 7.51653 1.51771C7.67615 1.50597 7.83738 1.5 8 1.5C8.16262 1.5 8.32384 1.50597 8.48347 1.51771ZM5.73202 1.90663C4.0949 2.51622 2.76975 3.7667 2.06159 5.35306C2.88085 5.20593 3.78617 5.08891 4.75309 5.00748C4.96639 3.79953 5.30497 2.73788 5.73202 1.90663ZM4.58133 6.27753C3.48633 6.37904 2.48837 6.52755 1.62761 6.71194C1.54392 7.12828 1.5 7.55901 1.5 8C1.5 8.44099 1.54392 8.87172 1.62761 9.28806C2.48837 9.47245 3.48633 9.62096 4.58133 9.72246C4.52807 9.16768 4.5 8.59133 4.5 8C4.5 7.40867 4.52807 6.83232 4.58133 6.27753ZM4.75309 10.9925C3.78617 10.9111 2.88085 10.7941 2.06159 10.6469C2.76975 12.2333 4.0949 13.4838 5.73202 14.0934C5.30497 13.2621 4.96639 12.2005 4.75309 10.9925Z",fill:"currentColor"})})},m=e=>{let{size:r=16}=e;return(0,o.jsx)("svg",{height:r,strokeLinejoin:"round",viewBox:"0 0 16 16",width:r,style:{color:"currentcolor"},children:(0,o.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10 4.5V6H6V4.5C6 3.39543 6.89543 2.5 8 2.5C9.10457 2.5 10 3.39543 10 4.5ZM4.5 6V4.5C4.5 2.567 6.067 1 8 1C9.933 1 11.5 2.567 11.5 4.5V6H12.5H14V7.5V12.5C14 13.8807 12.8807 15 11.5 15H4.5C3.11929 15 2 13.8807 2 12.5V7.5V6H3.5H4.5ZM11.5 7.5H10H6H4.5H3.5V12.5C3.5 13.0523 3.94772 13.5 4.5 13.5H11.5C12.0523 13.5 12.5 13.0523 12.5 12.5V7.5H11.5Z",fill:"currentColor"})})},b=e=>{let{size:r=16}=e;return(0,o.jsx)("svg",{height:r,strokeLinejoin:"round",viewBox:"0 0 16 16",width:r,style:{color:"currentcolor"},children:(0,o.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M15 11.25V10.5H13.5V11.25V12.75C13.5 13.1642 13.1642 13.5 12.75 13.5H3.25C2.83579 13.5 2.5 13.1642 2.5 12.75L2.5 3.25C2.5 2.83579 2.83579 2.5 3.25 2.5H5.75H6.5V1H5.75H3.25C2.00736 1 1 2.00736 1 3.25V12.75C1 13.9926 2.00736 15 3.25 15H12.75C13.9926 15 15 13.9926 15 12.75V11.25ZM15 5.5L10.5 1V4C7.46243 4 5 6.46243 5 9.5V10L5.05855 9.91218C6.27146 8.09281 8.31339 7 10.5 7V10L15 5.5Z",fill:"currentColor"})})},B=e=>{let{size:r=16}=e;return(0,o.jsx)("svg",{height:r,strokeLinejoin:"round",viewBox:"0 0 16 16",width:r,style:{color:"currentcolor"},children:(0,o.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M13.4549 7.22745L13.3229 7.16146L2.5 1.74999L2.4583 1.72914L1.80902 1.4045L1.3618 1.18089C1.19558 1.09778 1 1.21865 1 1.4045L1 1.9045L1 2.63041L1 2.67704L1 13.3229L1 13.3696L1 14.0955L1 14.5955C1 14.7813 1.19558 14.9022 1.3618 14.8191L1.80902 14.5955L2.4583 14.2708L2.5 14.25L13.3229 8.83852L13.4549 8.77253L14.2546 8.37267L14.5528 8.2236C14.737 8.13147 14.737 7.86851 14.5528 7.77638L14.2546 7.62731L13.4549 7.22745ZM11.6459 7.99999L2.5 3.42704L2.5 12.5729L11.6459 7.99999Z",fill:"currentColor"})})},P=e=>{let{size:r=16}=e;return(0,o.jsx)("svg",{height:r,strokeLinejoin:"round",viewBox:"0 0 16 16",width:r,style:{color:"currentcolor"},children:(0,o.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M1.5 2.5H14.5V12.5C14.5 13.0523 14.0523 13.5 13.5 13.5H2.5C1.94772 13.5 1.5 13.0523 1.5 12.5V2.5ZM0 1H1.5H14.5H16V2.5V12.5C16 13.8807 14.8807 15 13.5 15H2.5C1.11929 15 0 13.8807 0 12.5V2.5V1ZM4 11.1339L4.44194 10.6919L6.51516 8.61872C6.85687 8.27701 6.85687 7.72299 6.51517 7.38128L4.44194 5.30806L4 4.86612L3.11612 5.75L3.55806 6.19194L5.36612 8L3.55806 9.80806L3.11612 10.25L4 11.1339ZM8 9.75494H8.6225H11.75H12.3725V10.9999H11.75H8.6225H8V9.75494Z",fill:"currentColor"})})},_=e=>{let{size:r=16}=e;return(0,o.jsx)("svg",{height:r,strokeLinejoin:"round",viewBox:"0 0 16 16",width:r,style:{color:"currentcolor"},children:(0,o.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7.96452 2.5C11.0257 2.5 13.5 4.96643 13.5 8C13.5 11.0336 11.0257 13.5 7.96452 13.5C6.12055 13.5 4.48831 12.6051 3.48161 11.2273L3.03915 10.6217L1.828 11.5066L2.27046 12.1122C3.54872 13.8617 5.62368 15 7.96452 15C11.8461 15 15 11.87 15 8C15 4.13001 11.8461 1 7.96452 1C5.06835 1 2.57851 2.74164 1.5 5.23347V3.75V3H0V3.75V7.25C0 7.66421 0.335786 8 0.75 8H3.75H4.5V6.5H3.75H2.63724C3.29365 4.19393 5.42843 2.5 7.96452 2.5ZM8.75 5.25V4.5H7.25V5.25V7.8662C7.25 8.20056 7.4171 8.51279 7.6953 8.69825L9.08397 9.62404L9.70801 10.0401L10.5401 8.79199L9.91603 8.37596L8.75 7.59861V5.25Z",fill:"currentColor"})})},N=e=>{let{size:r=16}=e;return(0,o.jsx)("svg",{height:r,strokeLinejoin:"round",viewBox:"0 0 16 16",width:r,style:{color:"currentcolor"},children:(0,o.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M9 2H9.75H14.25H15V3.5H14.25H9.75H9V2ZM9 12.5H9.75H14.25H15V14H14.25H9.75H9V12.5ZM9.75 7.25H9V8.75H9.75H14.25H15V7.25H14.25H9.75ZM1 12.5H1.75H2.25H3V14H2.25H1.75H1V12.5ZM1.75 2H1V3.5H1.75H2.25H3V2H2.25H1.75ZM1 7.25H1.75H2.25H3V8.75H2.25H1.75H1V7.25ZM5.75 12.5H5V14H5.75H6.25H7V12.5H6.25H5.75ZM5 2H5.75H6.25H7V3.5H6.25H5.75H5V2ZM5.75 7.25H5V8.75H5.75H6.25H7V7.25H6.25H5.75Z",fill:"currentColor"})})},W=e=>{let{size:r=16}=e;return(0,o.jsx)("svg",{height:r,strokeLinejoin:"round",viewBox:"0 0 16 16",width:r,style:{color:"currentcolor"},children:(0,o.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M14.5 2.5H1.5V9.18933L2.96966 7.71967L3.18933 7.5H3.49999H6.63001H6.93933L6.96966 7.46967L10.4697 3.96967L11.5303 3.96967L14.5 6.93934V2.5ZM8.00066 8.55999L9.53034 10.0897L10.0607 10.62L9.00001 11.6807L8.46968 11.1503L6.31935 9H3.81065L1.53032 11.2803L1.5 11.3106V12.5C1.5 13.0523 1.94772 13.5 2.5 13.5H13.5C14.0523 13.5 14.5 13.0523 14.5 12.5V9.06066L11 5.56066L8.03032 8.53033L8.00066 8.55999ZM4.05312e-06 10.8107V12.5C4.05312e-06 13.8807 1.11929 15 2.5 15H13.5C14.8807 15 16 13.8807 16 12.5V9.56066L16.5607 9L16.0303 8.46967L16 8.43934V2.5V1H14.5H1.5H4.05312e-06V2.5V10.6893L-0.0606689 10.75L4.05312e-06 10.8107Z",fill:"currentColor"})})},z=e=>{let{size:r=16}=e;return(0,o.jsx)("svg",{height:r,strokeLinejoin:"round",viewBox:"0 0 16 16",width:r,style:{color:"currentcolor"},children:(0,o.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M1 5.25V6H2.5V5.25V2.5H5.25H6V1H5.25H2C1.44772 1 1 1.44772 1 2V5.25ZM5.25 14.9994H6V13.4994H5.25H2.5V10.7494V9.99939H1V10.7494V13.9994C1 14.5517 1.44772 14.9994 2 14.9994H5.25ZM15 10V10.75V14C15 14.5523 14.5523 15 14 15H10.75H10V13.5H10.75H13.5V10.75V10H15ZM10.75 1H10V2.5H10.75H13.5V5.25V6H15V5.25V2C15 1.44772 14.5523 1 14 1H10.75Z",fill:"currentColor"})})},D=e=>{let{size:r=16}=e;return(0,o.jsx)("svg",{height:r,strokeLinejoin:"round",viewBox:"0 0 16 16",width:r,style:{color:"currentcolor"},children:(0,o.jsx)("path",{fill:"currentColor",fillRule:"evenodd",d:"M1 1v11.75A2.25 2.25 0 0 0 3.25 15H15v-1.5H3.25a.75.75 0 0 1-.75-.75V1H1Zm13.297 5.013.513-.547-1.094-1.026-.513.547-3.22 3.434-2.276-2.275a1 1 0 0 0-1.414 0L4.22 8.22l-.53.53 1.06 1.06.53-.53L7 7.56l2.287 2.287a1 1 0 0 0 1.437-.023l3.573-3.811Z",clipRule:"evenodd"})})},q=e=>{let{size:r=16}=e;return(0,o.jsx)("svg",{height:r,strokeLinejoin:"round",viewBox:"0 0 16 16",width:r,style:{color:"currentcolor"},children:(0,o.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M8.55846 0.5C9.13413 0.5 9.65902 0.829456 9.90929 1.34788L15.8073 13.5653C16.1279 14.2293 15.6441 15 14.9068 15H1.09316C0.355835 15 -0.127943 14.2293 0.192608 13.5653L6.09065 1.34787C6.34092 0.829454 6.86581 0.5 7.44148 0.5H8.55846ZM8.74997 4.75V5.5V8V8.75H7.24997V8V5.5V4.75H8.74997ZM7.99997 12C8.55226 12 8.99997 11.5523 8.99997 11C8.99997 10.4477 8.55226 10 7.99997 10C7.44769 10 6.99997 10.4477 6.99997 11C6.99997 11.5523 7.44769 12 7.99997 12Z",fill:"currentColor"})})}},90551:(e,r,t)=>{t.d(r,{P7:()=>n});let o={database:"log",chat:"response",auth:"response",stream:"response",api:"response",history:"response",vote:"response",document:"response",suggestions:"response"};class n extends Error{toResponse(){let e="".concat(this.type,":").concat(this.surface),r=o[this.surface],{message:t,cause:n,statusCode:l}=this;return"log"===r?(console.error({code:e,message:t,cause:n}),Response.json({code:"",message:"Something went wrong. Please try again later."},{status:l})):Response.json({code:e,message:t,cause:n},{status:l})}constructor(e,r){super();let[t,o]=e.split(":");this.type=t,this.cause=r,this.surface=o,this.message=function(e){if(e.includes("database"))return"An error occurred while executing a database query.";switch(e){case"bad_request:api":return"The request couldn't be processed. Please check your input and try again.";case"unauthorized:auth":return"You need to sign in before continuing.";case"forbidden:auth":return"Your account does not have access to this feature.";case"rate_limit:chat":return"You have exceeded your maximum number of messages for the day. Please try again later.";case"not_found:chat":return"The requested chat was not found. Please check the chat ID and try again.";case"forbidden:chat":return"This chat belongs to another user. Please check the chat ID and try again.";case"unauthorized:chat":return"You need to sign in to view this chat. Please sign in and try again.";case"offline:chat":return"We're having trouble sending your message. Please check your internet connection and try again.";case"not_found:document":return"The requested document was not found. Please check the document ID and try again.";case"forbidden:document":return"This document belongs to another user. Please check the document ID and try again.";case"unauthorized:document":return"You need to sign in to view this document. Please sign in and try again.";case"bad_request:document":return"The request to create or update the document was invalid. Please check your input and try again.";default:return"Something went wrong. Please try again later."}}(e),this.statusCode=function(e){switch(e){case"bad_request":return 400;case"unauthorized":return 401;case"forbidden":return 403;case"not_found":return 404;case"rate_limit":return 429;case"offline":return 503;default:return 500}}(this.type)}}},99749:(e,r,t)=>{t.d(r,{Dn:()=>u,GO:()=>s,JZ:()=>a,cn:()=>i,jZ:()=>C,lk:()=>c,qz:()=>d});var o=t(32987),n=t(60607),l=t(90551);function i(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,n.QP)((0,o.$)(r))}let s=async e=>{let r=await fetch(e);if(!r.ok){let{code:e,cause:t}=await r.json();throw new l.P7(e,t)}return r.json()};async function d(e,r){try{let t=await fetch(e,r);if(!t.ok){let{code:e,cause:r}=await t.json();throw new l.P7(e,r)}return t}catch(e){if("undefined"!=typeof navigator&&!navigator.onLine)throw new l.P7("offline:chat");throw e}}function c(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,e=>{let r=16*Math.random()|0;return("x"===e?r:3&r|8).toString(16)})}function u(e,r){return!e||r>e.length?new Date:e[r].createdAt}function C(e){return e.replace("<has_function_call>","")}function a(e){return e.parts.filter(e=>"text"===e.type).map(e=>e.text).join("")}}}]);