(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2954],{2082:(e,t,r)=>{"use strict";r.d(t,{_r:()=>i});var n=Object.freeze({InvalidProxyUrlErrorMessage:"The proxyUrl passed to <PERSON> is invalid. The expected value for proxyUrl is an absolute URL or a relative path with a leading '/'. (key={{url}})",InvalidPublishableKeyErrorMessage:"The publishableKey passed to <PERSON> is invalid. You can get your Publishable key at https://dashboard.clerk.com/last-active?path=api-keys. (key={{key}})",MissingPublishableKeyErrorMessage:"Missing publishableKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingSecretKeyErrorMessage:"Missing secretKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingClerkProvider:"{{source}} can only be used within the <ClerkProvider /> component. Learn more: https://clerk.com/docs/components/clerk-provider"});function i({packageName:e,customMessages:t}){let r=e,i={...n,...t};function o(e,t){if(!t)return`${r}: ${e}`;let n=e;for(let r of e.matchAll(/{{([a-zA-Z0-9-_]+)}}/g)){let e=(t[r[1]]||"").toString();n=n.replace(`{{${r[1]}}}`,e)}return`${r}: ${n}`}return{setPackageName({packageName:e}){return"string"==typeof e&&(r=e),this},setMessages({customMessages:e}){return Object.assign(i,e||{}),this},throwInvalidPublishableKeyError(e){throw Error(o(i.InvalidPublishableKeyErrorMessage,e))},throwInvalidProxyUrl(e){throw Error(o(i.InvalidProxyUrlErrorMessage,e))},throwMissingPublishableKeyError(){throw Error(o(i.MissingPublishableKeyErrorMessage))},throwMissingSecretKeyError(){throw Error(o(i.MissingSecretKeyErrorMessage))},throwMissingClerkProviderError(e){throw Error(o(i.MissingClerkProvider,e))},throw(e){throw Error(o(e))}}}},5365:(e,t,r)=>{"use strict";r.d(t,{ED:()=>m,pc:()=>v,TS:()=>C,IC:()=>b,Rs:()=>f,e3:()=>h,MZ:()=>z,Kz:()=>w,ho:()=>M,hQ:()=>g});var n=r(82075);r(51300),r(2082),r(35466);var i=r(6097),o=r(41987),s=r(98686),l=r(36698),a=Object.prototype.hasOwnProperty;function u(e,t,r){for(r of e.keys())if(d(r,t))return r}function d(e,t){var r,n,i;if(e===t)return!0;if(e&&t&&(r=e.constructor)===t.constructor){if(r===Date)return e.getTime()===t.getTime();if(r===RegExp)return e.toString()===t.toString();if(r===Array){if((n=e.length)===t.length)for(;n--&&d(e[n],t[n]););return -1===n}if(r===Set){if(e.size!==t.size)return!1;for(n of e)if((i=n)&&"object"==typeof i&&!(i=u(t,i))||!t.has(i))return!1;return!0}if(r===Map){if(e.size!==t.size)return!1;for(n of e)if((i=n[0])&&"object"==typeof i&&!(i=u(t,i))||!d(n[1],t.get(i)))return!1;return!0}if(r===ArrayBuffer)e=new Uint8Array(e),t=new Uint8Array(t);else if(r===DataView){if((n=e.byteLength)===t.byteLength)for(;n--&&e.getInt8(n)===t.getInt8(n););return -1===n}if(ArrayBuffer.isView(e)){if((n=e.byteLength)===t.byteLength)for(;n--&&e[n]===t[n];);return -1===n}if(!r||"object"==typeof e){for(r in n=0,e)if(a.call(e,r)&&++n&&!a.call(t,r)||!(r in t)||!d(e[r],t[r]))return!1;return Object.keys(t).length===n}}return e!=e&&t!=t}function c(e,t){if(!e)throw"string"==typeof t?Error(t):Error(`${t.displayName} not found`)}var h=(e,t)=>{let{assertCtxFn:r=c}=t||{},n=o.createContext(void 0);return n.displayName=e,[n,()=>{let t=o.useContext(n);return r(t,`${e} not found`),t.value},()=>{let e=o.useContext(n);return e?e.value:{}}]},p={};(0,i.VA)(p,{useSWR:()=>s.default,useSWRInfinite:()=>l.Ay}),(0,i.ie)(p,s);var[m,g]=h("ClerkInstanceContext"),[f,k]=h("UserContext"),[v,y]=h("ClientContext"),[b,_]=h("SessionContext"),[P,j]=(o.createContext({}),h("OrganizationContext")),C=({children:e,organization:t,swrConfig:r})=>o.createElement(p.SWRConfig,{value:r},o.createElement(P.Provider,{value:{value:{organization:t}}},e));function w(e){if(!o.useContext(m)){if("function"==typeof e)return void e();throw Error(`${e} can only be used within the <ClerkProvider /> component.

Possible fixes:
1. Ensure that the <ClerkProvider /> is correctly wrapping your application where this component is used.
2. Check for multiple versions of the \`@clerk/shared\` package in your project. Use a tool like \`npm ls @clerk/shared\` to identify multiple versions, and update your dependencies to only rely on one.

Learn more: https://clerk.com/docs/components/clerk-provider`.trim())}}function S(e,t){let r=new Set(Object.keys(t)),n={};for(let t of Object.keys(e))r.has(t)||(n[t]=e[t]);return n}var O=(e,t)=>{let r="boolean"==typeof e&&e,n=(0,o.useRef)(r?t.initialPage:e?.initialPage??t.initialPage),i=(0,o.useRef)(r?t.pageSize:e?.pageSize??t.pageSize),s={};for(let n of Object.keys(t))s[n]=r?t[n]:e?.[n]??t[n];return{...s,initialPage:n.current,pageSize:i.current}},U={dedupingInterval:6e4,focusThrottleInterval:12e4},E=(e,t,r,n)=>{let[i,a]=(0,o.useState)(e.initialPage??1),u=(0,o.useRef)(e.initialPage??1),d=(0,o.useRef)(e.pageSize??10),c=r.enabled??!0,h="cache"===r.__experimental_mode,p=r.infinite??!1,m=r.keepPreviousData??!1,g={...n,...e,initialPage:i,pageSize:d.current},f=!p&&c&&(!!h||!!t),k=!h&&t?r=>{let i=S(r,n);return t({...e,...i})}:null,{data:v,isValidating:y,isLoading:b,error:_,mutate:P}=(0,s.default)(f?g:null,k,{keepPreviousData:m,...U}),{data:j,isLoading:C,isValidating:w,error:O,size:E,setSize:M,mutate:z}=(0,l.Ay)(t=>p&&c?{...e,...n,initialPage:u.current+t,pageSize:d.current}:null,e=>{let r=S(e,n);return t?.(r)},U),I=(0,o.useMemo)(()=>p?E:i,[p,E,i]),A=(0,o.useCallback)(e=>p?void M(e):a(e),[M]),W=(0,o.useMemo)(()=>p?j?.map(e=>e?.data).flat()??[]:v?.data??[],[p,v,j]),T=(0,o.useMemo)(()=>p?j?.[j?.length-1]?.total_count||0:v?.total_count??0,[p,v,j]),L=p?C:b,R=p?w:y,N=(p?O:_)??null,x=(0,o.useCallback)(()=>{A(e=>Math.max(0,e+1))},[A]),B=(0,o.useCallback)(()=>{A(e=>Math.max(0,e-1))},[A]),F=(u.current-1)*d.current,D=Math.ceil((T-F)/d.current),K=T-F*d.current>I*d.current,$=(I-1)*d.current>F*d.current,V=p?e=>z(e,{revalidate:!1}):e=>P(e,{revalidate:!1});return{data:W,count:T,error:N,isLoading:L,isFetching:R,isError:!!N,page:I,pageCount:D,fetchPage:A,fetchNext:x,fetchPrevious:B,hasNextPage:K,hasPreviousPage:$,revalidate:p?()=>z():()=>P(),setData:V}},M=("undefined"!=typeof window?o.useLayoutEffect:o.useEffect,()=>(w("useClerk"),g())),z=d;function I({hookName:e,resourceType:t,useFetcher:r}){return function(i){let{for:o,...s}=i;w(e);let l=r(o),a=O(s,{initialPage:1,pageSize:10,keepPreviousData:!1,infinite:!1,__experimental_mode:void 0}),u=g(),d=k(),{organization:c}=j();u.telemetry?.record((0,n.FJ)(e));let h=void 0===s?void 0:{initialPage:a.initialPage,pageSize:a.pageSize,..."organization"===o?{orgId:c?.id}:{}},p=!!(u.loaded&&d);return E(h||{},l,{keepPreviousData:a.keepPreviousData,infinite:a.infinite,enabled:!!h&&p,__experimental_mode:a.__experimental_mode},{type:t,userId:d?.id,..."organization"===o?{orgId:c?.id}:{}})}}I({hookName:"useStatements",resourceType:"commerce-statements",useFetcher:()=>g().billing.getStatements}),I({hookName:"usePaymentAttempts",resourceType:"commerce-payment-attempts",useFetcher:()=>g().billing.getPaymentAttempts}),I({hookName:"usePaymentMethods",resourceType:"commerce-payment-methods",useFetcher:e=>{let{organization:t}=j(),r=k();return"organization"===e?t?.getPaymentSources:r?.getPaymentSources}}),I({hookName:"useSubscriptionItems",resourceType:"commerce-subscription-items",useFetcher:()=>g().billing.getSubscriptions})},6097:(e,t,r)=>{"use strict";r.d(t,{OV:()=>p,S7:()=>h,VA:()=>a,ie:()=>d,jq:()=>m});var n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,s=Object.prototype.hasOwnProperty,l=e=>{throw TypeError(e)},a=(e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})},u=(e,t,r,l)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let a of o(t))s.call(e,a)||a===r||n(e,a,{get:()=>t[a],enumerable:!(l=i(t,a))||l.enumerable});return e},d=(e,t,r)=>(u(e,t,"default"),r&&u(r,t,"default")),c=(e,t,r)=>t.has(e)||l("Cannot "+r),h=(e,t,r)=>(c(e,t,"read from private field"),r?r.call(e):t.get(e)),p=(e,t,r,n)=>(c(e,t,"write to private field"),n?n.call(e,r):t.set(e,r),r),m=(e,t,r)=>(c(e,t,"access private method"),r)},15833:(e,t,r)=>{"use strict";r.d(t,{lJ:()=>eb,EH:()=>c.EH,iB:()=>c.iB,Bl:()=>c.Bl,As:()=>c.As,ho:()=>c.ho});var n,i,o,s,l,a,u,d,c=r(71721),h=e=>{throw TypeError(e)},p=(e,t,r)=>t.has(e)||h("Cannot "+r),m=(e,t,r)=>(p(e,t,"read from private field"),r?r.call(e):t.get(e)),g=(e,t,r)=>t.has(e)?h("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),f=(e,t,r,n)=>(p(e,t,"write to private field"),n?n.call(e,r):t.set(e,r),r),k=(e,t,r)=>(p(e,t,"access private method"),r),v=r(59448),y=r(34577),b=e=>{(0,y.b_)()&&console.error(`Clerk: ${e}`)};function _(e,t,r){return"function"==typeof e?e(t):void 0!==e?e:void 0!==r?r:void 0}r(6097);var P=r(41987),j=r(9604),C=(e,...t)=>{let r={...e};for(let e of t)delete r[e];return r},w=r(5365),S=r(99701),O=(e,t,r)=>!e&&r?U(r):E(t),U=e=>{let t=e.userId,r=e.user,n=e.sessionId,i=e.sessionStatus,o=e.sessionClaims,s=e.session,l=e.organization,a=e.orgId,u=e.orgRole,d=e.orgPermissions,c=e.orgSlug;return{userId:t,user:r,sessionId:n,session:s,sessionStatus:i,sessionClaims:o,organization:l,orgId:a,orgRole:u,orgPermissions:d,orgSlug:c,actor:e.actor,factorVerificationAge:e.factorVerificationAge}},E=e=>{let t=e.user?e.user.id:e.user,r=e.user,n=e.session?e.session.id:e.session,i=e.session,o=e.session?.status,s=e.session?e.session.lastActiveToken?.jwt?.claims:null,l=e.session?e.session.factorVerificationAge:null,a=i?.actor,u=e.organization,d=e.organization?e.organization.id:e.organization,c=u?.slug,h=u?r?.organizationMemberships?.find(e=>e.organization.id===d):u,p=h?h.permissions:h;return{userId:t,user:r,sessionId:n,session:i,sessionStatus:o,sessionClaims:s,organization:u,orgId:d,orgRole:h?h.role:h,orgSlug:c,orgPermissions:p,actor:a,factorVerificationAge:l}};function M(){return"undefined"!=typeof window}RegExp("bot|spider|crawl|APIs-Google|AdsBot|Googlebot|mediapartners|Google Favicon|FeedFetcher|Google-Read-Aloud|DuplexWeb-Google|googleweblight|bing|yandex|baidu|duckduck|yahoo|ecosia|ia_archiver|facebook|instagram|pinterest|reddit|slack|twitter|whatsapp|youtube|semrush","i");var z=(e,t,r,n,i)=>{let{notify:o}=i||{},s=e.get(r);s||(s=[],e.set(r,s)),s.push(n),o&&t.has(r)&&n(t.get(r))},I=(e,t,r)=>(e.get(t)||[]).map(e=>e(r)),A=(e,t,r)=>{let n=e.get(t);n&&(r?n.splice(n.indexOf(r)>>>0,1):e.set(t,[]))},W=()=>{let e=new Map,t=new Map,r=new Map;return{on:(...r)=>z(e,t,...r),prioritizedOn:(...e)=>z(r,t,...e),emit:(n,i)=>{t.set(n,i),I(r,n,i),I(e,n,i)},off:(...t)=>A(e,...t),prioritizedOff:(...e)=>A(r,...e),internal:{retrieveListeners:t=>e.get(t)||[]}}},T={Status:"status"},L=()=>W();"undefined"==typeof window||window.global||(window.global="undefined"==typeof global?window:global);var R=e=>t=>{try{return P.Children.only(e)}catch{return c.sb.throw((0,c.Wq)(t))}},N=(e,t)=>(e||(e=t),"string"==typeof e&&(e=P.createElement("button",null,e)),e),x=e=>(...t)=>{if(e&&"function"==typeof e)return e(...t)},B=new Map,F=e=>{let t=Array(e.length).fill(null),[r,n]=(0,P.useState)(t);return e.map((e,t)=>({id:e.id,mount:e=>n(r=>r.map((r,n)=>n===t?e:r)),unmount:()=>n(e=>e.map((e,r)=>r===t?null:e)),portal:()=>P.createElement(P.Fragment,null,r[t]?(0,j.createPortal)(e.component,r[t]):null)}))},D=(e,t)=>!!e&&P.isValidElement(e)&&(null==e?void 0:e.type)===t,K=(e,t)=>Q({children:e,reorderItemsLabels:["account","security"],LinkComponent:ea,PageComponent:el,MenuItemsComponent:ed,componentName:"UserProfile"},t),$=(e,t)=>Q({children:e,reorderItemsLabels:["general","members"],LinkComponent:em,PageComponent:ep,componentName:"OrganizationProfile"},t),V=e=>{let t=[],r=[em,ep,ed,el,ea];return P.Children.forEach(e,e=>{r.some(t=>D(e,t))||t.push(e)}),t},Q=(e,t)=>{let{children:r,LinkComponent:n,PageComponent:i,MenuItemsComponent:o,reorderItemsLabels:s,componentName:l}=e,{allowForAnyChildren:a=!1}=t||{},u=[];P.Children.forEach(r,e=>{if(!D(e,i)&&!D(e,n)&&!D(e,o)){e&&!a&&b((0,c.n)(l));return}let{props:t}=e,{children:r,label:d,url:h,labelIcon:p}=t;if(D(e,i))if(G(t,s))u.push({label:d});else{if(!J(t))return void b((0,c.sR)(l));u.push({label:d,labelIcon:p,children:r,url:h})}if(D(e,n))if(!q(t))return void b((0,c.D)(l));else u.push({label:d,labelIcon:p,url:h})});let d=[],h=[],p=[];u.forEach((e,t)=>{if(J(e)){d.push({component:e.children,id:t}),h.push({component:e.labelIcon,id:t});return}q(e)&&p.push({component:e.labelIcon,id:t})});let m=F(d),g=F(h),f=F(p),k=[],v=[];return u.forEach((e,t)=>{if(G(e,s))return void k.push({label:e.label});if(J(e)){let{portal:r,mount:n,unmount:i}=m.find(e=>e.id===t),{portal:o,mount:s,unmount:l}=g.find(e=>e.id===t);k.push({label:e.label,url:e.url,mount:n,unmount:i,mountIcon:s,unmountIcon:l}),v.push(r),v.push(o);return}if(q(e)){let{portal:r,mount:n,unmount:i}=f.find(e=>e.id===t);k.push({label:e.label,url:e.url,mountIcon:n,unmountIcon:i}),v.push(r);return}}),{customPages:k,customPagesPortals:v}},G=(e,t)=>{let{children:r,label:n,url:i,labelIcon:o}=e;return!r&&!i&&!o&&t.some(e=>e===n)},J=e=>{let{children:t,label:r,url:n,labelIcon:i}=e;return!!t&&!!n&&!!i&&!!r},q=e=>{let{children:t,label:r,url:n,labelIcon:i}=e;return!t&&!!n&&!!i&&!!r},H=e=>X({children:e,reorderItemsLabels:["manageAccount","signOut"],MenuItemsComponent:ed,MenuActionComponent:ec,MenuLinkComponent:eh,UserProfileLinkComponent:ea,UserProfilePageComponent:el}),X=({children:e,MenuItemsComponent:t,MenuActionComponent:r,MenuLinkComponent:n,UserProfileLinkComponent:i,UserProfilePageComponent:o,reorderItemsLabels:s})=>{let l=[],a=[],u=[];P.Children.forEach(e,e=>{if(!D(e,t)&&!D(e,i)&&!D(e,o)){e&&b(c.P6);return}if(D(e,i)||D(e,o))return;let{props:a}=e;P.Children.forEach(a.children,e=>{if(!D(e,r)&&!D(e,n)){e&&b(c.wm);return}let{props:t}=e,{label:i,labelIcon:o,href:a,onClick:u,open:d}=t;if(D(e,r))if(Z(t,s))l.push({label:i});else{if(!Y(t))return void b(c.Wv);let e={label:i,labelIcon:o};if(void 0!==u)l.push({...e,onClick:u});else{if(void 0===d)return void b("Custom menu item must have either onClick or open property");l.push({...e,open:d.startsWith("/")?d:`/${d}`})}}if(D(e,n))if(!ee(t))return void b(c.ld);else l.push({label:i,labelIcon:o,href:a})})});let d=[],h=[];l.forEach((e,t)=>{Y(e)&&d.push({component:e.labelIcon,id:t}),ee(e)&&h.push({component:e.labelIcon,id:t})});let p=F(d),m=F(h);return l.forEach((e,t)=>{if(Z(e,s)&&a.push({label:e.label}),Y(e)){let{portal:r,mount:n,unmount:i}=p.find(e=>e.id===t),o={label:e.label,mountIcon:n,unmountIcon:i};"onClick"in e?o.onClick=e.onClick:"open"in e&&(o.open=e.open),a.push(o),u.push(r)}if(ee(e)){let{portal:r,mount:n,unmount:i}=m.find(e=>e.id===t);a.push({label:e.label,href:e.href,mountIcon:n,unmountIcon:i}),u.push(r)}}),{customMenuItems:a,customMenuItemsPortals:u}},Z=(e,t)=>{let{children:r,label:n,onClick:i,labelIcon:o}=e;return!r&&!i&&!o&&t.some(e=>e===n)},Y=e=>{let{label:t,labelIcon:r,onClick:n,open:i}=e;return!!r&&!!t&&("function"==typeof n||"string"==typeof i)},ee=e=>{let{label:t,href:r,labelIcon:n}=e;return!!r&&!!n&&!!t};function et(e){let t=(0,P.useRef)(),[r,n]=(0,P.useState)("rendering");return(0,P.useEffect)(()=>{if(!e)throw Error("Clerk: no component name provided, unable to detect mount.");"undefined"==typeof window||t.current||(t.current=(function(e){let{root:t=null==document?void 0:document.body,selector:r,timeout:n=0}=e;return new Promise((e,i)=>{if(!t)return void i(Error("No root element provided"));let o=t;if(r&&(o=null==t?void 0:t.querySelector(r)),(null==o?void 0:o.childElementCount)&&o.childElementCount>0)return void e();let s=new MutationObserver(n=>{for(let i of n)if("childList"===i.type&&(!o&&r&&(o=null==t?void 0:t.querySelector(r)),(null==o?void 0:o.childElementCount)&&o.childElementCount>0)){s.disconnect(),e();return}});s.observe(t,{childList:!0,subtree:!0}),n>0&&setTimeout(()=>{s.disconnect(),i(Error("Timeout waiting for element children"))},n)})})({selector:`[data-clerk-component="${e}"]`}).then(()=>{n("rendered")}).catch(()=>{n("error")}))},[e]),r}var er=e=>"mount"in e,en=e=>"open"in e,ei=e=>null==e?void 0:e.map(({mountIcon:e,unmountIcon:t,...r})=>r),eo=class extends P.PureComponent{constructor(){super(...arguments),this.rootRef=P.createRef()}componentDidUpdate(e){var t,r,n,i;if(!er(e)||!er(this.props))return;let o=C(e.props,"customPages","customMenuItems","children"),s=C(this.props.props,"customPages","customMenuItems","children"),l=(null==(t=o.customPages)?void 0:t.length)!==(null==(r=s.customPages)?void 0:r.length),a=(null==(n=o.customMenuItems)?void 0:n.length)!==(null==(i=s.customMenuItems)?void 0:i.length),u=ei(e.props.customMenuItems),d=ei(this.props.props.customMenuItems);(!(0,w.MZ)(o,s)||!(0,w.MZ)(u,d)||l||a)&&this.rootRef.current&&this.props.updateProps({node:this.rootRef.current,props:this.props.props})}componentDidMount(){this.rootRef.current&&(er(this.props)&&this.props.mount(this.rootRef.current,this.props.props),en(this.props)&&this.props.open(this.props.props))}componentWillUnmount(){this.rootRef.current&&(er(this.props)&&this.props.unmount(this.rootRef.current),en(this.props)&&this.props.close())}render(){let{hideRootHtmlElement:e=!1}=this.props,t={ref:this.rootRef,...this.props.rootProps,...this.props.component&&{"data-clerk-component":this.props.component}};return P.createElement(P.Fragment,null,!e&&P.createElement("div",{...t}),this.props.children)}},es=e=>{var t,r;return P.createElement(P.Fragment,null,null==(t=null==e?void 0:e.customPagesPortals)?void 0:t.map((e,t)=>(0,P.createElement)(e,{key:t})),null==(r=null==e?void 0:e.customMenuItemsPortals)?void 0:r.map((e,t)=>(0,P.createElement)(e,{key:t})))};function el({children:e}){return b(c.$n),P.createElement(P.Fragment,null,e)}function ea({children:e}){return b(c._I),P.createElement(P.Fragment,null,e)}(0,c.Q)(({clerk:e,component:t,fallback:r,...n})=>{let i="rendering"===et(t)||!e.loaded,o={...i&&r&&{style:{display:"none"}}};return P.createElement(P.Fragment,null,i&&r,e.loaded&&P.createElement(eo,{component:t,mount:e.mountSignIn,unmount:e.unmountSignIn,updateProps:e.__unstable__updateProps,props:n,rootProps:o}))},{component:"SignIn",renderWhileLoading:!0}),(0,c.Q)(({clerk:e,component:t,fallback:r,...n})=>{let i="rendering"===et(t)||!e.loaded,o={...i&&r&&{style:{display:"none"}}};return P.createElement(P.Fragment,null,i&&r,e.loaded&&P.createElement(eo,{component:t,mount:e.mountSignUp,unmount:e.unmountSignUp,updateProps:e.__unstable__updateProps,props:n,rootProps:o}))},{component:"SignUp",renderWhileLoading:!0}),Object.assign((0,c.Q)(({clerk:e,component:t,fallback:r,...n})=>{let i="rendering"===et(t)||!e.loaded,o={...i&&r&&{style:{display:"none"}}},{customPages:s,customPagesPortals:l}=K(n.children);return P.createElement(P.Fragment,null,i&&r,P.createElement(eo,{component:t,mount:e.mountUserProfile,unmount:e.unmountUserProfile,updateProps:e.__unstable__updateProps,props:{...n,customPages:s},rootProps:o},P.createElement(es,{customPagesPortals:l})))},{component:"UserProfile",renderWhileLoading:!0}),{Page:el,Link:ea});var eu=(0,P.createContext)({mount:()=>{},unmount:()=>{},updateProps:()=>{}});function ed({children:e}){return b(c.UX),P.createElement(P.Fragment,null,e)}function ec({children:e}){return b(c.aU),P.createElement(P.Fragment,null,e)}function eh({children:e}){return b(c.Uw),P.createElement(P.Fragment,null,e)}function ep({children:e}){return b(c.vb),P.createElement(P.Fragment,null,e)}function em({children:e}){return b(c.kf),P.createElement(P.Fragment,null,e)}Object.assign((0,c.Q)(({clerk:e,component:t,fallback:r,...n})=>{let i="rendering"===et(t)||!e.loaded,o={...i&&r&&{style:{display:"none"}}},{customPages:s,customPagesPortals:l}=K(n.children,{allowForAnyChildren:!!n.__experimental_asProvider}),a=Object.assign(n.userProfileProps||{},{customPages:s}),{customMenuItems:u,customMenuItemsPortals:d}=H(n.children),c=V(n.children),h={mount:e.mountUserButton,unmount:e.unmountUserButton,updateProps:e.__unstable__updateProps,props:{...n,userProfileProps:a,customMenuItems:u}};return P.createElement(eu.Provider,{value:h},i&&r,e.loaded&&P.createElement(eo,{component:t,...h,hideRootHtmlElement:!!n.__experimental_asProvider,rootProps:o},n.__experimental_asProvider?c:null,P.createElement(es,{customPagesPortals:l,customMenuItemsPortals:d})))},{component:"UserButton",renderWhileLoading:!0}),{UserProfilePage:el,UserProfileLink:ea,MenuItems:ed,Action:ec,Link:eh,__experimental_Outlet:function(e){let t=(0,P.useContext)(eu),r={...t,props:{...t.props,...e}};return P.createElement(eo,{...r})}}),Object.assign((0,c.Q)(({clerk:e,component:t,fallback:r,...n})=>{let i="rendering"===et(t)||!e.loaded,o={...i&&r&&{style:{display:"none"}}},{customPages:s,customPagesPortals:l}=$(n.children);return P.createElement(P.Fragment,null,i&&r,e.loaded&&P.createElement(eo,{component:t,mount:e.mountOrganizationProfile,unmount:e.unmountOrganizationProfile,updateProps:e.__unstable__updateProps,props:{...n,customPages:s},rootProps:o},P.createElement(es,{customPagesPortals:l})))},{component:"OrganizationProfile",renderWhileLoading:!0}),{Page:ep,Link:em}),(0,c.Q)(({clerk:e,component:t,fallback:r,...n})=>{let i="rendering"===et(t)||!e.loaded,o={...i&&r&&{style:{display:"none"}}};return P.createElement(P.Fragment,null,i&&r,e.loaded&&P.createElement(eo,{component:t,mount:e.mountCreateOrganization,unmount:e.unmountCreateOrganization,updateProps:e.__unstable__updateProps,props:n,rootProps:o}))},{component:"CreateOrganization",renderWhileLoading:!0});var eg=(0,P.createContext)({mount:()=>{},unmount:()=>{},updateProps:()=>{}});Object.assign((0,c.Q)(({clerk:e,component:t,fallback:r,...n})=>{let i="rendering"===et(t)||!e.loaded,o={...i&&r&&{style:{display:"none"}}},{customPages:s,customPagesPortals:l}=$(n.children,{allowForAnyChildren:!!n.__experimental_asProvider}),a=Object.assign(n.organizationProfileProps||{},{customPages:s}),u=V(n.children),d={mount:e.mountOrganizationSwitcher,unmount:e.unmountOrganizationSwitcher,updateProps:e.__unstable__updateProps,props:{...n,organizationProfileProps:a},rootProps:o,component:t};return e.__experimental_prefetchOrganizationSwitcher(),P.createElement(eg.Provider,{value:d},P.createElement(P.Fragment,null,i&&r,e.loaded&&P.createElement(eo,{...d,hideRootHtmlElement:!!n.__experimental_asProvider},n.__experimental_asProvider?u:null,P.createElement(es,{customPagesPortals:l}))))},{component:"OrganizationSwitcher",renderWhileLoading:!0}),{OrganizationProfilePage:ep,OrganizationProfileLink:em,__experimental_Outlet:function(e){let t=(0,P.useContext)(eg),r={...t,props:{...t.props,...e}};return P.createElement(eo,{...r})}}),(0,c.Q)(({clerk:e,component:t,fallback:r,...n})=>{let i="rendering"===et(t)||!e.loaded,o={...i&&r&&{style:{display:"none"}}};return P.createElement(P.Fragment,null,i&&r,e.loaded&&P.createElement(eo,{component:t,mount:e.mountOrganizationList,unmount:e.unmountOrganizationList,updateProps:e.__unstable__updateProps,props:n,rootProps:o}))},{component:"OrganizationList",renderWhileLoading:!0}),(0,c.Q)(({clerk:e,component:t,fallback:r,...n})=>{let i="rendering"===et(t)||!e.loaded,o={...i&&r&&{style:{display:"none"}}};return P.createElement(P.Fragment,null,i&&r,e.loaded&&P.createElement(eo,{component:t,open:e.openGoogleOneTap,close:e.closeGoogleOneTap,updateProps:e.__unstable__updateProps,props:n,rootProps:o}))},{component:"GoogleOneTap",renderWhileLoading:!0}),(0,c.Q)(({clerk:e,component:t,fallback:r,...n})=>{let i="rendering"===et(t)||!e.loaded,o={...i&&r&&{style:{display:"none"}}};return P.createElement(P.Fragment,null,i&&r,e.loaded&&P.createElement(eo,{component:t,mount:e.mountWaitlist,unmount:e.unmountWaitlist,updateProps:e.__unstable__updateProps,props:n,rootProps:o}))},{component:"Waitlist",renderWhileLoading:!0}),(0,c.Q)(({clerk:e,component:t,fallback:r,...n})=>{let i="rendering"===et(t)||!e.loaded,o={...i&&r&&{style:{display:"none"}}};return P.createElement(P.Fragment,null,i&&r,e.loaded&&P.createElement(eo,{component:t,mount:e.mountPricingTable,unmount:e.unmountPricingTable,updateProps:e.__unstable__updateProps,props:n,rootProps:o}))},{component:"PricingTable",renderWhileLoading:!0}),(0,c.Q)(({clerk:e,component:t,fallback:r,...n})=>{let i="rendering"===et(t)||!e.loaded,o={...i&&r&&{style:{display:"none"}}};return P.createElement(P.Fragment,null,i&&r,e.loaded&&P.createElement(eo,{component:t,mount:e.mountApiKeys,unmount:e.unmountApiKeys,updateProps:e.__unstable__updateProps,props:n,rootProps:o}))},{component:"ApiKeys",renderWhileLoading:!0}),(0,c.Q)(({clerk:e,children:t,...r})=>{let{signUpFallbackRedirectUrl:n,forceRedirectUrl:i,fallbackRedirectUrl:o,signUpForceRedirectUrl:s,mode:l,initialValues:a,withSignUp:u,oauthFlow:d,...c}=r,h=R(t=N(t,"Sign in"))("SignInButton"),p=()=>{let t={forceRedirectUrl:i,fallbackRedirectUrl:o,signUpFallbackRedirectUrl:n,signUpForceRedirectUrl:s,initialValues:a,withSignUp:u,oauthFlow:d};return"modal"===l?e.openSignIn({...t,appearance:r.appearance}):e.redirectToSignIn({...t,signInFallbackRedirectUrl:o,signInForceRedirectUrl:i})},m=async e=>(h&&"object"==typeof h&&"props"in h&&await x(h.props.onClick)(e),p()),g={...c,onClick:m};return P.cloneElement(h,g)},{component:"SignInButton",renderWhileLoading:!0}),(0,c.Q)(({clerk:e,children:t,...r})=>{let{fallbackRedirectUrl:n,forceRedirectUrl:i,signInFallbackRedirectUrl:o,signInForceRedirectUrl:s,mode:l,unsafeMetadata:a,initialValues:u,oauthFlow:d,...c}=r,h=R(t=N(t,"Sign up"))("SignUpButton"),p=()=>{let t={fallbackRedirectUrl:n,forceRedirectUrl:i,signInFallbackRedirectUrl:o,signInForceRedirectUrl:s,unsafeMetadata:a,initialValues:u,oauthFlow:d};return"modal"===l?e.openSignUp({...t,appearance:r.appearance}):e.redirectToSignUp({...t,signUpFallbackRedirectUrl:n,signUpForceRedirectUrl:i})},m=async e=>(h&&"object"==typeof h&&"props"in h&&await x(h.props.onClick)(e),p()),g={...c,onClick:m};return P.cloneElement(h,g)},{component:"SignUpButton",renderWhileLoading:!0}),(0,c.Q)(({clerk:e,children:t,...r})=>{let{redirectUrl:n="/",signOutOptions:i,...o}=r,s=R(t=N(t,"Sign out"))("SignOutButton"),l=()=>e.signOut({redirectUrl:n,...i}),a=async e=>(await x(s.props.onClick)(e),l()),u={...o,onClick:a};return P.cloneElement(s,u)},{component:"SignOutButton",renderWhileLoading:!0}),(0,c.Q)(({clerk:e,children:t,...r})=>{let{redirectUrl:n,...i}=r,o=R(t=N(t,"Sign in with Metamask"))("SignInWithMetamaskButton"),s=async()=>{!async function(){await e.authenticateWithMetamask({redirectUrl:n||void 0})}()},l=async e=>(await x(o.props.onClick)(e),s()),a={...i,onClick:l};return P.cloneElement(o,a)},{component:"SignInWithMetamask",renderWhileLoading:!0}),void 0===globalThis.__BUILD_DISABLE_RHC__&&(globalThis.__BUILD_DISABLE_RHC__=!1);var ef={name:"@clerk/clerk-react",version:"5.32.4",environment:"production"},ek=class e{constructor(e){g(this,u),this.clerkjs=null,this.preopenOneTap=null,this.preopenUserVerification=null,this.preopenSignIn=null,this.preopenCheckout=null,this.preopenPlanDetails=null,this.preopenSignUp=null,this.preopenUserProfile=null,this.preopenOrganizationProfile=null,this.preopenCreateOrganization=null,this.preOpenWaitlist=null,this.premountSignInNodes=new Map,this.premountSignUpNodes=new Map,this.premountUserProfileNodes=new Map,this.premountUserButtonNodes=new Map,this.premountOrganizationProfileNodes=new Map,this.premountCreateOrganizationNodes=new Map,this.premountOrganizationSwitcherNodes=new Map,this.premountOrganizationListNodes=new Map,this.premountMethodCalls=new Map,this.premountWaitlistNodes=new Map,this.premountPricingTableNodes=new Map,this.premountApiKeysNodes=new Map,this.premountOAuthConsentNodes=new Map,this.premountAddListenerCalls=new Map,this.loadedListeners=[],g(this,n,"loading"),g(this,i),g(this,o),g(this,s),g(this,l,L()),this.buildSignInUrl=e=>{let t=()=>{var t;return(null==(t=this.clerkjs)?void 0:t.buildSignInUrl(e))||""};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("buildSignInUrl",t)},this.buildSignUpUrl=e=>{let t=()=>{var t;return(null==(t=this.clerkjs)?void 0:t.buildSignUpUrl(e))||""};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("buildSignUpUrl",t)},this.buildAfterSignInUrl=(...e)=>{let t=()=>{var t;return(null==(t=this.clerkjs)?void 0:t.buildAfterSignInUrl(...e))||""};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("buildAfterSignInUrl",t)},this.buildAfterSignUpUrl=(...e)=>{let t=()=>{var t;return(null==(t=this.clerkjs)?void 0:t.buildAfterSignUpUrl(...e))||""};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("buildAfterSignUpUrl",t)},this.buildAfterSignOutUrl=()=>{let e=()=>{var e;return(null==(e=this.clerkjs)?void 0:e.buildAfterSignOutUrl())||""};if(this.clerkjs&&this.loaded)return e();this.premountMethodCalls.set("buildAfterSignOutUrl",e)},this.buildNewSubscriptionRedirectUrl=()=>{let e=()=>{var e;return(null==(e=this.clerkjs)?void 0:e.buildNewSubscriptionRedirectUrl())||""};if(this.clerkjs&&this.loaded)return e();this.premountMethodCalls.set("buildNewSubscriptionRedirectUrl",e)},this.buildAfterMultiSessionSingleSignOutUrl=()=>{let e=()=>{var e;return(null==(e=this.clerkjs)?void 0:e.buildAfterMultiSessionSingleSignOutUrl())||""};if(this.clerkjs&&this.loaded)return e();this.premountMethodCalls.set("buildAfterMultiSessionSingleSignOutUrl",e)},this.buildUserProfileUrl=()=>{let e=()=>{var e;return(null==(e=this.clerkjs)?void 0:e.buildUserProfileUrl())||""};if(this.clerkjs&&this.loaded)return e();this.premountMethodCalls.set("buildUserProfileUrl",e)},this.buildCreateOrganizationUrl=()=>{let e=()=>{var e;return(null==(e=this.clerkjs)?void 0:e.buildCreateOrganizationUrl())||""};if(this.clerkjs&&this.loaded)return e();this.premountMethodCalls.set("buildCreateOrganizationUrl",e)},this.buildOrganizationProfileUrl=()=>{let e=()=>{var e;return(null==(e=this.clerkjs)?void 0:e.buildOrganizationProfileUrl())||""};if(this.clerkjs&&this.loaded)return e();this.premountMethodCalls.set("buildOrganizationProfileUrl",e)},this.buildWaitlistUrl=()=>{let e=()=>{var e;return(null==(e=this.clerkjs)?void 0:e.buildWaitlistUrl())||""};if(this.clerkjs&&this.loaded)return e();this.premountMethodCalls.set("buildWaitlistUrl",e)},this.buildUrlWithAuth=e=>{let t=()=>{var t;return(null==(t=this.clerkjs)?void 0:t.buildUrlWithAuth(e))||""};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("buildUrlWithAuth",t)},this.handleUnauthenticated=async()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.handleUnauthenticated()};this.clerkjs&&this.loaded?e():this.premountMethodCalls.set("handleUnauthenticated",e)},this.on=(...e)=>{var t;if(null==(t=this.clerkjs)?void 0:t.on)return this.clerkjs.on(...e);m(this,l).on(...e)},this.off=(...e)=>{var t;if(null==(t=this.clerkjs)?void 0:t.off)return this.clerkjs.off(...e);m(this,l).off(...e)},this.addOnLoaded=e=>{this.loadedListeners.push(e),this.loaded&&this.emitLoaded()},this.emitLoaded=()=>{this.loadedListeners.forEach(e=>e()),this.loadedListeners=[]},this.beforeLoad=e=>{if(!e)throw Error("Failed to hydrate latest Clerk JS")},this.hydrateClerkJS=e=>{var t;if(!e)throw Error("Failed to hydrate latest Clerk JS");return this.clerkjs=e,this.premountMethodCalls.forEach(e=>e()),this.premountAddListenerCalls.forEach((t,r)=>{t.nativeUnsubscribe=e.addListener(r)}),null==(t=m(this,l).internal.retrieveListeners("status"))||t.forEach(e=>{this.on("status",e,{notify:!0})}),null!==this.preopenSignIn&&e.openSignIn(this.preopenSignIn),null!==this.preopenCheckout&&e.__internal_openCheckout(this.preopenCheckout),null!==this.preopenPlanDetails&&e.__internal_openPlanDetails(this.preopenPlanDetails),null!==this.preopenSignUp&&e.openSignUp(this.preopenSignUp),null!==this.preopenUserProfile&&e.openUserProfile(this.preopenUserProfile),null!==this.preopenUserVerification&&e.__internal_openReverification(this.preopenUserVerification),null!==this.preopenOneTap&&e.openGoogleOneTap(this.preopenOneTap),null!==this.preopenOrganizationProfile&&e.openOrganizationProfile(this.preopenOrganizationProfile),null!==this.preopenCreateOrganization&&e.openCreateOrganization(this.preopenCreateOrganization),null!==this.preOpenWaitlist&&e.openWaitlist(this.preOpenWaitlist),this.premountSignInNodes.forEach((t,r)=>{e.mountSignIn(r,t)}),this.premountSignUpNodes.forEach((t,r)=>{e.mountSignUp(r,t)}),this.premountUserProfileNodes.forEach((t,r)=>{e.mountUserProfile(r,t)}),this.premountUserButtonNodes.forEach((t,r)=>{e.mountUserButton(r,t)}),this.premountOrganizationListNodes.forEach((t,r)=>{e.mountOrganizationList(r,t)}),this.premountWaitlistNodes.forEach((t,r)=>{e.mountWaitlist(r,t)}),this.premountPricingTableNodes.forEach((t,r)=>{e.mountPricingTable(r,t)}),this.premountApiKeysNodes.forEach((t,r)=>{e.mountApiKeys(r,t)}),this.premountOAuthConsentNodes.forEach((t,r)=>{e.__internal_mountOAuthConsent(r,t)}),void 0===this.clerkjs.status&&m(this,l).emit(T.Status,"ready"),this.emitLoaded(),this.clerkjs},this.__unstable__updateProps=async e=>{let t=await k(this,u,d).call(this);if(t&&"__unstable__updateProps"in t)return t.__unstable__updateProps(e)},this.__experimental_navigateToTask=async e=>this.clerkjs?this.clerkjs.__experimental_navigateToTask(e):Promise.reject(),this.setActive=e=>this.clerkjs?this.clerkjs.setActive(e):Promise.reject(),this.openSignIn=e=>{this.clerkjs&&this.loaded?this.clerkjs.openSignIn(e):this.preopenSignIn=e},this.closeSignIn=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeSignIn():this.preopenSignIn=null},this.__internal_openCheckout=e=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_openCheckout(e):this.preopenCheckout=e},this.__internal_closeCheckout=()=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_closeCheckout():this.preopenCheckout=null},this.__internal_openPlanDetails=e=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_openPlanDetails(e):this.preopenPlanDetails=e},this.__internal_closePlanDetails=()=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_closePlanDetails():this.preopenPlanDetails=null},this.__internal_openReverification=e=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_openReverification(e):this.preopenUserVerification=e},this.__internal_closeReverification=()=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_closeReverification():this.preopenUserVerification=null},this.openGoogleOneTap=e=>{this.clerkjs&&this.loaded?this.clerkjs.openGoogleOneTap(e):this.preopenOneTap=e},this.closeGoogleOneTap=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeGoogleOneTap():this.preopenOneTap=null},this.openUserProfile=e=>{this.clerkjs&&this.loaded?this.clerkjs.openUserProfile(e):this.preopenUserProfile=e},this.closeUserProfile=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeUserProfile():this.preopenUserProfile=null},this.openOrganizationProfile=e=>{this.clerkjs&&this.loaded?this.clerkjs.openOrganizationProfile(e):this.preopenOrganizationProfile=e},this.closeOrganizationProfile=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeOrganizationProfile():this.preopenOrganizationProfile=null},this.openCreateOrganization=e=>{this.clerkjs&&this.loaded?this.clerkjs.openCreateOrganization(e):this.preopenCreateOrganization=e},this.closeCreateOrganization=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeCreateOrganization():this.preopenCreateOrganization=null},this.openWaitlist=e=>{this.clerkjs&&this.loaded?this.clerkjs.openWaitlist(e):this.preOpenWaitlist=e},this.closeWaitlist=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeWaitlist():this.preOpenWaitlist=null},this.openSignUp=e=>{this.clerkjs&&this.loaded?this.clerkjs.openSignUp(e):this.preopenSignUp=e},this.closeSignUp=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeSignUp():this.preopenSignUp=null},this.mountSignIn=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountSignIn(e,t):this.premountSignInNodes.set(e,t)},this.unmountSignIn=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountSignIn(e):this.premountSignInNodes.delete(e)},this.mountSignUp=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountSignUp(e,t):this.premountSignUpNodes.set(e,t)},this.unmountSignUp=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountSignUp(e):this.premountSignUpNodes.delete(e)},this.mountUserProfile=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountUserProfile(e,t):this.premountUserProfileNodes.set(e,t)},this.unmountUserProfile=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountUserProfile(e):this.premountUserProfileNodes.delete(e)},this.mountOrganizationProfile=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountOrganizationProfile(e,t):this.premountOrganizationProfileNodes.set(e,t)},this.unmountOrganizationProfile=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountOrganizationProfile(e):this.premountOrganizationProfileNodes.delete(e)},this.mountCreateOrganization=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountCreateOrganization(e,t):this.premountCreateOrganizationNodes.set(e,t)},this.unmountCreateOrganization=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountCreateOrganization(e):this.premountCreateOrganizationNodes.delete(e)},this.mountOrganizationSwitcher=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountOrganizationSwitcher(e,t):this.premountOrganizationSwitcherNodes.set(e,t)},this.unmountOrganizationSwitcher=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountOrganizationSwitcher(e):this.premountOrganizationSwitcherNodes.delete(e)},this.__experimental_prefetchOrganizationSwitcher=()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.__experimental_prefetchOrganizationSwitcher()};this.clerkjs&&this.loaded?e():this.premountMethodCalls.set("__experimental_prefetchOrganizationSwitcher",e)},this.mountOrganizationList=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountOrganizationList(e,t):this.premountOrganizationListNodes.set(e,t)},this.unmountOrganizationList=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountOrganizationList(e):this.premountOrganizationListNodes.delete(e)},this.mountUserButton=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountUserButton(e,t):this.premountUserButtonNodes.set(e,t)},this.unmountUserButton=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountUserButton(e):this.premountUserButtonNodes.delete(e)},this.mountWaitlist=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountWaitlist(e,t):this.premountWaitlistNodes.set(e,t)},this.unmountWaitlist=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountWaitlist(e):this.premountWaitlistNodes.delete(e)},this.mountPricingTable=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountPricingTable(e,t):this.premountPricingTableNodes.set(e,t)},this.unmountPricingTable=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountPricingTable(e):this.premountPricingTableNodes.delete(e)},this.mountApiKeys=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountApiKeys(e,t):this.premountApiKeysNodes.set(e,t)},this.unmountApiKeys=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountApiKeys(e):this.premountApiKeysNodes.delete(e)},this.__internal_mountOAuthConsent=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_mountOAuthConsent(e,t):this.premountOAuthConsentNodes.set(e,t)},this.__internal_unmountOAuthConsent=e=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_unmountOAuthConsent(e):this.premountOAuthConsentNodes.delete(e)},this.addListener=e=>{if(this.clerkjs)return this.clerkjs.addListener(e);{let t=()=>{var t;let r=this.premountAddListenerCalls.get(e);r&&(null==(t=r.nativeUnsubscribe)||t.call(r),this.premountAddListenerCalls.delete(e))};return this.premountAddListenerCalls.set(e,{unsubscribe:t,nativeUnsubscribe:void 0}),t}},this.navigate=e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.navigate(e)};this.clerkjs&&this.loaded?t():this.premountMethodCalls.set("navigate",t)},this.redirectWithAuth=async(...e)=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.redirectWithAuth(...e)};return this.clerkjs&&this.loaded?t():void this.premountMethodCalls.set("redirectWithAuth",t)},this.redirectToSignIn=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.redirectToSignIn(e)};return this.clerkjs&&this.loaded?t():void this.premountMethodCalls.set("redirectToSignIn",t)},this.redirectToSignUp=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.redirectToSignUp(e)};return this.clerkjs&&this.loaded?t():void this.premountMethodCalls.set("redirectToSignUp",t)},this.redirectToUserProfile=async()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.redirectToUserProfile()};return this.clerkjs&&this.loaded?e():void this.premountMethodCalls.set("redirectToUserProfile",e)},this.redirectToAfterSignUp=()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.redirectToAfterSignUp()};if(this.clerkjs&&this.loaded)return e();this.premountMethodCalls.set("redirectToAfterSignUp",e)},this.redirectToAfterSignIn=()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.redirectToAfterSignIn()};this.clerkjs&&this.loaded?e():this.premountMethodCalls.set("redirectToAfterSignIn",e)},this.redirectToAfterSignOut=()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.redirectToAfterSignOut()};this.clerkjs&&this.loaded?e():this.premountMethodCalls.set("redirectToAfterSignOut",e)},this.redirectToOrganizationProfile=async()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.redirectToOrganizationProfile()};return this.clerkjs&&this.loaded?e():void this.premountMethodCalls.set("redirectToOrganizationProfile",e)},this.redirectToCreateOrganization=async()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.redirectToCreateOrganization()};return this.clerkjs&&this.loaded?e():void this.premountMethodCalls.set("redirectToCreateOrganization",e)},this.redirectToWaitlist=async()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.redirectToWaitlist()};return this.clerkjs&&this.loaded?e():void this.premountMethodCalls.set("redirectToWaitlist",e)},this.handleRedirectCallback=async e=>{var t;let r=()=>{var t;return null==(t=this.clerkjs)?void 0:t.handleRedirectCallback(e)};this.clerkjs&&this.loaded?null==(t=r())||t.catch(()=>{}):this.premountMethodCalls.set("handleRedirectCallback",r)},this.handleGoogleOneTapCallback=async(e,t)=>{var r;let n=()=>{var r;return null==(r=this.clerkjs)?void 0:r.handleGoogleOneTapCallback(e,t)};this.clerkjs&&this.loaded?null==(r=n())||r.catch(()=>{}):this.premountMethodCalls.set("handleGoogleOneTapCallback",n)},this.handleEmailLinkVerification=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.handleEmailLinkVerification(e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("handleEmailLinkVerification",t)},this.authenticateWithMetamask=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.authenticateWithMetamask(e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("authenticateWithMetamask",t)},this.authenticateWithCoinbaseWallet=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.authenticateWithCoinbaseWallet(e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("authenticateWithCoinbaseWallet",t)},this.authenticateWithOKXWallet=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.authenticateWithOKXWallet(e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("authenticateWithOKXWallet",t)},this.authenticateWithWeb3=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.authenticateWithWeb3(e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("authenticateWithWeb3",t)},this.authenticateWithGoogleOneTap=async e=>(await k(this,u,d).call(this)).authenticateWithGoogleOneTap(e),this.createOrganization=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.createOrganization(e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("createOrganization",t)},this.getOrganization=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.getOrganization(e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("getOrganization",t)},this.joinWaitlist=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.joinWaitlist(e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("joinWaitlist",t)},this.signOut=async(...e)=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.signOut(...e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("signOut",t)};let{Clerk:t=null,publishableKey:r}=e||{};f(this,s,r),f(this,o,null==e?void 0:e.proxyUrl),f(this,i,null==e?void 0:e.domain),this.options=e,this.Clerk=t,this.mode=M()?"browser":"server",this.options.sdkMetadata||(this.options.sdkMetadata=ef),m(this,l).emit(T.Status,"loading"),m(this,l).prioritizedOn(T.Status,e=>f(this,n,e)),m(this,s)&&this.loadClerkJS()}get publishableKey(){return m(this,s)}get loaded(){var e;return(null==(e=this.clerkjs)?void 0:e.loaded)||!1}get status(){var e;return this.clerkjs?(null==(e=this.clerkjs)?void 0:e.status)||(this.clerkjs.loaded?"ready":"loading"):m(this,n)}static getOrCreateInstance(t){return M()&&m(this,a)&&(!t.Clerk||m(this,a).Clerk===t.Clerk)&&m(this,a).publishableKey===t.publishableKey||f(this,a,new e(t)),m(this,a)}static clearInstance(){f(this,a,null)}get domain(){return"undefined"!=typeof window&&window.location?_(m(this,i),new URL(window.location.href),""):"function"==typeof m(this,i)?c.sb.throw(c.Vo):m(this,i)||""}get proxyUrl(){return"undefined"!=typeof window&&window.location?_(m(this,o),new URL(window.location.href),""):"function"==typeof m(this,o)?c.sb.throw(c.Vo):m(this,o)||""}__internal_getOption(e){var t,r;return(null==(t=this.clerkjs)?void 0:t.__internal_getOption)?null==(r=this.clerkjs)?void 0:r.__internal_getOption(e):this.options[e]}get sdkMetadata(){var e;return(null==(e=this.clerkjs)?void 0:e.sdkMetadata)||this.options.sdkMetadata||void 0}get instanceType(){var e;return null==(e=this.clerkjs)?void 0:e.instanceType}get frontendApi(){var e;return(null==(e=this.clerkjs)?void 0:e.frontendApi)||""}get isStandardBrowser(){var e;return(null==(e=this.clerkjs)?void 0:e.isStandardBrowser)||this.options.standardBrowser||!1}get isSatellite(){return"undefined"!=typeof window&&window.location?_(this.options.isSatellite,new URL(window.location.href),!1):"function"==typeof this.options.isSatellite&&c.sb.throw(c.Vo)}async loadClerkJS(){var e,t;if("browser"===this.mode&&!this.loaded){"undefined"!=typeof window&&(window.__clerk_publishable_key=m(this,s),window.__clerk_proxy_url=this.proxyUrl,window.__clerk_domain=this.domain);try{if(this.Clerk){let e;(t=this.Clerk,"function"==typeof t)?(e=new this.Clerk(m(this,s),{proxyUrl:this.proxyUrl,domain:this.domain}),this.beforeLoad(e),await e.load(this.options)):(e=this.Clerk).loaded||(this.beforeLoad(e),await e.load(this.options)),global.Clerk=e}else if(!__BUILD_DISABLE_RHC__){if(global.Clerk||await (0,v._R)({...this.options,publishableKey:m(this,s),proxyUrl:this.proxyUrl,domain:this.domain,nonce:this.options.nonce}),!global.Clerk)throw Error("Failed to download latest ClerkJS. Contact <EMAIL>.");this.beforeLoad(global.Clerk),await global.Clerk.load(this.options)}if(null==(e=global.Clerk)?void 0:e.loaded)return this.hydrateClerkJS(global.Clerk);return}catch(e){m(this,l).emit(T.Status,"error"),console.error(e.stack||e.message||e);return}}}get version(){var e;return null==(e=this.clerkjs)?void 0:e.version}get client(){return this.clerkjs?this.clerkjs.client:void 0}get session(){return this.clerkjs?this.clerkjs.session:void 0}get user(){return this.clerkjs?this.clerkjs.user:void 0}get organization(){return this.clerkjs?this.clerkjs.organization:void 0}get telemetry(){return this.clerkjs?this.clerkjs.telemetry:void 0}get __unstable__environment(){return this.clerkjs?this.clerkjs.__unstable__environment:void 0}get isSignedIn(){return!!this.clerkjs&&this.clerkjs.isSignedIn}get billing(){var e;return null==(e=this.clerkjs)?void 0:e.billing}get apiKeys(){var e;return null==(e=this.clerkjs)?void 0:e.apiKeys}__unstable__setEnvironment(...e){this.clerkjs&&"__unstable__setEnvironment"in this.clerkjs&&this.clerkjs.__unstable__setEnvironment(e)}};function ev(e){let{isomorphicClerkOptions:t,initialState:r,children:n}=e,{isomorphicClerk:i,clerkStatus:o}=ey(t),[s,l]=P.useState({client:i.client,session:i.session,user:i.user,organization:i.organization});P.useEffect(()=>i.addListener(e=>l({...e})),[]);let a=O(i.loaded,s,r),u=P.useMemo(()=>({value:i}),[o]),d=P.useMemo(()=>({value:s.client}),[s.client]),{sessionId:h,sessionStatus:p,sessionClaims:m,session:g,userId:f,user:k,orgId:v,actor:y,organization:b,orgRole:_,orgSlug:j,orgPermissions:C,factorVerificationAge:S}=a,U=P.useMemo(()=>({value:{sessionId:h,sessionStatus:p,sessionClaims:m,userId:f,actor:y,orgId:v,orgRole:_,orgSlug:j,orgPermissions:C,factorVerificationAge:S}}),[h,p,f,y,v,_,j,S,null==m?void 0:m.__raw]),E=P.useMemo(()=>({value:g}),[h,g]),M=P.useMemo(()=>({value:k}),[f,k]),z=P.useMemo(()=>({value:{organization:b}}),[v,b]);return P.createElement(c.SW.Provider,{value:u},P.createElement(w.pc.Provider,{value:d},P.createElement(w.IC.Provider,{value:E},P.createElement(w.TS,{...z.value},P.createElement(c.cy.Provider,{value:U},P.createElement(w.Rs.Provider,{value:M},n))))))}n=new WeakMap,i=new WeakMap,o=new WeakMap,s=new WeakMap,l=new WeakMap,a=new WeakMap,u=new WeakSet,d=function(){return new Promise(e=>{this.addOnLoaded(()=>e(this.clerkjs))})},g(ek,a);var ey=e=>{let t=P.useRef(ek.getOrCreateInstance(e)),[r,n]=P.useState(t.current.status);return P.useEffect(()=>{t.current.__unstable__updateProps({appearance:e.appearance})},[e.appearance]),P.useEffect(()=>{t.current.__unstable__updateProps({options:e})},[e.localization]),P.useEffect(()=>(t.current.on("status",n),()=>{t.current&&t.current.off("status",n),ek.clearInstance()}),[]),{isomorphicClerk:t.current,clerkStatus:r}},eb=function(e,t,r){let n=e.displayName||e.name||t||"Component",i=n=>(!function(e,t,r=1){P.useEffect(()=>{let n=B.get(e)||0;return n==r?c.sb.throw(t):(B.set(e,n+1),()=>{B.set(e,(B.get(e)||1)-1)})},[])}(t,r),P.createElement(e,{...n}));return i.displayName=`withMaxAllowedInstancesGuard(${n})`,i}(function(e){let{initialState:t,children:r,__internal_bypassMissingPublishableKey:n,...i}=e,{publishableKey:o="",Clerk:s}=i;return s||n||(o?o&&!(0,S.rA)(o)&&c.sb.throwInvalidPublishableKeyError({key:o}):c.sb.throwMissingPublishableKeyError()),P.createElement(ev,{initialState:t,isomorphicClerkOptions:i},r)},"ClerkProvider",c.yN);eb.displayName="ClerkProvider",(0,c.wV)({packageName:"@clerk/clerk-react"}),(0,v.kX)("@clerk/clerk-react")},19208:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RouterContext",{enumerable:!0,get:function(){return n}});let n=r(21510)._(r(41987)).default.createContext(null)},34577:(e,t,r)=>{"use strict";r.d(t,{Fj:()=>o,MC:()=>i,b_:()=>n});var n=()=>!1,i=()=>!1,o=()=>{try{return!0}catch{}return!1}},35466:(e,t,r)=>{"use strict";r.d(t,{M2:()=>k,MR:()=>f});var n={strict_mfa:{afterMinutes:10,level:"multi_factor"},strict:{afterMinutes:10,level:"second_factor"},moderate:{afterMinutes:60,level:"second_factor"},lax:{afterMinutes:1440,level:"second_factor"}},i=new Set(["first_factor","second_factor","multi_factor"]),o=new Set(["strict_mfa","strict","moderate","lax"]),s=e=>"number"==typeof e&&e>0,l=e=>i.has(e),a=e=>o.has(e),u=e=>e.replace(/^(org:)*/,"org:"),d=(e,t)=>{let{orgId:r,orgRole:n,orgPermissions:i}=t;return(e.role||e.permission)&&r&&n&&i?e.permission?i.includes(u(e.permission)):e.role?u(n)===u(e.role):null:null},c=(e,t)=>{let{org:r,user:n}=p(e),[i,o]=t.split(":"),s=o||i;return"org"===i?r.includes(s):"user"===i?n.includes(s):[...r,...n].includes(s)},h=(e,t)=>{let{features:r,plans:n}=t;return e.feature&&r?c(r,e.feature):e.plan&&n?c(n,e.plan):null},p=e=>{let t=e?e.split(",").map(e=>e.trim()):[];return{org:t.filter(e=>e.split(":")[0].includes("o")).map(e=>e.split(":")[1]),user:t.filter(e=>e.split(":")[0].includes("u")).map(e=>e.split(":")[1])}},m=e=>{if(!e)return!1;let t="string"==typeof e&&a(e),r="object"==typeof e&&l(e.level)&&s(e.afterMinutes);return(!!t||!!r)&&(e=>"string"==typeof e?n[e]:e).bind(null,e)},g=(e,{factorVerificationAge:t})=>{if(!e.reverification||!t)return null;let r=m(e.reverification);if(!r)return null;let{level:n,afterMinutes:i}=r(),[o,s]=t,l=-1!==o?i>o:null,a=-1!==s?i>s:null;switch(n){case"first_factor":return l;case"second_factor":return -1!==s?a:l;case"multi_factor":return -1===s?l:l&&a}},f=e=>t=>{if(!e.userId)return!1;let r=h(t,e),n=d(t,e),i=g(t,e);return[r||n,i].some(e=>null===e)?[r||n,i].some(e=>!0===e):[r||n,i].every(e=>!0===e)},k=({authObject:{sessionId:e,sessionStatus:t,userId:r,actor:n,orgId:i,orgRole:o,orgSlug:s,signOut:l,getToken:a,has:u,sessionClaims:d},options:{treatPendingAsSignedOut:c=!0}})=>void 0===e&&void 0===r?{isLoaded:!1,isSignedIn:void 0,sessionId:e,sessionClaims:void 0,userId:r,actor:void 0,orgId:void 0,orgRole:void 0,orgSlug:void 0,has:void 0,signOut:l,getToken:a}:null===e&&null===r?{isLoaded:!0,isSignedIn:!1,sessionId:e,userId:r,sessionClaims:null,actor:null,orgId:null,orgRole:null,orgSlug:null,has:()=>!1,signOut:l,getToken:a}:c&&"pending"===t?{isLoaded:!0,isSignedIn:!1,sessionId:null,userId:null,sessionClaims:null,actor:null,orgId:null,orgRole:null,orgSlug:null,has:()=>!1,signOut:l,getToken:a}:e&&d&&r&&i&&o?{isLoaded:!0,isSignedIn:!0,sessionId:e,sessionClaims:d,userId:r,actor:n||null,orgId:i,orgRole:o,orgSlug:s||null,has:u,signOut:l,getToken:a}:e&&d&&r&&!i?{isLoaded:!0,isSignedIn:!0,sessionId:e,sessionClaims:d,userId:r,actor:n||null,orgId:null,orgRole:null,orgSlug:null,has:u,signOut:l,getToken:a}:void 0},51300:(e,t,r)=>{"use strict";var n=e=>{let t=r=>{if(!r)return r;if(Array.isArray(r))return r.map(e=>"object"==typeof e||Array.isArray(e)?t(e):e);let n={...r};for(let r of Object.keys(n)){let i=e(r.toString());i!==r&&(n[i]=n[r],delete n[r]),"object"==typeof n[i]&&(n[i]=t(n[i]))}return n};return t};n(function(e){return e?e.replace(/[A-Z]/g,e=>`_${e.toLowerCase()}`):""}),n(function(e){return e?e.replace(/([-_][a-z])/g,e=>e.toUpperCase().replace(/-|_/,"")):""})},59448:(e,t,r)=>{"use strict";r.d(t,{T5:()=>P,nO:()=>_,_R:()=>b,kX:()=>y});var n=(e,t="5.70.0")=>{if(e)return e;let r=i(t);return r?"snapshot"===r?"5.70.0":r:o(t)},i=e=>e.trim().replace(/^v/,"").match(/-(.+?)(\.|$)/)?.[1],o=e=>e.trim().replace(/^v/,"").split(".")[0];function s(e){return e.startsWith("/")}var l=/\/$|\/\?|\/#/,a={initialDelay:125,maxDelayBetweenRetries:0,factor:2,shouldRetry:(e,t)=>t<5,retryImmediately:!1,jitter:!0},u=async e=>new Promise(t=>setTimeout(t,e)),d=(e,t)=>t?e*(1+Math.random()):e,c=e=>{let t=0,r=()=>{let r=e.initialDelay*Math.pow(e.factor,t);return r=d(r,e.jitter),Math.min(e.maxDelayBetweenRetries||r,r)};return async()=>{await u(r()),t++}},h=async(e,t={})=>{let r=0,{shouldRetry:n,initialDelay:i,maxDelayBetweenRetries:o,factor:s,retryImmediately:l,jitter:h}={...a,...t},p=c({initialDelay:i,maxDelayBetweenRetries:o,factor:s,jitter:h});for(;;)try{return await e()}catch(e){if(!n(e,++r))throw e;l&&1===r?await u(d(100,h)):await p()}};async function p(e="",t){let{async:r,defer:n,beforeLoad:i,crossOrigin:o,nonce:s}=t||{};return h(()=>new Promise((t,l)=>{e||l(Error("loadScript cannot be called without a src")),document&&document.body||l("loadScript cannot be called when document does not exist");let a=document.createElement("script");o&&a.setAttribute("crossorigin",o),a.async=r||!1,a.defer=n||!1,a.addEventListener("load",()=>{a.remove(),t(a)}),a.addEventListener("error",()=>{a.remove(),l()}),a.src=e,a.nonce=s,i?.(a),document.body.appendChild(a)}),{shouldRetry:(e,t)=>t<=5})}var m=r(2082),g=r(99701),f="Clerk: Failed to load Clerk",{isDevOrStagingUrl:k}=(0,g.RZ)(),v=(0,m._r)({packageName:"@clerk/shared"});function y(e){v.setPackageName({packageName:e})}var b=async e=>{let t=document.querySelector("script[data-clerk-js-script]");return t?new Promise((e,r)=>{t.addEventListener("load",()=>{e(t)}),t.addEventListener("error",()=>{r(f)})}):e?.publishableKey?p(_(e),{async:!0,crossOrigin:"anonymous",nonce:e.nonce,beforeLoad:j(e)}).catch(()=>{throw Error(f)}):void v.throwMissingPublishableKeyError()},_=e=>{let{clerkJSUrl:t,clerkJSVariant:r,clerkJSVersion:i,proxyUrl:o,domain:l,publishableKey:a}=e;if(t)return t;let u="";u=o&&function(e){var t;return!e||(t=e,/^http(s)?:\/\//.test(t||""))||s(e)}(o)?(function(e){return e?s(e)?new URL(e,window.location.origin).toString():e:""})(o).replace(/http(s)?:\/\//,""):l&&!k((0,g.q5)(a)?.frontendApi||"")?function(e){let t;if(!e)return"";if(e.match(/^(clerk\.)+\w*$/))t=/(clerk\.)*(?=clerk\.)/;else{if(e.match(/\.clerk.accounts/))return e;t=/^(clerk\.)*/gi}let r=e.replace(t,"");return`clerk.${r}`}(l):(0,g.q5)(a)?.frontendApi||"";let d=r?`${r.replace(/\.+$/,"")}.`:"",c=n(i);return`https://${u}/npm/@clerk/clerk-js@${c}/dist/clerk.${d}browser.js`},P=e=>{let t={};return e.publishableKey&&(t["data-clerk-publishable-key"]=e.publishableKey),e.proxyUrl&&(t["data-clerk-proxy-url"]=e.proxyUrl),e.domain&&(t["data-clerk-domain"]=e.domain),e.nonce&&(t.nonce=e.nonce),t},j=e=>t=>{let r=P(e);for(let e in r)t.setAttribute(e,r[e])};r(6097)},62954:(e,t,r)=>{"use strict";r.d(t,{d:()=>a});var n=r(15833),i=r(77575),o=r(90702),s=r(41987);let l=s.createContext(null);function a(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=(0,o.useRouter)(),r=s.useContext(l),a=r;return(r&&"then"in r&&(a=s.use(r)),"undefined"!=typeof window)?(0,n.As)({...a,...e}):t?(0,n.As)(e):(0,i.hP)({...a,...e})}},71721:(e,t,r)=>{"use strict";r.d(t,{cy:()=>p,SW:()=>g,EH:()=>K,iB:()=>F,Bl:()=>D,D:()=>S,wm:()=>U,sR:()=>w,n:()=>C,sb:()=>c,Wq:()=>v,yN:()=>k,kf:()=>j,vb:()=>P,wV:()=>h,Vo:()=>y,As:()=>N,ho:()=>l.ho,hP:()=>x,P6:()=>O,aU:()=>M,ld:()=>I,Wv:()=>A,UX:()=>E,Uw:()=>z,_I:()=>_,$n:()=>b,Q:()=>B});var n=r(2082);r(6097);var i=r(35466),o=r(82075);r(51300);var s=r(41987),l=r(5365),a=r(34577),u=new Set,d=(e,t,r)=>{let n=(0,a.MC)()||(0,a.Fj)(),i=r??e;u.has(i)||n||(u.add(i),console.warn(`Clerk - DEPRECATION WARNING: "${e}" is deprecated and will be removed in the next major release.
${t}`))},c=(0,n._r)({packageName:"@clerk/clerk-react"});function h(e){c.setMessages(e).setPackageName(e)}var[p,m]=(0,l.e3)("AuthContext"),g=l.ED,f=l.hQ,k="You've added multiple <ClerkProvider> components in your React component tree. Wrap your components in a single <ClerkProvider>.",v=e=>`You've passed multiple children components to <${e}/>. You can only pass a single child component or text.`,y="Unsupported usage of isSatellite, domain or proxyUrl. The usage of isSatellite, domain or proxyUrl as function is not supported in non-browser environments.",b="<UserProfile.Page /> component needs to be a direct child of `<UserProfile />` or `<UserButton />`.",_="<UserProfile.Link /> component needs to be a direct child of `<UserProfile />` or `<UserButton />`.",P="<OrganizationProfile.Page /> component needs to be a direct child of `<OrganizationProfile />` or `<OrganizationSwitcher />`.",j="<OrganizationProfile.Link /> component needs to be a direct child of `<OrganizationProfile />` or `<OrganizationSwitcher />`.",C=e=>`<${e} /> can only accept <${e}.Page /> and <${e}.Link /> as its children. Any other provided component will be ignored. Additionally, please ensure that the component is rendered in a client component.`,w=e=>`Missing props. <${e}.Page /> component requires the following props: url, label, labelIcon, alongside with children to be rendered inside the page.`,S=e=>`Missing props. <${e}.Link /> component requires the following props: url, label and labelIcon.`,O="<UserButton /> can only accept <UserButton.UserProfilePage />, <UserButton.UserProfileLink /> and <UserButton.MenuItems /> as its children. Any other provided component will be ignored. Additionally, please ensure that the component is rendered in a client component.",U="<UserButton.MenuItems /> component can only accept <UserButton.Action /> and <UserButton.Link /> as its children. Any other provided component will be ignored. Additionally, please ensure that the component is rendered in a client component.",E="<UserButton.MenuItems /> component needs to be a direct child of `<UserButton />`.",M="<UserButton.Action /> component needs to be a direct child of `<UserButton.MenuItems />`.",z="<UserButton.Link /> component needs to be a direct child of `<UserButton.MenuItems />`.",I="Missing props. <UserButton.Link /> component requires the following props: href, label and labelIcon.",A="Missing props. <UserButton.Action /> component requires the following props: label.",W=e=>{(0,l.Kz)(()=>{c.throwMissingClerkProviderError({source:e})})},T=e=>new Promise(t=>{let r=n=>{["ready","degraded"].includes(n)&&(t(),e.off("status",r))};e.on("status",r,{notify:!0})}),L=e=>async t=>(await T(e),e.session)?e.session.getToken(t):null,R=e=>async(...t)=>(await T(e),e.signOut(...t)),N=(e={})=>{var t,r;W("useAuth");let{treatPendingAsSignedOut:n,...i}=null!=e?e:{},l=m();void 0===l.sessionId&&void 0===l.userId&&(l=null!=i?i:{});let a=f(),u=(0,s.useCallback)(L(a),[a]),d=(0,s.useCallback)(R(a),[a]);return null==(t=a.telemetry)||t.record((0,o.FJ)("useAuth",{treatPendingAsSignedOut:n})),x({...l,getToken:u,signOut:d},{treatPendingAsSignedOut:null!=n?n:null==(r=a.__internal_getOption)?void 0:r.call(a,"treatPendingAsSignedOut")})};function x(e,{treatPendingAsSignedOut:t=!0}={}){let{userId:r,orgId:n,orgRole:o,has:l,signOut:a,getToken:u,orgPermissions:d,factorVerificationAge:h,sessionClaims:p}=null!=e?e:{},m=(0,s.useCallback)(e=>l?l(e):(0,i.MR)({userId:r,orgId:n,orgRole:o,orgPermissions:d,factorVerificationAge:h,features:(null==p?void 0:p.fea)||"",plans:(null==p?void 0:p.pla)||""})(e),[l,r,n,o,d,h]),g=(0,i.M2)({authObject:{...e,getToken:u,signOut:a,has:m},options:{treatPendingAsSignedOut:t}});return g||c.throw("Invalid state. Feel free to submit a bug or reach out to support here: https://clerk.com/support")}var B=(e,t)=>{let r=("string"==typeof t?t:null==t?void 0:t.component)||e.displayName||e.name||"Component";e.displayName=r;let n="string"==typeof t?void 0:t,i=t=>{W(r||"withClerk");let i=f();return i.loaded||(null==n?void 0:n.renderWhileLoading)?s.createElement(e,{...t,component:r,clerk:i}):null};return i.displayName=`withClerk(${r})`,i},F=({children:e,treatPendingAsSignedOut:t})=>{W("SignedIn");let{userId:r}=N({treatPendingAsSignedOut:t});return r?e:null},D=({children:e,treatPendingAsSignedOut:t})=>{W("SignedOut");let{userId:r}=N({treatPendingAsSignedOut:t});return null===r?e:null},K=({children:e,fallback:t,treatPendingAsSignedOut:r,...n})=>{W("Protect");let{isLoaded:i,has:o,userId:s}=N({treatPendingAsSignedOut:r});if(!i)return null;let l=null!=t?t:null;return s?"function"==typeof n.condition?n.condition(o)?e:l:n.role||n.permission||n.feature||n.plan?o(n)?e:l:e:l};B(({clerk:e,...t})=>{let{client:r,session:n}=e,i=r.signedInSessions?r.signedInSessions.length>0:r.activeSessions&&r.activeSessions.length>0;return s.useEffect(()=>{null===n&&i?e.redirectToAfterSignOut():e.redirectToSignIn(t)},[]),null},"RedirectToSignIn"),B(({clerk:e,...t})=>(s.useEffect(()=>{e.redirectToSignUp(t)},[]),null),"RedirectToSignUp"),B(({clerk:e})=>(s.useEffect(()=>{d("RedirectToUserProfile","Use the `redirectToUserProfile()` method instead."),e.redirectToUserProfile()},[]),null),"RedirectToUserProfile"),B(({clerk:e})=>(s.useEffect(()=>{d("RedirectToOrganizationProfile","Use the `redirectToOrganizationProfile()` method instead."),e.redirectToOrganizationProfile()},[]),null),"RedirectToOrganizationProfile"),B(({clerk:e})=>(s.useEffect(()=>{d("RedirectToCreateOrganization","Use the `redirectToCreateOrganization()` method instead."),e.redirectToCreateOrganization()},[]),null),"RedirectToCreateOrganization"),B(({clerk:e,...t})=>(s.useEffect(()=>{e.handleRedirectCallback(t)},[]),null),"AuthenticateWithRedirectCallback")},73755:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useRouter",{enumerable:!0,get:function(){return o}});let n=r(41987),i=r(19208);function o(){return(0,n.useContext)(i.RouterContext)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},77575:(e,t,r)=>{"use strict";r.d(t,{T5:()=>i.T5,hP:()=>n.hP,kX:()=>i.kX,nO:()=>i.nO,wV:()=>n.wV});var n=r(71721),i=r(59448)},82075:(e,t,r)=>{"use strict";r.d(t,{FJ:()=>y}),r(51300);var n,i,o,s,l,a,u,d,c,h,p,m,g,f,k,v=r(6097);r(40459),n=new WeakMap,i=new WeakMap,o=new WeakSet,s=function(e){let{sk:t,pk:r,payload:n,...i}=e,o={...n,...i};return JSON.stringify(Object.keys({...n,...i}).sort().map(e=>o[e]))},l=function(){let e=localStorage.getItem((0,v.S7)(this,n));return e?JSON.parse(e):{}},a=function(){if("undefined"==typeof window)return!1;let e=window.localStorage;if(!e)return!1;try{let t="test";return e.setItem(t,t),e.removeItem(t),!0}catch(t){return t instanceof DOMException&&("QuotaExceededError"===t.name||"NS_ERROR_DOM_QUOTA_REACHED"===t.name)&&e.length>0&&e.removeItem((0,v.S7)(this,n)),!1}};u=new WeakMap,d=new WeakMap,c=new WeakMap,h=new WeakMap,p=new WeakMap,m=new WeakSet,g=function(e,t){let r=Math.random();return!!(r<=(0,v.S7)(this,u).samplingRate&&(void 0===t||r<=t))&&!(0,v.S7)(this,d).isEventThrottled(e)},f=function(){fetch(new URL("/v1/event",(0,v.S7)(this,u).endpoint),{method:"POST",body:JSON.stringify({events:(0,v.S7)(this,h)}),headers:{"Content-Type":"application/json"}}).catch(()=>void 0).then(()=>{(0,v.OV)(this,h,[])}).catch(()=>void 0)},k=function(){let e={name:(0,v.S7)(this,c).sdk,version:(0,v.S7)(this,c).sdkVersion};return"undefined"!=typeof window&&window.Clerk&&(e={...e,...window.Clerk.constructor.sdkMetadata}),e};function y(e,t){return{event:"METHOD_CALLED",payload:{method:e,...t}}}},90702:(e,t,r)=>{e.exports=r(73755)},99701:(e,t,r)=>{"use strict";r.d(t,{RZ:()=>a,rA:()=>l,q5:()=>s});var n=e=>"undefined"!=typeof atob&&"function"==typeof atob?atob(e):"undefined"!=typeof global&&global.Buffer?new global.Buffer(e,"base64").toString():e,i=[".lcl.dev",".stg.dev",".lclstage.dev",".stgstage.dev",".dev.lclclerk.com",".stg.lclclerk.com",".accounts.lclclerk.com","accountsstage.dev","accounts.dev"],o="pk_live_";function s(e,t={}){if(!(e=e||"")||!l(e)){if(t.fatal&&!e)throw Error("Publishable key is missing. Ensure that your publishable key is correctly configured. Double-check your environment configuration for your keys, or access them here: https://dashboard.clerk.com/last-active?path=api-keys");if(t.fatal&&!l(e))throw Error("Publishable key not valid.");return null}let r=e.startsWith(o)?"production":"development",i=n(e.split("_")[2]);return i=i.slice(0,-1),t.proxyUrl?i=t.proxyUrl:"development"!==r&&t.domain&&t.isSatellite&&(i=`clerk.${t.domain}`),{instanceType:r,frontendApi:i}}function l(e=""){try{let t=e.startsWith(o)||e.startsWith("pk_test_"),r=n(e.split("_")[2]||"").endsWith("$");return t&&r}catch{return!1}}function a(){let e=new Map;return{isDevOrStagingUrl:t=>{if(!t)return!1;let r="string"==typeof t?t:t.hostname,n=e.get(r);return void 0===n&&(n=i.some(e=>r.endsWith(e)),e.set(r,n)),n}}}}}]);