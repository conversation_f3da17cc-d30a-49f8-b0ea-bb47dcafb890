(()=>{var e={};e.id=7735,e.ids=[7735],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},11455:(e,t,r)=>{"use strict";async function n(e){return console.warn("get<PERSON><PERSON> called with legacy implementation - needs Convex migration"),[]}async function i(){return console.warn("createGuest<PERSON><PERSON> called with legacy implementation - needs Convex migration"),[{id:`guest-${Date.now()}`,email:null,password:null}]}async function s(){return console.warn("saveChat called with legacy implementation - needs Convex migration"),null}async function a(){return console.warn("deleteChatById called with legacy implementation - needs Convex migration"),null}async function o(){return console.warn("getChatsByUserId called with legacy implementation - needs Convex migration"),[]}async function l(){return console.warn("getChatById called with legacy implementation - needs Convex migration"),null}async function c(){return console.warn("saveMessages called with legacy implementation - needs Convex migration"),null}async function u(){return console.warn("getMessagesByChatId called with legacy implementation - needs Convex migration"),[]}async function h(){return console.warn("voteMessage called with legacy implementation - needs Convex migration"),null}async function p(){return console.warn("getVotesByChatId called with legacy implementation - needs Convex migration"),[]}async function f(){return console.warn("saveDocument called with legacy implementation - needs Convex migration"),null}async function d(){return console.warn("getDocumentById called with legacy implementation - needs Convex migration"),null}async function m(){return console.warn("deleteDocumentsByIdAfterTimestamp called with legacy implementation - needs Convex migration"),null}async function g(){return console.warn("saveSuggestions called with legacy implementation - needs Convex migration"),null}async function b(){return console.warn("getSuggestionsByDocumentId called with legacy implementation - needs Convex migration"),[]}async function x(){return console.warn("getMessageById called with legacy implementation - needs Convex migration"),null}async function y(){return console.warn("deleteMessagesByChatIdAfterTimestamp called with legacy implementation - needs Convex migration"),null}async function v(){return console.warn("updateChatVisiblityById called with legacy implementation - needs Convex migration"),null}async function S(){return console.warn("getStreamIdsByChatId called with legacy implementation - needs Convex migration"),[]}async function _(){return console.warn("getMessageCountByUserId called with legacy implementation - needs Convex migration"),0}async function w(){return console.warn("createStreamId called with legacy implementation - needs Convex migration"),`stream-${Date.now()}`}async function k(){return console.warn("getDocumentsById called with legacy implementation - needs Convex migration"),[]}r.d(t,{$:()=>v,Ci:()=>h,Lz:()=>o,M7:()=>w,Nm:()=>S,TJ:()=>l,W8:()=>i,_L:()=>d,bd:()=>f,de:()=>u,iB:()=>m,kA:()=>x,mV:()=>p,q1:()=>_,qQ:()=>a,tw:()=>b,wA:()=>y,wz:()=>n,xt:()=>k,yM:()=>c,yd:()=>s,zL:()=>g})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29727:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page-experimental.runtime.prod.js")},46871:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route-experimental.runtime.prod.js")},48106:(e,t,r)=>{"use strict";e.exports=r(46871)},52335:(e,t,r)=>{"use strict";r.d(t,{fG:()=>l,LO:()=>c,j2:()=>u,Jv:()=>h});var n=r(89131),i=r(50109),s=r(9852),a=r(11455),o=r(73241);let{handlers:{GET:l,POST:c},auth:u,signIn:h,signOut:p}=(0,i.Ay)({...{pages:{signIn:"/login",newUser:"/"},providers:[],callbacks:{}},providers:[(0,s.A)({credentials:{},async authorize({email:e,password:t}){let r=await (0,a.wz)(e);if(0===r.length)return await (0,n.UD)(t,o.vt),null;let[i]=r;return i.password?await (0,n.UD)(t,i.password)?{...i,type:"regular"}:null:(await (0,n.UD)(t,o.vt),null)}}),(0,s.A)({id:"guest",credentials:{},async authorize(){let[e]=await (0,a.W8)();return{...e,type:"guest"}}})],callbacks:{jwt:async({token:e,user:t})=>(t&&(e.id=t.id,e.type=t.type),e),session:async({session:e,token:t})=>(e.user&&(e.user.id=t.id,e.user.type=t.type),e)}})},55511:e=>{"use strict";e.exports=require("crypto")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73241:(e,t,r)=>{"use strict";r.d(t,{vt:()=>a,b_:()=>i,Fj:()=>n,MC:()=>s}),r(89131);let n=!0,i=!1,s=!!(process.env.PLAYWRIGHT_TEST_BASE_URL||process.env.PLAYWRIGHT||process.env.CI_PLAYWRIGHT),a="$2b$10$K1V5qz0cZGaJGDwQO4CQuu4Xr5bZnQfCqFkC7l0qoF5zVhsVz7/.2"},77598:e=>{"use strict";e.exports=require("node:crypto")},80408:()=>{},87032:()=>{},87343:(e,t,r)=>{"use strict";let n,i,s,a,o,l,c,u,h,p,f,d,m,g,b,x,y,v,S,_,w,k,C,E,O,I,F,T,R;r.r(t),r.d(t,{patchFetch:()=>VN,routeModule:()=>VI,serverHooks:()=>VR,workAsyncStorage:()=>VF,workUnitAsyncStorage:()=>VT});var N,A,j={};r.r(j),r.d(j,{LoggerTypeId:()=>Wp,add:()=>Wd,addEffect:()=>Wm,addScoped:()=>Wg,batched:()=>WS,defaultLogger:()=>WP,filterLogLevel:()=>Wy,isLogger:()=>WQ,json:()=>WW,jsonLogger:()=>WL,logFmt:()=>WV,logfmtLogger:()=>WU,make:()=>Wf,map:()=>Wv,mapInput:()=>Wb,mapInputOptions:()=>Wx,minimumLogLevel:()=>WZ,none:()=>WC,pretty:()=>WG,prettyLogger:()=>WB,prettyLoggerDefault:()=>WJ,remove:()=>WE,replace:()=>WO,replaceEffect:()=>WI,replaceScoped:()=>WF,simple:()=>WT,stringLogger:()=>Wq,structured:()=>WY,structuredLogger:()=>WH,succeed:()=>WR,sync:()=>WN,test:()=>WA,tracerLogger:()=>WK,withConsoleError:()=>Wk,withConsoleLog:()=>W_,withLeveledConsole:()=>Ww,withMinimumLogLevel:()=>Wj,withSpanAnnotations:()=>WM,zip:()=>Wz,zipLeft:()=>WD,zipRight:()=>W$});var M={};r.r(M),r.d(M,{GET:()=>VE,POST:()=>VO});var z=r(48106),D=r(48819),$=r(12050),P="7.7.3";let L=e=>{console.warn(`⚠️ [uploadthing][deprecated] ${e}`)},U=function(e,t){if("function"==typeof e)return function(){return e(arguments)?t.apply(this,arguments):e=>t(e,...arguments)};switch(e){case 0:case 1:throw RangeError(`Invalid arity ${e}`);case 2:return function(e,r){return arguments.length>=2?t(e,r):function(r){return t(r,e)}};case 3:return function(e,r,n){return arguments.length>=3?t(e,r,n):function(n){return t(n,e,r)}};case 4:return function(e,r,n,i){return arguments.length>=4?t(e,r,n,i):function(i){return t(i,e,r,n)}};case 5:return function(e,r,n,i,s){return arguments.length>=5?t(e,r,n,i,s):function(s){return t(s,e,r,n,i)}};default:return function(){if(arguments.length>=e)return t.apply(this,arguments);let r=arguments;return function(e){return t(e,...r)}}}},q=e=>e,B=e=>()=>e,J=B(!0),H=B(!1),K=B(void 0);function W(e,t,r,n,i,s,a,o,l){switch(arguments.length){case 1:return e;case 2:return t(e);case 3:return r(t(e));case 4:return n(r(t(e)));case 5:return i(n(r(t(e))));case 6:return s(i(n(r(t(e)))));case 7:return a(s(i(n(r(t(e))))));case 8:return o(a(s(i(n(r(t(e)))))));case 9:return l(o(a(s(i(n(r(t(e))))))));default:{let e=arguments[0];for(let t=1;t<arguments.length;t++)e=arguments[t](e);return e}}}let V=()=>"3.16.8",G=`effect/GlobalValue/globalStoreId/${V()}`,Y=(e,t)=>(n||(globalThis[G]??=new Map,n=globalThis[G]),n.has(e)||n.set(e,t()),n.get(e)),Z=e=>"string"==typeof e,Q=e=>"number"==typeof e,X=e=>"boolean"==typeof e,ee=e=>"bigint"==typeof e,et=e=>"symbol"==typeof e,er=e=>"function"==typeof e,en=e=>void 0===e,ei=e=>void 0!==e,es=e=>null!==e,ea=e=>!1,eo=e=>"object"==typeof e&&null!==e,el=e=>eo(e)||er(e),ec=U(2,(e,t)=>el(e)&&t in e),eu=U(2,(e,t)=>ec(e,"_tag")&&e._tag===t),eh=e=>null==e,ep=e=>null!=e,ef=e=>e instanceof Uint8Array,ed=e=>e instanceof Date,em=e=>ec(e,Symbol.iterator),eg=e=>eo(e)&&!Array.isArray(e),eb=e=>ec(e,"then")&&er(e.then),ex=((e,t)=>r=>e(r)||t(r),e=>`BUG: ${e} - please report an issue at https://github.com/Effect-TS/effect/issues`);Symbol.iterator;class ey{constructor(e){this.called=!1,this.self=e}next(e){return this.called?{value:e,done:!0}:(this.called=!0,{value:this.self,done:!1})}return(e){return{value:e,done:!0}}throw(e){throw e}[Symbol.iterator](){return new ey(this.self)}}class ev{constructor(e,t,r,n){return eh(t)&&eh(e)?(t=0xffffffff*Math.random()>>>0,e=0):eh(t)&&(t=e,e=0),eh(n)&&eh(r)?(n=this._state?this._state[3]:0xf767814f,r=this._state?this._state[2]:0x14057b7e):eh(n)&&(n=r,r=0),this._state=new Int32Array([0,0,r>>>0,(1|(n||0))>>>0]),this._next(),eS(this._state,this._state[0],this._state[1],e>>>0,t>>>0),this._next(),this}getState(){return[this._state[0],this._state[1],this._state[2],this._state[3]]}setState(e){this._state[0]=e[0],this._state[1]=e[1],this._state[2]=e[2],this._state[3]=1|e[3]}integer(e){return Math.round(this.number()*Number.MAX_SAFE_INTEGER)%e}number(){return(0x8000000*((0x3ffffff&this._next())*1)+(0x7ffffff&this._next())*1)/0x20000000000000}_next(){var e,t,r,n;let i,s,a,o,l=this._state[0]>>>0,c=this._state[1]>>>0;e=this._state,t=l,n=0x4c957f2d,i=((r=c)>>>16)*32557>>>0,s=(65535&r)*(n>>>16)>>>0,a=(65535&r)*(65535&n)>>>0,o=(r>>>16)*(n>>>16)+((s>>>16)+(i>>>16))>>>0,(a=a+(s=s<<16>>>0)>>>0)>>>0<s>>>0&&(o=o+1>>>0),(a=a+(i=i<<16>>>0)>>>0)>>>0<i>>>0&&(o=o+1>>>0),o=(o=o+Math.imul(r,0x5851f42d)>>>0)+Math.imul(t,n)>>>0,e[0]=o,e[1]=a,eS(this._state,this._state[0],this._state[1],this._state[2],this._state[3]);let u=l>>>18,h=(c>>>18|l<<14)>>>0;u=(u^l)>>>0;let p=((h=(h^c)>>>0)>>>27|u<<5)>>>0,f=l>>>27;return(p>>>f|p<<((-f>>>0&31)>>>0))>>>0}}function eS(e,t,r,n,i){let s=t+n>>>0,a=r+i>>>0;a>>>0<r>>>0&&(s=s+1|0),e[0]=s,e[1]=a}let e_=Symbol.for("effect/Utils/YieldWrap");class ew{#e;constructor(e){this.#e=e}[e_](){return this.#e}}function ek(e){if("object"==typeof e&&null!==e&&e_ in e)return e[e_]();throw Error(ex("yieldWrapGet"))}let eC=Y("effect/Utils/isStructuralRegion",()=>({enabled:!1,tester:void 0})),eE={effect_internal_function:e=>e()},eO=eE.effect_internal_function(()=>Error().stack)?.includes("effect_internal_function")===!0?eE.effect_internal_function:e=>e(),eI=(function*(){}).constructor,eF=e=>el(e)&&e.constructor===eI,eT=Y(Symbol.for("effect/Hash/randomHashCache"),()=>new WeakMap),eR=Symbol.for("effect/Hash"),eN=e=>{if(!0===eC.enabled)return 0;switch(typeof e){case"number":return eD(e);case"bigint":return e$(e.toString(10));case"boolean":case"symbol":return e$(String(e));case"string":return e$(e);case"undefined":return e$("undefined");case"function":case"object":if(null===e)return e$("null");if(e instanceof Date)return eN(e.toISOString());if(e instanceof URL)return eN(e.href);else if(ez(e))return e[eR]();else return eA(e);default:throw Error(`BUG: unhandled typeof ${typeof e} - please report an issue at https://github.com/Effect-TS/effect/issues`)}},eA=e=>(eT.has(e)||eT.set(e,eD(Math.floor(Math.random()*Number.MAX_SAFE_INTEGER))),eT.get(e)),ej=e=>t=>53*t^e,eM=e=>0xbfffffff&e|e>>>1&0x40000000,ez=e=>ec(e,eR),eD=e=>{if(e!=e||e===1/0)return 0;let t=0|e;for(t!==e&&(t^=0xffffffff*e);e>0xffffffff;)t^=e/=0xffffffff;return eM(t)},e$=e=>{let t=5381,r=e.length;for(;r;)t=33*t^e.charCodeAt(--r);return eM(t)},eP=(e,t)=>{let r=12289;for(let n=0;n<t.length;n++)r^=W(e$(t[n]),ej(eN(e[t[n]])));return eM(r)},eL=e=>eP(e,Object.keys(e)),eU=e=>{let t=6151;for(let r=0;r<e.length;r++)t=W(t,ej(eN(e[r])));return eM(t)},eq=function(){if(1==arguments.length){let e=arguments[0];return function(t){return Object.defineProperty(e,eR,{value:()=>t,enumerable:!1}),t}}let e=arguments[0],t=arguments[1];return Object.defineProperty(e,eR,{value:()=>t,enumerable:!1}),t},eB=Symbol.for("effect/Equal");function eJ(){return 1==arguments.length?e=>eH(e,arguments[0]):eH(arguments[0],arguments[1])}function eH(e,t){if(e===t)return!0;let r=typeof e;if(r!==typeof t)return!1;if("object"===r||"function"===r){if(null!==e&&null!==t){if(eK(e)&&eK(t))if(eN(e)===eN(t)&&e[eB](t))return!0;else return!!eC.enabled&&!!eC.tester&&eC.tester(e,t);else if(e instanceof Date&&t instanceof Date)return e.toISOString()===t.toISOString();else if(e instanceof URL&&t instanceof URL)return e.href===t.href}if(eC.enabled){if(Array.isArray(e)&&Array.isArray(t))return e.length===t.length&&e.every((e,r)=>eH(e,t[r]));if(Object.getPrototypeOf(e)===Object.prototype&&Object.getPrototypeOf(e)===Object.prototype){let r=Object.keys(e),n=Object.keys(t);if(r.length===n.length){for(let n of r)if(!(n in t&&eH(e[n],t[n])))return!!eC.tester&&eC.tester(e,t);return!0}}return!!eC.tester&&eC.tester(e,t)}}return!!eC.enabled&&!!eC.tester&&eC.tester(e,t)}let eK=e=>ec(e,eB),eW=()=>eJ,eV=Symbol.for("nodejs.util.inspect.custom"),eG=e=>{try{if(ec(e,"toJSON")&&er(e.toJSON)&&0===e.toJSON.length)return e.toJSON();if(Array.isArray(e))return e.map(eG)}catch{return{}}return e4(e)},eY=e=>JSON.stringify(e,null,2),eZ={toJSON(){return eG(this)},[eV](){return this.toJSON()},toString(){return eY(this.toJSON())}};class eQ{[eV](){return this.toJSON()}toString(){return eY(this.toJSON())}}let eX=(e,t=2)=>{if("string"==typeof e)return e;try{return"object"==typeof e?e0(e,t):String(e)}catch{return String(e)}},e0=(e,t)=>{let r=[],n=JSON.stringify(e,(e,t)=>"object"==typeof t&&null!==t?r.includes(t)?void 0:r.push(t)&&(void 0!==e3.fiberRefs&&e2(t)?t[e1](e3.fiberRefs):t):t,t);return r=void 0,n},e1=Symbol.for("effect/Inspectable/Redactable"),e2=e=>"object"==typeof e&&null!==e&&e1 in e,e3=Y("effect/Inspectable/redactableState",()=>({fiberRefs:void 0})),e5=(e,t)=>{let r=e3.fiberRefs;e3.fiberRefs=e;try{return t()}finally{e3.fiberRefs=r}},e4=e=>e2(e)&&void 0!==e3.fiberRefs?e[e1](e3.fiberRefs):e,e6=(e,t)=>{switch(t.length){case 0:return e;case 1:return t[0](e);case 2:return t[1](t[0](e));case 3:return t[2](t[1](t[0](e)));case 4:return t[3](t[2](t[1](t[0](e))));case 5:return t[4](t[3](t[2](t[1](t[0](e)))));case 6:return t[5](t[4](t[3](t[2](t[1](t[0](e))))));case 7:return t[6](t[5](t[4](t[3](t[2](t[1](t[0](e)))))));case 8:return t[7](t[6](t[5](t[4](t[3](t[2](t[1](t[0](e))))))));case 9:return t[8](t[7](t[6](t[5](t[4](t[3](t[2](t[1](t[0](e)))))))));default:{let r=e;for(let e=0,n=t.length;e<n;e++)r=t[e](r);return r}}},e8="Async",e7="Commit",e9="Failure",te="OnFailure",tt="OnSuccess",tr="OnSuccessAndFailure",tn="Success",ti="Sync",ts="UpdateRuntimeFlags",ta="While",to="Iterator",tl="WithRuntime",tc="Yield",tu="RevertFlags",th=Symbol.for("effect/Effect"),tp=Symbol.for("effect/Stream"),tf=Symbol.for("effect/Sink"),td=Symbol.for("effect/Channel"),tm={_R:e=>e,_E:e=>e,_A:e=>e,_V:V()},tg={[th]:tm,[tp]:tm,[tf]:{_A:e=>e,_In:e=>e,_L:e=>e,_E:e=>e,_R:e=>e},[td]:{_Env:e=>e,_InErr:e=>e,_InElem:e=>e,_InDone:e=>e,_OutErr:e=>e,_OutElem:e=>e,_OutDone:e=>e},[eB](e){return this===e},[eR](){return eq(this,eA(this))},[Symbol.iterator](){return new ey(new ew(this))},pipe(){return e6(this,arguments)}},tb={[eR](){return eq(this,eL(this))},[eB](e){let t=Object.keys(this),r=Object.keys(e);if(t.length!==r.length)return!1;for(let r of t)if(!(r in e&&eJ(this[r],e[r])))return!1;return!0}},tx={...tg,_op:e7},ty={...tx,...tb},tv=function(){function e(){}return e.prototype=tx,e}(),tS=function(){function e(){}return e.prototype=ty,e}(),t_=Symbol.for("effect/Option"),tw={...tg,[t_]:{_A:e=>e},[eV](){return this.toJSON()},toString(){return eY(this.toJSON())}},tk=Object.assign(Object.create(tw),{_tag:"Some",_op:"Some",[eB](e){return tO(e)&&tF(e)&&eJ(this.value,e.value)},[eR](){return eq(this,ej(eN(this._tag))(eN(this.value)))},toJSON(){return{_id:"Option",_tag:this._tag,value:eG(this.value)}}}),tC=eN("None"),tE=Object.assign(Object.create(tw),{_tag:"None",_op:"None",[eB]:e=>tO(e)&&tI(e),[eR]:()=>tC,toJSON(){return{_id:"Option",_tag:this._tag}}}),tO=e=>ec(e,t_),tI=e=>"None"===e._tag,tF=e=>"Some"===e._tag,tT=Object.create(tE),tR=e=>{let t=Object.create(tk);return t.value=e,t},tN=Symbol.for("effect/Context/Tag"),tA=Symbol.for("effect/Context/Reference"),tj=Symbol.for("effect/STM"),tM={...tg,_op:"Tag",[tj]:tm,[tN]:{_Service:e=>e,_Identifier:e=>e},toString(){return eY(this.toJSON())},toJSON(){return{_id:"Tag",key:this.key,stack:this.stack}},[eV](){return this.toJSON()},of:e=>e,context(e){return tJ(this,e)}},tz={...tM,[tA]:tA},tD=Symbol.for("effect/Context"),t$={[tD]:{_Services:e=>e},[eB](e){if(tU(e)&&this.unsafeMap.size===e.unsafeMap.size){for(let t of this.unsafeMap.keys())if(!e.unsafeMap.has(t)||!eJ(this.unsafeMap.get(t),e.unsafeMap.get(t)))return!1;return!0}return!1},[eR](){return eq(this,eD(this.unsafeMap.size))},pipe(){return e6(this,arguments)},toString(){return eY(this.toJSON())},toJSON(){return{_id:"Context",services:Array.from(this.unsafeMap).map(eG)}},[eV](){return this.toJSON()}},tP=e=>{let t=Object.create(t$);return t.unsafeMap=e,t},tL=e=>{let t=Error(`Service not found${e.key?`: ${String(e.key)}`:""}`);if(e.stack){let r=e.stack.split("\n");if(r.length>2){let e=r[2].match(/at (.*)/);e&&(t.message=t.message+` (defined at ${e[1]})`)}}if(t.stack){let e=t.stack.split("\n");e.splice(1,3),t.stack=e.join("\n")}return t},tU=e=>ec(e,tD),tq=e=>ec(e,tA),tB=tP(new Map),tJ=(e,t)=>tP(new Map([[e.key,t]])),tH=U(3,(e,t,r)=>{let n=new Map(e.unsafeMap);return n.set(t.key,r),tP(n)}),tK=Y("effect/Context/defaultValueCache",()=>new Map),tW=e=>{if(tK.has(e.key))return tK.get(e.key);let t=e.defaultValue();return tK.set(e.key,t),t},tV=(e,t)=>e.unsafeMap.has(t.key)?e.unsafeMap.get(t.key):tW(t),tG=U(2,(e,t)=>{if(!e.unsafeMap.has(t.key)){if(tA in t)return tW(t);throw tL(t)}return e.unsafeMap.get(t.key)}),tY=U(2,(e,t)=>e.unsafeMap.has(t.key)?tR(e.unsafeMap.get(t.key)):tq(t)?tR(tW(t)):tT),tZ=U(2,(e,t)=>{let r=new Map(e.unsafeMap);for(let[e,n]of t.unsafeMap)r.set(e,n);return tP(r)}),tQ=e=>{let t=Error.stackTraceLimit;Error.stackTraceLimit=2;let r=Error();Error.stackTraceLimit=t;let n=Object.create(tM);return Object.defineProperty(n,"stack",{get:()=>r.stack}),n.key=e,n},tX=e=>ec(e,tN),t0=()=>tB,t1=e=>()=>{let t=Error.stackTraceLimit;Error.stackTraceLimit=2;let r=Error();function n(){}return Error.stackTraceLimit=t,Object.setPrototypeOf(n,tM),n.key=e,Object.defineProperty(n,"stack",{get:()=>r.stack}),n},t2=()=>(e,t)=>{let r=Error.stackTraceLimit;Error.stackTraceLimit=2;let n=Error();function i(){}return Error.stackTraceLimit=r,Object.setPrototypeOf(i,tz),i.key=e,i.defaultValue=t.defaultValue,Object.defineProperty(i,"stack",{get:()=>n.stack}),i},t3=tv,t5=tS;class t4 extends t3{}class t6 extends t5{}let t8=Symbol.for("effect/Micro"),t7=Symbol.for("effect/Micro/MicroExit"),t9=Symbol.for("effect/Micro/MicroCause"),re={_E:q};class rt extends globalThis.Error{constructor(e,t,r){let n,i,s,a=`MicroCause.${e}`;if(t instanceof globalThis.Error){n=`(${a}) ${t.name}`;let e=(i=t.message).split("\n").length;s=t.stack?`(${a}) ${t.stack.split("\n").slice(0,e+3).join("\n")}`:`${n}: ${i}`}else n=a,i=eX(t,0),s=`${n}: ${i}`;r.length>0&&(s+=`
    ${r.join("\n    ")}`),super(i),this._tag=e,this.traces=r,this[t9]=re,this.name=n,this.stack=s}pipe(){return e6(this,arguments)}toString(){return this.stack}[eV](){return this.stack}}class rr extends rt{constructor(e,t=[]){super("Fail",e,t),this.error=e}}let rn=(e,t=[])=>new rr(e,t);class ri extends rt{constructor(e,t=[]){super("Die",e,t),this.defect=e}}let rs=(e,t=[])=>new ri(e,t);class ra extends rt{constructor(e=[]){super("Interrupt","interrupted",e)}}let ro=(e=[])=>new ra(e),rl=e=>"Fail"===e._tag,rc=e=>"Interrupt"===e._tag,ru=U(2,(e,t)=>{let r=[...e.traces,t];switch(e._tag){case"Die":return rs(e.defect,r);case"Interrupt":return ro(r);case"Fail":return rn(e.error,r)}}),rh=Symbol.for("effect/Micro/MicroFiber"),rp={_A:q,_E:q};class rf{constructor(e,t=!0){this._stack=[],this._observers=[],this.currentOpCount=0,this._interrupted=!1,this._yielded=void 0,this.context=e,this.interruptible=t,this[rh]=rp}getRef(e){return tV(this.context,e)}addObserver(e){return this._exit?(e(this._exit),K):(this._observers.push(e),()=>{let t=this._observers.indexOf(e);t>=0&&this._observers.splice(t,1)})}unsafeInterrupt(){!this._exit&&(this._interrupted=!0,this.interruptible&&this.evaluate(rV))}unsafePoll(){return this._exit}evaluate(e){if(this._exit)return;if(void 0!==this._yielded){let e=this._yielded;this._yielded=void 0,e()}let t=this.runLoop(e);if(t===rS)return;let r=rd.interruptChildren&&rd.interruptChildren(this);if(void 0!==r)return this.evaluate(rB(r,()=>t));this._exit=t;for(let e=0;e<this._observers.length;e++)this._observers[e](t);this._observers.length=0}runLoop(e){let t=!1,r=e;this.currentOpCount=0;try{for(;;){if(this.currentOpCount++,!t&&this.getRef(r3).shouldYield(this)){t=!0;let e=r;r=rB(rN,()=>e)}if((r=r[rb](this))===rS){let e=this._yielded;if(t7 in e)return this._yielded=void 0,e;return rS}}}catch(e){if(!ec(r,rb))return rG(`MicroFiber.runLoop: Not a valid effect: ${String(r)}`);return rG(e)}}getCont(e){for(;;){let t=this._stack.pop();if(!t)return;let r=t[rv]&&t[rv](this);if(r)return{[e]:r};if(t[e])return t}}yieldWith(e){return this._yielded=e,rS}children(){return this._children??=new Set}}let rd=Y("effect/Micro/fiberMiddleware",()=>({interruptChildren:void 0})),rm=Symbol.for("effect/Micro/identifier"),rg=Symbol.for("effect/Micro/args"),rb=Symbol.for("effect/Micro/evaluate"),rx=Symbol.for("effect/Micro/successCont"),ry=Symbol.for("effect/Micro/failureCont"),rv=Symbol.for("effect/Micro/ensureCont"),rS=Symbol.for("effect/Micro/Yield"),r_={...tg,_op:"Micro",[t8]:{_A:q,_E:q,_R:q},pipe(){return e6(this,arguments)},[Symbol.iterator](){return new ey(new ew(this))},toJSON(){return{_id:"Micro",op:this[rm],...rg in this?{args:this[rg]}:void 0}},toString(){return eY(this)},[eV](){return eY(this)}};function rw(e){return rG("Micro.evaluate: Not implemented")}let rk=e=>({...r_,[rm]:e.op,[rb]:e.eval??rw,[rx]:e.contA,[ry]:e.contE,[rv]:e.ensure}),rC=e=>{let t=rk(e);return function(){let r=Object.create(t);return r[rg]=!1===e.single?arguments:arguments[0],r}},rE=e=>{let t={...rk(e),[t7]:t7,_tag:e.op,get[e.prop](){return this[rg]},toJSON(){return{_id:"MicroExit",_tag:e.op,[e.prop]:this[rg]}},[eB](t){return rK(t)&&t._tag===e.op&&eJ(this[rg],t[rg])},[eR](){return eq(this,ej(e$(e.op))(eN(this[rg])))}};return function(e){let r=Object.create(t);return r[rg]=e,r[rx]=void 0,r[ry]=void 0,r[rv]=void 0,r}},rO=rE({op:"Success",prop:"value",eval(e){let t=e.getCont(rx);return t?t[rx](this[rg],e):e.yieldWith(this)}}),rI=rE({op:"Failure",prop:"cause",eval(e){let t=e.getCont(ry);for(;rc(this[rg])&&t&&e.interruptible;)t=e.getCont(ry);return t?t[ry](this[rg],e):e.yieldWith(this)}}),rF=e=>rI(rn(e)),rT=rC({op:"Sync",eval(e){let t=this[rg](),r=e.getCont(rx);return r?r[rx](t,e):e.yieldWith(rW(t))}}),rR=rC({op:"Suspend",eval(e){return this[rg]()}}),rN=rC({op:"Yield",eval(e){let t=!1;return e.getRef(r3).scheduleTask(()=>{t||e.evaluate(rZ)},this[rg]??0),e.yieldWith(()=>{t=!0})}})(0),rA=e=>rG(e),rj=e=>"Right"===e._tag?rO(e.right):rF(e.left),rM=rO(void 0),rz=e=>rP(function(t,r){e(r).then(e=>t(rO(e)),e=>t(rA(e)))},0!==e.length),rD=e=>rP(function(t,r){try{e.try(r).then(e=>t(rO(e)),r=>t(rF(e.catch(r))))}catch(r){t(rF(e.catch(r)))}},0!==e.try.length),r$=rC({op:"WithMicroFiber",eval(e){return this[rg](e)}}),rP=rC({op:"Async",single:!1,eval(e){let t=this[rg][0],r=!1,n=!1,i=this[rg][1]?new AbortController:void 0,s=t(t=>{r||(r=!0,n?e.evaluate(t):n=t)},i?.signal);return!1!==n?n:(n=!0,e._yielded=()=>{r=!0},void 0===i&&void 0===s||e._stack.push(rL(()=>(r=!0,i?.abort(),s??rZ))),rS)}}),rL=rC({op:"AsyncFinalizer",ensure(e){e.interruptible&&(e.interruptible=!1,e._stack.push(ns(!0)))},contE(e,t){return rc(e)?rB(this[rg](),()=>rI(e)):rI(e)}}),rU=(...e)=>rR(()=>rq(1===e.length?e[0]():e[1].call(e[0]))),rq=rC({op:"Iterator",contA(e,t){let r=this[rg].next(e);return r.done?rO(r.value):(t._stack.push(this),ek(r.value))},eval(e){return this[rx](void 0,e)}}),rB=U(2,(e,t)=>{let r=Object.create(rJ);return r[rg]=e,r[rx]=t,r}),rJ=rk({op:"OnSuccess",eval(e){return e._stack.push(this),this[rg]}}),rH=U(2,(e,t)=>rB(e,e=>rO(t(e)))),rK=e=>ec(e,t7),rW=rO,rV=rI(ro()),rG=e=>rI(rs(e)),rY=e=>"Failure"===e._tag,rZ=rW(void 0),rQ="setImmediate"in globalThis?globalThis.setImmediate:e=>setTimeout(e,0);class rX{scheduleTask(e,t){this.tasks.push(e),this.running||(this.running=!0,rQ(this.afterScheduled))}runTasks(){let e=this.tasks;this.tasks=[];for(let t=0,r=e.length;t<r;t++)e[t]()}shouldYield(e){return e.currentOpCount>=e.getRef(r2)}flush(){for(;this.tasks.length>0;)this.runTasks()}constructor(){this.tasks=[],this.running=!1,this.afterScheduled=()=>{this.running=!1,this.runTasks()}}}let r0=U(2,(e,t)=>r$(r=>{let n=r.context;return r.context=t(n),nr(e,()=>(r.context=n,rM))})),r1=U(2,(e,t)=>r0(e,tZ(t)));class r2 extends t2()("effect/Micro/currentMaxOpsBeforeYield",{defaultValue:()=>2048}){}class r3 extends t2()("effect/Micro/currentScheduler",{defaultValue:()=>new rX}){}let r5=U(2,(e,t)=>{let r=Object.create(r4);return r[rg]=e,r[ry]=t,r}),r4=rk({op:"OnFailure",eval(e){return e._stack.push(this),this[rg]}}),r6=U(3,(e,t,r)=>r5(e,e=>t(e)?r(e):rI(e))),r8=U(2,(e,t)=>r6(e,rl,e=>t(e.error))),r7=U(2,(e,t)=>r8(e,e=>rT(t))),r9=function(){let e=globalThis.Error.stackTraceLimit;globalThis.Error.stackTraceLimit=2;let t=new globalThis.Error;globalThis.Error.stackTraceLimit=e;let r=e=>r=>ni(r,r=>rI(function(e,r){let n=t.stack;if(!n)return r;let i=n.split("\n")[2]?.trim().replace(/^at /,"");if(!i)return r;let s=i.match(/\((.*)\)$/);return ru(r,`at ${e} (${s?s[1]:i})`)}(e,r)));return 2==arguments.length?r(arguments[1])(arguments[0]):r(arguments[0])},ne=U(2,(e,t)=>{let r=Object.create(nt);return r[rg]=e,r[rx]=t.onSuccess,r[ry]=t.onFailure,r}),nt=rk({op:"OnSuccessAndFailure",eval(e){return e._stack.push(this),this[rg]}}),nr=U(2,(e,t)=>no(r=>ne(r(e),{onFailure:e=>rB(t(rI(e)),()=>rI(e)),onSuccess:e=>rB(t(rW(e)),()=>rO(e))}))),nn=U(3,(e,t,r)=>nr(e,e=>t(e)?r(e):rZ)),ni=U(2,(e,t)=>nn(e,rY,e=>t(e.cause))),ns=rC({op:"SetInterruptible",ensure(e){if(e.interruptible=this[rg],e._interrupted&&e.interruptible)return()=>rV}}),na=e=>r$(t=>t.interruptible?e:(t.interruptible=!0,t._stack.push(ns(!1)),t._interrupted)?rV:e),no=e=>r$(t=>t.interruptible?(t.interruptible=!1,t._stack.push(ns(!0)),e(na)):e(q)),nl=(e,t)=>{let r=new rf(r3.context(t?.scheduler??new rX));if(r.evaluate(e),t?.signal)if(t.signal.aborted)r.unsafeInterrupt();else{let e=()=>r.unsafeInterrupt();t.signal.addEventListener("abort",e,{once:!0}),r.addObserver(()=>t.signal.removeEventListener("abort",e))}return r},nc=function(){class e extends globalThis.Error{}return Object.assign(e.prototype,r_,tb,{[rm]:"Failure",[rb](){return rF(this)},toString(){return this.message?`${this.name}: ${this.message}`:this.name},toJSON(){return{...this}},[eV](){let e=this.stack;return e?`${this.toString()}
${e.split("\n").slice(1).join("\n")}`:this.toString()}}),e}(),nu=class extends nc{constructor(e){super(),e&&Object.assign(this,e)}},nh=e=>{class t extends nu{constructor(...t){super(...t),this._tag=e}}return t.prototype.name=e,t},np={"application/andrew-inset":{source:"iana",extensions:["ez"]},"application/applixware":{source:"apache",extensions:["aw"]},"application/atom+xml":{source:"iana",extensions:["atom"]},"application/atomcat+xml":{source:"iana",extensions:["atomcat"]},"application/atomdeleted+xml":{source:"iana",extensions:["atomdeleted"]},"application/atomsvc+xml":{source:"iana",extensions:["atomsvc"]},"application/atsc-dwd+xml":{source:"iana",extensions:["dwd"]},"application/atsc-held+xml":{source:"iana",extensions:["held"]},"application/atsc-rsat+xml":{source:"iana",extensions:["rsat"]},"application/calendar+xml":{source:"iana",extensions:["xcs"]},"application/ccxml+xml":{source:"iana",extensions:["ccxml"]},"application/cdfx+xml":{source:"iana",extensions:["cdfx"]},"application/cdmi-capability":{source:"iana",extensions:["cdmia"]},"application/cdmi-container":{source:"iana",extensions:["cdmic"]},"application/cdmi-domain":{source:"iana",extensions:["cdmid"]},"application/cdmi-object":{source:"iana",extensions:["cdmio"]},"application/cdmi-queue":{source:"iana",extensions:["cdmiq"]},"application/cpl+xml":{source:"iana",extensions:["cpl"]},"application/cu-seeme":{source:"apache",extensions:["cu"]},"application/dash+xml":{source:"iana",extensions:["mpd"]},"application/dash-patch+xml":{source:"iana",extensions:["mpp"]},"application/davmount+xml":{source:"iana",extensions:["davmount"]},"application/dicom":{source:"iana",extensions:["dcm"]},"application/docbook+xml":{source:"apache",extensions:["dbk"]},"application/dssc+der":{source:"iana",extensions:["dssc"]},"application/dssc+xml":{source:"iana",extensions:["xdssc"]},"application/ecmascript":{source:"iana",extensions:["es","ecma"]},"application/emma+xml":{source:"iana",extensions:["emma"]},"application/emotionml+xml":{source:"iana",extensions:["emotionml"]},"application/epub+zip":{source:"iana",extensions:["epub"]},"application/exi":{source:"iana",extensions:["exi"]},"application/express":{source:"iana",extensions:["exp"]},"application/fdt+xml":{source:"iana",extensions:["fdt"]},"application/font-tdpfr":{source:"iana",extensions:["pfr"]},"application/geo+json":{source:"iana",extensions:["geojson"]},"application/gml+xml":{source:"iana",extensions:["gml"]},"application/gpx+xml":{source:"apache",extensions:["gpx"]},"application/gxf":{source:"apache",extensions:["gxf"]},"application/gzip":{source:"iana",extensions:["gz"]},"application/hyperstudio":{source:"iana",extensions:["stk"]},"application/inkml+xml":{source:"iana",extensions:["ink","inkml"]},"application/ipfix":{source:"iana",extensions:["ipfix"]},"application/its+xml":{source:"iana",extensions:["its"]},"application/java-archive":{source:"apache",extensions:["jar","war","ear"]},"application/java-serialized-object":{source:"apache",extensions:["ser"]},"application/java-vm":{source:"apache",extensions:["class"]},"application/javascript":{source:"iana",charset:"UTF-8",extensions:["js","mjs"]},"application/json":{source:"iana",charset:"UTF-8",extensions:["json","map"]},"application/jsonml+json":{source:"apache",extensions:["jsonml"]},"application/ld+json":{source:"iana",extensions:["jsonld"]},"application/lgr+xml":{source:"iana",extensions:["lgr"]},"application/lost+xml":{source:"iana",extensions:["lostxml"]},"application/mac-binhex40":{source:"iana",extensions:["hqx"]},"application/mac-compactpro":{source:"apache",extensions:["cpt"]},"application/mads+xml":{source:"iana",extensions:["mads"]},"application/manifest+json":{source:"iana",charset:"UTF-8",extensions:["webmanifest"]},"application/marc":{source:"iana",extensions:["mrc"]},"application/marcxml+xml":{source:"iana",extensions:["mrcx"]},"application/mathematica":{source:"iana",extensions:["ma","nb","mb"]},"application/mathml+xml":{source:"iana",extensions:["mathml"]},"application/mbox":{source:"iana",extensions:["mbox"]},"application/media-policy-dataset+xml":{source:"iana",extensions:["mpf"]},"application/mediaservercontrol+xml":{source:"iana",extensions:["mscml"]},"application/metalink+xml":{source:"apache",extensions:["metalink"]},"application/metalink4+xml":{source:"iana",extensions:["meta4"]},"application/mets+xml":{source:"iana",extensions:["mets"]},"application/mmt-aei+xml":{source:"iana",extensions:["maei"]},"application/mmt-usd+xml":{source:"iana",extensions:["musd"]},"application/mods+xml":{source:"iana",extensions:["mods"]},"application/mp21":{source:"iana",extensions:["m21","mp21"]},"application/mp4":{source:"iana",extensions:["mp4s","m4p"]},"application/msword":{source:"iana",extensions:["doc","dot"]},"application/mxf":{source:"iana",extensions:["mxf"]},"application/n-quads":{source:"iana",extensions:["nq"]},"application/n-triples":{source:"iana",extensions:["nt"]},"application/node":{source:"iana",extensions:["cjs"]},"application/octet-stream":{source:"iana",extensions:["bin","dms","lrf","mar","so","dist","distz","pkg","bpk","dump","elc","deploy","exe","dll","deb","dmg","iso","img","msi","msp","msm","buffer"]},"application/oda":{source:"iana",extensions:["oda"]},"application/oebps-package+xml":{source:"iana",extensions:["opf"]},"application/ogg":{source:"iana",extensions:["ogx"]},"application/omdoc+xml":{source:"apache",extensions:["omdoc"]},"application/onenote":{source:"apache",extensions:["onetoc","onetoc2","onetmp","onepkg"]},"application/oxps":{source:"iana",extensions:["oxps"]},"application/p2p-overlay+xml":{source:"iana",extensions:["relo"]},"application/patch-ops-error+xml":{source:"iana",extensions:["xer"]},"application/pdf":{source:"iana",extensions:["pdf"]},"application/pgp-encrypted":{source:"iana",extensions:["pgp"]},"application/pgp-keys":{source:"iana",extensions:["asc"]},"application/pgp-signature":{source:"iana",extensions:["asc","sig"]},"application/pics-rules":{source:"apache",extensions:["prf"]},"application/pkcs10":{source:"iana",extensions:["p10"]},"application/pkcs7-mime":{source:"iana",extensions:["p7m","p7c"]},"application/pkcs7-signature":{source:"iana",extensions:["p7s"]},"application/pkcs8":{source:"iana",extensions:["p8"]},"application/pkix-attr-cert":{source:"iana",extensions:["ac"]},"application/pkix-cert":{source:"iana",extensions:["cer"]},"application/pkix-crl":{source:"iana",extensions:["crl"]},"application/pkix-pkipath":{source:"iana",extensions:["pkipath"]},"application/pkixcmp":{source:"iana",extensions:["pki"]},"application/pls+xml":{source:"iana",extensions:["pls"]},"application/postscript":{source:"iana",extensions:["ai","eps","ps"]},"application/provenance+xml":{source:"iana",extensions:["provx"]},"application/prs.cww":{source:"iana",extensions:["cww"]},"application/pskc+xml":{source:"iana",extensions:["pskcxml"]},"application/rdf+xml":{source:"iana",extensions:["rdf","owl"]},"application/reginfo+xml":{source:"iana",extensions:["rif"]},"application/relax-ng-compact-syntax":{source:"iana",extensions:["rnc"]},"application/resource-lists+xml":{source:"iana",extensions:["rl"]},"application/resource-lists-diff+xml":{source:"iana",extensions:["rld"]},"application/rls-services+xml":{source:"iana",extensions:["rs"]},"application/route-apd+xml":{source:"iana",extensions:["rapd"]},"application/route-s-tsid+xml":{source:"iana",extensions:["sls"]},"application/route-usd+xml":{source:"iana",extensions:["rusd"]},"application/rpki-ghostbusters":{source:"iana",extensions:["gbr"]},"application/rpki-manifest":{source:"iana",extensions:["mft"]},"application/rpki-roa":{source:"iana",extensions:["roa"]},"application/rsd+xml":{source:"apache",extensions:["rsd"]},"application/rss+xml":{source:"apache",extensions:["rss"]},"application/rtf":{source:"iana",extensions:["rtf"]},"application/sbml+xml":{source:"iana",extensions:["sbml"]},"application/scvp-cv-request":{source:"iana",extensions:["scq"]},"application/scvp-cv-response":{source:"iana",extensions:["scs"]},"application/scvp-vp-request":{source:"iana",extensions:["spq"]},"application/scvp-vp-response":{source:"iana",extensions:["spp"]},"application/sdp":{source:"iana",extensions:["sdp"]},"application/senml+xml":{source:"iana",extensions:["senmlx"]},"application/sensml+xml":{source:"iana",extensions:["sensmlx"]},"application/set-payment-initiation":{source:"iana",extensions:["setpay"]},"application/set-registration-initiation":{source:"iana",extensions:["setreg"]},"application/shf+xml":{source:"iana",extensions:["shf"]},"application/sieve":{source:"iana",extensions:["siv","sieve"]},"application/smil+xml":{source:"iana",extensions:["smi","smil"]},"application/sparql-query":{source:"iana",extensions:["rq"]},"application/sparql-results+xml":{source:"iana",extensions:["srx"]},"application/srgs":{source:"iana",extensions:["gram"]},"application/srgs+xml":{source:"iana",extensions:["grxml"]},"application/sru+xml":{source:"iana",extensions:["sru"]},"application/ssdl+xml":{source:"apache",extensions:["ssdl"]},"application/ssml+xml":{source:"iana",extensions:["ssml"]},"application/swid+xml":{source:"iana",extensions:["swidtag"]},"application/tei+xml":{source:"iana",extensions:["tei","teicorpus"]},"application/thraud+xml":{source:"iana",extensions:["tfi"]},"application/timestamped-data":{source:"iana",extensions:["tsd"]},"application/trig":{source:"iana",extensions:["trig"]},"application/ttml+xml":{source:"iana",extensions:["ttml"]},"application/urc-ressheet+xml":{source:"iana",extensions:["rsheet"]},"application/urc-targetdesc+xml":{source:"iana",extensions:["td"]},"application/vnd.1000minds.decision-model+xml":{source:"iana",extensions:["1km"]},"application/vnd.3gpp.pic-bw-large":{source:"iana",extensions:["plb"]},"application/vnd.3gpp.pic-bw-small":{source:"iana",extensions:["psb"]},"application/vnd.3gpp.pic-bw-var":{source:"iana",extensions:["pvb"]},"application/vnd.3gpp2.tcap":{source:"iana",extensions:["tcap"]},"application/vnd.3m.post-it-notes":{source:"iana",extensions:["pwn"]},"application/vnd.accpac.simply.aso":{source:"iana",extensions:["aso"]},"application/vnd.accpac.simply.imp":{source:"iana",extensions:["imp"]},"application/vnd.acucobol":{source:"iana",extensions:["acu"]},"application/vnd.acucorp":{source:"iana",extensions:["atc","acutc"]},"application/vnd.adobe.air-application-installer-package+zip":{source:"apache",extensions:["air"]},"application/vnd.adobe.formscentral.fcdt":{source:"iana",extensions:["fcdt"]},"application/vnd.adobe.fxp":{source:"iana",extensions:["fxp","fxpl"]},"application/vnd.adobe.xdp+xml":{source:"iana",extensions:["xdp"]},"application/vnd.adobe.xfdf":{source:"iana",extensions:["xfdf"]},"application/vnd.age":{source:"iana",extensions:["age"]},"application/vnd.ahead.space":{source:"iana",extensions:["ahead"]},"application/vnd.airzip.filesecure.azf":{source:"iana",extensions:["azf"]},"application/vnd.airzip.filesecure.azs":{source:"iana",extensions:["azs"]},"application/vnd.amazon.ebook":{source:"apache",extensions:["azw"]},"application/vnd.americandynamics.acc":{source:"iana",extensions:["acc"]},"application/vnd.amiga.ami":{source:"iana",extensions:["ami"]},"application/vnd.android.package-archive":{source:"apache",extensions:["apk"]},"application/vnd.anser-web-certificate-issue-initiation":{source:"iana",extensions:["cii"]},"application/vnd.anser-web-funds-transfer-initiation":{source:"apache",extensions:["fti"]},"application/vnd.antix.game-component":{source:"iana",extensions:["atx"]},"application/vnd.apple.installer+xml":{source:"iana",extensions:["mpkg"]},"application/vnd.apple.keynote":{source:"iana",extensions:["key"]},"application/vnd.apple.mpegurl":{source:"iana",extensions:["m3u8"]},"application/vnd.apple.numbers":{source:"iana",extensions:["numbers"]},"application/vnd.apple.pages":{source:"iana",extensions:["pages"]},"application/vnd.aristanetworks.swi":{source:"iana",extensions:["swi"]},"application/vnd.astraea-software.iota":{source:"iana",extensions:["iota"]},"application/vnd.audiograph":{source:"iana",extensions:["aep"]},"application/vnd.balsamiq.bmml+xml":{source:"iana",extensions:["bmml"]},"application/vnd.blueice.multipass":{source:"iana",extensions:["mpm"]},"application/vnd.bmi":{source:"iana",extensions:["bmi"]},"application/vnd.businessobjects":{source:"iana",extensions:["rep"]},"application/vnd.chemdraw+xml":{source:"iana",extensions:["cdxml"]},"application/vnd.chipnuts.karaoke-mmd":{source:"iana",extensions:["mmd"]},"application/vnd.cinderella":{source:"iana",extensions:["cdy"]},"application/vnd.citationstyles.style+xml":{source:"iana",extensions:["csl"]},"application/vnd.claymore":{source:"iana",extensions:["cla"]},"application/vnd.cloanto.rp9":{source:"iana",extensions:["rp9"]},"application/vnd.clonk.c4group":{source:"iana",extensions:["c4g","c4d","c4f","c4p","c4u"]},"application/vnd.cluetrust.cartomobile-config":{source:"iana",extensions:["c11amc"]},"application/vnd.cluetrust.cartomobile-config-pkg":{source:"iana",extensions:["c11amz"]},"application/vnd.commonspace":{source:"iana",extensions:["csp"]},"application/vnd.contact.cmsg":{source:"iana",extensions:["cdbcmsg"]},"application/vnd.cosmocaller":{source:"iana",extensions:["cmc"]},"application/vnd.crick.clicker":{source:"iana",extensions:["clkx"]},"application/vnd.crick.clicker.keyboard":{source:"iana",extensions:["clkk"]},"application/vnd.crick.clicker.palette":{source:"iana",extensions:["clkp"]},"application/vnd.crick.clicker.template":{source:"iana",extensions:["clkt"]},"application/vnd.crick.clicker.wordbank":{source:"iana",extensions:["clkw"]},"application/vnd.criticaltools.wbs+xml":{source:"iana",extensions:["wbs"]},"application/vnd.ctc-posml":{source:"iana",extensions:["pml"]},"application/vnd.cups-ppd":{source:"iana",extensions:["ppd"]},"application/vnd.curl.car":{source:"apache",extensions:["car"]},"application/vnd.curl.pcurl":{source:"apache",extensions:["pcurl"]},"application/vnd.dart":{source:"iana",extensions:["dart"]},"application/vnd.data-vision.rdz":{source:"iana",extensions:["rdz"]},"application/vnd.dbf":{source:"iana",extensions:["dbf"]},"application/vnd.dece.data":{source:"iana",extensions:["uvf","uvvf","uvd","uvvd"]},"application/vnd.dece.ttml+xml":{source:"iana",extensions:["uvt","uvvt"]},"application/vnd.dece.unspecified":{source:"iana",extensions:["uvx","uvvx"]},"application/vnd.dece.zip":{source:"iana",extensions:["uvz","uvvz"]},"application/vnd.denovo.fcselayout-link":{source:"iana",extensions:["fe_launch"]},"application/vnd.dna":{source:"iana",extensions:["dna"]},"application/vnd.dolby.mlp":{source:"apache",extensions:["mlp"]},"application/vnd.dpgraph":{source:"iana",extensions:["dpg"]},"application/vnd.dreamfactory":{source:"iana",extensions:["dfac"]},"application/vnd.ds-keypoint":{source:"apache",extensions:["kpxx"]},"application/vnd.dvb.ait":{source:"iana",extensions:["ait"]},"application/vnd.dvb.service":{source:"iana",extensions:["svc"]},"application/vnd.dynageo":{source:"iana",extensions:["geo"]},"application/vnd.ecowin.chart":{source:"iana",extensions:["mag"]},"application/vnd.enliven":{source:"iana",extensions:["nml"]},"application/vnd.epson.esf":{source:"iana",extensions:["esf"]},"application/vnd.epson.msf":{source:"iana",extensions:["msf"]},"application/vnd.epson.quickanime":{source:"iana",extensions:["qam"]},"application/vnd.epson.salt":{source:"iana",extensions:["slt"]},"application/vnd.epson.ssf":{source:"iana",extensions:["ssf"]},"application/vnd.eszigno3+xml":{source:"iana",extensions:["es3","et3"]},"application/vnd.ezpix-album":{source:"iana",extensions:["ez2"]},"application/vnd.ezpix-package":{source:"iana",extensions:["ez3"]},"application/vnd.fdf":{source:"iana",extensions:["fdf"]},"application/vnd.fdsn.mseed":{source:"iana",extensions:["mseed"]},"application/vnd.fdsn.seed":{source:"iana",extensions:["seed","dataless"]},"application/vnd.flographit":{source:"iana",extensions:["gph"]},"application/vnd.fluxtime.clip":{source:"iana",extensions:["ftc"]},"application/vnd.framemaker":{source:"iana",extensions:["fm","frame","maker","book"]},"application/vnd.frogans.fnc":{source:"iana",extensions:["fnc"]},"application/vnd.frogans.ltf":{source:"iana",extensions:["ltf"]},"application/vnd.fsc.weblaunch":{source:"iana",extensions:["fsc"]},"application/vnd.fujitsu.oasys":{source:"iana",extensions:["oas"]},"application/vnd.fujitsu.oasys2":{source:"iana",extensions:["oa2"]},"application/vnd.fujitsu.oasys3":{source:"iana",extensions:["oa3"]},"application/vnd.fujitsu.oasysgp":{source:"iana",extensions:["fg5"]},"application/vnd.fujitsu.oasysprs":{source:"iana",extensions:["bh2"]},"application/vnd.fujixerox.ddd":{source:"iana",extensions:["ddd"]},"application/vnd.fujixerox.docuworks":{source:"iana",extensions:["xdw"]},"application/vnd.fujixerox.docuworks.binder":{source:"iana",extensions:["xbd"]},"application/vnd.fuzzysheet":{source:"iana",extensions:["fzs"]},"application/vnd.genomatix.tuxedo":{source:"iana",extensions:["txd"]},"application/vnd.geogebra.file":{source:"iana",extensions:["ggb"]},"application/vnd.geogebra.tool":{source:"iana",extensions:["ggt"]},"application/vnd.geometry-explorer":{source:"iana",extensions:["gex","gre"]},"application/vnd.geonext":{source:"iana",extensions:["gxt"]},"application/vnd.geoplan":{source:"iana",extensions:["g2w"]},"application/vnd.geospace":{source:"iana",extensions:["g3w"]},"application/vnd.gmx":{source:"iana",extensions:["gmx"]},"application/vnd.google-earth.kml+xml":{source:"iana",extensions:["kml"]},"application/vnd.google-earth.kmz":{source:"iana",extensions:["kmz"]},"application/vnd.grafeq":{source:"iana",extensions:["gqf","gqs"]},"application/vnd.groove-account":{source:"iana",extensions:["gac"]},"application/vnd.groove-help":{source:"iana",extensions:["ghf"]},"application/vnd.groove-identity-message":{source:"iana",extensions:["gim"]},"application/vnd.groove-injector":{source:"iana",extensions:["grv"]},"application/vnd.groove-tool-message":{source:"iana",extensions:["gtm"]},"application/vnd.groove-tool-template":{source:"iana",extensions:["tpl"]},"application/vnd.groove-vcard":{source:"iana",extensions:["vcg"]},"application/vnd.hal+xml":{source:"iana",extensions:["hal"]},"application/vnd.handheld-entertainment+xml":{source:"iana",extensions:["zmm"]},"application/vnd.hbci":{source:"iana",extensions:["hbci"]},"application/vnd.hhe.lesson-player":{source:"iana",extensions:["les"]},"application/vnd.hp-hpgl":{source:"iana",extensions:["hpgl"]},"application/vnd.hp-hpid":{source:"iana",extensions:["hpid"]},"application/vnd.hp-hps":{source:"iana",extensions:["hps"]},"application/vnd.hp-jlyt":{source:"iana",extensions:["jlt"]},"application/vnd.hp-pcl":{source:"iana",extensions:["pcl"]},"application/vnd.hp-pclxl":{source:"iana",extensions:["pclxl"]},"application/vnd.hydrostatix.sof-data":{source:"iana",extensions:["sfd-hdstx"]},"application/vnd.ibm.minipay":{source:"iana",extensions:["mpy"]},"application/vnd.ibm.modcap":{source:"iana",extensions:["afp","listafp","list3820"]},"application/vnd.ibm.rights-management":{source:"iana",extensions:["irm"]},"application/vnd.ibm.secure-container":{source:"iana",extensions:["sc"]},"application/vnd.iccprofile":{source:"iana",extensions:["icc","icm"]},"application/vnd.igloader":{source:"iana",extensions:["igl"]},"application/vnd.immervision-ivp":{source:"iana",extensions:["ivp"]},"application/vnd.immervision-ivu":{source:"iana",extensions:["ivu"]},"application/vnd.insors.igm":{source:"iana",extensions:["igm"]},"application/vnd.intercon.formnet":{source:"iana",extensions:["xpw","xpx"]},"application/vnd.intergeo":{source:"iana",extensions:["i2g"]},"application/vnd.intu.qbo":{source:"iana",extensions:["qbo"]},"application/vnd.intu.qfx":{source:"iana",extensions:["qfx"]},"application/vnd.ipunplugged.rcprofile":{source:"iana",extensions:["rcprofile"]},"application/vnd.irepository.package+xml":{source:"iana",extensions:["irp"]},"application/vnd.is-xpr":{source:"iana",extensions:["xpr"]},"application/vnd.isac.fcs":{source:"iana",extensions:["fcs"]},"application/vnd.jam":{source:"iana",extensions:["jam"]},"application/vnd.jcp.javame.midlet-rms":{source:"iana",extensions:["rms"]},"application/vnd.jisp":{source:"iana",extensions:["jisp"]},"application/vnd.joost.joda-archive":{source:"iana",extensions:["joda"]},"application/vnd.kahootz":{source:"iana",extensions:["ktz","ktr"]},"application/vnd.kde.karbon":{source:"iana",extensions:["karbon"]},"application/vnd.kde.kchart":{source:"iana",extensions:["chrt"]},"application/vnd.kde.kformula":{source:"iana",extensions:["kfo"]},"application/vnd.kde.kivio":{source:"iana",extensions:["flw"]},"application/vnd.kde.kontour":{source:"iana",extensions:["kon"]},"application/vnd.kde.kpresenter":{source:"iana",extensions:["kpr","kpt"]},"application/vnd.kde.kspread":{source:"iana",extensions:["ksp"]},"application/vnd.kde.kword":{source:"iana",extensions:["kwd","kwt"]},"application/vnd.kenameaapp":{source:"iana",extensions:["htke"]},"application/vnd.kidspiration":{source:"iana",extensions:["kia"]},"application/vnd.kinar":{source:"iana",extensions:["kne","knp"]},"application/vnd.koan":{source:"iana",extensions:["skp","skd","skt","skm"]},"application/vnd.kodak-descriptor":{source:"iana",extensions:["sse"]},"application/vnd.las.las+xml":{source:"iana",extensions:["lasxml"]},"application/vnd.llamagraphics.life-balance.desktop":{source:"iana",extensions:["lbd"]},"application/vnd.llamagraphics.life-balance.exchange+xml":{source:"iana",extensions:["lbe"]},"application/vnd.lotus-1-2-3":{source:"iana",extensions:["123"]},"application/vnd.lotus-approach":{source:"iana",extensions:["apr"]},"application/vnd.lotus-freelance":{source:"iana",extensions:["pre"]},"application/vnd.lotus-notes":{source:"iana",extensions:["nsf"]},"application/vnd.lotus-organizer":{source:"iana",extensions:["org"]},"application/vnd.lotus-screencam":{source:"iana",extensions:["scm"]},"application/vnd.lotus-wordpro":{source:"iana",extensions:["lwp"]},"application/vnd.macports.portpkg":{source:"iana",extensions:["portpkg"]},"application/vnd.mapbox-vector-tile":{source:"iana",extensions:["mvt"]},"application/vnd.mcd":{source:"iana",extensions:["mcd"]},"application/vnd.medcalcdata":{source:"iana",extensions:["mc1"]},"application/vnd.mediastation.cdkey":{source:"iana",extensions:["cdkey"]},"application/vnd.mfer":{source:"iana",extensions:["mwf"]},"application/vnd.mfmp":{source:"iana",extensions:["mfm"]},"application/vnd.micrografx.flo":{source:"iana",extensions:["flo"]},"application/vnd.micrografx.igx":{source:"iana",extensions:["igx"]},"application/vnd.mif":{source:"iana",extensions:["mif"]},"application/vnd.mobius.daf":{source:"iana",extensions:["daf"]},"application/vnd.mobius.dis":{source:"iana",extensions:["dis"]},"application/vnd.mobius.mbk":{source:"iana",extensions:["mbk"]},"application/vnd.mobius.mqy":{source:"iana",extensions:["mqy"]},"application/vnd.mobius.msl":{source:"iana",extensions:["msl"]},"application/vnd.mobius.plc":{source:"iana",extensions:["plc"]},"application/vnd.mobius.txf":{source:"iana",extensions:["txf"]},"application/vnd.mophun.application":{source:"iana",extensions:["mpn"]},"application/vnd.mophun.certificate":{source:"iana",extensions:["mpc"]},"application/vnd.mozilla.xul+xml":{source:"iana",extensions:["xul"]},"application/vnd.ms-artgalry":{source:"iana",extensions:["cil"]},"application/vnd.ms-cab-compressed":{source:"iana",extensions:["cab"]},"application/vnd.ms-excel":{source:"iana",extensions:["xls","xlm","xla","xlc","xlt","xlw"]},"application/vnd.ms-excel.addin.macroenabled.12":{source:"iana",extensions:["xlam"]},"application/vnd.ms-excel.sheet.binary.macroenabled.12":{source:"iana",extensions:["xlsb"]},"application/vnd.ms-excel.sheet.macroenabled.12":{source:"iana",extensions:["xlsm"]},"application/vnd.ms-excel.template.macroenabled.12":{source:"iana",extensions:["xltm"]},"application/vnd.ms-fontobject":{source:"iana",extensions:["eot"]},"application/vnd.ms-htmlhelp":{source:"iana",extensions:["chm"]},"application/vnd.ms-ims":{source:"iana",extensions:["ims"]},"application/vnd.ms-lrm":{source:"iana",extensions:["lrm"]},"application/vnd.ms-officetheme":{source:"iana",extensions:["thmx"]},"application/vnd.ms-pki.seccat":{source:"apache",extensions:["cat"]},"application/vnd.ms-pki.stl":{source:"apache",extensions:["stl"]},"application/vnd.ms-powerpoint":{source:"iana",extensions:["ppt","pps","pot"]},"application/vnd.ms-powerpoint.addin.macroenabled.12":{source:"iana",extensions:["ppam"]},"application/vnd.ms-powerpoint.presentation.macroenabled.12":{source:"iana",extensions:["pptm"]},"application/vnd.ms-powerpoint.slide.macroenabled.12":{source:"iana",extensions:["sldm"]},"application/vnd.ms-powerpoint.slideshow.macroenabled.12":{source:"iana",extensions:["ppsm"]},"application/vnd.ms-powerpoint.template.macroenabled.12":{source:"iana",extensions:["potm"]},"application/vnd.ms-project":{source:"iana",extensions:["mpp","mpt"]},"application/vnd.ms-word.document.macroenabled.12":{source:"iana",extensions:["docm"]},"application/vnd.ms-word.template.macroenabled.12":{source:"iana",extensions:["dotm"]},"application/vnd.ms-works":{source:"iana",extensions:["wps","wks","wcm","wdb"]},"application/vnd.ms-wpl":{source:"iana",extensions:["wpl"]},"application/vnd.ms-xpsdocument":{source:"iana",extensions:["xps"]},"application/vnd.mseq":{source:"iana",extensions:["mseq"]},"application/vnd.musician":{source:"iana",extensions:["mus"]},"application/vnd.muvee.style":{source:"iana",extensions:["msty"]},"application/vnd.mynfc":{source:"iana",extensions:["taglet"]},"application/vnd.neurolanguage.nlu":{source:"iana",extensions:["nlu"]},"application/vnd.nitf":{source:"iana",extensions:["ntf","nitf"]},"application/vnd.noblenet-directory":{source:"iana",extensions:["nnd"]},"application/vnd.noblenet-sealer":{source:"iana",extensions:["nns"]},"application/vnd.noblenet-web":{source:"iana",extensions:["nnw"]},"application/vnd.nokia.n-gage.ac+xml":{source:"iana",extensions:["ac"]},"application/vnd.nokia.n-gage.data":{source:"iana",extensions:["ngdat"]},"application/vnd.nokia.n-gage.symbian.install":{source:"iana",extensions:["n-gage"]},"application/vnd.nokia.radio-preset":{source:"iana",extensions:["rpst"]},"application/vnd.nokia.radio-presets":{source:"iana",extensions:["rpss"]},"application/vnd.novadigm.edm":{source:"iana",extensions:["edm"]},"application/vnd.novadigm.edx":{source:"iana",extensions:["edx"]},"application/vnd.novadigm.ext":{source:"iana",extensions:["ext"]},"application/vnd.oasis.opendocument.chart":{source:"iana",extensions:["odc"]},"application/vnd.oasis.opendocument.chart-template":{source:"iana",extensions:["otc"]},"application/vnd.oasis.opendocument.database":{source:"iana",extensions:["odb"]},"application/vnd.oasis.opendocument.formula":{source:"iana",extensions:["odf"]},"application/vnd.oasis.opendocument.formula-template":{source:"iana",extensions:["odft"]},"application/vnd.oasis.opendocument.graphics":{source:"iana",extensions:["odg"]},"application/vnd.oasis.opendocument.graphics-template":{source:"iana",extensions:["otg"]},"application/vnd.oasis.opendocument.image":{source:"iana",extensions:["odi"]},"application/vnd.oasis.opendocument.image-template":{source:"iana",extensions:["oti"]},"application/vnd.oasis.opendocument.presentation":{source:"iana",extensions:["odp"]},"application/vnd.oasis.opendocument.presentation-template":{source:"iana",extensions:["otp"]},"application/vnd.oasis.opendocument.spreadsheet":{source:"iana",extensions:["ods"]},"application/vnd.oasis.opendocument.spreadsheet-template":{source:"iana",extensions:["ots"]},"application/vnd.oasis.opendocument.text":{source:"iana",extensions:["odt"]},"application/vnd.oasis.opendocument.text-master":{source:"iana",extensions:["odm"]},"application/vnd.oasis.opendocument.text-template":{source:"iana",extensions:["ott"]},"application/vnd.oasis.opendocument.text-web":{source:"iana",extensions:["oth"]},"application/vnd.olpc-sugar":{source:"iana",extensions:["xo"]},"application/vnd.oma.dd2+xml":{source:"iana",extensions:["dd2"]},"application/vnd.openblox.game+xml":{source:"iana",extensions:["obgx"]},"application/vnd.openofficeorg.extension":{source:"apache",extensions:["oxt"]},"application/vnd.openstreetmap.data+xml":{source:"iana",extensions:["osm"]},"application/vnd.openxmlformats-officedocument.presentationml.presentation":{source:"iana",extensions:["pptx"]},"application/vnd.openxmlformats-officedocument.presentationml.slide":{source:"iana",extensions:["sldx"]},"application/vnd.openxmlformats-officedocument.presentationml.slideshow":{source:"iana",extensions:["ppsx"]},"application/vnd.openxmlformats-officedocument.presentationml.template":{source:"iana",extensions:["potx"]},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":{source:"iana",extensions:["xlsx"]},"application/vnd.openxmlformats-officedocument.spreadsheetml.template":{source:"iana",extensions:["xltx"]},"application/vnd.openxmlformats-officedocument.wordprocessingml.document":{source:"iana",extensions:["docx"]},"application/vnd.openxmlformats-officedocument.wordprocessingml.template":{source:"iana",extensions:["dotx"]},"application/vnd.osgeo.mapguide.package":{source:"iana",extensions:["mgp"]},"application/vnd.osgi.dp":{source:"iana",extensions:["dp"]},"application/vnd.osgi.subsystem":{source:"iana",extensions:["esa"]},"application/vnd.palm":{source:"iana",extensions:["pdb","pqa","oprc"]},"application/vnd.pawaafile":{source:"iana",extensions:["paw"]},"application/vnd.pg.format":{source:"iana",extensions:["str"]},"application/vnd.pg.osasli":{source:"iana",extensions:["ei6"]},"application/vnd.picsel":{source:"iana",extensions:["efif"]},"application/vnd.pmi.widget":{source:"iana",extensions:["wg"]},"application/vnd.pocketlearn":{source:"iana",extensions:["plf"]},"application/vnd.powerbuilder6":{source:"iana",extensions:["pbd"]},"application/vnd.previewsystems.box":{source:"iana",extensions:["box"]},"application/vnd.proteus.magazine":{source:"iana",extensions:["mgz"]},"application/vnd.publishare-delta-tree":{source:"iana",extensions:["qps"]},"application/vnd.pvi.ptid1":{source:"iana",extensions:["ptid"]},"application/vnd.quark.quarkxpress":{source:"iana",extensions:["qxd","qxt","qwd","qwt","qxl","qxb"]},"application/vnd.rar":{source:"iana",extensions:["rar"]},"application/vnd.realvnc.bed":{source:"iana",extensions:["bed"]},"application/vnd.recordare.musicxml":{source:"iana",extensions:["mxl"]},"application/vnd.recordare.musicxml+xml":{source:"iana",extensions:["musicxml"]},"application/vnd.rig.cryptonote":{source:"iana",extensions:["cryptonote"]},"application/vnd.rim.cod":{source:"apache",extensions:["cod"]},"application/vnd.rn-realmedia":{source:"apache",extensions:["rm"]},"application/vnd.rn-realmedia-vbr":{source:"apache",extensions:["rmvb"]},"application/vnd.route66.link66+xml":{source:"iana",extensions:["link66"]},"application/vnd.sailingtracker.track":{source:"iana",extensions:["st"]},"application/vnd.seemail":{source:"iana",extensions:["see"]},"application/vnd.sema":{source:"iana",extensions:["sema"]},"application/vnd.semd":{source:"iana",extensions:["semd"]},"application/vnd.semf":{source:"iana",extensions:["semf"]},"application/vnd.shana.informed.formdata":{source:"iana",extensions:["ifm"]},"application/vnd.shana.informed.formtemplate":{source:"iana",extensions:["itp"]},"application/vnd.shana.informed.interchange":{source:"iana",extensions:["iif"]},"application/vnd.shana.informed.package":{source:"iana",extensions:["ipk"]},"application/vnd.simtech-mindmapper":{source:"iana",extensions:["twd","twds"]},"application/vnd.smaf":{source:"iana",extensions:["mmf"]},"application/vnd.smart.teacher":{source:"iana",extensions:["teacher"]},"application/vnd.software602.filler.form+xml":{source:"iana",extensions:["fo"]},"application/vnd.solent.sdkm+xml":{source:"iana",extensions:["sdkm","sdkd"]},"application/vnd.spotfire.dxp":{source:"iana",extensions:["dxp"]},"application/vnd.spotfire.sfs":{source:"iana",extensions:["sfs"]},"application/vnd.stardivision.calc":{source:"apache",extensions:["sdc"]},"application/vnd.stardivision.draw":{source:"apache",extensions:["sda"]},"application/vnd.stardivision.impress":{source:"apache",extensions:["sdd"]},"application/vnd.stardivision.math":{source:"apache",extensions:["smf"]},"application/vnd.stardivision.writer":{source:"apache",extensions:["sdw","vor"]},"application/vnd.stardivision.writer-global":{source:"apache",extensions:["sgl"]},"application/vnd.stepmania.package":{source:"iana",extensions:["smzip"]},"application/vnd.stepmania.stepchart":{source:"iana",extensions:["sm"]},"application/vnd.sun.wadl+xml":{source:"iana",extensions:["wadl"]},"application/vnd.sun.xml.calc":{source:"apache",extensions:["sxc"]},"application/vnd.sun.xml.calc.template":{source:"apache",extensions:["stc"]},"application/vnd.sun.xml.draw":{source:"apache",extensions:["sxd"]},"application/vnd.sun.xml.draw.template":{source:"apache",extensions:["std"]},"application/vnd.sun.xml.impress":{source:"apache",extensions:["sxi"]},"application/vnd.sun.xml.impress.template":{source:"apache",extensions:["sti"]},"application/vnd.sun.xml.math":{source:"apache",extensions:["sxm"]},"application/vnd.sun.xml.writer":{source:"apache",extensions:["sxw"]},"application/vnd.sun.xml.writer.global":{source:"apache",extensions:["sxg"]},"application/vnd.sun.xml.writer.template":{source:"apache",extensions:["stw"]},"application/vnd.sus-calendar":{source:"iana",extensions:["sus","susp"]},"application/vnd.svd":{source:"iana",extensions:["svd"]},"application/vnd.symbian.install":{source:"apache",extensions:["sis","sisx"]},"application/vnd.syncml+xml":{source:"iana",charset:"UTF-8",extensions:["xsm"]},"application/vnd.syncml.dm+wbxml":{source:"iana",charset:"UTF-8",extensions:["bdm"]},"application/vnd.syncml.dm+xml":{source:"iana",charset:"UTF-8",extensions:["xdm"]},"application/vnd.syncml.dmddf+xml":{source:"iana",charset:"UTF-8",extensions:["ddf"]},"application/vnd.tao.intent-module-archive":{source:"iana",extensions:["tao"]},"application/vnd.tcpdump.pcap":{source:"iana",extensions:["pcap","cap","dmp"]},"application/vnd.tmobile-livetv":{source:"iana",extensions:["tmo"]},"application/vnd.trid.tpt":{source:"iana",extensions:["tpt"]},"application/vnd.triscape.mxs":{source:"iana",extensions:["mxs"]},"application/vnd.trueapp":{source:"iana",extensions:["tra"]},"application/vnd.ufdl":{source:"iana",extensions:["ufd","ufdl"]},"application/vnd.uiq.theme":{source:"iana",extensions:["utz"]},"application/vnd.umajin":{source:"iana",extensions:["umj"]},"application/vnd.unity":{source:"iana",extensions:["unityweb"]},"application/vnd.uoml+xml":{source:"iana",extensions:["uoml"]},"application/vnd.vcx":{source:"iana",extensions:["vcx"]},"application/vnd.visio":{source:"iana",extensions:["vsd","vst","vss","vsw"]},"application/vnd.visionary":{source:"iana",extensions:["vis"]},"application/vnd.vsf":{source:"iana",extensions:["vsf"]},"application/vnd.wap.wbxml":{source:"iana",charset:"UTF-8",extensions:["wbxml"]},"application/vnd.wap.wmlc":{source:"iana",extensions:["wmlc"]},"application/vnd.wap.wmlscriptc":{source:"iana",extensions:["wmlsc"]},"application/vnd.webturbo":{source:"iana",extensions:["wtb"]},"application/vnd.wolfram.player":{source:"iana",extensions:["nbp"]},"application/vnd.wordperfect":{source:"iana",extensions:["wpd"]},"application/vnd.wqd":{source:"iana",extensions:["wqd"]},"application/vnd.wt.stf":{source:"iana",extensions:["stf"]},"application/vnd.xara":{source:"iana",extensions:["xar"]},"application/vnd.xfdl":{source:"iana",extensions:["xfdl"]},"application/vnd.yamaha.hv-dic":{source:"iana",extensions:["hvd"]},"application/vnd.yamaha.hv-script":{source:"iana",extensions:["hvs"]},"application/vnd.yamaha.hv-voice":{source:"iana",extensions:["hvp"]},"application/vnd.yamaha.openscoreformat":{source:"iana",extensions:["osf"]},"application/vnd.yamaha.openscoreformat.osfpvg+xml":{source:"iana",extensions:["osfpvg"]},"application/vnd.yamaha.smaf-audio":{source:"iana",extensions:["saf"]},"application/vnd.yamaha.smaf-phrase":{source:"iana",extensions:["spf"]},"application/vnd.yellowriver-custom-menu":{source:"iana",extensions:["cmp"]},"application/vnd.zul":{source:"iana",extensions:["zir","zirz"]},"application/vnd.zzazz.deck+xml":{source:"iana",extensions:["zaz"]},"application/voicexml+xml":{source:"iana",extensions:["vxml"]},"application/wasm":{source:"iana",extensions:["wasm"]},"application/watcherinfo+xml":{source:"iana",extensions:["wif"]},"application/widget":{source:"iana",extensions:["wgt"]},"application/winhlp":{source:"apache",extensions:["hlp"]},"application/wsdl+xml":{source:"iana",extensions:["wsdl"]},"application/wspolicy+xml":{source:"iana",extensions:["wspolicy"]},"application/x-7z-compressed":{source:"apache",extensions:["7z"]},"application/x-abiword":{source:"apache",extensions:["abw"]},"application/x-ace-compressed":{source:"apache",extensions:["ace"]},"application/x-apple-diskimage":{source:"apache",extensions:["dmg"]},"application/x-authorware-bin":{source:"apache",extensions:["aab","x32","u32","vox"]},"application/x-authorware-map":{source:"apache",extensions:["aam"]},"application/x-authorware-seg":{source:"apache",extensions:["aas"]},"application/x-bcpio":{source:"apache",extensions:["bcpio"]},"application/x-bittorrent":{source:"apache",extensions:["torrent"]},"application/x-blorb":{source:"apache",extensions:["blb","blorb"]},"application/x-bzip":{source:"apache",extensions:["bz"]},"application/x-bzip2":{source:"apache",extensions:["bz2","boz"]},"application/x-cbr":{source:"apache",extensions:["cbr","cba","cbt","cbz","cb7"]},"application/x-cdlink":{source:"apache",extensions:["vcd"]},"application/x-cfs-compressed":{source:"apache",extensions:["cfs"]},"application/x-chat":{source:"apache",extensions:["chat"]},"application/x-chess-pgn":{source:"apache",extensions:["pgn"]},"application/x-cocoa":{source:"nginx",extensions:["cco"]},"application/x-conference":{source:"apache",extensions:["nsc"]},"application/x-cpio":{source:"apache",extensions:["cpio"]},"application/x-csh":{source:"apache",extensions:["csh"]},"application/x-debian-package":{source:"apache",extensions:["deb","udeb"]},"application/x-dgc-compressed":{source:"apache",extensions:["dgc"]},"application/x-director":{source:"apache",extensions:["dir","dcr","dxr","cst","cct","cxt","w3d","fgd","swa"]},"application/x-doom":{source:"apache",extensions:["wad"]},"application/x-dtbncx+xml":{source:"apache",extensions:["ncx"]},"application/x-dtbook+xml":{source:"apache",extensions:["dtb"]},"application/x-dtbresource+xml":{source:"apache",extensions:["res"]},"application/x-dvi":{source:"apache",extensions:["dvi"]},"application/x-envoy":{source:"apache",extensions:["evy"]},"application/x-eva":{source:"apache",extensions:["eva"]},"application/x-font-bdf":{source:"apache",extensions:["bdf"]},"application/x-font-ghostscript":{source:"apache",extensions:["gsf"]},"application/x-font-linux-psf":{source:"apache",extensions:["psf"]},"application/x-font-pcf":{source:"apache",extensions:["pcf"]},"application/x-font-snf":{source:"apache",extensions:["snf"]},"application/x-font-type1":{source:"apache",extensions:["pfa","pfb","pfm","afm"]},"application/x-freearc":{source:"apache",extensions:["arc"]},"application/x-futuresplash":{source:"apache",extensions:["spl"]},"application/x-gca-compressed":{source:"apache",extensions:["gca"]},"application/x-glulx":{source:"apache",extensions:["ulx"]},"application/x-gnumeric":{source:"apache",extensions:["gnumeric"]},"application/x-gramps-xml":{source:"apache",extensions:["gramps"]},"application/x-gtar":{source:"apache",extensions:["gtar"]},"application/x-hdf":{source:"apache",extensions:["hdf"]},"application/x-install-instructions":{source:"apache",extensions:["install"]},"application/x-iso9660-image":{source:"apache",extensions:["iso"]},"application/x-java-archive-diff":{source:"nginx",extensions:["jardiff"]},"application/x-java-jnlp-file":{source:"apache",extensions:["jnlp"]},"application/x-latex":{source:"apache",extensions:["latex"]},"application/x-lzh-compressed":{source:"apache",extensions:["lzh","lha"]},"application/x-makeself":{source:"nginx",extensions:["run"]},"application/x-mie":{source:"apache",extensions:["mie"]},"application/x-mobipocket-ebook":{source:"apache",extensions:["prc","mobi"]},"application/x-ms-application":{source:"apache",extensions:["application"]},"application/x-ms-shortcut":{source:"apache",extensions:["lnk"]},"application/x-ms-wmd":{source:"apache",extensions:["wmd"]},"application/x-ms-wmz":{source:"apache",extensions:["wmz"]},"application/x-ms-xbap":{source:"apache",extensions:["xbap"]},"application/x-msaccess":{source:"apache",extensions:["mdb"]},"application/x-msbinder":{source:"apache",extensions:["obd"]},"application/x-mscardfile":{source:"apache",extensions:["crd"]},"application/x-msclip":{source:"apache",extensions:["clp"]},"application/x-msdownload":{source:"apache",extensions:["exe","dll","com","bat","msi"]},"application/x-msmediaview":{source:"apache",extensions:["mvb","m13","m14"]},"application/x-msmetafile":{source:"apache",extensions:["wmf","wmz","emf","emz"]},"application/x-msmoney":{source:"apache",extensions:["mny"]},"application/x-mspublisher":{source:"apache",extensions:["pub"]},"application/x-msschedule":{source:"apache",extensions:["scd"]},"application/x-msterminal":{source:"apache",extensions:["trm"]},"application/x-mswrite":{source:"apache",extensions:["wri"]},"application/x-netcdf":{source:"apache",extensions:["nc","cdf"]},"application/x-nzb":{source:"apache",extensions:["nzb"]},"application/x-perl":{source:"nginx",extensions:["pl","pm"]},"application/x-pilot":{source:"nginx",extensions:["prc","pdb"]},"application/x-pkcs12":{source:"apache",extensions:["p12","pfx"]},"application/x-pkcs7-certificates":{source:"apache",extensions:["p7b","spc"]},"application/x-pkcs7-certreqresp":{source:"apache",extensions:["p7r"]},"application/x-rar-compressed":{source:"apache",extensions:["rar"]},"application/x-redhat-package-manager":{source:"nginx",extensions:["rpm"]},"application/x-research-info-systems":{source:"apache",extensions:["ris"]},"application/x-sea":{source:"nginx",extensions:["sea"]},"application/x-sh":{source:"apache",extensions:["sh"]},"application/x-shar":{source:"apache",extensions:["shar"]},"application/x-shockwave-flash":{source:"apache",extensions:["swf"]},"application/x-silverlight-app":{source:"apache",extensions:["xap"]},"application/x-sql":{source:"apache",extensions:["sql"]},"application/x-stuffit":{source:"apache",extensions:["sit"]},"application/x-stuffitx":{source:"apache",extensions:["sitx"]},"application/x-subrip":{source:"apache",extensions:["srt"]},"application/x-sv4cpio":{source:"apache",extensions:["sv4cpio"]},"application/x-sv4crc":{source:"apache",extensions:["sv4crc"]},"application/x-t3vm-image":{source:"apache",extensions:["t3"]},"application/x-tads":{source:"apache",extensions:["gam"]},"application/x-tar":{source:"apache",extensions:["tar"]},"application/x-tcl":{source:"apache",extensions:["tcl","tk"]},"application/x-tex":{source:"apache",extensions:["tex"]},"application/x-tex-tfm":{source:"apache",extensions:["tfm"]},"application/x-texinfo":{source:"apache",extensions:["texinfo","texi"]},"application/x-tgif":{source:"apache",extensions:["obj"]},"application/x-ustar":{source:"apache",extensions:["ustar"]},"application/x-wais-source":{source:"apache",extensions:["src"]},"application/x-x509-ca-cert":{source:"iana",extensions:["der","crt","pem"]},"application/x-xfig":{source:"apache",extensions:["fig"]},"application/x-xliff+xml":{source:"apache",extensions:["xlf"]},"application/x-xpinstall":{source:"apache",extensions:["xpi"]},"application/x-xz":{source:"apache",extensions:["xz"]},"application/x-zmachine":{source:"apache",extensions:["z1","z2","z3","z4","z5","z6","z7","z8"]},"application/xaml+xml":{source:"apache",extensions:["xaml"]},"application/xcap-att+xml":{source:"iana",extensions:["xav"]},"application/xcap-caps+xml":{source:"iana",extensions:["xca"]},"application/xcap-diff+xml":{source:"iana",extensions:["xdf"]},"application/xcap-el+xml":{source:"iana",extensions:["xel"]},"application/xcap-ns+xml":{source:"iana",extensions:["xns"]},"application/xenc+xml":{source:"iana",extensions:["xenc"]},"application/xhtml+xml":{source:"iana",extensions:["xhtml","xht"]},"application/xliff+xml":{source:"iana",extensions:["xlf"]},"application/xml":{source:"iana",extensions:["xml","xsl","xsd","rng"]},"application/xml-dtd":{source:"iana",extensions:["dtd"]},"application/xop+xml":{source:"iana",extensions:["xop"]},"application/xproc+xml":{source:"apache",extensions:["xpl"]},"application/xslt+xml":{source:"iana",extensions:["xsl","xslt"]},"application/xspf+xml":{source:"apache",extensions:["xspf"]},"application/xv+xml":{source:"iana",extensions:["mxml","xhvml","xvml","xvm"]},"application/yaml":{source:"iana",extensions:["yaml","yml"]},"application/yang":{source:"iana",extensions:["yang"]},"application/yin+xml":{source:"iana",extensions:["yin"]},"application/zip":{source:"iana",extensions:["zip"]},"audio/3gpp":{source:"iana",extensions:["3gpp"]},"audio/adpcm":{source:"apache",extensions:["adp"]},"audio/amr":{source:"iana",extensions:["amr"]},"audio/basic":{source:"iana",extensions:["au","snd"]},"audio/midi":{source:"apache",extensions:["mid","midi","kar","rmi"]},"audio/mobile-xmf":{source:"iana",extensions:["mxmf"]},"audio/mp4":{source:"iana",extensions:["m4a","mp4a"]},"audio/mpeg":{source:"iana",extensions:["mpga","mp2","mp2a","mp3","m2a","m3a"]},"audio/ogg":{source:"iana",extensions:["oga","ogg","spx","opus"]},"audio/s3m":{source:"apache",extensions:["s3m"]},"audio/silk":{source:"apache",extensions:["sil"]},"audio/vnd.dece.audio":{source:"iana",extensions:["uva","uvva"]},"audio/vnd.digital-winds":{source:"iana",extensions:["eol"]},"audio/vnd.dra":{source:"iana",extensions:["dra"]},"audio/vnd.dts":{source:"iana",extensions:["dts"]},"audio/vnd.dts.hd":{source:"iana",extensions:["dtshd"]},"audio/vnd.lucent.voice":{source:"iana",extensions:["lvp"]},"audio/vnd.ms-playready.media.pya":{source:"iana",extensions:["pya"]},"audio/vnd.nuera.ecelp4800":{source:"iana",extensions:["ecelp4800"]},"audio/vnd.nuera.ecelp7470":{source:"iana",extensions:["ecelp7470"]},"audio/vnd.nuera.ecelp9600":{source:"iana",extensions:["ecelp9600"]},"audio/vnd.rip":{source:"iana",extensions:["rip"]},"audio/webm":{source:"apache",extensions:["weba"]},"audio/x-aac":{source:"apache",extensions:["aac"]},"audio/x-aiff":{source:"apache",extensions:["aif","aiff","aifc"]},"audio/x-caf":{source:"apache",extensions:["caf"]},"audio/x-flac":{source:"apache",extensions:["flac"]},"audio/x-m4a":{source:"nginx",extensions:["m4a"]},"audio/x-matroska":{source:"apache",extensions:["mka"]},"audio/x-mpegurl":{source:"apache",extensions:["m3u"]},"audio/x-ms-wax":{source:"apache",extensions:["wax"]},"audio/x-ms-wma":{source:"apache",extensions:["wma"]},"audio/x-pn-realaudio":{source:"apache",extensions:["ram","ra"]},"audio/x-pn-realaudio-plugin":{source:"apache",extensions:["rmp"]},"audio/x-realaudio":{source:"nginx",extensions:["ra"]},"audio/x-wav":{source:"apache",extensions:["wav"]},"audio/x-gsm":{source:"apache",extensions:["gsm"]},"audio/xm":{source:"apache",extensions:["xm"]},"image/aces":{source:"iana",extensions:["exr"]},"image/avci":{source:"iana",extensions:["avci"]},"image/avcs":{source:"iana",extensions:["avcs"]},"image/avif":{source:"iana",extensions:["avif"]},"image/bmp":{source:"iana",extensions:["bmp"]},"image/cgm":{source:"iana",extensions:["cgm"]},"image/dicom-rle":{source:"iana",extensions:["drle"]},"image/emf":{source:"iana",extensions:["emf"]},"image/fits":{source:"iana",extensions:["fits"]},"image/g3fax":{source:"iana",extensions:["g3"]},"image/gif":{source:"iana",extensions:["gif"]},"image/heic":{source:"iana",extensions:["heic"]},"image/heic-sequence":{source:"iana",extensions:["heics"]},"image/heif":{source:"iana",extensions:["heif"]},"image/heif-sequence":{source:"iana",extensions:["heifs"]},"image/hej2k":{source:"iana",extensions:["hej2"]},"image/hsj2":{source:"iana",extensions:["hsj2"]},"image/ief":{source:"iana",extensions:["ief"]},"image/jls":{source:"iana",extensions:["jls"]},"image/jp2":{source:"iana",extensions:["jp2","jpg2"]},"image/jpeg":{source:"iana",extensions:["jpeg","jpg","jpe","jfif","pjpeg","pjp"]},"image/jph":{source:"iana",extensions:["jph"]},"image/jphc":{source:"iana",extensions:["jhc"]},"image/jpm":{source:"iana",extensions:["jpm"]},"image/jpx":{source:"iana",extensions:["jpx","jpf"]},"image/jxr":{source:"iana",extensions:["jxr"]},"image/jxra":{source:"iana",extensions:["jxra"]},"image/jxrs":{source:"iana",extensions:["jxrs"]},"image/jxs":{source:"iana",extensions:["jxs"]},"image/jxsc":{source:"iana",extensions:["jxsc"]},"image/jxsi":{source:"iana",extensions:["jxsi"]},"image/jxss":{source:"iana",extensions:["jxss"]},"image/ktx":{source:"iana",extensions:["ktx"]},"image/ktx2":{source:"iana",extensions:["ktx2"]},"image/png":{source:"iana",extensions:["png"]},"image/prs.btif":{source:"iana",extensions:["btif"]},"image/prs.pti":{source:"iana",extensions:["pti"]},"image/sgi":{source:"apache",extensions:["sgi"]},"image/svg+xml":{source:"iana",extensions:["svg","svgz"]},"image/t38":{source:"iana",extensions:["t38"]},"image/tiff":{source:"iana",extensions:["tif","tiff"]},"image/tiff-fx":{source:"iana",extensions:["tfx"]},"image/vnd.adobe.photoshop":{source:"iana",extensions:["psd"]},"image/vnd.airzip.accelerator.azv":{source:"iana",extensions:["azv"]},"image/vnd.dece.graphic":{source:"iana",extensions:["uvi","uvvi","uvg","uvvg"]},"image/vnd.djvu":{source:"iana",extensions:["djvu","djv"]},"image/vnd.dvb.subtitle":{source:"iana",extensions:["sub"]},"image/vnd.dwg":{source:"iana",extensions:["dwg"]},"image/vnd.dxf":{source:"iana",extensions:["dxf"]},"image/vnd.fastbidsheet":{source:"iana",extensions:["fbs"]},"image/vnd.fpx":{source:"iana",extensions:["fpx"]},"image/vnd.fst":{source:"iana",extensions:["fst"]},"image/vnd.fujixerox.edmics-mmr":{source:"iana",extensions:["mmr"]},"image/vnd.fujixerox.edmics-rlc":{source:"iana",extensions:["rlc"]},"image/vnd.microsoft.icon":{source:"iana",extensions:["ico"]},"image/vnd.ms-modi":{source:"iana",extensions:["mdi"]},"image/vnd.ms-photo":{source:"apache",extensions:["wdp"]},"image/vnd.net-fpx":{source:"iana",extensions:["npx"]},"image/vnd.pco.b16":{source:"iana",extensions:["b16"]},"image/vnd.tencent.tap":{source:"iana",extensions:["tap"]},"image/vnd.valve.source.texture":{source:"iana",extensions:["vtf"]},"image/vnd.wap.wbmp":{source:"iana",extensions:["wbmp"]},"image/vnd.xiff":{source:"iana",extensions:["xif"]},"image/vnd.zbrush.pcx":{source:"iana",extensions:["pcx"]},"image/webp":{source:"apache",extensions:["webp"]},"image/wmf":{source:"iana",extensions:["wmf"]},"image/x-3ds":{source:"apache",extensions:["3ds"]},"image/x-cmu-raster":{source:"apache",extensions:["ras"]},"image/x-cmx":{source:"apache",extensions:["cmx"]},"image/x-freehand":{source:"apache",extensions:["fh","fhc","fh4","fh5","fh7"]},"image/x-icon":{source:"apache",extensions:["ico"]},"image/x-jng":{source:"nginx",extensions:["jng"]},"image/x-mrsid-image":{source:"apache",extensions:["sid"]},"image/x-ms-bmp":{source:"nginx",extensions:["bmp"]},"image/x-pcx":{source:"apache",extensions:["pcx"]},"image/x-pict":{source:"apache",extensions:["pic","pct"]},"image/x-portable-anymap":{source:"apache",extensions:["pnm"]},"image/x-portable-bitmap":{source:"apache",extensions:["pbm"]},"image/x-portable-graymap":{source:"apache",extensions:["pgm"]},"image/x-portable-pixmap":{source:"apache",extensions:["ppm"]},"image/x-rgb":{source:"apache",extensions:["rgb"]},"image/x-tga":{source:"apache",extensions:["tga"]},"image/x-xbitmap":{source:"apache",extensions:["xbm"]},"image/x-xpixmap":{source:"apache",extensions:["xpm"]},"image/x-xwindowdump":{source:"apache",extensions:["xwd"]},"text/cache-manifest":{source:"iana",extensions:["appcache","manifest"]},"text/calendar":{source:"iana",extensions:["ics","ifb"]},"text/css":{source:"iana",charset:"UTF-8",extensions:["css"]},"text/csv":{source:"iana",extensions:["csv"]},"text/html":{source:"iana",extensions:["html","htm","shtml"]},"text/markdown":{source:"iana",extensions:["markdown","md"]},"text/mathml":{source:"nginx",extensions:["mml"]},"text/n3":{source:"iana",charset:"UTF-8",extensions:["n3"]},"text/plain":{source:"iana",extensions:["txt","text","conf","def","list","log","in","ini"]},"text/prs.lines.tag":{source:"iana",extensions:["dsc"]},"text/richtext":{source:"iana",extensions:["rtx"]},"text/rtf":{source:"iana",extensions:["rtf"]},"text/sgml":{source:"iana",extensions:["sgml","sgm"]},"text/shex":{source:"iana",extensions:["shex"]},"text/spdx":{source:"iana",extensions:["spdx"]},"text/tab-separated-values":{source:"iana",extensions:["tsv"]},"text/troff":{source:"iana",extensions:["t","tr","roff","man","me","ms"]},"text/turtle":{source:"iana",charset:"UTF-8",extensions:["ttl"]},"text/uri-list":{source:"iana",extensions:["uri","uris","urls"]},"text/vcard":{source:"iana",extensions:["vcard"]},"text/vnd.curl":{source:"iana",extensions:["curl"]},"text/vnd.curl.dcurl":{source:"apache",extensions:["dcurl"]},"text/vnd.curl.mcurl":{source:"apache",extensions:["mcurl"]},"text/vnd.curl.scurl":{source:"apache",extensions:["scurl"]},"text/vnd.dvb.subtitle":{source:"iana",extensions:["sub"]},"text/vnd.familysearch.gedcom":{source:"iana",extensions:["ged"]},"text/vnd.fly":{source:"iana",extensions:["fly"]},"text/vnd.fmi.flexstor":{source:"iana",extensions:["flx"]},"text/vnd.graphviz":{source:"iana",extensions:["gv"]},"text/vnd.in3d.3dml":{source:"iana",extensions:["3dml"]},"text/vnd.in3d.spot":{source:"iana",extensions:["spot"]},"text/vnd.sun.j2me.app-descriptor":{source:"iana",charset:"UTF-8",extensions:["jad"]},"text/vnd.wap.wml":{source:"iana",extensions:["wml"]},"text/vnd.wap.wmlscript":{source:"iana",extensions:["wmls"]},"text/vtt":{source:"iana",charset:"UTF-8",extensions:["vtt"]},"text/x-asm":{source:"apache",extensions:["s","asm"]},"text/x-c":{source:"apache",extensions:["c","cc","cxx","cpp","h","hh","dic"]},"text/x-component":{source:"nginx",extensions:["htc"]},"text/x-fortran":{source:"apache",extensions:["f","for","f77","f90"]},"text/x-java-source":{source:"apache",extensions:["java"]},"text/x-nfo":{source:"apache",extensions:["nfo"]},"text/x-opml":{source:"apache",extensions:["opml"]},"text/x-pascal":{source:"apache",extensions:["p","pas"]},"text/x-setext":{source:"apache",extensions:["etx"]},"text/x-sfv":{source:"apache",extensions:["sfv"]},"text/x-uuencode":{source:"apache",extensions:["uu"]},"text/x-vcalendar":{source:"apache",extensions:["vcs"]},"text/x-vcard":{source:"apache",extensions:["vcf"]},"text/xml":{source:"iana",extensions:["xml"]},"video/3gpp":{source:"iana",extensions:["3gp","3gpp"]},"video/3gpp2":{source:"iana",extensions:["3g2"]},"video/h261":{source:"iana",extensions:["h261"]},"video/h263":{source:"iana",extensions:["h263"]},"video/h264":{source:"iana",extensions:["h264"]},"video/iso.segment":{source:"iana",extensions:["m4s"]},"video/jpeg":{source:"iana",extensions:["jpgv"]},"video/jpm":{source:"apache",extensions:["jpm","jpgm"]},"video/mj2":{source:"iana",extensions:["mj2","mjp2"]},"video/mp2t":{source:"iana",extensions:["ts"]},"video/mp4":{source:"iana",extensions:["mp4","mp4v","mpg4"]},"video/mpeg":{source:"iana",extensions:["mpeg","mpg","mpe","m1v","m2v"]},"video/ogg":{source:"iana",extensions:["ogv"]},"video/quicktime":{source:"iana",extensions:["qt","mov"]},"video/vnd.dece.hd":{source:"iana",extensions:["uvh","uvvh"]},"video/vnd.dece.mobile":{source:"iana",extensions:["uvm","uvvm"]},"video/vnd.dece.pd":{source:"iana",extensions:["uvp","uvvp"]},"video/vnd.dece.sd":{source:"iana",extensions:["uvs","uvvs"]},"video/vnd.dece.video":{source:"iana",extensions:["uvv","uvvv"]},"video/vnd.dvb.file":{source:"iana",extensions:["dvb"]},"video/vnd.fvt":{source:"iana",extensions:["fvt"]},"video/vnd.mpegurl":{source:"iana",extensions:["mxu","m4u"]},"video/vnd.ms-playready.media.pyv":{source:"iana",extensions:["pyv"]},"video/vnd.uvvu.mp4":{source:"iana",extensions:["uvu","uvvu"]},"video/vnd.vivo":{source:"iana",extensions:["viv"]},"video/webm":{source:"apache",extensions:["webm"]},"video/x-f4v":{source:"apache",extensions:["f4v"]},"video/x-fli":{source:"apache",extensions:["fli"]},"video/x-flv":{source:"apache",extensions:["flv"]},"video/x-m4v":{source:"apache",extensions:["m4v"]},"video/x-matroska":{source:"apache",extensions:["mkv","mk3d","mks"]},"video/x-mng":{source:"apache",extensions:["mng"]},"video/x-ms-asf":{source:"apache",extensions:["asf","asx"]},"video/x-ms-vob":{source:"apache",extensions:["vob"]},"video/x-ms-wm":{source:"apache",extensions:["wm"]},"video/x-ms-wmv":{source:"apache",extensions:["wmv"]},"video/x-ms-wmx":{source:"apache",extensions:["wmx"]},"video/x-ms-wvx":{source:"apache",extensions:["wvx"]},"video/x-msvideo":{source:"apache",extensions:["avi"]},"video/x-sgi-movie":{source:"apache",extensions:["movie"]},"video/x-smv":{source:"apache",extensions:["smv"]},"chemical/x-cdx":{source:"apache",extensions:["cdx"]},"chemical/x-cif":{source:"apache",extensions:["cif"]},"chemical/x-cmdf":{source:"apache",extensions:["cmdf"]},"chemical/x-cml":{source:"apache",extensions:["cml"]},"chemical/x-csml":{source:"apache",extensions:["csml"]},"chemical/x-xyz":{source:"apache",extensions:["xyz"]},"font/collection":{source:"iana",extensions:["ttc"]},"font/otf":{source:"iana",extensions:["otf"]},"font/ttf":{source:"iana",extensions:["ttf"]},"font/woff":{source:"iana",extensions:["woff"]},"font/woff2":{source:"iana",extensions:["woff2"]},"message/disposition-notification":{source:"iana",extensions:["disposition-notification"]},"message/global":{source:"iana",extensions:["u8msg"]},"message/global-delivery-status":{source:"iana",extensions:["u8dsn"]},"message/global-disposition-notification":{source:"iana",extensions:["u8mdn"]},"message/global-headers":{source:"iana",extensions:["u8hdr"]},"message/rfc822":{source:"iana",extensions:["eml","mime"]},"message/vnd.wfa.wsc":{source:"iana",extensions:["wsc"]},"model/3mf":{source:"iana",extensions:["3mf"]},"model/gltf+json":{source:"iana",extensions:["gltf"]},"model/gltf-binary":{source:"iana",extensions:["glb"]},"model/iges":{source:"iana",extensions:["igs","iges"]},"model/mesh":{source:"iana",extensions:["msh","mesh","silo"]},"model/mtl":{source:"iana",extensions:["mtl"]},"model/obj":{source:"iana",extensions:["obj"]},"model/step":{source:"iana",extensions:[".p21",".stp",".step",".stpnc",".210"]},"model/step+xml":{source:"iana",extensions:["stpx"]},"model/step+zip":{source:"iana",extensions:["stpz"]},"model/step-xml+zip":{source:"iana",extensions:["stpxz"]},"model/stl":{source:"iana",extensions:["stl"]},"model/vnd.collada+xml":{source:"iana",extensions:["dae"]},"model/vnd.dwf":{source:"iana",extensions:["dwf"]},"model/vnd.gdl":{source:"iana",extensions:["gdl"]},"model/vnd.gtw":{source:"iana",extensions:["gtw"]},"model/vnd.mts":{source:"iana",extensions:["mts"]},"model/vnd.opengex":{source:"iana",extensions:["ogex"]},"model/vnd.parasolid.transmit.binary":{source:"iana",extensions:["x_b"]},"model/vnd.parasolid.transmit.text":{source:"iana",extensions:["x_t"]},"model/vnd.sap.vds":{source:"iana",extensions:["vds"]},"model/vnd.usdz+zip":{source:"iana",extensions:["usdz"]},"model/vnd.valve.source.compiled-map":{source:"iana",extensions:["bsp"]},"model/vnd.vtu":{source:"iana",extensions:["vtu"]},"model/vrml":{source:"iana",extensions:["wrl","vrml"]},"model/x3d+binary":{source:"apache",extensions:["x3db","x3dbz"]},"model/x3d+fastinfoset":{source:"iana",extensions:["x3db"]},"model/x3d+vrml":{source:"apache",extensions:["x3dv","x3dvz"]},"model/x3d+xml":{source:"iana",extensions:["x3d","x3dz"]},"model/x3d-vrml":{source:"iana",extensions:["x3dv"]},"x-conference/x-cooltalk":{source:"apache",extensions:["ice"]}},nf={},nd={},nm=!1,ng=Symbol.for("effect/Either"),nb={...tg,[ng]:{_R:e=>e},[eV](){return this.toJSON()},toString(){return eY(this.toJSON())}},nx=Object.assign(Object.create(nb),{_tag:"Right",_op:"Right",[eB](e){return nv(e)&&n_(e)&&eJ(this.right,e.right)},[eR](){return ej(eN(this._tag))(eN(this.right))},toJSON(){return{_id:"Either",_tag:this._tag,right:eG(this.right)}}}),ny=Object.assign(Object.create(nb),{_tag:"Left",_op:"Left",[eB](e){return nv(e)&&nS(e)&&eJ(this.left,e.left)},[eR](){return ej(eN(this._tag))(eN(this.left))},toJSON(){return{_id:"Either",_tag:this._tag,left:eG(this.left)}}}),nv=e=>ec(e,ng),nS=e=>"Left"===e._tag,n_=e=>"Right"===e._tag,nw=e=>{let t=Object.create(ny);return t.left=e,t},nk=e=>{let t=Object.create(nx);return t.right=e,t},nC=U(2,(e,t)=>tI(e)?nw(t()):nk(e.value)),nE=U(2,(e,{onLeft:t,onRight:r})=>nS(e)?nw(t(e.left)):nk(r(e.right))),nO=U(2,(e,t)=>nS(e)?nw(t(e.left)):nk(e.right)),nI=U(2,(e,t)=>n_(e)?nk(t(e.right)):nw(e.left)),nF=U(2,(e,{onLeft:t,onRight:r})=>nS(e)?t(e.left):r(e.right)),nT=nF({onLeft:q,onRight:q}),nR=U(2,(e,t)=>{if(n_(e))return e.right;throw t(e.left)}),nN=nR(()=>Error("getOrThrow called on a Left")),nA=Symbol.for("effect/Encoding/errors/Decode"),nj=(e,t)=>{let r={_tag:"DecodeException",[nA]:nA,input:e};return Z(t)&&(r.message=t),r},nM=Symbol.for("effect/Encoding/errors/Encode"),nz=new TextEncoder,nD=e=>{let t,r=e.length,n="";for(t=2;t<r;t+=3)n+=nU[e[t-2]>>2],n+=nU[(3&e[t-2])<<4|e[t-1]>>4],n+=nU[(15&e[t-1])<<2|e[t]>>6],n+=nU[63&e[t]];return t===r+1&&(n+=nU[e[t-2]>>2],n+=nU[(3&e[t-2])<<4],n+="=="),t===r&&(n+=nU[e[t-2]>>2],n+=nU[(3&e[t-2])<<4|e[t-1]>>4],n+=nU[(15&e[t-1])<<2],n+="="),n},n$=e=>{let t=nP(e),r=t.length;if(r%4!=0)return nw(nj(t,`Length must be a multiple of 4, but is ${r}`));let n=t.indexOf("=");if(-1!==n&&(n<r-2||n===r-2&&"="!==t[r-1]))return nw(nj(t,"Found a '=' character, but it is not at the end"));try{let e=t.endsWith("==")?2:+!!t.endsWith("="),n=new Uint8Array(r/4*3-e);for(let e=0,i=0;e<r;e+=4,i+=3){let r=nL(t.charCodeAt(e))<<18|nL(t.charCodeAt(e+1))<<12|nL(t.charCodeAt(e+2))<<6|nL(t.charCodeAt(e+3));n[i]=r>>16,n[i+1]=r>>8&255,n[i+2]=255&r}return nk(n)}catch(e){return nw(nj(t,e instanceof Error?e.message:"Invalid input"))}},nP=e=>e.replace(/[\n\r]/g,"");function nL(e){if(e>=nq.length)throw TypeError(`Invalid character ${String.fromCharCode(e)}`);let t=nq[e];if(255===t)throw TypeError(`Invalid character ${String.fromCharCode(e)}`);return t}let nU=["A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z","0","1","2","3","4","5","6","7","8","9","+","/"],nq=[255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,62,255,255,255,63,52,53,54,55,56,57,58,59,60,61,255,255,255,0,255,255,255,0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,255,255,255,255,255,255,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51],nB=e=>{let t=nP(e),r=t.length;if(r%4==1)return nw(nj(t,`Length should be a multiple of 4, but is ${r}`));if(!/^[-_A-Z0-9]*?={0,2}$/i.test(t))return nw(nj(t,"Invalid input"));let n=r%4==2?`${t}==`:r%4==3?`${t}=`:t;return n$(n=n.replace(/-/g,"+").replace(/_/g,"/"))},nJ=e=>{let t="";for(let r=0;r<e.length;++r)t+=nK[e[r]];return t},nH=e=>{let t=new TextEncoder().encode(e);if(t.length%2!=0)return nw(nj(e,`Length must be a multiple of 2, but is ${t.length}`));try{let e=t.length/2,r=new Uint8Array(e);for(let n=0;n<e;n++){let e=nW(t[2*n]),i=nW(t[2*n+1]);r[n]=e<<4|i}return nk(r)}catch(t){return nw(nj(e,t instanceof Error?t.message:"Invalid input"))}},nK=["00","01","02","03","04","05","06","07","08","09","0a","0b","0c","0d","0e","0f","10","11","12","13","14","15","16","17","18","19","1a","1b","1c","1d","1e","1f","20","21","22","23","24","25","26","27","28","29","2a","2b","2c","2d","2e","2f","30","31","32","33","34","35","36","37","38","39","3a","3b","3c","3d","3e","3f","40","41","42","43","44","45","46","47","48","49","4a","4b","4c","4d","4e","4f","50","51","52","53","54","55","56","57","58","59","5a","5b","5c","5d","5e","5f","60","61","62","63","64","65","66","67","68","69","6a","6b","6c","6d","6e","6f","70","71","72","73","74","75","76","77","78","79","7a","7b","7c","7d","7e","7f","80","81","82","83","84","85","86","87","88","89","8a","8b","8c","8d","8e","8f","90","91","92","93","94","95","96","97","98","99","9a","9b","9c","9d","9e","9f","a0","a1","a2","a3","a4","a5","a6","a7","a8","a9","aa","ab","ac","ad","ae","af","b0","b1","b2","b3","b4","b5","b6","b7","b8","b9","ba","bb","bc","bd","be","bf","c0","c1","c2","c3","c4","c5","c6","c7","c8","c9","ca","cb","cc","cd","ce","cf","d0","d1","d2","d3","d4","d5","d6","d7","d8","d9","da","db","dc","dd","de","df","e0","e1","e2","e3","e4","e5","e6","e7","e8","e9","ea","eb","ec","ed","ee","ef","f0","f1","f2","f3","f4","f5","f6","f7","f8","f9","fa","fb","fc","fd","fe","ff"],nW=e=>{if(48<=e&&e<=57)return e-48;if(97<=e&&e<=102)return e-97+10;if(65<=e&&e<=70)return e-65+10;throw TypeError("Invalid input")},nV=e=>"string"==typeof e?nJ(nz.encode(e)):nJ(e),nG=e=>nH(e),nY=e=>(t,r)=>t===r||e(t,r),nZ=(e,t)=>e===t,nQ=U(2,(e,t)=>nY((r,n)=>e(t(r),t(n)))),nX=nQ(nZ,e=>e.getTime()),n0=e=>nY((t,r)=>{if(t.length!==r.length)return!1;for(let n=0;n<t.length;n++)if(!e(t[n],r[n]))return!1;return!0}),n1="effect/Redacted",n2=Y("effect/Redacted/redactedRegistry",()=>new WeakMap),n3=Symbol.for(n1),n5={[n3]:{_A:e=>e},pipe(){return e6(this,arguments)},toString:()=>"<redacted>",toJSON:()=>"<redacted>",[eV]:()=>"<redacted>",[eR](){return W(eN(n1),ej(eN(n2.get(this))),eq(this))},[eB](e){return n4(e)&&eJ(n2.get(this),n2.get(e))}},n4=e=>ec(e,n3),n6=e=>{let t=Object.create(n5);return n2.set(t,e),t},n8=e=>{if(n2.has(e))return n2.get(e);throw Error("Unable to get redacted value")},n7=e=>nY((t,r)=>e(n8(t),n8(r))),n9={alphabet:"abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",minLength:0,blocklist:new Set(["0rgasm","1d10t","1d1ot","1di0t","1diot","1eccacu10","1eccacu1o","1eccacul0","1eccaculo","1mbec11e","1mbec1le","1mbeci1e","1mbecile","a11upat0","a11upato","a1lupat0","a1lupato","aand","ah01e","ah0le","aho1e","ahole","al1upat0","al1upato","allupat0","allupato","ana1","ana1e","anal","anale","anus","arrapat0","arrapato","arsch","arse","ass","b00b","b00be","b01ata","b0ceta","b0iata","b0ob","b0obe","b0sta","b1tch","b1te","b1tte","ba1atkar","balatkar","bastard0","bastardo","batt0na","battona","bitch","bite","bitte","bo0b","bo0be","bo1ata","boceta","boiata","boob","boobe","bosta","bran1age","bran1er","bran1ette","bran1eur","bran1euse","branlage","branler","branlette","branleur","branleuse","c0ck","c0g110ne","c0g11one","c0g1i0ne","c0g1ione","c0gl10ne","c0gl1one","c0gli0ne","c0glione","c0na","c0nnard","c0nnasse","c0nne","c0u111es","c0u11les","c0u1l1es","c0u1lles","c0ui11es","c0ui1les","c0uil1es","c0uilles","c11t","c11t0","c11to","c1it","c1it0","c1ito","cabr0n","cabra0","cabrao","cabron","caca","cacca","cacete","cagante","cagar","cagare","cagna","cara1h0","cara1ho","caracu10","caracu1o","caracul0","caraculo","caralh0","caralho","cazz0","cazz1mma","cazzata","cazzimma","cazzo","ch00t1a","ch00t1ya","ch00tia","ch00tiya","ch0d","ch0ot1a","ch0ot1ya","ch0otia","ch0otiya","ch1asse","ch1avata","ch1er","ch1ng0","ch1ngadaz0s","ch1ngadazos","ch1ngader1ta","ch1ngaderita","ch1ngar","ch1ngo","ch1ngues","ch1nk","chatte","chiasse","chiavata","chier","ching0","chingadaz0s","chingadazos","chingader1ta","chingaderita","chingar","chingo","chingues","chink","cho0t1a","cho0t1ya","cho0tia","cho0tiya","chod","choot1a","choot1ya","chootia","chootiya","cl1t","cl1t0","cl1to","clit","clit0","clito","cock","cog110ne","cog11one","cog1i0ne","cog1ione","cogl10ne","cogl1one","cogli0ne","coglione","cona","connard","connasse","conne","cou111es","cou11les","cou1l1es","cou1lles","coui11es","coui1les","couil1es","couilles","cracker","crap","cu10","cu1att0ne","cu1attone","cu1er0","cu1ero","cu1o","cul0","culatt0ne","culattone","culer0","culero","culo","cum","cunt","d11d0","d11do","d1ck","d1ld0","d1ldo","damn","de1ch","deich","depp","di1d0","di1do","dick","dild0","dildo","dyke","encu1e","encule","enema","enf01re","enf0ire","enfo1re","enfoire","estup1d0","estup1do","estupid0","estupido","etr0n","etron","f0da","f0der","f0ttere","f0tters1","f0ttersi","f0tze","f0utre","f1ca","f1cker","f1ga","fag","fica","ficker","figa","foda","foder","fottere","fotters1","fottersi","fotze","foutre","fr0c10","fr0c1o","fr0ci0","fr0cio","fr0sc10","fr0sc1o","fr0sci0","fr0scio","froc10","froc1o","froci0","frocio","frosc10","frosc1o","frosci0","froscio","fuck","g00","g0o","g0u1ne","g0uine","gandu","go0","goo","gou1ne","gouine","gr0gnasse","grognasse","haram1","harami","haramzade","hund1n","hundin","id10t","id1ot","idi0t","idiot","imbec11e","imbec1le","imbeci1e","imbecile","j1zz","jerk","jizz","k1ke","kam1ne","kamine","kike","leccacu10","leccacu1o","leccacul0","leccaculo","m1erda","m1gn0tta","m1gnotta","m1nch1a","m1nchia","m1st","mam0n","mamahuev0","mamahuevo","mamon","masturbat10n","masturbat1on","masturbate","masturbati0n","masturbation","merd0s0","merd0so","merda","merde","merdos0","merdoso","mierda","mign0tta","mignotta","minch1a","minchia","mist","musch1","muschi","n1gger","neger","negr0","negre","negro","nerch1a","nerchia","nigger","orgasm","p00p","p011a","p01la","p0l1a","p0lla","p0mp1n0","p0mp1no","p0mpin0","p0mpino","p0op","p0rca","p0rn","p0rra","p0uff1asse","p0uffiasse","p1p1","p1pi","p1r1a","p1rla","p1sc10","p1sc1o","p1sci0","p1scio","p1sser","pa11e","pa1le","pal1e","palle","pane1e1r0","pane1e1ro","pane1eir0","pane1eiro","panele1r0","panele1ro","paneleir0","paneleiro","patakha","pec0r1na","pec0rina","pecor1na","pecorina","pen1s","pendej0","pendejo","penis","pip1","pipi","pir1a","pirla","pisc10","pisc1o","pisci0","piscio","pisser","po0p","po11a","po1la","pol1a","polla","pomp1n0","pomp1no","pompin0","pompino","poop","porca","porn","porra","pouff1asse","pouffiasse","pr1ck","prick","pussy","put1za","puta","puta1n","putain","pute","putiza","puttana","queca","r0mp1ba11e","r0mp1ba1le","r0mp1bal1e","r0mp1balle","r0mpiba11e","r0mpiba1le","r0mpibal1e","r0mpiballe","rand1","randi","rape","recch10ne","recch1one","recchi0ne","recchione","retard","romp1ba11e","romp1ba1le","romp1bal1e","romp1balle","rompiba11e","rompiba1le","rompibal1e","rompiballe","ruff1an0","ruff1ano","ruffian0","ruffiano","s1ut","sa10pe","sa1aud","sa1ope","sacanagem","sal0pe","salaud","salope","saugnapf","sb0rr0ne","sb0rra","sb0rrone","sbattere","sbatters1","sbattersi","sborr0ne","sborra","sborrone","sc0pare","sc0pata","sch1ampe","sche1se","sche1sse","scheise","scheisse","schlampe","schwachs1nn1g","schwachs1nnig","schwachsinn1g","schwachsinnig","schwanz","scopare","scopata","sexy","sh1t","shit","slut","sp0mp1nare","sp0mpinare","spomp1nare","spompinare","str0nz0","str0nza","str0nzo","stronz0","stronza","stronzo","stup1d","stupid","succh1am1","succh1ami","succhiam1","succhiami","sucker","t0pa","tapette","test1c1e","test1cle","testic1e","testicle","tette","topa","tr01a","tr0ia","tr0mbare","tr1ng1er","tr1ngler","tring1er","tringler","tro1a","troia","trombare","turd","twat","vaffancu10","vaffancu1o","vaffancul0","vaffanculo","vag1na","vagina","verdammt","verga","w1chsen","wank","wichsen","x0ch0ta","x0chota","xana","xoch0ta","xochota","z0cc01a","z0cc0la","z0cco1a","z0ccola","z1z1","z1zi","ziz1","zizi","zocc01a","zocc0la","zocco1a","zoccola"])};class ie{constructor(e){var t,r,n;let i=null!=(t=null==e?void 0:e.alphabet)?t:n9.alphabet,s=null!=(r=null==e?void 0:e.minLength)?r:n9.minLength,a=null!=(n=null==e?void 0:e.blocklist)?n:n9.blocklist;if(new Blob([i]).size!==i.length)throw Error("Alphabet cannot contain multibyte characters");if(i.length<3)throw Error("Alphabet length must be at least 3");if(new Set(i).size!==i.length)throw Error("Alphabet must contain unique characters");if("number"!=typeof s||s<0||s>255)throw Error("Minimum length has to be between 0 and 255");let o=new Set,l=i.toLowerCase().split("");for(let e of a)if(e.length>=3){let t=e.toLowerCase(),r=t.split("");r.filter(e=>l.includes(e)).length===r.length&&o.add(t)}this.alphabet=this.shuffle(i),this.minLength=s,this.blocklist=o}encode(e){if(0===e.length)return"";if(e.filter(e=>e>=0&&e<=this.maxValue()).length!==e.length)throw Error(`Encoding supports numbers between 0 and ${this.maxValue()}`);return this.encodeNumbers(e)}decode(e){let t=[];if(""===e)return t;let r=this.alphabet.split("");for(let n of e.split(""))if(!r.includes(n))return t;let n=e.charAt(0),i=this.alphabet.indexOf(n),s=this.alphabet.slice(i)+this.alphabet.slice(0,i);s=s.split("").reverse().join("");let a=e.slice(1);for(;a.length>0;){let e=s.slice(0,1),r=a.split(e);if(r.length>0){if(""===r[0])return t;t.push(this.toNumber(r[0],s.slice(1))),r.length>1&&(s=this.shuffle(s))}a=r.slice(1).join(e)}return t}encodeNumbers(e,t=0){if(t>this.alphabet.length)throw Error("Reached max attempts to re-generate the ID");let r=e.reduce((e,t,r)=>this.alphabet[t%this.alphabet.length].codePointAt(0)+r+e,e.length)%this.alphabet.length;r=(r+t)%this.alphabet.length;let n=this.alphabet.slice(r)+this.alphabet.slice(0,r),i=n.charAt(0);n=n.split("").reverse().join("");let s=[i];for(let t=0;t!==e.length;t++){let r=e[t];s.push(this.toId(r,n.slice(1))),t<e.length-1&&(s.push(n.slice(0,1)),n=this.shuffle(n))}let a=s.join("");if(this.minLength>a.length)for(a+=n.slice(0,1);this.minLength-a.length>0;)n=this.shuffle(n),a+=n.slice(0,Math.min(this.minLength-a.length,n.length));return this.isBlockedId(a)&&(a=this.encodeNumbers(e,t+1)),a}shuffle(e){let t=e.split("");for(let e=0,r=t.length-1;r>0;e++,r--){let n=(e*r+t[e].codePointAt(0)+t[r].codePointAt(0))%t.length;[t[e],t[n]]=[t[n],t[e]]}return t.join("")}toId(e,t){let r=[],n=t.split(""),i=e;do r.unshift(n[i%n.length]),i=Math.floor(i/n.length);while(i>0);return r.join("")}toNumber(e,t){let r=t.split("");return e.split("").reduce((e,t)=>e*r.length+r.indexOf(t),0)}isBlockedId(e){let t=e.toLowerCase();for(let e of this.blocklist)if(e.length<=t.length){if(t.length<=3||e.length<=3){if(t===e)return!0}else if(/\d/.test(e)){if(t.startsWith(e)||t.endsWith(e))return!0}else if(t.includes(e))return!0}return!1}maxValue(){return Number.MAX_SAFE_INTEGER}}var it=class extends nh("InvalidRouteConfig"){constructor(e,t){super({reason:t?`Expected route config to have a ${t} for key ${e} but none was found.`:`Encountered an invalid route config during backfilling. ${e} was not found.`})}},ir=class extends nh("UnknownFileType"){constructor(e){super({reason:`Could not determine type for ${e}`})}},ii=class extends nh("InvalidFileType"){constructor(e,t){super({reason:`File type ${e} not allowed for ${t}`})}},is=class extends nh("InvalidFileSize"){constructor(e){super({reason:`Invalid file size: ${e}`})}};function ia(e){return{maxFileSize:"image"===e?"4MB":"video"===e?"16MB":"audio"===e||"blob"===e?"8MB":"pdf"===e?"4MB":"text"===e?"64KB":"4MB",maxFileCount:1,minFileCount:1,contentDisposition:"inline"}}nh("InvalidURL"),nh("RetryError"),nh("FetchError"),nh("InvalidJson"),nh("BadRequestError"),nh("UploadAborted"),nh("UploadAborted");let io=e=>{if(Array.isArray(e))return rO(e.reduce((e,t)=>(e[t]=ia(t),e),{}));let t={};for(let r of ip(e)){let n=e[r];if(!n)return rF(new it(r));t[r]={...ia(r),...n}}return rO(JSON.parse(JSON.stringify(t,id)))},il=(e,t)=>{let r=e.type||function(e){if(!e||"string"!=typeof e)return!1;let t=(function(e){let t=e.lastIndexOf(".");return t<0?"":e.substring(t)})("x."+e).toLowerCase().substring(1);return!!t&&((function(e,t){if(nm)return;nm=!0;let r=["nginx","apache",void 0,"iana"];Object.keys(np).forEach(n=>{let i=np[n],s=i.extensions;if(s.length)for(let a of(e[n]=s,s)){if(a in t){let e=r.indexOf(np[t[a]].source),n=r.indexOf(i.source);if("application/octet-stream"!==t[a]&&(e>n||e===n&&t[a].startsWith("application/")))continue}t[a]=n}})}(nf,nd),nd)[t]||!1)}(e.name);if(!r)return t.includes("blob")?rO("blob"):rF(new ir(e.name));if(t.some(e=>e.includes("/"))&&t.includes(r))return rO(r);let n="application/pdf"===r.toLowerCase()?"pdf":r.split("/")[0];if(!t.includes(n))if(t.includes("blob"))return rO("blob");else return rF(new ii(n,e.name));return rO(n)},ic=["B","KB","MB","GB","TB"],iu=e=>{let t=RegExp(`^(\\d+)(\\.\\d+)?\\s*(${ic.join("|")})$`,"i"),r=e.match(t);if(!r?.[1]||!r[3])return rF(new is(e));let n=parseFloat(r[1]),i=r[3].toUpperCase();return rO(Math.floor(n*Math.pow(1024,ic.indexOf(i))))},ih=e=>{if(0===e||-1===e)return"0B";let t=Math.floor(Math.log(e)/Math.log(1024));return`${(e/Math.pow(1024,t)).toFixed(2)}${ic[t]}`};function ip(e){return Object.keys(e)}let id=(e,t)=>"number"!=typeof t||Number.isSafeInteger(t)||t<=Number.MAX_SAFE_INTEGER&&t>=Number.MIN_SAFE_INTEGER?t:t===1/0?Number.MAX_SAFE_INTEGER:t===-1/0?Number.MIN_SAFE_INTEGER:Number.isNaN(t)?0:void 0,im={BAD_REQUEST:400,NOT_FOUND:404,FORBIDDEN:403,INTERNAL_SERVER_ERROR:500,INTERNAL_CLIENT_ERROR:500,TOO_LARGE:413,TOO_SMALL:400,TOO_MANY_FILES:400,KEY_TOO_LONG:400,URL_GENERATION_FAILED:500,UPLOAD_FAILED:500,MISSING_ENV:500,INVALID_SERVER_CONFIG:500,FILE_LIMIT_EXCEEDED:500};var ig=class e extends nu{_tag="UploadThingError";name="UploadThingError";cause;code;data;constructor(e){let t="string"==typeof e?{code:"INTERNAL_SERVER_ERROR",message:e}:e;super({message:t.message??function(e,t){return"string"==typeof e?e:e instanceof Error||e&&"object"==typeof e&&"message"in e&&"string"==typeof e.message?e.message:t??"An unknown error occurred"}(t.cause,t.code)}),this.code=t.code,this.data=t.data,t.cause instanceof Error?this.cause=t.cause:eg(t.cause)&&Q(t.cause.status)&&Z(t.cause.statusText)?this.cause=Error(`Response ${t.cause.status} ${t.cause.statusText}`):Z(t.cause)?this.cause=Error(t.cause):this.cause=t.cause}static toObject(e){return{code:e.code,message:e.message,data:e.data}}static serialize(t){return JSON.stringify(e.toObject(t))}};t1("uploadthing/Fetch")();let ib="hmac-sha256=",ix={name:"HMAC",hash:"SHA-256"},iy=new TextEncoder,iv=(e,t)=>rU(function*(){let r=yield*rD({try:()=>crypto.subtle.importKey("raw",iy.encode(n8(t)),ix,!1,["sign"]),catch:e=>new ig({code:"BAD_REQUEST",message:"Invalid signing secret",cause:e})}),n=yield*rH(rD({try:()=>crypto.subtle.sign(ix,r,iy.encode(e)),catch:e=>new ig({code:"BAD_REQUEST",cause:e})}),e=>nV(new Uint8Array(e)));return`${ib}${n}`}).pipe(r9("signPayload")),iS=(e,t,r)=>rU(function*(){let n=t?.slice(ib.length);if(!n)return!1;let i=iy.encode(n8(r)),s=yield*rz(()=>crypto.subtle.importKey("raw",i,ix,!1,["verify"])),a=yield*rj(nG(n)),o=iy.encode(e);return yield*rz(()=>crypto.subtle.verify(ix,s,a,o))}).pipe(r9("verifySignature"),r7(()=>!1)),i_=(e,t,r)=>rT(()=>{let n=JSON.stringify(r?.(e)??[e.name,e.size,e.type,e.lastModified,Date.now()]),i=function(e,t){let r,n,i=e.split(""),s=e$(t);for(let e=0;e<i.length;e++)n=(s%(e+1)+e)%i.length,r=i[e],i[e]=i[n],i[n]=r;return i.join("")}(n9.alphabet,t),s=new ie({alphabet:i,minLength:36}).encode([Math.abs(e$(n))]);return new ie({alphabet:i,minLength:12}).encode([Math.abs(e$(t))])+s}).pipe(r9("generateKey")),iw=(e,t,r)=>rU(function*(){let n=new URL(e),i=r.ttlInSeconds?function(e){if("number"==typeof e)return e;let t=e.split(/(\d+)/).filter(Boolean),r=Number(t[0]);return r*({s:1,m:60,h:3600,d:86400})[(t[1]??"s").trim().slice(0,1)]}(r.ttlInSeconds):3600,s=Date.now()+1e3*i;n.searchParams.append("expires",s.toString()),r.data&&Object.entries(r.data).forEach(([e,t])=>{if(null==t)return;let r=encodeURIComponent(t);n.searchParams.append(e,r)});let a=yield*iv(n.toString(),t);return n.searchParams.append("signature",a),n.href}).pipe(r9("generateSignedURL")),ik=e=>e.length>0,iC=()=>tT,iE=U(2,(e,{onNone:t,onSome:r})=>tI(e)?t():r(e.value)),iO=U(2,(e,t)=>tI(e)?t():e.value),iI=U(2,(e,t)=>tI(e)?t():e),iF=U(2,(e,t)=>tI(e)?tR(t()):e),iT=e=>null==e?iC():tR(e),iR=iO(K),iN=e=>(...t)=>{try{return tR(e(...t))}catch{return iC()}},iA=U(2,(e,t)=>{if(tF(e))return e.value;throw t()}),ij=iA(()=>Error("getOrThrow called on a None")),iM=U(2,(e,t)=>tI(e)?iC():tR(t(e.value))),iz=U(2,(e,t)=>tI(e)?iC():t(e.value)),iD=U(2,(e,t)=>tI(e)?iC():iT(t(e.value))),i$=e=>{if(Symbol.iterator in e){let t=[];for(let r of e){if(tI(r))return iC();t.push(r.value)}return tR(t)}let t={};for(let r of Object.keys(e)){let n=e[r];if(tI(n))return iC();t[r]=n.value}return tR(t)},iP=U(2,(e,t)=>iz(e,e=>t(e)?tR(e):tT)),iL=e=>nY((t,r)=>tI(t)?tI(r):!tI(r)&&e(t.value,r.value)),iU=(x=eW(),U(2,(e,t)=>!tI(e)&&x(e.value,t))),iq=U(2,(e,t)=>!tI(e)&&t(e.value)),iB=e=>(t,r)=>tI(t)?r:tI(r)?t:tR(e(t.value,r.value)),iJ=e=>{let t=e[Symbol.iterator]().next();if(t.done)throw Error("unsafeHead: empty iterable");return t.value};Symbol.iterator,()=>iH;let iH={next:()=>({done:!0,value:void 0})},iK=e=>(t,r)=>t===r?0:e(t,r),iW=iK((e,t)=>e<t?-1:1),iV=U(2,(e,t)=>iK((r,n)=>e(t(r),t(n)))),iG=e=>0===iX(e).length;Object.fromEntries,(e,t)=>{let r=[];for(let n of iX(e))r.push(t(n,e[n]));return r};let iY=U(2,(e,t)=>Object.prototype.hasOwnProperty.call(e,t)),iZ=U(2,(e,t)=>{if(!iY(e,t))return{...e};let r={...e};return delete r[t],r}),iQ=U(2,(e,t)=>{let r={...e};for(let n of iX(e))r[n]=t(e[n],n);return r}),iX=e=>Object.keys(e),i0=U(3,(e,t,r)=>({...e,[t]:r})),i1=e=>U(2,(t,r)=>{for(let n of iX(t))if(!iY(r,n)||!e(t[n],r[n]))return!1;return!0}),i2=(...e)=>e,i3=(...e)=>e,i5=e=>Array(e),i4=U(2,(e,t)=>{let r=Math.max(1,Math.floor(e)),n=Array(r);for(let e=0;e<r;e++)n[e]=t(e);return n}),i6=e=>Array.isArray(e)?e:Array.from(e),i8=e=>Array.isArray(e)?e:[e],i7=U(2,(e,{onEmpty:t,onNonEmpty:r})=>ss(e)?r(e):t()),i9=U(2,(e,{onEmpty:t,onNonEmpty:r})=>ss(e)?r(sh(e),sd(e)):t()),se=U(2,(e,t)=>[t,...e]),st=U(2,(e,t)=>[...e,t]),sr=U(2,(e,t)=>i6(e).concat(i6(t))),sn=Array.isArray,si=e=>0===e.length,ss=ik,sa=(e,t)=>e<0||e>=t.length,so=(e,t)=>Math.floor(Math.min(Math.max(0,e),t.length)),sl=U(2,(e,t)=>{let r=Math.floor(t);return sa(r,e)?iC():tR(e[r])}),sc=U(2,(e,t)=>{let r=Math.floor(t);if(sa(r,e))throw Error(`Index ${r} out of bounds`);return e[r]}),su=sl(0),sh=sc(0),sp=e=>ss(e)?tR(sf(e)):iC(),sf=e=>e[e.length-1],sd=e=>e.slice(1),sm=(e,t)=>{let r=0;for(let n of e){if(!t(n,r))break;r++}return r},sg=U(2,(e,t)=>sw(e,sm(e,t))),sb=U(2,(e,t)=>{let r=i6(e);return r.slice(so(t,r),r.length)}),sx=((e,t)=>{let r=i6(e);for(let e=r.length-1;e>=0;e--){let n=r[e],i=t(n,e);if(X(i)){if(i)return tR(n)}else if(tF(i))return i}return iC()},e=>Array.from(e).reverse()),sy=U(2,(e,t)=>{let r=Array.from(e);return r.sort(t),r}),sv=U(2,(e,t)=>sS(e,t,i2)),sS=U(3,(e,t,r)=>{let n=i6(e),i=i6(t);if(ss(n)&&ss(i)){let e=[r(sh(n),sh(i))],t=Math.min(n.length,i.length);for(let s=1;s<t;s++)e[s]=r(n[s],i[s]);return e}return[]}),s_=eW(),sw=U(2,(e,t)=>{let r=Array.from(e),n=Math.floor(t);return ss(r)?n>=1?sk(r,n):[[],r]:[r,[]]}),sk=U(2,(e,t)=>{let r=Math.max(1,Math.floor(t));return r>=e.length?[sC(e),[]]:[se(e.slice(1,r),sh(e)),e.slice(r)]}),sC=e=>e.slice(),sE=U(3,(e,t,r)=>{let n=i6(e),i=i6(t);return ss(n)?ss(i)?sB(r)(sr(n,i)):n:i}),sO=U(2,(e,t)=>sE(e,t,s_)),sI=()=>[],sF=e=>[e],sT=U(2,(e,t)=>e.map(t)),sR=U(2,(e,t)=>{if(si(e))return[];let r=[];for(let n=0;n<e.length;n++){let i=t(e[n],n);for(let e=0;e<i.length;e++)r.push(i[e])}return r}),sN=sR(q),sA=U(2,(e,t)=>{let r=i6(e),n=[];for(let e=0;e<r.length;e++){let i=t(r[e],e);tF(i)&&n.push(i.value)}return n}),sj=U(2,(e,t)=>{let r=0,n=[];for(let i of e){let e=t(i,r);if(tF(e))n.push(e.value);else break;r++}return n}),sM=U(2,(e,t)=>{let r=[],n=[],i=i6(e);for(let e=0;e<i.length;e++){let s=t(i[e],e);nS(s)?r.push(s.left):n.push(s.right)}return[r,n]}),sz=sA(q),sD=U(2,(e,t)=>{let r=i6(e),n=[];for(let e=0;e<r.length;e++)t(r[e],e)&&n.push(r[e]);return n}),s$=U(3,(e,t,r)=>i6(e).reduce((e,t,n)=>r(e,t,n),t)),sP=U(3,(e,t,r)=>i6(e).reduceRight((e,t,n)=>r(e,t,n),t)),sL=U(2,(e,t)=>e.every(t)),sU=(e,t)=>{let r,n=[],i=e;for(;tF(r=t(i));){let[e,t]=r.value;n.push(e),i=t}return n},sq=n0,sB=U(2,(e,t)=>{let r=i6(e);if(ss(r)){let e=[sh(r)];for(let n of sd(r))e.every(e=>!t(n,e))&&e.push(n);return e}return[]}),sJ=e=>sB(e,eW()),sH=U(2,(e,t)=>i6(e).join(t)),sK=U(3,(e,t,r)=>{let n=0,i=t,s=[];for(let t of e){let e=r(i,t,n);i=e[0],s.push(e[1]),n++}return[i,s]}),sW=/^[+-]?\d+$/,sV=Symbol.for("effect/BigDecimal"),sG={[sV]:sV,[eR](){let e=s2(this);return W(eN(e.value),ej(eD(e.scale)),eq(this))},[eB](e){return sY(e)&&s6(this,e)},toString(){return`BigDecimal(${s9(this)})`},toJSON(){return{_id:"BigDecimal",value:String(this.value),scale:this.scale}},[eV](){return this.toJSON()},pipe(){return e6(this,arguments)}},sY=e=>ec(e,sV),sZ=(e,t)=>{let r=Object.create(sG);return r.value=e,r.scale=t,r},sQ=(e,t)=>{if(e!==sX&&e%s0===sX)throw RangeError("Value must be normalized");let r=sZ(e,t);return r.normalized=r,r},sX=BigInt(0),s0=BigInt(10),s1=sQ(sX,0),s2=e=>{if(void 0===e.normalized)if(e.value===sX)e.normalized=s1;else{let t=`${e.value}`,r=0;for(let e=t.length-1;e>=0;e--)if("0"===t[e])r++;else break;0===r&&(e.normalized=e);let n=BigInt(t.substring(0,t.length-r)),i=e.scale-r;e.normalized=sQ(n,i)}return e.normalized},s3=U(2,(e,t)=>t>e.scale?sZ(e.value*s0**BigInt(t-e.scale),t):t<e.scale?sZ(e.value/s0**BigInt(e.scale-t),t):e),s5=e=>e.value<sX?sZ(-e.value,e.scale):e,s4=nY((e,t)=>e.scale>t.scale?s3(t,e.scale).value===e.value:e.scale<t.scale?s3(e,t.scale).value===t.value:e.value===t.value),s6=U(2,(e,t)=>s4(e,t)),s8=e=>{if(!Number.isFinite(e))return iC();let t=`${e}`;if(t.includes("e"))return s7(t);let[r,n=""]=t.split(".");return tR(sZ(BigInt(`${r}${n}`),n.length))},s7=e=>{let t,r,n,i;if(""===e)return tR(s1);let s=e.search(/[eE]/);if(-1!==s){let n=e.slice(s+1);if(t=e.slice(0,s),r=Number(n),""===t||!Number.isSafeInteger(r)||!sW.test(n))return iC()}else t=e,r=0;let a=t.search(/\./);if(-1!==a){let e=t.slice(0,a),r=t.slice(a+1);n=`${e}${r}`,i=r.length}else n=t,i=0;if(!sW.test(n))return iC();let o=i-r;return Number.isSafeInteger(o)?tR(sZ(BigInt(n),o)):iC()},s9=e=>{let t,r,n=s2(e);if(Math.abs(n.scale)>=16)return ae(n);let i=n.value<sX,s=i?`${n.value}`.substring(1):`${n.value}`;if(n.scale>=s.length)t="0",r="0".repeat(n.scale-s.length)+s;else{let e=s.length-n.scale;if(e>s.length){let n=e-s.length;t=`${s}${"0".repeat(n)}`,r=""}else r=s.slice(e),t=s.slice(0,e)}let a=""===r?t:`${t}.${r}`;return i?`-${a}`:a},ae=e=>{if(at(e))return"0e+0";let t=s2(e),r=`${s5(t).value}`,n=r.slice(0,1),i=r.slice(1),s=`${ar(t)?"-":""}${n}`;""!==i&&(s+=`.${i}`);let a=i.length-t.scale;return`${s}e${a>=0?"+":""}${a}`},at=e=>e.value===sX,ar=e=>e.value<sX,an=e=>{try{return""===e.trim()?iC():tR(BigInt(e))}catch{return iC()}},ai=e=>!e,as=Symbol.for("effect/Chunk"),aa=[],ao=nY((e,t)=>e.length===t.length&&ab(e).every((e,r)=>eJ(e,a_(t,r)))),al={[as]:{_A:e=>e},toString(){return eY(this.toJSON())},toJSON(){return{_id:"Chunk",values:ab(this).map(eG)}},[eV](){return this.toJSON()},[eB](e){return au(e)&&ao(this,e)},[eR](){return eq(this,eU(ab(this)))},[Symbol.iterator](){switch(this.backing._tag){case"IArray":return this.backing.array[Symbol.iterator]();case"IEmpty":return aa[Symbol.iterator]();default:return ab(this)[Symbol.iterator]()}},pipe(){return e6(this,arguments)}},ac=e=>{let t=Object.create(al);switch(t.backing=e,e._tag){case"IEmpty":t.length=0,t.depth=0,t.left=t,t.right=t;break;case"IConcat":t.length=e.left.length+e.right.length,t.depth=1+Math.max(e.left.depth,e.right.depth),t.left=e.left,t.right=e.right;break;case"IArray":t.length=e.array.length,t.depth=0,t.left=ah,t.right=ah;break;case"ISingleton":t.length=1,t.depth=0,t.left=ah,t.right=ah;break;case"ISlice":t.length=e.length,t.depth=e.chunk.depth+1,t.left=ah,t.right=ah}return t},au=e=>ec(e,as),ah=ac({_tag:"IEmpty"}),ap=()=>ah,af=(...e)=>aS(e),ad=e=>ac({_tag:"ISingleton",a:e}),am=e=>au(e)?e:av(i6(e)),ag=(e,t,r)=>{switch(e.backing._tag){case"IArray":!function(e,t,r,n,i){for(let s=0;s<Math.min(e.length,0+i);s++)r[n+s-t]=e[s]}(e.backing.array,0,t,r,e.length);break;case"IConcat":ag(e.left,t,r),ag(e.right,t,r+e.left.length);break;case"ISingleton":t[r]=e.backing.a;break;case"ISlice":{let n=0,i=r;for(;n<e.length;)t[i]=a_(e,n),n+=1,i+=1}}},ab=e=>{switch(e.backing._tag){case"IEmpty":return aa;case"IArray":return e.backing.array;default:{let t=Array(e.length);return ag(e,t,0),e.backing={_tag:"IArray",array:t},e.left=ah,e.right=ah,e.depth=0,t}}},ax=e=>{switch(e.backing._tag){case"IEmpty":case"ISingleton":return e;case"IArray":return ac({_tag:"IArray",array:sx(e.backing.array)});case"IConcat":return ac({_tag:"IConcat",left:ax(e.backing.right),right:ax(e.backing.left)});case"ISlice":return av(sx(ab(e)))}},ay=U(2,(e,t)=>t<0||t>=e.length?iC():tR(a_(e,t))),av=e=>0===e.length?ap():1===e.length?ad(e[0]):ac({_tag:"IArray",array:e}),aS=e=>av(e),a_=U(2,(e,t)=>{switch(e.backing._tag){case"IEmpty":throw Error("Index out of bounds");case"ISingleton":if(0!==t)throw Error("Index out of bounds");return e.backing.a;case"IArray":if(t>=e.length||t<0)throw Error("Index out of bounds");return e.backing.array[t];case"IConcat":return t<e.left.length?a_(e.left,t):a_(e.right,t-e.left.length);case"ISlice":return a_(e.backing.chunk,t+e.backing.offset)}}),aw=U(2,(e,t)=>aI(e,ad(t))),ak=U(2,(e,t)=>aI(ad(t),e)),aC=U(2,(e,t)=>{if(t<=0)return ah;if(t>=e.length)return e;switch(e.backing._tag){case"ISlice":return ac({_tag:"ISlice",chunk:e.backing.chunk,length:t,offset:e.backing.offset});case"IConcat":if(t>e.left.length)return ac({_tag:"IConcat",left:e.left,right:aC(e.right,t-e.left.length)});return aC(e.left,t);default:return ac({_tag:"ISlice",chunk:e,offset:0,length:t})}}),aE=U(2,(e,t)=>{if(t<=0)return e;if(t>=e.length)return ah;switch(e.backing._tag){case"ISlice":return ac({_tag:"ISlice",chunk:e.backing.chunk,offset:e.backing.offset+t,length:e.backing.length-t});case"IConcat":if(t>e.left.length)return aE(e.right,t-e.left.length);return ac({_tag:"IConcat",left:aE(e.left,t),right:e.right});default:return ac({_tag:"ISlice",chunk:e,offset:t,length:e.length-t})}}),aO=U(2,(e,t)=>{let r=ab(e),n=r.length,i=0;for(;i<n&&t(r[i]);)i++;return aE(e,i)}),aI=U(2,(e,t)=>{if("IEmpty"===e.backing._tag)return t;if("IEmpty"===t.backing._tag)return e;let r=t.depth-e.depth;if(1>=Math.abs(r))return ac({_tag:"IConcat",left:e,right:t});if(r<-1)if(e.left.depth>=e.right.depth){let r=aI(e.right,t);return ac({_tag:"IConcat",left:e.left,right:r})}else{let r=aI(e.right.right,t);if(r.depth===e.depth-3){let t=ac({_tag:"IConcat",left:e.right.left,right:r});return ac({_tag:"IConcat",left:e.left,right:t})}{let t=ac({_tag:"IConcat",left:e.left,right:e.right.left});return ac({_tag:"IConcat",left:t,right:r})}}if(t.right.depth>=t.left.depth)return ac({_tag:"IConcat",left:aI(e,t.left),right:t.right});{let r=aI(e,t.left.left);if(r.depth===t.depth-3){let e=ac({_tag:"IConcat",left:r,right:t.left.right});return ac({_tag:"IConcat",left:e,right:t.right})}{let e=ac({_tag:"IConcat",left:t.left.right,right:t.right});return ac({_tag:"IConcat",left:r,right:e})}}}),aF=U(2,(e,t)=>av(sA(e,t))),aT=U(2,(e,t)=>av(sD(e,t))),aR=((e,t)=>av(sj(e,t)),U(2,(e,t)=>{if("ISingleton"===e.backing._tag)return t(e.backing.a,0);let r=ah,n=0;for(let i of e)r=aI(r,t(i,n++));return r})),aN=U(2,(e,t)=>ab(e).forEach(t)),aA=aR(q),aj=e=>0===e.length,aM=e=>e.length>0,az=ay(0),aD=e=>a_(e,0),a$=e=>a_(e,e.length-1),aP=U(2,(e,t)=>"ISingleton"===e.backing._tag?ad(t(e.backing.a,0)):av(W(ab(e),sT((e,r)=>t(e,r))))),aL=((e,t,r)=>{let[n,i]=sK(e,t,r);return[n,av(i)]},U(2,(e,t)=>[aC(e,t),aE(e,t)])),aU=U(2,(e,t)=>{let r=0;for(let n of ab(e))if(t(n))break;else r++;return aL(e,r)}),aq=e=>aE(e,1),aB=U(2,(e,t)=>aE(e,e.length-t)),aJ=U(2,(e,t)=>{let r=[];for(let n of ab(e))if(t(n))r.push(n);else break;return av(r)}),aH=U(3,(e,t,r)=>av(sS(e,t,r))),aK=U(2,(e,t)=>am(i4(e,t))),aW=(e,t)=>e<=t?aK(t-e+1,t=>e+t):ad(e),aV=U(2,(e,t)=>i6(e).some(t)),aG="InvalidData",aY="MissingData",aZ="SourceUnavailable",aQ="Unsupported",aX=Symbol.for("effect/ConfigError"),a0={_tag:"ConfigError",[aX]:aX},a1=(e,t)=>{let r=Object.create(a0);return r._op="And",r.left=e,r.right=t,Object.defineProperty(r,"toString",{enumerable:!1,value(){return`${this.left} and ${this.right}`}}),Object.defineProperty(r,"message",{enumerable:!1,get(){return this.toString()}}),r},a2=(e,t)=>{let r=Object.create(a0);return r._op="Or",r.left=e,r.right=t,Object.defineProperty(r,"toString",{enumerable:!1,value(){return`${this.left} or ${this.right}`}}),Object.defineProperty(r,"message",{enumerable:!1,get(){return this.toString()}}),r},a3=(e,t,r={pathDelim:"."})=>{let n=Object.create(a0);return n._op=aG,n.path=e,n.message=t,Object.defineProperty(n,"toString",{enumerable:!1,value(){let e=W(this.path,sH(r.pathDelim));return`(Invalid data at ${e}: "${this.message}")`}}),n},a5=(e,t,r={pathDelim:"."})=>{let n=Object.create(a0);return n._op=aY,n.path=e,n.message=t,Object.defineProperty(n,"toString",{enumerable:!1,value(){let e=W(this.path,sH(r.pathDelim));return`(Missing data at ${e}: "${this.message}")`}}),n},a4=(e,t,r,n={pathDelim:"."})=>{let i=Object.create(a0);return i._op=aZ,i.path=e,i.message=t,i.cause=r,Object.defineProperty(i,"toString",{enumerable:!1,value(){let e=W(this.path,sH(n.pathDelim));return`(Source unavailable at ${e}: "${this.message}")`}}),i},a6=(e,t,r={pathDelim:"."})=>{let n=Object.create(a0);return n._op=aQ,n.path=e,n.message=t,Object.defineProperty(n,"toString",{enumerable:!1,value(){let e=W(this.path,sH(r.pathDelim));return`(Unsupported operation at ${e}: "${this.message}")`}}),n},a8=U(2,(e,t)=>{switch(e._op){case"And":return a1(a8(e.left,t),a8(e.right,t));case"Or":return a2(a8(e.left,t),a8(e.right,t));case aG:return a3([...t,...e.path],e.message);case aY:return a5([...t,...e.path],e.message);case aZ:return a4([...t,...e.path],e.message,e.cause);case aQ:return a6([...t,...e.path],e.message)}}),a7={andCase:(e,t,r)=>t&&r,orCase:(e,t,r)=>t&&r,invalidDataCase:H,missingDataCase:J,sourceUnavailableCase:H,unsupportedCase:H},a9=U(3,(e,t,r)=>{let n=[e],i=[];for(;n.length>0;){let e=n.pop();switch(e._op){case"And":n.push(e.right),n.push(e.left),i.push(nw({_op:"AndCase"}));break;case"Or":n.push(e.right),n.push(e.left),i.push(nw({_op:"OrCase"}));break;case aG:i.push(nk(r.invalidDataCase(t,e.path,e.message)));break;case aY:i.push(nk(r.missingDataCase(t,e.path,e.message)));break;case aZ:i.push(nk(r.sourceUnavailableCase(t,e.path,e.message,e.cause)));break;case aQ:i.push(nk(r.unsupportedCase(t,e.path,e.message)))}}let s=[];for(;i.length>0;){let e=i.pop();switch(e._op){case"Left":switch(e.left._op){case"AndCase":{let e=s.pop(),n=s.pop(),i=r.andCase(t,e,n);s.push(i);break}case"OrCase":{let e=s.pop(),n=s.pop(),i=r.orCase(t,e,n);s.push(i)}}break;case"Right":s.push(e.right)}}if(0===s.length)throw Error("BUG: ConfigError.reduceWithContext - please report an issue at https://github.com/Effect-TS/effect/issues");return s.pop()}),oe=e=>a9(e,void 0,a7),ot=Symbol.for("effect/Duration"),or=BigInt(0),on=BigInt(24),oi=BigInt(60),os=BigInt(1e3),oa=BigInt(1e6),oo=BigInt(1e9),ol=/^(-?\d+(?:\.\d+)?)\s+(nanos?|micros?|millis?|seconds?|minutes?|hours?|days?|weeks?)$/,oc=e=>{if(od(e))return e;if(Q(e))return oS(e);if(ee(e))return oy(e);if(Array.isArray(e)&&2===e.length&&e.every(Q))return e[0]===-1/0||e[1]===-1/0||Number.isNaN(e[0])||Number.isNaN(e[1])?ob:e[0]===1/0||e[1]===1/0?ox:oy(BigInt(Math.round(1e9*e[0]))+BigInt(Math.round(e[1])));if(Z(e)){let t=ol.exec(e);if(t){let[e,r,n]=t,i=Number(r);switch(n){case"nano":case"nanos":return oy(BigInt(r));case"micro":case"micros":return ov(BigInt(r));case"milli":case"millis":return oS(i);case"second":case"seconds":return o_(i);case"minute":case"minutes":return ow(i);case"hour":case"hours":return ok(i);case"day":case"days":return oC(i);case"week":case"weeks":return oE(i)}}}throw Error("Invalid DurationInput")},ou={_tag:"Millis",millis:0},oh={_tag:"Infinity"},op={[ot]:ot,[eR](){return eq(this,eL(this.value))},[eB](e){return od(e)&&oP(this,e)},toString(){return`Duration(${oU(this)})`},toJSON(){switch(this.value._tag){case"Millis":return{_id:"Duration",_tag:"Millis",millis:this.value.millis};case"Nanos":return{_id:"Duration",_tag:"Nanos",hrtime:oT(this)};case"Infinity":return{_id:"Duration",_tag:"Infinity"}}},[eV](){return this.toJSON()},pipe(){return e6(this,arguments)}},of=e=>{let t=Object.create(op);return Q(e)?isNaN(e)||e<=0?t.value=ou:Number.isFinite(e)?Number.isInteger(e)?t.value={_tag:"Millis",millis:e}:t.value={_tag:"Nanos",nanos:BigInt(Math.round(1e6*e))}:t.value=oh:e<=or?t.value=ou:t.value={_tag:"Nanos",nanos:e},t},od=e=>ec(e,ot),om=e=>"Infinity"!==e.value._tag,og=e=>{switch(e.value._tag){case"Millis":return 0===e.value.millis;case"Nanos":return e.value.nanos===or;case"Infinity":return!1}},ob=of(0),ox=of(1/0),oy=e=>of(e),ov=e=>of(e*os),oS=e=>of(e),o_=e=>of(1e3*e),ow=e=>of(6e4*e),ok=e=>of(36e5*e),oC=e=>of(864e5*e),oE=e=>of(6048e5*e),oO=e=>oR(e,{onMillis:e=>e,onNanos:e=>Number(e)/1e6}),oI=e=>oR(e,{onMillis:e=>e/1e3,onNanos:e=>Number(e)/1e9}),oF=e=>{let t=oc(e);switch(t.value._tag){case"Infinity":throw Error("Cannot convert infinite duration to nanos");case"Nanos":return t.value.nanos;case"Millis":return BigInt(Math.round(1e6*t.value.millis))}},oT=e=>{let t=oc(e);switch(t.value._tag){case"Infinity":return[1/0,0];case"Nanos":return[Number(t.value.nanos/oo),Number(t.value.nanos%oo)];case"Millis":return[Math.floor(t.value.millis/1e3),Math.round(t.value.millis%1e3*1e6)]}},oR=U(2,(e,t)=>{let r=oc(e);switch(r.value._tag){case"Nanos":return t.onNanos(r.value.nanos);case"Infinity":return t.onMillis(1/0);case"Millis":return t.onMillis(r.value.millis)}}),oN=U(3,(e,t,r)=>{let n=oc(e),i=oc(t);if("Infinity"===n.value._tag||"Infinity"===i.value._tag)return r.onMillis(oO(n),oO(i));if("Nanos"===n.value._tag||"Nanos"===i.value._tag){let e="Nanos"===n.value._tag?n.value.nanos:BigInt(Math.round(1e6*n.value.millis)),t="Nanos"===i.value._tag?i.value.nanos:BigInt(Math.round(1e6*i.value.millis));return r.onNanos(e,t)}return r.onMillis(n.value.millis,i.value.millis)}),oA=(e,t)=>oN(e,t,{onMillis:(e,t)=>e===t,onNanos:(e,t)=>e===t}),oj=U(2,(e,t)=>oN(e,t,{onMillis:(e,t)=>of(e+t),onNanos:(e,t)=>of(e+t)})),oM=U(2,(e,t)=>oN(e,t,{onMillis:(e,t)=>e<t,onNanos:(e,t)=>e<t})),oz=U(2,(e,t)=>oN(e,t,{onMillis:(e,t)=>e<=t,onNanos:(e,t)=>e<=t})),oD=U(2,(e,t)=>oN(e,t,{onMillis:(e,t)=>e>t,onNanos:(e,t)=>e>t})),o$=U(2,(e,t)=>oN(e,t,{onMillis:(e,t)=>e>=t,onNanos:(e,t)=>e>=t})),oP=U(2,(e,t)=>oA(oc(e),oc(t))),oL=e=>{let t=oc(e);if("Infinity"===t.value._tag)return{days:1/0,hours:1/0,minutes:1/0,seconds:1/0,millis:1/0,nanos:1/0};let r=oF(t),n=r/oa,i=n/os,s=i/oi,a=s/oi;return{days:Number(a/on),hours:Number(a%on),minutes:Number(s%oi),seconds:Number(i%oi),millis:Number(n%os),nanos:Number(r%oa)}},oU=e=>{let t=oc(e);if("Infinity"===t.value._tag)return"Infinity";if(og(t))return"0";let r=oL(t),n=[];return 0!==r.days&&n.push(`${r.days}d`),0!==r.hours&&n.push(`${r.hours}h`),0!==r.minutes&&n.push(`${r.minutes}m`),0!==r.seconds&&n.push(`${r.seconds}s`),0!==r.millis&&n.push(`${r.millis}ms`),0!==r.nanos&&n.push(`${r.nanos}ns`),n.join(" ")};function oq(e,t){var r;return r=e&t-1,r-=r>>1&0x55555555,r=(r=(0x33333333&r)+(r>>2&0x33333333))+(r>>4)&0xf0f0f0f,r+=r>>8,127&(r+=r>>16)}let oB=(e,t)=>({value:e,previous:t});function oJ(e,t,r,n){let i=n;if(!e){let e=n.length;i=Array(e);for(let t=0;t<e;++t)i[t]=n[t]}return i[t]=r,i}function oH(e,t,r){let n=r.length-1,i=0,s=0,a=r;if(e)i=s=t;else for(a=Array(n);i<t;)a[s++]=r[i++];for(++i;i<=n;)a[s++]=r[i++];return e&&(a.length=n),a}class oK{modify(e,t,r,n,i,s){let a=r(iC());return tI(a)?new oK:(++s.value,new oG(e,n,i,a))}constructor(){this._tag="EmptyNode"}}function oW(e){return eu(e,"EmptyNode")}function oV(e,t){return!oW(e)&&t===e.edit}class oG{constructor(e,t,r,n){this._tag="LeafNode",this.edit=e,this.hash=t,this.key=r,this.value=n}modify(e,t,r,n,i,s){if(eJ(i,this.key)){let t=r(this.value);return t===this.value?this:tI(t)?(--s.value,new oK):oV(this,e)?(this.value=t,this):new oG(e,n,i,t)}let a=r(iC());return tI(a)?this:(++s.value,oX(e,t,this.hash,this,n,new oG(e,n,i,a)))}}class oY{constructor(e,t,r){this._tag="CollisionNode",this.edit=e,this.hash=t,this.children=r}modify(e,t,r,n,i,s){if(n===this.hash){let t=oV(this,e),n=this.updateCollisionList(t,e,this.hash,this.children,r,i,s);return n===this.children?this:n.length>1?new oY(e,this.hash,n):n[0]}let a=r(iC());return tI(a)?this:(++s.value,oX(e,t,this.hash,this,n,new oG(e,n,i,a)))}updateCollisionList(e,t,r,n,i,s,a){let o=n.length;for(let l=0;l<o;++l){let o=n[l];if("key"in o&&eJ(s,o.key)){let c=o.value,u=i(c);if(u===c)return n;if(tI(u))return--a.value,oH(e,l,n);return oJ(e,l,new oG(t,r,s,u),n)}}let l=i(iC());return tI(l)?n:(++a.value,oJ(e,o,new oG(t,r,s,l),n))}}class oZ{constructor(e,t,r){this._tag="IndexedNode",this.edit=e,this.mask=t,this.children=r}modify(e,t,r,n,i,s){let a,o=this.mask,l=this.children,c=n>>>t&31,u=1<<c,h=oq(o,u),p=o&u,f=oV(this,e);if(!p){let a=new oK().modify(e,t+5,r,n,i,s);return a?l.length>=16?function(e,t,r,n,i){let s=[],a=n,o=0;for(let e=0;a;++e)1&a&&(s[e]=i[o++]),a>>>=1;return s[t]=r,new oQ(e,o+1,s)}(e,c,a,o,l):new oZ(e,o|u,function(e,t,r,n){let i=n.length;if(e){let e=i;for(;e>=t;)n[e--]=n[e];return n[t]=r,n}let s=0,a=0,o=Array(i+1);for(;s<t;)o[a++]=n[s++];for(o[t]=r;s<i;)o[++a]=n[s++];return o}(f,h,a,l)):this}let d=l[h],m=d.modify(e,t+5,r,n,i,s);if(d===m)return this;let g=o;if(oW(m)){var b;if(!(g&=~u))return new oK;if(l.length<=2&&(oW(b=l[1^h])||"LeafNode"===b._tag||"CollisionNode"===b._tag))return l[1^h];a=oH(f,h,l)}else a=oJ(f,h,m,l);return f?(this.mask=g,this.children=a,this):new oZ(e,g,a)}}class oQ{constructor(e,t,r){this._tag="ArrayNode",this.edit=e,this.size=t,this.children=r}modify(e,t,r,n,i,s){let a,o=this.size,l=this.children,c=n>>>t&31,u=l[c],h=(u||new oK).modify(e,t+5,r,n,i,s);if(u===h)return this;let p=oV(this,e);if(oW(u)&&!oW(h))++o,a=oJ(p,c,h,l);else if(!oW(u)&&oW(h)){if(--o<=8)return function(e,t,r,n){let i=Array(t-1),s=0,a=0;for(let e=0,t=n.length;e<t;++e)if(e!==r){let t=n[e];t&&!oW(t)&&(i[s++]=t,a|=1<<e)}return new oZ(e,a,i)}(e,o,c,l);a=oJ(p,c,new oK,l)}else a=oJ(p,c,h,l);return p?(this.size=o,this.children=a,this):new oQ(e,o,a)}}function oX(e,t,r,n,i,s){let a,o=t;for(;;){let t=function(e,t,r,n,i,s){if(r===i)return new oY(e,r,[s,n]);let a=r>>>t&31,o=i>>>t&31;return a===o?t=>new oZ(e,1<<a|1<<o,[t]):new oZ(e,1<<a|1<<o,a<o?[n,s]:[s,n])}(e,o,r,n,i,s);if("function"==typeof t)a=oB(t,a),o+=5;else{let e=t;for(;null!=a;)e=a.value(e),a=a.previous;return e}}}let o0="effect/HashMap",o1=Symbol.for(o0),o2={[o1]:o1,[Symbol.iterator](){return new o5(this,(e,t)=>[e,t])},[eR](){let e=eN(o0);for(let t of this)e^=W(eN(t[0]),ej(eN(t[1])));return eq(this,e)},[eB](e){if(le(e)){if(e._size!==this._size)return!1;for(let t of this){let r=W(e,lr(t[0],eN(t[0])));if(tI(r)||!eJ(t[1],r.value))return!1}return!0}return!1},toString(){return eY(this.toJSON())},toJSON(){return{_id:"HashMap",values:Array.from(this).map(eG)}},[eV](){return this.toJSON()},pipe(){return e6(this,arguments)}},o3=(e,t,r,n)=>{let i=Object.create(o2);return i._editable=e,i._edit=t,i._root=r,i._size=n,i};class o5{constructor(e,t){this.map=e,this.f=t,this.v=o6(this.map._root,this.f,void 0)}next(){if(tI(this.v))return{done:!0,value:void 0};let e=this.v.value;return this.v=o4(e.cont),{done:!1,value:e.value}}[Symbol.iterator](){return new o5(this.map,this.f)}}let o4=e=>e?o8(e[0],e[1],e[2],e[3],e[4]):iC(),o6=(e,t,r)=>{switch(e._tag){case"LeafNode":if(tF(e.value))return tR({value:t(e.key,e.value.value),cont:r});return o4(r);case"CollisionNode":case"ArrayNode":case"IndexedNode":{let n=e.children;return o8(n.length,n,0,t,r)}default:return o4(r)}},o8=(e,t,r,n,i)=>{for(;r<e;){let s=t[r++];if(s&&!oW(s))return o6(s,n,[e,t,r,n,i])}return o4(i)},o7=o3(!1,0,new oK,0),o9=()=>o7,le=e=>ec(e,o1),lt=U(2,(e,t)=>lr(e,t,eN(t))),lr=U(3,(e,t,r)=>{let n=e._root,i=0;for(;;)switch(n._tag){case"LeafNode":return eJ(t,n.key)?n.value:iC();case"CollisionNode":if(r===n.hash){let e=n.children;for(let r=0,n=e.length;r<n;++r){let n=e[r];if("key"in n&&eJ(t,n.key))return n.value}}return iC();case"IndexedNode":{let e=1<<(r>>>i&31);if(n.mask&e){n=n.children[oq(n.mask,e)],i+=5;break}return iC()}case"ArrayNode":if(n=n.children[r>>>i&31]){i+=5;break}return iC();default:return iC()}}),ln=U(2,(e,t)=>tF(lr(e,t,eN(t)))),li=U(3,(e,t,r)=>lu(e,t,()=>tR(r))),ls=U(3,(e,t,r)=>e._editable?(e._root=t,e._size=r,e):t===e._root?e:o3(e._editable,e._edit,t,r)),la=e=>new o5(e,e=>e),lo=e=>e._size,ll=e=>o3(!0,e._edit+1,e._root,e._size),lc=e=>(e._editable=!1,e),lu=((e,t)=>{let r=ll(e);return t(r),lc(r)},U(3,(e,t,r)=>lh(e,t,eN(t),r))),lh=U(4,(e,t,r,n)=>{let i={value:e._size},s=e._root.modify(e._editable?e._edit:NaN,0,n,r,t,i);return W(e,ls(s,i.value))}),lp=U(2,(e,t)=>lu(e,t,iC)),lf=U(2,(e,t)=>lm(e,o9(),(e,r,n)=>li(e,n,t(r,n)))),ld=U(2,(e,t)=>lm(e,void 0,(e,r,n)=>t(r,n))),lm=U(3,(e,t,r)=>{let n,i=e._root;if("LeafNode"===i._tag)return tF(i.value)?r(t,i.value.value,i.key):t;if("EmptyNode"===i._tag)return t;let s=[i.children];for(;n=s.pop();)for(let e=0,i=n.length;e<i;){let i=n[e++];i&&!oW(i)&&("LeafNode"===i._tag?tF(i.value)&&(t=r(t,i.value.value,i.key)):s.push(i.children))}return t}),lg=((e,t)=>{for(let r of e)if(t(r[1],r[0]))return tR(r);return iC()},"effect/HashSet"),lb=Symbol.for(lg),lx={[lb]:lb,[Symbol.iterator](){return la(this._keyMap)},[eR](){return eq(this,ej(eN(this._keyMap))(eN(lg)))},[eB](e){return!!lv(e)&&lo(this._keyMap)===lo(e._keyMap)&&eJ(this._keyMap,e._keyMap)},toString(){return eY(this.toJSON())},toJSON(){return{_id:"HashSet",values:Array.from(this).map(eG)}},[eV](){return this.toJSON()},pipe(){return e6(this,arguments)}},ly=e=>{let t=Object.create(lx);return t._keyMap=e,t},lv=e=>ec(e,lb),lS=ly(o9()),l_=()=>lS,lw=U(2,(e,t)=>ln(e._keyMap,t)),lk=U(2,(e,t)=>{let r=!1;for(let n of e)if(r=t(n))break;return r}),lC=U(2,(e,t)=>!lk(e,e=>!t(e))),lE=e=>ly(ll(e._keyMap)),lO=e=>(e._keyMap._editable=!1,e),lI=U(2,(e,t)=>{let r=lE(e);return t(r),lO(r)}),lF=U(2,(e,t)=>e._keyMap._editable?(li(t,!0)(e._keyMap),e):ly(li(t,!0)(e._keyMap))),lT=U(2,(e,t)=>e._keyMap._editable?(lp(t)(e._keyMap),e):ly(lp(t)(e._keyMap))),lR=U(2,(e,t)=>lI(e,e=>{for(let r of t)lT(e,r)})),lN=U(2,(e,t)=>lI(l_(),r=>{for(let n of(lA(e,e=>lF(r,e)),t))lF(r,n)})),lA=U(2,(e,t)=>ld(e._keyMap,(e,r)=>t(r))),lj=U(3,(e,t,r)=>lm(e._keyMap,t,(e,t,n)=>r(e,n))),lM=e=>{let t=lE(l_());for(let r of e)lF(t,r);return lO(t)},lz=(...e)=>{let t=lE(l_());for(let r of e)lF(t,r);return lO(t)},lD=e=>lo(e._keyMap),l$=Symbol.for("effect/MutableRef"),lP={[l$]:l$,toString(){return eY(this.toJSON())},toJSON(){return{_id:"MutableRef",current:eG(this.current)}},[eV](){return this.toJSON()},pipe(){return e6(this,arguments)}},lL=e=>{let t=Object.create(lP);return t.current=e,t},lU=U(3,(e,t,r)=>!!eJ(t,e.current)&&(e.current=r,!0)),lq=e=>e.current,lB=U(2,(e,t)=>(e.current=t,e)),lJ="effect/FiberId",lH=Symbol.for(lJ),lK="None",lW="Runtime",lV="Composite",lG=e$(`${lJ}-${lK}`);class lY{[eR](){return lG}[eB](e){return l0(e)&&e._tag===lK}toString(){return eY(this.toJSON())}toJSON(){return{_id:"FiberId",_tag:this._tag}}[eV](){return this.toJSON()}constructor(){this[lH]=lH,this._tag=lK,this.id=-1,this.startTimeMillis=-1}}class lZ{constructor(e,t){this[lH]=lH,this._tag=lW,this.id=e,this.startTimeMillis=t}[eR](){return eq(this,e$(`${lJ}-${this._tag}-${this.id}-${this.startTimeMillis}`))}[eB](e){return l0(e)&&e._tag===lW&&this.id===e.id&&this.startTimeMillis===e.startTimeMillis}toString(){return eY(this.toJSON())}toJSON(){return{_id:"FiberId",_tag:this._tag,id:this.id,startTimeMillis:this.startTimeMillis}}[eV](){return this.toJSON()}}class lQ{constructor(e,t){this[lH]=lH,this._tag=lV,this.left=e,this.right=t}[eR](){return W(e$(`${lJ}-${this._tag}`),ej(eN(this.left)),ej(eN(this.right)),eq(this))}[eB](e){return l0(e)&&e._tag===lV&&eJ(this.left,e.left)&&eJ(this.right,e.right)}toString(){return eY(this.toJSON())}toJSON(){return{_id:"FiberId",_tag:this._tag,left:eG(this.left),right:eG(this.right)}}[eV](){return this.toJSON()}}let lX=new lY,l0=e=>ec(e,lH),l1=e=>e._tag===lK||W(l8(e),lC(e=>l1(e))),l2=U(2,(e,t)=>e._tag===lK?t:t._tag===lK?e:new lQ(e,t)),l3=U(2,(e,t)=>l1(e)?t:e),l5=e=>{switch(e._tag){case lK:return l_();case lW:return lz(e.id);case lV:return W(l5(e.left),lN(l5(e.right)))}},l4=Y(Symbol.for("effect/Fiber/Id/_fiberCounter"),()=>lL(0)),l6=e=>Array.from(l5(e)).map(e=>`#${e}`).join(","),l8=e=>{switch(e._tag){case lK:return l_();case lW:return lz(e);case lV:return W(l8(e.left),lN(l8(e.right)))}},l7=lX,l9=(e,t)=>new lZ(e,t),ce=(e,t)=>new lQ(e,t),ct=l0,cr=e=>W(e,lj(lX,(e,t)=>l2(t)(e))),cn=()=>{let e=lq(l4);return W(l4,lB(e+1)),new lZ(e,Date.now())},ci=e=>{let t=ll(o9());for(let r of e)li(t,r[0],r[1]);return lc(t)},cs=e=>e&&oW(e._root),ca=Symbol.for("effect/List"),co=e=>i6(e),cl=nQ(sq(eJ),co),cc={[ca]:ca,_tag:"Cons",toString(){return eY(this.toJSON())},toJSON(){return{_id:"List",_tag:"Cons",values:co(this).map(eG)}},[eV](){return this.toJSON()},[eB](e){return cf(e)&&this._tag===e._tag&&cl(this,e)},[eR](){return eq(this,eU(co(this)))},[Symbol.iterator](){let e=!1,t=this;return{next(){if(e)return this.return();if("Nil"===t._tag)return e=!0,this.return();let r=t.head;return t=t.tail,{done:e,value:r}},return:t=>(e||(e=!0),{done:!0,value:t})}},pipe(){return e6(this,arguments)}},cu=(e,t)=>{let r=Object.create(cc);return r.head=e,r.tail=t,r},ch=e$("Nil"),cp=Object.create({[ca]:ca,_tag:"Nil",toString(){return eY(this.toJSON())},toJSON:()=>({_id:"List",_tag:"Nil"}),[eV](){return this.toJSON()},[eR]:()=>ch,[eB](e){return cf(e)&&this._tag===e._tag},[Symbol.iterator]:()=>({next:()=>({done:!0,value:void 0})}),pipe(){return e6(this,arguments)}}),cf=e=>ec(e,ca),cd=e=>"Nil"===e._tag,cm=e=>"Cons"===e._tag,cg=(e,t)=>cu(e,t),cb=()=>cp,cx=e=>cu(e,cp),cy=U(2,(e,t)=>cS(t,e)),cv=U(2,(e,t)=>cg(t,e)),cS=U(2,(e,t)=>{if(cd(e))return t;{if(cd(t))return e;let r=cu(t.head,e),n=r,i=t.tail;for(;!cd(i);){let t=cu(i.head,e);n.tail=t,n=t,i=i.tail}return r}}),c_=U(3,(e,t,r)=>{let n=t,i=e;for(;!cd(i);)n=r(n,i.head),i=i.tail;return n}),cw=e=>{let t=cb(),r=e;for(;!cd(r);)t=cv(t,r.head),r=r.tail;return t};Array.prototype;let ck=function(){function e(e){e&&Object.assign(this,e)}return e.prototype=tb,e}(),cC=e=>Object.assign(Object.create(tb),e),cE=Symbol.for("effect/DifferChunkPatch");function cO(e){return e}let cI=Object.assign(Object.create({...ck.prototype,[cE]:{_Value:cO,_Patch:cO}}),{_tag:"AndThen"}),cF=Symbol.for("effect/DifferContextPatch");function cT(e){return e}let cR={...ck.prototype,[cF]:{_Value:cT,_Patch:cT}},cN=Object.create(Object.assign(Object.create(cR),{_tag:"Empty"})),cA=()=>cN,cj=Object.assign(Object.create(cR),{_tag:"AndThen"}),cM=(e,t)=>{let r=Object.create(cj);return r.first=e,r.second=t,r},cz=Object.assign(Object.create(cR),{_tag:"AddService"}),cD=(e,t)=>{let r=Object.create(cz);return r.key=e,r.service=t,r},c$=Object.assign(Object.create(cR),{_tag:"RemoveService"}),cP=e=>{let t=Object.create(c$);return t.key=e,t},cL=Object.assign(Object.create(cR),{_tag:"UpdateService"}),cU=(e,t)=>{let r=Object.create(cL);return r.key=e,r.update=t,r},cq=(e,t)=>{let r=new Map(e.unsafeMap),n=cA();for(let[e,i]of t.unsafeMap.entries())if(r.has(e)){let t=r.get(e);r.delete(e),eJ(t,i)||(n=cB(cU(e,()=>i))(n))}else r.delete(e),n=cB(cD(e,i))(n);for(let[e]of r.entries())n=cB(cP(e))(n);return n},cB=U(2,(e,t)=>cM(e,t)),cJ=U(2,(e,t)=>{if("Empty"===e._tag)return t;let r=!1,n=ad(e),i=new Map(t.unsafeMap);for(;aM(n);){let e=aD(n),t=aq(n);switch(e._tag){case"Empty":n=t;break;case"AddService":i.set(e.key,e.service),n=t;break;case"AndThen":n=ak(ak(t,e.second),e.first);break;case"RemoveService":i.delete(e.key),n=t;break;case"UpdateService":i.set(e.key,e.update(i.get(e.key))),r=!0,n=t}}if(!r)return tP(i);let s=new Map;for(let[e]of t.unsafeMap)i.has(e)&&(s.set(e,i.get(e)),i.delete(e));for(let[e,t]of i)s.set(e,t);return tP(s)}),cH=Symbol.for("effect/DifferHashMapPatch");function cK(e){return e}let cW=Object.assign(Object.create({...ck.prototype,[cH]:{_Value:cK,_Key:cK,_Patch:cK}}),{_tag:"AndThen"}),cV=Symbol.for("effect/DifferHashSetPatch");function cG(e){return e}let cY={...ck.prototype,[cV]:{_Value:cG,_Key:cG,_Patch:cG}},cZ=Object.create(Object.assign(Object.create(cY),{_tag:"Empty"})),cQ=()=>cZ,cX=Object.assign(Object.create(cY),{_tag:"AndThen"}),c0=(e,t)=>{let r=Object.create(cX);return r.first=e,r.second=t,r},c1=Object.assign(Object.create(cY),{_tag:"Add"}),c2=e=>{let t=Object.create(c1);return t.value=e,t},c3=Object.assign(Object.create(cY),{_tag:"Remove"}),c5=e=>{let t=Object.create(c3);return t.value=e,t},c4=(e,t)=>{let[r,n]=lj([e,cQ()],([e,t],r)=>lw(r)(e)?[lT(r)(e),t]:[e,c6(c2(r))(t)])(t);return lj(n,(e,t)=>c6(c5(t))(e))(r)},c6=U(2,(e,t)=>c0(e,t)),c8=U(2,(e,t)=>{if("Empty"===e._tag)return t;let r=t,n=ad(e);for(;aM(n);){let e=aD(n),t=aq(n);switch(e._tag){case"Empty":n=t;break;case"AndThen":n=ak(e.first)(ak(e.second)(t));break;case"Add":r=lF(e.value)(r),n=t;break;case"Remove":r=lT(e.value)(r),n=t}}return r}),c7=Symbol.for("effect/DifferOrPatch");function c9(e){return e}let ue={...ck.prototype,[c7]:{_Value:c9,_Key:c9,_Patch:c9}},ut=Object.create(Object.assign(Object.create(ue),{_tag:"Empty"})),ur=()=>ut,un=Object.assign(Object.create(ue),{_tag:"AndThen"}),ui=(e,t)=>{let r=Object.create(un);return r.first=e,r.second=t,r},us=Object.assign(Object.create(ue),{_tag:"SetLeft"}),ua=e=>{let t=Object.create(us);return t.value=e,t},uo=Object.assign(Object.create(ue),{_tag:"SetRight"}),ul=e=>{let t=Object.create(uo);return t.value=e,t},uc=Object.assign(Object.create(ue),{_tag:"UpdateLeft"}),uu=e=>{let t=Object.create(uc);return t.patch=e,t},uh=Object.assign(Object.create(ue),{_tag:"UpdateRight"}),up=e=>{let t=Object.create(uh);return t.patch=e,t},uf=((e,t)=>ui(e,t),Symbol.for("effect/DifferReadonlyArrayPatch"));function ud(e){return e}let um={...ck.prototype,[uf]:{_Value:ud,_Patch:ud}},ug=Object.create(Object.assign(Object.create(um),{_tag:"Empty"})),ub=()=>ug,ux=Object.assign(Object.create(um),{_tag:"AndThen"}),uy=(e,t)=>{let r=Object.create(ux);return r.first=e,r.second=t,r},uv=Object.assign(Object.create(um),{_tag:"Append"}),uS=e=>{let t=Object.create(uv);return t.values=e,t},u_=Object.assign(Object.create(um),{_tag:"Slice"}),uw=(e,t)=>{let r=Object.create(u_);return r.from=e,r.until=t,r},uk=Object.assign(Object.create(um),{_tag:"Update"}),uC=(e,t)=>{let r=Object.create(uk);return r.index=e,r.patch=t,r},uE=e=>{let t=0,r=ub();for(;t<e.oldValue.length&&t<e.newValue.length;){let n=e.oldValue[t],i=e.newValue[t],s=e.differ.diff(n,i);eJ(s,e.differ.empty)||(r=uO(r,uC(t,s))),t+=1}return t<e.oldValue.length&&(r=uO(r,uw(0,t))),t<e.newValue.length&&(r=uO(r,uS(sb(t)(e.newValue)))),r},uO=U(2,(e,t)=>uy(e,t)),uI=U(3,(e,t,r)=>{if("Empty"===e._tag)return t;let n=t.slice(),i=sF(e);for(;ik(i);){let e=sh(i),t=sd(i);switch(e._tag){case"Empty":i=t;break;case"AndThen":t.unshift(e.first,e.second),i=t;break;case"Append":for(let t of e.values)n.push(t);i=t;break;case"Slice":n=n.slice(e.from,e.until),i=t;break;case"Update":n[e.index]=r.patch(e.patch,n[e.index]),i=t}}return n}),uF={[Symbol.for("effect/Differ")]:{_P:q,_V:q},pipe(){return e6(this,arguments)}},uT=e=>{let t=Object.create(uF);return t.empty=e.empty,t.diff=e.diff,t.combine=e.combine,t.patch=e.patch,t},uR=()=>uT({empty:cA(),combine:(e,t)=>cB(t)(e),diff:(e,t)=>cq(e,t),patch:(e,t)=>cJ(t)(e)}),uN=()=>uT({empty:cQ(),combine:(e,t)=>c6(t)(e),diff:(e,t)=>c4(e,t),patch:(e,t)=>c8(t)(e)}),uA=e=>uT({empty:ub(),combine:(e,t)=>uO(e,t),diff:(t,r)=>uE({oldValue:t,newValue:r,differ:e}),patch:(t,r)=>uI(t,r,e)}),uj=()=>uM((e,t)=>t),uM=e=>uT({empty:q,combine:(e,t)=>e===q?t:t===q?e:r=>t(e(r)),diff:(e,t)=>eJ(e,t)?q:B(t),patch:(t,r)=>e(r,t(r))}),uz=e=>255&e,uD=e=>e>>8&255,u$=(e,t)=>(255&e)+((t&e&255)<<8),uP=u$(0,0),uL=U(2,(e,t)=>u$(uz(e)&~t,uD(e))),uU=U(2,(e,t)=>e|t),uq=e=>~e>>>0&255,uB=e=>uV(e,32),uJ=U(2,(e,t)=>e&~t),uH=U(2,(e,t)=>e|t),uK=e=>uW(e)&&!uQ(e),uW=e=>uV(e,1),uV=U(2,(e,t)=>(e&t)!=0),uG=(...e)=>e.reduce((e,t)=>e|t,0),uY=uG(0),uZ=e=>uV(e,4),uQ=e=>uV(e,16),uX=U(2,(e,t)=>u$(e^t,t)),u0=U(2,(e,t)=>e&(uq(uz(t))|uD(t))|uz(t)&uD(t)),u1=uT({empty:uP,diff:(e,t)=>uX(e,t),combine:(e,t)=>uU(t)(e),patch:(e,t)=>u0(t,e)}),u2=e=>u$(e,e),u3=e=>u$(e,0),u5="Empty",u4="Fail",u6="Interrupt",u8="Parallel",u7="Sequential",u9="effect/Cause",he=Symbol.for(u9),ht={[he]:{_E:e=>e},[eR](){return W(eN(u9),ej(eN(hO(this))),eq(this))},[eB](e){return hl(e)&&hE(this,e)},pipe(){return e6(this,arguments)},toJSON(){switch(this._tag){case"Empty":return{_id:"Cause",_tag:this._tag};case"Die":return{_id:"Cause",_tag:this._tag,defect:eG(this.defect)};case"Interrupt":return{_id:"Cause",_tag:this._tag,fiberId:this.fiberId.toJSON()};case"Fail":return{_id:"Cause",_tag:this._tag,failure:eG(this.error)};case"Sequential":case"Parallel":return{_id:"Cause",_tag:this._tag,left:eG(this.left),right:eG(this.right)}}},toString(){return hD(this)},[eV](){return this.toJSON()}},hr=(()=>{let e=Object.create(ht);return e._tag=u5,e})(),hn=e=>{let t=Object.create(ht);return t._tag=u4,t.error=e,t},hi=e=>{let t=Object.create(ht);return t._tag="Die",t.defect=e,t},hs=e=>{let t=Object.create(ht);return t._tag=u6,t.fiberId=e,t},ha=(e,t)=>{let r=Object.create(ht);return r._tag=u8,r.left=e,r.right=t,r},ho=(e,t)=>{let r=Object.create(ht);return r._tag=u7,r.left=e,r.right=t,r},hl=e=>ec(e,he),hc=e=>e._tag===u5,hu=e=>"Die"===e._tag,hh=e=>e._tag===u5||hM(e,!0,(e,t)=>{switch(t._tag){case u5:return tR(e);case"Die":case u4:case u6:return tR(!1);default:return iC()}}),hp=e=>tF(hy(e)),hf=e=>hz(void 0,hR)(e),hd=e=>ax(hM(e,ap(),(e,t)=>t._tag===u4?tR(W(e,ak(t.error))):iC())),hm=e=>ax(hM(e,ap(),(e,t)=>"Die"===t._tag?tR(W(e,ak(t.defect))):iC())),hg=e=>hM(e,l_(),(e,t)=>t._tag===u6?tR(W(e,lF(t.fiberId))):iC()),hb=e=>hF(e,e=>e._tag===u4?tR(e.error):iC()),hx=e=>{let t=hb(e);switch(t._tag){case"None":return nk(e);case"Some":return nw(t.value)}},hy=e=>hF(e,e=>e._tag===u6?tR(e.fiberId):iC()),hv=e=>hj(e,{onEmpty:iC(),onFail:e=>tR(hi(e)),onDie:e=>tR(hi(e)),onInterrupt:()=>iC(),onSequential:iB(ho),onParallel:iB(ha)}),hS=e=>hj(e,{onEmpty:hr,onFail:()=>hr,onDie:hi,onInterrupt:hs,onSequential:ho,onParallel:ha}),h_=e=>hj(e,{onEmpty:hr,onFail:hi,onDie:hi,onInterrupt:hs,onSequential:ho,onParallel:ha}),hw=U(2,(e,t)=>hj(e,{onEmpty:tR(hr),onFail:e=>tR(hn(e)),onDie:e=>tF(t(e))?iC():tR(hi(e)),onInterrupt:e=>tR(hs(e)),onSequential:iB(ho),onParallel:iB(ha)})),hk=U(2,(e,t)=>hC(e,e=>hn(t(e)))),hC=U(2,(e,t)=>hj(e,{onEmpty:hr,onFail:e=>t(e),onDie:e=>hi(e),onInterrupt:e=>hs(e),onSequential:(e,t)=>ho(e,t),onParallel:(e,t)=>ha(e,t)})),hE=(e,t)=>{let r=ad(e),n=ad(t);for(;aM(r)&&aM(n);){let[e,t]=W(aD(r),hM([l_(),ap()],([e,t],r)=>{let[n,i]=hT(r);return tR([W(e,lN(n)),W(t,aI(i))])})),[i,s]=W(aD(n),hM([l_(),ap()],([e,t],r)=>{let[n,i]=hT(r);return tR([W(e,lN(n)),W(t,aI(i))])}));if(!eJ(e,i))return!1;r=t,n=s}return!0},hO=e=>hI(ad(e),ap()),hI=(e,t)=>{for(;;){let[r,n]=W(e,s$([l_(),ap()],([e,t],r)=>{let[n,i]=hT(r);return[W(e,lN(n)),W(t,aI(i))]})),i=lD(r)>0?W(t,ak(r)):t;if(aj(n))return ax(i);e=n,t=i}throw Error(ex("Cause.flattenCauseLoop"))},hF=U(2,(e,t)=>{let r=[e];for(;r.length>0;){let e=r.pop(),n=t(e);switch(n._tag){case"None":switch(e._tag){case u7:case u8:r.push(e.right),r.push(e.left)}break;case"Some":return n}}return iC()}),hT=e=>{let t=e,r=[],n=l_(),i=ap();for(;void 0!==t;)switch(t._tag){case u5:if(0===r.length)return[n,i];t=r.pop();break;case u4:if(n=lF(n,af(t._tag,t.error)),0===r.length)return[n,i];t=r.pop();break;case"Die":if(n=lF(n,af(t._tag,t.defect)),0===r.length)return[n,i];t=r.pop();break;case u6:if(n=lF(n,af(t._tag,t.fiberId)),0===r.length)return[n,i];t=r.pop();break;case u7:switch(t.left._tag){case u5:t=t.right;break;case u7:t=ho(t.left.left,ho(t.left.right,t.right));break;case u8:t=ha(ho(t.left.left,t.right),ho(t.left.right,t.right));break;default:i=ak(i,t.right),t=t.left}break;case u8:r.push(t.right),t=t.left}throw Error(ex("Cause.evaluateCauseLoop"))},hR={emptyCase:J,failCase:H,dieCase:H,interruptCase:J,sequentialCase:(e,t,r)=>t&&r,parallelCase:(e,t,r)=>t&&r},hN="SequentialCase",hA="ParallelCase",hj=U(2,(e,{onDie:t,onEmpty:r,onFail:n,onInterrupt:i,onParallel:s,onSequential:a})=>hz(e,void 0,{emptyCase:()=>r,failCase:(e,t)=>n(t),dieCase:(e,r)=>t(r),interruptCase:(e,t)=>i(t),sequentialCase:(e,t,r)=>a(t,r),parallelCase:(e,t,r)=>s(t,r)})),hM=U(3,(e,t,r)=>{let n=t,i=e,s=[];for(;void 0!==i;){let e=r(n,i);switch(n=tF(e)?e.value:n,i._tag){case u7:case u8:s.push(i.right),i=i.left;break;default:i=void 0}void 0===i&&s.length>0&&(i=s.pop())}return n}),hz=U(3,(e,t,r)=>{let n=[e],i=[];for(;n.length>0;){let e=n.pop();switch(e._tag){case u5:i.push(nk(r.emptyCase(t)));break;case u4:i.push(nk(r.failCase(t,e.error)));break;case"Die":i.push(nk(r.dieCase(t,e.defect)));break;case u6:i.push(nk(r.interruptCase(t,e.fiberId)));break;case u7:n.push(e.right),n.push(e.left),i.push(nw({_tag:hN}));break;case u8:n.push(e.right),n.push(e.left),i.push(nw({_tag:hA}))}}let s=[];for(;i.length>0;){let e=i.pop();switch(e._tag){case"Left":switch(e.left._tag){case hN:{let e=s.pop(),n=s.pop(),i=r.sequentialCase(t,e,n);s.push(i);break}case hA:{let e=s.pop(),n=s.pop(),i=r.parallelCase(t,e,n);s.push(i)}}break;case"Right":s.push(e.right)}}if(0===s.length)throw Error("BUG: Cause.reduceWithContext - please report an issue at https://github.com/Effect-TS/effect/issues");return s.pop()}),hD=(e,t)=>hf(e)?"All fibers interrupted without errors.":hH(e).map(function(e){return t?.renderErrorCause!==!0||void 0===e.cause?e.stack:`${e.stack} {
${h$(e.cause,"  ")}
}`}).join("\n"),h$=(e,t)=>{let r=e.stack.split("\n"),n=`${t}[cause]: ${r[0]}`;for(let e=1,i=r.length;e<i;e++)n+=`
${t}${r[e]}`;return e.cause&&(n+=` {
${h$(e.cause,`${t}  `)}
${t}}`),n};class hP extends globalThis.Error{constructor(e){let t="object"==typeof e&&null!==e,r=Error.stackTraceLimit;Error.stackTraceLimit=1,super(hL(e),t&&"cause"in e&&void 0!==e.cause?{cause:new hP(e.cause)}:void 0),this.span=void 0,""===this.message&&(this.message="An error has occurred"),Error.stackTraceLimit=r,this.name=e instanceof Error?e.name:"Error",t&&(hJ in e&&(this.span=e[hJ]),Object.keys(e).forEach(t=>{t in this||(this[t]=e[t])})),this.stack=hB(`${this.name}: ${this.message}`,e instanceof Error&&e.stack?e.stack:"",this.span)}}let hL=e=>{if("string"==typeof e)return e;if("object"==typeof e&&null!==e&&e instanceof Error)return e.message;try{if(ec(e,"toString")&&er(e.toString)&&e.toString!==Object.prototype.toString&&e.toString!==globalThis.Array.prototype.toString)return e.toString()}catch{}return e0(e)},hU=/\((.*)\)/g,hq=Y("effect/Tracer/spanToTrace",()=>new WeakMap),hB=(e,t,r)=>{let n=[e],i=t.startsWith(e)?t.slice(e.length).split("\n"):t.split("\n");for(let e=1;e<i.length;e++){if(i[e].includes(" at new BaseEffectError")||i[e].includes(" at new YieldableError")){e++;continue}if(i[e].includes("Generator.next")||i[e].includes("effect_internal_function"))break;n.push(i[e].replace(/at .*effect_instruction_i.*\((.*)\)/,"at $1").replace(/EffectPrimitive\.\w+/,"<anonymous>"))}if(r){let e=r,t=0;for(;e&&"Span"===e._tag&&t<10;){let r=hq.get(e);if("function"==typeof r){let t=r();if("string"==typeof t){let r=t.matchAll(hU),i=!1;for(let[,t]of r)i=!0,n.push(`    at ${e.name} (${t})`);i||n.push(`    at ${e.name} (${t.replace(/^at /,"")})`)}else n.push(`    at ${e.name}`)}else n.push(`    at ${e.name}`);e=iR(e.parent),t++}}return n.join("\n")},hJ=Symbol.for("effect/SpanAnnotation"),hH=e=>hz(e,void 0,{emptyCase:()=>[],dieCase:(e,t)=>[new hP(t)],failCase:(e,t)=>[new hP(t)],interruptCase:()=>[],parallelCase:(e,t,r)=>[...t,...r],sequentialCase:(e,t,r)=>[...t,...r]}),hK="Pending",hW="Done",hV=Symbol.for("effect/Deferred"),hG={_E:e=>e,_A:e=>e},hY=e=>({_tag:hK,joiners:e}),hZ=e=>({_tag:hW,effect:e});class hQ{constructor(e){this.called=!1,this.self=e}next(e){return this.called?{value:e,done:!0}:(this.called=!0,{value:this.self,done:!1})}return(e){return{value:e,done:!0}}throw(e){throw e}[Symbol.iterator](){return new hQ(this.self)}}let hX=(e,t)=>{let r=new h3("Blocked");return r.effect_instruction_i0=e,r.effect_instruction_i1=t,r},h0=e=>{let t=new h3("RunBlocked");return t.effect_instruction_i0=e,t},h1=Symbol.for("effect/Effect");class h2{constructor(e,t){this._op=tu,this.patch=e,this.op=t}}class h3{constructor(e){this.effect_instruction_i0=void 0,this.effect_instruction_i1=void 0,this.effect_instruction_i2=void 0,this.trace=void 0,this[h1]=tm,this._op=e}[eB](e){return this===e}[eR](){return eq(this,eA(this))}pipe(){return e6(this,arguments)}toJSON(){return{_id:"Effect",_op:this._op,effect_instruction_i0:eG(this.effect_instruction_i0),effect_instruction_i1:eG(this.effect_instruction_i1),effect_instruction_i2:eG(this.effect_instruction_i2)}}toString(){return eY(this.toJSON())}[eV](){return this.toJSON()}[Symbol.iterator](){return new hQ(new ew(this))}}class h5{constructor(e){this.effect_instruction_i0=void 0,this.effect_instruction_i1=void 0,this.effect_instruction_i2=void 0,this.trace=void 0,this[h1]=tm,this._op=e,this._tag=e}[eB](e){return ds(e)&&"Failure"===e._op&&eJ(this.effect_instruction_i0,e.effect_instruction_i0)}[eR](){return W(e$(this._tag),ej(eN(this.effect_instruction_i0)),eq(this))}get cause(){return this.effect_instruction_i0}pipe(){return e6(this,arguments)}toJSON(){return{_id:"Exit",_tag:this._op,cause:this.cause.toJSON()}}toString(){return eY(this.toJSON())}[eV](){return this.toJSON()}[Symbol.iterator](){return new hQ(new ew(this))}}class h4{constructor(e){this.effect_instruction_i0=void 0,this.effect_instruction_i1=void 0,this.effect_instruction_i2=void 0,this.trace=void 0,this[h1]=tm,this._op=e,this._tag=e}[eB](e){return ds(e)&&"Success"===e._op&&eJ(this.effect_instruction_i0,e.effect_instruction_i0)}[eR](){return W(e$(this._tag),ej(eN(this.effect_instruction_i0)),eq(this))}get value(){return this.effect_instruction_i0}pipe(){return e6(this,arguments)}toJSON(){return{_id:"Exit",_tag:this._op,value:eG(this.value)}}toString(){return eY(this.toJSON())}[eV](){return this.toJSON()}[Symbol.iterator](){return new hQ(new ew(this))}}let h6=e=>ec(e,h1),h8=e=>{let t=new h3(tl);return t.effect_instruction_i0=e,t},h7=U(3,(e,t,r)=>pY(n=>pv(e,e=>pv(pf(pJ(()=>n(t(e)))),t=>pJ(()=>r(e,t)).pipe(pC({onFailure:e=>{switch(t._tag){case e9:return pg(ho(t.effect_instruction_i0,e));case tn:return pg(e)}},onSuccess:()=>t})))))),h9=U(2,(e,t)=>pv(e,()=>pB(t))),pe=e=>h9(e,void 0),pt=function(){let e=new h3(e7);switch(arguments.length){case 2:e.effect_instruction_i0=arguments[0],e.commit=arguments[1];break;case 3:e.effect_instruction_i0=arguments[0],e.effect_instruction_i1=arguments[1],e.commit=arguments[2];break;case 4:e.effect_instruction_i0=arguments[0],e.effect_instruction_i1=arguments[1],e.effect_instruction_i2=arguments[2],e.commit=arguments[3];break;default:throw Error(ex("you're not supposed to end up here"))}return e},pr=(e,t=l7)=>{let r,n=new h3(e8);return n.effect_instruction_i0=t=>{r=e(t)},n.effect_instruction_i1=t,p$(n,e=>h6(r)?r:pZ)},pn=(e,t=l7)=>pJ(()=>pr(e,t)),pi=(e,t=l7)=>pt(e,function(){let e,r,n,i;function s(t){e?e(t):void 0===r&&(r=t)}let a=new h3(e8);return a.effect_instruction_i0=t=>{e=t,r&&t(r)},a.effect_instruction_i1=t,1!==this.effect_instruction_i0.length?(i=new AbortController,n=eO(()=>this.effect_instruction_i0(s,i.signal))):n=eO(()=>this.effect_instruction_i0(s)),n||i?p$(a,e=>(i&&i.abort(),n??pZ)):a}),ps=U(2,(e,t)=>{let r=new h3(te);return r.effect_instruction_i0=e,r.effect_instruction_i1=t,r}),pa=U(2,(e,t)=>pE(e,{onFailure:t,onSuccess:pB})),po=U(3,(e,t,r)=>ps(e,e=>{let n=hx(e);switch(n._tag){case"Left":return t(n.left)?r(n.left):pg(e);case"Right":return pg(n.right)}})),pl=Symbol.for("effect/OriginalAnnotation"),pc=(e,t)=>tF(t)?new Proxy(e,{has:(e,t)=>t===hJ||t===pl||t in e,get:(r,n)=>n===hJ?t.value:n===pl?e:r[n]}):e,pu=e=>!el(e)||hJ in e?pg(hi(e)):h8(t=>pg(hi(pc(e,dJ(t))))),ph=e=>pb(()=>hi(new f1(e))),pp=e=>pE(e,{onFailure:e=>pB(nw(e)),onSuccess:e=>pB(nk(e))}),pf=e=>pk(e,{onFailure:dd,onSuccess:dS}),pd=e=>!el(e)||hJ in e?pg(hn(e)):h8(t=>pg(hn(pc(e,dJ(t))))),pm=e=>pv(pH(e),pd),pg=e=>{let t=new h5(e9);return t.effect_instruction_i0=e,t},pb=e=>pv(pH(e),pg),px=h8(e=>pB(e.id())),py=e=>h8(t=>e(t.id())),pv=U(2,(e,t)=>{let r=new h3(tt);return r.effect_instruction_i0=e,r.effect_instruction_i1=t,r}),pS=U(2,(e,t)=>pv(e,e=>{let r="function"==typeof t?t(e):t;return h6(r)?r:eb(r)?pr(e=>{r.then(t=>e(pB(t)),t=>e(pd(new di(t,"An unknown error occurred in Effect.andThen"))))}):pB(r)})),p_=e=>{let t=new h3("OnStep");return t.effect_instruction_i0=e,t},pw=e=>pv(e,q),pk=U(2,(e,t)=>pC(e,{onFailure:e=>pB(t.onFailure(e)),onSuccess:e=>pB(t.onSuccess(e))})),pC=U(2,(e,t)=>{let r=new h3(tr);return r.effect_instruction_i0=e,r.effect_instruction_i1=t.onFailure,r.effect_instruction_i2=t.onSuccess,r}),pE=U(2,(e,t)=>pC(e,{onFailure:e=>{if(hm(e).length>0)return pg(h_(e));let r=hd(e);return r.length>0?t.onFailure(aD(r)):pg(e)},onSuccess:t.onSuccess})),pO=U(2,(e,t)=>pJ(()=>{let r=i6(e),n=i5(r.length),i=0;return h9(p0({while:()=>i<r.length,body:()=>t(r[i],i),step:e=>{n[i++]=e}}),n)})),pI=U(2,(e,t)=>pJ(()=>{let r=i6(e),n=0;return p0({while:()=>n<r.length,body:()=>t(r[n],n),step:()=>{n++}})})),pF=U(e=>"boolean"==typeof e[0]||h6(e[0]),(e,t)=>h6(e)?pv(e,e=>e?t.onTrue():t.onFalse()):e?t.onTrue():t.onFalse()),pT=pv(px,e=>pR(e)),pR=e=>pg(hs(e)),pN=e=>{let t=new h3(ts);return t.effect_instruction_i0=u2(1),t.effect_instruction_i1=()=>e,t},pA=U(2,(e,t)=>pY(r=>pv(pf(r(e)),e=>dN(t,e)))),pj=U(2,(e,t)=>pv(e,e=>pH(()=>t(e)))),pM=U(2,(e,t)=>pE(e,{onFailure:e=>pm(()=>t.onFailure(e)),onSuccess:e=>pH(()=>t.onSuccess(e))})),pz=U(2,(e,t)=>pC(e,{onFailure:e=>{let r=hx(e);switch(r._tag){case"Left":return pm(()=>t(r.left));case"Right":return pg(r.right)}},onSuccess:pB})),pD=((e,t)=>pD(e,e=>dl(e)?pZ:t(e.effect_instruction_i0)),U(2,(e,t)=>pY(r=>pC(r(e),{onFailure:e=>{let r=dd(e);return pC(t(r),{onFailure:t=>dd(ho(e,t)),onSuccess:()=>r})},onSuccess:e=>{let r=dS(e);return p6(t(r),r)}})))),p$=U(2,(e,t)=>pD(e,dy({onFailure:e=>hf(e)?pe(t(hg(e))):pZ,onSuccess:()=>pZ}))),pP=U(2,(e,t)=>pV(e,t,pB)),pL=e=>pU(e,q),pU=U(2,(e,t)=>pE(e,{onFailure:e=>pu(t(e)),onSuccess:pB})),pq=h8((e,t)=>pB(t.runtimeFlags)),pB=e=>{let t=new h4(tn);return t.effect_instruction_i0=e,t},pJ=e=>{let t=new h3(e7);return t.commit=e,t},pH=e=>{let t=new h3(ti);return t.effect_instruction_i0=e,t},pK=U(e=>3===e.length||2===e.length&&!(el(e[1])&&"onlyEffect"in e[1]),(e,t)=>pv(e,e=>{let r="function"==typeof t?t(e):t;return h6(r)?h9(r,e):eb(r)?pr(t=>{r.then(r=>t(pB(e)),e=>t(pd(new di(e,"An unknown error occurred in Effect.tap"))))}):pB(e)})),pW=e=>h8(t=>{let r=W(t.getFiberRef(fP),iO(()=>t.scope()));return e(fS(fP,tR(r)))}),pV=U(3,(e,t,r)=>pC(e,{onFailure:e=>hm(e).length>0?pg(ij(hv(e))):t(),onSuccess:r})),pG=e=>{let t=new h3(ts);return t.effect_instruction_i0=u3(1),t.effect_instruction_i1=()=>e,t},pY=e=>pt(e,function(){let e=new h3(ts);return e.effect_instruction_i0=u3(1),e.effect_instruction_i1=e=>uW(e)?eO(()=>this.effect_instruction_i0(pN)):eO(()=>this.effect_instruction_i0(pG)),e}),pZ=pB(void 0),pQ=e=>{let t=new h3(ts);return t.effect_instruction_i0=e,t.effect_instruction_i1=void 0,t},pX=U(2,(e,t)=>pv(t,t=>t?W(e,pj(tR)):pB(iC()))),p0=e=>{let t=new h3(ta);return t.effect_instruction_i0=e.while,t.effect_instruction_i1=e.body,t.effect_instruction_i2=e.step,t},p1=e=>pJ(()=>{let t=new h3(to);return t.effect_instruction_i0=e(),t}),p2=U(2,(e,t)=>{let r=new h3(ts);return r.effect_instruction_i0=t,r.effect_instruction_i1=()=>e,r}),p3=e=>{let t=new h3(tc);return void 0!==e?.priority?fj(t,e.priority):t},p5=U(2,(e,t)=>pv(e,e=>pj(t,t=>[e,t]))),p4=U(2,(e,t)=>pv(e,e=>h9(t,e))),p6=U(2,(e,t)=>pv(e,()=>t)),p8=U(3,(e,t,r)=>pv(e,e=>pj(t,t=>r(e,t)))),p7=pn(()=>{let e=setInterval(()=>{},0x80000000-1);return pH(()=>clearInterval(e))}),p9=e=>pv(px,t=>W(e,fe(t))),fe=U(2,(e,t)=>pv(e.interruptAsFork(t),()=>e.await)),ft={_tag:"All",syslog:0,label:"ALL",ordinal:Number.MIN_SAFE_INTEGER,pipe(){return e6(this,arguments)}},fr={_tag:"Fatal",syslog:2,label:"FATAL",ordinal:5e4,pipe(){return e6(this,arguments)}},fn={_tag:"Error",syslog:3,label:"ERROR",ordinal:4e4,pipe(){return e6(this,arguments)}},fi={_tag:"Warning",syslog:4,label:"WARN",ordinal:3e4,pipe(){return e6(this,arguments)}},fs={_tag:"Info",syslog:6,label:"INFO",ordinal:2e4,pipe(){return e6(this,arguments)}},fa={_tag:"Debug",syslog:7,label:"DEBUG",ordinal:1e4,pipe(){return e6(this,arguments)}},fo={_tag:"Trace",syslog:7,label:"TRACE",ordinal:0,pipe(){return e6(this,arguments)}},fl={_tag:"None",syslog:7,label:"OFF",ordinal:Number.MAX_SAFE_INTEGER,pipe(){return e6(this,arguments)}},fc=[ft,fo,fa,fs,fi,fn,fr,fl],fu=Symbol.for("effect/FiberRef"),fh={_A:e=>e},fp=e=>h8(t=>dS(t.getFiberRef(e))),ff=U(2,(e,t)=>pv(fp(e),t)),fd=U(2,(e,t)=>fm(e,()=>[void 0,t])),fm=U(2,(e,t)=>h8(r=>{let[n,i]=t(r.getFiberRef(e));return r.setFiberRef(e,i),pB(n)})),fg=U(2,(e,t)=>fm(e,e=>[void 0,t(e)])),fb=Symbol.for("effect/RequestResolver"),fx={_A:e=>e,_R:e=>e};class fy{constructor(e,t){this[fb]=fx,this.runAll=e,this.target=t}[eR](){return eq(this,this.target?eN(this.target):eA(this))}[eB](e){return this.target?fv(e)&&eJ(this.target,e.target):this===e}identified(...e){return new fy(this.runAll,am(e))}pipe(){return e6(this,arguments)}}let fv=e=>ec(e,fb),fS=U(3,(e,t,r)=>h7(p4(fp(t),fd(t,r)),()=>e,e=>fd(t,e))),f_=U(3,(e,t,r)=>ff(t,n=>fS(e,t,r(n)))),fw=(e,t)=>fO(e,{differ:uj(),fork:t?.fork??q,join:t?.join}),fk=e=>{let t=uN();return fO(e,{differ:t,fork:t.empty})},fC=e=>{let t=uA(uj());return fO(e,{differ:t,fork:t.empty})},fE=e=>{let t=uR();return fO(e,{differ:t,fork:t.empty})},fO=(e,t)=>({...tx,[fu]:fh,initial:e,commit(){return fp(this)},diff:(e,r)=>t.differ.diff(e,r),combine:(e,r)=>t.differ.combine(e,r),patch:e=>r=>t.differ.patch(e,r),fork:t.fork,join:t.join??((e,t)=>t)}),fI=Y(Symbol.for("effect/FiberRef/currentContext"),()=>fE(t0())),fF=Y(Symbol.for("effect/FiberRef/currentSchedulingPriority"),()=>fw(0)),fT=Y(Symbol.for("effect/FiberRef/currentMaxOpsBeforeYield"),()=>fw(2048)),fR=Y(Symbol.for("effect/FiberRef/currentLogAnnotation"),()=>fw(o9())),fN=Y(Symbol.for("effect/FiberRef/currentLogLevel"),()=>fw(fs)),fA=Y(Symbol.for("effect/FiberRef/currentLogSpan"),()=>fw(cb())),fj=U(2,(e,t)=>fS(e,fF,t)),fM=Y(Symbol.for("effect/FiberRef/currentConcurrency"),()=>fw("unbounded")),fz=Y(Symbol.for("effect/FiberRef/currentRequestBatching"),()=>fw(!0)),fD=Y(Symbol.for("effect/FiberRef/currentUnhandledErrorLogLevel"),()=>fw(tR(fa))),f$=Y(Symbol.for("effect/FiberRef/currentMetricLabels"),()=>fC(sI())),fP=Y(Symbol.for("effect/FiberRef/currentForkScopeOverride"),()=>fw(iC(),{fork:()=>iC(),join:(e,t)=>e})),fL=Y(Symbol.for("effect/FiberRef/currentInterruptedCause"),()=>fw(hr,{fork:()=>hr,join:(e,t)=>e})),fU=Y(Symbol.for("effect/FiberRef/currentTracerEnabled"),()=>fw(!0)),fq=Y(Symbol.for("effect/FiberRef/currentTracerTiming"),()=>fw(!0)),fB=Y(Symbol.for("effect/FiberRef/currentTracerSpanAnnotations"),()=>fw(o9())),fJ=Y(Symbol.for("effect/FiberRef/currentTracerSpanLinks"),()=>fw(ap())),fH=Symbol.for("effect/Scope"),fK=Symbol.for("effect/CloseableScope"),fW=(e,t)=>e.addFinalizer(()=>pe(t)),fV=(e,t)=>e.addFinalizer(t),fG=(e,t)=>e.close(t),fY=(e,t)=>e.fork(t),fZ=U(2,(e,t)=>{let r=W(e,hb,iM(t));switch(r._tag){case"None":return W(hm(e),az,iE({onNone:()=>{let t=i6(hg(e)).flatMap(e=>i6(l5(e)).map(e=>`#${e}`));return new f3(t?`Interrupted by fibers: ${t.join(", ")}`:void 0)},onSome:q}));case"Some":return r.value}}),fQ=function(){class e extends globalThis.Error{commit(){return pd(this)}toJSON(){let e={...this};return this.message&&(e.message=this.message),this.cause&&(e.cause=this.cause),e}[eV](){return this.toString!==globalThis.Error.prototype.toString?this.stack?`${this.toString()}
${this.stack.split("\n").slice(1).join("\n")}`:this.toString():"Bun"in globalThis?hD(hn(this),{renderErrorCause:!0}):this}}return Object.assign(e.prototype,ty),e}(),fX=(e,t)=>{class r extends fQ{constructor(...e){super(...e),this._tag=t}}return Object.assign(r.prototype,e),r.prototype.name=t,r},f0=Symbol.for("effect/Cause/errors/RuntimeException"),f1=fX({[f0]:f0},"RuntimeException"),f2=Symbol.for("effect/Cause/errors/InterruptedException"),f3=fX({[f2]:f2},"InterruptedException"),f5=e=>ec(e,f2),f4=Symbol.for("effect/Cause/errors/IllegalArgument"),f6=fX({[f4]:f4},"IllegalArgumentException"),f8=Symbol.for("effect/Cause/errors/NoSuchElement"),f7=fX({[f8]:f8},"NoSuchElementException"),f9=Symbol.for("effect/Cause/errors/InvalidPubSubCapacityException"),de=fX({[f9]:f9},"InvalidPubSubCapacityException"),dt=Symbol.for("effect/Cause/errors/Timeout"),dr=fX({[dt]:dt},"TimeoutException"),dn=Symbol.for("effect/Cause/errors/UnknownException"),di=function(){class e extends fQ{constructor(e,t){super(t??"An unknown error occurred",{cause:e}),this._tag="UnknownException",this.error=e}}return Object.assign(e.prototype,{[dn]:dn,name:"UnknownException"}),e}(),ds=e=>h6(e)&&"_tag"in e&&("Success"===e._tag||"Failure"===e._tag),da=e=>"Failure"===e._tag,dl=e=>"Success"===e._tag,dc=U(2,(e,t)=>{switch(e._tag){case e9:return dd(e.effect_instruction_i0);case tn:return dS(t)}}),du=e=>dc(e,void 0),dh=(e,t)=>dE(e,t?.parallel?ha:ho),dp=e=>dd(hi(e)),df=e=>dd(hn(e)),dd=e=>{let t=new h5(e9);return t.effect_instruction_i0=e,t},dm=U(2,(e,t)=>{switch(e._tag){case e9:return dd(e.effect_instruction_i0);case tn:return t(e.effect_instruction_i0)}}),dg=U(2,(e,t)=>{switch(e._tag){case e9:return pB(dd(e.effect_instruction_i0));case tn:return pf(t(e.effect_instruction_i0))}}),db=e=>dd(hs(e)),dx=U(2,(e,t)=>{switch(e._tag){case e9:return dd(e.effect_instruction_i0);case tn:return dS(t(e.effect_instruction_i0))}}),dy=U(2,(e,{onFailure:t,onSuccess:r})=>{switch(e._tag){case e9:return t(e.effect_instruction_i0);case tn:return r(e.effect_instruction_i0)}}),dv=U(2,(e,{onFailure:t,onSuccess:r})=>{switch(e._tag){case e9:return t(e.effect_instruction_i0);case tn:return r(e.effect_instruction_i0)}}),dS=e=>{let t=new h4(tn);return t.effect_instruction_i0=e,t},d_=dS(void 0),dw=U(2,(e,t)=>dC(e,t,{onSuccess:(e,t)=>[e,t],onFailure:ho})),dk=U(2,(e,t)=>dC(e,t,{onSuccess:(e,t)=>t,onFailure:ho})),dC=U(3,(e,t,{onFailure:r,onSuccess:n})=>{switch(e._tag){case e9:switch(t._tag){case tn:return dd(e.effect_instruction_i0);case e9:return dd(r(e.effect_instruction_i0,t.effect_instruction_i0))}case tn:switch(t._tag){case tn:return dS(n(e.effect_instruction_i0,t.effect_instruction_i0));case e9:return dd(t.effect_instruction_i0)}}}),dE=(e,t)=>{let r=am(e);return aM(r)?W(aq(r),s$(W(aD(r),dx(ad)),(e,r)=>W(e,dC(r,{onSuccess:(e,t)=>W(e,ak(t)),onFailure:t}))),dx(ax),dx(e=>ab(e)),tR):iC()},dO=e=>({...tx,[hV]:hG,state:lL(hY([])),commit(){return dT(this)},blockingOn:e}),dI=()=>pv(px,e=>dF(e)),dF=e=>pH(()=>dO(e)),dT=e=>pn(t=>{let r=lq(e.state);switch(r._tag){case hW:return t(r.effect);case hK:return r.joiners.push(t),dD(e,t)}},e.blockingOn),dR=U(2,(e,t)=>pH(()=>{let r=lq(e.state);switch(r._tag){case hW:return!1;case hK:lB(e.state,hZ(t));for(let e=0,n=r.joiners.length;e<n;e++)r.joiners[e](t);return!0}})),dN=U(2,(e,t)=>dR(e,t)),dA=((e,t)=>dR(e,pd(t)),U(2,(e,t)=>dR(e,pg(t)))),dj=U(2,(e,t)=>dR(e,pR(t))),dM=U(2,(e,t)=>dR(e,pB(t))),dz=(e,t)=>{let r=lq(e.state);if(r._tag===hK){lB(e.state,hZ(t));for(let e=0,n=r.joiners.length;e<n;e++)r.joiners[e](t)}},dD=(e,t)=>pH(()=>{let r=lq(e.state);if(r._tag===hK){let e=r.joiners.indexOf(t);e>=0&&r.joiners.splice(e,1)}}),d$=h8(e=>dS(e.currentContext)),dP=()=>d$,dL=e=>pv(dP(),e),dU=U(2,(e,t)=>fS(fI,t)(e)),dq=U(2,(e,t)=>f_(fI,e=>tZ(e,t))(e)),dB=U(2,(e,t)=>dL(r=>dU(e,t(r)))),dJ=((e,t)=>pv(e,e=>pv(t.predicate(e),r=>r?pB(e):t.orElse(e))),e=>{let t=e.currentSpan;return void 0!==t&&"Span"===t._tag?tR(t):iC()}),dH={_tag:"Span",spanId:"noop",traceId:"noop",sampled:!1,status:{_tag:"Ended",startTime:BigInt(0),endTime:BigInt(0),exit:d_},attributes:new Map,links:[],kind:"internal",attribute(){},event(){},end(){},addLinks(){}},dK=e=>Object.assign(Object.create(dH),e),dW=Symbol.for("effect/Clock"),dV=tQ("effect/Clock"),dG=0x80000000-1,dY={unsafeSchedule(e,t){let r=oO(t);if(r>dG)return H;let n=!1,i=setTimeout(()=>{n=!0,e()},r);return()=>(clearTimeout(i),!n)}},dZ=function(){let e,t=BigInt(1e6);return"undefined"==typeof performance?()=>BigInt(Date.now())*t:()=>(void 0===e&&(e=BigInt(Date.now())*t-BigInt(Math.round(1e6*performance.now()))),e+BigInt(Math.round(1e6*performance.now())))}(),dQ=function(){let e="object"==typeof process&&"hrtime"in process&&"function"==typeof process.hrtime.bigint?process.hrtime:void 0;if(!e)return dZ;let t=dZ()-e.bigint();return()=>t+e.bigint()}();class dX{unsafeCurrentTimeMillis(){return Date.now()}unsafeCurrentTimeNanos(){return dQ()}scheduler(){return pB(dY)}sleep(e){return pi(t=>pe(pH(dY.unsafeSchedule(()=>t(pZ),e))))}constructor(){this[dW]=dW,this.currentTimeMillis=pH(()=>this.unsafeCurrentTimeMillis()),this.currentTimeNanos=pH(()=>this.unsafeCurrentTimeNanos())}}let d0=e=>Math.max(Math.pow(2,Math.ceil(Math.log(e)/Math.log(2))),2),d1=e=>{if("NaN"===e)return tR(NaN);if("Infinity"===e)return tR(1/0);if("-Infinity"===e)return tR(-1/0);if(""===e.trim())return tT;let t=Number(e);return Number.isNaN(t)?tT:tR(t)},d2=e=>e.replace(/[/\\^$*+?.()|[\]{}]/g,"\\$&"),d3={_tag:"Empty"},d5=U(2,(e,t)=>({_tag:"AndThen",first:e,second:t})),d4=U(2,(e,t)=>d5(e,{_tag:"MapName",f:t})),d6=U(2,(e,t)=>d5(e,{_tag:"Nested",name:t})),d8=U(2,(e,t)=>d5(e,{_tag:"Unnested",name:t})),d7=U(2,(e,t)=>{let r=cx(t),n=e;for(;cm(r);){let e=r.head;switch(e._tag){case"Empty":r=r.tail;break;case"AndThen":r=cg(e.first,cg(e.second,r.tail));break;case"MapName":n=sT(n,e.f),r=r.tail;break;case"Nested":n=se(n,e.name),r=r.tail;break;case"Unnested":if(!W(su(n),iU(e.name)))return nw(a5(n,`Expected ${e.name} to be in path in ConfigProvider#unnested`));n=sd(n),r=r.tail}}return nk(n)}),d9="Constant",me="Fallback",mt="Lazy",mr="MapOrFail",mn="Nested",mi="Primitive",ms="ZipWith",ma=e=>e.toLowerCase(),mo=e=>e.toUpperCase(),ml=(e,t,r)=>t instanceof RegExp?e.replace(t,r):t.reduce((e,t)=>e.replace(t,r),e),mc=[/([a-z0-9])([A-Z])/g,/([A-Z])([A-Z][a-z])/g],mu=/[^A-Z0-9]+/gi,mh=(e,t={})=>{let{delimiter:r=" ",splitRegexp:n=mc,stripRegexp:i=mu,transform:s=ma}=t,a=ml(ml(e,n,"$1\0$2"),i,"\0"),o=0,l=a.length;for(;"\0"===a.charAt(o);)o++;for(;"\0"===a.charAt(l-1);)l--;return a.slice(o,l).split("\0").map(s).join(r)},mp=(e,t)=>mh(e,{delimiter:"_",transform:mo,...t}),mf=(e,t)=>[...e,...t],md=Symbol.for("effect/ConfigProvider"),mm=tQ("effect/ConfigProvider"),mg=Symbol.for("effect/ConfigProviderFlat"),mb=e=>({[md]:md,pipe(){return e6(this,arguments)},...e}),mx=e=>({[mg]:mg,patch:e.patch,load:(t,r,n=!0)=>e.load(t,r,n),enumerateChildren:e.enumerateChildren}),my=e=>mb({load:t=>pv(mk(e,sI(),t,!1),e=>iE(su(e),{onNone:()=>pd(a5(sI(),`Expected a single value having structure: ${t}`)),onSome:pB})),flattened:e}),mv=e=>{let{pathDelim:t,seqDelim:r}=Object.assign({},{pathDelim:"_",seqDelim:","},e),n=e=>W(e,sH(t)),i=e=>e.split(t),s=()=>"undefined"!=typeof process&&"env"in process&&"object"==typeof process.env?process.env:{};return my(mx({load:(e,t,i=!0)=>{let a=n(e),o=s();return W(a in o?tR(o[a]):iC(),pz(()=>a5(e,`Expected ${a} to exist in the process context`)),pv(n=>mN(n,e,t,r,i)))},enumerateChildren:e=>pH(()=>lM(Object.keys(s()).map(e=>i(e.toUpperCase())).filter(t=>{for(let r=0;r<e.length;r++){let n=W(e,sc(r)),i=t[r];if(void 0===i||n!==i)return!1}return!0}).flatMap(t=>t.slice(e.length,e.length+1)))),patch:d3}))},mS=(e,t)=>{let{pathDelim:r,seqDelim:n}=Object.assign({seqDelim:",",pathDelim:"."},t),i=e=>W(e,sH(r)),s=e=>e.split(r),a=m$(e,e=>s(e),i);return my(mx({load:(e,t,r=!0)=>{let s=i(e);return W(a.has(s)?tR(a.get(s)):iC(),pz(()=>a5(e,`Expected ${s} to exist in the provided map`)),pv(i=>mN(i,e,t,n,r)))},enumerateChildren:e=>pH(()=>lM(i6(a.keys()).map(s).filter(t=>{for(let r=0;r<e.length;r++){let n=W(e,sc(r)),i=t[r];if(void 0===i||n!==i)return!1}return!0}).flatMap(t=>t.slice(e.length,e.length+1)))),patch:d3}))},m_=(e,t,r,n)=>{let i=sU(r.length,t=>t>=n.length?iC():tR([e(t),t+1])),s=sU(n.length,e=>e>=r.length?iC():tR([t(e),e+1]));return[mf(r,i),mf(n,s)]},mw=(e,t)=>{let r=t;if("Nested"===r._tag){let t=e.slice();for(;"Nested"===r._tag;)t.push(r.name),r=r.config;return t}return e},mk=(e,t,r,n)=>{switch(r._tag){case d9:return pB(sF(r.value));case"Described":return pJ(()=>mk(e,t,r.config,n));case"Fail":return pd(a5(t,r.message));case me:return W(pJ(()=>mk(e,t,r.first,n)),pa(i=>r.condition(i)?W(mk(e,t,r.second,n),pa(e=>pd(a2(i,e)))):pd(i)));case mt:return pJ(()=>mk(e,t,r.config(),n));case mr:return pJ(()=>W(mk(e,t,r.original,n),pv(pO(e=>W(r.mapOrFail(e),pz(a8(mw(t,r.original))))))));case mn:return pJ(()=>mk(e,mf(t,sF(r.name)),r.config,n));case mi:return W(d7(t,e.patch),pv(t=>W(e.load(t,r,n),pv(e=>{if(0===e.length){let e=W(sp(t),iO(()=>"<n/a>"));return pd(a5([],`Expected ${r.description} with name ${e}`))}return pB(e)}))));case"Sequence":return W(d7(t,e.patch),pv(n=>W(e.enumerateChildren(n),pv(mj),pv(n=>0===n.length?pJ(()=>pj(mk(e,t,r.config,!0),sF)):W(pO(n,n=>mk(e,st(t,`[${n}]`),r.config,!0)),pj(e=>{let t=sN(e);return 0===t.length?sF(sI()):sF(t)}))))));case"HashMap":return pJ(()=>W(d7(t,e.patch),pv(t=>W(e.enumerateChildren(t),pv(i=>W(i,pO(i=>mk(e,mf(t,sF(i)),r.valueConfig,n)),pj(e=>0===e.length?sF(o9()):W(mA(e),sT(e=>ci(sv(i6(i),e)))))))))));case ms:return pJ(()=>W(mk(e,t,r.left,n),pp,pv(i=>W(mk(e,t,r.right,n),pp,pv(e=>{if(nS(i)&&nS(e))return pd(a1(i.left,e.left));if(nS(i)&&n_(e))return pd(i.left);if(n_(i)&&nS(e))return pd(e.left);if(n_(i)&&n_(e)){let n=W(t,sH(".")),s=mC(t,n),[a,o]=m_(s,s,W(i.right,sT(nk)),W(e.right,sT(nk)));return W(a,sv(o),pO(([e,t])=>W(p5(e,t),pj(([e,t])=>r.zip(e,t)))))}throw Error("BUG: ConfigProvider.fromFlatLoop - please report an issue at https://github.com/Effect-TS/effect/issues")})))))}},mC=(e,t)=>r=>nw(a5(e,`The element at index ${r} in a sequence at path "${t}" was missing`)),mE=U(2,(e,t)=>my(mO(e.flattened,t))),mO=(e,t)=>mx({load:(t,r,n=!0)=>e.load(t,r,n),enumerateChildren:t=>e.enumerateChildren(t),patch:d4(e.patch,t)}),mI=U(2,(e,t)=>my(mx({load:(t,r)=>e.flattened.load(t,r,!0),enumerateChildren:t=>e.flattened.enumerateChildren(t),patch:d6(e.flattened.patch,t)}))),mF=((e,t)=>my(mx({load:(t,r)=>e.flattened.load(t,r,!0),enumerateChildren:t=>e.flattened.enumerateChildren(t),patch:d8(e.flattened.patch,t)})),U(2,(e,t)=>my(mT(e.flattened,()=>t().flattened)))),mT=(e,t)=>mx({load:(r,n,i)=>W(d7(r,e.patch),pv(t=>e.load(t,n,i)),pa(e=>W(pH(t),pv(t=>W(d7(r,t.patch),pv(e=>t.load(e,n,i)),pa(t=>pd(a2(e,t)))))))),enumerateChildren:r=>W(d7(r,e.patch),pv(t=>e.enumerateChildren(t)),pp,pv(e=>W(pH(t),pv(t=>W(d7(r,t.patch),pv(e=>t.enumerateChildren(e)),pp,pv(t=>{if(nS(e)&&nS(t))return pd(a1(e.left,t.left));if(nS(e)&&n_(t))return pB(t.right);if(n_(e)&&nS(t))return pB(e.right);if(n_(e)&&n_(t))return pB(W(e.right,lN(t.right)));throw Error("BUG: ConfigProvider.orElseFlat - please report an issue at https://github.com/Effect-TS/effect/issues")})))))),patch:d3}),mR=(e,t)=>e.split(RegExp(`\\s*${d2(t)}\\s*`)),mN=(e,t,r,n,i)=>i?W(mR(e,n),pO(e=>r.parse(e.trim())),pz(a8(t))):W(r.parse(e),pM({onFailure:a8(t),onSuccess:sF})),mA=e=>Object.keys(e[0]).map(t=>e.map(e=>e[t])),mj=e=>W(pO(e,mD),pM({onFailure:()=>sI(),onSuccess:sy(iW)}),pp,pj(nT)),mM=/(^.+)(\[(\d+)\])$/,mz=/^(\[(\d+)\])$/,mD=e=>{let t=e.match(mz);if(null!==t){let e=t[2];return W(void 0!==e&&e.length>0?tR(e):iC(),iz(mL))}return iC()},m$=(e,t,r)=>{let n=new Map;for(let[i,s]of e){let e=W(t(i),sR(e=>iE(mP(e),{onNone:()=>sF(e),onSome:([e,t])=>i3(e,`[${t}]`)})));n.set(r(e),s)}return n},mP=e=>{let t=e.match(mM);if(null!==t){let e=t[1],r=t[3];return i$([void 0!==e&&e.length>0?tR(e):iC(),W(void 0!==r&&r.length>0?tR(r):iC(),iz(mL))])}return iC()},mL=e=>{let t=Number.parseInt(e);return Number.isNaN(t)?iC():tR(t)},mU=e=>({_tag:"KeyName",name:e}),mq=e=>({_tag:"KeyIndex",index:e}),mB=e=>{let t=[],r=0;for(;r<e.length;){let n=e[r];if("KeyName"===n._tag)if(r+1<e.length){let i=e[r+1];"KeyIndex"===i._tag?(t.push(`${n.name}[${i.index}]`),r+=2):(t.push(n.name),r+=1)}else t.push(n.name),r+=1}return t},mJ=e=>{let t=(e,t)=>"string"==typeof t?i3([e,t]):"number"==typeof t||"boolean"==typeof t?i3([e,String(t)]):sn(t)?r(e,t):"object"==typeof t&&null!==t?n(e,t):sI(),r=(e,r)=>i7(r,{onEmpty:()=>i3([e,"<nil>"]),onNonEmpty:sR((r,n)=>t(st(e,mq(n)),r))}),n=(e,r)=>Object.entries(r).filter(([,e])=>ep(e)).flatMap(([r,n])=>{let i=st(e,mU(r)),s=t(i,n);return si(s)?i3([i,""]):s});return n(sI(),e)},mH=Symbol.for("effect/Console"),mK=tQ("effect/Console"),mW={[mH]:mH,assert:(e,...t)=>pH(()=>{console.assert(e,...t)}),clear:pH(()=>{console.clear()}),count:e=>pH(()=>{console.count(e)}),countReset:e=>pH(()=>{console.countReset(e)}),debug:(...e)=>pH(()=>{console.debug(...e)}),dir:(e,t)=>pH(()=>{console.dir(e,t)}),dirxml:(...e)=>pH(()=>{console.dirxml(...e)}),error:(...e)=>pH(()=>{console.error(...e)}),group:e=>e?.collapsed?pH(()=>console.groupCollapsed(e?.label)):pH(()=>console.group(e?.label)),groupEnd:pH(()=>{console.groupEnd()}),info:(...e)=>pH(()=>{console.info(...e)}),log:(...e)=>pH(()=>{console.log(...e)}),table:(e,t)=>pH(()=>{console.table(e,t)}),time:e=>pH(()=>console.time(e)),timeEnd:e=>pH(()=>console.timeEnd(e)),timeLog:(e,...t)=>pH(()=>{console.timeLog(e,...t)}),trace:(...e)=>pH(()=>{console.trace(...e)}),warn:(...e)=>pH(()=>{console.warn(...e)}),unsafe:console},mV=Symbol.for("effect/Random"),mG=tQ("effect/Random");class mY{constructor(e){this[mV]=mV,this.seed=e,this.PRNG=new ev(e)}get next(){return pH(()=>this.PRNG.number())}get nextBoolean(){return pj(this.next,e=>e>.5)}get nextInt(){return pH(()=>this.PRNG.integer(Number.MAX_SAFE_INTEGER))}nextRange(e,t){return pj(this.next,r=>(t-e)*r+e)}nextIntBetween(e,t){return pH(()=>this.PRNG.integer(t-e)+e)}shuffle(e){return mZ(e,e=>this.nextIntBetween(0,e))}}let mZ=(e,t)=>pJ(()=>W(pH(()=>Array.from(e)),pv(e=>{let r=[];for(let t=e.length;t>=2;t-=1)r.push(t);return W(r,pI(r=>W(t(r),pj(t=>mQ(e,r-1,t)))),h9(am(e)))}))),mQ=(e,t,r)=>{let n=e[t];return e[t]=e[r],e[r]=n,e},mX=Symbol.for("effect/Tracer"),m0=tQ("effect/Tracer"),m1=tQ("effect/ParentSpan"),m2=function(){let e="abcdef0123456789",t=e.length;return function(r){let n="";for(let i=0;i<r;i++)n+=e.charAt(Math.floor(Math.random()*t));return n}}();class m3{constructor(e,t,r,n,i,s){this._tag="Span",this.traceId="native",this.sampled=!0,this.events=[],this.name=e,this.parent=t,this.context=r,this.startTime=i,this.kind=s,this.status={_tag:"Started",startTime:i},this.attributes=new Map,this.traceId="Some"===t._tag?t.value.traceId:m2(32),this.spanId=m2(16),this.links=Array.from(n)}end(e,t){this.status={_tag:"Ended",endTime:e,exit:t,startTime:this.status.startTime}}attribute(e,t){this.attributes.set(e,t)}event(e,t,r){this.events.push([e,t,r??{}])}addLinks(e){this.links.push(...e)}}let m5={[mX]:mX,...{span:(e,t,r,n,i,s)=>new m3(e,t,r,n,i,s),context:e=>e()}},m4=e=>{if(e?.captureStackTrace===!1||e?.captureStackTrace!==void 0&&"boolean"!=typeof e.captureStackTrace)return e;let t=Error.stackTraceLimit;Error.stackTraceLimit=3;let r=Error();Error.stackTraceLimit=t;let n=!1;return{...e,captureStackTrace:()=>{if(!1!==n)return n;if(void 0!==r.stack){let e=r.stack.split("\n");if(void 0!==e[3])return n=e[3].trim()}}}},m6=t2()("effect/Tracer/DisablePropagation",{defaultValue:H}),m8=W(t0(),tH(dV,new dX),tH(mK,mW),tH(mG,new mY(eN(Math.random()))),tH(mm,mv()),tH(m0,m5)),m7=Y(Symbol.for("effect/DefaultServices/currentServices"),()=>fE(m8)),m9=e=>h8(t=>e(t.currentDefaultServices)),ge=e=>m9(t=>e(t.unsafeMap.get(dV.key))),gt=ge(e=>e.currentTimeMillis),gr=e=>m9(t=>e(t.unsafeMap.get(mm.key))),gn=e=>gr(t=>t.load(e)),gi=(y=e=>e.next,m9(e=>y(e.unsafeMap.get(mG.key))),Symbol.for("effect/Secret"));({...n5,[gi]:gi});let gs=Symbol.for("effect/Config"),ga={...tx,[gs]:{_A:e=>e},commit(){return gn(this)}},go=U(2,(e,t)=>gc(e,e=>nk(t(e)))),gl=U(2,(e,t)=>gc(e,e=>{try{return nk(t(e))}catch(e){return nw(a3([],e instanceof Error?e.message:`${e}`))}})),gc=U(2,(e,t)=>{let r=Object.create(ga);return r._tag=mr,r.original=e,r.mapOrFail=t,r}),gu=U(2,(e,t)=>{let r=Object.create(ga);return r._tag=mn,r.name=t,r.config=e,r}),gh=U(2,(e,t)=>{let r=Object.create(ga);return r._tag=me,r.first=e,r.second=gg(t),r.condition=J,r}),gp=U(2,(e,t)=>{let r=Object.create(ga);return r._tag=me,r.first=e,r.second=gg(t.orElse),r.condition=t.if,r}),gf=(e,t)=>{let r=Object.create(ga);return r._tag=mi,r.description=e,r.parse=t,r},gd=e=>{let t=gf("a text property",nk);return void 0===e?t:gu(t,e)},gm=e=>{let t=Object.create(ga);return t._tag=d9,t.value=e,t.parse=()=>nk(e),t},gg=e=>{let t=Object.create(ga);return t._tag=mt,t.config=e,t},gb=U(2,(e,t)=>gp(e,{orElse:()=>gm(t),if:oe})),gx=U(3,(e,t,r)=>{let n=Object.create(ga);return n._tag=ms,n.left=e,n.right=t,n.zip=r,n}),gy=(...e)=>t=>{let r=e.map(String).join(", "),n=gf(`one of (${r})`,t=>{let n=e.find(e=>String(e)===t);return void 0===n?nw(a3([],`Expected one of (${r}) but received ${t}`)):nk(n)});return void 0===t?n:gu(n,t)},gv=function(){let e=Symbol.for("effect/Data/Error/plainArgs");return({BaseEffectError:class extends fQ{constructor(t){super(t?.message,t?.cause?{cause:t.cause}:void 0),t&&(Object.assign(this,t),Object.defineProperty(this,e,{value:t,enumerable:!1}))}toJSON(){return{...this[e],...this}}}}).BaseEffectError}(),gS=e=>{let t={BaseEffectError:class extends gv{constructor(...t){super(...t),this._tag=e}}};return t.BaseEffectError.prototype.name=e,t.BaseEffectError},g_=e=>e._tag===u4,gw=e=>hj(e,{onEmpty:tR(hr),onFail:iM(hn),onDie:e=>tR(hi(e)),onInterrupt:e=>tR(hs(e)),onSequential:iB(ho),onParallel:iB(ha)}),gk=e=>fZ(q)(e),gC=e=>ec(e,f8),gE=e=>ec(e,f0),gO=e=>{let t=oc(e);return ge(e=>e.sleep(t))};function gI(){return new gT(new Map)}let gF=Symbol.for("effect/FiberRefs");class gT{constructor(e){this[gF]=gF,this.locals=e}pipe(){return e6(this,arguments)}}let gR=(e,t,r,n=!1)=>{let i,s=t,a=r,o=n;for(;void 0===i;)if(ss(s)&&ss(a)){let e=sh(s)[0],t=sd(s),r=sh(a)[0],n=sh(a)[1],l=sd(a);e.startTimeMillis<r.startTimeMillis?(a=l,o=!0):e.startTimeMillis>r.startTimeMillis?s=t:e.id<r.id?(a=l,o=!0):e.id>r.id?s=t:i=[n,o]}else i=[e.initial,!0];return i},gN=U(3,(e,t,r)=>{let n=new Map(e.locals);return r.locals.forEach((e,r)=>{let i=e[0][1];if(!e[0][0][eB](t)){if(!n.has(r)){if(eJ(i,r.initial))return;n.set(r,[[t,r.join(r.initial,i)]]);return}let s=n.get(r),[a,o]=gR(r,s,e);if(o){let e=r.diff(a,i),o=s[0][1],l=r.join(o,r.patch(e)(o));if(!eJ(o,l)){let e,i=s[0][0];e=i[eB](t)?[[i,l],...s.slice(1)]:[[t,l],...s],n.set(r,e)}}}}),new gT(n)}),gA=U(2,(e,t)=>{let r=new Map;return gj(e,r,t),new gT(r)}),gj=(e,t,r)=>{e.locals.forEach((e,n)=>{let i=e[0][1],s=n.patch(n.fork)(i);eJ(i,s)?t.set(n,e):t.set(n,[[r,s],...e])})},gM=U(2,(e,t)=>{let r=new Map(e.locals);return r.delete(t),new gT(r)}),gz=U(2,(e,t)=>e.locals.has(t)?tR(sh(e.locals.get(t))[1]):iC()),gD=U(2,(e,t)=>W(gz(e,t),iO(()=>t.initial))),g$=U(2,(e,{fiberId:t,fiberRef:r,value:n})=>{if(0===e.locals.size)return new gT(new Map([[r,[[t,n]]]]));let i=new Map(e.locals);return gP(i,t,r,n),new gT(i)}),gP=(e,t,r,n)=>{let i,s=e.get(r)??[];if(ss(s)){let[e,r]=sh(s);if(e[eB](t))if(eJ(r,n))return;else i=[[t,n],...s.slice(1)];else i=[[t,n],...s]}else i=[[t,n]];e.set(r,i)},gL=U(2,(e,{entries:t,forkAs:r})=>{if(0===e.locals.size)return new gT(new Map(t));let n=new Map(e.locals);return void 0!==r&&gj(e,n,r),t.forEach(([e,t])=>{1===t.length?gP(n,t[0][0],e,t[0][1]):t.forEach(([t,r])=>{gP(n,t,e,r)})}),new gT(n)}),gU=(I=W(iW,iV(e=>e.ordinal)),U(2,(e,t)=>1===I(e,t))),gq=e=>{switch(e){case"All":return ft;case"Debug":return fa;case"Error":return fn;case"Fatal":return fr;case"Info":return fs;case"Trace":return fo;case"None":return fl;case"Warning":return fi}},gB=e=>e.replace(/[\s="]/g,"_"),gJ=e=>t=>{let r=gB(t.label);return`${r}=${e-t.startTime}ms`},gH=(e,t)=>({label:e,startTime:t}),gK=Symbol.for("effect/Readable"),gW=Symbol.for("effect/Ref"),gV={_A:e=>e};class gG extends t4{static{i=gK}commit(){return this.get}constructor(e){super(),this[gW]=gV,this[i]=gK,this.ref=e,this.get=pH(()=>lq(this.ref))}modify(e){return pH(()=>{let t=lq(this.ref),[r,n]=e(t);return t!==n&&lB(n)(this.ref),r})}}let gY=e=>new gG(lL(e)),gZ=e=>pH(()=>gY(e)),gQ=e=>e.get,gX=U(2,(e,t)=>e.modify(()=>[void 0,t])),g0=U(2,(e,t)=>e.modify(e=>[e,t])),g1=U(2,(e,t)=>e.modify(t)),g2=U(2,(e,t)=>e.modify(e=>[void 0,t(e)])),g3=e=>({_tag:"ExternalSpan",spanId:e.spanId,traceId:e.traceId,sampled:e.sampled??!0,context:e.context??t0()}),g5="Empty",g4="Remove",g6="Update",g8="AndThen",g7={_tag:g5},g9=(e,t)=>{let r=new Map(e.locals),n=g7;for(let[e,i]of t.locals.entries()){let t=sh(i)[1],s=r.get(e);if(void 0!==s){let r=sh(s)[1];eJ(r,t)||(n=be({_tag:g6,fiberRef:e,patch:e.diff(r,t)})(n))}else n=be({_tag:"Add",fiberRef:e,value:t})(n);r.delete(e)}for(let[e]of r.entries())n=be({_tag:g4,fiberRef:e})(n);return n},be=U(2,(e,t)=>({_tag:g8,first:e,second:t})),bt=U(3,(e,t,r)=>{let n=r,i=sF(e);for(;ss(i);){let e=sh(i),r=sd(i);switch(e._tag){case g5:i=r;break;case"Add":n=g$(n,{fiberId:t,fiberRef:e.fiberRef,value:e.value}),i=r;break;case g4:n=gM(n,e.fiberRef),i=r;break;case g6:{let s=gD(n,e.fiberRef);n=g$(n,{fiberId:t,fiberRef:e.fiberRef,value:e.fiberRef.patch(e.patch)(s)}),i=r;break}case g8:i=se(e.first)(se(e.second)(r))}}return n}),br="effect/MetricLabel",bn=Symbol.for(br);class bi{constructor(e,t){this[bn]=bn,this.key=e,this.value=t,this._hash=e$(br+this.key+this.value)}[eR](){return this._hash}[eB](e){return ba(e)&&this.key===e.key&&this.value===e.value}pipe(){return e6(this,arguments)}}let bs=(e,t)=>new bi(e,t),ba=e=>ec(e,bn),bo=U(e=>h6(e[0]),function(){let e=arguments;return f_(e[0],fR,"string"==typeof e[1]?li(e[1],e[2]):t=>Object.entries(e[1]).reduce((e,[t,r])=>li(e,t,r),t))}),bl=e=>pj(e,tR),bc=e=>{let t,r;return"function"==typeof e?t=e:(t=e.try,r=e.catch),pJ(()=>{try{return pB(eO(t))}catch(e){return pd(r?eO(()=>r(e)):new di(e,"An unknown error occurred in Effect.try"))}})},bu=U(2,(e,t)=>pC(e,{onFailure:e=>{let r=t(e);switch(r._tag){case"None":return pg(e);case"Some":return r.value}},onSuccess:pB})),bh=U(e=>h6(e[0]),(e,...t)=>{let r,n=t[t.length-1];return po(e,2===t.length?eu(t[0]):e=>{let r=ec(e,"_tag")?e._tag:void 0;if(!r)return!1;for(let e=0;e<t.length-1;e++)if(t[e]===r)return!0;return!1},n)}),bp=U(2,(e,t)=>{let r;return po(e,e=>(r??=Object.keys(t),ec(e,"_tag")&&Z(e._tag)&&r.includes(e._tag)),e=>t[e._tag](e))}),bf=e=>bD(e,bS,g9),bd=e=>bD(e,p5(bS,pq),([e,t],[r,n])=>[g9(e,r),uX(t,n)]),bm=U(3,(e,t,r)=>pv(e,e=>pj(r(e),r=>({...e,[t]:r})))),bg=U(2,(e,t)=>pj(e,e=>({[t]:e}))),bb=((e,t)=>pJ(()=>{let r,n=e[Symbol.iterator](),i=[],s=pB(!1),a=0;for(;(r=n.next())&&!r.done;){let e=r.value,n=a++;s=pv(s,r=>r?(i.push(e),pB(!0)):t(e,n))}return pj(s,()=>i)}),U(3,(e,t,r)=>pv(e,e=>t(e)?pB(e):r(e)))),bx=((e=>h6(e[0]),(e,t,r)=>bb(e,t,e=>void 0===r?pd(new f7):pm(()=>r(e)))),(e,t,r,n)=>pv(r(n,t),i=>{if(i)return pB(tR(n));let s=e.next();return s.done?pB(iC()):bx(e,t+1,r,s.value)})),by=U(2,(e,t)=>pE(e,{onFailure:e=>pB(t.onFailure(e)),onSuccess:e=>pB(t.onSuccess(e))})),bv=e=>{let t=pv(pv(e,()=>p3()),()=>t);return t},bS=h8(e=>pB(e.getFiberRefs())),b_=e=>(...t)=>{let r,n=iT(e);for(let e=0,n=t.length;e<n;e++){let n=t[e];hl(n)&&(r=void 0!==r?ho(r,n):n,t=[...t.slice(0,e),...t.slice(e+1)],e--)}return void 0===r&&(r=hr),h8(e=>(e.log(t,r,n),pZ))},bw=b_(),bk=b_(fa),bC=b_(fs),bE=b_(fi),bO=b_(fn),bI=U(2,(e,t)=>pv(gt,r=>f_(e,fA,cv(gH(t,r))))),bF=U(2,(e,t)=>pC(e,{onFailure:e=>pb(()=>t(e)),onSuccess:pB})),bT=e=>pj(e,e=>!e),bR=e=>pj(gZ(!0),t=>pe(pX(e,g0(t,!1)))),bN=e=>bB((t,r)=>W(e,bt(t,r))),bA=U(3,(e,t,r)=>dL(n=>dU(e,tH(n,t,r)))),bj=U(3,(e,t,r)=>dL(n=>pv(r,r=>dU(e,W(n,tH(t,r)))))),bM=U(3,(e,t,r)=>i6(e).reduce((e,t,n)=>pv(e,e=>r(e,t,n)),pB(t))),bz=pB(iC()),bD=U(3,(e,t,r)=>pv(t,n=>pv(e,e=>pj(t,t=>[r(n,t),e]))));(e,t)=>pJ(()=>{let r,n=e[Symbol.iterator](),i=[],s=pB(!0),a=0;for(;(r=n.next())&&!r.done;){let e=r.value,n=a++;s=pv(s,r=>W(r?t(e,n):pB(!1),pj(t=>(t&&i.push(e),t))))}return pj(s,()=>i)});let b$=U(2,(e,{onFailure:t,onSuccess:r})=>pC(e,{onFailure:e=>{let r=hx(e);switch(r._tag){case"Left":return p6(t(r.left),pg(e));case"Right":return pg(e)}},onSuccess:e=>h9(r(e),e)})),bP=U(2,(e,t)=>pC(e,{onFailure:e=>{let r=hx(e);switch(r._tag){case"Left":return p6(t(r.left),pg(e));case"Right":return pg(e)}},onSuccess:pB})),bL=U(2,(e,t)=>pC(e,{onFailure:e=>p6(t(e),pg(e)),onSuccess:pB})),bU=e=>{let t,r;"function"==typeof e?t=e:(t=e.try,r=e.catch);let n=e=>r?pm(()=>r(e)):pd(new di(e,"An unknown error occurred in Effect.tryPromise"));return t.length>=1?pi((e,r)=>{try{t(r).then(t=>e(dS(t)),t=>e(n(t)))}catch(t){e(n(t))}}):pi(e=>{try{t().then(t=>e(dS(t)),t=>e(n(t)))}catch(t){e(n(t))}})},bq=U(2,(e,t)=>pv(e,e=>bc({try:()=>t.try(e),catch:t.catch}))),bB=e=>h8(t=>(t.setFiberRefs(e(t.id(),t.getFiberRefs())),pZ)),bJ=U(2,(e,t)=>pJ(()=>t()?pj(e,tR):pB(iC()))),bH=BigInt(0),bK=iz(e=>tG(e.context,m6)?"Span"===e._tag?bK(e.parent):iC():tR(e)),bW=(e,t,r)=>{let n,i=!e.getFiberRef(fU)||r.context&&tG(r.context,m6),s=e.getFiberRef(fI),a=r.parent?tR(r.parent):r.root?iC():bK(tY(s,m1));if(i)n=dK({name:t,parent:a,context:tH(r.context??t0(),m6,!0)});else{let i=e.getFiberRef(m7),s=tG(i,m0),o=tG(i,dV),l=e.getFiberRef(fq),c=e.getFiberRefs(),u=gz(c,fB),h=gz(c,fJ),p="Some"===h._tag?void 0!==r.links?[...ab(h.value),...r.links??[]]:ab(h.value):r.links??sI();n=s.span(t,a,r.context??t0(),p,l?o.unsafeCurrentTimeNanos():bH,r.kind??"internal"),"Some"===u._tag&&ld(u.value,(e,t)=>n.attribute(t,e)),void 0!==r.attributes&&Object.entries(r.attributes).forEach(([e,t])=>n.attribute(e,t))}return"function"==typeof r.captureStackTrace&&hq.set(n,r.captureStackTrace),n},bV=(e,t,r,n)=>pH(()=>{"Ended"!==e.status._tag&&(da(t)&&hq.has(e)&&e.attribute("code.stacktrace",hq.get(e)()),e.end(n?r.unsafeCurrentTimeNanos():bH,t))}),bG=(e,...t)=>{let r=m4(1===t.length?void 0:t[0]),n=t[t.length-1];return h8(t=>{let i=bW(t,e,r),s=t.getFiberRef(fq),a=tG(t.getFiberRef(m7),dV);return pD(n(i),e=>bV(i,e,a,s))})},bY=U(2,(e,t)=>bA(e,m1,t)),bZ=Symbol.for("effect/DateTime"),bQ=Symbol.for("effect/DateTime/TimeZone"),bX={[bZ]:bZ,pipe(){return e6(this,arguments)},[eV](){return this.toString()},toJSON(){return xm(this).toJSON()}},b0={...bX,_tag:"Utc",[eR](){return eq(this,eD(this.epochMillis))},[eB](e){return b6(e)&&"Utc"===e._tag&&this.epochMillis===e.epochMillis},toString(){return`DateTime.Utc(${xm(this).toJSON()})`}},b1={...bX,_tag:"Zoned",[eR](){return W(eD(this.epochMillis),ej(eN(this.zone)),eq(this))},[eB](e){return b6(e)&&"Zoned"===e._tag&&this.epochMillis===e.epochMillis&&eJ(this.zone,e.zone)},toString(){return`DateTime.Zoned(${xj(this)})`}},b2={[bQ]:bQ,[eV](){return this.toString()}},b3={...b2,_tag:"Named",[eR](){return eq(this,e$(`Named:${this.id}`))},[eB](e){return b8(e)&&"Named"===e._tag&&this.id===e.id},toString(){return`TimeZone.Named(${this.id})`},toJSON(){return{_id:"TimeZone",_tag:"Named",id:this.id}}},b5={...b2,_tag:"Offset",[eR](){return eq(this,e$(`Offset:${this.offset}`))},[eB](e){return b8(e)&&"Offset"===e._tag&&this.offset===e.offset},toString(){return`TimeZone.Offset(${xx(this.offset)})`},toJSON(){return{_id:"TimeZone",_tag:"Offset",offset:this.offset}}},b4=(e,t,r)=>{let n=Object.create(b1);return n.epochMillis=e,n.zone=t,Object.defineProperty(n,"partsUtc",{value:r,enumerable:!1,writable:!0}),Object.defineProperty(n,"adjustedEpochMillis",{value:void 0,enumerable:!1,writable:!0}),Object.defineProperty(n,"partsAdjusted",{value:void 0,enumerable:!1,writable:!0}),n},b6=e=>ec(e,bZ),b8=e=>ec(e,bQ),b7=e=>"Zoned"===e._tag,b9=nY((e,t)=>e.epochMillis===t.epochMillis),xe=e=>{let t=Object.create(b0);return t.epochMillis=e,Object.defineProperty(t,"partsUtc",{value:void 0,enumerable:!1,writable:!0}),t},xt=e=>{let t=e.getTime();if(Number.isNaN(t))throw new f6("Invalid date");return xe(t)},xr=e=>{if(b6(e))return e;if(e instanceof Date)return xt(e);if("object"==typeof e){let t=new Date(0);return xw(t,e),xt(t)}return"string"!=typeof e||xn(e)?xt(new Date(e)):xt(new Date(e+"Z"))},xn=e=>/Z|[+-]\d{2}$|[+-]\d{2}:\d{2}$|\]$/.test(e),xi=(e,t)=>{let r;if(t?.timeZone===void 0&&b6(e)&&b7(e))return e;let n=xr(e);if(n.epochMillis<-86399999568e5||n.epochMillis>86399999496e5)throw new f6(`Epoch millis out of range: ${n.epochMillis}`);if(t?.timeZone===void 0)r=xh(-60*new Date(n.epochMillis).getTimezoneOffset()*1e3);else if(b8(t?.timeZone))r=t.timeZone;else if("number"==typeof t?.timeZone)r=xh(t.timeZone);else{let e=xd(t.timeZone);if(tI(e))throw new f6(`Invalid time zone: ${t.timeZone}`);r=e.value}return t?.adjustForTimeZone!==!0?b4(n.epochMillis,r,n.partsUtc):xk(n.epochMillis,r)},xs=iN(xi),xa=/^(.{17,35})\[(.+)\]$/,xo=((e=>b6(e[0]),(e,t,r)=>r?.adjustForTimeZone===!0?xk(e.epochMillis,t):b4(e.epochMillis,t,e.partsUtc)),Y("effect/DateTime/validZoneCache",()=>new Map)),xl={day:"numeric",month:"numeric",year:"numeric",hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"longOffset",fractionalSecondDigits:3,hourCycle:"h23"},xc=e=>{let t=e.resolvedOptions().timeZone;if(xo.has(t))return xo.get(t);let r=Object.create(b3);return r.id=t,r.format=e,xo.set(t,r),r},xu=e=>{if(xo.has(e))return xo.get(e);try{return xc(new Intl.DateTimeFormat("en-US",{...xl,timeZone:e}))}catch{throw new f6(`Invalid time zone: ${e}`)}},xh=e=>{let t=Object.create(b5);return t.offset=e,t},xp=iN(xu),xf=/^(?:GMT|[+-])/,xd=e=>{if(xf.test(e)){let t=xE(e);return null===t?iC():tR(xh(t))}return xp(e)},xm=((e,t)=>xv(t)-xv(e),e=>new Date(e.epochMillis)),xg=e=>{if("Utc"===e._tag)return new Date(e.epochMillis);if("Offset"===e.zone._tag)return new Date(e.epochMillis+e.zone.offset);if(void 0!==e.adjustedEpochMillis)return new Date(e.adjustedEpochMillis);let t=e.zone.format.formatToParts(e.epochMillis).filter(e=>"literal"!==e.type),r=new Date(0);return r.setUTCFullYear(Number(t[2].value),Number(t[0].value)-1,Number(t[1].value)),r.setUTCHours(Number(t[3].value),Number(t[4].value),Number(t[5].value),Number(t[6].value)),e.adjustedEpochMillis=r.getTime(),r},xb=e=>xg(e).getTime()-xv(e),xx=e=>{let t=Math.abs(e),r=Math.floor(t/36e5),n=Math.round(t%36e5/6e4);return 60===n&&(r+=1,n=0),`${e<0?"-":"+"}${String(r).padStart(2,"0")}:${String(n).padStart(2,"0")}`},xy=e=>xx(xb(e)),xv=e=>e.epochMillis,xS=e=>({millis:e.getUTCMilliseconds(),seconds:e.getUTCSeconds(),minutes:e.getUTCMinutes(),hours:e.getUTCHours(),day:e.getUTCDate(),weekDay:e.getUTCDay(),month:e.getUTCMonth()+1,year:e.getUTCFullYear()}),x_=e=>(void 0!==e.partsUtc||(e.partsUtc=xR(e,xS)),e.partsUtc),xw=(e,t)=>{if(void 0!==t.year&&e.setUTCFullYear(t.year),void 0!==t.month&&e.setUTCMonth(t.month-1),void 0!==t.day&&e.setUTCDate(t.day),void 0!==t.weekDay){let r=t.weekDay-e.getUTCDay();e.setUTCDate(e.getUTCDate()+r)}void 0!==t.hours&&e.setUTCHours(t.hours),void 0!==t.minutes&&e.setUTCMinutes(t.minutes),void 0!==t.seconds&&e.setUTCSeconds(t.seconds),void 0!==t.millis&&e.setUTCMilliseconds(t.millis)},xk=(e,t)=>{let r="Offset"===t._tag?t.offset:xO(e,t);return b4(e-r,t)},xC=/([+-])(\d{2}):(\d{2})$/,xE=e=>{let t=xC.exec(e);if(null===t)return null;let[,r,n,i]=t;return("+"===r?1:-1)*(60*Number(n)+Number(i))*6e4},xO=(e,t)=>{let r=t.format.formatToParts(e).find(e=>"timeZoneName"===e.type)?.value??"";if("GMT"===r)return 0;let n=xE(r);return null===n?xb(b4(e,t)):n},xI=U(2,(e,t)=>{if("Utc"===e._tag){let r=xm(e);return t(r),xe(r.getTime())}let r=new Date(xg(e).getTime());return t(r),xk(r.getTime(),e.zone)}),xF=((e,t)=>xF(e,e=>{let r=new Date(e);return t(r),r.getTime()}),U(2,(e,t)=>{let r=t(xv(e));return"Utc"===e._tag?xe(r):b4(r,e.zone)})),xT=U(2,(e,t)=>t(xg(e))),xR=U(2,(e,t)=>t(xm(e))),xN=(e,t)=>{e.setTime(e.getTime()+t)},xA=((e,t)=>xI(e,e=>{if(t.millis&&xN(e,t.millis),t.seconds&&xN(e,1e3*t.seconds),t.minutes&&xN(e,60*t.minutes*1e3),t.hours&&xN(e,60*t.hours*6e4),t.days&&e.setUTCDate(e.getUTCDate()+t.days),t.weeks&&e.setUTCDate(e.getUTCDate()+7*t.weeks),t.months){let r=e.getUTCDate();e.setUTCMonth(e.getUTCMonth()+t.months+1,0),r<e.getUTCDate()&&e.setUTCDate(r)}if(t.years){let r=e.getUTCDate(),n=e.getUTCMonth();e.setUTCFullYear(e.getUTCFullYear()+t.years,n+1,0),r<e.getUTCDate()&&e.setUTCDate(r)}}),e=>{let t=xg(e);return"Utc"===e._tag?t.toISOString():`${t.toISOString().slice(0,-1)}${xy(e)}`}),xj=e=>"Offset"===e.zone._tag?xA(e):`${xA(e)}[${e.zone.id}]`,xM=b6,xz=e=>b8(e)&&"Offset"===e._tag,xD=e=>b8(e)&&"Named"===e._tag,x$=e=>"Utc"===e._tag,xP=b7,xL=b9,xU=xt,xq=xi,xB=e=>pv(px,t=>dR(e,pR(t))),xJ=e=>pH(()=>lq(e.state)._tag===hW),xH="Sequential",xK="Parallel",xW={_tag:xH},xV=e=>e._tag===xH,xG=e=>e._tag===xK,xY={_tag:xK},xZ=e=>({_tag:"ParallelN",parallelism:e}),xQ="effect/FiberStatus",xX=Symbol.for(xQ),x0="Done",x1="Running",x2="Suspended",x3=e$(`${xQ}-${x0}`);class x5{[eR](){return x3}[eB](e){return x7(e)&&e._tag===x0}constructor(){this[xX]=xX,this._tag=x0}}class x4{constructor(e){this[xX]=xX,this._tag=x1,this.runtimeFlags=e}[eR](){return W(eN(xQ),ej(eN(this._tag)),ej(eN(this.runtimeFlags)),eq(this))}[eB](e){return x7(e)&&e._tag===x1&&this.runtimeFlags===e.runtimeFlags}}class x6{constructor(e,t){this[xX]=xX,this._tag=x2,this.runtimeFlags=e,this.blockingOn=t}[eR](){return W(eN(xQ),ej(eN(this._tag)),ej(eN(this.runtimeFlags)),ej(eN(this.blockingOn)),eq(this))}[eB](e){return x7(e)&&e._tag===x2&&this.runtimeFlags===e.runtimeFlags&&eJ(this.blockingOn,e.blockingOn)}}let x8=new x5,x7=e=>ec(e,xX),x9=e=>new x4(e),ye=(e,t)=>new x6(e,t),yt=e=>e._tag===x0;class yr{scheduleTask(e,t){let r,n=this.buckets.length,i=0;for(;i<n;i++)if(this.buckets[i][0]<=t)r=this.buckets[i];else break;r&&r[0]===t?r[1].push(e):i===n?this.buckets.push([t,[e]]):this.buckets.splice(i,0,[t,[e]])}constructor(){this.buckets=[]}}class yn{constructor(e){this.running=!1,this.tasks=new yr,this.maxNextTickBeforeTimer=e}starveInternal(e){let t=this.tasks.buckets;for(let[e,r]of(this.tasks.buckets=[],t))for(let e=0;e<r.length;e++)r[e]();0===this.tasks.buckets.length?this.running=!1:this.starve(e)}starve(e=0){e>=this.maxNextTickBeforeTimer?setTimeout(()=>this.starveInternal(0),0):Promise.resolve(void 0).then(()=>this.starveInternal(e+1))}shouldYield(e){return e.currentOpCount>e.getFiberRef(fT)&&e.getFiberRef(fF)}scheduleTask(e,t){this.tasks.scheduleTask(e,t),this.running||(this.running=!0,this.starve())}}let yi=Y(Symbol.for("effect/Scheduler/defaultScheduler"),()=>new yn(2048));class ys{scheduleTask(e,t){this.deferred?yi.scheduleTask(e,t):this.tasks.scheduleTask(e,t)}shouldYield(e){return e.currentOpCount>e.getFiberRef(fT)&&e.getFiberRef(fF)}flush(){for(;this.tasks.buckets.length>0;){let e=this.tasks.buckets;for(let[t,r]of(this.tasks.buckets=[],e))for(let e=0;e<r.length;e++)r[e]()}this.deferred=!0}constructor(){this.tasks=new yr,this.deferred=!1}}let ya=Y(Symbol.for("effect/FiberRef/currentScheduler"),()=>fw(yi)),yo=(e,t)=>({_tag:"Par",left:e,right:t}),yl=(e,t)=>({_tag:"Seq",left:e,right:t}),yc=e=>{let t=cx(e),r=cb();for(;;){let[e,n]=c_(t,[yx(),cb()],([e,t],r)=>{let[n,i]=yu(r);return[yv(e,n),cy(t,i)]});if(r=yh(r,e),cd(n))return cw(r);t=n}throw Error("BUG: BlockedRequests.flatten - please report an issue at https://github.com/Effect-TS/effect/issues")},yu=e=>{let t=e,r=yx(),n=cb(),i=cb();for(;;)switch(t._tag){case"Empty":if(cd(n))return[r,i];t=n.head,n=n.tail;break;case"Par":n=cg(t.right,n),t=t.left;break;case"Seq":{let e=t.left,r=t.right;switch(e._tag){case"Empty":t=r;break;case"Par":{let n=e.left,i=e.right;t=yo(yl(n,r),yl(i,r));break}case"Seq":t=yl(e.left,yl(e.right,r));break;case"Single":t=e,i=cg(r,i)}break}case"Single":if(r=yy(r,t),cd(n))return[r,i];t=n.head,n=n.tail}throw Error("BUG: BlockedRequests.step - please report an issue at https://github.com/Effect-TS/effect/issues")},yh=(e,t)=>{if(cd(e))return cx(yw(t));if(yS(t))return e;let r=yF(e.head),n=y_(t);return 1===r.length&&1===n.length&&eJ(r[0],n[0])?cg(yI(e.head,yw(t)),e.tail):cg(yw(t),e)},yp=Symbol.for("effect/RequestBlock/Entry");class yf{constructor(e,t,r,n,i){this[yp]=yd,this.request=e,this.result=t,this.listeners=r,this.ownerId=n,this.state=i}}let yd={_R:e=>e},ym=Symbol.for("effect/RequestBlock/RequestBlockParallel"),yg={_R:e=>e};class yb{constructor(e){this[ym]=yg,this.map=e}}let yx=()=>new yb(o9()),yy=(e,t)=>new yb(lu(e.map,t.dataSource,e=>iF(iM(e,aw(t.blockedRequest)),()=>ad(t.blockedRequest)))),yv=(e,t)=>new yb(lm(e.map,t.map,(e,t,r)=>li(e,r,iE(lt(e,r),{onNone:()=>t,onSome:e=>aI(t,e)})))),yS=e=>cs(e.map),y_=e=>Array.from(la(e.map)),yw=e=>yO(lf(e.map,e=>ad(e))),yk=Symbol.for("effect/RequestBlock/RequestBlockSequential"),yC={_R:e=>e};class yE{constructor(e){this[yk]=yC,this.map=e}}let yO=e=>new yE(e),yI=(e,t)=>new yE(lm(t.map,e.map,(e,t,r)=>li(e,r,iE(lt(e,r),{onNone:()=>ap(),onSome:e=>aI(e,t)})))),yF=e=>Array.from(la(e.map)),yT=e=>Array.from(e.map),yR=Y(Symbol.for("effect/FiberRef/currentRequestMap"),()=>fw(new Map)),yN=(e,t,r,n)=>{switch(e){case void 0:return t();case"unbounded":return r();case"inherit":return ff(fM,e=>"unbounded"===e?r():e>1?n(e):t());default:return e>1?n(e):t()}},yA=(e,t,r)=>{switch(e){case void 0:return t();case"unbounded":return r();case"inherit":return ff(fM,e=>"unbounded"===e||e>1?r():t());default:return e>1?r():t()}},yj=e=>{switch(e._tag){case e9:return hp(e.effect_instruction_i0);case tn:return!1}},yM=e=>W(e,dm(q)),yz="InterruptSignal",yD="Stateful",y$="Resume",yP="YieldNow",yL=e=>({_tag:yz,cause:e}),yU=e=>({_tag:yD,onFiber:e}),yq=e=>({_tag:y$,effect:e}),yB=()=>({_tag:yP}),yJ=Symbol.for("effect/FiberScope");class yH{add(e,t){this.roots.add(t),t.addObserver(()=>{this.roots.delete(t)})}constructor(){this[yJ]=yJ,this.fiberId=l7,this.roots=new Set}}class yK{constructor(e,t){this[yJ]=yJ,this.fiberId=e,this.parent=t}add(e,t){this.parent.tell(yU(e=>{e.addChild(t),t.addObserver(()=>{e.removeChild(t)})}))}}let yW=e=>new yK(e.id(),e),yV=Y(Symbol.for("effect/FiberScope/Global"),()=>new yH),yG=Symbol.for("effect/Fiber"),yY={_E:e=>e,_A:e=>e},yZ={[yG]:yY,pipe(){return e6(this,arguments)}},yQ=Symbol.for("effect/Fiber"),yX=e=>e.await,y0=e=>({...tx,commit(){return y1(this)},...yZ,id:()=>l7,await:pB(e),children:pB([]),inheritAll:pZ,poll:pB(tR(e)),interruptAsFork:()=>pZ}),y1=((e,t)=>e.interruptAsFork(t),e=>p4(pw(e.await),e.inheritAll)),y2=((e,t)=>y2(e,e=>pH(()=>t(e))),U(2,(e,t)=>({...tx,commit(){return y1(this)},...yZ,id:()=>e.id(),await:pv(e.await,dg(t)),children:e.children,inheritAll:e.inheritAll,poll:pv(e.poll,e=>{switch(e._tag){case"None":return pB(iC());case"Some":return W(dg(e.value,t),pj(tR))}}),interruptAsFork:t=>e.interruptAsFork(t)})));({...tx,commit(){return y1(this)},...yZ,id:()=>l7,await:p7,children:pB([]),inheritAll:p7,poll:pB(iC()),interruptAsFork:()=>p7}),(e,t)=>({...tx,commit(){return y1(this)},...yZ,id:()=>l3(e.id(),t.id()),await:p8(e.await,t.await,(e,t)=>dl(e)?e:t),children:e.children,inheritAll:p6(t.inheritAll,e.inheritAll),poll:p8(e.poll,t.poll,(e,t)=>{switch(e._tag){case"None":return iC();case"Some":return dl(e.value)?e:t}}),interruptAsFork:r=>W(fe(e,r),p6(W(t,fe(r))),pe)});let y3="effect/FiberCurrent",y5=Symbol.for("effect/Logger"),y4={_Message:e=>e,_Output:e=>e},y6=e=>({[y5]:y4,log:e,pipe(){return e6(this,arguments)}}),y8=U(2,(e,t)=>y6(r=>e.log({...r,message:t(r.message)}))),y7=U(2,(e,t)=>y6(r=>e.log(t(r)))),y9=U(2,(e,t)=>y6(r=>t(r.logLevel)?tR(e.log(r)):iC())),ve=U(2,(e,t)=>y6(r=>t(e.log(r)))),vt=e=>({[y5]:y4,log:({message:t})=>e(t),pipe(){return e6(this,arguments)}}),vr=U(2,(e,t)=>y6(r=>[e.log(r),t.log(r)])),vn=U(2,(e,t)=>ve(vr(e,t),e=>e[0])),vi=U(2,(e,t)=>ve(vr(e,t),e=>e[1])),vs=/^[^\s"=]*$/,va=(e,t)=>({annotations:r,cause:n,date:i,fiberId:s,logLevel:a,message:o,spans:l})=>{let c=t=>t.match(vs)?t:e(t),u=(e,t)=>`${gB(e)}=${c(t)}`,h=(e,t)=>" "+u(e,t),p=u("timestamp",i.toISOString());p+=h("level",a.label),p+=h("fiber",l6(s));let f=i8(o);for(let e=0;e<f.length;e++)p+=h("message",eX(f[e],t));for(let e of(hc(n)||(p+=h("cause",hD(n,{renderErrorCause:!0}))),l))p+=" "+gJ(i.getTime())(e);for(let[e,n]of r)p+=h(e,eX(n,t));return p},vo=y6(va(e=>`"${e.replace(/\\([\s\S])|(")/g,"\\$1$2")}"`)),vl=y6(va(JSON.stringify,0)),vc=y6(({annotations:e,cause:t,date:r,fiberId:n,logLevel:i,message:s,spans:a})=>{let o=r.getTime(),l={},c={};if(lo(e)>0)for(let[t,r]of e)l[t]=vu(r);if(cm(a))for(let e of a)c[e.label]=o-e.startTime;let u=i8(s);return{message:1===u.length?vu(u[0]):u.map(vu),logLevel:i.label,timestamp:r.toISOString(),cause:hh(t)?void 0:hD(t,{renderErrorCause:!0}),annotations:l,spans:c,fiberId:l6(n)}}),vu=e=>{switch(typeof e){case"bigint":case"function":case"symbol":return String(e);default:return eG(e)}},vh=ve(vc,e0),vp=(e,...t)=>{let r="";for(let e=0;e<t.length;e++)r+=`\x1b[${t[e]}m`;return r+e+"\x1b[0m"},vf=(e,...t)=>e,vd={bold:"1",red:"31",green:"32",yellow:"33",blue:"34",cyan:"36",white:"37",gray:"90",black:"30",bgBrightRed:"101"},vm={None:[],All:[],Trace:[vd.gray],Debug:[vd.blue],Info:[vd.green],Warning:[vd.yellow],Error:[vd.red],Fatal:[vd.bgBrightRed,vd.black]},vg={None:"",All:"",Trace:"color:gray",Debug:"color:blue",Info:"color:green",Warning:"color:orange",Error:"color:red",Fatal:"background-color:red;color:white"},vb=e=>`${e.getHours().toString().padStart(2,"0")}:${e.getMinutes().toString().padStart(2,"0")}:${e.getSeconds().toString().padStart(2,"0")}.${e.getMilliseconds().toString().padStart(3,"0")}`,vx="object"==typeof process&&null!==process&&"object"==typeof process.stdout&&null!==process.stdout,vy=vx&&!0===process.stdout.isTTY,vv=vx||"Deno"in globalThis,vS=e=>{let t=e?.mode??"auto",r="browser"===("auto"===t?vv?"tty":"browser":t),n="boolean"==typeof e?.colors?e.colors:vy||r,i=e?.formatDate??vb;return r?vw({colors:n,formatDate:i}):v_({colors:n,formatDate:i,stderr:e?.stderr===!0})},v_=e=>{let t="object"==typeof process&&"isBun"in process&&!0===process.isBun,r=e.colors?vp:vf;return y6(({annotations:n,cause:i,context:s,date:a,fiberId:o,logLevel:l,message:c,spans:u})=>{let h=tG(gD(s,m7),mK).unsafe,p=!0===e.stderr?h.error:h.log,f=i8(c),d=r(`[${e.formatDate(a)}]`,vd.white)+` ${r(l.label,...vm[l._tag])}`+` (${l6(o)})`;if(cm(u)){let e=gJ(a.getTime());for(let t of u)d+=" "+e(t)}d+=":";let m=0;if(f.length>0){let e=vu(f[0]);"string"==typeof e&&(d+=" "+r(e,vd.bold,vd.cyan),m++)}if(p(d),t||h.group(),hh(i)||p(hD(i,{renderErrorCause:!0})),m<f.length)for(;m<f.length;m++)p(e4(f[m]));if(lo(n)>0)for(let[e,t]of n)p(r(`${e}:`,vd.bold,vd.white),e4(t));t||h.groupEnd()})},vw=e=>{let t=e.colors?"%c":"";return y6(({annotations:r,cause:n,context:i,date:s,fiberId:a,logLevel:o,message:l,spans:c})=>{let u=tG(gD(i,m7),mK).unsafe,h=i8(l),p=`${t}[${e.formatDate(s)}]`,f=[];if(e.colors&&f.push("color:gray"),p+=` ${t}${o.label}${t} (${l6(a)})`,e.colors&&f.push(vg[o._tag],""),cm(c)){let e=gJ(s.getTime());for(let t of c)p+=" "+e(t)}p+=":";let d=0;if(h.length>0){let r=vu(h[0]);"string"==typeof r&&(p+=` ${t}${r}`,e.colors&&f.push("color:deepskyblue"),d++)}if(u.groupCollapsed(p,...f),hh(n)||u.error(hD(n,{renderErrorCause:!0})),d<h.length)for(;d<h.length;d++)u.log(e4(h[d]));if(lo(r)>0)for(let[t,n]of r){let r=e4(n);e.colors?u.log(`%c${t}:`,"color:gray",r):u.log(`${t}:`,r)}u.groupEnd()})},vk=Y("effect/Logger/prettyLoggerDefault",()=>vS()),vC=Symbol.for("effect/MetricKeyType"),vE="effect/MetricKeyType/Counter",vO=Symbol.for(vE),vI="effect/MetricKeyType/Frequency",vF=Symbol.for(vI),vT="effect/MetricKeyType/Gauge",vR=Symbol.for(vT),vN="effect/MetricKeyType/Histogram",vA=Symbol.for(vN),vj=Symbol.for("effect/MetricKeyType/Summary"),vM={_In:e=>e,_Out:e=>e};class vz{constructor(e,t){this[vC]=vM,this[vO]=vO,this.incremental=e,this.bigint=t,this._hash=e$(vE)}[eR](){return this._hash}[eB](e){return vL(e)}pipe(){return e6(this,arguments)}}class vD{constructor(e){this[vC]=vM,this[vA]=vA,this.boundaries=e,this._hash=W(e$(vN),ej(eN(this.boundaries)))}[eR](){return this._hash}[eB](e){return vB(e)&&eJ(this.boundaries,e.boundaries)}pipe(){return e6(this,arguments)}}let v$=e=>new vz(e?.incremental??!1,e?.bigint??!1),vP=e=>new vD(e),vL=e=>ec(e,vO),vU=e=>ec(e,vF),vq=e=>ec(e,vR),vB=e=>ec(e,vA),vJ=e=>ec(e,vj),vH=Symbol.for("effect/MetricKey"),vK={_Type:e=>e},vW=sq(eJ);class vV{constructor(e,t,r,n=[]){this[vH]=vK,this.name=e,this.keyType=t,this.description=r,this.tags=n,this._hash=W(e$(this.name+this.description),ej(eN(this.keyType)),ej(eU(this.tags)))}[eR](){return this._hash}[eB](e){return vG(e)&&this.name===e.name&&eJ(this.keyType,e.keyType)&&eJ(this.description,e.description)&&vW(this.tags,e.tags)}pipe(){return e6(this,arguments)}}let vG=e=>ec(e,vH),vY=(e,t)=>new vV(e,v$(t),iT(t?.description)),vZ=U(2,(e,t)=>0===t.length?e:new vV(e.name,e.keyType,e.description,sO(e.tags,t))),vQ=Symbol.for("effect/MutableHashMap"),vX={[vQ]:vQ,[Symbol.iterator](){return new v0(this)},toString(){return eY(this.toJSON())},toJSON(){return{_id:"MutableHashMap",values:Array.from(this).map(eG)}},[eV](){return this.toJSON()},pipe(){return e6(this,arguments)}};class v0{constructor(e){this.self=e,this.referentialIterator=e.referential[Symbol.iterator]()}next(){if(void 0!==this.bucketIterator)return this.bucketIterator.next();let e=this.referentialIterator.next();return e.done?(this.bucketIterator=new v1(this.self.buckets.values()),this.next()):e}[Symbol.iterator](){return new v0(this.self)}}class v1{constructor(e){this.backing=e}next(){if(void 0===this.currentBucket){let e=this.backing.next();if(e.done)return e;this.currentBucket=e.value[Symbol.iterator]()}let e=this.currentBucket.next();return e.done?(this.currentBucket=void 0,this.next()):e}}let v2=()=>{let e=Object.create(vX);return e.referential=new Map,e.buckets=new Map,e.bucketsSize=0,e},v3=U(2,(e,t)=>{if(!1===eK(t))return e.referential.has(t)?tR(e.referential.get(t)):iC();let r=t[eR](),n=e.buckets.get(r);return void 0===n?iC():v5(e,n,t)}),v5=(e,t,r,n=!1)=>{for(let i=0,s=t.length;i<s;i++)if(r[eB](t[i][0])){let r=t[i][1];return n&&(t.splice(i,1),e.bucketsSize--),tR(r)}return iC()},v4=U(2,(e,t)=>tF(v3(e,t))),v6=U(3,(e,t,r)=>{if(!1===eK(t))return e.referential.set(t,r),e;let n=t[eR](),i=e.buckets.get(n);return void 0===i?e.buckets.set(n,[[t,r]]):(v8(e,i,t),i.push([t,r])),e.bucketsSize++,e}),v8=(e,t,r)=>{for(let n=0,i=t.length;n<i;n++)if(r[eB](t[n][0])){t.splice(n,1),e.bucketsSize--;return}},v7=U(2,(e,t)=>{if(!1===eK(t))return e.referential.delete(t),e;let r=t[eR](),n=e.buckets.get(r);return void 0===n||(v8(e,n,t),0===n.length&&e.buckets.delete(r)),e}),v9=e=>e.referential.size+e.bucketsSize,Se=Symbol.for("effect/MetricState"),St="effect/MetricState/Counter",Sr=Symbol.for(St),Sn="effect/MetricState/Frequency",Si=Symbol.for(Sn),Ss="effect/MetricState/Gauge",Sa=Symbol.for(Ss),So="effect/MetricState/Histogram",Sl=Symbol.for(So),Sc="effect/MetricState/Summary",Su=Symbol.for(Sc),Sh={_A:e=>e};class Sp{constructor(e){this[Se]=Sh,this[Sr]=Sr,this.count=e}[eR](){return W(eN(St),ej(eN(this.count)),eq(this))}[eB](e){return Sw(e)&&this.count===e.count}pipe(){return e6(this,arguments)}}let Sf=sq(eJ);class Sd{constructor(e){this[Se]=Sh,this[Si]=Si,this.occurrences=e}[eR](){return W(e$(Sn),ej(eU(i6(this.occurrences.entries()))),eq(this))}[eB](e){return Sk(e)&&Sf(i6(this.occurrences.entries()),i6(e.occurrences.entries()))}pipe(){return e6(this,arguments)}}class Sm{constructor(e){this[Se]=Sh,this[Sa]=Sa,this.value=e}[eR](){return W(eN(Ss),ej(eN(this.value)),eq(this))}[eB](e){return SC(e)&&this.value===e.value}pipe(){return e6(this,arguments)}}class Sg{constructor(e,t,r,n,i){this[Se]=Sh,this[Sl]=Sl,this.buckets=e,this.count=t,this.min=r,this.max=n,this.sum=i}[eR](){return W(eN(So),ej(eN(this.buckets)),ej(eN(this.count)),ej(eN(this.min)),ej(eN(this.max)),ej(eN(this.sum)),eq(this))}[eB](e){return SE(e)&&eJ(this.buckets,e.buckets)&&this.count===e.count&&this.min===e.min&&this.max===e.max&&this.sum===e.sum}pipe(){return e6(this,arguments)}}class Sb{constructor(e,t,r,n,i,s){this[Se]=Sh,this[Su]=Su,this.error=e,this.quantiles=t,this.count=r,this.min=n,this.max=i,this.sum=s}[eR](){return W(eN(Sc),ej(eN(this.error)),ej(eN(this.quantiles)),ej(eN(this.count)),ej(eN(this.min)),ej(eN(this.max)),ej(eN(this.sum)),eq(this))}[eB](e){return SO(e)&&this.error===e.error&&eJ(this.quantiles,e.quantiles)&&this.count===e.count&&this.min===e.min&&this.max===e.max&&this.sum===e.sum}pipe(){return e6(this,arguments)}}let Sx=e=>new Sp(e),Sy=e=>new Sd(e),Sv=e=>new Sm(e),SS=e=>new Sg(e.buckets,e.count,e.min,e.max,e.sum),S_=e=>new Sb(e.error,e.quantiles,e.count,e.min,e.max,e.sum),Sw=e=>ec(e,Sr),Sk=e=>ec(e,Si),SC=e=>ec(e,Sa),SE=e=>ec(e,Sl),SO=e=>ec(e,Su),SI=Symbol.for("effect/MetricHook"),SF={_In:e=>e,_Out:e=>e},ST=e=>({[SI]:SF,pipe(){return e6(this,arguments)},...e}),SR=BigInt(0),SN=e=>{let t=e.keyType.bigint?SR:0,r=e.keyType.incremental?e.keyType.bigint?e=>e>=SR:e=>e>=0:e=>!0,n=e=>{r(e)&&(t+=e)};return ST({get:()=>Sx(t),update:n,modify:n})},SA=e=>{let t=new Map;for(let r of e.keyType.preregisteredWords)t.set(r,0);let r=e=>{let r=t.get(e)??0;t.set(e,r+1)};return ST({get:()=>Sy(t),update:r,modify:r})},Sj=(e,t)=>{let r=t;return ST({get:()=>Sv(r),update:e=>{r=e},modify:e=>{r+=e}})},SM=e=>{let t=e.keyType.boundaries.values,r=t.length,n=new Uint32Array(r+1),i=new Float32Array(r),s=0,a=0,o=Number.MAX_VALUE,l=Number.MIN_VALUE;W(t,sy(iW),sT((e,t)=>{i[t]=e}));let c=e=>{let t=0,c=r;for(;t!==c;){let r=Math.floor(t+(c-t)/2);e<=i[r]?c=r:t=r,c===t+1&&(e<=i[t]?c=t:t=c)}n[t]=n[t]+1,s+=1,a+=e,e<o&&(o=e),e>l&&(l=e)},u=()=>{let e=i5(r),t=0;for(let s=0;s<r;s++){let r=i[s];t+=n[s],e[s]=[r,t]}return e};return ST({get:()=>SS({buckets:u(),count:s,min:o,max:l,sum:a}),update:c,modify:c})},Sz=e=>{let{error:t,maxAge:r,maxSize:n,quantiles:i}=e.keyType,s=W(i,sy(iW)),a=i5(n),o=0,l=0,c=0,u=0,h=0,p=e=>{let i=[],o=0;for(;o!==n-1;){let t=a[o];if(null!=t){let[n,s]=t,a=oS(e-n);o$(a,ob)&&oz(a,r)&&i.push(s)}o+=1}return SD(t,s,sy(i,iW))},f=(e,t)=>{n>0&&(a[(o+=1)%n]=[t,e]),u=0===l?e:Math.min(u,e),h=0===l?e:Math.max(h,e),l+=1,c+=e};return ST({get:()=>S_({error:t,quantiles:p(Date.now()),count:l,min:u,max:h,sum:c}),update:([e,t])=>f(e,t),modify:([e,t])=>f(e,t)})},SD=(e,t,r)=>{let n=r.length;if(!ss(t))return sI();let i=t[0],s=t.slice(1),a=S$(e,n,iC(),0,i,r),o=sF(a);return s.forEach(t=>{o.push(S$(e,n,a.value,a.consumed,t,a.rest))}),sT(o,e=>[e.quantile,e.value])},S$=(e,t,r,n,i,s)=>{let a=e,o=t,l=r,c=n,u=i,h=s,p=e,f=t,d=r,m=n,g=i,b=s;for(;;){if(!ss(h))return{quantile:u,value:iC(),consumed:c,rest:[]};if(1===u)return{quantile:u,value:tR(sf(h)),consumed:c+h.length,rest:[]};let e=sh(h),t=sg(h,t=>t===e),r=u*o,n=a/2*r,i=c+t[0].length,s=Math.abs(i-r);if(i<r-n){p=a,f=o,d=su(h),m=i,g=u,b=t[1],a=p,o=f,l=d,c=m,u=g,h=b;continue}if(i>r+n)return{quantile:u,value:tI(l)?tR(e):l,consumed:c,rest:h};switch(l._tag){case"None":p=a,f=o,d=su(h),m=i,g=u,b=t[1],a=p,o=f,l=d,c=m,u=g,h=b;continue;case"Some":if(s<Math.abs(r-l.value)){p=a,f=o,d=su(h),m=i,g=u,b=t[1],a=p,o=f,l=d,c=m,u=g,h=b;continue}return{quantile:u,value:tR(l.value),consumed:c,rest:h}}}throw Error("BUG: MetricHook.resolveQuantiles - please report an issue at https://github.com/Effect-TS/effect/issues")},SP=Symbol.for("effect/MetricPair"),SL={_Type:e=>e},SU=(e,t)=>({[SP]:SL,metricKey:e,metricState:t,pipe(){return e6(this,arguments)}}),Sq=Symbol.for("effect/MetricRegistry");class SB{snapshot(){let e=[];for(let[t,r]of this.map)e.push(SU(t,r.get()));return e}get(e){let t=W(this.map,v3(e),iR);if(null!=t)return t;if(vL(e.keyType))return this.getCounter(e);if(vq(e.keyType))return this.getGauge(e);if(vU(e.keyType))return this.getFrequency(e);if(vB(e.keyType))return this.getHistogram(e);if(vJ(e.keyType))return this.getSummary(e);throw Error("BUG: MetricRegistry.get - unknown MetricKeyType - please report an issue at https://github.com/Effect-TS/effect/issues")}getCounter(e){let t=W(this.map,v3(e),iR);if(null==t){let r=SN(e);W(this.map,v4(e))||W(this.map,v6(e,r)),t=r}return t}getFrequency(e){let t=W(this.map,v3(e),iR);if(null==t){let r=SA(e);W(this.map,v4(e))||W(this.map,v6(e,r)),t=r}return t}getGauge(e){let t=W(this.map,v3(e),iR);if(null==t){let r=Sj(e,e.keyType.bigint?BigInt(0):0);W(this.map,v4(e))||W(this.map,v6(e,r)),t=r}return t}getHistogram(e){let t=W(this.map,v3(e),iR);if(null==t){let r=SM(e);W(this.map,v4(e))||W(this.map,v6(e,r)),t=r}return t}getSummary(e){let t=W(this.map,v3(e),iR);if(null==t){let r=Sz(e);W(this.map,v4(e))||W(this.map,v6(e,r)),t=r}return t}constructor(){this[Sq]=Sq,this.map=v2()}}let SJ=()=>new SB,SH=Symbol.for("effect/Metric"),SK={_Type:e=>e,_In:e=>e,_Out:e=>e},SW=Y(Symbol.for("effect/Metric/globalMetricRegistry"),()=>SJ()),SV=function(e,t,r,n){let i=Object.assign(e=>pK(e,e=>SX(i,e)),{[SH]:SK,keyType:e,unsafeUpdate:t,unsafeValue:r,unsafeModify:n,register(){return this.unsafeValue([]),this},pipe(){return e6(this,arguments)}});return i},SG=(e,t)=>SY(vY(e,t)),SY=e=>{let t,r=new WeakMap,n=n=>{if(0===n.length)return void 0!==t?t:t=SW.get(e);let i=r.get(n);return void 0!==i||(i=SW.get(vZ(e,n)),r.set(n,i)),i};return SV(e.keyType,(e,t)=>n(t).update(e),e=>n(e).get(),(e,t)=>n(t).modify(e))},SZ=U(3,(e,t,r)=>SQ(e,[bs(t,r)])),SQ=U(2,(e,t)=>SV(e.keyType,(r,n)=>e.unsafeUpdate(r,sO(t,n)),r=>e.unsafeValue(sO(t,r)),(r,n)=>e.unsafeModify(r,sO(t,n)))),SX=U(2,(e,t)=>ff(f$,r=>pH(()=>e.unsafeUpdate(t,r)))),S0="effect/MetricBoundaries",S1=Symbol.for(S0);class S2{constructor(e){this[S1]=S1,this.values=e,this._hash=W(e$(S0),ej(eU(this.values)))}[eR](){return this._hash}[eB](e){return S3(e)&&eJ(this.values,e.values)}pipe(){return e6(this,arguments)}}let S3=e=>ec(e,S1),S5=e=>new S2(W(e,sr(ad(Number.POSITIVE_INFINITY)),sJ)),S4=Symbol.for("effect/Request"),S6=U(2,(e,t)=>ff(yR,r=>pH(()=>{if(r.has(e)){let n=r.get(e);n.state.completed||(n.state.completed=!0,dz(n.result,t))}})));class S8{addObserver(e){this.observers.add(e)}removeObserver(e){this.observers.delete(e)}increment(){this.count++,this.observers.forEach(e=>e(this.count))}decrement(){this.count--,this.observers.forEach(e=>e(this.count))}constructor(){this.count=0,this.observers=new Set,this.interrupted=!1}}let S7=Symbol.for("effect/Supervisor"),S9={_T:e=>e};class _e{constructor(e,t){this[S7]=S9,this.underlying=e,this.value0=t}get value(){return this.value0}onStart(e,t,r,n){this.underlying.onStart(e,t,r,n)}onEnd(e,t){this.underlying.onEnd(e,t)}onEffect(e,t){this.underlying.onEffect(e,t)}onSuspend(e){this.underlying.onSuspend(e)}onResume(e){this.underlying.onResume(e)}map(e){return new _e(this,W(this.value,pj(e)))}zip(e){return new _t(this,e)}}class _t{constructor(e,t){this._tag="Zip",this[S7]=S9,this.left=e,this.right=t}get value(){return p5(this.left.value,this.right.value)}onStart(e,t,r,n){this.left.onStart(e,t,r,n),this.right.onStart(e,t,r,n)}onEnd(e,t){this.left.onEnd(e,t),this.right.onEnd(e,t)}onEffect(e,t){this.left.onEffect(e,t),this.right.onEffect(e,t)}onSuspend(e){this.left.onSuspend(e),this.right.onSuspend(e)}onResume(e){this.left.onResume(e),this.right.onResume(e)}map(e){return new _e(this,W(this.value,pj(e)))}zip(e){return new _t(this,e)}}let _r=e=>ec(e,S7)&&eu(e,"Zip");class _n{get value(){return pH(()=>Array.from(this.fibers))}onStart(e,t,r,n){this.fibers.add(n)}onEnd(e,t){this.fibers.delete(t)}onEffect(e,t){}onSuspend(e){}onResume(e){}map(e){return new _e(this,W(this.value,pj(e)))}zip(e){return new _t(this,e)}onRun(e,t){return e()}constructor(){this[S7]=S9,this.fibers=new Set}}class _i{constructor(e){this[S7]=S9,this.effect=e}get value(){return this.effect}onStart(e,t,r,n){}onEnd(e,t){}onEffect(e,t){}onSuspend(e){}onResume(e){}map(e){return new _e(this,W(this.value,pj(e)))}zip(e){return new _t(this,e)}onRun(e,t){return e()}}let _s=pH(()=>new _n),_a=e=>new _i(e),_o=Y("effect/Supervisor/none",()=>_a(pZ)),_l="Empty",_c="AddSupervisor",_u="RemoveSupervisor",_h="AndThen",_p={_tag:_l},_f=(e,t)=>({_tag:_h,first:e,second:t}),_d=(e,t)=>{let r=e,n=t;for(;aM(n);){let e=aD(n);switch(e._tag){case _l:n=aq(n);break;case _c:r=r.zip(e.supervisor),n=aq(n);break;case _u:r=_m(r,e.supervisor),n=aq(n);break;case _h:n=ak(e.first)(ak(e.second)(aq(n)))}}return r},_m=(e,t)=>eJ(e,t)?_o:_r(e)?_m(e.left,t).zip(_m(e.right,t)):e,_g=e=>eJ(e,_o)?l_():_r(e)?W(_g(e.left),lN(_g(e.right))):lz(e),_b=uT({empty:_p,patch:(e,t)=>_d(t,ad(e)),combine:_f,diff:(e,t)=>{if(eJ(e,t))return _p;let r=_g(e),n=_g(t),i=W(n,lR(r),lj(_p,(e,t)=>_f(e,{_tag:_c,supervisor:t}))),s=W(r,lR(n),lj(_p,(e,t)=>_f(e,{_tag:_u,supervisor:t})));return _f(i,s)}}),_x=SG("effect_fiber_started",{incremental:!0}),_y=SG("effect_fiber_active"),_v=SG("effect_fiber_successes",{incremental:!0}),_S=SG("effect_fiber_failures",{incremental:!0}),__=SZ(SY((F="effect_fiber_lifetimes",T=(e=>W(i4(e.count-1,t=>e.start*Math.pow(e.factor,t)),av,S5))({start:.5,factor:2,count:35}),R=v,new vV(F,vP(T),iT(R)))),"time_unit","milliseconds"),_w="Continue",_k="Yield",_C={_E:e=>e,_A:e=>e},_E=e=>{throw Error(`BUG: FiberRuntime - ${eX(e)} - please report an issue at https://github.com/Effect-TS/effect/issues`)},_O=Symbol.for("effect/internal/fiberRuntime/YieldedOp"),_I=Y("effect/internal/fiberRuntime/yieldedOpChannel",()=>({currentOp:null})),_F={[tt]:(e,t,r)=>eO(()=>t.effect_instruction_i1(r)),OnStep:(e,t,r)=>dS(dS(r)),[tr]:(e,t,r)=>eO(()=>t.effect_instruction_i2(r)),[tu]:(e,t,r)=>(e.patchRuntimeFlags(e.currentRuntimeFlags,t.patch),uK(e.currentRuntimeFlags)&&e.isInterrupted())?dd(e.getInterruptedCause()):dS(r),[ta]:(e,t,r)=>(eO(()=>t.effect_instruction_i2(r)),eO(()=>t.effect_instruction_i0()))?(e.pushStack(t),eO(()=>t.effect_instruction_i1())):pZ,[to]:(e,t,r)=>{let n=eO(()=>t.effect_instruction_i0.next(r));return n.done?dS(n.value):(e.pushStack(t),ek(n.value))}},_T={[yz]:(e,t,r,n)=>(e.processNewInterruptSignal(n.cause),uK(t)?dd(n.cause):r),[y$]:(e,t,r,n)=>{throw Error("It is illegal to have multiple concurrent run loops in a single fiber")},[yD]:(e,t,r,n)=>(n.onFiber(e,x9(t)),r),[yP]:(e,t,r,n)=>pv(p3(),()=>r)},_R=e=>pI(yc(e),e=>_Q(yT(e),([e,t])=>{let r=new Map,n=[];for(let e of t)for(let t of(n.push(ab(e)),e))r.set(t.request,t);let i=n.flat();return fS(ww(e.runAll(n),i,()=>i.forEach(e=>{e.listeners.interrupted=!0})),yR,r)},!1,!1)),_N=V();class _A extends t4{static{s=yG,a=yQ}constructor(e,t,r){if(super(),this[s]=yY,this[a]=_C,this._queue=[],this._children=null,this._observers=[],this._running=!1,this._stack=[],this._asyncInterruptor=null,this._asyncBlockingOn=null,this._exitValue=null,this._steps=[],this._isYielding=!1,this.currentOpCount=0,this.run=()=>{this.drainQueueOnCurrentThread()},this.currentRuntimeFlags=r,this._fiberId=e,this._fiberRefs=t,uZ(r)){let e=this.getFiberRef(f$);_x.unsafeUpdate(1,e),_y.unsafeUpdate(1,e)}this.refreshRefCache()}commit(){return y1(this)}id(){return this._fiberId}resume(e){this.tell(yq(e))}get status(){return this.ask((e,t)=>t)}get runtimeFlags(){return this.ask((e,t)=>yt(t)?e.currentRuntimeFlags:t.runtimeFlags)}scope(){return yW(this)}get children(){return this.ask(e=>Array.from(e.getChildren()))}getChildren(){return null===this._children&&(this._children=new Set),this._children}getInterruptedCause(){return this.getFiberRef(fL)}fiberRefs(){return this.ask(e=>e.getFiberRefs())}ask(e){return pJ(()=>{let t=dO(this._fiberId);return this.tell(yU((r,n)=>{dz(t,pH(()=>e(r,n)))})),dT(t)})}tell(e){this._queue.push(e),this._running||(this._running=!0,this.drainQueueLaterOnExecutor())}get await(){return pi(e=>{let t=t=>e(pB(t));return this.tell(yU((e,r)=>{null!==e._exitValue?t(this._exitValue):e.addObserver(t)})),pH(()=>this.tell(yU((e,r)=>{e.removeObserver(t)})))},this.id())}get inheritAll(){return h8((e,t)=>{let r=e.id(),n=e.getFiberRefs(),i=t.runtimeFlags,s=gN(n,r,this.getFiberRefs());return e.setFiberRefs(s),pQ(W(uX(i,e.getFiberRef(wg)),uL(1),uL(16)))})}get poll(){return pH(()=>iT(this._exitValue))}unsafePoll(){return this._exitValue}interruptAsFork(e){return pH(()=>this.tell(yL(hs(e))))}unsafeInterruptAsFork(e){this.tell(yL(hs(e)))}addObserver(e){null!==this._exitValue?e(this._exitValue):this._observers.push(e)}removeObserver(e){this._observers=this._observers.filter(t=>t!==e)}getFiberRefs(){return this.setFiberRef(wg,this.currentRuntimeFlags),this._fiberRefs}unsafeDeleteFiberRef(e){this._fiberRefs=gM(this._fiberRefs,e)}getFiberRef(e){return this._fiberRefs.locals.has(e)?this._fiberRefs.locals.get(e)[0][1]:e.initial}setFiberRef(e,t){this._fiberRefs=g$(this._fiberRefs,{fiberId:this._fiberId,fiberRef:e,value:t}),this.refreshRefCache()}refreshRefCache(){this.currentDefaultServices=this.getFiberRef(m7),this.currentTracer=this.currentDefaultServices.unsafeMap.get(m0.key),this.currentSupervisor=this.getFiberRef(wb),this.currentScheduler=this.getFiberRef(ya),this.currentContext=this.getFiberRef(fI),this.currentSpan=this.currentContext.unsafeMap.get(m1.key)}setFiberRefs(e){this._fiberRefs=e,this.refreshRefCache()}addChild(e){this.getChildren().add(e)}removeChild(e){this.getChildren().delete(e)}transferChildren(e){let t=this._children;if(this._children=null,null!==t&&t.size>0)for(let r of t)null===r._exitValue&&e.add(this.currentRuntimeFlags,r)}drainQueueOnCurrentThread(){let e=!0;for(;e;){let t=_w,r=globalThis[y3];globalThis[y3]=this;try{for(;t===_w;)t=0===this._queue.length?"Done":this.evaluateMessageWhileSuspended(this._queue.splice(0,1)[0])}finally{this._running=!1,globalThis[y3]=r}this._queue.length>0&&!this._running?(this._running=!0,t===_k?(this.drainQueueLaterOnExecutor(),e=!1):e=!0):e=!1}}drainQueueLaterOnExecutor(){this.currentScheduler.scheduleTask(this.run,this.getFiberRef(fF))}drainQueueWhileRunning(e,t){let r=t;for(;this._queue.length>0;){let t=this._queue.splice(0,1)[0];r=_T[t._tag](this,e,r,t)}return r}isInterrupted(){return!hh(this.getFiberRef(fL))}addInterruptedCause(e){let t=this.getFiberRef(fL);this.setFiberRef(fL,ho(t,e))}processNewInterruptSignal(e){this.addInterruptedCause(e),this.sendInterruptSignalToAllChildren()}sendInterruptSignalToAllChildren(){if(null===this._children||0===this._children.size)return!1;let e=!1;for(let t of this._children)t.tell(yL(hs(this.id()))),e=!0;return e}interruptAllChildren(){if(this.sendInterruptSignalToAllChildren()){let e=this._children.values();this._children=null;let t=!1;return p0({while:()=>!t,body:()=>{let r=e.next();return r.done?pH(()=>{t=!0}):pe(r.value.await)},step:()=>{}})}return null}reportExitValue(e){if(uZ(this.currentRuntimeFlags)){let t=this.getFiberRef(f$),r=this.id().startTimeMillis,n=Date.now();switch(__.unsafeUpdate(n-r,t),_y.unsafeUpdate(-1,t),e._tag){case tn:_v.unsafeUpdate(1,t);break;case e9:_S.unsafeUpdate(1,t)}}if("Failure"===e._tag){let t=this.getFiberRef(fD);hf(e.cause)||"Some"!==t._tag||this.log("Fiber terminated with an unhandled error",e.cause,t)}}setExitValue(e){this._exitValue=e,this.reportExitValue(e);for(let t=this._observers.length-1;t>=0;t--)this._observers[t](e);this._observers=[]}getLoggers(){return this.getFiberRef(_q)}log(e,t,r){let n=tF(r)?r.value:this.getFiberRef(fN);if(gU(this.getFiberRef(_j),n))return;let i=this.getFiberRef(fA),s=this.getFiberRef(fR),a=this.getLoggers(),o=this.getFiberRefs();if(lD(a)>0){let r=new Date(tG(this.getFiberRef(m7),dV).unsafeCurrentTimeMillis());e5(o,()=>{for(let l of a)l.log({fiberId:this.id(),logLevel:n,message:e,cause:t,context:o,spans:i,annotations:s,date:r})})}}evaluateMessageWhileSuspended(e){switch(e._tag){case yP:return _k;case yz:return this.processNewInterruptSignal(e.cause),null!==this._asyncInterruptor&&(this._asyncInterruptor(dd(e.cause)),this._asyncInterruptor=null),_w;case y$:return this._asyncInterruptor=null,this._asyncBlockingOn=null,this.evaluateEffect(e.effect),_w;case yD:return e.onFiber(this,null!==this._exitValue?x8:ye(this.currentRuntimeFlags,this._asyncBlockingOn)),_w;default:return _E(e)}}evaluateEffect(e){this.currentSupervisor.onResume(this);try{let t=uK(this.currentRuntimeFlags)&&this.isInterrupted()?dd(this.getInterruptedCause()):e;for(;null!==t;){let e=t,r=this.runLoop(e);if(r===_O){let e=_I.currentOp;_I.currentOp=null,e._op===tc?uB(this.currentRuntimeFlags)?(this.tell(yB()),this.tell(yq(d_)),t=null):t=d_:e._op===e8&&(t=null)}else{this.currentRuntimeFlags=W(this.currentRuntimeFlags,uH(16));let e=this.interruptAllChildren();null!==e?t=pv(e,()=>r):(0===this._queue.length?this.setExitValue(r):this.tell(yq(r)),t=null)}}}finally{this.currentSupervisor.onSuspend(this)}}start(e){if(this._running)this.tell(yq(e));else{this._running=!0;let t=globalThis[y3];globalThis[y3]=this;try{this.evaluateEffect(e)}finally{this._running=!1,globalThis[y3]=t,this._queue.length>0&&this.drainQueueLaterOnExecutor()}}}startFork(e){this.tell(yq(e))}patchRuntimeFlags(e,t){let r=u0(e,t);return globalThis[y3]=this,this.currentRuntimeFlags=r,r}initiateAsync(e,t){let r=!1,n=e=>{r||(r=!0,this.tell(yq(e)))};uK(e)&&(this._asyncInterruptor=n);try{t(n)}catch(e){n(pg(hi(e)))}}pushStack(e){this._stack.push(e),"OnStep"===e._op&&this._steps.push({refs:this.getFiberRefs(),flags:this.currentRuntimeFlags})}popStack(){let e=this._stack.pop();if(e)return"OnStep"===e._op&&this._steps.pop(),e}getNextSuccessCont(){let e=this.popStack();for(;e;){if(e._op!==te)return e;e=this.popStack()}}getNextFailCont(){let e=this.popStack();for(;e;){if(e._op!==tt&&e._op!==ta&&e._op!==to)return e;e=this.popStack()}}Tag(e){return pH(()=>tG(this.currentContext,e))}Left(e){return pd(e.left)}None(e){return pd(new f7)}Right(e){return dS(e.right)}Some(e){return dS(e.value)}Micro(e){return pr(t=>{let r=t,n=nl(r1(e,this.currentContext));return n.addObserver(e=>{if("Success"===e._tag)return r(dS(e.value));switch(e.cause._tag){case"Interrupt":return r(dd(hs(l7)));case"Fail":return r(pd(e.cause.error));case"Die":return r(pu(e.cause.defect))}}),pr(e=>{r=t=>{e(pZ)},n.unsafeInterrupt()})})}[ti](e){let t=eO(()=>e.effect_instruction_i0()),r=this.getNextSuccessCont();return void 0!==r?(r._op in _F||_E(r),_F[r._op](this,r,t)):(_I.currentOp=dS(t),_O)}[tn](e){let t=this.getNextSuccessCont();return void 0!==t?(t._op in _F||_E(t),_F[t._op](this,t,e.effect_instruction_i0)):(_I.currentOp=e,_O)}[e9](e){let t=e.effect_instruction_i0,r=this.getNextFailCont();if(void 0===r)return _I.currentOp=dd(t),_O;switch(r._op){case te:case tr:if(!(uK(this.currentRuntimeFlags)&&this.isInterrupted()))return eO(()=>r.effect_instruction_i1(t));return dd(hS(t));case"OnStep":if(!(uK(this.currentRuntimeFlags)&&this.isInterrupted()))return dS(dd(t));return dd(hS(t));case tu:if(this.patchRuntimeFlags(this.currentRuntimeFlags,r.patch),uK(this.currentRuntimeFlags)&&this.isInterrupted())return dd(ho(t,this.getInterruptedCause()));return dd(t);default:_E(r)}}[tl](e){return eO(()=>e.effect_instruction_i0(this,x9(this.currentRuntimeFlags)))}Blocked(e){let t=this.getFiberRefs(),r=this.currentRuntimeFlags;if(this._steps.length>0){let n=[],i=this._steps[this._steps.length-1],s=this.popStack();for(;s&&"OnStep"!==s._op;)n.push(s),s=this.popStack();this.setFiberRefs(i.refs),this.currentRuntimeFlags=i.flags;let a=g9(i.refs,t),o=uX(i.flags,r);return dS(hX(e.effect_instruction_i0,h8(t=>{for(;n.length>0;)t.pushStack(n.pop());return t.setFiberRefs(bt(t.id(),t.getFiberRefs())(a)),t.currentRuntimeFlags=u0(o)(t.currentRuntimeFlags),e.effect_instruction_i1})))}return pY(t=>pv(_0(h0(e.effect_instruction_i0)),()=>t(e.effect_instruction_i1)))}RunBlocked(e){return _R(e.effect_instruction_i0)}[ts](e){let t=e.effect_instruction_i0,r=this.currentRuntimeFlags,n=u0(r,t);if(uK(n)&&this.isInterrupted())return dd(this.getInterruptedCause());if(this.patchRuntimeFlags(this.currentRuntimeFlags,t),!e.effect_instruction_i1)return d_;{let t=uX(n,r);return this.pushStack(new h2(t,e)),eO(()=>e.effect_instruction_i1(r))}}[tt](e){return this.pushStack(e),e.effect_instruction_i0}OnStep(e){return this.pushStack(e),e.effect_instruction_i0}[te](e){return this.pushStack(e),e.effect_instruction_i0}[tr](e){return this.pushStack(e),e.effect_instruction_i0}[e8](e){return this._asyncBlockingOn=e.effect_instruction_i1,this.initiateAsync(this.currentRuntimeFlags,e.effect_instruction_i0),_I.currentOp=e,_O}[tc](e){return this._isYielding=!1,_I.currentOp=e,_O}[ta](e){let t=e.effect_instruction_i0,r=e.effect_instruction_i1;return t()?(this.pushStack(e),r()):d_}[to](e){return _F[to](this,e,void 0)}[e7](e){return eO(()=>e.commit())}runLoop(e){let t=e;for(this.currentOpCount=0;;){if((2&this.currentRuntimeFlags)!=0&&this.currentSupervisor.onEffect(this,t),this._queue.length>0&&(t=this.drainQueueWhileRunning(this.currentRuntimeFlags,t)),!this._isYielding){this.currentOpCount+=1;let e=this.currentScheduler.shouldYield(this);if(!1!==e){this._isYielding=!0,this.currentOpCount=0;let r=t;t=pv(p3({priority:e}),()=>r)}}try{if((t=this.currentTracer.context(()=>_N!==t[h1]._V?ph(`Cannot execute an Effect versioned ${t[h1]._V} with a Runtime of version ${V()}`):this[t._op](t),this))===_O){let e=_I.currentOp;if(e._op===tc||e._op===e8)return _O;return _I.currentOp=null,e._op===tn||e._op===e9?e:dd(hi(e))}}catch(e){t=(t===_O||ec(t,"_op"))&&t._op in this?f5(e)?dd(ho(hi(e),hs(l7))):pu(e):ph(`Not a valid effect: ${eX(t)}`)}}}}let _j=Y("effect/FiberRef/currentMinimumLogLevel",()=>fw(gq("Info"))),_M=e=>y6(t=>{tG(gD(t.context,m7),mK).unsafe.log(e.log(t))}),_z=Y(Symbol.for("effect/Logger/defaultLogger"),()=>_M(vo)),_D=Y(Symbol.for("effect/Logger/jsonLogger"),()=>_M(vh)),_$=Y(Symbol.for("effect/Logger/logFmtLogger"),()=>_M(vl)),_P=Y(Symbol.for("effect/Logger/prettyLogger"),()=>vk),_L=Y(Symbol.for("effect/Logger/structuredLogger"),()=>_M(vc)),_U=Y(Symbol.for("effect/Logger/tracerLogger"),()=>y6(({annotations:e,cause:t,context:r,fiberId:n,logLevel:i,message:s})=>{let a=tY(gD(r,fI),m1);if("None"===a._tag||"ExternalSpan"===a.value._tag)return;let o=tG(gD(r,m7),dV),l={};for(let[t,r]of e)l[t]=r;l["effect.fiberId"]=l6(n),l["effect.logLevel"]=i.label,null!==t&&"Empty"!==t._tag&&(l["effect.cause"]=hD(t,{renderErrorCause:!0})),a.value.event(eX(Array.isArray(s)?s[0]:s),o.unsafeCurrentTimeNanos(),l)})),_q=Y(Symbol.for("effect/FiberRef/currentLoggers"),()=>fk(lz(_z,_U))),_B=U(3,(e,t,r)=>pv(wo,n=>{let i=[],s=pJ(()=>{if(0===i.length)return pZ;let e=i;return i=[],r(e)});return pY(r=>W(gO(t),p6(s),bv,r,_0,pv(e=>fW(n,p9(e))),p6(_H(()=>s)),h9(y6(t=>{i.push(e.log(t))}))))})),_J=U(e=>h6(e[0]),(e,t)=>pG(pK(e,e=>_H(r=>t(e,r))))),_H=e=>h8(t=>{let r=t.getFiberRefs(),n=uJ(t.currentRuntimeFlags,1);return pv(wo,t=>fV(t,t=>h8(i=>{let s=i.getFiberRefs(),a=i.currentRuntimeFlags,o=g9(s,r),l=uX(a,n),c=g9(r,s);return i.setFiberRefs(bt(o,i.id(),r)),w_(p2(e(t),l),pH(()=>{i.setFiberRefs(bt(c,i.id(),i.getFiberRefs()))}))})))}),_K=((e=>em(e[0])&&!h6(e[0]),(e,t,r)=>{let n=r?.negate?(e,r)=>pj(t(e,r),ai):t;return yA(r?.concurrency,()=>pJ(()=>i6(e).reduceRight((e,t,r)=>p8(e,pJ(()=>n(t,r)),(e,r)=>r?[t,...e]:e),pH(()=>[]))),()=>pj(_Y(e,(e,t)=>pj(n(e,t),t=>t?tR(e):iC()),r),sz))}),e=>{if(Array.isArray(e)||em(e))return[e,iC()];let t=Object.keys(e),r=t.length;return[t.map(t=>e[t]),tR(e=>{let n={};for(let i=0;i<r;i++)n[t[i]]=e[i];return n})]}),_W=(e,t,r)=>{let n=[];for(let t of e)n.push(pp(t));return pv(_Y(n,q,{concurrency:r?.concurrency,batching:r?.batching,concurrentFinalizers:r?.concurrentFinalizers}),e=>{let n=iC(),i=e.length,s=Array(i),a=Array(i),o=!1;for(let t=0;t<i;t++){let r=e[t];"Left"===r._tag?(s[t]=tR(r.left),o=!0):(a[t]=r.right,s[t]=n)}return o?"Some"===t._tag?pd(t.value(s)):pd(s):r?.discard?pZ:"Some"===t._tag?pB(t.value(a)):pB(a)})},_V=(e,t,r)=>{let n=[];for(let t of e)n.push(pp(t));return r?.discard?_Y(n,q,{concurrency:r?.concurrency,batching:r?.batching,discard:!0,concurrentFinalizers:r?.concurrentFinalizers}):pj(_Y(n,q,{concurrency:r?.concurrency,batching:r?.batching,concurrentFinalizers:r?.concurrentFinalizers}),e=>"Some"===t._tag?t.value(e):e)},_G=(e,t)=>{let[r,n]=_K(e);return t?.mode==="validate"?_W(r,n,t):t?.mode==="either"?_V(r,n,t):t?.discard!==!0&&"Some"===n._tag?pj(_Y(r,q,t),n.value):_Y(r,q,t)},_Y=((e,t)=>Array.from({length:t},()=>e),U(e=>em(e[0]),(e,t,r)=>h8(n=>{let i=r?.batching===!0||r?.batching==="inherit"&&n.getFiberRef(fz);return r?.discard?yN(r.concurrency,()=>_8(xW,r?.concurrentFinalizers)(r=>i?_Q(e,(e,n)=>r(t(e,n)),!0,!1,1):pI(e,(e,n)=>r(t(e,n)))),()=>_8(xY,r?.concurrentFinalizers)(r=>_Q(e,(e,n)=>r(t(e,n)),i,!1)),n=>_8(xZ(n),r?.concurrentFinalizers)(r=>_Q(e,(e,n)=>r(t(e,n)),i,!1,n))):yN(r?.concurrency,()=>_8(xW,r?.concurrentFinalizers)(r=>i?_X(e,1,(e,n)=>r(t(e,n)),!0):pO(e,(e,n)=>r(t(e,n)))),()=>_8(xY,r?.concurrentFinalizers)(r=>_Z(e,(e,n)=>r(t(e,n)),i)),n=>_8(xZ(n),r?.concurrentFinalizers)(r=>_X(e,n,(e,n)=>r(t(e,n)),i)))}))),_Z=(e,t,r)=>pJ(()=>{let n=i6(e),i=Array(n.length);return p6(_Q(n,(e,r)=>pv(t(e,r),e=>pH(()=>i[r]=e)),r,!1),pB(i))}),_Q=(e,t,r,n,i)=>pY(s=>pW(a=>h8(o=>{let l=Array.from(e).reverse(),c=l.length;if(0===c)return pZ;let u=0,h=!1,p=i?Math.min(l.length,i):l.length,f=new Set,d=[],m=()=>f.forEach(e=>{e.currentScheduler.scheduleTask(()=>{e.unsafeInterruptAsFork(o.id())},0)}),g=[],b=[],x=[],y=()=>{let e=d.filter(({exit:e})=>"Failure"===e._tag).sort((e,t)=>e.index<t.index?-1:+(e.index!==t.index)).map(({exit:e})=>e);return 0===e.length&&e.push(d_),e},v=(e,t=!1)=>{let r=pG(a(e)),n=_2(r,o,o.currentRuntimeFlags,yV);return o.currentScheduler.scheduleTask(()=>{t&&n.unsafeInterruptAsFork(o.id()),n.resume(r)},0),n},S=()=>{n||(c-=l.length,l=[]),h=!0,m()},_=r?p_:pf,w=v(pi(e=>{let n=(e,t)=>{"Blocked"===e._op?x.push(e):(d.push({index:t,exit:e}),"Failure"!==e._op||h||S())},a=()=>{if(l.length>0){let p=l.pop(),m=u++,S=()=>{let e=l.pop();return m=u++,pv(p3(),()=>pv(_(s(t(e,m))),w))},w=e=>l.length>0&&(n(e,m),l.length>0)?S():pB(e),k=v(pv(_(s(t(p,m))),w));g.push(k),f.add(k),h&&k.currentScheduler.scheduleTask(()=>{k.unsafeInterruptAsFork(o.id())},0),k.addObserver(t=>{let s;if(s="Failure"===t._op?t:t.effect_instruction_i0,b.push(k),f.delete(k),n(s,m),d.length===c)e(pB(iO(dh(y(),{parallel:!0}),()=>d_)));else if(x.length+d.length===c){let t=y();e(pB(hX(x.map(e=>e.effect_instruction_i0).reduce(yo),_Q([iO(dh(t,{parallel:!0}),()=>d_),...x.map(e=>e.effect_instruction_i1)],e=>e,r,!0,i))))}else a()})}};for(let e=0;e<p;e++)a()}));return pe(pD(pw(s(y1(w))),dy({onFailure:e=>{S();let t=x.length+1,r=Math.min("number"==typeof i?i:x.length,x.length),n=Array.from(x);return pi(i=>{let s=[],a=0,o=0,l=(r,o)=>l=>{s[r]=l,++a===t&&i(dS(dd(e))),n.length>0&&o&&c()},c=()=>{v(n.pop(),!0).addObserver(l(o,!0)),o++};w.addObserver(l(o,!1)),o++;for(let e=0;e<r;e++)c()})},onSuccess:()=>pO(b,e=>e.inheritAll)})))}))),_X=(e,t,r,n)=>pJ(()=>{let i=i6(e),s=Array(i.length);return p6(_Q(i,(e,t)=>pj(r(e,t),e=>s[t]=e),n,!1,t),pB(s))}),_0=e=>_5(e,yV),_1=(e,t,r,n=null)=>{let i=_3(e,t,r,n);return i.resume(e),i},_2=(e,t,r,n=null)=>_3(e,t,r,n),_3=(e,t,r,n=null)=>{let i=cn(),s=gA(t.getFiberRefs(),i),a=new _A(i,s,r),o=gD(s,fI),l=a.currentSupervisor;return l.onStart(o,e,tR(t),a),a.addObserver(e=>l.onEnd(e,a)),(null!==n?n:W(t.getFiberRef(fP),iO(()=>t.scope()))).add(r,a),a},_5=(e,t)=>h8((r,n)=>pB(_1(e,r,n.runtimeFlags,t))),_4=((e=>er(e[2]),(e,t,r,n)=>yA(n?.concurrency,()=>i6(e).reduce((e,t,n)=>p8(e,t,(e,t)=>r(e,t,n)),pB(t)),()=>pv(gZ(t),t=>pv(_Y(e,(e,n)=>pv(e,e=>g2(t,t=>r(t,e,n))),n),()=>gQ(t))))),e=>dL(t=>iE(tY(t,wa),{onNone:()=>e,onSome:t=>{switch(t.strategy._tag){case"Parallel":return e;case"Sequential":case"ParallelN":return pv(fY(t,xY),t=>wp(e,t))}}}))),_6=e=>t=>dL(r=>iE(tY(r,wa),{onNone:()=>t,onSome:r=>"ParallelN"===r.strategy._tag&&r.strategy.parallelism===e?t:pv(fY(r,xZ(e)),e=>wp(t,e))})),_8=(e,t)=>r=>dL(n=>iE(tY(n,wa),{onNone:()=>r(q),onSome:n=>{if(!0!==t)return r(q);{let t="Parallel"===e._tag?_4:"Sequential"===e._tag?we:_6(e.parallelism);switch(n.strategy._tag){case"Parallel":return t(r(_4));case"Sequential":return t(r(we));case"ParallelN":return t(r(_6(n.strategy.parallelism)))}}}})),_7=e=>pv(wa,e),_9=e=>pv(wh(),t=>pD(e(t),e=>t.close(e))),we=e=>dL(t=>iE(tY(t,wa),{onNone:()=>e,onSome:t=>{switch(t.strategy._tag){case"Sequential":return e;case"Parallel":case"ParallelN":return pv(fY(t,xW),t=>wp(e,t))}}})),wt=((e=>h6(e[1]),(e,t,r,n)=>pw(ws(pf(e),pf(t),(e,t)=>dC(e,t,{onSuccess:r,onFailure:(e,t)=>n?.concurrent?ha(e,t):ho(e,t)}),n))),e=>wm(m7,tH(mm,e))),wr=U(e=>h6(e[1]),(e,t,r)=>ws(e,t,(e,t)=>[e,t],r)),wn=U(e=>h6(e[1]),(e,t,r)=>r?.concurrent!==!0&&(r?.batching===void 0||!1===r.batching)?p4(e,t):ws(e,t,(e,t)=>e,r)),wi=U(e=>h6(e[1]),(e,t,r)=>r?.concurrent!==!0&&(r?.batching===void 0||!1===r.batching)?p6(e,t):ws(e,t,(e,t)=>t,r)),ws=U(e=>h6(e[1]),(e,t,r,n)=>pj(_G([e,t],{concurrency:n?.concurrent?2:1,batching:n?.batching,concurrentFinalizers:n?.concurrentFinalizers}),([e,t])=>r(e,t))),wa=tQ("effect/Scope"),wo=wa,wl=(e,t)=>{"Open"===e.state._tag&&e.state.finalizers.set({},t)},wc={[fH]:fH,[fK]:fK,pipe(){return e6(this,arguments)},fork(e){return pH(()=>{let t=wu(e);if("Closed"===this.state._tag)return t.state=this.state,t;let r={};return this.state.finalizers.set(r,e=>t.close(e)),wl(t,e=>pH(()=>{"Open"===this.state._tag&&this.state.finalizers.delete(r)})),t})},close(e){return pJ(()=>{if("Closed"===this.state._tag)return pZ;let t=Array.from(this.state.finalizers.values()).reverse();return(this.state={_tag:"Closed",exit:e},0===t.length)?pZ:xV(this.strategy)?W(pO(t,t=>pf(t(e))),pv(e=>W(dh(e),iM(du),iO(()=>d_)))):xG(this.strategy)?W(_Z(t,t=>pf(t(e)),!1),pv(e=>W(dh(e,{parallel:!0}),iM(du),iO(()=>d_)))):W(_X(t,this.strategy.parallelism,t=>pf(t(e)),!1),pv(e=>W(dh(e,{parallel:!0}),iM(du),iO(()=>d_))))})},addFinalizer(e){return pJ(()=>"Closed"===this.state._tag?e(this.state.exit):(this.state.finalizers.set({},e),pZ))}},wu=(e=xW)=>{let t=Object.create(wc);return t.strategy=e,t.state={_tag:"Open",finalizers:new Map},t},wh=(e=xW)=>pH(()=>wu(e)),wp=U(2,(e,t)=>dB(e,tZ(tJ(wa,t)))),wf=U(2,(e,t)=>W(e,wp(t),pD(e=>t.close(e)))),wd=U(2,(e,t)=>pe(_J(pv(fp(e),r=>h9(fd(e,t),r)),t=>fd(e,t)))),wm=U(2,(e,t)=>ff(e,r=>wd(e,t(r)))),wg=fO(uY,{differ:u1,fork:u1.empty}),wb=fO(_o,{differ:_b,fork:_p}),wx=U(3,(e,t,r)=>wv(e,t,{onSelfWin:(e,t)=>pv(e.await,n=>{switch(n._tag){case tn:return pv(e.inheritAll,()=>r.onSelfDone(n,t));case e9:return r.onSelfDone(n,t)}}),onOtherWin:(e,t)=>pv(e.await,n=>{switch(n._tag){case tn:return pv(e.inheritAll,()=>r.onOtherDone(n,t));case e9:return r.onOtherDone(n,t)}})})),wy=U(2,(e,t)=>py(r=>wx(e,t,{onSelfDone:(e,t)=>dv(e,{onFailure:e=>W(y1(t),bF(t=>ha(e,t))),onSuccess:e=>W(t,fe(r),h9(e))}),onOtherDone:(e,t)=>dv(e,{onFailure:e=>W(y1(t),bF(t=>ha(t,e))),onSuccess:e=>W(t,fe(r),h9(e))})}))),wv=U(3,(e,t,r)=>h8((n,i)=>{let s=i.runtimeFlags,a=lL(!0),o=_3(e,n,s,r.selfScope),l=_3(t,n,s,r.otherScope);return pi(n=>{o.addObserver(()=>wS(o,l,r.onSelfWin,a,n)),l.addObserver(()=>wS(l,o,r.onOtherWin,a,n)),o.startFork(e),l.startFork(t)},l2(o.id(),l.id()))})),wS=(e,t,r,n,i)=>{lU(!0,!1)(n)&&i(r(e,t))},w_=U(2,(e,t)=>pY(r=>pC(r(e),{onFailure:e=>pC(t,{onFailure:t=>pg(ho(e,t)),onSuccess:()=>pg(e)}),onSuccess:e=>h9(t,e)}))),ww=(e,t,r)=>py(n=>pv(pv(_0(pN(e)),e=>pi(n=>{let i=t.map(e=>e.listeners.count),s=()=>{i.every(e=>0===e)&&t.every(e=>"Pending"===e.result.state.current._tag||!!("Done"===e.result.state.current._tag&&ds(e.result.state.current.effect)&&"Failure"===e.result.state.current.effect._tag&&hp(e.result.state.current.effect.cause)))&&(a.forEach(e=>e()),r?.(),n(p9(e)))};e.addObserver(e=>{a.forEach(e=>e()),n(e)});let a=t.map((e,t)=>{let r=e=>{i[t]=e,s()};return e.listeners.addObserver(r),()=>e.listeners.removeObserver(r)});return s(),pH(()=>{a.forEach(e=>e())})})),()=>pJ(()=>pI(t.flatMap(e=>e.state.completed?[]:[e]),e=>S6(e.request,db(n))))));class wk{constructor(e){this.waiters=new Set,this.taken=0,this.take=e=>pn(t=>{if(this.free<e){let r=()=>{this.free<e||(this.waiters.delete(r),this.taken+=e,t(pB(e)))};return this.waiters.add(r),pH(()=>{this.waiters.delete(r)})}return this.taken+=e,t(pB(e))}),this.updateTaken=e=>h8(t=>(this.taken=e(this.taken),this.waiters.size>0&&t.getFiberRef(ya).scheduleTask(()=>{let e=this.waiters.values(),t=e.next();for(;!1===t.done&&this.free>0;)t.value(),t=e.next()},t.getFiberRef(fF)),pB(this.free))),this.release=e=>this.updateTaken(t=>t-e),this.releaseAll=this.updateTaken(e=>0),this.withPermits=e=>t=>pY(r=>pv(r(this.take(e)),e=>w_(r(t),this.release(e)))),this.withPermitsIfAvailable=e=>t=>pY(r=>pJ(()=>this.free<e?bz:(this.taken+=e,w_(r(bl(t)),this.release(e))))),this.permits=e}get free(){return this.permits-this.taken}}let wC=e=>new wk(e);class wE extends t4{constructor(e){super(),this.waiters=[],this.scheduled=!1,this.flushWaiters=()=>{this.scheduled=!1;let e=this.waiters;this.waiters=[];for(let t=0;t<e.length;t++)e[t](d_)},this.open=h8(e=>this.isOpen?pZ:(this.isOpen=!0,this.unsafeSchedule(e))),this.release=h8(e=>this.isOpen?pZ:this.unsafeSchedule(e)),this.await=pn(e=>this.isOpen?e(pZ):(this.waiters.push(e),pH(()=>{let t=this.waiters.indexOf(e);-1!==t&&this.waiters.splice(t,1)}))),this.close=pH(()=>{this.isOpen=!1}),this.whenOpen=e=>p6(this.await,e),this.isOpen=e}commit(){return this.await}unsafeSchedule(e){return this.scheduled||0===this.waiters.length||(this.scheduled=!0,e.currentScheduler.scheduleTask(this.flushWaiters,e.getFiberRef(fF))),pZ}unsafeOpen(){this.isOpen||(this.isOpen=!0,this.flushWaiters())}unsafeClose(){this.isOpen=!1}}(e,t)=>{let r=oc(t);return pv(dP(),t=>pj(wP(iC()),n=>[dU(wI(e,r,n),t),wF(n)]))};let wO=(e,t,r)=>{let n=oO(oc(t));return W(dI(),pK(t=>pA(e,t)),pj(e=>tR([r+n,e])))},wI=(e,t,r)=>pY(n=>W(ge(e=>e.currentTimeMillis),pv(n=>wU(r,r=>{switch(r._tag){case"None":return tR(wO(e,t,n));case"Some":{let[i]=r.value;return i-n<=0?tR(wO(e,t,n)):iC()}}})),pv(e=>tI(e)?ph("BUG: Effect.cachedInvalidate - please report an issue at https://github.com/Effect-TS/effect/issues"):n(dT(e.value[1]))))),wF=e=>gX(e,iC()),wT=((e,t)=>pv(_s,r=>W(wA(e,r),w_(pv(r.value,t)))),U(2,(e,t)=>h8((r,n)=>{let i=_1(e,r,n.runtimeFlags,yV);if("Open"===t.state._tag){let e={};t.state.finalizers.set(e,()=>py(e=>eJ(e,i.id())?pZ:pe(p9(i)))),i.addObserver(()=>{"Closed"!==t.state._tag&&t.state.finalizers.delete(e)})}else i.unsafeInterruptAsFork(r.id());return pB(i)}))),wR=e=>_7(t=>wT(e,t)),wN=U(2,(e,t)=>W(pf(e),wy(pf(t)),e=>pw(e))),wA=U(2,(e,t)=>f_(wb,e=>e.zip(t))(e)),wj=((e,{duration:t,onTimeout:r})=>pw(wM(e,{onTimeout:()=>pm(r),onSuccess:pB,duration:t})),U(2,(e,{duration:t,onTimeout:r})=>pw(wM(e,{onTimeout:()=>pb(r),onSuccess:pB,duration:t})))),wM=U(2,(e,{duration:t,onSuccess:r,onTimeout:n})=>py(i=>pY(s=>wv(s(e),pN(gO(t)),{onSelfWin:(e,t)=>pv(e.await,n=>"Success"===n._tag?pv(e.inheritAll,()=>h9(fe(t,i),r(n.value))):pv(fe(t,i),()=>dd(n.cause))),onOtherWin:(e,t)=>pv(e.await,r=>"Success"===r._tag?pv(e.inheritAll,()=>h9(fe(t,i),n())):pv(fe(t,i),()=>dd(r.cause))),otherScope:yV})))),wz=Symbol.for("effect/Ref/SynchronizedRef"),wD={_A:e=>e};class w$ extends t4{static{o=gW,l=gK}constructor(e,t){super(),this[wz]=wD,this[o]=gV,this[l]=gK,this.ref=e,this.withLock=t,this.get=gQ(this.ref)}commit(){return this.get}modify(e){return this.modifyEffect(t=>pB(e(t)))}modifyEffect(e){return this.withLock(W(pv(gQ(this.ref),e),pv(([e,t])=>h9(gX(this.ref,t),e))))}}let wP=e=>pH(()=>wL(e)),wL=e=>new w$(gY(e),wC(1).withPermits(1)),wU=U(2,(e,t)=>e.modifyEffect(e=>{let r=t(e);switch(r._tag){case"None":return pB([e,e]);case"Some":return pj(r.value,e=>[e,e])}})),wq=((e,t,r)=>({...tx,commit(){return y1(this)},[yG]:yY,id:()=>W(e.id(),l3(t.id())),await:W(e.await,pw,ws(pw(t.await),r,{concurrent:!0}),pf),children:e.children,inheritAll:p6(t.inheritAll,e.inheritAll),poll:p8(e.poll,t.poll,(e,t)=>W(e,iz(e=>W(t,iM(t=>dC(e,t,{onSuccess:r,onFailure:ha})))))),interruptAsFork:r=>p6(e.interruptAsFork(r),t.interruptAsFork(r)),pipe(){return e6(this,arguments)}}),Symbol.for("effect/ScheduleInterval")),wB={[wq]:wq,startMillis:0,endMillis:0},wJ=(e,t)=>e>t?wB:{[wq]:wq,startMillis:e,endMillis:t},wH=U(2,(e,t)=>wK(e,t)===e),wK=U(2,(e,t)=>e.endMillis<=t.startMillis?e:t.endMillis<=e.startMillis?t:e.startMillis<t.startMillis?e:t.startMillis<e.startMillis?t:e.endMillis<=t.endMillis?e:t),wW=U(2,(e,t)=>wJ(Math.max(e.startMillis,t.startMillis),Math.min(e.endMillis,t.endMillis))),wV=e=>e.startMillis>=e.endMillis,wG=e=>oS(e.endMillis-e.startMillis),wY=e=>wJ(e,Number.POSITIVE_INFINITY),wZ=Symbol.for("effect/ScheduleIntervals"),wQ=e=>({[wZ]:wZ,intervals:e}),wX=U(2,(e,t)=>aM(t.intervals)?aM(e.intervals)?aD(e.intervals).startMillis<aD(t.intervals).startMillis?w0(aq(e.intervals),t.intervals,aD(e.intervals),ap()):w0(e.intervals,aq(t.intervals),aD(t.intervals),ap()):t:e),w0=(e,t,r,n)=>{let i=e,s=t,a=r,o=n;for(;aM(i)||aM(s);)if(!aM(i)&&aM(s))a.endMillis<aD(s).startMillis?(o=W(o,ak(a)),a=aD(s)):a=wJ(a.startMillis,aD(s).endMillis),s=aq(s),i=ap();else if(aM(i)&&aj(s))a.endMillis<aD(i).startMillis?(o=W(o,ak(a)),a=aD(i)):a=wJ(a.startMillis,aD(i).endMillis),s=ap(),i=aq(i);else if(aM(i)&&aM(s))aD(i).startMillis<aD(s).startMillis?(a.endMillis<aD(i).startMillis?(o=W(o,ak(a)),a=aD(i)):a=wJ(a.startMillis,aD(i).endMillis),i=aq(i)):(a.endMillis<aD(s).startMillis?(o=W(o,ak(a)),a=aD(s)):a=wJ(a.startMillis,aD(s).endMillis),s=aq(s));else throw Error(ex("Intervals.unionLoop"));return wQ(W(o,ak(a),ax))},w1=U(2,(e,t)=>w2(e.intervals,t.intervals,ap())),w2=(e,t,r)=>{let n=e,i=t,s=r;for(;aM(n)&&aM(i);){let e=W(aD(n),wW(aD(i))),t=wV(e)?s:W(s,ak(e));W(aD(n),wH(aD(i)))?n=aq(n):i=aq(i),s=t}return wQ(ax(s))},w3=e=>W(e.intervals,az,iO(()=>wB)).startMillis,w5=U(2,(e,t)=>w3(e)<w3(t)),w4=((e,t)=>w5(e,t)?t:e,e=>W(e.intervals,az,iO(()=>wB)).endMillis),w6=e=>aM(e.intervals),w8="Continue",w7="Done",w9=e=>({_tag:w8,intervals:e}),ke=e=>({_tag:w8,intervals:wQ(ad(e))}),kt={_tag:w7},kr=e=>e._tag===w8,kn=e=>e._tag===w7,ki=Symbol.for("effect/ManagedRuntime"),ks="Fresh",ka="ProvideMerge",ko=e=>e.inheritAll,kl=e=>e.poll,kc=e=>function(){if(1==arguments.length){let t=arguments[0];return(r,...n)=>e(t,r,...n)}return e.apply(this,arguments)},ku=kc((e,t,r)=>{let n=cn(),i=[[fI,[[n,e.context]]]];r?.scheduler&&i.push([ya,[[n,r.scheduler]]]);let s=gL(e.fiberRefs,{entries:i,forkAs:n});r?.updateRefs&&(s=r.updateRefs(s,n));let a=new _A(n,s,e.runtimeFlags),o=t;r?.scope&&(o=pv(fY(r.scope,xW),e=>p6(fW(e,py(e=>eJ(e,a.id())?pZ:fe(a,e))),pD(t,t=>fG(e,t)))));let l=a.currentSupervisor;return l!==_o&&(l.onStart(e.context,o,iC(),a),a.addObserver(e=>l.onEnd(e,a))),yV.add(e.runtimeFlags,a),r?.immediate===!1?a.resume(o):a.start(o),a}),kh=kc((e,t,r={})=>{let n=ku(e,t,r);return r.onExit&&n.addObserver(e=>{r.onExit(e)}),(t,r)=>kh(e)(W(n,fe(t??l7)),{...r,onExit:r?.onExit?e=>r.onExit(yM(e)):void 0})}),kp=kc((e,t)=>{let r=kv(e)(t);if("Failure"===r._tag)throw kx(r.effect_instruction_i0);return r.effect_instruction_i0});class kf extends Error{constructor(e){super(`Fiber #${e.id().id} cannot be resolved synchronously. This is caused by using runSync on an effect that performs async work`),this._tag="AsyncFiberException",this.fiber=e,this.name=this._tag,this.stack=this.message}}let kd=e=>{let t=Error.stackTraceLimit;Error.stackTraceLimit=0;let r=new kf(e);return Error.stackTraceLimit=t,r},km=Symbol.for("effect/Runtime/FiberFailure"),kg=Symbol.for("effect/Runtime/FiberFailure/Cause");class kb extends Error{constructor(e){let t=hH(e)[0];super(t?.message||"An error has occurred"),this[km]=km,this[kg]=e,this.name=t?`(FiberFailure) ${t.name}`:"FiberFailure",t?.stack&&(this.stack=t.stack)}toJSON(){return{_id:"FiberFailure",cause:this[kg].toJSON()}}toString(){return"(FiberFailure) "+hD(this[kg],{renderErrorCause:!0})}[eV](){return this.toString()}}let kx=e=>{let t=Error.stackTraceLimit;Error.stackTraceLimit=0;let r=new kb(e);return Error.stackTraceLimit=t,r},ky=e=>{switch(e._op){case"Failure":case"Success":return e;case"Left":return df(e.left);case"Right":return dS(e.right);case"Some":return dS(e.value);case"None":return df(f7())}},kv=kc((e,t)=>{let r=ky(t);if(r)return r;let n=new ys,i=ku(e)(t,{scheduler:n});n.flush();let s=i.unsafePoll();return s||dp(pc(kd(i),dJ(i)))}),kS=kc((e,t,r)=>k_(e,t,r).then(e=>{switch(e._tag){case tn:return e.effect_instruction_i0;case e9:throw kx(e.effect_instruction_i0)}})),k_=kc((e,t,r)=>new Promise(n=>{let i=ky(t);i&&n(i);let s=ku(e)(t);s.addObserver(e=>{n(e)}),r?.signal!==void 0&&(r.signal.aborted?s.unsafeInterruptAsFork(s.id()):r.signal.addEventListener("abort",()=>{s.unsafeInterruptAsFork(s.id())},{once:!0}))}));class kw{constructor(e,t,r){this.context=e,this.runtimeFlags=t,this.fiberRefs=r}pipe(){return e6(this,arguments)}}let kk=e=>new kw(e.context,e.runtimeFlags,e.fiberRefs),kC=()=>h8((e,t)=>pB(new kw(e.getFiberRef(fI),t.runtimeFlags,e.getFiberRefs()))),kE=uG(1,32,4),kO=kk({context:t0(),runtimeFlags:kE,fiberRefs:gI()}),kI=((e,t)=>kk({context:e.context,runtimeFlags:t(e.runtimeFlags),fiberRefs:e.fiberRefs}),ku(kO)),kF=kS(kO),kT=k_(kO),kR=kp(kO),kN=kv(kO),kA=U(2,(e,t)=>e.modifyEffect(t)),kj=Symbol.for("effect/Layer"),kM={[kj]:{_RIn:e=>e,_E:e=>e,_ROut:e=>e},pipe(){return e6(this,arguments)}},kz=Symbol.for("effect/Layer/MemoMap"),kD=t2()("effect/Layer/CurrentMemoMap",{defaultValue:()=>kq()}),k$=e=>ec(e,kj),kP=e=>e._op_layer===ks;class kL{constructor(e){this.ref=e,this[kz]=kz}getOrElseMemoize(e,t){return W(kA(this.ref,r=>{let n=r.get(e);if(void 0!==n){let[e,i]=n;return pB([W(e,pv(([e,t])=>W(bN(e),h9(t))),pD(dy({onFailure:()=>pZ,onSuccess:()=>fV(t,i)}))),r])}return W(gZ(0),pv(n=>W(dI(),pv(i=>W(gZ(()=>pZ),pj(s=>{let a=pY(a=>W(wh(),pv(o=>W(a(pv(kH(e,o,!0),e=>bf(e(this)))),pf,pv(a=>{switch(a._tag){case e9:return W(dA(i,a.effect_instruction_i0),p6(fG(o,a)),p6(pg(a.effect_instruction_i0)));case tn:return W(gX(s,e=>W(fG(o,e),pX(g1(n,e=>[1===e,e-1])),pe)),p6(g2(n,e=>e+1)),p6(fV(t,t=>W(pH(()=>r.delete(e)),p6(gQ(s)),pv(e=>e(t))))),p6(dM(i,a.effect_instruction_i0)),h9(a.effect_instruction_i0[1]))}}))))),o=[W(dT(i),pD(dv({onFailure:()=>pZ,onSuccess:()=>g2(n,e=>e+1)}))),e=>W(gQ(s),pv(t=>t(e)))];return[a,kP(e)?r:r.set(e,o)]}))))))}),pw)}}let kU=pJ(()=>pj(wP(new Map),e=>new kL(e))),kq=()=>new kL(wL(new Map)),kB=U(2,(e,t)=>pv(kU,r=>kJ(e,r,t))),kJ=U(3,(e,t,r)=>pv(kH(e,r),e=>bA(e(t),kD,t))),kH=(e,t,r=!1)=>{switch(e._op_layer){case"Locally":return pH(()=>r=>e.f(r.getOrElseMemoize(e.self,t)));case"ExtendScope":return pH(()=>t=>_7(r=>t.getOrElseMemoize(e.layer,r)));case"Fold":return pH(()=>r=>W(r.getOrElseMemoize(e.layer,t),pC({onFailure:n=>r.getOrElseMemoize(e.failureK(n),t),onSuccess:n=>r.getOrElseMemoize(e.successK(n),t)})));case"Fresh":return pH(()=>r=>W(e.layer,kB(t)));case"FromEffect":return r?pH(()=>t=>e.effect):pH(()=>r=>r.getOrElseMemoize(e,t));case"Provide":return pH(()=>r=>W(r.getOrElseMemoize(e.first,t),pv(n=>W(r.getOrElseMemoize(e.second,t),dU(n)))));case"Scoped":return r?pH(()=>r=>wp(e.effect,t)):pH(()=>r=>r.getOrElseMemoize(e,t));case"Suspend":return pH(()=>r=>r.getOrElseMemoize(e.evaluate(),t));case"ProvideMerge":return pH(()=>r=>W(r.getOrElseMemoize(e.first,t),p8(r.getOrElseMemoize(e.second,t),e.zipK)));case"ZipWith":return pH(()=>r=>W(r.getOrElseMemoize(e.first,t),ws(r.getOrElseMemoize(e.second,t),e.zipK,{concurrent:!0})))}},kK=U(2,(e,t)=>k3(e,{onFailure:t,onSuccess:Ct})),kW=((e,t)=>k2(e,{onFailure:t,onSuccess:Ct}),()=>k0(dP())),kV=e=>kG(hn(e)),kG=e=>k0(pg(e)),kY=e=>k0(pb(e)),kZ=U(2,(e,t)=>k3(e,{onFailure:kV,onSuccess:t})),kQ=e=>{let t=Object.create(kM);return t._op_layer=ks,t.layer=e,t},kX=U(2,(e,t)=>{let r=tX(e),n=r?e:t;return k0(pj(r?t:e,e=>tJ(n,e)))});function k0(e){let t=Object.create(kM);return t._op_layer="FromEffect",t.effect=e,t}let k1=U(2,(e,t)=>{let r=Object.create(kM);return r._op_layer="Locally",r.self=e,r.f=t,r});(e,t)=>kZ(e,e=>Ct(t(e)));let k2=U(2,(e,{onFailure:t,onSuccess:r})=>{let n=Object.create(kM);return n._op_layer="Fold",n.layer=e,n.failureK=t,n.successK=r,n}),k3=U(2,(e,{onFailure:t,onSuccess:r})=>k2(e,{onFailure:e=>{let r=hx(e);switch(r._tag){case"Left":return t(r.left);case"Right":return kG(r.right)}},onSuccess:r})),k5=U(2,(e,t)=>Ca(e,t,(e,t)=>tZ(e,t))),k4=(...e)=>{let t=e[0];for(let r=1;r<e.length;r++)t=k5(t,e[r]);return t},k6=(e,t,r,n)=>kX(t,W(gt,pv(t=>W(e.step(t,r,n),pv(([e,n,i])=>kn(i)?pd(r):W(gO(oS(w3(i.intervals)-t)),h9({state:e}))))))),k8=U(2,(e,t)=>{let r=tX(e),n=r?e:t;return k9(pj(r?t:e,e=>tJ(n,e)))}),k7=e=>k9(W(e,h9(t0()))),k9=e=>{let t=Object.create(kM);return t._op_layer="Scoped",t.effect=e,t},Ce=U(2,(e,t)=>{let r=tX(e);return k0(pB(tJ(r?e:t,r?t:e)))}),Ct=e=>k0(pB(e)),Cr=e=>{let t=Object.create(kM);return t._op_layer="Suspend",t.evaluate=e,t},Cn=U(2,(e,t)=>pv(_7(r=>kJ(e,t,r)),e=>W(kC(),dU(e)))),Ci=U(2,(e,t)=>Cr(()=>{let r=Object.create(kM);return r._op_layer="Provide",r.first=Object.create(kM,{_op_layer:{value:ka,enumerable:!0},first:{value:kW(),enumerable:!0},second:{value:Array.isArray(t)?k4(...t):t},zipK:{value:(e,t)=>W(e,tZ(t))}}),r.second=e,r})),Cs=U(2,(e,t)=>{let r=Object.create(kM);return r._op_layer=ka,r.first=t,r.second=Ci(e,t),r.zipK=(e,t)=>W(e,tZ(t)),r}),Ca=U(3,(e,t,r)=>Cr(()=>{let n=Object.create(kM);return n._op_layer="ZipWith",n.first=e,n.second=t,n.zipK=r,n})),Co=e=>{let t=tQ("effect/Layer/unwrapEffect/Layer.Layer<R1, E1, A>");return kZ(kX(t,e),e=>tG(e,t))},Cl=e=>{let t=tQ("effect/Layer/unwrapScoped/Layer.Layer<R1, E1, A>");return kZ(k8(t,e),e=>tG(e,t))},Cc=U(2,(e,t)=>_9(r=>pv(kB(t,r),t=>dq(e,t)))),Cu=U(2,(e,t)=>{let r=g9(kO.fiberRefs,t.fiberRefs),n=uX(kO.runtimeFlags,t.runtimeFlags);return pY(i=>h8(s=>{let a=s.getFiberRef(fI),o=s.getFiberRefs(),l=bt(s.id(),o)(r),c=s.currentRuntimeFlags,u=u0(n)(c),h=g9(l,o),p=uX(u,c);return s.setFiberRefs(l),s.currentRuntimeFlags=u,w_(dq(i(e),tZ(a,t.context)),h8(e=>(e.setFiberRefs(bt(e.id(),e.getFiberRefs())(h)),e.currentRuntimeFlags=u0(p)(e.currentRuntimeFlags),pZ)))}))}),Ch=U(2,(e,t)=>Array.isArray(t)?Cc(e,k4(...t)):k$(t)?Cc(e,t):tU(t)?dq(e,t):ki in t?pv(t.runtimeEffect,t=>Cu(e,t)):Cu(e,t)),Cp=e=>e.toUpperCase(),Cf=e=>e.toLowerCase();class Cd{constructor(e,t=!1){this.s=e,this.stripped=t,this.index=0,this.length=e.length}next(){if(this.done)return{done:!0,value:void 0};let e=this.index;for(;!this.done&&!Cm(this.s[this.index]);)this.index=this.index+1;let t=this.index;if(!this.done){let e=this.s[this.index];this.index=this.index+1,!this.done&&Cg(e,this.s[this.index])&&(this.index=this.index+1),this.stripped||(t=this.index)}return{done:!1,value:this.s.substring(e,t)}}[Symbol.iterator](){return new Cd(this.s,this.stripped)}get done(){return this.index>=this.length}}let Cm=e=>{let t=e.charCodeAt(0);return 13===t||10===t},Cg=(e,t)=>13===e.charCodeAt(0)&&10===t.charCodeAt(0),Cb=((e,t)=>Cx(e.seconds,t.seconds)&&Cx(e.minutes,t.minutes)&&Cx(e.hours,t.hours)&&Cx(e.days,t.days)&&Cx(e.months,t.months)&&Cx(e.weekdays,t.weekdays),n0(nZ)),Cx=(e,t)=>Cb(i6(e),i6(t)),Cy=Symbol.for("effect/Schedule"),Cv=e=>ec(e,Cy),CS=Symbol.for("effect/ScheduleDriver"),C_={start:0,now:0,input:void 0,elapsed:ob,elapsedSincePrevious:ob,recurrence:0},Cw=t2()("effect/Schedule/CurrentIterationMetadata",{defaultValue:()=>C_}),Ck={_Out:e=>e,_In:e=>e,_R:e=>e},CC={_Out:e=>e,_In:e=>e,_R:e=>e};class CE{constructor(e,t){this[Cy]=Ck,this.initial=e,this.step=t}pipe(){return e6(this,arguments)}}let CO=(e,t,r)=>g2(e,e=>0===e.recurrence?{now:t,input:r,recurrence:e.recurrence+1,elapsed:ob,elapsedSincePrevious:ob,start:t}:{now:t,input:r,recurrence:e.recurrence+1,elapsed:oS(t-e.start),elapsedSincePrevious:oS(t-e.now),start:e.start});class CI{constructor(e,t){this[CS]=CC,this.iterationMeta=gY(C_),this.schedule=e,this.ref=t}get state(){return pj(gQ(this.ref),e=>e[1])}get last(){return pv(gQ(this.ref),([e,t])=>{switch(e._tag){case"None":return pm(()=>new f7);case"Some":return pB(e.value)}})}get reset(){return gX(this.ref,[iC(),this.schedule.initial]).pipe(p4(gX(this.iterationMeta,C_)))}next(e){return W(pj(gQ(this.ref),e=>e[1]),pv(t=>W(gt,pv(r=>W(pJ(()=>this.schedule.step(r,e,t)),pv(([t,n,i])=>{let s=gX(this.ref,[tR(n),t]);if(kn(i))return s.pipe(p6(pd(iC())));let a=w3(i.intervals)-r;if(a<=0)return s.pipe(p6(CO(this.iterationMeta,r,e)),h9(n));let o=oS(a);return W(s,p6(CO(this.iterationMeta,r,e)),p6(gO(o)),h9(n))}))))))}}let CF=(e,t)=>new CE(e,t),CT=U(2,(e,t)=>CR(e,e=>pH(()=>t(e)))),CR=U(2,(e,t)=>CU(e,(e,r)=>pj(t(e),e=>oj(r,oc(e))))),CN=((e,t)=>CF([e.initial,t.initial,!0],(r,n,i)=>i[2]?pv(e.step(r,n,i[0]),([e,s,a])=>kn(a)?pj(t.step(r,n,i[1]),([t,r,n])=>[[e,t,!1],nk(r),n]):pB([[e,i[1],!0],nw(s),a])):pj(t.step(r,n,i[1]),([e,t,r])=>[[i[0],e,!1],nk(t),r])),U(2,(e,t)=>CA(e,(e,r)=>pH(()=>t(e,r))))),CA=U(2,(e,t)=>CF(e.initial,(r,n,i)=>pv(e.step(r,n,i),([e,r,i])=>kn(i)?pB([e,r,kt]):pj(t(n,r),t=>t?[e,r,i]:[e,r,kt])))),Cj=((e,t)=>Cj(e,e=>pH(()=>t(e))),U(2,(e,t)=>CF(e.initial,(r,n,i)=>pv(t(n),t=>e.step(r,t,i))))),CM=((e,t)=>CU(e,(e,r)=>t(r)),e=>W(gZ([iC(),e.initial]),pj(t=>new CI(e,t)))),Cz=U(2,(e,t)=>CD(e,t,w1)),CD=U(3,(e,t,r)=>CF([e.initial,t.initial],(n,i,s)=>W(p8(e.step(n,i,s[0]),t.step(n,i,s[1]),(e,t)=>[e,t]),pv(([[n,s,a],[o,l,c]])=>kr(a)&&kr(c)?C$(e,t,i,n,s,a.intervals,o,l,c.intervals,r):pB([[n,o],[s,l],kt]))))),C$=(e,t,r,n,i,s,a,o,l,c)=>{let u=c(s,l);return w6(u)?pB([[n,a],[i,o],w9(u)]):W(s,w5(l))?pv(e.step(w4(s),r,n),([n,i,s])=>kn(s)?pB([[n,a],[i,o],kt]):C$(e,t,r,n,i,s.intervals,a,o,l,c)):pv(t.step(w4(l),r,a),([a,o,l])=>kn(l)?pB([[n,a],[i,o],kt]):C$(e,t,r,n,i,s,a,o,l.intervals,c))},CP=U(2,(e,t)=>CL(e,e=>pH(()=>t(e)))),CL=U(2,(e,t)=>CF(e.initial,(r,n,i)=>pv(e.step(r,n,i),([e,r,n])=>pj(t(r),t=>[e,t,n])))),CU=U(2,(e,t)=>CF(e.initial,(r,n,i)=>pv(e.step(r,n,i),([e,n,i])=>{if(kn(i))return pB([e,n,i]);let s=i.intervals;return pj(t(n,wG(wJ(r,w3(s)))),t=>{let i=oc(t),a=w3(s),o=r+oO(i),l=o-a;return[e,n,ke(wJ(o,Math.max(0,w4(s)+l)))]})}))),Cq=e=>CF(e.initial,(t,r,n)=>W(e.step(t,r,n),pj(([e,t,n])=>[e,r,n]))),CB=e=>CW(Et,t=>t<e),CJ=((e,t,r)=>CF([e.initial,t],(t,n,[i,s])=>pv(e.step(t,n,i),([e,t,n])=>kn(n)?pB([[e,s],s,n]):pj(r(s,t),t=>[[e,t],s,n]))),U(3,(e,t,r)=>CF([e.initial,t.initial],(n,i,s)=>p8(e.step(n,i,s[0]),t.step(n,i,s[1]),([e,t,n],[i,s,a])=>{if(kn(n)&&kn(a))return[[e,i],[t,s],kt];if(kn(n)&&kr(a))return[[e,i],[t,s],w9(a.intervals)];if(kr(n)&&kn(a))return[[e,i],[t,s],w9(n.intervals)];if(kr(n)&&kr(a))return[[e,i],[t,s],w9(r(n.intervals,a.intervals))];throw Error("BUG: Schedule.unionWith - please report an issue at https://github.com/Effect-TS/effect/issues")})))),CH=U(2,(e,t)=>CA(e,(e,r)=>bT(t(e)))),CK=U(2,(e,t)=>CA(e,(e,r)=>t(e))),CW=U(2,(e,t)=>CN(e,(e,r)=>t(r))),CV=((e,t)=>CP(Cz(e,t),e=>e[0]),Symbol.for("effect/Schedule/ScheduleDefect"));class CG{constructor(e){this.error=e,this[CV]=CV}}let CY=e=>ec(e,CV),CZ=e=>pa(e,e=>pu(new CG(e))),CQ=e=>iE(hF(e,e=>hu(e)&&CY(e.defect)?tR(e.defect):iC()),{onNone:()=>e,onSome:e=>hn(e.error)}),CX=e=>ps(e,e=>pg(CQ(e))),C0=U(2,(e,t)=>C2(e,t,(e,t)=>pd(e))),C1=U(2,(e,t)=>{if(Cv(t))return C0(e,t);let r=t.schedule??Cq(Et),n=t.while?CK(r,e=>{let r=t.while(e);return"boolean"==typeof r?pB(r):CZ(r)}):r,i=t.until?CH(n,e=>{let r=t.until(e);return"boolean"==typeof r?pB(r):CZ(r)}):n;return CX(C0(e,t.times?Cz(i,CB(t.times)).pipe(CP(e=>e[0])):i))}),C2=U(3,(e,t,r)=>pv(CM(t),t=>pE(e,{onFailure:e=>r(e,iC()),onSuccess:n=>C3(bj(e,Cw,gQ(t.iterationMeta)),t,(e,n)=>bj(r(e,n),Cw,gQ(t.iterationMeta)),n)}))),C3=(e,t,r,n)=>pE(t.next(n),{onFailure:()=>pL(t.last),onSuccess:n=>pE(e,{onFailure:e=>r(e,tR(n)),onSuccess:n=>C3(e,t,r,n)})}),C5=U(2,(e,t)=>C6(e,t,(e,t)=>pd(e))),C4=((e,t)=>Cv(t)?C5(e,t):CX(C5(e,C4(t))),e=>{let t=e.schedule??Et,r=e.while?CK(t,t=>{let r=e.while(t);return"boolean"==typeof r?pB(r):CZ(r)}):t,n=e.until?CH(r,t=>{let r=e.until(t);return"boolean"==typeof r?pB(r):CZ(r)}):r;return e.times?Cz(n,CB(e.times)):n}),C6=U(3,(e,t,r)=>pv(CM(t),t=>C8(bj(e,Cw,gQ(t.iterationMeta)),t,(e,n)=>bj(r(e,n),Cw,gQ(t.iterationMeta))))),C8=(e,t,r)=>pa(e,n=>pE(t.next(n),{onFailure:()=>W(t.last,pL,pv(e=>r(n,e))),onSuccess:()=>C8(e,t,r)})),C7=((e,t)=>C7(e,void 0,t),U(3,(e,t,r)=>pv(CM(r),r=>C9(bj(e,Cw,gQ(r.iterationMeta)),t,r)))),C9=(e,t,r)=>pE(r.next(t),{onFailure:()=>pL(r.last),onSuccess:()=>pv(e,t=>C9(e,t,r))}),Ee=CF(iC(),(e,t,r)=>{switch(r._tag){case"None":return pB([tR(e),ob,ke(wY(e))]);case"Some":return pB([tR(r.value),oS(e-r.value),ke(wY(e))])}}),Et=(S=e=>e+1,CF(0,(e,t,r)=>pH(()=>[S(r),r,ke(wY(e))]))),Er=CP(CB(1),K),En=Symbol.for("effect/MutableList"),Ei={[En]:En,[Symbol.iterator](){let e=!1,t=this.head;return{next(){if(e)return this.return();if(null==t)return e=!0,this.return();let r=t.value;return t=t.next,{done:e,value:r}},return:t=>(e||(e=!0),{done:!0,value:t})}},toString(){return eY(this.toJSON())},toJSON(){return{_id:"MutableList",values:Array.from(this).map(eG)}},[eV](){return this.toJSON()},pipe(){return e6(this,arguments)}},Es=e=>({value:e,removed:!1,prev:void 0,next:void 0}),Ea=()=>{let e=Object.create(Ei);return e.head=void 0,e.tail=void 0,e._length=0,e},Eo=e=>0===El(e),El=e=>e._length,Ec=U(2,(e,t)=>{let r=Es(t);return void 0===e.head&&(e.head=r),void 0===e.tail||(e.tail.next=r,r.prev=e.tail),e.tail=r,e._length+=1,e}),Eu=e=>{let t=e.head;if(void 0!==t)return Eh(e,t),t.value},Eh=(e,t)=>{!t.removed&&(t.removed=!0,void 0!==t.prev&&void 0!==t.next?(t.prev.next=t.next,t.next.prev=t.prev):void 0!==t.prev?(e.tail=t.prev,t.prev.next=void 0):void 0!==t.next?(e.head=t.next,t.next.prev=void 0):(e.tail=void 0,e.head=void 0),e._length>0&&(e._length-=1))},Ep=Symbol.for("effect/MutableQueue"),Ef=Symbol.for("effect/mutable/MutableQueue/Empty"),Ed={[Ep]:Ep,[Symbol.iterator](){return Array.from(this.queue)[Symbol.iterator]()},toString(){return eY(this.toJSON())},toJSON(){return{_id:"MutableQueue",values:Array.from(this).map(eG)}},[eV](){return this.toJSON()},pipe(){return e6(this,arguments)}},Em=e=>{let t=Object.create(Ed);return t.queue=Ea(),t.capacity=e,t},Eg=e=>Em(e),Eb=()=>Em(void 0),Ex=e=>El(e.queue),Ey=e=>Eo(e.queue),Ev=e=>void 0===e.capacity?1/0:e.capacity,ES=U(2,(e,t)=>{let r=El(e.queue);return(void 0===e.capacity||r!==e.capacity)&&(Ec(t)(e.queue),!0)}),E_=U(2,(e,t)=>{let r,n=t[Symbol.iterator](),i=ap(),s=!0;for(;s&&(r=n.next())&&!r.done;)s=ES(r.value)(e);for(;null!=r&&!r.done;)i=ak(r.value)(i),r=n.next();return ax(i)}),Ew=U(2,(e,t)=>Eo(e.queue)?t:Eu(e.queue)),Ek=U(2,(e,t)=>{let r=ap(),n=0;for(;n<t;){let t=Ew(Ef)(e);if(t===Ef)break;r=ak(t)(r),n+=1}return ax(r)}),EC=(e,t,r,n)=>cC({_tag:"Complete",key:e,exit:t,entryStats:r,timeToLiveMillis:n}),EE=(e,t)=>cC({_tag:"Pending",key:e,deferred:t}),EO=(e,t)=>cC({_tag:"Refreshing",deferred:e,complete:t}),EI=Symbol.for("effect/Cache/MapKey");class EF{constructor(e){this[EI]=EI,this.previous=void 0,this.next=void 0,this.current=e}[eR](){return W(eN(this.current),ej(eN(this.previous)),ej(eN(this.next)),eq(this))}[eB](e){return this===e||ER(e)&&eJ(this.current,e.current)&&eJ(this.previous,e.previous)&&eJ(this.next,e.next)}}let ET=e=>new EF(e),ER=e=>ec(e,EI);class EN{add(e){if(e!==this.tail)if(void 0===this.tail)this.head=e,this.tail=e;else{let t=e.previous,r=e.next;void 0!==r&&(e.next=void 0,void 0!==t?(t.next=r,r.previous=t):(this.head=r,this.head.previous=void 0)),this.tail.next=e,e.previous=this.tail,this.tail=e}}remove(){let e=this.head;if(void 0!==e){let t=e.next;void 0!==t?(e.next=void 0,this.head=t,this.head.previous=void 0):(this.head=void 0,this.tail=void 0)}return e}constructor(){this.head=void 0,this.tail=void 0}}let EA=()=>new EN,Ej=(e,t,r,n,i,s)=>({map:e,keys:t,accesses:r,updating:n,hits:i,misses:s}),EM=()=>Ej(v2(),EA(),Eb(),lL(!1),0,0),Ez=Symbol.for("effect/Cache"),ED={_Key:e=>e,_Error:e=>e,_Value:e=>e},E$=Symbol.for("effect/ConsumerCache"),EP={_Key:e=>e,_Error:e=>e,_Value:e=>e},EL=e=>e,EU=e=>({loadedMillis:e});class Eq{constructor(e,t,r,n,i){this[Ez]=ED,this[E$]=EP,this.capacity=e,this.context=t,this.fiberId=r,this.lookup=n,this.timeToLive=i,this.cacheState=EM()}get(e){return pj(this.getEither(e),nT)}get cacheStats(){return pH(()=>EL({hits:this.cacheState.hits,misses:this.cacheState.misses,size:v9(this.cacheState.map)}))}getOption(e){return pJ(()=>iE(v3(this.cacheState.map,e),{onNone:()=>{let t=ET(e);return this.trackAccess(t),this.trackMiss(),pB(iC())},onSome:e=>this.resolveMapValue(e)}))}getOptionComplete(e){return pJ(()=>iE(v3(this.cacheState.map,e),{onNone:()=>{let t=ET(e);return this.trackAccess(t),this.trackMiss(),pB(iC())},onSome:e=>this.resolveMapValue(e,!0)}))}contains(e){return pH(()=>v4(this.cacheState.map,e))}entryStats(e){return pH(()=>{let t=v3(this.cacheState.map,e);if(tF(t))switch(t.value._tag){case"Complete":return tR(EU(t.value.entryStats.loadedMillis));case"Pending":break;case"Refreshing":return tR(EU(t.value.complete.entryStats.loadedMillis))}return iC()})}getEither(e){return pJ(()=>{let t,r,n=iR(v3(this.cacheState.map,e));return(void 0===n&&(r=dO(this.fiberId),t=ET(e),v4(this.cacheState.map,e)?n=iR(v3(this.cacheState.map,e)):v6(this.cacheState.map,e,EE(t,r))),void 0===n)?(this.trackAccess(t),this.trackMiss(),pj(this.lookupValueOf(e,r),nk)):pv(this.resolveMapValue(n),iE({onNone:()=>this.getEither(e),onSome:e=>pB(nw(e))}))})}invalidate(e){return pH(()=>{v7(this.cacheState.map,e)})}invalidateWhen(e,t){return pH(()=>{let r=v3(this.cacheState.map,e);tF(r)&&"Complete"===r.value._tag&&"Success"===r.value.exit._tag&&t(r.value.exit.value)&&v7(this.cacheState.map,e)})}get invalidateAll(){return pH(()=>{this.cacheState.map=v2()})}refresh(e){return ge(t=>pJ(()=>{let r=dO(this.fiberId),n=iR(v3(this.cacheState.map,e));if(void 0===n&&(v4(this.cacheState.map,e)?n=iR(v3(this.cacheState.map,e)):v6(this.cacheState.map,e,EE(ET(e),r))),void 0===n)return pe(this.lookupValueOf(e,r));switch(n._tag){case"Complete":if(this.hasExpired(t,n.timeToLiveMillis))return eJ(iR(v3(this.cacheState.map,e)),n)&&v7(this.cacheState.map,e),pe(this.get(e));return W(this.lookupValueOf(e,r),bJ(()=>{if(eJ(iR(v3(this.cacheState.map,e)),n)){let t=EO(r,n);return v6(this.cacheState.map,e,t),!0}return!1}),pe);case"Pending":case"Refreshing":return dT(n.deferred)}}))}set(e,t){return ge(r=>pH(()=>{let n=r.unsafeCurrentTimeMillis(),i=dS(t),s=EC(ET(e),i,EU(n),n+oO(oc(this.timeToLive(i))));v6(this.cacheState.map,e,s)}))}get size(){return pH(()=>v9(this.cacheState.map))}get values(){return pH(()=>{let e=[];for(let t of this.cacheState.map)"Complete"===t[1]._tag&&"Success"===t[1].exit._tag&&e.push(t[1].exit.value);return e})}get entries(){return pH(()=>{let e=[];for(let t of this.cacheState.map)"Complete"===t[1]._tag&&"Success"===t[1].exit._tag&&e.push([t[0],t[1].exit.value]);return e})}get keys(){return pH(()=>{let e=[];for(let t of this.cacheState.map)"Complete"===t[1]._tag&&"Success"===t[1].exit._tag&&e.push(t[0]);return e})}resolveMapValue(e,t=!1){return ge(r=>{switch(e._tag){case"Complete":if(this.trackAccess(e.key),this.hasExpired(r,e.timeToLiveMillis))return v7(this.cacheState.map,e.key.current),pB(iC());return this.trackHit(),pj(e.exit,tR);case"Pending":if(this.trackAccess(e.key),this.trackHit(),t)return pB(iC());return pj(dT(e.deferred),tR);case"Refreshing":if(this.trackAccess(e.complete.key),this.trackHit(),this.hasExpired(r,e.complete.timeToLiveMillis)){if(t)return pB(iC());return pj(dT(e.deferred),tR)}return pj(e.complete.exit,tR)}})}trackHit(){this.cacheState.hits=this.cacheState.hits+1}trackMiss(){this.cacheState.misses=this.cacheState.misses+1}trackAccess(e){if(ES(this.cacheState.accesses,e),lU(this.cacheState.updating,!1,!0)){let e=!0;for(;e;){let t=Ew(this.cacheState.accesses,Ef);t===Ef?e=!1:this.cacheState.keys.add(t)}let t=v9(this.cacheState.map);for(e=t>this.capacity;e;){let r=this.cacheState.keys.remove();void 0!==r?v4(this.cacheState.map,r.current)&&(v7(this.cacheState.map,r.current),t-=1,e=t>this.capacity):e=!1}lB(this.cacheState.updating,!1)}}hasExpired(e,t){return e.unsafeCurrentTimeMillis()>t}lookupValueOf(e,t){return ge(r=>pJ(()=>W(this.lookup(e),dU(this.context),pf,pv(n=>{let i=r.unsafeCurrentTimeMillis(),s=EU(i),a=EC(ET(e),n,s,i+oO(oc(this.timeToLive(n))));return v6(this.cacheState.map,e,a),p6(dN(t,n),n)}),p$(()=>p6(xB(t),pH(()=>{v7(this.cacheState.map,e)}))))))}}let EB=(e,t,r)=>new Eq(e,t0(),lX,t,e=>oc(r(e))),EJ=(()=>fw(EB(65536,()=>pj(dI(),e=>({listeners:new S8,handle:e})),()=>o_(60))),e=>W(dI(),pv(t=>W(bd(e),pA(t),bR,pj(e=>p6(e,W(dT(t),pv(([e,t])=>h9(p5(bN(e[0]),pQ(e[1])),t))))))))),EH=function(){let e=1==arguments.length?arguments[0]:arguments[1].bind(arguments[0]);return p1(()=>e(W))},EK=e=>e.length>=1?pi((t,r)=>{try{e(r).then(e=>t(dS(e)),e=>t(dp(e)))}catch(e){t(dp(e))}}):pi(t=>{try{e().then(e=>t(dS(e)),e=>t(dp(e)))}catch(e){t(dp(e))}}),EW=e=>by(e,{onFailure:K,onSuccess:K}),EV=e=>pC(e,{onFailure:e=>bk(e,"An error was silently ignored because it is not anticipated to be useful"),onSuccess:()=>pZ}),EG=e=>pv(wh(),t=>wf(e,t)),EY=(e,...t)=>b_(e)(...t),EZ=e=>pH(()=>wC(e)),EQ=e=>new wE(e??!1),EX=U(2,(e,t)=>ws(e,t,(e,t)=>e(t))),E0=function(){let e="string"!=typeof arguments[0],t=e?arguments[1]:arguments[0],r=m4(e?arguments[2]:arguments[1]);if(e){let e=arguments[0];return bG(t,r,t=>bY(e,t))}return e=>bG(t,r,t=>bY(e,t))},E1=function(e,...t){let r=Error.stackTraceLimit;Error.stackTraceLimit=2;let n=Error();if(Error.stackTraceLimit=r,"string"!=typeof e)return E2(e.length,function(...r){let i=Error.stackTraceLimit;Error.stackTraceLimit=2;let s=Error();return Error.stackTraceLimit=i,E3({self:this,body:e,args:r,pipeables:t,spanName:"<anonymous>",spanOptions:{context:m6.context(!0)},errorDef:n,errorCall:s})});let i=t[0];return(t,...r)=>E2(t.length,{[e](...s){let a=Error.stackTraceLimit;Error.stackTraceLimit=2;let o=Error();return Error.stackTraceLimit=a,E3({self:this,body:t,args:s,pipeables:r,spanName:e,spanOptions:i,errorDef:n,errorCall:o})}}[e])};function E2(e,t){return Object.defineProperty(t,"length",{value:e,configurable:!0})}function E3(e){let t,r;if(eF(e.body))t=p1(()=>e.body.apply(e.self,e.args));else try{t=e.body.apply(e.self,e.args)}catch(e){r=e,t=pu(e)}if(e.pipeables.length>0)try{for(let r of e.pipeables)t=r(t,...e.args)}catch(e){t=r?pg(ho(hi(r),hi(e))):pu(e)}let n=!1,i=e.spanOptions&&"captureStackTrace"in e.spanOptions?e.spanOptions:{captureStackTrace:()=>{if(!1!==n)return n;if(e.errorCall.stack){let t=e.errorDef.stack.trim().split("\n"),r=e.errorCall.stack.trim().split("\n"),i=t.slice(2).join("\n").trim();i.includes("(")||(i=i.replace(/at (.*)/,"at ($1)"));let s=r.slice(2).join("\n").trim();return s.includes("(")||(s=s.replace(/at (.*)/,"at ($1)")),n=`${i}
${s}`}},...e.spanOptions};return E0(t,e.spanName,i)}let E5=(e,t)=>{switch(t._tag){case"StringKeyword":case"TemplateLiteral":return Object.keys(e);case"SymbolKeyword":return Object.getOwnPropertySymbols(e);case"Refinement":return E5(e,t.from)}},E4=e=>Object.keys(e).concat(Object.getOwnPropertySymbols(e)),E6=e=>{let t,r=!1;return()=>(r||(t=e(),r=!0),t)},E8=e=>{try{return e.toISOString()}catch{return String(e)}},E7=(e,t=!0)=>{if(Array.isArray(e))return`[${e.map(e=>E7(e,t)).join(",")}]`;if(ed(e))return E8(e);if(ec(e,"toString")&&er(e.toString)&&e.toString!==Object.prototype.toString)return e.toString();if(Z(e))return JSON.stringify(e);if(Q(e)||null==e||X(e)||et(e))return String(e);if(ee(e))return String(e)+"n";if(em(e))return`${e.constructor.name}(${E7(Array.from(e),t)})`;try{t&&JSON.stringify(e);let r=`{${E4(e).map(t=>`${Z(t)?JSON.stringify(t):String(t)}:${E7(e[t],!1)}`).join(",")}}`,n=e.constructor.name;return e.constructor!==Object.prototype.constructor?`${n}(${r})`:r}catch{return"<circular structure>"}},E9=e=>"string"==typeof e?JSON.stringify(e):String(e),Oe=e=>Array.isArray(e),Ot=e=>!Array.isArray(e),Or=e=>`[${E9(e)}]`,On=e=>Oe(e)?e.map(Or).join(""):Or(e),Oi=(e,t,r,n)=>{let i=e;return r&&ss(r)&&(i+=`
at path: ${On(r)}`),void 0!==t&&(i+=`
details: ${t}`),n&&(i+=`
schema (${n._tag}): ${n}`),i},Os=(e,t,r)=>Oi("Unsupported schema or overlapping types",`cannot extend ${e} with ${t}`,r),Oa=e=>Oi("Unsupported key schema",void 0,void 0,e),Oo=e=>Oi("Unsupported literal",`literal value: ${E7(e)}`),Ol=e=>Oi("Duplicate index signature",`${e} index signature`),Oc=Oi("Unsupported index signature parameter","An index signature parameter type must be `string`, `symbol`, a template literal type or a refinement of the previous types"),Ou=Oi("Invalid element","A required element cannot follow an optional element. ts(1257)"),Oh=e=>Oi("Duplicate property signature transformation",`Duplicate key ${E7(e)}`),Op=e=>Oi("Duplicate property signature",`Duplicate key ${E7(e)}`),Of=Symbol.for("effect/SchemaId/DateFromSelf"),Od=Symbol.for("effect/SchemaId/GreaterThan"),Om=Symbol.for("effect/SchemaId/GreaterThanOrEqualTo"),Og=Symbol.for("effect/SchemaId/LessThan"),Ob=Symbol.for("effect/SchemaId/LessThanOrEqualTo"),Ox=Symbol.for("effect/SchemaId/Int"),Oy=Symbol.for("effect/SchemaId/NonNaN"),Ov=Symbol.for("effect/SchemaId/Finite"),OS=Symbol.for("effect/SchemaId/JsonNumber"),O_=Symbol.for("effect/SchemaId/Between"),Ow=Symbol.for("effect/SchemaId/GreaterThanBigint"),Ok=Symbol.for("effect/SchemaId/GreaterThanOrEqualToBigint"),OC=Symbol.for("effect/SchemaId/LessThanBigint"),OE=Symbol.for("effect/SchemaId/LessThanOrEqualToBigint"),OO=Symbol.for("effect/SchemaId/BetweenBigint"),OI=Symbol.for("effect/SchemaId/MinLength"),OF=Symbol.for("effect/SchemaId/Length"),OT=Symbol.for("effect/annotation/Brand"),OR=Symbol.for("effect/annotation/SchemaId"),ON=Symbol.for("effect/annotation/Message"),OA=Symbol.for("effect/annotation/MissingMessage"),Oj=Symbol.for("effect/annotation/Identifier"),OM=Symbol.for("effect/annotation/Title"),Oz=Symbol.for("effect/annotation/AutoTitle"),OD=Symbol.for("effect/annotation/Description"),O$=Symbol.for("effect/annotation/Examples"),OP=Symbol.for("effect/annotation/Default"),OL=Symbol.for("effect/annotation/JSONSchema"),OU=Symbol.for("effect/annotation/Arbitrary"),Oq=Symbol.for("effect/annotation/Pretty"),OB=Symbol.for("effect/annotation/Equivalence"),OJ=Symbol.for("effect/annotation/Documentation"),OH=Symbol.for("effect/annotation/Concurrency"),OK=Symbol.for("effect/annotation/Batching"),OW=Symbol.for("effect/annotation/ParseIssueTitle"),OV=Symbol.for("effect/annotation/ParseOptions"),OG=Symbol.for("effect/annotation/DecodingFallback"),OY=Symbol.for("effect/annotation/Surrogate"),OZ=Symbol.for("effect/annotation/StableFilter"),OQ=U(2,(e,t)=>Object.prototype.hasOwnProperty.call(e.annotations,t)?tR(e.annotations[t]):iC()),OX=OQ(OT),O0=OQ(ON),O1=OQ(OA),O2=OQ(OM),O3=OQ(Oz),O5=OQ(Oj),O4=OQ(OD),O6=OQ(OH),O8=OQ(OK),O7=OQ(OW),O9=OQ(OV),Ie=OQ(OG),It=OQ(OY),Ir=OQ(OZ),In=e=>iq(Ir(e),e=>!0===e),Ii=Symbol.for("effect/annotation/JSONIdentifier"),Is=OQ(Ii),Ia=e=>iI(Is(e),()=>O5(e)),Io=Symbol.for("effect/schema/ParseJson");class Il{constructor(e,t,r,n={}){this._tag="Declaration",this.typeParameters=e,this.decodeUnknown=t,this.encodeUnknown=r,this.annotations=n}toString(){return iO(FR(this),()=>"<declaration schema>")}toJSON(){return{_tag:this._tag,typeParameters:this.typeParameters.map(e=>e.toJSON()),annotations:FE(this.annotations)}}}let Ic=e=>t=>t._tag===e;class Iu{constructor(e,t={}){this._tag="Literal",this.literal=e,this.annotations=t}toString(){return iO(FR(this),()=>E7(this.literal))}toJSON(){return{_tag:this._tag,literal:ee(this.literal)?String(this.literal):this.literal,annotations:FE(this.annotations)}}}let Ih=Ic("Literal"),Ip=new Iu(null);class If{constructor(e,t={}){this._tag="UniqueSymbol",this.symbol=e,this.annotations=t}toString(){return iO(FR(this),()=>E7(this.symbol))}toJSON(){return{_tag:this._tag,symbol:String(this.symbol),annotations:FE(this.annotations)}}}class Id{constructor(e={}){this._tag="UndefinedKeyword",this.annotations=e}toString(){return FF(this)}toJSON(){return{_tag:this._tag,annotations:FE(this.annotations)}}}let Im=new Id({[OM]:"undefined"});class Ig{constructor(e={}){this._tag="VoidKeyword",this.annotations=e}toString(){return FF(this)}toJSON(){return{_tag:this._tag,annotations:FE(this.annotations)}}}class Ib{constructor(e={}){this._tag="NeverKeyword",this.annotations=e}toString(){return FF(this)}toJSON(){return{_tag:this._tag,annotations:FE(this.annotations)}}}let Ix=new Ib({[OM]:"never"});class Iy{constructor(e={}){this._tag="UnknownKeyword",this.annotations=e}toString(){return FF(this)}toJSON(){return{_tag:this._tag,annotations:FE(this.annotations)}}}let Iv=new Iy({[OM]:"unknown"});class IS{constructor(e={}){this._tag="AnyKeyword",this.annotations=e}toString(){return FF(this)}toJSON(){return{_tag:this._tag,annotations:FE(this.annotations)}}}let I_=new IS({[OM]:"any"});class Iw{constructor(e={}){this._tag="StringKeyword",this.annotations=e}toString(){return FF(this)}toJSON(){return{_tag:this._tag,annotations:FE(this.annotations)}}}let Ik=new Iw({[OM]:"string",[OD]:"a string"}),IC=Ic("StringKeyword");class IE{constructor(e={}){this._tag="NumberKeyword",this.annotations=e}toString(){return FF(this)}toJSON(){return{_tag:this._tag,annotations:FE(this.annotations)}}}let IO=new IE({[OM]:"number",[OD]:"a number"}),II=Ic("NumberKeyword");class IF{constructor(e={}){this._tag="BooleanKeyword",this.annotations=e}toString(){return FF(this)}toJSON(){return{_tag:this._tag,annotations:FE(this.annotations)}}}let IT=new IF({[OM]:"boolean",[OD]:"a boolean"}),IR=Ic("BooleanKeyword");class IN{constructor(e={}){this._tag="BigIntKeyword",this.annotations=e}toString(){return FF(this)}toJSON(){return{_tag:this._tag,annotations:FE(this.annotations)}}}let IA=new IN({[OM]:"bigint",[OD]:"a bigint"});class Ij{constructor(e={}){this._tag="SymbolKeyword",this.annotations=e}toString(){return FF(this)}toJSON(){return{_tag:this._tag,annotations:FE(this.annotations)}}}let IM=new Ij({[OM]:"symbol",[OD]:"a symbol"}),Iz=Ic("SymbolKeyword");class ID{constructor(e={}){this._tag="ObjectKeyword",this.annotations=e}toString(){return FF(this)}toJSON(){return{_tag:this._tag,annotations:FE(this.annotations)}}}let I$=e=>{switch(e._tag){case"Literal":case"NumberKeyword":case"StringKeyword":case"TemplateLiteral":return!0;case"Union":return e.types.every(I$)}return!1},IP=e=>{switch(e._tag){case"Literal":return JSON.stringify(String(e.literal));case"StringKeyword":return"string";case"NumberKeyword":return"number";case"TemplateLiteral":return String(e);case"Union":return e.types.map(IP).join(" | ")}};class IL{constructor(e,t={}){this.type=e,this.annotations=t}toJSON(){return{type:this.type.toJSON(),annotations:FE(this.annotations)}}toString(){return String(this.type)}}class IU extends IL{constructor(e,t,r={}){super(e,r),this.isOptional=t}toJSON(){return{type:this.type.toJSON(),isOptional:this.isOptional,annotations:FE(this.annotations)}}toString(){return String(this.type)+(this.isOptional?"?":"")}}let Iq=e=>e.map(e=>e.type);class IB{constructor(e,t,r,n={}){this._tag="TupleType",this.elements=e,this.rest=t,this.isReadonly=r,this.annotations=n;let i=!1,s=!1;for(let t of e)if(t.isOptional)i=!0;else if(i){s=!0;break}if(s||i&&t.length>1)throw Error(Ou)}toString(){return iO(FR(this),()=>IJ(this))}toJSON(){return{_tag:this._tag,elements:this.elements.map(e=>e.toJSON()),rest:this.rest.map(e=>e.toJSON()),isReadonly:this.isReadonly,annotations:FE(this.annotations)}}}let IJ=e=>{let t=e.elements.map(String).join(", ");return i9(e.rest,{onEmpty:()=>`readonly [${t}]`,onNonEmpty:(r,n)=>{let i=String(r),s=i.includes(" | ")?`(${i})`:i;if(n.length>0){let r=n.map(String).join(", ");return e.elements.length>0?`readonly [${t}, ...${s}[], ${r}]`:`readonly [...${s}[], ${r}]`}return e.elements.length>0?`readonly [${t}, ...${s}[]]`:`ReadonlyArray<${i}>`}})};class IH extends IU{constructor(e,t,r,n,i){super(t,r,i),this.name=e,this.isReadonly=n}toString(){return(this.isReadonly?"readonly ":"")+String(this.name)+(this.isOptional?"?":"")+": "+this.type}toJSON(){return{name:String(this.name),type:this.type.toJSON(),isOptional:this.isOptional,isReadonly:this.isReadonly,annotations:FE(this.annotations)}}}let IK=e=>{switch(e._tag){case"StringKeyword":case"SymbolKeyword":case"TemplateLiteral":return!0;case"Refinement":return IK(e.from)}return!1};class IW{constructor(e,t,r){if(this.type=t,this.isReadonly=r,IK(e))this.parameter=e;else throw Error(Oc)}toString(){return(this.isReadonly?"readonly ":"")+`[x: ${this.parameter}]: ${this.type}`}toJSON(){return{parameter:this.parameter.toJSON(),type:this.type.toJSON(),isReadonly:this.isReadonly}}}class IV{constructor(e,t,r={}){this._tag="TypeLiteral",this.annotations=r;let n={};for(let t=0;t<e.length;t++){let r=e[t].name;if(Object.prototype.hasOwnProperty.call(n,r))throw Error(Op(r));n[r]=null}let i={string:!1,symbol:!1};for(let e=0;e<t.length;e++){let r=FO(t[e].parameter);if(IC(r)){if(i.string)throw Error(Ol("string"));i.string=!0}else if(Iz(r)){if(i.symbol)throw Error(Ol("symbol"));i.symbol=!0}}this.propertySignatures=e,this.indexSignatures=t}toString(){return iO(FR(this),()=>IY(this))}toJSON(){return{_tag:this._tag,propertySignatures:this.propertySignatures.map(e=>e.toJSON()),indexSignatures:this.indexSignatures.map(e=>e.toJSON()),annotations:FE(this.annotations)}}}let IG=e=>e.map(String).join("; "),IY=e=>{if(e.propertySignatures.length>0){let t=e.propertySignatures.map(String).join("; ");return e.indexSignatures.length>0?`{ ${t}; ${IG(e.indexSignatures)} }`:`{ ${t} }`}return e.indexSignatures.length>0?`{ ${IG(e.indexSignatures)} }`:"{}"},IZ=Ic("TypeLiteral"),IQ=sy(iV(iW,e=>{switch(e._tag){case"AnyKeyword":return 0;case"UnknownKeyword":return 1;case"ObjectKeyword":return 2;case"StringKeyword":case"NumberKeyword":case"BooleanKeyword":case"BigIntKeyword":case"SymbolKeyword":return 3}return 4})),IX={string:"StringKeyword",number:"NumberKeyword",boolean:"BooleanKeyword",bigint:"BigIntKeyword"},I0=e=>sR(e,e=>I4(e)?I0(e.types):[e]),I1=e=>{let t=IQ(e),r=[],n={},i=[];for(let e of t)switch(e._tag){case"NeverKeyword":break;case"AnyKeyword":return[I_];case"UnknownKeyword":return[Iv];case"ObjectKeyword":case"UndefinedKeyword":case"VoidKeyword":case"StringKeyword":case"NumberKeyword":case"BooleanKeyword":case"BigIntKeyword":case"SymbolKeyword":n[e._tag]||(n[e._tag]=e,r.push(e));break;case"Literal":{let t=typeof e.literal;switch(t){case"string":case"number":case"bigint":case"boolean":n[IX[t]]||i.includes(e.literal)||(i.push(e.literal),r.push(e));break;case"object":i.includes(e.literal)||(i.push(e.literal),r.push(e))}break}case"UniqueSymbol":n.SymbolKeyword||i.includes(e.symbol)||(i.push(e.symbol),r.push(e));break;case"TupleType":n.ObjectKeyword||r.push(e);break;case"TypeLiteral":0===e.propertySignatures.length&&0===e.indexSignatures.length?n["{}"]||(n["{}"]=e,r.push(e)):n.ObjectKeyword||r.push(e);break;default:r.push(e)}return r};class I2{static{this.make=(e,t)=>I5(e)?new I2(e,t):1===e.length?e[0]:Ix}static{this.unify=(e,t)=>I2.make(I1(I0(e)),t)}constructor(e,t={}){this._tag="Union",this.types=e,this.annotations=t}toString(){return iO(FR(this),()=>this.types.map(String).join(" | "))}toJSON(){return{_tag:this._tag,types:this.types.map(e=>e.toJSON()),annotations:FE(this.annotations)}}}let I3=(e,t)=>e.map(t),I5=e=>e.length>1,I4=Ic("Union"),I6=Y(Symbol.for("effect/Schema/AST/toJSONMemoMap"),()=>new WeakMap);class I8{constructor(e,t={}){this._tag="Suspend",this.f=e,this.annotations=t,this.f=E6(e)}toString(){return FR(this).pipe(iI(()=>iz(iN(this.f)(),e=>FR(e))),iO(()=>"<suspended schema>"))}toJSON(){let e=this.f(),t=I6.get(e);return t||(I6.set(e,{_tag:this._tag}),t={_tag:this._tag,ast:e.toJSON(),annotations:FE(this.annotations)},I6.set(e,t)),t}}class I7{constructor(e,t,r={}){this._tag="Refinement",this.from=e,this.filter=t,this.annotations=r}toString(){return O5(this).pipe(iO(()=>iE(FT(this),{onNone:()=>`{ ${this.from} | filter }`,onSome:e=>I9(this.from)?String(this.from)+" & "+e:e})))}toJSON(){return{_tag:this._tag,from:this.from.toJSON(),annotations:FE(this.annotations)}}}let I9=Ic("Refinement"),Fe={};class Ft{constructor(e,t,r,n={}){this._tag="Transformation",this.from=e,this.to=t,this.transformation=r,this.annotations=n}toString(){return iO(FR(this),()=>`(${String(this.from)} <-> ${String(this.to)})`)}toJSON(){return{_tag:this._tag,from:this.from.toJSON(),to:this.to.toJSON(),annotations:FE(this.annotations)}}}let Fr=Ic("Transformation");class Fn{constructor(e,t){this._tag="FinalTransformation",this.decode=e,this.encode=t}}class Fi{constructor(){this._tag="ComposeTransformation"}}let Fs=new Fi;class Fa{constructor(e,t,r,n){this.from=e,this.to=t,this.decode=r,this.encode=n}}class Fo{constructor(e){this._tag="TypeLiteralTransformation",this.propertySignatureTransformations=e;let t={},r={};for(let n of e){let e=n.from;if(t[e])throw Error(Oh(e));t[e]=!0;let i=n.to;if(r[i])throw Error(Oh(i));r[i]=!0}}}let Fl=e=>"TypeLiteralTransformation"===e._tag,Fc=(e,t)=>{let r=Object.getOwnPropertyDescriptors(e),n={...e.annotations,...t},i=It(e);return tF(i)&&(n[OY]=Fc(i.value,t)),r.annotations.value=n,Object.create(Object.getPrototypeOf(e),r)},Fu=(e,t)=>{switch(e._tag){case"Literal":return d2(String(e.literal));case"StringKeyword":return"[\\s\\S]*";case"NumberKeyword":return"[+-]?\\d*\\.?\\d+(?:[Ee][+-]?\\d+)?";case"TemplateLiteral":return Fp(e,t,!1);case"Union":return e.types.map(e=>Fu(e,t)).join("|")}},Fh=(e,t,r,n)=>{if(I4(e)){if(r&&!n)return`(?:${t})`}else if(!r||!n)return t;return`(${t})`},Fp=(e,t,r)=>{let n="";if(""!==e.head){let i=d2(e.head);n+=t&&r?`(${i})`:i}for(let i of e.spans){let e=Fu(i.type,t);if(n+=Fh(i.type,e,t,r),""!==i.literal){let e=d2(i.literal);n+=t&&r?`(${e})`:e}}return n},Ff=e=>RegExp(`^${Fp(e,!1,!0)}$`),Fd=e=>{switch(e._tag){case"TupleType":{let t=!1,r=[];for(let n of e.elements)n.isOptional&&(t=!0),r.push(n.type);return t&&r.push(Im),r=r.concat(Iq(e.rest)),I2.make(r)}case"Refinement":return Fd(e.from);case"Union":return I2.make(e.types.map(Fd));case"Suspend":return Fd(e.f())}throw Error(errors_.getASTUnsupportedSchemaErrorMessage(e))},Fm=(e,t)=>{let r=Arr.findFirst(e.propertySignatures,e=>e.name===t);if(Option.isSome(r))return r.value;if(Predicate.isString(t)){let r;for(let n of e.indexSignatures){let e=FO(n.parameter);switch(e._tag){case"TemplateLiteral":if(Ff(e).test(t))return new IH(t,n.type,!1,!0);break;case"StringKeyword":void 0===r&&(r=new IH(t,n.type,!1,!0))}}if(r)return r}else if(Predicate.isSymbol(t)){for(let r of e.indexSignatures)if(Iz(FO(r.parameter)))return new IH(t,r.type,!1,!0)}},Fg=(e,t)=>{let r=[],n=[],i=e=>{switch(e._tag){case"NeverKeyword":break;case"StringKeyword":case"SymbolKeyword":case"TemplateLiteral":case"Refinement":n.push(new IW(e,t,!0));break;case"Literal":if(Z(e.literal)||Q(e.literal))r.push(new IH(e.literal,t,!1,!0));else throw Error(Oo(e.literal));break;case"Enums":for(let[n,i]of e.enums)r.push(new IH(i,t,!1,!0));break;case"UniqueSymbol":r.push(new IH(e.symbol,t,!1,!0));break;case"Union":e.types.forEach(i);break;default:throw Error(Oa(e))}};return i(e),{propertySignatures:r,indexSignatures:n}},Fb=e=>{switch(e._tag){case"TupleType":return!1===e.isReadonly?e:new IB(e.elements,e.rest,!1,e.annotations);case"TypeLiteral":{let t=F_(e.propertySignatures,e=>!1===e.isReadonly?e:new IH(e.name,e.type,e.isOptional,!1,e.annotations)),r=F_(e.indexSignatures,e=>!1===e.isReadonly?e:new IW(e.parameter,e.type,!1));return t===e.propertySignatures&&r===e.indexSignatures?e:new IV(t,r,e.annotations)}case"Union":{let t=F_(e.types,Fb);return t===e.types?e:I2.make(t,e.annotations)}case"Suspend":return new I8(()=>Fb(e.f()),e.annotations);case"Refinement":{let t=Fb(e.from);return t===e.from?e:new I7(t,e.filter,e.annotations)}case"Transformation":{let t=Fb(e.from),r=Fb(e.to);return t===e.from&&r===e.to?e:new Ft(t,r,e.transformation,e.annotations)}}return e},Fx=e=>t=>{let r;for(let n of e)Object.prototype.hasOwnProperty.call(t.annotations,n)&&(void 0===r&&(r={}),r[n]=t.annotations[n]);return r},Fy=Fx([O$,OP,OL,OU,Oq,OB]),Fv=e=>{switch(e._tag){case"Declaration":{let t=F_(e.typeParameters,Fv);return t===e.typeParameters?e:new Il(t,e.decodeUnknown,e.encodeUnknown,e.annotations)}case"TupleType":{let t=F_(e.elements,e=>{let t=Fv(e.type);return t===e.type?e:new IU(t,e.isOptional)}),r=Iq(e.rest),n=F_(r,Fv);return t===e.elements&&n===r?e:new IB(t,n.map(e=>new IL(e)),e.isReadonly,e.annotations)}case"TypeLiteral":{let t=F_(e.propertySignatures,e=>{let t=Fv(e.type);return t===e.type?e:new IH(e.name,t,e.isOptional,e.isReadonly)}),r=F_(e.indexSignatures,e=>{let t=Fv(e.type);return t===e.type?e:new IW(e.parameter,t,e.isReadonly)});return t===e.propertySignatures&&r===e.indexSignatures?e:new IV(t,r,e.annotations)}case"Union":{let t=F_(e.types,Fv);return t===e.types?e:I2.make(t,e.annotations)}case"Suspend":return new I8(()=>Fv(e.f()),e.annotations);case"Refinement":{let t=Fv(e.from);return t===e.from?e:new I7(t,e.filter,e.annotations)}case"Transformation":{let t=Fy(e);return Fv(void 0!==t?Fc(e.to,t):e.to)}}return e},FS=e=>iE(Ia(e),{onNone:()=>void 0,onSome:e=>({[Ii]:e})});function F_(e,t){let r=!1,n=i5(e.length);for(let i=0;i<e.length;i++){let s=e[i],a=t(s);a!==s&&(r=!0),n[i]=a}return r?n:e}let Fw=e=>{switch(e._tag){case"Transformation":return e.from;case"Refinement":return Fw(e.from);case"Suspend":return Fw(e.f())}},Fk=(e,t)=>{switch(e._tag){case"Declaration":{let r=F_(e.typeParameters,e=>Fk(e,t));return r===e.typeParameters?e:new Il(r,e.decodeUnknown,e.encodeUnknown,e.annotations)}case"TupleType":{let r=F_(e.elements,e=>{let r=Fk(e.type,t);return r===e.type?e:new IU(r,e.isOptional)}),n=Iq(e.rest),i=F_(n,e=>Fk(e,t));return r===e.elements&&i===n?e:new IB(r,i.map(e=>new IL(e)),e.isReadonly,FS(e))}case"TypeLiteral":{let r=F_(e.propertySignatures,e=>{let r=Fk(e.type,t);return r===e.type?e:new IH(e.name,r,e.isOptional,e.isReadonly)}),n=F_(e.indexSignatures,e=>{let r=Fk(e.type,t);return r===e.type?e:new IW(e.parameter,r,e.isReadonly)});return r===e.propertySignatures&&n===e.indexSignatures?e:new IV(r,n,FS(e))}case"Union":{let r=F_(e.types,e=>Fk(e,t));return r===e.types?e:I2.make(r,FS(e))}case"Suspend":return new I8(()=>Fk(e.f(),t),FS(e));case"Refinement":{let r=Fk(e.from,t);if(t){if(r===e.from)return e;if(void 0===Fw(e.from)&&In(e))return new I7(r,e.filter,e.annotations)}let n=FS(e);return n?Fc(r,n):r}case"Transformation":{let r=FS(e);return Fk(r?Fc(e.from,r):e.from,t)}}return e},FC=e=>Fk(e,!1),FE=e=>{let t={};for(let r of Object.getOwnPropertySymbols(e))t[String(r)]=e[r];return t},FO=e=>{switch(e._tag){case"StringKeyword":case"SymbolKeyword":case"TemplateLiteral":return e;case"Refinement":return FO(e.from)}},FI=(e,t)=>new Ft(e,t,Fs),FF=e=>iO(FR(e),()=>e._tag),FT=e=>O2(e).pipe(iI(()=>O4(e)),iI(()=>O3(e)),iM(t=>t+iE(OX(e),{onNone:()=>"",onSome:e=>e.map(e=>` & Brand<${E7(e)}>`).join("")}))),FR=e=>iI(O5(e),()=>FT(e));class FN{constructor(e,t,r){this._tag="Pointer",this.path=e,this.actual=t,this.issue=r}}class FA{constructor(e,t){this._tag="Unexpected",this.actual=e,this.message=t}}class Fj{constructor(e,t){this._tag="Missing",this.actual=void 0,this.ast=e,this.message=t}}class FM{constructor(e,t,r,n){this._tag="Composite",this.ast=e,this.actual=t,this.issues=r,this.output=n}}class Fz{constructor(e,t,r,n){this._tag="Refinement",this.ast=e,this.actual=t,this.kind=r,this.issue=n}}class FD{constructor(e,t,r,n){this._tag="Transformation",this.ast=e,this.actual=t,this.kind=r,this.issue=n}}class F${constructor(e,t,r){this._tag="Type",this.ast=e,this.actual=t,this.message=r}}class FP{constructor(e,t,r){this._tag="Forbidden",this.ast=e,this.actual=t,this.message=r}}let FL=Symbol.for("effect/Schema/ParseErrorTypeId"),FU=e=>ec(e,FL);class Fq extends gS("ParseError"){get message(){return this.toString()}toString(){return Tp.formatIssueSync(this.issue)}toJSON(){return{_id:"ParseError",message:this.toString()}}[eV](){return this.toJSON()}constructor(...e){super(...e),this[FL]=FL}}let FB=e=>new Fq({issue:e}),FJ=nk,FH=e=>{if(er(e))try{return nk(e())}catch(e){return nw(e)}try{return nk(e.try())}catch(t){return nw(e.catch(t))}},FK=nC,FW=U(2,(e,t)=>nv(e)?nF(e,{onLeft:nw,onRight:t}):pv(e,t)),FV=U(2,(e,t)=>nv(e)?nI(e,t):pj(e,t)),FG=U(2,(e,t)=>nv(e)?nO(e,t):pz(e,t)),FY=U(2,(e,t)=>nv(e)?nE(e,{onLeft:t.onFailure,onRight:t.onSuccess}):pM(e,t)),FZ=U(2,(e,t)=>nv(e)?nF(e,{onLeft:t,onRight:nk}):pa(e,t)),FQ=(e,t)=>void 0===t||Q(t)?e:void 0===e?t:{...e,...t},FX=(e,t,r)=>{let n=F9(e,t);return(e,t)=>n(e,FQ(r,t))},F0=(e,t,r)=>{let n=FX(e,t,r);return(e,t)=>nR(n(e,t),FB)},F1=(e,t,r)=>{let n=F9(e,t);return(e,t)=>n(e,{...FQ(r,t),isEffectAllowed:!0})},F2=(e,t)=>FX(e.ast,!0,t),F3=(e,t)=>F1(e.ast,!0,t),F5=(e,t)=>F1(e.ast,!1,t),F4=(e,t)=>F0(Fv(e.ast),!0,t),F6=(e,t)=>{let r=F9(Fv(e.ast),!0);return(e,n)=>n_(r(e,{exact:!0,...FQ(t,n)}))},F8=Y(Symbol.for("effect/ParseResult/decodeMemoMap"),()=>new WeakMap),F7=Y(Symbol.for("effect/ParseResult/encodeMemoMap"),()=>new WeakMap),F9=(e,t)=>{let r=t?F8:F7,n=r.get(e);if(n)return n;let i=Tr(e,t),s=O9(e),a=tF(s)?(e,t)=>i(e,FQ(t,s.value)):i,o=Ie(e),l=t&&tF(o)?(t,r)=>To(FZ(a(t,r),o.value),e,t,r):a;return r.set(e,l),l},Te=e=>iR(O6(e)),Tt=e=>iR(O8(e)),Tr=(e,t)=>{switch(e._tag){case"Refinement":if(t){let t=F9(e.from,!0);return(r,n)=>{n=n??Fe;let i=n?.errors==="all";return To(FW(FZ(t(r,n),t=>{let s=new Fz(e,r,"From",t);return i&&In(e)&&TS(t)?iE(e.filter(r,n,e),{onNone:()=>nw(s),onSome:t=>nw(new FM(e,r,[s,new Fz(e,r,"Predicate",t)]))}):nw(s)}),t=>iE(e.filter(t,n,e),{onNone:()=>nk(t),onSome:t=>nw(new Fz(e,r,"Predicate",t))})),e,r,n)}}{let t=F9(Fv(e),!0),r=F9(Ta(e.from),!1);return(n,i)=>To(FW(t(n,i),e=>r(e,i)),e,n,i)}case"Transformation":{let r=Tu(e.transformation,t),n=t?F9(e.from,!0):F9(e.to,!1),i=t?F9(e.to,!0):F9(e.from,!1);return(s,a)=>To(FW(FG(n(s,a),r=>new FD(e,s,t?"Encoded":"Type",r)),n=>FW(FG(r(n,a??Fe,e,s),t=>new FD(e,s,"Transformation",t)),r=>FG(i(r,a),r=>new FD(e,s,t?"Type":"Encoded",r)))),e,s,a)}case"Declaration":{let r=t?e.decodeUnknown(...e.typeParameters):e.encodeUnknown(...e.typeParameters);return(t,n)=>To(r(t,n??Fe,e),e,t,n)}case"Literal":return Tn(e,t=>t===e.literal);case"UniqueSymbol":return Tn(e,t=>t===e.symbol);case"UndefinedKeyword":return Tn(e,en);case"NeverKeyword":return Tn(e,ea);case"UnknownKeyword":case"AnyKeyword":case"VoidKeyword":return nk;case"StringKeyword":return Tn(e,Z);case"NumberKeyword":return Tn(e,Q);case"BooleanKeyword":return Tn(e,X);case"BigIntKeyword":return Tn(e,ee);case"SymbolKeyword":return Tn(e,et);case"ObjectKeyword":return Tn(e,el);case"Enums":return Tn(e,t=>e.enums.some(([e,r])=>r===t));case"TemplateLiteral":{let t=Ff(e);return Tn(e,e=>Z(e)&&t.test(e))}case"TupleType":{let r=e.elements.map(e=>F9(e.type,t)),n=e.rest.map(e=>F9(e.type,t)),i=e.elements.filter(e=>!e.isOptional);e.rest.length>0&&(i=i.concat(e.rest.slice(1)));let s=i.length,a=e.elements.length>0?e.elements.map((e,t)=>t).join(" | "):"never",o=Te(e),l=Tt(e);return(t,c)=>{let u;if(!sn(t))return nw(new F$(e,t));let h=c?.errors==="all",p=[],f=0,d=[],m=t.length;for(let r=m;r<=s-1;r++){let n=new FN(r,t,new Fj(i[r-m]));if(!h)return nw(new FM(e,t,n,d));p.push([f++,n])}if(0===e.rest.length)for(let r=e.elements.length;r<=m-1;r++){let n=new FN(r,t,new FA(t[r],`is unexpected, expected: ${a}`));if(!h)return nw(new FM(e,t,n,d));p.push([f++,n])}let g=0;for(;g<r.length;g++)if(m<g+1){if(e.elements[g].isOptional)continue}else{let n=(0,r[g])(t[g],c);if(nv(n)){if(nS(n)){let r=new FN(g,t,n.left);if(!h)return nw(new FM(e,t,r,Tc(d)));p.push([f++,r]);continue}d.push([f++,n.right])}else{let r=f++,i=g;u||(u=[]),u.push(({es:s,output:a})=>pv(pp(n),n=>{if(nS(n)){let o=new FN(i,t,n.left);return h?(s.push([r,o]),pZ):nw(new FM(e,t,o,Tc(a)))}return a.push([r,n.right]),pZ}))}}if(ss(n)){let[r,...i]=n;for(;g<m-i.length;g++){let n=r(t[g],c);if(nv(n))if(nS(n)){let r=new FN(g,t,n.left);if(!h)return nw(new FM(e,t,r,Tc(d)));p.push([f++,r]);continue}else d.push([f++,n.right]);else{let r=f++,i=g;u||(u=[]),u.push(({es:s,output:a})=>pv(pp(n),n=>{if(!nS(n))return a.push([r,n.right]),pZ;{let o=new FN(i,t,n.left);return h?(s.push([r,o]),pZ):nw(new FM(e,t,o,Tc(a)))}}))}}for(let r=0;r<i.length;r++)if(!(m<(g+=r)+1)){let n=i[r](t[g],c);if(nv(n)){if(nS(n)){let r=new FN(g,t,n.left);if(!h)return nw(new FM(e,t,r,Tc(d)));p.push([f++,r]);continue}d.push([f++,n.right])}else{let r=f++,i=g;u||(u=[]),u.push(({es:s,output:a})=>pv(pp(n),n=>{if(nS(n)){let o=new FN(i,t,n.left);return h?(s.push([r,o]),pZ):nw(new FM(e,t,o,Tc(a)))}return a.push([r,n.right]),pZ}))}}}let b=({es:r,output:n})=>ik(r)?nw(new FM(e,t,Tc(r),Tc(n))):nk(Tc(n));if(u&&u.length>0){let e=u;return pJ(()=>{let t={es:sC(p),output:sC(d)};return pv(_Y(e,e=>e(t),{concurrency:o,batching:l,discard:!0}),()=>b(t))})}return b({output:d,es:p})}}case"TypeLiteral":{if(0===e.propertySignatures.length&&0===e.indexSignatures.length)return Tn(e,ep);let r=[],n={},i=[];for(let s of e.propertySignatures)r.push([F9(s.type,t),s]),n[s.name]=null,i.push(s.name);let s=e.indexSignatures.map(e=>[F9(e.parameter,t),F9(e.type,t),e.parameter]),a=I2.make(e.indexSignatures.map(e=>e.parameter).concat(i.map(e=>et(e)?new If(e):new Iu(e)))),o=F9(a,t),l=Te(e),c=Tt(e);return(t,u)=>{let h,p;if(!eg(t))return nw(new F$(e,t));let f=u?.errors==="all",d=[],m=0,g=u?.onExcessProperty==="error",b=u?.onExcessProperty==="preserve",x={};if(g||b)for(let r of h=E4(t)){let n=o(r,u);if(nv(n)&&nS(n))if(g){let n=new FN(r,t,new FA(t[r],`is unexpected, expected: ${String(a)}`));if(!f)return nw(new FM(e,t,n,x));d.push([m++,n]);continue}else x[r]=t[r]}let y=u?.exact===!0;for(let n=0;n<r.length;n++){let i=r[n][1],s=i.name,a=Object.prototype.hasOwnProperty.call(t,s);if(!a){if(i.isOptional)continue;else if(y){let r=new FN(s,t,new Fj(i));if(!f)return nw(new FM(e,t,r,x));d.push([m++,r]);continue}}let o=(0,r[n][0])(t[s],u);if(nv(o)){if(nS(o)){let r=new FN(s,t,a?o.left:new Fj(i));if(!f)return nw(new FM(e,t,r,x));d.push([m++,r]);continue}x[s]=o.right}else{let r=m++;p||(p=[]),p.push(({es:n,output:l})=>pv(pp(o),o=>{if(nS(o)){let c=new FN(s,t,a?o.left:new Fj(i));return f?(n.push([r,c]),pZ):nw(new FM(e,t,c,l))}return l[s]=o.right,pZ}))}}for(let r=0;r<s.length;r++){let i=s[r],a=i[0],o=i[1];for(let r of E5(t,i[2])){let i=a(r,u);if(nv(i)&&n_(i)){let i=o(t[r],u);if(nv(i))if(nS(i)){let n=new FN(r,t,i.left);if(!f)return nw(new FM(e,t,n,x));d.push([m++,n]);continue}else Object.prototype.hasOwnProperty.call(n,r)||(x[r]=i.right);else{let s=m++,a=r;p||(p=[]),p.push(({es:o,output:l})=>pv(pp(i),i=>{if(!nS(i))return Object.prototype.hasOwnProperty.call(n,r)||(l[r]=i.right),pZ;{let r=new FN(a,t,i.left);return f?(o.push([s,r]),pZ):nw(new FM(e,t,r,l))}}))}}}}let v=({es:r,output:n})=>{if(ik(r))return nw(new FM(e,t,Tc(r),n));if(u?.propertyOrder==="original"){let e=h||E4(t);for(let t of i)-1===e.indexOf(t)&&e.push(t);let r={};for(let t of e)Object.prototype.hasOwnProperty.call(n,t)&&(r[t]=n[t]);return nk(r)}return nk(n)};if(p&&p.length>0){let e=p;return pJ(()=>{let t={es:sC(d),output:Object.assign({},x)};return pv(_Y(e,e=>e(t),{concurrency:l,batching:c,discard:!0}),()=>v(t))})}return v({es:d,output:x})}}case"Union":{let r=Ts(e.types,t),n=E4(r.keys),i=n.length,s=e.types.length,a=new Map;for(let r=0;r<s;r++)a.set(e.types[r],F9(e.types[r],t));let o=Te(e)??1,l=Tt(e);return(t,c)=>{let u,h=[],p=0,f=[];if(i>0)if(eo(t))for(let e=0;e<i;e++){let i=n[e],a=r.keys[i].buckets;if(Object.prototype.hasOwnProperty.call(t,i)){let e=String(t[i]);if(Object.prototype.hasOwnProperty.call(a,e))f=f.concat(a[e]);else{let{candidates:e,literals:n}=r.keys[i],a=I2.make(n),o=e.length===s?new IV([new IH(i,a,!1,!0)],[]):I2.make(e);h.push([p++,new FM(o,t,new FN(i,t,new F$(a,t[i])))])}}else{let{candidates:e,literals:n}=r.keys[i],a=new IH(i,I2.make(n),!1,!0),o=e.length===s?new IV([a],[]):I2.make(e);h.push([p++,new FM(o,t,new FN(i,t,new Fj(a)))])}}else{let n=r.candidates.length===s?e:I2.make(r.candidates);h.push([p++,new F$(n,t)])}r.otherwise.length>0&&(f=f.concat(r.otherwise));for(let e=0;e<f.length;e++){let r=f[e],n=a.get(r)(t,c);if(nv(n)&&(!u||0===u.length))if(n_(n))return n;else h.push([p++,n.left]);else{let e=p++;u||(u=[]),u.push(t=>pJ(()=>"finalResult"in t?pZ:pv(pp(n),r=>(n_(r)?t.finalResult=r:t.es.push([e,r.left]),pZ))))}}let d=r=>ik(r)?1===r.length&&"Type"===r[0][1]._tag?nw(r[0][1]):nw(new FM(e,t,Tc(r))):nw(new F$(e,t));if(u&&u.length>0){let e=u;return pJ(()=>{let t={es:sC(h)};return pv(_Y(e,e=>e(t),{concurrency:o,batching:l,discard:!0}),()=>"finalResult"in t?t.finalResult:d(t.es))})}return d(h)}}case"Suspend":{let r=E6(()=>F9(Fc(e.f(),e.annotations),t));return(e,t)=>r()(e,t)}}},Tn=(e,t)=>r=>t(r)?nk(r):nw(new F$(e,r)),Ti=(e,t)=>{switch(e._tag){case"Declaration":{let r=It(e);if(tF(r))return Ti(r.value,t);break}case"TypeLiteral":{let r=[];for(let n=0;n<e.propertySignatures.length;n++){let i=e.propertySignatures[n],s=t?FC(i.type):Fv(i.type);Ih(s)&&!i.isOptional&&r.push([i.name,s])}return r}case"TupleType":{let r=[];for(let n=0;n<e.elements.length;n++){let i=e.elements[n],s=t?FC(i.type):Fv(i.type);Ih(s)&&!i.isOptional&&r.push([n,s])}return r}case"Refinement":return Ti(e.from,t);case"Suspend":return Ti(e.f(),t);case"Transformation":return Ti(t?e.from:e.to,t)}return[]},Ts=(e,t)=>{let r={},n=[],i=[];for(let s=0;s<e.length;s++){let a=e[s],o=Ti(a,t);if(o.length>0){i.push(a);for(let e=0;e<o.length;e++){let[t,n]=o[e],i=String(n.literal);r[t]=r[t]||{buckets:{},literals:[],candidates:[]};let s=r[t].buckets;if(Object.prototype.hasOwnProperty.call(s,i)){if(e<o.length-1)continue;s[i].push(a),r[t].literals.push(n),r[t].candidates.push(a)}else{s[i]=[a],r[t].literals.push(n),r[t].candidates.push(a);break}}}else n.push(a)}return{keys:r,otherwise:n,candidates:i}},Ta=e=>I9(e)?Ta(e.from):e,To=(e,t,r,n)=>{if(n?.isEffectAllowed===!0||nv(e))return e;let i=new ys,s=kI(e,{scheduler:i});i.flush();let a=s.unsafePoll();if(a){if(dl(a))return nk(a.value);let e=a.cause;return g_(e)?nw(e.error):nw(new FP(t,r,hD(e)))}return nw(new FP(t,r,"cannot be be resolved synchronously, this is caused by using runSync on an effect that performs async work"))},Tl=([e],[t])=>e>t?1:e<t?-1:0;function Tc(e){return e.sort(Tl).map(e=>e[1])}let Tu=(e,t)=>{switch(e._tag){case"FinalTransformation":return t?e.decode:e.encode;case"ComposeTransformation":return nk;case"TypeLiteralTransformation":return r=>{let n=nk(r);for(let r of e.propertySignatureTransformations){let[e,i]=t?[r.from,r.to]:[r.to,r.from],s=t?r.decode:r.encode;n=FV(n,t=>{let r=s(Object.prototype.hasOwnProperty.call(t,e)?tR(t[e]):iC());return delete t[e],tF(r)&&(t[i]=r.value),t})}return n}}},Th=(e,t=[])=>({value:e,forest:t}),Tp={formatIssue:e=>FV(TR(e),Tf),formatIssueSync:e=>{let t=Tp.formatIssue(e);return nv(t)?nN(t):kR(t)},formatError:e=>Tp.formatIssue(e.issue),formatErrorSync:e=>Tp.formatIssueSync(e.issue)},Tf=e=>e.value+Td("\n",e.forest),Td=(e,t)=>{let r,n="",i=t.length;for(let s=0;s<i;s++){r=t[s];let a=s===i-1;n+=e+(a?"└":"├")+"─ "+r.value,n+=Td(e+(i>1&&!a?"│  ":"   "),r.forest)}return n},Tm=e=>{switch(e){case"Encoded":return"Encoded side transformation failure";case"Transformation":return"Transformation process failure";case"Type":return"Type side transformation failure"}},Tg=e=>{switch(e){case"From":return"From side refinement failure";case"Predicate":return"Predicate refinement failure"}},Tb=e=>"ast"in e?tR(e.ast):iC(),Tx=nk(void 0),Ty=e=>Tb(e).pipe(iz(O0),iE({onNone:()=>Tx,onSome:t=>{let r=t(e);return Z(r)?nk({message:r,override:!1}):h6(r)?pj(r,e=>({message:e,override:!1})):Z(r.message)?nk({message:r.message,override:r.override}):pj(r.message,e=>({message:e,override:r.override}))}})),Tv=e=>t=>t._tag===e,TS=Tv("Composite"),T_=Tv("Refinement"),Tw=Tv("Transformation"),Tk=e=>FW(Ty(e),t=>void 0!==t?!t.override&&(TS(e)||T_(e)&&"From"===e.kind||Tw(e)&&"Transformation"!==e.kind)?Tw(e)||T_(e)?Tk(e.issue):Tx:nk(t.message):Tx),TC=e=>Tb(e).pipe(iz(O7),iD(t=>t(e)),iR),TE=e=>FV(Tk(e),t=>t??TC(e)??function(e){var t;if(void 0!==e.message)return e.message;let r=I9(e.ast)?O4(t=e.ast).pipe(iI(()=>O2(t)),iI(()=>O3(t)),iI(()=>O5(t)),iO(()=>`{ ${t.from} | filter }`)):String(e.ast);return`Expected ${r}, actual ${E7(e.actual)}`}(e)),TO=e=>TC(e)??String(e.ast),TI=e=>e.message??"is forbidden",TF=e=>e.message??"is unexpected",TT=e=>{let t=O1(e.ast);if(tF(t)){let e=t.value();return Z(e)?nk(e):e}return nk(e.message??"is missing")},TR=e=>{switch(e._tag){case"Type":return FV(TE(e),Th);case"Forbidden":return nk(Th(TO(e),[Th(TI(e))]));case"Unexpected":return nk(Th(TF(e)));case"Missing":return FV(TT(e),Th);case"Transformation":return FW(Tk(e),t=>void 0!==t?nk(Th(t)):FV(TR(e.issue),t=>Th(TO(e),[Th(Tm(e.kind),[t])])));case"Refinement":return FW(Tk(e),t=>void 0!==t?nk(Th(t)):FV(TR(e.issue),t=>Th(TO(e),[Th(Tg(e.kind),[t])])));case"Pointer":return FV(TR(e.issue),t=>Th(On(e.path),[t]));case"Composite":return FW(Tk(e),t=>{if(void 0!==t)return nk(Th(t));let r=TO(e);return Oe(e.issues)?FV(_Y(e.issues,TR),e=>Th(r,e)):FV(TR(e.issues),e=>Th(r,[e]))})}},TN=(e,t,r)=>({_tag:e,path:t,message:r}),TA=U(e=>el(e[0]),(e,...t)=>{let r={};for(let n of t)n in e&&(r[n]=e[n]);return r}),Tj=U(e=>el(e[0]),(e,...t)=>{let r={...e};for(let e of t)delete r[e];return r}),TM=Symbol.for("effect/Schema");function Tz(e){return class{static{this.ast=e}static annotations(e){return Tz(TL(this.ast,e))}static pipe(){return e6(this,arguments)}static toString(){return String(e)}static{this[TM]=TD}constructor(){this[TM]=TD}}}let TD={_A:e=>e,_I:e=>e,_R:e=>e},T$={schemaId:OR,message:ON,missingMessage:OA,identifier:Oj,title:OM,description:OD,examples:O$,default:OP,documentation:OJ,jsonSchema:OL,arbitrary:OU,pretty:Oq,equivalence:OB,concurrency:OH,batching:OK,parseIssueTitle:OW,parseOptions:OV,decodingFallback:OG},TP=e=>{if(!e)return{};let t={...e};for(let r in T$)r in e&&(t[T$[r]]=e[r],delete t[r]);return t},TL=(e,t)=>Fc(e,TP(t)),TU=e=>String(e.ast),Tq=e=>Tz(FC(e.ast)),TB=e=>Tz(Fv(e.ast)),TJ=(e,t)=>{let r=F3(e,t);return(e,t)=>FG(r(e,t),FB)},TH=(e,t)=>{let r=TJ(e,t);return(e,t)=>kF(r(e,t))},TK=e=>ec(e,TM)&&el(e[TM]);function TW(...e){return ss(e)?function e(t,r=function(e){return I5(e)?I2.make(I3(e,e=>new Iu(e))):new Iu(e[0])}(t)){return class extends Tz(r){static annotations(t){return e(this.literals,TL(this.ast,t))}static{this.literals=[...t]}}}(e):T2}let TV=(e,t,r)=>TY(e,new Il(e.map(e=>e.ast),(...e)=>t.decode(...e.map(Tz)),(...e)=>t.encode(...e.map(Tz)),TP(r))),TG=(e,t)=>{let r=()=>(t,r,n)=>e(t)?FJ(t):nw(new F$(n,t));return TY([],new Il([],r,r,TP(t)))};function TY(e,t){return class extends Tz(t){static annotations(e){return TY(this.typeParameters,TL(this.ast,e))}static{this.typeParameters=[...e]}}}let TZ=function(){if(Array.isArray(arguments[0])){let e=arguments[0],t=arguments[1],r=arguments[2];return TV(e,t,r)}let e=arguments[0],t=arguments[1];return TG(e,t)},TQ=Symbol.for("effect/SchemaId/InstanceOf"),TX=(e,t)=>TZ(t=>t instanceof e,{title:e.name,description:`an instance of ${e.name}`,pretty:()=>String,schemaId:TQ,[TQ]:{constructor:e},...t});class T0 extends Tz(Im){}class T1 extends Tz(Ip){}class T2 extends Tz(Ix){}class T3 extends Tz(Iv){}class T5 extends Tz(IA){}class T4 extends Tz(IM){}class T6 extends Tz(Ik){}class T8 extends Tz(IO){}class T7 extends Tz(IT){}let T9=e=>I2.make(e.map(e=>e.ast));function Re(...e){return I5(e)?function e(t,r=T9(t)){return class extends Tz(r){static annotations(t){return e(this.members,TL(this.ast,t))}static{this.members=[...t]}}}(e):ss(e)?e[0]:T2}let Rt=e=>Re(e,T1),Rr=e=>Re(e,T0),Rn=e=>Re(e,T1,T0),Ri=e=>new Rs(new IU(e.ast,!1),e);class Rs{constructor(e,t){this.ast=e,this.from=t}annotations(e){return new Rs(new IU(this.ast.type,this.ast.isOptional,{...this.ast.annotations,...TP(e)}),this.from)}toString(){return`${this.ast.type}${this.ast.isOptional?"?":""}`}}let Ra=(e,t)=>new IB(e.map(e=>TK(e)?new IU(e.ast,!1):e.ast),t.map(e=>TK(e)?new IL(e.ast):e.ast),!0);function Ro(e,t,r=Ra(e,t)){return class extends Tz(r){static annotations(e){return Ro(this.elements,this.rest,TL(this.ast,e))}static{this.elements=[...e]}static{this.rest=[...t]}}}function Rl(...e){return Array.isArray(e[0])?Ro(e[0],e.slice(1)):Ro(e,[])}let Rc=e=>(function e(t,r){return class extends Ro([],[t],r){static annotations(t){return e(this.value,TL(this.ast,t))}static{this.value=t}}})(e),Ru=e=>(function e(t,r){return class extends Ro([t],[t],r){static annotations(t){return e(this.value,TL(this.ast,t))}static{this.value=t}}})(e),Rh=e=>e?'"?:"':'":"';class Rp extends IU{constructor(e,t,r,n,i){super(e,t,n),this._tag="PropertySignatureDeclaration",this.isReadonly=r,this.defaultValue=i}toString(){let e=Rh(this.isOptional),t=String(this.type);return`PropertySignature<${e}, ${t}, never, ${e}, ${t}>`}}class Rf extends IU{constructor(e,t,r,n,i){super(e,t,n),this.isReadonly=r,this.fromKey=i}}class Rd extends IU{constructor(e,t,r,n,i){super(e,t,n),this.isReadonly=r,this.defaultValue=i}}let Rm=e=>void 0===e?"never":Z(e)?JSON.stringify(e):String(e);class Rg{constructor(e,t,r,n){this._tag="PropertySignatureTransformation",this.from=e,this.to=t,this.decode=r,this.encode=n}toString(){return`PropertySignature<${Rh(this.to.isOptional)}, ${this.to.type}, ${Rm(this.from.fromKey)}, ${Rh(this.from.isOptional)}, ${this.from.type}>`}}let Rb=(e,t)=>{switch(e._tag){case"PropertySignatureDeclaration":return new Rp(e.type,e.isOptional,e.isReadonly,{...e.annotations,...t},e.defaultValue);case"PropertySignatureTransformation":return new Rg(e.from,new Rd(e.to.type,e.to.isOptional,e.to.isReadonly,{...e.to.annotations,...t},e.to.defaultValue),e.decode,e.encode)}},Rx=Symbol.for("effect/PropertySignature"),Ry=e=>ec(e,Rx);class Rv{constructor(e){this[Rx]=null,this.ast=e}pipe(){return e6(this,arguments)}annotations(e){return new Rv(Rb(this.ast,TP(e)))}toString(){return String(this.ast)}}let RS=e=>new Rv(e);class R_ extends Rv{constructor(e,t){super(e),this.from=t}annotations(e){return new R_(Rb(this.ast,TP(e)),this.from)}}let Rw=e=>new R_(new Rp(e.ast,!1,!0,{},void 0),e),Rk=U(2,(e,t)=>{let r=e.ast;switch(r._tag){case"PropertySignatureDeclaration":return RS(new Rp(r.type,r.isOptional,r.isReadonly,r.annotations,t));case"PropertySignatureTransformation":return RS(new Rg(r.from,new Rd(r.to.type,r.to.isOptional,r.to.isReadonly,r.to.annotations,t),r.decode,r.encode))}}),RC=e=>AST.pruneUndefined(e,RC,e=>{let t=RC(e.to);if(t)return new AST.Transformation(e.from,t,e.transformation)}),RE=(e,t,r)=>RS(new Rg(new Rf(e.ast,!0,!0,{},void 0),new Rd(t.ast,!1,!0,{},void 0),e=>tR(r.decode(e)),iz(r.encode))),RO=(e,t,r)=>RS(new Rg(new Rf(e.ast,!0,!0,{},void 0),new Rd(t.ast,!0,!0,{},void 0),r.decode,r.encode)),RI=(e,t)=>{let r=t?.exact,n=t?.default,i=t?.nullable,s=t?.as=="Option",a=t?.onNoneEncoding?iI(t.onNoneEncoding):q;if(r)if(n)if(i)return Rk(RE(Rt(e),TB(e),{decode:iE({onNone:n,onSome:e=>null===e?n():e}),encode:tR}),n).ast;else return Rk(RE(e,TB(e),{decode:iE({onNone:n,onSome:q}),encode:tR}),n).ast;else if(s)if(i)return RE(Rt(e),NW(TB(e)),{decode:iP(es),encode:a}).ast;else return RE(e,NW(TB(e)),{decode:q,encode:q}).ast;else if(i)return RO(Rt(e),TB(e),{decode:iP(es),encode:q}).ast;else return new Rp(e.ast,!0,!0,{},void 0);if(n)if(i)return Rk(RE(Rn(e),TB(e),{decode:iE({onNone:n,onSome:e=>null==e?n():e}),encode:tR}),n).ast;else return Rk(RE(Rr(e),TB(e),{decode:iE({onNone:n,onSome:e=>void 0===e?n():e}),encode:tR}),n).ast;if(s)if(i)return RE(Rn(e),NW(TB(e)),{decode:iP(e=>null!=e),encode:a}).ast;else return RE(Rr(e),NW(TB(e)),{decode:iP(ei),encode:a}).ast;if(i)return RO(Rn(e),Rr(TB(e)),{decode:iP(es),encode:q}).ast;return new Rp(Rr(e).ast,!0,!0,{},void 0)},RF=e=>new R_(new Rp(e.ast===Im||e.ast===Ix?Im:Rr(e).ast,!0,!0,{},void 0),e),RT=U(e=>TK(e[0]),(e,t)=>new R_(RI(e,t),e)),RR=Fx([OA]),RN=(e,t)=>{let r=E4(e),n=[];if(r.length>0){let i=[],s=[],a=[];for(let t=0;t<r.length;t++){let o=r[t],l=e[o];if(Ry(l)){let e=l.ast;switch(e._tag){case"PropertySignatureDeclaration":{let t=e.type,r=e.isOptional,a=e.annotations;i.push(new IH(o,t,r,!0,RR(e))),s.push(new IH(o,Fv(t),r,!0,a)),n.push(new IH(o,t,r,!0,a));break}case"PropertySignatureTransformation":{let t=e.from.fromKey??o;i.push(new IH(t,e.from.type,e.from.isOptional,!0,e.from.annotations)),s.push(new IH(o,e.to.type,e.to.isOptional,!0,e.to.annotations)),a.push(new Fa(t,o,e.decode,e.encode))}}}else i.push(new IH(o,l.ast,!1,!0)),s.push(new IH(o,Fv(l.ast),!1,!0)),n.push(new IH(o,l.ast,!1,!0))}if(ss(a)){let e=[],r=[];for(let n of t){let{indexSignatures:t,propertySignatures:a}=Fg(n.key.ast,n.value.ast);a.forEach(e=>{i.push(e),s.push(new IH(e.name,Fv(e.type),e.isOptional,e.isReadonly,e.annotations))}),t.forEach(t=>{e.push(t),r.push(new IW(t.parameter,Fv(t.type),t.isReadonly))})}return new Ft(new IV(i,e,{[Oz]:"Struct (Encoded side)"}),new IV(s,r,{[Oz]:"Struct (Type side)"}),new Fo(a))}}let i=[];for(let e of t){let{indexSignatures:t,propertySignatures:r}=Fg(e.key.ast,e.value.ast);r.forEach(e=>n.push(e)),t.forEach(e=>i.push(e))}return new IV(n,i)},RA=(e,t)=>{for(let r of E4(e)){let n=e[r];if(void 0===t[r]&&Ry(n)){let e=n.ast,i="PropertySignatureDeclaration"===e._tag?e.defaultValue:e.to.defaultValue;void 0!==i&&(t[r]=i())}}return t};function Rj(e,t,r=RN(e,t)){return class extends Tz(r){static annotations(e){return Rj(this.fields,this.records,TL(this.ast,e))}static{this.fields={...e}}static{this.records=[...t]}static{this.make=(t,r)=>{let n=RA(e,{...t});return Ah(r)?n:F4(this)(n)}}static pick(...t){return RM(TA(e,...t))}static omit(...t){return RM(Tj(e,...t))}}}function RM(e,...t){return Rj(e,t)}let Rz=e=>TW(e).pipe(Rw,Rk(()=>e)),RD=(e,t)=>RM({_tag:Rz(e),...t}),R$=e=>(function e(t,r,n){return class extends Rj({},[{key:t,value:r}],n){static annotations(n){return e(t,r,TL(this.ast,n))}static{this.key=t}static{this.value=r}}})(e.key,e.value),RP=(e,t,r)=>{if(IZ(e)&&IZ(t)){let n=[...e.propertySignatures];for(let e of t.propertySignatures){let t=e.name,i=n.findIndex(e=>e.name===t);if(-1===i)n.push(e);else{let{isOptional:s,type:a}=n[i];n[i]=new IH(t,Rq(a,e.type,r.concat(t)),s,!0)}}return new IV(n,e.indexSignatures.concat(t.indexSignatures))}throw Error(Os(e,t,r))},RL=(_=[Oj],e=>{let t={...e.annotations};for(let e of _)delete t[e];return t}),RU=(e,t)=>t.map(t=>new I7(t,e.filter,RL(e))),Rq=(e,t,r)=>I2.make(RJ([e],[t],r)),RB=e=>I4(e)?e.types:[e],RJ=(e,t,r)=>sR(e,e=>sR(t,t=>{switch(t._tag){case"Literal":if(Z(t.literal)&&IC(e)||Q(t.literal)&&II(e)||X(t.literal)&&IR(e))return[t];break;case"StringKeyword":if(t===Ik){if(IC(e)||Ih(e)&&Z(e.literal))return[e];else if(I9(e))return RU(e,RJ(RB(e.from),[t],r))}else if(e===Ik)return[t];break;case"NumberKeyword":if(t===IO){if(II(e)||Ih(e)&&Q(e.literal))return[e];else if(I9(e))return RU(e,RJ(RB(e.from),[t],r))}else if(e===IO)return[t];break;case"BooleanKeyword":if(t===IT){if(IR(e)||Ih(e)&&X(e.literal))return[e];else if(I9(e))return RU(e,RJ(RB(e.from),[t],r))}else if(e===IT)return[t];break;case"Union":return RJ(RB(e),t.types,r);case"Suspend":return[new I8(()=>Rq(e,t.f(),r))];case"Refinement":return RU(t,RJ(RB(e),RB(t.from),r));case"TypeLiteral":switch(e._tag){case"Union":return RJ(e.types,[t],r);case"Suspend":return[new I8(()=>Rq(e.f(),t,r))];case"Refinement":return RU(e,RJ(RB(e.from),[t],r));case"TypeLiteral":return[RP(e,t,r)];case"Transformation":{let n=e.transformation,i=RP(e.from,t,r),s=RP(e.to,Fv(t),r);switch(n._tag){case"TypeLiteralTransformation":return[new Ft(i,s,new Fo(n.propertySignatureTransformations))];case"ComposeTransformation":return[new Ft(i,s,Fs)];case"FinalTransformation":return[new Ft(i,s,new Fn((e,t,r,i)=>FV(n.decode(e,t,r,i),t=>({...e,...t})),(e,t,r,i)=>FV(n.encode(e,t,r,i),t=>({...e,...t}))))]}}}break;case"Transformation":if(!Fr(e))return RJ([t],[e],r);if(Fl(t.transformation)&&Fl(e.transformation))return[new Ft(RP(e.from,t.from,r),RP(e.to,t.to,r),new Fo(t.transformation.propertySignatureTransformations.concat(e.transformation.propertySignatureTransformations)))]}throw Error(Os(e,t,r))})),RH=U(2,(e,t)=>Tz(Rq(e.ast,t.ast,[]))),RK=U(e=>TK(e[1]),(e,t)=>RQ(e,t,FI(e.ast,t.ast))),RW=e=>Tz(new I8(()=>e().ast)),RV=Symbol.for("effect/SchemaId/Refine"),RG=(e,t,r)=>{if(X(e))return e?iC():tR(new F$(t,r));if(Z(e))return tR(new F$(t,r,e));if(void 0!==e){if("_tag"in e)return tR(e);let n=new F$(t,r,e.message);return tR(ss(e.path)?new FN(e.path,r,n):n)}return iC()},RY=(e,t,r)=>{if(Ot(e))return RG(e,t,r);if(ss(e)){let n=sA(e,e=>RG(e,t,r));if(ss(n))return tR(1===n.length?n[0]:new FM(t,r,n))}return iC()};function RZ(e,t){return r=>{function n(t,r,n){return RY(e(t,r,n),n,t)}let i=new I7(r.ast,n,TP(t));return function e(t,r,n){return class extends Tz(n){static annotations(t){return e(this.from,this.filter,TL(this.ast,t))}static{this[RV]=t}static{this.from=t}static{this.filter=r}static{this.make=(e,t)=>Ah(t)?e:F4(this)(e)}}}(r,n,i)}}function RQ(e,t,r){return class extends Tz(r){static annotations(e){return RQ(this.from,this.to,TL(this.ast,e))}static{this.from=e}static{this.to=t}}}let RX=U(e=>TK(e[0])&&TK(e[1]),(e,t,r)=>RQ(e,t,new Ft(e.ast,t.ast,new Fn(r.decode,r.encode)))),R0=U(e=>TK(e[0])&&TK(e[1]),(e,t,r)=>RX(e,t,{strict:!0,decode:(e,t,n,i)=>FJ(r.decode(e,i)),encode:(e,t,n,i)=>FJ(r.encode(e,i))})),R1=Symbol.for("effect/SchemaId/Trimmed"),R2=e=>t=>t.pipe(RZ(e=>e===e.trim(),{schemaId:R1,title:"trimmed",description:"a string with no leading or trailing whitespace",jsonSchema:{pattern:"^\\S[\\s\\S]*\\S$|^\\S$|^$"},...e})),R3=(e,t)=>r=>r.pipe(RZ(t=>t.length>=e,{schemaId:OI,title:`minLength(${e})`,description:`a string at least ${e} character(s) long`,jsonSchema:{minLength:e},...t})),R5=Symbol.for("effect/SchemaId/Pattern"),R4=Symbol.for("effect/SchemaId/StartsWith"),R6=Symbol.for("effect/SchemaId/Lowercased"),R8=e=>t=>t.pipe(RZ(e=>e===e.toLowerCase(),{schemaId:R6,title:"lowercased",description:"a lowercase string",jsonSchema:{pattern:"^[^A-Z]*$"},...e})),R7=Symbol.for("effect/SchemaId/Uppercased"),R9=e=>t=>t.pipe(RZ(e=>e===e.toUpperCase(),{schemaId:R7,title:"uppercased",description:"an uppercase string",jsonSchema:{pattern:"^[^a-z]*$"},...e})),Ne=Symbol.for("effect/SchemaId/Capitalized"),Nt=e=>t=>t.pipe(RZ(e=>e[0]?.toUpperCase()===e[0],{schemaId:Ne,title:"capitalized",description:"a capitalized string",jsonSchema:{pattern:"^[^a-z]?.*$"},...e})),Nr=Symbol.for("effect/SchemaId/Uncapitalized"),Nn=e=>t=>t.pipe(RZ(e=>e[0]?.toLowerCase()===e[0],{schemaId:Nr,title:"uncapitalized",description:"a uncapitalized string",jsonSchema:{pattern:"^[^A-Z]?.*$"},...e})),Ni=e=>R3(1,{title:"nonEmptyString",description:"a non empty string",...e});class Ns extends T6.pipe(R2({identifier:"Trimmed"})){}let Na=e=>e instanceof Error?e.message:String(e),No=e=>RX(T6.annotations({description:"a string to be decoded into JSON"}),T3,{strict:!0,decode:(t,r,n)=>FH({try:()=>JSON.parse(t,e?.reviver),catch:e=>new F$(n,t,Na(e))}),encode:(t,r,n)=>FH({try:()=>JSON.stringify(t,e?.replacer,e?.space),catch:e=>new F$(n,t,Na(e))})}).annotations({title:"parseJson",schemaId:Io}),Nl=(e,t)=>TK(e)?RK(Nl(t),e):No(e);/^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/i.source,URL;let Nc=(e,t)=>r=>r.pipe(RZ(t=>t>e,{schemaId:Od,title:`greaterThan(${e})`,description:0===e?"a positive number":`a number greater than ${e}`,jsonSchema:{exclusiveMinimum:e},...t})),Nu=(e,t)=>r=>r.pipe(RZ(t=>t>=e,{schemaId:Om,title:`greaterThanOrEqualTo(${e})`,description:0===e?"a non-negative number":`a number greater than or equal to ${e}`,jsonSchema:{minimum:e},...t})),Nh=e=>t=>t.pipe(RZ(e=>Number.isSafeInteger(e),{schemaId:Ox,title:"int",description:"an integer",jsonSchema:{type:"integer"},...e})),Np=(e,t)=>r=>r.pipe(RZ(t=>t<e,{schemaId:Og,title:`lessThan(${e})`,description:0===e?"a negative number":`a number less than ${e}`,jsonSchema:{exclusiveMaximum:e},...t})),Nf=(e,t)=>r=>r.pipe(RZ(t=>t<=e,{schemaId:Ob,title:`lessThanOrEqualTo(${e})`,description:0===e?"a non-positive number":`a number less than or equal to ${e}`,jsonSchema:{maximum:e},...t})),Nd=(e,t,r)=>n=>n.pipe(RZ(r=>r>=e&&r<=t,{schemaId:O_,title:`between(${e}, ${t})`,description:`a number between ${e} and ${t}`,jsonSchema:{minimum:e,maximum:t},...r})),Nm=e=>Nu(0,{title:"nonNegative",...e});function Ng(e){return RX(e,T8,{strict:!1,decode:(e,t,r)=>FK(d1(e),()=>new F$(r,e,`Unable to decode ${JSON.stringify(e)} into a number`)),encode:e=>FJ(String(e))})}class Nb extends T8.pipe(Nh({identifier:"Int"})){}class Nx extends T8.pipe(Nm({identifier:"NonNegative"})){}let Ny=(e,t)=>{let r=Symbol.keyFor(e);return void 0===r?nw(new F$(t,e,`Unable to encode a unique symbol ${String(e)} into a string`)):FJ(r)},Nv=e=>FJ(Symbol.for(e));class NS extends RX(T6.annotations({description:"a string to be decoded into a bigint"}),T5,{strict:!0,decode:(e,t,r)=>FK(an(e),()=>new F$(r,e,`Unable to decode ${JSON.stringify(e)} into a bigint`)),encode:e=>FJ(String(e))}).annotations({identifier:"BigInt"}){}((e,t)=>r=>r.pipe(RZ(t=>t>=e,{schemaId:Ok,[Ok]:{min:e},title:`greaterThanOrEqualToBigInt(${e})`,description:0n===e?"a non-negative bigint":`a bigint greater than or equal to ${e}n`,...t})))(0n,{title:"nonNegativeBigInt",identifier:"NonNegativeBigintFromSelf"});let N_=e=>t=>e(t).map(n6),Nw=(e,t,r,n)=>FY(e,{onFailure:e=>new FM(r,n,e),onSuccess:t}),Nk=e=>(t,r,n)=>n4(t)?Nw(e(n8(t),r),n6,n,t):nw(new F$(n,t)),NC=e=>TZ([e],{decode:e=>Nk(F3(e)),encode:e=>Nk(F5(e))},{description:"Redacted(<redacted>)",pretty:()=>()=>"Redacted(<redacted>)",arbitrary:N_,equivalence:n7}),NE=Nx.pipe(Nh()).annotations({identifier:"NonNegativeInt"}),NO=RD("Millis",{millis:NE}),NI=RD("Nanos",{nanos:NS}),NF=RD("Infinity",{}),NT=Rl(Ri(NE).annotations({title:"seconds"}),Ri(NE).annotations({title:"nanos"})).annotations({identifier:"FiniteHRTime"});class NR extends TZ(ef,{identifier:"Uint8ArrayFromSelf",pretty:()=>e=>`new Uint8Array(${JSON.stringify(Array.from(e))})`,arbitrary:()=>e=>e.uint8Array(),equivalence:()=>sq(eJ)}){}let NN=(w="Uint8ArrayFromBase64",k=e=>n$(e),C=e=>"string"==typeof e?nD(nz.encode(e)):nD(e),RX(T6.annotations({description:"a string to be decoded into a Uint8Array"}),NR,{strict:!0,decode:(e,t,r)=>nO(k(e),t=>new F$(r,e,t.message)),encode:e=>FJ(C(e))}).annotations({identifier:w})),NA=Symbol.for("effect/SchemaId/ValidDate"),Nj=Of;class NM extends TZ(ed,{identifier:"DateFromSelf",schemaId:Nj,[Nj]:{noInvalidDate:!1},description:"a potentially invalid Date instance",pretty:()=>e=>`new Date(${JSON.stringify(e)})`,arbitrary:()=>e=>e.date({noInvalidDate:!1}),equivalence:()=>nX}){}e=>xM(e)&&x$(e);let Nz=()=>e=>e.integer({min:-432e5,max:504e5}).map(xh);class ND extends TZ(xz,{identifier:"TimeZoneOffsetFromSelf",description:"a TimeZone.Offset instance",pretty:()=>e=>e.toString(),arbitrary:Nz}){}let N$=()=>e=>e.constantFrom(...Intl.supportedValuesOf("timeZone")).map(xu);class NP extends TZ(xD,{identifier:"TimeZoneNamedFromSelf",description:"a TimeZone.Named instance",pretty:()=>e=>e.toString(),arbitrary:N$}){}let NL=e=>e.oneof(Nz()(e),N$()(e));e=>xM(e)&&xP(e);let NU=RM({_tag:TW("None")}).annotations({description:"NoneEncoded"}),Nq=e=>RM({_tag:TW("Some"),value:e}).annotations({description:`SomeEncoded<${TU(e)}>`}),NB=e=>"None"===e._tag?iC():tR(e.value),NJ=(e,t)=>r=>r.oneof(t,r.record({_tag:r.constant("None")}),r.record({_tag:r.constant("Some"),value:e(r)})).map(NB),NH=e=>iE({onNone:()=>"none()",onSome:t=>`some(${e(t)})`}),NK=e=>(t,r,n)=>tO(t)?tI(t)?FJ(iC()):Nw(e(t.value,r),tR,n,t):nw(new F$(n,t)),NW=e=>TZ([e],{decode:e=>NK(F3(e)),encode:e=>NK(F5(e))},{description:`Option<${TU(e)}>`,pretty:NH,arbitrary:NJ,equivalence:iL}),NV=(e,t,r)=>n=>{let i=n.array(n.tuple(e(n),t(n)));return(void 0!==r.depthIdentifier?n.oneof(r,n.constant([]),i):i).map(e=>new Map(e))},NG=(e,t)=>r=>`new Map([${Array.from(r.entries()).map(([r,n])=>`[${e(r)}, ${t(n)}]`).join(", ")}])`,NY=(e,t)=>{let r=array_.getEquivalence(Equivalence.make(([r,n],[i,s])=>e(r,i)&&t(n,s)));return Equivalence.make((e,t)=>r(Array.from(e.entries()),Array.from(t.entries())))},NZ=e=>(t,r,n)=>Predicate.isMap(t)?Nw(e(Array.from(t.entries()),r),e=>new Map(e),n,t):ParseResult.fail(new ParseResult.Type(n,t)),NQ=(e,t,r)=>TZ([e,t],{decode:(e,t)=>NZ(ParseResult.decodeUnknown(Rc(Rl(e,t)))),encode:(e,t)=>NZ(ParseResult.encodeUnknown(Rc(Rl(e,t))))},{description:r,pretty:NG,arbitrary:NV,equivalence:NY}),NX=(e,t)=>r=>{let n=r.array(e(r));return(void 0!==t.depthIdentifier?r.oneof(t,r.constant([]),n):n).map(e=>new Set(e))},N0=e=>t=>`new Set([${Array.from(t.values()).map(t=>e(t)).join(", ")}])`,N1=e=>{let t=array_.getEquivalence(e);return Equivalence.make((e,r)=>t(Array.from(e.values()),Array.from(r.values())))},N2=e=>(t,r,n)=>Predicate.isSet(t)?Nw(e(Array.from(t.values()),r),e=>new Set(e),n,t):ParseResult.fail(new ParseResult.Type(n,t)),N3=(e,t)=>TZ([e],{decode:e=>N2(ParseResult.decodeUnknown(Rc(e))),encode:e=>N2(ParseResult.encodeUnknown(Rc(e)))},{description:t,pretty:N0,arbitrary:NX,equivalence:N1}),N5=()=>e=>`BigDecimal(${s9(s2(e))})`,N4=()=>e=>e.tuple(e.bigInt(),e.integer({min:0,max:18})).map(([e,t])=>sZ(e,t)),N6=(e,t)=>r=>{let n=r.array(e(r));return(void 0!==t.depthIdentifier?r.oneof(t,r.constant([]),n):n).map(chunk_.fromIterable)},N8=e=>t=>`Chunk(${chunk_.toReadonlyArray(t).map(e).join(", ")})`,N7=e=>(t,r,n)=>chunk_.isChunk(t)?chunk_.isEmpty(t)?ParseResult.succeed(chunk_.empty()):Nw(e(chunk_.toReadonlyArray(t),r),chunk_.fromIterable,n,t):ParseResult.fail(new ParseResult.Type(n,t)),N9=e=>t=>fastCheck_.array(e(t),{minLength:1}).map(e=>chunk_.unsafeFromNonEmptyArray(e)),Ae=e=>t=>`NonEmptyChunk(${chunk_.toReadonlyArray(t).map(e).join(", ")})`,At=e=>(t,r,n)=>chunk_.isChunk(t)&&chunk_.isNonEmpty(t)?Nw(e(chunk_.toReadonlyArray(t),r),chunk_.unsafeFromNonEmptyArray,n,t):ParseResult.fail(new ParseResult.Type(n,t)),Ar=e=>TK(e)||Ry(e),An=e=>E4(e).every(t=>Ar(e[t])),Ai=e=>"fields"in e?e.fields:Ai(e[RV]),As=e=>An(e)?RM(e):TK(e)?e:RM(Ai(e)),Aa=e=>An(e)?e:Ai(e),Ao=e=>(t,r)=>Ad({kind:"Class",identifier:e,schema:As(t),fields:Aa(t),Base:ck,annotations:r}),Al=e=>Rk(Rw(TW(e)),()=>e),Ac=e=>(t,r,n)=>{class i extends gv{}i.prototype.name=t;let s=Aa(r),a=As(r),o={_tag:Al(t)},l=Au(o,s),c="message"in l;class u extends Ad({kind:"TaggedError",identifier:e??t,schema:RH(a,RM(o)),fields:l,Base:i,annotations:n,disableToString:!0}){static{this._tag=t}}return c||Object.defineProperty(u.prototype,"message",{get(){return`{ ${E4(s).map(e=>`${E9(e)}: ${E7(this[e])}`).join(", ")} }`},enumerable:!1,configurable:!0}),u},Au=(e,t)=>{let r={...e};for(let n of E4(t)){if(n in e)throw Error(Op(n));r[n]=t[n]}return r};function Ah(e){return X(e)?e:e?.disableValidation??!1}let Ap=Y("effect/Schema/astCache",()=>new WeakMap),Af=e=>void 0===e?[]:Array.isArray(e)?e:[e],Ad=({Base:e,annotations:t,disableToString:r,fields:n,identifier:i,kind:s,schema:a})=>{let o=Symbol.for(`effect/Schema/${s}/${i}`),[l,c,u]=Af(t),h=TB(a),p=h.annotations({identifier:i,...l}),f=h.annotations({[Oz]:`${i} (Type side)`,...l}),d=a.annotations({[Oz]:`${i} (Constructor)`,...l}),m=a.annotations({[Oz]:`${i} (Encoded side)`,...u}),g=a.annotations({[Ii]:i,...u,...l,...c}),b=e=>ec(e,o)&&F6(f)(e),x=class extends e{constructor(e={},t=!1){e={...e},"Class"!==s&&delete e._tag,e=RA(n,e),Ah(t)||(e=F4(d)(e)),super(e,!0)}static{this[TM]=TD}static get ast(){let e=Ap.get(this);return e||(e=R0(m,TZ([a],{decode:()=>(e,t,r)=>e instanceof this||b(e)?FJ(e):nw(new F$(r,e)),encode:()=>(e,t)=>e instanceof this?FJ(e):FV(F5(f)(e,t),e=>new this(e,!0))},{identifier:i,pretty:e=>t=>`${i}(${e(t)})`,arbitrary:e=>t=>e(t).map(e=>new this(e)),equivalence:q,[OY]:p.ast,...l}),{strict:!0,decode:e=>new this(e,!0),encode:q}).annotations({[OY]:g.ast,...c}).ast,Ap.set(this,e)),e}static pipe(){return e6(this,arguments)}static annotations(e){return Tz(this.ast).annotations(e)}static toString(){return`(${String(m)} <-> ${i})`}static make(...e){return new this(...e)}static{this.fields={...n}}static{this.identifier=i}static extend(e){return(t,r)=>{let i=Aa(t),o=As(t),l=Au(n,i);return Ad({kind:s,identifier:e,schema:RH(a,o),fields:l,Base:this,annotations:r})}}static transformOrFail(e){return(t,r,i)=>{let o=Au(n,t);return Ad({kind:s,identifier:e,schema:RX(a,TB(RM(o)),r),fields:o,Base:this,annotations:i})}}static transformOrFailFrom(e){return(t,r,i)=>{let o=Au(n,t);return Ad({kind:s,identifier:e,schema:RX(Tq(a),RM(o),r),fields:o,Base:this,annotations:i})}}get[o](){return o}};return!0!==r&&Object.defineProperty(x.prototype,"toString",{value(){return`${i}({ ${E4(n).map(e=>`${E9(e)}: ${E7(this[e])}`).join(", ")} })`},configurable:!0,writable:!0}),x},Am=RM({_tag:TW("None")}).annotations({identifier:"FiberIdNoneEncoded"}),Ag=Re(Am,RM({_tag:TW("Runtime"),id:Nb,startTimeMillis:Nb}).annotations({identifier:"FiberIdRuntimeEncoded"}),RM({_tag:TW("Composite"),left:RW(()=>Ag),right:RW(()=>Ag)}).annotations({identifier:"FiberIdCompositeEncoded"})).annotations({identifier:"FiberIdEncoded"}),Ab=e=>e.letrec(t=>({None:e.record({_tag:e.constant("None")}),Runtime:e.record({_tag:e.constant("Runtime"),id:e.integer(),startTimeMillis:e.integer()}),Composite:e.record({_tag:e.constant("Composite"),left:t("FiberId"),right:t("FiberId")}),FiberId:e.oneof(t("None"),t("Runtime"),t("Composite"))})).FiberId.map(Ay),Ax=e=>{switch(e._tag){case"None":return"FiberId.none";case"Runtime":return`FiberId.runtime(${e.id}, ${e.startTimeMillis})`;case"Composite":return`FiberId.composite(${Ax(e.right)}, ${Ax(e.left)})`}},Ay=e=>{switch(e._tag){case"None":return l7;case"Runtime":return l9(e.id,e.startTimeMillis);case"Composite":return ce(Ay(e.left),Ay(e.right))}};class Av extends R0(T3,T3,{strict:!0,decode:e=>{if(el(e)&&"message"in e&&"string"==typeof e.message){let t=Error(e.message,{cause:e});return"name"in e&&"string"==typeof e.name&&(t.name=e.name),t.stack="stack"in e&&"string"==typeof e.stack?e.stack:"",t}return String(e)},encode:e=>e instanceof Error?{name:e.name,message:e.message}:hL(e)}).annotations({identifier:"Defect"}){}let AS=(e,t)=>r=>{let n=r.array(e(r));return(void 0!==t.depthIdentifier?r.oneof(t,r.constant([]),n):n).map(hashSet_.fromIterable)},A_=e=>t=>`HashSet(${Array.from(t).map(t=>e(t)).join(", ")})`,Aw=e=>{let t=array_.getEquivalence(e);return Equivalence.make((e,r)=>t(Array.from(e),Array.from(r)))},Ak=e=>(t,r,n)=>hashSet_.isHashSet(t)?Nw(e(Array.from(t),r),hashSet_.fromIterable,n,t):ParseResult.fail(new ParseResult.Type(n,t)),AC=(e,t)=>r=>{let n=r.array(e(r));return(void 0!==t.depthIdentifier?r.oneof(t,r.constant([]),n):n).map(list_.fromIterable)},AE=e=>t=>`List(${Array.from(t).map(t=>e(t)).join(", ")})`,AO=e=>{let t=array_.getEquivalence(e);return Equivalence.make((e,r)=>t(Array.from(e),Array.from(r)))},AI=e=>(t,r,n)=>list_.isList(t)?Nw(e(Array.from(t),r),list_.fromIterable,n,t):ParseResult.fail(new ParseResult.Type(n,t)),AF=(e,t,r)=>n=>{let i=n.array(e(n));return(void 0!==r.depthIdentifier?n.oneof(r,n.constant([]),i):i).map(e=>sortedSet_.fromIterable(e,t))},AT=e=>t=>`new SortedSet([${Array.from(sortedSet_.values(t)).map(t=>e(t)).join(", ")}])`,AR=(e,t)=>(r,n,i)=>sortedSet_.isSortedSet(r)?Nw(e(Array.from(sortedSet_.values(r)),n),e=>sortedSet_.fromIterable(e,t),i,r):ParseResult.fail(new ParseResult.Type(i,r)),AN=RD("symbol",{key:T6}).annotations({description:"an object to be decoded into a globally shared symbol"}),AA=RX(AN,T4,{strict:!0,decode:e=>Nv(e.key),encode:(e,t,r)=>FV(Ny(e,r),e=>AN.make({key:e}))});TW("inline","attachment"),TW("public-read","private");let Aj=TW("upload"),AM=TW("callback","error"),Az=R0(NR,T6,{decode:e=>new TextDecoder().decode(e),encode:e=>new TextEncoder().encode(e)}),AD=RM({apiKey:function(e){return R0(e,NC(TB(e)),{strict:!0,decode:e=>n6(e),encode:e=>n8(e)})}(T6.pipe(((e,t)=>r=>{let n=JSON.stringify(e);return r.pipe(RZ(t=>t.startsWith(e),{schemaId:R4,[R4]:{startsWith:e},title:`startsWith(${n})`,description:`a string starting with ${n}`,jsonSchema:{pattern:`^${e}`},...t}))})("sk_"))),appId:T6,regions:Ru(T6),ingestHost:T6.pipe(RT({default:()=>"ingest.uploadthing.com"}))}),A$=NN.pipe(RK(Az),RK(Nl(AD)));var AP=class extends Ao("FileUploadData")({name:T6,size:T8,type:T6,lastModified:T8.pipe(RF)}){},AL=class extends AP.extend("FileUploadDataWithCustomId")({customId:Rt(T6)}){},AU=class extends AL.extend("UploadedFileData")({key:T6,url:T6,appUrl:T6,ufsUrl:T6,fileHash:T6}){},Aq=class extends Ao("MetadataFetchStreamPart")({payload:T6,signature:T6,hook:AM}){},AB=class extends Ao("MetadataFetchResponse")({ok:T7}){},AJ=class extends Ao("CallbackResultResponse")({ok:T7}){},AH=class extends Ao("UploadActionPayload")({files:Rc(AP),input:T3}){};let AK=Symbol.for("@effect/platform/Error"),AW=(e,t)=>{class r extends gv{constructor(...e){super(...e),this._tag=t}}return r.prototype[e]=e,r.prototype.name=t,r},AV=TW("Clipboard","Command","FileSystem","KeyValueStore","Path","Stream","Terminal");class AG extends Ac("@effect/platform/Error/BadArgument")("BadArgument",{module:AV,method:T6,description:RF(T6),cause:RF(Av)}){get message(){return`${this.module}.${this.method}${this.description?`: ${this.description}`:""}`}constructor(...e){super(...e),this[AK]=AK}}let AY=TW("AlreadyExists","BadResource","Busy","InvalidData","NotFound","PermissionDenied","TimedOut","UnexpectedEof","Unknown","WouldBlock","WriteZero");class AZ extends Ac("@effect/platform/Error/SystemError")("SystemError",{reason:AY,module:AV,method:T6,description:RF(T6),syscall:RF(T6),pathOrDescriptor:RF(Re(T6,T8)),cause:RF(Av)}){get message(){return`${this.reason}: ${this.module}.${this.method}${void 0!==this.pathOrDescriptor?` (${this.pathOrDescriptor})`:""}${this.description?`: ${this.description}`:""}`}constructor(...e){super(...e),this[AK]=AK}}let AQ=Symbol.for("effect/QueueEnqueue"),AX=Symbol.for("effect/QueueDequeue"),A0=Symbol.for("effect/QueueStrategy"),A1=Symbol.for("effect/BackingQueue"),A2={_A:e=>e},A3={_A:e=>e},A5={_In:e=>e},A4={_Out:e=>e};class A6 extends t4{constructor(e,t,r,n,i){super(),this[AQ]=A5,this[AX]=A4,this.queue=e,this.takers=t,this.shutdownHook=r,this.shutdownFlag=n,this.strategy=i}pipe(){return e6(this,arguments)}commit(){return this.take}capacity(){return this.queue.capacity()}get size(){return pJ(()=>pa(this.unsafeSize(),()=>pT))}unsafeSize(){return lq(this.shutdownFlag)?iC():tR(this.queue.length()-Ex(this.takers)+this.strategy.surplusSize())}get isEmpty(){return pj(this.size,e=>e<=0)}get isFull(){return pj(this.size,e=>e>=this.capacity())}get shutdown(){return pG(h8(e=>(W(this.shutdownFlag,lB(!0)),W(_Q(jg(this.takers),t=>dj(t,e.id()),!1,!1),p6(this.strategy.shutdown),pX(dM(this.shutdownHook,void 0)),pe))))}get isShutdown(){return pH(()=>lq(this.shutdownFlag))}get awaitShutdown(){return dT(this.shutdownHook)}isActive(){return!lq(this.shutdownFlag)}unsafeOffer(e){let t;if(lq(this.shutdownFlag))return!1;if(0===this.queue.length()){let r=W(this.takers,Ew(Ef));r!==Ef?(jd(r,e),t=!0):t=!1}else t=!1;if(t)return!0;let r=this.queue.offer(e);return jy(this.strategy,this.queue,this.takers),r}offer(e){return pJ(()=>{let t;if(lq(this.shutdownFlag))return pT;if(0===this.queue.length()){let r=W(this.takers,Ew(Ef));r!==Ef?(jd(r,e),t=!0):t=!1}else t=!1;if(t)return pB(!0);let r=this.queue.offer(e);return jy(this.strategy,this.queue,this.takers),r?pB(!0):this.strategy.handleSurplus([e],this.queue,this.takers,this.shutdownFlag)})}offerAll(e){return pJ(()=>{if(lq(this.shutdownFlag))return pT;let t=i6(e),r=0===this.queue.length()?i6(jb(this.takers,t.length)):sI,[n,i]=W(t,sw(r.length));for(let e=0;e<r.length;e++)jd(r[e],n[e]);if(0===i.length)return pB(!0);let s=this.queue.offerAll(i);return jy(this.strategy,this.queue,this.takers),aj(s)?pB(!0):this.strategy.handleSurplus(s,this.queue,this.takers,this.shutdownFlag)})}get take(){return h8(e=>{if(lq(this.shutdownFlag))return pT;let t=this.queue.poll(Ef);if(t!==Ef)return this.strategy.unsafeOnQueueEmptySpace(this.queue,this.takers),pB(t);{let t=dO(e.id());return W(pJ(()=>(W(this.takers,ES(t)),jy(this.strategy,this.queue,this.takers),lq(this.shutdownFlag)?pT:dT(t))),p$(()=>pH(()=>jx(this.takers,t))))}})}get takeAll(){return pJ(()=>lq(this.shutdownFlag)?pT:pH(()=>{let e=this.queue.pollUpTo(Number.POSITIVE_INFINITY);return this.strategy.unsafeOnQueueEmptySpace(this.queue,this.takers),am(e)}))}takeUpTo(e){return pJ(()=>lq(this.shutdownFlag)?pT:pH(()=>{let t=this.queue.pollUpTo(e);return this.strategy.unsafeOnQueueEmptySpace(this.queue,this.takers),am(t)}))}takeBetween(e,t){return pJ(()=>A8(this,e,t,ap()))}}let A8=(e,t,r,n)=>r<t?pB(n):W(js(e,r),pv(i=>{let s=t-i.length;return 1===s?W(ji(e),pj(e=>W(n,aI(i),aw(e)))):s>1?W(ji(e),pv(t=>A8(e,s-1,r-i.length-1,W(n,aI(i),aw(t))))):pB(W(n,aI(i)))})),A7=(e,t,r,n,i)=>new A6(e,t,r,n,i),A9=(e,t)=>W(dI(),pj(r=>A7(e,Eb(),r,lL(!1),t)));class je{constructor(e){this[A1]=A3,this.mutable=e}poll(e){return Ew(this.mutable,e)}pollUpTo(e){return Ek(this.mutable,e)}offerAll(e){return E_(this.mutable,e)}offer(e){return ES(this.mutable,e)}capacity(){return Ev(this.mutable)}length(){return Ex(this.mutable)}}let jt=e=>new je(e),jr=U(2,(e,t)=>e.offer(t)),jn=U(2,(e,t)=>e.offerAll(t)),ji=e=>e.take,js=U(2,(e,t)=>e.takeUpTo(t)),ja=U(3,(e,t,r)=>e.takeBetween(t,r)),jo=U(2,(e,t)=>e.takeBetween(t,t)),jl=()=>new jh,jc=()=>new jp,ju=()=>new jf;class jh{surplusSize(){return Ex(this.putters)}onCompleteTakersWithEmptyQueue(e){for(;!Ey(this.putters)&&!Ey(e);){let t=Ew(e,void 0),r=Ew(this.putters,void 0);r[2]&&jd(r[1],!0),jd(t,r[0])}}get shutdown(){return W(px,pv(e=>W(pH(()=>jg(this.putters)),pv(t=>_Q(t,([t,r,n])=>n?W(dj(r,e),pe):pZ,!1,!1)))))}handleSurplus(e,t,r,n){return h8(i=>{let s=dO(i.id());return W(pJ(()=>(this.unsafeOffer(e,s),this.unsafeOnQueueEmptySpace(t,r),jy(this,t,r),lq(n)?pT:dT(s))),p$(()=>pH(()=>this.unsafeRemove(s))))})}unsafeOnQueueEmptySpace(e,t){let r=!0;for(;r&&(e.capacity()===Number.POSITIVE_INFINITY||e.length()<e.capacity());){let n=W(this.putters,Ew(Ef));if(n===Ef)r=!1;else{let r=e.offer(n[0]);r&&n[2]?jd(n[1],!0):r||jm(this.putters,W(jg(this.putters),ak(n))),jy(this,e,t)}}}unsafeOffer(e,t){let r=i6(e);for(let e=0;e<r.length;e++){let n=r[e];e===r.length-1?W(this.putters,ES([n,t,!0])):W(this.putters,ES([n,t,!1]))}}unsafeRemove(e){jm(this.putters,W(jg(this.putters),aT(([,t])=>t!==e)))}constructor(){this[A0]=A2,this.putters=Eb()}}class jp{surplusSize(){return 0}get shutdown(){return pZ}onCompleteTakersWithEmptyQueue(){}handleSurplus(e,t,r,n){return pB(!1)}unsafeOnQueueEmptySpace(e,t){}constructor(){this[A0]=A2}}class jf{surplusSize(){return 0}get shutdown(){return pZ}onCompleteTakersWithEmptyQueue(){}handleSurplus(e,t,r,n){return pH(()=>(this.unsafeOffer(t,e),jy(this,t,r),!0))}unsafeOnQueueEmptySpace(e,t){}unsafeOffer(e,t){let r,n=t[Symbol.iterator](),i=!0;for(;!(r=n.next()).done&&i;){if(0===e.capacity())return;e.poll(Ef),i=e.offer(r.value)}}constructor(){this[A0]=A2}}let jd=(e,t)=>dz(e,pB(t)),jm=(e,t)=>W(e,E_(t)),jg=e=>W(e,Ek(Number.POSITIVE_INFINITY)),jb=(e,t)=>W(e,Ek(t)),jx=(e,t)=>{jm(e,W(jg(e),aT(e=>t!==e)))},jy=(e,t,r)=>{let n=!0;for(;n&&0!==t.length();){let i=W(r,Ew(Ef));if(i!==Ef){let s=t.poll(Ef);s!==Ef?(jd(i,s),e.unsafeOnQueueEmptySpace(t,r)):jm(r,W(jg(r),ak(i))),n=!0}else n=!1}n&&0===t.length()&&!Ey(r)&&e.onCompleteTakersWithEmptyQueue(r)},jv=e=>W(pH(()=>Eg(e)),pv(e=>A9(jt(e),jl()))),jS=e=>W(pH(()=>Eg(e)),pv(e=>A9(jt(e),jc()))),j_=e=>W(pH(()=>Eg(e)),pv(e=>A9(jt(e),ju()))),jw=()=>W(pH(()=>Eb()),pv(e=>A9(jt(e),jc()))),jk=e=>e.capacity(),jC=e=>e.size,jE=e=>e.isEmpty,jO=e=>e.isFull,jI=e=>e.isShutdown,jF=e=>e.awaitShutdown,jT=e=>e.shutdown,jR=e=>pj(e.takeUpTo(1),az),jN=e=>e.takeAll,jA=U(2,(e,t)=>fS(_j,t)(e)),jj=e=>k7(wm(_q,lF(e))),jM=e=>Co(pj(e,jj)),jz=e=>Cl(pj(e,jj)),jD=e=>k7(wm(_q,lT(e))),j$=U(2,(e,t)=>kZ(jD(e),()=>jj(t))),jP=U(2,(e,t)=>kZ(jD(e),()=>jM(t))),jL=U(2,(e,t)=>kZ(jD(e),()=>jz(t))),jU=e=>k0(pj(e,()=>t0())),jq=e=>k7(wt(e)),jB=Symbol.for("effect/PubSub/AbsentValue"),jJ=(e,t)=>r=>{r.has(e)||r.set(e,new Set),r.get(e).add(t)},jH=(e,t)=>r=>{if(!r.has(e))return;let n=r.get(e);n.delete(t),0===n.size&&r.delete(e)},jK=e=>{let t="number"==typeof e?{capacity:e}:e;j9(t.capacity);let r=t.replay&&t.replay>0?new Mp(Math.ceil(t.replay)):void 0;return 1===t.capacity?new j0(r):d0(t.capacity)===t.capacity?new jQ(t.capacity,r):new jY(t.capacity,r)},jW=e=>new j2(e?.replay?new Mp(e.replay):void 0),jV=(e,t,r)=>pj(dI(),n=>jG(e,t,e.subscribe(),Eb(),n,lL(!1),r)),jG=(e,t,r,n,i,s,a)=>new j5(e,t,r,n,i,s,a,e.replayWindow());class jY{constructor(e,t){this.publisherIndex=0,this.subscriberCount=0,this.subscribersIndex=0,this.capacity=e,this.replayBuffer=t,this.array=Array.from({length:e}),this.subscribers=Array.from({length:e})}replayWindow(){return this.replayBuffer?new Mf(this.replayBuffer):Md}isEmpty(){return this.publisherIndex===this.subscribersIndex}isFull(){return this.publisherIndex===this.subscribersIndex+this.capacity}size(){return this.publisherIndex-this.subscribersIndex}publish(e){if(this.isFull())return!1;if(0!==this.subscriberCount){let t=this.publisherIndex%this.capacity;this.array[t]=e,this.subscribers[t]=this.subscriberCount,this.publisherIndex+=1}return this.replayBuffer&&this.replayBuffer.offer(e),!0}publishAll(e){if(0===this.subscriberCount)return this.replayBuffer&&this.replayBuffer.offerAll(e),ap();let t=am(e),r=t.length,n=this.publisherIndex-this.subscribersIndex,i=Math.min(r,this.capacity-n);if(0===i)return t;let s=0,a=this.publisherIndex+i;for(;this.publisherIndex!==a;){let e=a_(t,s++),r=this.publisherIndex%this.capacity;this.array[r]=e,this.subscribers[r]=this.subscriberCount,this.publisherIndex+=1,this.replayBuffer&&this.replayBuffer.offer(e)}return aE(t,s)}slide(){if(this.subscribersIndex!==this.publisherIndex){let e=this.subscribersIndex%this.capacity;this.array[e]=jB,this.subscribers[e]=0,this.subscribersIndex+=1}this.replayBuffer&&this.replayBuffer.slide()}subscribe(){return this.subscriberCount+=1,new jZ(this,this.publisherIndex,!1)}}class jZ{constructor(e,t,r){this.self=e,this.subscriberIndex=t,this.unsubscribed=r}isEmpty(){return this.unsubscribed||this.self.publisherIndex===this.subscriberIndex||this.self.publisherIndex===this.self.subscribersIndex}size(){return this.unsubscribed?0:this.self.publisherIndex-Math.max(this.subscriberIndex,this.self.subscribersIndex)}poll(e){if(this.unsubscribed)return e;if(this.subscriberIndex=Math.max(this.subscriberIndex,this.self.subscribersIndex),this.subscriberIndex!==this.self.publisherIndex){let e=this.subscriberIndex%this.self.capacity,t=this.self.array[e];return this.self.subscribers[e]-=1,0===this.self.subscribers[e]&&(this.self.array[e]=jB,this.self.subscribersIndex+=1),this.subscriberIndex+=1,t}return e}pollUpTo(e){if(this.unsubscribed)return ap();this.subscriberIndex=Math.max(this.subscriberIndex,this.self.subscribersIndex);let t=Math.min(e,this.self.publisherIndex-this.subscriberIndex);if(t<=0)return ap();let r=[],n=this.subscriberIndex+t;for(;this.subscriberIndex!==n;){let e=this.subscriberIndex%this.self.capacity,t=this.self.array[e];this.self.subscribers[e]-=1,0===this.self.subscribers[e]&&(this.self.array[e]=jB,this.self.subscribersIndex+=1),r.push(t),this.subscriberIndex+=1}return am(r)}unsubscribe(){if(!this.unsubscribed)for(this.unsubscribed=!0,this.self.subscriberCount-=1,this.subscriberIndex=Math.max(this.subscriberIndex,this.self.subscribersIndex);this.subscriberIndex!==this.self.publisherIndex;){let e=this.subscriberIndex%this.self.capacity;this.self.subscribers[e]-=1,0===this.self.subscribers[e]&&(this.self.array[e]=jB,this.self.subscribersIndex+=1),this.subscriberIndex+=1}}}class jQ{constructor(e,t){this.publisherIndex=0,this.subscriberCount=0,this.subscribersIndex=0,this.capacity=e,this.replayBuffer=t,this.array=Array.from({length:e}),this.mask=e-1,this.subscribers=Array.from({length:e})}replayWindow(){return this.replayBuffer?new Mf(this.replayBuffer):Md}isEmpty(){return this.publisherIndex===this.subscribersIndex}isFull(){return this.publisherIndex===this.subscribersIndex+this.capacity}size(){return this.publisherIndex-this.subscribersIndex}publish(e){if(this.isFull())return!1;if(0!==this.subscriberCount){let t=this.publisherIndex&this.mask;this.array[t]=e,this.subscribers[t]=this.subscriberCount,this.publisherIndex+=1}return this.replayBuffer&&this.replayBuffer.offer(e),!0}publishAll(e){if(0===this.subscriberCount)return this.replayBuffer&&this.replayBuffer.offerAll(e),ap();let t=am(e),r=t.length,n=this.publisherIndex-this.subscribersIndex,i=Math.min(r,this.capacity-n);if(0===i)return t;let s=0,a=this.publisherIndex+i;for(;this.publisherIndex!==a;){let e=a_(t,s++),r=this.publisherIndex&this.mask;this.array[r]=e,this.subscribers[r]=this.subscriberCount,this.publisherIndex+=1,this.replayBuffer&&this.replayBuffer.offer(e)}return aE(t,s)}slide(){if(this.subscribersIndex!==this.publisherIndex){let e=this.subscribersIndex&this.mask;this.array[e]=jB,this.subscribers[e]=0,this.subscribersIndex+=1}this.replayBuffer&&this.replayBuffer.slide()}subscribe(){return this.subscriberCount+=1,new jX(this,this.publisherIndex,!1)}}class jX{constructor(e,t,r){this.self=e,this.subscriberIndex=t,this.unsubscribed=r}isEmpty(){return this.unsubscribed||this.self.publisherIndex===this.subscriberIndex||this.self.publisherIndex===this.self.subscribersIndex}size(){return this.unsubscribed?0:this.self.publisherIndex-Math.max(this.subscriberIndex,this.self.subscribersIndex)}poll(e){if(this.unsubscribed)return e;if(this.subscriberIndex=Math.max(this.subscriberIndex,this.self.subscribersIndex),this.subscriberIndex!==this.self.publisherIndex){let e=this.subscriberIndex&this.self.mask,t=this.self.array[e];return this.self.subscribers[e]-=1,0===this.self.subscribers[e]&&(this.self.array[e]=jB,this.self.subscribersIndex+=1),this.subscriberIndex+=1,t}return e}pollUpTo(e){if(this.unsubscribed)return ap();this.subscriberIndex=Math.max(this.subscriberIndex,this.self.subscribersIndex);let t=Math.min(e,this.self.publisherIndex-this.subscriberIndex);if(t<=0)return ap();let r=[],n=this.subscriberIndex+t;for(;this.subscriberIndex!==n;){let e=this.subscriberIndex&this.self.mask,t=this.self.array[e];this.self.subscribers[e]-=1,0===this.self.subscribers[e]&&(this.self.array[e]=jB,this.self.subscribersIndex+=1),r.push(t),this.subscriberIndex+=1}return am(r)}unsubscribe(){if(!this.unsubscribed)for(this.unsubscribed=!0,this.self.subscriberCount-=1,this.subscriberIndex=Math.max(this.subscriberIndex,this.self.subscribersIndex);this.subscriberIndex!==this.self.publisherIndex;){let e=this.subscriberIndex&this.self.mask;this.self.subscribers[e]-=1,0===this.self.subscribers[e]&&(this.self.array[e]=jB,this.self.subscribersIndex+=1),this.subscriberIndex+=1}}}class j0{constructor(e){this.publisherIndex=0,this.subscriberCount=0,this.subscribers=0,this.value=jB,this.capacity=1,this.replayBuffer=e}replayWindow(){return this.replayBuffer?new Mf(this.replayBuffer):Md}pipe(){return e6(this,arguments)}isEmpty(){return 0===this.subscribers}isFull(){return!this.isEmpty()}size(){return+!this.isEmpty()}publish(e){return!this.isFull()&&(0!==this.subscriberCount&&(this.value=e,this.subscribers=this.subscriberCount,this.publisherIndex+=1),this.replayBuffer&&this.replayBuffer.offer(e),!0)}publishAll(e){if(0===this.subscriberCount)return this.replayBuffer&&this.replayBuffer.offerAll(e),ap();let t=am(e);return aj(t)?t:this.publish(aD(t))?aE(t,1):t}slide(){this.isFull()&&(this.subscribers=0,this.value=jB),this.replayBuffer&&this.replayBuffer.slide()}subscribe(){return this.subscriberCount+=1,new j1(this,this.publisherIndex,!1)}}class j1{constructor(e,t,r){this.self=e,this.subscriberIndex=t,this.unsubscribed=r}isEmpty(){return this.unsubscribed||0===this.self.subscribers||this.subscriberIndex===this.self.publisherIndex}size(){return+!this.isEmpty()}poll(e){if(this.isEmpty())return e;let t=this.self.value;return this.self.subscribers-=1,0===this.self.subscribers&&(this.self.value=jB),this.subscriberIndex+=1,t}pollUpTo(e){if(this.isEmpty()||e<1)return ap();let t=this.self.value;return this.self.subscribers-=1,0===this.self.subscribers&&(this.self.value=jB),this.subscriberIndex+=1,ad(t)}unsubscribe(){this.unsubscribed||(this.unsubscribed=!0,this.self.subscriberCount-=1,this.subscriberIndex!==this.self.publisherIndex&&(this.self.subscribers-=1,0===this.self.subscribers&&(this.self.value=jB)))}}class j2{constructor(e){this.publisherHead={value:jB,subscribers:0,next:null},this.publisherTail=this.publisherHead,this.publisherIndex=0,this.subscribersIndex=0,this.capacity=Number.MAX_SAFE_INTEGER,this.replayBuffer=e}replayWindow(){return this.replayBuffer?new Mf(this.replayBuffer):Md}isEmpty(){return this.publisherHead===this.publisherTail}isFull(){return!1}size(){return this.publisherIndex-this.subscribersIndex}publish(e){let t=this.publisherTail.subscribers;return 0!==t&&(this.publisherTail.next={value:e,subscribers:t,next:null},this.publisherTail=this.publisherTail.next,this.publisherIndex+=1),this.replayBuffer&&this.replayBuffer.offer(e),!0}publishAll(e){if(0!==this.publisherTail.subscribers)for(let t of e)this.publish(t);else this.replayBuffer&&this.replayBuffer.offerAll(e);return ap()}slide(){this.publisherHead!==this.publisherTail&&(this.publisherHead=this.publisherHead.next,this.publisherHead.value=jB,this.subscribersIndex+=1),this.replayBuffer&&this.replayBuffer.slide()}subscribe(){return this.publisherTail.subscribers+=1,new j3(this,this.publisherTail,this.publisherIndex,!1)}}class j3{constructor(e,t,r,n){this.self=e,this.subscriberHead=t,this.subscriberIndex=r,this.unsubscribed=n}isEmpty(){if(this.unsubscribed)return!0;let e=!0,t=!0;for(;t;)this.subscriberHead===this.self.publisherTail?t=!1:this.subscriberHead.next.value!==jB?(e=!1,t=!1):(this.subscriberHead=this.subscriberHead.next,this.subscriberIndex+=1);return e}size(){return this.unsubscribed?0:this.self.publisherIndex-Math.max(this.subscriberIndex,this.self.subscribersIndex)}poll(e){if(this.unsubscribed)return e;let t=!0,r=e;for(;t;)if(this.subscriberHead===this.self.publisherTail)t=!1;else{let e=this.subscriberHead.next.value;e!==jB&&(r=e,this.subscriberHead.subscribers-=1,0===this.subscriberHead.subscribers&&(this.self.publisherHead=this.self.publisherHead.next,this.self.publisherHead.value=jB,this.self.subscribersIndex+=1),t=!1),this.subscriberHead=this.subscriberHead.next,this.subscriberIndex+=1}return r}pollUpTo(e){let t=[],r=0;for(;r!==e;){let n=this.poll(jB);n===jB?r=e:(t.push(n),r+=1)}return am(t)}unsubscribe(){if(!this.unsubscribed)for(this.unsubscribed=!0,this.self.publisherTail.subscribers-=1;this.subscriberHead!==this.self.publisherTail;)this.subscriberHead.next.value!==jB&&(this.subscriberHead.subscribers-=1,0===this.subscriberHead.subscribers&&(this.self.publisherHead=this.self.publisherHead.next,this.self.publisherHead.value=jB,this.self.subscribersIndex+=1)),this.subscriberHead=this.subscriberHead.next}}class j5 extends t4{static{c=AX}constructor(e,t,r,n,i,s,a,o){super(),this[c]=A4,this.pubsub=e,this.subscribers=t,this.subscription=r,this.pollers=n,this.shutdownHook=i,this.shutdownFlag=s,this.strategy=a,this.replayWindow=o}commit(){return this.take}pipe(){return e6(this,arguments)}capacity(){return this.pubsub.capacity}isActive(){return!lq(this.shutdownFlag)}get size(){return pJ(()=>lq(this.shutdownFlag)?pT:pB(this.subscription.size()+this.replayWindow.remaining))}unsafeSize(){return lq(this.shutdownFlag)?iC():tR(this.subscription.size()+this.replayWindow.remaining)}get isFull(){return pJ(()=>lq(this.shutdownFlag)?pT:pB(this.subscription.size()===this.capacity()))}get isEmpty(){return pj(this.size,e=>0===e)}get shutdown(){return pG(h8(e=>(lB(this.shutdownFlag,!0),W(_Z(Mr(this.pollers),t=>dj(t,e.id()),!1),p6(pH(()=>{this.subscribers.delete(this.subscription),this.subscription.unsubscribe(),this.strategy.unsafeOnPubSubEmptySpace(this.pubsub,this.subscribers)})),pX(dM(this.shutdownHook,void 0)),pe))))}get isShutdown(){return pH(()=>lq(this.shutdownFlag))}get awaitShutdown(){return dT(this.shutdownHook)}get take(){return h8(e=>{if(lq(this.shutdownFlag))return pT;if(this.replayWindow.remaining>0)return pB(this.replayWindow.take());let t=Ey(this.pollers)?this.subscription.poll(Ef):Ef;if(t!==Ef)return this.strategy.unsafeOnPubSubEmptySpace(this.pubsub,this.subscribers),pB(t);{let t=dO(e.id());return W(pJ(()=>(W(this.pollers,ES(t)),W(this.subscribers,jJ(this.subscription,this.pollers)),this.strategy.unsafeCompletePollers(this.pubsub,this.subscribers,this.subscription,this.pollers),lq(this.shutdownFlag)?pT:dT(t))),p$(()=>pH(()=>Ma(this.pollers,t))))}})}get takeAll(){return pJ(()=>{if(lq(this.shutdownFlag))return pT;let e=Ey(this.pollers)?Mn(this.subscription):ap();return(this.strategy.unsafeOnPubSubEmptySpace(this.pubsub,this.subscribers),this.replayWindow.remaining>0)?pB(aI(this.replayWindow.takeAll(),e)):pB(e)})}takeUpTo(e){return pJ(()=>{let t;if(lq(this.shutdownFlag))return pT;if(this.replayWindow.remaining>=e)return pB(this.replayWindow.takeN(e));this.replayWindow.remaining>0&&(t=this.replayWindow.takeAll(),e-=t.length);let r=Ey(this.pollers)?Mi(this.subscription,e):ap();return this.strategy.unsafeOnPubSubEmptySpace(this.pubsub,this.subscribers),t?pB(aI(t,r)):pB(r)})}takeBetween(e,t){return pJ(()=>j4(this,e,t,ap()))}}let j4=(e,t,r,n)=>r<t?pB(n):W(e.takeUpTo(r),pv(i=>{let s=t-i.length;return 1===s?W(e.take,pj(e=>W(n,aI(i),aw(e)))):s>1?W(e.take,pv(t=>j4(e,s-1,r-i.length-1,W(n,aI(i),aw(t))))):pB(W(n,aI(i)))}));class j6{static{u=AQ,h=AX}constructor(e,t,r,n,i,s){this[u]=A5,this[h]=A4,this.pubsub=e,this.subscribers=t,this.scope=r,this.shutdownHook=n,this.shutdownFlag=i,this.strategy=s}capacity(){return this.pubsub.capacity}get size(){return pJ(()=>lq(this.shutdownFlag)?pT:pH(()=>this.pubsub.size()))}unsafeSize(){return lq(this.shutdownFlag)?iC():tR(this.pubsub.size())}get isFull(){return pj(this.size,e=>e===this.capacity())}get isEmpty(){return pj(this.size,e=>0===e)}get awaitShutdown(){return dT(this.shutdownHook)}get isShutdown(){return pH(()=>lq(this.shutdownFlag))}get shutdown(){return pG(h8(e=>(W(this.shutdownFlag,lB(!0)),W(this.scope.close(db(e.id())),p6(this.strategy.shutdown),pX(dM(this.shutdownHook,void 0)),pe))))}publish(e){return pJ(()=>lq(this.shutdownFlag)?pT:this.pubsub.publish(e)?(this.strategy.unsafeCompleteSubscribers(this.pubsub,this.subscribers),pB(!0)):this.strategy.handleSurplus(this.pubsub,this.subscribers,ad(e),this.shutdownFlag))}isActive(){return!lq(this.shutdownFlag)}unsafeOffer(e){return!lq(this.shutdownFlag)&&!!this.pubsub.publish(e)&&(this.strategy.unsafeCompleteSubscribers(this.pubsub,this.subscribers),!0)}publishAll(e){return pJ(()=>{if(lq(this.shutdownFlag))return pT;let t=Ms(this.pubsub,e);return(this.strategy.unsafeCompleteSubscribers(this.pubsub,this.subscribers),aj(t))?pB(!0):this.strategy.handleSurplus(this.pubsub,this.subscribers,t,this.shutdownFlag)})}get subscribe(){return pj(_J(pK(_G([this.scope.fork(xW),jV(this.pubsub,this.subscribers,this.strategy)]),e=>e[0].addFinalizer(()=>e[1].shutdown)),(e,t)=>e[0].close(t)),e=>e[1])}offer(e){return this.publish(e)}offerAll(e){return this.publishAll(e)}pipe(){return e6(this,arguments)}}let j8=(e,t)=>pv(wh(),r=>pj(dI(),n=>j7(e,new Map,r,n,lL(!1),t))),j7=(e,t,r,n,i,s)=>new j6(e,t,r,n,i,s),j9=e=>{if(e<=0)throw new de(`Cannot construct PubSub with capacity of ${e}`)},Me=(e,t)=>{dz(e,pB(t))},Mt=(e,t)=>W(e,E_(t)),Mr=e=>W(e,Ek(Number.POSITIVE_INFINITY)),Mn=e=>e.pollUpTo(Number.POSITIVE_INFINITY),Mi=(e,t)=>e.pollUpTo(t),Ms=(e,t)=>e.publishAll(t),Ma=(e,t)=>{Mt(e,W(Mr(e),aT(e=>e!==t)))};class Mo{get shutdown(){return pv(px,e=>pv(pH(()=>Mr(this.publishers)),t=>_Q(t,([t,r,n])=>n?W(dj(r,e),pe):pZ,!1,!1)))}handleSurplus(e,t,r,n){return h8(i=>{let s=dO(i.id());return W(pJ(()=>(this.unsafeOffer(r,s),this.unsafeOnPubSubEmptySpace(e,t),this.unsafeCompleteSubscribers(e,t),lq(n)?pT:dT(s))),p$(()=>pH(()=>this.unsafeRemove(s))))})}unsafeOnPubSubEmptySpace(e,t){let r=!0;for(;r&&!e.isFull();){let n=W(this.publishers,Ew(Ef));if(n===Ef)r=!1;else{let r=e.publish(n[0]);r&&n[2]?Me(n[1],!0):r||Mt(this.publishers,W(Mr(this.publishers),ak(n))),this.unsafeCompleteSubscribers(e,t)}}}unsafeCompletePollers(e,t,r,n){return Mu(this,e,t,r,n)}unsafeCompleteSubscribers(e,t){return Mh(this,e,t)}unsafeOffer(e,t){let r=e[Symbol.iterator](),n=r.next();if(!n.done)for(;;){let e=n.value;if((n=r.next()).done){W(this.publishers,ES([e,t,!0]));break}W(this.publishers,ES([e,t,!1]))}}unsafeRemove(e){Mt(this.publishers,W(Mr(this.publishers),aT(([t,r])=>r!==e)))}constructor(){this.publishers=Eb()}}class Ml{get shutdown(){return pZ}handleSurplus(e,t,r,n){return pB(!1)}unsafeOnPubSubEmptySpace(e,t){}unsafeCompletePollers(e,t,r,n){return Mu(this,e,t,r,n)}unsafeCompleteSubscribers(e,t){return Mh(this,e,t)}}class Mc{get shutdown(){return pZ}handleSurplus(e,t,r,n){return pH(()=>(this.unsafeSlidingPublish(e,r),this.unsafeCompleteSubscribers(e,t),!0))}unsafeOnPubSubEmptySpace(e,t){}unsafeCompletePollers(e,t,r,n){return Mu(this,e,t,r,n)}unsafeCompleteSubscribers(e,t){return Mh(this,e,t)}unsafeSlidingPublish(e,t){let r=t[Symbol.iterator](),n=r.next();if(!n.done&&e.capacity>0){let t=n.value,i=!0;for(;i;){e.slide();let s=e.publish(t);s&&(n=r.next())&&!n.done?t=n.value:s&&(i=!1)}}}}let Mu=(e,t,r,n,i)=>{let s=!0;for(;s&&!n.isEmpty();){let a=W(i,Ew(Ef));if(a===Ef)W(r,jH(n,i)),Ey(i)?s=!1:W(r,jJ(n,i));else{let s=n.poll(Ef);s===Ef?Mt(i,W(Mr(i),ak(a))):(Me(a,s),e.unsafeOnPubSubEmptySpace(t,r))}}},Mh=(e,t,r)=>{for(let[n,i]of r)for(let s of i)e.unsafeCompletePollers(t,r,n,s)};class Mp{constructor(e){this.head={value:jB,next:null},this.tail=this.head,this.size=0,this.index=0,this.capacity=e}slide(){this.index++}offer(e){this.tail.value=e,this.tail.next={value:jB,next:null},this.tail=this.tail.next,this.size===this.capacity?this.head=this.head.next:this.size+=1}offerAll(e){for(let t of e)this.offer(t)}}class Mf{constructor(e){this.buffer=e,this.index=e.index,this.remaining=e.size,this.head=e.head}fastForward(){for(;this.index<this.buffer.index;)this.head=this.head.next,this.index++}take(){if(0===this.remaining)return;this.index<this.buffer.index&&this.fastForward(),this.remaining--;let e=this.head.value;return this.head=this.head.next,e}takeN(e){if(0===this.remaining)return ap();this.index<this.buffer.index&&this.fastForward();let t=Math.min(e,this.remaining),r=Array(t);for(let e=0;e<t;e++){let t=this.head.value;this.head=this.head.next,r[e]=t}return this.remaining-=t,av(r)}takeAll(){return this.takeN(this.remaining)}}let Md={remaining:0,take:()=>void 0,takeN:()=>ap(),takeAll:()=>ap()},Mm=e=>pJ(()=>j8(jK(e),new Mo)),Mg=e=>pJ(()=>j8(jK(e),new Ml)),Mb=e=>pJ(()=>j8(jK(e),new Mc)),Mx=e=>pJ(()=>j8(jW(e),new Ml)),My=e=>e.shutdown,Mv=e=>e.subscribe,MS="Continue",M_=Symbol.for("effect/ChannelChildExecutorDecision"),Mw={[M_]:M_},Mk=e=>{let t=Object.create(Mw);return t._tag=MS,t},MC="ContinuationK",ME=Symbol.for("effect/ChannelContinuation"),MO={_Env:e=>e,_InErr:e=>e,_InElem:e=>e,_InDone:e=>e,_OutErr:e=>e,_OutDone:e=>e,_OutErr2:e=>e,_OutElem:e=>e,_OutDone2:e=>e};class MI{constructor(e,t){this._tag=MC,this[ME]=MO,this.onSuccess=e,this.onHalt=t}onExit(e){return da(e)?this.onHalt(e.cause):this.onSuccess(e.value)}}class MF{constructor(e){this._tag="ContinuationFinalizer",this[ME]=MO,this.finalizer=e}}let MT="PullAfterNext",MR={[Symbol.for("effect/ChannelUpstreamPullStrategy")]:{_A:e=>e}},MN=e=>{let t=Object.create(MR);return t._tag=MT,t.emitSeparator=e,t},MA="BracketOut",Mj="Bridge",MM="ConcatAll",Mz="Emit",MD="Ensuring",M$="Fail",MP="Fold",ML="FromEffect",MU="PipeTo",Mq="Provide",MB="Read",MJ="Succeed",MH="SucceedNow",MK="Suspend",MW=Symbol.for("effect/Channel"),MV={[MW]:{_Env:e=>e,_InErr:e=>e,_InElem:e=>e,_InDone:e=>e,_OutErr:e=>e,_OutElem:e=>e,_OutDone:e=>e},pipe(){return e6(this,arguments)}},MG=e=>ec(e,MW)||h6(e),MY=U(2,(e,t)=>{let r=Object.create(MV);return r._tag=MA,r.acquire=()=>e,r.finalizer=t,r}),MZ=U(2,(e,t)=>{let r=Object.create(MV);return r._tag=MP,r.channel=e,r.k=new MI(zi,t),r}),MQ=e=>za(()=>{let t=[];return M8(ze(e,MX(t)),e=>zo(()=>[am(t),e]))}),MX=e=>zr({onInput:t=>M8(zo(()=>{e.push(t)}),()=>MX(e)),onFailure:M5,onDone:zs}),M0=(e,t,r)=>{let n=Object.create(MV);return n._tag=MM,n.combineInners=t,n.combineAll=r,n.onPull=()=>MN(iC()),n.onEmit=()=>Mk,n.value=()=>e,n.k=q,n},M1=U(4,(e,t,r,n)=>{let i=Object.create(MV);return i._tag=MM,i.combineInners=r,i.combineAll=n,i.onPull=()=>MN(iC()),i.onEmit=()=>Mk,i.value=()=>e,i.k=t,i}),M2=U(2,(e,t)=>{let r=Object.create(MV);return r._tag=Mj,r.input=t,r.channel=e,r}),M3=U(2,(e,t)=>{let r=Object.create(MV);return r._tag=MD,r.channel=e,r.finalizer=t,r}),M5=e=>M4(hn(e)),M4=e=>M6(()=>e),M6=e=>{let t=Object.create(MV);return t._tag=M$,t.error=e,t},M8=U(2,(e,t)=>{let r=Object.create(MV);return r._tag=MP,r.channel=e,r.k=new MI(t,M4),r}),M7=U(2,(e,t)=>{let r=Object.create(MV);return r._tag=MP,r.channel=e,r.k=new MI(t.onSuccess,t.onFailure),r}),M9=e=>{let t=Object.create(MV);return t._tag=ML,t.effect=()=>e,t},ze=U(2,(e,t)=>{let r=Object.create(MV);return r._tag=MU,r.left=()=>e,r.right=()=>t,r}),zt=U(2,(e,t)=>{let r=Object.create(MV);return r._tag=Mq,r.context=()=>t,r.inner=e,r}),zr=e=>zn({onInput:e.onInput,onFailure:t=>nF(hx(t),{onLeft:e.onFailure,onRight:M4}),onDone:e.onDone}),zn=e=>{let t=Object.create(MV);return t._tag=MB,t.more=e.onInput,t.done=new MI(e.onDone,e.onFailure),t},zi=e=>zo(()=>e),zs=e=>{let t=Object.create(MV);return t._tag=MH,t.terminal=e,t},za=e=>{let t=Object.create(MV);return t._tag=MK,t.channel=e,t},zo=e=>{let t=Object.create(MV);return t._tag=MJ,t.evaluate=e,t},zl=zs(void 0),zc=e=>{let t=Object.create(MV);return t._tag=Mz,t.out=e,t},zu="Done",zh="Emit",zp="FromEffect",zf="Read",zd={[Symbol.for("effect/ChannelState")]:{_E:e=>e,_R:e=>e}},zm=()=>{let e=Object.create(zd);return e._tag=zu,e},zg=()=>{let e=Object.create(zd);return e._tag=zh,e},zb=e=>{let t=Object.create(zd);return t._tag=zp,t.effect=e,t},zx=(e,t,r,n)=>{let i=Object.create(zd);return i._tag=zf,i.upstream=e,i.onEffect=t,i.onEmit=r,i.onDone=n,i},zy=e=>e._tag===zp,zv=e=>zy(e)?e.effect:pZ,zS=e=>zy(e)?EW(e.effect):void 0,z_="PullFromChild",zw="PullFromUpstream",zk="DrainChildExecutors",zC="Emit";class zE{constructor(e,t,r){this._tag=z_,this.childExecutor=e,this.parentSubexecutor=t,this.onEmit=r}close(e){let t=this.childExecutor.close(e),r=this.parentSubexecutor.close(e);return void 0!==t&&void 0!==r?ws(pf(t),pf(r),(e,t)=>W(e,dk(t))):void 0!==t?t:void 0!==r?r:void 0}enqueuePullFromChild(e){return this}}class zO{constructor(e,t,r,n,i,s,a,o){this._tag=zw,this.upstreamExecutor=e,this.createChild=t,this.lastDone=r,this.activeChildExecutors=n,this.combineChildResults=i,this.combineWithChildResult=s,this.onPull=a,this.onEmit=o}close(e){let t=this.upstreamExecutor.close(e);return[...this.activeChildExecutors.map(t=>void 0!==t?t.childExecutor.close(e):void 0),t].reduce((e,t)=>void 0!==e&&void 0!==t?ws(e,pf(t),(e,t)=>dk(e,t)):void 0!==e?e:void 0!==t?pf(t):void 0,void 0)}enqueuePullFromChild(e){return new zO(this.upstreamExecutor,this.createChild,this.lastDone,[...this.activeChildExecutors,e],this.combineChildResults,this.combineWithChildResult,this.onPull,this.onEmit)}}class zI{constructor(e,t,r,n,i,s,a){this._tag=zk,this.upstreamExecutor=e,this.lastDone=t,this.activeChildExecutors=r,this.upstreamDone=n,this.combineChildResults=i,this.combineWithChildResult=s,this.onPull=a}close(e){let t=this.upstreamExecutor.close(e);return[...this.activeChildExecutors.map(t=>void 0!==t?t.childExecutor.close(e):void 0),t].reduce((e,t)=>void 0!==e&&void 0!==t?ws(e,pf(t),(e,t)=>dk(e,t)):void 0!==e?e:void 0!==t?pf(t):void 0,void 0)}enqueuePullFromChild(e){return new zI(this.upstreamExecutor,this.lastDone,[...this.activeChildExecutors,e],this.upstreamDone,this.combineChildResults,this.combineWithChildResult,this.onPull)}}class zF{constructor(e,t){this._tag=zC,this.value=e,this.next=t}close(e){return this.next.close(e)}enqueuePullFromChild(e){return this}}let zT={[Symbol.for("effect/ChannelUpstreamPullRequest")]:{_A:e=>e}},zR=e=>{let t=Object.create(zT);return t._tag="Pulled",t.value=e,t},zN=e=>{let t=Object.create(zT);return t._tag="NoUpstream",t.activeDownstreamCount=e,t};class zA{constructor(e,t,r){this._activeSubexecutor=void 0,this._cancelled=void 0,this._closeLastSubstream=void 0,this._done=void 0,this._doneStack=[],this._emitted=void 0,this._input=void 0,this._inProgressFinalizer=void 0,this._currentChannel=e,this._executeCloseLastSubstream=r,this._providedEnv=t}run(){let e;for(;void 0===e;)if(void 0!==this._cancelled)e=this.processCancellation();else if(void 0!==this._activeSubexecutor)e=this.runSubexecutor();else try{if(void 0===this._currentChannel)e=zm();else switch(h6(this._currentChannel)&&(this._currentChannel=M9(this._currentChannel)),this._currentChannel._tag){case MA:e=this.runBracketOut(this._currentChannel);break;case Mj:{let t=this._currentChannel.input;if(this._currentChannel=this._currentChannel.channel,void 0!==this._input){let r=this._input;this._input=void 0;let n=()=>pv(t.awaitRead(),()=>pJ(()=>{let e=r.run();switch(e._tag){case zu:return dy(r.getDone(),{onFailure:e=>t.error(e),onSuccess:e=>t.done(e)});case zh:return pv(t.emit(r.getEmit()),()=>n());case zp:return pC(e.effect,{onFailure:e=>t.error(e),onSuccess:()=>n()});case zf:return zz(e,()=>n(),e=>t.error(e))}}));e=zb(pv(_0(pN(n())),e=>pH(()=>this.addFinalizer(t=>pv(p9(e),()=>pJ(()=>{let e=this.restorePipe(t,r);return void 0!==e?e:pZ}))))))}break}case MM:{let e=new zA(this._currentChannel.value(),this._providedEnv,e=>pH(()=>{let t=void 0===this._closeLastSubstream?pZ:this._closeLastSubstream;this._closeLastSubstream=W(t,wi(e))}));e._input=this._input;let t=this._currentChannel;this._activeSubexecutor=new zO(e,e=>t.k(e),void 0,[],(e,r)=>t.combineInners(e,r),(e,r)=>t.combineAll(e,r),e=>t.onPull(e),e=>t.onEmit(e)),this._closeLastSubstream=void 0,this._currentChannel=void 0;break}case Mz:this._emitted=this._currentChannel.out,this._currentChannel=void 0!==this._activeSubexecutor?void 0:zl,e=zg();break;case MD:this.runEnsuring(this._currentChannel);break;case M$:e=this.doneHalt(this._currentChannel.error());break;case MP:this._doneStack.push(this._currentChannel.k),this._currentChannel=this._currentChannel.channel;break;case ML:{let t=void 0===this._providedEnv?this._currentChannel.effect():W(this._currentChannel.effect(),Ch(this._providedEnv));e=zb(pC(t,{onFailure:e=>{let t=this.doneHalt(e);return void 0!==t&&zy(t)?t.effect:pZ},onSuccess:e=>{let t=this.doneSucceed(e);return void 0!==t&&zy(t)?t.effect:pZ}}));break}case MU:{let e=this._input,t=new zA(this._currentChannel.left(),this._providedEnv,e=>this._executeCloseLastSubstream(e));t._input=e,this._input=t,this.addFinalizer(t=>{let r=this.restorePipe(t,e);return void 0!==r?r:pZ}),this._currentChannel=this._currentChannel.right();break}case Mq:{let e=this._providedEnv;this._providedEnv=this._currentChannel.context(),this._currentChannel=this._currentChannel.inner,this.addFinalizer(()=>pH(()=>{this._providedEnv=e}));break}case MB:{let t=this._currentChannel;e=zx(this._input,q,e=>{try{this._currentChannel=t.more(e)}catch(e){this._currentChannel=t.done.onExit(dp(e))}},e=>{let r=e=>t.done.onExit(e);this._currentChannel=r(e)});break}case MJ:e=this.doneSucceed(this._currentChannel.evaluate());break;case MH:e=this.doneSucceed(this._currentChannel.terminal);break;case MK:this._currentChannel=this._currentChannel.channel()}}catch(e){this._currentChannel=M4(hi(e))}return e}getDone(){return this._done}getEmit(){return this._emitted}cancelWith(e){this._cancelled=e}clearInProgressFinalizer(){this._inProgressFinalizer=void 0}storeInProgressFinalizer(e){this._inProgressFinalizer=e}popAllFinalizers(e){let t=[],r=this._doneStack.pop();for(;r;)"ContinuationFinalizer"===r._tag&&t.push(r.finalizer),r=this._doneStack.pop();let n=0===t.length?pZ:zM(t,e);return this.storeInProgressFinalizer(n),n}popNextFinalizers(){let e=[];for(;0!==this._doneStack.length;){let t=this._doneStack[this._doneStack.length-1];if(t._tag===MC)break;e.push(t),this._doneStack.pop()}return e}restorePipe(e,t){let r=this._input;return(this._input=t,void 0!==r)?r.close(e):pZ}close(e){let t,r,n=this._inProgressFinalizer;void 0!==n&&(t=W(n,w_(pH(()=>this.clearInProgressFinalizer()))));let i=this.popAllFinalizers(e);void 0!==i&&(r=W(i,w_(pH(()=>this.clearInProgressFinalizer()))));let s=void 0===this._activeSubexecutor?void 0:this._activeSubexecutor.close(e);if(void 0!==s||void 0!==t||void 0!==r)return W(pf(zj(s)),wr(pf(zj(t))),wr(pf(zj(r))),pj(([[e,t],r])=>W(e,dk(t),dk(r))),pG,pv(e=>pJ(()=>e)))}doneSucceed(e){if(0===this._doneStack.length)return this._done=dS(e),this._currentChannel=void 0,zm();let t=this._doneStack[this._doneStack.length-1];if(t._tag===MC){this._doneStack.pop(),this._currentChannel=t.onSuccess(e);return}let r=this.popNextFinalizers();if(0===this._doneStack.length)return this._doneStack=r.reverse(),this._done=dS(e),this._currentChannel=void 0,zm();let n=zM(r.map(e=>e.finalizer),dS(e));return this.storeInProgressFinalizer(n),zb(W(n,w_(pH(()=>this.clearInProgressFinalizer())),pG,pv(()=>pH(()=>this.doneSucceed(e)))))}doneHalt(e){if(0===this._doneStack.length)return this._done=dd(e),this._currentChannel=void 0,zm();let t=this._doneStack[this._doneStack.length-1];if(t._tag===MC){this._doneStack.pop();try{this._currentChannel=t.onHalt(e)}catch(e){this._currentChannel=M4(hi(e))}return}let r=this.popNextFinalizers();if(0===this._doneStack.length)return this._doneStack=r.reverse(),this._done=dd(e),this._currentChannel=void 0,zm();let n=zM(r.map(e=>e.finalizer),dd(e));return this.storeInProgressFinalizer(n),zb(W(n,w_(pH(()=>this.clearInProgressFinalizer())),pG,pv(()=>pH(()=>this.doneHalt(e)))))}processCancellation(){return this._currentChannel=void 0,this._done=this._cancelled,this._cancelled=void 0,zm()}runBracketOut(e){return zb(pG(pC(this.provide(e.acquire()),{onFailure:e=>pH(()=>{this._currentChannel=M4(e)}),onSuccess:t=>pH(()=>{this.addFinalizer(r=>this.provide(e.finalizer(t,r))),this._currentChannel=zc(t)})})))}provide(e){return void 0===this._providedEnv?e:W(e,Ch(this._providedEnv))}runEnsuring(e){this.addFinalizer(e.finalizer),this._currentChannel=e.channel}addFinalizer(e){this._doneStack.push(new MF(e))}runSubexecutor(){let e=this._activeSubexecutor;switch(e._tag){case z_:return this.pullFromChild(e.childExecutor,e.parentSubexecutor,e.onEmit,e);case zw:return this.pullFromUpstream(e);case zk:return this.drainChildExecutors(e);case zC:return this._emitted=e.value,this._activeSubexecutor=e.next,zg()}}replaceSubexecutor(e){this._currentChannel=void 0,this._activeSubexecutor=e}finishWithExit(e){let t=dy(e,{onFailure:e=>this.doneHalt(e),onSuccess:e=>this.doneSucceed(e)});return this._activeSubexecutor=void 0,void 0===t?pZ:zv(t)}finishSubexecutorWithCloseEffect(e,...t){this.addFinalizer(()=>W(t,_Y(t=>W(pH(()=>t(e)),pv(e=>void 0!==e?e:pZ)),{discard:!0})));let r=W(e,dy({onFailure:e=>this.doneHalt(e),onSuccess:e=>this.doneSucceed(e)}));return this._activeSubexecutor=void 0,r}applyUpstreamPullStrategy(e,t,r){switch(r._tag){case MT:{let n=!e||t.some(e=>void 0!==e);return[r.emitSeparator,n?[void 0,...t]:t]}case"PullAfterAllEnqueued":{let n=!e||t.some(e=>void 0!==e);return[r.emitSeparator,n?[...t,void 0]:t]}}}pullFromChild(e,t,r,n){return zx(e,q,i=>{let s=r(i);switch(s._tag){case MS:break;case"Close":this.finishWithDoneValue(e,t,s.value);break;case"Yield":{let e=t.enqueuePullFromChild(n);this.replaceSubexecutor(e)}}this._activeSubexecutor=new zF(i,this._activeSubexecutor)},dy({onFailure:r=>{let n=this.handleSubexecutorFailure(e,t,r);return void 0===n?void 0:zS(n)},onSuccess:r=>{this.finishWithDoneValue(e,t,r)}}))}finishWithDoneValue(e,t,r){switch(t._tag){case zw:{let n=new zO(t.upstreamExecutor,t.createChild,void 0!==t.lastDone?t.combineChildResults(t.lastDone,r):r,t.activeChildExecutors,t.combineChildResults,t.combineWithChildResult,t.onPull,t.onEmit);this._closeLastSubstream=e.close(dS(r)),this.replaceSubexecutor(n);break}case zk:{let n=new zI(t.upstreamExecutor,void 0!==t.lastDone?t.combineChildResults(t.lastDone,r):r,t.activeChildExecutors,t.upstreamDone,t.combineChildResults,t.combineWithChildResult,t.onPull);this._closeLastSubstream=e.close(dS(r)),this.replaceSubexecutor(n)}}}handleSubexecutorFailure(e,t,r){return this.finishSubexecutorWithCloseEffect(dd(r),e=>t.close(e),t=>e.close(t))}pullFromUpstream(e){if(0===e.activeChildExecutors.length)return this.performPullFromUpstream(e);let t=e.activeChildExecutors[0],r=new zO(e.upstreamExecutor,e.createChild,e.lastDone,e.activeChildExecutors.slice(1),e.combineChildResults,e.combineWithChildResult,e.onPull,e.onEmit);if(void 0===t)return this.performPullFromUpstream(r);this.replaceSubexecutor(new zE(t.childExecutor,r,t.onEmit))}performPullFromUpstream(e){return zx(e.upstreamExecutor,e=>{let t=void 0===this._closeLastSubstream?pZ:this._closeLastSubstream;return this._closeLastSubstream=void 0,W(this._executeCloseLastSubstream(t),wi(e))},t=>{if(void 0!==this._closeLastSubstream){let r=this._closeLastSubstream;return this._closeLastSubstream=void 0,W(this._executeCloseLastSubstream(r),pj(()=>{let r=new zA(e.createChild(t),this._providedEnv,this._executeCloseLastSubstream);r._input=this._input;let[n,i]=this.applyUpstreamPullStrategy(!1,e.activeChildExecutors,e.onPull(zR(t)));this._activeSubexecutor=new zE(r,new zO(e.upstreamExecutor,e.createChild,e.lastDone,i,e.combineChildResults,e.combineWithChildResult,e.onPull,e.onEmit),e.onEmit),tF(n)&&(this._activeSubexecutor=new zF(n.value,this._activeSubexecutor))}))}let r=new zA(e.createChild(t),this._providedEnv,this._executeCloseLastSubstream);r._input=this._input;let[n,i]=this.applyUpstreamPullStrategy(!1,e.activeChildExecutors,e.onPull(zR(t)));this._activeSubexecutor=new zE(r,new zO(e.upstreamExecutor,e.createChild,e.lastDone,i,e.combineChildResults,e.combineWithChildResult,e.onPull,e.onEmit),e.onEmit),tF(n)&&(this._activeSubexecutor=new zF(n.value,this._activeSubexecutor))},t=>{if(e.activeChildExecutors.some(e=>void 0!==e)){let t=new zI(e.upstreamExecutor,e.lastDone,[void 0,...e.activeChildExecutors],e.upstreamExecutor.getDone(),e.combineChildResults,e.combineWithChildResult,e.onPull);if(void 0!==this._closeLastSubstream){let e=this._closeLastSubstream;return this._closeLastSubstream=void 0,W(this._executeCloseLastSubstream(e),pj(()=>this.replaceSubexecutor(t)))}return void this.replaceSubexecutor(t)}let r=this._closeLastSubstream,n=this.finishSubexecutorWithCloseEffect(W(t,dx(t=>e.combineWithChildResult(e.lastDone,t))),()=>r,t=>e.upstreamExecutor.close(t));return void 0===n?void 0:zS(n)})}drainChildExecutors(e){if(0===e.activeChildExecutors.length){let t=this._closeLastSubstream;return void 0!==t&&this.addFinalizer(()=>pB(t)),this.finishSubexecutorWithCloseEffect(e.upstreamDone,()=>t,t=>e.upstreamExecutor.close(t))}let t=e.activeChildExecutors[0],r=e.activeChildExecutors.slice(1);if(void 0===t){let[t,n]=this.applyUpstreamPullStrategy(!0,r,e.onPull(zN(r.reduce((e,t)=>void 0!==t?e+1:e,0))));return(this.replaceSubexecutor(new zI(e.upstreamExecutor,e.lastDone,n,e.upstreamDone,e.combineChildResults,e.combineWithChildResult,e.onPull)),tF(t))?(this._emitted=t.value,zg()):void 0}let n=new zI(e.upstreamExecutor,e.lastDone,r,e.upstreamDone,e.combineChildResults,e.combineWithChildResult,e.onPull);this.replaceSubexecutor(new zE(t.childExecutor,n,t.onEmit))}}let zj=e=>void 0!==e?e:pZ,zM=(e,t)=>W(_Y(e,e=>pf(e(t))),pj(e=>W(dh(e),iO(()=>d_))),pv(e=>pJ(()=>e))),zz=(e,t,r)=>{let n=[e],i=()=>{let e=n.pop();if(void 0===e||void 0===e.upstream)return ph("Unexpected end of input for channel execution");let s=e.upstream.run();switch(s._tag){case zh:{let s=e.onEmit(e.upstream.getEmit());if(0===n.length){if(void 0===s)return pJ(t);return W(s,pC({onFailure:r,onSuccess:t}))}if(void 0===s)return pJ(()=>i());return W(s,pC({onFailure:r,onSuccess:()=>i()}))}case zu:{let s=e.onDone(e.upstream.getDone());if(0===n.length){if(void 0===s)return pJ(t);return W(s,pC({onFailure:r,onSuccess:t}))}if(void 0===s)return pJ(()=>i());return W(s,pC({onFailure:r,onSuccess:()=>i()}))}case zp:return n.push(e),W(e.onEffect(s.effect),ps(t=>pJ(()=>{let r=e.onDone(dd(t));return void 0===r?pZ:r})),pC({onFailure:r,onSuccess:()=>i()}));case zf:return n.push(e),n.push(s),pJ(()=>i())}};return i()},zD=U(2,(e,t)=>{let r=(t,r,n)=>h7(pH(()=>new zA(e,void 0,q)),e=>pJ(()=>z$(e.run(),e).pipe(pA(t),wi(dT(t)),wn(dT(r)))),(e,t)=>{let r=e.close(t);return void 0===r?pZ:bL(r,e=>fW(n,pg(e)))});return pY(e=>_G([fY(t,xW),dI(),dI()]).pipe(pv(([n,i,s])=>e(r(i,s,n)).pipe(wT(t),pv(r=>t.addFinalizer(e=>{let t=da(e)?hg(e.cause):void 0;return xJ(i).pipe(pv(e=>e?dM(s,void 0).pipe(wi(yX(r)),wi(ko(r))):dM(s,void 0).pipe(wi(t&&lD(t)>0?fe(r,cr(t)):p9(r)),wi(ko(r)))))}).pipe(wi(e(dT(i)))))))))}),z$=(e,t)=>{switch(e._tag){case zp:return W(e.effect,pv(()=>z$(t.run(),t)));case zh:return z$(t.run(),t);case zu:return pJ(()=>t.getDone());case zf:return zz(e,()=>z$(t.run(),t),pg)}},zP="Done",zL={[Symbol.for("effect/ChannelMergeDecision")]:{_R:e=>e,_E0:e=>e,_Z0:e=>e,_E:e=>e,_Z:e=>e}},zU=e=>{let t=Object.create(zL);return t._tag=zP,t.effect=e,t},zq=e=>{let t=Object.create(zL);return t._tag="Await",t.f=e,t},zB="BothRunning",zJ="LeftDone",zH="RightDone",zK=Symbol.for("effect/ChannelMergeState"),zW={[zK]:zK},zV=(e,t)=>{let r=Object.create(zW);return r._tag=zB,r.left=e,r.right=t,r},zG=e=>{let t=Object.create(zW);return t._tag=zJ,t.f=e,t},zY=e=>{let t=Object.create(zW);return t._tag=zH,t.f=e,t},zZ="BackPressure",zQ="BufferSliding",zX=Symbol.for("effect/ChannelMergeStrategy"),z0={[zX]:zX},z1=e=>{let t=Object.create(z0);return t._tag=zZ,t},z2=e=>{let t=Object.create(z0);return t._tag=zQ,t},z3=U(2,(e,{onBackPressure:t,onBufferSliding:r})=>{switch(e._tag){case zZ:return t();case zQ:return r()}}),z5="Empty",z4="Emit",z6="Error",z8="Done",z7=e=>({_tag:z5,notifyProducer:e}),z9=e=>({_tag:z4,notifyConsumers:e}),De=e=>({_tag:z6,cause:e}),Dt=e=>({_tag:z8,done:e});class Dr{constructor(e){this.ref=e}awaitRead(){return pw(g1(this.ref,e=>e._tag===z5?[dT(e.notifyProducer),e]:[pZ,e]))}get close(){return py(e=>this.error(hs(e)))}done(e){return pw(g1(this.ref,t=>{switch(t._tag){case z5:return[dT(t.notifyProducer),t];case z4:return[_Y(t.notifyConsumers,t=>dM(t,nw(e)),{discard:!0}),Dt(e)];case z6:case z8:return[pT,t]}}))}emit(e){return pv(dI(),t=>pw(g1(this.ref,r=>{switch(r._tag){case z5:return[dT(r.notifyProducer),r];case z4:{let n=r.notifyConsumers[0],i=r.notifyConsumers.slice(1);if(void 0!==n)return[dM(n,nk(e)),0===i.length?z7(t):z9(i)];throw Error("Bug: Channel.SingleProducerAsyncInput.emit - Queue was empty! please report an issue at https://github.com/Effect-TS/effect/issues")}case z6:case z8:return[pT,r]}})))}error(e){return pw(g1(this.ref,t=>{switch(t._tag){case z5:return[dT(t.notifyProducer),t];case z4:return[_Y(t.notifyConsumers,t=>dA(t,e),{discard:!0}),De(e)];case z6:case z8:return[pT,t]}}))}get take(){return this.takeWith(e=>dd(hk(e,nw)),e=>dS(e),e=>df(nk(e)))}takeWith(e,t,r){return pv(dI(),n=>pw(g1(this.ref,i=>{switch(i._tag){case z5:return[wi(dM(i.notifyProducer,void 0),pk(dT(n),{onFailure:e,onSuccess:nF({onLeft:r,onRight:t})})),z9([n])];case z4:return[pk(dT(n),{onFailure:e,onSuccess:nF({onLeft:r,onRight:t})}),z9([...i.notifyConsumers,n])];case z6:return[pB(e(i.cause)),i];case z8:return[pB(r(i.done)),i]}})))}}let Dn=()=>W(dI(),pv(e=>gZ(z7(e))),pj(e=>new Dr(e))),Di=U(2,(e,t)=>D_(e,()=>t)),Ds=e=>za(()=>{let t=(e,r,n)=>DW(g1(n,i=>r(i)?[zr({onInput:i=>M8(zc(i),()=>t(e,r,n)),onFailure:e=>M5(e),onDone:e=>zs(e)}),i]:[M8(zc(i),()=>t(e,r,n)),e]));return t(e.empty,e.isEmpty,e.ref)}),Da=U(2,(e,t)=>MZ(e,e=>nF(hx(e),{onLeft:t,onRight:M4}))),Do=U(2,(e,t)=>M1(e,t,()=>void 0,()=>void 0)),Dl=e=>za(()=>{let t=[];return W(ze(e,Dc(t)),M8(e=>zi([av(t),e])))}),Dc=e=>zr({onInput:t=>M8(zo(()=>{e.push(t)}),()=>Dc(e)),onFailure:M5,onDone:zi}),Du=e=>{let t=zn({onInput:()=>t,onFailure:M4,onDone:zi});return ze(e,t)},Dh=U(2,(e,t)=>M3(e,()=>t)),Dp=()=>M9(dP()),Df=e=>M8(Dp(),e),Dd=e=>M8(e,q),Dm=U(2,(e,t)=>M7(e,{onFailure:e=>{let r=hx(e);switch(r._tag){case"Left":return t.onFailure(r.left);case"Right":return M4(r.right)}},onSuccess:t.onSuccess})),Dg=e=>DW(e.takeWith(M4,t=>M8(zc(t),()=>Dg(e)),zi)),Db=e=>za(()=>Dx(e)),Dx=e=>W(M9(ji(e)),M8(nF({onLeft:dy({onFailure:M4,onSuccess:zs}),onRight:t=>M8(zc(t),()=>Dx(e))}))),Dy=()=>zr({onInput:e=>M8(zc(e),()=>Dy()),onFailure:M5,onDone:zs}),Dv=U(2,(e,t)=>DN(e,{other:M9(t),onSelfDone:e=>zU(pJ(()=>e)),onOtherDone:e=>zU(pJ(()=>e))})),DS=U(2,(e,t)=>Dv(e,dT(t))),D_=U(2,(e,t)=>M8(e,e=>zo(()=>t(e)))),Dw=U(2,(e,t)=>M8(e,e=>M9(t(e)))),Dk=U(2,(e,t)=>DC(e,hk(t))),DC=U(2,(e,t)=>MZ(e,e=>M4(t(e)))),DE=U(2,(e,t)=>{let r=zr({onInput:e=>M8(zc(t(e)),()=>r),onFailure:M5,onDone:zs});return ze(e,r)}),DO=U(2,(e,t)=>{let r=zn({onInput:e=>W(M9(t(e)),M8(zc),M8(()=>r)),onFailure:M4,onDone:zs});return ze(e,r)}),DI=U(3,(e,t,r)=>DG(n=>EH(function*(){let i=yield*Dn(),s=Dg(i),a=yield*jv(r);yield*fW(n,jT(a));let o=yield*dI(),l=r===Number.POSITIVE_INFINITY?e=>q:(yield*EZ(r)).withPermits,c=yield*s.pipe(ze(e),DB(n));yield*c.pipe(pC({onFailure:e=>jr(a,pg(e)),onSuccess:nF({onLeft:e=>wi(pN(l(r)(pZ)),pe(jr(a,pB(nw(e))))),onRight:e=>EH(function*(){let r=yield*dI(),i=yield*dI();yield*jr(a,pj(dT(r),nk)),yield*dM(i,void 0).pipe(wi(pY(r=>pf(r(dT(o))).pipe(wN(pf(r(t(e)))),pv(q))).pipe(bL(e=>dA(o,e)),pA(r))),l(1),wT(n)),yield*dT(i)})})}),bv,pN,wT(n));let u=DW(pk(pw(ji(a)),{onFailure:M4,onSuccess:nF({onLeft:zs,onRight:e=>M8(zc(e),()=>u)})}));return M2(u,i)}))),DF=e=>t=>DT(e)(t,K),DT=({bufferSize:e=16,concurrency:t,mergeStrategy:r=z1()})=>(n,i)=>DG(s=>EH(function*(){let a="unbounded"===t?Number.MAX_SAFE_INTEGER:t,o=yield*Dn(),l=Dg(o),c=yield*jv(e);yield*fW(s,jT(c));let u=yield*jw();yield*fW(s,jT(u));let h=yield*gZ(iC()),p=yield*dI(),f=(yield*EZ(a)).withPermits,d=yield*DB(ze(l,n),s);function m(e){return e.pipe(pv(nF({onLeft:e=>pB(tR(e)),onRight:e=>h9(jr(c,pB(nk(e))),iC())})),C1({until:e=>tF(e)}),pv(e=>g2(h,iE({onNone:()=>tR(e.value),onSome:t=>tR(i(t,e.value))}))),ps(e=>hp(e)?pg(e):jr(c,pg(e)).pipe(wi(dM(p,void 0)),pe)))}yield*d.pipe(pC({onFailure:e=>jr(c,pg(e)).pipe(wi(pB(!1))),onSuccess:nF({onLeft:e=>wx(pN(dT(p)),pN(f(a)(pZ)),{onSelfDone:(e,t)=>h9(p9(t),!1),onOtherDone:(t,r)=>wi(p9(r),gQ(h).pipe(pv(iE({onNone:()=>jr(c,pB(nw(e))),onSome:t=>jr(c,pB(nw(i(t,e))))})),h9(!1)))}),onRight:e=>z3(r,{onBackPressure:()=>EH(function*(){let t=yield*dI(),r=_9(t=>DB(ze(l,e),t).pipe(pv(e=>wy(pf(m(e)),pf(pN(dT(p))))),pv(q)));return yield*dM(t,void 0).pipe(wi(r),f(1),wT(s)),yield*dT(t),!(yield*xJ(p))}),onBufferSliding:()=>EH(function*(){let t=yield*dI(),r=yield*dI(),n=yield*jC(u);yield*ji(u).pipe(pv(e=>dM(e,void 0)),bJ(()=>n>=a)),yield*jr(u,t);let i=_9(r=>DB(ze(l,e),r).pipe(pv(e=>pf(m(e)).pipe(wy(pf(pN(dT(p)))),wy(pf(pN(dT(t)))))),pv(q)));return yield*dM(r,void 0).pipe(wi(i),f(1),wT(s)),yield*dT(r),!(yield*xJ(p))})})})}),C1({while:e=>e}),wT(s));let g=W(ji(c),pw,pk({onFailure:M4,onSuccess:nF({onLeft:zs,onRight:e=>M8(zc(e),()=>g)})}),DW);return M2(g,o)})),DR=U(3,(e,t,r)=>DF(r)(DE(e,t))),DN=U(2,(e,t)=>DG(function(r){return EH(function*(){let n=yield*Dn(),i=Dg(n),s=yield*DB(ze(i,e),r),a=yield*DB(ze(i,t.other),r);function o(e,t,n){return(i,s,a)=>{function o(e){return e._tag===zP?pB(M9(wi(p9(t),e.effect))):pj(yX(t),dy({onFailure:t=>M9(e.f(dd(t))),onSuccess:nF({onLeft:t=>M9(e.f(dS(t))),onRight:t=>D1(zc(t),l(a(e.f)))})}))}return dy(e,{onFailure:e=>o(i(dd(e))),onSuccess:nF({onLeft:e=>o(i(dS(e))),onRight:e=>pB(M8(zc(e),()=>M8(M9(wT(pN(n),r)),e=>l(s(e,t)))))})})}}function l(e){switch(e._tag){case zB:return DW(wx(pN(y1(e.left)),pN(y1(e.right)),{onSelfDone:(r,n)=>wi(p9(n),o(r,e.right,s)(t.onSelfDone,zV,e=>zG(e))),onOtherDone:(r,n)=>wi(p9(n),o(r,e.left,a)(t.onOtherDone,(e,t)=>zV(t,e),e=>zY(e)))}));case zJ:return DW(pj(pf(a),dy({onFailure:t=>M9(e.f(dd(t))),onSuccess:nF({onLeft:t=>M9(e.f(dS(t))),onRight:t=>M8(zc(t),()=>l(zG(e.f)))})})));case zH:return DW(pj(pf(s),dy({onFailure:t=>M9(e.f(dd(t))),onSuccess:nF({onLeft:t=>M9(e.f(dS(t))),onRight:t=>M8(zc(t),()=>l(zY(e.f)))})})))}}return M9(h8(e=>{let t=h8(t=>(t.transferChildren(e.scope()),pZ));return ws(pN(s).pipe(w_(t),wT(r)),pN(a).pipe(w_(t),wT(r)),(e,t)=>zV(e,t))})).pipe(M8(l),M2(n))})})),DA=((e,t)=>Da(e,e=>M6(()=>hi(t(e)))),U(2,(e,t)=>Da(e,t))),Dj=U(2,(e,t)=>za(()=>{let r,n=zr({onInput:e=>M8(zc(e),()=>n),onFailure:e=>M4(hi(r=D3(e))),onDone:zs}),i=zn({onInput:e=>W(zc(e),M8(()=>i)),onFailure:e=>hu(e)&&D5(e.defect)&&eJ(e.defect,r)?M5(e.defect.error):M4(e),onDone:zs});return ze(ze(ze(e,n),t),i)})),DM=((e,t)=>DG(r=>pj(kB(t,r),t=>zt(e,t))),e=>M8(e,()=>DM(e))),Dz=e=>_9(t=>zD(e,t)),DD=e=>Dz(Du(e)),D$=e=>_7(t=>zD(e,t)),DP=e=>DW(pY(t=>pj(wh(),r=>MY(bL(t(wp(e,r)),e=>fG(r,dd(e))),(e,t)=>fG(r,t))))),DL=e=>DV(pj(wo,t=>M8(M9(e(t)),zc))),DU=e=>DH(e),Dq=e=>pv(wo,t=>DB(e,t)),DB=U(2,(e,t)=>wr(pH(()=>new zA(e,void 0,q)),kC()).pipe(pK(([e,r])=>fV(t,t=>{let n=e.close(t);return void 0!==n?Ch(n,r):pZ})),pG,pj(([e])=>pJ(()=>DJ(e.run(),e))))),DJ=(e,t)=>{switch(e._tag){case zu:return dy(t.getDone(),{onFailure:pg,onSuccess:e=>pB(nw(e))});case zh:return pB(nk(t.getEmit()));case zp:return W(e.effect,pv(()=>DJ(t.run(),t)));case zf:return zz(e,()=>DJ(t.run(),t),e=>pg(e))}},DH=e=>za(()=>DK(e)),DK=e=>zn({onInput:t=>M8(M9(jr(e,nk(t))),()=>DK(e)),onFailure:t=>M9(W(jr(e,nw(dd(t))))),onDone:t=>M9(W(jr(e,nw(dS(t)))))}),DW=e=>Dd(M9(e)),DV=e=>M0(DP(e),(e,t)=>e,(e,t)=>e),DG=e=>M0(DL(e),(e,t)=>e,(e,t)=>e),DY=(...e)=>DZ(am(e)),DZ=e=>DQ(0,e.length,e),DQ=(e,t,r)=>e===t?zl:W(zc(W(r,a_(e))),M8(()=>DQ(e+1,t,r))),DX=U(e=>MG(e[1]),(e,t,r)=>r?.concurrent?DN(e,{other:t,onSelfDone:e=>zq(t=>pJ(()=>dw(e,t))),onOtherDone:e=>zq(t=>pJ(()=>dw(t,e)))}):M8(e,e=>D_(t,t=>[e,t]))),D0=U(e=>MG(e[1]),(e,t,r)=>r?.concurrent?D_(DX(e,t,{concurrent:!0}),e=>e[0]):M8(e,e=>Di(t,e))),D1=U(e=>MG(e[1]),(e,t,r)=>r?.concurrent?D_(DX(e,t,{concurrent:!0}),e=>e[1]):M8(e,()=>t)),D2=Symbol.for("effect/Channel/ChannelException"),D3=e=>({_tag:"ChannelException",[D2]:D2,error:e}),D5=e=>ec(e,D2),D4=Symbol.for("effect/RcRef"),D6={_tag:"Empty"},D8={_tag:"Closed"},D7={_A:q,_E:q};class D9 extends t4{static{p=gK}constructor(e,t,r,n){super(),this[D4]=D7,this[p]=gK,this.state=D6,this.semaphore=wC(1),this.acquire=e,this.context=t,this.scope=r,this.idleTimeToLive=n,this.get=$e(this)}commit(){return this.get}}let $e=e=>pY(t=>pJ(()=>{switch(e.state._tag){case"Closed":return pT;case"Acquired":return e.state.refCount++,e.state.fiber?h9(p9(e.state.fiber),e.state):pB(e.state);case"Empty":return wh().pipe(bg("scope"),bm("value",({scope:r})=>t(fS(e.acquire,fI,tH(e.context,wa,r)))),pj(({scope:t,value:r})=>{let n={_tag:"Acquired",value:r,scope:t,fiber:void 0,refCount:1};return e.state=n,n}))}})).pipe(e.semaphore.withPermits(1),bg("state"),bm("scope",()=>wa),pK(({scope:t,state:r})=>t.addFinalizer(()=>pJ(()=>(r.refCount--,r.refCount>0)?pZ:void 0===e.idleTimeToLive?(e.state=D6,fG(r.scope,d_)):gO(e.idleTimeToLive).pipe(pN,p6(pJ(()=>"Acquired"===e.state._tag&&0===e.state.refCount?(e.state=D6,fG(r.scope,d_)):pZ)),w_(pH(()=>{r.fiber=void 0})),wT(e.scope),pK(e=>{r.fiber=e}),e.semaphore.withPermits(1))))),pj(({state:e})=>e.value)),$t={_tag:"Left"},$r={_tag:"Right"},$n={_tag:"Both"},$i={_tag:"Either"},$s=e=>{switch(e){case"left":return $t;case"right":return $r;case"both":return $n;case"either":return $i;default:return e}},$a=Symbol.for("effect/Sink"),$o={_A:e=>e,_In:e=>e,_L:e=>e,_E:e=>e,_R:e=>e};class $l{constructor(e){this[$a]=$o,this.channel=e}pipe(){return e6(this,arguments)}}let $c=e=>ec(e,$a),$u=e=>new $l(za(()=>$z(e()))),$h=(e,t)=>zn({onInput:r=>{let[n,i]=aL(r,e);return n.length<e?$h(e-n.length,aI(t,n)):aj(i)?zi(aI(t,n)):M8(zc(i),()=>zi(aI(t,n)))},onFailure:M4,onDone:()=>zi(t)}),$p=((e,t)=>W(e,$p(aP(t))),U(2,(e,t)=>{let r=zr({onInput:e=>W(zc(t(e)),M8(()=>r)),onFailure:M5,onDone:zi});return new $l(W(r,ze($z(e))))})),$f=U(2,(e,t)=>{let r=zr({onInput:e=>W(M9(t(e)),M8(zc),M8(()=>r)),onFailure:M5,onDone:zi});return new $l(W(r,Dj($z(e))))}),$d=new $l(Du(Dy())),$m=e=>new $l(M5(e)),$g=(e,t,r)=>$u(()=>new $l($b(e,t,r))),$b=(e,t,r)=>t(e)?zr({onInput:n=>{let[i,s]=$x(e,n,t,r,0,n.length);return aM(s)?W(zc(s),Di(i)):$b(i,t,r)},onFailure:M5,onDone:()=>zs(e)}):zs(e),$x=(e,t,r,n,i,s)=>{if(i===s)return[e,ap()];let a=n(e,W(t,a_(i)));return r(a)?$x(a,t,r,n,i+1,s):[a,W(t,aE(i+1))]},$y=U(2,(e,t)=>new $l(W($z(e),MQ,Dm({onFailure:e=>$z(t.onFailure(e)),onSuccess:([e,r])=>za(()=>{let n={ref:W(e,aT(aM))},i=W(zo(()=>{let e=n.ref;return n.ref=ap(),e}),M8(e=>DZ(e)));return M8(MQ(W(i,D1(Dy()),ze($z(t.onSuccess(r))))),([e,t])=>W(zi(n.ref),M8(DZ),D1(DZ(e)),Di(t)))})})))),$v=(e,t,r)=>$u(()=>new $l($S(e,t,r))),$S=(e,t,r)=>t(e)?zr({onInput:n=>$S(r(e,n),t,r),onFailure:M5,onDone:()=>zs(e)}):zs(e),$_=(e,t,r)=>$u(()=>new $l($w(e,t,r))),$w=(e,t,r)=>t(e)?zr({onInput:n=>W(M9($k(e,n,t,r)),M8(([e,n])=>W(n,iE({onNone:()=>$w(e,t,r),onSome:t=>W(zc(t),Di(e))})))),onFailure:M5,onDone:()=>zs(e)}):zs(e),$k=(e,t,r,n)=>$C(e,t,0,t.length,r,n),$C=(e,t,r,n,i,s)=>r===n?pB([e,iC()]):W(s(e,W(t,a_(r))),pv(e=>i(e)?$C(e,t,r+1,n,i,s):pB([e,tR(W(t,aE(r+1)))]))),$E=U(2,(e,t)=>$y(e,{onFailure:$m,onSuccess:t})),$O=e=>{let t=zn({onInput:r=>W(M9(_Y(r,t=>e(t),{discard:!0})),M8(()=>t)),onFailure:M4,onDone:()=>zl});return new $l(t)},$I=e=>{let t=zn({onInput:r=>W(M9(e(r)),M8(()=>t)),onFailure:M4,onDone:()=>zl});return new $l(t)},$F=(e,t,r,n,i)=>r===n?i:W(M9(e(W(t,a_(r)))),M8(s=>s?$F(e,t,r+1,n,i):zc(W(t,aE(r)))),Da(e=>W(zc(W(t,aE(r))),D1(M5(e))))),$T=e=>new $l(e),$R=e=>new $l(M9(e)),$N=(e,t)=>t?.shutdown?$D(pj(_J(pB(e),jT),$N)):$I(t=>W(jn(e,t))),$A=U(2,(e,t)=>new $l(W($z(e),D_(t)))),$j=((e,t)=>new $l(W($z(e),Dw(t))),U(2,(e,t)=>$$(function(r){return EH(function*(){let n=yield*Mm(t?.capacity??16),i=yield*wp(Mv(n),r),s=yield*wp(Mv(n),r),a=DU(n),o=Db(i).pipe(ze($z(e)),D0(M9(jT(i))),DN({other:Db(s).pipe(ze($z(t.other)),D0(M9(jT(s)))),onSelfDone:t.onSelfDone,onOtherDone:t.onOtherDone}));return new $l(DN(a,{other:o,onSelfDone:()=>zq(q),onOtherDone:e=>zU(e)}))})}))),$M=((e,t,r)=>new $l(W(e,$z,Da(e=>iE(t(e),{onNone:()=>M6(()=>hi(r(e))),onSome:M5})))),(e,t,r=0)=>{let n,i=e[Symbol.iterator](),s=0,a=-1;for(;a<0&&(n=i.next())&&!n.done;){let e=n.value;s>=r&&t(e)&&(a=s),s+=1}return a}),$z=((e,t,r)=>new $l(W(M9(t),M8(n=>W(e,$z,M8(e=>W(M9(t),D_(t=>[e,r(n,t)]))))))),e=>h6(e)?$z($R(e)):e.channel),$D=e=>new $l(DV(e.pipe(pj(e=>$z(e))))),$$=e=>new $l(DG(t=>e(t).pipe(pj(e=>$z(e))))),$P=((e=>$c(e[1]),(e,t,r)=>$P(e,t,(e,t)=>t,r)),U(e=>$c(e[1]),(e,t,r,n)=>n?.concurrent?$j(e,{other:t,onSelfDone:dy({onFailure:e=>zU(pg(e)),onSuccess:e=>zq(dy({onFailure:pg,onSuccess:t=>pB(r(e,t))}))}),onOtherDone:dy({onFailure:e=>zU(pg(e)),onSuccess:e=>zq(dy({onFailure:pg,onSuccess:t=>pB(r(t,e))}))})}):$E(e,e=>$A(t,t=>r(e,t)))));class $L{constructor(e){this.value=e}}let $U=(e,t)=>({ref:e,isNew:t,isChanged:!1,expected:e.versioned,newValue:e.versioned.value}),$q=e=>e.newValue,$B=(e,t)=>{e.isChanged=!0,e.newValue=t},$J=e=>{e.ref.versioned=new $L(e.newValue)},$H=e=>e.ref.versioned!==e.expected,$K=e=>e.isChanged,$W="Invalid",$V="ReadWrite",$G=e=>{for(let t of e)$J(t[1])},$Y=e=>{let t="ReadOnly";for(let[,r]of e)if((t=$H(r)?$W:$K(r)?$V:t)===$W)break;return t},$Z=e=>{let t=new Map;for(let[,r]of e){for(let e of r.ref.todos)t.set(e[0],e[1]);r.ref.todos=new Map}return t},$Q=e=>{for(let[t,r]of Array.from(e.entries()).sort((e,t)=>e[0]-t[0]))r()},$X=(e,t,r)=>{let n=!1;for(let[,i]of t)i.ref.todos.has(e)||(i.ref.todos.set(e,r),n=!0);return n},$0="WithSTMRuntime",$1="OnFailure",$2="OnRetry",$3="OnSuccess",$5="Sync",$4="Succeed",$6="Retry",$8="Fail",$7="Interrupt",$9="Fail",Pe="Interrupt",Pt="Succeed",Pr="Retry",Pn="Done",Pi="Suspend",Ps="Done",Pa="Interrupted",Po="Running",Pl="effect/STM/State",Pc=Symbol.for(Pl),Pu=e=>ec(e,Pc),Ph=e=>e._tag===Po,Pp=e=>e._tag===Ps,Pf=e=>({[Pc]:Pc,_tag:Ps,exit:e,[eR](){return W(eN(Pl),ej(eN(Ps)),ej(eN(e)),eq(this))},[eB]:t=>Pu(t)&&t._tag===Ps&&eJ(e,t.exit)}),Pd=W(eN(Pl),ej(eN(Pa)),ej(eN("interrupted"))),Pm={[Pc]:Pc,_tag:Pa,[eR]:()=>Pd,[eB]:e=>Pu(e)&&e._tag===Pa},Pg=W(eN(Pl),ej(eN(Po)),ej(eN("running"))),Pb={[Pc]:Pc,_tag:Po,[eR]:()=>Pg,[eB]:e=>Pu(e)&&e._tag===Po},Px=e=>{switch(e._tag){case $9:return Pf(df(e.error));case"Die":return Pf(dp(e.defect));case Pe:return Pf(db(e.fiberId));case Pt:return Pf(dS(e.value));case Pr:throw Error("BUG: STM.STMState.fromTExit - please report an issue at https://github.com/Effect-TS/effect/issues")}},Py="effect/TExit",Pv=Symbol.for(Py),PS={_A:e=>e,_E:e=>e},P_=e=>ec(e,Pv),Pw=e=>e._tag===Pt,Pk=e=>e._tag===Pr,PC=e=>({[Pv]:PS,_tag:$9,error:e,[eR](){return W(eN(Py),ej(eN($9)),ej(eN(e)),eq(this))},[eB]:t=>P_(t)&&t._tag===$9&&eJ(e,t.error)}),PE=e=>({[Pv]:PS,_tag:"Die",defect:e,[eR](){return W(eN(Py),ej(eN("Die")),ej(eN(e)),eq(this))},[eB]:t=>P_(t)&&"Die"===t._tag&&eJ(e,t.defect)}),PO=e=>({[Pv]:PS,_tag:Pe,fiberId:e,[eR](){return W(eN(Py),ej(eN(Pe)),ej(eN(e)),eq(this))},[eB]:t=>P_(t)&&t._tag===Pe&&eJ(e,t.fiberId)}),PI=e=>({[Pv]:PS,_tag:Pt,value:e,[eR](){return W(eN(Py),ej(eN(Pt)),ej(eN(e)),eq(this))},[eB]:t=>P_(t)&&t._tag===Pt&&eJ(e,t.value)}),PF=W(eN(Py),ej(eN(Pr)),ej(eN("retry"))),PT={[Pv]:PS,_tag:Pr,[eR]:()=>PF,[eB]:e=>P_(e)&&Pk(e)},PR=e=>({_tag:Pn,exit:e}),PN=e=>({_tag:Pi,journal:e}),PA={ref:0},Pj=()=>{let e=PA.ref+1;return PA.ref=e,e},PM=Symbol.for("effect/STM"),Pz={_R:e=>e,_E:e=>e,_A:e=>e};class PD{get[PM](){return Pz}constructor(e){this._op=e7,this.effect_instruction_i1=void 0,this.effect_instruction_i2=void 0,this.effect_instruction_i0=e,this[h1]=tm,this[tp]=Pz,this[$a]=Pz,this[MW]=Pz}[eB](e){return this===e}[eR](){return eq(this,eA(this))}[Symbol.iterator](){return new hQ(new ew(this))}commit(){return P$(this,K,K)}pipe(){return e6(this,arguments)}}let P$=(e,t,r)=>h8(n=>{let i=n.id(),s=n.getFiberRef(fI),a=n.getFiberRef(ya),o=n.getFiberRef(fF),l=PL(i,e,s,a,o);switch(l._tag){case Pn:return t(l.exit),l.exit;case Pi:{let n=Pj(),l={value:Pb},c=pi(t=>PU(i,e,n,l,s,a,o,t));return pY(e=>W(e(c),ps(e=>{let n=l.value;return(Ph(n)&&(l.value=Pm),Pp(n=l.value))?(t(n.exit),n.exit):(r(),pg(e))})))}}}),PP=(e,t,r,n,i,s)=>{let a=new Map,o=new PJ(t,a,e,n).run(),l=$Y(a);if(l===$V)$G(a);else if(l===$W)throw Error("BUG: STM.TryCommit.tryCommit - please report an issue at https://github.com/Effect-TS/effect/issues");switch(o._tag){case Pt:return r.value=Px(o),Pq(dS(o.value),a,i,s);case $9:return r.value=Px(o),Pq(dd(hn(o.error)),a,i,s);case"Die":return r.value=Px(o),Pq(dd(hi(o.defect)),a,i,s);case Pe:return r.value=Px(o),Pq(dd(hs(e)),a,i,s);case Pr:return PN(a)}},PL=(e,t,r,n,i)=>{let s=new Map,a=new PJ(t,s,e,r).run(),o=$Y(s);if(o===$V&&Pw(a))$G(s);else if(o===$W)throw Error("BUG: STM.TryCommit.tryCommitSync - please report an issue at https://github.com/Effect-TS/effect/issues");switch(a._tag){case Pt:return Pq(dS(a.value),s,n,i);case $9:return Pq(dd(hn(a.error)),s,n,i);case"Die":return Pq(dd(hi(a.defect)),s,n,i);case Pe:return Pq(dd(hs(e)),s,n,i);case Pr:return PN(s)}},PU=(e,t,r,n,i,s,a,o)=>{if(Ph(n.value)){let l=PP(e,t,n,i,s,a);switch(l._tag){case Pn:PB(l.exit,o);break;case Pi:$X(r,l.journal,()=>PU(e,t,r,n,i,s,a,o))}}},Pq=(e,t,r,n)=>{let i=$Z(t);return i.size>0&&r.scheduleTask(()=>$Q(i),n),PR(e)},PB=(e,t)=>{t(e)};class PJ{constructor(e,t,r,n){this.contStack=[],this.self=e,this.journal=t,this.fiberId=r,this.env=n}getEnv(){return this.env}pushStack(e){this.contStack.push(e)}popStack(){return this.contStack.pop()}nextSuccess(){let e=this.popStack();for(;void 0!==e&&e.effect_instruction_i0!==$3;)e=this.popStack();return e}nextFailure(){let e=this.popStack();for(;void 0!==e&&e.effect_instruction_i0!==$1;)e=this.popStack();return e}nextRetry(){let e=this.popStack();for(;void 0!==e&&e.effect_instruction_i0!==$2;)e=this.popStack();return e}run(){let e,t=this.self;for(;void 0===e&&void 0!==t;)try{let r=t;if(r)switch(r._op){case"Tag":t=PV((e,t,n)=>tG(n,r));break;case"Left":t=PY(r.left);break;case"None":t=PY(new f7);break;case"Right":t=P5(r.right);break;case"Some":t=P5(r.value);break;case"Commit":switch(r.effect_instruction_i0){case"Die":e=PE(eO(()=>r.effect_instruction_i1()));break;case $8:{let n=this.nextFailure();void 0===n?e=PC(eO(()=>r.effect_instruction_i1())):t=eO(()=>n.effect_instruction_i2(eO(()=>r.effect_instruction_i1())));break}case $6:{let r=this.nextRetry();void 0===r?e=PT:t=eO(()=>r.effect_instruction_i2());break}case $7:e=PO(this.fiberId);break;case $0:t=eO(()=>r.effect_instruction_i1(this));break;case $3:case $1:case $2:this.pushStack(r),t=r.effect_instruction_i1;break;case"Provide":{let e=this.env;this.env=eO(()=>r.effect_instruction_i2(e)),t=W(r.effect_instruction_i1,PG(P4(()=>this.env=e)));break}case $4:{let n=r.effect_instruction_i1,i=this.nextSuccess();void 0===i?e=PI(n):t=eO(()=>i.effect_instruction_i2(n));break}case $5:{let n=eO(()=>r.effect_instruction_i1()),i=this.nextSuccess();void 0===i?e=PI(n):t=eO(()=>i.effect_instruction_i2(n))}}}}catch(e){t=PK(e)}return e}}let PH=U(2,(e,t)=>{let r=new PD($1);return r.effect_instruction_i1=e,r.effect_instruction_i2=t,r}),PK=e=>PW(()=>e),PW=e=>{let t=new PD("Die");return t.effect_instruction_i1=e,t},PV=e=>P0(t=>P5(e(t.journal,t.fiberId,t.getEnv()))),PG=U(2,(e,t)=>PX(e,{onFailure:e=>P6(t,PY(e)),onSuccess:e=>P6(t,P5(e))})),PY=e=>PZ(()=>e),PZ=e=>{let t=new PD($8);return t.effect_instruction_i1=e,t},PQ=U(2,(e,t)=>{let r=new PD($3);return r.effect_instruction_i1=e,r.effect_instruction_i2=t,r}),PX=U(2,(e,{onFailure:t,onSuccess:r})=>W(e,P2(nk),PH(e=>W(t(e),P2(nw))),PQ(e=>{switch(e._tag){case"Left":return P5(e.left);case"Right":return r(e.right)}}))),P0=e=>{let t=new PD($0);return t.effect_instruction_i1=e,t},P1=e=>{let t=new PD($7);return t.effect_instruction_i1=e,t},P2=U(2,(e,t)=>W(e,PQ(e=>P4(()=>t(e))))),P3=new PD($6),P5=e=>{let t=new PD($4);return t.effect_instruction_i1=e,t},P4=e=>{let t=new PD($5);return t.effect_instruction_i1=e,t},P6=U(2,(e,t)=>W(e,PQ(()=>t))),P8=U(3,(e,t,r)=>W(e,PQ(e=>W(t,P2(t=>r(e,t)))))),P7=((e,t)=>W(e,P2(()=>t)),e=>PQ(e,q)),P9=U(e=>em(e[0]),(e,t,r)=>r?.discard?W(P4(()=>e[Symbol.iterator]()),PQ(e=>{let r=Le(()=>{let n=e.next();return n.done?Lt:W(t(n.value),PQ(()=>r))});return r})):Le(()=>i6(e).reduce((e,r)=>P8(e,t(r),(e,t)=>(e.push(t),e)),P5([])))),Le=e=>P7(P4(e)),Lt=P5(void 0),Lr=Symbol.for("effect/TRef"),Ln={_A:e=>e};class Li{constructor(e){this[Lr]=Ln,this.versioned=new $L(e),this.todos=new Map}modify(e){return PV(t=>{let r=Ls(this,t),[n,i]=e($q(r));return $B(r,i),n})}pipe(){return e6(this,arguments)}}let Ls=(e,t)=>{if(t.has(e))return t.get(e);let r=$U(e,!1);return t.set(e,r),r},La=U(2,(e,t)=>$q(Ls(e,t))),Lo=U(3,(e,t,r)=>{$B(Ls(e,r),t)}),Ll=Symbol.for("effect/TQueue/TEnqueue"),Lc=Symbol.for("effect/TQueue/TDequeue"),Lu={_Out:e=>e},Lh={_In:e=>e},Lp=((e,t,r)=>Le(()=>{let n=(t,r,i)=>r<t?P5(i):W(e.takeUpTo(r),PQ(s=>{let a=t-s.length;return 1===a?W(e.take,P2(e=>W(i,aI(av(s)),aw(e)))):a>1?W(e.take,PQ(e=>n(a-1,r-s.length-1,W(i,aI(av(s)),aw(e))))):P5(W(i,aI(av(s))))}));return P2(n(t,r,ap()),e=>Array.from(e))}),Symbol.for("effect/TPubSub")),Lf=Symbol.for("effect/TPubSub/AbsentValue"),Ld=(e,t,r)=>({head:e,subscribers:t,tail:r});class Lm{static{f=Ll}constructor(e,t,r,n,i,s,a){this[Lp]={_A:e=>e},this[f]=Lh,this.isShutdown=PV(e=>void 0===La(this.publisherTail,e)),this.awaitShutdown=PQ(this.isShutdown,e=>e?Lt:P3),this.size=P0(e=>void 0===La(this.publisherTail,e.journal)?P1(e.fiberId):P5(La(this.pubsubSize,e.journal))),this.isEmpty=P2(this.size,e=>0===e),this.isFull=P2(this.size,e=>e===this.capacity()),this.shutdown=PV(e=>{void 0!==La(this.publisherTail,e)&&(Lo(this.publisherTail,void 0,e),lA(La(this.subscribers,e),t=>{Lo(t,void 0,e)}),Lo(this.subscribers,l_(),e))}),this.pubsubSize=e,this.publisherHead=t,this.publisherTail=r,this.requestedCapacity=n,this.strategy=i,this.subscriberCount=s,this.subscribers=a}capacity(){return this.requestedCapacity}offer(e){return P0(t=>{let r=La(this.publisherTail,t.journal);if(void 0===r)return P1(t.fiberId);let n=La(this.subscriberCount,t.journal);if(0===n)return P5(!0);let i=La(this.pubsubSize,t.journal);if(i<this.requestedCapacity){let s=new Li(void 0);return Lo(r,Ld(e,n,s),t.journal),Lo(this.publisherTail,s,t.journal),Lo(this.pubsubSize,i+1,t.journal),P5(!0)}switch(this.strategy._tag){case"BackPressure":return P3;case"Dropping":return P5(!1);case"Sliding":{if(this.requestedCapacity>0){let e=La(this.publisherHead,t.journal),r=!0;for(;r;){let n=La(e,t.journal);if(void 0===n)return P3;let i=n.head,s=n.tail;i!==Lf?(Lo(e,Ld(Lf,n.subscribers,n.tail),t.journal),Lo(this.publisherHead,s,t.journal),r=!1):e=s}}let i=new Li(void 0);return Lo(r,Ld(e,n,i),t.journal),Lo(this.publisherTail,i,t.journal),P5(!0)}}})}offerAll(e){return P2(P9(e,e=>this.offer(e)),sL(q))}}class Lg{static{d=Lc}constructor(e,t,r,n,i,s){this[Lp]=Lp,this[d]=Lu,this.isShutdown=PV(e=>void 0===La(this.subscriberHead,e)),this.awaitShutdown=PQ(this.isShutdown,e=>e?Lt:P3),this.size=P0(e=>{let t=La(this.subscriberHead,e.journal);if(void 0===t)return P1(e.fiberId);let r=!0,n=0;for(;r;){let i=La(t,e.journal);if(void 0===i)r=!1;else{let e=i.head;e!==Lf&&(n+=1)>=Number.MAX_SAFE_INTEGER&&(r=!1),t=i.tail}}return P5(n)}),this.isEmpty=P2(this.size,e=>0===e),this.isFull=P2(this.size,e=>e===this.capacity()),this.peek=P0(e=>{let t=La(this.subscriberHead,e.journal);if(void 0===t)return P1(e.fiberId);let r=Lf,n=!0;for(;n;){let i=La(t,e.journal);if(void 0===i)return P3;let s=i.head,a=i.tail;s!==Lf?(r=s,n=!1):t=a}return P5(r)}),this.peekOption=P0(e=>{let t=La(this.subscriberHead,e.journal);if(void 0===t)return P1(e.fiberId);let r=iC(),n=!0;for(;n;){let i=La(t,e.journal);if(void 0===i)r=iC(),n=!1;else{let e=i.head,s=i.tail;e!==Lf?(r=tR(e),n=!1):t=s}}return P5(r)}),this.shutdown=PV(e=>{let t=La(this.subscriberHead,e);if(void 0!==t){Lo(this.subscriberHead,void 0,e);let r=!0;for(;r;){let n=La(t,e);if(void 0===n)r=!1;else{let r=n.head,i=n.tail;if(r!==Lf){let s=n.subscribers;if(1===s){let r=La(this.pubsubSize,e);Lo(t,Ld(Lf,0,i),e),Lo(this.publisherHead,i,e),Lo(this.pubsubSize,r-1,e)}else Lo(t,Ld(r,s-1,i),e)}t=i}}let n=La(this.subscriberCount,e);Lo(this.subscriberCount,n-1,e),Lo(this.subscribers,lT(La(this.subscribers,e),this.subscriberHead),e)}}),this.take=P0(e=>{let t=La(this.subscriberHead,e.journal);if(void 0===t)return P1(e.fiberId);let r=Lf,n=!0;for(;n;){let i=La(t,e.journal);if(void 0===i)return P3;let s=i.head,a=i.tail;if(s!==Lf){let o=i.subscribers;if(1===o){let r=La(this.pubsubSize,e.journal);Lo(t,Ld(Lf,0,a),e.journal),Lo(this.publisherHead,a,e.journal),Lo(this.pubsubSize,r-1,e.journal)}else Lo(t,Ld(s,o-1,a),e.journal);Lo(this.subscriberHead,a,e.journal),r=s,n=!1}else t=a}return P5(r)}),this.takeAll=this.takeUpTo(Number.POSITIVE_INFINITY),this.pubsubSize=e,this.publisherHead=t,this.requestedCapacity=r,this.subscriberHead=n,this.subscriberCount=i,this.subscribers=s}capacity(){return this.requestedCapacity}takeUpTo(e){return P0(t=>{let r=La(this.subscriberHead,t.journal);if(void 0===r)return P1(t.fiberId);let n=[],i=0;for(;i!==e;){let s=La(r,t.journal);if(void 0===s)i=e;else{let e=s.head,a=s.tail;if(e!==Lf){let o=s.subscribers;if(1===o){let e=La(this.pubsubSize,t.journal);Lo(r,Ld(Lf,0,a),t.journal),Lo(this.publisherHead,a,t.journal),Lo(this.pubsubSize,e-1,t.journal)}else Lo(r,Ld(e,o-1,a),t.journal);n.push(e),i+=1}r=a}}return Lo(this.subscriberHead,r,t.journal),P5(n)})}}class Lb{constructor(e){this.size=0,this.current=0,this.capacity=e,this.array=Array.from({length:e},K)}head(){return iT(this.array[this.current])}lastOrNull(){if(0===this.size)return;let e=0===this.current?this.array.length-1:this.current-1;return this.array[e]??void 0}put(e){this.array[this.current]=e,this.increment()}dropLast(){this.size>0&&(this.decrement(),this.array[this.current]=void 0)}toChunk(){let e=this.current-this.size;return am(e<0?[...this.array.slice(this.capacity+e,this.capacity),...this.array.slice(0,this.current)]:this.array.slice(e,this.current))}increment(){this.size<this.capacity&&(this.size+=1),this.current=(this.current+1)%this.capacity}decrement(){this.size-=1,this.current>0?this.current-=1:this.current=this.capacity-1}}let Lx=Symbol.for("effect/Stream/Handoff"),Ly="Empty",Lv="Full",LS=e=>({_tag:Ly,notifyConsumer:e}),L_=(e,t)=>({_tag:Lv,value:e,notifyProducer:t}),Lw=(e,t)=>r=>{switch(r._tag){case Ly:return e(r.notifyConsumer);case Lv:return t(r.value,r.notifyProducer)}},Lk={_A:e=>e},LC=()=>W(dI(),pv(e=>gZ(LS(e))),pj(e=>({[Lx]:Lk,ref:e}))),LE=U(2,(e,t)=>pv(dI(),r=>pw(g1(e.ref,n=>W(n,Lw(e=>[wi(dM(e,void 0),dT(r)),L_(t,r)],(r,i)=>[pv(dT(i),()=>W(e,LE(t))),n])))))),LO=e=>pv(dI(),t=>pw(g1(e.ref,r=>W(r,Lw(t=>[pv(dT(t),()=>LO(e)),r],(e,r)=>[h9(dM(r,void 0),e),LS(t)]))))),LI="Emit",LF="Halt",LT=e=>({_tag:LI,elements:e}),LR=e=>({_tag:LF,cause:e}),LN=e=>({_tag:"End",reason:e}),LA=()=>pd(iC()),Lj=e=>pz(pg(e),tR),LM="ScheduleEnd",Lz="UpstreamEnd",LD={_tag:LM},L$={_tag:Lz},LP="DrainLeft",LL="DrainRight",LU="PullBoth",Lq="PullLeft",LB="PullRight",LJ={_tag:LP},LH={_tag:LL},LK={_tag:LU},LW=e=>({_tag:Lq,rightChunk:e}),LV=e=>({_tag:LB,leftChunk:e}),LG="PullBoth",LY="PullLet",LZ="PullRight",LQ={_tag:LG},LX=e=>({_tag:LY,rightChunk:e}),L0=e=>({_tag:LZ,leftChunk:e}),L1=Symbol.for("effect/Take"),L2={_A:e=>e,_E:e=>e};class L3{constructor(e){this[L1]=L2,this.exit=e}pipe(){return e6(this,arguments)}}let L5=e=>new L3(dS(e)),L4=e=>pJ(()=>e.exit),L6=new L3(df(iC())),L8=e=>new L3(dd(W(e,hk(tR)))),L7=U(2,(e,{onEnd:t,onFailure:r,onSuccess:n})=>dy(e.exit,{onFailure:e=>iE(gw(e),{onNone:t,onSome:r}),onSuccess:n})),L9=e=>new L3(dS(ad(e))),Ue=Symbol.for("effect/Stream"),Ut={_R:e=>e,_E:e=>e,_A:e=>e};class Ur{constructor(e){this[Ue]=Ut,this.channel=e}pipe(){return e6(this,arguments)}}let Un=e=>ec(e,Ue)||h6(e),Ui=4096,Us=((e,t,r)=>UO(Us(e,t,r),e=>nF(e,{onLeft:iC,onRight:tR})),U(3,(e,t,r)=>U$(_G([LC(),gZ(LD),gZ(ap()),CM(r),gZ(!1),gZ(!1)])).pipe(UI(([r,n,i,s,a,o])=>{let l=zn({onInput:e=>M8(M9(W(r,LE(LT(e)),bJ(()=>aM(e)))),()=>l),onFailure:e=>M9(LE(r,LR(e))),onDone:()=>M9(LE(r,LN(L$)))}),c=W(g0(i,ap()),pv(e=>aM(e)?W(gX(a,!0),wi(pB(W(zc(e),M8(()=>c))))):W(LO(r),pj(e=>{switch(e._tag){case LI:return W(M9(gX(a,!0)),D1(zc(e.elements)),D1(M9(gQ(o))),M8(e=>e?zl:c));case LF:return M4(e.cause);case"End":if(e.reason._tag===LM)return W(gQ(a),pj(e=>e?M9(W(gX(n,LD),wi(gX(o,!0)))):W(M9(W(gX(n,LD),wi(gX(o,!0)))),M8(()=>c))),DW);return W(gX(n,e.reason),wi(gX(o,!0)),M9)}}))),DW),u=e=>s.next(e),h=(e,s,l)=>{let p=W(gX(a,!1),wi(gX(o,!1)),wi(W(c,Dj($z(t)),MQ,Dz,wT(l)))),f=(e,t,r)=>W(gX(i,aA(e)),wi(pj(gQ(n),e=>{switch(e._tag){case LM:return W(_G([gQ(a),p,W(u(tR(t)),wT(l))]),pj(([e,n,i])=>{let s=W(r,iE({onNone:()=>ad(nk(t)),onSome:e=>af(nk(t),nw(e))}));return e?W(zc(s),M8(()=>h(n,i,l))):h(n,i,l)}),DW);case Lz:return W(gQ(a),pj(e=>e?zc(ad(nk(t))):zl),DW)}})),DW);return DW(wx(y1(e),y1(s),{onSelfDone:(e,t)=>W(p9(s),wi(W(pJ(()=>e),pj(([e,t])=>f(e,t,iC()))))),onOtherDone:(t,n)=>pC(pJ(()=>t),{onFailure:t=>nF(hx(t),{onLeft:()=>W(r,LE(LN(LD)),_0,wi(W(y1(e),pj(([e,t])=>f(e,t,iC()))))),onRight:t=>W(r,LE(LR(t)),_0,wi(W(y1(e),pj(([e,t])=>f(e,t,iC())))))}),onSuccess:t=>W(r,LE(LN(LD)),_0,wi(W(y1(e),pj(([e,r])=>f(e,r,tR(t))))))})}))};return qI(r=>ze(Uz(e),l).pipe(Dz,wT(r),wi(Dj(c,$z(t)).pipe(MQ,Dz,wT(r),pv(e=>u(iC()).pipe(wT(r),pj(t=>new Ur(h(e,t,r)))))))))})))),Ua=((e,t)=>pj(qv(e,t),e=>Uj(UL(e))),(e,t)=>{let r=(e,t)=>{let n=r=>W(gQ(t),pK(dT),wi(dI()),pv(n=>W(jr(e,[r,n]),wi(gX(t,n)),wi(dT(n)))),pe,M9);return zn({onInput:n=>W(dI(),pv(r=>W(jr(e,[L5(n),r]),pv(e=>W(gX(t,r),bJ(()=>e))))),pe,M9,M8(()=>r(e,t))),onFailure:e=>n(L8(e)),onDone:()=>n(L6)})},n=e=>{let t=W(M9(ji(e)),M8(([e,r])=>D1(M9(dM(r,void 0)),L7(e,{onEnd:()=>zl,onFailure:M4,onSuccess:e=>W(zc(e),M8(()=>t))}))));return t};return DV(W(e,pv(e=>W(dI(),pK(e=>dM(e,void 0)),pv(i=>W(gZ(i),pv(n=>W(t,ze(r(e,n)),D$,wR)),h9(n(e))))))))}),Uo=U(2,(e,t)=>Ul(e,e=>nF(hx(e),{onLeft:t,onRight:UE}))),Ul=U(2,(e,t)=>new Ur(W(Uz(e),MZ(e=>Uz(t(e)))))),Uc=U(2,(e,t)=>W(e,Ul(e=>W(t(e),iO(()=>UE(e)))))),Uu=e=>pa(bl(e),e=>"None"===e._tag?bz:pd(e.value)),Uh=U(4,(e,t,r,n)=>{let i=(e,t)=>D1(M9(LO(t)),zn({onInput:r=>M8(M9(W(e,LE(L5(r)))),()=>i(e,t)),onFailure:t=>M9(LE(e,L8(t))),onDone:()=>M9(LE(e,L6))}));return new Ur(DG(s=>_G([LC(),LC(),LC(),LC()]).pipe(pK(([t,r,n])=>ze(Uz(e),i(t,n)).pipe(zD(s),wT(s))),pK(([e,r,n,a])=>ze(Uz(t),i(r,a)).pipe(zD(s),wT(s))),pj(([e,t,i,s])=>{let a=LE(i,void 0).pipe(wi(LO(e).pipe(pv(L4)))),o=LE(s,void 0).pipe(wi(LO(t).pipe(pv(L4))));return Uz(qC(r,e=>pv(n(e,a,o),Uu)))}))))}),Up=U(2,(e,t)=>new Ur(W(Uz(e),D1(Uz(t))))),Uf=((e,t)=>UI(e,()=>t),e=>U$(pu(e))),Ud=U(2,(e,t)=>W(dI(),pv(r=>W(e,Ub({maximumLag:t.maximumLag,decide:e=>pv(dT(r),t=>t(e))}),pv(e=>W(_G(aP(aW(0,t.size-1),t=>pj(e,([e,r])=>[[e,t],r]))),pj(av),pv(e=>{let[n,i]=sP(e,[new Map,ap()],([e,t],[r,n])=>[e.set(r[0],r[1]),W(t,ak(n))]);return W(dM(r,e=>pj(t.decide(e),e=>t=>W(e(n.get(t))))),h9(Array.from(i)))}))))))),Um={ref:0},Ug=()=>{let e=Um.ref;return Um.ref=e+1,e},Ub=U(2,(e,t)=>Ux(e,t.maximumLag,t.decide,()=>pZ)),Ux=U(4,(e,t,r,n)=>W(_J(gZ(new Map),(e,t)=>W(gQ(e),pv(e=>W(e.values(),_Y(jT))))),pv(i=>EH(function*(){let s=yield*EZ(1),a=yield*gZ(W(jv(t),pv(e=>{let t=Ug();return W(g2(i,r=>r.set(t,e)),h9([t,e]))}))),o=e=>s.withPermits(1)(W(gX(a,W(jv(1),pK(t=>jr(t,e)),pv(e=>{let t=Ug();return W(g2(i,r=>r.set(t,e)),h9(i2(t,e)))}))),wi(W(gQ(i),pv(t=>W(am(t.values()),_Y(t=>W(jr(t,e),bu(e=>hp(e)?tR(pZ):iC()))))))),wi(n(e)),pe));return yield*W(e,qo(e=>W(r(e),pv(t=>W(gQ(i),pv(r=>W(r.entries(),bM(ap(),(r,[n,i])=>t(n)?W(jr(i,dS(e)),pC({onFailure:e=>hp(e)?pB(W(r,ak(n))):pg(e),onSuccess:()=>pB(r)})):pB(r)),pv(e=>aM(e)?W(g2(i,t=>{for(let r of e)t.delete(r);return t})):pZ))))),pe)),pC({onFailure:e=>o(dd(W(e,hk(tR)))),onSuccess:()=>o(df(iC()))}),wR),s.withPermits(1)(pw(gQ(a)))})))),Uy=e=>new Ur(Du(Uz(e))),Uv=((e,t)=>{let r=e=>zr({onInput:t=>{let n=W(t,aE(e)),i=Math.max(0,e-t.length);return aj(t)||i>0?r(i):W(zc(n),D1(Dy()))},onFailure:M5,onDone:()=>zl});return new Ur(W(Uz(e),Dj(r(t))))},new Ur(zl)),US=U(2,(e,t)=>new Ur(W(Uz(e),Dh(t)))),U_=U(2,(e,t)=>new Ur(M3(Uz(e),t))),Uw=()=>U$(dP()),Uk=e=>W(Uw(),UI(e)),UC=e=>UP(pd(tR(e))),UE=e=>U$(pg(e)),UO=U(2,(e,t)=>UV(e,aF(t))),UI=U(e=>Un(e[0]),(e,t,r)=>{let n=r?.bufferSize??16;return r?.switch?UF(r?.concurrency,()=>UT(e,1,n,t),r=>UT(e,r,n,t)):UF(r?.concurrency,()=>new Ur(Do(Uz(e),e=>W(e,aP(e=>Uz(t(e))),s$(zl,(e,t)=>W(e,D1(t)))))),n=>new Ur(W(Uz(e),Do(DZ),DR(e=>Uz(t(e)),r))))}),UF=(e,t,r)=>{switch(e){case void 0:return t();case"unbounded":return r(Number.MAX_SAFE_INTEGER);default:return e>1?r(e):t()}},UT=U(4,(e,t,r,n)=>new Ur(W(Uz(e),Do(DZ),DR(e=>Uz(n(e)),{concurrency:t,mergeStrategy:z2(),bufferSize:r})))),UR=U(e=>Un(e[0]),(e,t)=>UI(e,q,t)),UN=e=>{let t=zn({onInput:e=>M8(DZ(e),()=>t),onFailure:M4,onDone:()=>zl});return new Ur(W(Uz(e),ze(t)))},UA=e=>{let t=(e,t)=>{let[r,n]=W(e,aU(e=>!dl(e))),i=W(az(n),iE({onNone:()=>t,onSome:dy({onFailure:e=>iE(gw(e),{onNone:()=>zl,onSome:M4}),onSuccess:()=>zl})}));return W(zc(W(r,aF(e=>dl(e)?tR(e.value):iC()))),M8(()=>i))},r=zn({onInput:e=>t(e,r),onFailure:e=>M4(e),onDone:()=>zl});return new Ur(W(Uz(e),ze(r)))},Uj=e=>UN(UA(W(e,UK(e=>e.exit)))),UM=e=>new Ur(e),Uz=e=>{if("channel"in e)return e.channel;if(h6(e))return Uz(U$(e));throw TypeError("Expected a Stream.")},UD=e=>new Ur(aj(e)?zl:zc(e)),U$=e=>W(e,pz(tR),UP),UP=e=>new Ur(DW(by(e,{onFailure:iE({onNone:()=>zl,onSome:M5}),onSuccess:e=>zc(ad(e))}))),UL=(e,t)=>{let r=t?.maxChunkSize??Ui;if(t?.scoped){let n=pj(Mv(e),e=>UJ(e,{maxChunkSize:r,shutdown:!0}));return t.shutdown?pj(n,US(My(e))):n}let n=UI(qp(Mv(e)),e=>UJ(e,{maxChunkSize:r}));return t?.shutdown?US(n,My(e)):n},UU=e=>qd(()=>au(e)?UD(e):Uq(e[Symbol.iterator]())),Uq=(e,t=Ui)=>W(pH(()=>{let r=[],n=e=>W(pH(()=>{let i=e.next();if(1===t)return i.done?zl:W(zc(ad(i.value)),M8(()=>n(e)));r=[];let s=0;for(;!1===i.done&&(r.push(i.value),!((s+=1)>=t));)i=e.next();return s>0?W(zc(av(r)),M8(()=>n(e))):zl}),DW);return new Ur(n(e))}),qE),UB=e=>W(e,pj(qt),qO),UJ=(e,t)=>W(ja(e,1,t?.maxChunkSize??Ui),ps(t=>W(jI(e),pv(e=>e&&hp(t)?LA():Lj(t)))),qt,t?.shutdown?US(jT(e)):q),UH=((e,t)=>{let r=e=>W(kl(e),pj(iE({onNone:()=>zr({onInput:t=>M8(zc(t),()=>r(e)),onFailure:M5,onDone:()=>zl}),onSome:dy({onFailure:M4,onSuccess:()=>zl})})),DW);return new Ur(DG(n=>t.pipe(wT(n),pj(t=>Uz(e).pipe(ze(r(t)))))))},U(2,(e,t)=>new Ur(W(Uz(e),Dv(t))))),UK=((e,t)=>new Ur(W(Uz(e),DS(t))),U(2,(e,t)=>new Ur(W(Uz(e),DE(aP(t)))))),UW=U(3,(e,t,r)=>qd(()=>{let n=e=>zr({onInput:t=>W(pJ(()=>{let i=[],s=e=>pH(()=>{i.push(e)});return W(t,bM(e,(e,t)=>W(r(e,t),pv(([e,t])=>W(s(t),h9(e))))),by({onFailure:e=>0!==i.length?D1(zc(av(i)),M5(e)):M5(e),onSuccess:e=>M8(zc(av(i)),()=>n(e))}))}),DW),onFailure:M5,onDone:()=>zl});return new Ur(W(Uz(e),Dj(n(t))))})),UV=U(2,(e,t)=>new Ur(W(Uz(e),DE(t)))),UG=((e,t)=>W(e,UV(aR(t))),U(2,(e,t)=>{let r=e=>{let n=e.next();return n.done?zn({onInput:e=>r(e[Symbol.iterator]()),onFailure:M4,onDone:zi}):DW(pj(t(n.value),t=>M8(zc(ad(t)),()=>r(e))))};return new Ur(W(Uz(e),ze(za(()=>r(ap()[Symbol.iterator]())))))})),UY=U(3,(e,t,r)=>new Ur(W(Uz(e),Do(DZ),DI(r,t),DE(ad)))),UZ=U(2,(e,t)=>new Ur(W(Uz(e),Dk(t)))),UQ=((e=>Un(e[1]),(e,t,r)=>U0(e,t,{onSelf:q,onOther:q,haltStrategy:r?.haltStrategy})),U(e=>Symbol.iterator in e[0],(e,t)=>UR(UU(e),t))),UX=U(2,(e,t)=>U0(e,t,{onSelf:nw,onOther:nk})),U0=U(3,(e,t,r)=>{let n=r.haltStrategy?$s(r.haltStrategy):$n,i=e=>t=>e||!dl(t)?zU(pJ(()=>t)):zq(e=>pJ(()=>e));return new Ur(DN(Uz(UK(e,r.onSelf)),{other:Uz(UK(t,r.onOther)),onSelfDone:i("Either"===n._tag||"Left"===n._tag),onOtherDone:i("Either"===n._tag||"Right"===n._tag)}))}),U1=((e,t)=>new Ur(W(Uz(e),DA(()=>Uz(t())))),U(2,(e,t)=>{let r=zr({onInput:e=>aj(e)?za(()=>r):W(zc(e),D1(Dy())),onFailure:M5,onDone:()=>za(()=>Uz(t()))});return new Ur(W(Uz(e),ze(r)))})),U2=((e=>"function"==typeof e[1],(e,t,r)=>W(UG(e,t),Ud({size:2,maximumLag:r?.bufferSize??16,decide:nF({onLeft:()=>pB(e=>0===e),onRight:()=>pB(e=>1===e)})}),pv(([e,t])=>pB([UO(UA(UJ(e,{shutdown:!0})),e=>nF(e,{onLeft:tR,onRight:iC})),UO(UA(UJ(t,{shutdown:!0})),e=>nF(e,{onLeft:iC,onRight:tR}))])))),U(2,(e,t)=>new Ur(ze(Uz(e),t)))),U3=U(2,(e,t)=>new Ur(W(Uz(e),zt(t)))),U5=((e,t)=>U8(e,tZ(t)),U(2,(e,t)=>new Ur(DG(r=>kB(t,r).pipe(pj(t=>W(Uz(e),zt(t)))))))),U4=U(3,(e,t,r)=>U6(e,t,U$(r))),U6=U(3,(e,t,r)=>Uk(n=>UI(r,r=>W(e,U3(tH(n,t,r)))))),U8=U(2,(e,t)=>Uk(r=>W(e,U3(t(r))))),U7=((e,t)=>W(e,U5(W(kW(),k5(t)))),U(2,(e,t)=>qd(()=>{let r=Math.max(t,1),n=U9(new qe(r),r);return new Ur(W(Uz(e),ze(n)))}))),U9=(e,t)=>zn({onInput:r=>{if(r.length===t&&e.isEmpty())return M8(zc(r),()=>U9(e,t));if(r.length>0){let n,i=[],s=0;for(;s<r.length;){for(;s<r.length&&void 0===n;)n=e.write(W(r,a_(s))),s+=1;void 0!==n&&(i.push(n),n=void 0)}return M8(DY(...i),()=>U9(e,t))}return za(()=>U9(e,t))},onFailure:t=>D1(e.emitIfNotEmpty(),M4(t)),onDone:()=>e.emitIfNotEmpty()});class qe{constructor(e){this.builder=[],this.pos=0,this.n=e}isEmpty(){return 0===this.pos}write(e){if(this.builder.push(e),this.pos+=1,this.pos===this.n){let e=av(this.builder);return this.builder=[],this.pos=0,e}}emitIfNotEmpty(){return 0!==this.pos?zc(av(this.builder)):zl}}(e,t,r)=>new Ur(Da(Uz(e),e=>iE(t(e),{onNone:()=>M4(hi(r(e))),onSome:M5})));let qt=e=>qC(e,e=>W(pj(e,t=>tR([t,e])),pa(iE({onNone:()=>pB(iC()),onSome:pd})))),qr=e=>qt(W(e,pj(ad))),qn=((e,t)=>qn(e,t,{onElement:e=>nk(e),onSchedule:nw}),U(3,(e,t,r)=>W(CM(t),pj(t=>{let n=W(e,U4(Cw,gQ(t.iterationMeta)),UK(r.onElement),Uz),i=DW(by(t.next(void 0),{onFailure:()=>zl,onSuccess:e=>M8(n,()=>D1(zc(ad(r.onSchedule(e))),i))}));return new Ur(D1(n,i))}),qE))),qi=((e,t)=>CM(t).pipe(pj(t=>{let r=Uz(U4(Cw,gQ(t.iterationMeta))(e)).pipe(DO(e=>h9(t.reset,e)),Da(e=>t.next(e).pipe(by({onFailure:()=>M5(e),onSuccess:()=>r}),DW)));return r}),DW,UM),U(2,(e,t)=>Uz(e).pipe(Dj($z(t)),DD))),qs=((e,t,r,n)=>qi(e,$g(t,r,n)),U(2,(e,t)=>qi(e,$O(t)))),qa=U(2,(e,t)=>qi(e,$I(t))),qo=U(2,(e,t)=>W(e,qh($O(t)))),ql=U(2,(e,t)=>W(e,qu(t))),qc=((e,t)=>W(e,qu(t),EG),U(2,(e,t)=>{let r=zn({onInput:e=>M8(M9(jn(t,aP(e,dS))),()=>r),onFailure:e=>M9(jr(t,dd(hk(e,tR)))),onDone:()=>M9(jr(t,df(iC())))});return W(ze(Uz(e),r),Du,D$,pe)})),qu=U(2,(e,t)=>{let r=zn({onInput:e=>M8(zc(L5(e)),()=>r),onFailure:e=>zc(L8(e)),onDone:()=>zc(L6)});return W(ze(Uz(e),r),DO(e=>jr(t,e)),Du,D$,pe)}),qh=U(2,(e,t)=>W(Uz(e),Dj($z(t)),Du,D$)),qp=((e,t)=>W(e,UW(iC(),(e,r)=>{switch(e._tag){case"None":return pB([tR(r),r]);case"Some":return W(t(e.value,r),pj(e=>[tR(e),e]))}})),e=>new Ur(Dh(DP(W(e,pj(ad))),pZ))),qf=e=>new Ur(DL(t=>e(t).pipe(pj(ad)))),qd=((e,t,r)=>t<=0||r<=0?Uf(new f6("Invalid bounds - `chunkSize` and `stepSize` must be greater than zero")):new Ur(za(()=>{let n=new Lb(t),i=(e,i)=>{if(e<t){let e=n.toChunk();return W(zc(aj(e)?ap():ad(e)),M8(()=>i))}let s=e-(e-t)%r;if(s===e)return i;let a=W(n.toChunk(),aB(e-(s-t+r)));return W(zc(aj(a)?ap():ad(a)),M8(()=>i))},s=e=>zn({onInput:i=>M8(zc(aF(i,(i,s)=>{n.put(i);let a=e+s+1;return a<t||(a-t)%r>0?iC():tR(n.toChunk())})),()=>s(e+i.length)),onFailure:t=>i(e,M4(t)),onDone:()=>i(e,zl)});return W(Uz(e),ze(s(0)))})),e=>new Ur(za(()=>Uz(e())))),qm=U(2,(e,t)=>{let r=zr({onInput:e=>{let n=W(e,aJ(t));return n.length===e.length?W(zc(n),M8(()=>r)):zc(n)},onFailure:M5,onDone:zi});return new Ur(W(Uz(e),Dj(r)))}),qg=((e,t)=>UG(e,e=>h9(t(e),e)),(e,t,r,n,i)=>{let s=(e,a)=>zn({onInput:o=>W(t(o),wr(gt),pj(([t,l])=>{let c=e+(l-a)/oO(n)*r,u=r+i<0?Number.POSITIVE_INFINITY:r+i,h=c<0?u:Math.min(c,u);return t<=h?W(zc(o),M8(()=>s(h-t,l))):s(e,a)}),DW),onFailure:M4,onDone:()=>zl}),a=W(gt,pj(e=>s(r,e)),DW);return new Ur(W(Uz(e),Dj(a)))}),qb=(e,t,r,n,i)=>{let s=(e,a)=>zn({onInput:o=>W(t(o),wr(gt),pj(([t,l])=>{let c=e+(l-a)/oO(n)*r,u=r+i<0?Number.POSITIVE_INFINITY:r+i,h=(c<0?u:Math.min(c,u))-t,p=oS(Math.max(0,(h>=0?0:-h/r)*oO(n)));return oD(p,ob)?W(M9(gO(p)),D1(zc(o)),M8(()=>s(h,l))):M8(zc(o),()=>s(h,l))}),DW),onFailure:M4,onDone:()=>zl}),a=W(gt,pj(e=>s(r,e)),DW);return new Ur(W(Uz(e),Dj(a)))},qx=U(3,(e,t,r)=>W(qS(e),pj(wj({onTimeout:()=>hk(t(),tR),duration:r})),UB)),qy=((e,t,r)=>{let n=new f1("Stream Timeout");return W(e,qx(()=>hi(n),t),Uc(e=>hu(e)&&gE(e.defect)&&void 0!==e.defect.message&&"Stream Timeout"===e.defect.message?tR(r):iC()))},e=>{if("number"==typeof e)return Mm(e);if("unbounded"===e.capacity)return Mx({replay:e.replay});switch(e.strategy){case"dropping":return Mg(e);case"sliding":return Mb(e);default:return Mm(e)}}),qv=U(2,(e,t)=>W(_J(qy(t),e=>My(e)),pK(t=>W(e,ql(t),wR)))),qS=e=>pj(Dq(Uz(e)),e=>W(e,pz(tR),pv(nF({onLeft:()=>pd(iC()),onRight:pB})))),q_=U(e=>Un(e[0]),(e,t)=>pK(_J(t?.strategy==="unbounded"?jw():t?.strategy==="dropping"?jS(t.capacity??2):t?.strategy==="sliding"?j_(t.capacity??2):jv(t?.capacity??2),e=>jT(e)),t=>wR(qu(e,t)))),qw=((e=>Un(e[0]),(e,t)=>pK(_J(jv(t?.capacity??2),e=>jT(e)),t=>wR(qc(e,t)))),U(e=>Un(e[0]),(e,t)=>pj(kC(),r=>qk(e,r,t)))),qk=U(e=>Un(e[0]),(e,t,r)=>{let n,i,s=ku(t),a=EQ(!1);return new ReadableStream({start(t){(i=s(qa(e,e=>0===e.length?pZ:a.whenOpen(pH(()=>{for(let r of(a.unsafeClose(),e))t.enqueue(r);n(),n=void 0}))))).addObserver(e=>{try{"Failure"===e._tag?t.error(gk(e.cause)):t.close()}catch{}})},pull:()=>new Promise(e=>{n=e,kR(a.open)}),cancel(){if(i)return kF(pe(p9(i)))}},r?.strategy)}),qC=(e,t)=>qd(()=>{let r=e=>DW(pj(t(e),iE({onNone:()=>zl,onSome:([e,t])=>M8(zc(e),()=>r(t))})));return new Ur(r(e))}),qE=e=>UR(U$(e)),qO=e=>UR(qp(e)),qI=e=>UR(qf(t=>e(t))),qF=((e,t)=>W(U$(t),UI(t=>t?e:Uv)),U(3,(e,t,r)=>{let n=(e,t,r)=>{switch(e._tag){case LG:return W(Uu(t),wr(Uu(r),{concurrent:!0}),pE({onFailure:e=>pB(df(tR(e))),onSuccess:([e,s])=>tF(e)&&tF(s)?aj(e.value)&&aj(s.value)?n(LQ,t,r):aj(e.value)?n(LX(s.value),t,r):aj(s.value)?n(L0(e.value),t,r):pB(dS(i(e.value,s.value))):pB(df(iC()))}));case LY:return pE(t,{onFailure:e=>pB(df(e)),onSuccess:s=>aj(s)?n(LX(e.rightChunk),t,r):aj(e.rightChunk)?n(L0(s),t,r):pB(dS(i(s,e.rightChunk)))});case LZ:return pE(r,{onFailure:e=>pB(df(e)),onSuccess:s=>aj(s)?n(L0(e.leftChunk),t,r):aj(e.leftChunk)?n(LX(s),t,r):pB(dS(i(e.leftChunk,s)))})}},i=(e,t)=>{let[n,i]=r(e,t);switch(i._tag){case"Left":if(aj(i.left))return[n,LQ];return[n,L0(i.left)];case"Right":if(aj(i.right))return[n,LQ];return[n,LX(i.right)]}};return W(e,Uh(t,LQ,n))})),qT=(e,t,r)=>e.length>t.length?[W(e,aC(t.length),aH(t,r)),nw(W(e,aE(t.length)))]:[W(e,aH(W(t,aC(e.length)),r)),nk(W(t,aE(e.length)))],qR=U(e=>Un(e[0]),(e,t="utf-8")=>qd(()=>{let r=new TextDecoder(t);return UK(e,e=>r.decode(e))})),qN=Symbol.for("effect/GroupBy"),qA={_R:e=>e,_E:e=>e,_K:e=>e,_V:e=>e},qj=e=>ec(e,qN),qM=U(e=>qj(e[0]),(e,t,r)=>UI(e.grouped,([e,r])=>t(e,Uj(UJ(r,{shutdown:!0}))),{concurrency:"unbounded",bufferSize:r?.bufferSize??16})),qz=e=>({[qN]:qA,pipe(){return e6(this,arguments)},grouped:e}),qD=U(e=>"function"!=typeof e[0],(e,t,r)=>r?.key?qM(qP(e,r.key,{bufferSize:r.bufferSize}),(e,r)=>UG(r,t)):UF(r?.concurrency,()=>UG(e,t),n=>r?.unordered?UI(e,e=>U$(t(e)),{concurrency:n}):UY(e,n,t)));class q$ extends t4{static{m=AX}constructor(e,t){super(),this[m]={_Out:e=>e},this.dequeue=e,this.f=t}capacity(){return jk(this.dequeue)}get size(){return jC(this.dequeue)}unsafeSize(){return this.dequeue.unsafeSize()}get awaitShutdown(){return jF(this.dequeue)}isActive(){return this.dequeue.isActive()}get isShutdown(){return jI(this.dequeue)}get shutdown(){return jT(this.dequeue)}get isFull(){return jO(this.dequeue)}get isEmpty(){return jE(this.dequeue)}get take(){return W(ji(this.dequeue),pj(e=>this.f(e)))}get takeAll(){return W(jN(this.dequeue),pj(aP(e=>this.f(e))))}takeUpTo(e){return W(js(this.dequeue,e),pj(aP(e=>this.f(e))))}takeBetween(e,t){return W(ja(this.dequeue,e,t),pj(aP(e=>this.f(e))))}takeN(e){return W(jo(this.dequeue,e),pj(aP(e=>this.f(e))))}poll(){return W(jR(this.dequeue),pj(iM(e=>this.f(e))))}pipe(){return e6(this,arguments)}commit(){return this.take}}let qP=U(e=>"function"!=typeof e[0],(e,t,r)=>{let n=(e,i)=>zn({onInput:s=>M8(M9(_Y(qL(s,t),([t,n])=>{let s=e.get(t);return void 0===s?W(jv(r?.bufferSize??16),pv(r=>W(pH(()=>{e.set(t,r)}),wi(jr(i,L9([t,r]))),wi(W(jr(r,L5(n)),bu(e=>hf(e)?tR(pZ):iC())))))):bu(jr(s,L5(n)),e=>hf(e)?tR(pZ):iC())},{discard:!0})),()=>n(e,i)),onFailure:e=>M9(jr(i,L8(e))),onDone:()=>W(M9(W(_Y(e.entries(),([e,t])=>W(jr(t,L6),bu(e=>hf(e)?tR(pZ):iC())),{discard:!0}),wi(jr(i,L6)))))});return qz(qI(t=>EH(function*(){let r=new Map,i=yield*jw();return yield*fW(t,jT(i)),yield*Uz(e).pipe(ze(n(r,i)),Du,zD(t),wT(t),h9(Uj(UJ(i,{shutdown:!0}))))})))}),qL=U(2,(e,t)=>{let r,n=[],i=e[Symbol.iterator](),s=new Map;for(;(r=i.next())&&!r.done;){let e=r.value,i=t(e);if(s.has(i))s.get(i).push(e);else{let t=[e];n.push([i,t]),s.set(i,t)}}return av(n.map(e=>[e[0],av(e[1])]))}),qU=(...e)=>{let t=1===e.length?e[0].evaluate:e[0],r=1===e.length?e[0].onError:e[1],n=1===e.length&&!0===e[0].releaseLockOnEnd;return qO(pj(_J(pH(()=>t().getReader()),e=>n?pH(()=>e.releaseLock()):EK(()=>e.cancel())),e=>qr(pv(bU({try:()=>e.read(),catch:e=>tR(r(e))}),({done:e,value:t})=>e?pd(iC()):pB(t)))))},qq=e=>qi(e,$d),qB=Symbol.for("@effect/platform/Cookies"),qJ=e=>ec(e,qB),qH=Symbol.for("@effect/platform/Cookies/Cookie"),qK=Symbol.for("@effect/platform/Cookies/CookieError");class qW extends AW(qK,"CookieError"){get message(){return this.reason}}let qV={[qB]:qB,...eZ,toJSON(){return{_id:"@effect/platform/Cookies",cookies:iQ(this.cookies,e=>e.toJSON())}},pipe(){return e6(this,arguments)}},qG=e=>{let t=Object.create(qV);return t.cookies=e,t},qY=e=>{let t={};for(let r of e)t[r.name]=r;return qG(t)},qZ=e=>{let t=[];for(let r of"string"==typeof e?[e]:e){let e=function(e){let t=e.split(";").map(e=>e.trim()).filter(e=>""!==e);if(0===t.length)return iC();let r=t[0].indexOf("=");if(-1===r)return iC();let n=t[0].slice(0,r);if(!q0.test(n))return iC();let i=t[0].slice(r+1),s=q8(i);if(1===t.length)return tR(Object.assign(Object.create(q1),{name:n,value:s,valueEncoded:i}));let a={};for(let e=1;e<t.length;e++){let r=t[e],n=r.indexOf("="),i=-1===n?r:r.slice(0,n).trim(),s=-1===n?void 0:r.slice(n+1).trim();switch(i.toLowerCase()){case"domain":{if(void 0===s)break;let e=s.trim().replace(/^\./,"");e&&(a.domain=e);break}case"expires":{if(void 0===s)break;let e=new Date(s);isNaN(e.getTime())||(a.expires=e);break}case"max-age":{if(void 0===s)break;let e=parseInt(s,10);isNaN(e)||(a.maxAge=o_(e));break}case"path":if(void 0===s)break;"/"===s[0]&&(a.path=s);break;case"priority":if(void 0===s)break;switch(s.toLowerCase()){case"low":a.priority="low";break;case"medium":a.priority="medium";break;case"high":a.priority="high"}break;case"httponly":a.httpOnly=!0;break;case"secure":a.secure=!0;break;case"partitioned":a.partitioned=!0;break;case"samesite":if(void 0===s)break;switch(s.toLowerCase()){case"lax":a.sameSite="lax";break;case"strict":a.sameSite="strict";break;case"none":a.sameSite="none"}}}return tR(Object.assign(Object.create(q1),{name:n,value:s,valueEncoded:i,options:Object.keys(a).length>0?a:void 0}))}(r.trim());tF(e)&&t.push(e.value)}return qY(t)},qQ=qY([]),qX=e=>iG(e.cookies),q0=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/,q1={[qH]:qH,...eZ,toJSON(){return{_id:"@effect/platform/Cookies/Cookie",name:this.name,value:this.value,options:this.options}}};function q2(e,t,r){if(!q0.test(e))return nw(new qW({reason:"InvalidName"}));let n=encodeURIComponent(t);if(n&&!q0.test(n))return nw(new qW({reason:"InvalidValue"}));if(void 0!==r){if(void 0!==r.domain&&!q0.test(r.domain))return nw(new qW({reason:"InvalidDomain"}));if(void 0!==r.path&&!q0.test(r.path))return nw(new qW({reason:"InvalidPath"}));if(void 0!==r.maxAge&&!om(oc(r.maxAge)))return nw(new qW({reason:"InfinityMaxAge"}))}return nk(Object.assign(Object.create(q1),{name:e,value:t,valueEncoded:n,options:r}))}let q3=(e,t,r)=>nR(q2(e,t,r),q),q5=((e,t)=>qG({...e.cookies,...t.cookies}),U(2,(e,t)=>{let r={...e.cookies};for(let[e,n,i]of t){let t=q2(e,n,i);if(nS(t))return t;r[e]=t.right}return nk(qG(r))}));function q4(e){let t=e.name+"="+e.valueEncoded;if(void 0===e.options)return t;let r=e.options;if(void 0!==r.maxAge&&(t+="; Max-Age="+Math.trunc(oI(r.maxAge))),void 0!==r.domain&&(t+="; Domain="+r.domain),void 0!==r.path&&(t+="; Path="+r.path),void 0!==r.priority)switch(r.priority){case"low":t+="; Priority=Low";break;case"medium":t+="; Priority=Medium";break;case"high":t+="; Priority=High"}if(void 0!==r.expires&&(t+="; Expires="+r.expires.toUTCString()),r.httpOnly&&(t+="; HttpOnly"),r.secure&&(t+="; Secure"),r.partitioned&&(t+="; Partitioned"),void 0!==r.sameSite)switch(r.sameSite){case"lax":t+="; SameSite=Lax";break;case"strict":t+="; SameSite=Strict";break;case"none":t+="; SameSite=None"}return t}(e,t)=>nR(q5(e,t),q);let q6=e=>Object.values(e.cookies).map(q4),q8=e=>{try{return decodeURIComponent(e)}catch{return e}},q7=Symbol.for("@effect/platform/Headers"),q9=Object.assign(Object.create(null),{[q7]:q7,[e1](e){return Bl(this,gD(e,Bc))}}),Be=e=>Object.assign(Object.create(q9),e),Bt=Object.create(q9),Br=e=>{if(void 0===e)return Bt;if(Symbol.iterator in e){let t=Object.create(q9);for(let[r,n]of e)t[r.toLowerCase()]=n;return t}let t=Object.create(q9);for(let[r,n]of Object.entries(e))Array.isArray(n)?t[r.toLowerCase()]=n.join(", "):void 0!==n&&(t[r.toLowerCase()]=n);return t},Bn=e=>Object.setPrototypeOf(e,q9),Bi=U(3,(e,t,r)=>{let n=Be(e);return n[t.toLowerCase()]=r,n}),Bs=U(2,(e,t)=>Be({...e,...Br(t)})),Ba=U(2,(e,t)=>{let r=Be(e);return Object.assign(r,t),r}),Bo=U(2,(e,t)=>{let r=Be(e),n=t=>{if("string"==typeof t){let n=t.toLowerCase();n in e&&delete r[n]}else for(let n in e)t.test(n)&&delete r[n]};if(Array.isArray(t))for(let e=0;e<t.length;e++)n(t[e]);else n(t);return r}),Bl=U(2,(e,t)=>{let r={...e},n=t=>{if("string"==typeof t){let n=t.toLowerCase();n in e&&(r[n]=n6(e[n]))}else for(let n in e)t.test(n)&&(r[n]=n6(e[n]))};if(Array.isArray(t))for(let e=0;e<t.length;e++)n(t[e]);else n(t);return r}),Bc=Y("@effect/platform/Headers/currentRedactedNames",()=>fw(["authorization","cookie","set-cookie","x-api-key"]));function Bu(e){if(Array.isArray(e))return e.map(Bu).join("");switch(typeof e){case"string":return e;case"number":case"bigint":return e.toString();case"boolean":return e?"true":"false";default:return""}}let Bh=e=>{let t=Bp(e),r=[];for(let e=0;e<t.length;e++)if(Array.isArray(t[e][0])){let[n,i]=t[e];r.push([`${n[0]}[${n.slice(1).join("][")}]`,i])}else r.push(t[e]);return r},Bp=e=>{let t=Symbol.iterator in e?i6(e):Object.entries(e),r=[];for(let[e,n]of t)if(Array.isArray(n))for(let t=0;t<n.length;t++)void 0!==n[t]&&r.push([e,String(n[t])]);else if("object"==typeof n)for(let[t,i]of Bp(n))r.push([[e,..."string"==typeof t?[t]:t],i]);else void 0!==n&&r.push([e,String(n)]);return r},Bf=((e,t,r)=>st(sD(e,([e])=>e!==t),[t,String(r)]),U(2,(e,t)=>{let r=Bh(t),n=r.map(([e])=>e);return sr(sD(e,([e])=>n.includes(e)),r)})),Bd=U(3,(e,t,r)=>st(e,[t,String(r)])),Bm=((e,t)=>sr(e,Bh(t)),(e,t,r)=>{try{let n=new URL(e,Bg());for(let e=0;e<t.length;e++){let[r,i]=t[e];void 0!==i&&n.searchParams.append(r,i)}return"Some"===r._tag&&(n.hash=r.value),nk(n)}catch(e){return nw(e)}}),Bg=()=>{if("location"in globalThis&&void 0!==globalThis.location&&void 0!==globalThis.location.origin&&void 0!==globalThis.location.pathname)return location.origin+location.pathname},Bb=(e,t,r)=>M8(M9(gZ(()=>pZ)),n=>W(M9(pG(pK(e,e=>gX(n,t=>r(e,t))))),M8(t),M3(e=>pv(gQ(n),t=>t(e))))),Bx=tQ("@effect/platform/FileSystem"),By=(e,t)=>new AZ({module:"FileSystem",method:e,reason:"NotFound",description:"No such file or directory",pathOrDescriptor:t}),Bv=e=>"bigint"==typeof e?e:BigInt(e),BS=Symbol.for("@effect/platform/HttpBody"),B_=Symbol.for("@effect/platform/HttpBody/HttpBodyError"),Bw=e=>{let t=void 0===e?Object.create(tb):cC(e);return t._tag="HttpBodyError",t},Bk=e=>Bw({[B_]:B_,reason:e});class BC{constructor(){this[BS]=BS}[eV](){return this.toJSON()}toString(){return eY(this)}}class BE extends BC{toJSON(){return{_id:"@effect/platform/HttpBody",_tag:"Empty"}}constructor(...e){super(...e),this._tag="Empty"}}let BO=new BE;class BI extends BC{constructor(e,t){super(),this._tag="Uint8Array",this.body=e,this.contentType=t}get contentLength(){return this.body.length}toJSON(){return{_id:"@effect/platform/HttpBody",_tag:"Uint8Array",body:this.contentType.startsWith("text/")||this.contentType.endsWith("json")?new TextDecoder().decode(this.body):`Uint8Array(${this.body.length})`,contentType:this.contentType,contentLength:this.contentLength}}}let BF=(e,t)=>new BI(e,t??"application/octet-stream"),BT=new TextEncoder,BR=(e,t)=>BF(BT.encode(e),t??"text/plain"),BN=e=>BR(JSON.stringify(e),"application/json"),BA=e=>bc({try:()=>BN(e),catch:e=>Bk({_tag:"JsonError",error:e})});class Bj extends BC{constructor(e){super(),this._tag="FormData",this.formData=e}toJSON(){return{_id:"@effect/platform/HttpBody",_tag:"FormData",formData:this.formData}}}class BM extends BC{constructor(e,t,r){super(),this._tag="Stream",this.stream=e,this.contentType=t,this.contentLength=r}toJSON(){return{_id:"@effect/platform/HttpBody",_tag:"Stream",contentType:this.contentType,contentLength:this.contentLength}}}let Bz=(e,t,r)=>new BM(e,t??"application/octet-stream",r),BD=Symbol.for("@effect/platform/HttpServerResponse"),B$=Symbol.for("@effect/platform/HttpServerRespondable");class BP extends t6{constructor(e,t,r,n,i){if(super(),this.status=e,this.statusText=t,this.cookies=n,this.body=i,this[BD]=BD,i.contentType||i.contentLength){let e={...r};i.contentType&&(e["content-type"]=i.contentType),i.contentLength&&(e["content-length"]=i.contentLength.toString()),this.headers=e}else this.headers=r}commit(){return pB(this)}[B$](){return pB(this)}[eV](){return this.toJSON()}toString(){return eY(this)}toJSON(){return{_id:"@effect/platform/HttpServerResponse",status:this.status,statusText:this.statusText,headers:e4(this.headers),cookies:this.cookies.toJSON(),body:this.body.toJSON()}}}let BL=e=>"object"==typeof e&&null!==e&&BD in e,BU=e=>new BP(e?.status??204,e?.statusText,e?.headers?Br(e.headers):Bt,e?.cookies??qQ,BO),Bq=(e,t)=>e?.contentType?e.contentType:e?.headers?t["content-type"]:void 0,BB=U(3,(e,t,r)=>new BP(e.status,e.statusText,Bi(e.headers,t,r),e.cookies,e.body)),BJ=(e,t)=>pj(BA(e),e=>new BP(t?.status??200,t?.statusText,t?.headers?Br(t.headers):Bt,t?.cookies??qQ,e)),BH=(e,t)=>{let r=new globalThis.Headers(e.headers);if(!qX(e.cookies))for(let t of q6(e.cookies))r.append("set-cookie",t);if(t?.withoutBody)return new Response(void 0,{status:e.status,statusText:e.statusText,headers:r});let n=e.body;switch(n._tag){case"Empty":return new Response(void 0,{status:e.status,statusText:e.statusText,headers:r});case"Uint8Array":case"Raw":if(n.body instanceof Response)return n.body;return new Response(n.body,{status:e.status,statusText:e.statusText,headers:r});case"FormData":return new Response(n.formData,{status:e.status,statusText:e.statusText,headers:r});case"Stream":return new Response(qk(n.stream,t?.runtime??kO),{status:e.status,statusText:e.statusText,headers:r})}},BK=Symbol.for("@effect/platform/HttpServerRespondable"),BW=e=>ec(e,BK),BV=BU({status:400}),BG=BU({status:404}),BY=e=>BL(e)?pB(e):pL(e[BK]()),BZ=(e,t)=>BL(e)?pB(e):BW(e)?ps(e[BK](),()=>pB(t)):FU(e)?pB(BV):gC(e)?pB(BG):pB(t),BQ=(e,t)=>BL(e)?pB(e):BW(e)?ps(e[BK](),()=>pB(t)):pB(t),BX=Symbol.for("@effect/platform/HttpServerError"),B0=Y("@effect/platform/HttpServerError/clientAbortFiberId",()=>l9(-499,0)),B1=e=>{let t,r=hw(e,e=>BL(e)?(t=e,tR(hr)):iC());return[t??B2,r]},B2=BU({status:500}),B3=BU({status:499}),B5=BU({status:503}),B4=BX;class B6 extends AW(B4,"RequestError"){[BK](){return BU({status:400})}get methodAndUrl(){return`${this.request.method} ${this.request.url}`}get message(){return this.description?`${this.reason}: ${this.description} (${this.methodAndUrl})`:`${this.reason} error (${this.methodAndUrl})`}}class B8 extends AW(B4,"RouteNotFound"){constructor(e){super(e),this.stack=`${this.name}: ${this.message}`}[BK](){return BU({status:404})}get message(){return`${this.request.method} ${this.request.url} not found`}}let B7=e=>{let[t,r]=hM(e,[pB(B2),hr],(e,t)=>{switch(t._tag){case"Empty":return tR(e);case"Fail":return tR([BZ(t.error,B2),t]);case"Die":return tR([BQ(t.defect,B2),t]);case"Interrupt":if("Empty"!==e[1]._tag)return iC();return tR([pB(t.fiberId===B0?B3:B5),t]);default:return iC()}});return pj(t,e=>hc(r)?[e,hi(e)]:[e,ho(r,hi(e))])},B9=e=>"Success"===e._tag?e.value:B1(e.cause)[0],Je=Symbol.for("@effect/platform/HttpIncomingMessage"),Jt=(e,t)=>{let r=TJ(e,t);return e=>pv(e.json,r)},Jr=(e,t)=>{let r=TJ(e,t);return e=>r(e.headers)},Jn=Y("@effect/platform/HttpIncomingMessage/maxBodySize",()=>fw(iC())),Ji=(e,t)=>{let r,n=e.headers["content-type"]??"";if(n.includes("application/json"))try{r=kR(e.json)}catch{}else if(n.includes("text/")||n.includes("urlencoded"))try{r=kR(e.text)}catch{}let i={...t,headers:e4(e.headers),remoteAddress:e.remoteAddress.toJSON()};return void 0!==r&&(i.body=r),i},Js=Symbol.for("effect/Mailbox"),Ja=Symbol.for("effect/Mailbox/ReadonlyMailbox"),Jo=ap(),Jl=dS(Jo),Jc=dS(!1),Ju=dS(!0),Jh=[Jo,!0];class Jp extends t4{constructor(e,t,r){super(),this[Js]=Js,this[Ja]=Ja,this.state={_tag:"Open",takers:new Set,offers:new Set,awaiters:new Set},this.messages=[],this.messagesChunk=ap(),this.shutdown=pH(()=>{if("Done"===this.state._tag)return!0;this.messages=[],this.messagesChunk=Jo;let e=this.state.offers;if(this.finalize("Open"===this.state._tag?d_:this.state.exit),e.size>0){for(let t of e)"Single"===t._tag?t.resume(Jc):t.resume(dS(av(t.remaining.slice(t.offset))));e.clear()}return!0}),this.end=this.done(d_),this.clear=pJ(()=>{if("Done"===this.state._tag)return dc(this.state.exit,Jo);let e=this.unsafeTakeAll();return this.releaseCapacity(),pB(e)}),this.takeAll=pJ(()=>{if("Done"===this.state._tag)return dc(this.state.exit,Jh);let e=this.unsafeTakeAll();return 0===e.length?p6(this.awaitTake,this.takeAll):pB([e,this.releaseCapacity()])}),this.take=pJ(()=>this.unsafeTake()??p6(this.awaitTake,this.take)),this.await=pn(e=>"Done"===this.state._tag?e(this.state.exit):(this.state.awaiters.add(e),pH(()=>{"Done"!==this.state._tag&&this.state.awaiters.delete(e)}))),this.size=pH(()=>this.unsafeSize()),this.awaitTake=pn(e=>"Done"===this.state._tag?e(this.state.exit):(this.state.takers.add(e),pH(()=>{"Done"!==this.state._tag&&this.state.takers.delete(e)}))),this.scheduleRunning=!1,this.releaseTaker=()=>{if(this.scheduleRunning=!1,"Done"===this.state._tag||0===this.state.takers.size)return;let e=iJ(this.state.takers);this.state.takers.delete(e),e(d_)},this.scheduler=e,this.capacity=t,this.strategy=r}offer(e){return pJ(()=>{if("Open"!==this.state._tag)return Jc;if(this.messages.length+this.messagesChunk.length>=this.capacity)switch(this.strategy){case"dropping":return Jc;case"suspend":if(this.capacity<=0&&this.state.takers.size>0)return this.messages.push(e),this.releaseTaker(),Ju;return this.offerRemainingSingle(e);case"sliding":return this.unsafeTake(),this.messages.push(e),Ju}return this.messages.push(e),this.scheduleReleaseTaker(),Ju})}unsafeOffer(e){return"Open"===this.state._tag&&(this.messages.length+this.messagesChunk.length>=this.capacity?"sliding"===this.strategy?(this.unsafeTake(),this.messages.push(e),!0):this.capacity<=0&&this.state.takers.size>0&&(this.messages.push(e),this.releaseTaker(),!0):(this.messages.push(e),this.scheduleReleaseTaker(),!0))}offerAll(e){return pJ(()=>{if("Open"!==this.state._tag)return pB(am(e));let t=this.unsafeOfferAllArray(e);return 0===t.length?Jl:"dropping"===this.strategy?pB(av(t)):this.offerRemainingArray(t)})}unsafeOfferAll(e){return av(this.unsafeOfferAllArray(e))}unsafeOfferAllArray(e){if("Open"!==this.state._tag)return i6(e);if(this.capacity===Number.POSITIVE_INFINITY||"sliding"===this.strategy)return this.messages.length>0&&(this.messagesChunk=aI(this.messagesChunk,av(this.messages))),"sliding"===this.strategy?this.messagesChunk=this.messagesChunk.pipe(aI(am(e)),aB(this.capacity)):au(e)?this.messagesChunk=aI(this.messagesChunk,e):this.messages=i6(e),this.scheduleReleaseTaker(),[];let t=this.capacity<=0?this.state.takers.size:this.capacity-this.messages.length-this.messagesChunk.length;if(0===t)return i6(e);let r=[],n=0;for(let i of e)n<t?this.messages.push(i):r.push(i),n++;return this.scheduleReleaseTaker(),r}fail(e){return this.done(df(e))}failCause(e){return this.done(dd(e))}unsafeDone(e){return"Open"===this.state._tag&&(0===this.state.offers.size&&0===this.messages.length&&0===this.messagesChunk.length?this.finalize(e):this.state={...this.state,_tag:"Closing",exit:e},!0)}done(e){return pH(()=>this.unsafeDone(e))}takeN(e){return pJ(()=>{let t;if("Done"===this.state._tag)return dc(this.state.exit,Jh);if(e<=0)return pB([Jo,!1]);if((e=Math.min(e,this.capacity))<=this.messagesChunk.length)t=aC(this.messagesChunk,e),this.messagesChunk=aE(this.messagesChunk,e);else{if(!(e<=this.messages.length+this.messagesChunk.length))return p6(this.awaitTake,this.takeN(e));this.messagesChunk=aI(this.messagesChunk,av(this.messages)),this.messages=[],t=aC(this.messagesChunk,e),this.messagesChunk=aE(this.messagesChunk,e)}return pB([t,this.releaseCapacity()])})}unsafeTake(){let e;if("Done"===this.state._tag)return dk(this.state.exit,df(new f7));if(this.messagesChunk.length>0)e=aD(this.messagesChunk),this.messagesChunk=aE(this.messagesChunk,1);else if(this.messages.length>0)e=this.messages[0],this.messagesChunk=aE(av(this.messages),1),this.messages=[];else if(this.capacity<=0&&this.state.offers.size>0)return this.capacity=1,this.releaseCapacity(),this.capacity=0,this.messages.length>0?dS(this.messages.pop()):void 0;else return;return this.releaseCapacity(),dS(e)}unsafeSize(){let e=this.messages.length+this.messagesChunk.length;return"Done"===this.state._tag?iC():tR(e)}commit(){return this.takeAll}pipe(){return e6(this,arguments)}toJSON(){return{_id:"effect/Mailbox",state:this.state._tag,size:this.unsafeSize().toJSON()}}toString(){return eY(this)}[eV](){return eY(this)}offerRemainingSingle(e){return pn(t=>{if("Open"!==this.state._tag)return t(Jc);let r={_tag:"Single",message:e,resume:t};return this.state.offers.add(r),pH(()=>{"Open"===this.state._tag&&this.state.offers.delete(r)})})}offerRemainingArray(e){return pn(t=>{if("Open"!==this.state._tag)return t(dS(av(e)));let r={_tag:"Array",remaining:e,offset:0,resume:t};return this.state.offers.add(r),pH(()=>{"Open"===this.state._tag&&this.state.offers.delete(r)})})}releaseCapacity(){if("Done"===this.state._tag)return"Success"===this.state.exit._tag;if(0===this.state.offers.size)return"Closing"===this.state._tag&&0===this.messages.length&&0===this.messagesChunk.length&&(this.finalize(this.state.exit),"Success"===this.state.exit._tag);let e=this.capacity-this.messages.length-this.messagesChunk.length;for(let t of this.state.offers)if(0===e)break;else if("Single"===t._tag)this.messages.push(t.message),e--,t.resume(Ju),this.state.offers.delete(t);else{for(;t.offset<t.remaining.length;t.offset++){if(0===e)return!1;this.messages.push(t.remaining[t.offset]),e--}t.resume(Jl),this.state.offers.delete(t)}return!1}scheduleReleaseTaker(){this.scheduleRunning||(this.scheduleRunning=!0,this.scheduler.scheduleTask(this.releaseTaker,0))}unsafeTakeAll(){if(this.messagesChunk.length>0){let e=this.messages.length>0?aI(this.messagesChunk,av(this.messages)):this.messagesChunk;return this.messagesChunk=Jo,this.messages=[],e}if(this.messages.length>0){let e=av(this.messages);return this.messages=[],e}return"Done"!==this.state._tag&&this.state.offers.size>0?(this.capacity=1,this.releaseCapacity(),this.capacity=0,ad(this.messages.pop())):Jo}finalize(e){if("Done"===this.state._tag)return;let t=this.state;for(let r of(this.state={_tag:"Done",exit:e},t.takers))r(e);for(let r of(t.takers.clear(),t.awaiters))r(e);t.awaiters.clear()}}let Jf=e=>h8(t=>pB(new Jp(t.currentScheduler,"number"==typeof e?e:e?.capacity??Number.POSITIVE_INFINITY,"number"==typeof e?"suspend":e?.strategy??"suspend"))),Jd=/; *([!#$%&'*+.^\w`|~-]+)=("(?:[\v\u0020\u0021\u0023-\u005b\u005d-\u007e\u0080-\u00ff]|\\[\v\u0020-\u00ff])*"|[!#$%&'*+.^\w`|~-]+) */gu,Jm=/\\([\v\u0020-\u00ff])/gu,Jg=/^[!#$%&'*+.^\w|~-]+\/[!#$%&'*+.^\w|~-]+$/u,Jb=/^[!#$%&'*+.^\w|~-]+$/u,Jx={value:"",parameters:Object.create(null)};function Jy(e,t=!1){let r,n,i;if("string"!=typeof e)return Jx;let s=e.indexOf(";"),a=-1!==s?e.slice(0,s).trim():e.trim();if(!1===(t?Jb:Jg).test(a))return Jx;let o={value:a.toLowerCase(),parameters:Object.create(null)};if(-1===s)return o;for(Jd.lastIndex=s;n=Jd.exec(e);){if(n.index!==s)return Jx;s+=n[0].length,r=n[1].toLowerCase(),'"'===(i=n[2])[0]&&(i=i.slice(1,i.length-1),!t&&Jm.test(i)&&(i=i.replace(Jm,"$1"))),o.parameters[r]=i}return s!==e.length?Jx:o}!function(e){e[e.key=0]="key",e[e.whitespace=1]="whitespace",e[e.value=2]="value"}(N||(N={}));let Jv={_tag:"Continue"},JS=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,1,1,1,1,1,0,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1],J_=[0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1];!function(e){e[e.headers=0]="headers",e[e.body=1]="body"}(A||(A={}));let Jw={_tag:"InvalidDisposition"},Jk={_tag:"EndNotReached"},JC={_tag:"ReachedLimit",limit:"MaxParts"},JE={_tag:"ReachedLimit",limit:"MaxTotalSize"},JO={_tag:"ReachedLimit",limit:"MaxPartSize"},JI={_tag:"ReachedLimit",limit:"MaxFieldSize"},JF=new TextEncoder().encode("\r\n");function JT(e){return void 0!==e.filename||"application/octet-stream"===e.contentType}function JR(e){}let JN=new TextDecoder("utf-8"),JA=function({headers:e,onFile:t,onField:r,onError:n,onDone:i,isFile:s=JT,maxParts:a=1/0,maxTotalSize:o=1/0,maxPartSize:l=1/0,maxFieldSize:c=1048576}){let u=Jy(e["content-type"]).parameters.boundary;if(void 0===u)return n({_tag:"InvalidBoundary"}),{write:JR,end(){}};let h={state:A.headers,index:0,parts:0,onChunk:JR,info:void 0,headerSkip:0,partSize:0,totalSize:0,isFile:!1,fieldChunks:[],fieldSize:0};function p(){h.state=A.body,h.isFile=!0,h.onChunk=JR}let f=function(){let e=new TextDecoder,t={state:N.key,headers:Object.create(null),key:"",value:void 0,crlf:0,previousChunk:void 0,pairs:0,size:0};function r(e){return t.state=N.key,t.headers=Object.create(null),t.key="",t.value=void 0,t.crlf=0,t.previousChunk=void 0,t.pairs=0,t.size=0,e}function n(e,t){let r=new Uint8Array(e.length+t.length);return r.set(e),r.set(t,e.length),r}function i(e){return r({_tag:"Failure",reason:e,headers:t.headers})}return function(s,a){let o,l=0;if(void 0!==t.previousChunk){o=l=t.previousChunk.length;let e=new Uint8Array(s.length+l);e.set(t.previousChunk),e.set(s,l),t.previousChunk=void 0,s=e}let c=s.length;e:for(;a<c;){if(t.state===N.key){let r=a;for(;r<c;r++){if(t.size++>16384)return i("HeaderTooLarge");if(58===s[r]){if(t.key+=e.decode(s.subarray(a,r)).toLowerCase(),0===t.key.length)return i("InvalidHeaderName");32===s[r+1]&&32!==s[r+2]&&9!==s[r+2]?(a=r+2,t.state=N.value,t.size++):32!==s[r+1]&&9!==s[r+1]?(a=r+1,t.state=N.value):(a=r+1,t.state=N.whitespace);break}if(1!==JS[s[r]])return i("InvalidHeaderName")}if(r===c)return t.key+=e.decode(s.subarray(a,c)).toLowerCase(),Jv}if(t.state===N.whitespace){for(;a<c;a++){if(t.size++>16384)return i("HeaderTooLarge");if(32!==s[a]&&9!==s[a]){t.state=N.value;break}}if(a===c)return Jv}if(t.state===N.value){let u=a;for(void 0!==o&&(u=o,o=void 0);u<c;u++){if(t.size++>16384)return i("HeaderTooLarge");if(13===s[u]||t.crlf>0){let o=s[u];if(13===o&&0===t.crlf&&(t.crlf=1,u++,t.size++,o=s[u]),10===o&&1===t.crlf&&(t.crlf=2,u++,t.size++,o=s[u]),13===o&&2===t.crlf&&(t.crlf=3,u++,t.size++,o=s[u]),10===o&&3===t.crlf&&(t.crlf=4,u++,t.size++),t.crlf<4&&u>=c)return t.previousChunk=s.subarray(a),Jv;if(t.crlf>=2){t.value=void 0===t.value?s.subarray(a,u-t.crlf):n(t.value,s.subarray(a,u-t.crlf));let o=e.decode(t.value);if(void 0===t.headers[t.key]?t.headers[t.key]=o:"string"==typeof t.headers[t.key]?t.headers[t.key]=[t.headers[t.key],o]:t.headers[t.key].push(o),a=u,t.size--,4!==t.crlf&&100===t.pairs)return i("TooManyHeaders");if(3===t.crlf)return i("InvalidHeaderValue");if(4===t.crlf)return r({_tag:"Headers",headers:t.headers,endPosition:a-l});t.pairs++,t.key="",t.value=void 0,t.crlf=0,t.state=N.key;continue e}}else if(1!==J_[s[u]])return i("InvalidHeaderValue")}if(u===c)return t.value=void 0===t.value?s.subarray(a,c):n(t.value,s.subarray(a,c)),Jv}}return a>c&&(t.size+=c-a),Jv}}(),d=function(e,t,r){let n=function(e){let t=new TextEncoder().encode(e),r=t.length,n={};for(let e=0;e<r;e++){let r=t[e];void 0===n[r]&&(n[r]=[]),n[r].push(e)}return{needle:t,needleLength:r,indexes:n,firstByte:t[0],previousChunk:void 0,previousChunkLength:0,matchIndex:0}}(e);void 0!==r&&(n.previousChunk=r,n.previousChunkLength=r.length);let i=function(){if("Buffer"in globalThis&&!("Bun"in globalThis||"Deno"in globalThis))return function(e,t,r){return Buffer.prototype.indexOf.call(e,t,r)};let e=new Uint8Array(256).fill(n.needle.length);for(let t=0,r=n.needle.length-1;t<r;++t)e[n.needle[t]]=r-t;return function(t,r,i){let s=t.length,a=i+n.needleLength-1;for(;a<s;){for(let e=n.needleLength-1,i=a;e>=0&&t[i]===r[e];e--,i--)if(0===e)return i;a+=e[t[a]]}return -1}}();return{write:function(e){let r=e.length;if(void 0!==n.previousChunk){let t=new Uint8Array(n.previousChunkLength+r);t.set(n.previousChunk),t.set(e,n.previousChunkLength),e=t,r=n.previousChunkLength+r,n.previousChunk=void 0}if(r<n.needleLength){n.previousChunk=e,n.previousChunkLength=r;return}let s=0;for(;s<r;){let a=i(e,n.needle,s);if(a>-1){a>s&&t(n.matchIndex,e.subarray(s,a)),n.matchIndex+=1,s=a+n.needleLength;continue}if(e[r-1]in n.indexes){let i=n.indexes[e[r-1]],a=-1;for(let t=0,s=i.length;t<s;t++){let s=i[t];e[r-1-s]===n.firstByte&&t>a&&(a=s)}-1===a?0===s?t(n.matchIndex,e):t(n.matchIndex,e.subarray(s)):(r-1-a>s&&t(n.matchIndex,e.subarray(s,r-1-a)),n.previousChunk=e.subarray(r-1-a),n.previousChunkLength=a+1)}else 0===s?t(n.matchIndex,e):t(n.matchIndex,e.subarray(s));break}},end:function(){void 0!==n.previousChunk&&n.previousChunk!==r&&t(n.matchIndex,n.previousChunk),n.previousChunk=r,n.previousChunkLength=r?.length??0,n.matchIndex=0}}}(`\r
--${u}`,function(e,o){if(0===e)return void p();if(e!==h.index){if(h.index>0)if(h.isFile)h.onChunk(null),h.partSize=0;else{if(1===h.fieldChunks.length)r(h.info,h.fieldChunks[0]);else{let e=new Uint8Array(h.fieldSize),t=0;for(let r=0;r<h.fieldChunks.length;r++){let n=h.fieldChunks[r];e.set(n,t),t+=n.length}r(h.info,e)}h.fieldSize=0,h.fieldChunks=[]}if(h.state=A.headers,h.index=e,h.headerSkip=2,45===o[0]&&45===o[1])return i();h.parts++,h.parts>a&&n(JC)}if((h.partSize+=o.length)>l&&n(JO),h.state===A.headers){let e,r=f(o,h.headerSkip);if(h.headerSkip=0,"Continue"===r._tag)return;if("Failure"===r._tag)return p(),n({_tag:"BadHeaders",error:r});let i=Jy(r.headers["content-type"]),a=Jy(r.headers["content-disposition"],!0);if("form-data"===a.value&&!("name"in a.parameters))return p(),n(Jw);if("filename*"in a.parameters){let t=a.parameters["filename*"].split("''");2===t.length&&(e=decodeURIComponent(t[1]))}if(h.info={name:a.parameters.name??"",filename:e??a.parameters.filename,contentType:""===i.value?void 0!==a.parameters.filename?"application/octet-stream":"text/plain":i.value,contentTypeParameters:i.parameters,contentDisposition:a.value,contentDispositionParameters:a.parameters,headers:r.headers},h.state=A.body,h.isFile=s(h.info),h.isFile&&(h.onChunk=t(h.info)),r.endPosition<o.length)if(h.isFile)h.onChunk(o.subarray(r.endPosition));else{let e=o.subarray(r.endPosition);(h.fieldSize+=e.length)>c&&n(JI),h.fieldChunks.push(e)}}else h.isFile?h.onChunk(o):((h.fieldSize+=o.length)>c&&n(JI),h.fieldChunks.push(o))},JF);return{write:e=>(h.totalSize+=e.length)>o?n(JE):d.write(e),end(){d.end(),h.state===A.body&&n(Jk),h.state=A.headers,h.index=0,h.parts=0,h.onChunk=JR,h.info=void 0,h.totalSize=0,h.partSize=0,h.fieldChunks=[],h.fieldSize=0}}},Jj=Symbol.for("@effect/platform/Path"),JM=tQ("@effect/platform/Path");function Jz(e,t){let r,n="",i=0,s=-1,a=0;for(let o=0;o<=e.length;++o){if(o<e.length)r=e.charCodeAt(o);else if(47===r)break;else r=47;if(47===r){if(s===o-1||1===a);else if(s!==o-1&&2===a){if(n.length<2||2!==i||46!==n.charCodeAt(n.length-1)||46!==n.charCodeAt(n.length-2)){if(n.length>2){let e=n.lastIndexOf("/");if(e!==n.length-1){-1===e?(n="",i=0):i=(n=n.slice(0,e)).length-1-n.lastIndexOf("/"),s=o,a=0;continue}}else if(2===n.length||1===n.length){n="",i=0,s=o,a=0;continue}}t&&(n.length>0?n+="/..":n="..",i=2)}else n.length>0?n+="/"+e.slice(s+1,o):n=e.slice(s+1,o),i=o-s-1;s=o,a=0}else 46===r&&-1!==a?++a:a=-1}return n}let JD=function(){let e,t="",r=!1;for(let n=arguments.length-1;n>=-1&&!r;n--){let i;if(n>=0)i=arguments[n];else{let t=globalThis.process;void 0===e&&"process"in globalThis&&"object"==typeof t&&null!==t&&"function"==typeof t.cwd&&(e=t.cwd()),i=e}0!==i.length&&(t=i+"/"+t,r=47===i.charCodeAt(0))}if(t=Jz(t,!r),r)if(t.length>0)return"/"+t;else return"/";return t.length>0?t:"."},J$=/%/g,JP=/\\/g,JL=/\n/g,JU=/\r/g,Jq=/\t/g,JB=JM.of({[Jj]:Jj,resolve:JD,normalize(e){if(0===e.length)return".";let t=47===e.charCodeAt(0),r=47===e.charCodeAt(e.length-1);return(0!==(e=Jz(e,!t)).length||t||(e="."),e.length>0&&r&&(e+="/"),t)?"/"+e:e},isAbsolute:e=>e.length>0&&47===e.charCodeAt(0),join(){let e;if(0==arguments.length)return".";for(let t=0;t<arguments.length;++t){let r=arguments[t];r.length>0&&(void 0===e?e=r:e+="/"+r)}return void 0===e?".":JB.normalize(e)},relative(e,t){if(e===t||(e=JB.resolve(e),t=JB.resolve(t),e===t))return"";let r=1;for(;r<e.length&&47===e.charCodeAt(r);++r);let n=e.length,i=n-r,s=1;for(;s<t.length&&47===t.charCodeAt(s);++s);let a=t.length-s,o=i<a?i:a,l=-1,c=0;for(;c<=o;++c){if(c===o){if(a>o){if(47===t.charCodeAt(s+c))return t.slice(s+c+1);else if(0===c)return t.slice(s+c)}else i>o&&(47===e.charCodeAt(r+c)?l=c:0===c&&(l=0));break}let n=e.charCodeAt(r+c);if(n!==t.charCodeAt(s+c))break;47===n&&(l=c)}let u="";for(c=r+l+1;c<=n;++c)(c===n||47===e.charCodeAt(c))&&(0===u.length?u+="..":u+="/..");return u.length>0?u+t.slice(s+l):(s+=l,47===t.charCodeAt(s)&&++s,t.slice(s))},dirname(e){if(0===e.length)return".";let t=e.charCodeAt(0),r=47===t,n=-1,i=!0;for(let r=e.length-1;r>=1;--r)if(47===(t=e.charCodeAt(r))){if(!i){n=r;break}}else i=!1;return -1===n?r?"/":".":r&&1===n?"//":e.slice(0,n)},basename(e,t){let r,n=0,i=-1,s=!0;if(void 0!==t&&t.length>0&&t.length<=e.length){if(t.length===e.length&&t===e)return"";let a=t.length-1,o=-1;for(r=e.length-1;r>=0;--r){let l=e.charCodeAt(r);if(47===l){if(!s){n=r+1;break}}else -1===o&&(s=!1,o=r+1),a>=0&&(l===t.charCodeAt(a)?-1==--a&&(i=r):(a=-1,i=o))}return n===i?i=o:-1===i&&(i=e.length),e.slice(n,i)}for(r=e.length-1;r>=0;--r)if(47===e.charCodeAt(r)){if(!s){n=r+1;break}}else -1===i&&(s=!1,i=r+1);return -1===i?"":e.slice(n,i)},extname(e){let t=-1,r=0,n=-1,i=!0,s=0;for(let a=e.length-1;a>=0;--a){let o=e.charCodeAt(a);if(47===o){if(!i){r=a+1;break}continue}-1===n&&(i=!1,n=a+1),46===o?-1===t?t=a:1!==s&&(s=1):-1!==t&&(s=-1)}return -1===t||-1===n||0===s||1===s&&t===n-1&&t===r+1?"":e.slice(t,n)},format:function(e){if(null===e||"object"!=typeof e)throw TypeError('The "pathObject" argument must be of type Object. Received type '+typeof e);let t=e.dir||e.root,r=e.base||(e.name||"")+(e.ext||"");return t?t===e.root?t+r:t+"/"+r:r},parse(e){let t,r={root:"",dir:"",base:"",ext:"",name:""};if(0===e.length)return r;let n=e.charCodeAt(0),i=47===n;i?(r.root="/",t=1):t=0;let s=-1,a=0,o=-1,l=!0,c=e.length-1,u=0;for(;c>=t;--c){if(47===(n=e.charCodeAt(c))){if(!l){a=c+1;break}continue}-1===o&&(l=!1,o=c+1),46===n?-1===s?s=c:1!==u&&(u=1):-1!==s&&(u=-1)}return -1===s||-1===o||0===u||1===u&&s===o-1&&s===a+1?-1!==o&&(0===a&&i?r.base=r.name=e.slice(1,o):r.base=r.name=e.slice(a,o)):(0===a&&i?(r.name=e.slice(1,s),r.base=e.slice(1,o)):(r.name=e.slice(a,s),r.base=e.slice(a,o)),r.ext=e.slice(s,o)),a>0?r.dir=e.slice(0,a-1):i&&(r.dir="/"),r},sep:"/",fromFileUrl:function(e){if("file:"!==e.protocol)return pd(new AG({module:"Path",method:"fromFileUrl",description:"URL must be of scheme file"}));if(""!==e.hostname)return pd(new AG({module:"Path",method:"fromFileUrl",description:"Invalid file URL host"}));let t=e.pathname;for(let e=0;e<t.length;e++)if("%"===t[e]){let r=32|t.codePointAt(e+2);if("2"===t[e+1]&&102===r)return pd(new AG({module:"Path",method:"fromFileUrl",description:"must not include encoded / characters"}))}return pB(decodeURIComponent(t))},toFileUrl:function(e){var t;let r=new URL("file://"),n=JD(e);return 47===e.charCodeAt(e.length-1)&&"/"!==n[n.length-1]&&(n+="/"),(t=n).includes("%")&&(t=t.replace(J$,"%25")),t.includes("\\")&&(t=t.replace(JP,"%5C")),t.includes("\n")&&(t=t.replace(JL,"%0A")),t.includes("\r")&&(t=t.replace(JU,"%0D")),t.includes("	")&&(t=t.replace(Jq,"%09")),r.pathname=t,pB(r)},toNamespacedPath:q}),JJ=Symbol.for("@effect/platform/Multipart"),JH=Symbol.for("@effect/platform/Multipart/MultipartError");class JK extends AW(JH,"MultipartError"){get message(){return this.reason}}let JW=Y("@effect/platform/Multipart/maxParts",()=>fw(iC())),JV=Y("@effect/platform/Multipart/maxFieldSize",()=>fw(Bv(0xa00000))),JG=Y("@effect/platform/Multipart/maxFileSize",()=>fw(iC())),JY=Y("@effect/platform/Multipart/fieldMimeTypes",()=>fw(af("application/json"))),JZ=e=>h8(t=>{let r=t.getFiberRef(JY);return pB({headers:e,maxParts:iR(t.getFiberRef(JW)),maxFieldSize:Number(t.getFiberRef(JV)),maxPartSize:t.getFiberRef(JG).pipe(iM(Number),iR),maxTotalSize:t.getFiberRef(Jn).pipe(iM(Number),iR),isFile:0===r.length?void 0:e=>!aV(r,t=>e.contentType.includes(t))&&JT(e)})}),JQ=e=>"Success"===e._tag?zl:M4(e.cause);class JX extends eQ{constructor(){super(),this[JJ]=JJ}}class J0 extends JX{constructor(e,t,r){super(),this._tag="Field",this.key=e,this.contentType=t,this.value=r}toJSON(){return{_id:"@effect/platform/Multipart/Part",_tag:"Field",key:this.key,contentType:this.contentType,value:this.value}}}class J1 extends JX{constructor(e,t){super(),this._tag="File",this.key=e.name,this.name=e.filename??e.name,this.contentType=e.contentType,this.content=UM(t),this.contentEffect=t.pipe(ze(J3),Dz,pz(e=>new JK({reason:"InternalError",cause:e})))}toJSON(){return{_id:"@effect/platform/Multipart/Part",_tag:"File",key:this.key,name:this.name,contentType:this.contentType}}}let J2=(e,t)=>pv(Bx,r=>pz(qi(t.content,r.sink(e)),e=>new JK({reason:"InternalError",cause:e}))),J3=za(()=>{let e=new Uint8Array(0),t=zn({onInput(r){for(let t of r){let r=new Uint8Array(e.length+t.length);r.set(e,0),r.set(t,e.length),e=r}return t},onFailure:e=>M4(e),onDone:()=>zi(e)});return t});class J5 extends JX{constructor(e,t,r,n){super(),this._tag="PersistedFile",this.key=e,this.name=t,this.contentType=r,this.path=n}toJSON(){return{_id:"@effect/platform/Multipart/Part",_tag:"PersistedFile",key:this.key,name:this.name,contentType:this.contentType,path:this.path}}}let J4=(e,t=16)=>Bb(_G([JZ(e),Jf(t)]),([e,t])=>{let r=[],n=iC(),i=JA({...e,onField(e,t){r.push(new J0(e.name,e.contentType,(function(e){if("utf-8"===e||"utf8"===e||""===e)return JN;try{return new TextDecoder(e)}catch(e){return JN}})(e.contentTypeParameters.charset??"utf-8").decode(t)))},onFile(e){let t=[],n=!1,i=za(()=>{if(0===t.length)return n?zl:D1(s,i);let e=av(t);return t=[],n?zc(e):D1(zc(e),D1(s,i))});return r.push(new J1(e,i)),function(e){null===e?n=!0:t.push(e)}},onError(e){n=tR(df(function(e){if("ReachedLimit"===e._tag)switch(e.limit){case"MaxParts":return new JK({reason:"TooManyParts",cause:e});case"MaxFieldSize":return new JK({reason:"FieldTooLarge",cause:e});case"MaxPartSize":return new JK({reason:"FileTooLarge",cause:e});case"MaxTotalSize":return new JK({reason:"BodyTooLarge",cause:e})}return new JK({reason:"Parse",cause:e})}(e)))},onDone(){n=tR(d_)}}),s=M8(t.takeAll,([e,t])=>zo(()=>{aN(e,aN(i.write)),t&&i.end()})),a=M8(s,()=>{if(0===r.length)return"None"===n._tag?a:JQ(n.value);let e=av(r);return r=[],D1(zc(e),"None"===n._tag?a:JQ(n.value))});return M2(a,{awaitRead:()=>pZ,emit:e=>t.offer(e),error:e=>(n=tR(dd(e)),t.end),done:e=>t.end})},([,e])=>e.shutdown),J6=(e,t=J2)=>EH(function*(){let r=yield*Bx,n=yield*JM,i=yield*r.makeTempDirectoryScoped(),s=Object.create(null);return yield*qs(e,e=>{if("Field"===e._tag)return e.key in s?"string"==typeof s[e.key]?s[e.key]=[s[e.key],e.value]:s[e.key].push(e.value):s[e.key]=e.value,pZ;if(""===e.name)return pZ;let r=n.join(i,n.basename(e.name).slice(-128)),a=new J5(e.key,e.name,e.contentType,r);return Array.isArray(s[e.key])?s[e.key].push(a):s[e.key]=[a],t(r,e)}),s}).pipe(bp({SystemError:e=>pd(new JK({reason:"InternalError",cause:e})),BadArgument:e=>pd(new JK({reason:"InternalError",cause:e}))})),J8=Symbol.for("@effect/platform/HttpServerRequest"),J7=tQ("@effect/platform/HttpServerRequest"),J9=tQ("@effect/platform/HttpServerRequest/ParsedSearchParams"),He=e=>{if("/"===e[0])return e;let t=e.indexOf("/",e.indexOf("//")+2);return -1===t?"/":e.slice(t)};class Ht extends eQ{constructor(e,t,r,n){super(),this.source=e,this.url=t,this.headersOverride=r,this.remoteAddressOverride=n,this[J8]=J8,this[Je]=Je}toJSON(){return Ji(this,{_id:"@effect/platform/HttpServerRequest",method:this.method,url:this.originalUrl})}modify(e){return new Ht(this.source,e.url??this.url,e.headers??this.headersOverride,e.remoteAddress??this.remoteAddressOverride)}get method(){return this.source.method.toUpperCase()}get originalUrl(){return this.source.url}get remoteAddress(){return this.remoteAddressOverride?tR(this.remoteAddressOverride):iC()}get headers(){return this.headersOverride??=Br(this.source.headers),this.headersOverride}get cookies(){return this.cachedCookies?this.cachedCookies:this.cachedCookies=function(e){let t={},r=e.length,n=0,i=0;for(;i!==r;){-1===(i=e.indexOf(";",n))&&(i=r);let s=e.indexOf("=",n);if(-1===s)break;if(s>i){n=i+1;continue}let a=e.substring(n,s++).trim();if(void 0===t[a]){let r=34===e.charCodeAt(s)?e.substring(s+1,i-1).trim():e.substring(s,i).trim();t[a]=-1!==r.indexOf("%")?q8(r):r}n=i+1}return t}(this.headers.cookie??"")}get stream(){return this.source.body?qU(()=>this.source.body,e=>new B6({request:this,reason:"Decode",cause:e})):UC(new B6({request:this,reason:"Decode",description:"can not create stream from empty body"}))}get text(){return this.textEffect||(this.textEffect=kR(EJ(bU({try:()=>this.source.text(),catch:e=>new B6({request:this,reason:"Decode",cause:e})})))),this.textEffect}get json(){return bq(this.text,{try:e=>JSON.parse(e),catch:e=>new B6({request:this,reason:"Decode",cause:e})})}get urlParamsBody(){return pv(this.text,e=>bc({try:()=>Bh(new URLSearchParams(e)),catch:e=>new B6({request:this,reason:"Decode",cause:e})}))}get multipart(){return this.multipartEffect||(this.multipartEffect=kR(EJ(J6(this.multipartStream)))),this.multipartEffect}get multipartStream(){return U2(UZ(this.stream,e=>new JK({reason:"InternalError",cause:e})),J4(this.headers))}get arrayBuffer(){return this.arrayBufferEffect||(this.arrayBufferEffect=kR(EJ(bU({try:()=>this.source.arrayBuffer(),catch:e=>new B6({request:this,reason:"Decode",cause:e})})))),this.arrayBufferEffect}get upgrade(){return pd(new B6({request:this,reason:"Decode",description:"Not an upgradeable ServerRequest"}))}}let Hr=(e,t)=>pv(J7,Jr(e,t)),Hn=(e,t)=>pv(J7,Jt(e,t)),Hi=e=>new Ht(e,He(e.url)),Hs=e=>{let t=e.headers.host??"localhost",r="https"===e.headers["x-forwarded-proto"]?"https":"http";try{return tR(new URL(e.url,`${r}://${t}`))}catch{return iC()}},Ha=Y(Symbol.for("@effect/platform/HttpApp/preResponseHandlers"),()=>fw(iC())),Ho=e=>Bn({b3:`${e.traceId}-${e.spanId}-${e.sampled?"1":"0"}${"Some"===e.parent._tag?`-${e.parent.value.spanId}`:""}`,traceparent:`00-${e.traceId}-${e.spanId}-${e.sampled?"01":"00"}`}),Hl=e=>{let t=Hf(e);return"Some"===t._tag||"Some"===(t=Hc(e))._tag?t:Hu(e)},Hc=e=>{if(!("b3"in e))return iC();let t=e.b3.split("-");return t.length<2?iC():tR(g3({traceId:t[0],spanId:t[1],sampled:!t[2]||"1"===t[2]}))},Hu=e=>e["x-b3-traceid"]&&e["x-b3-spanid"]?tR(g3({traceId:e["x-b3-traceid"],spanId:e["x-b3-spanid"],sampled:!e["x-b3-sampled"]||"1"===e["x-b3-sampled"]})):iC(),Hh=/^[0-9a-f]{32}$/i,Hp=/^[0-9a-f]{16}$/i,Hf=e=>{if(!e.traceparent)return iC();let t=e.traceparent.split("-");if(4!==t.length)return iC();let[r,n,i,s]=t;return"00"!==r||!1===Hh.test(n)||!1===Hp.test(i)?iC():tR(g3({traceId:n,spanId:i,sampled:(1&parseInt(s,16))==1}))},Hd=Y(Symbol.for("@effect/platform/HttpMiddleware/tracerDisabledWhen"),()=>fw(H)),Hm=t2()("@effect/platform/HttpMiddleware/SpanNameGenerator",{defaultValue:()=>e=>`http.server ${e.method}`}),Hg=e=>h8(t=>{let r=tG(t.currentContext,J7);if(t.getFiberRef(Hd)(r))return e;let n=iR(Hs(r));void 0!==n&&(""!==n.username||""!==n.password)&&(n.username="REDACTED",n.password="REDACTED");let i=t.getFiberRef(Bc),s=Bl(r.headers,i);return bG(tG(t.currentContext,Hm)(r),{parent:iR(Hl(r.headers)),kind:"server",captureStackTrace:!1},t=>{for(let e in t.attribute("http.request.method",r.method),void 0!==n&&(t.attribute("url.full",n.toString()),t.attribute("url.path",n.pathname),""!==n.search.slice(1)&&t.attribute("url.query",n.search.slice(1)),t.attribute("url.scheme",n.protocol.slice(0,-1))),void 0!==r.headers["user-agent"]&&t.attribute("user_agent.original",r.headers["user-agent"]),s)t.attribute(`http.request.header.${e}`,String(s[e]));return"Some"===r.remoteAddress._tag&&t.attribute("client.address",r.remoteAddress.value),pv(pf(bY(e,t)),e=>{let r=B9(e);t.attribute("http.response.status_code",r.status);let n=Bl(r.headers,i);for(let e in n)t.attribute(`http.response.header.${e}`,String(n[e]));return e})})}),Hb=Symbol.for("@effect/platform/HttpApp/handled"),Hx=(e,t,r)=>{let n=ps(h8(r=>pv(e,e=>{let n=tG(r.currentContext,J7),i=r.getFiberRef(Hy);return"None"===i._tag?(n[Hb]=!0,h9(t(n,e),e)):pK(i.value(n,e),e=>(n[Hb]=!0,t(n,e)))})),e=>h8(r=>pv(B7(e),([e,n])=>{let i=tG(r.currentContext,J7),s=r.getFiberRef(Hy);return"None"===s._tag?(i[Hb]=!0,wi(t(i,e),pg(n))):wi(pK(s.value(i,e),e=>(i[Hb]=!0,t(i,e))),pg(n))})));return pG(EG(q(void 0===r?Hg(n):pC(r(Hg(n)),{onFailure:e=>h8(r=>{let n=tG(r.currentContext,J7);return Hb in n?pZ:pC(B7(e),{onFailure:e=>t(n,BU({status:500})),onSuccess:([e])=>t(n,e)})}),onSuccess:e=>h8(r=>{let n=tG(r.currentContext,J7);return Hb in n?pZ:t(n,e)})}))))},Hy=Ha,Hv=e=>{let t=ku(e);return(r,n)=>{let i=Symbol.for("@effect/platform/HttpApp/resolve"),s=Hx(r,(t,r)=>(t[i](BH(r,{withoutBody:"HEAD"===t.method,runtime:e})),pZ),n);return(r,n)=>new Promise(a=>{let o=new Map(e.context.unsafeMap);if(tU(n))for(let[e,t]of n.unsafeMap)o.set(e,t);let l=Hi(r);o.set(J7.key,l),l[i]=a;let c=t(fS(s,fI,tP(o)));r.signal?.addEventListener("abort",()=>{c.unsafeInterruptAsFork(B0)},{once:!0})})}},HS=Symbol.for("@effect/platform/HttpClientError"),H_=e=>ec(e,HS);class Hw extends AW(HS,"RequestError"){get methodAndUrl(){return`${this.request.method} ${this.request.url}`}get message(){return this.description?`${this.reason}: ${this.description} (${this.methodAndUrl})`:`${this.reason} error (${this.methodAndUrl})`}}class Hk extends AW(HS,"ResponseError"){get methodAndUrl(){return`${this.request.method} ${this.request.url}`}get message(){let e=`${this.response.status} ${this.methodAndUrl}`;return this.description?`${this.reason}: ${this.description} (${e})`:`${this.reason} error (${e})`}}let HC=Symbol.for("@effect/platform/HttpClientRequest"),HE={[HC]:HC,...eZ,toJSON(){return{_id:"@effect/platform/HttpClientRequest",method:this.method,url:this.url,urlParams:this.urlParams,hash:this.hash,headers:e4(this.headers),body:this.body.toJSON()}},pipe(){return e6(this,arguments)}};function HO(e,t,r,n,i,s){let a=Object.create(HE);return a.method=e,a.url=t,a.urlParams=r,a.hash=n,a.headers=i,a.body=s,a}let HI=HO("GET","",[],iC(),Bt,BO),HF=e=>(t,r)=>HD(HI,{method:e,url:t,...r??void 0}),HT=HF("GET"),HR=HF("POST"),HN=HF("PUT"),HA=HF("PATCH"),Hj=HF("DELETE"),HM=HF("HEAD"),Hz=HF("OPTIONS"),HD=U(2,(e,t)=>{let r=e;return t.method&&(r=Hq(r,t.method)),t.url&&(r=HB(r,t.url)),t.headers&&(r=HP(r,t.headers)),t.urlParams&&(r=HK(r,t.urlParams)),t.hash&&(r=HW(r,t.hash)),t.body&&(r=HV(r,t.body)),t.accept&&(r=HL(r,t.accept)),t.acceptJson&&(r=HU(r)),r}),H$=U(3,(e,t,r)=>HO(e.method,e.url,e.urlParams,e.hash,Bi(e.headers,t,r),e.body)),HP=U(2,(e,t)=>HO(e.method,e.url,e.urlParams,e.hash,Bs(e.headers,t),e.body)),HL=U(2,(e,t)=>H$(e,"Accept",t)),HU=HL("application/json"),Hq=U(2,(e,t)=>HO(t,e.url,e.urlParams,e.hash,e.headers,e.body)),HB=U(2,(e,t)=>{if("string"==typeof t)return HO(e.method,t,e.urlParams,e.hash,e.headers,e.body);let r=new URL(t.toString()),n=Bh(r.searchParams),i=r.hash?tR(r.hash.slice(1)):iC();return r.search="",r.hash="",HO(e.method,r.toString(),n,i,e.headers,e.body)}),HJ=U(2,(e,t)=>HO(e.method,t.endsWith("/")&&e.url.startsWith("/")?t+e.url.slice(1):t+e.url,e.urlParams,e.hash,e.headers,e.body)),HH=U(3,(e,t,r)=>HO(e.method,e.url,Bd(e.urlParams,t,r),e.hash,e.headers,e.body)),HK=U(2,(e,t)=>HO(e.method,e.url,Bf(e.urlParams,t),e.hash,e.headers,e.body)),HW=U(2,(e,t)=>HO(e.method,e.url,e.urlParams,tR(t),e.headers,e.body)),HV=U(2,(e,t)=>{let r=e.headers;if("Empty"===t._tag||"FormData"===t._tag)r=Bo(r,["Content-type","Content-length"]);else{let e=t.contentType;e&&(r=Bi(r,"content-type",e));let n=t.contentLength;n&&(r=Bi(r,"content-length",n.toString()))}return HO(e.method,e.url,e.urlParams,e.hash,r,t)}),HG=U(2,(e,t)=>pj(BA(t),t=>HV(e,t))),HY=Symbol.for("@effect/platform/HttpClientResponse"),HZ=(e,t)=>new HQ(e,t);class HQ extends eQ{constructor(e,t){super(),this.request=e,this.source=t,this[Je]=Je,this[HY]=HY}toJSON(){return Ji(this,{_id:"@effect/platform/HttpClientResponse",request:this.request.toJSON(),status:this.status})}get status(){return this.source.status}get headers(){return Br(this.source.headers)}get cookies(){return this.cachedCookies?this.cachedCookies:this.cachedCookies=qZ(this.source.headers.getSetCookie())}get remoteAddress(){return iC()}get stream(){return this.source.body?qU(()=>this.source.body,e=>new Hk({request:this.request,response:this,reason:"Decode",cause:e})):UC(new Hk({request:this.request,response:this,reason:"EmptyBody",description:"can not create stream from empty body"}))}get json(){return bq(this.text,{try:e=>""===e?null:JSON.parse(e),catch:e=>new Hk({request:this.request,response:this,reason:"Decode",cause:e})})}get text(){return this.textBody??=bU({try:()=>this.source.text(),catch:e=>new Hk({request:this.request,response:this,reason:"Decode",cause:e})}).pipe(EJ,kR)}get urlParamsBody(){return pv(this.text,e=>bc({try:()=>Bh(new URLSearchParams(e)),catch:e=>new Hk({request:this.request,response:this,reason:"Decode",cause:e})}))}get formData(){return this.formDataBody??=bU({try:()=>this.source.formData(),catch:e=>new Hk({request:this.request,response:this,reason:"Decode",cause:e})}).pipe(EJ,kR)}get arrayBuffer(){return this.arrayBufferBody??=bU({try:()=>this.source.arrayBuffer(),catch:e=>new Hk({request:this.request,response:this,reason:"Decode",cause:e})}).pipe(EJ,kR)}}(e,t)=>pJ(()=>t(e.status)?pB(e):pd(new Hk({response:e,request:e.request,reason:"StatusCode",description:"invalid status code"})));let HX=e=>e.status>=200&&e.status<300?pB(e):pd(new Hk({response:e,request:e.request,reason:"StatusCode",description:"non 2xx status code"})),H0=Symbol.for("@effect/platform/HttpClient"),H1=tQ("@effect/platform/HttpClient"),H2=Y(Symbol.for("@effect/platform/HttpClient/tracerDisabledWhen"),()=>fw(H)),H3=Y(Symbol.for("@effect/platform/HttpClient/currentTracerPropagation"),()=>fw(!0)),H5=t2()("@effect/platform/HttpClient/SpanNameGenerator",{defaultValue:()=>e=>`http.client ${e.method}`}),H4={[H0]:H0,pipe(){return e6(this,arguments)},...eZ,toJSON:()=>({_id:"@effect/platform/HttpClient"}),get(e,t){return this.execute(HT(e,t))},head(e,t){return this.execute(HM(e,t))},post(e,t){return this.execute(HR(e,t))},put(e,t){return this.execute(HN(e,t))},patch(e,t){return this.execute(HA(e,t))},del(e,t){return this.execute(Hj(e,t))},options(e,t){return this.execute(Hz(e,t))}},H6=(e,t)=>{let r=Object.create(H4);return r.preprocess=t,r.postprocess=e,r.execute=function(r){return e(t(r))},r},H8=Y("@effect/platform/HttpClient/responseRegistry",()=>{if("FinalizationRegistry"in globalThis&&globalThis.FinalizationRegistry){let e=new FinalizationRegistry(e=>{e.abort()});return{register(t,r){e.register(t,r,t)},unregister(t){e.unregister(t)}}}let e=new Map;return{register(t,r){e.set(t,setTimeout(()=>r.abort(),5e3))},unregister(t){let r=e.get(t);void 0!==r&&(clearTimeout(r),e.delete(t))}}}),H7=Y("@effect/platform/HttpClient/scopedRequests",()=>new WeakMap);class H9{static{g=HY,b=Je}constructor(e,t){this[g]=HY,this[b]=Je,this.original=e,this.controller=t}applyInterrupt(e){return pJ(()=>(H8.unregister(this.original),p$(e,()=>pH(()=>{this.controller.abort()}))))}get request(){return this.original.request}get status(){return this.original.status}get headers(){return this.original.headers}get cookies(){return this.original.cookies}get remoteAddress(){return this.original.remoteAddress}get formData(){return this.applyInterrupt(this.original.formData)}get text(){return this.applyInterrupt(this.original.text)}get json(){return this.applyInterrupt(this.original.json)}get urlParamsBody(){return this.applyInterrupt(this.original.urlParamsBody)}get arrayBuffer(){return this.applyInterrupt(this.original.arrayBuffer)}get stream(){return qd(()=>(H8.unregister(this.original),U_(this.original.stream,e=>(yj(e)&&this.controller.abort(),pZ))))}toJSON(){return this.original.toJSON()}[eV](){return this.original[eV]()}}let{del:Ke,execute:Kt,get:Kr,head:Kn,options:Ki,patch:Ks,post:Ka,put:Ko}=new Proxy({},{get:(e,t,r)=>(...e)=>pv(H1,r=>r[t](...e))}),Kl=U(2,(e,t)=>H6(r=>t(e.postprocess(r)),e.preprocess)),Kc=U(2,(e,t)=>H6(e.postprocess,r=>pj(e.preprocess(r),t))),Ku=e=>H_(e)&&("RequestError"===e._tag&&"Transport"===e.reason||"ResponseError"===e._tag&&e.response.status>=429),Kh=e=>Kl(e,pv(HX)),Kp=e=>qE(pj(e,e=>e.stream)),Kf=/\+/g,Kd=function(){};Kd.prototype=Object.create(null);let Km=Array.from({length:256},(e,t)=>"%"+((t<16?"0":"")+t.toString(16)).toUpperCase()),Kg=new Int8Array([0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,1,1,1,1,0,0,1,1,0,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,0,1,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,1,0]);function Kb(e){let t=e.length;if(0===t)return"";let r="",n=0,i=0;e:for(;i<t;i++){let s=e.charCodeAt(i);for(;s<128;){if(1!==Kg[s]&&(n<i&&(r+=e.slice(n,i)),n=i+1,r+=Km[s]),++i===t)break e;s=e.charCodeAt(i)}if(n<i&&(r+=e.slice(n,i)),s<2048){n=i+1,r+=Km[192|s>>6]+Km[128|63&s];continue}if(s<55296||s>=57344){n=i+1,r+=Km[224|s>>12]+Km[128|s>>6&63]+Km[128|63&s];continue}if(++i>=t)throw Error("URI malformed");let a=1023&e.charCodeAt(i);n=i+1,r+=Km[240|(s=65536+((1023&s)<<10|a))>>18]+Km[128|s>>12&63]+Km[128|s>>6&63]+Km[128|63&s]}return 0===n?e:n<t?r+e.slice(n):r}let Kx=/^https?:\/\/.*?\//,Ky=/(\/:[^/()]*?)\?(\/?)/;class Kv{constructor(e={}){this.options={ignoreTrailingSlash:!0,ignoreDuplicateSlashes:!0,caseSensitive:!1,maxParamLength:100,...e}}options;routes=[];trees={};on(e,t,r){let n=t.match(Ky);if(n&&void 0!==n.index){KO(t.length===n.index+n[0].length,"Optional Parameter needs to be the last parameter of the path");let i=t.replace(Ky,"$1$2"),s=t.replace(Ky,"$2");this.on(e,i,r),this.on(e,s,r);return}for(let n of(this.options.ignoreDuplicateSlashes&&(t=KI(t)),this.options.ignoreTrailingSlash&&(t=KF(t)),"string"==typeof e?[e]:e))this._on(n,t,r)}all(e,t){this.on(KN,e,t)}_on(e,t,r){void 0===this.trees[e]&&(this.trees[e]=new Kk("/"));let n=t;if("*"===n&&0!==this.trees[e].prefix.length){let t=this.trees[e];this.trees[e]=new Kk(""),this.trees[e].staticChildren["/"]=t}let i=this.trees[e].prefix.length,s=this.trees[e],a=[];for(let e=0;e<=n.length;e++){if(58===n.charCodeAt(e)&&58===n.charCodeAt(e+1)){e++;continue}let t=58===n.charCodeAt(e)&&58!==n.charCodeAt(e+1),r=42===n.charCodeAt(e);if(t||r||e===n.length&&e!==i){let t=n.slice(i,e);this.options.caseSensitive||(t=t.toLowerCase()),t=(t=t.split("::").join(":")).split("%").join("%25"),s=s.createStaticChild(t)}if(t){let t=!1,r=[],l=e+1;for(let c=l;;c++){let u=n.charCodeAt(c),h=40===u,p=45===u||46===u,f=47===u||c===n.length;if(h||p||f){let u=n.slice(l,c);if(a.push(u),t=t||h||p,h){var o;let e=function(e,t){let r=1;for(;t<e.length;){if("\\"===e[++t]){t++;continue}if(")"===e[t]?r--:"("===e[t]&&r++,!r)return t}throw TypeError('Invalid regexp expression in "'+e+'"')}(n,c),t=n.slice(c,e+1);r.push((94===(o=t).charCodeAt(1)&&(o=o.slice(0,1)+o.slice(2)),36===o.charCodeAt(o.length-2)&&(o=o.slice(0,o.length-2)+o.slice(o.length-1)),o)),c=e+1}else r.push("(.*?)");let d=c;for(;c<n.length;c++){let e=n.charCodeAt(c);if(47===e)break;if(58===e)if(58===n.charCodeAt(c+1))c++;else break}let m=n.slice(d,c);if(m&&(m=(m=m.split("::").join(":")).split("%").join("%25"),r.push(m.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"))),l=c+1,f||47===n.charCodeAt(c)||c===n.length){let a=t?"()"+m:m,o=n.slice(e,c);n=n.slice(0,e+1)+a+n.slice(c),e+=a.length;let l=t?RegExp("^"+r.join("")+"$"):void 0;s=s.createParametricChild(l,m,o),i=e+1;break}}}}else if(r&&(a.push("*"),s=s.createWildcardChild(),i=e+1,e!==n.length-1))throw Error("Wildcard must be the last character in the route")}for(let t of(this.options.caseSensitive||(n=n.toLowerCase()),"*"===n&&(n="/*"),this.routes))if(t.method===e&&t.pattern===n)throw Error(`Method '${e}' already declared for route '${n}'`);let l={method:e,path:t,pattern:n,params:a,handler:r};this.routes.push(l),s.addRoute(l)}has(e,t){let r=this.trees[e];if(void 0===r)return!1;let n=r.getStaticChild(t);return void 0!==n&&n.isLeafNode}find(e,t){let r,n,i,s=this.trees[e];if(void 0===s)return;47!==t.charCodeAt(0)&&(t=t.replace(Kx,"/")),this.options.ignoreDuplicateSlashes&&(t=KI(t));try{t=(r=function(e){let t=!1,r=!1,n="";for(let i=1;i<e.length;i++){let s=e.charCodeAt(i);if(37===s){let n=e.charCodeAt(i+1),s=e.charCodeAt(i+2);void 0===KT(n,s)?t=!0:(r=!0,50===n&&53===s&&(t=!0,e=e.slice(0,i+1)+"25"+e.slice(i+1),i+=2),i+=2)}else if(63===s||59===s||35===s){n=e.slice(i+1),e=e.slice(0,i);break}}return{path:t?decodeURI(e):e,querystring:n,shouldDecodeParam:r}}(t)).path,n=r.querystring,i=r.shouldDecodeParam}catch(e){return}this.options.ignoreTrailingSlash&&(t=KF(t));let a=t;!1===this.options.caseSensitive&&(t=t.toLowerCase());let o=this.options.maxParamLength,l=s.prefix.length,c=[],u=t.length,h=[];for(;;){if(l===u&&s.isLeafNode){let e=s.handlerStorage?.find();if(void 0!==e)return{handler:e.handler,params:e.createParams(c),searchParams:function(e){let t=new Kd;if("string"!=typeof e)return t;let r=e.length,n="",i="",s=-1,a=-1,o=!1,l=!1,c=!1,u=!1,h=!1,p=0;for(let f=0;f<r+1;f++)if(38===(p=f!==r?e.charCodeAt(f):38)){if((h=a>s)||(a=f),n=e.slice(s+1,a),h||n.length>0){c&&(n=n.replace(Kf," ")),o&&(n=decodeURIComponent(n)||n),h&&(i=e.slice(a+1,f),u&&(i=i.replace(Kf," ")),l&&(i=decodeURIComponent(i)||i));let r=t[n];void 0===r?t[n]=i:r.pop?r.push(i):t[n]=[r,i]}i="",s=f,a=f,o=!1,l=!1,c=!1,u=!1}else 61===p?a<=s?a=f:l=!0:43===p?a>s?u=!0:c=!0:37===p&&(a>s?l=!0:o=!0);return t}(n)}}let e=s.getNextNode(t,l,h,c.length);if(void 0===e){if(0===h.length)return;let t=h.pop();l=t.brotherPathIndex,c.splice(t.paramsCount),e=t.brotherNode}if("StaticNode"===(s=e)._tag){l+=s.prefix.length;continue}if("WildcardNode"===s._tag){let e=a.slice(l);i&&(e=KR(e)),c.push(e),l=u;continue}if("ParametricNode"===s._tag){let e=a.indexOf("/",l);-1===e&&(e=u);let t=a.slice(l,e);if(i&&(t=KR(t)),void 0!==s.regex){let e=s.regex.exec(t);if(null===e)continue;for(let t=1;t<e.length;t++){let r=e[t];if(r.length>o)return;c.push(r)}}else{if(t.length>o)return;c.push(t)}l=e}}}}class KS{handlers=[];unconstrainedHandler;find(){return this.unconstrainedHandler}add(e){let t={params:e.params,handler:e.handler,createParams:function(e){let t=e.length;return function(r){let n={};for(let i=0;i<t;i++)n[e[i]]=r[i];return n}}(e.params)};this.handlers.push(t),this.unconstrainedHandler=this.handlers[0]}}class K_{isLeafNode=!1;routes;handlerStorage;addRoute(e){void 0===this.routes?this.routes=[e]:this.routes.push(e),void 0===this.handlerStorage&&(this.handlerStorage=new KS),this.isLeafNode=!0,this.handlerStorage.add(e)}}class Kw extends K_{staticChildren={};findStaticMatchingChild(e,t){let r=this.staticChildren[e.charAt(t)];if(void 0!==r&&r.matchPrefix(e,t))return r}getStaticChild(e,t=0){if(e.length===t)return this;let r=this.findStaticMatchingChild(e,t);if(void 0!==r)return r.getStaticChild(e,t+r.prefix.length)}createStaticChild(e){if(0===e.length)return this;let t=this.staticChildren[e.charAt(0)];if(t){let r=1;for(;r<t.prefix.length;r++)if(e.charCodeAt(r)!==t.prefix.charCodeAt(r)){t=t.split(this,r);break}return t.createStaticChild(e.slice(r))}let r=e.charAt(0);return this.staticChildren[r]=new Kk(e),this.staticChildren[r]}}class Kk extends Kw{_tag="StaticNode";constructor(e){super(),this.setPrefix(e)}prefix;matchPrefix;parametricChildren=[];wildcardChild;setPrefix(e){if(this.prefix=e,1===e.length)this.matchPrefix=(e,t)=>!0;else{let t=e.length;this.matchPrefix=function(e,r){for(let n=1;n<t;n++)if(e.charCodeAt(r+n)!==this.prefix.charCodeAt(n))return!1;return!0}}}getParametricChild(e){if(void 0===e)return this.parametricChildren.find(e=>!1===e.isRegex);let t=e.source;return this.parametricChildren.find(e=>void 0!==e.regex&&e.regex.source===t)}createParametricChild(e,t,r){let n=this.getParametricChild(e);return void 0!==n?n.nodePaths.add(r):(n=new KC(e,t,r),this.parametricChildren.push(n),this.parametricChildren.sort((e,t)=>e.isRegex?t.isRegex?void 0===e.staticSuffix?1:void 0===t.staticSuffix?-1:t.staticSuffix.endsWith(e.staticSuffix)?1:e.staticSuffix.endsWith(t.staticSuffix)?-1:0:-1:1)),n}createWildcardChild(){return void 0===this.wildcardChild&&(this.wildcardChild=new KE),this.wildcardChild}split(e,t){let r=this.prefix.slice(0,t),n=this.prefix.slice(t);this.setPrefix(n);let i=new Kk(r);return i.staticChildren[n.charAt(0)]=this,e.staticChildren[r.charAt(0)]=i,i}getNextNode(e,t,r,n){let i=this.findStaticMatchingChild(e,t),s=0;if(void 0===i){if(0===this.parametricChildren.length)return this.wildcardChild;i=this.parametricChildren[0],s=1}void 0!==this.wildcardChild&&r.push({paramsCount:n,brotherPathIndex:t,brotherNode:this.wildcardChild});for(let e=this.parametricChildren.length-1;e>=s;e--)r.push({paramsCount:n,brotherPathIndex:t,brotherNode:this.parametricChildren[e]});return i}}class KC extends Kw{regex;staticSuffix;_tag="ParametricNode";constructor(e,t,r){super(),this.regex=e,this.staticSuffix=t,this.isRegex=!!e,this.nodePaths=new Set([r])}isRegex;nodePaths;getNextNode(e,t){return this.findStaticMatchingChild(e,t)}}class KE extends K_{_tag="WildcardNode";getNextNode(e,t,r,n){}}let KO=(e,t)=>{if(!e)throw Error(t)};function KI(e){return e.replace(/\/\/+/g,"/")}function KF(e){return e.length>1&&47===e.charCodeAt(e.length-1)?e.slice(0,-1):e}function KT(e,t){return 50===e?53===t?"%":51===t?"#":52===t?"$":54===t?"&":66===t||98===t?"+":67===t||99===t?",":70===t||102===t?"/":void 0:51===e?65===t||97===t?":":66===t||98===t?";":68===t||100===t?"=":70===t||102===t?"?":void 0:52===e&&48===t?"@":void 0}function KR(e){let t=e.indexOf("%");if(-1===t)return e;let r="",n=t;for(let i=t;i<e.length;i++)if(37===e.charCodeAt(i)){let t=KT(e.charCodeAt(i+1),e.charCodeAt(i+2));r+=e.slice(n,i)+t,n=i+3}return e.slice(0,t)+r+e.slice(n)}let KN=["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],KA=(e={})=>new Kv(e),Kj=Symbol.for("@effect/platform/Etag/Generator"),KM=tQ("@effect/platform/Etag/Generator"),Kz=e=>{let t="Some"===e.mtime._tag?e.mtime.value.getTime().toString(16):"0";return`${e.size.toString(16)}-${t}`},KD=e=>`${e.size.toString(16)}-${e.lastModified.toString(16)}`,K$=e=>{switch(e._tag){case"Weak":return`W/"${e.value}"`;case"Strong":return`"${e.value}"`}},KP=Symbol.for("@effect/platform/HttpPlatform"),KL=tQ("@effect/platform/HttpPlatform"),KU=tQ("@effect/platform/HttpServer");E=e=>pv(H1,t=>{if("UnixAddress"===e._tag)return pu(Error("HttpServer.layerTestClient: UnixAddress not supported"));let r="0.0.0.0"===e.hostname?"127.0.0.1":e.hostname;return pB(Kc(t,HJ(`http://${r}:${e.port}`)))}),pv(KU,e=>E(e.address));let Kq=Symbol.for("@effect/platform/HttpRouter"),KB=Symbol.for("@effect/platform/HttpRouter/Route"),KJ=Symbol.for("@effect/platform/HttpRouter/RouteContext"),KH=tQ("@effect/platform/HttpRouter/RouteContext"),KK=e=>ec(e,Kq),KW=Y("@effect/platform/HttpRouter/currentRouterConfig",()=>fw({}));class KV extends t6{constructor(e,t){super(),this.routes=e,this.mounts=t,this[Kq]=Kq,this.httpApp=fp(KW).pipe(pv(e=>this.httpApp=KG(this,e)))}commit(){return this.httpApp}toJSON(){return{_id:"Router",routes:this.routes.toJSON(),mounts:this.mounts.toJSON()}}toString(){return eY(this)}[eV](){return this.toJSON()}}let KG=(e,t)=>{let r=KA(t),n=ab(e.mounts).map(([e,t,r])=>[e,new KQ(new KZ("*",r?.includePrefix?`${e}/*`:"/*",t,r?.includePrefix?iC():tR(e),!1),{}),r]),i=n.length;return aN(e.routes,e=>{"*"===e.method?r.all(e.path,e):r.on(e.method,e.path,e)}),h8(e=>{let t=tP(new Map(e.getFiberRef(fI).unsafeMap)),s=tG(t,J7);if(i>0)for(let e=0;e<i;e++){let[r,i,a]=n[e];if(s.url.startsWith(r))return t.unsafeMap.set(KH.key,i),a?.includePrefix!==!0&&t.unsafeMap.set(J7.key,KY(s,r)),fS(pv(i.route.handler,BY),fI,t)}let a=r.find(s.method,s.url);if(void 0===a&&"HEAD"===s.method&&(a=r.find("GET",s.url)),void 0===a)return pd(new B8({request:s}));let o=a.handler;"Some"===o.prefix._tag&&t.unsafeMap.set(J7.key,KY(s,o.prefix.value)),t.unsafeMap.set(J9.key,a.searchParams),t.unsafeMap.set(KH.key,new KQ(o,a.params));let l=tY(t,m1);"Some"===l._tag&&"Span"===l.value._tag&&l.value.attribute("http.route",o.path);let c=pv(o.handler,BY);return fS(o.uninterruptible?c:pN(c),fI,t)})};function KY(e,t){let r=t.length;return e.modify({url:e.url.length<=r?"/":e.url.slice(r)})}class KZ extends eQ{constructor(e,t,r,n=iC(),i=!1){super(),this.method=e,this.path=t,this.handler=r,this.prefix=n,this.uninterruptible=i,this[KB]=KB}toJSON(){return{_id:"@effect/platform/HttpRouter/Route",method:this.method,path:this.path,prefix:this.prefix.toJSON()}}}class KQ{constructor(e,t){this.route=e,this.params=t,this[KJ]=KJ}}let KX=new KV(ap(),ap()),K0=((e,t)=>K0(e,t),(...e)=>new KV(e.reduce((e,t)=>aI(e,t.routes),ap()),e.reduce((e,t)=>aI(e,t.mounts),ap()))),K1=e=>e.endsWith("/")?e.slice(0,-1):e,K2=((e,t)=>(t=K1(t),new KV(aP(e.routes,e=>new KZ(e.method,"/"===e.path?t:t+e.path,e.handler,iI(iM(e.prefix,e=>t+e),()=>tR(t)),e.uninterruptible)),aP(e.mounts,([e,r])=>["/"===e?t:t+e,r]))),e=>U(e=>KK(e[0]),(t,r,n,i)=>new KV(aw(t.routes,new KZ(e,r,n,iC(),i?.uninterruptible??!1)),t.mounts))),K3=K2("GET"),K5=K2("POST"),K4=U(2,(e,t)=>new KV(aP(e.routes,e=>new KZ(e.method,e.path,t(pv(e.handler,BY)),e.prefix,e.uninterruptible)),aP(e.mounts,([e,r])=>[e,t(r)]))),K6=((e,t)=>new KV(aP(e.routes,e=>new KZ(e.method,e.path,t(e.handler),e.prefix,e.uninterruptible)),aP(e.mounts,([e,r])=>[e,pv(t(r),BY)])),(e,t)=>{let r=TJ(e,t);return pv(dP(),e=>{let t=tG(e,J9),n=tG(e,KH);return r({...t,...n.params})})}),K8=Symbol.for("@effect/matcher/Matcher"),K7={[K8]:{_input:q,_filters:q,_remaining:q,_result:q,_return:q},_tag:"TypeMatcher",add(e){return function(e){let t=Object.create(K7);return t.cases=e,t}([...this.cases,e])},pipe(){return e6(this,arguments)}},K9={[K8]:{_input:q,_filters:q,_remaining:q,_result:q,_provided:q,_return:q},_tag:"ValueMatcher",add(e){return"Right"===this.value._tag?this:"When"===e._tag&&!0===e.guard(this.provided)||"Not"===e._tag&&!1===e.guard(this.provided)?We(this.provided,nk(e.evaluate(this.provided))):this},pipe(){return e6(this,arguments)}};function We(e,t){let r=Object.create(K9);return r.provided=e,r.value=t,r}let Wt=(e,t)=>({_tag:"When",guard:e,evaluate:t}),Wr=e=>{if("function"==typeof e)return e;if(Array.isArray(e)){let t=e.map(Wr),r=t.length;return e=>{if(!Array.isArray(e))return!1;for(let n=0;n<r;n++)if(!1===t[n](e[n]))return!1;return!0}}if(null!==e&&"object"==typeof e){let t=Object.entries(e).map(([e,t])=>[e,Wr(t)]),r=t.length;return e=>{if("object"!=typeof e||null===e)return!1;for(let n=0;n<r;n++){let[r,i]=t[n];if(!(r in e)||!1===i(e[r]))return!1}return!0}}return t=>t===e},Wn=e=>t=>{let r=Wt(r=>null!=r&&r[e]in t,r=>t[r[e]](r));return e=>e.add(r)},Wi=(e=>{let t=Wn("_tag")(e);return e=>Wa(t(e))},e=>{if("ValueMatcher"===e._tag)return e.value;let t=e.cases.length;if(1===t){let t=e.cases[0];return e=>"When"===t._tag&&!0===t.guard(e)||"Not"===t._tag&&!1===t.guard(e)?nk(t.evaluate(e)):nw(e)}return r=>{for(let n=0;n<t;n++){let t=e.cases[n];if("When"===t._tag&&!0===t.guard(r)||"Not"===t._tag&&!1===t.guard(r))return nk(t.evaluate(r))}return nw(r)}}),Ws="effect/Match/exhaustive: absurd",Wa=e=>{let t=Wi(e);if(nv(t)){if("Right"===t._tag)return t.right;throw Error(Ws)}return e=>{let r=t(e);if("Right"===r._tag)return r.right;throw Error(Ws)}},Wo=e=>We(e,nw(e)),Wl=(e,t)=>r=>r.add(Wt(Wr(e),t)),Wc=e=>t=>{let r=Wi(t);return nv(r)?"Right"===r._tag?r.right:e(r.left):t=>{let n=r(t);return"Right"===n._tag?n.right:e(n.left)}},Wu=e=>mS(new Map(sT(mJ(e),([e,t])=>[mB(e).join("\uFEFF"),t])),{pathDelim:"\uFEFF",seqDelim:"\uFEFF"}),Wh=U(2,(e,t)=>e.log({fiberId:lX,logLevel:fs,message:t,cause:hr,context:gI(),spans:cb(),annotations:o9(),date:new Date})),Wp=y5,Wf=y6,Wd=jj,Wm=jM,Wg=jz,Wb=y8,Wx=y7,Wy=y9,Wv=ve,WS=_B,W_=_M,Ww=e=>y6(t=>{let r=tG(gD(t.context,m7),mK).unsafe;switch(t.logLevel._tag){case"Debug":return r.debug(e.log(t));case"Info":return r.info(e.log(t));case"Trace":return r.trace(e.log(t));case"Warning":return r.warn(e.log(t));case"Error":case"Fatal":return r.error(e.log(t));default:return r.log(e.log(t))}}),Wk=e=>y6(t=>{tG(gD(t.context,m7),mK).unsafe.error(e.log(t))}),WC={[y5]:y4,log:K,pipe(){return e6(this,arguments)}},WE=jD,WO=j$,WI=jP,WF=jL,WT=vt,WR=e=>vt(()=>e),WN=e=>vt(e),WA=Wh,Wj=jA,WM=e=>y7(e,e=>{let t=iz(gz(e.context,fI),tY(m1));return"None"===t._tag?e:{...e,annotations:W(e.annotations,li("effect.traceId",t.value.traceId),li("effect.spanId",t.value.spanId),"Span"===t.value._tag?li("effect.spanName",t.value.name):q)}}),Wz=vr,WD=vn,W$=vi,WP=_z,WL=vh,WU=vl,Wq=vo,WB=vS,WJ=vk,WH=vc,WK=_U,WW=WO(_z,_D),WV=WO(_z,_$),WG=WO(_z,_P),WY=WO(_z,_L),WZ=e=>k7(wd(_j,e)),WQ=e=>"object"==typeof e&&null!=e&&y5 in e,WX="@effect/platform/FetchHttpClient/Fetch",W0=(e=>H6(t=>pv(t,t=>h8(r=>{let n=H7.get(t),i=n??new AbortController,s=Bm(t.url,t.urlParams,t.hash);if("Left"===s._tag)return pd(new Hw({request:t,reason:"InvalidUrl",cause:s.left}));let a=s.right;if(!r.getFiberRef(fU)||r.getFiberRef(H2)(t)){let s=e(t,a,i.signal,r);return n?s:pY(e=>pC(e(s),{onSuccess:e=>(H8.register(e,i),pB(new H9(e,i))),onFailure:e=>(hp(e)&&i.abort(),pg(e))}))}return bG(tG(r.currentContext,H5)(t),{kind:"client",captureStackTrace:!1},s=>{s.attribute("http.request.method",t.method),s.attribute("server.address",a.origin),""!==a.port&&s.attribute("server.port",+a.port),s.attribute("url.full",a.toString()),s.attribute("url.path",a.pathname),s.attribute("url.scheme",a.protocol.slice(0,-1));let o=a.search.slice(1);""!==o&&s.attribute("url.query",o);let l=r.getFiberRef(Bc),c=Bl(t.headers,l);for(let e in c)s.attribute(`http.request.header.${e}`,String(c[e]));return t=r.getFiberRef(H3)?HP(t,Ho(s)):t,pY(o=>o(e(t,a,i.signal,r)).pipe(bY(s),pC({onSuccess:e=>{s.attribute("http.response.status_code",e.status);let t=Bl(e.headers,l);for(let e in t)s.attribute(`http.response.header.${e}`,String(t[e]));return n?pB(e):(H8.register(e,i),pB(new H9(e,i)))},onFailure:e=>(!n&&hp(e)&&i.abort(),pg(e))})))})})),pB))((e,t,r,n)=>{let i=n.getFiberRef(fI),s=i.unsafeMap.get(WX)??globalThis.fetch,a=i.unsafeMap.get("@effect/platform/FetchHttpClient/FetchOptions")??{},o=a.headers?Ba(Br(a.headers),e.headers):e.headers,l=n=>pj(bU({try:()=>s(t,{...a,method:e.method,headers:o,body:n,duplex:"Stream"===e.body._tag?"half":void 0,signal:r}),catch:t=>new Hw({request:e,reason:"Transport",cause:t})}),t=>HZ(e,t));switch(e.body._tag){case"Raw":case"Uint8Array":return l(e.body.body);case"FormData":return l(e.body.formData);case"Stream":return pv(qw(e.body.stream),l)}return l(void 0)}),W1=(O=pB(W0),kX(H1,pv(dP(),e=>pj(O,t=>Kl(t,dB(t=>tZ(e,t)))))));class W2 extends t1(WX)(){}function W3(e,t){return pv(e.runtimeEffect,e=>h8(r=>(r.setFiberRefs(e.fiberRefs),r.currentRuntimeFlags=e.runtimeFlags,dU(t,e.context))))}let W5={...tx,[ki]:ki,pipe(){return e6(this,arguments)},commit(){return this.runtimeEffect}},W4=(e,t)=>{let r;t=t??kq();let n=kR(wh()),i=h8(i=>(r||(r=kI(pK(wp(Cn(e,t),n),e=>{s.cachedRuntime=e}),{scope:n,scheduler:i.currentScheduler})),pw(r.await))),s=Object.assign(Object.create(W5),{memoMap:t,scope:n,runtimeEffect:i,cachedRuntime:void 0,runtime:()=>void 0===s.cachedRuntime?kF(s.runtimeEffect):Promise.resolve(s.cachedRuntime),dispose:()=>kF(s.disposeEffect),disposeEffect:pJ(()=>(s.runtimeEffect=pu("ManagedRuntime disposed"),s.cachedRuntime=void 0,fG(s.scope,d_))),runFork:(e,t)=>void 0===s.cachedRuntime?kI(W3(s,e),t):ku(s.cachedRuntime)(e,t),runSyncExit:e=>void 0===s.cachedRuntime?kN(W3(s,e)):kv(s.cachedRuntime)(e),runSync:e=>void 0===s.cachedRuntime?kR(W3(s,e)):kp(s.cachedRuntime)(e),runPromiseExit:(e,t)=>void 0===s.cachedRuntime?kT(W3(s,e),t):k_(s.cachedRuntime)(e,t),runCallback:(e,t)=>void 0===s.cachedRuntime?kh(kO)(W3(s,e),t):kh(s.cachedRuntime)(e,t),runPromise:(e,t)=>void 0===s.cachedRuntime?kF(W3(s,e),t):kS(s.cachedRuntime)(e,t)});return s},W6=mv().pipe(mF(()=>mS(new Map(Object.entries(Object.fromEntries(Object.entries({}).filter(e=>null!=e[1])))),{pathDelim:"_"})),mI("uploadthing"),e=>mE(e,mp)),W8=e=>Wu(e??{}).pipe(mF(()=>W6)),W7=(e=>{let t=gf("a boolean property",e=>{switch(e){case"true":case"yes":case"on":case"1":return nk(!0);case"false":case"no":case"off":case"0":return nk(!1);default:return nw(a3([],`Expected a boolean value but received ${e}`))}});return void 0===e?t:gu(t,e)})("isDev").pipe(gh(()=>gm("undefined"!=typeof process?"production":void 0).pipe(go(e=>"development"===e))),gb(!1)),W9=((e,t)=>{let r=F2(t);return gd(e).pipe(gc(e=>r(e).pipe(nO(e=>a3([],Tp.formatIssueSync(e))))))})("token",A$).pipe(bp({ConfigError:e=>new ig({code:"InvalidData"===e._op?"INVALID_SERVER_CONFIG":"MISSING_ENV",message:"InvalidData"===e._op?"Invalid token. A token is a base64 encoded JSON object matching { apiKey: string, appId: string, regions: string[] }.":"Missing token. Please set the `UPLOADTHING_TOKEN` environment variable or provide a token manually through config.",cause:e})}));gd("apiUrl").pipe(gb("https://api.uploadthing.com"),gl(e=>new URL(e)),go(e=>e.href.replace(/\/$/,"")));let Ve=E1(function*(e){let{regions:t,ingestHost:r}=yield*W9,n=e?t.find(t=>t===e)??t[0]:t[0];return yield*gd("ingestUrl").pipe(gb(`https://${n}.${r}`),gl(e=>new URL(e)),go(e=>e.href.replace(/\/$/,"")))});function Vt(e){return{message:e.message}}function Vr(e,t){let r=Object.keys(t)[0];return(r?t[r]?.errorFormatter??Vt:Vt)(e)}gd("utfsHost").pipe(gb("utfs.io")),gd("ufsHost").pipe(gb("ufs.sh")),gy("subdomain","path")("ufsAppIdLocation").pipe(gb("subdomain"));let Vn=(e,t)=>r=>{let n="";return r.pipe(qR(),qD(e=>EH(function*(){let t=(n+=e).split("\n"),r=[];for(let e of t)try{r.push(JSON.parse(e)),n=n.slice(e.length+1)}catch{}return yield*bk("Received chunks").pipe(bo("chunk",e),bo("parsedChunks",r),bo("buf",n)),r})),qD(TJ(Rc(e))),qD(_Y(e=>t(e))),qq,bI("handleJsonLineStream"))},Vi=(e=>{let t=gc(gd(),e=>{let t=fc.find(t=>t._tag===e);return void 0===t?nw(a3([],`Expected a log level but received ${e}`)):nk(t)});return void 0===e?t:gu(t,e)})("logLevel").pipe(gb(fs),pS(e=>WZ(e)),bP(e=>bO("Invalid log level").pipe(bo("error",e))),bh("ConfigError",e=>new ig({code:"INVALID_SERVER_CONFIG",message:"Invalid server configuration",cause:e})),Co),Vs=gy("json","logFmt","structured","pretty")("logFormat"),Va=EH(function*(){let e=yield*W7;return j[yield*Vs.pipe(gb(e?"pretty":"json"))]}).pipe(bh("ConfigError",e=>new ig({code:"INVALID_SERVER_CONFIG",message:"Invalid server configuration",cause:e})),Co),Vo=(e,t)=>{let r=t?.mixin??"json",n=gq(t?.level??"Debug");return t=>pv("None"!==r?t[r]:pZ,()=>EY(n,`${e} (${t.status})`).pipe(bo("response",t)))},Vl=e=>t=>"ResponseError"===t._tag?Vo(e,{level:"Error"})(t.response):bO(e).pipe(bo("error",t));var Vc=class extends gS("ParserError"){message="Input validation failed. The original error with it's validation issues is in the error cause."},Vu=class extends gv{_tag="FileSizeMismatch";name="FileSizeMismatchError";constructor(e,t,r){super({reason:`You uploaded a ${e} file that was ${ih(r)}, but the limit for that type is ${t}`})}},Vh=class extends gv{_tag="FileCountMismatch";name="FileCountMismatchError";constructor(e,t,r,n){super({reason:`You uploaded ${n} file(s) of type '${e}', but the ${t} for that type is ${r}`})}};let Vp=(e,t)=>EH(function*(){let r={};for(let n of e){let e=yield*il(n,ip(t));r[e]=(r[e]??0)+1;let i=t[e]?.maxFileSize;if(!i)return yield*new it(e,"maxFileSize");let s=yield*iu(i);if(n.size>s)return yield*new Vu(e,i,n.size)}for(let e in r){let n=t[e];if(!n)return yield*new it(e);let i=r[e],s=n.minFileCount,a=n.maxFileCount;if(s>a)return yield*new ig({code:"BAD_REQUEST",message:"Invalid config during file count - minFileCount > maxFileCount",cause:`minFileCount must be less than maxFileCount for key ${e}. got: ${s} > ${a}`});if(null!=i&&i<s)return yield*new Vh(e,"minimum",s,i);if(null!=i&&i>a)return yield*new Vh(e,"maximum",a,i)}return null}),Vf=e=>_Y(ip(e),t=>pj(io(e[t].routerConfig),e=>({slug:t,config:e}))),Vd=(e,t)=>W4(Ci(k4(Va,Vi,Cs(W1,Ce(W2,e)),jU(fg(Bc,e=>e.concat(["x-uploadthing-api-key"])))),jq(W8(t)))),Vm=Symbol("uploadthing-region-symbol"),Vg=Symbol("uploadthing-custom-id-symbol");var Vb=class extends t1("uploadthing/AdapterArguments")(){};let Vx=(e,t,r,n)=>{let i=Vd(r.config?.fetch,r.config),s=EK(()=>i.runtime().then(Hv)),a=(...t)=>pj(EK(()=>i.runPromise(Vy(r,n??"custom"))),bj(Vb,e(...t)));return async(...e)=>await s.pipe(EX(a(...e)),EX(t(...e)),bI("requestHandler"),i.runPromise)},Vy=(e,t)=>EH(function*(){let r=yield*W7,n=yield*Vf(e.router),i=e.config?.handleDaemonPromise?e.config.handleDaemonPromise:r?"void":"await";if(r&&"await"===i)return yield*new ig({code:"INVALID_SERVER_CONFIG",message:'handleDaemonPromise: "await" is forbidden in development.'});let s=EH(function*(){return yield*BJ(n)}),a=EH(function*(){let{"uploadthing-hook":r,"x-uploadthing-package":n,"x-uploadthing-version":s}=yield*Hr(RM({"uploadthing-hook":AM.pipe(RF),"x-uploadthing-package":T6.pipe(RT({default:()=>"unknown"})),"x-uploadthing-version":T6.pipe(RT({default:()=>P}))}));s!==P&&(yield*bE("Client version mismatch. Things may not work as expected, please sync your versions to ensure compatibility.").pipe(bo({clientVersion:s,serverVersion:P})));let{slug:a,actionType:o}=yield*K6(RM({actionType:Aj.pipe(RF),slug:T6})),l=e.router[a];if(!l){let e=`No file route found for slug ${a}`;return yield*bO(e),yield*new ig({code:"NOT_FOUND",message:e})}let{body:c,fiber:u}=yield*Wo({actionType:o,uploadthingHook:r}).pipe(Wl({actionType:"upload",uploadthingHook:void 0},()=>Vw({uploadable:l,fePackage:n,beAdapter:t,slug:a})),Wl({actionType:void 0,uploadthingHook:"callback"},()=>VS({uploadable:l,fePackage:n,beAdapter:t})),Wl({actionType:void 0,uploadthingHook:"error"},()=>Vv({uploadable:l})),Wc(()=>pB({body:null,fiber:null})));return u&&(yield*bk("Running fiber as daemon").pipe(bo("handleDaemon",i)),"void"===i||("await"===i?yield*u.await:"function"==typeof i&&i(kF(u.await)))),yield*bk("Sending response").pipe(bo("body",c)),yield*BJ(c)}).pipe(bp({ParseError:t=>BJ(Vr(new ig({code:"BAD_REQUEST",message:"Invalid input",cause:t.message}),e.router),{status:400}),UploadThingError:t=>BJ(Vr(t,e.router),{status:im[t.code]})})),o=pj(BB("x-uploadthing-version",P));return KX.pipe(K3("*",s),K5("*",a),K4(o))}).pipe(bI("createRequestHandler")),Vv=e=>EH(function*(){let{uploadable:t}=e,r=yield*J7,{apiKey:n}=yield*W9,i=yield*iS((yield*r.text),r.headers["x-uploadthing-signature"]??null,n);if(yield*bk(`Signature verified: ${i}`),!i)return yield*bO("Invalid signature"),yield*new ig({code:"BAD_REQUEST",message:"Invalid signature"});let s=yield*Hn(RM({fileKey:T6,error:T6}));yield*bk("Handling error callback request with input:").pipe(bo("json",s));let a=yield*Vb;return{body:null,fiber:yield*bU({try:async()=>t.onUploadError({...a,error:new ig({code:"UPLOAD_FAILED",message:`Upload failed for ${s.fileKey}: ${s.error}`}),fileKey:s.fileKey}),catch:e=>new ig({code:"INTERNAL_SERVER_ERROR",message:"Failed to run onUploadError",cause:e})}).pipe(bP(e=>bO("Failed to run onUploadError. You probably shouldn't be throwing errors here.").pipe(bo("error",e)))).pipe(EV,_0)}}).pipe(bI("handleErrorRequest")),VS=e=>EH(function*(){let{uploadable:t,fePackage:r,beAdapter:n}=e,i=yield*J7,{apiKey:s}=yield*W9,a=yield*iS((yield*i.text),i.headers["x-uploadthing-signature"]??null,s);if(yield*bk(`Signature verified: ${a}`),!a)return yield*bO("Invalid signature"),yield*new ig({code:"BAD_REQUEST",message:"Invalid signature"});let o=yield*Hn(RM({status:T6,file:AU,origin:T6,metadata:R$({key:T6,value:T3})}));return yield*bk("Handling callback request with input:").pipe(bo("json",o)),{body:null,fiber:yield*EH(function*(){let e=yield*Vb,i=yield*bU({try:async()=>t.onUploadComplete({...e,file:{...o.file,get url(){return L("`file.url` is deprecated and will be removed in uploadthing v9. Use `file.ufsUrl` instead."),o.file.url},get appUrl(){return L("`file.appUrl` is deprecated and will be removed in uploadthing v9. Use `file.ufsUrl` instead."),o.file.appUrl}},metadata:o.metadata}),catch:e=>new ig({code:"INTERNAL_SERVER_ERROR",message:"Failed to run onUploadComplete. You probably shouldn't be throwing errors here.",cause:e})}),a={fileKey:o.file.key,callbackData:i??null};yield*bk("'onUploadComplete' callback finished. Sending response to UploadThing:").pipe(bo("callbackData",a));let l=(yield*H1).pipe(Kh);yield*HR("/callback-result").pipe(HJ(o.origin),HP({"x-uploadthing-api-key":n8(s),"x-uploadthing-version":P,"x-uploadthing-be-adapter":n,"x-uploadthing-fe-package":r}),HG(a),pv(l.execute),bP(Vl("Failed to register callback result")),pv(Jt(AJ)),pK(bw("Sent callback result to UploadThing")),EG)}).pipe(EV,_0)}}).pipe(bI("handleCallbackRequest")),V_=e=>EH(function*(){let{json:{files:t,input:r},uploadable:n}=e;yield*bk("Running middleware");let i=yield*Vb,s=yield*bU({try:async()=>n.middleware({...i,input:r,files:t}),catch:e=>e instanceof ig?e:new ig({code:"INTERNAL_SERVER_ERROR",message:"Failed to run middleware",cause:e})});if(s[Vg]&&s[Vg].length!==t.length){let e=`Expected files override to have the same length as original files, got ${s[Vg].length} but expected ${t.length}`;return yield*bO(e),yield*new ig({code:"BAD_REQUEST",message:"Files override must have the same length as files",cause:e})}let a=yield*_Y(t,(e,t)=>EH(function*(){let r=s[Vg]?.[t];return r&&r.size!==e.size&&(yield*bE("File size mismatch. Reverting to original size")),{name:r?.name??e.name,size:e.size,type:e.type,customId:r?.customId,lastModified:r?.lastModified??Date.now()}}));return{metadata:s,filesWithCustomIds:a,preferredRegion:s[Vm]}}).pipe(bI("runRouteMiddleware")),Vw=e=>EH(function*(){let t=(yield*H1).pipe(Kh),{uploadable:r,fePackage:n,beAdapter:i,slug:s}=e,a=yield*Hn(AH);yield*bk("Handling upload request").pipe(bo("json",a)),yield*bk("Parsing user input");let o=yield*bU({try:()=>(function(e){if("parseAsync"in e&&"function"==typeof e.parseAsync)return e.parseAsync;if(TK(e))return t=>TH(e)(t).catch(e=>{throw new Vc({cause:gk(e[kg])})});if("~standard"in e)return async t=>{let r=await e["~standard"].validate(t);if(r.issues)throw new Vc({cause:r.issues});return r.value};throw Error("Invalid parser")})(r.inputParser)(a.input),catch:e=>new ig({code:"BAD_REQUEST",message:"Invalid input",cause:e})});yield*bk("Input parsed successfully").pipe(bo("input",o));let{metadata:l,filesWithCustomIds:c,preferredRegion:u}=yield*V_({json:{input:o,files:a.files},uploadable:r});yield*bk("Parsing route config").pipe(bo("routerConfig",r.routerConfig));let h=yield*io(r.routerConfig).pipe(bh("InvalidRouteConfig",e=>new ig({code:"BAD_REQUEST",message:"Invalid route config",cause:e})));yield*bk("Route config parsed successfully").pipe(bo("routeConfig",h)),yield*bk("Validating files meet the config requirements").pipe(bo("files",a.files)),yield*Vp(a.files,h).pipe(pz(e=>new ig({code:"BAD_REQUEST",message:`Invalid config: ${e._tag}`,cause:"reason"in e?e.reason:e.message}))),yield*bk("Files validated.");let p=yield*_Y(c,e=>pj(il(e,ip(h)),t=>({name:e.name,size:e.size,type:e.type||t,lastModified:e.lastModified,customId:e.customId,contentDisposition:h[t]?.contentDisposition??"inline",acl:h[t]?.acl}))).pipe(bp({InvalidFileType:e=>pu(e),UnknownFileType:e=>pu(e)})),f=r.routeOptions,{apiKey:d,appId:m}=yield*W9,g=yield*Ve(u),b=yield*W7;yield*bk("Generating presigned URLs").pipe(bo("fileUploadRequests",p),bo("ingestUrl",g));let x=yield*_Y(p,e=>EH(function*(){let t=yield*i_(e,m,f.getFileHashParts);return{url:yield*iw(`${g}/${t}`,d,{ttlInSeconds:f.presignedURLTTL,data:{"x-ut-identifier":m,"x-ut-file-name":e.name,"x-ut-file-size":e.size,"x-ut-file-type":e.type,"x-ut-slug":s,"x-ut-custom-id":e.customId,"x-ut-content-disposition":e.contentDisposition,"x-ut-acl":e.acl}}),key:t}}),{concurrency:"unbounded"}),y=yield*J7,v=yield*Hs(y),S=yield*gd("callbackUrl").pipe(gb(v.origin+v.pathname),pj(e=>HR(e).pipe(HH("slug",s)))),_=HR("/route-metadata").pipe(HJ(g),HP({"x-uploadthing-api-key":n8(d),"x-uploadthing-version":P,"x-uploadthing-be-adapter":i,"x-uploadthing-fe-package":n}),HG({fileKeys:x.map(({key:e})=>e),metadata:l,isDev:b,callbackUrl:S.url,callbackSlug:s,awaitServerData:f.awaitServerData??!0}),pv(t.execute)),w=E1("handleDevStreamError")(function*(e,r){let s=Nl(RM({file:AU})),a=(yield*TJ(s)(r)).file.key;yield*bO("Failed to forward callback request from dev stream").pipe(bo({fileKey:a,error:e.message}));let o=yield*HR("/callback-result").pipe(HJ(g),HP({"x-uploadthing-api-key":n8(d),"x-uploadthing-version":P,"x-uploadthing-be-adapter":i,"x-uploadthing-fe-package":n}),HG({fileKey:a,error:`Failed to forward callback request from dev stream: ${e.message}`}),pv(t.execute));yield*Vo("Reported callback error to UploadThing")(o)}),k=yield*pF(b,{onTrue:()=>_.pipe(b$({onSuccess:Vo("Registered metadata",{mixin:"None"}),onFailure:Vl("Failed to register metadata")}),Kp,Vn(Aq,e=>S.pipe(HP({"uploadthing-hook":e.hook,"x-uploadthing-signature":e.signature}),HV(BR(e.payload,"application/json")),t.execute,pK(Vo("Successfully forwarded callback request from dev stream")),bh("ResponseError",t=>w(t,e.payload)),bo(e),pe,EV,EG))),onFalse:()=>_.pipe(b$({onSuccess:Vo("Registered metadata"),onFailure:Vl("Failed to register metadata")}),pv(Jt(AB)),EG)}).pipe(_0),C=x.map((e,t)=>({url:e.url,key:e.key,name:p[t].name,customId:p[t].customId??null}));return yield*bC("Sending presigned URLs to client").pipe(bo("presignedUrls",C)),{body:C,fiber:k}}).pipe(bI("handleUploadAction"));var Vk=r(52335);let VC=(e=>(t,r)=>(function e(t={}){let r={$types:{},routerConfig:{image:{maxFileSize:"4MB"}},routeOptions:{awaitServerData:!0},inputParser:{parseAsync:()=>Promise.resolve(void 0),_input:void 0,_output:void 0},middleware:()=>({}),onUploadError:()=>{},onUploadComplete:()=>void 0,errorFormatter:t.errorFormatter??Vt,...t};return{input:t=>e({...r,inputParser:t}),middleware:t=>e({...r,middleware:t}),onUploadComplete:e=>({...r,onUploadComplete:e}),onUploadError:t=>e({...r,onUploadError:t})}})({routerConfig:t,routeOptions:r??{},...e}))(),{GET:VE,POST:VO}=(e=>{let t=Vx(e=>pB({req:e}),e=>pB(e),e,"nextjs-app");return{POST:t,GET:t}})({router:{imageUploader:VC({image:{maxFileSize:"4MB",maxFileCount:1}}).middleware(async({req:e})=>{let t=await (0,Vk.j2)();if(!t)throw new ig("Unauthorized");return{userId:t.user?.id}}).onUploadComplete(async({metadata:e,file:t})=>(console.log("Upload complete for userId:",e.userId),console.log("file url",t.url),{uploadedBy:e.userId})),mediaUploader:VC({image:{maxFileSize:"4MB",maxFileCount:1},pdf:{maxFileSize:"4MB",maxFileCount:1}}).middleware(async({req:e})=>{let t=await (0,Vk.j2)();if(!t)throw new ig("Unauthorized");return{userId:t.user?.id}}).onUploadComplete(async({metadata:e,file:t})=>(console.log("Upload complete for userId:",e.userId),console.log("file url",t.url),{uploadedBy:e.userId}))}}),VI=new z.AppRouteRouteModule({definition:{kind:D.RouteKind.APP_ROUTE,page:"/api/uploadthing/route",pathname:"/api/uploadthing",filename:"route",bundlePath:"app/api/uploadthing/route"},resolvedPagePath:"/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/api/uploadthing/route.ts",nextConfigOutput:"",userland:M}),{workAsyncStorage:VF,workUnitAsyncStorage:VT,serverHooks:VR}=VI;function VN(){return(0,$.patchFetch)({workAsyncStorage:VF,workUnitAsyncStorage:VT})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[191,4235,2543,5208,8814],()=>r(87343));module.exports=n})();