{"name": "@bonkai/api", "version": "0.0.1", "private": true, "type": "module", "scripts": {"dev": "bun run --watch src/index.ts", "build": "bun build src/index.ts --outdir dist --target node", "start": "bun run dist/index.js", "lint": "biome lint --write --unsafe", "format": "biome format --write"}, "dependencies": {"hono": "^4.5.8", "@hono/node-server": "^1.12.2", "ws": "^8.18.0", "@bonkai/auth": "workspace:*", "@bonkai/database": "workspace:*", "@bonkai/blockchain": "workspace:*", "@bonkai/types": "workspace:*"}, "devDependencies": {"@types/ws": "^8.5.10"}}