import { auth, clerkClient } from '@clerk/nextjs/server';
import { NextResponse } from 'next/server';

export async function POST() {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Remove wallet address from user's public metadata
    await clerkClient.users.updateUser(userId, {
      publicMetadata: {
        walletAddress: null,
        walletLinkedAt: null,
      },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error unlinking wallet:', error);
    return NextResponse.json(
      { error: 'Failed to unlink wallet' },
      { status: 500 },
    );
  }
}
