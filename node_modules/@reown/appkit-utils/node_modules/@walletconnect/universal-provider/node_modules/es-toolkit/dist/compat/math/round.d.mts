/**
 * Computes number rounded to precision.
 *
 * @param {number | string} number  The number to round.
 * @param {number | string} precision The precision to round to.
 * @returns {number} Returns the rounded number.
 *
 * @example
 * round(4.006); // => 4
 * round(4.006, 2); // => 4.01
 * round(4060, -2); // => 4100
 */
declare function round(number: number | string, precision?: number | string): number;

export { round };
