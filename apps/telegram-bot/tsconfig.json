{"extends": "../../tsconfig.base.json", "compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "bundler", "outDir": "dist", "noEmit": false, "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "types": ["node"], "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/convex/*": ["../../convex/*"], "@bonkai/ai": ["../../packages/ai"], "@bonkai/auth": ["../../packages/auth"], "@bonkai/blockchain": ["../../packages/blockchain"], "@bonkai/types": ["../../packages/types"], "@bonkai/ui": ["../../packages/ui"]}}, "include": ["src/**/*", "../../convex/**/*"], "exclude": ["node_modules", "dist"]}