(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2678],{8387:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return d}});let a=r(44995),n=r(41987),s=r(57720),l=r(77849),i=r(9330),o=r(87533),u=r(51509),c=r(43781);function d(e){let{replace:t,scroll:r,prefetch:d,ref:f,...m}=e,p=(0,n.useContext)(i.AppRouterContext),x=m.action,h="string"==typeof x;for(let e of u.DISALLOWED_FORM_PROPS)e in m&&delete m[e];let b=!!p&&h&&null===(!1===d||null===d?d:null),y=(0,n.useCallback)(e=>(b&&null!==p&&(0,c.mountFormInstance)(e,x,p,o.PrefetchKind.AUTO),()=>{(0,c.unmountPrefetchableInstance)(e)}),[b,x,p]),g=(0,l.useMergedRef)(y,null!=f?f:null);if(!h)return(0,a.jsx)("form",{...m,ref:g});let v=(0,s.addBasePath)(x);return(0,a.jsx)("form",{...m,ref:g,action:v,onSubmit:e=>(function(e,t){let{actionHref:r,onSubmit:a,replace:n,scroll:s,router:l}=t;if("function"==typeof a&&(a(e),e.defaultPrevented)||!l)return;let i=e.currentTarget,o=e.nativeEvent.submitter,c=r;if(o){if((0,u.hasUnsupportedSubmitterAttributes)(o)||(0,u.hasReactClientActionAttributes)(o))return;let e=o.getAttribute("formAction");null!==e&&(c=e)}let d=(0,u.createFormSubmitDestinationUrl)(c,i);e.preventDefault();let f=d.href;l[n?"replace":"push"](f,{scroll:s})})(e,{router:p,actionHref:v,replace:t,scroll:r,onSubmit:m.onSubmit})})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},11186:(e,t,r)=>{"use strict";r.d(t,{b:()=>i});var a=r(44995),n=r(9604),s=r(34109),l=r(22710);function i(e){let{children:t,isSuccessful:r}=e,{pending:i}=(0,n.useFormStatus)();return(0,a.jsxs)(l.$,{type:i?"button":"submit","aria-disabled":i||r,disabled:i||r,className:"relative",children:[t,(i||r)&&(0,a.jsx)("span",{className:"animate-spin absolute right-4",children:(0,a.jsx)(s.hz,{})}),(0,a.jsx)("output",{"aria-live":"polite",className:"sr-only",children:i||r?"Loading":"Submit form"})]})}},40458:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var a=r(44995),n=r(27261),s=r.n(n),l=r(62942),i=r(41987),o=r(67455),u=r(11186),c=r(25316);let d=(0,c.createServerReference)("7ff244110ce9ec9f8e153fa4ff7837dc9cb443ebe3",c.callServer,void 0,c.findSourceMapURL,"register");var f=r(18654),m=r(6808);function p(){let e=(0,l.useRouter)(),[t,r]=(0,i.useState)(""),[n,c]=(0,i.useState)(!1),[p,x]=(0,i.useActionState)(d,{status:"idle"}),{update:h}=(0,m.wV)();return(0,i.useEffect)(()=>{"user_exists"===p.status?(0,f.o)({type:"error",description:"Account already exists!"}):"failed"===p.status?(0,f.o)({type:"error",description:"Failed to create account!"}):"invalid_data"===p.status?(0,f.o)({type:"error",description:"Failed validating your submission!"}):"success"===p.status&&((0,f.o)({type:"success",description:"Account created successfully!"}),c(!0),h(),e.refresh())},[p]),(0,a.jsx)("div",{className:"flex h-dvh w-screen items-start pt-12 md:pt-0 md:items-center justify-center bg-background",children:(0,a.jsxs)("div",{className:"w-full max-w-md overflow-hidden rounded-2xl gap-12 flex flex-col",children:[(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center gap-2 px-4 text-center sm:px-16",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold dark:text-zinc-50",children:"Sign Up"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-zinc-400",children:"Create an account with your email and password"})]}),(0,a.jsxs)(o.T,{action:e=>{r(e.get("email")),x(e)},defaultEmail:t,children:[(0,a.jsx)(u.b,{isSuccessful:n,children:"Sign Up"}),(0,a.jsxs)("p",{className:"text-center text-sm text-gray-600 mt-4 dark:text-zinc-400",children:["Already have an account? ",(0,a.jsx)(s(),{href:"/login",className:"font-semibold text-gray-800 hover:underline dark:text-zinc-200",children:"Sign in"})," instead."]})]})]})})}},51509:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DISALLOWED_FORM_PROPS:function(){return r},checkFormActionUrl:function(){return n},createFormSubmitDestinationUrl:function(){return a},hasReactClientActionAttributes:function(){return u},hasUnsupportedSubmitterAttributes:function(){return o},isSupportedFormEncType:function(){return s},isSupportedFormMethod:function(){return l},isSupportedFormTarget:function(){return i}});let r=["method","encType","target"];function a(e,t){let r;try{let t=window.location.href;r=new URL(e,t)}catch(t){throw Object.defineProperty(Error('Cannot parse form action "'+e+'" as a URL',{cause:t}),"__NEXT_ERROR_CODE",{value:"E152",enumerable:!1,configurable:!0})}for(let[e,a]of(r.searchParams.size&&(r.search=""),new FormData(t)))"string"!=typeof a&&(a=a.name),r.searchParams.append(e,a);return r}function n(e,t){let r,a="action"===t?"an `action`":"a `formAction`";try{r=new URL(e,"http://n")}catch(t){console.error("<Form> received "+a+' that cannot be parsed as a URL: "'+e+'".');return}r.searchParams.size&&console.warn("<Form> received "+a+' that contains search params: "'+e+'". This is not supported, and they will be ignored. If you need to pass in additional search params, use an `<input type="hidden" />` instead.')}let s=e=>"application/x-www-form-urlencoded"===e,l=e=>"get"===e,i=e=>"_self"===e;function o(e){let t=e.getAttribute("formEncType");if(null!==t&&!s(t))return!0;let r=e.getAttribute("formMethod");if(null!==r&&!l(r))return!0;let a=e.getAttribute("formTarget");return!(null===a||i(a))}function u(e){let t=e.getAttribute("formAction");return t&&/\s*javascript:/i.test(t)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},67455:(e,t,r)=>{"use strict";r.d(t,{T:()=>p});var a=r(44995),n=r(8387),s=r.n(n),l=r(25118),i=r(41987),o=r(7156),u=i.forwardRef((e,t)=>(0,a.jsx)(o.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));u.displayName="Label";var c=r(615),d=r(99749);let f=(0,c.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),m=i.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(u,{ref:t,className:(0,d.cn)(f(),r),...n})});function p(e){let{action:t,children:r,defaultEmail:n=""}=e;return(0,a.jsxs)(s(),{action:t,className:"flex flex-col gap-4 px-4 sm:px-16",children:[(0,a.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,a.jsx)(m,{htmlFor:"email",className:"text-zinc-600 font-normal dark:text-zinc-400",children:"Email Address"}),(0,a.jsx)(l.p,{id:"email",name:"email",className:"bg-muted text-md md:text-sm",type:"email",placeholder:"<EMAIL>",autoComplete:"email",required:!0,autoFocus:!0,defaultValue:n})]}),(0,a.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,a.jsx)(m,{htmlFor:"password",className:"text-zinc-600 font-normal dark:text-zinc-400",children:"Password"}),(0,a.jsx)(l.p,{id:"password",name:"password",className:"bg-muted text-md md:text-sm",type:"password",required:!0})]}),r]})}m.displayName=u.displayName},85753:(e,t,r)=>{Promise.resolve().then(r.bind(r,40458))}},e=>{var t=t=>e(e.s=t);e.O(0,[9718,2106,6448,6808,5082,1149,1166,7358],()=>t(85753)),_N_E=e.O()}]);