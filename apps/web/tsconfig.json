{"extends": "../../tsconfig.base.json", "compilerOptions": {"plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./*"], "@/components/*": ["./components/*"], "@/lib/*": ["./lib/*"], "@/hooks/*": ["./hooks/*"], "@/app/*": ["./app/*"], "@bonkai/ai": ["../../packages/ai"], "@bonkai/auth": ["../../packages/auth"], "@bonkai/blockchain": ["../../packages/blockchain"], "@bonkai/types": ["../../packages/types"], "@bonkai/ui": ["../../packages/ui"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}