'use client';

import { useState, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  ArrowUpRight, 
  ArrowDownRight, 
  Clock, 
  Search, 
  Filter,
  ExternalLink,
  Copy,
  Calendar,
  TrendingUp,
  TrendingDown,
  RefreshCw
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

interface Transaction {
  id: string;
  txHash: string;
  type: 'send' | 'receive' | 'stake' | 'unstake' | 'claim' | 'swap';
  status: 'pending' | 'confirmed' | 'failed';
  amount: string;
  token: string;
  toAddress?: string;
  fromAddress?: string;
  timestamp: string;
  fee?: string;
  blockHeight?: number;
}

interface TransactionHistoryProps {
  transactions: Transaction[];
  isLoading?: boolean;
  onRefresh?: () => void;
  onLoadMore?: () => void;
  hasMore?: boolean;
  className?: string;
}

const transactionTypeConfig = {
  send: {
    label: 'Send',
    icon: ArrowUpRight,
    color: 'text-red-600 dark:text-red-400',
    bgColor: 'bg-red-50 dark:bg-red-950/20',
  },
  receive: {
    label: 'Receive',
    icon: ArrowDownRight,
    color: 'text-green-600 dark:text-green-400',
    bgColor: 'bg-green-50 dark:bg-green-950/20',
  },
  stake: {
    label: 'Stake',
    icon: TrendingUp,
    color: 'text-blue-600 dark:text-blue-400',
    bgColor: 'bg-blue-50 dark:bg-blue-950/20',
  },
  unstake: {
    label: 'Unstake',
    icon: TrendingDown,
    color: 'text-orange-600 dark:text-orange-400',
    bgColor: 'bg-orange-50 dark:bg-orange-950/20',
  },
  claim: {
    label: 'Claim',
    icon: Calendar,
    color: 'text-purple-600 dark:text-purple-400',
    bgColor: 'bg-purple-50 dark:bg-purple-950/20',
  },
  swap: {
    label: 'Swap',
    icon: RefreshCw,
    color: 'text-indigo-600 dark:text-indigo-400',
    bgColor: 'bg-indigo-50 dark:bg-indigo-950/20',
  },
};

const statusConfig = {
  pending: { label: 'Pending', color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' },
  confirmed: { label: 'Confirmed', color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' },
  failed: { label: 'Failed', color: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' },
};

const formatAmount = (amount: string, token: string) => {
  const num = parseFloat(amount);
  const formatted = num >= 1000000 
    ? `${(num / 1000000).toFixed(2)}M` 
    : num >= 1000 
    ? `${(num / 1000).toFixed(2)}K` 
    : num.toLocaleString();
  return `${formatted} ${token}`;
};

const formatAddress = (address: string) => {
  return `${address.slice(0, 4)}...${address.slice(-4)}`;
};

const formatDate = (timestamp: string) => {
  const date = new Date(timestamp);
  return date.toLocaleDateString('en-US', { 
    month: 'short', 
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

export function TransactionHistory({ 
  transactions, 
  isLoading = false,
  onRefresh,
  onLoadMore,
  hasMore = false,
  className 
}: TransactionHistoryProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<string>('all');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [selectedTab, setSelectedTab] = useState('all');

  const filteredTransactions = useMemo(() => {
    return transactions.filter(tx => {
      const matchesSearch = 
        tx.txHash.toLowerCase().includes(searchTerm.toLowerCase()) ||
        tx.token.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (tx.toAddress && tx.toAddress.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (tx.fromAddress && tx.fromAddress.toLowerCase().includes(searchTerm.toLowerCase()));

      const matchesType = filterType === 'all' || tx.type === filterType;
      const matchesStatus = filterStatus === 'all' || tx.status === filterStatus;
      const matchesTab = selectedTab === 'all' || tx.type === selectedTab;

      return matchesSearch && matchesType && matchesStatus && matchesTab;
    });
  }, [transactions, searchTerm, filterType, filterStatus, selectedTab]);

  const copyTxHash = (txHash: string) => {
    navigator.clipboard.writeText(txHash);
    toast.success('Transaction hash copied to clipboard');
  };

  const openInExplorer = (txHash: string) => {
    window.open(`https://explorer.solana.com/tx/${txHash}`, '_blank');
  };

  const TransactionRow = ({ transaction }: { transaction: Transaction }) => {
    const typeConfig = transactionTypeConfig[transaction.type];
    const statusStyle = statusConfig[transaction.status];
    const TypeIcon = typeConfig.icon;

    return (
      <div className="flex items-center justify-between p-4 border-b last:border-b-0 hover:bg-muted/50 transition-colors">
        <div className="flex items-center gap-3">
          <div className={cn('size-10 rounded-full flex items-center justify-center', typeConfig.bgColor)}>
            <TypeIcon className={cn('size-5', typeConfig.color)} />
          </div>
          
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <span className="font-medium">{typeConfig.label}</span>
              <Badge className={statusStyle.color}>
                {statusStyle.label}
              </Badge>
            </div>
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <span>{formatDate(transaction.timestamp)}</span>
              {transaction.blockHeight && (
                <>
                  <span>•</span>
                  <span>Block {transaction.blockHeight.toLocaleString()}</span>
                </>
              )}
            </div>
          </div>
        </div>

        <div className="text-right space-y-1">
          <p className="font-medium">
            {transaction.type === 'send' ? '-' : '+'}
            {formatAmount(transaction.amount, transaction.token)}
          </p>
          {transaction.fee && (
            <p className="text-xs text-muted-foreground">
              Fee: {transaction.fee} SOL
            </p>
          )}
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => copyTxHash(transaction.txHash)}
              className="size-6 p-0"
            >
              <Copy className="size-3" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => openInExplorer(transaction.txHash)}
              className="size-6 p-0"
            >
              <ExternalLink className="size-3" />
            </Button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <Card className={cn('w-full', className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Clock className="size-5" />
              Transaction History
            </CardTitle>
            <CardDescription>
              View your recent blockchain transactions
            </CardDescription>
          </div>
          {onRefresh && (
            <Button variant="outline" size="sm" onClick={onRefresh}>
              <RefreshCw className="size-4 mr-2" />
              Refresh
            </Button>
          )}
        </div>

        {/* Filters */}
        <div className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-3">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 size-4 text-muted-foreground" />
              <Input
                placeholder="Search by hash, token, or address..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex gap-2">
              <Select value={filterType} onValueChange={setFilterType}>
                <SelectTrigger className="w-32">
                  <Filter className="size-4 mr-2" />
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="send">Send</SelectItem>
                  <SelectItem value="receive">Receive</SelectItem>
                  <SelectItem value="stake">Stake</SelectItem>
                  <SelectItem value="unstake">Unstake</SelectItem>
                  <SelectItem value="claim">Claim</SelectItem>
                  <SelectItem value="swap">Swap</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={filterStatus} onValueChange={setFilterStatus}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="confirmed">Confirmed</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="failed">Failed</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Quick Filter Tabs */}
          <Tabs value={selectedTab} onValueChange={setSelectedTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="all">All</TabsTrigger>
              <TabsTrigger value="send">Outgoing</TabsTrigger>
              <TabsTrigger value="receive">Incoming</TabsTrigger>
              <TabsTrigger value="stake">Staking</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </CardHeader>

      <CardContent className="p-0">
        {isLoading ? (
          <div className="p-8 text-center">
            <RefreshCw className="size-8 mx-auto animate-spin text-muted-foreground mb-3" />
            <p className="text-sm text-muted-foreground">Loading transactions...</p>
          </div>
        ) : filteredTransactions.length === 0 ? (
          <div className="p-8 text-center">
            <Clock className="size-12 mx-auto text-muted-foreground/50 mb-3" />
            <p className="text-sm text-muted-foreground">
              {transactions.length === 0 
                ? 'No transactions found' 
                : 'No transactions match your filters'
              }
            </p>
          </div>
        ) : (
          <>
            <div className="max-h-96 overflow-y-auto">
              {filteredTransactions.map((transaction) => (
                <TransactionRow key={transaction.id} transaction={transaction} />
              ))}
            </div>
            
            {hasMore && onLoadMore && (
              <div className="p-4 border-t">
                <Button variant="outline" onClick={onLoadMore} className="w-full">
                  Load More Transactions
                </Button>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
}