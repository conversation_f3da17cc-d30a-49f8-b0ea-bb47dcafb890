(()=>{var e={};e.id=4362,e.ids=[4362],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},7459:(e,t,n)=>{"use strict";n.r(t),n.d(t,{patchFetch:()=>y,routeModule:()=>p,serverHooks:()=>h,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>m});var a={};n.r(a),n.d(a,{GET:()=>l,PATCH:()=>d});var r=n(48106),s=n(48819),o=n(12050),i=n(52335),u=n(11455),c=n(56617);async function l(e){let{searchParams:t}=new URL(e.url),n=t.get("chatId");if(!n)return new c.P7("bad_request:api","Parameter chatId is required.").toResponse();let a=await (0,i.j2)();if(!a?.user)return new c.P7("unauthorized:vote").toResponse();let r=await (0,u.TJ)({id:n});if(!r)return new c.P7("not_found:chat").toResponse();if(r.userId!==a.user.id)return new c.P7("forbidden:vote").toResponse();let s=await (0,u.mV)({id:n});return Response.json(s,{status:200})}async function d(e){let{chatId:t,messageId:n,type:a}=await e.json();if(!t||!n||!a)return new c.P7("bad_request:api","Parameters chatId, messageId, and type are required.").toResponse();let r=await (0,i.j2)();if(!r?.user)return new c.P7("unauthorized:vote").toResponse();let s=await (0,u.TJ)({id:t});return s?s.userId!==r.user.id?new c.P7("forbidden:vote").toResponse():(await (0,u.Ci)({chatId:t,messageId:n,type:a}),new Response("Message voted",{status:200})):new c.P7("not_found:vote").toResponse()}let p=new r.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/(chat)/api/vote/route",pathname:"/api/vote",filename:"route",bundlePath:"app/(chat)/api/vote/route"},resolvedPagePath:"/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(chat)/api/vote/route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:g,workUnitAsyncStorage:m,serverHooks:h}=p;function y(){return(0,o.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:m})}},11455:(e,t,n)=>{"use strict";async function a(e){return console.warn("getUser called with legacy implementation - needs Convex migration"),[]}async function r(){return console.warn("createGuestUser called with legacy implementation - needs Convex migration"),[{id:`guest-${Date.now()}`,email:null,password:null}]}async function s(){return console.warn("saveChat called with legacy implementation - needs Convex migration"),null}async function o(){return console.warn("deleteChatById called with legacy implementation - needs Convex migration"),null}async function i(){return console.warn("getChatsByUserId called with legacy implementation - needs Convex migration"),[]}async function u(){return console.warn("getChatById called with legacy implementation - needs Convex migration"),null}async function c(){return console.warn("saveMessages called with legacy implementation - needs Convex migration"),null}async function l(){return console.warn("getMessagesByChatId called with legacy implementation - needs Convex migration"),[]}async function d(){return console.warn("voteMessage called with legacy implementation - needs Convex migration"),null}async function p(){return console.warn("getVotesByChatId called with legacy implementation - needs Convex migration"),[]}async function g(){return console.warn("saveDocument called with legacy implementation - needs Convex migration"),null}async function m(){return console.warn("getDocumentById called with legacy implementation - needs Convex migration"),null}async function h(){return console.warn("deleteDocumentsByIdAfterTimestamp called with legacy implementation - needs Convex migration"),null}async function y(){return console.warn("saveSuggestions called with legacy implementation - needs Convex migration"),null}async function w(){return console.warn("getSuggestionsByDocumentId called with legacy implementation - needs Convex migration"),[]}async function f(){return console.warn("getMessageById called with legacy implementation - needs Convex migration"),null}async function v(){return console.warn("deleteMessagesByChatIdAfterTimestamp called with legacy implementation - needs Convex migration"),null}async function x(){return console.warn("updateChatVisiblityById called with legacy implementation - needs Convex migration"),null}async function C(){return console.warn("getStreamIdsByChatId called with legacy implementation - needs Convex migration"),[]}async function P(){return console.warn("getMessageCountByUserId called with legacy implementation - needs Convex migration"),0}async function b(){return console.warn("createStreamId called with legacy implementation - needs Convex migration"),`stream-${Date.now()}`}async function I(){return console.warn("getDocumentsById called with legacy implementation - needs Convex migration"),[]}n.d(t,{$:()=>x,Ci:()=>d,Lz:()=>i,M7:()=>b,Nm:()=>C,TJ:()=>u,W8:()=>r,_L:()=>m,bd:()=>g,de:()=>l,iB:()=>h,kA:()=>f,mV:()=>p,q1:()=>P,qQ:()=>o,tw:()=>w,wA:()=>v,wz:()=>a,xt:()=>I,yM:()=>c,yd:()=>s,zL:()=>y})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29727:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page-experimental.runtime.prod.js")},46871:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route-experimental.runtime.prod.js")},48106:(e,t,n)=>{"use strict";e.exports=n(46871)},52335:(e,t,n)=>{"use strict";n.d(t,{fG:()=>u,LO:()=>c,j2:()=>l,Jv:()=>d});var a=n(89131),r=n(50109),s=n(9852),o=n(11455),i=n(73241);let{handlers:{GET:u,POST:c},auth:l,signIn:d,signOut:p}=(0,r.Ay)({...{pages:{signIn:"/login",newUser:"/"},providers:[],callbacks:{}},providers:[(0,s.A)({credentials:{},async authorize({email:e,password:t}){let n=await (0,o.wz)(e);if(0===n.length)return await (0,a.UD)(t,i.vt),null;let[r]=n;return r.password?await (0,a.UD)(t,r.password)?{...r,type:"regular"}:null:(await (0,a.UD)(t,i.vt),null)}}),(0,s.A)({id:"guest",credentials:{},async authorize(){let[e]=await (0,o.W8)();return{...e,type:"guest"}}})],callbacks:{jwt:async({token:e,user:t})=>(t&&(e.id=t.id,e.type=t.type),e),session:async({session:e,token:t})=>(e.user&&(e.user.id=t.id,e.user.type=t.type),e)}})},55511:e=>{"use strict";e.exports=require("crypto")},56617:(e,t,n)=>{"use strict";n.d(t,{P7:()=>r});let a={database:"log",chat:"response",auth:"response",stream:"response",api:"response",history:"response",vote:"response",document:"response",suggestions:"response"};class r extends Error{constructor(e,t){super();let[n,a]=e.split(":");this.type=n,this.cause=t,this.surface=a,this.message=function(e){if(e.includes("database"))return"An error occurred while executing a database query.";switch(e){case"bad_request:api":return"The request couldn't be processed. Please check your input and try again.";case"unauthorized:auth":return"You need to sign in before continuing.";case"forbidden:auth":return"Your account does not have access to this feature.";case"rate_limit:chat":return"You have exceeded your maximum number of messages for the day. Please try again later.";case"not_found:chat":return"The requested chat was not found. Please check the chat ID and try again.";case"forbidden:chat":return"This chat belongs to another user. Please check the chat ID and try again.";case"unauthorized:chat":return"You need to sign in to view this chat. Please sign in and try again.";case"offline:chat":return"We're having trouble sending your message. Please check your internet connection and try again.";case"not_found:document":return"The requested document was not found. Please check the document ID and try again.";case"forbidden:document":return"This document belongs to another user. Please check the document ID and try again.";case"unauthorized:document":return"You need to sign in to view this document. Please sign in and try again.";case"bad_request:document":return"The request to create or update the document was invalid. Please check your input and try again.";default:return"Something went wrong. Please try again later."}}(e),this.statusCode=function(e){switch(e){case"bad_request":return 400;case"unauthorized":return 401;case"forbidden":return 403;case"not_found":return 404;case"rate_limit":return 429;case"offline":return 503;default:return 500}}(this.type)}toResponse(){let e=`${this.type}:${this.surface}`,t=a[this.surface],{message:n,cause:r,statusCode:s}=this;return"log"===t?(console.error({code:e,message:n,cause:r}),Response.json({code:"",message:"Something went wrong. Please try again later."},{status:s})):Response.json({code:e,message:n,cause:r},{status:s})}}},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73241:(e,t,n)=>{"use strict";n.d(t,{vt:()=>o,b_:()=>r,Fj:()=>a,MC:()=>s}),n(89131);let a=!0,r=!1,s=!!(process.env.PLAYWRIGHT_TEST_BASE_URL||process.env.PLAYWRIGHT||process.env.CI_PLAYWRIGHT),o="$2b$10$K1V5qz0cZGaJGDwQO4CQuu4Xr5bZnQfCqFkC7l0qoF5zVhsVz7/.2"},77598:e=>{"use strict";e.exports=require("node:crypto")},80408:()=>{},87032:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),a=t.X(0,[191,4235,2543,5208,8814],()=>n(7459));module.exports=a})();