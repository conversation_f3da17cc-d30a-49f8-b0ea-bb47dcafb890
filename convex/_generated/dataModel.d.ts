/* eslint-disable */
/**
 * Generated `dataModel` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  DataModelFromSchemaDefinition,
  DocumentByName,
  TableNamesInDataModel,
} from "convex/server";
import type { GenericId } from "convex/values";
import schema from "../schema.js";

/**
 * The shape of a document stored in Convex.
 *
 * @public
 */
export type Doc<TableName extends TableNamesInDataModel<DataModel>> =
  DocumentByName<DataModel, TableName>;

/**
 * An identifier for a document in Convex.
 *
 * Convex documents are uniquely identified by their `Id`, which is accessible
 * on the `_id` field. To learn more, see [Document IDs](https://docs.convex.dev/database/document-ids).
 *
 * Documents can be loaded using `db.get(id)` in query and mutation functions.
 *
 * **Important**: Use `Id<TableName>` where `TableName` is the name of the table,
 * not `Id` directly. For example, use `Id<"users">`, not `Id`.
 *
 * @public
 */
export type Id<TableName extends TableNamesInDataModel<DataModel>> =
  GenericId<TableName>;

/**
 * A type describing your Convex data model.
 *
 * This type is used to parameterize methods like `queryGeneric` and
 * `mutationGeneric` to make them type-safe.
 *
 * @public
 */
export type DataModel = DataModelFromSchemaDefinition<typeof schema>;

export declare const dataModel: DataModel;