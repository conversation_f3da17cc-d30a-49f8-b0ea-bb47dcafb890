(()=>{var e={};e.id=6678,e.ids=[6678],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},11455:(e,n,t)=>{"use strict";async function a(e){return console.warn("get<PERSON><PERSON> called with legacy implementation - needs Convex migration"),[]}async function r(){return console.warn("createGuest<PERSON><PERSON> called with legacy implementation - needs Convex migration"),[{id:`guest-${Date.now()}`,email:null,password:null}]}async function s(){return console.warn("saveChat called with legacy implementation - needs Convex migration"),null}async function i(){return console.warn("deleteChatById called with legacy implementation - needs Convex migration"),null}async function o(){return console.warn("getChatsByUserId called with legacy implementation - needs Convex migration"),[]}async function l(){return console.warn("getChatById called with legacy implementation - needs Convex migration"),null}async function c(){return console.warn("saveMessages called with legacy implementation - needs Convex migration"),null}async function u(){return console.warn("getMessagesByChatId called with legacy implementation - needs Convex migration"),[]}async function d(){return console.warn("voteMessage called with legacy implementation - needs Convex migration"),null}async function m(){return console.warn("getVotesByChatId called with legacy implementation - needs Convex migration"),[]}async function p(){return console.warn("saveDocument called with legacy implementation - needs Convex migration"),null}async function g(){return console.warn("getDocumentById called with legacy implementation - needs Convex migration"),null}async function y(){return console.warn("deleteDocumentsByIdAfterTimestamp called with legacy implementation - needs Convex migration"),null}async function w(){return console.warn("saveSuggestions called with legacy implementation - needs Convex migration"),null}async function h(){return console.warn("getSuggestionsByDocumentId called with legacy implementation - needs Convex migration"),[]}async function x(){return console.warn("getMessageById called with legacy implementation - needs Convex migration"),null}async function v(){return console.warn("deleteMessagesByChatIdAfterTimestamp called with legacy implementation - needs Convex migration"),null}async function C(){return console.warn("updateChatVisiblityById called with legacy implementation - needs Convex migration"),null}async function f(){return console.warn("getStreamIdsByChatId called with legacy implementation - needs Convex migration"),[]}async function I(){return console.warn("getMessageCountByUserId called with legacy implementation - needs Convex migration"),0}async function B(){return console.warn("createStreamId called with legacy implementation - needs Convex migration"),`stream-${Date.now()}`}async function q(){return console.warn("getDocumentsById called with legacy implementation - needs Convex migration"),[]}t.d(n,{$:()=>C,Ci:()=>d,Lz:()=>o,M7:()=>B,Nm:()=>f,TJ:()=>l,W8:()=>r,_L:()=>g,bd:()=>p,de:()=>u,iB:()=>y,kA:()=>x,mV:()=>m,q1:()=>I,qQ:()=>i,tw:()=>h,wA:()=>v,wz:()=>a,xt:()=>q,yM:()=>c,yd:()=>s,zL:()=>w})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29727:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page-experimental.runtime.prod.js")},46871:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route-experimental.runtime.prod.js")},48106:(e,n,t)=>{"use strict";e.exports=t(46871)},52335:(e,n,t)=>{"use strict";t.d(n,{fG:()=>l,LO:()=>c,j2:()=>u,Jv:()=>d});var a=t(89131),r=t(50109),s=t(9852),i=t(11455),o=t(73241);let{handlers:{GET:l,POST:c},auth:u,signIn:d,signOut:m}=(0,r.Ay)({...{pages:{signIn:"/login",newUser:"/"},providers:[],callbacks:{}},providers:[(0,s.A)({credentials:{},async authorize({email:e,password:n}){let t=await (0,i.wz)(e);if(0===t.length)return await (0,a.UD)(n,o.vt),null;let[r]=t;return r.password?await (0,a.UD)(n,r.password)?{...r,type:"regular"}:null:(await (0,a.UD)(n,o.vt),null)}}),(0,s.A)({id:"guest",credentials:{},async authorize(){let[e]=await (0,i.W8)();return{...e,type:"guest"}}})],callbacks:{jwt:async({token:e,user:n})=>(n&&(e.id=n.id,e.type=n.type),e),session:async({session:e,token:n})=>(e.user&&(e.user.id=n.id,e.user.type=n.type),e)}})},55511:e=>{"use strict";e.exports=require("crypto")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73241:(e,n,t)=>{"use strict";t.d(n,{vt:()=>i,b_:()=>r,Fj:()=>a,MC:()=>s}),t(89131);let a=!0,r=!1,s=!!(process.env.PLAYWRIGHT_TEST_BASE_URL||process.env.PLAYWRIGHT||process.env.CI_PLAYWRIGHT),i="$2b$10$K1V5qz0cZGaJGDwQO4CQuu4Xr5bZnQfCqFkC7l0qoF5zVhsVz7/.2"},77598:e=>{"use strict";e.exports=require("node:crypto")},80408:()=>{},85699:(e,n,t)=>{"use strict";t.r(n),t.d(n,{patchFetch:()=>m,routeModule:()=>l,serverHooks:()=>d,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>u});var a={};t.r(a),t.d(a,{GET:()=>o.fG,POST:()=>o.LO});var r=t(48106),s=t(48819),i=t(12050),o=t(52335);let l=new r.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/(auth)/api/auth/[...nextauth]/route",pathname:"/api/auth/[...nextauth]",filename:"route",bundlePath:"app/(auth)/api/auth/[...nextauth]/route"},resolvedPagePath:"/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(auth)/api/auth/[...nextauth]/route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:c,workUnitAsyncStorage:u,serverHooks:d}=l;function m(){return(0,i.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:u})}},87032:()=>{}};var n=require("../../../../../webpack-runtime.js");n.C(e);var t=e=>n(n.s=e),a=n.X(0,[191,4235,2543,5208,8814],()=>t(85699));module.exports=a})();