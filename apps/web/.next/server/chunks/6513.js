"use strict";exports.id=6513,exports.ids=[6513],exports.modules={66513:(e,t,s)=>{s.r(t),s.d(t,{createOrReadKeyless:()=>Y,removeKeyless:()=>F,safeParseClerkFile:()=>$});var i,n,r,l,a,o,c,d,h,u,p,f,y,S,m,k,g,w,b,v,K=s(4772);s(47183),s(20358),s(54113);var V=s(65011),E=s(37894),O=s(62584),M=class{constructor(){(0,O.VK)(this,l),(0,O.VK)(this,n,"clerk_telemetry_throttler"),(0,O.VK)(this,r,864e5)}isEventThrottled(e){if(!(0,O.S7)(this,l,c))return!1;let t=Date.now(),s=(0,O.jq)(this,l,a).call(this,e),i=(0,O.S7)(this,l,o)?.[s];if(!i){let e={...(0,O.S7)(this,l,o),[s]:t};localStorage.setItem((0,O.S7)(this,n),JSON.stringify(e))}if(i&&t-i>(0,O.S7)(this,r)){let e=(0,O.S7)(this,l,o);delete e[s],localStorage.setItem((0,O.S7)(this,n),JSON.stringify(e))}return!!i}};n=new WeakMap,r=new WeakMap,l=new WeakSet,a=function(e){let{sk:t,pk:s,payload:i,...n}=e,r={...i,...n};return JSON.stringify(Object.keys({...i,...n}).sort().map(e=>r[e]))},o=function(){let e=localStorage.getItem((0,O.S7)(this,n));return e?JSON.parse(e):{}},c=function(){if("undefined"==typeof window)return!1;let e=window.localStorage;if(!e)return!1;try{let t="test";return e.setItem(t,t),e.removeItem(t),!0}catch(t){return t instanceof DOMException&&("QuotaExceededError"===t.name||"NS_ERROR_DOM_QUOTA_REACHED"===t.name)&&e.length>0&&e.removeItem((0,O.S7)(this,n)),!1}};var j={samplingRate:1,maxBufferSize:5,endpoint:"https://clerk-telemetry.com"},T=class{constructor(e){(0,O.VK)(this,y),(0,O.VK)(this,d),(0,O.VK)(this,h),(0,O.VK)(this,u,{}),(0,O.VK)(this,p,[]),(0,O.VK)(this,f),(0,O.OV)(this,d,{maxBufferSize:e.maxBufferSize??j.maxBufferSize,samplingRate:e.samplingRate??j.samplingRate,disabled:e.disabled??!1,debug:e.debug??!1,endpoint:j.endpoint}),e.clerkVersion||"undefined"!=typeof window?(0,O.S7)(this,u).clerkVersion=e.clerkVersion??"":(0,O.S7)(this,u).clerkVersion="",(0,O.S7)(this,u).sdk=e.sdk,(0,O.S7)(this,u).sdkVersion=e.sdkVersion,(0,O.S7)(this,u).publishableKey=e.publishableKey??"";let t=(0,E.q5)(e.publishableKey);t&&((0,O.S7)(this,u).instanceType=t.instanceType),e.secretKey&&((0,O.S7)(this,u).secretKey=e.secretKey.substring(0,16)),(0,O.OV)(this,h,new M)}get isEnabled(){return!("development"!==(0,O.S7)(this,u).instanceType||(0,O.S7)(this,d).disabled||"undefined"!=typeof process&&process.env&&(0,V.zz)(process.env.CLERK_TELEMETRY_DISABLED)||"undefined"!=typeof window&&window?.navigator?.webdriver)}get isDebug(){return(0,O.S7)(this,d).debug||"undefined"!=typeof process&&process.env&&(0,V.zz)(process.env.CLERK_TELEMETRY_DEBUG)}record(e){let t=(0,O.jq)(this,y,v).call(this,e.event,e.payload);(0,O.jq)(this,y,w).call(this,t.event,t),(0,O.jq)(this,y,S).call(this,t,e.eventSamplingRate)&&((0,O.S7)(this,p).push(t),(0,O.jq)(this,y,k).call(this))}};d=new WeakMap,h=new WeakMap,u=new WeakMap,p=new WeakMap,f=new WeakMap,y=new WeakSet,S=function(e,t){return this.isEnabled&&!this.isDebug&&(0,O.jq)(this,y,m).call(this,e,t)},m=function(e,t){let s=Math.random();return!!(s<=(0,O.S7)(this,d).samplingRate&&(void 0===t||s<=t))&&!(0,O.S7)(this,h).isEventThrottled(e)},k=function(){if("undefined"==typeof window)return void(0,O.jq)(this,y,g).call(this);if((0,O.S7)(this,p).length>=(0,O.S7)(this,d).maxBufferSize){(0,O.S7)(this,f)&&("undefined"!=typeof cancelIdleCallback?cancelIdleCallback:clearTimeout)((0,O.S7)(this,f)),(0,O.jq)(this,y,g).call(this);return}(0,O.S7)(this,f)||("requestIdleCallback"in window?(0,O.OV)(this,f,requestIdleCallback(()=>{(0,O.jq)(this,y,g).call(this)})):(0,O.OV)(this,f,setTimeout(()=>{(0,O.jq)(this,y,g).call(this)},0)))},g=function(){fetch(new URL("/v1/event",(0,O.S7)(this,d).endpoint),{method:"POST",body:JSON.stringify({events:(0,O.S7)(this,p)}),headers:{"Content-Type":"application/json"}}).catch(()=>void 0).then(()=>{(0,O.OV)(this,p,[])}).catch(()=>void 0)},w=function(e,t){this.isDebug&&(void 0!==console.groupCollapsed?(console.groupCollapsed("[clerk/telemetry]",e),console.log(t),console.groupEnd()):console.log("[clerk/telemetry]",e,t))},b=function(){let e={name:(0,O.S7)(this,u).sdk,version:(0,O.S7)(this,u).sdkVersion};return"undefined"!=typeof window&&window.Clerk&&(e={...e,...window.Clerk.constructor.sdkMetadata}),e},v=function(e,t){let s=(0,O.jq)(this,y,b).call(this);return{event:e,cv:(0,O.S7)(this,u).clerkVersion??"",it:(0,O.S7)(this,u).instanceType??"",sdk:s.name,sdkv:s.version,...(0,O.S7)(this,u).publishableKey?{pk:(0,O.S7)(this,u).publishableKey}:{},...(0,O.S7)(this,u).secretKey?{sk:(0,O.S7)(this,u).secretKey}:{},payload:t}};i=K.nr,async(...e)=>{let{data:t,errors:s}=await i(...e);if(s)throw s[0];return t};var R=s(78854);let x={secretKey:R.rB,publishableKey:R.At,apiUrl:R.H$,apiVersion:R.mG,userAgent:"@clerk/nextjs@6.23.3",proxyUrl:R.Rg,domain:R.V2,isSatellite:R.fS,sdkMetadata:R.tm,telemetry:{disabled:R.nN,debug:R.Mh}},C=e=>(function(e){let t={...e},s=(0,K.y3)(t),i=(0,K.Bs)({options:t,apiClient:s}),n=new T({...e.telemetry,publishableKey:t.publishableKey,secretKey:t.secretKey,samplingRate:.1,...t.sdkMetadata?{sdk:t.sdkMetadata.name,sdkVersion:t.sdkMetadata.version}:{}});return{...s,...i,telemetry:n}})({...x,...e});var q=Object.getOwnPropertyNames;let _=((e,t)=>function(){return t||(0,e[q(e)[0]])((t={exports:{}}).exports,t),t.exports})({"src/runtime/node/safe-node-apis.js"(e,t){let{existsSync:i,writeFileSync:n,readFileSync:r,appendFileSync:l,mkdirSync:a,rmSync:o}=s(73024);t.exports={fs:{existsSync:i,writeFileSync:n,readFileSync:r,appendFileSync:l,mkdirSync:a,rmSync:o},path:s(76760),cwd:()=>process.cwd()}}})(),D=e=>{throw Error(`Clerk: ${e} is missing. This is an internal error. Please contact Clerk's support.`)},I=()=>(_.fs||D("fs"),_.fs),N=()=>(_.path||D("path"),_.path),A=()=>(_.cwd||D("cwd"),_.cwd),z=".clerk",B="clerk.lock",W=(...e)=>{let t=N(),s=A();return t.join(s(),z,...e)},J=".tmp",L=()=>W(J,"keyless.json"),P=()=>W(J,"README.md"),U=!1;function $(){let{readFileSync:e}=I();try{let t,s=L();try{t=e(s,{encoding:"utf-8"})||"{}"}catch{t="{}"}return JSON.parse(t)}catch{return}}let G=()=>{let{writeFileSync:e}=I();U=!0,e(B,"This file can be deleted. Please delete this file and refresh your application",{encoding:"utf8",mode:"0777",flag:"w"})},H=()=>{let{rmSync:e}=I();try{e(B,{force:!0,recursive:!0})}catch{}U=!1},Q=()=>{let{existsSync:e}=I();return U||e(B)};async function Y(){let{writeFileSync:e,mkdirSync:t}=I();if(Q())return null;G();let s=L(),i=P();t(W(J),{recursive:!0}),function(){let{existsSync:e,writeFileSync:t,readFileSync:s,appendFileSync:i}=I(),n=N(),r=A(),l=n.join(r(),".gitignore");e(l)||t(l,""),s(l,"utf-8").includes(z+"/")||i(l,`
# clerk configuration (can include secrets)
/${z}/
`)}();let n=$();if((null==n?void 0:n.publishableKey)&&(null==n?void 0:n.secretKey))return H(),n;let r=C({}),l=await r.__experimental_accountlessApplications.createAccountlessApplication().catch(()=>null);return l&&(e(s,JSON.stringify(l),{encoding:"utf8",mode:"0777",flag:"w"}),e(i,`
## DO NOT COMMIT
This directory is auto-generated from \`@clerk/nextjs\` because you are running in Keyless mode. Avoid committing the \`.clerk/\` directory as it includes the secret key of the unclaimed instance.
  `,{encoding:"utf8",mode:"0777",flag:"w"})),H(),l}function F(){let{rmSync:e}=I();if(!Q()){G();try{e(W(),{force:!0,recursive:!0})}catch{}H()}}}};