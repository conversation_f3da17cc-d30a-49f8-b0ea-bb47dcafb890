{"name": "@scure/base", "version": "1.2.6", "description": "Secure, audited & 0-dep implementation of base64, bech32, base58, base32 & base16", "files": ["lib/index.js", "lib/index.js.map", "lib/index.d.ts", "lib/index.d.ts.map", "lib/esm/index.js", "lib/esm/index.js.map", "lib/esm/index.d.ts", "lib/esm/index.d.ts.map", "lib/esm/package.json", "index.ts"], "main": "./lib/index.js", "module": "./lib/esm/index.js", "types": "./lib/index.d.ts", "exports": {".": {"import": "./lib/esm/index.js", "require": "./lib/index.js"}}, "scripts": {"bench": "node test/benchmark/index.js", "build": "tsc && tsc -p tsconfig.cjs.json", "build:release": "npx jsbt esbuild test/build", "lint": "prettier --check index.ts", "format": "prettier --write index.ts", "test": "node test/index.js", "test:bun": "bun test/index.js", "test:deno": "deno --allow-env --allow-read test/index.js && deno test/deno.ts"}, "sideEffects": false, "author": "<PERSON> (https://paulmillr.com)", "license": "MIT", "homepage": "https://paulmillr.com/noble/#scure", "repository": {"type": "git", "url": "git+https://github.com/paulmillr/scure-base.git"}, "devDependencies": {"@noble/hashes": "1.8.0", "@paulmillr/jsbt": "0.3.3", "@types/node": "22.15.23", "fast-check": "4.1.1", "micro-bmark": "0.4.2", "micro-should": "0.5.3", "prettier": "3.5.3", "typescript": "5.8.3"}, "keywords": ["bech32", "bech32m", "base64", "base58", "base32", "base16", "rfc4648", "rfc3548", "crockford", "encode", "encoder", "base-x", "base"], "funding": "https://paulmillr.com/funding/"}