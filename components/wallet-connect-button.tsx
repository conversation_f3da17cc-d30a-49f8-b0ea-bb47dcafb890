'use client';

import { useWallet } from '@solana/wallet-adapter-react';
import { WalletMultiButton } from '@solana/wallet-adapter-react-ui';
import { useUser } from '@clerk/nextjs';
import { useEffect, useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { toast } from 'sonner';
import { Loader2 } from 'lucide-react';

// Import wallet adapter CSS
import '@solana/wallet-adapter-react-ui/styles.css';

export function WalletConnectButton() {
  const { publicKey, connected, disconnect } = useWallet();
  const { user, isLoaded } = useUser();
  const [isLinking, setIsLinking] = useState(false);
  const [walletLinked, setWalletLinked] = useState(false);

  useEffect(() => {
    if (isLoaded && user) {
      setWalletLinked(!!user.publicMetadata?.walletAddress);
    }
  }, [user, isLoaded]);

  const handleLinkWallet = async () => {
    if (!publicKey || !user) return;

    setIsLinking(true);
    try {
      const response = await fetch('/api/wallet/link', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ walletAddress: publicKey.toBase58() }),
      });

      if (!response.ok) {
        throw new Error('Failed to link wallet');
      }

      await user.reload();
      setWalletLinked(true);
      toast.success('Wallet linked successfully!');
    } catch (error) {
      console.error('Error linking wallet:', error);
      toast.error('Failed to link wallet. Please try again.');
    } finally {
      setIsLinking(false);
    }
  };

  const handleUnlinkWallet = async () => {
    if (!user) return;

    setIsLinking(true);
    try {
      const response = await fetch('/api/wallet/unlink', {
        method: 'POST',
      });

      if (!response.ok) {
        throw new Error('Failed to unlink wallet');
      }

      await user.reload();
      setWalletLinked(false);
      disconnect();
      toast.success('Wallet unlinked successfully!');
    } catch (error) {
      console.error('Error unlinking wallet:', error);
      toast.error('Failed to unlink wallet. Please try again.');
    } finally {
      setIsLinking(false);
    }
  };

  if (!isLoaded || !user) {
    return null;
  }

  return (
    <div className="flex items-center gap-2">
      <WalletMultiButton className="!bg-primary !text-primary-foreground hover:!bg-primary/90 !h-10 !px-4 !text-sm !font-medium !rounded-md !transition-colors" />

      {connected && publicKey && (
        <>
          {walletLinked ? (
            <Button
              variant="outline"
              size="sm"
              onClick={handleUnlinkWallet}
              disabled={isLinking}
            >
              {isLinking ? (
                <Loader2 className="size-4 animate-spin" />
              ) : (
                'Unlink'
              )}
            </Button>
          ) : (
            <Button
              variant="default"
              size="sm"
              onClick={handleLinkWallet}
              disabled={isLinking}
            >
              {isLinking ? (
                <Loader2 className="size-4 animate-spin" />
              ) : (
                'Link to Account'
              )}
            </Button>
          )}
        </>
      )}
    </div>
  );
}
