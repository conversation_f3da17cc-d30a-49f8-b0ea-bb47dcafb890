const CHUNK_PUBLIC_PATH = "server/app/(auth)/api/auth/[...nextauth]/route.js";
const runtime = require("../../../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules_next_7fd66281._.js");
runtime.loadChunk("server/chunks/node_modules_@auth_core_8fd8d1ce._.js");
runtime.loadChunk("server/chunks/node_modules_jose_dist_node_esm_69b56690._.js");
runtime.loadChunk("server/chunks/node_modules_9bf45c61._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__d8800277._.js");
runtime.getOrInstantiateRuntimeModule("[project]/apps/web/.next-internal/server/app/(auth)/api/auth/[...nextauth]/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/apps/web/app/(auth)/api/auth/[...nextauth]/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/apps/web/app/(auth)/api/auth/[...nextauth]/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
