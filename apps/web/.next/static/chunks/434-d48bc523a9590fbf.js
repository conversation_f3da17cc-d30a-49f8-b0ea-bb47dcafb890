"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[434],{1129:(e,t,n)=>{n.d(t,{I:()=>o});var r=n(93451);let i=["read","resolveKeyframes","update","preRender","render","postRender"];function o(e,t){let n=!1,o=!0,s={delta:0,timestamp:0,isProcessing:!1},a=()=>n=!0,l=i.reduce((e,t)=>(e[t]=function(e){let t=new Set,n=new Set,r=!1,i=!1,o=new WeakSet,s={delta:0,timestamp:0,isProcessing:!1};function a(t){o.has(t)&&(l.schedule(t),e()),t(s)}let l={schedule:(e,i=!1,s=!1)=>{let a=s&&r?t:n;return i&&o.add(e),a.has(e)||a.add(e),e},cancel:e=>{n.delete(e),o.delete(e)},process:e=>{if(s=e,r){i=!0;return}r=!0,[t,n]=[n,t],t.forEach(a),t.clear(),r=!1,i&&(i=!1,l.process(e))}};return l}(a),e),{}),{read:u,resolveKeyframes:c,update:d,preRender:h,render:f,postRender:p}=l,m=()=>{let i=r.W.useManualTiming?s.timestamp:performance.now();n=!1,s.delta=o?1e3/60:Math.max(Math.min(i-s.timestamp,40),1),s.timestamp=i,s.isProcessing=!0,u.process(s),c.process(s),d.process(s),h.process(s),f.process(s),p.process(s),s.isProcessing=!1,n&&t&&(o=!1,e(m))},v=()=>{n=!0,o=!0,s.isProcessing||e(m)};return{schedule:i.reduce((e,t)=>{let r=l[t];return e[t]=(e,t=!1,i=!1)=>(n||v(),r.schedule(e,t,i)),e},{}),cancel:e=>{for(let t=0;t<i.length;t++)l[i[t]].cancel(e)},state:s,steps:l}}},2686:(e,t,n)=>{n.d(t,{x:()=>i});var r=n(19922);function i(e){for(var t=arguments.length,n=Array(t>1?t-1:0),i=1;i<t;i++)n[i-1]=arguments[i];let o=r.w.bind(null,e||n.find(e=>"object"==typeof e));return n.map(o)}},4931:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(98889).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},7297:(e,t,n)=>{n.d(t,{b:()=>u});var r=n(41987),i=n(7156),o=n(44995),s="horizontal",a=["horizontal","vertical"],l=r.forwardRef((e,t)=>{var n;let{decorative:r,orientation:l=s,...u}=e,c=(n=l,a.includes(n))?l:s;return(0,o.jsx)(i.sG.div,{"data-orientation":c,...r?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...u,ref:t})});l.displayName="Separator";var u=l},8041:(e,t,n)=>{n.d(t,{H_:()=>tw,UC:()=>tv,YJ:()=>tg,q7:()=>tx,VF:()=>tE,JU:()=>ty,ZL:()=>tm,z6:()=>tb,hN:()=>tT,bL:()=>tf,wv:()=>tP,Pb:()=>tA,G5:()=>tC,ZP:()=>tS,l9:()=>tp});var r,i=n(41987),o=n(29254),s=n(49640),a=n(80482),l=n(87076),u=n(7156),c=n(67198),d=n(86111),h=n(79807),f=n(79649),p=n(44995);function m(e){let t=e+"CollectionProvider",[n,r]=(0,a.A)(t),[o,l]=n(t,{collectionRef:{current:null},itemMap:new Map}),u=e=>{let{scope:t,children:n}=e,r=i.useRef(null),s=i.useRef(new Map).current;return(0,p.jsx)(o,{scope:t,itemMap:s,collectionRef:r,children:n})};u.displayName=t;let c=e+"CollectionSlot",d=(0,f.TL)(c),h=i.forwardRef((e,t)=>{let{scope:n,children:r}=e,i=l(c,n),o=(0,s.s)(t,i.collectionRef);return(0,p.jsx)(d,{ref:o,children:r})});h.displayName=c;let m=e+"CollectionItemSlot",v="data-radix-collection-item",g=(0,f.TL)(m),y=i.forwardRef((e,t)=>{let{scope:n,children:r,...o}=e,a=i.useRef(null),u=(0,s.s)(t,a),c=l(m,n);return i.useEffect(()=>(c.itemMap.set(a,{ref:a,...o}),()=>void c.itemMap.delete(a))),(0,p.jsx)(g,{...{[v]:""},ref:u,children:r})});return y.displayName=m,[{Provider:u,Slot:h,ItemSlot:y},function(t){let n=l(e+"CollectionConsumer",t);return i.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(v,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},r]}var v=new WeakMap;function g(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let n=function(e,t){let n=e.length,r=y(t),i=r>=0?r:n+r;return i<0||i>=n?-1:i}(e,t);return -1===n?void 0:e[n]}function y(e){return e!=e||0===e?0:Math.trunc(e)}r=new WeakMap;var x=i.createContext(void 0);function w(e){let t=i.useContext(x);return e||t||"ltr"}var b=n(82491),T=n(17848),E=n(38552),P=n(60728),A=n(25784),S=n(93343),C=n(18400),R=n(93568),M="rovingFocusGroup.onEntryFocus",D={bubbles:!1,cancelable:!0},k="RovingFocusGroup",[j,L,O]=m(k),[V,N]=(0,a.A)(k,[O]),[F,I]=V(k),B=i.forwardRef((e,t)=>(0,p.jsx)(j.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(j.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(_,{...e,ref:t})})}));B.displayName=k;var _=i.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:r,loop:a=!1,dir:c,currentTabStopId:d,defaultCurrentTabStopId:h,onCurrentTabStopIdChange:f,onEntryFocus:m,preventScrollOnEntryFocus:v=!1,...g}=e,y=i.useRef(null),x=(0,s.s)(t,y),b=w(c),[T,E]=(0,l.i)({prop:d,defaultProp:null!=h?h:null,onChange:f,caller:k}),[P,A]=i.useState(!1),S=(0,R.c)(m),C=L(n),j=i.useRef(!1),[O,V]=i.useState(0);return i.useEffect(()=>{let e=y.current;if(e)return e.addEventListener(M,S),()=>e.removeEventListener(M,S)},[S]),(0,p.jsx)(F,{scope:n,orientation:r,dir:b,loop:a,currentTabStopId:T,onItemFocus:i.useCallback(e=>E(e),[E]),onItemShiftTab:i.useCallback(()=>A(!0),[]),onFocusableItemAdd:i.useCallback(()=>V(e=>e+1),[]),onFocusableItemRemove:i.useCallback(()=>V(e=>e-1),[]),children:(0,p.jsx)(u.sG.div,{tabIndex:P||0===O?-1:0,"data-orientation":r,...g,ref:x,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{j.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!j.current;if(e.target===e.currentTarget&&t&&!P){let t=new CustomEvent(M,D);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=C().filter(e=>e.focusable);$([e.find(e=>e.active),e.find(e=>e.id===T),...e].filter(Boolean).map(e=>e.ref.current),v)}}j.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>A(!1))})})}),W="RovingFocusGroupItem",U=i.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:r=!0,active:s=!1,tabStopId:a,children:l,...c}=e,d=(0,P.B)(),h=a||d,f=I(W,n),m=f.currentTabStopId===h,v=L(n),{onFocusableItemAdd:g,onFocusableItemRemove:y,currentTabStopId:x}=f;return i.useEffect(()=>{if(r)return g(),()=>y()},[r,g,y]),(0,p.jsx)(j.ItemSlot,{scope:n,id:h,focusable:r,active:s,children:(0,p.jsx)(u.sG.span,{tabIndex:m?0:-1,"data-orientation":f.orientation,...c,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{r?f.onItemFocus(h):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>f.onItemFocus(h)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void f.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let i=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(i))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(i)))return G[i]}(e,f.orientation,f.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=v().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=f.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>$(n))}}),children:"function"==typeof l?l({isCurrentTabStop:m,hasTabStop:null!=x}):l})})});U.displayName=W;var G={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function $(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var K=n(23027),H=n(39160),q=["Enter"," "],z=["ArrowUp","PageDown","End"],Y=["ArrowDown","PageUp","Home",...z],X={ltr:[...q,"ArrowRight"],rtl:[...q,"ArrowLeft"]},Z={ltr:["ArrowLeft"],rtl:["ArrowRight"]},Q="Menu",[J,ee,et]=m(Q),[en,er]=(0,a.A)(Q,[et,A.Bk,N]),ei=(0,A.Bk)(),eo=N(),[es,ea]=en(Q),[el,eu]=en(Q),ec=e=>{let{__scopeMenu:t,open:n=!1,children:r,dir:o,onOpenChange:s,modal:a=!0}=e,l=ei(t),[u,c]=i.useState(null),d=i.useRef(!1),h=(0,R.c)(s),f=w(o);return i.useEffect(()=>{let e=()=>{d.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>d.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,p.jsx)(A.bL,{...l,children:(0,p.jsx)(es,{scope:t,open:n,onOpenChange:h,content:u,onContentChange:c,children:(0,p.jsx)(el,{scope:t,onClose:i.useCallback(()=>h(!1),[h]),isUsingKeyboardRef:d,dir:f,modal:a,children:r})})})};ec.displayName=Q;var ed=i.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,i=ei(n);return(0,p.jsx)(A.Mz,{...i,...r,ref:t})});ed.displayName="MenuAnchor";var eh="MenuPortal",[ef,ep]=en(eh,{forceMount:void 0}),em=e=>{let{__scopeMenu:t,forceMount:n,children:r,container:i}=e,o=ea(eh,t);return(0,p.jsx)(ef,{scope:t,forceMount:n,children:(0,p.jsx)(C.C,{present:n||o.open,children:(0,p.jsx)(S.Z,{asChild:!0,container:i,children:r})})})};em.displayName=eh;var ev="MenuContent",[eg,ey]=en(ev),ex=i.forwardRef((e,t)=>{let n=ep(ev,e.__scopeMenu),{forceMount:r=n.forceMount,...i}=e,o=ea(ev,e.__scopeMenu),s=eu(ev,e.__scopeMenu);return(0,p.jsx)(J.Provider,{scope:e.__scopeMenu,children:(0,p.jsx)(C.C,{present:r||o.open,children:(0,p.jsx)(J.Slot,{scope:e.__scopeMenu,children:s.modal?(0,p.jsx)(ew,{...i,ref:t}):(0,p.jsx)(eb,{...i,ref:t})})})})}),ew=i.forwardRef((e,t)=>{let n=ea(ev,e.__scopeMenu),r=i.useRef(null),a=(0,s.s)(t,r);return i.useEffect(()=>{let e=r.current;if(e)return(0,K.Eq)(e)},[]),(0,p.jsx)(eE,{...e,ref:a,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),eb=i.forwardRef((e,t)=>{let n=ea(ev,e.__scopeMenu);return(0,p.jsx)(eE,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),eT=(0,f.TL)("MenuContent.ScrollLock"),eE=i.forwardRef((e,t)=>{let{__scopeMenu:n,loop:r=!1,trapFocus:a,onOpenAutoFocus:l,onCloseAutoFocus:u,disableOutsidePointerEvents:c,onEntryFocus:d,onEscapeKeyDown:h,onPointerDownOutside:f,onFocusOutside:m,onInteractOutside:v,onDismiss:g,disableOutsideScroll:y,...x}=e,w=ea(ev,n),P=eu(ev,n),S=ei(n),C=eo(n),R=ee(n),[M,D]=i.useState(null),k=i.useRef(null),j=(0,s.s)(t,k,w.onContentChange),L=i.useRef(0),O=i.useRef(""),V=i.useRef(0),N=i.useRef(null),F=i.useRef("right"),I=i.useRef(0),_=y?H.A:i.Fragment,W=e=>{var t,n;let r=O.current+e,i=R().filter(e=>!e.disabled),o=document.activeElement,s=null==(t=i.find(e=>e.ref.current===o))?void 0:t.textValue,a=function(e,t,n){var r;let i=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,o=n?e.indexOf(n):-1,s=(r=Math.max(o,0),e.map((t,n)=>e[(r+n)%e.length]));1===i.length&&(s=s.filter(e=>e!==n));let a=s.find(e=>e.toLowerCase().startsWith(i.toLowerCase()));return a!==n?a:void 0}(i.map(e=>e.textValue),r,s),l=null==(n=i.find(e=>e.textValue===a))?void 0:n.ref.current;!function e(t){O.current=t,window.clearTimeout(L.current),""!==t&&(L.current=window.setTimeout(()=>e(""),1e3))}(r),l&&setTimeout(()=>l.focus())};i.useEffect(()=>()=>window.clearTimeout(L.current),[]),(0,T.Oh)();let U=i.useCallback(e=>{var t,n;return F.current===(null==(t=N.current)?void 0:t.side)&&function(e,t){return!!t&&function(e,t){let{x:n,y:r}=e,i=!1;for(let e=0,o=t.length-1;e<t.length;o=e++){let s=t[e],a=t[o],l=s.x,u=s.y,c=a.x,d=a.y;u>r!=d>r&&n<(c-l)*(r-u)/(d-u)+l&&(i=!i)}return i}({x:e.clientX,y:e.clientY},t)}(e,null==(n=N.current)?void 0:n.area)},[]);return(0,p.jsx)(eg,{scope:n,searchRef:O,onItemEnter:i.useCallback(e=>{U(e)&&e.preventDefault()},[U]),onItemLeave:i.useCallback(e=>{var t;U(e)||(null==(t=k.current)||t.focus(),D(null))},[U]),onTriggerLeave:i.useCallback(e=>{U(e)&&e.preventDefault()},[U]),pointerGraceTimerRef:V,onPointerGraceIntentChange:i.useCallback(e=>{N.current=e},[]),children:(0,p.jsx)(_,{...y?{as:eT,allowPinchZoom:!0}:void 0,children:(0,p.jsx)(E.n,{asChild:!0,trapped:a,onMountAutoFocus:(0,o.m)(l,e=>{var t;e.preventDefault(),null==(t=k.current)||t.focus({preventScroll:!0})}),onUnmountAutoFocus:u,children:(0,p.jsx)(b.qW,{asChild:!0,disableOutsidePointerEvents:c,onEscapeKeyDown:h,onPointerDownOutside:f,onFocusOutside:m,onInteractOutside:v,onDismiss:g,children:(0,p.jsx)(B,{asChild:!0,...C,dir:P.dir,orientation:"vertical",loop:r,currentTabStopId:M,onCurrentTabStopIdChange:D,onEntryFocus:(0,o.m)(d,e=>{P.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,p.jsx)(A.UC,{role:"menu","aria-orientation":"vertical","data-state":eZ(w.open),"data-radix-menu-content":"",dir:P.dir,...S,...x,ref:j,style:{outline:"none",...x.style},onKeyDown:(0,o.m)(x.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,r=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!n&&r&&W(e.key));let i=k.current;if(e.target!==i||!Y.includes(e.key))return;e.preventDefault();let o=R().filter(e=>!e.disabled).map(e=>e.ref.current);z.includes(e.key)&&o.reverse(),function(e){let t=document.activeElement;for(let n of e)if(n===t||(n.focus(),document.activeElement!==t))return}(o)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(L.current),O.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,e0(e=>{let t=e.target,n=I.current!==e.clientX;e.currentTarget.contains(t)&&n&&(F.current=e.clientX>I.current?"right":"left",I.current=e.clientX)}))})})})})})})});ex.displayName=ev;var eP=i.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,p.jsx)(u.sG.div,{role:"group",...r,ref:t})});eP.displayName="MenuGroup";var eA=i.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,p.jsx)(u.sG.div,{...r,ref:t})});eA.displayName="MenuLabel";var eS="MenuItem",eC="menu.itemSelect",eR=i.forwardRef((e,t)=>{let{disabled:n=!1,onSelect:r,...a}=e,l=i.useRef(null),c=eu(eS,e.__scopeMenu),d=ey(eS,e.__scopeMenu),h=(0,s.s)(t,l),f=i.useRef(!1);return(0,p.jsx)(eM,{...a,ref:h,disabled:n,onClick:(0,o.m)(e.onClick,()=>{let e=l.current;if(!n&&e){let t=new CustomEvent(eC,{bubbles:!0,cancelable:!0});e.addEventListener(eC,e=>null==r?void 0:r(e),{once:!0}),(0,u.hO)(e,t),t.defaultPrevented?f.current=!1:c.onClose()}}),onPointerDown:t=>{var n;null==(n=e.onPointerDown)||n.call(e,t),f.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{var t;f.current||null==(t=e.currentTarget)||t.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=""!==d.searchRef.current;n||t&&" "===e.key||q.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});eR.displayName=eS;var eM=i.forwardRef((e,t)=>{let{__scopeMenu:n,disabled:r=!1,textValue:a,...l}=e,c=ey(eS,n),d=eo(n),h=i.useRef(null),f=(0,s.s)(t,h),[m,v]=i.useState(!1),[g,y]=i.useState("");return i.useEffect(()=>{let e=h.current;if(e){var t;y((null!=(t=e.textContent)?t:"").trim())}},[l.children]),(0,p.jsx)(J.ItemSlot,{scope:n,disabled:r,textValue:null!=a?a:g,children:(0,p.jsx)(U,{asChild:!0,...d,focusable:!r,children:(0,p.jsx)(u.sG.div,{role:"menuitem","data-highlighted":m?"":void 0,"aria-disabled":r||void 0,"data-disabled":r?"":void 0,...l,ref:f,onPointerMove:(0,o.m)(e.onPointerMove,e0(e=>{r?c.onItemLeave(e):(c.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.m)(e.onPointerLeave,e0(e=>c.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>v(!0)),onBlur:(0,o.m)(e.onBlur,()=>v(!1))})})})}),eD=i.forwardRef((e,t)=>{let{checked:n=!1,onCheckedChange:r,...i}=e;return(0,p.jsx)(eI,{scope:e.__scopeMenu,checked:n,children:(0,p.jsx)(eR,{role:"menuitemcheckbox","aria-checked":eQ(n)?"mixed":n,...i,ref:t,"data-state":eJ(n),onSelect:(0,o.m)(i.onSelect,()=>null==r?void 0:r(!!eQ(n)||!n),{checkForDefaultPrevented:!1})})})});eD.displayName="MenuCheckboxItem";var ek="MenuRadioGroup",[ej,eL]=en(ek,{value:void 0,onValueChange:()=>{}}),eO=i.forwardRef((e,t)=>{let{value:n,onValueChange:r,...i}=e,o=(0,R.c)(r);return(0,p.jsx)(ej,{scope:e.__scopeMenu,value:n,onValueChange:o,children:(0,p.jsx)(eP,{...i,ref:t})})});eO.displayName=ek;var eV="MenuRadioItem",eN=i.forwardRef((e,t)=>{let{value:n,...r}=e,i=eL(eV,e.__scopeMenu),s=n===i.value;return(0,p.jsx)(eI,{scope:e.__scopeMenu,checked:s,children:(0,p.jsx)(eR,{role:"menuitemradio","aria-checked":s,...r,ref:t,"data-state":eJ(s),onSelect:(0,o.m)(r.onSelect,()=>{var e;return null==(e=i.onValueChange)?void 0:e.call(i,n)},{checkForDefaultPrevented:!1})})})});eN.displayName=eV;var eF="MenuItemIndicator",[eI,eB]=en(eF,{checked:!1}),e_=i.forwardRef((e,t)=>{let{__scopeMenu:n,forceMount:r,...i}=e,o=eB(eF,n);return(0,p.jsx)(C.C,{present:r||eQ(o.checked)||!0===o.checked,children:(0,p.jsx)(u.sG.span,{...i,ref:t,"data-state":eJ(o.checked)})})});e_.displayName=eF;var eW=i.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,p.jsx)(u.sG.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});eW.displayName="MenuSeparator";var eU=i.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,i=ei(n);return(0,p.jsx)(A.i3,{...i,...r,ref:t})});eU.displayName="MenuArrow";var eG="MenuSub",[e$,eK]=en(eG),eH=e=>{let{__scopeMenu:t,children:n,open:r=!1,onOpenChange:o}=e,s=ea(eG,t),a=ei(t),[l,u]=i.useState(null),[c,d]=i.useState(null),h=(0,R.c)(o);return i.useEffect(()=>(!1===s.open&&h(!1),()=>h(!1)),[s.open,h]),(0,p.jsx)(A.bL,{...a,children:(0,p.jsx)(es,{scope:t,open:r,onOpenChange:h,content:c,onContentChange:d,children:(0,p.jsx)(e$,{scope:t,contentId:(0,P.B)(),triggerId:(0,P.B)(),trigger:l,onTriggerChange:u,children:n})})})};eH.displayName=eG;var eq="MenuSubTrigger",ez=i.forwardRef((e,t)=>{let n=ea(eq,e.__scopeMenu),r=eu(eq,e.__scopeMenu),a=eK(eq,e.__scopeMenu),l=ey(eq,e.__scopeMenu),u=i.useRef(null),{pointerGraceTimerRef:c,onPointerGraceIntentChange:d}=l,h={__scopeMenu:e.__scopeMenu},f=i.useCallback(()=>{u.current&&window.clearTimeout(u.current),u.current=null},[]);return i.useEffect(()=>f,[f]),i.useEffect(()=>{let e=c.current;return()=>{window.clearTimeout(e),d(null)}},[c,d]),(0,p.jsx)(ed,{asChild:!0,...h,children:(0,p.jsx)(eM,{id:a.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":a.contentId,"data-state":eZ(n.open),...e,ref:(0,s.t)(t,a.onTriggerChange),onClick:t=>{var r;null==(r=e.onClick)||r.call(e,t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,e0(t=>{l.onItemEnter(t),!t.defaultPrevented&&(e.disabled||n.open||u.current||(l.onPointerGraceIntentChange(null),u.current=window.setTimeout(()=>{n.onOpenChange(!0),f()},100)))})),onPointerLeave:(0,o.m)(e.onPointerLeave,e0(e=>{var t,r;f();let i=null==(t=n.content)?void 0:t.getBoundingClientRect();if(i){let t=null==(r=n.content)?void 0:r.dataset.side,o="right"===t,s=i[o?"left":"right"],a=i[o?"right":"left"];l.onPointerGraceIntentChange({area:[{x:e.clientX+(o?-5:5),y:e.clientY},{x:s,y:i.top},{x:a,y:i.top},{x:a,y:i.bottom},{x:s,y:i.bottom}],side:t}),window.clearTimeout(c.current),c.current=window.setTimeout(()=>l.onPointerGraceIntentChange(null),300)}else{if(l.onTriggerLeave(e),e.defaultPrevented)return;l.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,t=>{let i=""!==l.searchRef.current;if(!e.disabled&&(!i||" "!==t.key)&&X[r.dir].includes(t.key)){var o;n.onOpenChange(!0),null==(o=n.content)||o.focus(),t.preventDefault()}})})})});ez.displayName=eq;var eY="MenuSubContent",eX=i.forwardRef((e,t)=>{let n=ep(ev,e.__scopeMenu),{forceMount:r=n.forceMount,...a}=e,l=ea(ev,e.__scopeMenu),u=eu(ev,e.__scopeMenu),c=eK(eY,e.__scopeMenu),d=i.useRef(null),h=(0,s.s)(t,d);return(0,p.jsx)(J.Provider,{scope:e.__scopeMenu,children:(0,p.jsx)(C.C,{present:r||l.open,children:(0,p.jsx)(J.Slot,{scope:e.__scopeMenu,children:(0,p.jsx)(eE,{id:c.contentId,"aria-labelledby":c.triggerId,...a,ref:h,align:"start",side:"rtl"===u.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var t;u.isUsingKeyboardRef.current&&(null==(t=d.current)||t.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==c.trigger&&l.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{u.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),n=Z[u.dir].includes(e.key);if(t&&n){var r;l.onOpenChange(!1),null==(r=c.trigger)||r.focus(),e.preventDefault()}})})})})})});function eZ(e){return e?"open":"closed"}function eQ(e){return"indeterminate"===e}function eJ(e){return eQ(e)?"indeterminate":e?"checked":"unchecked"}function e0(e){return t=>"mouse"===t.pointerType?e(t):void 0}eX.displayName=eY;var e1="DropdownMenu",[e9,e2]=(0,a.A)(e1,[er]),e4=er(),[e5,e8]=e9(e1),e7=e=>{let{__scopeDropdownMenu:t,children:n,dir:r,open:o,defaultOpen:s,onOpenChange:a,modal:u=!0}=e,c=e4(t),d=i.useRef(null),[h,f]=(0,l.i)({prop:o,defaultProp:null!=s&&s,onChange:a,caller:e1});return(0,p.jsx)(e5,{scope:t,triggerId:(0,P.B)(),triggerRef:d,contentId:(0,P.B)(),open:h,onOpenChange:f,onOpenToggle:i.useCallback(()=>f(e=>!e),[f]),modal:u,children:(0,p.jsx)(ec,{...c,open:h,onOpenChange:f,dir:r,modal:u,children:n})})};e7.displayName=e1;var e6="DropdownMenuTrigger",e3=i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,disabled:r=!1,...i}=e,a=e8(e6,n),l=e4(n);return(0,p.jsx)(ed,{asChild:!0,...l,children:(0,p.jsx)(u.sG.button,{type:"button",id:a.triggerId,"aria-haspopup":"menu","aria-expanded":a.open,"aria-controls":a.open?a.contentId:void 0,"data-state":a.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...i,ref:(0,s.t)(t,a.triggerRef),onPointerDown:(0,o.m)(e.onPointerDown,e=>{!r&&0===e.button&&!1===e.ctrlKey&&(a.onOpenToggle(),a.open||e.preventDefault())}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{!r&&(["Enter"," "].includes(e.key)&&a.onOpenToggle(),"ArrowDown"===e.key&&a.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});e3.displayName=e6;var te=e=>{let{__scopeDropdownMenu:t,...n}=e,r=e4(t);return(0,p.jsx)(em,{...r,...n})};te.displayName="DropdownMenuPortal";var tt="DropdownMenuContent",tn=i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,s=e8(tt,n),a=e4(n),l=i.useRef(!1);return(0,p.jsx)(ex,{id:s.contentId,"aria-labelledby":s.triggerId,...a,...r,ref:t,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;l.current||null==(t=s.triggerRef.current)||t.focus(),l.current=!1,e.preventDefault()}),onInteractOutside:(0,o.m)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,r=2===t.button||n;(!s.modal||r)&&(l.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});tn.displayName=tt;var tr=i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,i=e4(n);return(0,p.jsx)(eP,{...i,...r,ref:t})});tr.displayName="DropdownMenuGroup";var ti=i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,i=e4(n);return(0,p.jsx)(eA,{...i,...r,ref:t})});ti.displayName="DropdownMenuLabel";var to=i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,i=e4(n);return(0,p.jsx)(eR,{...i,...r,ref:t})});to.displayName="DropdownMenuItem";var ts=i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,i=e4(n);return(0,p.jsx)(eD,{...i,...r,ref:t})});ts.displayName="DropdownMenuCheckboxItem";var ta=i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,i=e4(n);return(0,p.jsx)(eO,{...i,...r,ref:t})});ta.displayName="DropdownMenuRadioGroup";var tl=i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,i=e4(n);return(0,p.jsx)(eN,{...i,...r,ref:t})});tl.displayName="DropdownMenuRadioItem";var tu=i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,i=e4(n);return(0,p.jsx)(e_,{...i,...r,ref:t})});tu.displayName="DropdownMenuItemIndicator";var tc=i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,i=e4(n);return(0,p.jsx)(eW,{...i,...r,ref:t})});tc.displayName="DropdownMenuSeparator",i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,i=e4(n);return(0,p.jsx)(eU,{...i,...r,ref:t})}).displayName="DropdownMenuArrow";var td=i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,i=e4(n);return(0,p.jsx)(ez,{...i,...r,ref:t})});td.displayName="DropdownMenuSubTrigger";var th=i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,i=e4(n);return(0,p.jsx)(eX,{...i,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});th.displayName="DropdownMenuSubContent";var tf=e7,tp=e3,tm=te,tv=tn,tg=tr,ty=ti,tx=to,tw=ts,tb=ta,tT=tl,tE=tu,tP=tc,tA=e=>{let{__scopeDropdownMenu:t,children:n,open:r,onOpenChange:i,defaultOpen:o}=e,s=e4(t),[a,u]=(0,l.i)({prop:r,defaultProp:null!=o&&o,onChange:i,caller:"DropdownMenuSub"});return(0,p.jsx)(eH,{...s,open:a,onOpenChange:u,children:n})},tS=td,tC=th},11330:(e,t,n)=>{n.d(t,{l:()=>r});let r=e=>e},11675:(e,t,n)=>{n.d(t,{j:()=>i,p:()=>s});let r=e=>t=>"string"==typeof t&&t.startsWith(e),i=r("--"),o=r("var(--"),s=e=>!!o(e)&&a.test(e.split("/*")[0].trim()),a=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu},12463:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(98889).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},14196:(e,t,n)=>{n.d(t,{k:()=>i});var r=n(95775);function i(e,t,n){var i;return i=-t,(0,r.f)(e,7*i,n)}},14576:(e,t,n)=>{n.d(t,{V:()=>a});var r=n(44627),i=n(94866),o=n(71097),s=n(69292);let a={test:(0,s.$)("hsl","hue"),parse:(0,s.q)("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:s=1})=>"hsla("+Math.round(e)+", "+i.KN.transform((0,o.a)(t))+", "+i.KN.transform((0,o.a)(n))+", "+(0,o.a)(r.X4.transform(s))+")"}},15265:(e,t,n)=>{function r(e,t){-1===e.indexOf(t)&&e.push(t)}function i(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}n.d(t,{Ai:()=>i,Kq:()=>r})},17247:(e,t,n)=>{n.d(t,{N:()=>i});var r=n(41987),i=globalThis?.document?r.useLayoutEffect:()=>{}},17848:(e,t,n)=>{n.d(t,{Oh:()=>o});var r=n(41987),i=0;function o(){r.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=n[0])?e:s()),document.body.insertAdjacentElement("beforeend",null!=(t=n[1])?t:s()),i++,()=>{1===i&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),i--}},[])}function s(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},18400:(e,t,n)=>{n.d(t,{C:()=>s});var r=n(41987),i=n(49640),o=n(17247),s=e=>{let{present:t,children:n}=e,s=function(e){var t,n;let[i,s]=r.useState(),l=r.useRef(null),u=r.useRef(e),c=r.useRef("none"),[d,h]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=a(l.current);c.current="mounted"===d?e:"none"},[d]),(0,o.N)(()=>{let t=l.current,n=u.current;if(n!==e){let r=c.current,i=a(t);e?h("MOUNT"):"none"===i||(null==t?void 0:t.display)==="none"?h("UNMOUNT"):n&&r!==i?h("ANIMATION_OUT"):h("UNMOUNT"),u.current=e}},[e,h]),(0,o.N)(()=>{if(i){var e;let t,n=null!=(e=i.ownerDocument.defaultView)?e:window,r=e=>{let r=a(l.current).includes(e.animationName);if(e.target===i&&r&&(h("ANIMATION_END"),!u.current)){let e=i.style.animationFillMode;i.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===i.style.animationFillMode&&(i.style.animationFillMode=e)})}},o=e=>{e.target===i&&(c.current=a(l.current))};return i.addEventListener("animationstart",o),i.addEventListener("animationcancel",r),i.addEventListener("animationend",r),()=>{n.clearTimeout(t),i.removeEventListener("animationstart",o),i.removeEventListener("animationcancel",r),i.removeEventListener("animationend",r)}}h("ANIMATION_END")},[i,h]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{l.current=e?getComputedStyle(e):null,s(e)},[])}}(t),l="function"==typeof n?n({present:s.isPresent}):r.Children.only(n),u=(0,i.s)(s.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,i=r&&"isReactWarning"in r&&r.isReactWarning;return i?e.ref:(i=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(l));return"function"==typeof n||s.isPresent?r.cloneElement(l,{ref:u}):null};function a(e){return(null==e?void 0:e.animationName)||"none"}s.displayName="Presence"},18411:(e,t,n)=>{n.d(t,{q:()=>r});let r=(e,t,n)=>{let r=t-e;return 0===r?1:(n-e)/r}},19922:(e,t,n)=>{n.d(t,{w:()=>i});var r=n(47869);function i(e,t){return"function"==typeof e?e(t):e&&"object"==typeof e&&r._P in e?e[r._P](t):e instanceof Date?new e.constructor(t):new Date(t)}},23027:(e,t,n)=>{n.d(t,{Eq:()=>c});var r=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},i=new WeakMap,o=new WeakMap,s={},a=0,l=function(e){return e&&(e.host||l(e.parentNode))},u=function(e,t,n,r){var u=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=l(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});s[n]||(s[n]=new WeakMap);var c=s[n],d=[],h=new Set,f=new Set(u),p=function(e){!e||h.has(e)||(h.add(e),p(e.parentNode))};u.forEach(p);var m=function(e){!e||f.has(e)||Array.prototype.forEach.call(e.children,function(e){if(h.has(e))m(e);else try{var t=e.getAttribute(r),s=null!==t&&"false"!==t,a=(i.get(e)||0)+1,l=(c.get(e)||0)+1;i.set(e,a),c.set(e,l),d.push(e),1===a&&s&&o.set(e,!0),1===l&&e.setAttribute(n,"true"),s||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return m(t),h.clear(),a++,function(){d.forEach(function(e){var t=i.get(e)-1,s=c.get(e)-1;i.set(e,t),c.set(e,s),t||(o.has(e)||e.removeAttribute(r),o.delete(e)),s||e.removeAttribute(n)}),--a||(i=new WeakMap,i=new WeakMap,o=new WeakMap,s={})}},c=function(e,t,n){void 0===n&&(n="data-aria-hidden");var i=Array.from(Array.isArray(e)?e:[e]),o=t||r(e);return o?(i.push.apply(i,Array.from(o.querySelectorAll("[aria-live], script"))),u(i,o,n,"aria-hidden")):function(){return null}}},25173:(e,t,n)=>{n.d(t,{G$:()=>z,Hs:()=>b,UC:()=>en,VY:()=>ei,ZL:()=>ee,bL:()=>Q,bm:()=>eo,hE:()=>er,hJ:()=>et,l9:()=>J});var r=n(41987),i=n(29254),o=n(49640),s=n(80482),a=n(60728),l=n(87076),u=n(82491),c=n(38552),d=n(93343),h=n(18400),f=n(7156),p=n(17848),m=n(39160),v=n(23027),g=n(79649),y=n(44995),x="Dialog",[w,b]=(0,s.A)(x),[T,E]=w(x),P=e=>{let{__scopeDialog:t,children:n,open:i,defaultOpen:o,onOpenChange:s,modal:u=!0}=e,c=r.useRef(null),d=r.useRef(null),[h,f]=(0,l.i)({prop:i,defaultProp:null!=o&&o,onChange:s,caller:x});return(0,y.jsx)(T,{scope:t,triggerRef:c,contentRef:d,contentId:(0,a.B)(),titleId:(0,a.B)(),descriptionId:(0,a.B)(),open:h,onOpenChange:f,onOpenToggle:r.useCallback(()=>f(e=>!e),[f]),modal:u,children:n})};P.displayName=x;var A="DialogTrigger",S=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,s=E(A,n),a=(0,o.s)(t,s.triggerRef);return(0,y.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":s.open,"aria-controls":s.contentId,"data-state":H(s.open),...r,ref:a,onClick:(0,i.m)(e.onClick,s.onOpenToggle)})});S.displayName=A;var C="DialogPortal",[R,M]=w(C,{forceMount:void 0}),D=e=>{let{__scopeDialog:t,forceMount:n,children:i,container:o}=e,s=E(C,t);return(0,y.jsx)(R,{scope:t,forceMount:n,children:r.Children.map(i,e=>(0,y.jsx)(h.C,{present:n||s.open,children:(0,y.jsx)(d.Z,{asChild:!0,container:o,children:e})}))})};D.displayName=C;var k="DialogOverlay",j=r.forwardRef((e,t)=>{let n=M(k,e.__scopeDialog),{forceMount:r=n.forceMount,...i}=e,o=E(k,e.__scopeDialog);return o.modal?(0,y.jsx)(h.C,{present:r||o.open,children:(0,y.jsx)(O,{...i,ref:t})}):null});j.displayName=k;var L=(0,g.TL)("DialogOverlay.RemoveScroll"),O=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=E(k,n);return(0,y.jsx)(m.A,{as:L,allowPinchZoom:!0,shards:[i.contentRef],children:(0,y.jsx)(f.sG.div,{"data-state":H(i.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),V="DialogContent",N=r.forwardRef((e,t)=>{let n=M(V,e.__scopeDialog),{forceMount:r=n.forceMount,...i}=e,o=E(V,e.__scopeDialog);return(0,y.jsx)(h.C,{present:r||o.open,children:o.modal?(0,y.jsx)(F,{...i,ref:t}):(0,y.jsx)(I,{...i,ref:t})})});N.displayName=V;var F=r.forwardRef((e,t)=>{let n=E(V,e.__scopeDialog),s=r.useRef(null),a=(0,o.s)(t,n.contentRef,s);return r.useEffect(()=>{let e=s.current;if(e)return(0,v.Eq)(e)},[]),(0,y.jsx)(B,{...e,ref:a,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,i.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=n.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,i.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,i.m)(e.onFocusOutside,e=>e.preventDefault())})}),I=r.forwardRef((e,t)=>{let n=E(V,e.__scopeDialog),i=r.useRef(!1),o=r.useRef(!1);return(0,y.jsx)(B,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var r,s;null==(r=e.onCloseAutoFocus)||r.call(e,t),t.defaultPrevented||(i.current||null==(s=n.triggerRef.current)||s.focus(),t.preventDefault()),i.current=!1,o.current=!1},onInteractOutside:t=>{var r,s;null==(r=e.onInteractOutside)||r.call(e,t),t.defaultPrevented||(i.current=!0,"pointerdown"===t.detail.originalEvent.type&&(o.current=!0));let a=t.target;(null==(s=n.triggerRef.current)?void 0:s.contains(a))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}})}),B=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:i,onOpenAutoFocus:s,onCloseAutoFocus:a,...l}=e,d=E(V,n),h=r.useRef(null),f=(0,o.s)(t,h);return(0,p.Oh)(),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(c.n,{asChild:!0,loop:!0,trapped:i,onMountAutoFocus:s,onUnmountAutoFocus:a,children:(0,y.jsx)(u.qW,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":H(d.open),...l,ref:f,onDismiss:()=>d.onOpenChange(!1)})}),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(X,{titleId:d.titleId}),(0,y.jsx)(Z,{contentRef:h,descriptionId:d.descriptionId})]})]})}),_="DialogTitle",W=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=E(_,n);return(0,y.jsx)(f.sG.h2,{id:i.titleId,...r,ref:t})});W.displayName=_;var U="DialogDescription",G=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=E(U,n);return(0,y.jsx)(f.sG.p,{id:i.descriptionId,...r,ref:t})});G.displayName=U;var $="DialogClose",K=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=E($,n);return(0,y.jsx)(f.sG.button,{type:"button",...r,ref:t,onClick:(0,i.m)(e.onClick,()=>o.onOpenChange(!1))})});function H(e){return e?"open":"closed"}K.displayName=$;var q="DialogTitleWarning",[z,Y]=(0,s.q)(q,{contentName:V,titleName:_,docsSlug:"dialog"}),X=e=>{let{titleId:t}=e,n=Y(q),i="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return r.useEffect(()=>{t&&(document.getElementById(t)||console.error(i))},[i,t]),null},Z=e=>{let{contentRef:t,descriptionId:n}=e,i=Y("DialogDescriptionWarning"),o="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(i.contentName,"}.");return r.useEffect(()=>{var e;let r=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");n&&r&&(document.getElementById(n)||console.warn(o))},[o,t,n]),null},Q=P,J=S,ee=D,et=j,en=N,er=W,ei=G,eo=K},25784:(e,t,n)=>{n.d(t,{Mz:()=>e3,i3:()=>tt,UC:()=>te,bL:()=>e6,Bk:()=>eH});var r=n(41987);let i=["top","right","bottom","left"],o=Math.min,s=Math.max,a=Math.round,l=Math.floor,u=e=>({x:e,y:e}),c={left:"right",right:"left",bottom:"top",top:"bottom"},d={start:"end",end:"start"};function h(e,t){return"function"==typeof e?e(t):e}function f(e){return e.split("-")[0]}function p(e){return e.split("-")[1]}function m(e){return"x"===e?"y":"x"}function v(e){return"y"===e?"height":"width"}let g=new Set(["top","bottom"]);function y(e){return g.has(f(e))?"y":"x"}function x(e){return e.replace(/start|end/g,e=>d[e])}let w=["left","right"],b=["right","left"],T=["top","bottom"],E=["bottom","top"];function P(e){return e.replace(/left|right|bottom|top/g,e=>c[e])}function A(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function S(e){let{x:t,y:n,width:r,height:i}=e;return{width:r,height:i,top:n,left:t,right:t+r,bottom:n+i,x:t,y:n}}function C(e,t,n){let r,{reference:i,floating:o}=e,s=y(t),a=m(y(t)),l=v(a),u=f(t),c="y"===s,d=i.x+i.width/2-o.width/2,h=i.y+i.height/2-o.height/2,g=i[l]/2-o[l]/2;switch(u){case"top":r={x:d,y:i.y-o.height};break;case"bottom":r={x:d,y:i.y+i.height};break;case"right":r={x:i.x+i.width,y:h};break;case"left":r={x:i.x-o.width,y:h};break;default:r={x:i.x,y:i.y}}switch(p(t)){case"start":r[a]-=g*(n&&c?-1:1);break;case"end":r[a]+=g*(n&&c?-1:1)}return r}let R=async(e,t,n)=>{let{placement:r="bottom",strategy:i="absolute",middleware:o=[],platform:s}=n,a=o.filter(Boolean),l=await (null==s.isRTL?void 0:s.isRTL(t)),u=await s.getElementRects({reference:e,floating:t,strategy:i}),{x:c,y:d}=C(u,r,l),h=r,f={},p=0;for(let n=0;n<a.length;n++){let{name:o,fn:m}=a[n],{x:v,y:g,data:y,reset:x}=await m({x:c,y:d,initialPlacement:r,placement:h,strategy:i,middlewareData:f,rects:u,platform:s,elements:{reference:e,floating:t}});c=null!=v?v:c,d=null!=g?g:d,f={...f,[o]:{...f[o],...y}},x&&p<=50&&(p++,"object"==typeof x&&(x.placement&&(h=x.placement),x.rects&&(u=!0===x.rects?await s.getElementRects({reference:e,floating:t,strategy:i}):x.rects),{x:c,y:d}=C(u,h,l)),n=-1)}return{x:c,y:d,placement:h,strategy:i,middlewareData:f}};async function M(e,t){var n;void 0===t&&(t={});let{x:r,y:i,platform:o,rects:s,elements:a,strategy:l}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=h(t,e),m=A(p),v=a[f?"floating"===d?"reference":"floating":d],g=S(await o.getClippingRect({element:null==(n=await (null==o.isElement?void 0:o.isElement(v)))||n?v:v.contextElement||await (null==o.getDocumentElement?void 0:o.getDocumentElement(a.floating)),boundary:u,rootBoundary:c,strategy:l})),y="floating"===d?{x:r,y:i,width:s.floating.width,height:s.floating.height}:s.reference,x=await (null==o.getOffsetParent?void 0:o.getOffsetParent(a.floating)),w=await (null==o.isElement?void 0:o.isElement(x))&&await (null==o.getScale?void 0:o.getScale(x))||{x:1,y:1},b=S(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:y,offsetParent:x,strategy:l}):y);return{top:(g.top-b.top+m.top)/w.y,bottom:(b.bottom-g.bottom+m.bottom)/w.y,left:(g.left-b.left+m.left)/w.x,right:(b.right-g.right+m.right)/w.x}}function D(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function k(e){return i.some(t=>e[t]>=0)}let j=new Set(["left","top"]);async function L(e,t){let{placement:n,platform:r,elements:i}=e,o=await (null==r.isRTL?void 0:r.isRTL(i.floating)),s=f(n),a=p(n),l="y"===y(n),u=j.has(s)?-1:1,c=o&&l?-1:1,d=h(t,e),{mainAxis:m,crossAxis:v,alignmentAxis:g}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return a&&"number"==typeof g&&(v="end"===a?-1*g:g),l?{x:v*c,y:m*u}:{x:m*u,y:v*c}}function O(){return"undefined"!=typeof window}function V(e){return I(e)?(e.nodeName||"").toLowerCase():"#document"}function N(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function F(e){var t;return null==(t=(I(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function I(e){return!!O()&&(e instanceof Node||e instanceof N(e).Node)}function B(e){return!!O()&&(e instanceof Element||e instanceof N(e).Element)}function _(e){return!!O()&&(e instanceof HTMLElement||e instanceof N(e).HTMLElement)}function W(e){return!!O()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof N(e).ShadowRoot)}let U=new Set(["inline","contents"]);function G(e){let{overflow:t,overflowX:n,overflowY:r,display:i}=ee(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!U.has(i)}let $=new Set(["table","td","th"]),K=[":popover-open",":modal"];function H(e){return K.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let q=["transform","translate","scale","rotate","perspective"],z=["transform","translate","scale","rotate","perspective","filter"],Y=["paint","layout","strict","content"];function X(e){let t=Z(),n=B(e)?ee(e):e;return q.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||z.some(e=>(n.willChange||"").includes(e))||Y.some(e=>(n.contain||"").includes(e))}function Z(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let Q=new Set(["html","body","#document"]);function J(e){return Q.has(V(e))}function ee(e){return N(e).getComputedStyle(e)}function et(e){return B(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function en(e){if("html"===V(e))return e;let t=e.assignedSlot||e.parentNode||W(e)&&e.host||F(e);return W(t)?t.host:t}function er(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let i=function e(t){let n=en(t);return J(n)?t.ownerDocument?t.ownerDocument.body:t.body:_(n)&&G(n)?n:e(n)}(e),o=i===(null==(r=e.ownerDocument)?void 0:r.body),s=N(i);if(o){let e=ei(s);return t.concat(s,s.visualViewport||[],G(i)?i:[],e&&n?er(e):[])}return t.concat(i,er(i,[],n))}function ei(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function eo(e){let t=ee(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,i=_(e),o=i?e.offsetWidth:n,s=i?e.offsetHeight:r,l=a(n)!==o||a(r)!==s;return l&&(n=o,r=s),{width:n,height:r,$:l}}function es(e){return B(e)?e:e.contextElement}function ea(e){let t=es(e);if(!_(t))return u(1);let n=t.getBoundingClientRect(),{width:r,height:i,$:o}=eo(t),s=(o?a(n.width):n.width)/r,l=(o?a(n.height):n.height)/i;return s&&Number.isFinite(s)||(s=1),l&&Number.isFinite(l)||(l=1),{x:s,y:l}}let el=u(0);function eu(e){let t=N(e);return Z()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:el}function ec(e,t,n,r){var i;void 0===t&&(t=!1),void 0===n&&(n=!1);let o=e.getBoundingClientRect(),s=es(e),a=u(1);t&&(r?B(r)&&(a=ea(r)):a=ea(e));let l=(void 0===(i=n)&&(i=!1),r&&(!i||r===N(s))&&i)?eu(s):u(0),c=(o.left+l.x)/a.x,d=(o.top+l.y)/a.y,h=o.width/a.x,f=o.height/a.y;if(s){let e=N(s),t=r&&B(r)?N(r):r,n=e,i=ei(n);for(;i&&r&&t!==n;){let e=ea(i),t=i.getBoundingClientRect(),r=ee(i),o=t.left+(i.clientLeft+parseFloat(r.paddingLeft))*e.x,s=t.top+(i.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,d*=e.y,h*=e.x,f*=e.y,c+=o,d+=s,i=ei(n=N(i))}}return S({width:h,height:f,x:c,y:d})}function ed(e,t){let n=et(e).scrollLeft;return t?t.left+n:ec(F(e)).left+n}function eh(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:ed(e,r)),y:r.top+t.scrollTop}}let ef=new Set(["absolute","fixed"]);function ep(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=N(e),r=F(e),i=n.visualViewport,o=r.clientWidth,s=r.clientHeight,a=0,l=0;if(i){o=i.width,s=i.height;let e=Z();(!e||e&&"fixed"===t)&&(a=i.offsetLeft,l=i.offsetTop)}return{width:o,height:s,x:a,y:l}}(e,n);else if("document"===t)r=function(e){let t=F(e),n=et(e),r=e.ownerDocument.body,i=s(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),o=s(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+ed(e),l=-n.scrollTop;return"rtl"===ee(r).direction&&(a+=s(t.clientWidth,r.clientWidth)-i),{width:i,height:o,x:a,y:l}}(F(e));else if(B(t))r=function(e,t){let n=ec(e,!0,"fixed"===t),r=n.top+e.clientTop,i=n.left+e.clientLeft,o=_(e)?ea(e):u(1),s=e.clientWidth*o.x,a=e.clientHeight*o.y;return{width:s,height:a,x:i*o.x,y:r*o.y}}(t,n);else{let n=eu(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return S(r)}function em(e){return"static"===ee(e).position}function ev(e,t){if(!_(e)||"fixed"===ee(e).position)return null;if(t)return t(e);let n=e.offsetParent;return F(e)===n&&(n=n.ownerDocument.body),n}function eg(e,t){var n;let r=N(e);if(H(e))return r;if(!_(e)){let t=en(e);for(;t&&!J(t);){if(B(t)&&!em(t))return t;t=en(t)}return r}let i=ev(e,t);for(;i&&(n=i,$.has(V(n)))&&em(i);)i=ev(i,t);return i&&J(i)&&em(i)&&!X(i)?r:i||function(e){let t=en(e);for(;_(t)&&!J(t);){if(X(t))return t;if(H(t))break;t=en(t)}return null}(e)||r}let ey=async function(e){let t=this.getOffsetParent||eg,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=_(t),i=F(t),o="fixed"===n,s=ec(e,!0,o,t),a={scrollLeft:0,scrollTop:0},l=u(0);if(r||!r&&!o)if(("body"!==V(t)||G(i))&&(a=et(t)),r){let e=ec(t,!0,o,t);l.x=e.x+t.clientLeft,l.y=e.y+t.clientTop}else i&&(l.x=ed(i));o&&!r&&i&&(l.x=ed(i));let c=!i||r||o?u(0):eh(i,a);return{x:s.left+a.scrollLeft-l.x-c.x,y:s.top+a.scrollTop-l.y-c.y,width:s.width,height:s.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ex={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:i}=e,o="fixed"===i,s=F(r),a=!!t&&H(t.floating);if(r===s||a&&o)return n;let l={scrollLeft:0,scrollTop:0},c=u(1),d=u(0),h=_(r);if((h||!h&&!o)&&(("body"!==V(r)||G(s))&&(l=et(r)),_(r))){let e=ec(r);c=ea(r),d.x=e.x+r.clientLeft,d.y=e.y+r.clientTop}let f=!s||h||o?u(0):eh(s,l,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-l.scrollLeft*c.x+d.x+f.x,y:n.y*c.y-l.scrollTop*c.y+d.y+f.y}},getDocumentElement:F,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:i}=e,a=[..."clippingAncestors"===n?H(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=er(e,[],!1).filter(e=>B(e)&&"body"!==V(e)),i=null,o="fixed"===ee(e).position,s=o?en(e):e;for(;B(s)&&!J(s);){let t=ee(s),n=X(s);n||"fixed"!==t.position||(i=null),(o?!n&&!i:!n&&"static"===t.position&&!!i&&ef.has(i.position)||G(s)&&!n&&function e(t,n){let r=en(t);return!(r===n||!B(r)||J(r))&&("fixed"===ee(r).position||e(r,n))}(e,s))?r=r.filter(e=>e!==s):i=t,s=en(s)}return t.set(e,r),r}(t,this._c):[].concat(n),r],l=a[0],u=a.reduce((e,n)=>{let r=ep(t,n,i);return e.top=s(r.top,e.top),e.right=o(r.right,e.right),e.bottom=o(r.bottom,e.bottom),e.left=s(r.left,e.left),e},ep(t,l,i));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}},getOffsetParent:eg,getElementRects:ey,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=eo(e);return{width:t,height:n}},getScale:ea,isElement:B,isRTL:function(e){return"rtl"===ee(e).direction}};function ew(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let eb=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:i,rects:a,platform:l,elements:u,middlewareData:c}=t,{element:d,padding:f=0}=h(e,t)||{};if(null==d)return{};let g=A(f),x={x:n,y:r},w=m(y(i)),b=v(w),T=await l.getDimensions(d),E="y"===w,P=E?"clientHeight":"clientWidth",S=a.reference[b]+a.reference[w]-x[w]-a.floating[b],C=x[w]-a.reference[w],R=await (null==l.getOffsetParent?void 0:l.getOffsetParent(d)),M=R?R[P]:0;M&&await (null==l.isElement?void 0:l.isElement(R))||(M=u.floating[P]||a.floating[b]);let D=M/2-T[b]/2-1,k=o(g[E?"top":"left"],D),j=o(g[E?"bottom":"right"],D),L=M-T[b]-j,O=M/2-T[b]/2+(S/2-C/2),V=s(k,o(O,L)),N=!c.arrow&&null!=p(i)&&O!==V&&a.reference[b]/2-(O<k?k:j)-T[b]/2<0,F=N?O<k?O-k:O-L:0;return{[w]:x[w]+F,data:{[w]:V,centerOffset:O-V-F,...N&&{alignmentOffset:F}},reset:N}}}),eT=(e,t,n)=>{let r=new Map,i={platform:ex,...n},o={...i.platform,_c:r};return R(e,t,{...i,platform:o})};var eE=n(9604),eP="undefined"!=typeof document?r.useLayoutEffect:function(){};function eA(e,t){let n,r,i;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!eA(e[r],t[r]))return!1;return!0}if((n=(i=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,i[r]))return!1;for(r=n;0!=r--;){let n=i[r];if(("_owner"!==n||!e.$$typeof)&&!eA(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function eS(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eC(e,t){let n=eS(e);return Math.round(t*n)/n}function eR(e){let t=r.useRef(e);return eP(()=>{t.current=e}),t}let eM=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?eb({element:n.current,padding:r}).fn(t):{}:n?eb({element:n,padding:r}).fn(t):{}}}),eD=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:i,y:o,placement:s,middlewareData:a}=t,l=await L(t,e);return s===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:i+l.x,y:o+l.y,data:{...l,placement:s}}}}}(e),options:[e,t]}),ek=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:i}=t,{mainAxis:a=!0,crossAxis:l=!1,limiter:u={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...c}=h(e,t),d={x:n,y:r},p=await M(t,c),v=y(f(i)),g=m(v),x=d[g],w=d[v];if(a){let e="y"===g?"top":"left",t="y"===g?"bottom":"right",n=x+p[e],r=x-p[t];x=s(n,o(x,r))}if(l){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",n=w+p[e],r=w-p[t];w=s(n,o(w,r))}let b=u.fn({...t,[g]:x,[v]:w});return{...b,data:{x:b.x-n,y:b.y-r,enabled:{[g]:a,[v]:l}}}}}}(e),options:[e,t]}),ej=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:i,rects:o,middlewareData:s}=t,{offset:a=0,mainAxis:l=!0,crossAxis:u=!0}=h(e,t),c={x:n,y:r},d=y(i),p=m(d),v=c[p],g=c[d],x=h(a,t),w="number"==typeof x?{mainAxis:x,crossAxis:0}:{mainAxis:0,crossAxis:0,...x};if(l){let e="y"===p?"height":"width",t=o.reference[p]-o.floating[e]+w.mainAxis,n=o.reference[p]+o.reference[e]-w.mainAxis;v<t?v=t:v>n&&(v=n)}if(u){var b,T;let e="y"===p?"width":"height",t=j.has(f(i)),n=o.reference[d]-o.floating[e]+(t&&(null==(b=s.offset)?void 0:b[d])||0)+(t?0:w.crossAxis),r=o.reference[d]+o.reference[e]+(t?0:(null==(T=s.offset)?void 0:T[d])||0)-(t?w.crossAxis:0);g<n?g=n:g>r&&(g=r)}return{[p]:v,[d]:g}}}}(e),options:[e,t]}),eL=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,i,o,s;let{placement:a,middlewareData:l,rects:u,initialPlacement:c,platform:d,elements:g}=t,{mainAxis:A=!0,crossAxis:S=!0,fallbackPlacements:C,fallbackStrategy:R="bestFit",fallbackAxisSideDirection:D="none",flipAlignment:k=!0,...j}=h(e,t);if(null!=(n=l.arrow)&&n.alignmentOffset)return{};let L=f(a),O=y(c),V=f(c)===c,N=await (null==d.isRTL?void 0:d.isRTL(g.floating)),F=C||(V||!k?[P(c)]:function(e){let t=P(e);return[x(e),t,x(t)]}(c)),I="none"!==D;!C&&I&&F.push(...function(e,t,n,r){let i=p(e),o=function(e,t,n){switch(e){case"top":case"bottom":if(n)return t?b:w;return t?w:b;case"left":case"right":return t?T:E;default:return[]}}(f(e),"start"===n,r);return i&&(o=o.map(e=>e+"-"+i),t&&(o=o.concat(o.map(x)))),o}(c,k,D,N));let B=[c,...F],_=await M(t,j),W=[],U=(null==(r=l.flip)?void 0:r.overflows)||[];if(A&&W.push(_[L]),S){let e=function(e,t,n){void 0===n&&(n=!1);let r=p(e),i=m(y(e)),o=v(i),s="x"===i?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[o]>t.floating[o]&&(s=P(s)),[s,P(s)]}(a,u,N);W.push(_[e[0]],_[e[1]])}if(U=[...U,{placement:a,overflows:W}],!W.every(e=>e<=0)){let e=((null==(i=l.flip)?void 0:i.index)||0)+1,t=B[e];if(t&&("alignment"!==S||O===y(t)||U.every(e=>e.overflows[0]>0&&y(e.placement)===O)))return{data:{index:e,overflows:U},reset:{placement:t}};let n=null==(o=U.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:o.placement;if(!n)switch(R){case"bestFit":{let e=null==(s=U.filter(e=>{if(I){let t=y(e.placement);return t===O||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:s[0];e&&(n=e);break}case"initialPlacement":n=c}if(a!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),eO=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let i,a,{placement:l,rects:u,platform:c,elements:d}=t,{apply:m=()=>{},...v}=h(e,t),g=await M(t,v),x=f(l),w=p(l),b="y"===y(l),{width:T,height:E}=u.floating;"top"===x||"bottom"===x?(i=x,a=w===(await (null==c.isRTL?void 0:c.isRTL(d.floating))?"start":"end")?"left":"right"):(a=x,i="end"===w?"top":"bottom");let P=E-g.top-g.bottom,A=T-g.left-g.right,S=o(E-g[i],P),C=o(T-g[a],A),R=!t.middlewareData.shift,D=S,k=C;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(k=A),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(D=P),R&&!w){let e=s(g.left,0),t=s(g.right,0),n=s(g.top,0),r=s(g.bottom,0);b?k=T-2*(0!==e||0!==t?e+t:s(g.left,g.right)):D=E-2*(0!==n||0!==r?n+r:s(g.top,g.bottom))}await m({...t,availableWidth:k,availableHeight:D});let j=await c.getDimensions(d.floating);return T!==j.width||E!==j.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eV=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...i}=h(e,t);switch(r){case"referenceHidden":{let e=D(await M(t,{...i,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:k(e)}}}case"escaped":{let e=D(await M(t,{...i,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:k(e)}}}default:return{}}}}}(e),options:[e,t]}),eN=(e,t)=>({...eM(e),options:[e,t]});var eF=n(7156),eI=n(44995),eB=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:i=5,...o}=e;return(0,eI.jsx)(eF.sG.svg,{...o,ref:t,width:r,height:i,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eI.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eB.displayName="Arrow";var e_=n(49640),eW=n(80482),eU=n(93568),eG=n(17247),e$="Popper",[eK,eH]=(0,eW.A)(e$),[eq,ez]=eK(e$),eY=e=>{let{__scopePopper:t,children:n}=e,[i,o]=r.useState(null);return(0,eI.jsx)(eq,{scope:t,anchor:i,onAnchorChange:o,children:n})};eY.displayName=e$;var eX="PopperAnchor",eZ=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:i,...o}=e,s=ez(eX,n),a=r.useRef(null),l=(0,e_.s)(t,a);return r.useEffect(()=>{s.onAnchorChange((null==i?void 0:i.current)||a.current)}),i?null:(0,eI.jsx)(eF.sG.div,{...o,ref:l})});eZ.displayName=eX;var eQ="PopperContent",[eJ,e0]=eK(eQ),e1=r.forwardRef((e,t)=>{var n,i,a,u,c,d,h,f;let{__scopePopper:p,side:m="bottom",sideOffset:v=0,align:g="center",alignOffset:y=0,arrowPadding:x=0,avoidCollisions:w=!0,collisionBoundary:b=[],collisionPadding:T=0,sticky:E="partial",hideWhenDetached:P=!1,updatePositionStrategy:A="optimized",onPlaced:S,...C}=e,R=ez(eQ,p),[M,D]=r.useState(null),k=(0,e_.s)(t,e=>D(e)),[j,L]=r.useState(null),O=function(e){let[t,n]=r.useState(void 0);return(0,eG.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,i;if(!Array.isArray(t)||!t.length)return;let o=t[0];if("borderBoxSize"in o){let e=o.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,i=t.blockSize}else r=e.offsetWidth,i=e.offsetHeight;n({width:r,height:i})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(j),V=null!=(h=null==O?void 0:O.width)?h:0,N=null!=(f=null==O?void 0:O.height)?f:0,I="number"==typeof T?T:{top:0,right:0,bottom:0,left:0,...T},B=Array.isArray(b)?b:[b],_=B.length>0,W={padding:I,boundary:B.filter(e5),altBoundary:_},{refs:U,floatingStyles:G,placement:$,isPositioned:K,middlewareData:H}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:i=[],platform:o,elements:{reference:s,floating:a}={},transform:l=!0,whileElementsMounted:u,open:c}=e,[d,h]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[f,p]=r.useState(i);eA(f,i)||p(i);let[m,v]=r.useState(null),[g,y]=r.useState(null),x=r.useCallback(e=>{e!==E.current&&(E.current=e,v(e))},[]),w=r.useCallback(e=>{e!==P.current&&(P.current=e,y(e))},[]),b=s||m,T=a||g,E=r.useRef(null),P=r.useRef(null),A=r.useRef(d),S=null!=u,C=eR(u),R=eR(o),M=eR(c),D=r.useCallback(()=>{if(!E.current||!P.current)return;let e={placement:t,strategy:n,middleware:f};R.current&&(e.platform=R.current),eT(E.current,P.current,e).then(e=>{let t={...e,isPositioned:!1!==M.current};k.current&&!eA(A.current,t)&&(A.current=t,eE.flushSync(()=>{h(t)}))})},[f,t,n,R,M]);eP(()=>{!1===c&&A.current.isPositioned&&(A.current.isPositioned=!1,h(e=>({...e,isPositioned:!1})))},[c]);let k=r.useRef(!1);eP(()=>(k.current=!0,()=>{k.current=!1}),[]),eP(()=>{if(b&&(E.current=b),T&&(P.current=T),b&&T){if(C.current)return C.current(b,T,D);D()}},[b,T,D,C,S]);let j=r.useMemo(()=>({reference:E,floating:P,setReference:x,setFloating:w}),[x,w]),L=r.useMemo(()=>({reference:b,floating:T}),[b,T]),O=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!L.floating)return e;let t=eC(L.floating,d.x),r=eC(L.floating,d.y);return l?{...e,transform:"translate("+t+"px, "+r+"px)",...eS(L.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,l,L.floating,d.x,d.y]);return r.useMemo(()=>({...d,update:D,refs:j,elements:L,floatingStyles:O}),[d,D,j,L,O])}({strategy:"fixed",placement:m+("center"!==g?"-"+g:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let i;void 0===r&&(r={});let{ancestorScroll:a=!0,ancestorResize:u=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:d="function"==typeof IntersectionObserver,animationFrame:h=!1}=r,f=es(e),p=a||u?[...f?er(f):[],...er(t)]:[];p.forEach(e=>{a&&e.addEventListener("scroll",n,{passive:!0}),u&&e.addEventListener("resize",n)});let m=f&&d?function(e,t){let n,r=null,i=F(e);function a(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function u(c,d){void 0===c&&(c=!1),void 0===d&&(d=1),a();let h=e.getBoundingClientRect(),{left:f,top:p,width:m,height:v}=h;if(c||t(),!m||!v)return;let g=l(p),y=l(i.clientWidth-(f+m)),x={rootMargin:-g+"px "+-y+"px "+-l(i.clientHeight-(p+v))+"px "+-l(f)+"px",threshold:s(0,o(1,d))||1},w=!0;function b(t){let r=t[0].intersectionRatio;if(r!==d){if(!w)return u();r?u(!1,r):n=setTimeout(()=>{u(!1,1e-7)},1e3)}1!==r||ew(h,e.getBoundingClientRect())||u(),w=!1}try{r=new IntersectionObserver(b,{...x,root:i.ownerDocument})}catch(e){r=new IntersectionObserver(b,x)}r.observe(e)}(!0),a}(f,n):null,v=-1,g=null;c&&(g=new ResizeObserver(e=>{let[r]=e;r&&r.target===f&&g&&(g.unobserve(t),cancelAnimationFrame(v),v=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),n()}),f&&!h&&g.observe(f),g.observe(t));let y=h?ec(e):null;return h&&function t(){let r=ec(e);y&&!ew(y,r)&&n(),y=r,i=requestAnimationFrame(t)}(),n(),()=>{var e;p.forEach(e=>{a&&e.removeEventListener("scroll",n),u&&e.removeEventListener("resize",n)}),null==m||m(),null==(e=g)||e.disconnect(),g=null,h&&cancelAnimationFrame(i)}}(...t,{animationFrame:"always"===A})},elements:{reference:R.anchor},middleware:[eD({mainAxis:v+N,alignmentAxis:y}),w&&ek({mainAxis:!0,crossAxis:!1,limiter:"partial"===E?ej():void 0,...W}),w&&eL({...W}),eO({...W,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:i}=e,{width:o,height:s}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(i,"px")),a.setProperty("--radix-popper-anchor-width","".concat(o,"px")),a.setProperty("--radix-popper-anchor-height","".concat(s,"px"))}}),j&&eN({element:j,padding:x}),e8({arrowWidth:V,arrowHeight:N}),P&&eV({strategy:"referenceHidden",...W})]}),[q,z]=e7($),Y=(0,eU.c)(S);(0,eG.N)(()=>{K&&(null==Y||Y())},[K,Y]);let X=null==(n=H.arrow)?void 0:n.x,Z=null==(i=H.arrow)?void 0:i.y,Q=(null==(a=H.arrow)?void 0:a.centerOffset)!==0,[J,ee]=r.useState();return(0,eG.N)(()=>{M&&ee(window.getComputedStyle(M).zIndex)},[M]),(0,eI.jsx)("div",{ref:U.setFloating,"data-radix-popper-content-wrapper":"",style:{...G,transform:K?G.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:J,"--radix-popper-transform-origin":[null==(u=H.transformOrigin)?void 0:u.x,null==(c=H.transformOrigin)?void 0:c.y].join(" "),...(null==(d=H.hide)?void 0:d.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eI.jsx)(eJ,{scope:p,placedSide:q,onArrowChange:L,arrowX:X,arrowY:Z,shouldHideArrow:Q,children:(0,eI.jsx)(eF.sG.div,{"data-side":q,"data-align":z,...C,ref:k,style:{...C.style,animation:K?void 0:"none"}})})})});e1.displayName=eQ;var e9="PopperArrow",e2={top:"bottom",right:"left",bottom:"top",left:"right"},e4=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,i=e0(e9,n),o=e2[i.placedSide];return(0,eI.jsx)("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[o]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:(0,eI.jsx)(eB,{...r,ref:t,style:{...r.style,display:"block"}})})});function e5(e){return null!==e}e4.displayName=e9;var e8=e=>({name:"transformOrigin",options:e,fn(t){var n,r,i,o,s;let{placement:a,rects:l,middlewareData:u}=t,c=(null==(n=u.arrow)?void 0:n.centerOffset)!==0,d=c?0:e.arrowWidth,h=c?0:e.arrowHeight,[f,p]=e7(a),m={start:"0%",center:"50%",end:"100%"}[p],v=(null!=(o=null==(r=u.arrow)?void 0:r.x)?o:0)+d/2,g=(null!=(s=null==(i=u.arrow)?void 0:i.y)?s:0)+h/2,y="",x="";return"bottom"===f?(y=c?m:"".concat(v,"px"),x="".concat(-h,"px")):"top"===f?(y=c?m:"".concat(v,"px"),x="".concat(l.floating.height+h,"px")):"right"===f?(y="".concat(-h,"px"),x=c?m:"".concat(g,"px")):"left"===f&&(y="".concat(l.floating.width+h,"px"),x=c?m:"".concat(g,"px")),{data:{x:y,y:x}}}});function e7(e){let[t,n="center"]=e.split("-");return[t,n]}var e6=eY,e3=eZ,te=e1,tt=e4},26468:(e,t,n)=>{n.d(t,{a:()=>i});var r=n(19922);function i(e,t){return(0,r.w)(t||e,e)}},26655:(e,t,n)=>{n.d(t,{P:()=>a});var r=n(19922),i=n(55810),o=n(76411),s=n(95775);function a(e,t){var n;return(0,o.r)((0,r.w)((null==t?void 0:t.in)||e,e),(n=(0,i.A)((null==t?void 0:t.in)||e),(0,s.f)(n,-1,void 0)))}},27644:(e,t,n)=>{n.d(t,{UC:()=>$,Kq:()=>W,bL:()=>U,l9:()=>G});var r=n(41987),i=n(29254),o=n(49640),s=n(80482),a=n(82491),l=n(60728),u=n(25784),c=(n(93343),n(18400)),d=n(7156),h=n(79649),f=n(87076),p=n(44995),m=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),v=r.forwardRef((e,t)=>(0,p.jsx)(d.sG.span,{...e,ref:t,style:{...m,...e.style}}));v.displayName="VisuallyHidden";var[g,y]=(0,s.A)("Tooltip",[u.Bk]),x=(0,u.Bk)(),w="TooltipProvider",b="tooltip.open",[T,E]=g(w),P=e=>{let{__scopeTooltip:t,delayDuration:n=700,skipDelayDuration:i=300,disableHoverableContent:o=!1,children:s}=e,a=r.useRef(!0),l=r.useRef(!1),u=r.useRef(0);return r.useEffect(()=>{let e=u.current;return()=>window.clearTimeout(e)},[]),(0,p.jsx)(T,{scope:t,isOpenDelayedRef:a,delayDuration:n,onOpen:r.useCallback(()=>{window.clearTimeout(u.current),a.current=!1},[]),onClose:r.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>a.current=!0,i)},[i]),isPointerInTransitRef:l,onPointerInTransitChange:r.useCallback(e=>{l.current=e},[]),disableHoverableContent:o,children:s})};P.displayName=w;var A="Tooltip",[S,C]=g(A),R=e=>{let{__scopeTooltip:t,children:n,open:i,defaultOpen:o,onOpenChange:s,disableHoverableContent:a,delayDuration:c}=e,d=E(A,e.__scopeTooltip),h=x(t),[m,v]=r.useState(null),g=(0,l.B)(),y=r.useRef(0),w=null!=a?a:d.disableHoverableContent,T=null!=c?c:d.delayDuration,P=r.useRef(!1),[C,R]=(0,f.i)({prop:i,defaultProp:null!=o&&o,onChange:e=>{e?(d.onOpen(),document.dispatchEvent(new CustomEvent(b))):d.onClose(),null==s||s(e)},caller:A}),M=r.useMemo(()=>C?P.current?"delayed-open":"instant-open":"closed",[C]),D=r.useCallback(()=>{window.clearTimeout(y.current),y.current=0,P.current=!1,R(!0)},[R]),k=r.useCallback(()=>{window.clearTimeout(y.current),y.current=0,R(!1)},[R]),j=r.useCallback(()=>{window.clearTimeout(y.current),y.current=window.setTimeout(()=>{P.current=!0,R(!0),y.current=0},T)},[T,R]);return r.useEffect(()=>()=>{y.current&&(window.clearTimeout(y.current),y.current=0)},[]),(0,p.jsx)(u.bL,{...h,children:(0,p.jsx)(S,{scope:t,contentId:g,open:C,stateAttribute:M,trigger:m,onTriggerChange:v,onTriggerEnter:r.useCallback(()=>{d.isOpenDelayedRef.current?j():D()},[d.isOpenDelayedRef,j,D]),onTriggerLeave:r.useCallback(()=>{w?k():(window.clearTimeout(y.current),y.current=0)},[k,w]),onOpen:D,onClose:k,disableHoverableContent:w,children:n})})};R.displayName=A;var M="TooltipTrigger",D=r.forwardRef((e,t)=>{let{__scopeTooltip:n,...s}=e,a=C(M,n),l=E(M,n),c=x(n),h=r.useRef(null),f=(0,o.s)(t,h,a.onTriggerChange),m=r.useRef(!1),v=r.useRef(!1),g=r.useCallback(()=>m.current=!1,[]);return r.useEffect(()=>()=>document.removeEventListener("pointerup",g),[g]),(0,p.jsx)(u.Mz,{asChild:!0,...c,children:(0,p.jsx)(d.sG.button,{"aria-describedby":a.open?a.contentId:void 0,"data-state":a.stateAttribute,...s,ref:f,onPointerMove:(0,i.m)(e.onPointerMove,e=>{"touch"!==e.pointerType&&(v.current||l.isPointerInTransitRef.current||(a.onTriggerEnter(),v.current=!0))}),onPointerLeave:(0,i.m)(e.onPointerLeave,()=>{a.onTriggerLeave(),v.current=!1}),onPointerDown:(0,i.m)(e.onPointerDown,()=>{a.open&&a.onClose(),m.current=!0,document.addEventListener("pointerup",g,{once:!0})}),onFocus:(0,i.m)(e.onFocus,()=>{m.current||a.onOpen()}),onBlur:(0,i.m)(e.onBlur,a.onClose),onClick:(0,i.m)(e.onClick,a.onClose)})})});D.displayName=M;var[k,j]=g("TooltipPortal",{forceMount:void 0}),L="TooltipContent",O=r.forwardRef((e,t)=>{let n=j(L,e.__scopeTooltip),{forceMount:r=n.forceMount,side:i="top",...o}=e,s=C(L,e.__scopeTooltip);return(0,p.jsx)(c.C,{present:r||s.open,children:s.disableHoverableContent?(0,p.jsx)(B,{side:i,...o,ref:t}):(0,p.jsx)(V,{side:i,...o,ref:t})})}),V=r.forwardRef((e,t)=>{let n=C(L,e.__scopeTooltip),i=E(L,e.__scopeTooltip),s=r.useRef(null),a=(0,o.s)(t,s),[l,u]=r.useState(null),{trigger:c,onClose:d}=n,h=s.current,{onPointerInTransitChange:f}=i,m=r.useCallback(()=>{u(null),f(!1)},[f]),v=r.useCallback((e,t)=>{let n=e.currentTarget,r={x:e.clientX,y:e.clientY},i=function(e,t){let n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),i=Math.abs(t.right-e.x),o=Math.abs(t.left-e.x);switch(Math.min(n,r,i,o)){case o:return"left";case i:return"right";case n:return"top";case r:return"bottom";default:throw Error("unreachable")}}(r,n.getBoundingClientRect());u(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:1*!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let n=0;n<e.length;n++){let r=e[n];for(;t.length>=2;){let e=t[t.length-1],n=t[t.length-2];if((e.x-n.x)*(r.y-n.y)>=(e.y-n.y)*(r.x-n.x))t.pop();else break}t.push(r)}t.pop();let n=[];for(let t=e.length-1;t>=0;t--){let r=e[t];for(;n.length>=2;){let e=n[n.length-1],t=n[n.length-2];if((e.x-t.x)*(r.y-t.y)>=(e.y-t.y)*(r.x-t.x))n.pop();else break}n.push(r)}return(n.pop(),1===t.length&&1===n.length&&t[0].x===n[0].x&&t[0].y===n[0].y)?t:t.concat(n)}(t)}([...function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n})}return r}(r,i),...function(e){let{top:t,right:n,bottom:r,left:i}=e;return[{x:i,y:t},{x:n,y:t},{x:n,y:r},{x:i,y:r}]}(t.getBoundingClientRect())])),f(!0)},[f]);return r.useEffect(()=>()=>m(),[m]),r.useEffect(()=>{if(c&&h){let e=e=>v(e,h),t=e=>v(e,c);return c.addEventListener("pointerleave",e),h.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),h.removeEventListener("pointerleave",t)}}},[c,h,v,m]),r.useEffect(()=>{if(l){let e=e=>{let t=e.target,n={x:e.clientX,y:e.clientY},r=(null==c?void 0:c.contains(t))||(null==h?void 0:h.contains(t)),i=!function(e,t){let{x:n,y:r}=e,i=!1;for(let e=0,o=t.length-1;e<t.length;o=e++){let s=t[e],a=t[o],l=s.x,u=s.y,c=a.x,d=a.y;u>r!=d>r&&n<(c-l)*(r-u)/(d-u)+l&&(i=!i)}return i}(n,l);r?m():i&&(m(),d())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,h,l,d,m]),(0,p.jsx)(B,{...e,ref:a})}),[N,F]=g(A,{isInside:!1}),I=(0,h.Dc)("TooltipContent"),B=r.forwardRef((e,t)=>{let{__scopeTooltip:n,children:i,"aria-label":o,onEscapeKeyDown:s,onPointerDownOutside:l,...c}=e,d=C(L,n),h=x(n),{onClose:f}=d;return r.useEffect(()=>(document.addEventListener(b,f),()=>document.removeEventListener(b,f)),[f]),r.useEffect(()=>{if(d.trigger){let e=e=>{let t=e.target;(null==t?void 0:t.contains(d.trigger))&&f()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[d.trigger,f]),(0,p.jsx)(a.qW,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:s,onPointerDownOutside:l,onFocusOutside:e=>e.preventDefault(),onDismiss:f,children:(0,p.jsxs)(u.UC,{"data-state":d.stateAttribute,...h,...c,ref:t,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,p.jsx)(I,{children:i}),(0,p.jsx)(N,{scope:n,isInside:!0,children:(0,p.jsx)(v,{id:d.contentId,role:"tooltip",children:o||i})})]})})});O.displayName=L;var _="TooltipArrow";r.forwardRef((e,t)=>{let{__scopeTooltip:n,...r}=e,i=x(n);return F(_,n).isInside?null:(0,p.jsx)(u.i3,{...i,...r,ref:t})}).displayName=_;var W=P,U=R,G=D,$=O},27911:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(98889).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},29254:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},30281:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(98889).A)("PanelLeft",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]])},31088:(e,t,n)=>{n.d(t,{F:()=>i});let r=(e,t)=>n=>t(e(n)),i=(...e)=>e.reduce(r)},31546:(e,t,n)=>{n.d(t,{S:()=>r});let r=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu},37741:(e,t,n)=>{n.d(t,{v:()=>i});var r=n(15265);class i{constructor(){this.subscriptions=[]}add(e){return(0,r.Kq)(this.subscriptions,e),()=>(0,r.Ai)(this.subscriptions,e)}notify(e,t,n){let r=this.subscriptions.length;if(r)if(1===r)this.subscriptions[0](e,t,n);else for(let i=0;i<r;i++){let r=this.subscriptions[i];r&&r(e,t,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}},38094:(e,t,n)=>{n.d(t,{M:()=>i});var r=n(41987);function i(e){let t=(0,r.useRef)(null);return null===t.current&&(t.current=e()),t.current}},38552:(e,t,n)=>{n.d(t,{n:()=>d});var r=n(41987),i=n(49640),o=n(7156),s=n(93568),a=n(44995),l="focusScope.autoFocusOnMount",u="focusScope.autoFocusOnUnmount",c={bubbles:!1,cancelable:!0},d=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:d=!1,onMountAutoFocus:v,onUnmountAutoFocus:g,...y}=e,[x,w]=r.useState(null),b=(0,s.c)(v),T=(0,s.c)(g),E=r.useRef(null),P=(0,i.s)(t,e=>w(e)),A=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(d){let e=function(e){if(A.paused||!x)return;let t=e.target;x.contains(t)?E.current=t:p(E.current,{select:!0})},t=function(e){if(A.paused||!x)return;let t=e.relatedTarget;null!==t&&(x.contains(t)||p(E.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&p(x)});return x&&n.observe(x,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[d,x,A.paused]),r.useEffect(()=>{if(x){m.add(A);let e=document.activeElement;if(!x.contains(e)){let t=new CustomEvent(l,c);x.addEventListener(l,b),x.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(p(r,{select:t}),document.activeElement!==n)return}(h(x).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&p(x))}return()=>{x.removeEventListener(l,b),setTimeout(()=>{let t=new CustomEvent(u,c);x.addEventListener(u,T),x.dispatchEvent(t),t.defaultPrevented||p(null!=e?e:document.body,{select:!0}),x.removeEventListener(u,T),m.remove(A)},0)}}},[x,b,T,A]);let S=r.useCallback(e=>{if(!n&&!d||A.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[i,o]=function(e){let t=h(e);return[f(t,e),f(t.reverse(),e)]}(t);i&&o?e.shiftKey||r!==o?e.shiftKey&&r===i&&(e.preventDefault(),n&&p(o,{select:!0})):(e.preventDefault(),n&&p(i,{select:!0})):r===t&&e.preventDefault()}},[n,d,A.paused]);return(0,a.jsx)(o.sG.div,{tabIndex:-1,...y,ref:P,onKeyDown:S})});function h(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function f(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function p(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}d.displayName="FocusScope";var m=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=v(e,t)).unshift(t)},remove(t){var n;null==(n=(e=v(e,t))[0])||n.resume()}}}();function v(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},39160:(e,t,n)=>{n.d(t,{A:()=>H});var r,i,o=function(){return(o=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};Object.create;Object.create;var s=("function"==typeof SuppressedError&&SuppressedError,n(41987)),a="right-scroll-bar-position",l="width-before-scroll-bar";function u(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var c="undefined"!=typeof window?s.useLayoutEffect:s.useEffect,d=new WeakMap,h=function(){return(h=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};Object.create;function f(e){return e}Object.create,"function"==typeof SuppressedError&&SuppressedError;var p=function(e){void 0===e&&(e={});var t,n,r,i,o=(t=null,void 0===n&&(n=f),r=[],i=!1,{read:function(){if(i)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,i);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(i=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){i=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var o=function(){var n=t;t=[],n.forEach(e)},s=function(){return Promise.resolve().then(o)};s(),r={push:function(e){t.push(e),s()},filter:function(e){return t=t.filter(e),r}}}});return o.options=h({async:!0,ssr:!1},e),o}(),m=function(){},v=s.forwardRef(function(e,t){var n,r,i,a,l=s.useRef(null),h=s.useState({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:m}),f=h[0],v=h[1],g=e.forwardProps,y=e.children,x=e.className,w=e.removeScrollBar,b=e.enabled,T=e.shards,E=e.sideCar,P=e.noRelative,A=e.noIsolation,S=e.inert,C=e.allowPinchZoom,R=e.as,M=e.gapMode,D=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,r=Object.getOwnPropertySymbols(e);i<r.length;i++)0>t.indexOf(r[i])&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(n[r[i]]=e[r[i]]);return n}(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),k=(n=[l,t],r=function(e){return n.forEach(function(t){return u(t,e)})},(i=(0,s.useState)(function(){return{value:null,callback:r,facade:{get current(){return i.value},set current(value){var e=i.value;e!==value&&(i.value=value,i.callback(value,e))}}}})[0]).callback=r,a=i.facade,c(function(){var e=d.get(a);if(e){var t=new Set(e),r=new Set(n),i=a.current;t.forEach(function(e){r.has(e)||u(e,null)}),r.forEach(function(e){t.has(e)||u(e,i)})}d.set(a,n)},[n]),a),j=o(o({},D),f);return s.createElement(s.Fragment,null,b&&s.createElement(E,{sideCar:p,removeScrollBar:w,shards:T,noRelative:P,noIsolation:A,inert:S,setCallbacks:v,allowPinchZoom:!!C,lockRef:l,gapMode:M}),g?s.cloneElement(s.Children.only(y),o(o({},j),{ref:k})):s.createElement(void 0===R?"div":R,o({},j,{className:x,ref:k}),y))});v.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},v.classNames={fullWidth:l,zeroRight:a};var g=function(e){var t=e.sideCar,n=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,r=Object.getOwnPropertySymbols(e);i<r.length;i++)0>t.indexOf(r[i])&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(n[r[i]]=e[r[i]]);return n}(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return s.createElement(r,h({},n))};g.isSideCarExport=!0;var y=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=i||n.nc;return t&&e.setAttribute("nonce",t),e}())){var o,s;(o=t).styleSheet?o.styleSheet.cssText=r:o.appendChild(document.createTextNode(r)),s=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(s)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},x=function(){var e=y();return function(t,n){s.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},w=function(){var e=x();return function(t){return e(t.styles,t.dynamic),null}},b={left:0,top:0,right:0,gap:0},T=function(e){return parseInt(e||"",10)||0},E=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],i=t["padding"===e?"paddingRight":"marginRight"];return[T(n),T(r),T(i)]},P=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return b;var t=E(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},A=w(),S="data-scroll-locked",C=function(e,t,n,r){var i=e.left,o=e.top,s=e.right,u=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(u,"px ").concat(r,";\n  }\n  body[").concat(S,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(i,"px;\n    padding-top: ").concat(o,"px;\n    padding-right: ").concat(s,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(u,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(u,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(a," {\n    right: ").concat(u,"px ").concat(r,";\n  }\n  \n  .").concat(l," {\n    margin-right: ").concat(u,"px ").concat(r,";\n  }\n  \n  .").concat(a," .").concat(a," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(l," .").concat(l," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(S,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(u,"px;\n  }\n")},R=function(){var e=parseInt(document.body.getAttribute(S)||"0",10);return isFinite(e)?e:0},M=function(){s.useEffect(function(){return document.body.setAttribute(S,(R()+1).toString()),function(){var e=R()-1;e<=0?document.body.removeAttribute(S):document.body.setAttribute(S,e.toString())}},[])},D=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,i=void 0===r?"margin":r;M();var o=s.useMemo(function(){return P(i)},[i]);return s.createElement(A,{styles:C(o,!t,i,n?"":"!important")})},k=!1;if("undefined"!=typeof window)try{var j=Object.defineProperty({},"passive",{get:function(){return k=!0,!0}});window.addEventListener("test",j,j),window.removeEventListener("test",j,j)}catch(e){k=!1}var L=!!k&&{passive:!1},O=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},V=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),N(e,r)){var i=F(e,r);if(i[1]>i[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},N=function(e,t){return"v"===e?O(t,"overflowY"):O(t,"overflowX")},F=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},I=function(e,t,n,r,i){var o,s=(o=window.getComputedStyle(t).direction,"h"===e&&"rtl"===o?-1:1),a=s*r,l=n.target,u=t.contains(l),c=!1,d=a>0,h=0,f=0;do{if(!l)break;var p=F(e,l),m=p[0],v=p[1]-p[2]-s*m;(m||v)&&N(e,l)&&(h+=v,f+=m);var g=l.parentNode;l=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!u&&l!==document.body||u&&(t.contains(l)||t===l));return d&&(i&&1>Math.abs(h)||!i&&a>h)?c=!0:!d&&(i&&1>Math.abs(f)||!i&&-a>f)&&(c=!0),c},B=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},_=function(e){return[e.deltaX,e.deltaY]},W=function(e){return e&&"current"in e?e.current:e},U=0,G=[];let $=(r=function(e){var t=s.useRef([]),n=s.useRef([0,0]),r=s.useRef(),i=s.useState(U++)[0],o=s.useState(w)[0],a=s.useRef(e);s.useEffect(function(){a.current=e},[e]),s.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(i));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,i=0,o=t.length;i<o;i++)!r&&i in t||(r||(r=Array.prototype.slice.call(t,0,i)),r[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(W),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(i))}),function(){document.body.classList.remove("block-interactivity-".concat(i)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(i))})}}},[e.inert,e.lockRef.current,e.shards]);var l=s.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!a.current.allowPinchZoom;var i,o=B(e),s=n.current,l="deltaX"in e?e.deltaX:s[0]-o[0],u="deltaY"in e?e.deltaY:s[1]-o[1],c=e.target,d=Math.abs(l)>Math.abs(u)?"h":"v";if("touches"in e&&"h"===d&&"range"===c.type)return!1;var h=V(d,c);if(!h)return!0;if(h?i=d:(i="v"===d?"h":"v",h=V(d,c)),!h)return!1;if(!r.current&&"changedTouches"in e&&(l||u)&&(r.current=i),!i)return!0;var f=r.current||i;return I(f,t,e,"h"===f?l:u,!0)},[]),u=s.useCallback(function(e){if(G.length&&G[G.length-1]===o){var n="deltaY"in e?_(e):B(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var i=(a.current.shards||[]).map(W).filter(Boolean).filter(function(t){return t.contains(e.target)});(i.length>0?l(e,i[0]):!a.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=s.useCallback(function(e,n,r,i){var o={name:e,delta:n,target:r,should:i,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(o),setTimeout(function(){t.current=t.current.filter(function(e){return e!==o})},1)},[]),d=s.useCallback(function(e){n.current=B(e),r.current=void 0},[]),h=s.useCallback(function(t){c(t.type,_(t),t.target,l(t,e.lockRef.current))},[]),f=s.useCallback(function(t){c(t.type,B(t),t.target,l(t,e.lockRef.current))},[]);s.useEffect(function(){return G.push(o),e.setCallbacks({onScrollCapture:h,onWheelCapture:h,onTouchMoveCapture:f}),document.addEventListener("wheel",u,L),document.addEventListener("touchmove",u,L),document.addEventListener("touchstart",d,L),function(){G=G.filter(function(e){return e!==o}),document.removeEventListener("wheel",u,L),document.removeEventListener("touchmove",u,L),document.removeEventListener("touchstart",d,L)}},[]);var p=e.removeScrollBar,m=e.inert;return s.createElement(s.Fragment,null,m?s.createElement(o,{styles:"\n  .block-interactivity-".concat(i," {pointer-events: none;}\n  .allow-interactivity-").concat(i," {pointer-events: all;}\n")}):null,p?s.createElement(D,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},p.useMedium(r),g);var K=s.forwardRef(function(e,t){return s.createElement(v,o({},e,{ref:t,sideCar:$}))});K.classNames=v.classNames;let H=K},41200:(e,t,n)=>{n.d(t,{o:()=>i});var r=n(26468);function i(e,t){let n=(0,r.a)(e,null==t?void 0:t.in);return n.setHours(0,0,0,0),n}},41261:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(98889).A)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},43877:(e,t,n)=>{n.d(t,{Q:()=>r});let r=(0,n(41987).createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"})},43992:(e,t,n)=>{function r(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}n.d(t,{P:()=>iD});let i=e=>Array.isArray(e);function o(e,t){if(!Array.isArray(t))return!1;let n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function s(e){return"string"==typeof e||Array.isArray(e)}function a(e){let t=[{},{}];return null==e||e.values.forEach((e,n)=>{t[0][n]=e.get(),t[1][n]=e.getVelocity()}),t}function l(e,t,n,r){if("function"==typeof t){let[i,o]=a(r);t=t(void 0!==n?n:e.custom,i,o)}if("string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t){let[i,o]=a(r);t=t(void 0!==n?n:e.custom,i,o)}return t}function u(e,t,n){let r=e.getProps();return l(r,t,void 0!==n?n:r.custom,e)}let c=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],d=["initial",...c];function h(e){let t;return()=>(void 0===t&&(t=e()),t)}let f=h(()=>void 0!==window.ScrollTimeline);class p{constructor(e){this.stop=()=>this.runAll("stop"),this.animations=e.filter(Boolean)}get finished(){return Promise.all(this.animations.map(e=>"finished"in e?e.finished:e))}getAll(e){return this.animations[0][e]}setAll(e,t){for(let n=0;n<this.animations.length;n++)this.animations[n][e]=t}attachTimeline(e,t){let n=this.animations.map(n=>f()&&n.attachTimeline?n.attachTimeline(e):"function"==typeof t?t(n):void 0);return()=>{n.forEach((e,t)=>{e&&e(),this.animations[t].stop()})}}get time(){return this.getAll("time")}set time(e){this.setAll("time",e)}get speed(){return this.getAll("speed")}set speed(e){this.setAll("speed",e)}get startTime(){return this.getAll("startTime")}get duration(){let e=0;for(let t=0;t<this.animations.length;t++)e=Math.max(e,this.animations[t].duration);return e}runAll(e){this.animations.forEach(t=>t[e]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class m extends p{then(e,t){return Promise.all(this.animations).then(e).catch(t)}}function v(e,t){return e?e[t]||e.default||e:void 0}function g(e){let t=0,n=e.next(t);for(;!n.done&&t<2e4;)t+=50,n=e.next(t);return t>=2e4?1/0:t}function y(e){return"function"==typeof e}function x(e,t){e.timeline=t,e.onfinish=null}let w=e=>Array.isArray(e)&&"number"==typeof e[0],b={linearEasing:void 0},T=function(e,t){let n=h(e);return()=>{var e;return null!=(e=b[t])?e:n()}}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(e){return!1}return!0},"linearEasing");var E,P,A=n(18411);let S=(e,t,n=10)=>{let r="",i=Math.max(Math.round(t/n),2);for(let t=0;t<i;t++)r+=e((0,A.q)(0,i-1,t))+", ";return`linear(${r.substring(0,r.length-2)})`},C=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,R={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:C([0,.65,.55,1]),circOut:C([.55,0,1,.45]),backIn:C([.31,.01,.66,-.59]),backOut:C([.33,1.53,.69,.99])},M={x:!1,y:!1};function D(e,t){let n=function(e,t,n){if(e instanceof Element)return[e];if("string"==typeof e){let t=document.querySelectorAll(e);return t?Array.from(t):[]}return Array.from(e)}(e),r=new AbortController;return[n,{passive:!0,...t,signal:r.signal},()=>r.abort()]}function k(e){return t=>{"touch"===t.pointerType||M.x||M.y||e(t)}}let j=(e,t)=>!!t&&(e===t||j(e,t.parentElement)),L=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary,O=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),V=new WeakSet;function N(e){return t=>{"Enter"===t.key&&e(t)}}function F(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}let I=(e,t)=>{let n=e.currentTarget;if(!n)return;let r=N(()=>{if(V.has(n))return;F(n,"down");let e=N(()=>{F(n,"up")});n.addEventListener("keyup",e,t),n.addEventListener("blur",()=>F(n,"cancel"),t)});n.addEventListener("keydown",r,t),n.addEventListener("blur",()=>n.removeEventListener("keydown",r),t)};function B(e){return L(e)&&!(M.x||M.y)}let _=e=>1e3*e,W=e=>e/1e3;var U=n(11330);let G=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],$=new Set(G),K=new Set(["width","height","top","left","right","bottom",...G]),H=e=>!!(e&&"object"==typeof e&&e.mix&&e.toValue),q=e=>i(e)?e[e.length-1]||0:e;var z=n(83718);let Y=e=>!!(e&&e.getVelocity);function X(e,t){let n=e.getValue("willChange");if(Y(n)&&n.add)return n.add(t)}let Z=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),Q="data-"+Z("framerAppearId");var J=n(62927),ee=n(93451);let et={current:!1},en=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e;function er(e,t,n,r){if(e===t&&n===r)return U.l;let i=t=>(function(e,t,n,r,i){let o,s,a=0;do(o=en(s=t+(n-t)/2,r,i)-e)>0?n=s:t=s;while(Math.abs(o)>1e-7&&++a<12);return s})(t,0,1,e,n);return e=>0===e||1===e?e:en(i(e),t,r)}let ei=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,eo=e=>t=>1-e(1-t),es=er(.33,1.53,.69,.99),ea=eo(es),el=ei(ea),eu=e=>(e*=2)<1?.5*ea(e):.5*(2-Math.pow(2,-10*(e-1))),ec=e=>1-Math.sin(Math.acos(e)),ed=eo(ec),eh=ei(ec),ef=e=>/^0[^.\s]+$/u.test(e);var ep=n(98718),em=n(31546);let ev=new Set(["brightness","contrast","saturate","opacity"]);function eg(e){let[t,n]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;let[r]=n.match(em.S)||[];if(!r)return e;let i=n.replace(r,""),o=+!!ev.has(t);return r!==n&&(o*=100),t+"("+o+i+")"}let ey=/\b([a-z-]*)\(.*?\)/gu,ex={...ep.f,getAnimatableNone:e=>{let t=e.match(ey);return t?t.map(eg).join(" "):e}};var ew=n(62396),eb=n(44627),eT=n(94866);let eE={borderWidth:eT.px,borderTopWidth:eT.px,borderRightWidth:eT.px,borderBottomWidth:eT.px,borderLeftWidth:eT.px,borderRadius:eT.px,radius:eT.px,borderTopLeftRadius:eT.px,borderTopRightRadius:eT.px,borderBottomRightRadius:eT.px,borderBottomLeftRadius:eT.px,width:eT.px,maxWidth:eT.px,height:eT.px,maxHeight:eT.px,top:eT.px,right:eT.px,bottom:eT.px,left:eT.px,padding:eT.px,paddingTop:eT.px,paddingRight:eT.px,paddingBottom:eT.px,paddingLeft:eT.px,margin:eT.px,marginTop:eT.px,marginRight:eT.px,marginBottom:eT.px,marginLeft:eT.px,backgroundPositionX:eT.px,backgroundPositionY:eT.px},eP={rotate:eT.uj,rotateX:eT.uj,rotateY:eT.uj,rotateZ:eT.uj,scale:eb.hs,scaleX:eb.hs,scaleY:eb.hs,scaleZ:eb.hs,skew:eT.uj,skewX:eT.uj,skewY:eT.uj,distance:eT.px,translateX:eT.px,translateY:eT.px,translateZ:eT.px,x:eT.px,y:eT.px,z:eT.px,perspective:eT.px,transformPerspective:eT.px,opacity:eb.X4,originX:eT.gQ,originY:eT.gQ,originZ:eT.px},eA={...eb.ai,transform:Math.round},eS={...eE,...eP,zIndex:eA,size:eT.px,fillOpacity:eb.X4,strokeOpacity:eb.X4,numOctaves:eA},eC={...eS,color:ew.y,backgroundColor:ew.y,outlineColor:ew.y,fill:ew.y,stroke:ew.y,borderColor:ew.y,borderTopColor:ew.y,borderRightColor:ew.y,borderBottomColor:ew.y,borderLeftColor:ew.y,filter:ex,WebkitFilter:ex},eR=e=>eC[e];function eM(e,t){let n=eR(e);return n!==ex&&(n=ep.f),n.getAnimatableNone?n.getAnimatableNone(t):void 0}let eD=new Set(["auto","none","0"]),ek=e=>e===eb.ai||e===eT.px,ej=(e,t)=>parseFloat(e.split(", ")[t]),eL=(e,t)=>(n,{transform:r})=>{if("none"===r||!r)return 0;let i=r.match(/^matrix3d\((.+)\)$/u);if(i)return ej(i[1],t);{let t=r.match(/^matrix\((.+)\)$/u);return t?ej(t[1],e):0}},eO=new Set(["x","y","z"]),eV=G.filter(e=>!eO.has(e)),eN={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:eL(4,13),y:eL(5,14)};eN.translateX=eN.x,eN.translateY=eN.y;let eF=new Set,eI=!1,eB=!1;function e_(){if(eB){let e=Array.from(eF).filter(e=>e.needsMeasurement),t=new Set(e.map(e=>e.element)),n=new Map;t.forEach(e=>{let t=function(e){let t=[];return eV.forEach(n=>{let r=e.getValue(n);void 0!==r&&(t.push([n,r.get()]),r.set(+!!n.startsWith("scale")))}),t}(e);t.length&&(n.set(e,t),e.render())}),e.forEach(e=>e.measureInitialState()),t.forEach(e=>{e.render();let t=n.get(e);t&&t.forEach(([t,n])=>{var r;null==(r=e.getValue(t))||r.set(n)})}),e.forEach(e=>e.measureEndState()),e.forEach(e=>{void 0!==e.suspendedScrollY&&window.scrollTo(0,e.suspendedScrollY)})}eB=!1,eI=!1,eF.forEach(e=>e.complete()),eF.clear()}function eW(){eF.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(eB=!0)})}class eU{constructor(e,t,n,r,i,o=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...e],this.onComplete=t,this.name=n,this.motionValue=r,this.element=i,this.isAsync=o}scheduleResolve(){this.isScheduled=!0,this.isAsync?(eF.add(this),eI||(eI=!0,J.Gt.read(eW),J.Gt.resolveKeyframes(e_))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:e,name:t,element:n,motionValue:r}=this;for(let i=0;i<e.length;i++)if(null===e[i])if(0===i){let i=null==r?void 0:r.get(),o=e[e.length-1];if(void 0!==i)e[0]=i;else if(n&&t){let r=n.readValue(t,o);null!=r&&(e[0]=r)}void 0===e[0]&&(e[0]=o),r&&void 0===i&&r.set(e[0])}else e[i]=e[i-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),eF.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,eF.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}var eG=n(59991);let e$=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e);var eK=n(11675);let eH=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,eq=e=>t=>t.test(e),ez=[eb.ai,eT.px,eT.KN,eT.uj,eT.vw,eT.vh,{test:e=>"auto"===e,parse:e=>e}],eY=e=>ez.find(eq(e));class eX extends eU{constructor(e,t,n,r,i){super(e,t,n,r,i,!0)}readKeyframes(){let{unresolvedKeyframes:e,element:t,name:n}=this;if(!t||!t.current)return;super.readKeyframes();for(let n=0;n<e.length;n++){let r=e[n];if("string"==typeof r&&(r=r.trim(),(0,eK.p)(r))){let i=function e(t,n,r=1){(0,eG.V)(r<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`);let[i,o]=function(e){let t=eH.exec(e);if(!t)return[,];let[,n,r,i]=t;return[`--${null!=n?n:r}`,i]}(t);if(!i)return;let s=window.getComputedStyle(n).getPropertyValue(i);if(s){let e=s.trim();return e$(e)?parseFloat(e):e}return(0,eK.p)(o)?e(o,n,r+1):o}(r,t.current);void 0!==i&&(e[n]=i),n===e.length-1&&(this.finalKeyframe=r)}}if(this.resolveNoneKeyframes(),!K.has(n)||2!==e.length)return;let[r,i]=e,o=eY(r),s=eY(i);if(o!==s)if(ek(o)&&ek(s))for(let t=0;t<e.length;t++){let n=e[t];"string"==typeof n&&(e[t]=parseFloat(n))}else this.needsMeasurement=!0}resolveNoneKeyframes(){let{unresolvedKeyframes:e,name:t}=this,n=[];for(let t=0;t<e.length;t++){var r;("number"==typeof(r=e[t])?0===r:null===r||"none"===r||"0"===r||ef(r))&&n.push(t)}n.length&&function(e,t,n){let r,i=0;for(;i<e.length&&!r;){let t=e[i];"string"==typeof t&&!eD.has(t)&&(0,ep.V)(t).values.length&&(r=e[i]),i++}if(r&&n)for(let i of t)e[i]=eM(n,r)}(e,n,t)}measureInitialState(){let{element:e,unresolvedKeyframes:t,name:n}=this;if(!e||!e.current)return;"height"===n&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=eN[n](e.measureViewportBox(),window.getComputedStyle(e.current)),t[0]=this.measuredOrigin;let r=t[t.length-1];void 0!==r&&e.getValue(n,r).jump(r,!1)}measureEndState(){var e;let{element:t,name:n,unresolvedKeyframes:r}=this;if(!t||!t.current)return;let i=t.getValue(n);i&&i.jump(this.measuredOrigin,!1);let o=r.length-1,s=r[o];r[o]=eN[n](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==s&&void 0===this.finalKeyframe&&(this.finalKeyframe=s),(null==(e=this.removedTransforms)?void 0:e.length)&&this.removedTransforms.forEach(([e,n])=>{t.getValue(e).set(n)}),this.resolveNoneKeyframes()}}var eZ=n(93177);let eQ=(e,t)=>"zIndex"!==t&&!!("number"==typeof e||Array.isArray(e)||"string"==typeof e&&(ep.f.test(e)||"0"===e)&&!e.startsWith("url(")),eJ=e=>null!==e;function e0(e,{repeat:t,repeatType:n="loop"},r){let i=e.filter(eJ),o=t&&"loop"!==n&&t%2==1?0:i.length-1;return o&&void 0!==r?r:i[o]}class e1{constructor({autoplay:e=!0,delay:t=0,type:n="keyframes",repeat:r=0,repeatDelay:i=0,repeatType:o="loop",...s}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=eZ.k.now(),this.options={autoplay:e,delay:t,type:n,repeat:r,repeatDelay:i,repeatType:o,...s},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt}get resolved(){return this._resolved||this.hasAttemptedResolve||(eW(),e_()),this._resolved}onKeyframesResolved(e,t){this.resolvedAt=eZ.k.now(),this.hasAttemptedResolve=!0;let{name:n,type:r,velocity:i,delay:o,onComplete:s,onUpdate:a,isGenerator:l}=this.options;if(!l&&!function(e,t,n,r){let i=e[0];if(null===i)return!1;if("display"===t||"visibility"===t)return!0;let o=e[e.length-1],s=eQ(i,t),a=eQ(o,t);return(0,eG.$)(s===a,`You are trying to animate ${t} from "${i}" to "${o}". ${i} is not an animatable value - to enable this animation set ${i} to a value animatable to ${o} via the \`style\` property.`),!!s&&!!a&&(function(e){let t=e[0];if(1===e.length)return!0;for(let n=0;n<e.length;n++)if(e[n]!==t)return!0}(e)||("spring"===n||y(n))&&r)}(e,n,r,i))if(et.current||!o){a&&a(e0(e,this.options,t)),s&&s(),this.resolveFinishedPromise();return}else this.options.duration=0;let u=this.initPlayback(e,t);!1!==u&&(this._resolved={keyframes:e,finalKeyframe:t,...u},this.onPostResolved())}onPostResolved(){}then(e,t){return this.currentFinishedPromise.then(e,t)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise(e=>{this.resolveFinishedPromise=e})}}var e9=n(63879),e2=n(69009),e4=n(31088),e5=n(75170);function e8(e,t,n){let r=Math.max(t-5,0);return(0,e5.f)(n-e(r),t-r)}let e7={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function e6(e,t){return e*Math.sqrt(1-t*t)}let e3=["duration","bounce"],te=["stiffness","damping","mass"];function tt(e,t){return t.some(t=>void 0!==e[t])}function tn(e=e7.visualDuration,t=e7.bounce){let n,r="object"!=typeof e?{visualDuration:e,keyframes:[0,1],bounce:t}:e,{restSpeed:i,restDelta:o}=r,s=r.keyframes[0],a=r.keyframes[r.keyframes.length-1],l={done:!1,value:s},{stiffness:u,damping:c,mass:d,duration:h,velocity:f,isResolvedFromDuration:p}=function(e){let t={velocity:e7.velocity,stiffness:e7.stiffness,damping:e7.damping,mass:e7.mass,isResolvedFromDuration:!1,...e};if(!tt(e,te)&&tt(e,e3))if(e.visualDuration){let n=2*Math.PI/(1.2*e.visualDuration),r=n*n,i=2*(0,e9.q)(.05,1,1-(e.bounce||0))*Math.sqrt(r);t={...t,mass:e7.mass,stiffness:r,damping:i}}else{let n=function({duration:e=e7.duration,bounce:t=e7.bounce,velocity:n=e7.velocity,mass:r=e7.mass}){let i,o;(0,eG.$)(e<=_(e7.maxDuration),"Spring duration must be 10 seconds or less");let s=1-t;s=(0,e9.q)(e7.minDamping,e7.maxDamping,s),e=(0,e9.q)(e7.minDuration,e7.maxDuration,W(e)),s<1?(i=t=>{let r=t*s,i=r*e;return .001-(r-n)/e6(t,s)*Math.exp(-i)},o=t=>{let r=t*s*e,o=Math.pow(s,2)*Math.pow(t,2)*e,a=Math.exp(-r),l=e6(Math.pow(t,2),s);return(r*n+n-o)*a*(-i(t)+.001>0?-1:1)/l}):(i=t=>-.001+Math.exp(-t*e)*((t-n)*e+1),o=t=>e*e*(n-t)*Math.exp(-t*e));let a=function(e,t,n){let r=n;for(let n=1;n<12;n++)r-=e(r)/t(r);return r}(i,o,5/e);if(e=_(e),isNaN(a))return{stiffness:e7.stiffness,damping:e7.damping,duration:e};{let t=Math.pow(a,2)*r;return{stiffness:t,damping:2*s*Math.sqrt(r*t),duration:e}}}(e);(t={...t,...n,mass:e7.mass}).isResolvedFromDuration=!0}return t}({...r,velocity:-W(r.velocity||0)}),m=f||0,v=c/(2*Math.sqrt(u*d)),y=a-s,x=W(Math.sqrt(u/d)),w=5>Math.abs(y);if(i||(i=w?e7.restSpeed.granular:e7.restSpeed.default),o||(o=w?e7.restDelta.granular:e7.restDelta.default),v<1){let e=e6(x,v);n=t=>a-Math.exp(-v*x*t)*((m+v*x*y)/e*Math.sin(e*t)+y*Math.cos(e*t))}else if(1===v)n=e=>a-Math.exp(-x*e)*(y+(m+x*y)*e);else{let e=x*Math.sqrt(v*v-1);n=t=>{let n=Math.exp(-v*x*t),r=Math.min(e*t,300);return a-n*((m+v*x*y)*Math.sinh(r)+e*y*Math.cosh(r))/e}}let b={calculatedDuration:p&&h||null,next:e=>{let t=n(e);if(p)l.done=e>=h;else{let r=0;v<1&&(r=0===e?_(m):e8(n,e,t));let s=Math.abs(a-t)<=o;l.done=Math.abs(r)<=i&&s}return l.value=l.done?a:t,l},toString:()=>{let e=Math.min(g(b),2e4),t=S(t=>b.next(e*t).value,e,30);return e+"ms "+t}};return b}function tr({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:i=10,bounceStiffness:o=500,modifyTarget:s,min:a,max:l,restDelta:u=.5,restSpeed:c}){let d,h,f=e[0],p={done:!1,value:f},m=e=>void 0!==a&&e<a||void 0!==l&&e>l,v=e=>void 0===a?l:void 0===l||Math.abs(a-e)<Math.abs(l-e)?a:l,g=n*t,y=f+g,x=void 0===s?y:s(y);x!==y&&(g=x-f);let w=e=>-g*Math.exp(-e/r),b=e=>x+w(e),T=e=>{let t=w(e),n=b(e);p.done=Math.abs(t)<=u,p.value=p.done?x:n},E=e=>{m(p.value)&&(d=e,h=tn({keyframes:[p.value,v(p.value)],velocity:e8(b,e,p.value),damping:i,stiffness:o,restDelta:u,restSpeed:c}))};return E(0),{calculatedDuration:null,next:e=>{let t=!1;return(h||void 0!==d||(t=!0,T(e),E(e)),void 0!==d&&e>=d)?h.next(e-d):(t||T(e),p)}}}let ti=er(.42,0,1,1),to=er(0,0,.58,1),ts=er(.42,0,.58,1),ta=e=>Array.isArray(e)&&"number"!=typeof e[0],tl={linear:U.l,easeIn:ti,easeInOut:ts,easeOut:to,circIn:ec,circInOut:eh,circOut:ed,backIn:ea,backInOut:el,backOut:es,anticipate:eu},tu=e=>{if(w(e)){(0,eG.V)(4===e.length,"Cubic bezier arrays must contain four numerical values.");let[t,n,r,i]=e;return er(t,n,r,i)}return"string"==typeof e?((0,eG.V)(void 0!==tl[e],`Invalid easing type '${e}'`),tl[e]):e};var tc=n(97283),td=n(76246);function th({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){var i;let o=ta(r)?r.map(tu):tu(r),s={done:!1,value:t[0]},a=(i=n&&n.length===t.length?n:function(e){let t=[0];return!function(e,t){let n=e[e.length-1];for(let r=1;r<=t;r++){let i=(0,A.q)(0,t,r);e.push((0,td.k)(n,1,i))}}(t,e.length-1),t}(t),i.map(t=>t*e)),l=(0,tc.G)(a,t,{ease:Array.isArray(o)?o:t.map(()=>o||ts).splice(0,t.length-1)});return{calculatedDuration:e,next:t=>(s.value=l(t),s.done=t>=e,s)}}let tf=e=>{let t=({timestamp:t})=>e(t);return{start:()=>J.Gt.update(t,!0),stop:()=>(0,J.WG)(t),now:()=>J.uv.isProcessing?J.uv.timestamp:eZ.k.now()}},tp={decay:tr,inertia:tr,tween:th,keyframes:th,spring:tn},tm=e=>e/100;class tv extends e1{constructor(e){super(e),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.teardown();let{onStop:e}=this.options;e&&e()};let{name:t,motionValue:n,element:r,keyframes:i}=this.options,o=(null==r?void 0:r.KeyframeResolver)||eU;this.resolver=new o(i,(e,t)=>this.onKeyframesResolved(e,t),t,n,r),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(e){let t,n,{type:r="keyframes",repeat:i=0,repeatDelay:o=0,repeatType:s,velocity:a=0}=this.options,l=y(r)?r:tp[r]||th;l!==th&&"number"!=typeof e[0]&&(t=(0,e4.F)(tm,(0,e2.j)(e[0],e[1])),e=[0,100]);let u=l({...this.options,keyframes:e});"mirror"===s&&(n=l({...this.options,keyframes:[...e].reverse(),velocity:-a})),null===u.calculatedDuration&&(u.calculatedDuration=g(u));let{calculatedDuration:c}=u,d=c+o;return{generator:u,mirroredGenerator:n,mapPercentToKeyframes:t,calculatedDuration:c,resolvedDuration:d,totalDuration:d*(i+1)-o}}onPostResolved(){let{autoplay:e=!0}=this.options;this.play(),"paused"!==this.pendingPlayState&&e?this.state=this.pendingPlayState:this.pause()}tick(e,t=!1){let{resolved:n}=this;if(!n){let{keyframes:e}=this.options;return{done:!0,value:e[e.length-1]}}let{finalKeyframe:r,generator:i,mirroredGenerator:o,mapPercentToKeyframes:s,keyframes:a,calculatedDuration:l,totalDuration:u,resolvedDuration:c}=n;if(null===this.startTime)return i.next(0);let{delay:d,repeat:h,repeatType:f,repeatDelay:p,onUpdate:m}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-u/this.speed,this.startTime)),t?this.currentTime=e:null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=Math.round(e-this.startTime)*this.speed;let v=this.currentTime-d*(this.speed>=0?1:-1),g=this.speed>=0?v<0:v>u;this.currentTime=Math.max(v,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=u);let y=this.currentTime,x=i;if(h){let e=Math.min(this.currentTime,u)/c,t=Math.floor(e),n=e%1;!n&&e>=1&&(n=1),1===n&&t--,(t=Math.min(t,h+1))%2&&("reverse"===f?(n=1-n,p&&(n-=p/c)):"mirror"===f&&(x=o)),y=(0,e9.q)(0,1,n)*c}let w=g?{done:!1,value:a[0]}:x.next(y);s&&(w.value=s(w.value));let{done:b}=w;g||null===l||(b=this.speed>=0?this.currentTime>=u:this.currentTime<=0);let T=null===this.holdTime&&("finished"===this.state||"running"===this.state&&b);return T&&void 0!==r&&(w.value=e0(a,this.options,r)),m&&m(w.value),T&&this.finish(),w}get duration(){let{resolved:e}=this;return e?W(e.calculatedDuration):0}get time(){return W(this.currentTime)}set time(e){e=_(e),this.currentTime=e,null!==this.holdTime||0===this.speed?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.speed)}get speed(){return this.playbackSpeed}set speed(e){let t=this.playbackSpeed!==e;this.playbackSpeed=e,t&&(this.time=W(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;let{driver:e=tf,onPlay:t,startTime:n}=this.options;this.driver||(this.driver=e(e=>this.tick(e))),t&&t();let r=this.driver.now();null!==this.holdTime?this.startTime=r-this.holdTime:this.startTime?"finished"===this.state&&(this.startTime=r):this.startTime=null!=n?n:this.calcStartTime(),"finished"===this.state&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var e;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=null!=(e=this.currentTime)?e:0}complete(){"running"!==this.state&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";let{onComplete:e}=this.options;e&&e()}cancel(){null!==this.cancelTime&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}}let tg=new Set(["opacity","clipPath","filter","transform"]),ty=h(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),tx={anticipate:eu,backInOut:el,circInOut:eh};class tw extends e1{constructor(e){super(e);let{name:t,motionValue:n,element:r,keyframes:i}=this.options;this.resolver=new eX(i,(e,t)=>this.onKeyframesResolved(e,t),t,n,r),this.resolver.scheduleResolve()}initPlayback(e,t){var n;let{duration:r=300,times:i,ease:o,type:s,motionValue:a,name:l,startTime:u}=this.options;if(!a.owner||!a.owner.current)return!1;if("string"==typeof o&&T()&&o in tx&&(o=tx[o]),y((n=this.options).type)||"spring"===n.type||!function e(t){return!!("function"==typeof t&&T()||!t||"string"==typeof t&&(t in R||T())||w(t)||Array.isArray(t)&&t.every(e))}(n.ease)){let{onComplete:t,onUpdate:n,motionValue:a,element:l,...u}=this.options,c=function(e,t){let n=new tv({...t,keyframes:e,repeat:0,delay:0,isGenerator:!0}),r={done:!1,value:e[0]},i=[],o=0;for(;!r.done&&o<2e4;)i.push((r=n.sample(o)).value),o+=10;return{times:void 0,keyframes:i,duration:o-10,ease:"linear"}}(e,u);1===(e=c.keyframes).length&&(e[1]=e[0]),r=c.duration,i=c.times,o=c.ease,s="keyframes"}let c=function(e,t,n,{delay:r=0,duration:i=300,repeat:o=0,repeatType:s="loop",ease:a="easeInOut",times:l}={}){let u={[t]:n};l&&(u.offset=l);let c=function e(t,n){if(t)return"function"==typeof t&&T()?S(t,n):w(t)?C(t):Array.isArray(t)?t.map(t=>e(t,n)||R.easeOut):R[t]}(a,i);return Array.isArray(c)&&(u.easing=c),e.animate(u,{delay:r,duration:i,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:o+1,direction:"reverse"===s?"alternate":"normal"})}(a.owner.current,l,e,{...this.options,duration:r,times:i,ease:o});return c.startTime=null!=u?u:this.calcStartTime(),this.pendingTimeline?(x(c,this.pendingTimeline),this.pendingTimeline=void 0):c.onfinish=()=>{let{onComplete:n}=this.options;a.set(e0(e,this.options,t)),n&&n(),this.cancel(),this.resolveFinishedPromise()},{animation:c,duration:r,times:i,type:s,ease:o,keyframes:e}}get duration(){let{resolved:e}=this;if(!e)return 0;let{duration:t}=e;return W(t)}get time(){let{resolved:e}=this;if(!e)return 0;let{animation:t}=e;return W(t.currentTime||0)}set time(e){let{resolved:t}=this;if(!t)return;let{animation:n}=t;n.currentTime=_(e)}get speed(){let{resolved:e}=this;if(!e)return 1;let{animation:t}=e;return t.playbackRate}set speed(e){let{resolved:t}=this;if(!t)return;let{animation:n}=t;n.playbackRate=e}get state(){let{resolved:e}=this;if(!e)return"idle";let{animation:t}=e;return t.playState}get startTime(){let{resolved:e}=this;if(!e)return null;let{animation:t}=e;return t.startTime}attachTimeline(e){if(this._resolved){let{resolved:t}=this;if(!t)return U.l;let{animation:n}=t;x(n,e)}else this.pendingTimeline=e;return U.l}play(){if(this.isStopped)return;let{resolved:e}=this;if(!e)return;let{animation:t}=e;"finished"===t.playState&&this.updateFinishedPromise(),t.play()}pause(){let{resolved:e}=this;if(!e)return;let{animation:t}=e;t.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.resolveFinishedPromise(),this.updateFinishedPromise();let{resolved:e}=this;if(!e)return;let{animation:t,keyframes:n,duration:r,type:i,ease:o,times:s}=e;if("idle"===t.playState||"finished"===t.playState)return;if(this.time){let{motionValue:e,onUpdate:t,onComplete:a,element:l,...u}=this.options,c=new tv({...u,keyframes:n,duration:r,type:i,ease:o,times:s,isGenerator:!0}),d=_(this.time);e.setWithVelocity(c.sample(d-10).value,c.sample(d).value,10)}let{onStop:a}=this.options;a&&a(),this.cancel()}complete(){let{resolved:e}=this;e&&e.animation.finish()}cancel(){let{resolved:e}=this;e&&e.animation.cancel()}static supports(e){let{motionValue:t,name:n,repeatDelay:r,repeatType:i,damping:o,type:s}=e;if(!t||!t.owner||!(t.owner.current instanceof HTMLElement))return!1;let{onUpdate:a,transformTemplate:l}=t.owner.getProps();return ty()&&n&&tg.has(n)&&!a&&!l&&!r&&"mirror"!==i&&0!==o&&"inertia"!==s}}let tb={type:"spring",stiffness:500,damping:25,restSpeed:10},tT=e=>({type:"spring",stiffness:550,damping:0===e?2*Math.sqrt(550):30,restSpeed:10}),tE={type:"keyframes",duration:.8},tP={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},tA=(e,{keyframes:t})=>t.length>2?tE:$.has(e)?e.startsWith("scale")?tT(t[1]):tb:tP,tS=(e,t,n,r={},i,o)=>s=>{let a=v(r,e)||{},l=a.delay||r.delay||0,{elapsed:u=0}=r;u-=_(l);let c={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:t.getVelocity(),...a,delay:-u,onUpdate:e=>{t.set(e),a.onUpdate&&a.onUpdate(e)},onComplete:()=>{s(),a.onComplete&&a.onComplete()},name:e,motionValue:t,element:o?void 0:i};!function({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:i,repeat:o,repeatType:s,repeatDelay:a,from:l,elapsed:u,...c}){return!!Object.keys(c).length}(a)&&(c={...c,...tA(e,c)}),c.duration&&(c.duration=_(c.duration)),c.repeatDelay&&(c.repeatDelay=_(c.repeatDelay)),void 0!==c.from&&(c.keyframes[0]=c.from);let d=!1;if(!1!==c.type&&(0!==c.duration||c.repeatDelay)||(c.duration=0,0===c.delay&&(d=!0)),(et.current||ee.W.skipAnimations)&&(d=!0,c.duration=0,c.delay=0),d&&!o&&void 0!==t.get()){let e=e0(c.keyframes,a);if(void 0!==e)return J.Gt.update(()=>{c.onUpdate(e),c.onComplete()}),new m([])}return!o&&tw.supports(c)?new tw(c):new tv(c)};function tC(e,t,{delay:n=0,transitionOverride:r,type:i}={}){var o;let{transition:s=e.getDefaultTransition(),transitionEnd:a,...l}=t;r&&(s=r);let c=[],d=i&&e.animationState&&e.animationState.getState()[i];for(let t in l){let r=e.getValue(t,null!=(o=e.latestValues[t])?o:null),i=l[t];if(void 0===i||d&&function({protectedKeys:e,needsAnimating:t},n){let r=e.hasOwnProperty(n)&&!0!==t[n];return t[n]=!1,r}(d,t))continue;let a={delay:n,...v(s||{},t)},u=!1;if(window.MotionHandoffAnimation){let n=e.props[Q];if(n){let e=window.MotionHandoffAnimation(n,t,J.Gt);null!==e&&(a.startTime=e,u=!0)}}X(e,t),r.start(tS(t,r,i,e.shouldReduceMotion&&K.has(t)?{type:!1}:a,e,u));let h=r.animation;h&&c.push(h)}return a&&Promise.all(c).then(()=>{J.Gt.update(()=>{a&&function(e,t){let{transitionEnd:n={},transition:r={},...i}=u(e,t)||{};for(let t in i={...i,...n}){let n=q(i[t]);e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,(0,z.OQ)(n))}}(e,a)})}),c}function tR(e,t,n={}){var r;let i=u(e,t,"exit"===n.type?null==(r=e.presenceContext)?void 0:r.custom:void 0),{transition:o=e.getDefaultTransition()||{}}=i||{};n.transitionOverride&&(o=n.transitionOverride);let s=i?()=>Promise.all(tC(e,i,n)):()=>Promise.resolve(),a=e.variantChildren&&e.variantChildren.size?(r=0)=>{let{delayChildren:i=0,staggerChildren:s,staggerDirection:a}=o;return function(e,t,n=0,r=0,i=1,o){let s=[],a=(e.variantChildren.size-1)*r,l=1===i?(e=0)=>e*r:(e=0)=>a-e*r;return Array.from(e.variantChildren).sort(tM).forEach((e,r)=>{e.notify("AnimationStart",t),s.push(tR(e,t,{...o,delay:n+l(r)}).then(()=>e.notify("AnimationComplete",t)))}),Promise.all(s)}(e,t,i+r,s,a,n)}:()=>Promise.resolve(),{when:l}=o;if(!l)return Promise.all([s(),a(n.delay)]);{let[e,t]="beforeChildren"===l?[s,a]:[a,s];return e().then(()=>t())}}function tM(e,t){return e.sortNodePosition(t)}let tD=d.length,tk=[...c].reverse(),tj=c.length;function tL(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function tO(){return{animate:tL(!0),whileInView:tL(),whileHover:tL(),whileTap:tL(),whileDrag:tL(),whileFocus:tL(),exit:tL()}}class tV{constructor(e){this.isMounted=!1,this.node=e}update(){}}class tN extends tV{constructor(e){super(e),e.animationState||(e.animationState=function(e){let t=t=>Promise.all(t.map(({animation:t,options:n})=>(function(e,t,n={}){let r;if(e.notify("AnimationStart",t),Array.isArray(t))r=Promise.all(t.map(t=>tR(e,t,n)));else if("string"==typeof t)r=tR(e,t,n);else{let i="function"==typeof t?u(e,t,n.custom):t;r=Promise.all(tC(e,i,n))}return r.then(()=>{e.notify("AnimationComplete",t)})})(e,t,n))),n=tO(),a=!0,l=t=>(n,r)=>{var i;let o=u(e,r,"exit"===t?null==(i=e.presenceContext)?void 0:i.custom:void 0);if(o){let{transition:e,transitionEnd:t,...r}=o;n={...n,...r,...t}}return n};function c(u){let{props:c}=e,h=function e(t){if(!t)return;if(!t.isControllingVariants){let n=t.parent&&e(t.parent)||{};return void 0!==t.props.initial&&(n.initial=t.props.initial),n}let n={};for(let e=0;e<tD;e++){let r=d[e],i=t.props[r];(s(i)||!1===i)&&(n[r]=i)}return n}(e.parent)||{},f=[],p=new Set,m={},v=1/0;for(let t=0;t<tj;t++){var g,y;let d=tk[t],x=n[d],w=void 0!==c[d]?c[d]:h[d],b=s(w),T=d===u?x.isActive:null;!1===T&&(v=t);let E=w===h[d]&&w!==c[d]&&b;if(E&&a&&e.manuallyAnimateOnMount&&(E=!1),x.protectedKeys={...m},!x.isActive&&null===T||!w&&!x.prevProp||r(w)||"boolean"==typeof w)continue;let P=(g=x.prevProp,"string"==typeof(y=w)?y!==g:!!Array.isArray(y)&&!o(y,g)),A=P||d===u&&x.isActive&&!E&&b||t>v&&b,S=!1,C=Array.isArray(w)?w:[w],R=C.reduce(l(d),{});!1===T&&(R={});let{prevResolvedValues:M={}}=x,D={...M,...R},k=t=>{A=!0,p.has(t)&&(S=!0,p.delete(t)),x.needsAnimating[t]=!0;let n=e.getValue(t);n&&(n.liveStyle=!1)};for(let e in D){let t=R[e],n=M[e];if(m.hasOwnProperty(e))continue;let r=!1;(i(t)&&i(n)?o(t,n):t===n)?void 0!==t&&p.has(e)?k(e):x.protectedKeys[e]=!0:null!=t?k(e):p.add(e)}x.prevProp=w,x.prevResolvedValues=R,x.isActive&&(m={...m,...R}),a&&e.blockInitialAnimation&&(A=!1);let j=!(E&&P)||S;A&&j&&f.push(...C.map(e=>({animation:e,options:{type:d}})))}if(p.size){let t={};p.forEach(n=>{let r=e.getBaseTarget(n),i=e.getValue(n);i&&(i.liveStyle=!0),t[n]=null!=r?r:null}),f.push({animation:t})}let x=!!f.length;return a&&(!1===c.initial||c.initial===c.animate)&&!e.manuallyAnimateOnMount&&(x=!1),a=!1,x?t(f):Promise.resolve()}return{animateChanges:c,setActive:function(t,r){var i;if(n[t].isActive===r)return Promise.resolve();null==(i=e.variantChildren)||i.forEach(e=>{var n;return null==(n=e.animationState)?void 0:n.setActive(t,r)}),n[t].isActive=r;let o=c(t);for(let e in n)n[e].protectedKeys={};return o},setAnimateFunction:function(n){t=n(e)},getState:()=>n,reset:()=>{n=tO(),a=!0}}}(e))}updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();r(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){var e;this.node.animationState.reset(),null==(e=this.unmountControls)||e.call(this)}}let tF=0;class tI extends tV{constructor(){super(...arguments),this.id=tF++}update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===n)return;let r=this.node.animationState.setActive("exit",!e);t&&!e&&r.then(()=>t(this.id))}mount(){let{register:e}=this.node.presenceContext||{};e&&(this.unmount=e(this.id))}unmount(){}}function tB(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}function t_(e){return{point:{x:e.pageX,y:e.pageY}}}let tW=e=>t=>L(t)&&e(t,t_(t));function tU(e,t,n,r){return tB(e,t,tW(n),r)}let tG=(e,t)=>Math.abs(e-t);class t${constructor(e,t,{transformPagePoint:n,contextWindow:r,dragSnapToOrigin:i=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let e=tq(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,n=function(e,t){return Math.sqrt(tG(e.x,t.x)**2+tG(e.y,t.y)**2)}(e.offset,{x:0,y:0})>=3;if(!t&&!n)return;let{point:r}=e,{timestamp:i}=J.uv;this.history.push({...r,timestamp:i});let{onStart:o,onMove:s}=this.handlers;t||(o&&o(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),s&&s(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=tK(t,this.transformPagePoint),J.Gt.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();let{onEnd:n,onSessionEnd:r,resumeAnimation:i}=this.handlers;if(this.dragSnapToOrigin&&i&&i(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let o=tq("pointercancel"===e.type?this.lastMoveEventInfo:tK(t,this.transformPagePoint),this.history);this.startEvent&&n&&n(e,o),r&&r(e,o)},!L(e))return;this.dragSnapToOrigin=i,this.handlers=t,this.transformPagePoint=n,this.contextWindow=r||window;let o=tK(t_(e),this.transformPagePoint),{point:s}=o,{timestamp:a}=J.uv;this.history=[{...s,timestamp:a}];let{onSessionStart:l}=t;l&&l(e,tq(o,this.history)),this.removeListeners=(0,e4.F)(tU(this.contextWindow,"pointermove",this.handlePointerMove),tU(this.contextWindow,"pointerup",this.handlePointerUp),tU(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),(0,J.WG)(this.updatePoint)}}function tK(e,t){return t?{point:t(e.point)}:e}function tH(e,t){return{x:e.x-t.x,y:e.y-t.y}}function tq({point:e},t){return{point:e,delta:tH(e,tz(t)),offset:tH(e,t[0]),velocity:function(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null,i=tz(e);for(;n>=0&&(r=e[n],!(i.timestamp-r.timestamp>_(.1)));)n--;if(!r)return{x:0,y:0};let o=W(i.timestamp-r.timestamp);if(0===o)return{x:0,y:0};let s={x:(i.x-r.x)/o,y:(i.y-r.y)/o};return s.x===1/0&&(s.x=0),s.y===1/0&&(s.y=0),s}(t,.1)}}function tz(e){return e[e.length-1]}function tY(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}function tX(e){return e.max-e.min}function tZ(e,t,n,r=.5){e.origin=r,e.originPoint=(0,td.k)(t.min,t.max,e.origin),e.scale=tX(n)/tX(t),e.translate=(0,td.k)(n.min,n.max,e.origin)-e.originPoint,(e.scale>=.9999&&e.scale<=1.0001||isNaN(e.scale))&&(e.scale=1),(e.translate>=-.01&&e.translate<=.01||isNaN(e.translate))&&(e.translate=0)}function tQ(e,t,n,r){tZ(e.x,t.x,n.x,r?r.originX:void 0),tZ(e.y,t.y,n.y,r?r.originY:void 0)}function tJ(e,t,n){e.min=n.min+t.min,e.max=e.min+tX(t)}function t0(e,t,n){e.min=t.min-n.min,e.max=e.min+tX(t)}function t1(e,t,n){t0(e.x,t.x,n.x),t0(e.y,t.y,n.y)}function t9(e,t,n){return{min:void 0!==t?e.min+t:void 0,max:void 0!==n?e.max+n-(e.max-e.min):void 0}}function t2(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function t4(e,t,n){return{min:t5(e,t),max:t5(e,n)}}function t5(e,t){return"number"==typeof e?e:e[t]||0}let t8=()=>({translate:0,scale:1,origin:0,originPoint:0}),t7=()=>({x:t8(),y:t8()}),t6=()=>({min:0,max:0}),t3=()=>({x:t6(),y:t6()});function ne(e){return[e("x"),e("y")]}function nt({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function nn(e){return void 0===e||1===e}function nr({scale:e,scaleX:t,scaleY:n}){return!nn(e)||!nn(t)||!nn(n)}function ni(e){return nr(e)||no(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function no(e){var t,n;return(t=e.x)&&"0%"!==t||(n=e.y)&&"0%"!==n}function ns(e,t,n,r,i){return void 0!==i&&(e=r+i*(e-r)),r+n*(e-r)+t}function na(e,t=0,n=1,r,i){e.min=ns(e.min,t,n,r,i),e.max=ns(e.max,t,n,r,i)}function nl(e,{x:t,y:n}){na(e.x,t.translate,t.scale,t.originPoint),na(e.y,n.translate,n.scale,n.originPoint)}function nu(e,t){e.min=e.min+t,e.max=e.max+t}function nc(e,t,n,r,i=.5){let o=(0,td.k)(e.min,e.max,i);na(e,t,n,o,r)}function nd(e,t){nc(e.x,t.x,t.scaleX,t.scale,t.originX),nc(e.y,t.y,t.scaleY,t.scale,t.originY)}function nh(e,t){return nt(function(e,t){if(!t)return e;let n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}(e.getBoundingClientRect(),t))}let nf=({current:e})=>e?e.ownerDocument.defaultView:null,np=new WeakMap;class nm{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=t3(),this.visualElement=e}start(e,{snapToCursor:t=!1}={}){let{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;let{dragSnapToOrigin:r}=this.getProps();this.panSession=new t$(e,{onSessionStart:e=>{let{dragSnapToOrigin:n}=this.getProps();n?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(t_(e).point)},onStart:(e,t)=>{let{drag:n,dragPropagation:r,onDragStart:i}=this.getProps();if(n&&!r&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(e){if("x"===e||"y"===e)if(M[e])return null;else return M[e]=!0,()=>{M[e]=!1};return M.x||M.y?null:(M.x=M.y=!0,()=>{M.x=M.y=!1})}(n),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),ne(e=>{let t=this.getAxisMotionValue(e).get()||0;if(eT.KN.test(t)){let{projection:n}=this.visualElement;if(n&&n.layout){let r=n.layout.layoutBox[e];r&&(t=tX(r)*(parseFloat(t)/100))}}this.originPoint[e]=t}),i&&J.Gt.postRender(()=>i(e,t)),X(this.visualElement,"transform");let{animationState:o}=this.visualElement;o&&o.setActive("whileDrag",!0)},onMove:(e,t)=>{let{dragPropagation:n,dragDirectionLock:r,onDirectionLock:i,onDrag:o}=this.getProps();if(!n&&!this.openDragLock)return;let{offset:s}=t;if(r&&null===this.currentDirection){this.currentDirection=function(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}(s),null!==this.currentDirection&&i&&i(this.currentDirection);return}this.updateAxis("x",t.point,s),this.updateAxis("y",t.point,s),this.visualElement.render(),o&&o(e,t)},onSessionEnd:(e,t)=>this.stop(e,t),resumeAnimation:()=>ne(e=>{var t;return"paused"===this.getAnimationState(e)&&(null==(t=this.getAxisMotionValue(e).animation)?void 0:t.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:r,contextWindow:nf(this.visualElement)})}stop(e,t){let n=this.isDragging;if(this.cancel(),!n)return;let{velocity:r}=t;this.startAnimation(r);let{onDragEnd:i}=this.getProps();i&&J.Gt.postRender(()=>i(e,t))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:n}=this.getProps();!n&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,n){let{drag:r}=this.getProps();if(!n||!nv(e,r,this.currentDirection))return;let i=this.getAxisMotionValue(e),o=this.originPoint[e]+n[e];this.constraints&&this.constraints[e]&&(o=function(e,{min:t,max:n},r){return void 0!==t&&e<t?e=r?(0,td.k)(t,e,r.min):Math.max(e,t):void 0!==n&&e>n&&(e=r?(0,td.k)(n,e,r.max):Math.min(e,n)),e}(o,this.constraints[e],this.elastic[e])),i.set(o)}resolveConstraints(){var e;let{dragConstraints:t,dragElastic:n}=this.getProps(),r=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null==(e=this.visualElement.projection)?void 0:e.layout,i=this.constraints;t&&tY(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&r?this.constraints=function(e,{top:t,left:n,bottom:r,right:i}){return{x:t9(e.x,n,i),y:t9(e.y,t,r)}}(r.layoutBox,t):this.constraints=!1,this.elastic=function(e=.35){return!1===e?e=0:!0===e&&(e=.35),{x:t4(e,"left","right"),y:t4(e,"top","bottom")}}(n),i!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&ne(e=>{!1!==this.constraints&&this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){let n={};return void 0!==t.min&&(n.min=t.min-e.min),void 0!==t.max&&(n.max=t.max-e.min),n}(r.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){var e;let{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!tY(t))return!1;let r=t.current;(0,eG.V)(null!==r,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:i}=this.visualElement;if(!i||!i.layout)return!1;let o=function(e,t,n){let r=nh(e,n),{scroll:i}=t;return i&&(nu(r.x,i.offset.x),nu(r.y,i.offset.y)),r}(r,i.root,this.visualElement.getTransformPagePoint()),s=(e=i.layout.layoutBox,{x:t2(e.x,o.x),y:t2(e.y,o.y)});if(n){let e=n(function({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}(s));this.hasMutatedConstraints=!!e,e&&(s=nt(e))}return s}startAnimation(e){let{drag:t,dragMomentum:n,dragElastic:r,dragTransition:i,dragSnapToOrigin:o,onDragTransitionEnd:s}=this.getProps(),a=this.constraints||{};return Promise.all(ne(s=>{if(!nv(s,t,this.currentDirection))return;let l=a&&a[s]||{};o&&(l={min:0,max:0});let u={type:"inertia",velocity:n?e[s]:0,bounceStiffness:r?200:1e6,bounceDamping:r?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...i,...l};return this.startAxisValueAnimation(s,u)})).then(s)}startAxisValueAnimation(e,t){let n=this.getAxisMotionValue(e);return X(this.visualElement,e),n.start(tS(e,n,0,t,this.visualElement,!1))}stopAnimation(){ne(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){ne(e=>{var t;return null==(t=this.getAxisMotionValue(e).animation)?void 0:t.pause()})}getAnimationState(e){var t;return null==(t=this.getAxisMotionValue(e).animation)?void 0:t.state}getAxisMotionValue(e){let t=`_drag${e.toUpperCase()}`,n=this.visualElement.getProps();return n[t]||this.visualElement.getValue(e,(n.initial?n.initial[e]:void 0)||0)}snapToCursor(e){ne(t=>{let{drag:n}=this.getProps();if(!nv(t,n,this.currentDirection))return;let{projection:r}=this.visualElement,i=this.getAxisMotionValue(t);if(r&&r.layout){let{min:n,max:o}=r.layout.layoutBox[t];i.set(e[t]-(0,td.k)(n,o,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:n}=this.visualElement;if(!tY(t)||!n||!this.constraints)return;this.stopAnimation();let r={x:0,y:0};ne(e=>{let t=this.getAxisMotionValue(e);if(t&&!1!==this.constraints){let n=t.get();r[e]=function(e,t){let n=.5,r=tX(e),i=tX(t);return i>r?n=(0,A.q)(t.min,t.max-r,e.min):r>i&&(n=(0,A.q)(e.min,e.max-i,t.min)),(0,e9.q)(0,1,n)}({min:n,max:n},this.constraints[e])}});let{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),ne(t=>{if(!nv(t,e,null))return;let n=this.getAxisMotionValue(t),{min:i,max:o}=this.constraints[t];n.set((0,td.k)(i,o,r[t]))})}addListeners(){if(!this.visualElement.current)return;np.set(this.visualElement,this);let e=tU(this.visualElement.current,"pointerdown",e=>{let{drag:t,dragListener:n=!0}=this.getProps();t&&n&&this.start(e)}),t=()=>{let{dragConstraints:e}=this.getProps();tY(e)&&e.current&&(this.constraints=this.resolveRefConstraints())},{projection:n}=this.visualElement,r=n.addEventListener("measure",t);n&&!n.layout&&(n.root&&n.root.updateScroll(),n.updateLayout()),J.Gt.read(t);let i=tB(window,"resize",()=>this.scalePositionWithinConstraints()),o=n.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t})=>{this.isDragging&&t&&(ne(t=>{let n=this.getAxisMotionValue(t);n&&(this.originPoint[t]+=e[t].translate,n.set(n.get()+e[t].translate))}),this.visualElement.render())});return()=>{i(),e(),r(),o&&o()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:n=!1,dragPropagation:r=!1,dragConstraints:i=!1,dragElastic:o=.35,dragMomentum:s=!0}=e;return{...e,drag:t,dragDirectionLock:n,dragPropagation:r,dragConstraints:i,dragElastic:o,dragMomentum:s}}}function nv(e,t,n){return(!0===t||t===e)&&(null===n||n===e)}class ng extends tV{constructor(e){super(e),this.removeGroupControls=U.l,this.removeListeners=U.l,this.controls=new nm(e)}mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||U.l}unmount(){this.removeGroupControls(),this.removeListeners()}}let ny=e=>(t,n)=>{e&&J.Gt.postRender(()=>e(t,n))};class nx extends tV{constructor(){super(...arguments),this.removePointerDownListener=U.l}onPointerDown(e){this.session=new t$(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:nf(this.node)})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:n,onPanEnd:r}=this.node.getProps();return{onSessionStart:ny(e),onStart:ny(t),onMove:n,onEnd:(e,t)=>{delete this.session,r&&J.Gt.postRender(()=>r(e,t))}}}mount(){this.removePointerDownListener=tU(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var nw=n(44995),nb=n(41987),nT=n(69219),nE=n(78386);let nP=(0,nb.createContext)({}),nA={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function nS(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}let nC={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e)if(!eT.px.test(e))return e;else e=parseFloat(e);let n=nS(e,t.target.x),r=nS(e,t.target.y);return`${n}% ${r}%`}},nR={},{schedule:nM,cancel:nD}=(0,n(1129).I)(queueMicrotask,!1);class nk extends nb.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:n,layoutId:r}=this.props,{projection:i}=e;Object.assign(nR,nL),i&&(t.group&&t.group.add(i),n&&n.register&&r&&n.register(i),i.root.didUpdate(),i.addEventListener("animationComplete",()=>{this.safeToRemove()}),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),nA.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:n,drag:r,isPresent:i}=this.props,o=n.projection;return o&&(o.isPresent=i,r||e.layoutDependency!==t||void 0===t?o.willUpdate():this.safeToRemove(),e.isPresent!==i&&(i?o.promote():o.relegate()||J.Gt.postRender(()=>{let e=o.getStack();e&&e.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),nM.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:n}=this.props,{projection:r}=e;r&&(r.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(r),n&&n.deregister&&n.deregister(r))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}}function nj(e){let[t,n]=(0,nT.xQ)(),r=(0,nb.useContext)(nE.L);return(0,nw.jsx)(nk,{...e,layoutGroup:r,switchLayoutGroup:(0,nb.useContext)(nP),isPresent:t,safeToRemove:n})}let nL={borderRadius:{...nC,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:nC,borderTopRightRadius:nC,borderBottomLeftRadius:nC,borderBottomRightRadius:nC,boxShadow:{correct:(e,{treeScale:t,projectionDelta:n})=>{let r=ep.f.parse(e);if(r.length>5)return e;let i=ep.f.createTransformer(e),o=+("number"!=typeof r[0]),s=n.x.scale*t.x,a=n.y.scale*t.y;r[0+o]/=s,r[1+o]/=a;let l=(0,td.k)(s,a,.5);return"number"==typeof r[2+o]&&(r[2+o]/=l),"number"==typeof r[3+o]&&(r[3+o]/=l),i(r)}}};var nO=n(15265);let nV=(e,t)=>e.depth-t.depth;class nN{constructor(){this.children=[],this.isDirty=!1}add(e){(0,nO.Kq)(this.children,e),this.isDirty=!0}remove(e){(0,nO.Ai)(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(nV),this.isDirty=!1,this.children.forEach(e)}}var nF=n(37741);function nI(e){let t=Y(e)?e.get():e;return H(t)?t.toValue():t}let nB=["TopLeft","TopRight","BottomLeft","BottomRight"],n_=nB.length,nW=e=>"string"==typeof e?parseFloat(e):e,nU=e=>"number"==typeof e||eT.px.test(e);function nG(e,t){return void 0!==e[t]?e[t]:e.borderRadius}let n$=nH(0,.5,ed),nK=nH(.5,.95,U.l);function nH(e,t,n){return r=>r<e?0:r>t?1:n((0,A.q)(e,t,r))}function nq(e,t){e.min=t.min,e.max=t.max}function nz(e,t){nq(e.x,t.x),nq(e.y,t.y)}function nY(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function nX(e,t,n,r,i){return e-=t,e=r+1/n*(e-r),void 0!==i&&(e=r+1/i*(e-r)),e}function nZ(e,t,[n,r,i],o,s){!function(e,t=0,n=1,r=.5,i,o=e,s=e){if(eT.KN.test(t)&&(t=parseFloat(t),t=(0,td.k)(s.min,s.max,t/100)-s.min),"number"!=typeof t)return;let a=(0,td.k)(o.min,o.max,r);e===o&&(a-=t),e.min=nX(e.min,t,n,a,i),e.max=nX(e.max,t,n,a,i)}(e,t[n],t[r],t[i],t.scale,o,s)}let nQ=["x","scaleX","originX"],nJ=["y","scaleY","originY"];function n0(e,t,n,r){nZ(e.x,t,nQ,n?n.x:void 0,r?r.x:void 0),nZ(e.y,t,nJ,n?n.y:void 0,r?r.y:void 0)}function n1(e){return 0===e.translate&&1===e.scale}function n9(e){return n1(e.x)&&n1(e.y)}function n2(e,t){return e.min===t.min&&e.max===t.max}function n4(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function n5(e,t){return n4(e.x,t.x)&&n4(e.y,t.y)}function n8(e){return tX(e.x)/tX(e.y)}function n7(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class n6{constructor(){this.members=[]}add(e){(0,nO.Kq)(this.members,e),e.scheduleRender()}remove(e){if((0,nO.Ai)(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){let t,n=this.members.findIndex(t=>e===t);if(0===n)return!1;for(let e=n;e>=0;e--){let n=this.members[e];if(!1!==n.isPresent){t=n;break}}return!!t&&(this.promote(t),!0)}promote(e,t){let n=this.lead;if(e!==n&&(this.prevLead=n,this.lead=e,e.show(),n)){n.instance&&n.scheduleRender(),e.scheduleRender(),e.resumeFrom=n,t&&(e.resumeFrom.preserveOpacity=!0),n.snapshot&&(e.snapshot=n.snapshot,e.snapshot.latestValues=n.animationValues||n.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:r}=e.options;!1===r&&n.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:n}=e;t.onExitComplete&&t.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let n3={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},re="undefined"!=typeof window&&void 0!==window.MotionDebug,rt=["","X","Y","Z"],rn={visibility:"hidden"},rr=0;function ri(e,t,n,r){let{latestValues:i}=t;i[e]&&(n[e]=i[e],t.setStaticValue(e,0),r&&(r[e]=0))}function ro({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:i}){return class{constructor(e={},n=null==t?void 0:t()){this.id=rr++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,re&&(n3.totalNodes=n3.resolvedTargetDeltas=n3.recalculatedProjection=0),this.nodes.forEach(rl),this.nodes.forEach(rm),this.nodes.forEach(rv),this.nodes.forEach(ru),re&&window.MotionDebug.record(n3)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=n?n.root||n:this,this.path=n?[...n.path,n]:[],this.parent=n,this.depth=n?n.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new nN)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new nF.v),this.eventHandlers.get(e).add(t)}notifyListeners(e,...t){let n=this.eventHandlers.get(e);n&&n.notify(...t)}hasListeners(e){return this.eventHandlers.has(e)}mount(t,n=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=t instanceof SVGElement&&"svg"!==t.tagName,this.instance=t;let{layoutId:r,layout:i,visualElement:o}=this.options;if(o&&!o.current&&o.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),n&&(i||r)&&(this.isLayoutDirty=!0),e){let n,r=()=>this.root.updateBlockedByResize=!1;e(t,()=>{this.root.updateBlockedByResize=!0,n&&n(),n=function(e,t){let n=eZ.k.now(),r=({timestamp:i})=>{let o=i-n;o>=250&&((0,J.WG)(r),e(o-t))};return J.Gt.read(r,!0),()=>(0,J.WG)(r)}(r,250),nA.hasAnimatedSinceResize&&(nA.hasAnimatedSinceResize=!1,this.nodes.forEach(rp))})}r&&this.root.registerSharedNode(r,this),!1!==this.options.animate&&o&&(r||i)&&this.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t,hasRelativeTargetChanged:n,layout:r})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let i=this.options.transition||o.getDefaultTransition()||rT,{onLayoutAnimationStart:s,onLayoutAnimationComplete:a}=o.getProps(),l=!this.targetLayout||!n5(this.targetLayout,r)||n,u=!t&&n;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||u||t&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(e,u);let t={...v(i,"layout"),onPlay:s,onComplete:a};(o.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t)}else t||rp(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=r})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,(0,J.WG)(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(rg),this.animationId++)}getTransformTemplate(){let{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(e=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function e(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;let{visualElement:n}=t.options;if(!n)return;let r=n.props[Q];if(window.MotionHasOptimisedAnimation(r,"transform")){let{layout:e,layoutId:n}=t.options;window.MotionCancelOptimisedAnimation(r,"transform",J.Gt,!(e||n))}let{parent:i}=t;i&&!i.hasCheckedOptimisedAppear&&e(i)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let e=0;e<this.path.length;e++){let t=this.path[e];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}let{layoutId:t,layout:n}=this.options;if(void 0===t&&!n)return;let r=this.getTransformTemplate();this.prevTransformTemplateValue=r?r(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(rd);return}this.isUpdating||this.nodes.forEach(rh),this.isUpdating=!1,this.nodes.forEach(rf),this.nodes.forEach(rs),this.nodes.forEach(ra),this.clearAllSnapshots();let e=eZ.k.now();J.uv.delta=(0,e9.q)(0,1e3/60,e-J.uv.timestamp),J.uv.timestamp=e,J.uv.isProcessing=!0,J.PP.update.process(J.uv),J.PP.preRender.process(J.uv),J.PP.render.process(J.uv),J.uv.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,nM.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(rc),this.sharedNodes.forEach(ry)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,J.Gt.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){J.Gt.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let e=0;e<this.path.length;e++)this.path[e].updateScroll();let e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=t3(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(e="measure"){let t=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t){let t=r(this.instance);this.scroll={animationId:this.root.animationId,phase:e,isRoot:t,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:t}}}resetTransform(){if(!i)return;let e=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,t=this.projectionDelta&&!n9(this.projectionDelta),n=this.getTransformTemplate(),r=n?n(this.latestValues,""):void 0,o=r!==this.prevTransformTemplateValue;e&&(t||ni(this.latestValues)||o)&&(i(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}measure(e=!0){var t;let n=this.measurePageBox(),r=this.removeElementScroll(n);return e&&(r=this.removeTransform(r)),rA((t=r).x),rA(t.y),{animationId:this.root.animationId,measuredBox:n,layoutBox:r,latestValues:{},source:this.id}}measurePageBox(){var e;let{visualElement:t}=this.options;if(!t)return t3();let n=t.measureViewportBox();if(!((null==(e=this.scroll)?void 0:e.wasRoot)||this.path.some(rC))){let{scroll:e}=this.root;e&&(nu(n.x,e.offset.x),nu(n.y,e.offset.y))}return n}removeElementScroll(e){var t;let n=t3();if(nz(n,e),null==(t=this.scroll)?void 0:t.wasRoot)return n;for(let t=0;t<this.path.length;t++){let r=this.path[t],{scroll:i,options:o}=r;r!==this.root&&i&&o.layoutScroll&&(i.wasRoot&&nz(n,e),nu(n.x,i.offset.x),nu(n.y,i.offset.y))}return n}applyTransform(e,t=!1){let n=t3();nz(n,e);for(let e=0;e<this.path.length;e++){let r=this.path[e];!t&&r.options.layoutScroll&&r.scroll&&r!==r.root&&nd(n,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),ni(r.latestValues)&&nd(n,r.latestValues)}return ni(this.latestValues)&&nd(n,this.latestValues),n}removeTransform(e){let t=t3();nz(t,e);for(let e=0;e<this.path.length;e++){let n=this.path[e];if(!n.instance||!ni(n.latestValues))continue;nr(n.latestValues)&&n.updateSnapshot();let r=t3();nz(r,n.measurePageBox()),n0(t,n.latestValues,n.snapshot?n.snapshot.layoutBox:void 0,r)}return ni(this.latestValues)&&n0(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==J.uv.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(e=!1){var t,n,r,i;let o=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=o.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=o.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=o.isSharedProjectionDirty);let s=!!this.resumingFrom||this!==o;if(!(e||s&&this.isSharedProjectionDirty||this.isProjectionDirty||(null==(t=this.parent)?void 0:t.isProjectionDirty)||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:a,layoutId:l}=this.options;if(this.layout&&(a||l)){if(this.resolvedRelativeTargetAt=J.uv.timestamp,!this.targetDelta&&!this.relativeTarget){let e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=t3(),this.relativeTargetOrigin=t3(),t1(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),nz(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if((this.target||(this.target=t3(),this.targetWithTransforms=t3()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target)?(this.forceRelativeParentToResolveTarget(),n=this.target,r=this.relativeTarget,i=this.relativeParent.target,tJ(n.x,r.x,i.x),tJ(n.y,r.y,i.y)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):nz(this.target,this.layout.layoutBox),nl(this.target,this.targetDelta)):nz(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let e=this.getClosestProjectingParent();e&&!!e.resumingFrom==!!this.resumingFrom&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=t3(),this.relativeTargetOrigin=t3(),t1(this.relativeTargetOrigin,this.target,e.target),nz(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}re&&n3.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||nr(this.parent.latestValues)||no(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var e;let t=this.getLead(),n=!!this.resumingFrom||this!==t,r=!0;if((this.isProjectionDirty||(null==(e=this.parent)?void 0:e.isProjectionDirty))&&(r=!1),n&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(r=!1),this.resolvedRelativeTargetAt===J.uv.timestamp&&(r=!1),r)return;let{layout:i,layoutId:o}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(i||o))return;nz(this.layoutCorrected,this.layout.layoutBox);let s=this.treeScale.x,a=this.treeScale.y;!function(e,t,n,r=!1){let i,o,s=n.length;if(s){t.x=t.y=1;for(let a=0;a<s;a++){o=(i=n[a]).projectionDelta;let{visualElement:s}=i.options;(!s||!s.props.style||"contents"!==s.props.style.display)&&(r&&i.options.layoutScroll&&i.scroll&&i!==i.root&&nd(e,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),o&&(t.x*=o.x.scale,t.y*=o.y.scale,nl(e,o)),r&&ni(i.latestValues)&&nd(e,i.latestValues))}t.x<1.0000000000001&&t.x>.999999999999&&(t.x=1),t.y<1.0000000000001&&t.y>.999999999999&&(t.y=1)}}(this.layoutCorrected,this.treeScale,this.path,n),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=t3());let{target:l}=t;if(!l){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(nY(this.prevProjectionDelta.x,this.projectionDelta.x),nY(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),tQ(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.treeScale.x===s&&this.treeScale.y===a&&n7(this.projectionDelta.x,this.prevProjectionDelta.x)&&n7(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),re&&n3.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(e=!0){var t;if(null==(t=this.options.visualElement)||t.scheduleRender(),e){let e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=t7(),this.projectionDelta=t7(),this.projectionDeltaWithTransform=t7()}setAnimationOrigin(e,t=!1){let n,r=this.snapshot,i=r?r.latestValues:{},o={...this.latestValues},s=t7();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;let a=t3(),l=(r?r.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),c=!u||u.members.length<=1,d=!!(l&&!c&&!0===this.options.crossfade&&!this.path.some(rb));this.animationProgress=0,this.mixTargetDelta=t=>{let r=t/1e3;if(rx(s.x,e.x,r),rx(s.y,e.y,r),this.setTargetDelta(s),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,h,f,p,m,v;t1(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),f=this.relativeTarget,p=this.relativeTargetOrigin,m=a,v=r,rw(f.x,p.x,m.x,v),rw(f.y,p.y,m.y,v),n&&(u=this.relativeTarget,h=n,n2(u.x,h.x)&&n2(u.y,h.y))&&(this.isProjectionDirty=!1),n||(n=t3()),nz(n,this.relativeTarget)}l&&(this.animationValues=o,function(e,t,n,r,i,o){i?(e.opacity=(0,td.k)(0,void 0!==n.opacity?n.opacity:1,n$(r)),e.opacityExit=(0,td.k)(void 0!==t.opacity?t.opacity:1,0,nK(r))):o&&(e.opacity=(0,td.k)(void 0!==t.opacity?t.opacity:1,void 0!==n.opacity?n.opacity:1,r));for(let i=0;i<n_;i++){let o=`border${nB[i]}Radius`,s=nG(t,o),a=nG(n,o);(void 0!==s||void 0!==a)&&(s||(s=0),a||(a=0),0===s||0===a||nU(s)===nU(a)?(e[o]=Math.max((0,td.k)(nW(s),nW(a),r),0),(eT.KN.test(a)||eT.KN.test(s))&&(e[o]+="%")):e[o]=a)}(t.rotate||n.rotate)&&(e.rotate=(0,td.k)(t.rotate||0,n.rotate||0,r))}(o,i,this.latestValues,r,d,c)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=r},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&((0,J.WG)(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=J.Gt.update(()=>{nA.hasAnimatedSinceResize=!0,this.currentAnimation=function(e,t,n){let r=Y(0)?0:(0,z.OQ)(e);return r.start(tS("",r,1e3,n)),r.animation}(0,0,{...e,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onComplete:()=>{e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let e=this.getLead(),{targetWithTransforms:t,target:n,layout:r,latestValues:i}=e;if(t&&n&&r){if(this!==e&&this.layout&&r&&rS(this.options.animationType,this.layout.layoutBox,r.layoutBox)){n=this.target||t3();let t=tX(this.layout.layoutBox.x);n.x.min=e.target.x.min,n.x.max=n.x.min+t;let r=tX(this.layout.layoutBox.y);n.y.min=e.target.y.min,n.y.max=n.y.min+r}nz(t,n),nd(t,i),tQ(this.projectionDeltaWithTransform,this.layoutCorrected,t,i)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new n6),this.sharedNodes.get(e).add(t);let n=t.options.initialPromotionConfig;t.promote({transition:n?n.transition:void 0,preserveFollowOpacity:n&&n.shouldPreserveFollowOpacity?n.shouldPreserveFollowOpacity(t):void 0})}isLead(){let e=this.getStack();return!e||e.lead===this}getLead(){var e;let{layoutId:t}=this.options;return t&&(null==(e=this.getStack())?void 0:e.lead)||this}getPrevLead(){var e;let{layoutId:t}=this.options;return t?null==(e=this.getStack())?void 0:e.prevLead:void 0}getStack(){let{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote({needsReset:e,transition:t,preserveFollowOpacity:n}={}){let r=this.getStack();r&&r.promote(this,n),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){let e=this.getStack();return!!e&&e.relegate(this)}resetSkewAndRotation(){let{visualElement:e}=this.options;if(!e)return;let t=!1,{latestValues:n}=e;if((n.z||n.rotate||n.rotateX||n.rotateY||n.rotateZ||n.skewX||n.skewY)&&(t=!0),!t)return;let r={};n.z&&ri("z",e,r,this.animationValues);for(let t=0;t<rt.length;t++)ri(`rotate${rt[t]}`,e,r,this.animationValues),ri(`skew${rt[t]}`,e,r,this.animationValues);for(let t in e.render(),r)e.setStaticValue(t,r[t]),this.animationValues&&(this.animationValues[t]=r[t]);e.scheduleRender()}getProjectionStyles(e){var t,n;if(!this.instance||this.isSVG)return;if(!this.isVisible)return rn;let r={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,r.opacity="",r.pointerEvents=nI(null==e?void 0:e.pointerEvents)||"",r.transform=i?i(this.latestValues,""):"none",r;let o=this.getLead();if(!this.projectionDelta||!this.layout||!o.target){let t={};return this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=nI(null==e?void 0:e.pointerEvents)||""),this.hasProjected&&!ni(this.latestValues)&&(t.transform=i?i({},""):"none",this.hasProjected=!1),t}let s=o.animationValues||o.latestValues;this.applyTransformsToTarget(),r.transform=function(e,t,n){let r="",i=e.x.translate/t.x,o=e.y.translate/t.y,s=(null==n?void 0:n.z)||0;if((i||o||s)&&(r=`translate3d(${i}px, ${o}px, ${s}px) `),(1!==t.x||1!==t.y)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){let{transformPerspective:e,rotate:t,rotateX:i,rotateY:o,skewX:s,skewY:a}=n;e&&(r=`perspective(${e}px) ${r}`),t&&(r+=`rotate(${t}deg) `),i&&(r+=`rotateX(${i}deg) `),o&&(r+=`rotateY(${o}deg) `),s&&(r+=`skewX(${s}deg) `),a&&(r+=`skewY(${a}deg) `)}let a=e.x.scale*t.x,l=e.y.scale*t.y;return(1!==a||1!==l)&&(r+=`scale(${a}, ${l})`),r||"none"}(this.projectionDeltaWithTransform,this.treeScale,s),i&&(r.transform=i(s,r.transform));let{x:a,y:l}=this.projectionDelta;for(let e in r.transformOrigin=`${100*a.origin}% ${100*l.origin}% 0`,o.animationValues?r.opacity=o===this?null!=(n=null!=(t=s.opacity)?t:this.latestValues.opacity)?n:1:this.preserveOpacity?this.latestValues.opacity:s.opacityExit:r.opacity=o===this?void 0!==s.opacity?s.opacity:"":void 0!==s.opacityExit?s.opacityExit:0,nR){if(void 0===s[e])continue;let{correct:t,applyTo:n}=nR[e],i="none"===r.transform?s[e]:t(s[e],o);if(n){let e=n.length;for(let t=0;t<e;t++)r[n[t]]=i}else r[e]=i}return this.options.layoutId&&(r.pointerEvents=o===this?nI(null==e?void 0:e.pointerEvents)||"":"none"),r}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>{var t;return null==(t=e.currentAnimation)?void 0:t.stop()}),this.root.nodes.forEach(rd),this.root.sharedNodes.clear()}}}function rs(e){e.updateLayout()}function ra(e){var t;let n=(null==(t=e.resumeFrom)?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&n&&e.hasListeners("didUpdate")){let{layoutBox:t,measuredBox:r}=e.layout,{animationType:i}=e.options,o=n.source!==e.layout.source;"size"===i?ne(e=>{let r=o?n.measuredBox[e]:n.layoutBox[e],i=tX(r);r.min=t[e].min,r.max=r.min+i}):rS(i,n.layoutBox,t)&&ne(r=>{let i=o?n.measuredBox[r]:n.layoutBox[r],s=tX(t[r]);i.max=i.min+s,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[r].max=e.relativeTarget[r].min+s)});let s=t7();tQ(s,t,n.layoutBox);let a=t7();o?tQ(a,e.applyTransform(r,!0),n.measuredBox):tQ(a,t,n.layoutBox);let l=!n9(s),u=!1;if(!e.resumeFrom){let r=e.getClosestProjectingParent();if(r&&!r.resumeFrom){let{snapshot:i,layout:o}=r;if(i&&o){let s=t3();t1(s,n.layoutBox,i.layoutBox);let a=t3();t1(a,t,o.layoutBox),n5(s,a)||(u=!0),r.options.layoutRoot&&(e.relativeTarget=a,e.relativeTargetOrigin=s,e.relativeParent=r)}}}e.notifyListeners("didUpdate",{layout:t,snapshot:n,delta:a,layoutDelta:s,hasLayoutChanged:l,hasRelativeTargetChanged:u})}else if(e.isLead()){let{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function rl(e){re&&n3.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function ru(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function rc(e){e.clearSnapshot()}function rd(e){e.clearMeasurements()}function rh(e){e.isLayoutDirty=!1}function rf(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function rp(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function rm(e){e.resolveTargetDelta()}function rv(e){e.calcProjection()}function rg(e){e.resetSkewAndRotation()}function ry(e){e.removeLeadSnapshot()}function rx(e,t,n){e.translate=(0,td.k)(t.translate,0,n),e.scale=(0,td.k)(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function rw(e,t,n,r){e.min=(0,td.k)(t.min,n.min,r),e.max=(0,td.k)(t.max,n.max,r)}function rb(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}let rT={duration:.45,ease:[.4,0,.1,1]},rE=e=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),rP=rE("applewebkit/")&&!rE("chrome/")?Math.round:U.l;function rA(e){e.min=rP(e.min),e.max=rP(e.max)}function rS(e,t,n){return"position"===e||"preserve-aspect"===e&&!(.2>=Math.abs(n8(t)-n8(n)))}function rC(e){var t;return e!==e.root&&(null==(t=e.scroll)?void 0:t.wasRoot)}let rR=ro({attachResizeListener:(e,t)=>tB(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),rM={current:void 0},rD=ro({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!rM.current){let e=new rR({});e.mount(window),e.setOptions({layoutScroll:!0}),rM.current=e}return rM.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>"fixed"===window.getComputedStyle(e).position});function rk(e,t,n){let{props:r}=e;e.animationState&&r.whileHover&&e.animationState.setActive("whileHover","Start"===n);let i=r["onHover"+n];i&&J.Gt.postRender(()=>i(t,t_(t)))}class rj extends tV{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,n={}){let[r,i,o]=D(e,n),s=k(e=>{let{target:n}=e,r=t(e);if("function"!=typeof r||!n)return;let o=k(e=>{r(e),n.removeEventListener("pointerleave",o)});n.addEventListener("pointerleave",o,i)});return r.forEach(e=>{e.addEventListener("pointerenter",s,i)}),o}(e,e=>(rk(this.node,e,"Start"),e=>rk(this.node,e,"End"))))}unmount(){}}class rL extends tV{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=(0,e4.F)(tB(this.node.current,"focus",()=>this.onFocus()),tB(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function rO(e,t,n){let{props:r}=e;e.animationState&&r.whileTap&&e.animationState.setActive("whileTap","Start"===n);let i=r["onTap"+("End"===n?"":n)];i&&J.Gt.postRender(()=>i(t,t_(t)))}class rV extends tV{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,n={}){let[r,i,o]=D(e,n),s=e=>{let r=e.currentTarget;if(!B(e)||V.has(r))return;V.add(r);let o=t(e),s=(e,t)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),B(e)&&V.has(r)&&(V.delete(r),"function"==typeof o&&o(e,{success:t}))},a=e=>{s(e,n.useGlobalTarget||j(r,e.target))},l=e=>{s(e,!1)};window.addEventListener("pointerup",a,i),window.addEventListener("pointercancel",l,i)};return r.forEach(e=>{O.has(e.tagName)||-1!==e.tabIndex||null!==e.getAttribute("tabindex")||(e.tabIndex=0),(n.useGlobalTarget?window:e).addEventListener("pointerdown",s,i),e.addEventListener("focus",e=>I(e,i),i)}),o}(e,e=>(rO(this.node,e,"Start"),(e,{success:t})=>rO(this.node,e,t?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let rN=new WeakMap,rF=new WeakMap,rI=e=>{let t=rN.get(e.target);t&&t(e)},rB=e=>{e.forEach(rI)},r_={some:0,all:1};class rW extends tV{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:n,amount:r="some",once:i}=e,o={root:t?t.current:void 0,rootMargin:n,threshold:"number"==typeof r?r:r_[r]};return function(e,t,n){let r=function({root:e,...t}){let n=e||document;rF.has(n)||rF.set(n,{});let r=rF.get(n),i=JSON.stringify(t);return r[i]||(r[i]=new IntersectionObserver(rB,{root:e,...t})),r[i]}(t);return rN.set(e,n),r.observe(e),()=>{rN.delete(e),r.unobserve(e)}}(this.node.current,o,e=>{let{isIntersecting:t}=e;if(this.isInView===t||(this.isInView=t,i&&!t&&this.hasEnteredView))return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);let{onViewportEnter:n,onViewportLeave:r}=this.node.getProps(),o=t?n:r;o&&o(e)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:e,prevProps:t}=this.node;["amount","margin","root"].some(function({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}(e,t))&&this.startObserver()}unmount(){}}let rU=(0,nb.createContext)({strict:!1});var rG=n(43877);let r$=(0,nb.createContext)({});function rK(e){return r(e.animate)||d.some(t=>s(e[t]))}function rH(e){return!!(rK(e)||e.variants)}function rq(e){return Array.isArray(e)?e.join(" "):e}var rz=n(96035);let rY={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},rX={};for(let e in rY)rX[e]={isEnabled:t=>rY[e].some(e=>!!t[e])};let rZ=Symbol.for("motionComponentSymbol");var rQ=n(90548),rJ=n(96303);let r0=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function r1(e){if("string"!=typeof e||e.includes("-"));else if(r0.indexOf(e)>-1||/[A-Z]/u.test(e))return!0;return!1}var r9=n(38094);let r2=e=>(t,n)=>{let i=(0,nb.useContext)(r$),o=(0,nb.useContext)(rQ.t),s=()=>(function({scrapeMotionValuesFromProps:e,createRenderState:t,onUpdate:n},i,o,s){let a={latestValues:function(e,t,n,i){let o={},s=i(e,{});for(let e in s)o[e]=nI(s[e]);let{initial:a,animate:u}=e,c=rK(e),d=rH(e);t&&d&&!c&&!1!==e.inherit&&(void 0===a&&(a=t.initial),void 0===u&&(u=t.animate));let h=!!n&&!1===n.initial,f=(h=h||!1===a)?u:a;if(f&&"boolean"!=typeof f&&!r(f)){let t=Array.isArray(f)?f:[f];for(let n=0;n<t.length;n++){let r=l(e,t[n]);if(r){let{transitionEnd:e,transition:t,...n}=r;for(let e in n){let t=n[e];if(Array.isArray(t)){let e=h?t.length-1:0;t=t[e]}null!==t&&(o[e]=t)}for(let t in e)o[t]=e[t]}}}return o}(i,o,s,e),renderState:t()};return n&&(a.onMount=e=>n({props:i,current:e,...a}),a.onUpdate=e=>n(e)),a})(e,t,i,o);return n?s():(0,r9.M)(s)},r4=(e,t)=>t&&"number"==typeof e?t.transform(e):e,r5={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},r8=G.length;function r7(e,t,n){let{style:r,vars:i,transformOrigin:o}=e,s=!1,a=!1;for(let e in t){let n=t[e];if($.has(e)){s=!0;continue}if((0,eK.j)(e)){i[e]=n;continue}{let t=r4(n,eS[e]);e.startsWith("origin")?(a=!0,o[e]=t):r[e]=t}}if(!t.transform&&(s||n?r.transform=function(e,t,n){let r="",i=!0;for(let o=0;o<r8;o++){let s=G[o],a=e[s];if(void 0===a)continue;let l=!0;if(!(l="number"==typeof a?a===+!!s.startsWith("scale"):0===parseFloat(a))||n){let e=r4(a,eS[s]);if(!l){i=!1;let t=r5[s]||s;r+=`${t}(${e}) `}n&&(t[s]=e)}}return r=r.trim(),n?r=n(t,i?"":r):i&&(r="none"),r}(t,e.transform,n):r.transform&&(r.transform="none")),a){let{originX:e="50%",originY:t="50%",originZ:n=0}=o;r.transformOrigin=`${e} ${t} ${n}`}}let r6={offset:"stroke-dashoffset",array:"stroke-dasharray"},r3={offset:"strokeDashoffset",array:"strokeDasharray"};function ie(e,t,n){return"string"==typeof e?e:eT.px.transform(t+n*e)}function it(e,{attrX:t,attrY:n,attrScale:r,originX:i,originY:o,pathLength:s,pathSpacing:a=1,pathOffset:l=0,...u},c,d){if(r7(e,u,d),c){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:h,style:f,dimensions:p}=e;h.transform&&(p&&(f.transform=h.transform),delete h.transform),p&&(void 0!==i||void 0!==o||f.transform)&&(f.transformOrigin=function(e,t,n){let r=ie(t,e.x,e.width),i=ie(n,e.y,e.height);return`${r} ${i}`}(p,void 0!==i?i:.5,void 0!==o?o:.5)),void 0!==t&&(h.x=t),void 0!==n&&(h.y=n),void 0!==r&&(h.scale=r),void 0!==s&&function(e,t,n=1,r=0,i=!0){e.pathLength=1;let o=i?r6:r3;e[o.offset]=eT.px.transform(-r);let s=eT.px.transform(t),a=eT.px.transform(n);e[o.array]=`${s} ${a}`}(h,s,a,l,!1)}let ir=()=>({style:{},transform:{},transformOrigin:{},vars:{}}),ii=()=>({...ir(),attrs:{}}),io=e=>"string"==typeof e&&"svg"===e.toLowerCase();function is(e,{style:t,vars:n},r,i){for(let o in Object.assign(e.style,t,i&&i.getProjectionStyles(r)),n)e.style.setProperty(o,n[o])}let ia=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function il(e,t,n,r){for(let n in is(e,t,void 0,r),t.attrs)e.setAttribute(ia.has(n)?n:Z(n),t.attrs[n])}function iu(e,{layout:t,layoutId:n}){return $.has(e)||e.startsWith("origin")||(t||void 0!==n)&&(!!nR[e]||"opacity"===e)}function ic(e,t,n){var r;let{style:i}=e,o={};for(let s in i)(Y(i[s])||t.style&&Y(t.style[s])||iu(s,e)||(null==(r=null==n?void 0:n.getValue(s))?void 0:r.liveStyle)!==void 0)&&(o[s]=i[s]);return o}function id(e,t,n){let r=ic(e,t,n);for(let n in e)(Y(e[n])||Y(t[n]))&&(r[-1!==G.indexOf(n)?"attr"+n.charAt(0).toUpperCase()+n.substring(1):n]=e[n]);return r}let ih=["x","y","width","height","cx","cy","r"],ip={useVisualState:r2({scrapeMotionValuesFromProps:id,createRenderState:ii,onUpdate:({props:e,prevProps:t,current:n,renderState:r,latestValues:i})=>{if(!n)return;let o=!!e.drag;if(!o){for(let e in i)if($.has(e)){o=!0;break}}if(!o)return;let s=!t;if(t)for(let n=0;n<ih.length;n++){let r=ih[n];e[r]!==t[r]&&(s=!0)}s&&J.Gt.read(()=>{!function(e,t){try{t.dimensions="function"==typeof e.getBBox?e.getBBox():e.getBoundingClientRect()}catch(e){t.dimensions={x:0,y:0,width:0,height:0}}}(n,r),J.Gt.render(()=>{it(r,i,io(n.tagName),e.transformTemplate),il(n,r)})})}})},im={useVisualState:r2({scrapeMotionValuesFromProps:ic,createRenderState:ir})};function iv(e,t,n){for(let r in t)Y(t[r])||iu(r,n)||(e[r]=t[r])}let ig=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function iy(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||ig.has(e)}let ix=e=>!iy(e);try{!function(e){e&&(ix=t=>t.startsWith("on")?!iy(t):e(t))}(require("@emotion/is-prop-valid").default)}catch(e){}let iw={current:null},ib={current:!1},iT=[...ez,ew.y,ep.f],iE=e=>iT.find(eq(e)),iP=new WeakMap,iA=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class iS{scrapeMotionValuesFromProps(e,t,n){return{}}constructor({parent:e,props:t,presenceContext:n,reducedMotionConfig:r,blockInitialAnimation:i,visualState:o},s={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=eU,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let e=eZ.k.now();this.renderScheduledAt<e&&(this.renderScheduledAt=e,J.Gt.render(this.render,!1,!0))};let{latestValues:a,renderState:l,onUpdate:u}=o;this.onUpdate=u,this.latestValues=a,this.baseTarget={...a},this.initialValues=t.initial?{...a}:{},this.renderState=l,this.parent=e,this.props=t,this.presenceContext=n,this.depth=e?e.depth+1:0,this.reducedMotionConfig=r,this.options=s,this.blockInitialAnimation=!!i,this.isControllingVariants=rK(t),this.isVariantNode=rH(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:c,...d}=this.scrapeMotionValuesFromProps(t,{},this);for(let e in d){let t=d[e];void 0!==a[e]&&Y(t)&&t.set(a[e],!1)}}mount(e){this.current=e,iP.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),ib.current||function(){if(ib.current=!0,rz.B)if(window.matchMedia){let e=window.matchMedia("(prefers-reduced-motion)"),t=()=>iw.current=e.matches;e.addListener(t),t()}else iw.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||iw.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let e in iP.delete(this.current),this.projection&&this.projection.unmount(),(0,J.WG)(this.notifyUpdate),(0,J.WG)(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[e].clear();for(let e in this.features){let t=this.features[e];t&&(t.unmount(),t.isMounted=!1)}this.current=null}bindToMotionValue(e,t){let n;this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();let r=$.has(e),i=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&J.Gt.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),o=t.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(n=window.MotionCheckAppearSync(this,e,t)),this.valueSubscriptions.set(e,()=>{i(),o(),n&&n(),t.owner&&t.stop()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}updateFeatures(){let e="animation";for(e in rX){let t=rX[e];if(!t)continue;let{isEnabled:n,Feature:r}=t;if(!this.features[e]&&r&&n(this.props)&&(this.features[e]=new r(this)),this.features[e]){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):t3()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let t=0;t<iA.length;t++){let n=iA[t];this.propEventSubscriptions[n]&&(this.propEventSubscriptions[n](),delete this.propEventSubscriptions[n]);let r=e["on"+n];r&&(this.propEventSubscriptions[n]=this.on(n,r))}this.prevMotionValues=function(e,t,n){for(let r in t){let i=t[r],o=n[r];if(Y(i))e.addValue(r,i);else if(Y(o))e.addValue(r,(0,z.OQ)(i,{owner:e}));else if(o!==i)if(e.hasValue(r)){let t=e.getValue(r);!0===t.liveStyle?t.jump(i):t.hasAnimated||t.set(i)}else{let t=e.getStaticValue(r);e.addValue(r,(0,z.OQ)(void 0!==t?t:i,{owner:e}))}}for(let r in n)void 0===t[r]&&e.removeValue(r);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){let n=this.values.get(e);t!==n&&(n&&this.removeValue(e),this.bindToMotionValue(e,t),this.values.set(e,t),this.latestValues[e]=t.get())}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let n=this.values.get(e);return void 0===n&&void 0!==t&&(n=(0,z.OQ)(null===t?void 0:t,{owner:this}),this.addValue(e,n)),n}readValue(e,t){var n;let r=void 0===this.latestValues[e]&&this.current?null!=(n=this.getBaseTargetFromProps(this.props,e))?n:this.readValueFromInstance(this.current,e,this.options):this.latestValues[e];return null!=r&&("string"==typeof r&&(e$(r)||ef(r))?r=parseFloat(r):!iE(r)&&ep.f.test(t)&&(r=eM(e,t)),this.setBaseTarget(e,Y(r)?r.get():r)),Y(r)?r.get():r}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){var t;let n,{initial:r}=this.props;if("string"==typeof r||"object"==typeof r){let i=l(this.props,r,null==(t=this.presenceContext)?void 0:t.custom);i&&(n=i[e])}if(r&&void 0!==n)return n;let i=this.getBaseTargetFromProps(this.props,e);return void 0===i||Y(i)?void 0!==this.initialValues[e]&&void 0===n?void 0:this.baseTarget[e]:i}on(e,t){return this.events[e]||(this.events[e]=new nF.v),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}}class iC extends iS{constructor(){super(...arguments),this.KeyframeResolver=eX}sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:n}){delete t[e],delete n[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;Y(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent=`${e}`)}))}}class iR extends iC{constructor(){super(...arguments),this.type="html",this.renderInstance=is}readValueFromInstance(e,t){if($.has(t)){let e=eR(t);return e&&e.default||0}{let n=window.getComputedStyle(e),r=((0,eK.j)(t)?n.getPropertyValue(t):n[t])||0;return"string"==typeof r?r.trim():r}}measureInstanceViewportBox(e,{transformPagePoint:t}){return nh(e,t)}build(e,t,n){r7(e,t,n.transformTemplate)}scrapeMotionValuesFromProps(e,t,n){return ic(e,t,n)}}class iM extends iC{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=t3}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if($.has(t)){let e=eR(t);return e&&e.default||0}return t=ia.has(t)?t:Z(t),e.getAttribute(t)}scrapeMotionValuesFromProps(e,t,n){return id(e,t,n)}build(e,t,n){it(e,t,this.isSVGTag,n.transformTemplate)}renderInstance(e,t,n,r){il(e,t,n,r)}mount(e){this.isSVGTag=io(e.tagName),super.mount(e)}}let iD=function(e){if("undefined"==typeof Proxy)return e;let t=new Map;return new Proxy((...t)=>e(...t),{get:(n,r)=>"create"===r?e:(t.has(r)||t.set(r,e(r)),t.get(r))})}((E={animation:{Feature:tN},exit:{Feature:tI},inView:{Feature:rW},tap:{Feature:rV},focus:{Feature:rL},hover:{Feature:rj},pan:{Feature:nx},drag:{Feature:ng,ProjectionNode:rD,MeasureLayout:nj},layout:{ProjectionNode:rD,MeasureLayout:nj}},P=(e,t)=>r1(e)?new iM(t):new iR(t,{allowProjection:e!==nb.Fragment}),function(e,{forwardMotionProps:t}={forwardMotionProps:!1}){return function(e){var t,n;let{preloadedFeatures:r,createVisualElement:i,useRender:o,useVisualState:a,Component:l}=e;function u(e,t){var n,r,u;let c,d={...(0,nb.useContext)(rG.Q),...e,layoutId:function(e){let{layoutId:t}=e,n=(0,nb.useContext)(nE.L).id;return n&&void 0!==t?n+"-"+t:t}(e)},{isStatic:h}=d,f=function(e){let{initial:t,animate:n}=function(e,t){if(rK(e)){let{initial:t,animate:n}=e;return{initial:!1===t||s(t)?t:void 0,animate:s(n)?n:void 0}}return!1!==e.inherit?t:{}}(e,(0,nb.useContext)(r$));return(0,nb.useMemo)(()=>({initial:t,animate:n}),[rq(t),rq(n)])}(e),p=a(e,h);if(!h&&rz.B){r=0,u=0,(0,nb.useContext)(rU).strict;let e=function(e){let{drag:t,layout:n}=rX;if(!t&&!n)return{};let r={...t,...n};return{MeasureLayout:(null==t?void 0:t.isEnabled(e))||(null==n?void 0:n.isEnabled(e))?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}(d);c=e.MeasureLayout,f.visualElement=function(e,t,n,r,i){var o,s;let{visualElement:a}=(0,nb.useContext)(r$),l=(0,nb.useContext)(rU),u=(0,nb.useContext)(rQ.t),c=(0,nb.useContext)(rG.Q).reducedMotion,d=(0,nb.useRef)(null);r=r||l.renderer,!d.current&&r&&(d.current=r(e,{visualState:t,parent:a,props:n,presenceContext:u,blockInitialAnimation:!!u&&!1===u.initial,reducedMotionConfig:c}));let h=d.current,f=(0,nb.useContext)(nP);h&&!h.projection&&i&&("html"===h.type||"svg"===h.type)&&function(e,t,n,r){let{layoutId:i,layout:o,drag:s,dragConstraints:a,layoutScroll:l,layoutRoot:u}=t;e.projection=new n(e.latestValues,t["data-framer-portal-id"]?void 0:function e(t){if(t)return!1!==t.options.allowProjection?t.projection:e(t.parent)}(e.parent)),e.projection.setOptions({layoutId:i,layout:o,alwaysMeasureLayout:!!s||a&&tY(a),visualElement:e,animationType:"string"==typeof o?o:"both",initialPromotionConfig:r,layoutScroll:l,layoutRoot:u})}(d.current,n,i,f);let p=(0,nb.useRef)(!1);(0,nb.useInsertionEffect)(()=>{h&&p.current&&h.update(n,u)});let m=n[Q],v=(0,nb.useRef)(!!m&&!(null==(o=window.MotionHandoffIsComplete)?void 0:o.call(window,m))&&(null==(s=window.MotionHasOptimisedAnimation)?void 0:s.call(window,m)));return(0,rJ.E)(()=>{h&&(p.current=!0,window.MotionIsMounted=!0,h.updateFeatures(),nM.render(h.render),v.current&&h.animationState&&h.animationState.animateChanges())}),(0,nb.useEffect)(()=>{h&&(!v.current&&h.animationState&&h.animationState.animateChanges(),v.current&&(queueMicrotask(()=>{var e;null==(e=window.MotionHandoffMarkAsComplete)||e.call(window,m)}),v.current=!1))}),h}(l,p,d,i,e.ProjectionNode)}return(0,nw.jsxs)(r$.Provider,{value:f,children:[c&&f.visualElement?(0,nw.jsx)(c,{visualElement:f.visualElement,...d}):null,o(l,e,(n=f.visualElement,(0,nb.useCallback)(e=>{e&&p.onMount&&p.onMount(e),n&&(e?n.mount(e):n.unmount()),t&&("function"==typeof t?t(e):tY(t)&&(t.current=e))},[n])),p,h,f.visualElement)]})}r&&function(e){for(let t in e)rX[t]={...rX[t],...e[t]}}(r),u.displayName="motion.".concat("string"==typeof l?l:"create(".concat(null!=(n=null!=(t=l.displayName)?t:l.name)?n:"",")"));let c=(0,nb.forwardRef)(u);return c[rZ]=l,c}({...r1(e)?ip:im,preloadedFeatures:E,useRender:function(e=!1){return(t,n,r,{latestValues:i},o)=>{let s=(r1(t)?function(e,t,n,r){let i=(0,nb.useMemo)(()=>{let n=ii();return it(n,t,io(r),e.transformTemplate),{...n.attrs,style:{...n.style}}},[t]);if(e.style){let t={};iv(t,e.style,e),i.style={...t,...i.style}}return i}:function(e,t){let n={},r=function(e,t){let n=e.style||{},r={};return iv(r,n,e),Object.assign(r,function({transformTemplate:e},t){return(0,nb.useMemo)(()=>{let n=ir();return r7(n,t,e),Object.assign({},n.vars,n.style)},[t])}(e,t)),r}(e,t);return e.drag&&!1!==e.dragListener&&(n.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(n.tabIndex=0),n.style=r,n})(n,i,o,t),a=function(e,t,n){let r={};for(let i in e)("values"!==i||"object"!=typeof e.values)&&(ix(i)||!0===n&&iy(i)||!t&&!iy(i)||e.draggable&&i.startsWith("onDrag"))&&(r[i]=e[i]);return r}(n,"string"==typeof t,e),l=t!==nb.Fragment?{...a,...s,ref:r}:{},{children:u}=n,c=(0,nb.useMemo)(()=>Y(u)?u.get():u,[u]);return(0,nb.createElement)(t,{...l,children:c})}}(t),createVisualElement:P,Component:e})}))},44627:(e,t,n)=>{n.d(t,{X4:()=>o,ai:()=>i,hs:()=>s});var r=n(63879);let i={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},o={...i,transform:e=>(0,r.q)(0,1,e)},s={...i,default:1}},45568:(e,t,n)=>{n.d(t,{_:()=>r});function r(e,t,n){if(!t.has(e))throw TypeError("attempted to "+n+" private field on non-instance");return t.get(e)}},47869:(e,t,n)=>{n.d(t,{F6:()=>s,Nw:()=>o,_P:()=>a,my:()=>r,w4:()=>i});let r=6048e5,i=864e5,o=43200,s=1440,a=Symbol.for("constructDateFrom")},55810:(e,t,n)=>{n.d(t,{A:()=>i});var r=n(19922);function i(e){return(0,r.w)(e,Date.now())}},59991:(e,t,n)=>{n.d(t,{$:()=>i,V:()=>o});var r=n(11330);let i=r.l,o=r.l},60728:(e,t,n)=>{n.d(t,{B:()=>l});var r,i=n(41987),o=n(17247),s=(r||(r=n.t(i,2)))[" useId ".trim().toString()]||(()=>void 0),a=0;function l(e){let[t,n]=i.useState(s());return(0,o.N)(()=>{e||n(e=>e??String(a++))},[e]),e||(t?`radix-${t}`:"")}},62396:(e,t,n)=>{n.d(t,{y:()=>s});var r=n(74611),i=n(14576),o=n(92844);let s={test:e=>o.B.test(e)||r.u.test(e)||i.V.test(e),parse:e=>o.B.test(e)?o.B.parse(e):i.V.test(e)?i.V.parse(e):r.u.parse(e),transform:e=>"string"==typeof e?e:e.hasOwnProperty("red")?o.B.transform(e):i.V.transform(e)}},62927:(e,t,n)=>{n.d(t,{Gt:()=>i,PP:()=>a,WG:()=>o,uv:()=>s});var r=n(11330);let{schedule:i,cancel:o,state:s,steps:a}=(0,n(1129).I)("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:r.l,!0)},63879:(e,t,n)=>{n.d(t,{q:()=>r});let r=(e,t,n)=>n>t?t:n<e?e:n},67198:(e,t,n)=>{n.d(t,{_:()=>i});var r=n(45568);function i(e,t){var n=(0,r._)(e,t,"get");return n.get?n.get.call(e):n.value}},68278:(e,t,n)=>{n.d(t,{c:()=>s});var r=n(19922),i=n(55810),o=n(76411);function s(e,t){return(0,o.r)((0,r.w)((null==t?void 0:t.in)||e,e),(0,i.A)((null==t?void 0:t.in)||e))}},68309:(e,t,n)=>{n.d(t,{D:()=>l,N:()=>u});var r=n(41987),i=["light","dark"],o="(prefers-color-scheme: dark)",s=r.createContext(void 0),a={setTheme:e=>{},themes:[]},l=()=>{var e;return null!=(e=r.useContext(s))?e:a},u=e=>r.useContext(s)?e.children:r.createElement(d,{...e}),c=["light","dark"],d=e=>{let{forcedTheme:t,disableTransitionOnChange:n=!1,enableSystem:a=!0,enableColorScheme:l=!0,storageKey:u="theme",themes:d=c,defaultTheme:v=a?"system":"light",attribute:g="data-theme",value:y,children:x,nonce:w}=e,[b,T]=r.useState(()=>f(u,v)),[E,P]=r.useState(()=>f(u)),A=y?Object.values(y):d,S=r.useCallback(e=>{let t=e;if(!t)return;"system"===e&&a&&(t=m());let r=y?y[t]:t,o=n?p():null,s=document.documentElement;if("class"===g?(s.classList.remove(...A),r&&s.classList.add(r)):r?s.setAttribute(g,r):s.removeAttribute(g),l){let e=i.includes(v)?v:null,n=i.includes(t)?t:e;s.style.colorScheme=n}null==o||o()},[]),C=r.useCallback(e=>{let t="function"==typeof e?e(e):e;T(t);try{localStorage.setItem(u,t)}catch(e){}},[t]),R=r.useCallback(e=>{P(m(e)),"system"===b&&a&&!t&&S("system")},[b,t]);r.useEffect(()=>{let e=window.matchMedia(o);return e.addListener(R),R(e),()=>e.removeListener(R)},[R]),r.useEffect(()=>{let e=e=>{e.key===u&&C(e.newValue||v)};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[C]),r.useEffect(()=>{S(null!=t?t:b)},[t,b]);let M=r.useMemo(()=>({theme:b,setTheme:C,forcedTheme:t,resolvedTheme:"system"===b?E:b,themes:a?[...d,"system"]:d,systemTheme:a?E:void 0}),[b,C,t,E,a,d]);return r.createElement(s.Provider,{value:M},r.createElement(h,{forcedTheme:t,disableTransitionOnChange:n,enableSystem:a,enableColorScheme:l,storageKey:u,themes:d,defaultTheme:v,attribute:g,value:y,children:x,attrs:A,nonce:w}),x)},h=r.memo(e=>{let{forcedTheme:t,storageKey:n,attribute:s,enableSystem:a,enableColorScheme:l,defaultTheme:u,value:c,attrs:d,nonce:h}=e,f="system"===u,p="class"===s?"var d=document.documentElement,c=d.classList;".concat("c.remove(".concat(d.map(e=>"'".concat(e,"'")).join(","),")"),";"):"var d=document.documentElement,n='".concat(s,"',s='setAttribute';"),m=l?(i.includes(u)?u:null)?"if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'".concat(u,"'"):"if(e==='light'||e==='dark')d.style.colorScheme=e":"",v=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=!(arguments.length>2)||void 0===arguments[2]||arguments[2],r=c?c[e]:e,o=t?e+"|| ''":"'".concat(r,"'"),a="";return l&&n&&!t&&i.includes(e)&&(a+="d.style.colorScheme = '".concat(e,"';")),"class"===s?t||r?a+="c.add(".concat(o,")"):a+="null":r&&(a+="d[s](n,".concat(o,")")),a},g=t?"!function(){".concat(p).concat(v(t),"}()"):a?"!function(){try{".concat(p,"var e=localStorage.getItem('").concat(n,"');if('system'===e||(!e&&").concat(f,")){var t='").concat(o,"',m=window.matchMedia(t);if(m.media!==t||m.matches){").concat(v("dark"),"}else{").concat(v("light"),"}}else if(e){").concat(c?"var x=".concat(JSON.stringify(c),";"):"").concat(v(c?"x[e]":"e",!0),"}").concat(f?"":"else{"+v(u,!1,!1)+"}").concat(m,"}catch(e){}}()"):"!function(){try{".concat(p,"var e=localStorage.getItem('").concat(n,"');if(e){").concat(c?"var x=".concat(JSON.stringify(c),";"):"").concat(v(c?"x[e]":"e",!0),"}else{").concat(v(u,!1,!1),";}").concat(m,"}catch(t){}}();");return r.createElement("script",{nonce:h,dangerouslySetInnerHTML:{__html:g}})}),f=(e,t)=>{let n;try{n=localStorage.getItem(e)||void 0}catch(e){}return n||t},p=()=>{let e=document.createElement("style");return e.appendChild(document.createTextNode("*{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(e),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(e)},1)}},m=e=>(e||(e=window.matchMedia(o)),e.matches?"dark":"light")},69009:(e,t,n)=>{n.d(t,{j:()=>A});var r=n(76246),i=n(59991);function o(e,t,n){return(n<0&&(n+=1),n>1&&(n-=1),n<1/6)?e+(t-e)*6*n:n<.5?t:n<2/3?e+(t-e)*(2/3-n)*6:e}var s=n(74611),a=n(92844),l=n(14576);function u(e,t){return n=>n>0?t:e}let c=(e,t,n)=>{let r=e*e,i=n*(t*t-r)+r;return i<0?0:Math.sqrt(i)},d=[s.u,a.B,l.V],h=e=>d.find(t=>t.test(e));function f(e){let t=h(e);if((0,i.$)(!!t,`'${e}' is not an animatable color. Use the equivalent color code instead.`),!t)return!1;let n=t.parse(e);return t===l.V&&(n=function({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,n/=100;let i=0,s=0,a=0;if(t/=100){let r=n<.5?n*(1+t):n+t-n*t,l=2*n-r;i=o(l,r,e+1/3),s=o(l,r,e),a=o(l,r,e-1/3)}else i=s=a=n;return{red:Math.round(255*i),green:Math.round(255*s),blue:Math.round(255*a),alpha:r}}(n)),n}let p=(e,t)=>{let n=f(e),i=f(t);if(!n||!i)return u(e,t);let o={...n};return e=>(o.red=c(n.red,i.red,e),o.green=c(n.green,i.green,e),o.blue=c(n.blue,i.blue,e),o.alpha=(0,r.k)(n.alpha,i.alpha,e),a.B.transform(o))};var m=n(31088),v=n(62396),g=n(98718),y=n(11675);let x=new Set(["none","hidden"]);function w(e,t){return n=>(0,r.k)(e,t,n)}function b(e){return"number"==typeof e?w:"string"==typeof e?(0,y.p)(e)?u:v.y.test(e)?p:P:Array.isArray(e)?T:"object"==typeof e?v.y.test(e)?p:E:u}function T(e,t){let n=[...e],r=n.length,i=e.map((e,n)=>b(e)(e,t[n]));return e=>{for(let t=0;t<r;t++)n[t]=i[t](e);return n}}function E(e,t){let n={...e,...t},r={};for(let i in n)void 0!==e[i]&&void 0!==t[i]&&(r[i]=b(e[i])(e[i],t[i]));return e=>{for(let t in r)n[t]=r[t](e);return n}}let P=(e,t)=>{let n=g.f.createTransformer(t),r=(0,g.V)(e),o=(0,g.V)(t);return r.indexes.var.length===o.indexes.var.length&&r.indexes.color.length===o.indexes.color.length&&r.indexes.number.length>=o.indexes.number.length?x.has(e)&&!o.values.length||x.has(t)&&!r.values.length?function(e,t){return x.has(e)?n=>n<=0?e:t:n=>n>=1?t:e}(e,t):(0,m.F)(T(function(e,t){var n;let r=[],i={color:0,var:0,number:0};for(let o=0;o<t.values.length;o++){let s=t.types[o],a=e.indexes[s][i[s]],l=null!=(n=e.values[a])?n:0;r[o]=l,i[s]++}return r}(r,o),o.values),n):((0,i.$)(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),u(e,t))};function A(e,t,n){return"number"==typeof e&&"number"==typeof t&&"number"==typeof n?(0,r.k)(e,t,n):b(e)(e,t)}},69219:(e,t,n)=>{n.d(t,{xQ:()=>o});var r=n(41987),i=n(90548);function o(e=!0){let t=(0,r.useContext)(i.t);if(null===t)return[!0,null];let{isPresent:n,onExitComplete:s,register:a}=t,l=(0,r.useId)();(0,r.useEffect)(()=>{e&&a(l)},[e]);let u=(0,r.useCallback)(()=>e&&s&&s(l),[l,s,e]);return!n&&s?[!1,u]:[!0]}},69292:(e,t,n)=>{n.d(t,{$:()=>o,q:()=>s});var r=n(31546);let i=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,o=(e,t)=>n=>!!("string"==typeof n&&i.test(n)&&n.startsWith(e)||t&&null!=n&&Object.prototype.hasOwnProperty.call(n,t)),s=(e,t,n)=>i=>{if("string"!=typeof i)return i;let[o,s,a,l]=i.match(r.S);return{[e]:parseFloat(o),[t]:parseFloat(s),[n]:parseFloat(a),alpha:void 0!==l?parseFloat(l):1}}},71097:(e,t,n)=>{n.d(t,{a:()=>r});let r=e=>Math.round(1e5*e)/1e5},74611:(e,t,n)=>{n.d(t,{u:()=>i});var r=n(92844);let i={test:(0,n(69292).$)("#"),parse:function(e){let t="",n="",r="",i="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),i=e.substring(4,5),t+=t,n+=n,r+=r,i+=i),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:i?parseInt(i,16)/255:1}},transform:r.B.transform}},75170:(e,t,n)=>{n.d(t,{f:()=>r});function r(e,t){return t?1e3/t*e:0}},76246:(e,t,n)=>{n.d(t,{k:()=>r});let r=(e,t,n)=>e+(t-e)*n},76411:(e,t,n)=>{n.d(t,{r:()=>o});var r=n(2686),i=n(41200);function o(e,t,n){let[o,s]=(0,r.x)(null==n?void 0:n.in,e,t);return+(0,i.o)(o)==+(0,i.o)(s)}},78386:(e,t,n)=>{n.d(t,{L:()=>r});let r=(0,n(41987).createContext)({})},79807:(e,t,n)=>{n.d(t,{_:()=>i});var r=n(45568);function i(e,t,n){var i=(0,r._)(e,t,"set");if(i.set)i.set.call(e,n);else{if(!i.writable)throw TypeError("attempted to set read only private field");i.value=n}return n}},80482:(e,t,n)=>{n.d(t,{A:()=>s,q:()=>o});var r=n(41987),i=n(44995);function o(e,t){let n=r.createContext(t),o=e=>{let{children:t,...o}=e,s=r.useMemo(()=>o,Object.values(o));return(0,i.jsx)(n.Provider,{value:s,children:t})};return o.displayName=e+"Provider",[o,function(i){let o=r.useContext(n);if(o)return o;if(void 0!==t)return t;throw Error(`\`${i}\` must be used within \`${e}\``)}]}function s(e,t=[]){let n=[],o=()=>{let t=n.map(e=>r.createContext(e));return function(n){let i=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:i}}),[n,i])}};return o.scopeName=e,[function(t,o){let s=r.createContext(o),a=n.length;n=[...n,o];let l=t=>{let{scope:n,children:o,...l}=t,u=n?.[e]?.[a]||s,c=r.useMemo(()=>l,Object.values(l));return(0,i.jsx)(u.Provider,{value:c,children:o})};return l.displayName=t+"Provider",[l,function(n,i){let l=i?.[e]?.[a]||s,u=r.useContext(l);if(u)return u;if(void 0!==o)return o;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let i=n.reduce((t,{useScope:n,scopeName:r})=>{let i=n(e)[`__scope${r}`];return{...t,...i}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}(o,...t)]}},82491:(e,t,n)=>{n.d(t,{qW:()=>h});var r,i=n(41987),o=n(29254),s=n(7156),a=n(49640),l=n(93568),u=n(44995),c="dismissableLayer.update",d=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),h=i.forwardRef((e,t)=>{var n,h;let{disableOutsidePointerEvents:m=!1,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:x,onDismiss:w,...b}=e,T=i.useContext(d),[E,P]=i.useState(null),A=null!=(h=null==E?void 0:E.ownerDocument)?h:null==(n=globalThis)?void 0:n.document,[,S]=i.useState({}),C=(0,a.s)(t,e=>P(e)),R=Array.from(T.layers),[M]=[...T.layersWithOutsidePointerEventsDisabled].slice(-1),D=R.indexOf(M),k=E?R.indexOf(E):-1,j=T.layersWithOutsidePointerEventsDisabled.size>0,L=k>=D,O=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,l.c)(e),o=i.useRef(!1),s=i.useRef(()=>{});return i.useEffect(()=>{let e=e=>{if(e.target&&!o.current){let t=function(){p("dismissableLayer.pointerDownOutside",r,i,{discrete:!0})},i={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",s.current),s.current=t,n.addEventListener("click",s.current,{once:!0})):t()}else n.removeEventListener("click",s.current);o.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",s.current)}},[n,r]),{onPointerDownCapture:()=>o.current=!0}}(e=>{let t=e.target,n=[...T.branches].some(e=>e.contains(t));L&&!n&&(null==g||g(e),null==x||x(e),e.defaultPrevented||null==w||w())},A),V=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,l.c)(e),o=i.useRef(!1);return i.useEffect(()=>{let e=e=>{e.target&&!o.current&&p("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{let t=e.target;![...T.branches].some(e=>e.contains(t))&&(null==y||y(e),null==x||x(e),e.defaultPrevented||null==w||w())},A);return!function(e,t=globalThis?.document){let n=(0,l.c)(e);i.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{k===T.layers.size-1&&(null==v||v(e),!e.defaultPrevented&&w&&(e.preventDefault(),w()))},A),i.useEffect(()=>{if(E)return m&&(0===T.layersWithOutsidePointerEventsDisabled.size&&(r=A.body.style.pointerEvents,A.body.style.pointerEvents="none"),T.layersWithOutsidePointerEventsDisabled.add(E)),T.layers.add(E),f(),()=>{m&&1===T.layersWithOutsidePointerEventsDisabled.size&&(A.body.style.pointerEvents=r)}},[E,A,m,T]),i.useEffect(()=>()=>{E&&(T.layers.delete(E),T.layersWithOutsidePointerEventsDisabled.delete(E),f())},[E,T]),i.useEffect(()=>{let e=()=>S({});return document.addEventListener(c,e),()=>document.removeEventListener(c,e)},[]),(0,u.jsx)(s.sG.div,{...b,ref:C,style:{pointerEvents:j?L?"auto":"none":void 0,...e.style},onFocusCapture:(0,o.m)(e.onFocusCapture,V.onFocusCapture),onBlurCapture:(0,o.m)(e.onBlurCapture,V.onBlurCapture),onPointerDownCapture:(0,o.m)(e.onPointerDownCapture,O.onPointerDownCapture)})});function f(){let e=new CustomEvent(c);document.dispatchEvent(e)}function p(e,t,n,r){let{discrete:i}=r,o=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),i?(0,s.hO)(o,a):o.dispatchEvent(a)}h.displayName="DismissableLayer",i.forwardRef((e,t)=>{let n=i.useContext(d),r=i.useRef(null),o=(0,a.s)(t,r);return i.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,u.jsx)(s.sG.div,{...e,ref:o})}).displayName="DismissableLayerBranch"},83718:(e,t,n)=>{n.d(t,{OQ:()=>c,bt:()=>l});var r=n(93177),i=n(37741),o=n(75170),s=n(62927);let a=e=>!isNaN(parseFloat(e)),l={current:void 0};class u{constructor(e,t={}){this.version="11.18.2",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(e,t=!0)=>{let n=r.k.now();this.updatedAt!==n&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(e),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),t&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(e),this.owner=t.owner}setCurrent(e){this.current=e,this.updatedAt=r.k.now(),null===this.canTrackVelocity&&void 0!==e&&(this.canTrackVelocity=a(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new i.v);let n=this.events[e].add(t);return"change"===e?()=>{n(),s.Gt.read(()=>{this.events.change.getSize()||this.stop()})}:n}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e,t=!0){t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,n){this.set(t),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-n}jump(e,t=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,t&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return l.current&&l.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){let e=r.k.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let t=Math.min(this.updatedAt-this.prevUpdatedAt,30);return(0,o.f)(parseFloat(this.current)-parseFloat(this.prevFrameValue),t)}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function c(e,t){return new u(e,t)}},84160:(e,t,n)=>{n.d(t,{UC:()=>V,VY:()=>B,ZD:()=>F,ZL:()=>L,bL:()=>k,hE:()=>I,hJ:()=>O,l9:()=>j,rc:()=>N});var r=n(41987),i=n(80482),o=n(49640),s=n(25173),a=n(29254),l=n(79649),u=n(44995),c="AlertDialog",[d,h]=(0,i.A)(c,[s.Hs]),f=(0,s.Hs)(),p=e=>{let{__scopeAlertDialog:t,...n}=e,r=f(t);return(0,u.jsx)(s.bL,{...r,...n,modal:!0})};p.displayName=c;var m=r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,...r}=e,i=f(n);return(0,u.jsx)(s.l9,{...i,...r,ref:t})});m.displayName="AlertDialogTrigger";var v=e=>{let{__scopeAlertDialog:t,...n}=e,r=f(t);return(0,u.jsx)(s.ZL,{...r,...n})};v.displayName="AlertDialogPortal";var g=r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,...r}=e,i=f(n);return(0,u.jsx)(s.hJ,{...i,...r,ref:t})});g.displayName="AlertDialogOverlay";var y="AlertDialogContent",[x,w]=d(y),b=(0,l.Dc)("AlertDialogContent"),T=r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,children:i,...l}=e,c=f(n),d=r.useRef(null),h=(0,o.s)(t,d),p=r.useRef(null);return(0,u.jsx)(s.G$,{contentName:y,titleName:E,docsSlug:"alert-dialog",children:(0,u.jsx)(x,{scope:n,cancelRef:p,children:(0,u.jsxs)(s.UC,{role:"alertdialog",...c,...l,ref:h,onOpenAutoFocus:(0,a.m)(l.onOpenAutoFocus,e=>{var t;e.preventDefault(),null==(t=p.current)||t.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,u.jsx)(b,{children:i}),(0,u.jsx)(D,{contentRef:d})]})})})});T.displayName=y;var E="AlertDialogTitle",P=r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,...r}=e,i=f(n);return(0,u.jsx)(s.hE,{...i,...r,ref:t})});P.displayName=E;var A="AlertDialogDescription",S=r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,...r}=e,i=f(n);return(0,u.jsx)(s.VY,{...i,...r,ref:t})});S.displayName=A;var C=r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,...r}=e,i=f(n);return(0,u.jsx)(s.bm,{...i,...r,ref:t})});C.displayName="AlertDialogAction";var R="AlertDialogCancel",M=r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,...r}=e,{cancelRef:i}=w(R,n),a=f(n),l=(0,o.s)(t,i);return(0,u.jsx)(s.bm,{...a,...r,ref:l})});M.displayName=R;var D=e=>{let{contentRef:t}=e,n="`".concat(y,"` requires a description for the component to be accessible for screen reader users.\n\nYou can add a description to the `").concat(y,"` by passing a `").concat(A,"` component as a child, which also benefits sighted users by adding visible context to the dialog.\n\nAlternatively, you can use your own component as a description by assigning it an `id` and passing the same value to the `aria-describedby` prop in `").concat(y,"`. If the description is confusing or duplicative for sighted users, you can use the `@radix-ui/react-visually-hidden` primitive as a wrapper around your description component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/alert-dialog");return r.useEffect(()=>{var e;document.getElementById(null==(e=t.current)?void 0:e.getAttribute("aria-describedby"))||console.warn(n)},[n,t]),null},k=p,j=m,L=v,O=g,V=T,N=C,F=M,I=P,B=S},86111:(e,t,n)=>{n.d(t,{_:()=>r});function r(e,t,n){if(t.has(e))throw TypeError("Cannot initialize the same private elements twice on an object");t.set(e,n)}},87076:(e,t,n)=>{n.d(t,{i:()=>a});var r,i=n(41987),o=n(17247),s=(r||(r=n.t(i,2)))[" useInsertionEffect ".trim().toString()]||o.N;function a({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[o,a,l]=function({defaultProp:e,onChange:t}){let[n,r]=i.useState(e),o=i.useRef(n),a=i.useRef(t);return s(()=>{a.current=t},[t]),i.useEffect(()=>{o.current!==n&&(a.current?.(n),o.current=n)},[n,o]),[n,r,a]}({defaultProp:t,onChange:n}),u=void 0!==e,c=u?e:o;{let t=i.useRef(void 0!==e);i.useEffect(()=>{let e=t.current;if(e!==u){let t=u?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=u},[u,r])}return[c,i.useCallback(t=>{if(u){let n="function"==typeof t?t(e):t;n!==e&&l.current?.(n)}else a(t)},[u,e,a,l])]}Symbol("RADIX:SYNC_STATE")},89075:(e,t,n)=>{n.d(t,{a:()=>o});var r=n(19922),i=n(26468);function o(e,t,n){return function(e,t,n){let o=(0,i.a)(e,null==n?void 0:n.in);if(isNaN(t))return(0,r.w)((null==n?void 0:n.in)||e,NaN);if(!t)return o;let s=o.getDate(),a=(0,r.w)((null==n?void 0:n.in)||e,o.getTime());return(a.setMonth(o.getMonth()+t+1,0),s>=a.getDate())?a:(o.setFullYear(a.getFullYear(),a.getMonth(),s),o)}(e,-t,n)}},90548:(e,t,n)=>{n.d(t,{t:()=>r});let r=(0,n(41987).createContext)(null)},92844:(e,t,n)=>{n.d(t,{B:()=>u});var r=n(63879),i=n(44627),o=n(71097),s=n(69292);let a=e=>(0,r.q)(0,255,e),l={...i.ai,transform:e=>Math.round(a(e))},u={test:(0,s.$)("rgb","red"),parse:(0,s.q)("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+l.transform(e)+", "+l.transform(t)+", "+l.transform(n)+", "+(0,o.a)(i.X4.transform(r))+")"}},93177:(e,t,n)=>{let r;n.d(t,{k:()=>a});var i=n(93451),o=n(62927);function s(){r=void 0}let a={now:()=>(void 0===r&&a.set(o.uv.isProcessing||i.W.useManualTiming?o.uv.timestamp:performance.now()),r),set:e=>{r=e,queueMicrotask(s)}}},93343:(e,t,n)=>{n.d(t,{Z:()=>l});var r=n(41987),i=n(9604),o=n(7156),s=n(17247),a=n(44995),l=r.forwardRef((e,t)=>{var n,l;let{container:u,...c}=e,[d,h]=r.useState(!1);(0,s.N)(()=>h(!0),[]);let f=u||d&&(null==(l=globalThis)||null==(n=l.document)?void 0:n.body);return f?i.createPortal((0,a.jsx)(o.sG.div,{...c,ref:t}),f):null});l.displayName="Portal"},93451:(e,t,n)=>{n.d(t,{W:()=>r});let r={skipAnimations:!1,useManualTiming:!1}},93568:(e,t,n)=>{n.d(t,{c:()=>i});var r=n(41987);function i(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},94866:(e,t,n)=>{n.d(t,{KN:()=>o,gQ:()=>u,px:()=>s,uj:()=>i,vh:()=>a,vw:()=>l});let r=e=>({test:t=>"string"==typeof t&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),i=r("deg"),o=r("%"),s=r("px"),a=r("vh"),l=r("vw"),u={...o,parse:e=>o.parse(e)/100,transform:e=>o.transform(100*e)}},95775:(e,t,n)=>{n.d(t,{f:()=>o});var r=n(19922),i=n(26468);function o(e,t,n){let o=(0,i.a)(e,null==n?void 0:n.in);return isNaN(t)?(0,r.w)((null==n?void 0:n.in)||e,NaN):(t&&o.setDate(o.getDate()+t),o)}},96035:(e,t,n)=>{n.d(t,{B:()=>r});let r="undefined"!=typeof window},96303:(e,t,n)=>{n.d(t,{E:()=>i});var r=n(41987);let i=n(96035).B?r.useLayoutEffect:r.useEffect},97283:(e,t,n)=>{n.d(t,{G:()=>u});var r=n(11330),i=n(59991),o=n(18411),s=n(63879),a=n(69009),l=n(31088);function u(e,t,{clamp:n=!0,ease:c,mixer:d}={}){let h=e.length;if((0,i.V)(h===t.length,"Both input and output ranges must be the same length"),1===h)return()=>t[0];if(2===h&&t[0]===t[1])return()=>t[1];let f=e[0]===e[1];e[0]>e[h-1]&&(e=[...e].reverse(),t=[...t].reverse());let p=function(e,t,n){let i=[],o=n||a.j,s=e.length-1;for(let n=0;n<s;n++){let s=o(e[n],e[n+1]);if(t){let e=Array.isArray(t)?t[n]||r.l:t;s=(0,l.F)(e,s)}i.push(s)}return i}(t,c,d),m=p.length,v=n=>{if(f&&n<e[0])return t[0];let r=0;if(m>1)for(;r<e.length-2&&!(n<e[r+1]);r++);let i=(0,o.q)(e[r],e[r+1],n);return p[r](i)};return n?t=>v((0,s.q)(e[0],e[h-1],t)):v}},98718:(e,t,n)=>{n.d(t,{V:()=>c,f:()=>p});var r=n(62396);let i=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;var o=n(31546),s=n(71097);let a="number",l="color",u=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function c(e){let t=e.toString(),n=[],i={color:[],number:[],var:[]},o=[],s=0,c=t.replace(u,e=>(r.y.test(e)?(i.color.push(s),o.push(l),n.push(r.y.parse(e))):e.startsWith("var(")?(i.var.push(s),o.push("var"),n.push(e)):(i.number.push(s),o.push(a),n.push(parseFloat(e))),++s,"${}")).split("${}");return{values:n,split:c,indexes:i,types:o}}function d(e){return c(e).values}function h(e){let{split:t,types:n}=c(e),i=t.length;return e=>{let o="";for(let u=0;u<i;u++)if(o+=t[u],void 0!==e[u]){let t=n[u];t===a?o+=(0,s.a)(e[u]):t===l?o+=r.y.transform(e[u]):o+=e[u]}return o}}let f=e=>"number"==typeof e?0:e,p={test:function(e){var t,n;return isNaN(e)&&"string"==typeof e&&((null==(t=e.match(o.S))?void 0:t.length)||0)+((null==(n=e.match(i))?void 0:n.length)||0)>0},parse:d,createTransformer:h,getAnimatableNone:function(e){let t=d(e);return h(e)(t.map(f))}}},98889:(e,t,n)=>{n.d(t,{A:()=>l});var r=n(41987);let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((e,t,n)=>!!e&&n.indexOf(e)===t).join(" ")};var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let a=(0,r.forwardRef)((e,t)=>{let{color:n="currentColor",size:i=24,strokeWidth:a=2,absoluteStrokeWidth:l,className:u="",children:c,iconNode:d,...h}=e;return(0,r.createElement)("svg",{ref:t,...s,width:i,height:i,stroke:n,strokeWidth:l?24*Number(a)/Number(i):a,className:o("lucide",u),...h},[...d.map(e=>{let[t,n]=e;return(0,r.createElement)(t,n)}),...Array.isArray(c)?c:[c]])}),l=(e,t)=>{let n=(0,r.forwardRef)((n,s)=>{let{className:l,...u}=n;return(0,r.createElement)(a,{ref:s,iconNode:t,className:o("lucide-".concat(i(e)),l),...u})});return n.displayName="".concat(e),n}}}]);