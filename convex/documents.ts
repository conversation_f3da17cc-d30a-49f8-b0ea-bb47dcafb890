import { v } from 'convex/values';
import { mutation, query } from './_generated/server';
import { paginationOptsValidator } from 'convex/server';

// Create a new document
export const createDocument = mutation({
  args: {
    userId: v.id('users'),
    chatId: v.optional(v.id('chats')),
    title: v.string(),
    content: v.string(),
    kind: v.union(
      v.literal('text'),
      v.literal('code'),
      v.literal('image'),
      v.literal('sheet'),
    ),
    language: v.optional(v.string()),
    metadata: v.optional(v.any()),
  },
  handler: async (ctx, args) => {
    const now = new Date().toISOString();

    return await ctx.db.insert('documents', {
      userId: args.userId,
      chatId: args.chatId,
      title: args.title,
      content: args.content,
      kind: args.kind,
      language: args.language,
      metadata: args.metadata,
      createdAt: now,
      updatedAt: now,
    });
  },
});

// Update document
export const updateDocument = mutation({
  args: {
    documentId: v.id('documents'),
    title: v.optional(v.string()),
    content: v.optional(v.string()),
    metadata: v.optional(v.any()),
  },
  handler: async (ctx, args) => {
    const updates: any = {
      updatedAt: new Date().toISOString(),
    };

    if (args.title !== undefined) updates.title = args.title;
    if (args.content !== undefined) updates.content = args.content;
    if (args.metadata !== undefined) updates.metadata = args.metadata;

    await ctx.db.patch(args.documentId, updates);

    return { success: true };
  },
});

// Get document by ID
export const getDocumentById = query({
  args: { documentId: v.id('documents') },
  handler: async (ctx, args) => {
    const document = await ctx.db.get(args.documentId);
    if (!document) return null;

    const user = await ctx.db.get(document.userId);
    const suggestions = await ctx.db
      .query('suggestions')
      .withIndex('by_document', (q) => q.eq('documentId', args.documentId))
      .collect();

    return {
      ...document,
      user,
      suggestions,
    };
  },
});

// Get user documents
export const getUserDocuments = query({
  args: {
    userId: v.id('users'),
    kind: v.optional(
      v.union(
        v.literal('text'),
        v.literal('code'),
        v.literal('image'),
        v.literal('sheet'),
      ),
    ),
    paginationOpts: paginationOptsValidator,
  },
  handler: async (ctx, args) => {
    let query = ctx.db
      .query('documents')
      .withIndex('by_user', (q) => q.eq('userId', args.userId));

    if (args.kind) {
      query = ctx.db
        .query('documents')
        .withIndex('by_kind', (q) => q.eq('kind', args.kind))
        .filter((q) => q.eq(q.field('userId'), args.userId));
    }

    return await query.order('desc').paginate(args.paginationOpts);
  },
});

// Delete document
export const deleteDocument = mutation({
  args: {
    documentId: v.id('documents'),
    userId: v.id('users'),
  },
  handler: async (ctx, args) => {
    const document = await ctx.db.get(args.documentId);

    if (!document || document.userId !== args.userId) {
      throw new Error('Document not found or unauthorized');
    }

    // Delete all suggestions for this document
    const suggestions = await ctx.db
      .query('suggestions')
      .withIndex('by_document', (q) => q.eq('documentId', args.documentId))
      .collect();

    for (const suggestion of suggestions) {
      await ctx.db.delete(suggestion._id);
    }

    // Delete the document
    await ctx.db.delete(args.documentId);

    return { success: true };
  },
});

// Create suggestion
export const createSuggestion = mutation({
  args: {
    documentId: v.id('documents'),
    userId: v.id('users'),
    originalText: v.string(),
    suggestedText: v.string(),
    description: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert('suggestions', {
      documentId: args.documentId,
      userId: args.userId,
      originalText: args.originalText,
      suggestedText: args.suggestedText,
      description: args.description,
      isResolved: false,
      createdAt: new Date().toISOString(),
    });
  },
});

// Resolve suggestion
export const resolveSuggestion = mutation({
  args: {
    suggestionId: v.id('suggestions'),
    accept: v.boolean(),
  },
  handler: async (ctx, args) => {
    const suggestion = await ctx.db.get(args.suggestionId);
    if (!suggestion) {
      throw new Error('Suggestion not found');
    }

    await ctx.db.patch(args.suggestionId, {
      isResolved: true,
    });

    // If accepted, update the document
    if (args.accept) {
      const document = await ctx.db.get(suggestion.documentId);
      if (document) {
        const newContent = document.content.replace(
          suggestion.originalText,
          suggestion.suggestedText,
        );

        await ctx.db.patch(suggestion.documentId, {
          content: newContent,
          updatedAt: new Date().toISOString(),
        });
      }
    }

    return { success: true };
  },
});
