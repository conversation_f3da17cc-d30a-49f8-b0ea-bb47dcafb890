"use strict";(()=>{var e={};e.id=8449,e.ids=[8449],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{e.exports=require("node:buffer")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29727:e=>{e.exports=require("next/dist/compiled/next-server/app-page-experimental.runtime.prod.js")},41692:e=>{e.exports=require("node:tls")},46871:e=>{e.exports=require("next/dist/compiled/next-server/app-route-experimental.runtime.prod.js")},48412:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>q,routeModule:()=>w,serverHooks:()=>m,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>f});var n={};r.r(n),r.d(n,{GET:()=>x});var s=r(48106),a=r(48819),o=r(12050),i=r(52335),u=r(11455),p=r(56617),d=r(39824),c=r(34015),l=r(97420);async function x(e,{params:t}){let r,{id:n}=await t,s=(0,c.getStreamContext)(),a=new Date;if(!s)return new Response(null,{status:204});if(!n)return new p.P7("bad_request:api").toResponse();let o=await (0,i.j2)();if(!o?.user)return new p.P7("unauthorized:chat").toResponse();try{r=await (0,u.TJ)({id:n})}catch{return new p.P7("not_found:chat").toResponse()}if(!r)return new p.P7("not_found:chat").toResponse();if("private"===r.visibility&&r.userId!==o.user.id)return new p.P7("forbidden:chat").toResponse();let x=await (0,u.Nm)({chatId:n});if(!x.length)return new p.P7("not_found:stream").toResponse();let w=x.at(-1);if(!w)return new p.P7("not_found:stream").toResponse();let h=(0,d.G5)({execute:()=>{}}),f=await s.resumableStream(w,()=>h.pipeThrough(new d.JE));if(!f){let e=(await (0,u.de)({id:n})).at(-1);return!e||"assistant"!==e.role||function(e,t,r){var n;return(n=void 0,e=>{let t=(n?Math[n]:Math.trunc)(e);return 0===t?0:t})((+(0,l.a)(e)-+(0,l.a)(t))/1e3)}(a,new Date(e.createdAt))>15?new Response(h,{status:200}):new Response((0,d.G5)({execute:({writer:t})=>{t.write({type:"data-appendMessage",data:JSON.stringify(e),transient:!0})}}).pipeThrough(new d.JE),{status:200})}return new Response(f,{status:200})}let w=new s.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/(chat)/api/chat/[id]/stream/route",pathname:"/api/chat/[id]/stream",filename:"route",bundlePath:"app/(chat)/api/chat/[id]/stream/route"},resolvedPagePath:"/Users/<USER>/Downloads/Coding/Personal/Bonkai/apps/web/app/(chat)/api/chat/[id]/stream/route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:h,workUnitAsyncStorage:f,serverHooks:m}=w;function q(){return(0,o.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:f})}},55511:e=>{e.exports=require("crypto")},57975:e=>{e.exports=require("node:util")},58500:e=>{e.exports=require("node:timers/promises")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{e.exports=require("node:url")},77030:e=>{e.exports=require("node:net")},77598:e=>{e.exports=require("node:crypto")},78474:e=>{e.exports=require("node:events")}};var t=require("../../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[191,4235,2543,5208,8814,2911,8310,4294,4578],()=>r(48412));module.exports=n})();