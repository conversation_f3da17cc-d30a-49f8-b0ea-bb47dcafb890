import { ConvexHttpClient } from 'convex/browser';
import { env } from '../config/env.js';

// Initialize Convex client
export const convex = new ConvexHttpClient(env.CONVEX_URL);

// Helper function to execute Convex functions with error handling
export async function executeConvexFunction<T>(
  functionName: string,
  args: any = {},
): Promise<T> {
  try {
    return await convex.query(functionName as any, args);
  } catch (error) {
    console.error(`Error executing Convex function ${functionName}:`, error);
    throw error;
  }
}

export async function executeConvexMutation<T>(
  functionName: string,
  args: any = {},
): Promise<T> {
  try {
    return await convex.mutation(functionName as any, args);
  } catch (error) {
    console.error(`Error executing Convex mutation ${functionName}:`, error);
    throw error;
  }
}
