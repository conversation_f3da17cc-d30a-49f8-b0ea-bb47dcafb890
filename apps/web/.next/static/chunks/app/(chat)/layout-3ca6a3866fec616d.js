(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7097],{44766:(e,s,t)=>{Promise.resolve().then(t.bind(t,45706)),Promise.resolve().then(t.bind(t,91984)),Promise.resolve().then(t.bind(t,92630)),Promise.resolve().then(t.t.bind(t,44760,23))},45706:(e,s,t)=>{"use strict";t.d(s,{AppSidebar:()=>N});var a=t(44995),n=t(62942),r=t(34109),i=t(87747),l=t(76430),d=t(61773),c=t(6808),o=t(68309),u=t(72123),h=t(92630),x=t(18654);t(50251);var m=t(40459);m.env.PLAYWRIGHT_TEST_BASE_URL||m.env.PLAYWRIGHT||m.env.CI_PLAYWRIGHT;let j=/^guest-\d+$/;function p(e){var s,t,i;let{user:m}=e,p=(0,n.useRouter)(),{data:g,status:b}=(0,c.wV)(),{setTheme:v,resolvedTheme:f}=(0,o.D)(),N=j.test(null!=(t=null==g||null==(s=g.user)?void 0:s.email)?t:"");return(0,a.jsx)(h.wZ,{children:(0,a.jsx)(h.FX,{children:(0,a.jsxs)(u.rI,{children:[(0,a.jsx)(u.ty,{asChild:!0,children:"loading"===b?(0,a.jsxs)(h.Uj,{className:"data-[state=open]:bg-sidebar-accent bg-background data-[state=open]:text-sidebar-accent-foreground h-10 justify-between",children:[(0,a.jsxs)("div",{className:"flex flex-row gap-2",children:[(0,a.jsx)("div",{className:"size-6 bg-zinc-500/30 rounded-full animate-pulse"}),(0,a.jsx)("span",{className:"bg-zinc-500/30 text-transparent rounded-md animate-pulse",children:"Loading auth status"})]}),(0,a.jsx)("div",{className:"animate-spin text-zinc-500",children:(0,a.jsx)(r.hz,{})})]}):(0,a.jsxs)(h.Uj,{"data-testid":"user-nav-button",className:"data-[state=open]:bg-sidebar-accent bg-background data-[state=open]:text-sidebar-accent-foreground h-10",children:[(0,a.jsx)(d.default,{src:"https://avatar.vercel.sh/".concat(m.email),alt:null!=(i=m.email)?i:"User Avatar",width:24,height:24,className:"rounded-full"}),(0,a.jsx)("span",{"data-testid":"user-email",className:"truncate",children:N?"Guest":null==m?void 0:m.email}),(0,a.jsx)(l.A,{className:"ml-auto"})]})}),(0,a.jsxs)(u.SQ,{"data-testid":"user-nav-menu",side:"top",className:"w-[--radix-popper-anchor-width]",children:[(0,a.jsx)(u._2,{"data-testid":"user-nav-item-theme",className:"cursor-pointer",onSelect:()=>v("dark"===f?"light":"dark"),children:"Toggle ".concat("light"===f?"dark":"light"," mode")}),(0,a.jsx)(u.mB,{}),(0,a.jsx)(u._2,{asChild:!0,"data-testid":"user-nav-item-auth",children:(0,a.jsx)("button",{type:"button",className:"w-full cursor-pointer",onClick:()=>{if("loading"===b)return void(0,x.o)({type:"error",description:"Checking authentication status, please try again!"});N?p.push("/login"):(0,c.CI)({redirectTo:"/"})},children:N?"Login to your account":"Sign out"})})]})]})})})}var g=t(22710),b=t(27261),v=t.n(b),f=t(66999);function N(e){let{user:s}=e,t=(0,n.useRouter)(),{setOpenMobile:l}=(0,h.cL)();return(0,a.jsxs)(h.Bx,{className:"group-data-[side=left]:border-r-0",children:[(0,a.jsx)(h.Gh,{children:(0,a.jsx)(h.wZ,{children:(0,a.jsxs)("div",{className:"flex flex-row justify-between items-center",children:[(0,a.jsx)(v(),{href:"/",onClick:()=>{l(!1)},className:"flex flex-row gap-3 items-center",children:(0,a.jsx)("span",{className:"text-lg font-semibold px-2 hover:bg-muted rounded-md cursor-pointer",children:"Chatbot"})}),(0,a.jsxs)(f.m_,{children:[(0,a.jsx)(f.k$,{asChild:!0,children:(0,a.jsx)(g.$,{variant:"ghost",type:"button",className:"p-2 h-fit",onClick:()=>{l(!1),t.push("/"),t.refresh()},children:(0,a.jsx)(r.c1,{})})}),(0,a.jsx)(f.ZI,{align:"end",children:"New Chat"})]})]})})}),(0,a.jsx)(h.Yv,{children:(0,a.jsx)(i.b,{user:s})}),(0,a.jsx)(h.CG,{children:s&&(0,a.jsx)(p,{user:s})})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[9718,2106,6698,6448,434,6808,6561,5082,6634,1149,1166,7358],()=>s(44766)),_N_E=e.O()}]);