module.exports = {

"[project]/apps/web/.next-internal/server/app/(auth)/api/auth/guest/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-experimental.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-experimental.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-experimental.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-experimental.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/@opentelemetry/api [external] (@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@opentelemetry/api", () => require("@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-experimental.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-experimental.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-experimental.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-experimental.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/node:crypto [external] (node:crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:crypto", () => require("node:crypto"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/node:buffer [external] (node:buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}}),
"[externals]/node:crypto [external] (node:crypto, cjs) <export randomFillSync as default>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$crypto__$5b$external$5d$__$28$node$3a$crypto$2c$__cjs$29$__["randomFillSync"])
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$crypto__$5b$external$5d$__$28$node$3a$crypto$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/node:crypto [external] (node:crypto, cjs)");
}}),
"[externals]/node:util [external] (node:util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:util", () => require("node:util"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/apps/web/lib/db/queries.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Temporary stub functions for legacy compatibility
// These should be replaced with Convex queries in the full migration
__turbopack_context__.s({
    "createGuestUser": (()=>createGuestUser),
    "createStreamId": (()=>createStreamId),
    "createUser": (()=>createUser),
    "deleteChatById": (()=>deleteChatById),
    "deleteDocumentsByIdAfterTimestamp": (()=>deleteDocumentsByIdAfterTimestamp),
    "deleteMessagesByChatIdAfterTimestamp": (()=>deleteMessagesByChatIdAfterTimestamp),
    "getChatById": (()=>getChatById),
    "getChatsByUserId": (()=>getChatsByUserId),
    "getDocumentById": (()=>getDocumentById),
    "getDocumentsById": (()=>getDocumentsById),
    "getDocumentsByUserId": (()=>getDocumentsByUserId),
    "getMessageById": (()=>getMessageById),
    "getMessageCountByUserId": (()=>getMessageCountByUserId),
    "getMessagesByChatId": (()=>getMessagesByChatId),
    "getStreamIdsByChatId": (()=>getStreamIdsByChatId),
    "getSuggestionsByDocumentId": (()=>getSuggestionsByDocumentId),
    "getUser": (()=>getUser),
    "getVotesByChatId": (()=>getVotesByChatId),
    "saveChat": (()=>saveChat),
    "saveDocument": (()=>saveDocument),
    "saveMessages": (()=>saveMessages),
    "saveSuggestions": (()=>saveSuggestions),
    "updateChatVisiblityById": (()=>updateChatVisiblityById),
    "voteMessage": (()=>voteMessage)
});
async function getUser(email) {
    // TODO: Replace with Convex query
    console.warn('getUser called with legacy implementation - needs Convex migration');
    return [];
}
async function createGuestUser() {
    // TODO: Replace with Convex mutation
    console.warn('createGuestUser called with legacy implementation - needs Convex migration');
    return [
        {
            id: `guest-${Date.now()}`,
            email: null,
            password: null
        }
    ];
}
async function saveChat() {
    // TODO: Replace with Convex mutation
    console.warn('saveChat called with legacy implementation - needs Convex migration');
    return null;
}
async function deleteChatById() {
    // TODO: Replace with Convex mutation
    console.warn('deleteChatById called with legacy implementation - needs Convex migration');
    return null;
}
async function getChatsByUserId() {
    // TODO: Replace with Convex query
    console.warn('getChatsByUserId called with legacy implementation - needs Convex migration');
    return [];
}
async function getChatById() {
    // TODO: Replace with Convex query
    console.warn('getChatById called with legacy implementation - needs Convex migration');
    return null;
}
async function saveMessages() {
    // TODO: Replace with Convex mutation
    console.warn('saveMessages called with legacy implementation - needs Convex migration');
    return null;
}
async function getMessagesByChatId() {
    // TODO: Replace with Convex query
    console.warn('getMessagesByChatId called with legacy implementation - needs Convex migration');
    return [];
}
async function voteMessage() {
    // TODO: Replace with Convex mutation
    console.warn('voteMessage called with legacy implementation - needs Convex migration');
    return null;
}
async function getVotesByChatId() {
    // TODO: Replace with Convex query
    console.warn('getVotesByChatId called with legacy implementation - needs Convex migration');
    return [];
}
async function saveDocument() {
    // TODO: Replace with Convex mutation
    console.warn('saveDocument called with legacy implementation - needs Convex migration');
    return null;
}
async function getDocumentById() {
    // TODO: Replace with Convex query
    console.warn('getDocumentById called with legacy implementation - needs Convex migration');
    return null;
}
async function getDocumentsByUserId() {
    // TODO: Replace with Convex query
    console.warn('getDocumentsByUserId called with legacy implementation - needs Convex migration');
    return [];
}
async function deleteDocumentsByIdAfterTimestamp() {
    // TODO: Replace with Convex mutation
    console.warn('deleteDocumentsByIdAfterTimestamp called with legacy implementation - needs Convex migration');
    return null;
}
async function saveSuggestions() {
    // TODO: Replace with Convex mutation
    console.warn('saveSuggestions called with legacy implementation - needs Convex migration');
    return null;
}
async function getSuggestionsByDocumentId() {
    // TODO: Replace with Convex query
    console.warn('getSuggestionsByDocumentId called with legacy implementation - needs Convex migration');
    return [];
}
async function createUser() {
    // TODO: Replace with Convex mutation
    console.warn('createUser called with legacy implementation - needs Convex migration');
    return null;
}
async function getMessageById() {
    // TODO: Replace with Convex query
    console.warn('getMessageById called with legacy implementation - needs Convex migration');
    return null;
}
async function deleteMessagesByChatIdAfterTimestamp() {
    // TODO: Replace with Convex mutation
    console.warn('deleteMessagesByChatIdAfterTimestamp called with legacy implementation - needs Convex migration');
    return null;
}
async function updateChatVisiblityById() {
    // TODO: Replace with Convex mutation
    console.warn('updateChatVisiblityById called with legacy implementation - needs Convex migration');
    return null;
}
async function getStreamIdsByChatId() {
    // TODO: Replace with Convex query
    console.warn('getStreamIdsByChatId called with legacy implementation - needs Convex migration');
    return [];
}
async function getMessageCountByUserId() {
    // TODO: Replace with Convex query
    console.warn('getMessageCountByUserId called with legacy implementation - needs Convex migration');
    return 0;
}
async function createStreamId() {
    // TODO: Replace with Convex mutation
    console.warn('createStreamId called with legacy implementation - needs Convex migration');
    return `stream-${Date.now()}`;
}
async function getDocumentsById() {
    // TODO: Replace with Convex query
    console.warn('getDocumentsById called with legacy implementation - needs Convex migration');
    return [];
}
}}),
"[project]/apps/web/app/(auth)/auth.config.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "authConfig": (()=>authConfig)
});
const authConfig = {
    pages: {
        signIn: '/login',
        newUser: '/'
    },
    providers: [],
    callbacks: {}
};
}}),
"[project]/apps/web/lib/db/utils.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "generateDummyPassword": (()=>generateDummyPassword),
    "hashPassword": (()=>hashPassword)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcrypt$2d$ts$2f$dist$2f$node$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/bcrypt-ts/dist/node.mjs [app-route] (ecmascript)");
;
function generateDummyPassword() {
    // Generate a dummy password hash for timing attack protection
    return '$2b$10$K1V5qz0cZGaJGDwQO4CQuu4Xr5bZnQfCqFkC7l0qoF5zVhsVz7/.2';
}
async function hashPassword(password) {
    const salt = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcrypt$2d$ts$2f$dist$2f$node$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["genSalt"])(10);
    return await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcrypt$2d$ts$2f$dist$2f$node$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["hash"])(password, salt);
}
}}),
"[project]/apps/web/lib/constants.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "DUMMY_PASSWORD": (()=>DUMMY_PASSWORD),
    "guestRegex": (()=>guestRegex),
    "isDevelopmentEnvironment": (()=>isDevelopmentEnvironment),
    "isProductionEnvironment": (()=>isProductionEnvironment),
    "isTestEnvironment": (()=>isTestEnvironment)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$lib$2f$db$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/lib/db/utils.ts [app-route] (ecmascript)");
;
const isProductionEnvironment = ("TURBOPACK compile-time value", "development") === 'production';
const isDevelopmentEnvironment = ("TURBOPACK compile-time value", "development") === 'development';
const isTestEnvironment = Boolean(process.env.PLAYWRIGHT_TEST_BASE_URL || process.env.PLAYWRIGHT || process.env.CI_PLAYWRIGHT);
const guestRegex = /^guest-\d+$/;
const DUMMY_PASSWORD = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$lib$2f$db$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateDummyPassword"])();
}}),
"[project]/apps/web/app/(auth)/auth.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST),
    "auth": (()=>auth),
    "signIn": (()=>signIn),
    "signOut": (()=>signOut)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcrypt$2d$ts$2f$dist$2f$node$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/bcrypt-ts/dist/node.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next-auth/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-auth/index.js [app-route] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next-auth/providers/credentials.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$auth$2f$core$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@auth/core/providers/credentials.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$lib$2f$db$2f$queries$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/lib/db/queries.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$app$2f28$auth$292f$auth$2e$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/app/(auth)/auth.config.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$lib$2f$constants$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/lib/constants.ts [app-route] (ecmascript)");
;
;
;
;
;
;
const { handlers: { GET, POST }, auth, signIn, signOut } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"])({
    ...__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$app$2f28$auth$292f$auth$2e$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authConfig"],
    providers: [
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$auth$2f$core$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])({
            credentials: {},
            async authorize ({ email, password }) {
                const users = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$lib$2f$db$2f$queries$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getUser"])(email);
                if (users.length === 0) {
                    await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcrypt$2d$ts$2f$dist$2f$node$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["compare"])(password, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$lib$2f$constants$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DUMMY_PASSWORD"]);
                    return null;
                }
                const [user] = users;
                if (!user.password) {
                    await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcrypt$2d$ts$2f$dist$2f$node$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["compare"])(password, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$lib$2f$constants$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DUMMY_PASSWORD"]);
                    return null;
                }
                const passwordsMatch = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcrypt$2d$ts$2f$dist$2f$node$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["compare"])(password, user.password);
                if (!passwordsMatch) return null;
                return {
                    ...user,
                    type: 'regular'
                };
            }
        }),
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$auth$2f$core$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])({
            id: 'guest',
            credentials: {},
            async authorize () {
                const [guestUser] = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$lib$2f$db$2f$queries$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createGuestUser"])();
                return {
                    ...guestUser,
                    type: 'guest'
                };
            }
        })
    ],
    callbacks: {
        async jwt ({ token, user }) {
            if (user) {
                token.id = user.id;
                token.type = user.type;
            }
            return token;
        },
        async session ({ session, token }) {
            if (session.user) {
                session.user.id = token.id;
                session.user.type = token.type;
            }
            return session;
        }
    }
});
}}),
"[project]/apps/web/app/(auth)/api/auth/guest/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$app$2f28$auth$292f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/app/(auth)/auth.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$lib$2f$constants$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/lib/constants.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$jwt$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next-auth/jwt.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$auth$2f$core$2f$jwt$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@auth/core/jwt.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
;
;
;
;
async function GET(request) {
    const { searchParams } = new URL(request.url);
    const redirectUrl = searchParams.get('redirectUrl') || '/';
    const token = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$auth$2f$core$2f$jwt$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getToken"])({
        req: request,
        secret: process.env.AUTH_SECRET,
        secureCookie: !__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$lib$2f$constants$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isDevelopmentEnvironment"]
    });
    if (token) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL('/', request.url));
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$app$2f28$auth$292f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["signIn"])('guest', {
        redirect: true,
        redirectTo: redirectUrl
    });
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__7c5de47c._.js.map