"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1426],{34985:(e,t,r)=>{r.r(t),r.d(t,{Account:()=>x,AddressLookupTableAccount:()=>eH,AddressLookupTableInstruction:()=>rS,AddressLookupTableProgram:()=>rI,Authorized:()=>rR,BLOCKHASH_CACHE_TIMEOUT_MS:()=>eF,BPF_LOADER_DEPRECATED_PROGRAM_ID:()=>L,BPF_LOADER_PROGRAM_ID:()=>ex,BpfLoader:()=>eL,COMPUTE_BUDGET_INSTRUCTION_LAYOUTS:()=>rv,ComputeBudgetInstruction:()=>r_,ComputeBudgetProgram:()=>rA,Connection:()=>rf,Ed25519Program:()=>rE,Enum:()=>W,EpochSchedule:()=>eY,FeeCalculatorLayout:()=>eA,Keypair:()=>rk,LAMPORTS_PER_SOL:()=>r1,LOOKUP_TABLE_INSTRUCTION_LAYOUTS:()=>rw,Loader:()=>eC,Lockup:()=>rC,MAX_SEED_LENGTH:()=>N,Message:()=>ee,MessageAccountKeys:()=>j,MessageV0:()=>et,NONCE_ACCOUNT_LENGTH:()=>eE,NonceAccount:()=>eB,PACKET_DATA_SIZE:()=>K,PUBLIC_KEY_LENGTH:()=>O,PublicKey:()=>C,SIGNATURE_LENGTH_IN_BYTES:()=>Y,SOLANA_SCHEMA:()=>T,STAKE_CONFIG_ID:()=>rO,STAKE_INSTRUCTION_LAYOUTS:()=>rL,SYSTEM_INSTRUCTION_LAYOUTS:()=>eN,SYSVAR_CLOCK_PUBKEY:()=>eu,SYSVAR_EPOCH_SCHEDULE_PUBKEY:()=>el,SYSVAR_INSTRUCTIONS_PUBKEY:()=>ed,SYSVAR_RECENT_BLOCKHASHES_PUBKEY:()=>eh,SYSVAR_RENT_PUBKEY:()=>eg,SYSVAR_REWARDS_PUBKEY:()=>ep,SYSVAR_SLOT_HASHES_PUBKEY:()=>em,SYSVAR_SLOT_HISTORY_PUBKEY:()=>ey,SYSVAR_STAKE_HISTORY_PUBKEY:()=>eb,Secp256k1Program:()=>rN,SendTransactionError:()=>ef,SolanaJSONRPCError:()=>ew,SolanaJSONRPCErrorCode:()=>ek,StakeAuthorizationLayout:()=>rK,StakeInstruction:()=>rx,StakeProgram:()=>rz,Struct:()=>B,SystemInstruction:()=>eT,SystemProgram:()=>eO,Transaction:()=>ea,TransactionExpiredBlockheightExceededError:()=>q,TransactionExpiredNonceInvalidError:()=>H,TransactionExpiredTimeoutError:()=>D,TransactionInstruction:()=>en,TransactionMessage:()=>eo,TransactionStatus:()=>ei,VALIDATOR_INFO_KEY:()=>rM,VERSION_PREFIX_MASK:()=>z,VOTE_PROGRAM_ID:()=>r$,ValidatorInfo:()=>rV,VersionedMessage:()=>er,VersionedTransaction:()=>ec,VoteAccount:()=>rJ,VoteAuthorizationLayout:()=>rH,VoteInit:()=>rY,VoteInstruction:()=>rq,VoteProgram:()=>rj,clusterApiUrl:()=>rQ,sendAndConfirmRawTransaction:()=>r0,sendAndConfirmTransaction:()=>eS});var i,s,n=r(50887),a=r(2364),o=r(66732),c=r.n(o),u=r(22735),l=r.n(u),d=r(94769),h=r(84437),g=r(68153),p=r(83562),m=r(21189),y=r(24380),b=r.n(y),f=r(52155),k=r(62889),w=r(13458);let S=a.ev.utils.randomPrivateKey,I=()=>{let e=a.ev.utils.randomPrivateKey(),t=_(e),r=new Uint8Array(64);return r.set(e),r.set(t,32),{publicKey:t,secretKey:r}},_=a.ev.getPublicKey;function v(e){try{return a.ev.ExtendedPoint.fromHex(e),!0}catch{return!1}}let A=(e,t)=>a.ev.sign(e,t.slice(0,32)),P=a.ev.verify,E=e=>n.Buffer.isBuffer(e)?e:e instanceof Uint8Array?n.Buffer.from(e.buffer,e.byteOffset,e.byteLength):n.Buffer.from(e);class B{constructor(e){Object.assign(this,e)}encode(){return n.Buffer.from((0,h.serialize)(T,this))}static decode(e){return(0,h.deserialize)(T,this,e)}static decodeUnchecked(e){return(0,h.deserializeUnchecked)(T,this,e)}}class W extends B{constructor(e){if(super(e),this.enum="",1!==Object.keys(e).length)throw Error("Enum can only take single value");Object.keys(e).map(e=>{this.enum=e})}}let T=new Map,N=32,O=32,R=1;class C extends B{constructor(e){if(super({}),this._bn=void 0,void 0!==e._bn)this._bn=e._bn;else{if("string"==typeof e){let t=l().decode(e);if(t.length!=O)throw Error("Invalid public key input");this._bn=new(c())(t)}else this._bn=new(c())(e);if(this._bn.byteLength()>O)throw Error("Invalid public key input")}}static unique(){let e=new C(R);return R+=1,new C(e.toBuffer())}equals(e){return this._bn.eq(e._bn)}toBase58(){return l().encode(this.toBytes())}toJSON(){return this.toBase58()}toBytes(){let e=this.toBuffer();return new Uint8Array(e.buffer,e.byteOffset,e.byteLength)}toBuffer(){let e=this._bn.toArrayLike(n.Buffer);if(e.length===O)return e;let t=n.Buffer.alloc(32);return e.copy(t,32-e.length),t}get[Symbol.toStringTag](){return`PublicKey(${this.toString()})`}toString(){return this.toBase58()}static async createWithSeed(e,t,r){let i=n.Buffer.concat([e.toBuffer(),n.Buffer.from(t),r.toBuffer()]);return new C((0,d.sc)(i))}static createProgramAddressSync(e,t){let r=n.Buffer.alloc(0);e.forEach(function(e){if(e.length>N)throw TypeError("Max seed length exceeded");r=n.Buffer.concat([r,E(e)])}),r=n.Buffer.concat([r,t.toBuffer(),n.Buffer.from("ProgramDerivedAddress")]);let i=(0,d.sc)(r);if(v(i))throw Error("Invalid seeds, address must fall off the curve");return new C(i)}static async createProgramAddress(e,t){return this.createProgramAddressSync(e,t)}static findProgramAddressSync(e,t){let r,i=255;for(;0!=i;){try{let s=e.concat(n.Buffer.from([i]));r=this.createProgramAddressSync(s,t)}catch(e){if(e instanceof TypeError)throw e;i--;continue}return[r,i]}throw Error("Unable to find a viable program address nonce")}static async findProgramAddress(e,t){return this.findProgramAddressSync(e,t)}static isOnCurve(e){return v(new C(e).toBytes())}}C.default=new C("11111111111111111111111111111111"),T.set(C,{kind:"struct",fields:[["_bn","u256"]]});class x{constructor(e){if(this._publicKey=void 0,this._secretKey=void 0,e){let t=E(e);if(64!==e.length)throw Error("bad secret key size");this._publicKey=t.slice(32,64),this._secretKey=t.slice(0,32)}else this._secretKey=E(S()),this._publicKey=E(_(this._secretKey))}get publicKey(){return new C(this._publicKey)}get secretKey(){return n.Buffer.concat([this._secretKey,this._publicKey],64)}}let L=new C("BPFLoader1111111111111111111111111111111111"),K=1232,z=127,Y=64;class q extends Error{constructor(e){super(`Signature ${e} has expired: block height exceeded.`),this.signature=void 0,this.signature=e}}Object.defineProperty(q.prototype,"name",{value:"TransactionExpiredBlockheightExceededError"});class D extends Error{constructor(e,t){super(`Transaction was not confirmed in ${t.toFixed(2)} seconds. It is unknown if it succeeded or failed. Check signature ${e} using the Solana Explorer or CLI tools.`),this.signature=void 0,this.signature=e}}Object.defineProperty(D.prototype,"name",{value:"TransactionExpiredTimeoutError"});class H extends Error{constructor(e){super(`Signature ${e} has expired: the nonce is no longer valid.`),this.signature=void 0,this.signature=e}}Object.defineProperty(H.prototype,"name",{value:"TransactionExpiredNonceInvalidError"});class j{constructor(e,t){this.staticAccountKeys=void 0,this.accountKeysFromLookups=void 0,this.staticAccountKeys=e,this.accountKeysFromLookups=t}keySegments(){let e=[this.staticAccountKeys];return this.accountKeysFromLookups&&(e.push(this.accountKeysFromLookups.writable),e.push(this.accountKeysFromLookups.readonly)),e}get(e){for(let t of this.keySegments())if(e<t.length)return t[e];else e-=t.length}get length(){return this.keySegments().flat().length}compileInstructions(e){if(this.length>256)throw Error("Account index overflow encountered during compilation");let t=new Map;this.keySegments().flat().forEach((e,r)=>{t.set(e.toBase58(),r)});let r=e=>{let r=t.get(e.toBase58());if(void 0===r)throw Error("Encountered an unknown instruction account key during compilation");return r};return e.map(e=>({programIdIndex:r(e.programId),accountKeyIndexes:e.keys.map(e=>r(e.pubkey)),data:e.data}))}}let M=(e="publicKey")=>g.av(32,e),U=(e="signature")=>g.av(64,e),V=(e="string")=>{let t=g.w3([g.DH("length"),g.DH("lengthPadding"),g.av(g.cY(g.DH(),-8),"chars")],e),r=t.decode.bind(t),i=t.encode.bind(t);return t.decode=(e,t)=>r(e,t).chars.toString(),t.encode=(e,t,r)=>i({chars:n.Buffer.from(e,"utf8")},t,r),t.alloc=e=>g.DH().span+g.DH().span+n.Buffer.from(e,"utf8").length,t};function $(e){let t=0,r=0;for(;;){let i=e.shift();if(t|=(127&i)<<7*r,r+=1,(128&i)==0)break}return t}function F(e,t){let r=t;for(;;){let t=127&r;if(0==(r>>=7)){e.push(t);break}t|=128,e.push(t)}}function J(e,t){if(!e)throw Error(t||"Assertion failed")}class G{constructor(e,t){this.payer=void 0,this.keyMetaMap=void 0,this.payer=e,this.keyMetaMap=t}static compile(e,t){let r=new Map,i=e=>{let t=e.toBase58(),i=r.get(t);return void 0===i&&(i={isSigner:!1,isWritable:!1,isInvoked:!1},r.set(t,i)),i},s=i(t);for(let t of(s.isSigner=!0,s.isWritable=!0,e))for(let e of(i(t.programId).isInvoked=!0,t.keys)){let t=i(e.pubkey);t.isSigner||=e.isSigner,t.isWritable||=e.isWritable}return new G(t,r)}getMessageComponents(){let e=[...this.keyMetaMap.entries()];J(e.length<=256,"Max static account keys length exceeded");let t=e.filter(([,e])=>e.isSigner&&e.isWritable),r=e.filter(([,e])=>e.isSigner&&!e.isWritable),i=e.filter(([,e])=>!e.isSigner&&e.isWritable),s=e.filter(([,e])=>!e.isSigner&&!e.isWritable),n={numRequiredSignatures:t.length+r.length,numReadonlySignedAccounts:r.length,numReadonlyUnsignedAccounts:s.length};{J(t.length>0,"Expected at least one writable signer key");let[e]=t[0];J(e===this.payer.toBase58(),"Expected first writable signer key to be the fee payer")}return[n,[...t.map(([e])=>new C(e)),...r.map(([e])=>new C(e)),...i.map(([e])=>new C(e)),...s.map(([e])=>new C(e))]]}extractTableLookup(e){let[t,r]=this.drainKeysFoundInLookupTable(e.state.addresses,e=>!e.isSigner&&!e.isInvoked&&e.isWritable),[i,s]=this.drainKeysFoundInLookupTable(e.state.addresses,e=>!e.isSigner&&!e.isInvoked&&!e.isWritable);if(0!==t.length||0!==i.length)return[{accountKey:e.key,writableIndexes:t,readonlyIndexes:i},{writable:r,readonly:s}]}drainKeysFoundInLookupTable(e,t){let r=[],i=[];for(let[s,n]of this.keyMetaMap.entries())if(t(n)){let t=new C(s),n=e.findIndex(e=>e.equals(t));n>=0&&(J(n<256,"Max lookup table index exceeded"),r.push(n),i.push(t),this.keyMetaMap.delete(s))}return[r,i]}}let X="Reached end of buffer unexpectedly";function Z(e){if(0===e.length)throw Error(X);return e.shift()}function Q(e,...t){let[r]=t;if(2===t.length?r+(t[1]??0)>e.length:r>=e.length)throw Error(X);return e.splice(...t)}class ee{constructor(e){this.header=void 0,this.accountKeys=void 0,this.recentBlockhash=void 0,this.instructions=void 0,this.indexToProgramIds=new Map,this.header=e.header,this.accountKeys=e.accountKeys.map(e=>new C(e)),this.recentBlockhash=e.recentBlockhash,this.instructions=e.instructions,this.instructions.forEach(e=>this.indexToProgramIds.set(e.programIdIndex,this.accountKeys[e.programIdIndex]))}get version(){return"legacy"}get staticAccountKeys(){return this.accountKeys}get compiledInstructions(){return this.instructions.map(e=>({programIdIndex:e.programIdIndex,accountKeyIndexes:e.accounts,data:l().decode(e.data)}))}get addressTableLookups(){return[]}getAccountKeys(){return new j(this.staticAccountKeys)}static compile(e){let[t,r]=G.compile(e.instructions,e.payerKey).getMessageComponents(),i=new j(r).compileInstructions(e.instructions).map(e=>({programIdIndex:e.programIdIndex,accounts:e.accountKeyIndexes,data:l().encode(e.data)}));return new ee({header:t,accountKeys:r,recentBlockhash:e.recentBlockhash,instructions:i})}isAccountSigner(e){return e<this.header.numRequiredSignatures}isAccountWritable(e){let t=this.header.numRequiredSignatures;if(!(e>=this.header.numRequiredSignatures))return e<t-this.header.numReadonlySignedAccounts;{let r=this.accountKeys.length-t-this.header.numReadonlyUnsignedAccounts;return e-t<r}}isProgramId(e){return this.indexToProgramIds.has(e)}programIds(){return[...this.indexToProgramIds.values()]}nonProgramIds(){return this.accountKeys.filter((e,t)=>!this.isProgramId(t))}serialize(){let e=this.accountKeys.length,t=[];F(t,e);let r=this.instructions.map(e=>{let{accounts:t,programIdIndex:r}=e,i=Array.from(l().decode(e.data)),s=[];F(s,t.length);let a=[];return F(a,i.length),{programIdIndex:r,keyIndicesCount:n.Buffer.from(s),keyIndices:t,dataLength:n.Buffer.from(a),data:i}}),i=[];F(i,r.length);let s=n.Buffer.alloc(K);n.Buffer.from(i).copy(s);let a=i.length;r.forEach(e=>{let t=g.w3([g.u8("programIdIndex"),g.av(e.keyIndicesCount.length,"keyIndicesCount"),g.O6(g.u8("keyIndex"),e.keyIndices.length,"keyIndices"),g.av(e.dataLength.length,"dataLength"),g.O6(g.u8("userdatum"),e.data.length,"data")]).encode(e,s,a);a+=t}),s=s.slice(0,a);let o=g.w3([g.av(1,"numRequiredSignatures"),g.av(1,"numReadonlySignedAccounts"),g.av(1,"numReadonlyUnsignedAccounts"),g.av(t.length,"keyCount"),g.O6(M("key"),e,"keys"),M("recentBlockhash")]),c={numRequiredSignatures:n.Buffer.from([this.header.numRequiredSignatures]),numReadonlySignedAccounts:n.Buffer.from([this.header.numReadonlySignedAccounts]),numReadonlyUnsignedAccounts:n.Buffer.from([this.header.numReadonlyUnsignedAccounts]),keyCount:n.Buffer.from(t),keys:this.accountKeys.map(e=>E(e.toBytes())),recentBlockhash:l().decode(this.recentBlockhash)},u=n.Buffer.alloc(2048),d=o.encode(c,u);return s.copy(u,d),u.slice(0,d+s.length)}static from(e){let t=[...e],r=Z(t);if(r!==(r&z))throw Error("Versioned messages must be deserialized with VersionedMessage.deserialize()");let i=Z(t),s=Z(t),a=$(t),o=[];for(let e=0;e<a;e++){let e=Q(t,0,O);o.push(new C(n.Buffer.from(e)))}let c=Q(t,0,O),u=$(t),d=[];for(let e=0;e<u;e++){let e=Z(t),r=$(t),i=Q(t,0,r),s=$(t),a=Q(t,0,s),o=l().encode(n.Buffer.from(a));d.push({programIdIndex:e,accounts:i,data:o})}return new ee({header:{numRequiredSignatures:r,numReadonlySignedAccounts:i,numReadonlyUnsignedAccounts:s},recentBlockhash:l().encode(n.Buffer.from(c)),accountKeys:o,instructions:d})}}class et{constructor(e){this.header=void 0,this.staticAccountKeys=void 0,this.recentBlockhash=void 0,this.compiledInstructions=void 0,this.addressTableLookups=void 0,this.header=e.header,this.staticAccountKeys=e.staticAccountKeys,this.recentBlockhash=e.recentBlockhash,this.compiledInstructions=e.compiledInstructions,this.addressTableLookups=e.addressTableLookups}get version(){return 0}get numAccountKeysFromLookups(){let e=0;for(let t of this.addressTableLookups)e+=t.readonlyIndexes.length+t.writableIndexes.length;return e}getAccountKeys(e){let t;if(e&&"accountKeysFromLookups"in e&&e.accountKeysFromLookups){if(this.numAccountKeysFromLookups!=e.accountKeysFromLookups.writable.length+e.accountKeysFromLookups.readonly.length)throw Error("Failed to get account keys because of a mismatch in the number of account keys from lookups");t=e.accountKeysFromLookups}else if(e&&"addressLookupTableAccounts"in e&&e.addressLookupTableAccounts)t=this.resolveAddressTableLookups(e.addressLookupTableAccounts);else if(this.addressTableLookups.length>0)throw Error("Failed to get account keys because address table lookups were not resolved");return new j(this.staticAccountKeys,t)}isAccountSigner(e){return e<this.header.numRequiredSignatures}isAccountWritable(e){let t=this.header.numRequiredSignatures,r=this.staticAccountKeys.length;if(e>=r)return e-r<this.addressTableLookups.reduce((e,t)=>e+t.writableIndexes.length,0);if(!(e>=this.header.numRequiredSignatures))return e<t-this.header.numReadonlySignedAccounts;{let i=r-t-this.header.numReadonlyUnsignedAccounts;return e-t<i}}resolveAddressTableLookups(e){let t={writable:[],readonly:[]};for(let r of this.addressTableLookups){let i=e.find(e=>e.key.equals(r.accountKey));if(!i)throw Error(`Failed to find address lookup table account for table key ${r.accountKey.toBase58()}`);for(let e of r.writableIndexes)if(e<i.state.addresses.length)t.writable.push(i.state.addresses[e]);else throw Error(`Failed to find address for index ${e} in address lookup table ${r.accountKey.toBase58()}`);for(let e of r.readonlyIndexes)if(e<i.state.addresses.length)t.readonly.push(i.state.addresses[e]);else throw Error(`Failed to find address for index ${e} in address lookup table ${r.accountKey.toBase58()}`)}return t}static compile(e){let t=G.compile(e.instructions,e.payerKey),r=[],i={writable:[],readonly:[]};for(let s of e.addressLookupTableAccounts||[]){let e=t.extractTableLookup(s);if(void 0!==e){let[t,{writable:s,readonly:n}]=e;r.push(t),i.writable.push(...s),i.readonly.push(...n)}}let[s,n]=t.getMessageComponents(),a=new j(n,i).compileInstructions(e.instructions);return new et({header:s,staticAccountKeys:n,recentBlockhash:e.recentBlockhash,compiledInstructions:a,addressTableLookups:r})}serialize(){let e=[];F(e,this.staticAccountKeys.length);let t=this.serializeInstructions(),r=[];F(r,this.compiledInstructions.length);let i=this.serializeAddressTableLookups(),s=[];F(s,this.addressTableLookups.length);let n=g.w3([g.u8("prefix"),g.w3([g.u8("numRequiredSignatures"),g.u8("numReadonlySignedAccounts"),g.u8("numReadonlyUnsignedAccounts")],"header"),g.av(e.length,"staticAccountKeysLength"),g.O6(M(),this.staticAccountKeys.length,"staticAccountKeys"),M("recentBlockhash"),g.av(r.length,"instructionsLength"),g.av(t.length,"serializedInstructions"),g.av(s.length,"addressTableLookupsLength"),g.av(i.length,"serializedAddressTableLookups")]),a=new Uint8Array(K),o=n.encode({prefix:128,header:this.header,staticAccountKeysLength:new Uint8Array(e),staticAccountKeys:this.staticAccountKeys.map(e=>e.toBytes()),recentBlockhash:l().decode(this.recentBlockhash),instructionsLength:new Uint8Array(r),serializedInstructions:t,addressTableLookupsLength:new Uint8Array(s),serializedAddressTableLookups:i},a);return a.slice(0,o)}serializeInstructions(){let e=0,t=new Uint8Array(K);for(let r of this.compiledInstructions){let i=[];F(i,r.accountKeyIndexes.length);let s=[];F(s,r.data.length);let n=g.w3([g.u8("programIdIndex"),g.av(i.length,"encodedAccountKeyIndexesLength"),g.O6(g.u8(),r.accountKeyIndexes.length,"accountKeyIndexes"),g.av(s.length,"encodedDataLength"),g.av(r.data.length,"data")]);e+=n.encode({programIdIndex:r.programIdIndex,encodedAccountKeyIndexesLength:new Uint8Array(i),accountKeyIndexes:r.accountKeyIndexes,encodedDataLength:new Uint8Array(s),data:r.data},t,e)}return t.slice(0,e)}serializeAddressTableLookups(){let e=0,t=new Uint8Array(K);for(let r of this.addressTableLookups){let i=[];F(i,r.writableIndexes.length);let s=[];F(s,r.readonlyIndexes.length);let n=g.w3([M("accountKey"),g.av(i.length,"encodedWritableIndexesLength"),g.O6(g.u8(),r.writableIndexes.length,"writableIndexes"),g.av(s.length,"encodedReadonlyIndexesLength"),g.O6(g.u8(),r.readonlyIndexes.length,"readonlyIndexes")]);e+=n.encode({accountKey:r.accountKey.toBytes(),encodedWritableIndexesLength:new Uint8Array(i),writableIndexes:r.writableIndexes,encodedReadonlyIndexesLength:new Uint8Array(s),readonlyIndexes:r.readonlyIndexes},t,e)}return t.slice(0,e)}static deserialize(e){let t=[...e],r=Z(t),i=r&z;J(r!==i,"Expected versioned message but received legacy message"),J(0===i,`Expected versioned message with version 0 but found version ${i}`);let s={numRequiredSignatures:Z(t),numReadonlySignedAccounts:Z(t),numReadonlyUnsignedAccounts:Z(t)},n=[],a=$(t);for(let e=0;e<a;e++)n.push(new C(Q(t,0,O)));let o=l().encode(Q(t,0,O)),c=$(t),u=[];for(let e=0;e<c;e++){let e=Z(t),r=$(t),i=Q(t,0,r),s=$(t),n=new Uint8Array(Q(t,0,s));u.push({programIdIndex:e,accountKeyIndexes:i,data:n})}let d=$(t),h=[];for(let e=0;e<d;e++){let e=new C(Q(t,0,O)),r=$(t),i=Q(t,0,r),s=$(t),n=Q(t,0,s);h.push({accountKey:e,writableIndexes:i,readonlyIndexes:n})}return new et({header:s,staticAccountKeys:n,recentBlockhash:o,compiledInstructions:u,addressTableLookups:h})}}let er={deserializeMessageVersion(e){let t=e[0],r=t&z;return r===t?"legacy":r},deserialize:e=>{let t=er.deserializeMessageVersion(e);if("legacy"===t)return ee.from(e);if(0===t)return et.deserialize(e);throw Error(`Transaction message version ${t} deserialization is not supported`)}},ei=function(e){return e[e.BLOCKHEIGHT_EXCEEDED=0]="BLOCKHEIGHT_EXCEEDED",e[e.PROCESSED=1]="PROCESSED",e[e.TIMED_OUT=2]="TIMED_OUT",e[e.NONCE_INVALID=3]="NONCE_INVALID",e}({}),es=n.Buffer.alloc(Y).fill(0);class en{constructor(e){this.keys=void 0,this.programId=void 0,this.data=n.Buffer.alloc(0),this.programId=e.programId,this.keys=e.keys,e.data&&(this.data=e.data)}toJSON(){return{keys:this.keys.map(({pubkey:e,isSigner:t,isWritable:r})=>({pubkey:e.toJSON(),isSigner:t,isWritable:r})),programId:this.programId.toJSON(),data:[...this.data]}}}class ea{get signature(){return this.signatures.length>0?this.signatures[0].signature:null}constructor(e){if(this.signatures=[],this.feePayer=void 0,this.instructions=[],this.recentBlockhash=void 0,this.lastValidBlockHeight=void 0,this.nonceInfo=void 0,this.minNonceContextSlot=void 0,this._message=void 0,this._json=void 0,!e)return;if(e.feePayer&&(this.feePayer=e.feePayer),e.signatures&&(this.signatures=e.signatures),Object.prototype.hasOwnProperty.call(e,"nonceInfo")){let{minContextSlot:t,nonceInfo:r}=e;this.minNonceContextSlot=t,this.nonceInfo=r}else if(Object.prototype.hasOwnProperty.call(e,"lastValidBlockHeight")){let{blockhash:t,lastValidBlockHeight:r}=e;this.recentBlockhash=t,this.lastValidBlockHeight=r}else{let{recentBlockhash:t,nonceInfo:r}=e;r&&(this.nonceInfo=r),this.recentBlockhash=t}}toJSON(){return{recentBlockhash:this.recentBlockhash||null,feePayer:this.feePayer?this.feePayer.toJSON():null,nonceInfo:this.nonceInfo?{nonce:this.nonceInfo.nonce,nonceInstruction:this.nonceInfo.nonceInstruction.toJSON()}:null,instructions:this.instructions.map(e=>e.toJSON()),signers:this.signatures.map(({publicKey:e})=>e.toJSON())}}add(...e){if(0===e.length)throw Error("No instructions");return e.forEach(e=>{"instructions"in e?this.instructions=this.instructions.concat(e.instructions):"data"in e&&"programId"in e&&"keys"in e?this.instructions.push(e):this.instructions.push(new en(e))}),this}compileMessage(){let e,t,r;if(this._message&&JSON.stringify(this.toJSON())===JSON.stringify(this._json))return this._message;if(this.nonceInfo?(e=this.nonceInfo.nonce,t=this.instructions[0]!=this.nonceInfo.nonceInstruction?[this.nonceInfo.nonceInstruction,...this.instructions]:this.instructions):(e=this.recentBlockhash,t=this.instructions),!e)throw Error("Transaction recentBlockhash required");if(t.length<1&&console.warn("No instructions provided"),this.feePayer)r=this.feePayer;else if(this.signatures.length>0&&this.signatures[0].publicKey)r=this.signatures[0].publicKey;else throw Error("Transaction fee payer required");for(let e=0;e<t.length;e++)if(void 0===t[e].programId)throw Error(`Transaction instruction index ${e} has undefined program id`);let i=[],s=[];t.forEach(e=>{e.keys.forEach(e=>{s.push({...e})});let t=e.programId.toString();i.includes(t)||i.push(t)}),i.forEach(e=>{s.push({pubkey:new C(e),isSigner:!1,isWritable:!1})});let n=[];s.forEach(e=>{let t=e.pubkey.toString(),r=n.findIndex(e=>e.pubkey.toString()===t);r>-1?(n[r].isWritable=n[r].isWritable||e.isWritable,n[r].isSigner=n[r].isSigner||e.isSigner):n.push(e)}),n.sort(function(e,t){return e.isSigner!==t.isSigner?e.isSigner?-1:1:e.isWritable!==t.isWritable?e.isWritable?-1:1:e.pubkey.toBase58().localeCompare(t.pubkey.toBase58(),"en",{localeMatcher:"best fit",usage:"sort",sensitivity:"variant",ignorePunctuation:!1,numeric:!1,caseFirst:"lower"})});let a=n.findIndex(e=>e.pubkey.equals(r));if(a>-1){let[e]=n.splice(a,1);e.isSigner=!0,e.isWritable=!0,n.unshift(e)}else n.unshift({pubkey:r,isSigner:!0,isWritable:!0});for(let e of this.signatures){let t=n.findIndex(t=>t.pubkey.equals(e.publicKey));if(t>-1)n[t].isSigner||(n[t].isSigner=!0,console.warn("Transaction references a signature that is unnecessary, only the fee payer and instruction signer accounts should sign a transaction. This behavior is deprecated and will throw an error in the next major version release."));else throw Error(`unknown signer: ${e.publicKey.toString()}`)}let o=0,c=0,u=0,d=[],h=[];n.forEach(({pubkey:e,isSigner:t,isWritable:r})=>{t?(d.push(e.toString()),o+=1,r||(c+=1)):(h.push(e.toString()),r||(u+=1))});let g=d.concat(h),p=t.map(e=>{let{data:t,programId:r}=e;return{programIdIndex:g.indexOf(r.toString()),accounts:e.keys.map(e=>g.indexOf(e.pubkey.toString())),data:l().encode(t)}});return p.forEach(e=>{J(e.programIdIndex>=0),e.accounts.forEach(e=>J(e>=0))}),new ee({header:{numRequiredSignatures:o,numReadonlySignedAccounts:c,numReadonlyUnsignedAccounts:u},accountKeys:g,recentBlockhash:e,instructions:p})}_compile(){let e=this.compileMessage(),t=e.accountKeys.slice(0,e.header.numRequiredSignatures);return this.signatures.length===t.length&&this.signatures.every((e,r)=>t[r].equals(e.publicKey))?e:(this.signatures=t.map(e=>({signature:null,publicKey:e})),e)}serializeMessage(){return this._compile().serialize()}async getEstimatedFee(e){return(await e.getFeeForMessage(this.compileMessage())).value}setSigners(...e){if(0===e.length)throw Error("No signers");let t=new Set;this.signatures=e.filter(e=>{let r=e.toString();return!t.has(r)&&(t.add(r),!0)}).map(e=>({signature:null,publicKey:e}))}sign(...e){if(0===e.length)throw Error("No signers");let t=new Set,r=[];for(let i of e){let e=i.publicKey.toString();t.has(e)||(t.add(e),r.push(i))}this.signatures=r.map(e=>({signature:null,publicKey:e.publicKey}));let i=this._compile();this._partialSign(i,...r)}partialSign(...e){if(0===e.length)throw Error("No signers");let t=new Set,r=[];for(let i of e){let e=i.publicKey.toString();t.has(e)||(t.add(e),r.push(i))}let i=this._compile();this._partialSign(i,...r)}_partialSign(e,...t){let r=e.serialize();t.forEach(e=>{let t=A(r,e.secretKey);this._addSignature(e.publicKey,E(t))})}addSignature(e,t){this._compile(),this._addSignature(e,t)}_addSignature(e,t){J(64===t.length);let r=this.signatures.findIndex(t=>e.equals(t.publicKey));if(r<0)throw Error(`unknown signer: ${e.toString()}`);this.signatures[r].signature=n.Buffer.from(t)}verifySignatures(e=!0){return!this._getMessageSignednessErrors(this.serializeMessage(),e)}_getMessageSignednessErrors(e,t){let r={};for(let{signature:i,publicKey:s}of this.signatures)null===i?t&&(r.missing||=[]).push(s):P(i,e,s.toBytes())||(r.invalid||=[]).push(s);return r.invalid||r.missing?r:void 0}serialize(e){let{requireAllSignatures:t,verifySignatures:r}=Object.assign({requireAllSignatures:!0,verifySignatures:!0},e),i=this.serializeMessage();if(r){let e=this._getMessageSignednessErrors(i,t);if(e){let t="Signature verification failed.";throw e.invalid&&(t+=`
Invalid signature for public key${1===e.invalid.length?"":"(s)"} [\`${e.invalid.map(e=>e.toBase58()).join("`, `")}\`].`),e.missing&&(t+=`
Missing signature for public key${1===e.missing.length?"":"(s)"} [\`${e.missing.map(e=>e.toBase58()).join("`, `")}\`].`),Error(t)}}return this._serialize(i)}_serialize(e){let{signatures:t}=this,r=[];F(r,t.length);let i=r.length+64*t.length+e.length,s=n.Buffer.alloc(i);return J(t.length<256),n.Buffer.from(r).copy(s,0),t.forEach(({signature:e},t)=>{null!==e&&(J(64===e.length,"signature has invalid length"),n.Buffer.from(e).copy(s,r.length+64*t))}),e.copy(s,r.length+64*t.length),J(s.length<=K,`Transaction too large: ${s.length} > ${K}`),s}get keys(){return J(1===this.instructions.length),this.instructions[0].keys.map(e=>e.pubkey)}get programId(){return J(1===this.instructions.length),this.instructions[0].programId}get data(){return J(1===this.instructions.length),this.instructions[0].data}static from(e){let t=[...e],r=$(t),i=[];for(let e=0;e<r;e++){let e=Q(t,0,Y);i.push(l().encode(n.Buffer.from(e)))}return ea.populate(ee.from(t),i)}static populate(e,t=[]){let r=new ea;return r.recentBlockhash=e.recentBlockhash,e.header.numRequiredSignatures>0&&(r.feePayer=e.accountKeys[0]),t.forEach((t,i)=>{let s={signature:t==l().encode(es)?null:l().decode(t),publicKey:e.accountKeys[i]};r.signatures.push(s)}),e.instructions.forEach(t=>{let i=t.accounts.map(t=>{let i=e.accountKeys[t];return{pubkey:i,isSigner:r.signatures.some(e=>e.publicKey.toString()===i.toString())||e.isAccountSigner(t),isWritable:e.isAccountWritable(t)}});r.instructions.push(new en({keys:i,programId:e.accountKeys[t.programIdIndex],data:l().decode(t.data)}))}),r._message=e,r._json=r.toJSON(),r}}class eo{constructor(e){this.payerKey=void 0,this.instructions=void 0,this.recentBlockhash=void 0,this.payerKey=e.payerKey,this.instructions=e.instructions,this.recentBlockhash=e.recentBlockhash}static decompile(e,t){let{header:r,compiledInstructions:i,recentBlockhash:s}=e,{numRequiredSignatures:n,numReadonlySignedAccounts:a,numReadonlyUnsignedAccounts:o}=r,c=n-a;J(c>0,"Message header is invalid");let u=e.staticAccountKeys.length-n-o;J(u>=0,"Message header is invalid");let l=e.getAccountKeys(t),d=l.get(0);if(void 0===d)throw Error("Failed to decompile message because no account keys were found");let h=[];for(let e of i){let t=[];for(let i of e.accountKeyIndexes){let e,s=l.get(i);if(void 0===s)throw Error(`Failed to find key for account key index ${i}`);e=i<n?i<c:i<l.staticAccountKeys.length?i-n<u:i-l.staticAccountKeys.length<l.accountKeysFromLookups.writable.length,t.push({pubkey:s,isSigner:i<r.numRequiredSignatures,isWritable:e})}let i=l.get(e.programIdIndex);if(void 0===i)throw Error(`Failed to find program id for program id index ${e.programIdIndex}`);h.push(new en({programId:i,data:E(e.data),keys:t}))}return new eo({payerKey:d,instructions:h,recentBlockhash:s})}compileToLegacyMessage(){return ee.compile({payerKey:this.payerKey,recentBlockhash:this.recentBlockhash,instructions:this.instructions})}compileToV0Message(e){return et.compile({payerKey:this.payerKey,recentBlockhash:this.recentBlockhash,instructions:this.instructions,addressLookupTableAccounts:e})}}class ec{get version(){return this.message.version}constructor(e,t){if(this.signatures=void 0,this.message=void 0,void 0!==t)J(t.length===e.header.numRequiredSignatures,"Expected signatures length to be equal to the number of required signatures"),this.signatures=t;else{let t=[];for(let r=0;r<e.header.numRequiredSignatures;r++)t.push(new Uint8Array(Y));this.signatures=t}this.message=e}serialize(){let e=this.message.serialize(),t=[];F(t,this.signatures.length);let r=g.w3([g.av(t.length,"encodedSignaturesLength"),g.O6(U(),this.signatures.length,"signatures"),g.av(e.length,"serializedMessage")]),i=new Uint8Array(2048),s=r.encode({encodedSignaturesLength:new Uint8Array(t),signatures:this.signatures,serializedMessage:e},i);return i.slice(0,s)}static deserialize(e){let t=[...e],r=[],i=$(t);for(let e=0;e<i;e++)r.push(new Uint8Array(Q(t,0,Y)));return new ec(er.deserialize(new Uint8Array(t)),r)}sign(e){let t=this.message.serialize(),r=this.message.staticAccountKeys.slice(0,this.message.header.numRequiredSignatures);for(let i of e){let e=r.findIndex(e=>e.equals(i.publicKey));J(e>=0,`Cannot sign with non signer key ${i.publicKey.toBase58()}`),this.signatures[e]=A(t,i.secretKey)}}addSignature(e,t){J(64===t.byteLength,"Signature must be 64 bytes long");let r=this.message.staticAccountKeys.slice(0,this.message.header.numRequiredSignatures).findIndex(t=>t.equals(e));J(r>=0,`Can not add signature; \`${e.toBase58()}\` is not required to sign this transaction`),this.signatures[r]=t}}let eu=new C("SysvarC1ock11111111111111111111111111111111"),el=new C("SysvarEpochSchedu1e111111111111111111111111"),ed=new C("Sysvar1nstructions1111111111111111111111111"),eh=new C("SysvarRecentB1ockHashes11111111111111111111"),eg=new C("SysvarRent111111111111111111111111111111111"),ep=new C("SysvarRewards111111111111111111111111111111"),em=new C("SysvarS1otHashes111111111111111111111111111"),ey=new C("SysvarS1otHistory11111111111111111111111111"),eb=new C("SysvarStakeHistory1111111111111111111111111");class ef extends Error{constructor({action:e,signature:t,transactionMessage:r,logs:i}){let s,n=i?`Logs: 
${JSON.stringify(i.slice(-10),null,2)}. `:"",a="\nCatch the `SendTransactionError` and call `getLogs()` on it for full details.";switch(e){case"send":s=`Transaction ${t} resulted in an error. 
${r}. `+n+a;break;case"simulate":s=`Simulation failed. 
Message: ${r}. 
`+n+a;break;default:s=`Unknown action '${e}'`}super(s),this.signature=void 0,this.transactionMessage=void 0,this.transactionLogs=void 0,this.signature=t,this.transactionMessage=r,this.transactionLogs=i||void 0}get transactionError(){return{message:this.transactionMessage,logs:Array.isArray(this.transactionLogs)?this.transactionLogs:void 0}}get logs(){let e=this.transactionLogs;if(null==e||"object"!=typeof e||!("then"in e))return e}async getLogs(e){return Array.isArray(this.transactionLogs)||(this.transactionLogs=new Promise((t,r)=>{e.getTransaction(this.signature).then(e=>{if(e&&e.meta&&e.meta.logMessages){let r=e.meta.logMessages;this.transactionLogs=r,t(r)}else r(Error("Log messages not found"))}).catch(r)})),await this.transactionLogs}}let ek={JSON_RPC_SERVER_ERROR_BLOCK_CLEANED_UP:-32001,JSON_RPC_SERVER_ERROR_SEND_TRANSACTION_PREFLIGHT_FAILURE:-32002,JSON_RPC_SERVER_ERROR_TRANSACTION_SIGNATURE_VERIFICATION_FAILURE:-32003,JSON_RPC_SERVER_ERROR_BLOCK_NOT_AVAILABLE:-32004,JSON_RPC_SERVER_ERROR_NODE_UNHEALTHY:-32005,JSON_RPC_SERVER_ERROR_TRANSACTION_PRECOMPILE_VERIFICATION_FAILURE:-32006,JSON_RPC_SERVER_ERROR_SLOT_SKIPPED:-32007,JSON_RPC_SERVER_ERROR_NO_SNAPSHOT:-32008,JSON_RPC_SERVER_ERROR_LONG_TERM_STORAGE_SLOT_SKIPPED:-32009,JSON_RPC_SERVER_ERROR_KEY_EXCLUDED_FROM_SECONDARY_INDEX:-32010,JSON_RPC_SERVER_ERROR_TRANSACTION_HISTORY_NOT_AVAILABLE:-32011,JSON_RPC_SCAN_ERROR:-32012,JSON_RPC_SERVER_ERROR_TRANSACTION_SIGNATURE_LEN_MISMATCH:-32013,JSON_RPC_SERVER_ERROR_BLOCK_STATUS_NOT_AVAILABLE_YET:-32014,JSON_RPC_SERVER_ERROR_UNSUPPORTED_TRANSACTION_VERSION:-32015,JSON_RPC_SERVER_ERROR_MIN_CONTEXT_SLOT_NOT_REACHED:-32016};class ew extends Error{constructor({code:e,message:t,data:r},i){super(null!=i?`${i}: ${t}`:t),this.code=void 0,this.data=void 0,this.code=e,this.data=r,this.name="SolanaJSONRPCError"}}async function eS(e,t,r,i){let s,n=i&&{skipPreflight:i.skipPreflight,preflightCommitment:i.preflightCommitment||i.commitment,maxRetries:i.maxRetries,minContextSlot:i.minContextSlot},a=await e.sendTransaction(t,r,n);if(null!=t.recentBlockhash&&null!=t.lastValidBlockHeight)s=(await e.confirmTransaction({abortSignal:i?.abortSignal,signature:a,blockhash:t.recentBlockhash,lastValidBlockHeight:t.lastValidBlockHeight},i&&i.commitment)).value;else if(null!=t.minNonceContextSlot&&null!=t.nonceInfo){let{nonceInstruction:r}=t.nonceInfo,n=r.keys[0].pubkey;s=(await e.confirmTransaction({abortSignal:i?.abortSignal,minContextSlot:t.minNonceContextSlot,nonceAccountPubkey:n,nonceValue:t.nonceInfo.nonce,signature:a},i&&i.commitment)).value}else i?.abortSignal!=null&&console.warn("sendAndConfirmTransaction(): A transaction with a deprecated confirmation strategy was supplied along with an `abortSignal`. Only transactions having `lastValidBlockHeight` or a combination of `nonceInfo` and `minNonceContextSlot` are abortable."),s=(await e.confirmTransaction(a,i&&i.commitment)).value;if(s.err){if(null!=a)throw new ef({action:"send",signature:a,transactionMessage:`Status: (${JSON.stringify(s)})`});throw Error(`Transaction ${a} failed (${JSON.stringify(s)})`)}return a}function eI(e){return new Promise(t=>setTimeout(t,e))}function e_(e,t){let r=e.layout.span>=0?e.layout.span:function e(t,r){let i=t=>{if(t.span>=0)return t.span;if("function"==typeof t.alloc)return t.alloc(r[t.property]);if("count"in t&&"elementLayout"in t){let e=r[t.property];if(Array.isArray(e))return e.length*i(t.elementLayout)}else if("fields"in t)return e({layout:t},r[t.property]);return 0},s=0;return t.layout.fields.forEach(e=>{s+=i(e)}),s}(e,t),i=n.Buffer.alloc(r),s=Object.assign({instruction:e.index},t);return e.layout.encode(s,i),i}function ev(e,t){let r;try{r=e.layout.decode(t)}catch(e){throw Error("invalid instruction; "+e)}if(r.instruction!==e.index)throw Error(`invalid instruction; instruction index mismatch ${r.instruction} != ${e.index}`);return r}let eA=g.I0("lamportsPerSignature"),eP=g.w3([g.DH("version"),g.DH("state"),M("authorizedPubkey"),M("nonce"),g.w3([eA],"feeCalculator")]),eE=eP.span;class eB{constructor(e){this.authorizedPubkey=void 0,this.nonce=void 0,this.feeCalculator=void 0,this.authorizedPubkey=e.authorizedPubkey,this.nonce=e.nonce,this.feeCalculator=e.feeCalculator}static fromAccountData(e){let t=eP.decode(E(e),0);return new eB({authorizedPubkey:new C(t.authorizedPubkey),nonce:new C(t.nonce).toString(),feeCalculator:t.feeCalculator})}}function eW(e){let t=(0,g.av)(8,e),r=t.decode.bind(t),i=t.encode.bind(t),s=(0,p.g2)();return t.decode=(e,t)=>{let i=r(e,t);return s.decode(i)},t.encode=(e,t,r)=>i(s.encode(e),t,r),t}class eT{constructor(){}static decodeInstructionType(e){let t;this.checkProgramId(e.programId);let r=g.DH("instruction").decode(e.data);for(let[e,i]of Object.entries(eN))if(i.index==r){t=e;break}if(!t)throw Error("Instruction type incorrect; not a SystemInstruction");return t}static decodeCreateAccount(e){this.checkProgramId(e.programId),this.checkKeyLength(e.keys,2);let{lamports:t,space:r,programId:i}=ev(eN.Create,e.data);return{fromPubkey:e.keys[0].pubkey,newAccountPubkey:e.keys[1].pubkey,lamports:t,space:r,programId:new C(i)}}static decodeTransfer(e){this.checkProgramId(e.programId),this.checkKeyLength(e.keys,2);let{lamports:t}=ev(eN.Transfer,e.data);return{fromPubkey:e.keys[0].pubkey,toPubkey:e.keys[1].pubkey,lamports:t}}static decodeTransferWithSeed(e){this.checkProgramId(e.programId),this.checkKeyLength(e.keys,3);let{lamports:t,seed:r,programId:i}=ev(eN.TransferWithSeed,e.data);return{fromPubkey:e.keys[0].pubkey,basePubkey:e.keys[1].pubkey,toPubkey:e.keys[2].pubkey,lamports:t,seed:r,programId:new C(i)}}static decodeAllocate(e){this.checkProgramId(e.programId),this.checkKeyLength(e.keys,1);let{space:t}=ev(eN.Allocate,e.data);return{accountPubkey:e.keys[0].pubkey,space:t}}static decodeAllocateWithSeed(e){this.checkProgramId(e.programId),this.checkKeyLength(e.keys,1);let{base:t,seed:r,space:i,programId:s}=ev(eN.AllocateWithSeed,e.data);return{accountPubkey:e.keys[0].pubkey,basePubkey:new C(t),seed:r,space:i,programId:new C(s)}}static decodeAssign(e){this.checkProgramId(e.programId),this.checkKeyLength(e.keys,1);let{programId:t}=ev(eN.Assign,e.data);return{accountPubkey:e.keys[0].pubkey,programId:new C(t)}}static decodeAssignWithSeed(e){this.checkProgramId(e.programId),this.checkKeyLength(e.keys,1);let{base:t,seed:r,programId:i}=ev(eN.AssignWithSeed,e.data);return{accountPubkey:e.keys[0].pubkey,basePubkey:new C(t),seed:r,programId:new C(i)}}static decodeCreateWithSeed(e){this.checkProgramId(e.programId),this.checkKeyLength(e.keys,2);let{base:t,seed:r,lamports:i,space:s,programId:n}=ev(eN.CreateWithSeed,e.data);return{fromPubkey:e.keys[0].pubkey,newAccountPubkey:e.keys[1].pubkey,basePubkey:new C(t),seed:r,lamports:i,space:s,programId:new C(n)}}static decodeNonceInitialize(e){this.checkProgramId(e.programId),this.checkKeyLength(e.keys,3);let{authorized:t}=ev(eN.InitializeNonceAccount,e.data);return{noncePubkey:e.keys[0].pubkey,authorizedPubkey:new C(t)}}static decodeNonceAdvance(e){return this.checkProgramId(e.programId),this.checkKeyLength(e.keys,3),ev(eN.AdvanceNonceAccount,e.data),{noncePubkey:e.keys[0].pubkey,authorizedPubkey:e.keys[2].pubkey}}static decodeNonceWithdraw(e){this.checkProgramId(e.programId),this.checkKeyLength(e.keys,5);let{lamports:t}=ev(eN.WithdrawNonceAccount,e.data);return{noncePubkey:e.keys[0].pubkey,toPubkey:e.keys[1].pubkey,authorizedPubkey:e.keys[4].pubkey,lamports:t}}static decodeNonceAuthorize(e){this.checkProgramId(e.programId),this.checkKeyLength(e.keys,2);let{authorized:t}=ev(eN.AuthorizeNonceAccount,e.data);return{noncePubkey:e.keys[0].pubkey,authorizedPubkey:e.keys[1].pubkey,newAuthorizedPubkey:new C(t)}}static checkProgramId(e){if(!e.equals(eO.programId))throw Error("invalid instruction; programId is not SystemProgram")}static checkKeyLength(e,t){if(e.length<t)throw Error(`invalid instruction; found ${e.length} keys, expected at least ${t}`)}}let eN=Object.freeze({Create:{index:0,layout:g.w3([g.DH("instruction"),g.Wg("lamports"),g.Wg("space"),M("programId")])},Assign:{index:1,layout:g.w3([g.DH("instruction"),M("programId")])},Transfer:{index:2,layout:g.w3([g.DH("instruction"),eW("lamports")])},CreateWithSeed:{index:3,layout:g.w3([g.DH("instruction"),M("base"),V("seed"),g.Wg("lamports"),g.Wg("space"),M("programId")])},AdvanceNonceAccount:{index:4,layout:g.w3([g.DH("instruction")])},WithdrawNonceAccount:{index:5,layout:g.w3([g.DH("instruction"),g.Wg("lamports")])},InitializeNonceAccount:{index:6,layout:g.w3([g.DH("instruction"),M("authorized")])},AuthorizeNonceAccount:{index:7,layout:g.w3([g.DH("instruction"),M("authorized")])},Allocate:{index:8,layout:g.w3([g.DH("instruction"),g.Wg("space")])},AllocateWithSeed:{index:9,layout:g.w3([g.DH("instruction"),M("base"),V("seed"),g.Wg("space"),M("programId")])},AssignWithSeed:{index:10,layout:g.w3([g.DH("instruction"),M("base"),V("seed"),M("programId")])},TransferWithSeed:{index:11,layout:g.w3([g.DH("instruction"),eW("lamports"),V("seed"),M("programId")])},UpgradeNonceAccount:{index:12,layout:g.w3([g.DH("instruction")])}});class eO{constructor(){}static createAccount(e){let t=e_(eN.Create,{lamports:e.lamports,space:e.space,programId:E(e.programId.toBuffer())});return new en({keys:[{pubkey:e.fromPubkey,isSigner:!0,isWritable:!0},{pubkey:e.newAccountPubkey,isSigner:!0,isWritable:!0}],programId:this.programId,data:t})}static transfer(e){let t,r;return"basePubkey"in e?(t=e_(eN.TransferWithSeed,{lamports:BigInt(e.lamports),seed:e.seed,programId:E(e.programId.toBuffer())}),r=[{pubkey:e.fromPubkey,isSigner:!1,isWritable:!0},{pubkey:e.basePubkey,isSigner:!0,isWritable:!1},{pubkey:e.toPubkey,isSigner:!1,isWritable:!0}]):(t=e_(eN.Transfer,{lamports:BigInt(e.lamports)}),r=[{pubkey:e.fromPubkey,isSigner:!0,isWritable:!0},{pubkey:e.toPubkey,isSigner:!1,isWritable:!0}]),new en({keys:r,programId:this.programId,data:t})}static assign(e){let t,r;return"basePubkey"in e?(t=e_(eN.AssignWithSeed,{base:E(e.basePubkey.toBuffer()),seed:e.seed,programId:E(e.programId.toBuffer())}),r=[{pubkey:e.accountPubkey,isSigner:!1,isWritable:!0},{pubkey:e.basePubkey,isSigner:!0,isWritable:!1}]):(t=e_(eN.Assign,{programId:E(e.programId.toBuffer())}),r=[{pubkey:e.accountPubkey,isSigner:!0,isWritable:!0}]),new en({keys:r,programId:this.programId,data:t})}static createAccountWithSeed(e){let t=e_(eN.CreateWithSeed,{base:E(e.basePubkey.toBuffer()),seed:e.seed,lamports:e.lamports,space:e.space,programId:E(e.programId.toBuffer())}),r=[{pubkey:e.fromPubkey,isSigner:!0,isWritable:!0},{pubkey:e.newAccountPubkey,isSigner:!1,isWritable:!0}];return e.basePubkey.equals(e.fromPubkey)||r.push({pubkey:e.basePubkey,isSigner:!0,isWritable:!1}),new en({keys:r,programId:this.programId,data:t})}static createNonceAccount(e){let t=new ea;"basePubkey"in e&&"seed"in e?t.add(eO.createAccountWithSeed({fromPubkey:e.fromPubkey,newAccountPubkey:e.noncePubkey,basePubkey:e.basePubkey,seed:e.seed,lamports:e.lamports,space:eE,programId:this.programId})):t.add(eO.createAccount({fromPubkey:e.fromPubkey,newAccountPubkey:e.noncePubkey,lamports:e.lamports,space:eE,programId:this.programId}));let r={noncePubkey:e.noncePubkey,authorizedPubkey:e.authorizedPubkey};return t.add(this.nonceInitialize(r)),t}static nonceInitialize(e){let t=e_(eN.InitializeNonceAccount,{authorized:E(e.authorizedPubkey.toBuffer())});return new en({keys:[{pubkey:e.noncePubkey,isSigner:!1,isWritable:!0},{pubkey:eh,isSigner:!1,isWritable:!1},{pubkey:eg,isSigner:!1,isWritable:!1}],programId:this.programId,data:t})}static nonceAdvance(e){let t=e_(eN.AdvanceNonceAccount);return new en({keys:[{pubkey:e.noncePubkey,isSigner:!1,isWritable:!0},{pubkey:eh,isSigner:!1,isWritable:!1},{pubkey:e.authorizedPubkey,isSigner:!0,isWritable:!1}],programId:this.programId,data:t})}static nonceWithdraw(e){let t=e_(eN.WithdrawNonceAccount,{lamports:e.lamports});return new en({keys:[{pubkey:e.noncePubkey,isSigner:!1,isWritable:!0},{pubkey:e.toPubkey,isSigner:!1,isWritable:!0},{pubkey:eh,isSigner:!1,isWritable:!1},{pubkey:eg,isSigner:!1,isWritable:!1},{pubkey:e.authorizedPubkey,isSigner:!0,isWritable:!1}],programId:this.programId,data:t})}static nonceAuthorize(e){let t=e_(eN.AuthorizeNonceAccount,{authorized:E(e.newAuthorizedPubkey.toBuffer())});return new en({keys:[{pubkey:e.noncePubkey,isSigner:!1,isWritable:!0},{pubkey:e.authorizedPubkey,isSigner:!0,isWritable:!1}],programId:this.programId,data:t})}static allocate(e){let t,r;return"basePubkey"in e?(t=e_(eN.AllocateWithSeed,{base:E(e.basePubkey.toBuffer()),seed:e.seed,space:e.space,programId:E(e.programId.toBuffer())}),r=[{pubkey:e.accountPubkey,isSigner:!1,isWritable:!0},{pubkey:e.basePubkey,isSigner:!0,isWritable:!1}]):(t=e_(eN.Allocate,{space:e.space}),r=[{pubkey:e.accountPubkey,isSigner:!0,isWritable:!0}]),new en({keys:r,programId:this.programId,data:t})}}eO.programId=new C("11111111111111111111111111111111");let eR=K-300;class eC{constructor(){}static getMinNumSignatures(e){return 2*(Math.ceil(e/eC.chunkSize)+1+1)}static async load(e,t,r,i,s){{let n=await e.getMinimumBalanceForRentExemption(s.length),a=await e.getAccountInfo(r.publicKey,"confirmed"),o=null;if(null!==a){if(a.executable)return console.error("Program load failed, account is already executable"),!1;a.data.length!==s.length&&(o=o||new ea).add(eO.allocate({accountPubkey:r.publicKey,space:s.length})),a.owner.equals(i)||(o=o||new ea).add(eO.assign({accountPubkey:r.publicKey,programId:i})),a.lamports<n&&(o=o||new ea).add(eO.transfer({fromPubkey:t.publicKey,toPubkey:r.publicKey,lamports:n-a.lamports}))}else o=new ea().add(eO.createAccount({fromPubkey:t.publicKey,newAccountPubkey:r.publicKey,lamports:n>0?n:1,space:s.length,programId:i}));null!==o&&await eS(e,o,[t,r],{commitment:"confirmed"})}let a=g.w3([g.DH("instruction"),g.DH("offset"),g.DH("bytesLength"),g.DH("bytesLengthPadding"),g.O6(g.u8("byte"),g.cY(g.DH(),-8),"bytes")]),o=eC.chunkSize,c=0,u=s,l=[];for(;u.length>0;){let s=u.slice(0,o),d=n.Buffer.alloc(o+16);a.encode({instruction:0,offset:c,bytes:s,bytesLength:0,bytesLengthPadding:0},d);let h=new ea().add({keys:[{pubkey:r.publicKey,isSigner:!0,isWritable:!0}],programId:i,data:d});l.push(eS(e,h,[t,r],{commitment:"confirmed"})),e._rpcEndpoint.includes("solana.com")&&await eI(250),c+=o,u=u.slice(o)}await Promise.all(l);{let s=g.w3([g.DH("instruction")]),a=n.Buffer.alloc(s.span);s.encode({instruction:1},a);let o=new ea().add({keys:[{pubkey:r.publicKey,isSigner:!0,isWritable:!0},{pubkey:eg,isSigner:!1,isWritable:!1}],programId:i,data:a}),c="processed",u=await e.sendTransaction(o,[t,r],{preflightCommitment:c}),{context:l,value:d}=await e.confirmTransaction({signature:u,lastValidBlockHeight:o.lastValidBlockHeight,blockhash:o.recentBlockhash},c);if(d.err)throw Error(`Transaction ${u} failed (${JSON.stringify(d)})`);for(;;){try{if(await e.getSlot({commitment:c})>l.slot)break}catch{}await new Promise(e=>setTimeout(e,Math.round(200)))}}return!0}}eC.chunkSize=eR;let ex=new C("BPFLoader2111111111111111111111111111111111");class eL{static getMinNumSignatures(e){return eC.getMinNumSignatures(e)}static load(e,t,r,i,s){return eC.load(e,t,r,s,i)}}var eK=function(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}(function(){if(s)return i;s=1;var e=Object.prototype.toString,t=Object.keys||function(e){var t=[];for(var r in e)t.push(r);return t};return i=function(r){var i=function r(i,s){var n,a,o,c,u,l,d;if(!0===i)return"true";if(!1===i)return"false";switch(typeof i){case"object":if(null===i)return null;if(i.toJSON&&"function"==typeof i.toJSON)return r(i.toJSON(),s);if("[object Array]"===(d=e.call(i))){for(n=0,o="[",a=i.length-1;n<a;n++)o+=r(i[n],!0)+",";return a>-1&&(o+=r(i[n],!0)),o+"]"}if("[object Object]"!==d)return JSON.stringify(i);for(a=(c=t(i).sort()).length,o="",n=0;n<a;)void 0!==(l=r(i[u=c[n]],!1))&&(o&&(o+=","),o+=JSON.stringify(u)+":"+l),n++;return"{"+o+"}";case"function":case"undefined":return s?null:void 0;case"string":return JSON.stringify(i);default:return isFinite(i)?i:null}}(r,!1);if(void 0!==i)return""+i}}());function ez(e){let t=0;for(;e>1;)e/=2,t++;return t}class eY{constructor(e,t,r,i,s){this.slotsPerEpoch=void 0,this.leaderScheduleSlotOffset=void 0,this.warmup=void 0,this.firstNormalEpoch=void 0,this.firstNormalSlot=void 0,this.slotsPerEpoch=e,this.leaderScheduleSlotOffset=t,this.warmup=r,this.firstNormalEpoch=i,this.firstNormalSlot=s}getEpoch(e){return this.getEpochAndSlotIndex(e)[0]}getEpochAndSlotIndex(e){if(e<this.firstNormalSlot){var t;let r=ez(0===(t=e+32+1)?1:(t--,t|=t>>1,t|=t>>2,t|=t>>4,t|=t>>8,t|=t>>16,(t|=t>>32)+1))-ez(32)-1,i=this.getSlotsInEpoch(r);return[r,e-(i-32)]}{let t=e-this.firstNormalSlot,r=Math.floor(t/this.slotsPerEpoch);return[this.firstNormalEpoch+r,t%this.slotsPerEpoch]}}getFirstSlotInEpoch(e){return e<=this.firstNormalEpoch?(Math.pow(2,e)-1)*32:(e-this.firstNormalEpoch)*this.slotsPerEpoch+this.firstNormalSlot}getLastSlotInEpoch(e){return this.getFirstSlotInEpoch(e)+this.getSlotsInEpoch(e)-1}getSlotsInEpoch(e){return e<this.firstNormalEpoch?Math.pow(2,e+ez(32)):this.slotsPerEpoch}}var eq=globalThis.fetch;class eD extends f.vE{constructor(e,t,r){super(e=>{let r=(0,f.kb)(e,{autoconnect:!0,max_reconnects:5,reconnect:!0,reconnect_interval:1e3,...t});return"socket"in r?this.underlyingSocket=r.socket:this.underlyingSocket=r,r},e,t,r),this.underlyingSocket=void 0}call(...e){let t=this.underlyingSocket?.readyState;return 1===t?super.call(...e):Promise.reject(Error("Tried to call a JSON-RPC method `"+e[0]+"` but the socket was not `CONNECTING` or `OPEN` (`readyState` was "+t+")"))}notify(...e){let t=this.underlyingSocket?.readyState;return 1===t?super.notify(...e):Promise.reject(Error("Tried to send a JSON-RPC notification `"+e[0]+"` but the socket was not `CONNECTING` or `OPEN` (`readyState` was "+t+")"))}}class eH{constructor(e){this.key=void 0,this.state=void 0,this.key=e.key,this.state=e.state}isActive(){let e=BigInt("0xffffffffffffffff");return this.state.deactivationSlot===e}static deserialize(e){let t=function(e,t){let r;try{r=e.layout.decode(t)}catch(e){throw Error("invalid instruction; "+e)}if(r.typeIndex!==e.index)throw Error(`invalid account data; account type mismatch ${r.typeIndex} != ${e.index}`);return r}(ej,e),r=e.length-56;J(r>=0,"lookup table is invalid"),J(r%32==0,"lookup table is invalid");let{addresses:i}=g.w3([g.O6(M(),r/32,"addresses")]).decode(e.slice(56));return{deactivationSlot:t.deactivationSlot,lastExtendedSlot:t.lastExtendedSlot,lastExtendedSlotStartIndex:t.lastExtendedStartIndex,authority:0!==t.authority.length?new C(t.authority[0]):void 0,addresses:i.map(e=>new C(e))}}}let ej={index:1,layout:g.w3([g.DH("typeIndex"),eW("deactivationSlot"),g.I0("lastExtendedSlot"),g.u8("lastExtendedStartIndex"),g.u8(),g.O6(M(),g.cY(g.u8(),-1),"authority")])},eM=/^[^:]+:\/\/([^:[]+|\[[^\]]+\])(:\d+)?(.*)/i,eU=(0,m.au)((0,m.KJ)(C),(0,m.Yj)(),e=>new C(e)),eV=(0,m.PV)([(0,m.Yj)(),(0,m.eu)("base64")]),e$=(0,m.au)((0,m.KJ)(n.Buffer),eV,e=>n.Buffer.from(e[0],"base64")),eF=3e4;function eJ(e){let t,r;if("string"==typeof e)t=e;else if(e){let{commitment:i,...s}=e;t=i,r=s}return{commitment:t,config:r}}function eG(e){return e.map(e=>"memcmp"in e?{...e,memcmp:{...e.memcmp,encoding:e.memcmp.encoding??"base58"}}:e)}function eX(e){return(0,m.KC)([(0,m.NW)({jsonrpc:(0,m.eu)("2.0"),id:(0,m.Yj)(),result:e}),(0,m.NW)({jsonrpc:(0,m.eu)("2.0"),id:(0,m.Yj)(),error:(0,m.NW)({code:(0,m.L5)(),message:(0,m.Yj)(),data:(0,m.lq)((0,m.bz)())})})])}let eZ=eX((0,m.L5)());function eQ(e){return(0,m.au)(eX(e),eZ,t=>"error"in t?t:{...t,result:(0,m.vt)(t.result,e)})}function e0(e){return eQ((0,m.NW)({context:(0,m.NW)({slot:(0,m.ai)()}),value:e}))}function e1(e){return(0,m.NW)({context:(0,m.NW)({slot:(0,m.ai)()}),value:e})}function e3(e,t){return 0===e?new et({header:t.header,staticAccountKeys:t.accountKeys.map(e=>new C(e)),recentBlockhash:t.recentBlockhash,compiledInstructions:t.instructions.map(e=>({programIdIndex:e.programIdIndex,accountKeyIndexes:e.accounts,data:l().decode(e.data)})),addressTableLookups:t.addressTableLookups}):new ee(t)}let e8=(0,m.NW)({foundation:(0,m.ai)(),foundationTerm:(0,m.ai)(),initial:(0,m.ai)(),taper:(0,m.ai)(),terminal:(0,m.ai)()}),e2=eQ((0,m.YO)((0,m.me)((0,m.NW)({epoch:(0,m.ai)(),effectiveSlot:(0,m.ai)(),amount:(0,m.ai)(),postBalance:(0,m.ai)(),commission:(0,m.lq)((0,m.me)((0,m.ai)()))})))),e5=(0,m.YO)((0,m.NW)({slot:(0,m.ai)(),prioritizationFee:(0,m.ai)()})),e6=(0,m.NW)({total:(0,m.ai)(),validator:(0,m.ai)(),foundation:(0,m.ai)(),epoch:(0,m.ai)()}),e4=(0,m.NW)({epoch:(0,m.ai)(),slotIndex:(0,m.ai)(),slotsInEpoch:(0,m.ai)(),absoluteSlot:(0,m.ai)(),blockHeight:(0,m.lq)((0,m.ai)()),transactionCount:(0,m.lq)((0,m.ai)())}),e7=(0,m.NW)({slotsPerEpoch:(0,m.ai)(),leaderScheduleSlotOffset:(0,m.ai)(),warmup:(0,m.zM)(),firstNormalEpoch:(0,m.ai)(),firstNormalSlot:(0,m.ai)()}),e9=(0,m.g1)((0,m.Yj)(),(0,m.YO)((0,m.ai)())),te=(0,m.me)((0,m.KC)([(0,m.NW)({}),(0,m.Yj)()])),tt=(0,m.NW)({err:te}),tr=(0,m.eu)("receivedSignature"),ti=(0,m.NW)({"solana-core":(0,m.Yj)(),"feature-set":(0,m.lq)((0,m.ai)())}),ts=(0,m.NW)({program:(0,m.Yj)(),programId:eU,parsed:(0,m.L5)()}),tn=(0,m.NW)({programId:eU,accounts:(0,m.YO)(eU),data:(0,m.Yj)()}),ta=e0((0,m.NW)({err:(0,m.me)((0,m.KC)([(0,m.NW)({}),(0,m.Yj)()])),logs:(0,m.me)((0,m.YO)((0,m.Yj)())),accounts:(0,m.lq)((0,m.me)((0,m.YO)((0,m.me)((0,m.NW)({executable:(0,m.zM)(),owner:(0,m.Yj)(),lamports:(0,m.ai)(),data:(0,m.YO)((0,m.Yj)()),rentEpoch:(0,m.lq)((0,m.ai)())}))))),unitsConsumed:(0,m.lq)((0,m.ai)()),returnData:(0,m.lq)((0,m.me)((0,m.NW)({programId:(0,m.Yj)(),data:(0,m.PV)([(0,m.Yj)(),(0,m.eu)("base64")])}))),innerInstructions:(0,m.lq)((0,m.me)((0,m.YO)((0,m.NW)({index:(0,m.ai)(),instructions:(0,m.YO)((0,m.KC)([ts,tn]))}))))})),to=e0((0,m.NW)({byIdentity:(0,m.g1)((0,m.Yj)(),(0,m.YO)((0,m.ai)())),range:(0,m.NW)({firstSlot:(0,m.ai)(),lastSlot:(0,m.ai)()})})),tc=eQ(e8),tu=eQ(e6),tl=eQ(e5),td=eQ(e4),th=eQ(e7),tg=eQ(e9),tp=eQ((0,m.ai)()),tm=e0((0,m.NW)({total:(0,m.ai)(),circulating:(0,m.ai)(),nonCirculating:(0,m.ai)(),nonCirculatingAccounts:(0,m.YO)(eU)})),ty=(0,m.NW)({amount:(0,m.Yj)(),uiAmount:(0,m.me)((0,m.ai)()),decimals:(0,m.ai)(),uiAmountString:(0,m.lq)((0,m.Yj)())}),tb=e0((0,m.YO)((0,m.NW)({address:eU,amount:(0,m.Yj)(),uiAmount:(0,m.me)((0,m.ai)()),decimals:(0,m.ai)(),uiAmountString:(0,m.lq)((0,m.Yj)())}))),tf=e0((0,m.YO)((0,m.NW)({pubkey:eU,account:(0,m.NW)({executable:(0,m.zM)(),owner:eU,lamports:(0,m.ai)(),data:e$,rentEpoch:(0,m.ai)()})}))),tk=(0,m.NW)({program:(0,m.Yj)(),parsed:(0,m.L5)(),space:(0,m.ai)()}),tw=e0((0,m.YO)((0,m.NW)({pubkey:eU,account:(0,m.NW)({executable:(0,m.zM)(),owner:eU,lamports:(0,m.ai)(),data:tk,rentEpoch:(0,m.ai)()})}))),tS=e0((0,m.YO)((0,m.NW)({lamports:(0,m.ai)(),address:eU}))),tI=(0,m.NW)({executable:(0,m.zM)(),owner:eU,lamports:(0,m.ai)(),data:e$,rentEpoch:(0,m.ai)()}),t_=(0,m.NW)({pubkey:eU,account:tI}),tv=(0,m.au)((0,m.KC)([(0,m.KJ)(n.Buffer),tk]),(0,m.KC)([eV,tk]),e=>Array.isArray(e)?(0,m.vt)(e,e$):e),tA=(0,m.NW)({executable:(0,m.zM)(),owner:eU,lamports:(0,m.ai)(),data:tv,rentEpoch:(0,m.ai)()}),tP=(0,m.NW)({pubkey:eU,account:tA}),tE=(0,m.NW)({state:(0,m.KC)([(0,m.eu)("active"),(0,m.eu)("inactive"),(0,m.eu)("activating"),(0,m.eu)("deactivating")]),active:(0,m.ai)(),inactive:(0,m.ai)()}),tB=eQ((0,m.YO)((0,m.NW)({signature:(0,m.Yj)(),slot:(0,m.ai)(),err:te,memo:(0,m.me)((0,m.Yj)()),blockTime:(0,m.lq)((0,m.me)((0,m.ai)()))}))),tW=eQ((0,m.YO)((0,m.NW)({signature:(0,m.Yj)(),slot:(0,m.ai)(),err:te,memo:(0,m.me)((0,m.Yj)()),blockTime:(0,m.lq)((0,m.me)((0,m.ai)()))}))),tT=(0,m.NW)({subscription:(0,m.ai)(),result:e1(tI)}),tN=(0,m.NW)({pubkey:eU,account:tI}),tO=(0,m.NW)({subscription:(0,m.ai)(),result:e1(tN)}),tR=(0,m.NW)({parent:(0,m.ai)(),slot:(0,m.ai)(),root:(0,m.ai)()}),tC=(0,m.NW)({subscription:(0,m.ai)(),result:tR}),tx=(0,m.KC)([(0,m.NW)({type:(0,m.KC)([(0,m.eu)("firstShredReceived"),(0,m.eu)("completed"),(0,m.eu)("optimisticConfirmation"),(0,m.eu)("root")]),slot:(0,m.ai)(),timestamp:(0,m.ai)()}),(0,m.NW)({type:(0,m.eu)("createdBank"),parent:(0,m.ai)(),slot:(0,m.ai)(),timestamp:(0,m.ai)()}),(0,m.NW)({type:(0,m.eu)("frozen"),slot:(0,m.ai)(),timestamp:(0,m.ai)(),stats:(0,m.NW)({numTransactionEntries:(0,m.ai)(),numSuccessfulTransactions:(0,m.ai)(),numFailedTransactions:(0,m.ai)(),maxTransactionsPerEntry:(0,m.ai)()})}),(0,m.NW)({type:(0,m.eu)("dead"),slot:(0,m.ai)(),timestamp:(0,m.ai)(),err:(0,m.Yj)()})]),tL=(0,m.NW)({subscription:(0,m.ai)(),result:tx}),tK=(0,m.NW)({subscription:(0,m.ai)(),result:e1((0,m.KC)([tt,tr]))}),tz=(0,m.NW)({subscription:(0,m.ai)(),result:(0,m.ai)()}),tY=(0,m.NW)({pubkey:(0,m.Yj)(),gossip:(0,m.me)((0,m.Yj)()),tpu:(0,m.me)((0,m.Yj)()),rpc:(0,m.me)((0,m.Yj)()),version:(0,m.me)((0,m.Yj)())}),tq=(0,m.NW)({votePubkey:(0,m.Yj)(),nodePubkey:(0,m.Yj)(),activatedStake:(0,m.ai)(),epochVoteAccount:(0,m.zM)(),epochCredits:(0,m.YO)((0,m.PV)([(0,m.ai)(),(0,m.ai)(),(0,m.ai)()])),commission:(0,m.ai)(),lastVote:(0,m.ai)(),rootSlot:(0,m.me)((0,m.ai)())}),tD=eQ((0,m.NW)({current:(0,m.YO)(tq),delinquent:(0,m.YO)(tq)})),tH=(0,m.KC)([(0,m.eu)("processed"),(0,m.eu)("confirmed"),(0,m.eu)("finalized")]),tj=(0,m.NW)({slot:(0,m.ai)(),confirmations:(0,m.me)((0,m.ai)()),err:te,confirmationStatus:(0,m.lq)(tH)}),tM=e0((0,m.YO)((0,m.me)(tj))),tU=eQ((0,m.ai)()),tV=(0,m.NW)({accountKey:eU,writableIndexes:(0,m.YO)((0,m.ai)()),readonlyIndexes:(0,m.YO)((0,m.ai)())}),t$=(0,m.NW)({signatures:(0,m.YO)((0,m.Yj)()),message:(0,m.NW)({accountKeys:(0,m.YO)((0,m.Yj)()),header:(0,m.NW)({numRequiredSignatures:(0,m.ai)(),numReadonlySignedAccounts:(0,m.ai)(),numReadonlyUnsignedAccounts:(0,m.ai)()}),instructions:(0,m.YO)((0,m.NW)({accounts:(0,m.YO)((0,m.ai)()),data:(0,m.Yj)(),programIdIndex:(0,m.ai)()})),recentBlockhash:(0,m.Yj)(),addressTableLookups:(0,m.lq)((0,m.YO)(tV))})}),tF=(0,m.NW)({pubkey:eU,signer:(0,m.zM)(),writable:(0,m.zM)(),source:(0,m.lq)((0,m.KC)([(0,m.eu)("transaction"),(0,m.eu)("lookupTable")]))}),tJ=(0,m.NW)({accountKeys:(0,m.YO)(tF),signatures:(0,m.YO)((0,m.Yj)())}),tG=(0,m.NW)({parsed:(0,m.L5)(),program:(0,m.Yj)(),programId:eU}),tX=(0,m.NW)({accounts:(0,m.YO)(eU),data:(0,m.Yj)(),programId:eU}),tZ=(0,m.KC)([tX,tG]),tQ=(0,m.KC)([(0,m.NW)({parsed:(0,m.L5)(),program:(0,m.Yj)(),programId:(0,m.Yj)()}),(0,m.NW)({accounts:(0,m.YO)((0,m.Yj)()),data:(0,m.Yj)(),programId:(0,m.Yj)()})]),t0=(0,m.au)(tZ,tQ,e=>"accounts"in e?(0,m.vt)(e,tX):(0,m.vt)(e,tG)),t1=(0,m.NW)({signatures:(0,m.YO)((0,m.Yj)()),message:(0,m.NW)({accountKeys:(0,m.YO)(tF),instructions:(0,m.YO)(t0),recentBlockhash:(0,m.Yj)(),addressTableLookups:(0,m.lq)((0,m.me)((0,m.YO)(tV)))})}),t3=(0,m.NW)({accountIndex:(0,m.ai)(),mint:(0,m.Yj)(),owner:(0,m.lq)((0,m.Yj)()),programId:(0,m.lq)((0,m.Yj)()),uiTokenAmount:ty}),t8=(0,m.NW)({writable:(0,m.YO)(eU),readonly:(0,m.YO)(eU)}),t2=(0,m.NW)({err:te,fee:(0,m.ai)(),innerInstructions:(0,m.lq)((0,m.me)((0,m.YO)((0,m.NW)({index:(0,m.ai)(),instructions:(0,m.YO)((0,m.NW)({accounts:(0,m.YO)((0,m.ai)()),data:(0,m.Yj)(),programIdIndex:(0,m.ai)()}))})))),preBalances:(0,m.YO)((0,m.ai)()),postBalances:(0,m.YO)((0,m.ai)()),logMessages:(0,m.lq)((0,m.me)((0,m.YO)((0,m.Yj)()))),preTokenBalances:(0,m.lq)((0,m.me)((0,m.YO)(t3))),postTokenBalances:(0,m.lq)((0,m.me)((0,m.YO)(t3))),loadedAddresses:(0,m.lq)(t8),computeUnitsConsumed:(0,m.lq)((0,m.ai)())}),t5=(0,m.NW)({err:te,fee:(0,m.ai)(),innerInstructions:(0,m.lq)((0,m.me)((0,m.YO)((0,m.NW)({index:(0,m.ai)(),instructions:(0,m.YO)(t0)})))),preBalances:(0,m.YO)((0,m.ai)()),postBalances:(0,m.YO)((0,m.ai)()),logMessages:(0,m.lq)((0,m.me)((0,m.YO)((0,m.Yj)()))),preTokenBalances:(0,m.lq)((0,m.me)((0,m.YO)(t3))),postTokenBalances:(0,m.lq)((0,m.me)((0,m.YO)(t3))),loadedAddresses:(0,m.lq)(t8),computeUnitsConsumed:(0,m.lq)((0,m.ai)())}),t6=(0,m.KC)([(0,m.eu)(0),(0,m.eu)("legacy")]),t4=(0,m.NW)({pubkey:(0,m.Yj)(),lamports:(0,m.ai)(),postBalance:(0,m.me)((0,m.ai)()),rewardType:(0,m.me)((0,m.Yj)()),commission:(0,m.lq)((0,m.me)((0,m.ai)()))}),t7=eQ((0,m.me)((0,m.NW)({blockhash:(0,m.Yj)(),previousBlockhash:(0,m.Yj)(),parentSlot:(0,m.ai)(),transactions:(0,m.YO)((0,m.NW)({transaction:t$,meta:(0,m.me)(t2),version:(0,m.lq)(t6)})),rewards:(0,m.lq)((0,m.YO)(t4)),blockTime:(0,m.me)((0,m.ai)()),blockHeight:(0,m.me)((0,m.ai)())}))),t9=eQ((0,m.me)((0,m.NW)({blockhash:(0,m.Yj)(),previousBlockhash:(0,m.Yj)(),parentSlot:(0,m.ai)(),rewards:(0,m.lq)((0,m.YO)(t4)),blockTime:(0,m.me)((0,m.ai)()),blockHeight:(0,m.me)((0,m.ai)())}))),re=eQ((0,m.me)((0,m.NW)({blockhash:(0,m.Yj)(),previousBlockhash:(0,m.Yj)(),parentSlot:(0,m.ai)(),transactions:(0,m.YO)((0,m.NW)({transaction:tJ,meta:(0,m.me)(t2),version:(0,m.lq)(t6)})),rewards:(0,m.lq)((0,m.YO)(t4)),blockTime:(0,m.me)((0,m.ai)()),blockHeight:(0,m.me)((0,m.ai)())}))),rt=eQ((0,m.me)((0,m.NW)({blockhash:(0,m.Yj)(),previousBlockhash:(0,m.Yj)(),parentSlot:(0,m.ai)(),transactions:(0,m.YO)((0,m.NW)({transaction:t1,meta:(0,m.me)(t5),version:(0,m.lq)(t6)})),rewards:(0,m.lq)((0,m.YO)(t4)),blockTime:(0,m.me)((0,m.ai)()),blockHeight:(0,m.me)((0,m.ai)())}))),rr=eQ((0,m.me)((0,m.NW)({blockhash:(0,m.Yj)(),previousBlockhash:(0,m.Yj)(),parentSlot:(0,m.ai)(),transactions:(0,m.YO)((0,m.NW)({transaction:tJ,meta:(0,m.me)(t5),version:(0,m.lq)(t6)})),rewards:(0,m.lq)((0,m.YO)(t4)),blockTime:(0,m.me)((0,m.ai)()),blockHeight:(0,m.me)((0,m.ai)())}))),ri=eQ((0,m.me)((0,m.NW)({blockhash:(0,m.Yj)(),previousBlockhash:(0,m.Yj)(),parentSlot:(0,m.ai)(),rewards:(0,m.lq)((0,m.YO)(t4)),blockTime:(0,m.me)((0,m.ai)()),blockHeight:(0,m.me)((0,m.ai)())}))),rs=eQ((0,m.me)((0,m.NW)({blockhash:(0,m.Yj)(),previousBlockhash:(0,m.Yj)(),parentSlot:(0,m.ai)(),transactions:(0,m.YO)((0,m.NW)({transaction:t$,meta:(0,m.me)(t2)})),rewards:(0,m.lq)((0,m.YO)(t4)),blockTime:(0,m.me)((0,m.ai)())}))),rn=eQ((0,m.me)((0,m.NW)({blockhash:(0,m.Yj)(),previousBlockhash:(0,m.Yj)(),parentSlot:(0,m.ai)(),signatures:(0,m.YO)((0,m.Yj)()),blockTime:(0,m.me)((0,m.ai)())}))),ra=eQ((0,m.me)((0,m.NW)({slot:(0,m.ai)(),meta:(0,m.me)(t2),blockTime:(0,m.lq)((0,m.me)((0,m.ai)())),transaction:t$,version:(0,m.lq)(t6)}))),ro=eQ((0,m.me)((0,m.NW)({slot:(0,m.ai)(),transaction:t1,meta:(0,m.me)(t5),blockTime:(0,m.lq)((0,m.me)((0,m.ai)())),version:(0,m.lq)(t6)}))),rc=e0((0,m.NW)({blockhash:(0,m.Yj)(),lastValidBlockHeight:(0,m.ai)()})),ru=e0((0,m.zM)()),rl=(0,m.NW)({slot:(0,m.ai)(),numTransactions:(0,m.ai)(),numSlots:(0,m.ai)(),samplePeriodSecs:(0,m.ai)()}),rd=eQ((0,m.YO)(rl)),rh=e0((0,m.me)((0,m.NW)({feeCalculator:(0,m.NW)({lamportsPerSignature:(0,m.ai)()})}))),rg=eQ((0,m.Yj)()),rp=eQ((0,m.Yj)()),rm=(0,m.NW)({err:te,logs:(0,m.YO)((0,m.Yj)()),signature:(0,m.Yj)()}),ry=(0,m.NW)({result:e1(rm),subscription:(0,m.ai)()}),rb={"solana-client":"js/1.0.0-maintenance"};class rf{constructor(e,t){var r;let i,s,n,a,o,c;this._commitment=void 0,this._confirmTransactionInitialTimeout=void 0,this._rpcEndpoint=void 0,this._rpcWsEndpoint=void 0,this._rpcClient=void 0,this._rpcRequest=void 0,this._rpcBatchRequest=void 0,this._rpcWebSocket=void 0,this._rpcWebSocketConnected=!1,this._rpcWebSocketHeartbeat=null,this._rpcWebSocketIdleTimeout=null,this._rpcWebSocketGeneration=0,this._disableBlockhashCaching=!1,this._pollingBlockhash=!1,this._blockhashInfo={latestBlockhash:null,lastFetch:0,transactionSignatures:[],simulatedSignatures:[]},this._nextClientSubscriptionId=0,this._subscriptionDisposeFunctionsByClientSubscriptionId={},this._subscriptionHashByClientSubscriptionId={},this._subscriptionStateChangeCallbacksByHash={},this._subscriptionCallbacksByServerSubscriptionId={},this._subscriptionsByHash={},this._subscriptionsAutoDisposedByRpc=new Set,this.getBlockHeight=(()=>{let e={};return async t=>{let{commitment:r,config:i}=eJ(t),s=this._buildArgs([],r,void 0,i),n=eK(s);return e[n]=e[n]??(async()=>{try{let e=await this._rpcRequest("getBlockHeight",s),t=(0,m.vt)(e,eQ((0,m.ai)()));if("error"in t)throw new ew(t.error,"failed to get block height information");return t.result}finally{delete e[n]}})(),await e[n]}})(),t&&"string"==typeof t?this._commitment=t:t&&(this._commitment=t.commitment,this._confirmTransactionInitialTimeout=t.confirmTransactionInitialTimeout,i=t.wsEndpoint,s=t.httpHeaders,n=t.fetch,a=t.fetchMiddleware,o=t.disableRetryOnRateLimit,c=t.httpAgent),this._rpcEndpoint=function(e){if(!1===/^https?:/.test(e))throw TypeError("Endpoint URL must start with `http:` or `https:`.");return e}(e),this._rpcWsEndpoint=i||function(e){let t=e.match(eM);if(null==t)throw TypeError(`Failed to validate endpoint URL \`${e}\``);let[r,i,s,n]=t,a=e.startsWith("https:")?"wss:":"ws:",o=null==s?null:parseInt(s.slice(1),10),c=null==o?"":`:${o+1}`;return`${a}//${i}${c}${n}`}(e),this._rpcClient=function(e,t,r,i,s,n){let a,o,c=r||eq;return null!=n&&console.warn("You have supplied an `httpAgent` when creating a `Connection` in a browser environment.It has been ignored; `httpAgent` is only used in Node environments."),i&&(o=async(e,t)=>{let r=await new Promise((r,s)=>{try{i(e,t,(e,t)=>r([e,t]))}catch(e){s(e)}});return await c(...r)}),new(b())(async(r,i)=>{let n={method:"POST",body:r,agent:a,headers:Object.assign({"Content-Type":"application/json"},t||{},rb)};try{let t,r=5,a=500;for(;(t=o?await o(e,n):await c(e,n),429===t.status&&!0!==s)&&(r-=1,0!==r);){;console.error(`Server responded with ${t.status} ${t.statusText}.  Retrying after ${a}ms delay...`),await eI(a),a*=2}let u=await t.text();t.ok?i(null,u):i(Error(`${t.status} ${t.statusText}: ${u}`))}catch(e){e instanceof Error&&i(e)}},{})}(e,s,n,a,o,c),this._rpcRequest=(r=this._rpcClient,(e,t)=>new Promise((i,s)=>{r.request(e,t,(e,t)=>{if(e)return void s(e);i(t)})})),this._rpcBatchRequest=function(e){return t=>new Promise((r,i)=>{0===t.length&&r([]);let s=t.map(t=>e.request(t.methodName,t.args));e.request(s,(e,t)=>{if(e)return void i(e);r(t)})})}(this._rpcClient),this._rpcWebSocket=new eD(this._rpcWsEndpoint,{autoconnect:!1,max_reconnects:1/0}),this._rpcWebSocket.on("open",this._wsOnOpen.bind(this)),this._rpcWebSocket.on("error",this._wsOnError.bind(this)),this._rpcWebSocket.on("close",this._wsOnClose.bind(this)),this._rpcWebSocket.on("accountNotification",this._wsOnAccountNotification.bind(this)),this._rpcWebSocket.on("programNotification",this._wsOnProgramAccountNotification.bind(this)),this._rpcWebSocket.on("slotNotification",this._wsOnSlotNotification.bind(this)),this._rpcWebSocket.on("slotsUpdatesNotification",this._wsOnSlotUpdatesNotification.bind(this)),this._rpcWebSocket.on("signatureNotification",this._wsOnSignatureNotification.bind(this)),this._rpcWebSocket.on("rootNotification",this._wsOnRootNotification.bind(this)),this._rpcWebSocket.on("logsNotification",this._wsOnLogsNotification.bind(this))}get commitment(){return this._commitment}get rpcEndpoint(){return this._rpcEndpoint}async getBalanceAndContext(e,t){let{commitment:r,config:i}=eJ(t),s=this._buildArgs([e.toBase58()],r,void 0,i),n=await this._rpcRequest("getBalance",s),a=(0,m.vt)(n,e0((0,m.ai)()));if("error"in a)throw new ew(a.error,`failed to get balance for ${e.toBase58()}`);return a.result}async getBalance(e,t){return await this.getBalanceAndContext(e,t).then(e=>e.value).catch(t=>{throw Error("failed to get balance of account "+e.toBase58()+": "+t)})}async getBlockTime(e){let t=await this._rpcRequest("getBlockTime",[e]),r=(0,m.vt)(t,eQ((0,m.me)((0,m.ai)())));if("error"in r)throw new ew(r.error,`failed to get block time for slot ${e}`);return r.result}async getMinimumLedgerSlot(){let e=await this._rpcRequest("minimumLedgerSlot",[]),t=(0,m.vt)(e,eQ((0,m.ai)()));if("error"in t)throw new ew(t.error,"failed to get minimum ledger slot");return t.result}async getFirstAvailableBlock(){let e=await this._rpcRequest("getFirstAvailableBlock",[]),t=(0,m.vt)(e,tp);if("error"in t)throw new ew(t.error,"failed to get first available block");return t.result}async getSupply(e){let t={};t="string"==typeof e?{commitment:e}:e?{...e,commitment:e&&e.commitment||this.commitment}:{commitment:this.commitment};let r=await this._rpcRequest("getSupply",[t]),i=(0,m.vt)(r,tm);if("error"in i)throw new ew(i.error,"failed to get supply");return i.result}async getTokenSupply(e,t){let r=this._buildArgs([e.toBase58()],t),i=await this._rpcRequest("getTokenSupply",r),s=(0,m.vt)(i,e0(ty));if("error"in s)throw new ew(s.error,"failed to get token supply");return s.result}async getTokenAccountBalance(e,t){let r=this._buildArgs([e.toBase58()],t),i=await this._rpcRequest("getTokenAccountBalance",r),s=(0,m.vt)(i,e0(ty));if("error"in s)throw new ew(s.error,"failed to get token account balance");return s.result}async getTokenAccountsByOwner(e,t,r){let{commitment:i,config:s}=eJ(r),n=[e.toBase58()];"mint"in t?n.push({mint:t.mint.toBase58()}):n.push({programId:t.programId.toBase58()});let a=this._buildArgs(n,i,"base64",s),o=await this._rpcRequest("getTokenAccountsByOwner",a),c=(0,m.vt)(o,tf);if("error"in c)throw new ew(c.error,`failed to get token accounts owned by account ${e.toBase58()}`);return c.result}async getParsedTokenAccountsByOwner(e,t,r){let i=[e.toBase58()];"mint"in t?i.push({mint:t.mint.toBase58()}):i.push({programId:t.programId.toBase58()});let s=this._buildArgs(i,r,"jsonParsed"),n=await this._rpcRequest("getTokenAccountsByOwner",s),a=(0,m.vt)(n,tw);if("error"in a)throw new ew(a.error,`failed to get token accounts owned by account ${e.toBase58()}`);return a.result}async getLargestAccounts(e){let t={...e,commitment:e&&e.commitment||this.commitment},r=t.filter||t.commitment?[t]:[],i=await this._rpcRequest("getLargestAccounts",r),s=(0,m.vt)(i,tS);if("error"in s)throw new ew(s.error,"failed to get largest accounts");return s.result}async getTokenLargestAccounts(e,t){let r=this._buildArgs([e.toBase58()],t),i=await this._rpcRequest("getTokenLargestAccounts",r),s=(0,m.vt)(i,tb);if("error"in s)throw new ew(s.error,"failed to get token largest accounts");return s.result}async getAccountInfoAndContext(e,t){let{commitment:r,config:i}=eJ(t),s=this._buildArgs([e.toBase58()],r,"base64",i),n=await this._rpcRequest("getAccountInfo",s),a=(0,m.vt)(n,e0((0,m.me)(tI)));if("error"in a)throw new ew(a.error,`failed to get info about account ${e.toBase58()}`);return a.result}async getParsedAccountInfo(e,t){let{commitment:r,config:i}=eJ(t),s=this._buildArgs([e.toBase58()],r,"jsonParsed",i),n=await this._rpcRequest("getAccountInfo",s),a=(0,m.vt)(n,e0((0,m.me)(tA)));if("error"in a)throw new ew(a.error,`failed to get info about account ${e.toBase58()}`);return a.result}async getAccountInfo(e,t){try{return(await this.getAccountInfoAndContext(e,t)).value}catch(t){throw Error("failed to get info about account "+e.toBase58()+": "+t)}}async getMultipleParsedAccounts(e,t){let{commitment:r,config:i}=eJ(t),s=e.map(e=>e.toBase58()),n=this._buildArgs([s],r,"jsonParsed",i),a=await this._rpcRequest("getMultipleAccounts",n),o=(0,m.vt)(a,e0((0,m.YO)((0,m.me)(tA))));if("error"in o)throw new ew(o.error,`failed to get info for accounts ${s}`);return o.result}async getMultipleAccountsInfoAndContext(e,t){let{commitment:r,config:i}=eJ(t),s=e.map(e=>e.toBase58()),n=this._buildArgs([s],r,"base64",i),a=await this._rpcRequest("getMultipleAccounts",n),o=(0,m.vt)(a,e0((0,m.YO)((0,m.me)(tI))));if("error"in o)throw new ew(o.error,`failed to get info for accounts ${s}`);return o.result}async getMultipleAccountsInfo(e,t){return(await this.getMultipleAccountsInfoAndContext(e,t)).value}async getStakeActivation(e,t,r){let{commitment:i,config:s}=eJ(t),n=this._buildArgs([e.toBase58()],i,void 0,{...s,epoch:null!=r?r:s?.epoch}),a=await this._rpcRequest("getStakeActivation",n),o=(0,m.vt)(a,eQ(tE));if("error"in o)throw new ew(o.error,`failed to get Stake Activation ${e.toBase58()}`);return o.result}async getProgramAccounts(e,t){let{commitment:r,config:i}=eJ(t),{encoding:s,...n}=i||{},a=this._buildArgs([e.toBase58()],r,s||"base64",{...n,...n.filters?{filters:eG(n.filters)}:null}),o=await this._rpcRequest("getProgramAccounts",a),c=(0,m.YO)(t_),u=!0===n.withContext?(0,m.vt)(o,e0(c)):(0,m.vt)(o,eQ(c));if("error"in u)throw new ew(u.error,`failed to get accounts owned by program ${e.toBase58()}`);return u.result}async getParsedProgramAccounts(e,t){let{commitment:r,config:i}=eJ(t),s=this._buildArgs([e.toBase58()],r,"jsonParsed",i),n=await this._rpcRequest("getProgramAccounts",s),a=(0,m.vt)(n,eQ((0,m.YO)(tP)));if("error"in a)throw new ew(a.error,`failed to get accounts owned by program ${e.toBase58()}`);return a.result}async confirmTransaction(e,t){let r,i;if("string"==typeof e)r=e;else{if(e.abortSignal?.aborted)return Promise.reject(e.abortSignal.reason);r=e.signature}try{i=l().decode(r)}catch(e){throw Error("signature must be base58 encoded: "+r)}return(J(64===i.length,"signature has invalid length"),"string"==typeof e)?await this.confirmTransactionUsingLegacyTimeoutStrategy({commitment:t||this.commitment,signature:r}):"lastValidBlockHeight"in e?await this.confirmTransactionUsingBlockHeightExceedanceStrategy({commitment:t||this.commitment,strategy:e}):await this.confirmTransactionUsingDurableNonceStrategy({commitment:t||this.commitment,strategy:e})}getCancellationPromise(e){return new Promise((t,r)=>{null!=e&&(e.aborted?r(e.reason):e.addEventListener("abort",()=>{r(e.reason)}))})}getTransactionConfirmationPromise({commitment:e,signature:t}){let r,i,s=!1;return{abortConfirmation:()=>{i&&(i(),i=void 0),null!=r&&(this.removeSignatureListener(r),r=void 0)},confirmationPromise:new Promise((n,a)=>{try{r=this.onSignature(t,(e,t)=>{r=void 0,n({__type:ei.PROCESSED,response:{context:t,value:e}})},e);let o=new Promise(e=>{null==r?e():i=this._onSubscriptionStateChange(r,t=>{"subscribed"===t&&e()})});(async()=>{if(await o,s)return;let r=await this.getSignatureStatus(t);if(s||null==r)return;let{context:i,value:c}=r;if(null!=c)if(c?.err)a(c.err);else{switch(e){case"confirmed":case"single":case"singleGossip":if("processed"===c.confirmationStatus)return;break;case"finalized":case"max":case"root":if("processed"===c.confirmationStatus||"confirmed"===c.confirmationStatus)return}s=!0,n({__type:ei.PROCESSED,response:{context:i,value:c}})}})()}catch(e){a(e)}})}}async confirmTransactionUsingBlockHeightExceedanceStrategy({commitment:e,strategy:{abortSignal:t,lastValidBlockHeight:r,signature:i}}){let s,n=!1,a=new Promise(t=>{let i=async()=>{try{return await this.getBlockHeight(e)}catch(e){return -1}};(async()=>{let e=await i();if(!n){for(;e<=r;)if(await eI(1e3),n||(e=await i(),n))return;t({__type:ei.BLOCKHEIGHT_EXCEEDED})}})()}),{abortConfirmation:o,confirmationPromise:c}=this.getTransactionConfirmationPromise({commitment:e,signature:i}),u=this.getCancellationPromise(t);try{let e=await Promise.race([u,c,a]);if(e.__type===ei.PROCESSED)s=e.response;else throw new q(i)}finally{n=!0,o()}return s}async confirmTransactionUsingDurableNonceStrategy({commitment:e,strategy:{abortSignal:t,minContextSlot:r,nonceAccountPubkey:i,nonceValue:s,signature:n}}){let a,o=!1,c=new Promise(t=>{let n=s,a=null,c=async()=>{try{let{context:t,value:s}=await this.getNonceAndContext(i,{commitment:e,minContextSlot:r});return a=t.slot,s?.nonce}catch(e){return n}};(async()=>{if(n=await c(),!o)for(;;){if(s!==n)return void t({__type:ei.NONCE_INVALID,slotInWhichNonceDidAdvance:a});if(await eI(2e3),o||(n=await c(),o))return}})()}),{abortConfirmation:u,confirmationPromise:l}=this.getTransactionConfirmationPromise({commitment:e,signature:n}),d=this.getCancellationPromise(t);try{let t=await Promise.race([d,l,c]);if(t.__type===ei.PROCESSED)a=t.response;else{let i;for(;;){let e=await this.getSignatureStatus(n);if(null==e)break;if(e.context.slot<(t.slotInWhichNonceDidAdvance??r)){await eI(400);continue}i=e;break}if(i?.value){let t=e||"finalized",{confirmationStatus:r}=i.value;switch(t){case"processed":case"recent":if("processed"!==r&&"confirmed"!==r&&"finalized"!==r)throw new H(n);break;case"confirmed":case"single":case"singleGossip":if("confirmed"!==r&&"finalized"!==r)throw new H(n);break;case"finalized":case"max":case"root":if("finalized"!==r)throw new H(n)}a={context:i.context,value:{err:i.value.err}}}else throw new H(n)}}finally{o=!0,u()}return a}async confirmTransactionUsingLegacyTimeoutStrategy({commitment:e,signature:t}){let r,i,s=new Promise(t=>{let i=this._confirmTransactionInitialTimeout||6e4;switch(e){case"processed":case"recent":case"single":case"confirmed":case"singleGossip":i=this._confirmTransactionInitialTimeout||3e4}r=setTimeout(()=>t({__type:ei.TIMED_OUT,timeoutMs:i}),i)}),{abortConfirmation:n,confirmationPromise:a}=this.getTransactionConfirmationPromise({commitment:e,signature:t});try{let e=await Promise.race([a,s]);if(e.__type===ei.PROCESSED)i=e.response;else throw new D(t,e.timeoutMs/1e3)}finally{clearTimeout(r),n()}return i}async getClusterNodes(){let e=await this._rpcRequest("getClusterNodes",[]),t=(0,m.vt)(e,eQ((0,m.YO)(tY)));if("error"in t)throw new ew(t.error,"failed to get cluster nodes");return t.result}async getVoteAccounts(e){let t=this._buildArgs([],e),r=await this._rpcRequest("getVoteAccounts",t),i=(0,m.vt)(r,tD);if("error"in i)throw new ew(i.error,"failed to get vote accounts");return i.result}async getSlot(e){let{commitment:t,config:r}=eJ(e),i=this._buildArgs([],t,void 0,r),s=await this._rpcRequest("getSlot",i),n=(0,m.vt)(s,eQ((0,m.ai)()));if("error"in n)throw new ew(n.error,"failed to get slot");return n.result}async getSlotLeader(e){let{commitment:t,config:r}=eJ(e),i=this._buildArgs([],t,void 0,r),s=await this._rpcRequest("getSlotLeader",i),n=(0,m.vt)(s,eQ((0,m.Yj)()));if("error"in n)throw new ew(n.error,"failed to get slot leader");return n.result}async getSlotLeaders(e,t){let r=await this._rpcRequest("getSlotLeaders",[e,t]),i=(0,m.vt)(r,eQ((0,m.YO)(eU)));if("error"in i)throw new ew(i.error,"failed to get slot leaders");return i.result}async getSignatureStatus(e,t){let{context:r,value:i}=await this.getSignatureStatuses([e],t);return J(1===i.length),{context:r,value:i[0]}}async getSignatureStatuses(e,t){let r=[e];t&&r.push(t);let i=await this._rpcRequest("getSignatureStatuses",r),s=(0,m.vt)(i,tM);if("error"in s)throw new ew(s.error,"failed to get signature status");return s.result}async getTransactionCount(e){let{commitment:t,config:r}=eJ(e),i=this._buildArgs([],t,void 0,r),s=await this._rpcRequest("getTransactionCount",i),n=(0,m.vt)(s,eQ((0,m.ai)()));if("error"in n)throw new ew(n.error,"failed to get transaction count");return n.result}async getTotalSupply(e){return(await this.getSupply({commitment:e,excludeNonCirculatingAccountsList:!0})).value.total}async getInflationGovernor(e){let t=this._buildArgs([],e),r=await this._rpcRequest("getInflationGovernor",t),i=(0,m.vt)(r,tc);if("error"in i)throw new ew(i.error,"failed to get inflation");return i.result}async getInflationReward(e,t,r){let{commitment:i,config:s}=eJ(r),n=this._buildArgs([e.map(e=>e.toBase58())],i,void 0,{...s,epoch:null!=t?t:s?.epoch}),a=await this._rpcRequest("getInflationReward",n),o=(0,m.vt)(a,e2);if("error"in o)throw new ew(o.error,"failed to get inflation reward");return o.result}async getInflationRate(){let e=await this._rpcRequest("getInflationRate",[]),t=(0,m.vt)(e,tu);if("error"in t)throw new ew(t.error,"failed to get inflation rate");return t.result}async getEpochInfo(e){let{commitment:t,config:r}=eJ(e),i=this._buildArgs([],t,void 0,r),s=await this._rpcRequest("getEpochInfo",i),n=(0,m.vt)(s,td);if("error"in n)throw new ew(n.error,"failed to get epoch info");return n.result}async getEpochSchedule(){let e=await this._rpcRequest("getEpochSchedule",[]),t=(0,m.vt)(e,th);if("error"in t)throw new ew(t.error,"failed to get epoch schedule");let r=t.result;return new eY(r.slotsPerEpoch,r.leaderScheduleSlotOffset,r.warmup,r.firstNormalEpoch,r.firstNormalSlot)}async getLeaderSchedule(){let e=await this._rpcRequest("getLeaderSchedule",[]),t=(0,m.vt)(e,tg);if("error"in t)throw new ew(t.error,"failed to get leader schedule");return t.result}async getMinimumBalanceForRentExemption(e,t){let r=this._buildArgs([e],t),i=await this._rpcRequest("getMinimumBalanceForRentExemption",r),s=(0,m.vt)(i,tU);return"error"in s?(console.warn("Unable to fetch minimum balance for rent exemption"),0):s.result}async getRecentBlockhashAndContext(e){let{context:t,value:{blockhash:r}}=await this.getLatestBlockhashAndContext(e);return{context:t,value:{blockhash:r,feeCalculator:{get lamportsPerSignature(){throw Error("The capability to fetch `lamportsPerSignature` using the `getRecentBlockhash` API is no longer offered by the network. Use the `getFeeForMessage` API to obtain the fee for a given message.")},toJSON:()=>({})}}}}async getRecentPerformanceSamples(e){let t=await this._rpcRequest("getRecentPerformanceSamples",e?[e]:[]),r=(0,m.vt)(t,rd);if("error"in r)throw new ew(r.error,"failed to get recent performance samples");return r.result}async getFeeCalculatorForBlockhash(e,t){let r=this._buildArgs([e],t),i=await this._rpcRequest("getFeeCalculatorForBlockhash",r),s=(0,m.vt)(i,rh);if("error"in s)throw new ew(s.error,"failed to get fee calculator");let{context:n,value:a}=s.result;return{context:n,value:null!==a?a.feeCalculator:null}}async getFeeForMessage(e,t){let r=E(e.serialize()).toString("base64"),i=this._buildArgs([r],t),s=await this._rpcRequest("getFeeForMessage",i),n=(0,m.vt)(s,e0((0,m.me)((0,m.ai)())));if("error"in n)throw new ew(n.error,"failed to get fee for message");if(null===n.result)throw Error("invalid blockhash");return n.result}async getRecentPrioritizationFees(e){let t=e?.lockedWritableAccounts?.map(e=>e.toBase58()),r=t?.length?[t]:[],i=await this._rpcRequest("getRecentPrioritizationFees",r),s=(0,m.vt)(i,tl);if("error"in s)throw new ew(s.error,"failed to get recent prioritization fees");return s.result}async getRecentBlockhash(e){try{return(await this.getRecentBlockhashAndContext(e)).value}catch(e){throw Error("failed to get recent blockhash: "+e)}}async getLatestBlockhash(e){try{return(await this.getLatestBlockhashAndContext(e)).value}catch(e){throw Error("failed to get recent blockhash: "+e)}}async getLatestBlockhashAndContext(e){let{commitment:t,config:r}=eJ(e),i=this._buildArgs([],t,void 0,r),s=await this._rpcRequest("getLatestBlockhash",i),n=(0,m.vt)(s,rc);if("error"in n)throw new ew(n.error,"failed to get latest blockhash");return n.result}async isBlockhashValid(e,t){let{commitment:r,config:i}=eJ(t),s=this._buildArgs([e],r,void 0,i),n=await this._rpcRequest("isBlockhashValid",s),a=(0,m.vt)(n,ru);if("error"in a)throw new ew(a.error,"failed to determine if the blockhash `"+e+"`is valid");return a.result}async getVersion(){let e=await this._rpcRequest("getVersion",[]),t=(0,m.vt)(e,eQ(ti));if("error"in t)throw new ew(t.error,"failed to get version");return t.result}async getGenesisHash(){let e=await this._rpcRequest("getGenesisHash",[]),t=(0,m.vt)(e,eQ((0,m.Yj)()));if("error"in t)throw new ew(t.error,"failed to get genesis hash");return t.result}async getBlock(e,t){let{commitment:r,config:i}=eJ(t),s=this._buildArgsAtLeastConfirmed([e],r,void 0,i),n=await this._rpcRequest("getBlock",s);try{switch(i?.transactionDetails){case"accounts":{let e=(0,m.vt)(n,re);if("error"in e)throw e.error;return e.result}case"none":{let e=(0,m.vt)(n,t9);if("error"in e)throw e.error;return e.result}default:{let e=(0,m.vt)(n,t7);if("error"in e)throw e.error;let{result:t}=e;return t?{...t,transactions:t.transactions.map(({transaction:e,meta:t,version:r})=>({meta:t,transaction:{...e,message:e3(r,e.message)},version:r}))}:null}}}catch(e){throw new ew(e,"failed to get confirmed block")}}async getParsedBlock(e,t){let{commitment:r,config:i}=eJ(t),s=this._buildArgsAtLeastConfirmed([e],r,"jsonParsed",i),n=await this._rpcRequest("getBlock",s);try{switch(i?.transactionDetails){case"accounts":{let e=(0,m.vt)(n,rr);if("error"in e)throw e.error;return e.result}case"none":{let e=(0,m.vt)(n,ri);if("error"in e)throw e.error;return e.result}default:{let e=(0,m.vt)(n,rt);if("error"in e)throw e.error;return e.result}}}catch(e){throw new ew(e,"failed to get block")}}async getBlockProduction(e){let t,r;if("string"==typeof e)r=e;else if(e){let{commitment:i,...s}=e;r=i,t=s}let i=this._buildArgs([],r,"base64",t),s=await this._rpcRequest("getBlockProduction",i),n=(0,m.vt)(s,to);if("error"in n)throw new ew(n.error,"failed to get block production information");return n.result}async getTransaction(e,t){let{commitment:r,config:i}=eJ(t),s=this._buildArgsAtLeastConfirmed([e],r,void 0,i),n=await this._rpcRequest("getTransaction",s),a=(0,m.vt)(n,ra);if("error"in a)throw new ew(a.error,"failed to get transaction");let o=a.result;return o?{...o,transaction:{...o.transaction,message:e3(o.version,o.transaction.message)}}:o}async getParsedTransaction(e,t){let{commitment:r,config:i}=eJ(t),s=this._buildArgsAtLeastConfirmed([e],r,"jsonParsed",i),n=await this._rpcRequest("getTransaction",s),a=(0,m.vt)(n,ro);if("error"in a)throw new ew(a.error,"failed to get transaction");return a.result}async getParsedTransactions(e,t){let{commitment:r,config:i}=eJ(t),s=e.map(e=>({methodName:"getTransaction",args:this._buildArgsAtLeastConfirmed([e],r,"jsonParsed",i)}));return(await this._rpcBatchRequest(s)).map(e=>{let t=(0,m.vt)(e,ro);if("error"in t)throw new ew(t.error,"failed to get transactions");return t.result})}async getTransactions(e,t){let{commitment:r,config:i}=eJ(t),s=e.map(e=>({methodName:"getTransaction",args:this._buildArgsAtLeastConfirmed([e],r,void 0,i)}));return(await this._rpcBatchRequest(s)).map(e=>{let t=(0,m.vt)(e,ra);if("error"in t)throw new ew(t.error,"failed to get transactions");let r=t.result;return r?{...r,transaction:{...r.transaction,message:e3(r.version,r.transaction.message)}}:r})}async getConfirmedBlock(e,t){let r=this._buildArgsAtLeastConfirmed([e],t),i=await this._rpcRequest("getBlock",r),s=(0,m.vt)(i,rs);if("error"in s)throw new ew(s.error,"failed to get confirmed block");let n=s.result;if(!n)throw Error("Confirmed block "+e+" not found");let a={...n,transactions:n.transactions.map(({transaction:e,meta:t})=>{let r=new ee(e.message);return{meta:t,transaction:{...e,message:r}}})};return{...a,transactions:a.transactions.map(({transaction:e,meta:t})=>({meta:t,transaction:ea.populate(e.message,e.signatures)}))}}async getBlocks(e,t,r){let i=this._buildArgsAtLeastConfirmed(void 0!==t?[e,t]:[e],r),s=await this._rpcRequest("getBlocks",i),n=(0,m.vt)(s,eQ((0,m.YO)((0,m.ai)())));if("error"in n)throw new ew(n.error,"failed to get blocks");return n.result}async getBlockSignatures(e,t){let r=this._buildArgsAtLeastConfirmed([e],t,void 0,{transactionDetails:"signatures",rewards:!1}),i=await this._rpcRequest("getBlock",r),s=(0,m.vt)(i,rn);if("error"in s)throw new ew(s.error,"failed to get block");let n=s.result;if(!n)throw Error("Block "+e+" not found");return n}async getConfirmedBlockSignatures(e,t){let r=this._buildArgsAtLeastConfirmed([e],t,void 0,{transactionDetails:"signatures",rewards:!1}),i=await this._rpcRequest("getBlock",r),s=(0,m.vt)(i,rn);if("error"in s)throw new ew(s.error,"failed to get confirmed block");let n=s.result;if(!n)throw Error("Confirmed block "+e+" not found");return n}async getConfirmedTransaction(e,t){let r=this._buildArgsAtLeastConfirmed([e],t),i=await this._rpcRequest("getTransaction",r),s=(0,m.vt)(i,ra);if("error"in s)throw new ew(s.error,"failed to get transaction");let n=s.result;if(!n)return n;let a=new ee(n.transaction.message),o=n.transaction.signatures;return{...n,transaction:ea.populate(a,o)}}async getParsedConfirmedTransaction(e,t){let r=this._buildArgsAtLeastConfirmed([e],t,"jsonParsed"),i=await this._rpcRequest("getTransaction",r),s=(0,m.vt)(i,ro);if("error"in s)throw new ew(s.error,"failed to get confirmed transaction");return s.result}async getParsedConfirmedTransactions(e,t){let r=e.map(e=>({methodName:"getTransaction",args:this._buildArgsAtLeastConfirmed([e],t,"jsonParsed")}));return(await this._rpcBatchRequest(r)).map(e=>{let t=(0,m.vt)(e,ro);if("error"in t)throw new ew(t.error,"failed to get confirmed transactions");return t.result})}async getConfirmedSignaturesForAddress(e,t,r){let i={},s=await this.getFirstAvailableBlock();for(;!("until"in i)&&!(--t<=0)&&!(t<s);)try{let e=await this.getConfirmedBlockSignatures(t,"finalized");e.signatures.length>0&&(i.until=e.signatures[e.signatures.length-1].toString())}catch(e){if(e instanceof Error&&e.message.includes("skipped"))continue;throw e}let n=await this.getSlot("finalized");for(;!("before"in i)&&!(++r>n);)try{let e=await this.getConfirmedBlockSignatures(r);e.signatures.length>0&&(i.before=e.signatures[e.signatures.length-1].toString())}catch(e){if(e instanceof Error&&e.message.includes("skipped"))continue;throw e}return(await this.getConfirmedSignaturesForAddress2(e,i)).map(e=>e.signature)}async getConfirmedSignaturesForAddress2(e,t,r){let i=this._buildArgsAtLeastConfirmed([e.toBase58()],r,void 0,t),s=await this._rpcRequest("getConfirmedSignaturesForAddress2",i),n=(0,m.vt)(s,tB);if("error"in n)throw new ew(n.error,"failed to get confirmed signatures for address");return n.result}async getSignaturesForAddress(e,t,r){let i=this._buildArgsAtLeastConfirmed([e.toBase58()],r,void 0,t),s=await this._rpcRequest("getSignaturesForAddress",i),n=(0,m.vt)(s,tW);if("error"in n)throw new ew(n.error,"failed to get signatures for address");return n.result}async getAddressLookupTable(e,t){let{context:r,value:i}=await this.getAccountInfoAndContext(e,t),s=null;return null!==i&&(s=new eH({key:e,state:eH.deserialize(i.data)})),{context:r,value:s}}async getNonceAndContext(e,t){let{context:r,value:i}=await this.getAccountInfoAndContext(e,t),s=null;return null!==i&&(s=eB.fromAccountData(i.data)),{context:r,value:s}}async getNonce(e,t){return await this.getNonceAndContext(e,t).then(e=>e.value).catch(t=>{throw Error("failed to get nonce for account "+e.toBase58()+": "+t)})}async requestAirdrop(e,t){let r=await this._rpcRequest("requestAirdrop",[e.toBase58(),t]),i=(0,m.vt)(r,rg);if("error"in i)throw new ew(i.error,`airdrop to ${e.toBase58()} failed`);return i.result}async _blockhashWithExpiryBlockHeight(e){if(!e){for(;this._pollingBlockhash;)await eI(100);let e=Date.now()-this._blockhashInfo.lastFetch;if(null!==this._blockhashInfo.latestBlockhash&&!(e>=eF))return this._blockhashInfo.latestBlockhash}return await this._pollNewBlockhash()}async _pollNewBlockhash(){this._pollingBlockhash=!0;try{let e=Date.now(),t=this._blockhashInfo.latestBlockhash,r=t?t.blockhash:null;for(let e=0;e<50;e++){let e=await this.getLatestBlockhash("finalized");if(r!==e.blockhash)return this._blockhashInfo={latestBlockhash:e,lastFetch:Date.now(),transactionSignatures:[],simulatedSignatures:[]},e;await eI(200)}throw Error(`Unable to obtain a new blockhash after ${Date.now()-e}ms`)}finally{this._pollingBlockhash=!1}}async getStakeMinimumDelegation(e){let{commitment:t,config:r}=eJ(e),i=this._buildArgs([],t,"base64",r),s=await this._rpcRequest("getStakeMinimumDelegation",i),n=(0,m.vt)(s,e0((0,m.ai)()));if("error"in n)throw new ew(n.error,"failed to get stake minimum delegation");return n.result}async simulateTransaction(e,t,r){let i;if("message"in e){let i=e.serialize(),s=n.Buffer.from(i).toString("base64");if(Array.isArray(t)||void 0!==r)throw Error("Invalid arguments");let a=t||{};a.encoding="base64","commitment"in a||(a.commitment=this.commitment),t&&"object"==typeof t&&"innerInstructions"in t&&(a.innerInstructions=t.innerInstructions);let o=[s,a],c=await this._rpcRequest("simulateTransaction",o),u=(0,m.vt)(c,ta);if("error"in u)throw Error("failed to simulate transaction: "+u.error.message);return u.result}if(e instanceof ea?((i=new ea).feePayer=e.feePayer,i.instructions=e.instructions,i.nonceInfo=e.nonceInfo,i.signatures=e.signatures):(i=ea.populate(e))._message=i._json=void 0,void 0!==t&&!Array.isArray(t))throw Error("Invalid arguments");if(i.nonceInfo&&t)i.sign(...t);else{let e=this._disableBlockhashCaching;for(;;){let r=await this._blockhashWithExpiryBlockHeight(e);if(i.lastValidBlockHeight=r.lastValidBlockHeight,i.recentBlockhash=r.blockhash,!t)break;if(i.sign(...t),!i.signature)throw Error("!signature");let s=i.signature.toString("base64");if(this._blockhashInfo.simulatedSignatures.includes(s)||this._blockhashInfo.transactionSignatures.includes(s))e=!0;else{this._blockhashInfo.simulatedSignatures.push(s);break}}}let s=i._compile(),a=s.serialize(),o=i._serialize(a).toString("base64"),c={encoding:"base64",commitment:this.commitment};r&&(c.accounts={encoding:"base64",addresses:(Array.isArray(r)?r:s.nonProgramIds()).map(e=>e.toBase58())}),t&&(c.sigVerify=!0),t&&"object"==typeof t&&"innerInstructions"in t&&(c.innerInstructions=t.innerInstructions);let u=[o,c],l=await this._rpcRequest("simulateTransaction",u),d=(0,m.vt)(l,ta);if("error"in d){let e;if("data"in d.error&&(e=d.error.data.logs)&&Array.isArray(e)){let t="\n    ",r=t+e.join(t);console.error(d.error.message,r)}throw new ef({action:"simulate",signature:"",transactionMessage:d.error.message,logs:e})}return d.result}async sendTransaction(e,t,r){if("version"in e){if(t&&Array.isArray(t))throw Error("Invalid arguments");let r=e.serialize();return await this.sendRawTransaction(r,t)}if(void 0===t||!Array.isArray(t))throw Error("Invalid arguments");if(e.nonceInfo)e.sign(...t);else{let r=this._disableBlockhashCaching;for(;;){let i=await this._blockhashWithExpiryBlockHeight(r);if(e.lastValidBlockHeight=i.lastValidBlockHeight,e.recentBlockhash=i.blockhash,e.sign(...t),!e.signature)throw Error("!signature");let s=e.signature.toString("base64");if(this._blockhashInfo.transactionSignatures.includes(s))r=!0;else{this._blockhashInfo.transactionSignatures.push(s);break}}}let i=e.serialize();return await this.sendRawTransaction(i,r)}async sendRawTransaction(e,t){let r=E(e).toString("base64");return await this.sendEncodedTransaction(r,t)}async sendEncodedTransaction(e,t){let r={encoding:"base64"},i=t&&t.skipPreflight,s=!0===i?"processed":t&&t.preflightCommitment||this.commitment;t&&null!=t.maxRetries&&(r.maxRetries=t.maxRetries),t&&null!=t.minContextSlot&&(r.minContextSlot=t.minContextSlot),i&&(r.skipPreflight=i),s&&(r.preflightCommitment=s);let n=[e,r],a=await this._rpcRequest("sendTransaction",n),o=(0,m.vt)(a,rp);if("error"in o){let e;throw"data"in o.error&&(e=o.error.data.logs),new ef({action:i?"send":"simulate",signature:"",transactionMessage:o.error.message,logs:e})}return o.result}_wsOnOpen(){this._rpcWebSocketConnected=!0,this._rpcWebSocketHeartbeat=setInterval(()=>{(async()=>{try{await this._rpcWebSocket.notify("ping")}catch{}})()},5e3),this._updateSubscriptions()}_wsOnError(e){this._rpcWebSocketConnected=!1,console.error("ws error:",e.message)}_wsOnClose(e){if(this._rpcWebSocketConnected=!1,this._rpcWebSocketGeneration=(this._rpcWebSocketGeneration+1)%Number.MAX_SAFE_INTEGER,this._rpcWebSocketIdleTimeout&&(clearTimeout(this._rpcWebSocketIdleTimeout),this._rpcWebSocketIdleTimeout=null),this._rpcWebSocketHeartbeat&&(clearInterval(this._rpcWebSocketHeartbeat),this._rpcWebSocketHeartbeat=null),1e3===e)return void this._updateSubscriptions();this._subscriptionCallbacksByServerSubscriptionId={},Object.entries(this._subscriptionsByHash).forEach(([e,t])=>{this._setSubscription(e,{...t,state:"pending"})})}_setSubscription(e,t){let r=this._subscriptionsByHash[e]?.state;if(this._subscriptionsByHash[e]=t,r!==t.state){let r=this._subscriptionStateChangeCallbacksByHash[e];r&&r.forEach(e=>{try{e(t.state)}catch{}})}}_onSubscriptionStateChange(e,t){let r=this._subscriptionHashByClientSubscriptionId[e];if(null==r)return()=>{};let i=this._subscriptionStateChangeCallbacksByHash[r]||=new Set;return i.add(t),()=>{i.delete(t),0===i.size&&delete this._subscriptionStateChangeCallbacksByHash[r]}}async _updateSubscriptions(){if(0===Object.keys(this._subscriptionsByHash).length){this._rpcWebSocketConnected&&(this._rpcWebSocketConnected=!1,this._rpcWebSocketIdleTimeout=setTimeout(()=>{this._rpcWebSocketIdleTimeout=null;try{this._rpcWebSocket.close()}catch(e){e instanceof Error&&console.log(`Error when closing socket connection: ${e.message}`)}},500));return}if(null!==this._rpcWebSocketIdleTimeout&&(clearTimeout(this._rpcWebSocketIdleTimeout),this._rpcWebSocketIdleTimeout=null,this._rpcWebSocketConnected=!0),!this._rpcWebSocketConnected)return void this._rpcWebSocket.connect();let e=this._rpcWebSocketGeneration,t=()=>e===this._rpcWebSocketGeneration;await Promise.all(Object.keys(this._subscriptionsByHash).map(async e=>{let r=this._subscriptionsByHash[e];if(void 0!==r)switch(r.state){case"pending":case"unsubscribed":if(0===r.callbacks.size){delete this._subscriptionsByHash[e],"unsubscribed"===r.state&&delete this._subscriptionCallbacksByServerSubscriptionId[r.serverSubscriptionId],await this._updateSubscriptions();return}await (async()=>{let{args:i,method:s}=r;try{this._setSubscription(e,{...r,state:"subscribing"});let t=await this._rpcWebSocket.call(s,i);this._setSubscription(e,{...r,serverSubscriptionId:t,state:"subscribed"}),this._subscriptionCallbacksByServerSubscriptionId[t]=r.callbacks,await this._updateSubscriptions()}catch(n){if(console.error(`Received ${n instanceof Error?"":"JSON-RPC "}error calling \`${s}\``,{args:i,error:n}),!t())return;this._setSubscription(e,{...r,state:"pending"}),await this._updateSubscriptions()}})();break;case"subscribed":0===r.callbacks.size&&await (async()=>{let{serverSubscriptionId:i,unsubscribeMethod:s}=r;if(this._subscriptionsAutoDisposedByRpc.has(i))this._subscriptionsAutoDisposedByRpc.delete(i);else{this._setSubscription(e,{...r,state:"unsubscribing"}),this._setSubscription(e,{...r,state:"unsubscribing"});try{await this._rpcWebSocket.call(s,[i])}catch(i){if(i instanceof Error&&console.error(`${s} error:`,i.message),!t())return;this._setSubscription(e,{...r,state:"subscribed"}),await this._updateSubscriptions();return}}this._setSubscription(e,{...r,state:"unsubscribed"}),await this._updateSubscriptions()})()}}))}_handleServerNotification(e,t){let r=this._subscriptionCallbacksByServerSubscriptionId[e];void 0!==r&&r.forEach(e=>{try{e(...t)}catch(e){console.error(e)}})}_wsOnAccountNotification(e){let{result:t,subscription:r}=(0,m.vt)(e,tT);this._handleServerNotification(r,[t.value,t.context])}_makeSubscription(e,t){let r=this._nextClientSubscriptionId++,i=eK([e.method,t]),s=this._subscriptionsByHash[i];return void 0===s?this._subscriptionsByHash[i]={...e,args:t,callbacks:new Set([e.callback]),state:"pending"}:s.callbacks.add(e.callback),this._subscriptionHashByClientSubscriptionId[r]=i,this._subscriptionDisposeFunctionsByClientSubscriptionId[r]=async()=>{delete this._subscriptionDisposeFunctionsByClientSubscriptionId[r],delete this._subscriptionHashByClientSubscriptionId[r];let t=this._subscriptionsByHash[i];J(void 0!==t,`Could not find a \`Subscription\` when tearing down client subscription #${r}`),t.callbacks.delete(e.callback),await this._updateSubscriptions()},this._updateSubscriptions(),r}onAccountChange(e,t,r){let{commitment:i,config:s}=eJ(r),n=this._buildArgs([e.toBase58()],i||this._commitment||"finalized","base64",s);return this._makeSubscription({callback:t,method:"accountSubscribe",unsubscribeMethod:"accountUnsubscribe"},n)}async removeAccountChangeListener(e){await this._unsubscribeClientSubscription(e,"account change")}_wsOnProgramAccountNotification(e){let{result:t,subscription:r}=(0,m.vt)(e,tO);this._handleServerNotification(r,[{accountId:t.value.pubkey,accountInfo:t.value.account},t.context])}onProgramAccountChange(e,t,r,i){let{commitment:s,config:n}=eJ(r),a=this._buildArgs([e.toBase58()],s||this._commitment||"finalized","base64",n||(i?{filters:eG(i)}:void 0));return this._makeSubscription({callback:t,method:"programSubscribe",unsubscribeMethod:"programUnsubscribe"},a)}async removeProgramAccountChangeListener(e){await this._unsubscribeClientSubscription(e,"program account change")}onLogs(e,t,r){let i=this._buildArgs(["object"==typeof e?{mentions:[e.toString()]}:e],r||this._commitment||"finalized");return this._makeSubscription({callback:t,method:"logsSubscribe",unsubscribeMethod:"logsUnsubscribe"},i)}async removeOnLogsListener(e){await this._unsubscribeClientSubscription(e,"logs")}_wsOnLogsNotification(e){let{result:t,subscription:r}=(0,m.vt)(e,ry);this._handleServerNotification(r,[t.value,t.context])}_wsOnSlotNotification(e){let{result:t,subscription:r}=(0,m.vt)(e,tC);this._handleServerNotification(r,[t])}onSlotChange(e){return this._makeSubscription({callback:e,method:"slotSubscribe",unsubscribeMethod:"slotUnsubscribe"},[])}async removeSlotChangeListener(e){await this._unsubscribeClientSubscription(e,"slot change")}_wsOnSlotUpdatesNotification(e){let{result:t,subscription:r}=(0,m.vt)(e,tL);this._handleServerNotification(r,[t])}onSlotUpdate(e){return this._makeSubscription({callback:e,method:"slotsUpdatesSubscribe",unsubscribeMethod:"slotsUpdatesUnsubscribe"},[])}async removeSlotUpdateListener(e){await this._unsubscribeClientSubscription(e,"slot update")}async _unsubscribeClientSubscription(e,t){let r=this._subscriptionDisposeFunctionsByClientSubscriptionId[e];r?await r():console.warn(`Ignored unsubscribe request because an active subscription with id \`${e}\` for '${t}' events could not be found.`)}_buildArgs(e,t,r,i){let s=t||this._commitment;if(s||r||i){let t={};r&&(t.encoding=r),s&&(t.commitment=s),i&&(t=Object.assign(t,i)),e.push(t)}return e}_buildArgsAtLeastConfirmed(e,t,r,i){let s=t||this._commitment;if(s&&!["confirmed","finalized"].includes(s))throw Error("Using Connection with default commitment: `"+this._commitment+"`, but method requires at least `confirmed`");return this._buildArgs(e,t,r,i)}_wsOnSignatureNotification(e){let{result:t,subscription:r}=(0,m.vt)(e,tK);"receivedSignature"!==t.value&&this._subscriptionsAutoDisposedByRpc.add(r),this._handleServerNotification(r,"receivedSignature"===t.value?[{type:"received"},t.context]:[{type:"status",result:t.value},t.context])}onSignature(e,t,r){let i=this._buildArgs([e],r||this._commitment||"finalized"),s=this._makeSubscription({callback:(e,r)=>{if("status"===e.type){t(e.result,r);try{this.removeSignatureListener(s)}catch(e){}}},method:"signatureSubscribe",unsubscribeMethod:"signatureUnsubscribe"},i);return s}onSignatureWithOptions(e,t,r){let{commitment:i,...s}={...r,commitment:r&&r.commitment||this._commitment||"finalized"},n=this._buildArgs([e],i,void 0,s),a=this._makeSubscription({callback:(e,r)=>{t(e,r);try{this.removeSignatureListener(a)}catch(e){}},method:"signatureSubscribe",unsubscribeMethod:"signatureUnsubscribe"},n);return a}async removeSignatureListener(e){await this._unsubscribeClientSubscription(e,"signature result")}_wsOnRootNotification(e){let{result:t,subscription:r}=(0,m.vt)(e,tz);this._handleServerNotification(r,[t])}onRootChange(e){return this._makeSubscription({callback:e,method:"rootSubscribe",unsubscribeMethod:"rootUnsubscribe"},[])}async removeRootChangeListener(e){await this._unsubscribeClientSubscription(e,"root change")}}class rk{constructor(e){this._keypair=void 0,this._keypair=e??I()}static generate(){return new rk(I())}static fromSecretKey(e,t){if(64!==e.byteLength)throw Error("bad secret key size");let r=e.slice(32,64);if(!t||!t.skipValidation){let t=_(e.slice(0,32));for(let e=0;e<32;e++)if(r[e]!==t[e])throw Error("provided secretKey is invalid")}return new rk({publicKey:r,secretKey:e})}static fromSeed(e){let t=_(e),r=new Uint8Array(64);return r.set(e),r.set(t,32),new rk({publicKey:t,secretKey:r})}get publicKey(){return new C(this._keypair.publicKey)}get secretKey(){return new Uint8Array(this._keypair.secretKey)}}let rw=Object.freeze({CreateLookupTable:{index:0,layout:g.w3([g.DH("instruction"),eW("recentSlot"),g.u8("bumpSeed")])},FreezeLookupTable:{index:1,layout:g.w3([g.DH("instruction")])},ExtendLookupTable:{index:2,layout:g.w3([g.DH("instruction"),eW(),g.O6(M(),g.cY(g.DH(),-8),"addresses")])},DeactivateLookupTable:{index:3,layout:g.w3([g.DH("instruction")])},CloseLookupTable:{index:4,layout:g.w3([g.DH("instruction")])}});class rS{constructor(){}static decodeInstructionType(e){let t;this.checkProgramId(e.programId);let r=g.DH("instruction").decode(e.data);for(let[e,i]of Object.entries(rw))if(i.index==r){t=e;break}if(!t)throw Error("Invalid Instruction. Should be a LookupTable Instruction");return t}static decodeCreateLookupTable(e){this.checkProgramId(e.programId),this.checkKeysLength(e.keys,4);let{recentSlot:t}=ev(rw.CreateLookupTable,e.data);return{authority:e.keys[1].pubkey,payer:e.keys[2].pubkey,recentSlot:Number(t)}}static decodeExtendLookupTable(e){if(this.checkProgramId(e.programId),e.keys.length<2)throw Error(`invalid instruction; found ${e.keys.length} keys, expected at least 2`);let{addresses:t}=ev(rw.ExtendLookupTable,e.data);return{lookupTable:e.keys[0].pubkey,authority:e.keys[1].pubkey,payer:e.keys.length>2?e.keys[2].pubkey:void 0,addresses:t.map(e=>new C(e))}}static decodeCloseLookupTable(e){return this.checkProgramId(e.programId),this.checkKeysLength(e.keys,3),{lookupTable:e.keys[0].pubkey,authority:e.keys[1].pubkey,recipient:e.keys[2].pubkey}}static decodeFreezeLookupTable(e){return this.checkProgramId(e.programId),this.checkKeysLength(e.keys,2),{lookupTable:e.keys[0].pubkey,authority:e.keys[1].pubkey}}static decodeDeactivateLookupTable(e){return this.checkProgramId(e.programId),this.checkKeysLength(e.keys,2),{lookupTable:e.keys[0].pubkey,authority:e.keys[1].pubkey}}static checkProgramId(e){if(!e.equals(rI.programId))throw Error("invalid instruction; programId is not AddressLookupTable Program")}static checkKeysLength(e,t){if(e.length<t)throw Error(`invalid instruction; found ${e.length} keys, expected at least ${t}`)}}class rI{constructor(){}static createLookupTable(e){let[t,r]=C.findProgramAddressSync([e.authority.toBuffer(),(0,p.eC)().encode(e.recentSlot)],this.programId),i=e_(rw.CreateLookupTable,{recentSlot:BigInt(e.recentSlot),bumpSeed:r}),s=[{pubkey:t,isSigner:!1,isWritable:!0},{pubkey:e.authority,isSigner:!0,isWritable:!1},{pubkey:e.payer,isSigner:!0,isWritable:!0},{pubkey:eO.programId,isSigner:!1,isWritable:!1}];return[new en({programId:this.programId,keys:s,data:i}),t]}static freezeLookupTable(e){let t=e_(rw.FreezeLookupTable),r=[{pubkey:e.lookupTable,isSigner:!1,isWritable:!0},{pubkey:e.authority,isSigner:!0,isWritable:!1}];return new en({programId:this.programId,keys:r,data:t})}static extendLookupTable(e){let t=e_(rw.ExtendLookupTable,{addresses:e.addresses.map(e=>e.toBytes())}),r=[{pubkey:e.lookupTable,isSigner:!1,isWritable:!0},{pubkey:e.authority,isSigner:!0,isWritable:!1}];return e.payer&&r.push({pubkey:e.payer,isSigner:!0,isWritable:!0},{pubkey:eO.programId,isSigner:!1,isWritable:!1}),new en({programId:this.programId,keys:r,data:t})}static deactivateLookupTable(e){let t=e_(rw.DeactivateLookupTable),r=[{pubkey:e.lookupTable,isSigner:!1,isWritable:!0},{pubkey:e.authority,isSigner:!0,isWritable:!1}];return new en({programId:this.programId,keys:r,data:t})}static closeLookupTable(e){let t=e_(rw.CloseLookupTable),r=[{pubkey:e.lookupTable,isSigner:!1,isWritable:!0},{pubkey:e.authority,isSigner:!0,isWritable:!1},{pubkey:e.recipient,isSigner:!1,isWritable:!0}];return new en({programId:this.programId,keys:r,data:t})}}rI.programId=new C("AddressLookupTab1e1111111111111111111111111");class r_{constructor(){}static decodeInstructionType(e){let t;this.checkProgramId(e.programId);let r=g.u8("instruction").decode(e.data);for(let[e,i]of Object.entries(rv))if(i.index==r){t=e;break}if(!t)throw Error("Instruction type incorrect; not a ComputeBudgetInstruction");return t}static decodeRequestUnits(e){this.checkProgramId(e.programId);let{units:t,additionalFee:r}=ev(rv.RequestUnits,e.data);return{units:t,additionalFee:r}}static decodeRequestHeapFrame(e){this.checkProgramId(e.programId);let{bytes:t}=ev(rv.RequestHeapFrame,e.data);return{bytes:t}}static decodeSetComputeUnitLimit(e){this.checkProgramId(e.programId);let{units:t}=ev(rv.SetComputeUnitLimit,e.data);return{units:t}}static decodeSetComputeUnitPrice(e){this.checkProgramId(e.programId);let{microLamports:t}=ev(rv.SetComputeUnitPrice,e.data);return{microLamports:t}}static checkProgramId(e){if(!e.equals(rA.programId))throw Error("invalid instruction; programId is not ComputeBudgetProgram")}}let rv=Object.freeze({RequestUnits:{index:0,layout:g.w3([g.u8("instruction"),g.DH("units"),g.DH("additionalFee")])},RequestHeapFrame:{index:1,layout:g.w3([g.u8("instruction"),g.DH("bytes")])},SetComputeUnitLimit:{index:2,layout:g.w3([g.u8("instruction"),g.DH("units")])},SetComputeUnitPrice:{index:3,layout:g.w3([g.u8("instruction"),eW("microLamports")])}});class rA{constructor(){}static requestUnits(e){let t=e_(rv.RequestUnits,e);return new en({keys:[],programId:this.programId,data:t})}static requestHeapFrame(e){let t=e_(rv.RequestHeapFrame,e);return new en({keys:[],programId:this.programId,data:t})}static setComputeUnitLimit(e){let t=e_(rv.SetComputeUnitLimit,e);return new en({keys:[],programId:this.programId,data:t})}static setComputeUnitPrice(e){let t=e_(rv.SetComputeUnitPrice,{microLamports:BigInt(e.microLamports)});return new en({keys:[],programId:this.programId,data:t})}}rA.programId=new C("ComputeBudget111111111111111111111111111111");let rP=g.w3([g.u8("numSignatures"),g.u8("padding"),g.NX("signatureOffset"),g.NX("signatureInstructionIndex"),g.NX("publicKeyOffset"),g.NX("publicKeyInstructionIndex"),g.NX("messageDataOffset"),g.NX("messageDataSize"),g.NX("messageInstructionIndex")]);class rE{constructor(){}static createInstructionWithPublicKey(e){let{publicKey:t,message:r,signature:i,instructionIndex:s}=e;J(32===t.length,`Public Key must be 32 bytes but received ${t.length} bytes`),J(64===i.length,`Signature must be 64 bytes but received ${i.length} bytes`);let a=rP.span,o=a+t.length,c=o+i.length,u=n.Buffer.alloc(c+r.length),l=null==s?65535:s;return rP.encode({numSignatures:1,padding:0,signatureOffset:o,signatureInstructionIndex:l,publicKeyOffset:a,publicKeyInstructionIndex:l,messageDataOffset:c,messageDataSize:r.length,messageInstructionIndex:l},u),u.fill(t,a),u.fill(i,o),u.fill(r,c),new en({keys:[],programId:rE.programId,data:u})}static createInstructionWithPrivateKey(e){let{privateKey:t,message:r,instructionIndex:i}=e;J(64===t.length,`Private key must be 64 bytes but received ${t.length} bytes`);try{let e=rk.fromSecretKey(t),s=e.publicKey.toBytes(),n=A(r,e.secretKey);return this.createInstructionWithPublicKey({publicKey:s,message:r,signature:n,instructionIndex:i})}catch(e){throw Error(`Error creating instruction; ${e}`)}}}rE.programId=new C("Ed25519SigVerify111111111111111111111111111");let rB=(e,t)=>{let r=w.bI.sign(e,t);return[r.toCompactRawBytes(),r.recovery]};w.bI.utils.isValidPrivateKey;let rW=w.bI.getPublicKey,rT=g.w3([g.u8("numSignatures"),g.NX("signatureOffset"),g.u8("signatureInstructionIndex"),g.NX("ethAddressOffset"),g.u8("ethAddressInstructionIndex"),g.NX("messageDataOffset"),g.NX("messageDataSize"),g.u8("messageInstructionIndex"),g.av(20,"ethAddress"),g.av(64,"signature"),g.u8("recoveryId")]);class rN{constructor(){}static publicKeyToEthAddress(e){J(64===e.length,`Public key must be 64 bytes but received ${e.length} bytes`);try{return n.Buffer.from((0,k.lY)(E(e))).slice(-20)}catch(e){throw Error(`Error constructing Ethereum address: ${e}`)}}static createInstructionWithPublicKey(e){let{publicKey:t,message:r,signature:i,recoveryId:s,instructionIndex:n}=e;return rN.createInstructionWithEthAddress({ethAddress:rN.publicKeyToEthAddress(t),message:r,signature:i,recoveryId:s,instructionIndex:n})}static createInstructionWithEthAddress(e){let t,{ethAddress:r,message:i,signature:s,recoveryId:a,instructionIndex:o=0}=e;J(20===(t="string"==typeof r?r.startsWith("0x")?n.Buffer.from(r.substr(2),"hex"):n.Buffer.from(r,"hex"):r).length,`Address must be 20 bytes but received ${t.length} bytes`);let c=12+t.length,u=c+s.length+1,l=n.Buffer.alloc(rT.span+i.length);return rT.encode({numSignatures:1,signatureOffset:c,signatureInstructionIndex:o,ethAddressOffset:12,ethAddressInstructionIndex:o,messageDataOffset:u,messageDataSize:i.length,messageInstructionIndex:o,signature:E(s),ethAddress:E(t),recoveryId:a},l),l.fill(E(i),rT.span),new en({keys:[],programId:rN.programId,data:l})}static createInstructionWithPrivateKey(e){let{privateKey:t,message:r,instructionIndex:i}=e;J(32===t.length,`Private key must be 32 bytes but received ${t.length} bytes`);try{let e=E(t),s=rW(e,!1).slice(1),a=n.Buffer.from((0,k.lY)(E(r))),[o,c]=rB(a,e);return this.createInstructionWithPublicKey({publicKey:s,message:r,signature:o,recoveryId:c,instructionIndex:i})}catch(e){throw Error(`Error creating instruction; ${e}`)}}}rN.programId=new C("KeccakSecp256k11111111111111111111111111111");let rO=new C("StakeConfig11111111111111111111111111111111");class rR{constructor(e,t){this.staker=void 0,this.withdrawer=void 0,this.staker=e,this.withdrawer=t}}class rC{constructor(e,t,r){this.unixTimestamp=void 0,this.epoch=void 0,this.custodian=void 0,this.unixTimestamp=e,this.epoch=t,this.custodian=r}}rC.default=new rC(0,0,C.default);class rx{constructor(){}static decodeInstructionType(e){let t;this.checkProgramId(e.programId);let r=g.DH("instruction").decode(e.data);for(let[e,i]of Object.entries(rL))if(i.index==r){t=e;break}if(!t)throw Error("Instruction type incorrect; not a StakeInstruction");return t}static decodeInitialize(e){this.checkProgramId(e.programId),this.checkKeyLength(e.keys,2);let{authorized:t,lockup:r}=ev(rL.Initialize,e.data);return{stakePubkey:e.keys[0].pubkey,authorized:new rR(new C(t.staker),new C(t.withdrawer)),lockup:new rC(r.unixTimestamp,r.epoch,new C(r.custodian))}}static decodeDelegate(e){return this.checkProgramId(e.programId),this.checkKeyLength(e.keys,6),ev(rL.Delegate,e.data),{stakePubkey:e.keys[0].pubkey,votePubkey:e.keys[1].pubkey,authorizedPubkey:e.keys[5].pubkey}}static decodeAuthorize(e){this.checkProgramId(e.programId),this.checkKeyLength(e.keys,3);let{newAuthorized:t,stakeAuthorizationType:r}=ev(rL.Authorize,e.data),i={stakePubkey:e.keys[0].pubkey,authorizedPubkey:e.keys[2].pubkey,newAuthorizedPubkey:new C(t),stakeAuthorizationType:{index:r}};return e.keys.length>3&&(i.custodianPubkey=e.keys[3].pubkey),i}static decodeAuthorizeWithSeed(e){this.checkProgramId(e.programId),this.checkKeyLength(e.keys,2);let{newAuthorized:t,stakeAuthorizationType:r,authoritySeed:i,authorityOwner:s}=ev(rL.AuthorizeWithSeed,e.data),n={stakePubkey:e.keys[0].pubkey,authorityBase:e.keys[1].pubkey,authoritySeed:i,authorityOwner:new C(s),newAuthorizedPubkey:new C(t),stakeAuthorizationType:{index:r}};return e.keys.length>3&&(n.custodianPubkey=e.keys[3].pubkey),n}static decodeSplit(e){this.checkProgramId(e.programId),this.checkKeyLength(e.keys,3);let{lamports:t}=ev(rL.Split,e.data);return{stakePubkey:e.keys[0].pubkey,splitStakePubkey:e.keys[1].pubkey,authorizedPubkey:e.keys[2].pubkey,lamports:t}}static decodeMerge(e){return this.checkProgramId(e.programId),this.checkKeyLength(e.keys,3),ev(rL.Merge,e.data),{stakePubkey:e.keys[0].pubkey,sourceStakePubKey:e.keys[1].pubkey,authorizedPubkey:e.keys[4].pubkey}}static decodeWithdraw(e){this.checkProgramId(e.programId),this.checkKeyLength(e.keys,5);let{lamports:t}=ev(rL.Withdraw,e.data),r={stakePubkey:e.keys[0].pubkey,toPubkey:e.keys[1].pubkey,authorizedPubkey:e.keys[4].pubkey,lamports:t};return e.keys.length>5&&(r.custodianPubkey=e.keys[5].pubkey),r}static decodeDeactivate(e){return this.checkProgramId(e.programId),this.checkKeyLength(e.keys,3),ev(rL.Deactivate,e.data),{stakePubkey:e.keys[0].pubkey,authorizedPubkey:e.keys[2].pubkey}}static checkProgramId(e){if(!e.equals(rz.programId))throw Error("invalid instruction; programId is not StakeProgram")}static checkKeyLength(e,t){if(e.length<t)throw Error(`invalid instruction; found ${e.length} keys, expected at least ${t}`)}}let rL=Object.freeze({Initialize:{index:0,layout:g.w3([g.DH("instruction"),((e="authorized")=>g.w3([M("staker"),M("withdrawer")],e))(),((e="lockup")=>g.w3([g.Wg("unixTimestamp"),g.Wg("epoch"),M("custodian")],e))()])},Authorize:{index:1,layout:g.w3([g.DH("instruction"),M("newAuthorized"),g.DH("stakeAuthorizationType")])},Delegate:{index:2,layout:g.w3([g.DH("instruction")])},Split:{index:3,layout:g.w3([g.DH("instruction"),g.Wg("lamports")])},Withdraw:{index:4,layout:g.w3([g.DH("instruction"),g.Wg("lamports")])},Deactivate:{index:5,layout:g.w3([g.DH("instruction")])},Merge:{index:7,layout:g.w3([g.DH("instruction")])},AuthorizeWithSeed:{index:8,layout:g.w3([g.DH("instruction"),M("newAuthorized"),g.DH("stakeAuthorizationType"),V("authoritySeed"),M("authorityOwner")])}}),rK=Object.freeze({Staker:{index:0},Withdrawer:{index:1}});class rz{constructor(){}static initialize(e){let{stakePubkey:t,authorized:r,lockup:i}=e,s=i||rC.default,n=e_(rL.Initialize,{authorized:{staker:E(r.staker.toBuffer()),withdrawer:E(r.withdrawer.toBuffer())},lockup:{unixTimestamp:s.unixTimestamp,epoch:s.epoch,custodian:E(s.custodian.toBuffer())}});return new en({keys:[{pubkey:t,isSigner:!1,isWritable:!0},{pubkey:eg,isSigner:!1,isWritable:!1}],programId:this.programId,data:n})}static createAccountWithSeed(e){let t=new ea;t.add(eO.createAccountWithSeed({fromPubkey:e.fromPubkey,newAccountPubkey:e.stakePubkey,basePubkey:e.basePubkey,seed:e.seed,lamports:e.lamports,space:this.space,programId:this.programId}));let{stakePubkey:r,authorized:i,lockup:s}=e;return t.add(this.initialize({stakePubkey:r,authorized:i,lockup:s}))}static createAccount(e){let t=new ea;t.add(eO.createAccount({fromPubkey:e.fromPubkey,newAccountPubkey:e.stakePubkey,lamports:e.lamports,space:this.space,programId:this.programId}));let{stakePubkey:r,authorized:i,lockup:s}=e;return t.add(this.initialize({stakePubkey:r,authorized:i,lockup:s}))}static delegate(e){let{stakePubkey:t,authorizedPubkey:r,votePubkey:i}=e,s=e_(rL.Delegate);return new ea().add({keys:[{pubkey:t,isSigner:!1,isWritable:!0},{pubkey:i,isSigner:!1,isWritable:!1},{pubkey:eu,isSigner:!1,isWritable:!1},{pubkey:eb,isSigner:!1,isWritable:!1},{pubkey:rO,isSigner:!1,isWritable:!1},{pubkey:r,isSigner:!0,isWritable:!1}],programId:this.programId,data:s})}static authorize(e){let{stakePubkey:t,authorizedPubkey:r,newAuthorizedPubkey:i,stakeAuthorizationType:s,custodianPubkey:n}=e,a=e_(rL.Authorize,{newAuthorized:E(i.toBuffer()),stakeAuthorizationType:s.index}),o=[{pubkey:t,isSigner:!1,isWritable:!0},{pubkey:eu,isSigner:!1,isWritable:!0},{pubkey:r,isSigner:!0,isWritable:!1}];return n&&o.push({pubkey:n,isSigner:!0,isWritable:!1}),new ea().add({keys:o,programId:this.programId,data:a})}static authorizeWithSeed(e){let{stakePubkey:t,authorityBase:r,authoritySeed:i,authorityOwner:s,newAuthorizedPubkey:n,stakeAuthorizationType:a,custodianPubkey:o}=e,c=e_(rL.AuthorizeWithSeed,{newAuthorized:E(n.toBuffer()),stakeAuthorizationType:a.index,authoritySeed:i,authorityOwner:E(s.toBuffer())}),u=[{pubkey:t,isSigner:!1,isWritable:!0},{pubkey:r,isSigner:!0,isWritable:!1},{pubkey:eu,isSigner:!1,isWritable:!1}];return o&&u.push({pubkey:o,isSigner:!0,isWritable:!1}),new ea().add({keys:u,programId:this.programId,data:c})}static splitInstruction(e){let{stakePubkey:t,authorizedPubkey:r,splitStakePubkey:i,lamports:s}=e,n=e_(rL.Split,{lamports:s});return new en({keys:[{pubkey:t,isSigner:!1,isWritable:!0},{pubkey:i,isSigner:!1,isWritable:!0},{pubkey:r,isSigner:!0,isWritable:!1}],programId:this.programId,data:n})}static split(e,t){let r=new ea;return r.add(eO.createAccount({fromPubkey:e.authorizedPubkey,newAccountPubkey:e.splitStakePubkey,lamports:t,space:this.space,programId:this.programId})),r.add(this.splitInstruction(e))}static splitWithSeed(e,t){let{stakePubkey:r,authorizedPubkey:i,splitStakePubkey:s,basePubkey:n,seed:a,lamports:o}=e,c=new ea;return c.add(eO.allocate({accountPubkey:s,basePubkey:n,seed:a,space:this.space,programId:this.programId})),t&&t>0&&c.add(eO.transfer({fromPubkey:e.authorizedPubkey,toPubkey:s,lamports:t})),c.add(this.splitInstruction({stakePubkey:r,authorizedPubkey:i,splitStakePubkey:s,lamports:o}))}static merge(e){let{stakePubkey:t,sourceStakePubKey:r,authorizedPubkey:i}=e,s=e_(rL.Merge);return new ea().add({keys:[{pubkey:t,isSigner:!1,isWritable:!0},{pubkey:r,isSigner:!1,isWritable:!0},{pubkey:eu,isSigner:!1,isWritable:!1},{pubkey:eb,isSigner:!1,isWritable:!1},{pubkey:i,isSigner:!0,isWritable:!1}],programId:this.programId,data:s})}static withdraw(e){let{stakePubkey:t,authorizedPubkey:r,toPubkey:i,lamports:s,custodianPubkey:n}=e,a=e_(rL.Withdraw,{lamports:s}),o=[{pubkey:t,isSigner:!1,isWritable:!0},{pubkey:i,isSigner:!1,isWritable:!0},{pubkey:eu,isSigner:!1,isWritable:!1},{pubkey:eb,isSigner:!1,isWritable:!1},{pubkey:r,isSigner:!0,isWritable:!1}];return n&&o.push({pubkey:n,isSigner:!0,isWritable:!1}),new ea().add({keys:o,programId:this.programId,data:a})}static deactivate(e){let{stakePubkey:t,authorizedPubkey:r}=e,i=e_(rL.Deactivate);return new ea().add({keys:[{pubkey:t,isSigner:!1,isWritable:!0},{pubkey:eu,isSigner:!1,isWritable:!1},{pubkey:r,isSigner:!0,isWritable:!1}],programId:this.programId,data:i})}}rz.programId=new C("Stake11111111111111111111111111111111111111"),rz.space=200;class rY{constructor(e,t,r,i){this.nodePubkey=void 0,this.authorizedVoter=void 0,this.authorizedWithdrawer=void 0,this.commission=void 0,this.nodePubkey=e,this.authorizedVoter=t,this.authorizedWithdrawer=r,this.commission=i}}class rq{constructor(){}static decodeInstructionType(e){let t;this.checkProgramId(e.programId);let r=g.DH("instruction").decode(e.data);for(let[e,i]of Object.entries(rD))if(i.index==r){t=e;break}if(!t)throw Error("Instruction type incorrect; not a VoteInstruction");return t}static decodeInitializeAccount(e){this.checkProgramId(e.programId),this.checkKeyLength(e.keys,4);let{voteInit:t}=ev(rD.InitializeAccount,e.data);return{votePubkey:e.keys[0].pubkey,nodePubkey:e.keys[3].pubkey,voteInit:new rY(new C(t.nodePubkey),new C(t.authorizedVoter),new C(t.authorizedWithdrawer),t.commission)}}static decodeAuthorize(e){this.checkProgramId(e.programId),this.checkKeyLength(e.keys,3);let{newAuthorized:t,voteAuthorizationType:r}=ev(rD.Authorize,e.data);return{votePubkey:e.keys[0].pubkey,authorizedPubkey:e.keys[2].pubkey,newAuthorizedPubkey:new C(t),voteAuthorizationType:{index:r}}}static decodeAuthorizeWithSeed(e){this.checkProgramId(e.programId),this.checkKeyLength(e.keys,3);let{voteAuthorizeWithSeedArgs:{currentAuthorityDerivedKeyOwnerPubkey:t,currentAuthorityDerivedKeySeed:r,newAuthorized:i,voteAuthorizationType:s}}=ev(rD.AuthorizeWithSeed,e.data);return{currentAuthorityDerivedKeyBasePubkey:e.keys[2].pubkey,currentAuthorityDerivedKeyOwnerPubkey:new C(t),currentAuthorityDerivedKeySeed:r,newAuthorizedPubkey:new C(i),voteAuthorizationType:{index:s},votePubkey:e.keys[0].pubkey}}static decodeWithdraw(e){this.checkProgramId(e.programId),this.checkKeyLength(e.keys,3);let{lamports:t}=ev(rD.Withdraw,e.data);return{votePubkey:e.keys[0].pubkey,authorizedWithdrawerPubkey:e.keys[2].pubkey,lamports:t,toPubkey:e.keys[1].pubkey}}static checkProgramId(e){if(!e.equals(rj.programId))throw Error("invalid instruction; programId is not VoteProgram")}static checkKeyLength(e,t){if(e.length<t)throw Error(`invalid instruction; found ${e.length} keys, expected at least ${t}`)}}let rD=Object.freeze({InitializeAccount:{index:0,layout:g.w3([g.DH("instruction"),((e="voteInit")=>g.w3([M("nodePubkey"),M("authorizedVoter"),M("authorizedWithdrawer"),g.u8("commission")],e))()])},Authorize:{index:1,layout:g.w3([g.DH("instruction"),M("newAuthorized"),g.DH("voteAuthorizationType")])},Withdraw:{index:3,layout:g.w3([g.DH("instruction"),g.Wg("lamports")])},UpdateValidatorIdentity:{index:4,layout:g.w3([g.DH("instruction")])},AuthorizeWithSeed:{index:10,layout:g.w3([g.DH("instruction"),((e="voteAuthorizeWithSeedArgs")=>g.w3([g.DH("voteAuthorizationType"),M("currentAuthorityDerivedKeyOwnerPubkey"),V("currentAuthorityDerivedKeySeed"),M("newAuthorized")],e))()])}}),rH=Object.freeze({Voter:{index:0},Withdrawer:{index:1}});class rj{constructor(){}static initializeAccount(e){let{votePubkey:t,nodePubkey:r,voteInit:i}=e,s=e_(rD.InitializeAccount,{voteInit:{nodePubkey:E(i.nodePubkey.toBuffer()),authorizedVoter:E(i.authorizedVoter.toBuffer()),authorizedWithdrawer:E(i.authorizedWithdrawer.toBuffer()),commission:i.commission}});return new en({keys:[{pubkey:t,isSigner:!1,isWritable:!0},{pubkey:eg,isSigner:!1,isWritable:!1},{pubkey:eu,isSigner:!1,isWritable:!1},{pubkey:r,isSigner:!0,isWritable:!1}],programId:this.programId,data:s})}static createAccount(e){let t=new ea;return t.add(eO.createAccount({fromPubkey:e.fromPubkey,newAccountPubkey:e.votePubkey,lamports:e.lamports,space:this.space,programId:this.programId})),t.add(this.initializeAccount({votePubkey:e.votePubkey,nodePubkey:e.voteInit.nodePubkey,voteInit:e.voteInit}))}static authorize(e){let{votePubkey:t,authorizedPubkey:r,newAuthorizedPubkey:i,voteAuthorizationType:s}=e,n=e_(rD.Authorize,{newAuthorized:E(i.toBuffer()),voteAuthorizationType:s.index});return new ea().add({keys:[{pubkey:t,isSigner:!1,isWritable:!0},{pubkey:eu,isSigner:!1,isWritable:!1},{pubkey:r,isSigner:!0,isWritable:!1}],programId:this.programId,data:n})}static authorizeWithSeed(e){let{currentAuthorityDerivedKeyBasePubkey:t,currentAuthorityDerivedKeyOwnerPubkey:r,currentAuthorityDerivedKeySeed:i,newAuthorizedPubkey:s,voteAuthorizationType:n,votePubkey:a}=e,o=e_(rD.AuthorizeWithSeed,{voteAuthorizeWithSeedArgs:{currentAuthorityDerivedKeyOwnerPubkey:E(r.toBuffer()),currentAuthorityDerivedKeySeed:i,newAuthorized:E(s.toBuffer()),voteAuthorizationType:n.index}});return new ea().add({keys:[{pubkey:a,isSigner:!1,isWritable:!0},{pubkey:eu,isSigner:!1,isWritable:!1},{pubkey:t,isSigner:!0,isWritable:!1}],programId:this.programId,data:o})}static withdraw(e){let{votePubkey:t,authorizedWithdrawerPubkey:r,lamports:i,toPubkey:s}=e,n=e_(rD.Withdraw,{lamports:i});return new ea().add({keys:[{pubkey:t,isSigner:!1,isWritable:!0},{pubkey:s,isSigner:!1,isWritable:!0},{pubkey:r,isSigner:!0,isWritable:!1}],programId:this.programId,data:n})}static safeWithdraw(e,t,r){if(e.lamports>t-r)throw Error("Withdraw will leave vote account with insufficient funds.");return rj.withdraw(e)}static updateValidatorIdentity(e){let{votePubkey:t,authorizedWithdrawerPubkey:r,nodePubkey:i}=e,s=e_(rD.UpdateValidatorIdentity);return new ea().add({keys:[{pubkey:t,isSigner:!1,isWritable:!0},{pubkey:i,isSigner:!0,isWritable:!1},{pubkey:r,isSigner:!0,isWritable:!1}],programId:this.programId,data:s})}}rj.programId=new C("Vote111111111111111111111111111111111111111"),rj.space=3762;let rM=new C("Va1idator1nfo111111111111111111111111111111"),rU=(0,m.NW)({name:(0,m.Yj)(),website:(0,m.lq)((0,m.Yj)()),details:(0,m.lq)((0,m.Yj)()),iconUrl:(0,m.lq)((0,m.Yj)()),keybaseUsername:(0,m.lq)((0,m.Yj)())});class rV{constructor(e,t){this.key=void 0,this.info=void 0,this.key=e,this.info=t}static fromConfigData(e){let t=[...e];if(2!==$(t))return null;let r=[];for(let e=0;e<2;e++){let e=new C(Q(t,0,O)),i=1===Z(t);r.push({publicKey:e,isSigner:i})}if(r[0].publicKey.equals(rM)&&r[1].isSigner){let e=JSON.parse(V().decode(n.Buffer.from(t)));return(0,m.vA)(e,rU),new rV(r[1].publicKey,e)}return null}}let r$=new C("Vote111111111111111111111111111111111111111"),rF=g.w3([M("nodePubkey"),M("authorizedWithdrawer"),g.u8("commission"),g.I0(),g.O6(g.w3([g.I0("slot"),g.DH("confirmationCount")]),g.cY(g.DH(),-8),"votes"),g.u8("rootSlotValid"),g.I0("rootSlot"),g.I0(),g.O6(g.w3([g.I0("epoch"),M("authorizedVoter")]),g.cY(g.DH(),-8),"authorizedVoters"),g.w3([g.O6(g.w3([M("authorizedPubkey"),g.I0("epochOfLastAuthorizedSwitch"),g.I0("targetEpoch")]),32,"buf"),g.I0("idx"),g.u8("isEmpty")],"priorVoters"),g.I0(),g.O6(g.w3([g.I0("epoch"),g.I0("credits"),g.I0("prevCredits")]),g.cY(g.DH(),-8),"epochCredits"),g.w3([g.I0("slot"),g.I0("timestamp")],"lastTimestamp")]);class rJ{constructor(e){this.nodePubkey=void 0,this.authorizedWithdrawer=void 0,this.commission=void 0,this.rootSlot=void 0,this.votes=void 0,this.authorizedVoters=void 0,this.priorVoters=void 0,this.epochCredits=void 0,this.lastTimestamp=void 0,this.nodePubkey=e.nodePubkey,this.authorizedWithdrawer=e.authorizedWithdrawer,this.commission=e.commission,this.rootSlot=e.rootSlot,this.votes=e.votes,this.authorizedVoters=e.authorizedVoters,this.priorVoters=e.priorVoters,this.epochCredits=e.epochCredits,this.lastTimestamp=e.lastTimestamp}static fromAccountData(e){let t=rF.decode(E(e),4),r=t.rootSlot;return t.rootSlotValid||(r=null),new rJ({nodePubkey:new C(t.nodePubkey),authorizedWithdrawer:new C(t.authorizedWithdrawer),commission:t.commission,votes:t.votes,rootSlot:r,authorizedVoters:t.authorizedVoters.map(rG),priorVoters:function({buf:e,idx:t,isEmpty:r}){return r?[]:[...e.slice(t+1).map(rX),...e.slice(0,t).map(rX)]}(t.priorVoters),epochCredits:t.epochCredits,lastTimestamp:t.lastTimestamp})}}function rG({authorizedVoter:e,epoch:t}){return{epoch:t,authorizedVoter:new C(e)}}function rX({authorizedPubkey:e,epochOfLastAuthorizedSwitch:t,targetEpoch:r}){return{authorizedPubkey:new C(e),epochOfLastAuthorizedSwitch:t,targetEpoch:r}}let rZ={http:{devnet:"http://api.devnet.solana.com",testnet:"http://api.testnet.solana.com","mainnet-beta":"http://api.mainnet-beta.solana.com/"},https:{devnet:"https://api.devnet.solana.com",testnet:"https://api.testnet.solana.com","mainnet-beta":"https://api.mainnet-beta.solana.com/"}};function rQ(e,t){let r=!1===t?"http":"https";if(!e)return rZ[r].devnet;let i=rZ[r][e];if(!i)throw Error(`Unknown ${r} cluster: ${e}`);return i}async function r0(e,t,r,i){let s,n;r&&Object.prototype.hasOwnProperty.call(r,"lastValidBlockHeight")||r&&Object.prototype.hasOwnProperty.call(r,"nonceValue")?(s=r,n=i):n=r;let a=n&&{skipPreflight:n.skipPreflight,preflightCommitment:n.preflightCommitment||n.commitment,minContextSlot:n.minContextSlot},o=await e.sendRawTransaction(t,a),c=n&&n.commitment,u=s?e.confirmTransaction(s,c):e.confirmTransaction(o,c),l=(await u).value;if(l.err){if(null!=o)throw new ef({action:a?.skipPreflight?"send":"simulate",signature:o,transactionMessage:`Status: (${JSON.stringify(l)})`});throw Error(`Raw transaction ${o} failed (${JSON.stringify(l)})`)}return o}let r1=1e9}}]);