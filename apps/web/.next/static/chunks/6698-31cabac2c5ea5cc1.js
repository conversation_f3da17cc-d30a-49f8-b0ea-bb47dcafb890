"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6698],{4908:(e,t,r)=>{r.d(t,{B:()=>g,I:()=>R,O:()=>s,S:()=>H,U:()=>o,a:()=>f,b:()=>l,c:()=>N,d:()=>$,e:()=>c,f:()=>B,g:()=>X,i:()=>h,j:()=>J,m:()=>d,n:()=>F,o:()=>z,r:()=>C,s:()=>P,t:()=>j,u:()=>L,z:()=>S});var n=r(41987),i=r(17967),a=Object.prototype.hasOwnProperty;let l=new WeakMap,u=()=>{},o=u(),s=Object,c=e=>e===o,f=e=>"function"==typeof e,d=(e,t)=>({...e,...t}),g=e=>f(e.then),y={},p={},b="undefined",h=typeof window!=b,v=typeof document!=b,w=h&&"Deno"in window,m=()=>h&&typeof window.requestAnimationFrame!=b,S=(e,t)=>{let r=l.get(e);return[()=>!c(t)&&e.get(t)||y,n=>{if(!c(t)){let i=e.get(t);t in p||(p[t]=i),r[5](t,d(i,n),i||y)}},r[6],()=>!c(t)&&t in p?p[t]:!c(t)&&e.get(t)||y]},O=!0,[_,E]=h&&window.addEventListener?[window.addEventListener.bind(window),window.removeEventListener.bind(window)]:[u,u],k={initFocus:e=>(v&&document.addEventListener("visibilitychange",e),_("focus",e),()=>{v&&document.removeEventListener("visibilitychange",e),E("focus",e)}),initReconnect:e=>{let t=()=>{O=!0,e()},r=()=>{O=!1};return _("online",t),_("offline",r),()=>{E("online",t),E("offline",r)}}},R=!n.useId,C=!h||w,j=e=>m()?window.requestAnimationFrame(e):setTimeout(e,1),L=C?n.useEffect:n.useLayoutEffect,T="undefined"!=typeof navigator&&navigator.connection,V=!C&&T&&(["slow-2g","2g"].includes(T.effectiveType)||T.saveData),A=new WeakMap,D=e=>s.prototype.toString.call(e),I=(e,t)=>e==="[object ".concat(t,"]"),x=0,q=e=>{let t,r,n=typeof e,i=D(e),a=I(i,"Date"),l=I(i,"RegExp"),u=I(i,"Object");if(s(e)!==e||a||l)t=a?e.toJSON():"symbol"==n?e.toString():"string"==n?JSON.stringify(e):""+e;else{if(t=A.get(e))return t;if(t=++x+"~",A.set(e,t),Array.isArray(e)){for(r=0,t="@";r<e.length;r++)t+=q(e[r])+",";A.set(e,t)}if(u){t="#";let n=s.keys(e).sort();for(;!c(r=n.pop());)c(e[r])||(t+=r+":"+q(e[r])+",");A.set(e,t)}}return t},P=e=>{if(f(e))try{e=e()}catch(t){e=""}let t=e;return[e="string"==typeof e?e:(Array.isArray(e)?e.length:e)?q(e):"",t]},W=0,z=()=>++W;async function F(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let[n,a,u,s]=t,y=d({populateCache:!0,throwOnError:!0},"boolean"==typeof s?{revalidate:s}:s||{}),p=y.populateCache,b=y.rollbackOnError,h=y.optimisticData,v=e=>"function"==typeof b?b(e):!1!==b,w=y.throwOnError;if(f(a)){let e=[];for(let t of n.keys())!/^\$(inf|sub)\$/.test(t)&&a(n.get(t)._k)&&e.push(t);return Promise.all(e.map(m))}return m(a);async function m(e){let r,[a]=P(e);if(!a)return;let[s,d]=S(n,a),[b,m,O,_]=l.get(n),E=()=>{let t=b[a];return(f(y.revalidate)?y.revalidate(s().data,e):!1!==y.revalidate)&&(delete O[a],delete _[a],t&&t[0])?t[0](i.q2).then(()=>s().data):s().data};if(t.length<3)return E();let k=u,R=!1,C=z();m[a]=[C,0];let j=!c(h),L=s(),T=L.data,V=L._c,A=c(V)?T:V;if(j&&d({data:h=f(h)?h(A,T):h,_c:A}),f(k))try{k=k(A)}catch(e){r=e,R=!0}if(k&&g(k)){if(k=await k.catch(e=>{r=e,R=!0}),C!==m[a][0]){if(R)throw r;return k}R&&j&&v(r)&&(p=!0,d({data:A,_c:o}))}if(p&&!R&&(f(p)?d({data:p(k,A),error:o,_c:o}):d({data:k,error:o,_c:o})),m[a][1]=z(),Promise.resolve(E()).then(()=>{d({_c:o})}),R){if(w)throw r;return}return k}}let M=(e,t)=>{for(let r in e)e[r][0]&&e[r][0](t)},U=(e,t)=>{if(!l.has(e)){let r=d(k,t),n=Object.create(null),a=F.bind(o,e),s=u,c=Object.create(null),f=(e,t)=>{let r=c[e]||[];return c[e]=r,r.push(t),()=>r.splice(r.indexOf(t),1)},g=(t,r,n)=>{e.set(t,r);let i=c[t];if(i)for(let e of i)e(r,n)},y=()=>{if(!l.has(e)&&(l.set(e,[n,Object.create(null),Object.create(null),Object.create(null),a,g,f]),!C)){let t=r.initFocus(setTimeout.bind(o,M.bind(o,n,i.CC))),a=r.initReconnect(setTimeout.bind(o,M.bind(o,n,i.jB)));s=()=>{t&&t(),a&&a(),l.delete(e)}}};return y(),[e,a,y,s]}return[e,l.get(e)[4]]},[N,J]=U(new Map),$=d({onLoadingSlow:u,onSuccess:u,onError:u,onErrorRetry:(e,t,r,n,i)=>{let a=r.errorRetryCount,l=i.retryCount,u=~~((Math.random()+.5)*(1<<(l<8?l:8)))*r.errorRetryInterval;(c(a)||!(l>a))&&setTimeout(n,u,i)},onDiscarded:u,revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,shouldRetryOnError:!0,errorRetryInterval:V?1e4:5e3,focusThrottleInterval:5e3,dedupingInterval:2e3,loadingTimeout:V?5e3:3e3,compare:function e(t,r){var n,i;if(t===r)return!0;if(t&&r&&(n=t.constructor)===r.constructor){if(n===Date)return t.getTime()===r.getTime();if(n===RegExp)return t.toString()===r.toString();if(n===Array){if((i=t.length)===r.length)for(;i--&&e(t[i],r[i]););return -1===i}if(!n||"object"==typeof t){for(n in i=0,t)if(a.call(t,n)&&++i&&!a.call(r,n)||!(n in r)||!e(t[n],r[n]))return!1;return Object.keys(r).length===i}}return t!=t&&r!=r},isPaused:()=>!1,cache:N,mutate:J,fallback:{}},{isOnline:()=>O,isVisible:()=>{let e=v&&document.visibilityState;return c(e)||"hidden"!==e}}),B=(e,t)=>{let r=d(e,t);if(t){let{use:n,fallback:i}=e,{use:a,fallback:l}=t;n&&a&&(r.use=n.concat(a)),i&&l&&(r.fallback=d(i,l))}return r},H=(0,n.createContext)({}),X=e=>{let{value:t}=e,r=(0,n.useContext)(H),i=f(t),a=(0,n.useMemo)(()=>i?t(r):t,[i,r,t]),l=(0,n.useMemo)(()=>i?a:B(r,a),[i,r,a]),u=a&&a.provider,s=(0,n.useRef)(o);u&&!s.current&&(s.current=U(u(l.cache||N),a));let c=s.current;return c&&(l.cache=c[0],l.mutate=c[1]),L(()=>{if(c)return c[2]&&c[2](),c[3]},[]),(0,n.createElement)(H.Provider,d(e,{value:l}))}},7989:(e,t,r)=>{r.d(t,{q:()=>n});let n="$inf$"},13100:(e,t,r)=>{e.exports=r(61907)},17967:(e,t,r)=>{r.d(t,{CC:()=>n,I0:()=>l,jB:()=>i,q2:()=>a});let n=0,i=1,a=2,l=3},36698:(e,t,r)=>{r.d(t,{Ay:()=>_,WI:()=>S});var n=r(41987),i=r(98686),a=r(4908),l=r(7989),u=r(71266),o=r(13100);let s=()=>{},c=s(),f=Object,d=e=>e===c,g=e=>"function"==typeof e,y=new WeakMap,p=e=>f.prototype.toString.call(e),b=(e,t)=>e===`[object ${t}]`,h=0,v=e=>{let t,r,n=typeof e,i=p(e),a=b(i,"Date"),l=b(i,"RegExp"),u=b(i,"Object");if(f(e)!==e||a||l)t=a?e.toJSON():"symbol"==n?e.toString():"string"==n?JSON.stringify(e):""+e;else{if(t=y.get(e))return t;if(t=++h+"~",y.set(e,t),Array.isArray(e)){for(r=0,t="@";r<e.length;r++)t+=v(e[r])+",";y.set(e,t)}if(u){t="#";let n=f.keys(e).sort();for(;!d(r=n.pop());)d(e[r])||(t+=r+":"+v(e[r])+",");y.set(e,t)}}return t},w=e=>{if(g(e))try{e=e()}catch(t){e=""}let t=e;return[e="string"==typeof e?e:(Array.isArray(e)?e.length:e)?v(e):"",t]},m=e=>w(e?e(0,null):null)[0],S=e=>l.q+m(e),O=Promise.resolve(),_=(0,u.Ht)(i.default,e=>(t,r,i)=>{let u,s=(0,n.useRef)(!1),{cache:c,initialSize:f=1,revalidateAll:d=!1,persistSize:g=!1,revalidateFirstPage:y=!0,revalidateOnMount:p=!1,parallel:b=!1}=i,[,,,h]=a.b.get(a.c);try{(u=m(t))&&(u=l.q+u)}catch(e){}let[v,w,S]=(0,a.z)(c,u),_=(0,n.useCallback)(()=>(0,a.e)(v()._l)?f:v()._l,[c,u,f]);(0,o.useSyncExternalStore)((0,n.useCallback)(e=>u?S(u,()=>{e()}):()=>{},[c,u]),_,_);let E=(0,n.useCallback)(()=>{let e=v()._l;return(0,a.e)(e)?f:e},[u,f]),k=(0,n.useRef)(E());(0,a.u)(()=>{if(!s.current){s.current=!0;return}u&&w({_l:g?k.current:E()})},[u,c]);let R=p&&!s.current,C=e(u,async e=>{let n=v()._i,l=v()._r;w({_r:a.U});let u=[],o=E(),[s]=(0,a.z)(c,e),f=s().data,g=[],p=null;for(let e=0;e<o;++e){let[o,s]=(0,a.s)(t(e,b?null:p));if(!o)break;let[v,w]=(0,a.z)(c,o),m=v().data,S=d||n||(0,a.e)(m)||y&&!e&&!(0,a.e)(f)||R||f&&!(0,a.e)(f[e])&&!i.compare(f[e],m);if(r&&("function"==typeof l?l(m,s):S)){let t=async()=>{if(o in h){let e=h[o];delete h[o],m=await e}else m=await r(s);w({data:m,_k:s}),u[e]=m};b?g.push(t):await t()}else u[e]=m;b||(p=m)}return b&&await Promise.all(g.map(e=>e())),w({_i:a.U}),u},i),j=(0,n.useCallback)(function(e,t){let r="boolean"==typeof t?{revalidate:t}:t||{},n=!1!==r.revalidate;return u?(n&&((0,a.e)(e)?w({_i:!0,_r:r.revalidate}):w({_i:!1,_r:r.revalidate})),arguments.length?C.mutate(e,{...r,revalidate:n}):C.mutate()):O},[u,c]),L=(0,n.useCallback)(e=>{let r;if(!u)return O;let[,n]=(0,a.z)(c,u);if((0,a.a)(e)?r=e(E()):"number"==typeof e&&(r=e),"number"!=typeof r)return O;n({_l:r}),k.current=r;let i=[],[l]=(0,a.z)(c,u),o=null;for(let e=0;e<r;++e){let[r]=(0,a.s)(t(e,o)),[n]=(0,a.z)(c,r),u=r?n().data:a.U;if((0,a.e)(u))return j(l().data);i.push(u),o=u}return j(i)},[u,c,j,E]);return{size:E(),setSize:L,mutate:j,get data(){return C.data},get error(){return C.error},get isValidating(){return C.isValidating},get isLoading(){return C.isLoading}}})},61907:(e,t,r)=>{var n=r(41987),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=n.useState,l=n.useEffect,u=n.useLayoutEffect,o=n.useDebugValue;function s(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!i(e,r)}catch(e){return!0}}var c="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=a({inst:{value:r,getSnapshot:t}}),i=n[0].inst,c=n[1];return u(function(){i.value=r,i.getSnapshot=t,s(i)&&c({inst:i})},[e,r,t]),l(function(){return s(i)&&c({inst:i}),e(function(){s(i)&&c({inst:i})})},[e]),o(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:c},71266:(e,t,r)=>{r.d(t,{Ht:()=>y,aw:()=>g,iX:()=>s,qm:()=>d,uv:()=>c});var n=r(4908),i=r(7989),a=r(41987);let l=n.i&&window.__SWR_DEVTOOLS_USE__,u=l?window.__SWR_DEVTOOLS_USE__:[],o=e=>(0,n.a)(e[1])?[e[0],e[1],e[2]||{}]:[e[0],null,(null===e[1]?e[2]:e[1])||{}],s=()=>(0,n.m)(n.d,(0,a.useContext)(n.S)),c=(e,t)=>{let[r,i]=(0,n.s)(e),[,,,a]=n.b.get(n.c);if(a[r])return a[r];let l=t(i);return a[r]=l,l},f=u.concat(e=>(t,r,a)=>{let l=r&&((...e)=>{let[a]=(0,n.s)(t),[,,,l]=n.b.get(n.c);if(a.startsWith(i.q))return r(...e);let u=l[a];return(0,n.e)(u)?r(...e):(delete l[a],u)});return e(t,l,a)}),d=e=>function(...t){let r=s(),[i,a,l]=o(t),u=(0,n.f)(r,l),c=e,{use:d}=u,g=(d||[]).concat(f);for(let e=g.length;e--;)c=g[e](c);return c(i,a||u.fetcher||null,u)},g=(e,t,r)=>{let n=t[e]||(t[e]=[]);return n.push(r),()=>{let e=n.indexOf(r);e>=0&&(n[e]=n[n.length-1],n.pop())}},y=(e,t)=>(...r)=>{let[n,i,a]=o(r),l=(a.use||[]).concat(t);return e(n,i,{...a,use:l})};l&&(window.__SWR_DEVTOOLS_REACT__=a)},98686:(e,t,r)=>{r.r(t),r.d(t,{SWRConfig:()=>O,default:()=>_,mutate:()=>a.j,preload:()=>u.uv,unstable_serialize:()=>w,useSWRConfig:()=>u.iX});var n=r(41987),i=r(13100),a=r(4908),l=r(17967),u=r(71266);let o=()=>{},s=o(),c=Object,f=e=>e===s,d=e=>"function"==typeof e,g=new WeakMap,y=e=>c.prototype.toString.call(e),p=(e,t)=>e===`[object ${t}]`,b=0,h=e=>{let t,r,n=typeof e,i=y(e),a=p(i,"Date"),l=p(i,"RegExp"),u=p(i,"Object");if(c(e)!==e||a||l)t=a?e.toJSON():"symbol"==n?e.toString():"string"==n?JSON.stringify(e):""+e;else{if(t=g.get(e))return t;if(t=++b+"~",g.set(e,t),Array.isArray(e)){for(r=0,t="@";r<e.length;r++)t+=h(e[r])+",";g.set(e,t)}if(u){t="#";let n=c.keys(e).sort();for(;!f(r=n.pop());)f(e[r])||(t+=r+":"+h(e[r])+",");g.set(e,t)}}return t},v=e=>{if(d(e))try{e=e()}catch(t){e=""}let t=e;return[e="string"==typeof e?e:(Array.isArray(e)?e.length:e)?h(e):"",t]},w=e=>v(e)[0],m=n.use||(e=>{switch(e.status){case"pending":throw e;case"fulfilled":return e.value;case"rejected":throw e.reason;default:throw e.status="pending",e.then(t=>{e.status="fulfilled",e.value=t},t=>{e.status="rejected",e.reason=t}),e}}),S={dedupe:!0},O=a.O.defineProperty(a.g,"defaultValue",{value:a.d}),_=(0,u.qm)((e,t,r)=>{let{cache:o,compare:s,suspense:c,fallbackData:f,revalidateOnMount:d,revalidateIfStale:g,refreshInterval:y,refreshWhenHidden:p,refreshWhenOffline:b,keepPreviousData:h}=r,[v,w,O,_]=a.b.get(o),[E,k]=(0,a.s)(e),R=(0,n.useRef)(!1),C=(0,n.useRef)(!1),j=(0,n.useRef)(E),L=(0,n.useRef)(t),T=(0,n.useRef)(r),V=()=>T.current,A=()=>V().isVisible()&&V().isOnline(),[D,I,x,q]=(0,a.z)(o,E),P=(0,n.useRef)({}).current,W=(0,a.e)(f)?(0,a.e)(r.fallback)?a.U:r.fallback[E]:f,z=(e,t)=>{for(let r in P)if("data"===r){if(!s(e[r],t[r])&&(!(0,a.e)(e[r])||!s(X,t[r])))return!1}else if(t[r]!==e[r])return!1;return!0},F=(0,n.useMemo)(()=>{let e=!!E&&!!t&&((0,a.e)(d)?!V().isPaused()&&!c&&!1!==g:d),r=t=>{let r=(0,a.m)(t);return(delete r._k,e)?{isValidating:!0,isLoading:!0,...r}:r},n=D(),i=q(),l=r(n),u=n===i?l:r(i),o=l;return[()=>{let e=r(D());return z(e,o)?(o.data=e.data,o.isLoading=e.isLoading,o.isValidating=e.isValidating,o.error=e.error,o):(o=e,e)},()=>u]},[o,E]),M=(0,i.useSyncExternalStore)((0,n.useCallback)(e=>x(E,(t,r)=>{z(r,t)||e()}),[o,E]),F[0],F[1]),U=!R.current,N=v[E]&&v[E].length>0,J=M.data,$=(0,a.e)(J)?W&&(0,a.B)(W)?m(W):W:J,B=M.error,H=(0,n.useRef)($),X=h?(0,a.e)(J)?(0,a.e)(H.current)?$:H.current:J:$,G=(!N||!!(0,a.e)(B))&&(U&&!(0,a.e)(d)?d:!V().isPaused()&&(c?!(0,a.e)($)&&g:(0,a.e)($)||g)),K=!!(E&&t&&U&&G),Q=(0,a.e)(M.isValidating)?K:M.isValidating,Y=(0,a.e)(M.isLoading)?K:M.isLoading,Z=(0,n.useCallback)(async e=>{let t,n,i=L.current;if(!E||!i||C.current||V().isPaused())return!1;let u=!0,o=e||{},c=!O[E]||!o.dedupe,f=()=>a.I?!C.current&&E===j.current&&R.current:E===j.current,d={isValidating:!1,isLoading:!1},g=()=>{I(d)},y=()=>{let e=O[E];e&&e[1]===n&&delete O[E]},p={isValidating:!0};(0,a.e)(D().data)&&(p.isLoading=!0);try{if(c&&(I(p),r.loadingTimeout&&(0,a.e)(D().data)&&setTimeout(()=>{u&&f()&&V().onLoadingSlow(E,r)},r.loadingTimeout),O[E]=[i(k),(0,a.o)()]),[t,n]=O[E],t=await t,c&&setTimeout(y,r.dedupingInterval),!O[E]||O[E][1]!==n)return c&&f()&&V().onDiscarded(E),!1;d.error=a.U;let e=w[E];if(!(0,a.e)(e)&&(n<=e[0]||n<=e[1]||0===e[1]))return g(),c&&f()&&V().onDiscarded(E),!1;let l=D().data;d.data=s(l,t)?l:t,c&&f()&&V().onSuccess(t,E,r)}catch(r){y();let e=V(),{shouldRetryOnError:t}=e;!e.isPaused()&&(d.error=r,c&&f()&&(e.onError(r,E,e),(!0===t||(0,a.a)(t)&&t(r))&&(!V().revalidateOnFocus||!V().revalidateOnReconnect||A())&&e.onErrorRetry(r,E,e,e=>{let t=v[E];t&&t[0]&&t[0](l.I0,e)},{retryCount:(o.retryCount||0)+1,dedupe:!0})))}return u=!1,g(),!0},[E,o]),ee=(0,n.useCallback)((...e)=>(0,a.n)(o,j.current,...e),[]);if((0,a.u)(()=>{L.current=t,T.current=r,(0,a.e)(J)||(H.current=J)}),(0,a.u)(()=>{if(!E)return;let e=Z.bind(a.U,S),t=0;V().revalidateOnFocus&&(t=Date.now()+V().focusThrottleInterval);let r=(0,u.aw)(E,v,(r,n={})=>{if(r==l.CC){let r=Date.now();V().revalidateOnFocus&&r>t&&A()&&(t=r+V().focusThrottleInterval,e())}else if(r==l.jB)V().revalidateOnReconnect&&A()&&e();else if(r==l.q2)return Z();else if(r==l.I0)return Z(n)});return C.current=!1,j.current=E,R.current=!0,I({_k:k}),G&&!O[E]&&((0,a.e)($)||a.r?e():(0,a.t)(e)),()=>{C.current=!0,r()}},[E]),(0,a.u)(()=>{let e;function t(){let t=(0,a.a)(y)?y(D().data):y;t&&-1!==e&&(e=setTimeout(r,t))}function r(){!D().error&&(p||V().isVisible())&&(b||V().isOnline())?Z(S).then(t):t()}return t(),()=>{e&&(clearTimeout(e),e=-1)}},[y,p,b,E]),(0,n.useDebugValue)(X),c&&(0,a.e)($)&&E){if(!a.I&&a.r)throw Error("Fallback data is required when using Suspense in SSR.");L.current=t,T.current=r,C.current=!1;let e=_[E];if((0,a.e)(e)||m(ee(e)),(0,a.e)(B)){let e=Z(S);(0,a.e)(X)||(e.status="fulfilled",e.value=!0),m(e)}else throw B}return{mutate:ee,get data(){return P.data=!0,X},get error(){return P.error=!0,B},get isValidating(){return P.isValidating=!0,Q},get isLoading(){return P.isLoading=!0,Y}}})}}]);